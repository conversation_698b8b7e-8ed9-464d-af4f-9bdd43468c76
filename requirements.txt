--extra-index-url https://us-python.pkg.dev/bks-ar-pkg/aoss-python/simple/

# wsgi
uWSGI==2.0.28
uwsgitop==0.12

# connection and security
backports.ssl_match_hostname==*******  # for old tornado on python-3.12
cryptography==43.0.3
django-encrypted-secrets==0.9.9
grafana-django-saml2-auth==3.12.0
httplib2==0.22.0
idna==3.10
oauthlib==3.2.2
paramiko==3.5.0
pyOpenSSL==24.2.1
pyotp==2.9.0
requests-toolbelt==1.0.0
requests==2.32.3
urllib3==1.26.20
xmlschema==3.4.3

# django and tornado related
Collectfast==2.2.0
Django==4.2.18
Jinja2==3.1.5
django-admin-lightweight-date-hierarchy==1.3.0
django-admin-rangefilter==0.11.2
django-adminplus==0.6
django-allauth==0.47.0
django-autocomplete-light==3.11.0
django-ckeditor==6.7.2
django-cors-headers==4.6.0
django-countries==7.6.1
django-cursor-pagination==0.3.0
django-dirtyfields==1.9.5
django-extensions==3.2.3
django-filter==24.3
django-nested-admin==4.1.1
django-oauth-toolkit==2.4.0
django-pgtrigger==4.13.3
django-qr-code==4.1.0
django-utils-six==2.0
djangorestframework-dataclasses==1.3.1
djangorestframework-simplejwt==5.3.1
djangorestframework==3.14.0
drf-extra-fields==3.7.0
drf-nested-routers==0.94.1
pillow==11.2.1
psycopg2==2.9.10
tornado==4.5.3  # TODO: booksy not compatible with >4.5.3 and we are replacing tornado with drf

# redis and celery related
amqp==5.3.1
billiard==4.2.1
celery[pytest,redis]==5.4.0
django-redis==5.4.0
kombu==5.4.2
redis==5.2.1
redlock-py==1.0.8
vine==5.1.0

# elasticsearch related
elasticsearch==7.17.12
elasticsearch-dsl==7.4.1

# external APIs
PyJWT==2.8.0
aioapns==3.3.1
bitly-api==0.3
braintree==4.31.0
eppo-server-sdk==4.3.1
facebook-business==22.0.2
launchdarkly-server-sdk==7.6.1
python-lokalise-api==3.0.0
screenshotone==0.0.14
segment-analytics-python==2.3.3
stripe==7.7.0
telnyx==2.1.4
twilio==7.17.0
vonage==3.17.4

# Google RPC
django-grpc==1.0.21
django-socio-grpc==0.17.3
djangogrpcframework==0.2.1
grpc-interceptor==0.15.4
grpcio-health-checking==1.62.0
grpcio-tools==1.62.0
grpcio==1.62.0
protobuf==4.25.3

# Google APIs
cachetools==5.5.1
firebase-admin==6.6.0
gdown==5.2.0
google-api-core==2.23.0
google-api-python-client==2.161.0
google-cloud-pubsub==2.27.2
google-cloud-recaptcha-enterprise==1.25.0
google-cloud-secret-manager==2.21.1
google-cloud-storage==2.18.2
oauth2client==4.1.3
rele[django]==1.13.0
tabulate==0.9.0

# aws
awscli==1.40.0
boto3==1.38.1
botocore==1.38.1
django-storages==1.11.1
rsa==4.7.2

# reliability and observability
kubernetes==31.0.0
retrying==1.3.4
python-json-logger==2.0.7

# parsing and formats
Babel==2.11.0
PyYAML==6.0.2
Unidecode==1.3.8
WeasyPrint==52.5
arrow==1.3.0
beautifulsoup4==4.12.3
bleach==6.2.0
calweek==0.5.0
dataclasses-json==0.5.8
emoji==2.14.1
glom==24.11.0
holidays==0.67
html2text==2024.2.26
icalendar==6.0.1
iso3166==2.1.1
iso8601==2.1.0
joblib==1.4.2
lxml==5.3.0
mammoth==1.8.0
markdown2==2.5.2
nptime==1.1
num2words==0.5.14  # allows: 1042 -> "one thousand and forty-two"
numpy==2.1.3
openpyxl==3.1.5
pandas==2.2.3
phonenumbers==8.13.53
pyarrow==18.0.0
py-moneyed==3.0
python-barcode==0.15.1
python-dateutil==2.9.0.post0
python-slugify[unidecode]==8.0.4
python-stdnum==1.20
pytz==2024.1
regex==2024.11.6
simplejson==3.17.4
xlrd==2.0.1

# geo
geographiclib==2.0
pyshp==2.3.1  # gives `shapefile` package
shapely==2.0.6

# utils
MarkupSafe==3.0.2
dacite==1.8.1
deepdiff==8.2.0
dparse==0.6.4
lagom==2.7.5
more-itertools==10.5.0
packaging==24.2
qrcode==8.0
tqdm==4.67.1
wrapt==1.16.0

############################
####### CI & Testing #######
############################

black==25.1.0
cookies==2.2.1
coverage[toml]==7.6.10
flake8==7.1.2
import-linter==2.3
parameterized==0.9.0
pep8==1.7.1
pyflakes==3.2.0
pylint-django==2.6.1
pylint-protobuf==0.22.0
pylint==3.3.4
pytest-cov==6.0.0
pytest-django==4.10.0
pytest-env==1.1.5
pytest-freezegun==0.4.2
pytest-repeat==0.9.3
pytest-timestamper==0.0.10
pytest-watch==4.2.0
pytest-xdist==3.6.1
pytest==8.3.4
responses==0.25.6
tomli==2.1.0

# warning - below libs are used in application code for some reason
freezegun==1.5.1  # webapps.admin_extra.tasks.BookingImporter.create_transaction
mock==5.1.0  # webapps.notification.email_mocks.mock_objects.MockObjects
model-bakery==1.18.2

############################
########## Debug ###########
############################

Pympler==1.1
ipdb==0.13.13
ipython==9.1.0
psutil==7.0.0
pudb==2025.1

############################
####### DEPENDENCIES #######
############################

CacheControl==0.14.2
CairoSVG==2.7.1
Deprecated==1.2.15
PyNaCl==1.5.0
Pygments==2.19.1
SecretStorage==3.3.3
aiohttp==3.11.11
aiosignal==1.3.2
asgiref==3.8.1
astroid==3.3.8
asttokens==2.4.1
asyncio==3.4.3
attrs==25.1.0
backoff==2.2.1
backports.tarfile==1.2.0
bcrypt==4.2.1
boltons==24.1.0
bytecode==0.16.1
cairocffi==1.7.1
cattrs==24.1.2
celery-redbeat==2.3.2
certifi==2024.12.14
cffi==1.17.1
charset-normalizer==3.4.1
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
click==8.1.8
cobble==0.1.4
colorama==0.4.6
confluent-kafka==2.5.3
cssselect2==0.7.0
curlify==2.2.1
dataclasses-avroschema==0.65.7
ddsketch==3.0.1
debugpy==1.8.12
decorator==5.1.1
defusedxml==0.7.1
dictor==0.1.11
dill==0.3.9
django-js-asset==2.2.0
django-jsonform==2.23.1
docker==7.1.0
docopt==0.6.2
docutils==0.18.1
elementpath==4.6.0
envier==0.6.1
et-xmlfile==2.0.0
execnet==2.1.1
executing==2.1.0
expiringdict==1.2.2
face==24.0.0
filetype==1.2.0
frozenlist==1.5.0
future==1.0.0
google-auth-httplib2==0.2.0
google-auth==2.36.0
google-cloud-aiplatform==1.79.0
google-cloud-core==2.4.1
google-cloud-firestore==2.19.0
google-crc32c==1.6.0
google-generativeai==0.8.4
google-resumable-media==2.7.2
googleapis-common-protos==1.66.0
grimp==3.7.1
grpc-google-iam-v1==0.13.1
grpcio-status==1.62.0
h2==4.1.0
hpack==4.0.0
html5lib==1.1
hyperframe==6.0.1
importlib_metadata==6.11.0
iniconfig==2.0.0
isort==6.0.1
jaraco.classes==3.4.0
jaraco.context==6.0.1
jaraco.functools==4.1.0
jedi==0.19.2
jeepney==0.8.0
jmespath==1.0.1
jwcrypto==1.5.6
lark-parser==0.12.0
marshmallow-enum==1.5.1
marshmallow==3.26.1
matplotlib-inline==0.1.7
mccabe==0.7.0
monotonic==1.6
msgpack==1.1.0
multidict==6.1.0
mypy-extensions==1.0.0
opentelemetry-api==1.27.0
ordered-set==4.1.0
parso==0.8.4
pathspec==0.12.1
pexpect==4.9.0
platformdirs==4.3.6
pluggy==1.5.0
prompt_toolkit==3.0.50
proto-parser==1.6.3
proto-plus==1.25.0
ptyprocess==0.7.0
pure-eval==0.2.3
py==1.11.0
pyRFC3339==2.0.1
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycodestyle==2.12.1
pycountry==24.6.1
pycparser==2.22
pycryptodome==3.21.0
pydantic-settings==2.6.1
pydantic==2.9.2
# pydantic_core - unpinned to fix renovate; this is always fixed to the pydantic version
pylint-plugin-utils==0.8.2
pyparsing==3.2.1
pyphen==0.17.2
pypng==0.20220715.0
pysaml2==7.4.2
pytest-celery==1.1.3
pytest-docker-tools==3.1.3
python-dotenv==1.0.1
python-memcached==1.62
python-monkey-business==1.1.0
python3-openid==3.2.0
requests-oauthlib==2.0.0
retry==0.9.2
s3transfer==0.12.0
segno==1.6.1
semver==2.13.0
six==1.17.0
soupsieve==2.6
sqlparse==0.5.3
stack-data==0.6.3
terminaltables==3.1.10
text-unidecode==1.3
tinycss2==1.4.0
tomlkit==0.13.2
traitlets==5.14.3
thefuzz==0.22.1
types-protobuf==5.26.0.20240422
types-python-dateutil==2.9.0.20241206
types-PyYAML==6.0.12.20241230
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2025.1
uritemplate==4.1.1
urwid==2.6.16
urwid_readline==0.15.1
vonage-jwt==1.1.5
vonage-utils==1.1.4
watchdog==6.0.0
wcwidth==0.2.13
webencodings==0.5.1
websocket-client==1.8.0
xmltodict==0.14.2
yarl==1.17.1
zipp==3.21.0
