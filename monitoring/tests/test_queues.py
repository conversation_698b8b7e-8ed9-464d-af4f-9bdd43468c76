import os
from pathlib import Path

import pytest
from django.conf import settings
from mock import patch, call

from lib.celery_tools import create_redis_queue_check, get_broker
from lib.email.backend import BooksyQueueEmailBackendRedisFIFO
from lib.rivers import River, get_client
from lib.tasks import consts
from monitoring import emit_redis_metrics
from monitoring.emit_redis_metrics import INSTRUCTION_PATH


class NoSleepException(Exception):
    pass


def setup_queues():
    Path(INSTRUCTION_PATH).parent.mkdir(exist_ok=True, parents=True)
    # SET CELERY
    celery_client = get_broker()
    for queue in settings.CELERY_QUEUES:
        celery_client.delete(queue)
        celery_client.lpush(queue, 1, 2, 3, 4, 5)
    celery_client.delete('unacked')
    celery_client.hset('unacked', mapping={'a': 1, 'b': 1, 'c': 1, 'd': 1, 'e': 1})
    celery_client.delete(consts.ETA_CELERY_BODY_BY_ID)
    celery_client.hset(
        consts.ETA_CELERY_BODY_BY_ID,
        mapping={'a': 1, 'b': 1, 'c': 1, 'd': 1, 'e': 1},
    )
    celery_client.delete(consts.ETA_CELERY_QUEUE_BY_ID)
    celery_client.hset(
        consts.ETA_CELERY_QUEUE_BY_ID,
        mapping={'a': 1, 'b': 1, 'c': 1, 'd': 1, 'e': 1},
    )
    celery_client.delete(consts.ETA_CELERY_ID_BY_TIME_KEY)
    celery_client.zadd(consts.ETA_CELERY_ID_BY_TIME_KEY, {'a': 1, 'b': 1, 'c': 1, 'd': 1, 'e': 1})

    # SET RIVER
    river_client = get_client()
    for river in River:
        river_client.delete(river)
        river_client.sadd(river, 1, 2, 3, 4, 5)

    # SET EMAIL
    BooksyQueueEmailBackendRedisFIFO.CACHE.client.get_client().delete(
        BooksyQueueEmailBackendRedisFIFO.email_queue_name
    )
    BooksyQueueEmailBackendRedisFIFO.CACHE.client.get_client().lpush(
        BooksyQueueEmailBackendRedisFIFO.email_queue_name,
        *[1, 2, 3, 4, 5],
    )


@patch.dict(os.environ, {'CUSTOM_METRICS_ENABLED': 'true'}, clear=True)
@patch('monitoring.emit_redis_metrics.statsd')
@patch('monitoring.emit_redis_metrics.initialize')
@patch('monitoring.emit_redis_metrics.sleep', side_effect=NoSleepException)
def test_queues(patch_sleep, patch_initialize, patch_statsd):
    setup_queues()

    with pytest.raises(NoSleepException):
        create_redis_queue_check()
        emit_redis_metrics.emit_queue_metric()

    gauge_calls = [
        call(
            'booksy.queue.by_app.celery_priority_worker.gauge',
            len(settings.CELERY_QUEUES_BY_APP['celery-priority-worker']) * 5,
            tags=[
                'app:celery_priority_worker',
                'horizontalpodautoscaler:core-us-celery-priority-worker',
                'country:us',
            ],
        ),
        call(
            'booksy.queue.by_app.celery_regular_worker.gauge',
            len(settings.CELERY_QUEUES_BY_APP['celery-regular-worker']) * 5,
            tags=[
                'app:celery_regular_worker',
                'horizontalpodautoscaler:core-us-celery-regular-worker',
                'country:us',
            ],
        ),
        call(
            'booksy.queue.by_app.celery_push_worker.gauge',
            len(settings.CELERY_QUEUES_BY_APP['celery-push-worker']) * 5,
            tags=[
                'app:celery_push_worker',
                'horizontalpodautoscaler:core-us-celery-push-worker',
                'country:us',
            ],
        ),
        call(
            'booksy.queue.by_app.celery_segment_worker.gauge',
            len(settings.CELERY_QUEUES_BY_APP['celery-segment-worker']) * 5,
            tags=[
                'app:celery_segment_worker',
                'horizontalpodautoscaler:core-us-celery-segment-worker',
                'country:us',
            ],
        ),
        call(
            'booksy.queue.by_app.celery_index_worker.gauge',
            len(settings.CELERY_QUEUES_BY_APP['celery-index-worker']) * 5,
            tags=[
                'app:celery_index_worker',
                'horizontalpodautoscaler:core-us-celery-index-worker',
                'country:us',
            ],
        ),
        call(
            'booksy.queue.by_app.celery_all_queue_worker',
            len(settings.CELERY_QUEUES_BY_APP['celery-all-queue-worker']) * 5,
            tags=[
                'app:celery-all-queue-worker',
                'horizontalpodautoscaler:core-us-celery-all-queue-worker',
                'country:us',
            ],
        ),
        call('booksy.queue.eta_tasks', 5, tags=['country:us']),
        call(
            'booksy.queue.by_priority.segment.gauge',
            len(settings.CELERY_QUEUES_BY_PRIORITY['segment']) * 5,
        ),
        call(
            'booksy.queue.by_priority.index.gauge',
            len(settings.CELERY_QUEUES_BY_PRIORITY['index']) * 5,
        ),
        call(
            'booksy.queue.by_priority.push_notifications.gauge',
            len(settings.CELERY_QUEUES_BY_PRIORITY['push_notifications']) * 5,
        ),
        call(
            'booksy.queue.by_priority.high.gauge',
            len(settings.CELERY_QUEUES_BY_PRIORITY['high']) * 5,
        ),
        call(
            'booksy.queue.by_priority.normal.gauge',
            len(settings.CELERY_QUEUES_BY_PRIORITY['normal']) * 5,
        ),
        call(
            'booksy.queue.by_priority.low.gauge', len(settings.CELERY_QUEUES_BY_PRIORITY['low']) * 5
        ),
        call('booksy.river_queue.gauge', 5, tags=['river_type:business']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:open_hours']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:resource']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:review']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:image']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:image_comment']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:image_like']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:availability']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:business_search_data']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:commodity']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:commodity_category']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:service']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:service_variant']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:addon']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:external_business']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:business_customer']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:business_category']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:business_account']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:assign_service_treatment']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:region']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:cms_content']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:seo_metadata']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:seo_feature_flag']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:seo_region_homepage']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:seo_cms_content_data']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:seo_region_category']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:user']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:users_to_update_booking_score']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:user_search_data_fast']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:user_search_data']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:aggregate_statistics']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:update_google_availability']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:MARTECH_APP_USED']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:welcome_new_client_message_blast']),
        call('booksy.river_queue.gauge', 5, tags=['river_type:profile_completeness_stats']),
        call('booksy.email_queue', 5),
    ]

    histogram_calls = [
        call(
            'booksy.queue.by_app.celery_priority_worker',
            len(settings.CELERY_QUEUES_BY_APP['celery-priority-worker']) * 5,
            tags=[
                'app:celery_priority_worker',
                'horizontalpodautoscaler:core-us-celery-priority-worker',
                'country:us',
            ],
        ),
        call(
            'booksy.queue.by_app.celery_regular_worker',
            len(settings.CELERY_QUEUES_BY_APP['celery-regular-worker']) * 5,
            tags=[
                'app:celery_regular_worker',
                'horizontalpodautoscaler:core-us-celery-regular-worker',
                'country:us',
            ],
        ),
        call(
            'booksy.queue.by_app.celery_push_worker',
            len(settings.CELERY_QUEUES_BY_APP['celery-push-worker']) * 5,
            tags=[
                'app:celery_push_worker',
                'horizontalpodautoscaler:core-us-celery-push-worker',
                'country:us',
            ],
        ),
        call(
            'booksy.queue.by_app.celery_segment_worker',
            len(settings.CELERY_QUEUES_BY_APP['celery-segment-worker']) * 5,
            tags=[
                'app:celery_segment_worker',
                'horizontalpodautoscaler:core-us-celery-segment-worker',
                'country:us',
            ],
        ),
        call(
            'booksy.queue.by_app.celery_index_worker',
            len(settings.CELERY_QUEUES_BY_APP['celery-index-worker']) * 5,
            tags=[
                'app:celery_index_worker',
                'horizontalpodautoscaler:core-us-celery-index-worker',
                'country:us',
            ],
        ),
        call('booksy.queue.unacked', 5),
        call(
            'booksy.queue.by_priority.segment',
            len(settings.CELERY_QUEUES_BY_PRIORITY['segment']) * 5,
        ),
        call(
            'booksy.queue.by_priority.index', len(settings.CELERY_QUEUES_BY_PRIORITY['index']) * 5
        ),
        call(
            'booksy.queue.by_priority.push_notifications',
            len(settings.CELERY_QUEUES_BY_PRIORITY['push_notifications']) * 5,
        ),
        call('booksy.queue.by_priority.high', len(settings.CELERY_QUEUES_BY_PRIORITY['high']) * 5),
        call(
            'booksy.queue.by_priority.normal', len(settings.CELERY_QUEUES_BY_PRIORITY['normal']) * 5
        ),
        call('booksy.queue.by_priority.low', len(settings.CELERY_QUEUES_BY_PRIORITY['low']) * 5),
    ]

    patch_statsd.histogram.assert_has_calls(histogram_calls, any_order=True)
    assert patch_statsd.histogram.call_count == len(histogram_calls)

    patch_statsd.gauge.assert_has_calls(gauge_calls, any_order=True)
    assert patch_statsd.gauge.call_count == len(gauge_calls)
