#!/usr/bin/env python
from operator import or_
from functools import reduce
import requests

COUNTRIES = [
    # prd-1
    'es',
    'fr',
    'gb',
    'ie',
    'za',
    'pt',
    'it',
    'nl',
    # prd-2
    'br',
    'ca',
    'mx',
    'au',
    'ar',
    'cl',
    'co',
    # prd-3
    'pl',
    # prd-4
    'us',
]


def load_stats(country_code):
    resp = requests.get(
        f'https://{country_code}.booksy.com/core/v2/debug/stats/?businesses=1',
        timeout=5.0,
    )
    if resp.status_code != 200:
        print(f'{country_code}: {resp.status_code} error')
        return {}
    return resp.json()


def main():
    # load stats
    data = {country_code: load_stats(country_code) for country_code in COUNTRIES}
    keys = sorted(reduce(or_, (set(stat) for stat in data.values())))
    if 'kill_switches' in keys:
        keys.remove('kill_switches')
    sums = {key: sum(stat.get(key) or 0 for stat in data.values()) for key in keys}
    print('\t'.join(f'{v.upper():>20}' for v in (['cc'] + keys)))
    print('\t'.join('-' * 20 for _ in (['cc'] + keys)))
    for country_code, stat in sorted(data.items()):
        print(
            '\t'.join(
                f'{v:>20}'
                for v in ([country_code] + [stat.get(key, '__missing__') for key in keys])
            )
        )
    print('\t'.join('-' * 20 for _ in (['cc'] + keys)))
    print(
        '\t'.join(f'{v:>20}' for v in (['total'] + [sums.get(key, '__missing__') for key in keys]))
    )
    kill_switches = sorted(
        reduce(
            or_,
            (set(stat.get('kill_switches', [])) for stat in data.values()),
        )
    )
    empty = ''
    padding = max(len(kill_switch) for kill_switch in kill_switches + ['']) + 2
    if kill_switches:
        print(f'{empty:{padding}}', *COUNTRIES)
    for kill_switch in kill_switches:
        print(
            f'{kill_switch:{padding}}',
            *(
                str(data[cc].get('kill_switches', {}).get(kill_switch, '?'))[0] + ' '
                for cc in COUNTRIES
            ),
            sum(1 for cc in COUNTRIES if data[cc].get('kill_switches', {}).get(kill_switch, False)),
            '/',
            len(COUNTRIES),
        )


if __name__ == '__main__':
    main()
