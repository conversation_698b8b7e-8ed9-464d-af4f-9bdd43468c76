#!/usr/bin/env python
# -*- coding: utf-8 -*-
import json
import django

django.setup()  # noqa
# pylint: disable=wrong-import-position
from django.conf import settings
from lib import (
    celery_tools,
    rivers,
)
from lib.email.backend import BooksyQueueEmailBackendRedisFIFO

# pylint: enable=disable=wrong-import-position


def main(verbose=False):
    # redis-celery
    celery_total, celery_queues, by_priority, by_app, eta_tasks = celery_tools.check_queues()
    email = BooksyQueueEmailBackendRedisFIFO.check_queue()
    river_total, river_queues = rivers.check_queues()  # redis-river
    data = {
        'redis_queues': {
            'country_code': settings.API_COUNTRY,
            'deployment_level': settings.DEPLOYMENT_LEVEL,
            'celery': {
                'total': celery_total,
                'queues': celery_queues,
                'by_priority': by_priority,
                'by_app': by_app,
                'unacked': celery_tools.get_unacked_tasks(),
            },
            'email': {
                'total': email,
            },
            'river': {
                'total': river_total,
                'queues': river_queues,
            },
            'eta_tasks': {
                'body_hashmap': eta_tasks[0],
                'queue_hashmap': eta_tasks[1],
                'timestamp_sortedset': eta_tasks[2],
            },
        }
    }
    if verbose:
        print(json.dumps(data))

    return data['redis_queues']


if __name__ == '__main__':
    main(verbose=True)
