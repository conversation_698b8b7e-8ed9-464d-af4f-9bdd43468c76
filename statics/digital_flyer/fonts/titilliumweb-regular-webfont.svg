<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="titillium_webregular" horiz-adv-x="560" >
<font-face units-per-em="1000" ascent="800" descent="-200" />
<missing-glyph horiz-adv-x="220" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="333" />
<glyph unicode=" "  horiz-adv-x="220" />
<glyph unicode="&#x09;" horiz-adv-x="220" />
<glyph unicode="&#xa0;" horiz-adv-x="220" />
<glyph unicode="!" horiz-adv-x="247" d="M83 0v112h82v-112h-82zM89 228l-5 464h79l-5 -464h-69z" />
<glyph unicode="&#x22;" horiz-adv-x="373" d="M303 470h-67l-4 222h75zM137 470h-67l-4 222h75z" />
<glyph unicode="#" d="M536 174h-111v-174h-68v174h-154v-174h-68v174h-111v64h111v186h-111v64h111v178h68v-178h154v178h68v-178h111v-64h-111v-186h111v-64zM357 238v186h-154v-186h154z" />
<glyph unicode="$" d="M494 194q0 -203 -211 -203h-15l-14 -115q-51 3 -51 7l14 111l-143 18l8 61q77 -11 143 -15l33 250q-102 24 -145.5 61.5t-43.5 123.5q0 177 212 177q15 0 23 -1l17 129h51l-17 -132l125 -15l-6 -62q-69 8 -127 13l-30 -233q97 -23 137 -59t40 -116zM143 497q0 -54 27 -77 t97 -40l29 225h-10q-143 0 -143 -108zM420 189q0 48 -25 71t-87 38l-31 -242h2q141 0 141 133z" />
<glyph unicode="%" d="M155 -4l205 685l48 -16l-205 -683zM138 670q110 0 110 -138q0 -71 -29 -106t-81 -35t-81 35t-29 106q0 138 110 138zM89 532q0 -90 49.5 -90t49.5 90q0 45 -11.5 66t-38 21t-38 -21t-11.5 -66zM313 132q0 137 109.5 137t109.5 -138q0 -70 -29 -105.5t-81 -35.5 t-80.5 35.5t-28.5 106.5zM373 132q0 -91 49 -91q28 0 39 22t11 68t-11 66.5t-38 20.5t-38.5 -20.5t-11.5 -65.5z" />
<glyph unicode="&#x26;" horiz-adv-x="688" d="M186 661q46 40 130 40t127.5 -40t43.5 -110.5t-35 -111t-119 -79.5l152 -150q22 64 28 162l73 -1q-13 -125 -46 -208l129 -122l-45 -50l-121 113q-69 -114 -209 -114t-196 54t-56 149.5t36.5 140t118.5 71.5q-35 40 -46 71t-11 78q0 67 46 107zM282 58q123 0 169 90 l-211 210q-66 -21 -94 -54t-28 -102q0 -144 164 -144zM215 542q0 -61 45 -109l29 -28q69 30 96 59t27 80q0 92 -98.5 92t-98.5 -94z" />
<glyph unicode="'" horiz-adv-x="208" d="M70 470l-3 222h75l-5 -222h-67z" />
<glyph unicode="(" horiz-adv-x="264" d="M155 -125q-104 234 -104 426q0 96 26 208.5t52 176.5l26 64h72q-35 -92 -65 -227.5t-30 -221t24 -192t48 -170.5l23 -64h-72z" />
<glyph unicode=")" horiz-adv-x="264" d="M202 431q11 -70 11 -148t-26 -184.5t-52 -164.5l-26 -59h-72q34 80 64.5 210.5t30.5 216t-23.5 197.5t-47.5 181l-24 70h72q66 -149 93 -319z" />
<glyph unicode="*" horiz-adv-x="420" d="M373 511h-132l41 -125l-49 -15l-41 126l-107 -79l-31 40l108 79l-106 77l31 42l106 -78l41 126l49 -16l-40 -127h130v-50z" />
<glyph unicode="+" d="M55 217v68h189v195h70v-195h191v-68h-191v-197h-70v197h-189z" />
<glyph unicode="," horiz-adv-x="223" d="M34 -123l46 230h83l-67 -230h-62z" />
<glyph unicode="-" horiz-adv-x="438" d="M68 240v70h303v-70h-303z" />
<glyph unicode="." horiz-adv-x="216" d="M67 0v114h82v-114h-82z" />
<glyph unicode="/" horiz-adv-x="412" d="M32 10l282 698l67 -25l-282 -697z" />
<glyph unicode="0" d="M279 671q82 0 133 -31q108 -64 108 -320q0 -181 -60 -255.5t-180 -74.5t-180.5 73.5t-60.5 262t59.5 267t180.5 78.5zM280 602q-57 0 -90 -24q-71 -49 -71 -259q0 -148 39 -204.5t122 -56.5t122 57t39 211.5t-37.5 215t-123.5 60.5z" />
<glyph unicode="1" d="M389 660v-660h-77v574l-170 -112l-35 58l210 140h72z" />
<glyph unicode="2" d="M491 0h-422v65l193 203q50 52 73 79t41.5 64t18.5 74q0 64 -35 90t-109 26q-65 0 -145 -17l-26 -5l-6 64q95 27 196 27t152.5 -42.5t51.5 -134.5q0 -70 -31 -122t-107 -125l-176 -179h331v-67z" />
<glyph unicode="3" d="M72 643q94 28 201.5 28t155.5 -41t48 -131q0 -46 -12.5 -66.5t-20 -31.5t-19.5 -21q-22 -17 -35 -24l-11 -7q58 -21 86 -53.5t28 -106.5q0 -101 -51.5 -150t-162.5 -49q-88 0 -186 20l-31 7l7 63q108 -22 202 -22q143 1 143 128q0 119 -136 124h-133v66h133q42 0 81 33.5 t39 88.5t-31 79.5t-104 24.5q-80 0 -158 -15l-26 -5z" />
<glyph unicode="4" d="M360 0v129h-320v60l188 471h84l-191 -463h239v206h77v-206h84v-68h-84v-129h-77z" />
<glyph unicode="5" d="M477 660v-70h-321l-18 -215q77 41 155 41q210 0 210 -195q0 -112 -58 -171t-163 -59q-84 0 -186 22l-33 7l9 61q115 -22 193.5 -22t118.5 40.5t40 113.5t-36.5 103.5t-103.5 30.5q-36 0 -75.5 -10t-60.5 -20l-21 -10l-53 10l18 343h385z" />
<glyph unicode="6" d="M482 590q-90 12 -175 12t-131 -59t-46 -169l25 10q25 9 66.5 19t73.5 10q222 0 222 -205q0 -105 -60.5 -161.5t-170.5 -56.5q-236 0 -236 343q0 173 67.5 255t190.5 82q81 0 154 -12l27 -5zM290 344q-34 0 -74.5 -9.5t-63.5 -18.5l-23 -10q1 -114 38.5 -181t114.5 -67 t116 38.5t39 109t-38.5 104.5t-108.5 34z" />
<glyph unicode="7" d="M77 590v70h410v-101l-254 -569l-73 23l249 551v26h-332z" />
<glyph unicode="8" d="M278 671q110 0 174.5 -43.5t64.5 -128.5q0 -62 -25 -94.5t-85 -59.5q60 -27 90 -63.5t30 -102.5q0 -189 -247.5 -189t-247.5 178q0 71 27.5 108t86.5 69q-54 27 -78.5 61.5t-24.5 93.5q0 84 62.5 127.5t172.5 43.5zM114 181q0 -121 165.5 -121t165.5 127q0 53 -29.5 78.5 t-87.5 42.5h-105q-55 -15 -82 -44.5t-27 -82.5zM435 490q0 111 -155 111t-155 -110q0 -44 24 -71t74 -45h105q54 17 80.5 44t26.5 71z" />
<glyph unicode="9" d="M247 58q181 0 182 230q-100 -38 -172 -38q-216 0 -216 201q0 101 62 160t167 59q121 0 180 -89.5t59 -263t-66.5 -250.5t-195.5 -77q-75 0 -155 14l-26 4l7 62q90 -12 174 -12zM263 318q61 0 141 27l26 9q-3 248 -160 248q-70 0 -109.5 -40.5t-39.5 -110.5 q0 -133 142 -133z" />
<glyph unicode=":" horiz-adv-x="217" d="M67 322v114h82v-114h-82zM67 0v114h82v-114h-82z" />
<glyph unicode=";" horiz-adv-x="248" d="M93 107h84l-67 -230h-62zM88 322v114h81v-114h-81z" />
<glyph unicode="&#x3c;" d="M477 409l-339 -157l339 -160v-79l-416 205v66l416 203v-78z" />
<glyph unicode="=" d="M64 309v69h432v-69h-432zM64 124v69h432v-69h-432z" />
<glyph unicode="&#x3e;" d="M421 252l-339 157v78l416 -203v-66l-416 -205v79z" />
<glyph unicode="?" horiz-adv-x="447" d="M406 542q0 -65 -17.5 -98.5t-71 -78t-73.5 -71t-20 -58.5v-35h-63q-13 27 -13 61t23 63.5t74 73.5t68 70t17 72.5t-32 69.5t-101 23q-44 0 -129 -18l-26 -5l-5 61q101 30 167 30q103 0 152.5 -37.5t49.5 -122.5zM150 1v112h82v-112h-82z" />
<glyph unicode="@" horiz-adv-x="977" d="M933 305v-13q0 -212 -67 -267q-24 -19 -48.5 -25t-56 -6t-54 6t-34.5 15q-24 18 -33 39q-98 -59 -174 -59q-37 0 -64.5 9t-55.5 34q-56 50 -56 195t45.5 208.5t157.5 63.5q43 0 97 -19l18 -7v16h75v-191q0 -175 11 -208q5 -13 14.5 -24t20.5 -13t38 -2t45.5 13t31.5 66 t13 156v14q0 185 -82 268t-272.5 83t-283 -94.5t-92.5 -307t88 -302.5t289 -90l143 10l3 -67q-94 -9 -146 -9q-116 0 -196.5 22t-139.5 75q-117 102 -117 367q0 241 115 351.5t338 110.5t326 -102.5t103 -315.5zM471 61q43 0 146 46q-9 54 -9 173v139q-60 20 -103 20 q-81 0 -110 -46t-29 -149q0 -183 105 -183z" />
<glyph unicode="A" horiz-adv-x="596" d="M24 0l190 692h168l190 -692h-76l-50 178h-296l-50 -178h-76zM272 626l-105 -379h262l-105 379h-52z" />
<glyph unicode="B" horiz-adv-x="616" d="M85 692h251q103 0 154.5 -42.5t51.5 -133.5q0 -65 -22.5 -101.5t-66.5 -57.5q110 -38 110 -163q0 -104 -55 -149t-159 -45h-264v692zM341 318h-179v-250h184q68 0 103.5 28.5t35.5 100.5q0 38 -14.5 64t-38.5 37q-44 20 -91 20zM333 624h-171v-239h178q64 0 93.5 30.5 t29.5 92t-31.5 89t-98.5 27.5z" />
<glyph unicode="C" horiz-adv-x="544" d="M503 11q-103 -21 -185 -21t-132.5 22.5t-78 70t-38 109t-10.5 153.5t10.5 154.5t38 110.5t77.5 70t130 22t188 -22l-3 -65q-101 18 -180 18q-110 0 -145.5 -65.5t-35.5 -223.5q0 -79 6.5 -127t25.5 -86.5t55.5 -55t107 -16.5t166.5 18z" />
<glyph unicode="D" horiz-adv-x="645" d="M332 0h-247v692h247q169 0 224 -134q32 -77 32 -202q0 -261 -130 -329q-52 -27 -126 -27zM509 356q0 199 -89 248q-36 20 -88 20h-170v-556h170q177 0 177 288z" />
<glyph unicode="E" horiz-adv-x="553" d="M85 0v692h421v-68h-344v-239h284v-67h-284v-250h344v-68h-421z" />
<glyph unicode="F" horiz-adv-x="526" d="M85 0v692h414v-68h-337v-270h285v-68h-285v-286h-77z" />
<glyph unicode="G" horiz-adv-x="616" d="M359 285v69h191v-340q-126 -24 -225 -24q-151 0 -209.5 87t-58.5 268.5t58.5 269t207.5 87.5q93 0 193 -20l34 -6l-3 -64q-124 21 -215 21q-115 0 -155 -66.5t-40 -221t40 -221t154 -66.5q70 0 143 14v213h-115z" />
<glyph unicode="H" horiz-adv-x="675" d="M514 0v313h-352v-313h-77v692h77v-310h352v310h76v-692h-76z" />
<glyph unicode="I" horiz-adv-x="246" d="M85 0v692h77v-692h-77z" />
<glyph unicode="J" horiz-adv-x="289" d="M18 -72v69q72 0 92.5 17.5t20.5 91.5v586h76l1 -597q0 -84 -28 -123q-23 -33 -91 -41q-27 -3 -71 -3z" />
<glyph unicode="K" horiz-adv-x="569" d="M162 0h-77v692h77v-322l114 4l177 318h88l-198 -348l209 -344h-91l-184 306l-115 -4v-302z" />
<glyph unicode="L" horiz-adv-x="479" d="M466 0h-381v692h77v-623h304v-69z" />
<glyph unicode="M" horiz-adv-x="840" d="M85 0v692h139l196 -585l196 585h139v-692h-77v610h-14l-201 -585h-86l-201 585h-14v-610h-77z" />
<glyph unicode="N" horiz-adv-x="676" d="M85 0v692h144l265 -624h21v624h76v-692h-141l-269 624h-19v-624h-77z" />
<glyph unicode="O" horiz-adv-x="660" d="M176 124.5q40 -66.5 154.5 -66.5t154 65t39.5 218t-41 223t-153.5 70t-153 -69.5t-40.5 -221.5t40 -218.5zM545 73q-59 -83 -214.5 -83t-214.5 85t-59 267.5t60 271t213 88.5t213.5 -88t60.5 -273t-59 -268z" />
<glyph unicode="P" horiz-adv-x="593" d="M338 233h-176v-233h-77v692h253q113 0 167 -55t54 -168q0 -236 -221 -236zM162 301h175q143 0 143 168q0 80 -34 117.5t-109 37.5h-175v-323z" />
<glyph unicode="Q" horiz-adv-x="660" d="M330 -10q-155 0 -214 85t-59 267.5t60 271t213 88.5t213.5 -88t60.5 -271q0 -127 -26 -203.5t-89 -113.5l85 -137l-72 -34l-88 144q-33 -9 -84 -9zM176 124.5q40 -66.5 154.5 -66.5t154 64.5t39.5 218t-41 223.5t-153.5 70t-153 -69.5t-40.5 -221.5t40 -218.5z" />
<glyph unicode="R" horiz-adv-x="616" d="M162 265v-265h-77v692h256q110 0 165.5 -51t55.5 -159q0 -160 -127 -201l133 -281h-85l-125 265h-196zM342 333q141 0 141 145.5t-142 145.5h-179v-291h180z" />
<glyph unicode="S" horiz-adv-x="542" d="M276 635q-150 0 -150 -113q0 -63 34.5 -86.5t140 -47t149.5 -61t44 -123.5q0 -213 -221 -213q-73 0 -183 17l-36 5l8 64q136 -18 207 -18q148 0 148 139q0 56 -32.5 80.5t-121.5 41.5q-117 25 -166 64.5t-49 132.5q0 186 223 186q73 0 175 -15l34 -5l-7 -65 q-139 17 -197 17z" />
<glyph unicode="T" horiz-adv-x="526" d="M13 623v69h500v-69h-211v-623h-76v623h-213z" />
<glyph unicode="U" horiz-adv-x="644" d="M157 208q0 -150 162 -150q84 0 126.5 35.5t42.5 114.5v484h76v-482q0 -116 -61.5 -168t-181.5 -52t-180.5 52t-60.5 168v482h77v-484z" />
<glyph unicode="V" horiz-adv-x="582" d="M478 692h80l-181 -692h-172l-181 692h80l159 -624h56z" />
<glyph unicode="W" horiz-adv-x="886" d="M30 692h80l118 -624h29l141 618h90l141 -618h29l118 624h79l-140 -692h-141l-131 591l-131 -591h-141z" />
<glyph unicode="X" horiz-adv-x="558" d="M106 692l175 -296l176 296h83l-213 -351l213 -341h-87l-174 287l-177 -287h-83l214 339l-214 353h87z" />
<glyph unicode="Y" horiz-adv-x="538" d="M308 0h-77v291l-221 401h87l172 -324l172 324h87l-220 -401v-291z" />
<glyph unicode="Z" horiz-adv-x="536" d="M43 624v68h450v-90l-362 -514v-20h362v-69h-450v89l361 514v22h-361z" />
<glyph unicode="[" horiz-adv-x="329" d="M289 749v-67h-134v-738h134v-67h-210v872h210z" />
<glyph unicode="\" horiz-adv-x="436" d="M405 14l-67 -28l-308 692l67 29z" />
<glyph unicode="]" horiz-adv-x="329" d="M40 682v67h210v-872h-210v67h134v738h-134z" />
<glyph unicode="^" d="M421 319l-143 267l-140 -267h-80l185 341h67l191 -341h-80z" />
<glyph unicode="_" horiz-adv-x="632" d="M102 -92h428v-66h-428v66z" />
<glyph unicode="`" horiz-adv-x="236" d="M25 740l216 -98l-21 -51l-221 85z" />
<glyph unicode="a" horiz-adv-x="501" d="M425 349v-256q3 -37 58 -44l-3 -59q-79 0 -119 40q-90 -40 -180 -40q-69 0 -105 39t-36 112t37 107.5t116 42.5l157 15v43q0 51 -22 73t-60 22q-80 0 -166 -10l-31 -3l-3 57q110 22 195 22t123.5 -39t38.5 -122zM117 145q0 -92 76 -92q68 0 134 23l23 8v165l-148 -14 q-45 -4 -65 -26t-20 -64z" />
<glyph unicode="b" horiz-adv-x="526" d="M293 510q101 0 141.5 -57.5t40.5 -201.5t-50.5 -202.5t-185.5 -58.5q-42 0 -139 8l-28 3v717h74v-243q76 35 147 35zM238 57q98 0 129.5 44.5t31.5 150.5t-25 148.5t-89 42.5q-58 0 -119 -22l-20 -7v-352q65 -5 92 -5z" />
<glyph unicode="c" horiz-adv-x="436" d="M259 510q33 0 112 -12l24 -3l-3 -61q-80 9 -118 9q-85 0 -115.5 -40.5t-30.5 -150t28.5 -152.5t118.5 -43l118 9l3 -62q-93 -14 -139 -14q-117 0 -161.5 60t-44.5 202.5t48 200t160 57.5z" />
<glyph unicode="d" horiz-adv-x="529" d="M456 718v-718h-74v34q-77 -44 -152 -44q-40 0 -70 10t-56 36q-54 54 -54 199.5t48.5 210t160.5 64.5q58 0 123 -13v221h74zM196 64q18 -7 46.5 -7t64 9.5t55.5 18.5l20 9v339q-64 12 -119 12q-76 0 -106 -48.5t-30 -150.5q0 -116 35 -156q16 -19 34 -26z" />
<glyph unicode="e" horiz-adv-x="505" d="M412 62l29 3l2 -59q-114 -16 -195 -16q-108 0 -153 62.5t-45 194.5q0 263 209 263q101 0 151 -56.5t50 -177.5l-4 -57h-330q0 -83 30 -123t104.5 -40t151.5 6zM385 279q0 92 -29.5 130t-96 38t-100 -40t-34.5 -128h260z" />
<glyph unicode="f" horiz-adv-x="331" d="M168 435v-435h-75v435h-63v65h63v45q0 106 30 144.5t105 38.5l101 -7l-1 -62q-56 2 -92.5 2t-52 -24t-15.5 -93v-44h145v-65h-145z" />
<glyph unicode="g" horiz-adv-x="514" d="M265 -235q-120 0 -167.5 32.5t-47.5 118.5q0 41 18 66.5t61 57.5q-28 19 -28 69q0 17 25 63l9 16q-79 36 -79 151q0 171 187 171q48 0 89 -10l15 -3l148 4v-64l-95 2q33 -33 33 -100q0 -94 -46.5 -131.5t-146.5 -37.5q-27 0 -49 4q-18 -44 -18 -57q0 -31 19 -38.5 t115 -7.5t138 -30t42 -117q0 -159 -222 -159zM124 -78q0 -53 29.5 -73.5t109 -20.5t114 22t34.5 73.5t-25 66.5t-98 15l-108 5q-33 -24 -44.5 -42t-11.5 -46zM157.5 255q26.5 -25 87.5 -25t87 25t26 84.5t-26 84.5t-87 25t-87.5 -25.5t-26.5 -84.5t26.5 -84z" />
<glyph unicode="h" horiz-adv-x="537" d="M147 0h-75v718h75v-246q80 38 154 38q100 0 134.5 -54t34.5 -192v-264h-75v262q0 104 -20.5 142.5t-86.5 38.5q-63 0 -122 -23l-19 -7v-413z" />
<glyph unicode="i" horiz-adv-x="219" d="M72 0v500h75v-500h-75zM72 613v87h75v-87h-75z" />
<glyph unicode="j" horiz-adv-x="219" d="M73 13v487h74v-488q0 -92 -33 -138t-133 -97l-30 58q78 45 100 76.5t22 101.5zM73 613v87h74v-87h-74z" />
<glyph unicode="k" horiz-adv-x="479" d="M147 0h-75v718h75v-424l78 4l143 202h85l-163 -230l170 -270h-85l-149 232l-79 -3v-229z" />
<glyph unicode="l" horiz-adv-x="231" d="M78 0v718h75v-718h-75z" />
<glyph unicode="m" horiz-adv-x="836" d="M147 0h-75v500h74v-35q75 45 146 45q93 0 130 -51q33 20 85.5 35.5t92.5 15.5q100 0 134.5 -53.5t34.5 -192.5v-264h-75v262q0 104 -20 142.5t-85 38.5q-33 0 -68 -9.5t-54 -18.5l-19 -9q13 -33 13 -146v-260h-75v258q0 108 -19.5 146.5t-85.5 38.5q-32 0 -65.5 -9.5 t-51.5 -18.5l-17 -9v-406z" />
<glyph unicode="n" horiz-adv-x="537" d="M147 0h-75v500h74v-35q81 45 155 45q100 0 134.5 -54t34.5 -192v-264h-74v262q0 104 -20.5 142.5t-87.5 38.5q-32 0 -67.5 -9.5t-54.5 -18.5l-19 -9v-406z" />
<glyph unicode="o" horiz-adv-x="528" d="M99 449q49 61 165.5 61t165 -61t48.5 -198t-46 -199t-168 -62t-168 62t-46 199t49 198zM153.5 99q26.5 -44 111 -44t110.5 43.5t26 153.5t-29 151.5t-107.5 41.5t-108 -41.5t-29.5 -151t26.5 -153.5z" />
<glyph unicode="p" horiz-adv-x="527" d="M72 -222v722h74v-36q76 46 150 46q95 0 138 -60.5t43 -199.5t-50.5 -199.5t-167.5 -60.5q-61 0 -112 11v-223h-75zM285 443q-30 0 -64.5 -10t-54.5 -20l-19 -10v-337q71 -11 108 -11q81 0 113.5 46t32.5 150t-29.5 148t-86.5 44z" />
<glyph unicode="q" horiz-adv-x="526" d="M238 -10q-101 0 -144.5 59t-43.5 200t52 201t184 60l167 -10v-722h-74v245q-70 -33 -141 -33zM287 445q-94 0 -127.5 -47t-33.5 -149.5t27.5 -147t92.5 -44.5q58 0 115 22l18 7v353q-62 6 -92 6z" />
<glyph unicode="r" horiz-adv-x="346" d="M72 0v500h74v-68q87 60 183 79v-76q-42 -8 -87.5 -25t-69.5 -30l-25 -13v-367h-75z" />
<glyph unicode="s" horiz-adv-x="465" d="M229 443q-109 0 -109 -76q0 -35 25 -49.5t114 -30t126 -43.5t37 -105t-49.5 -113t-144.5 -36q-62 0 -146 14l-30 5l4 65q114 -17 172 -17t88.5 18.5t30.5 62t-26 59t-114 29.5t-125 41t-37 100t51.5 108t128.5 35q61 0 153 -14l28 -5l-2 -64q-111 16 -175 16z" />
<glyph unicode="t" horiz-adv-x="351" d="M331 435h-159v-239q0 -86 12.5 -113t59.5 -27l89 6l5 -62q-67 -11 -102 -11q-78 0 -108 38t-30 145v263h-71v65h71v153h74v-153h159v-65z" />
<glyph unicode="u" horiz-adv-x="531" d="M384 500h75v-500h-75v35q-75 -45 -148 -45q-102 0 -135.5 53t-33.5 196v261h75v-260q0 -109 19 -146t86 -37q33 0 67 9.5t52 18.5l18 9v406z" />
<glyph unicode="v" horiz-adv-x="481" d="M25 500h80l117 -435h37l121 435h77l-141 -500h-150z" />
<glyph unicode="w" horiz-adv-x="762" d="M31 500h75l101 -435h16l119 425h78l119 -425h17l100 435h75l-119 -500h-125l-106 391l-106 -391h-125z" />
<glyph unicode="x" horiz-adv-x="452" d="M22 500h82l122 -196l122 196h82l-159 -248l158 -252h-82l-121 195l-122 -195h-82l157 250z" />
<glyph unicode="y" horiz-adv-x="483" d="M25 500h75l125 -435h33l126 435h75l-208 -722h-75l65 222h-74z" />
<glyph unicode="z" horiz-adv-x="455" d="M42 433v67h371v-67l-280 -366h280v-67h-371v67l280 366h-280z" />
<glyph unicode="{" horiz-adv-x="352" d="M197 575l7 -123q0 -64 -21.5 -93t-83.5 -47q61 -18 83 -48.5t22 -93.5l-7 -116q0 -56 24.5 -84.5t90.5 -32.5l-2 -65q-101 4 -143.5 44.5t-42.5 130.5l7 122q0 43 -25 69.5t-82 42.5v61q57 13 82 39.5t25 69.5l-7 128q0 92 42.5 132t144.5 44l1 -65q-66 -4 -90.5 -31.5 t-24.5 -83.5z" />
<glyph unicode="|" horiz-adv-x="230" d="M78 -222v940h74v-940h-74z" />
<glyph unicode="}" horiz-adv-x="352" d="M155 54l-7 116q0 63 22 93.5t83 48.5q-62 18 -83.5 47t-21.5 93l7 123q0 56 -24.5 83.5t-90.5 31.5l1 65q102 -4 144.5 -44t42.5 -132l-7 -128q0 -43 25 -69.5t82 -39.5v-61q-57 -16 -82 -42.5t-25 -69.5l7 -122q0 -90 -42.5 -130.5t-143.5 -44.5l-2 65q66 4 90.5 32.5 t24.5 84.5z" />
<glyph unicode="~" d="M379 195q-27 0 -102.5 31t-94 31t-44.5 -11.5t-43 -23.5l-17 -12l-11 62q62 53 116 53q29 0 103.5 -30.5t93.5 -30.5q35 0 88 34l17 12l10 -61q-63 -54 -116 -54z" />
<glyph unicode="&#xa1;" horiz-adv-x="228" d="M154 500v-112h-82v112h82zM148 272l5 -464h-79l5 464h69z" />
<glyph unicode="&#xa2;" d="M278 -81v117q-93 5 -135 52t-42 158t43.5 162.5t133.5 56.5v118h65v-121q39 0 85 -9l16 -3l-3 -60q-79 7 -129 7q-74 0 -103.5 -33.5t-29.5 -115.5t29.5 -113.5t109.5 -31.5l124 7l3 -61q-60 -9 -102 -11v-119h-65z" />
<glyph unicode="&#xa3;" d="M445 592q-80 11 -126.5 11t-63.5 -28t-17 -108v-50h171v-65h-171v-286h154l77 16l13 -64l-82 -18h-320v66h85v286h-67v65h67v55q0 117 33.5 157.5t114.5 40.5q52 0 111 -12l22 -5z" />
<glyph unicode="&#xa4;" d="M379 101q-46 -30 -100 -30t-98 30l-70 -71l-53 53l70 70q-29 45 -29 98t29 100l-70 70l53 53l70 -71q44 30 98 30t100 -30l70 71l53 -53l-71 -70q30 -46 30 -100t-30 -98l71 -70l-53 -53zM280 137q47 0 81 34t34 81t-34 81t-81 34t-81 -34t-34 -81t34 -81t81 -34z" />
<glyph unicode="&#xa5;" d="M57 315v65h140l-174 280h86l172 -270l170 270h86l-171 -280h136v-65h-174l-8 -19v-68h183v-65h-183v-163h-77v163h-185v65h185v68l-8 19h-178z" />
<glyph unicode="&#xa6;" horiz-adv-x="234" d="M80 718h74v-374h-74v374zM80 156h74v-378h-74v378z" />
<glyph unicode="&#xa7;" horiz-adv-x="499" d="M418 571q-109 15 -165 15t-85 -22t-29 -70t30.5 -66.5t115.5 -34.5t121.5 -45t36.5 -97.5t-44 -132.5q37 -32 37 -100q0 -168 -191 -168q-64 0 -145 12l-30 5l7 63q109 -15 164 -15q123 0 123 99q0 45 -29 60.5t-117 32t-127 47.5t-39 101q0 39 21 76t37 50 q-44 32 -44 111q0 159 192 159q51 0 138 -13l26 -4zM160 354q-33 -55 -33 -93.5t25 -57.5t92.5 -31.5t97.5 -25.5q5 7 15 36.5t10 65.5t-25.5 54t-90 29.5t-91.5 22.5z" />
<glyph unicode="&#xa8;" horiz-adv-x="236" d="M-12 621v90h72v-90h-72zM188 621v90h73v-90h-73z" />
<glyph unicode="&#xa9;" horiz-adv-x="645" d="M134.5 240q-75.5 79 -75.5 194.5t75 194t189 78.5t188.5 -79.5t74.5 -194.5t-74 -193.5t-188 -78.5t-189.5 79zM103 434q0 -96 62.5 -162.5t156.5 -66.5t157 66.5t63 162.5t-63 163t-156.5 67t-156.5 -67t-63 -163zM326 276q-65 0 -91 37.5t-26 120t26.5 120t91.5 37.5 q37 0 67 -7l10 -3l-4 -53q-38 6 -72.5 6t-46 -22.5t-11.5 -76t12.5 -78t47.5 -24.5l70 7l4 -52q-37 -12 -78 -12z" />
<glyph unicode="&#xaa;" horiz-adv-x="386" d="M302 560v-137q9 -11 30 -17l-2 -49q-38 0 -54 6t-29 20q-55 -26 -101 -26t-69 25t-23 67q0 80 96 86l88 6v22q0 22 -12 31t-37 9l-115 -7l-2 48q69 15 123 15t80.5 -22t26.5 -77zM120 450q0 -39 36 -39q29 0 70 13l12 4v67l-77 -5q-22 -2 -31.5 -11t-9.5 -29z" />
<glyph unicode="&#xab;" horiz-adv-x="539" d="M234 344l-124 -94l124 -105v-78l-189 151v60l189 143v-77zM472 344l-125 -94l125 -105v-78l-190 151v60l190 143v-77z" />
<glyph unicode="&#xac;" d="M65 342h428v-225h-70v157h-358v68z" />
<glyph unicode="&#xad;" horiz-adv-x="438" d="M68 240v70h303v-70h-303z" />
<glyph unicode="&#xae;" horiz-adv-x="645" d="M134.5 240q-75.5 79 -75.5 194.5t75 194t189 78.5t188.5 -79t74.5 -194.5t-74 -194t-188 -78.5t-189.5 79zM166 597q-63 -67 -63 -162.5t63 -162.5t156.5 -67t156.5 66.5t63 162.5t-63 163t-156.5 67t-156.5 -67zM271 389v-107h-58v303h110q57 0 85.5 -22t28.5 -66 t-11.5 -65.5t-39.5 -34.5l55 -115h-61l-50 107h-59zM270 538v-102h59q27 0 39 12t12 39q0 51 -62 51h-48z" />
<glyph unicode="&#xaf;" horiz-adv-x="236" d="M-14 624v59h275v-59h-275z" />
<glyph unicode="&#xb0;" d="M138 561q0 63 39.5 102t102.5 39t102.5 -39t39.5 -102t-39.5 -102t-102.5 -39t-102.5 39t-39.5 102zM214 628q-25 -26 -25 -67.5t25 -67t66 -25.5t67 25.5t26 67t-26 67.5t-67 26t-66 -26z" />
<glyph unicode="&#xb1;" d="M55 293v68h189v126h70v-126h191v-68h-191v-127h-70v127h-189zM55 106h450v-68h-450v68z" />
<glyph unicode="&#xb2;" horiz-adv-x="280" d="M239 478h-209v55l88 87q51 49 51 83q0 37 -49 37l-85 -9l-2 57q59 12 107 12q95 0 95 -91q0 -33 -14 -57.5t-47 -54.5l-63 -61h128v-58z" />
<glyph unicode="&#xb3;" horiz-adv-x="280" d="M136 800q105 0 105 -84q0 -51 -39 -73q24 -8 35.5 -24.5t11.5 -53.5q0 -97 -106 -97q-43 0 -95 8l-18 2l4 56q57 -8 103.5 -8t46.5 42.5t-47 42.5h-65v53h65q14 0 26 13t12 31q0 34 -45 34l-92 -7l-4 55q59 10 102 10z" />
<glyph unicode="&#xb4;" horiz-adv-x="268" d="M26 642l216 98l26 -64l-221 -85z" />
<glyph unicode="&#xb5;" d="M401 500h75v-500h-75v35q-75 -45 -140.5 -45t-101.5 17v-229h-75v722h75v-260q0 -109 19 -146t86 -37q33 0 67 9.5t52 18.5l18 9v406z" />
<glyph unicode="&#xb6;" horiz-adv-x="593" d="M411 0v627h-116v-627h-67v324h-7q-81 0 -133.5 51t-52.5 132t53 133t134 52h329v-65h-72v-627h-68z" />
<glyph unicode="&#xb7;" horiz-adv-x="217" d="M68 229v114h82v-114h-82z" />
<glyph unicode="&#xb8;" horiz-adv-x="263" d="M221 -127q0 -90 -93 -90q-46 0 -74 5l-12 2l3 48q39 -3 66 -3q44 0 44 38q0 34 -44 34h-42v94h41v-50q59 0 85 -16t26 -62z" />
<glyph unicode="&#xb9;" horiz-adv-x="280" d="M193 790v-312h-64v242l-68 -47l-29 47l101 70h60z" />
<glyph unicode="&#xba;" horiz-adv-x="380" d="M87.5 623q34.5 36 103 36t102 -36t33.5 -113q0 -153 -135 -153q-138 0 -138 153q0 77 34.5 113zM137.5 439.5q15.5 -21.5 53 -21.5t52 21.5t14.5 70.5t-14.5 68.5t-52 19.5t-53 -20t-15.5 -68.5t15.5 -70z" />
<glyph unicode="&#xbb;" horiz-adv-x="539" d="M429 250l-125 94v77l190 -143v-60l-190 -151v78zM192 250l-125 94v77l189 -143v-60l-189 -151v78z" />
<glyph unicode="&#xbc;" horiz-adv-x="534" d="M198 790v-312h-64v242l-68 -47l-29 47l101 70h60zM35 28l392 632l40 -26l-393 -631zM412 -100v48h-141v54l69 210h70l-73 -206h75l5 89h58v-89h27v-58h-27v-48h-63z" />
<glyph unicode="&#xbd;" horiz-adv-x="529" d="M202 790v-312h-64v242l-68 -47l-29 47l101 70h60zM33 28l392 632l40 -26l-393 -631zM486 -100h-209v55l88 87q51 49 51 83q0 37 -49 37l-85 -9l-2 57q59 12 106 12q96 0 96 -91q0 -33 -14 -57.5t-47 -54.5l-63 -61h128v-58z" />
<glyph unicode="&#xbe;" horiz-adv-x="550" d="M155 800q105 0 105 -84q0 -51 -39 -73q24 -8 35.5 -24.5t11.5 -53.5q0 -97 -106 -97q-43 0 -96 8l-17 2l4 56q57 -8 103.5 -8t46.5 42.5t-47 42.5h-65v53h65q14 0 26 13t12 31q0 34 -45 34l-92 -7l-4 55q59 10 102 10zM52 28l392 632l40 -26l-393 -631zM429 -100v48h-141 v54l69 210h70l-73 -206h75l5 89h58v-89h27v-58h-27v-48h-63z" />
<glyph unicode="&#xbf;" horiz-adv-x="441" d="M38 -41q0 65 17.5 98.5t71 78t73.5 71t20 58.5v35h63q13 -27 13 -61t-23 -63.5t-74 -73.5t-68 -70t-17 -72.5t32 -69.5t101 -23q53 0 129 17l26 6l5 -61q-101 -30 -167 -30q-103 0 -152.5 37.5t-49.5 122.5zM294 500v-112h-82v112h82z" />
<glyph unicode="&#xc0;" horiz-adv-x="596" d="M24 0l190 692h168l190 -692h-76l-50 178h-296l-50 -178h-76zM272 626l-105 -379h262l-105 379h-52zM189 931l216 -100l-23 -53l-219 87z" />
<glyph unicode="&#xc1;" horiz-adv-x="596" d="M24 0l190 692h168l190 -692h-76l-50 178h-296l-50 -178h-76zM272 626l-105 -379h262l-105 379h-52zM186 831l216 100l26 -66l-219 -87z" />
<glyph unicode="&#xc2;" horiz-adv-x="596" d="M24 0l190 692h168l190 -692h-76l-50 178h-296l-50 -178h-76zM272 626l-105 -379h262l-105 379h-52zM130 791l134 135h65l134 -135h-84l-82 80l-83 -80h-84z" />
<glyph unicode="&#xc3;" horiz-adv-x="596" d="M24 0l190 692h168l190 -692h-76l-50 178h-296l-50 -178h-76zM272 626l-105 -379h262l-105 379h-52zM374 803q-21 0 -83 27t-75 27q-22 0 -61 -36l-13 -12l-17 56q19 22 45 40t49 18t84 -27t71 -27q20 0 58 35l12 11l18 -56q-47 -56 -88 -56z" />
<glyph unicode="&#xc4;" horiz-adv-x="596" d="M24 0l190 692h168l190 -692h-76l-50 178h-296l-50 -178h-76zM272 626l-105 -379h262l-105 379h-52zM159 808v90h73v-90h-73zM363 808v90h72v-90h-72z" />
<glyph unicode="&#xc5;" horiz-adv-x="596" d="M382 821q33 -28 33 -74t-28 -76l185 -671h-76l-50 178h-296l-50 -178h-76l184 669q-30 28 -30 76t33.5 76t85.5 28t85 -28zM272 626l-105 -379h262l-105 379h-52zM235 744q0 -44 43 -52h37q44 8 44 52q0 25 -16.5 39t-45.5 14q-62 0 -62 -53z" />
<glyph unicode="&#xc6;" horiz-adv-x="876" d="M408 0v175h-251l-58 -175h-79l224 696h585v-73h-345v-230h285v-73h-285v-247h345v-73h-421zM300 623l-122 -374h230l1 374h-109z" />
<glyph unicode="&#xc7;" horiz-adv-x="544" d="M433 -127q0 -90 -93 -90q-46 0 -74 5l-12 2l3 48q39 -3 66 -3q44 0 44 38q0 34 -44 34h-42v84q-129 9 -175.5 93.5t-46.5 259.5q0 93 10.5 155.5t38 110.5t77.5 70t130 22t188 -22l-3 -65q-101 18 -180 18q-110 0 -145.5 -65.5t-35.5 -223.5q0 -79 6.5 -127t25.5 -86.5 t55.5 -55t107 -16.5t166.5 18l3 -66q-92 -20 -181 -21v-39q59 0 85 -16t26 -62z" />
<glyph unicode="&#xc8;" horiz-adv-x="553" d="M85 0v692h421v-68h-344v-239h284v-67h-284v-250h344v-68h-421zM195 931l216 -100l-23 -53l-219 87z" />
<glyph unicode="&#xc9;" horiz-adv-x="553" d="M85 0v692h421v-68h-344v-239h284v-67h-284v-250h344v-68h-421zM176 831l216 100l26 -66l-219 -87z" />
<glyph unicode="&#xca;" horiz-adv-x="553" d="M85 0v692h421v-68h-344v-239h284v-67h-284v-250h344v-68h-421zM127 791l134 135h65l134 -135h-84l-82 80l-83 -80h-84z" />
<glyph unicode="&#xcb;" horiz-adv-x="553" d="M85 0v692h421v-68h-344v-239h284v-67h-284v-250h344v-68h-421zM157 808v90h73v-90h-73zM361 808v90h72v-90h-72z" />
<glyph unicode="&#xcc;" horiz-adv-x="246" d="M85 0v692h77v-692h-77zM6 931l216 -100l-23 -53l-219 87z" />
<glyph unicode="&#xcd;" horiz-adv-x="246" d="M85 0v692h77v-692h-77zM13 831l216 100l26 -66l-219 -87z" />
<glyph unicode="&#xce;" horiz-adv-x="246" d="M85 0v692h77v-692h-77zM-47 791l134 135h65l134 -135h-84l-82 80l-83 -80h-84z" />
<glyph unicode="&#xcf;" horiz-adv-x="246" d="M85 0v692h77v-692h-77zM-15 808v90h73v-90h-73zM189 808v90h72v-90h-72z" />
<glyph unicode="&#xd0;" horiz-adv-x="647" d="M20 311v73h68v312h247q141 0 198.5 -88t57.5 -249q0 -262 -131 -331q-51 -28 -125 -28h-247v311h-68zM511 359q0 194 -89 244q-35 20 -87 20h-171v-239h158v-73h-158v-238h171q176 0 176 286z" />
<glyph unicode="&#xd1;" horiz-adv-x="676" d="M85 0v692h144l265 -624h21v624h76v-692h-141l-269 624h-19v-624h-77zM416 803q-21 0 -83 27t-75 27q-22 0 -61 -36l-13 -12l-17 56q19 22 45 40t49 18t84 -27t71 -27q20 0 58 35l12 11l18 -56q-47 -56 -88 -56z" />
<glyph unicode="&#xd2;" horiz-adv-x="660" d="M176 124.5q40 -66.5 154.5 -66.5t154 65t39.5 218t-41 223t-153.5 70t-153 -69.5t-40.5 -221.5t40 -218.5zM545 73q-59 -83 -214.5 -83t-214.5 85t-59 267.5t60 271t213 88.5t213.5 -88t60.5 -273t-59 -268zM227 931l216 -100l-23 -53l-219 87z" />
<glyph unicode="&#xd3;" horiz-adv-x="660" d="M176 124.5q40 -66.5 154.5 -66.5t154 65t39.5 218t-41 223t-153.5 70t-153 -69.5t-40.5 -221.5t40 -218.5zM545 73q-59 -83 -214.5 -83t-214.5 85t-59 267.5t60 271t213 88.5t213.5 -88t60.5 -273t-59 -268zM198 831l216 100l26 -66l-219 -87z" />
<glyph unicode="&#xd4;" horiz-adv-x="660" d="M176 124.5q40 -66.5 154.5 -66.5t154 65t39.5 218t-41 223t-153.5 70t-153 -69.5t-40.5 -221.5t40 -218.5zM545 73q-59 -83 -214.5 -83t-214.5 85t-59 267.5t60 271t213 88.5t213.5 -88t60.5 -273t-59 -268zM164 791l134 135h65l134 -135h-84l-82 80l-83 -80h-84z" />
<glyph unicode="&#xd5;" horiz-adv-x="660" d="M176 124.5q40 -66.5 154.5 -66.5t154 65t39.5 218t-41 223t-153.5 70t-153 -69.5t-40.5 -221.5t40 -218.5zM545 73q-59 -83 -214.5 -83t-214.5 85t-59 267.5t60 271t213 88.5t213.5 -88t60.5 -273t-59 -268zM408 803q-21 0 -83 27t-75 27q-22 0 -61 -36l-13 -12l-17 56 q19 22 45 40t49 18t84 -27t71 -27q20 0 58 35l12 11l18 -56q-47 -56 -88 -56z" />
<glyph unicode="&#xd6;" horiz-adv-x="660" d="M176 124.5q40 -66.5 154.5 -66.5t154 65t39.5 218t-41 223t-153.5 70t-153 -69.5t-40.5 -221.5t40 -218.5zM545 73q-59 -83 -214.5 -83t-214.5 85t-59 267.5t60 271t213 88.5t213.5 -88t60.5 -273t-59 -268zM192 808v90h73v-90h-73zM396 808v90h72v-90h-72z" />
<glyph unicode="&#xd7;" d="M120 459l160 -162l161 163l48 -48l-163 -161l163 -161l-48 -48l-161 162l-161 -162l-48 48l162 161l-162 160z" />
<glyph unicode="&#xd8;" horiz-adv-x="660" d="M330 -10q-69 0 -120 17l-57 -123l-57 26l59 126q-53 40 -75.5 115t-22.5 192q0 182 60 270.5t213 88.5q75 0 126 -21l56 118l58 -24l-59 -127q93 -78 93 -305q0 -187 -59 -270t-215 -83zM176.5 564.5q-40.5 -69.5 -40.5 -234t52 -222.5l238 507q-38 19 -96 19 q-113 0 -153.5 -69.5zM330 58q115 0 154.5 65t39.5 226t-48 225l-235 -502q34 -14 89 -14z" />
<glyph unicode="&#xd9;" horiz-adv-x="644" d="M157 208q0 -150 162 -150q84 0 126.5 35.5t42.5 114.5v484h76v-482q0 -116 -61.5 -168t-181.5 -52t-180.5 52t-60.5 168v482h77v-484zM222 931l216 -100l-23 -53l-219 87z" />
<glyph unicode="&#xda;" horiz-adv-x="644" d="M157 208q0 -150 162 -150q84 0 126.5 35.5t42.5 114.5v484h76v-482q0 -116 -61.5 -168t-181.5 -52t-180.5 52t-60.5 168v482h77v-484zM199 831l216 100l26 -66l-219 -87z" />
<glyph unicode="&#xdb;" horiz-adv-x="644" d="M157 208q0 -150 162 -150q84 0 126.5 35.5t42.5 114.5v484h76v-482q0 -116 -61.5 -168t-181.5 -52t-180.5 52t-60.5 168v482h77v-484zM155 791l134 135h65l134 -135h-84l-82 80l-83 -80h-84z" />
<glyph unicode="&#xdc;" horiz-adv-x="644" d="M157 208q0 -150 162 -150q84 0 126.5 35.5t42.5 114.5v484h76v-482q0 -116 -61.5 -168t-181.5 -52t-180.5 52t-60.5 168v482h77v-484zM184 808v90h73v-90h-73zM388 808v90h72v-90h-72z" />
<glyph unicode="&#xdd;" horiz-adv-x="538" d="M308 0h-77v291l-221 401h87l172 -324l172 324h87l-220 -401v-291zM167 831l216 100l26 -66l-219 -87z" />
<glyph unicode="&#xde;" horiz-adv-x="599" d="M337 195q144 0 144 166q0 78 -34.5 113.5t-109.5 35.5h-175v-315h175zM338 121h-176v-121h-77v696h77v-112h176q113 0 167.5 -55t54.5 -169.5t-55.5 -176.5t-166.5 -62z" />
<glyph unicode="&#xdf;" horiz-adv-x="574" d="M147 0h-75v545q0 101 45.5 142t149.5 41t149.5 -34t45.5 -110q0 -53 -19 -81.5t-59 -46t-51.5 -26.5t-11.5 -24.5t16.5 -28.5t81.5 -45t92.5 -64t27.5 -91q0 -102 -45.5 -144.5t-155.5 -42.5q-50 0 -110 11l-21 4l3 63q85 -11 122 -11q72 0 100.5 26.5t28.5 75t-21 70.5 t-86 53t-88 55t-23 61.5t18.5 58t57.5 37.5t52 35.5t13 57t-25.5 56.5t-92 18t-93 -26t-26.5 -99v-536z" />
<glyph unicode="&#xe0;" horiz-adv-x="501" d="M425 349v-256q3 -37 58 -44l-3 -59q-79 0 -119 40q-90 -40 -180 -40q-69 0 -105 39t-36 112t37 107.5t116 42.5l157 15v43q0 51 -22 73t-60 22q-80 0 -166 -10l-31 -3l-3 57q110 22 195 22t123.5 -39t38.5 -122zM117 145q0 -92 76 -92q68 0 134 23l23 8v165l-148 -14 q-45 -4 -65 -26t-20 -64zM147 740l216 -98l-21 -51l-221 85z" />
<glyph unicode="&#xe1;" horiz-adv-x="501" d="M425 349v-256q3 -37 58 -44l-3 -59q-79 0 -119 40q-90 -40 -180 -40q-69 0 -105 39t-36 112t37 107.5t116 42.5l157 15v43q0 51 -22 73t-60 22q-80 0 -166 -10l-31 -3l-3 57q110 22 195 22t123.5 -39t38.5 -122zM117 145q0 -92 76 -92q68 0 134 23l23 8v165l-148 -14 q-45 -4 -65 -26t-20 -64zM118 642l216 98l26 -64l-221 -85z" />
<glyph unicode="&#xe2;" horiz-adv-x="501" d="M425 349v-256q3 -37 58 -44l-3 -59q-79 0 -119 40q-90 -40 -180 -40q-69 0 -105 39t-36 112t37 107.5t116 42.5l157 15v43q0 51 -22 73t-60 22q-80 0 -166 -10l-31 -3l-3 57q110 22 195 22t123.5 -39t38.5 -122zM117 145q0 -92 76 -92q68 0 134 23l23 8v165l-148 -14 q-45 -4 -65 -26t-20 -64zM95 592l123 140h51l124 -140h-75l-73 87l-75 -87h-75z" />
<glyph unicode="&#xe3;" horiz-adv-x="501" d="M425 349v-256q3 -37 58 -44l-3 -59q-79 0 -119 40q-90 -40 -180 -40q-69 0 -105 39t-36 112t37 107.5t116 42.5l157 15v43q0 51 -22 73t-60 22q-80 0 -166 -10l-31 -3l-3 57q110 22 195 22t123.5 -39t38.5 -122zM117 145q0 -92 76 -92q68 0 134 23l23 8v165l-148 -14 q-45 -4 -65 -26t-20 -64zM307 612q-21 0 -72.5 23.5t-64.5 23.5q-23 0 -62 -30l-13 -10l-18 48q50 52 91 52q21 0 73 -23t63 -23q22 0 60 29l12 9l17 -48q-46 -51 -86 -51z" />
<glyph unicode="&#xe4;" horiz-adv-x="501" d="M425 349v-256q3 -37 58 -44l-3 -59q-79 0 -119 40q-90 -40 -180 -40q-69 0 -105 39t-36 112t37 107.5t116 42.5l157 15v43q0 51 -22 73t-60 22q-80 0 -166 -10l-31 -3l-3 57q110 22 195 22t123.5 -39t38.5 -122zM117 145q0 -92 76 -92q68 0 134 23l23 8v165l-148 -14 q-45 -4 -65 -26t-20 -64zM105 621v90h72v-90h-72zM305 621v90h73v-90h-73z" />
<glyph unicode="&#xe5;" horiz-adv-x="501" d="M425 349v-256q3 -37 58 -44l-3 -59q-79 0 -119 40q-90 -40 -180 -40q-69 0 -105 39t-36 112t37 107.5t116 42.5l157 15v43q0 51 -22 73t-60 22q-80 0 -166 -10l-31 -3l-3 57q110 22 195 22t123.5 -39t38.5 -122zM117 145q0 -92 76 -92q68 0 134 23l23 8v165l-148 -14 q-45 -4 -65 -26t-20 -64zM151 645q0 44 30 74t73.5 30t73.5 -30t30 -74t-29.5 -74t-73.5 -30t-74 30t-30 74zM216 684.5q-16 -16.5 -16 -40t16 -40t39 -16.5t39 16.5t16 40t-16 40t-39 16.5t-39 -16.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="804" d="M710 65l29 2l2 -61q-113 -16 -203 -16t-138 54l-32 -14q-87 -40 -192 -40q-67 0 -101.5 39.5t-34.5 116.5t41 107.5t140 40.5l129 12v41q0 91 -87 91q-66 0 -159 -8l-31 -2l-3 67q119 15 197 15q102 0 135 -73q48 73 152.5 73t154.5 -56.5t50 -177.5l-4 -58h-330 q0 -82 30 -121t91.5 -39t163.5 7zM188 55q46 0 105 14.5t74 22.5q-19 51 -19 153l-146 -12q-44 -4 -64.5 -25t-20.5 -63q0 -90 71 -90zM425 279h259q0 89 -29.5 126.5t-96 37.5t-100 -39t-33.5 -125z" />
<glyph unicode="&#xe7;" horiz-adv-x="436" d="M360 -127q0 -90 -93 -90q-46 0 -75 5l-11 2l3 48q39 -3 66 -3q44 0 44 38q0 34 -44 34h-42v86q-88 10 -122.5 71t-34.5 196t48 192.5t160 57.5q33 0 112 -12l24 -3l-3 -61q-80 9 -118 9q-85 0 -115.5 -40.5t-30.5 -150t28.5 -152.5t118.5 -43l118 9l3 -62 q-93 -14 -139 -14h-8v-39q59 0 85 -16t26 -62z" />
<glyph unicode="&#xe8;" horiz-adv-x="505" d="M412 62l29 3l2 -59q-114 -16 -195 -16q-108 0 -153 62.5t-45 194.5q0 263 209 263q101 0 151 -56.5t50 -177.5l-4 -57h-330q0 -83 30 -123t104.5 -40t151.5 6zM385 279q0 92 -29.5 130t-96 38t-100 -40t-34.5 -128h260zM147 741l216 -98l-21 -51l-221 85z" />
<glyph unicode="&#xe9;" horiz-adv-x="505" d="M412 62l29 3l2 -59q-114 -16 -195 -16q-108 0 -153 62.5t-45 194.5q0 263 209 263q101 0 151 -56.5t50 -177.5l-4 -57h-330q0 -83 30 -123t104.5 -40t151.5 6zM385 279q0 92 -29.5 130t-96 38t-100 -40t-34.5 -128h260zM145 642l216 98l26 -64l-221 -85z" />
<glyph unicode="&#xea;" horiz-adv-x="505" d="M412 62l29 3l2 -59q-114 -16 -195 -16q-108 0 -153 62.5t-45 194.5q0 263 209 263q101 0 151 -56.5t50 -177.5l-4 -57h-330q0 -83 30 -123t104.5 -40t151.5 6zM385 279q0 92 -29.5 130t-96 38t-100 -40t-34.5 -128h260zM112 592l123 140h51l124 -140h-75l-73 87l-75 -87 h-75z" />
<glyph unicode="&#xeb;" horiz-adv-x="505" d="M412 62l29 3l2 -59q-114 -16 -195 -16q-108 0 -153 62.5t-45 194.5q0 263 209 263q101 0 151 -56.5t50 -177.5l-4 -57h-330q0 -83 30 -123t104.5 -40t151.5 6zM385 279q0 92 -29.5 130t-96 38t-100 -40t-34.5 -128h260zM120 621v90h72v-90h-72zM320 621v90h73v-90h-73z " />
<glyph unicode="&#xec;" horiz-adv-x="219" d="M72 0v500h75v-500h-75zM-34 740l216 -98l-21 -51l-221 85z" />
<glyph unicode="&#xed;" horiz-adv-x="219" d="M38 642l216 98l26 -64l-221 -85zM72 500h75v-500h-75v500z" />
<glyph unicode="&#xee;" horiz-adv-x="219" d="M72 500h75v-500h-75v500zM-42 592l123 140h51l124 -140h-75l-73 87l-75 -87h-75z" />
<glyph unicode="&#xef;" horiz-adv-x="219" d="M72 500h75v-500h-75v500zM-31 621v90h72v-90h-72zM169 621v90h73v-90h-73z" />
<glyph unicode="&#xf0;" horiz-adv-x="548" d="M342 643q154 -98 154 -291.5t-54 -277t-177 -83.5q-107 0 -165 57.5t-58 161t55 162t155 58.5q65 0 140 -27l25 -9q-7 130 -137 207l-122 -81l-33 47l95 64q-54 23 -124 41l15 56q100 -20 173 -54l101 68l34 -47zM418 327q-90 36 -158 36t-103.5 -41.5t-35.5 -111.5 t36.5 -110.5t113 -40.5t111.5 59t36 209z" />
<glyph unicode="&#xf1;" horiz-adv-x="537" d="M72 0v500h74v-35l22 11q23 12 61.5 23t71.5 11q100 0 134.5 -54t34.5 -192v-264h-74v262q0 104 -20.5 142.5t-90 38.5t-138.5 -37v-406h-75zM349 612q-21 0 -72.5 23.5t-64.5 23.5q-23 0 -62 -30l-13 -10l-18 48q50 52 91 52q21 0 73 -23t63 -23q22 0 60 29l12 9l17 -48 q-46 -51 -86 -51z" />
<glyph unicode="&#xf2;" horiz-adv-x="528" d="M99 449q49 61 165.5 61t165 -61t48.5 -198t-46 -199t-168 -62t-168 62t-46 199t49 198zM153.5 99q26.5 -44 111 -44t110.5 43.5t26 153.5t-29 151.5t-107.5 41.5t-108 -41.5t-29.5 -151t26.5 -153.5zM167 740l216 -98l-21 -51l-221 85z" />
<glyph unicode="&#xf3;" horiz-adv-x="528" d="M429.5 449q48.5 -61 48.5 -198t-46 -199t-168 -62t-168 62t-46 199t49 198t165.5 61t165 -61zM375 98.5q26 43.5 26 153.5t-29 151.5t-107.5 41.5t-108 -41.5t-29.5 -151t26.5 -153.5t111 -44t110.5 43.5zM154 642l216 98l26 -64l-221 -85z" />
<glyph unicode="&#xf4;" horiz-adv-x="528" d="M429.5 449q48.5 -61 48.5 -198t-46 -199t-168 -62t-168 62t-46 199t49 198t165.5 61t165 -61zM375 98.5q26 43.5 26 153.5t-29 151.5t-107.5 41.5t-108 -41.5t-29.5 -151t26.5 -153.5t111 -44t110.5 43.5zM113 592l123 140h51l124 -140h-75l-73 87l-75 -87h-75z" />
<glyph unicode="&#xf5;" horiz-adv-x="528" d="M429.5 449q48.5 -61 48.5 -198t-46 -199t-168 -62t-168 62t-46 199t49 198t165.5 61t165 -61zM375 98.5q26 43.5 26 153.5t-29 151.5t-107.5 41.5t-108 -41.5t-29.5 -151t26.5 -153.5t111 -44t110.5 43.5zM335 612q-21 0 -72.5 23.5t-64.5 23.5q-23 0 -62 -30l-13 -10 l-18 48q50 52 91 52q21 0 73 -23t63 -23q22 0 60 29l12 9l17 -48q-46 -51 -86 -51z" />
<glyph unicode="&#xf6;" horiz-adv-x="528" d="M429.5 449q48.5 -61 48.5 -198t-46 -199t-168 -62t-168 62t-46 199t49 198t165.5 61t165 -61zM375 98.5q26 43.5 26 153.5t-29 151.5t-107.5 41.5t-108 -41.5t-29.5 -151t26.5 -153.5t111 -44t110.5 43.5zM125 621v90h72v-90h-72zM325 621v90h73v-90h-73z" />
<glyph unicode="&#xf7;" d="M120 281v73h57v48h206v-48h56v-73h-319zM242 30v101h75v-101h-75z" />
<glyph unicode="&#xf8;" horiz-adv-x="528" d="M264 510q39 0 74 -8l43 104l50 -19l-43 -104q90 -50 90 -227q0 -142 -46 -204t-168 -62q-41 0 -76 8l-43 -110l-50 18l44 109q-49 27 -69 85t-20 156q0 132 49 193t165 61zM264 445q-78 0 -107.5 -41.5t-29.5 -131t8.5 -130.5t33.5 -62l145 359q-21 6 -50 6zM264 55 q85 0 111 43.5t26 162t-42 156.5l-146 -357q18 -5 51 -5z" />
<glyph unicode="&#xf9;" horiz-adv-x="531" d="M384 500h75v-500h-75v35q-75 -45 -148 -45q-102 0 -135.5 53t-33.5 196v261h75v-260q0 -109 19 -146t86 -37q33 0 67 9.5t52 18.5l18 9v406zM140 740l216 -98l-21 -51l-221 85z" />
<glyph unicode="&#xfa;" horiz-adv-x="531" d="M459 500v-500h-75v35l-21 -11q-21 -12 -58 -23t-69 -11q-102 0 -135.5 53t-33.5 196v261h75v-260q0 -109 19 -146t89.5 -37t133.5 37v406h75zM155 642l216 98l26 -64l-221 -85z" />
<glyph unicode="&#xfb;" horiz-adv-x="531" d="M459 500v-500h-75v35l-21 -11q-21 -12 -58 -23t-69 -11q-102 0 -135.5 53t-33.5 196v261h75v-260q0 -109 19 -146t89.5 -37t133.5 37v406h75zM105 592l123 140h51l124 -140h-75l-73 87l-75 -87h-75z" />
<glyph unicode="&#xfc;" horiz-adv-x="531" d="M459 500v-500h-75v35l-21 -11q-21 -12 -58 -23t-69 -11q-102 0 -135.5 53t-33.5 196v261h75v-260q0 -109 19 -146t89.5 -37t133.5 37v406h75zM132 621v90h72v-90h-72zM332 621v90h73v-90h-73z" />
<glyph unicode="&#xfd;" horiz-adv-x="483" d="M25 500h75l125 -435h33l126 435h75l-208 -722h-75l65 222h-74zM154 642l216 98l26 -64l-221 -85z" />
<glyph unicode="&#xfe;" horiz-adv-x="527" d="M244 55q94 0 124.5 45.5t30.5 151t-24.5 148.5t-88.5 43q-58 0 -120 -22l-19 -7v-353q65 -6 97 -6zM293 510q101 0 142 -57.5t41 -200.5t-49.5 -202.5t-178.5 -59.5l-101 7v-219h-75v940h75v-243q75 35 146 35z" />
<glyph unicode="&#xff;" horiz-adv-x="483" d="M25 500h75l125 -435h33l126 435h75l-208 -722h-75l65 222h-74zM104 621v90h72v-90h-72zM304 621v90h73v-90h-73z" />
<glyph unicode="&#x152;" horiz-adv-x="925" d="M878 -1h-413q-92 -9 -143 -9q-153 0 -209 84.5t-56 272t58 273.5t207 86q79 0 141 -10h415v-74h-339v-229h279v-74h-279v-245h339v-75zM332 65q52 0 131 7v550q-105 9 -133 9q-113 0 -153 -65t-40 -219.5t39 -218t156 -63.5z" />
<glyph unicode="&#x153;" horiz-adv-x="857" d="M763 65l29 2l2 -61q-113 -16 -194 -16q-119 0 -162 91q-22 -48 -63 -69.5t-110 -21.5q-123 0 -169 62t-46 199.5t49.5 198t165.5 60.5q68 0 110.5 -24.5t65.5 -80.5q45 105 170 105q101 0 151 -56.5t50 -177.5l-4 -58h-330q0 -82 30 -121t92 -39t163 7zM154 101.5 q27 -44.5 110.5 -44.5t110 45t26.5 149t-31.5 147.5t-107.5 43.5t-105.5 -40.5t-29.5 -148t27 -152zM477 279h260q0 89 -29.5 126.5t-96.5 37.5q-134 0 -134 -164z" />
<glyph unicode="&#x178;" horiz-adv-x="538" d="M308 0h-77v291l-221 401h87l172 -324l172 324h87l-220 -401v-291zM130 808v90h73v-90h-73zM334 808v90h72v-90h-72z" />
<glyph unicode="&#x2c6;" horiz-adv-x="236" d="M-19 592l123 140h51l124 -140h-75l-73 87l-75 -87h-75z" />
<glyph unicode="&#x2dc;" horiz-adv-x="236" d="M200 612q-21 0 -72.5 23.5t-64.5 23.5q-23 0 -62 -30l-13 -10l-18 48q50 52 91 52q21 0 73 -23t64 -23q21 0 58 29l13 9l17 -48q-46 -51 -86 -51z" />
<glyph unicode="&#x2000;" horiz-adv-x="465" />
<glyph unicode="&#x2001;" horiz-adv-x="931" />
<glyph unicode="&#x2002;" horiz-adv-x="465" />
<glyph unicode="&#x2003;" horiz-adv-x="931" />
<glyph unicode="&#x2004;" horiz-adv-x="310" />
<glyph unicode="&#x2005;" horiz-adv-x="232" />
<glyph unicode="&#x2006;" horiz-adv-x="155" />
<glyph unicode="&#x2007;" horiz-adv-x="155" />
<glyph unicode="&#x2008;" horiz-adv-x="116" />
<glyph unicode="&#x2009;" horiz-adv-x="186" />
<glyph unicode="&#x200a;" horiz-adv-x="51" />
<glyph unicode="&#x2010;" horiz-adv-x="438" d="M68 240v70h303v-70h-303z" />
<glyph unicode="&#x2011;" horiz-adv-x="438" d="M68 240v70h303v-70h-303z" />
<glyph unicode="&#x2012;" horiz-adv-x="438" d="M68 240v70h303v-70h-303z" />
<glyph unicode="&#x2013;" horiz-adv-x="631" d="M66 301h500v-66h-500v66z" />
<glyph unicode="&#x2014;" horiz-adv-x="1131" d="M66 301h1000v-66h-1000v66z" />
<glyph unicode="&#x2018;" horiz-adv-x="219" d="M174 708l-44 -219h-79l64 219h59z" />
<glyph unicode="&#x2019;" horiz-adv-x="216" d="M53 489l44 219h79l-64 -219h-59z" />
<glyph unicode="&#x201a;" horiz-adv-x="218" d="M93 22l-9 -43h-16l13 43h12z" />
<glyph unicode="&#x201c;" horiz-adv-x="373" d="M327 708l-44 -219h-79l64 219h59zM174 708l-44 -219h-79l64 219h59z" />
<glyph unicode="&#x201d;" horiz-adv-x="377" d="M53 490l44 219h79l-64 -219h-59zM213 490l44 219h79l-64 -219h-59z" />
<glyph unicode="&#x201e;" horiz-adv-x="359" d="M145 112l-44 -219h-79l64 219h59zM297 112l-44 -219h-79l64 219h59z" />
<glyph unicode="&#x2022;" horiz-adv-x="473" d="M111 106v286h250v-286h-250z" />
<glyph unicode="&#x2026;" horiz-adv-x="725" d="M67 0v114h82v-114h-82zM322 0v114h81v-114h-81zM576 0v114h82v-114h-82z" />
<glyph unicode="&#x202f;" horiz-adv-x="186" />
<glyph unicode="&#x2039;" horiz-adv-x="301" d="M234 344l-124 -94l124 -105v-78l-189 151v60l189 143v-77z" />
<glyph unicode="&#x203a;" horiz-adv-x="301" d="M191 254l-124 94v77l189 -143v-60l-189 -151v78z" />
<glyph unicode="&#x205f;" horiz-adv-x="232" />
<glyph unicode="&#x20ac;" d="M30 378v61h64q12 119 65.5 175t171.5 56q84 0 182 -21l-3 -62q-91 17 -173 17t-119.5 -39t-46.5 -126h286v-61h-290q-1 -16 -1 -50v-45h291v-61h-287q9 -87 46.5 -126.5t120.5 -39.5t173 18l3 -63q-99 -21 -182 -21q-118 0 -171.5 57t-66.5 175h-63v61h60q-1 14 -1 46.5 t1 48.5h-60z" />
<glyph unicode="&#x2122;" horiz-adv-x="677" d="M91 580v50h200v-50h-66v-248h-55v248h-79zM322 331v299h72l66 -208l71 208h70v-299h-52v224l-66 -209h-43l-66 209v-224h-52z" />
<glyph unicode="&#x25fc;" horiz-adv-x="500" d="M0 500h500v-500h-500v500z" />
<hkern u1="&#x22;" u2="&#xf0;" k="13" />
<hkern u1="&#x22;" u2="&#xef;" k="-7" />
<hkern u1="&#x22;" u2="&#xee;" k="-5" />
<hkern u1="&#x22;" u2="&#xec;" k="-17" />
<hkern u1="&#x22;" u2="&#xc6;" k="53" />
<hkern u1="&#x22;" u2="&#x40;" k="12" />
<hkern u1="&#x22;" u2="&#x2f;" k="63" />
<hkern u1="&#x22;" u2="&#x26;" k="28" />
<hkern u1="&#x26;" u2="&#x201d;" k="48" />
<hkern u1="&#x26;" u2="&#x2019;" k="48" />
<hkern u1="&#x26;" u2="&#x178;" k="55" />
<hkern u1="&#x26;" u2="&#x152;" k="7" />
<hkern u1="&#x26;" u2="&#xff;" k="13" />
<hkern u1="&#x26;" u2="&#xfd;" k="13" />
<hkern u1="&#x26;" u2="&#xdd;" k="55" />
<hkern u1="&#x26;" u2="&#xdc;" k="6" />
<hkern u1="&#x26;" u2="&#xdb;" k="6" />
<hkern u1="&#x26;" u2="&#xda;" k="6" />
<hkern u1="&#x26;" u2="&#xd9;" k="6" />
<hkern u1="&#x26;" u2="&#xd8;" k="7" />
<hkern u1="&#x26;" u2="&#xd6;" k="7" />
<hkern u1="&#x26;" u2="&#xd5;" k="7" />
<hkern u1="&#x26;" u2="&#xd4;" k="7" />
<hkern u1="&#x26;" u2="&#xd3;" k="7" />
<hkern u1="&#x26;" u2="&#xd2;" k="7" />
<hkern u1="&#x26;" u2="&#xc7;" k="7" />
<hkern u1="&#x26;" u2="y" k="13" />
<hkern u1="&#x26;" u2="w" k="10" />
<hkern u1="&#x26;" u2="v" k="13" />
<hkern u1="&#x26;" u2="t" k="7" />
<hkern u1="&#x26;" u2="Y" k="55" />
<hkern u1="&#x26;" u2="W" k="20" />
<hkern u1="&#x26;" u2="V" k="33" />
<hkern u1="&#x26;" u2="U" k="6" />
<hkern u1="&#x26;" u2="T" k="41" />
<hkern u1="&#x26;" u2="Q" k="7" />
<hkern u1="&#x26;" u2="O" k="7" />
<hkern u1="&#x26;" u2="G" k="7" />
<hkern u1="&#x26;" u2="C" k="7" />
<hkern u1="&#x26;" u2="&#x27;" k="49" />
<hkern u1="&#x26;" u2="&#x22;" k="49" />
<hkern u1="&#x27;" u2="&#xf0;" k="13" />
<hkern u1="&#x27;" u2="&#xef;" k="-7" />
<hkern u1="&#x27;" u2="&#xee;" k="-5" />
<hkern u1="&#x27;" u2="&#xec;" k="-17" />
<hkern u1="&#x27;" u2="&#xc6;" k="53" />
<hkern u1="&#x27;" u2="&#x40;" k="12" />
<hkern u1="&#x27;" u2="&#x2f;" k="63" />
<hkern u1="&#x27;" u2="&#x26;" k="28" />
<hkern u1="&#x28;" u2="&#x153;" k="18" />
<hkern u1="&#x28;" u2="&#x152;" k="13" />
<hkern u1="&#x28;" u2="&#xff;" k="4" />
<hkern u1="&#x28;" u2="&#xfd;" k="4" />
<hkern u1="&#x28;" u2="&#xfc;" k="14" />
<hkern u1="&#x28;" u2="&#xfb;" k="14" />
<hkern u1="&#x28;" u2="&#xfa;" k="14" />
<hkern u1="&#x28;" u2="&#xf9;" k="14" />
<hkern u1="&#x28;" u2="&#xf8;" k="18" />
<hkern u1="&#x28;" u2="&#xf6;" k="18" />
<hkern u1="&#x28;" u2="&#xf5;" k="18" />
<hkern u1="&#x28;" u2="&#xf4;" k="18" />
<hkern u1="&#x28;" u2="&#xf3;" k="18" />
<hkern u1="&#x28;" u2="&#xf2;" k="18" />
<hkern u1="&#x28;" u2="&#xf1;" k="4" />
<hkern u1="&#x28;" u2="&#xf0;" k="4" />
<hkern u1="&#x28;" u2="&#xef;" k="-15" />
<hkern u1="&#x28;" u2="&#xec;" k="-22" />
<hkern u1="&#x28;" u2="&#xeb;" k="18" />
<hkern u1="&#x28;" u2="&#xea;" k="18" />
<hkern u1="&#x28;" u2="&#xe9;" k="18" />
<hkern u1="&#x28;" u2="&#xe8;" k="18" />
<hkern u1="&#x28;" u2="&#xe7;" k="18" />
<hkern u1="&#x28;" u2="&#xe6;" k="5" />
<hkern u1="&#x28;" u2="&#xe5;" k="5" />
<hkern u1="&#x28;" u2="&#xe4;" k="5" />
<hkern u1="&#x28;" u2="&#xe3;" k="5" />
<hkern u1="&#x28;" u2="&#xe2;" k="5" />
<hkern u1="&#x28;" u2="&#xe1;" k="5" />
<hkern u1="&#x28;" u2="&#xe0;" k="5" />
<hkern u1="&#x28;" u2="&#xd8;" k="13" />
<hkern u1="&#x28;" u2="&#xd6;" k="13" />
<hkern u1="&#x28;" u2="&#xd5;" k="13" />
<hkern u1="&#x28;" u2="&#xd4;" k="13" />
<hkern u1="&#x28;" u2="&#xd3;" k="13" />
<hkern u1="&#x28;" u2="&#xd2;" k="13" />
<hkern u1="&#x28;" u2="&#xc7;" k="12" />
<hkern u1="&#x28;" u2="&#x7b;" k="11" />
<hkern u1="&#x28;" u2="y" k="4" />
<hkern u1="&#x28;" u2="w" k="6" />
<hkern u1="&#x28;" u2="v" k="4" />
<hkern u1="&#x28;" u2="u" k="14" />
<hkern u1="&#x28;" u2="s" k="4" />
<hkern u1="&#x28;" u2="r" k="4" />
<hkern u1="&#x28;" u2="q" k="18" />
<hkern u1="&#x28;" u2="p" k="4" />
<hkern u1="&#x28;" u2="o" k="18" />
<hkern u1="&#x28;" u2="n" k="4" />
<hkern u1="&#x28;" u2="m" k="4" />
<hkern u1="&#x28;" u2="j" k="-9" />
<hkern u1="&#x28;" u2="f" k="4" />
<hkern u1="&#x28;" u2="e" k="18" />
<hkern u1="&#x28;" u2="d" k="18" />
<hkern u1="&#x28;" u2="c" k="18" />
<hkern u1="&#x28;" u2="a" k="5" />
<hkern u1="&#x28;" u2="Q" k="13" />
<hkern u1="&#x28;" u2="O" k="13" />
<hkern u1="&#x28;" u2="G" k="13" />
<hkern u1="&#x28;" u2="C" k="12" />
<hkern u1="&#x29;" u2="&#x7d;" k="8" />
<hkern u1="&#x29;" u2="]" k="8" />
<hkern u1="&#x2a;" u2="&#x153;" k="17" />
<hkern u1="&#x2a;" u2="&#xf8;" k="17" />
<hkern u1="&#x2a;" u2="&#xf6;" k="17" />
<hkern u1="&#x2a;" u2="&#xf5;" k="17" />
<hkern u1="&#x2a;" u2="&#xf4;" k="17" />
<hkern u1="&#x2a;" u2="&#xf3;" k="17" />
<hkern u1="&#x2a;" u2="&#xf2;" k="17" />
<hkern u1="&#x2a;" u2="&#xf0;" k="17" />
<hkern u1="&#x2a;" u2="&#xef;" k="-16" />
<hkern u1="&#x2a;" u2="&#xee;" k="-21" />
<hkern u1="&#x2a;" u2="&#xec;" k="-10" />
<hkern u1="&#x2a;" u2="&#xeb;" k="17" />
<hkern u1="&#x2a;" u2="&#xea;" k="17" />
<hkern u1="&#x2a;" u2="&#xe9;" k="17" />
<hkern u1="&#x2a;" u2="&#xe8;" k="17" />
<hkern u1="&#x2a;" u2="&#xe7;" k="17" />
<hkern u1="&#x2a;" u2="&#xc6;" k="48" />
<hkern u1="&#x2a;" u2="&#xc5;" k="39" />
<hkern u1="&#x2a;" u2="&#xc4;" k="39" />
<hkern u1="&#x2a;" u2="&#xc3;" k="39" />
<hkern u1="&#x2a;" u2="&#xc2;" k="39" />
<hkern u1="&#x2a;" u2="&#xc1;" k="39" />
<hkern u1="&#x2a;" u2="&#xc0;" k="39" />
<hkern u1="&#x2a;" u2="s" k="11" />
<hkern u1="&#x2a;" u2="q" k="20" />
<hkern u1="&#x2a;" u2="o" k="17" />
<hkern u1="&#x2a;" u2="g" k="14" />
<hkern u1="&#x2a;" u2="e" k="17" />
<hkern u1="&#x2a;" u2="d" k="20" />
<hkern u1="&#x2a;" u2="c" k="17" />
<hkern u1="&#x2a;" u2="Z" k="11" />
<hkern u1="&#x2a;" u2="T" k="-4" />
<hkern u1="&#x2a;" u2="J" k="21" />
<hkern u1="&#x2a;" u2="A" k="39" />
<hkern u1="&#x2c;" u2="v" k="35" />
<hkern u1="&#x2c;" u2="f" k="10" />
<hkern u1="&#x2c;" u2="V" k="53" />
<hkern u1="&#x2d;" u2="&#xc6;" k="17" />
<hkern u1="&#x2d;" u2="x" k="31" />
<hkern u1="&#x2d;" u2="v" k="13" />
<hkern u1="&#x2d;" u2="f" k="12" />
<hkern u1="&#x2d;" u2="X" k="39" />
<hkern u1="&#x2d;" u2="V" k="31" />
<hkern u1="&#x2e;" u2="v" k="35" />
<hkern u1="&#x2e;" u2="f" k="10" />
<hkern u1="&#x2e;" u2="V" k="53" />
<hkern u1="&#x2f;" u2="&#x153;" k="33" />
<hkern u1="&#x2f;" u2="&#x152;" k="6" />
<hkern u1="&#x2f;" u2="&#xff;" k="6" />
<hkern u1="&#x2f;" u2="&#xfd;" k="6" />
<hkern u1="&#x2f;" u2="&#xfc;" k="18" />
<hkern u1="&#x2f;" u2="&#xfb;" k="18" />
<hkern u1="&#x2f;" u2="&#xfa;" k="18" />
<hkern u1="&#x2f;" u2="&#xf9;" k="18" />
<hkern u1="&#x2f;" u2="&#xf8;" k="33" />
<hkern u1="&#x2f;" u2="&#xf6;" k="33" />
<hkern u1="&#x2f;" u2="&#xf5;" k="33" />
<hkern u1="&#x2f;" u2="&#xf4;" k="33" />
<hkern u1="&#x2f;" u2="&#xf3;" k="33" />
<hkern u1="&#x2f;" u2="&#xf2;" k="33" />
<hkern u1="&#x2f;" u2="&#xf1;" k="20" />
<hkern u1="&#x2f;" u2="&#xf0;" k="15" />
<hkern u1="&#x2f;" u2="&#xef;" k="-25" />
<hkern u1="&#x2f;" u2="&#xec;" k="-41" />
<hkern u1="&#x2f;" u2="&#xeb;" k="33" />
<hkern u1="&#x2f;" u2="&#xea;" k="33" />
<hkern u1="&#x2f;" u2="&#xe9;" k="33" />
<hkern u1="&#x2f;" u2="&#xe8;" k="33" />
<hkern u1="&#x2f;" u2="&#xe7;" k="33" />
<hkern u1="&#x2f;" u2="&#xe6;" k="21" />
<hkern u1="&#x2f;" u2="&#xe5;" k="21" />
<hkern u1="&#x2f;" u2="&#xe4;" k="21" />
<hkern u1="&#x2f;" u2="&#xe3;" k="21" />
<hkern u1="&#x2f;" u2="&#xe2;" k="21" />
<hkern u1="&#x2f;" u2="&#xe1;" k="21" />
<hkern u1="&#x2f;" u2="&#xe0;" k="21" />
<hkern u1="&#x2f;" u2="&#xd8;" k="6" />
<hkern u1="&#x2f;" u2="&#xd6;" k="6" />
<hkern u1="&#x2f;" u2="&#xd5;" k="6" />
<hkern u1="&#x2f;" u2="&#xd4;" k="6" />
<hkern u1="&#x2f;" u2="&#xd3;" k="6" />
<hkern u1="&#x2f;" u2="&#xd2;" k="6" />
<hkern u1="&#x2f;" u2="&#xc7;" k="5" />
<hkern u1="&#x2f;" u2="&#xc6;" k="52" />
<hkern u1="&#x2f;" u2="&#xc5;" k="43" />
<hkern u1="&#x2f;" u2="&#xc4;" k="43" />
<hkern u1="&#x2f;" u2="&#xc3;" k="43" />
<hkern u1="&#x2f;" u2="&#xc2;" k="43" />
<hkern u1="&#x2f;" u2="&#xc1;" k="43" />
<hkern u1="&#x2f;" u2="&#xc0;" k="43" />
<hkern u1="&#x2f;" u2="z" k="7" />
<hkern u1="&#x2f;" u2="y" k="6" />
<hkern u1="&#x2f;" u2="w" k="5" />
<hkern u1="&#x2f;" u2="v" k="5" />
<hkern u1="&#x2f;" u2="u" k="18" />
<hkern u1="&#x2f;" u2="s" k="26" />
<hkern u1="&#x2f;" u2="r" k="20" />
<hkern u1="&#x2f;" u2="q" k="34" />
<hkern u1="&#x2f;" u2="p" k="20" />
<hkern u1="&#x2f;" u2="o" k="33" />
<hkern u1="&#x2f;" u2="n" k="20" />
<hkern u1="&#x2f;" u2="m" k="20" />
<hkern u1="&#x2f;" u2="g" k="32" />
<hkern u1="&#x2f;" u2="e" k="33" />
<hkern u1="&#x2f;" u2="d" k="34" />
<hkern u1="&#x2f;" u2="c" k="33" />
<hkern u1="&#x2f;" u2="a" k="21" />
<hkern u1="&#x2f;" u2="Q" k="6" />
<hkern u1="&#x2f;" u2="O" k="6" />
<hkern u1="&#x2f;" u2="J" k="19" />
<hkern u1="&#x2f;" u2="G" k="6" />
<hkern u1="&#x2f;" u2="C" k="5" />
<hkern u1="&#x2f;" u2="A" k="43" />
<hkern u1="&#x2f;" u2="&#x2f;" k="281" />
<hkern u1="&#x3a;" u2="V" k="14" />
<hkern u1="&#x3b;" u2="V" k="14" />
<hkern u1="&#x40;" u2="&#x178;" k="29" />
<hkern u1="&#x40;" u2="&#xdd;" k="29" />
<hkern u1="&#x40;" u2="&#xc6;" k="6" />
<hkern u1="&#x40;" u2="Y" k="29" />
<hkern u1="&#x40;" u2="V" k="8" />
<hkern u1="&#x40;" u2="T" k="16" />
<hkern u1="&#x40;" u2="J" k="8" />
<hkern u1="A" u2="&#x2122;" k="41" />
<hkern u1="A" u2="&#xf0;" k="5" />
<hkern u1="A" u2="&#xae;" k="26" />
<hkern u1="A" u2="&#x7d;" k="20" />
<hkern u1="A" u2="v" k="18" />
<hkern u1="A" u2="f" k="7" />
<hkern u1="A" u2="]" k="23" />
<hkern u1="A" u2="\" k="47" />
<hkern u1="A" u2="V" k="28" />
<hkern u1="A" u2="&#x3f;" k="25" />
<hkern u1="A" u2="&#x2a;" k="36" />
<hkern u1="B" u2="&#x178;" k="25" />
<hkern u1="B" u2="&#xff;" k="4" />
<hkern u1="B" u2="&#xfd;" k="4" />
<hkern u1="B" u2="&#xdd;" k="25" />
<hkern u1="B" u2="&#xc6;" k="13" />
<hkern u1="B" u2="&#xc5;" k="10" />
<hkern u1="B" u2="&#xc4;" k="10" />
<hkern u1="B" u2="&#xc3;" k="10" />
<hkern u1="B" u2="&#xc2;" k="10" />
<hkern u1="B" u2="&#xc1;" k="10" />
<hkern u1="B" u2="&#xc0;" k="10" />
<hkern u1="B" u2="&#x7d;" k="20" />
<hkern u1="B" u2="y" k="4" />
<hkern u1="B" u2="x" k="5" />
<hkern u1="B" u2="w" k="4" />
<hkern u1="B" u2="v" k="4" />
<hkern u1="B" u2="g" k="9" />
<hkern u1="B" u2="]" k="27" />
<hkern u1="B" u2="\" k="6" />
<hkern u1="B" u2="Y" k="25" />
<hkern u1="B" u2="X" k="13" />
<hkern u1="B" u2="W" k="3" />
<hkern u1="B" u2="V" k="11" />
<hkern u1="B" u2="T" k="16" />
<hkern u1="B" u2="J" k="16" />
<hkern u1="B" u2="A" k="10" />
<hkern u1="B" u2="&#x3f;" k="7" />
<hkern u1="C" u2="&#xf0;" k="7" />
<hkern u1="C" u2="&#xef;" k="-14" />
<hkern u1="C" u2="&#xee;" k="-12" />
<hkern u1="C" u2="&#xec;" k="-33" />
<hkern u1="C" u2="&#xae;" k="13" />
<hkern u1="C" u2="v" k="16" />
<hkern u1="C" u2="f" k="6" />
<hkern u1="D" u2="&#xc6;" k="17" />
<hkern u1="D" u2="&#x7d;" k="26" />
<hkern u1="D" u2="x" k="3" />
<hkern u1="D" u2="]" k="29" />
<hkern u1="D" u2="\" k="7" />
<hkern u1="D" u2="X" k="21" />
<hkern u1="D" u2="V" k="12" />
<hkern u1="D" u2="&#x3f;" k="9" />
<hkern u1="D" u2="&#x2f;" k="6" />
<hkern u1="D" u2="&#x29;" k="12" />
<hkern u1="E" u2="&#xf0;" k="8" />
<hkern u1="E" u2="&#xef;" k="-13" />
<hkern u1="E" u2="&#xee;" k="-12" />
<hkern u1="E" u2="&#xec;" k="-36" />
<hkern u1="E" u2="v" k="11" />
<hkern u1="E" u2="f" k="3" />
<hkern u1="F" u2="&#x2026;" k="72" />
<hkern u1="F" u2="&#x201e;" k="72" />
<hkern u1="F" u2="&#x201a;" k="72" />
<hkern u1="F" u2="&#x2014;" k="4" />
<hkern u1="F" u2="&#x2013;" k="4" />
<hkern u1="F" u2="&#x153;" k="18" />
<hkern u1="F" u2="&#x152;" k="10" />
<hkern u1="F" u2="&#xff;" k="13" />
<hkern u1="F" u2="&#xfd;" k="13" />
<hkern u1="F" u2="&#xfc;" k="18" />
<hkern u1="F" u2="&#xfb;" k="18" />
<hkern u1="F" u2="&#xfa;" k="18" />
<hkern u1="F" u2="&#xf9;" k="18" />
<hkern u1="F" u2="&#xf8;" k="18" />
<hkern u1="F" u2="&#xf6;" k="18" />
<hkern u1="F" u2="&#xf5;" k="18" />
<hkern u1="F" u2="&#xf4;" k="18" />
<hkern u1="F" u2="&#xf3;" k="18" />
<hkern u1="F" u2="&#xf2;" k="18" />
<hkern u1="F" u2="&#xf1;" k="21" />
<hkern u1="F" u2="&#xf0;" k="20" />
<hkern u1="F" u2="&#xef;" k="-28" />
<hkern u1="F" u2="&#xee;" k="-18" />
<hkern u1="F" u2="&#xec;" k="-55" />
<hkern u1="F" u2="&#xeb;" k="18" />
<hkern u1="F" u2="&#xea;" k="18" />
<hkern u1="F" u2="&#xe9;" k="18" />
<hkern u1="F" u2="&#xe8;" k="18" />
<hkern u1="F" u2="&#xe7;" k="18" />
<hkern u1="F" u2="&#xe6;" k="33" />
<hkern u1="F" u2="&#xe5;" k="33" />
<hkern u1="F" u2="&#xe4;" k="33" />
<hkern u1="F" u2="&#xe3;" k="33" />
<hkern u1="F" u2="&#xe2;" k="33" />
<hkern u1="F" u2="&#xe1;" k="33" />
<hkern u1="F" u2="&#xe0;" k="33" />
<hkern u1="F" u2="&#xd8;" k="10" />
<hkern u1="F" u2="&#xd6;" k="10" />
<hkern u1="F" u2="&#xd5;" k="10" />
<hkern u1="F" u2="&#xd4;" k="10" />
<hkern u1="F" u2="&#xd3;" k="10" />
<hkern u1="F" u2="&#xd2;" k="10" />
<hkern u1="F" u2="&#xc7;" k="10" />
<hkern u1="F" u2="&#xc6;" k="46" />
<hkern u1="F" u2="&#xc5;" k="34" />
<hkern u1="F" u2="&#xc4;" k="34" />
<hkern u1="F" u2="&#xc3;" k="34" />
<hkern u1="F" u2="&#xc2;" k="34" />
<hkern u1="F" u2="&#xc1;" k="34" />
<hkern u1="F" u2="&#xc0;" k="34" />
<hkern u1="F" u2="z" k="20" />
<hkern u1="F" u2="y" k="13" />
<hkern u1="F" u2="x" k="23" />
<hkern u1="F" u2="w" k="14" />
<hkern u1="F" u2="v" k="11" />
<hkern u1="F" u2="u" k="18" />
<hkern u1="F" u2="t" k="7" />
<hkern u1="F" u2="s" k="17" />
<hkern u1="F" u2="r" k="21" />
<hkern u1="F" u2="q" k="19" />
<hkern u1="F" u2="p" k="21" />
<hkern u1="F" u2="o" k="18" />
<hkern u1="F" u2="n" k="21" />
<hkern u1="F" u2="m" k="21" />
<hkern u1="F" u2="g" k="21" />
<hkern u1="F" u2="f" k="7" />
<hkern u1="F" u2="e" k="18" />
<hkern u1="F" u2="d" k="19" />
<hkern u1="F" u2="c" k="18" />
<hkern u1="F" u2="a" k="33" />
<hkern u1="F" u2="X" k="4" />
<hkern u1="F" u2="S" k="10" />
<hkern u1="F" u2="Q" k="10" />
<hkern u1="F" u2="O" k="10" />
<hkern u1="F" u2="J" k="21" />
<hkern u1="F" u2="G" k="10" />
<hkern u1="F" u2="C" k="10" />
<hkern u1="F" u2="A" k="34" />
<hkern u1="F" u2="&#x2f;" k="31" />
<hkern u1="F" u2="&#x2e;" k="72" />
<hkern u1="F" u2="&#x2d;" k="4" />
<hkern u1="F" u2="&#x2c;" k="72" />
<hkern u1="G" u2="&#xef;" k="-6" />
<hkern u1="G" u2="&#xee;" k="-4" />
<hkern u1="G" u2="&#xec;" k="-15" />
<hkern u1="G" u2="v" k="7" />
<hkern u1="G" u2="f" k="6" />
<hkern u1="G" u2="\" k="4" />
<hkern u1="G" u2="V" k="9" />
<hkern u1="H" u2="&#xf0;" k="3" />
<hkern u1="H" u2="&#xec;" k="-3" />
<hkern u1="I" u2="&#xf0;" k="3" />
<hkern u1="I" u2="&#xec;" k="-3" />
<hkern u1="J" u2="&#xf0;" k="3" />
<hkern u1="J" u2="&#xec;" k="-4" />
<hkern u1="K" u2="&#xf0;" k="8" />
<hkern u1="K" u2="&#xef;" k="-27" />
<hkern u1="K" u2="&#xec;" k="-49" />
<hkern u1="K" u2="&#xae;" k="11" />
<hkern u1="K" u2="v" k="24" />
<hkern u1="K" u2="f" k="4" />
<hkern u1="L" u2="&#x2122;" k="90" />
<hkern u1="L" u2="&#xf0;" k="5" />
<hkern u1="L" u2="&#xb7;" k="56" />
<hkern u1="L" u2="&#xae;" k="84" />
<hkern u1="L" u2="&#x7d;" k="11" />
<hkern u1="L" u2="v" k="45" />
<hkern u1="L" u2="f" k="3" />
<hkern u1="L" u2="]" k="14" />
<hkern u1="L" u2="\" k="71" />
<hkern u1="L" u2="V" k="59" />
<hkern u1="L" u2="&#x3f;" k="13" />
<hkern u1="L" u2="&#x2a;" k="89" />
<hkern u1="M" u2="&#xf0;" k="3" />
<hkern u1="M" u2="&#xec;" k="-3" />
<hkern u1="N" u2="&#xf0;" k="3" />
<hkern u1="N" u2="&#xec;" k="-3" />
<hkern u1="O" u2="&#xc6;" k="15" />
<hkern u1="O" u2="&#x7d;" k="25" />
<hkern u1="O" u2="]" k="28" />
<hkern u1="O" u2="\" k="8" />
<hkern u1="O" u2="X" k="19" />
<hkern u1="O" u2="V" k="12" />
<hkern u1="O" u2="&#x3f;" k="7" />
<hkern u1="O" u2="&#x2f;" k="6" />
<hkern u1="O" u2="&#x29;" k="5" />
<hkern u1="P" u2="&#x2039;" k="9" />
<hkern u1="P" u2="&#x2026;" k="81" />
<hkern u1="P" u2="&#x201e;" k="81" />
<hkern u1="P" u2="&#x201a;" k="81" />
<hkern u1="P" u2="&#x2014;" k="9" />
<hkern u1="P" u2="&#x2013;" k="9" />
<hkern u1="P" u2="&#x178;" k="20" />
<hkern u1="P" u2="&#x153;" k="4" />
<hkern u1="P" u2="&#xf8;" k="4" />
<hkern u1="P" u2="&#xf6;" k="4" />
<hkern u1="P" u2="&#xf5;" k="4" />
<hkern u1="P" u2="&#xf4;" k="4" />
<hkern u1="P" u2="&#xf3;" k="4" />
<hkern u1="P" u2="&#xf2;" k="4" />
<hkern u1="P" u2="&#xf0;" k="17" />
<hkern u1="P" u2="&#xef;" k="-3" />
<hkern u1="P" u2="&#xee;" k="-6" />
<hkern u1="P" u2="&#xec;" k="-4" />
<hkern u1="P" u2="&#xeb;" k="4" />
<hkern u1="P" u2="&#xea;" k="4" />
<hkern u1="P" u2="&#xe9;" k="4" />
<hkern u1="P" u2="&#xe8;" k="4" />
<hkern u1="P" u2="&#xe7;" k="4" />
<hkern u1="P" u2="&#xe6;" k="5" />
<hkern u1="P" u2="&#xe5;" k="5" />
<hkern u1="P" u2="&#xe4;" k="5" />
<hkern u1="P" u2="&#xe3;" k="5" />
<hkern u1="P" u2="&#xe2;" k="5" />
<hkern u1="P" u2="&#xe1;" k="5" />
<hkern u1="P" u2="&#xe0;" k="5" />
<hkern u1="P" u2="&#xdd;" k="20" />
<hkern u1="P" u2="&#xc6;" k="36" />
<hkern u1="P" u2="&#xc5;" k="31" />
<hkern u1="P" u2="&#xc4;" k="31" />
<hkern u1="P" u2="&#xc3;" k="31" />
<hkern u1="P" u2="&#xc2;" k="31" />
<hkern u1="P" u2="&#xc1;" k="31" />
<hkern u1="P" u2="&#xc0;" k="31" />
<hkern u1="P" u2="&#xab;" k="9" />
<hkern u1="P" u2="&#x7d;" k="21" />
<hkern u1="P" u2="q" k="5" />
<hkern u1="P" u2="o" k="4" />
<hkern u1="P" u2="g" k="4" />
<hkern u1="P" u2="e" k="4" />
<hkern u1="P" u2="d" k="5" />
<hkern u1="P" u2="c" k="4" />
<hkern u1="P" u2="a" k="5" />
<hkern u1="P" u2="]" k="23" />
<hkern u1="P" u2="\" k="5" />
<hkern u1="P" u2="Z" k="7" />
<hkern u1="P" u2="Y" k="20" />
<hkern u1="P" u2="X" k="17" />
<hkern u1="P" u2="V" k="6" />
<hkern u1="P" u2="J" k="26" />
<hkern u1="P" u2="A" k="31" />
<hkern u1="P" u2="&#x2f;" k="36" />
<hkern u1="P" u2="&#x2e;" k="81" />
<hkern u1="P" u2="&#x2d;" k="9" />
<hkern u1="P" u2="&#x2c;" k="81" />
<hkern u1="P" u2="&#x29;" k="4" />
<hkern u1="Q" u2="&#xc6;" k="15" />
<hkern u1="Q" u2="&#x7d;" k="25" />
<hkern u1="Q" u2="]" k="28" />
<hkern u1="Q" u2="\" k="8" />
<hkern u1="Q" u2="X" k="19" />
<hkern u1="Q" u2="V" k="12" />
<hkern u1="Q" u2="&#x3f;" k="7" />
<hkern u1="Q" u2="&#x2f;" k="6" />
<hkern u1="Q" u2="&#x29;" k="5" />
<hkern u1="R" u2="&#xf0;" k="13" />
<hkern u1="R" u2="&#xc6;" k="8" />
<hkern u1="R" u2="&#x7d;" k="15" />
<hkern u1="R" u2="]" k="17" />
<hkern u1="R" u2="\" k="6" />
<hkern u1="R" u2="V" k="10" />
<hkern u1="S" u2="&#xef;" k="-8" />
<hkern u1="S" u2="&#xee;" k="-3" />
<hkern u1="S" u2="&#xec;" k="-20" />
<hkern u1="S" u2="&#xc6;" k="12" />
<hkern u1="S" u2="x" k="9" />
<hkern u1="S" u2="v" k="10" />
<hkern u1="S" u2="f" k="8" />
<hkern u1="S" u2="X" k="5" />
<hkern u1="S" u2="V" k="9" />
<hkern u1="T" u2="&#xf0;" k="35" />
<hkern u1="T" u2="&#xef;" k="-41" />
<hkern u1="T" u2="&#xee;" k="-22" />
<hkern u1="T" u2="&#xec;" k="-69" />
<hkern u1="T" u2="&#xe4;" k="82" />
<hkern u1="T" u2="&#xe3;" k="66" />
<hkern u1="T" u2="&#xdf;" k="4" />
<hkern u1="T" u2="&#xc6;" k="54" />
<hkern u1="T" u2="&#xae;" k="8" />
<hkern u1="T" u2="x" k="72" />
<hkern u1="T" u2="v" k="67" />
<hkern u1="T" u2="f" k="14" />
<hkern u1="T" u2="&#x40;" k="26" />
<hkern u1="T" u2="&#x2f;" k="51" />
<hkern u1="T" u2="&#x26;" k="21" />
<hkern u1="U" u2="&#xf0;" k="3" />
<hkern u1="U" u2="&#xec;" k="-6" />
<hkern u1="U" u2="&#xc6;" k="9" />
<hkern u1="U" u2="&#x2f;" k="6" />
<hkern u1="V" u2="&#x203a;" k="19" />
<hkern u1="V" u2="&#x2039;" k="28" />
<hkern u1="V" u2="&#x2026;" k="53" />
<hkern u1="V" u2="&#x201e;" k="53" />
<hkern u1="V" u2="&#x201a;" k="53" />
<hkern u1="V" u2="&#x2014;" k="31" />
<hkern u1="V" u2="&#x2013;" k="31" />
<hkern u1="V" u2="&#x153;" k="32" />
<hkern u1="V" u2="&#x152;" k="12" />
<hkern u1="V" u2="&#xff;" k="8" />
<hkern u1="V" u2="&#xfd;" k="8" />
<hkern u1="V" u2="&#xfc;" k="23" />
<hkern u1="V" u2="&#xfb;" k="23" />
<hkern u1="V" u2="&#xfa;" k="23" />
<hkern u1="V" u2="&#xf9;" k="23" />
<hkern u1="V" u2="&#xf8;" k="32" />
<hkern u1="V" u2="&#xf6;" k="32" />
<hkern u1="V" u2="&#xf5;" k="32" />
<hkern u1="V" u2="&#xf4;" k="32" />
<hkern u1="V" u2="&#xf3;" k="32" />
<hkern u1="V" u2="&#xf2;" k="32" />
<hkern u1="V" u2="&#xf1;" k="26" />
<hkern u1="V" u2="&#xf0;" k="28" />
<hkern u1="V" u2="&#xef;" k="-30" />
<hkern u1="V" u2="&#xee;" k="-12" />
<hkern u1="V" u2="&#xec;" k="-55" />
<hkern u1="V" u2="&#xeb;" k="32" />
<hkern u1="V" u2="&#xea;" k="32" />
<hkern u1="V" u2="&#xe9;" k="32" />
<hkern u1="V" u2="&#xe8;" k="32" />
<hkern u1="V" u2="&#xe7;" k="32" />
<hkern u1="V" u2="&#xe6;" k="27" />
<hkern u1="V" u2="&#xe5;" k="27" />
<hkern u1="V" u2="&#xe4;" k="27" />
<hkern u1="V" u2="&#xe3;" k="27" />
<hkern u1="V" u2="&#xe2;" k="27" />
<hkern u1="V" u2="&#xe1;" k="27" />
<hkern u1="V" u2="&#xe0;" k="27" />
<hkern u1="V" u2="&#xd8;" k="12" />
<hkern u1="V" u2="&#xd6;" k="12" />
<hkern u1="V" u2="&#xd5;" k="12" />
<hkern u1="V" u2="&#xd4;" k="12" />
<hkern u1="V" u2="&#xd3;" k="12" />
<hkern u1="V" u2="&#xd2;" k="12" />
<hkern u1="V" u2="&#xc7;" k="11" />
<hkern u1="V" u2="&#xc6;" k="32" />
<hkern u1="V" u2="&#xc5;" k="28" />
<hkern u1="V" u2="&#xc4;" k="28" />
<hkern u1="V" u2="&#xc3;" k="28" />
<hkern u1="V" u2="&#xc2;" k="28" />
<hkern u1="V" u2="&#xc1;" k="28" />
<hkern u1="V" u2="&#xc0;" k="28" />
<hkern u1="V" u2="&#xbb;" k="19" />
<hkern u1="V" u2="&#xae;" k="7" />
<hkern u1="V" u2="&#xab;" k="28" />
<hkern u1="V" u2="z" k="12" />
<hkern u1="V" u2="y" k="8" />
<hkern u1="V" u2="x" k="8" />
<hkern u1="V" u2="w" k="10" />
<hkern u1="V" u2="v" k="8" />
<hkern u1="V" u2="u" k="23" />
<hkern u1="V" u2="s" k="25" />
<hkern u1="V" u2="r" k="26" />
<hkern u1="V" u2="q" k="32" />
<hkern u1="V" u2="p" k="26" />
<hkern u1="V" u2="o" k="32" />
<hkern u1="V" u2="n" k="26" />
<hkern u1="V" u2="m" k="26" />
<hkern u1="V" u2="g" k="37" />
<hkern u1="V" u2="f" k="3" />
<hkern u1="V" u2="e" k="32" />
<hkern u1="V" u2="d" k="32" />
<hkern u1="V" u2="c" k="32" />
<hkern u1="V" u2="a" k="27" />
<hkern u1="V" u2="S" k="8" />
<hkern u1="V" u2="Q" k="12" />
<hkern u1="V" u2="O" k="12" />
<hkern u1="V" u2="J" k="27" />
<hkern u1="V" u2="G" k="12" />
<hkern u1="V" u2="C" k="11" />
<hkern u1="V" u2="A" k="28" />
<hkern u1="V" u2="&#x40;" k="14" />
<hkern u1="V" u2="&#x3b;" k="14" />
<hkern u1="V" u2="&#x3a;" k="14" />
<hkern u1="V" u2="&#x2f;" k="41" />
<hkern u1="V" u2="&#x2e;" k="53" />
<hkern u1="V" u2="&#x2d;" k="31" />
<hkern u1="V" u2="&#x2c;" k="53" />
<hkern u1="V" u2="&#x26;" k="19" />
<hkern u1="W" u2="&#xf0;" k="18" />
<hkern u1="W" u2="&#xef;" k="-21" />
<hkern u1="W" u2="&#xee;" k="-12" />
<hkern u1="W" u2="&#xec;" k="-48" />
<hkern u1="W" u2="&#xc6;" k="27" />
<hkern u1="W" u2="&#x2f;" k="27" />
<hkern u1="W" u2="&#x26;" k="6" />
<hkern u1="X" u2="&#x2039;" k="23" />
<hkern u1="X" u2="&#x2014;" k="38" />
<hkern u1="X" u2="&#x2013;" k="38" />
<hkern u1="X" u2="&#x153;" k="21" />
<hkern u1="X" u2="&#x152;" k="19" />
<hkern u1="X" u2="&#xff;" k="27" />
<hkern u1="X" u2="&#xfd;" k="27" />
<hkern u1="X" u2="&#xfc;" k="16" />
<hkern u1="X" u2="&#xfb;" k="16" />
<hkern u1="X" u2="&#xfa;" k="16" />
<hkern u1="X" u2="&#xf9;" k="16" />
<hkern u1="X" u2="&#xf8;" k="21" />
<hkern u1="X" u2="&#xf6;" k="21" />
<hkern u1="X" u2="&#xf5;" k="21" />
<hkern u1="X" u2="&#xf4;" k="21" />
<hkern u1="X" u2="&#xf3;" k="21" />
<hkern u1="X" u2="&#xf2;" k="21" />
<hkern u1="X" u2="&#xf0;" k="12" />
<hkern u1="X" u2="&#xef;" k="-36" />
<hkern u1="X" u2="&#xee;" k="-5" />
<hkern u1="X" u2="&#xec;" k="-56" />
<hkern u1="X" u2="&#xeb;" k="21" />
<hkern u1="X" u2="&#xea;" k="21" />
<hkern u1="X" u2="&#xe9;" k="21" />
<hkern u1="X" u2="&#xe8;" k="21" />
<hkern u1="X" u2="&#xe7;" k="21" />
<hkern u1="X" u2="&#xd8;" k="19" />
<hkern u1="X" u2="&#xd6;" k="19" />
<hkern u1="X" u2="&#xd5;" k="19" />
<hkern u1="X" u2="&#xd4;" k="19" />
<hkern u1="X" u2="&#xd3;" k="19" />
<hkern u1="X" u2="&#xd2;" k="19" />
<hkern u1="X" u2="&#xc7;" k="18" />
<hkern u1="X" u2="&#xae;" k="9" />
<hkern u1="X" u2="&#xab;" k="23" />
<hkern u1="X" u2="y" k="27" />
<hkern u1="X" u2="w" k="25" />
<hkern u1="X" u2="v" k="26" />
<hkern u1="X" u2="u" k="16" />
<hkern u1="X" u2="t" k="9" />
<hkern u1="X" u2="q" k="18" />
<hkern u1="X" u2="o" k="21" />
<hkern u1="X" u2="g" k="16" />
<hkern u1="X" u2="f" k="4" />
<hkern u1="X" u2="e" k="21" />
<hkern u1="X" u2="d" k="18" />
<hkern u1="X" u2="c" k="21" />
<hkern u1="X" u2="Q" k="19" />
<hkern u1="X" u2="O" k="19" />
<hkern u1="X" u2="G" k="19" />
<hkern u1="X" u2="C" k="18" />
<hkern u1="X" u2="&#x2d;" k="38" />
<hkern u1="Y" u2="&#xff;" k="34" />
<hkern u1="Y" u2="&#xf0;" k="39" />
<hkern u1="Y" u2="&#xef;" k="-44" />
<hkern u1="Y" u2="&#xee;" k="-8" />
<hkern u1="Y" u2="&#xec;" k="-64" />
<hkern u1="Y" u2="&#xeb;" k="67" />
<hkern u1="Y" u2="&#xe4;" k="57" />
<hkern u1="Y" u2="&#xe3;" k="53" />
<hkern u1="Y" u2="&#xdf;" k="6" />
<hkern u1="Y" u2="&#xc6;" k="52" />
<hkern u1="Y" u2="&#xae;" k="22" />
<hkern u1="Y" u2="x" k="40" />
<hkern u1="Y" u2="v" k="41" />
<hkern u1="Y" u2="f" k="18" />
<hkern u1="Y" u2="&#x40;" k="36" />
<hkern u1="Y" u2="&#x2f;" k="64" />
<hkern u1="Y" u2="&#x26;" k="37" />
<hkern u1="Z" u2="&#xf0;" k="8" />
<hkern u1="Z" u2="&#xef;" k="-12" />
<hkern u1="Z" u2="&#xee;" k="-15" />
<hkern u1="Z" u2="&#xec;" k="-39" />
<hkern u1="Z" u2="&#xae;" k="7" />
<hkern u1="Z" u2="v" k="12" />
<hkern u1="Z" u2="f" k="3" />
<hkern u1="[" u2="&#x153;" k="38" />
<hkern u1="[" u2="&#x152;" k="28" />
<hkern u1="[" u2="&#xff;" k="30" />
<hkern u1="[" u2="&#xfd;" k="30" />
<hkern u1="[" u2="&#xfc;" k="34" />
<hkern u1="[" u2="&#xfb;" k="34" />
<hkern u1="[" u2="&#xfa;" k="34" />
<hkern u1="[" u2="&#xf9;" k="34" />
<hkern u1="[" u2="&#xf8;" k="38" />
<hkern u1="[" u2="&#xf6;" k="38" />
<hkern u1="[" u2="&#xf5;" k="38" />
<hkern u1="[" u2="&#xf4;" k="38" />
<hkern u1="[" u2="&#xf3;" k="38" />
<hkern u1="[" u2="&#xf2;" k="38" />
<hkern u1="[" u2="&#xf1;" k="25" />
<hkern u1="[" u2="&#xf0;" k="13" />
<hkern u1="[" u2="&#xef;" k="-15" />
<hkern u1="[" u2="&#xec;" k="-39" />
<hkern u1="[" u2="&#xeb;" k="38" />
<hkern u1="[" u2="&#xea;" k="38" />
<hkern u1="[" u2="&#xe9;" k="38" />
<hkern u1="[" u2="&#xe8;" k="38" />
<hkern u1="[" u2="&#xe7;" k="38" />
<hkern u1="[" u2="&#xe6;" k="31" />
<hkern u1="[" u2="&#xe5;" k="31" />
<hkern u1="[" u2="&#xe4;" k="31" />
<hkern u1="[" u2="&#xe3;" k="31" />
<hkern u1="[" u2="&#xe2;" k="31" />
<hkern u1="[" u2="&#xe1;" k="31" />
<hkern u1="[" u2="&#xe0;" k="31" />
<hkern u1="[" u2="&#xd8;" k="28" />
<hkern u1="[" u2="&#xd6;" k="28" />
<hkern u1="[" u2="&#xd5;" k="28" />
<hkern u1="[" u2="&#xd4;" k="28" />
<hkern u1="[" u2="&#xd3;" k="28" />
<hkern u1="[" u2="&#xd2;" k="28" />
<hkern u1="[" u2="&#xc7;" k="26" />
<hkern u1="[" u2="&#xc6;" k="23" />
<hkern u1="[" u2="&#xc5;" k="23" />
<hkern u1="[" u2="&#xc4;" k="23" />
<hkern u1="[" u2="&#xc3;" k="23" />
<hkern u1="[" u2="&#xc2;" k="23" />
<hkern u1="[" u2="&#xc1;" k="23" />
<hkern u1="[" u2="&#xc0;" k="23" />
<hkern u1="[" u2="&#x7b;" k="21" />
<hkern u1="[" u2="z" k="20" />
<hkern u1="[" u2="y" k="30" />
<hkern u1="[" u2="x" k="18" />
<hkern u1="[" u2="w" k="33" />
<hkern u1="[" u2="v" k="32" />
<hkern u1="[" u2="u" k="34" />
<hkern u1="[" u2="t" k="25" />
<hkern u1="[" u2="s" k="25" />
<hkern u1="[" u2="r" k="25" />
<hkern u1="[" u2="q" k="37" />
<hkern u1="[" u2="p" k="25" />
<hkern u1="[" u2="o" k="38" />
<hkern u1="[" u2="n" k="25" />
<hkern u1="[" u2="m" k="25" />
<hkern u1="[" u2="j" k="-4" />
<hkern u1="[" u2="f" k="15" />
<hkern u1="[" u2="e" k="38" />
<hkern u1="[" u2="d" k="37" />
<hkern u1="[" u2="c" k="38" />
<hkern u1="[" u2="a" k="31" />
<hkern u1="[" u2="S" k="11" />
<hkern u1="[" u2="Q" k="28" />
<hkern u1="[" u2="O" k="28" />
<hkern u1="[" u2="G" k="28" />
<hkern u1="[" u2="C" k="26" />
<hkern u1="[" u2="A" k="23" />
<hkern u1="[" u2="&#x28;" k="8" />
<hkern u1="\" u2="&#x201d;" k="68" />
<hkern u1="\" u2="&#x2019;" k="68" />
<hkern u1="\" u2="&#x178;" k="68" />
<hkern u1="\" u2="&#x153;" k="4" />
<hkern u1="\" u2="&#x152;" k="8" />
<hkern u1="\" u2="&#xff;" k="23" />
<hkern u1="\" u2="&#xfd;" k="23" />
<hkern u1="\" u2="&#xf8;" k="4" />
<hkern u1="\" u2="&#xf6;" k="4" />
<hkern u1="\" u2="&#xf5;" k="4" />
<hkern u1="\" u2="&#xf4;" k="4" />
<hkern u1="\" u2="&#xf3;" k="4" />
<hkern u1="\" u2="&#xf2;" k="4" />
<hkern u1="\" u2="&#xeb;" k="4" />
<hkern u1="\" u2="&#xea;" k="4" />
<hkern u1="\" u2="&#xe9;" k="4" />
<hkern u1="\" u2="&#xe8;" k="4" />
<hkern u1="\" u2="&#xe7;" k="4" />
<hkern u1="\" u2="&#xdd;" k="68" />
<hkern u1="\" u2="&#xdc;" k="9" />
<hkern u1="\" u2="&#xdb;" k="9" />
<hkern u1="\" u2="&#xda;" k="9" />
<hkern u1="\" u2="&#xd9;" k="9" />
<hkern u1="\" u2="&#xd8;" k="8" />
<hkern u1="\" u2="&#xd6;" k="8" />
<hkern u1="\" u2="&#xd5;" k="8" />
<hkern u1="\" u2="&#xd4;" k="8" />
<hkern u1="\" u2="&#xd3;" k="8" />
<hkern u1="\" u2="&#xd2;" k="8" />
<hkern u1="\" u2="&#xc7;" k="8" />
<hkern u1="\" u2="y" k="23" />
<hkern u1="\" u2="w" k="18" />
<hkern u1="\" u2="v" k="23" />
<hkern u1="\" u2="t" k="12" />
<hkern u1="\" u2="o" k="4" />
<hkern u1="\" u2="f" k="5" />
<hkern u1="\" u2="e" k="4" />
<hkern u1="\" u2="c" k="4" />
<hkern u1="\" u2="Y" k="68" />
<hkern u1="\" u2="W" k="30" />
<hkern u1="\" u2="V" k="44" />
<hkern u1="\" u2="U" k="9" />
<hkern u1="\" u2="T" k="57" />
<hkern u1="\" u2="S" k="4" />
<hkern u1="\" u2="Q" k="8" />
<hkern u1="\" u2="O" k="8" />
<hkern u1="\" u2="G" k="8" />
<hkern u1="\" u2="C" k="8" />
<hkern u1="\" u2="&#x27;" k="69" />
<hkern u1="\" u2="&#x22;" k="69" />
<hkern u1="a" u2="&#x2122;" k="12" />
<hkern u1="a" u2="&#x7d;" k="9" />
<hkern u1="a" u2="v" k="5" />
<hkern u1="a" u2="]" k="11" />
<hkern u1="a" u2="\" k="35" />
<hkern u1="a" u2="V" k="25" />
<hkern u1="a" u2="&#x3f;" k="15" />
<hkern u1="a" u2="&#x2a;" k="3" />
<hkern u1="b" u2="&#x2122;" k="17" />
<hkern u1="b" u2="&#xc6;" k="7" />
<hkern u1="b" u2="&#x7d;" k="33" />
<hkern u1="b" u2="x" k="12" />
<hkern u1="b" u2="v" k="8" />
<hkern u1="b" u2="]" k="37" />
<hkern u1="b" u2="\" k="36" />
<hkern u1="b" u2="X" k="20" />
<hkern u1="b" u2="V" k="31" />
<hkern u1="b" u2="&#x3f;" k="25" />
<hkern u1="b" u2="&#x2a;" k="8" />
<hkern u1="b" u2="&#x29;" k="18" />
<hkern u1="c" u2="&#xf0;" k="11" />
<hkern u1="c" u2="&#x7d;" k="16" />
<hkern u1="c" u2="]" k="19" />
<hkern u1="c" u2="\" k="10" />
<hkern u1="c" u2="V" k="12" />
<hkern u1="c" u2="&#x3f;" k="9" />
<hkern u1="d" u2="&#xef;" k="-3" />
<hkern u1="d" u2="&#xec;" k="-7" />
<hkern u1="e" u2="&#x2122;" k="14" />
<hkern u1="e" u2="&#xc6;" k="5" />
<hkern u1="e" u2="&#x7d;" k="27" />
<hkern u1="e" u2="x" k="4" />
<hkern u1="e" u2="v" k="8" />
<hkern u1="e" u2="]" k="25" />
<hkern u1="e" u2="\" k="34" />
<hkern u1="e" u2="V" k="31" />
<hkern u1="e" u2="&#x3f;" k="20" />
<hkern u1="e" u2="&#x29;" k="5" />
<hkern u1="f" u2="&#x203a;" k="22" />
<hkern u1="f" u2="&#x2039;" k="34" />
<hkern u1="f" u2="&#x2026;" k="41" />
<hkern u1="f" u2="&#x201e;" k="41" />
<hkern u1="f" u2="&#x201a;" k="41" />
<hkern u1="f" u2="&#x2014;" k="42" />
<hkern u1="f" u2="&#x2013;" k="42" />
<hkern u1="f" u2="&#x178;" k="10" />
<hkern u1="f" u2="&#x153;" k="9" />
<hkern u1="f" u2="&#xf8;" k="9" />
<hkern u1="f" u2="&#xf6;" k="9" />
<hkern u1="f" u2="&#xf5;" k="9" />
<hkern u1="f" u2="&#xf4;" k="9" />
<hkern u1="f" u2="&#xf3;" k="9" />
<hkern u1="f" u2="&#xf2;" k="9" />
<hkern u1="f" u2="&#xf0;" k="35" />
<hkern u1="f" u2="&#xef;" k="-24" />
<hkern u1="f" u2="&#xee;" k="-18" />
<hkern u1="f" u2="&#xec;" k="-77" />
<hkern u1="f" u2="&#xeb;" k="9" />
<hkern u1="f" u2="&#xea;" k="9" />
<hkern u1="f" u2="&#xe9;" k="9" />
<hkern u1="f" u2="&#xe8;" k="9" />
<hkern u1="f" u2="&#xe7;" k="9" />
<hkern u1="f" u2="&#xdd;" k="10" />
<hkern u1="f" u2="&#xc6;" k="34" />
<hkern u1="f" u2="&#xc5;" k="30" />
<hkern u1="f" u2="&#xc4;" k="30" />
<hkern u1="f" u2="&#xc3;" k="30" />
<hkern u1="f" u2="&#xc2;" k="30" />
<hkern u1="f" u2="&#xc1;" k="30" />
<hkern u1="f" u2="&#xc0;" k="30" />
<hkern u1="f" u2="&#xbb;" k="22" />
<hkern u1="f" u2="&#xab;" k="34" />
<hkern u1="f" u2="q" k="10" />
<hkern u1="f" u2="o" k="9" />
<hkern u1="f" u2="g" k="5" />
<hkern u1="f" u2="e" k="9" />
<hkern u1="f" u2="d" k="10" />
<hkern u1="f" u2="c" k="9" />
<hkern u1="f" u2="Z" k="11" />
<hkern u1="f" u2="Y" k="10" />
<hkern u1="f" u2="X" k="13" />
<hkern u1="f" u2="T" k="31" />
<hkern u1="f" u2="J" k="25" />
<hkern u1="f" u2="A" k="30" />
<hkern u1="f" u2="&#x2f;" k="27" />
<hkern u1="f" u2="&#x2e;" k="41" />
<hkern u1="f" u2="&#x2d;" k="42" />
<hkern u1="f" u2="&#x2c;" k="41" />
<hkern u1="f" u2="&#x26;" k="9" />
<hkern u1="g" u2="&#xf0;" k="5" />
<hkern u1="g" u2="j" k="-19" />
<hkern u1="g" u2="\" k="6" />
<hkern u1="h" u2="&#x2122;" k="16" />
<hkern u1="h" u2="&#x7d;" k="24" />
<hkern u1="h" u2="v" k="5" />
<hkern u1="h" u2="]" k="26" />
<hkern u1="h" u2="\" k="36" />
<hkern u1="h" u2="V" k="29" />
<hkern u1="h" u2="&#x3f;" k="22" />
<hkern u1="h" u2="&#x2a;" k="3" />
<hkern u1="h" u2="&#x29;" k="4" />
<hkern u1="i" u2="&#xef;" k="-3" />
<hkern u1="i" u2="&#xec;" k="-7" />
<hkern u1="j" u2="&#xef;" k="-3" />
<hkern u1="j" u2="&#xec;" k="-7" />
<hkern u1="k" u2="&#xf0;" k="17" />
<hkern u1="k" u2="&#x7d;" k="13" />
<hkern u1="k" u2="]" k="17" />
<hkern u1="k" u2="\" k="7" />
<hkern u1="k" u2="V" k="9" />
<hkern u1="k" u2="&#x3f;" k="7" />
<hkern u1="l" u2="&#xec;" k="-5" />
<hkern u1="l" u2="&#xb7;" k="54" />
<hkern u1="m" u2="&#x2122;" k="16" />
<hkern u1="m" u2="&#x7d;" k="24" />
<hkern u1="m" u2="v" k="5" />
<hkern u1="m" u2="]" k="26" />
<hkern u1="m" u2="\" k="36" />
<hkern u1="m" u2="V" k="29" />
<hkern u1="m" u2="&#x3f;" k="22" />
<hkern u1="m" u2="&#x2a;" k="3" />
<hkern u1="m" u2="&#x29;" k="4" />
<hkern u1="n" u2="&#x2122;" k="16" />
<hkern u1="n" u2="&#x7d;" k="24" />
<hkern u1="n" u2="v" k="5" />
<hkern u1="n" u2="]" k="26" />
<hkern u1="n" u2="\" k="36" />
<hkern u1="n" u2="V" k="29" />
<hkern u1="n" u2="&#x3f;" k="22" />
<hkern u1="n" u2="&#x2a;" k="3" />
<hkern u1="n" u2="&#x29;" k="4" />
<hkern u1="o" u2="&#x2122;" k="15" />
<hkern u1="o" u2="&#xc6;" k="7" />
<hkern u1="o" u2="&#x7d;" k="33" />
<hkern u1="o" u2="x" k="12" />
<hkern u1="o" u2="v" k="9" />
<hkern u1="o" u2="]" k="38" />
<hkern u1="o" u2="\" k="37" />
<hkern u1="o" u2="X" k="21" />
<hkern u1="o" u2="V" k="32" />
<hkern u1="o" u2="&#x3f;" k="23" />
<hkern u1="o" u2="&#x2a;" k="3" />
<hkern u1="o" u2="&#x29;" k="18" />
<hkern u1="p" u2="&#x2122;" k="17" />
<hkern u1="p" u2="&#xc6;" k="7" />
<hkern u1="p" u2="&#x7d;" k="33" />
<hkern u1="p" u2="x" k="12" />
<hkern u1="p" u2="v" k="8" />
<hkern u1="p" u2="]" k="37" />
<hkern u1="p" u2="\" k="36" />
<hkern u1="p" u2="X" k="20" />
<hkern u1="p" u2="V" k="31" />
<hkern u1="p" u2="&#x3f;" k="25" />
<hkern u1="p" u2="&#x2a;" k="8" />
<hkern u1="p" u2="&#x29;" k="18" />
<hkern u1="q" u2="&#x2122;" k="11" />
<hkern u1="q" u2="&#x7d;" k="23" />
<hkern u1="q" u2="]" k="25" />
<hkern u1="q" u2="\" k="23" />
<hkern u1="q" u2="V" k="26" />
<hkern u1="q" u2="&#x3f;" k="13" />
<hkern u1="q" u2="&#x29;" k="4" />
<hkern u1="r" u2="&#xf0;" k="40" />
<hkern u1="r" u2="&#xc6;" k="42" />
<hkern u1="r" u2="&#x7d;" k="23" />
<hkern u1="r" u2="]" k="29" />
<hkern u1="r" u2="\" k="4" />
<hkern u1="r" u2="X" k="27" />
<hkern u1="r" u2="&#x2f;" k="34" />
<hkern u1="r" u2="&#x29;" k="4" />
<hkern u1="r" u2="&#x26;" k="10" />
<hkern u1="s" u2="&#x2122;" k="12" />
<hkern u1="s" u2="&#xc6;" k="5" />
<hkern u1="s" u2="&#x7d;" k="27" />
<hkern u1="s" u2="x" k="3" />
<hkern u1="s" u2="v" k="7" />
<hkern u1="s" u2="]" k="32" />
<hkern u1="s" u2="\" k="22" />
<hkern u1="s" u2="X" k="3" />
<hkern u1="s" u2="V" k="22" />
<hkern u1="s" u2="&#x3f;" k="13" />
<hkern u1="s" u2="&#x29;" k="5" />
<hkern u1="t" u2="&#xf0;" k="4" />
<hkern u1="t" u2="&#x7d;" k="8" />
<hkern u1="t" u2="]" k="11" />
<hkern u1="t" u2="\" k="7" />
<hkern u1="u" u2="&#x2122;" k="11" />
<hkern u1="u" u2="&#x7d;" k="23" />
<hkern u1="u" u2="]" k="25" />
<hkern u1="u" u2="\" k="23" />
<hkern u1="u" u2="V" k="26" />
<hkern u1="u" u2="&#x3f;" k="13" />
<hkern u1="u" u2="&#x29;" k="4" />
<hkern u1="v" u2="&#x2039;" k="12" />
<hkern u1="v" u2="&#x2026;" k="35" />
<hkern u1="v" u2="&#x201e;" k="35" />
<hkern u1="v" u2="&#x201a;" k="35" />
<hkern u1="v" u2="&#x2014;" k="13" />
<hkern u1="v" u2="&#x2013;" k="13" />
<hkern u1="v" u2="&#x178;" k="41" />
<hkern u1="v" u2="&#x153;" k="9" />
<hkern u1="v" u2="&#xf8;" k="9" />
<hkern u1="v" u2="&#xf6;" k="9" />
<hkern u1="v" u2="&#xf5;" k="9" />
<hkern u1="v" u2="&#xf4;" k="9" />
<hkern u1="v" u2="&#xf3;" k="9" />
<hkern u1="v" u2="&#xf2;" k="9" />
<hkern u1="v" u2="&#xf0;" k="13" />
<hkern u1="v" u2="&#xeb;" k="9" />
<hkern u1="v" u2="&#xea;" k="9" />
<hkern u1="v" u2="&#xe9;" k="9" />
<hkern u1="v" u2="&#xe8;" k="9" />
<hkern u1="v" u2="&#xe7;" k="9" />
<hkern u1="v" u2="&#xe6;" k="8" />
<hkern u1="v" u2="&#xe5;" k="8" />
<hkern u1="v" u2="&#xe4;" k="8" />
<hkern u1="v" u2="&#xe3;" k="8" />
<hkern u1="v" u2="&#xe2;" k="8" />
<hkern u1="v" u2="&#xe1;" k="8" />
<hkern u1="v" u2="&#xe0;" k="8" />
<hkern u1="v" u2="&#xdd;" k="41" />
<hkern u1="v" u2="&#xc6;" k="21" />
<hkern u1="v" u2="&#xc5;" k="18" />
<hkern u1="v" u2="&#xc4;" k="18" />
<hkern u1="v" u2="&#xc3;" k="18" />
<hkern u1="v" u2="&#xc2;" k="18" />
<hkern u1="v" u2="&#xc1;" k="18" />
<hkern u1="v" u2="&#xc0;" k="18" />
<hkern u1="v" u2="&#xab;" k="12" />
<hkern u1="v" u2="&#x7d;" k="27" />
<hkern u1="v" u2="s" k="6" />
<hkern u1="v" u2="q" k="8" />
<hkern u1="v" u2="o" k="9" />
<hkern u1="v" u2="g" k="9" />
<hkern u1="v" u2="e" k="9" />
<hkern u1="v" u2="d" k="8" />
<hkern u1="v" u2="c" k="9" />
<hkern u1="v" u2="a" k="8" />
<hkern u1="v" u2="]" k="32" />
<hkern u1="v" u2="\" k="7" />
<hkern u1="v" u2="Z" k="15" />
<hkern u1="v" u2="Y" k="41" />
<hkern u1="v" u2="X" k="26" />
<hkern u1="v" u2="V" k="8" />
<hkern u1="v" u2="T" k="67" />
<hkern u1="v" u2="J" k="26" />
<hkern u1="v" u2="A" k="18" />
<hkern u1="v" u2="&#x3f;" k="9" />
<hkern u1="v" u2="&#x2f;" k="20" />
<hkern u1="v" u2="&#x2e;" k="35" />
<hkern u1="v" u2="&#x2d;" k="13" />
<hkern u1="v" u2="&#x2c;" k="35" />
<hkern u1="v" u2="&#x29;" k="4" />
<hkern u1="w" u2="&#xf0;" k="9" />
<hkern u1="w" u2="&#xc6;" k="18" />
<hkern u1="w" u2="&#x7d;" k="28" />
<hkern u1="w" u2="]" k="33" />
<hkern u1="w" u2="\" k="7" />
<hkern u1="w" u2="X" k="25" />
<hkern u1="w" u2="V" k="10" />
<hkern u1="w" u2="&#x3f;" k="10" />
<hkern u1="w" u2="&#x2f;" k="17" />
<hkern u1="w" u2="&#x29;" k="6" />
<hkern u1="x" u2="&#x2039;" k="26" />
<hkern u1="x" u2="&#x2014;" k="32" />
<hkern u1="x" u2="&#x2013;" k="32" />
<hkern u1="x" u2="&#x178;" k="39" />
<hkern u1="x" u2="&#x153;" k="12" />
<hkern u1="x" u2="&#xf8;" k="12" />
<hkern u1="x" u2="&#xf6;" k="12" />
<hkern u1="x" u2="&#xf5;" k="12" />
<hkern u1="x" u2="&#xf4;" k="12" />
<hkern u1="x" u2="&#xf3;" k="12" />
<hkern u1="x" u2="&#xf2;" k="12" />
<hkern u1="x" u2="&#xf0;" k="18" />
<hkern u1="x" u2="&#xeb;" k="12" />
<hkern u1="x" u2="&#xea;" k="12" />
<hkern u1="x" u2="&#xe9;" k="12" />
<hkern u1="x" u2="&#xe8;" k="12" />
<hkern u1="x" u2="&#xe7;" k="12" />
<hkern u1="x" u2="&#xe6;" k="4" />
<hkern u1="x" u2="&#xe5;" k="4" />
<hkern u1="x" u2="&#xe4;" k="4" />
<hkern u1="x" u2="&#xe3;" k="4" />
<hkern u1="x" u2="&#xe2;" k="4" />
<hkern u1="x" u2="&#xe1;" k="4" />
<hkern u1="x" u2="&#xe0;" k="4" />
<hkern u1="x" u2="&#xdd;" k="39" />
<hkern u1="x" u2="&#xab;" k="26" />
<hkern u1="x" u2="&#x7d;" k="16" />
<hkern u1="x" u2="q" k="13" />
<hkern u1="x" u2="o" k="12" />
<hkern u1="x" u2="g" k="10" />
<hkern u1="x" u2="e" k="12" />
<hkern u1="x" u2="d" k="13" />
<hkern u1="x" u2="c" k="12" />
<hkern u1="x" u2="a" k="4" />
<hkern u1="x" u2="]" k="19" />
<hkern u1="x" u2="\" k="6" />
<hkern u1="x" u2="Y" k="39" />
<hkern u1="x" u2="V" k="7" />
<hkern u1="x" u2="T" k="72" />
<hkern u1="x" u2="J" k="3" />
<hkern u1="x" u2="&#x2d;" k="32" />
<hkern u1="y" u2="&#xf0;" k="14" />
<hkern u1="y" u2="&#xc6;" k="21" />
<hkern u1="y" u2="&#x7d;" k="24" />
<hkern u1="y" u2="]" k="29" />
<hkern u1="y" u2="\" k="7" />
<hkern u1="y" u2="X" k="26" />
<hkern u1="y" u2="V" k="8" />
<hkern u1="y" u2="&#x3f;" k="9" />
<hkern u1="y" u2="&#x2f;" k="21" />
<hkern u1="z" u2="&#x2122;" k="5" />
<hkern u1="z" u2="&#xf0;" k="8" />
<hkern u1="z" u2="&#x7d;" k="17" />
<hkern u1="z" u2="]" k="20" />
<hkern u1="z" u2="\" k="10" />
<hkern u1="z" u2="V" k="13" />
<hkern u1="z" u2="&#x3f;" k="8" />
<hkern u1="&#x7b;" u2="&#x153;" k="33" />
<hkern u1="&#x7b;" u2="&#x152;" k="25" />
<hkern u1="&#x7b;" u2="&#xff;" k="25" />
<hkern u1="&#x7b;" u2="&#xfd;" k="25" />
<hkern u1="&#x7b;" u2="&#xfc;" k="30" />
<hkern u1="&#x7b;" u2="&#xfb;" k="30" />
<hkern u1="&#x7b;" u2="&#xfa;" k="30" />
<hkern u1="&#x7b;" u2="&#xf9;" k="30" />
<hkern u1="&#x7b;" u2="&#xf8;" k="33" />
<hkern u1="&#x7b;" u2="&#xf6;" k="33" />
<hkern u1="&#x7b;" u2="&#xf5;" k="33" />
<hkern u1="&#x7b;" u2="&#xf4;" k="33" />
<hkern u1="&#x7b;" u2="&#xf3;" k="33" />
<hkern u1="&#x7b;" u2="&#xf2;" k="33" />
<hkern u1="&#x7b;" u2="&#xf1;" k="23" />
<hkern u1="&#x7b;" u2="&#xf0;" k="9" />
<hkern u1="&#x7b;" u2="&#xef;" k="-15" />
<hkern u1="&#x7b;" u2="&#xec;" k="-38" />
<hkern u1="&#x7b;" u2="&#xeb;" k="33" />
<hkern u1="&#x7b;" u2="&#xea;" k="33" />
<hkern u1="&#x7b;" u2="&#xe9;" k="33" />
<hkern u1="&#x7b;" u2="&#xe8;" k="33" />
<hkern u1="&#x7b;" u2="&#xe7;" k="33" />
<hkern u1="&#x7b;" u2="&#xe6;" k="27" />
<hkern u1="&#x7b;" u2="&#xe5;" k="27" />
<hkern u1="&#x7b;" u2="&#xe4;" k="27" />
<hkern u1="&#x7b;" u2="&#xe3;" k="27" />
<hkern u1="&#x7b;" u2="&#xe2;" k="27" />
<hkern u1="&#x7b;" u2="&#xe1;" k="27" />
<hkern u1="&#x7b;" u2="&#xe0;" k="27" />
<hkern u1="&#x7b;" u2="&#xd8;" k="25" />
<hkern u1="&#x7b;" u2="&#xd6;" k="25" />
<hkern u1="&#x7b;" u2="&#xd5;" k="25" />
<hkern u1="&#x7b;" u2="&#xd4;" k="25" />
<hkern u1="&#x7b;" u2="&#xd3;" k="25" />
<hkern u1="&#x7b;" u2="&#xd2;" k="25" />
<hkern u1="&#x7b;" u2="&#xc7;" k="24" />
<hkern u1="&#x7b;" u2="&#xc6;" k="21" />
<hkern u1="&#x7b;" u2="&#xc5;" k="20" />
<hkern u1="&#x7b;" u2="&#xc4;" k="20" />
<hkern u1="&#x7b;" u2="&#xc3;" k="20" />
<hkern u1="&#x7b;" u2="&#xc2;" k="20" />
<hkern u1="&#x7b;" u2="&#xc1;" k="20" />
<hkern u1="&#x7b;" u2="&#xc0;" k="20" />
<hkern u1="&#x7b;" u2="&#x7b;" k="19" />
<hkern u1="&#x7b;" u2="z" k="17" />
<hkern u1="&#x7b;" u2="y" k="25" />
<hkern u1="&#x7b;" u2="x" k="16" />
<hkern u1="&#x7b;" u2="w" k="28" />
<hkern u1="&#x7b;" u2="v" k="26" />
<hkern u1="&#x7b;" u2="u" k="30" />
<hkern u1="&#x7b;" u2="t" k="18" />
<hkern u1="&#x7b;" u2="s" k="22" />
<hkern u1="&#x7b;" u2="r" k="23" />
<hkern u1="&#x7b;" u2="q" k="33" />
<hkern u1="&#x7b;" u2="p" k="23" />
<hkern u1="&#x7b;" u2="o" k="33" />
<hkern u1="&#x7b;" u2="n" k="23" />
<hkern u1="&#x7b;" u2="m" k="23" />
<hkern u1="&#x7b;" u2="j" k="-8" />
<hkern u1="&#x7b;" u2="f" k="11" />
<hkern u1="&#x7b;" u2="e" k="33" />
<hkern u1="&#x7b;" u2="d" k="33" />
<hkern u1="&#x7b;" u2="c" k="33" />
<hkern u1="&#x7b;" u2="a" k="27" />
<hkern u1="&#x7b;" u2="S" k="11" />
<hkern u1="&#x7b;" u2="Q" k="25" />
<hkern u1="&#x7b;" u2="O" k="25" />
<hkern u1="&#x7b;" u2="G" k="25" />
<hkern u1="&#x7b;" u2="C" k="24" />
<hkern u1="&#x7b;" u2="A" k="20" />
<hkern u1="&#x7b;" u2="&#x28;" k="8" />
<hkern u1="&#x7c;" u2="&#xec;" k="-6" />
<hkern u1="&#x7d;" u2="&#x7d;" k="19" />
<hkern u1="&#x7d;" u2="]" k="21" />
<hkern u1="&#x7d;" u2="&#x29;" k="11" />
<hkern u1="&#xa1;" u2="&#x178;" k="32" />
<hkern u1="&#xa1;" u2="&#xdd;" k="32" />
<hkern u1="&#xa1;" u2="Y" k="32" />
<hkern u1="&#xa1;" u2="V" k="8" />
<hkern u1="&#xa1;" u2="T" k="48" />
<hkern u1="&#xab;" u2="V" k="19" />
<hkern u1="&#xae;" u2="&#x178;" k="23" />
<hkern u1="&#xae;" u2="&#xdd;" k="23" />
<hkern u1="&#xae;" u2="&#xc6;" k="33" />
<hkern u1="&#xae;" u2="&#xc5;" k="27" />
<hkern u1="&#xae;" u2="&#xc4;" k="27" />
<hkern u1="&#xae;" u2="&#xc3;" k="27" />
<hkern u1="&#xae;" u2="&#xc2;" k="27" />
<hkern u1="&#xae;" u2="&#xc1;" k="27" />
<hkern u1="&#xae;" u2="&#xc0;" k="27" />
<hkern u1="&#xae;" u2="Z" k="13" />
<hkern u1="&#xae;" u2="Y" k="23" />
<hkern u1="&#xae;" u2="X" k="10" />
<hkern u1="&#xae;" u2="V" k="7" />
<hkern u1="&#xae;" u2="T" k="9" />
<hkern u1="&#xae;" u2="J" k="24" />
<hkern u1="&#xae;" u2="A" k="27" />
<hkern u1="&#xb7;" u2="l" k="54" />
<hkern u1="&#xbb;" u2="&#xc6;" k="7" />
<hkern u1="&#xbb;" u2="x" k="26" />
<hkern u1="&#xbb;" u2="v" k="8" />
<hkern u1="&#xbb;" u2="f" k="4" />
<hkern u1="&#xbb;" u2="X" k="24" />
<hkern u1="&#xbb;" u2="V" k="28" />
<hkern u1="&#xbf;" u2="&#x178;" k="66" />
<hkern u1="&#xbf;" u2="&#x153;" k="33" />
<hkern u1="&#xbf;" u2="&#x152;" k="28" />
<hkern u1="&#xbf;" u2="&#xff;" k="30" />
<hkern u1="&#xbf;" u2="&#xfe;" k="31" />
<hkern u1="&#xbf;" u2="&#xfd;" k="30" />
<hkern u1="&#xbf;" u2="&#xfc;" k="31" />
<hkern u1="&#xbf;" u2="&#xfb;" k="31" />
<hkern u1="&#xbf;" u2="&#xfa;" k="31" />
<hkern u1="&#xbf;" u2="&#xf9;" k="31" />
<hkern u1="&#xbf;" u2="&#xf8;" k="33" />
<hkern u1="&#xbf;" u2="&#xf6;" k="33" />
<hkern u1="&#xbf;" u2="&#xf5;" k="33" />
<hkern u1="&#xbf;" u2="&#xf4;" k="33" />
<hkern u1="&#xbf;" u2="&#xf3;" k="33" />
<hkern u1="&#xbf;" u2="&#xf2;" k="33" />
<hkern u1="&#xbf;" u2="&#xf1;" k="31" />
<hkern u1="&#xbf;" u2="&#xf0;" k="33" />
<hkern u1="&#xbf;" u2="&#xef;" k="31" />
<hkern u1="&#xbf;" u2="&#xee;" k="31" />
<hkern u1="&#xbf;" u2="&#xed;" k="31" />
<hkern u1="&#xbf;" u2="&#xec;" k="31" />
<hkern u1="&#xbf;" u2="&#xeb;" k="33" />
<hkern u1="&#xbf;" u2="&#xea;" k="33" />
<hkern u1="&#xbf;" u2="&#xe9;" k="33" />
<hkern u1="&#xbf;" u2="&#xe8;" k="33" />
<hkern u1="&#xbf;" u2="&#xe7;" k="33" />
<hkern u1="&#xbf;" u2="&#xe6;" k="34" />
<hkern u1="&#xbf;" u2="&#xe5;" k="34" />
<hkern u1="&#xbf;" u2="&#xe4;" k="34" />
<hkern u1="&#xbf;" u2="&#xe3;" k="34" />
<hkern u1="&#xbf;" u2="&#xe2;" k="34" />
<hkern u1="&#xbf;" u2="&#xe1;" k="34" />
<hkern u1="&#xbf;" u2="&#xe0;" k="34" />
<hkern u1="&#xbf;" u2="&#xdf;" k="31" />
<hkern u1="&#xbf;" u2="&#xde;" k="28" />
<hkern u1="&#xbf;" u2="&#xdd;" k="66" />
<hkern u1="&#xbf;" u2="&#xdc;" k="29" />
<hkern u1="&#xbf;" u2="&#xdb;" k="29" />
<hkern u1="&#xbf;" u2="&#xda;" k="29" />
<hkern u1="&#xbf;" u2="&#xd9;" k="29" />
<hkern u1="&#xbf;" u2="&#xd8;" k="28" />
<hkern u1="&#xbf;" u2="&#xd6;" k="28" />
<hkern u1="&#xbf;" u2="&#xd5;" k="28" />
<hkern u1="&#xbf;" u2="&#xd4;" k="28" />
<hkern u1="&#xbf;" u2="&#xd3;" k="28" />
<hkern u1="&#xbf;" u2="&#xd2;" k="28" />
<hkern u1="&#xbf;" u2="&#xd1;" k="28" />
<hkern u1="&#xbf;" u2="&#xd0;" k="28" />
<hkern u1="&#xbf;" u2="&#xcf;" k="28" />
<hkern u1="&#xbf;" u2="&#xce;" k="28" />
<hkern u1="&#xbf;" u2="&#xcd;" k="28" />
<hkern u1="&#xbf;" u2="&#xcc;" k="28" />
<hkern u1="&#xbf;" u2="&#xcb;" k="28" />
<hkern u1="&#xbf;" u2="&#xca;" k="28" />
<hkern u1="&#xbf;" u2="&#xc9;" k="28" />
<hkern u1="&#xbf;" u2="&#xc8;" k="28" />
<hkern u1="&#xbf;" u2="&#xc7;" k="28" />
<hkern u1="&#xbf;" u2="&#xc6;" k="40" />
<hkern u1="&#xbf;" u2="&#xc5;" k="38" />
<hkern u1="&#xbf;" u2="&#xc4;" k="38" />
<hkern u1="&#xbf;" u2="&#xc3;" k="38" />
<hkern u1="&#xbf;" u2="&#xc2;" k="38" />
<hkern u1="&#xbf;" u2="&#xc1;" k="38" />
<hkern u1="&#xbf;" u2="&#xc0;" k="38" />
<hkern u1="&#xbf;" u2="z" k="29" />
<hkern u1="&#xbf;" u2="y" k="30" />
<hkern u1="&#xbf;" u2="x" k="27" />
<hkern u1="&#xbf;" u2="w" k="32" />
<hkern u1="&#xbf;" u2="v" k="32" />
<hkern u1="&#xbf;" u2="u" k="31" />
<hkern u1="&#xbf;" u2="t" k="28" />
<hkern u1="&#xbf;" u2="s" k="32" />
<hkern u1="&#xbf;" u2="r" k="31" />
<hkern u1="&#xbf;" u2="q" k="33" />
<hkern u1="&#xbf;" u2="p" k="31" />
<hkern u1="&#xbf;" u2="o" k="33" />
<hkern u1="&#xbf;" u2="n" k="31" />
<hkern u1="&#xbf;" u2="m" k="31" />
<hkern u1="&#xbf;" u2="l" k="31" />
<hkern u1="&#xbf;" u2="k" k="31" />
<hkern u1="&#xbf;" u2="j" k="31" />
<hkern u1="&#xbf;" u2="i" k="31" />
<hkern u1="&#xbf;" u2="h" k="31" />
<hkern u1="&#xbf;" u2="f" k="27" />
<hkern u1="&#xbf;" u2="e" k="33" />
<hkern u1="&#xbf;" u2="d" k="33" />
<hkern u1="&#xbf;" u2="c" k="33" />
<hkern u1="&#xbf;" u2="b" k="31" />
<hkern u1="&#xbf;" u2="a" k="34" />
<hkern u1="&#xbf;" u2="Z" k="37" />
<hkern u1="&#xbf;" u2="Y" k="66" />
<hkern u1="&#xbf;" u2="X" k="37" />
<hkern u1="&#xbf;" u2="W" k="37" />
<hkern u1="&#xbf;" u2="V" k="44" />
<hkern u1="&#xbf;" u2="U" k="29" />
<hkern u1="&#xbf;" u2="T" k="79" />
<hkern u1="&#xbf;" u2="S" k="28" />
<hkern u1="&#xbf;" u2="R" k="28" />
<hkern u1="&#xbf;" u2="Q" k="28" />
<hkern u1="&#xbf;" u2="P" k="28" />
<hkern u1="&#xbf;" u2="O" k="28" />
<hkern u1="&#xbf;" u2="N" k="28" />
<hkern u1="&#xbf;" u2="M" k="28" />
<hkern u1="&#xbf;" u2="L" k="28" />
<hkern u1="&#xbf;" u2="K" k="28" />
<hkern u1="&#xbf;" u2="J" k="13" />
<hkern u1="&#xbf;" u2="I" k="28" />
<hkern u1="&#xbf;" u2="H" k="28" />
<hkern u1="&#xbf;" u2="G" k="28" />
<hkern u1="&#xbf;" u2="F" k="28" />
<hkern u1="&#xbf;" u2="E" k="28" />
<hkern u1="&#xbf;" u2="D" k="28" />
<hkern u1="&#xbf;" u2="C" k="28" />
<hkern u1="&#xbf;" u2="B" k="28" />
<hkern u1="&#xbf;" u2="A" k="38" />
<hkern u1="&#xc0;" u2="&#x2122;" k="41" />
<hkern u1="&#xc0;" u2="&#xf0;" k="5" />
<hkern u1="&#xc0;" u2="&#xae;" k="26" />
<hkern u1="&#xc0;" u2="&#x7d;" k="20" />
<hkern u1="&#xc0;" u2="v" k="18" />
<hkern u1="&#xc0;" u2="f" k="7" />
<hkern u1="&#xc0;" u2="]" k="23" />
<hkern u1="&#xc0;" u2="\" k="47" />
<hkern u1="&#xc0;" u2="V" k="28" />
<hkern u1="&#xc0;" u2="&#x3f;" k="25" />
<hkern u1="&#xc0;" u2="&#x2a;" k="36" />
<hkern u1="&#xc1;" u2="&#x2122;" k="41" />
<hkern u1="&#xc1;" u2="&#xf0;" k="5" />
<hkern u1="&#xc1;" u2="&#xae;" k="26" />
<hkern u1="&#xc1;" u2="&#x7d;" k="20" />
<hkern u1="&#xc1;" u2="v" k="18" />
<hkern u1="&#xc1;" u2="f" k="7" />
<hkern u1="&#xc1;" u2="]" k="23" />
<hkern u1="&#xc1;" u2="\" k="47" />
<hkern u1="&#xc1;" u2="V" k="28" />
<hkern u1="&#xc1;" u2="&#x3f;" k="25" />
<hkern u1="&#xc1;" u2="&#x2a;" k="36" />
<hkern u1="&#xc2;" u2="&#x2122;" k="41" />
<hkern u1="&#xc2;" u2="&#xf0;" k="5" />
<hkern u1="&#xc2;" u2="&#xae;" k="26" />
<hkern u1="&#xc2;" u2="&#x7d;" k="20" />
<hkern u1="&#xc2;" u2="v" k="18" />
<hkern u1="&#xc2;" u2="f" k="7" />
<hkern u1="&#xc2;" u2="]" k="23" />
<hkern u1="&#xc2;" u2="\" k="47" />
<hkern u1="&#xc2;" u2="V" k="28" />
<hkern u1="&#xc2;" u2="&#x3f;" k="25" />
<hkern u1="&#xc2;" u2="&#x2a;" k="36" />
<hkern u1="&#xc3;" u2="&#x2122;" k="41" />
<hkern u1="&#xc3;" u2="&#xf0;" k="5" />
<hkern u1="&#xc3;" u2="&#xae;" k="26" />
<hkern u1="&#xc3;" u2="&#x7d;" k="20" />
<hkern u1="&#xc3;" u2="v" k="18" />
<hkern u1="&#xc3;" u2="f" k="7" />
<hkern u1="&#xc3;" u2="]" k="23" />
<hkern u1="&#xc3;" u2="\" k="47" />
<hkern u1="&#xc3;" u2="V" k="28" />
<hkern u1="&#xc3;" u2="&#x3f;" k="25" />
<hkern u1="&#xc3;" u2="&#x2a;" k="36" />
<hkern u1="&#xc4;" u2="&#x2122;" k="41" />
<hkern u1="&#xc4;" u2="&#xf0;" k="5" />
<hkern u1="&#xc4;" u2="&#xae;" k="26" />
<hkern u1="&#xc4;" u2="&#x7d;" k="20" />
<hkern u1="&#xc4;" u2="v" k="18" />
<hkern u1="&#xc4;" u2="f" k="7" />
<hkern u1="&#xc4;" u2="]" k="23" />
<hkern u1="&#xc4;" u2="\" k="47" />
<hkern u1="&#xc4;" u2="V" k="28" />
<hkern u1="&#xc4;" u2="&#x3f;" k="25" />
<hkern u1="&#xc4;" u2="&#x2a;" k="36" />
<hkern u1="&#xc5;" u2="&#x2122;" k="41" />
<hkern u1="&#xc5;" u2="&#xf0;" k="5" />
<hkern u1="&#xc5;" u2="&#xae;" k="26" />
<hkern u1="&#xc5;" u2="&#x7d;" k="20" />
<hkern u1="&#xc5;" u2="v" k="18" />
<hkern u1="&#xc5;" u2="f" k="7" />
<hkern u1="&#xc5;" u2="]" k="23" />
<hkern u1="&#xc5;" u2="\" k="47" />
<hkern u1="&#xc5;" u2="V" k="28" />
<hkern u1="&#xc5;" u2="&#x3f;" k="25" />
<hkern u1="&#xc5;" u2="&#x2a;" k="36" />
<hkern u1="&#xc6;" u2="&#xf0;" k="8" />
<hkern u1="&#xc6;" u2="&#xef;" k="-13" />
<hkern u1="&#xc6;" u2="&#xee;" k="-12" />
<hkern u1="&#xc6;" u2="&#xec;" k="-36" />
<hkern u1="&#xc6;" u2="v" k="11" />
<hkern u1="&#xc6;" u2="f" k="3" />
<hkern u1="&#xc7;" u2="&#xf0;" k="7" />
<hkern u1="&#xc7;" u2="&#xef;" k="-14" />
<hkern u1="&#xc7;" u2="&#xee;" k="-12" />
<hkern u1="&#xc7;" u2="&#xec;" k="-33" />
<hkern u1="&#xc7;" u2="&#xae;" k="13" />
<hkern u1="&#xc7;" u2="v" k="16" />
<hkern u1="&#xc7;" u2="f" k="6" />
<hkern u1="&#xc8;" u2="&#xf0;" k="8" />
<hkern u1="&#xc8;" u2="&#xef;" k="-13" />
<hkern u1="&#xc8;" u2="&#xee;" k="-12" />
<hkern u1="&#xc8;" u2="&#xec;" k="-36" />
<hkern u1="&#xc8;" u2="v" k="11" />
<hkern u1="&#xc8;" u2="f" k="3" />
<hkern u1="&#xc9;" u2="&#xf0;" k="8" />
<hkern u1="&#xc9;" u2="&#xef;" k="-13" />
<hkern u1="&#xc9;" u2="&#xee;" k="-12" />
<hkern u1="&#xc9;" u2="&#xec;" k="-36" />
<hkern u1="&#xc9;" u2="v" k="11" />
<hkern u1="&#xc9;" u2="f" k="3" />
<hkern u1="&#xca;" u2="&#xf0;" k="8" />
<hkern u1="&#xca;" u2="&#xef;" k="-13" />
<hkern u1="&#xca;" u2="&#xee;" k="-12" />
<hkern u1="&#xca;" u2="&#xec;" k="-36" />
<hkern u1="&#xca;" u2="v" k="11" />
<hkern u1="&#xca;" u2="f" k="3" />
<hkern u1="&#xcb;" u2="&#xf0;" k="8" />
<hkern u1="&#xcb;" u2="&#xef;" k="-13" />
<hkern u1="&#xcb;" u2="&#xee;" k="-12" />
<hkern u1="&#xcb;" u2="&#xec;" k="-36" />
<hkern u1="&#xcb;" u2="v" k="11" />
<hkern u1="&#xcb;" u2="f" k="3" />
<hkern u1="&#xcc;" u2="&#xf0;" k="3" />
<hkern u1="&#xcc;" u2="&#xec;" k="-3" />
<hkern u1="&#xcd;" u2="&#xf0;" k="3" />
<hkern u1="&#xcd;" u2="&#xec;" k="-3" />
<hkern u1="&#xce;" u2="&#xf0;" k="3" />
<hkern u1="&#xce;" u2="&#xec;" k="-3" />
<hkern u1="&#xcf;" u2="&#xf0;" k="3" />
<hkern u1="&#xcf;" u2="&#xec;" k="-3" />
<hkern u1="&#xd0;" u2="&#xc6;" k="17" />
<hkern u1="&#xd0;" u2="&#x7d;" k="26" />
<hkern u1="&#xd0;" u2="x" k="3" />
<hkern u1="&#xd0;" u2="]" k="29" />
<hkern u1="&#xd0;" u2="\" k="7" />
<hkern u1="&#xd0;" u2="X" k="21" />
<hkern u1="&#xd0;" u2="V" k="12" />
<hkern u1="&#xd0;" u2="&#x3f;" k="9" />
<hkern u1="&#xd0;" u2="&#x2f;" k="6" />
<hkern u1="&#xd0;" u2="&#x29;" k="12" />
<hkern u1="&#xd1;" u2="&#xf0;" k="3" />
<hkern u1="&#xd1;" u2="&#xec;" k="-3" />
<hkern u1="&#xd2;" u2="&#xc6;" k="15" />
<hkern u1="&#xd2;" u2="&#x7d;" k="25" />
<hkern u1="&#xd2;" u2="]" k="28" />
<hkern u1="&#xd2;" u2="\" k="8" />
<hkern u1="&#xd2;" u2="X" k="19" />
<hkern u1="&#xd2;" u2="V" k="12" />
<hkern u1="&#xd2;" u2="&#x3f;" k="7" />
<hkern u1="&#xd2;" u2="&#x2f;" k="6" />
<hkern u1="&#xd2;" u2="&#x29;" k="5" />
<hkern u1="&#xd3;" u2="&#xc6;" k="15" />
<hkern u1="&#xd3;" u2="&#x7d;" k="25" />
<hkern u1="&#xd3;" u2="]" k="28" />
<hkern u1="&#xd3;" u2="\" k="8" />
<hkern u1="&#xd3;" u2="X" k="19" />
<hkern u1="&#xd3;" u2="V" k="12" />
<hkern u1="&#xd3;" u2="&#x3f;" k="7" />
<hkern u1="&#xd3;" u2="&#x2f;" k="6" />
<hkern u1="&#xd3;" u2="&#x29;" k="5" />
<hkern u1="&#xd4;" u2="&#xc6;" k="15" />
<hkern u1="&#xd4;" u2="&#x7d;" k="25" />
<hkern u1="&#xd4;" u2="]" k="28" />
<hkern u1="&#xd4;" u2="\" k="8" />
<hkern u1="&#xd4;" u2="X" k="19" />
<hkern u1="&#xd4;" u2="V" k="12" />
<hkern u1="&#xd4;" u2="&#x3f;" k="7" />
<hkern u1="&#xd4;" u2="&#x2f;" k="6" />
<hkern u1="&#xd4;" u2="&#x29;" k="5" />
<hkern u1="&#xd5;" u2="&#xc6;" k="15" />
<hkern u1="&#xd5;" u2="&#x7d;" k="25" />
<hkern u1="&#xd5;" u2="]" k="28" />
<hkern u1="&#xd5;" u2="\" k="8" />
<hkern u1="&#xd5;" u2="X" k="19" />
<hkern u1="&#xd5;" u2="V" k="12" />
<hkern u1="&#xd5;" u2="&#x3f;" k="7" />
<hkern u1="&#xd5;" u2="&#x2f;" k="6" />
<hkern u1="&#xd5;" u2="&#x29;" k="5" />
<hkern u1="&#xd6;" u2="&#xc6;" k="15" />
<hkern u1="&#xd6;" u2="&#x7d;" k="25" />
<hkern u1="&#xd6;" u2="]" k="28" />
<hkern u1="&#xd6;" u2="\" k="8" />
<hkern u1="&#xd6;" u2="X" k="19" />
<hkern u1="&#xd6;" u2="V" k="12" />
<hkern u1="&#xd6;" u2="&#x3f;" k="7" />
<hkern u1="&#xd6;" u2="&#x2f;" k="6" />
<hkern u1="&#xd6;" u2="&#x29;" k="5" />
<hkern u1="&#xd8;" u2="&#xc6;" k="15" />
<hkern u1="&#xd8;" u2="&#x7d;" k="25" />
<hkern u1="&#xd8;" u2="]" k="28" />
<hkern u1="&#xd8;" u2="\" k="8" />
<hkern u1="&#xd8;" u2="X" k="19" />
<hkern u1="&#xd8;" u2="V" k="12" />
<hkern u1="&#xd8;" u2="&#x3f;" k="7" />
<hkern u1="&#xd8;" u2="&#x2f;" k="6" />
<hkern u1="&#xd8;" u2="&#x29;" k="5" />
<hkern u1="&#xd9;" u2="&#xf0;" k="3" />
<hkern u1="&#xd9;" u2="&#xec;" k="-6" />
<hkern u1="&#xd9;" u2="&#xc6;" k="9" />
<hkern u1="&#xd9;" u2="&#x2f;" k="6" />
<hkern u1="&#xda;" u2="&#xf0;" k="3" />
<hkern u1="&#xda;" u2="&#xec;" k="-6" />
<hkern u1="&#xda;" u2="&#xc6;" k="9" />
<hkern u1="&#xda;" u2="&#x2f;" k="6" />
<hkern u1="&#xdb;" u2="&#xf0;" k="3" />
<hkern u1="&#xdb;" u2="&#xec;" k="-6" />
<hkern u1="&#xdb;" u2="&#xc6;" k="9" />
<hkern u1="&#xdb;" u2="&#x2f;" k="6" />
<hkern u1="&#xdc;" u2="&#xf0;" k="3" />
<hkern u1="&#xdc;" u2="&#xec;" k="-6" />
<hkern u1="&#xdc;" u2="&#xc6;" k="9" />
<hkern u1="&#xdc;" u2="&#x2f;" k="6" />
<hkern u1="&#xdd;" u2="&#xff;" k="34" />
<hkern u1="&#xdd;" u2="&#xf0;" k="39" />
<hkern u1="&#xdd;" u2="&#xef;" k="-44" />
<hkern u1="&#xdd;" u2="&#xee;" k="-8" />
<hkern u1="&#xdd;" u2="&#xec;" k="-64" />
<hkern u1="&#xdd;" u2="&#xeb;" k="67" />
<hkern u1="&#xdd;" u2="&#xe4;" k="57" />
<hkern u1="&#xdd;" u2="&#xe3;" k="53" />
<hkern u1="&#xdd;" u2="&#xdf;" k="6" />
<hkern u1="&#xdd;" u2="&#xc6;" k="52" />
<hkern u1="&#xdd;" u2="&#xae;" k="22" />
<hkern u1="&#xdd;" u2="x" k="40" />
<hkern u1="&#xdd;" u2="v" k="41" />
<hkern u1="&#xdd;" u2="f" k="18" />
<hkern u1="&#xdd;" u2="&#x40;" k="36" />
<hkern u1="&#xdd;" u2="&#x2f;" k="64" />
<hkern u1="&#xdd;" u2="&#x26;" k="37" />
<hkern u1="&#xde;" u2="&#x2122;" k="6" />
<hkern u1="&#xde;" u2="&#x2026;" k="32" />
<hkern u1="&#xde;" u2="&#x201e;" k="32" />
<hkern u1="&#xde;" u2="&#x201a;" k="32" />
<hkern u1="&#xde;" u2="&#x178;" k="39" />
<hkern u1="&#xde;" u2="&#xdd;" k="39" />
<hkern u1="&#xde;" u2="&#xc6;" k="22" />
<hkern u1="&#xde;" u2="&#xc5;" k="18" />
<hkern u1="&#xde;" u2="&#xc4;" k="18" />
<hkern u1="&#xde;" u2="&#xc3;" k="18" />
<hkern u1="&#xde;" u2="&#xc2;" k="18" />
<hkern u1="&#xde;" u2="&#xc1;" k="18" />
<hkern u1="&#xde;" u2="&#xc0;" k="18" />
<hkern u1="&#xde;" u2="&#x7d;" k="31" />
<hkern u1="&#xde;" u2="]" k="37" />
<hkern u1="&#xde;" u2="\" k="18" />
<hkern u1="&#xde;" u2="Z" k="21" />
<hkern u1="&#xde;" u2="Y" k="39" />
<hkern u1="&#xde;" u2="X" k="40" />
<hkern u1="&#xde;" u2="W" k="6" />
<hkern u1="&#xde;" u2="V" k="15" />
<hkern u1="&#xde;" u2="T" k="49" />
<hkern u1="&#xde;" u2="J" k="23" />
<hkern u1="&#xde;" u2="A" k="18" />
<hkern u1="&#xde;" u2="&#x3f;" k="17" />
<hkern u1="&#xde;" u2="&#x2f;" k="19" />
<hkern u1="&#xde;" u2="&#x2e;" k="32" />
<hkern u1="&#xde;" u2="&#x2c;" k="32" />
<hkern u1="&#xde;" u2="&#x29;" k="15" />
<hkern u1="&#xdf;" u2="&#x2122;" k="11" />
<hkern u1="&#xdf;" u2="&#x201d;" k="14" />
<hkern u1="&#xdf;" u2="&#x201c;" k="15" />
<hkern u1="&#xdf;" u2="&#x2019;" k="14" />
<hkern u1="&#xdf;" u2="&#x2018;" k="15" />
<hkern u1="&#xdf;" u2="&#x178;" k="41" />
<hkern u1="&#xdf;" u2="&#xff;" k="17" />
<hkern u1="&#xdf;" u2="&#xfd;" k="17" />
<hkern u1="&#xdf;" u2="&#xdd;" k="41" />
<hkern u1="&#xdf;" u2="&#xc6;" k="3" />
<hkern u1="&#xdf;" u2="&#xae;" k="13" />
<hkern u1="&#xdf;" u2="&#x7d;" k="17" />
<hkern u1="&#xdf;" u2="y" k="17" />
<hkern u1="&#xdf;" u2="x" k="9" />
<hkern u1="&#xdf;" u2="w" k="11" />
<hkern u1="&#xdf;" u2="v" k="16" />
<hkern u1="&#xdf;" u2="t" k="9" />
<hkern u1="&#xdf;" u2="g" k="5" />
<hkern u1="&#xdf;" u2="f" k="5" />
<hkern u1="&#xdf;" u2="]" k="21" />
<hkern u1="&#xdf;" u2="\" k="19" />
<hkern u1="&#xdf;" u2="Y" k="41" />
<hkern u1="&#xdf;" u2="X" k="6" />
<hkern u1="&#xdf;" u2="W" k="19" />
<hkern u1="&#xdf;" u2="V" k="28" />
<hkern u1="&#xdf;" u2="T" k="31" />
<hkern u1="&#xdf;" u2="S" k="5" />
<hkern u1="&#xdf;" u2="J" k="20" />
<hkern u1="&#xdf;" u2="&#x3f;" k="9" />
<hkern u1="&#xdf;" u2="&#x2a;" k="15" />
<hkern u1="&#xdf;" u2="&#x27;" k="13" />
<hkern u1="&#xdf;" u2="&#x22;" k="13" />
<hkern u1="&#xe0;" u2="&#x2122;" k="12" />
<hkern u1="&#xe0;" u2="&#x7d;" k="9" />
<hkern u1="&#xe0;" u2="v" k="5" />
<hkern u1="&#xe0;" u2="]" k="11" />
<hkern u1="&#xe0;" u2="\" k="35" />
<hkern u1="&#xe0;" u2="V" k="25" />
<hkern u1="&#xe0;" u2="&#x3f;" k="15" />
<hkern u1="&#xe0;" u2="&#x2a;" k="3" />
<hkern u1="&#xe1;" u2="&#x2122;" k="12" />
<hkern u1="&#xe1;" u2="&#x7d;" k="9" />
<hkern u1="&#xe1;" u2="v" k="5" />
<hkern u1="&#xe1;" u2="]" k="11" />
<hkern u1="&#xe1;" u2="\" k="35" />
<hkern u1="&#xe1;" u2="V" k="25" />
<hkern u1="&#xe1;" u2="&#x3f;" k="15" />
<hkern u1="&#xe1;" u2="&#x2a;" k="3" />
<hkern u1="&#xe2;" u2="&#x2122;" k="12" />
<hkern u1="&#xe2;" u2="&#x7d;" k="9" />
<hkern u1="&#xe2;" u2="v" k="5" />
<hkern u1="&#xe2;" u2="]" k="11" />
<hkern u1="&#xe2;" u2="\" k="35" />
<hkern u1="&#xe2;" u2="V" k="25" />
<hkern u1="&#xe2;" u2="&#x3f;" k="15" />
<hkern u1="&#xe2;" u2="&#x2a;" k="3" />
<hkern u1="&#xe3;" u2="&#x2122;" k="12" />
<hkern u1="&#xe3;" u2="&#x7d;" k="9" />
<hkern u1="&#xe3;" u2="v" k="5" />
<hkern u1="&#xe3;" u2="]" k="11" />
<hkern u1="&#xe3;" u2="\" k="35" />
<hkern u1="&#xe3;" u2="V" k="25" />
<hkern u1="&#xe3;" u2="&#x3f;" k="15" />
<hkern u1="&#xe3;" u2="&#x2a;" k="3" />
<hkern u1="&#xe4;" u2="&#x2122;" k="12" />
<hkern u1="&#xe4;" u2="&#x7d;" k="9" />
<hkern u1="&#xe4;" u2="v" k="5" />
<hkern u1="&#xe4;" u2="]" k="11" />
<hkern u1="&#xe4;" u2="\" k="35" />
<hkern u1="&#xe4;" u2="V" k="25" />
<hkern u1="&#xe4;" u2="&#x3f;" k="15" />
<hkern u1="&#xe4;" u2="&#x2a;" k="3" />
<hkern u1="&#xe5;" u2="&#x2122;" k="12" />
<hkern u1="&#xe5;" u2="&#x7d;" k="9" />
<hkern u1="&#xe5;" u2="v" k="5" />
<hkern u1="&#xe5;" u2="]" k="11" />
<hkern u1="&#xe5;" u2="\" k="35" />
<hkern u1="&#xe5;" u2="V" k="25" />
<hkern u1="&#xe5;" u2="&#x3f;" k="15" />
<hkern u1="&#xe5;" u2="&#x2a;" k="3" />
<hkern u1="&#xe6;" u2="&#x2122;" k="14" />
<hkern u1="&#xe6;" u2="&#xc6;" k="5" />
<hkern u1="&#xe6;" u2="&#x7d;" k="27" />
<hkern u1="&#xe6;" u2="x" k="4" />
<hkern u1="&#xe6;" u2="v" k="8" />
<hkern u1="&#xe6;" u2="]" k="25" />
<hkern u1="&#xe6;" u2="\" k="34" />
<hkern u1="&#xe6;" u2="V" k="31" />
<hkern u1="&#xe6;" u2="&#x3f;" k="20" />
<hkern u1="&#xe6;" u2="&#x29;" k="5" />
<hkern u1="&#xe7;" u2="&#xf0;" k="11" />
<hkern u1="&#xe7;" u2="&#x7d;" k="16" />
<hkern u1="&#xe7;" u2="]" k="19" />
<hkern u1="&#xe7;" u2="\" k="10" />
<hkern u1="&#xe7;" u2="V" k="12" />
<hkern u1="&#xe7;" u2="&#x3f;" k="9" />
<hkern u1="&#xe8;" u2="&#x2122;" k="14" />
<hkern u1="&#xe8;" u2="&#xc6;" k="5" />
<hkern u1="&#xe8;" u2="&#x7d;" k="27" />
<hkern u1="&#xe8;" u2="x" k="4" />
<hkern u1="&#xe8;" u2="v" k="8" />
<hkern u1="&#xe8;" u2="]" k="25" />
<hkern u1="&#xe8;" u2="\" k="34" />
<hkern u1="&#xe8;" u2="V" k="31" />
<hkern u1="&#xe8;" u2="&#x3f;" k="20" />
<hkern u1="&#xe8;" u2="&#x29;" k="5" />
<hkern u1="&#xe9;" u2="&#x2122;" k="14" />
<hkern u1="&#xe9;" u2="&#xc6;" k="5" />
<hkern u1="&#xe9;" u2="&#x7d;" k="27" />
<hkern u1="&#xe9;" u2="x" k="4" />
<hkern u1="&#xe9;" u2="v" k="8" />
<hkern u1="&#xe9;" u2="]" k="25" />
<hkern u1="&#xe9;" u2="\" k="34" />
<hkern u1="&#xe9;" u2="V" k="31" />
<hkern u1="&#xe9;" u2="&#x3f;" k="20" />
<hkern u1="&#xe9;" u2="&#x29;" k="5" />
<hkern u1="&#xea;" u2="&#x2122;" k="14" />
<hkern u1="&#xea;" u2="&#xc6;" k="5" />
<hkern u1="&#xea;" u2="&#x7d;" k="27" />
<hkern u1="&#xea;" u2="x" k="4" />
<hkern u1="&#xea;" u2="v" k="8" />
<hkern u1="&#xea;" u2="]" k="25" />
<hkern u1="&#xea;" u2="\" k="34" />
<hkern u1="&#xea;" u2="V" k="31" />
<hkern u1="&#xea;" u2="&#x3f;" k="20" />
<hkern u1="&#xea;" u2="&#x29;" k="5" />
<hkern u1="&#xeb;" u2="&#x2122;" k="14" />
<hkern u1="&#xeb;" u2="&#xc6;" k="5" />
<hkern u1="&#xeb;" u2="&#x7d;" k="27" />
<hkern u1="&#xeb;" u2="x" k="4" />
<hkern u1="&#xeb;" u2="v" k="8" />
<hkern u1="&#xeb;" u2="]" k="25" />
<hkern u1="&#xeb;" u2="\" k="34" />
<hkern u1="&#xeb;" u2="V" k="31" />
<hkern u1="&#xeb;" u2="&#x3f;" k="20" />
<hkern u1="&#xeb;" u2="&#x29;" k="5" />
<hkern u1="&#xec;" u2="&#xef;" k="-3" />
<hkern u1="&#xec;" u2="&#xec;" k="-7" />
<hkern u1="&#xed;" u2="&#x2122;" k="-14" />
<hkern u1="&#xed;" u2="&#x201d;" k="-3" />
<hkern u1="&#xed;" u2="&#x2019;" k="-3" />
<hkern u1="&#xed;" u2="&#xfe;" k="-10" />
<hkern u1="&#xed;" u2="&#xef;" k="-3" />
<hkern u1="&#xed;" u2="&#xee;" k="-10" />
<hkern u1="&#xed;" u2="&#xed;" k="-10" />
<hkern u1="&#xed;" u2="&#xec;" k="-7" />
<hkern u1="&#xed;" u2="&#xdf;" k="-10" />
<hkern u1="&#xed;" u2="&#x7d;" k="-40" />
<hkern u1="&#xed;" u2="&#x7c;" k="-8" />
<hkern u1="&#xed;" u2="l" k="-7" />
<hkern u1="&#xed;" u2="k" k="-10" />
<hkern u1="&#xed;" u2="j" k="-10" />
<hkern u1="&#xed;" u2="i" k="-10" />
<hkern u1="&#xed;" u2="h" k="-10" />
<hkern u1="&#xed;" u2="b" k="-10" />
<hkern u1="&#xed;" u2="]" k="-40" />
<hkern u1="&#xed;" u2="\" k="-44" />
<hkern u1="&#xed;" u2="&#x3f;" k="-37" />
<hkern u1="&#xed;" u2="&#x2a;" k="-20" />
<hkern u1="&#xed;" u2="&#x29;" k="-25" />
<hkern u1="&#xed;" u2="&#x27;" k="-17" />
<hkern u1="&#xed;" u2="&#x22;" k="-17" />
<hkern u1="&#xed;" u2="&#x21;" k="-8" />
<hkern u1="&#xee;" u2="&#x2122;" k="-3" />
<hkern u1="&#xee;" u2="&#xef;" k="-3" />
<hkern u1="&#xee;" u2="&#xec;" k="-7" />
<hkern u1="&#xee;" u2="&#x3f;" k="-10" />
<hkern u1="&#xee;" u2="&#x2a;" k="-19" />
<hkern u1="&#xef;" u2="&#x2122;" k="-4" />
<hkern u1="&#xef;" u2="&#xef;" k="-3" />
<hkern u1="&#xef;" u2="&#xec;" k="-7" />
<hkern u1="&#xef;" u2="&#x7d;" k="-11" />
<hkern u1="&#xef;" u2="]" k="-11" />
<hkern u1="&#xef;" u2="\" k="-18" />
<hkern u1="&#xef;" u2="&#x3f;" k="-14" />
<hkern u1="&#xef;" u2="&#x2a;" k="-18" />
<hkern u1="&#xef;" u2="&#x29;" k="-11" />
<hkern u1="&#xf0;" u2="&#x2122;" k="7" />
<hkern u1="&#xf0;" u2="&#x2026;" k="9" />
<hkern u1="&#xf0;" u2="&#x201e;" k="9" />
<hkern u1="&#xf0;" u2="&#x201d;" k="5" />
<hkern u1="&#xf0;" u2="&#x201c;" k="5" />
<hkern u1="&#xf0;" u2="&#x201a;" k="9" />
<hkern u1="&#xf0;" u2="&#x2019;" k="5" />
<hkern u1="&#xf0;" u2="&#x2018;" k="5" />
<hkern u1="&#xf0;" u2="&#x178;" k="42" />
<hkern u1="&#xf0;" u2="&#xff;" k="7" />
<hkern u1="&#xf0;" u2="&#xfd;" k="7" />
<hkern u1="&#xf0;" u2="&#xde;" k="5" />
<hkern u1="&#xf0;" u2="&#xdd;" k="42" />
<hkern u1="&#xf0;" u2="&#xd1;" k="5" />
<hkern u1="&#xf0;" u2="&#xd0;" k="5" />
<hkern u1="&#xf0;" u2="&#xcf;" k="5" />
<hkern u1="&#xf0;" u2="&#xce;" k="5" />
<hkern u1="&#xf0;" u2="&#xcd;" k="5" />
<hkern u1="&#xf0;" u2="&#xcc;" k="5" />
<hkern u1="&#xf0;" u2="&#xcb;" k="5" />
<hkern u1="&#xf0;" u2="&#xca;" k="5" />
<hkern u1="&#xf0;" u2="&#xc9;" k="5" />
<hkern u1="&#xf0;" u2="&#xc8;" k="5" />
<hkern u1="&#xf0;" u2="&#xc6;" k="11" />
<hkern u1="&#xf0;" u2="&#xc5;" k="9" />
<hkern u1="&#xf0;" u2="&#xc4;" k="9" />
<hkern u1="&#xf0;" u2="&#xc3;" k="9" />
<hkern u1="&#xf0;" u2="&#xc2;" k="9" />
<hkern u1="&#xf0;" u2="&#xc1;" k="9" />
<hkern u1="&#xf0;" u2="&#xc0;" k="9" />
<hkern u1="&#xf0;" u2="&#x7d;" k="21" />
<hkern u1="&#xf0;" u2="y" k="7" />
<hkern u1="&#xf0;" u2="x" k="5" />
<hkern u1="&#xf0;" u2="w" k="4" />
<hkern u1="&#xf0;" u2="v" k="6" />
<hkern u1="&#xf0;" u2="]" k="24" />
<hkern u1="&#xf0;" u2="\" k="19" />
<hkern u1="&#xf0;" u2="Z" k="12" />
<hkern u1="&#xf0;" u2="Y" k="42" />
<hkern u1="&#xf0;" u2="X" k="28" />
<hkern u1="&#xf0;" u2="W" k="12" />
<hkern u1="&#xf0;" u2="V" k="21" />
<hkern u1="&#xf0;" u2="T" k="50" />
<hkern u1="&#xf0;" u2="S" k="4" />
<hkern u1="&#xf0;" u2="R" k="5" />
<hkern u1="&#xf0;" u2="P" k="5" />
<hkern u1="&#xf0;" u2="N" k="5" />
<hkern u1="&#xf0;" u2="M" k="5" />
<hkern u1="&#xf0;" u2="L" k="5" />
<hkern u1="&#xf0;" u2="K" k="5" />
<hkern u1="&#xf0;" u2="J" k="27" />
<hkern u1="&#xf0;" u2="I" k="5" />
<hkern u1="&#xf0;" u2="H" k="5" />
<hkern u1="&#xf0;" u2="F" k="5" />
<hkern u1="&#xf0;" u2="E" k="5" />
<hkern u1="&#xf0;" u2="D" k="5" />
<hkern u1="&#xf0;" u2="B" k="5" />
<hkern u1="&#xf0;" u2="A" k="9" />
<hkern u1="&#xf0;" u2="&#x3f;" k="13" />
<hkern u1="&#xf0;" u2="&#x2f;" k="5" />
<hkern u1="&#xf0;" u2="&#x2e;" k="9" />
<hkern u1="&#xf0;" u2="&#x2c;" k="9" />
<hkern u1="&#xf0;" u2="&#x29;" k="14" />
<hkern u1="&#xf1;" u2="&#x2122;" k="16" />
<hkern u1="&#xf1;" u2="&#x7d;" k="24" />
<hkern u1="&#xf1;" u2="v" k="5" />
<hkern u1="&#xf1;" u2="]" k="26" />
<hkern u1="&#xf1;" u2="\" k="36" />
<hkern u1="&#xf1;" u2="V" k="29" />
<hkern u1="&#xf1;" u2="&#x3f;" k="22" />
<hkern u1="&#xf1;" u2="&#x2a;" k="3" />
<hkern u1="&#xf1;" u2="&#x29;" k="4" />
<hkern u1="&#xf2;" u2="&#x2122;" k="15" />
<hkern u1="&#xf2;" u2="&#xc6;" k="7" />
<hkern u1="&#xf2;" u2="&#x7d;" k="33" />
<hkern u1="&#xf2;" u2="x" k="12" />
<hkern u1="&#xf2;" u2="v" k="9" />
<hkern u1="&#xf2;" u2="]" k="38" />
<hkern u1="&#xf2;" u2="\" k="37" />
<hkern u1="&#xf2;" u2="X" k="21" />
<hkern u1="&#xf2;" u2="V" k="32" />
<hkern u1="&#xf2;" u2="&#x3f;" k="23" />
<hkern u1="&#xf2;" u2="&#x2a;" k="3" />
<hkern u1="&#xf2;" u2="&#x29;" k="18" />
<hkern u1="&#xf3;" u2="&#x2122;" k="15" />
<hkern u1="&#xf3;" u2="&#xc6;" k="7" />
<hkern u1="&#xf3;" u2="&#x7d;" k="33" />
<hkern u1="&#xf3;" u2="x" k="12" />
<hkern u1="&#xf3;" u2="v" k="9" />
<hkern u1="&#xf3;" u2="]" k="38" />
<hkern u1="&#xf3;" u2="\" k="37" />
<hkern u1="&#xf3;" u2="X" k="21" />
<hkern u1="&#xf3;" u2="V" k="32" />
<hkern u1="&#xf3;" u2="&#x3f;" k="23" />
<hkern u1="&#xf3;" u2="&#x2a;" k="3" />
<hkern u1="&#xf3;" u2="&#x29;" k="18" />
<hkern u1="&#xf4;" u2="&#x2122;" k="15" />
<hkern u1="&#xf4;" u2="&#xc6;" k="7" />
<hkern u1="&#xf4;" u2="&#x7d;" k="33" />
<hkern u1="&#xf4;" u2="x" k="12" />
<hkern u1="&#xf4;" u2="v" k="9" />
<hkern u1="&#xf4;" u2="]" k="38" />
<hkern u1="&#xf4;" u2="\" k="37" />
<hkern u1="&#xf4;" u2="X" k="21" />
<hkern u1="&#xf4;" u2="V" k="32" />
<hkern u1="&#xf4;" u2="&#x3f;" k="23" />
<hkern u1="&#xf4;" u2="&#x2a;" k="3" />
<hkern u1="&#xf4;" u2="&#x29;" k="18" />
<hkern u1="&#xf5;" u2="&#x2122;" k="15" />
<hkern u1="&#xf5;" u2="&#xc6;" k="7" />
<hkern u1="&#xf5;" u2="&#x7d;" k="33" />
<hkern u1="&#xf5;" u2="x" k="12" />
<hkern u1="&#xf5;" u2="v" k="9" />
<hkern u1="&#xf5;" u2="]" k="38" />
<hkern u1="&#xf5;" u2="\" k="37" />
<hkern u1="&#xf5;" u2="X" k="21" />
<hkern u1="&#xf5;" u2="V" k="32" />
<hkern u1="&#xf5;" u2="&#x3f;" k="23" />
<hkern u1="&#xf5;" u2="&#x2a;" k="3" />
<hkern u1="&#xf5;" u2="&#x29;" k="18" />
<hkern u1="&#xf6;" u2="&#x2122;" k="15" />
<hkern u1="&#xf6;" u2="&#xc6;" k="7" />
<hkern u1="&#xf6;" u2="&#x7d;" k="33" />
<hkern u1="&#xf6;" u2="x" k="12" />
<hkern u1="&#xf6;" u2="v" k="9" />
<hkern u1="&#xf6;" u2="]" k="38" />
<hkern u1="&#xf6;" u2="\" k="37" />
<hkern u1="&#xf6;" u2="X" k="21" />
<hkern u1="&#xf6;" u2="V" k="32" />
<hkern u1="&#xf6;" u2="&#x3f;" k="23" />
<hkern u1="&#xf6;" u2="&#x2a;" k="3" />
<hkern u1="&#xf6;" u2="&#x29;" k="18" />
<hkern u1="&#xf8;" u2="&#x2122;" k="15" />
<hkern u1="&#xf8;" u2="&#xc6;" k="7" />
<hkern u1="&#xf8;" u2="&#x7d;" k="33" />
<hkern u1="&#xf8;" u2="x" k="12" />
<hkern u1="&#xf8;" u2="v" k="9" />
<hkern u1="&#xf8;" u2="]" k="38" />
<hkern u1="&#xf8;" u2="\" k="37" />
<hkern u1="&#xf8;" u2="X" k="21" />
<hkern u1="&#xf8;" u2="V" k="32" />
<hkern u1="&#xf8;" u2="&#x3f;" k="23" />
<hkern u1="&#xf8;" u2="&#x2a;" k="3" />
<hkern u1="&#xf8;" u2="&#x29;" k="18" />
<hkern u1="&#xf9;" u2="&#x2122;" k="11" />
<hkern u1="&#xf9;" u2="&#x7d;" k="23" />
<hkern u1="&#xf9;" u2="]" k="25" />
<hkern u1="&#xf9;" u2="\" k="23" />
<hkern u1="&#xf9;" u2="V" k="26" />
<hkern u1="&#xf9;" u2="&#x3f;" k="13" />
<hkern u1="&#xf9;" u2="&#x29;" k="4" />
<hkern u1="&#xfa;" u2="&#x2122;" k="11" />
<hkern u1="&#xfa;" u2="&#x7d;" k="23" />
<hkern u1="&#xfa;" u2="]" k="25" />
<hkern u1="&#xfa;" u2="\" k="23" />
<hkern u1="&#xfa;" u2="V" k="26" />
<hkern u1="&#xfa;" u2="&#x3f;" k="13" />
<hkern u1="&#xfa;" u2="&#x29;" k="4" />
<hkern u1="&#xfb;" u2="&#x2122;" k="11" />
<hkern u1="&#xfb;" u2="&#x7d;" k="23" />
<hkern u1="&#xfb;" u2="]" k="25" />
<hkern u1="&#xfb;" u2="\" k="23" />
<hkern u1="&#xfb;" u2="V" k="26" />
<hkern u1="&#xfb;" u2="&#x3f;" k="13" />
<hkern u1="&#xfb;" u2="&#x29;" k="4" />
<hkern u1="&#xfc;" u2="&#x2122;" k="11" />
<hkern u1="&#xfc;" u2="&#x7d;" k="23" />
<hkern u1="&#xfc;" u2="]" k="25" />
<hkern u1="&#xfc;" u2="\" k="23" />
<hkern u1="&#xfc;" u2="V" k="26" />
<hkern u1="&#xfc;" u2="&#x3f;" k="13" />
<hkern u1="&#xfc;" u2="&#x29;" k="4" />
<hkern u1="&#xfd;" u2="&#xf0;" k="14" />
<hkern u1="&#xfd;" u2="&#xc6;" k="21" />
<hkern u1="&#xfd;" u2="&#x7d;" k="24" />
<hkern u1="&#xfd;" u2="]" k="29" />
<hkern u1="&#xfd;" u2="\" k="7" />
<hkern u1="&#xfd;" u2="X" k="26" />
<hkern u1="&#xfd;" u2="V" k="8" />
<hkern u1="&#xfd;" u2="&#x3f;" k="9" />
<hkern u1="&#xfd;" u2="&#x2f;" k="21" />
<hkern u1="&#xfe;" u2="&#x2122;" k="17" />
<hkern u1="&#xfe;" u2="&#xc6;" k="7" />
<hkern u1="&#xfe;" u2="&#x7d;" k="33" />
<hkern u1="&#xfe;" u2="x" k="12" />
<hkern u1="&#xfe;" u2="v" k="8" />
<hkern u1="&#xfe;" u2="]" k="37" />
<hkern u1="&#xfe;" u2="\" k="36" />
<hkern u1="&#xfe;" u2="X" k="20" />
<hkern u1="&#xfe;" u2="V" k="31" />
<hkern u1="&#xfe;" u2="&#x3f;" k="25" />
<hkern u1="&#xfe;" u2="&#x2a;" k="8" />
<hkern u1="&#xfe;" u2="&#x29;" k="18" />
<hkern u1="&#xff;" u2="&#xf0;" k="14" />
<hkern u1="&#xff;" u2="&#xc6;" k="21" />
<hkern u1="&#xff;" u2="&#x7d;" k="24" />
<hkern u1="&#xff;" u2="]" k="29" />
<hkern u1="&#xff;" u2="\" k="7" />
<hkern u1="&#xff;" u2="X" k="26" />
<hkern u1="&#xff;" u2="V" k="8" />
<hkern u1="&#xff;" u2="&#x3f;" k="9" />
<hkern u1="&#xff;" u2="&#x2f;" k="21" />
<hkern u1="&#x152;" u2="&#xf0;" k="8" />
<hkern u1="&#x152;" u2="&#xef;" k="-13" />
<hkern u1="&#x152;" u2="&#xee;" k="-12" />
<hkern u1="&#x152;" u2="&#xec;" k="-36" />
<hkern u1="&#x152;" u2="v" k="11" />
<hkern u1="&#x152;" u2="f" k="3" />
<hkern u1="&#x153;" u2="&#x2122;" k="14" />
<hkern u1="&#x153;" u2="&#xc6;" k="5" />
<hkern u1="&#x153;" u2="&#x7d;" k="27" />
<hkern u1="&#x153;" u2="x" k="4" />
<hkern u1="&#x153;" u2="v" k="8" />
<hkern u1="&#x153;" u2="]" k="25" />
<hkern u1="&#x153;" u2="\" k="34" />
<hkern u1="&#x153;" u2="V" k="31" />
<hkern u1="&#x153;" u2="&#x3f;" k="20" />
<hkern u1="&#x153;" u2="&#x29;" k="5" />
<hkern u1="&#x178;" u2="&#xff;" k="34" />
<hkern u1="&#x178;" u2="&#xf0;" k="39" />
<hkern u1="&#x178;" u2="&#xef;" k="-44" />
<hkern u1="&#x178;" u2="&#xee;" k="-8" />
<hkern u1="&#x178;" u2="&#xec;" k="-64" />
<hkern u1="&#x178;" u2="&#xeb;" k="67" />
<hkern u1="&#x178;" u2="&#xe4;" k="57" />
<hkern u1="&#x178;" u2="&#xe3;" k="53" />
<hkern u1="&#x178;" u2="&#xdf;" k="6" />
<hkern u1="&#x178;" u2="&#xc6;" k="52" />
<hkern u1="&#x178;" u2="&#xae;" k="22" />
<hkern u1="&#x178;" u2="x" k="40" />
<hkern u1="&#x178;" u2="v" k="41" />
<hkern u1="&#x178;" u2="f" k="18" />
<hkern u1="&#x178;" u2="&#x40;" k="36" />
<hkern u1="&#x178;" u2="&#x2f;" k="64" />
<hkern u1="&#x178;" u2="&#x26;" k="37" />
<hkern u1="&#x2013;" u2="&#xc6;" k="17" />
<hkern u1="&#x2013;" u2="x" k="31" />
<hkern u1="&#x2013;" u2="v" k="13" />
<hkern u1="&#x2013;" u2="f" k="12" />
<hkern u1="&#x2013;" u2="X" k="39" />
<hkern u1="&#x2013;" u2="V" k="31" />
<hkern u1="&#x2014;" u2="&#xc6;" k="17" />
<hkern u1="&#x2014;" u2="x" k="31" />
<hkern u1="&#x2014;" u2="v" k="13" />
<hkern u1="&#x2014;" u2="f" k="12" />
<hkern u1="&#x2014;" u2="X" k="39" />
<hkern u1="&#x2014;" u2="V" k="31" />
<hkern u1="&#x2018;" u2="&#xf0;" k="10" />
<hkern u1="&#x2018;" u2="&#xef;" k="-14" />
<hkern u1="&#x2018;" u2="&#xee;" k="-6" />
<hkern u1="&#x2018;" u2="&#xec;" k="-31" />
<hkern u1="&#x2018;" u2="&#xc6;" k="59" />
<hkern u1="&#x2019;" u2="&#xf0;" k="10" />
<hkern u1="&#x2019;" u2="&#xef;" k="-18" />
<hkern u1="&#x2019;" u2="&#xee;" k="-4" />
<hkern u1="&#x2019;" u2="&#xec;" k="-35" />
<hkern u1="&#x2019;" u2="&#xc6;" k="61" />
<hkern u1="&#x2019;" u2="&#x40;" k="24" />
<hkern u1="&#x2019;" u2="&#x2f;" k="71" />
<hkern u1="&#x2019;" u2="&#x26;" k="31" />
<hkern u1="&#x201a;" u2="v" k="35" />
<hkern u1="&#x201a;" u2="f" k="10" />
<hkern u1="&#x201a;" u2="V" k="53" />
<hkern u1="&#x201c;" u2="&#xf0;" k="10" />
<hkern u1="&#x201c;" u2="&#xef;" k="-14" />
<hkern u1="&#x201c;" u2="&#xee;" k="-6" />
<hkern u1="&#x201c;" u2="&#xec;" k="-31" />
<hkern u1="&#x201c;" u2="&#xc6;" k="59" />
<hkern u1="&#x201d;" u2="&#xf0;" k="10" />
<hkern u1="&#x201d;" u2="&#xef;" k="-18" />
<hkern u1="&#x201d;" u2="&#xee;" k="-4" />
<hkern u1="&#x201d;" u2="&#xec;" k="-35" />
<hkern u1="&#x201d;" u2="&#xc6;" k="61" />
<hkern u1="&#x201d;" u2="&#x40;" k="24" />
<hkern u1="&#x201d;" u2="&#x2f;" k="71" />
<hkern u1="&#x201d;" u2="&#x26;" k="31" />
<hkern u1="&#x201e;" u2="v" k="35" />
<hkern u1="&#x201e;" u2="f" k="10" />
<hkern u1="&#x201e;" u2="V" k="53" />
<hkern u1="&#x2039;" u2="V" k="19" />
<hkern u1="&#x203a;" u2="&#xc6;" k="7" />
<hkern u1="&#x203a;" u2="x" k="26" />
<hkern u1="&#x203a;" u2="v" k="8" />
<hkern u1="&#x203a;" u2="f" k="4" />
<hkern u1="&#x203a;" u2="X" k="24" />
<hkern u1="&#x203a;" u2="V" k="28" />
<hkern u1="&#x2122;" u2="&#xef;" k="-14" />
<hkern u1="&#x2122;" u2="&#xee;" k="-18" />
<hkern u1="&#x2122;" u2="&#xec;" k="-18" />
<hkern u1="&#x2122;" u2="&#xc6;" k="35" />
<hkern u1="&#x2122;" u2="&#xc5;" k="28" />
<hkern u1="&#x2122;" u2="&#xc4;" k="28" />
<hkern u1="&#x2122;" u2="&#xc3;" k="28" />
<hkern u1="&#x2122;" u2="&#xc2;" k="28" />
<hkern u1="&#x2122;" u2="&#xc1;" k="28" />
<hkern u1="&#x2122;" u2="&#xc0;" k="28" />
<hkern u1="&#x2122;" u2="Z" k="5" />
<hkern u1="&#x2122;" u2="J" k="22" />
<hkern u1="&#x2122;" u2="A" k="28" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="C,Ccedilla" 	k="12" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="J" 	k="4" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="12" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="S" 	k="7" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="47" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="7" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="W" 	k="22" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="48" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="d,q" 	k="6" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="g" 	k="9" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="guillemotleft,guilsinglleft" 	k="5" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="hyphen,endash,emdash" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="6" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteleft,quotedblleft" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="42" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotedbl,quotesingle" 	k="43" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="t" 	k="15" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="w" 	k="16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="y,yacute,ydieresis" 	k="18" />
<hkern g1="C,Ccedilla" 	g2="C,Ccedilla" 	k="8" />
<hkern g1="C,Ccedilla" 	g2="J" 	k="4" />
<hkern g1="C,Ccedilla" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="11" />
<hkern g1="C,Ccedilla" 	g2="d,q" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="g" 	k="15" />
<hkern g1="C,Ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="15" />
<hkern g1="C,Ccedilla" 	g2="hyphen,endash,emdash" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="t" 	k="5" />
<hkern g1="C,Ccedilla" 	g2="w" 	k="16" />
<hkern g1="C,Ccedilla" 	g2="y,yacute,ydieresis" 	k="15" />
<hkern g1="C,Ccedilla" 	g2="guillemotright,guilsinglright" 	k="5" />
<hkern g1="C,Ccedilla" 	g2="m,n,p,r,ntilde" 	k="4" />
<hkern g1="C,Ccedilla" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="11" />
<hkern g1="D,Eth" 	g2="J" 	k="20" />
<hkern g1="D,Eth" 	g2="T" 	k="23" />
<hkern g1="D,Eth" 	g2="W" 	k="3" />
<hkern g1="D,Eth" 	g2="Y,Yacute,Ydieresis" 	k="29" />
<hkern g1="D,Eth" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="14" />
<hkern g1="D,Eth" 	g2="Z" 	k="7" />
<hkern g1="D,Eth" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="15" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="C,Ccedilla" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="S" 	k="3" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="d,q" 	k="9" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="g" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="guillemotleft,guilsinglleft" 	k="12" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="hyphen,endash,emdash" 	k="21" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="9" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="t" 	k="6" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="w" 	k="13" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="y,yacute,ydieresis" 	k="11" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="8" />
<hkern g1="G" 	g2="J" 	k="10" />
<hkern g1="G" 	g2="Y,Yacute,Ydieresis" 	k="17" />
<hkern g1="G" 	g2="g" 	k="6" />
<hkern g1="G" 	g2="t" 	k="5" />
<hkern g1="G" 	g2="w" 	k="7" />
<hkern g1="G" 	g2="y,yacute,ydieresis" 	k="7" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="J" 	k="6" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="d,q" 	k="5" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="g" 	k="10" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="5" />
<hkern g1="J" 	g2="d,q" 	k="5" />
<hkern g1="J" 	g2="g" 	k="8" />
<hkern g1="J" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="5" />
<hkern g1="K" 	g2="C,Ccedilla" 	k="15" />
<hkern g1="K" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="17" />
<hkern g1="K" 	g2="d,q" 	k="14" />
<hkern g1="K" 	g2="g" 	k="12" />
<hkern g1="K" 	g2="guillemotleft,guilsinglleft" 	k="22" />
<hkern g1="K" 	g2="hyphen,endash,emdash" 	k="36" />
<hkern g1="K" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="15" />
<hkern g1="K" 	g2="t" 	k="11" />
<hkern g1="K" 	g2="w" 	k="25" />
<hkern g1="K" 	g2="y,yacute,ydieresis" 	k="25" />
<hkern g1="K" 	g2="m,n,p,r,ntilde" 	k="3" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="13" />
<hkern g1="L" 	g2="C,Ccedilla" 	k="16" />
<hkern g1="L" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="L" 	g2="T" 	k="95" />
<hkern g1="L" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="13" />
<hkern g1="L" 	g2="W" 	k="45" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="89" />
<hkern g1="L" 	g2="d,q" 	k="5" />
<hkern g1="L" 	g2="guillemotleft,guilsinglleft" 	k="39" />
<hkern g1="L" 	g2="hyphen,endash,emdash" 	k="73" />
<hkern g1="L" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="5" />
<hkern g1="L" 	g2="quoteleft,quotedblleft" 	k="90" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="89" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="90" />
<hkern g1="L" 	g2="t" 	k="18" />
<hkern g1="L" 	g2="w" 	k="35" />
<hkern g1="L" 	g2="y,yacute,ydieresis" 	k="46" />
<hkern g1="L" 	g2="guillemotright,guilsinglright" 	k="14" />
<hkern g1="L" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="5" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="19" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="20" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="3" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="28" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="12" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="5" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="11" />
<hkern g1="R" 	g2="J" 	k="5" />
<hkern g1="R" 	g2="T" 	k="10" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="21" />
<hkern g1="R" 	g2="d,q" 	k="9" />
<hkern g1="R" 	g2="g" 	k="8" />
<hkern g1="R" 	g2="guillemotleft,guilsinglleft" 	k="14" />
<hkern g1="R" 	g2="hyphen,endash,emdash" 	k="10" />
<hkern g1="R" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="9" />
<hkern g1="R" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="3" />
<hkern g1="R" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="7" />
<hkern g1="S" 	g2="J" 	k="14" />
<hkern g1="S" 	g2="Y,Yacute,Ydieresis" 	k="15" />
<hkern g1="S" 	g2="g" 	k="8" />
<hkern g1="S" 	g2="t" 	k="9" />
<hkern g1="S" 	g2="w" 	k="9" />
<hkern g1="S" 	g2="y,yacute,ydieresis" 	k="10" />
<hkern g1="S" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="S" 	g2="z" 	k="3" />
<hkern g1="T" 	g2="C,Ccedilla" 	k="14" />
<hkern g1="T" 	g2="J" 	k="22" />
<hkern g1="T" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="T" 	g2="d,q" 	k="90" />
<hkern g1="T" 	g2="g" 	k="101" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="61" />
<hkern g1="T" 	g2="hyphen,endash,emdash" 	k="64" />
<hkern g1="T" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="91" />
<hkern g1="T" 	g2="t" 	k="26" />
<hkern g1="T" 	g2="w" 	k="69" />
<hkern g1="T" 	g2="y,yacute,ydieresis" 	k="68" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="58" />
<hkern g1="T" 	g2="m,n,p,r,ntilde" 	k="86" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="87" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="47" />
<hkern g1="T" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="63" />
<hkern g1="T" 	g2="z" 	k="86" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="89" />
<hkern g1="T" 	g2="b,thorn" 	k="7" />
<hkern g1="T" 	g2="colon,semicolon" 	k="55" />
<hkern g1="T" 	g2="s" 	k="88" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="J" 	k="16" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="d,q" 	k="5" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="g" 	k="11" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="5" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="5" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="7" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="12" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="s" 	k="5" />
<hkern g1="W" 	g2="J" 	k="19" />
<hkern g1="W" 	g2="d,q" 	k="20" />
<hkern g1="W" 	g2="g" 	k="24" />
<hkern g1="W" 	g2="guillemotleft,guilsinglleft" 	k="17" />
<hkern g1="W" 	g2="hyphen,endash,emdash" 	k="19" />
<hkern g1="W" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="W" 	g2="guillemotright,guilsinglright" 	k="8" />
<hkern g1="W" 	g2="m,n,p,r,ntilde" 	k="15" />
<hkern g1="W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="12" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="22" />
<hkern g1="W" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="36" />
<hkern g1="W" 	g2="z" 	k="3" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="15" />
<hkern g1="W" 	g2="s" 	k="15" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,Ccedilla" 	k="27" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="38" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="29" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="S" 	k="18" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="d,q" 	k="71" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="75" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="66" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,endash,emdash" 	k="71" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="70" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="17" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="45" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="y,yacute,ydieresis" 	k="42" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="50" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,p,r,ntilde" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="59" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="48" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="74" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="48" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="65" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="43" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="70" />
<hkern g1="Z" 	g2="C,Ccedilla" 	k="5" />
<hkern g1="Z" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="5" />
<hkern g1="Z" 	g2="d,q" 	k="11" />
<hkern g1="Z" 	g2="g" 	k="15" />
<hkern g1="Z" 	g2="guillemotleft,guilsinglleft" 	k="16" />
<hkern g1="Z" 	g2="hyphen,endash,emdash" 	k="32" />
<hkern g1="Z" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="Z" 	g2="t" 	k="4" />
<hkern g1="Z" 	g2="w" 	k="13" />
<hkern g1="Z" 	g2="y,yacute,ydieresis" 	k="12" />
<hkern g1="Z" 	g2="guillemotright,guilsinglright" 	k="5" />
<hkern g1="Z" 	g2="m,n,p,r,ntilde" 	k="6" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="11" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="T" 	k="79" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="4" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="W" 	k="14" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="Y,Yacute,Ydieresis" 	k="63" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quotedbl,quotesingle" 	k="4" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="y,yacute,ydieresis" 	k="5" />
<hkern g1="b,p,thorn" 	g2="J" 	k="26" />
<hkern g1="b,p,thorn" 	g2="S" 	k="7" />
<hkern g1="b,p,thorn" 	g2="T" 	k="91" />
<hkern g1="b,p,thorn" 	g2="W" 	k="19" />
<hkern g1="b,p,thorn" 	g2="Y,Yacute,Ydieresis" 	k="68" />
<hkern g1="b,p,thorn" 	g2="quoteleft,quotedblleft" 	k="15" />
<hkern g1="b,p,thorn" 	g2="quoteright,quotedblright" 	k="15" />
<hkern g1="b,p,thorn" 	g2="quotedbl,quotesingle" 	k="16" />
<hkern g1="b,p,thorn" 	g2="t" 	k="3" />
<hkern g1="b,p,thorn" 	g2="w" 	k="5" />
<hkern g1="b,p,thorn" 	g2="y,yacute,ydieresis" 	k="9" />
<hkern g1="b,p,thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="6" />
<hkern g1="b,p,thorn" 	g2="Z" 	k="8" />
<hkern g1="b,p,thorn" 	g2="z" 	k="3" />
<hkern g1="b,p,thorn" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="5" />
<hkern g1="c,ccedilla" 	g2="J" 	k="7" />
<hkern g1="c,ccedilla" 	g2="T" 	k="102" />
<hkern g1="c,ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="49" />
<hkern g1="c,ccedilla" 	g2="d,q" 	k="5" />
<hkern g1="c,ccedilla" 	g2="g" 	k="5" />
<hkern g1="c,ccedilla" 	g2="guillemotleft,guilsinglleft" 	k="31" />
<hkern g1="c,ccedilla" 	g2="hyphen,endash,emdash" 	k="40" />
<hkern g1="c,ccedilla" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="6" />
<hkern g1="c,ccedilla" 	g2="guillemotright,guilsinglright" 	k="7" />
<hkern g1="colon,semicolon" 	g2="T" 	k="55" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ydieresis" 	k="43" />
<hkern g1="d" 	g2="J" 	k="17" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="J" 	k="15" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="S" 	k="4" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="T" 	k="92" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="W" 	k="17" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="Y,Yacute,Ydieresis" 	k="84" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quotedbl,quotesingle" 	k="13" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="t" 	k="3" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="w" 	k="5" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="y,yacute,ydieresis" 	k="9" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe" 	g2="Z" 	k="5" />
<hkern g1="g" 	g2="T" 	k="73" />
<hkern g1="g" 	g2="Y,Yacute,Ydieresis" 	k="32" />
<hkern g1="g" 	g2="guillemotleft,guilsinglleft" 	k="4" />
<hkern g1="g" 	g2="hyphen,endash,emdash" 	k="12" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="J" 	k="18" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="59" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="W" 	k="8" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="51" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="quoteright,quotedblright" 	k="35" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="quotedbl,quotesingle" 	k="37" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="t" 	k="8" />
<hkern g1="guillemotright,guilsinglright" 	g2="J" 	k="19" />
<hkern g1="guillemotright,guilsinglright" 	g2="S" 	k="15" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="61" />
<hkern g1="guillemotright,guilsinglright" 	g2="W" 	k="17" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="66" />
<hkern g1="guillemotright,guilsinglright" 	g2="quoteright,quotedblright" 	k="61" />
<hkern g1="guillemotright,guilsinglright" 	g2="quotedbl,quotesingle" 	k="65" />
<hkern g1="guillemotright,guilsinglright" 	g2="t" 	k="7" />
<hkern g1="guillemotright,guilsinglright" 	g2="w" 	k="6" />
<hkern g1="guillemotright,guilsinglright" 	g2="y,yacute,ydieresis" 	k="12" />
<hkern g1="guillemotright,guilsinglright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="5" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z" 	k="11" />
<hkern g1="guillemotright,guilsinglright" 	g2="z" 	k="26" />
<hkern g1="hyphen,endash,emdash" 	g2="J" 	k="29" />
<hkern g1="hyphen,endash,emdash" 	g2="S" 	k="27" />
<hkern g1="hyphen,endash,emdash" 	g2="T" 	k="64" />
<hkern g1="hyphen,endash,emdash" 	g2="W" 	k="19" />
<hkern g1="hyphen,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="71" />
<hkern g1="hyphen,endash,emdash" 	g2="quoteright,quotedblright" 	k="69" />
<hkern g1="hyphen,endash,emdash" 	g2="quotedbl,quotesingle" 	k="74" />
<hkern g1="hyphen,endash,emdash" 	g2="t" 	k="15" />
<hkern g1="hyphen,endash,emdash" 	g2="w" 	k="8" />
<hkern g1="hyphen,endash,emdash" 	g2="y,yacute,ydieresis" 	k="14" />
<hkern g1="hyphen,endash,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="hyphen,endash,emdash" 	g2="Z" 	k="28" />
<hkern g1="hyphen,endash,emdash" 	g2="z" 	k="37" />
<hkern g1="hyphen,endash,emdash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="8" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis" 	g2="J" 	k="17" />
<hkern g1="k" 	g2="C,Ccedilla" 	k="3" />
<hkern g1="k" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="4" />
<hkern g1="k" 	g2="T" 	k="75" />
<hkern g1="k" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="k" 	g2="d,q" 	k="12" />
<hkern g1="k" 	g2="g" 	k="10" />
<hkern g1="k" 	g2="guillemotleft,guilsinglleft" 	k="27" />
<hkern g1="k" 	g2="hyphen,endash,emdash" 	k="36" />
<hkern g1="k" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="13" />
<hkern g1="k" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="3" />
<hkern g1="l" 	g2="J" 	k="17" />
<hkern g1="h,m,n,ntilde" 	g2="J" 	k="18" />
<hkern g1="h,m,n,ntilde" 	g2="S" 	k="6" />
<hkern g1="h,m,n,ntilde" 	g2="T" 	k="95" />
<hkern g1="h,m,n,ntilde" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="6" />
<hkern g1="h,m,n,ntilde" 	g2="W" 	k="18" />
<hkern g1="h,m,n,ntilde" 	g2="Y,Yacute,Ydieresis" 	k="69" />
<hkern g1="h,m,n,ntilde" 	g2="quoteleft,quotedblleft" 	k="8" />
<hkern g1="h,m,n,ntilde" 	g2="quoteright,quotedblright" 	k="8" />
<hkern g1="h,m,n,ntilde" 	g2="quotedbl,quotesingle" 	k="12" />
<hkern g1="h,m,n,ntilde" 	g2="t" 	k="3" />
<hkern g1="h,m,n,ntilde" 	g2="w" 	k="5" />
<hkern g1="h,m,n,ntilde" 	g2="y,yacute,ydieresis" 	k="6" />
<hkern g1="h,m,n,ntilde" 	g2="Z" 	k="3" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="J" 	k="26" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="S" 	k="8" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="T" 	k="93" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="5" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="W" 	k="20" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="Y,Yacute,Ydieresis" 	k="70" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteleft,quotedblleft" 	k="10" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteright,quotedblright" 	k="11" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quotedbl,quotesingle" 	k="15" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="t" 	k="4" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="w" 	k="5" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="y,yacute,ydieresis" 	k="10" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="6" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="Z" 	k="8" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="z" 	k="3" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn" 	k="5" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="C,Ccedilla" 	k="11" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="11" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="T" 	k="64" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="12" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="W" 	k="36" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ydieresis" 	k="74" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="hyphen,endash,emdash" 	k="34" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="quoteleft,quotedblleft" 	k="142" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="quoteright,quotedblright" 	k="144" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="quotedbl,quotesingle" 	k="147" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="t" 	k="19" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="w" 	k="28" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="y,yacute,ydieresis" 	k="36" />
<hkern g1="quoteleft,quotedblleft" 	g2="J" 	k="22" />
<hkern g1="quoteleft,quotedblleft" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="5" />
<hkern g1="quoteleft,quotedblleft" 	g2="d,q" 	k="30" />
<hkern g1="quoteleft,quotedblleft" 	g2="g" 	k="17" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="49" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="152" />
<hkern g1="quoteleft,quotedblleft" 	g2="s" 	k="14" />
<hkern g1="quoteright,quotedblright" 	g2="C,Ccedilla" 	k="5" />
<hkern g1="quoteright,quotedblright" 	g2="J" 	k="22" />
<hkern g1="quoteright,quotedblright" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="9" />
<hkern g1="quoteright,quotedblright" 	g2="d,q" 	k="34" />
<hkern g1="quoteright,quotedblright" 	g2="g" 	k="21" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotleft,guilsinglleft" 	k="79" />
<hkern g1="quoteright,quotedblright" 	g2="hyphen,endash,emdash" 	k="82" />
<hkern g1="quoteright,quotedblright" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="24" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotright,guilsinglright" 	k="46" />
<hkern g1="quoteright,quotedblright" 	g2="m,n,p,r,ntilde" 	k="3" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="50" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="157" />
<hkern g1="quoteright,quotedblright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="5" />
<hkern g1="quoteright,quotedblright" 	g2="colon,semicolon" 	k="5" />
<hkern g1="quoteright,quotedblright" 	g2="s" 	k="17" />
<hkern g1="quotedbl,quotesingle" 	g2="J" 	k="23" />
<hkern g1="quotedbl,quotesingle" 	g2="d,q" 	k="20" />
<hkern g1="quotedbl,quotesingle" 	g2="g" 	k="12" />
<hkern g1="quotedbl,quotesingle" 	g2="guillemotleft,guilsinglleft" 	k="66" />
<hkern g1="quotedbl,quotesingle" 	g2="hyphen,endash,emdash" 	k="75" />
<hkern g1="quotedbl,quotesingle" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="15" />
<hkern g1="quotedbl,quotesingle" 	g2="guillemotright,guilsinglright" 	k="37" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="43" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="148" />
<hkern g1="quotedbl,quotesingle" 	g2="s" 	k="5" />
<hkern g1="r" 	g2="J" 	k="28" />
<hkern g1="r" 	g2="T" 	k="69" />
<hkern g1="r" 	g2="Y,Yacute,Ydieresis" 	k="25" />
<hkern g1="r" 	g2="d,q" 	k="13" />
<hkern g1="r" 	g2="g" 	k="7" />
<hkern g1="r" 	g2="guillemotleft,guilsinglleft" 	k="42" />
<hkern g1="r" 	g2="hyphen,endash,emdash" 	k="46" />
<hkern g1="r" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="r" 	g2="guillemotright,guilsinglright" 	k="22" />
<hkern g1="r" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="33" />
<hkern g1="r" 	g2="Z" 	k="16" />
<hkern g1="r" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="55" />
<hkern g1="s" 	g2="J" 	k="22" />
<hkern g1="s" 	g2="T" 	k="89" />
<hkern g1="s" 	g2="W" 	k="12" />
<hkern g1="s" 	g2="Y,Yacute,Ydieresis" 	k="56" />
<hkern g1="s" 	g2="g" 	k="5" />
<hkern g1="s" 	g2="hyphen,endash,emdash" 	k="10" />
<hkern g1="s" 	g2="w" 	k="4" />
<hkern g1="s" 	g2="y,yacute,ydieresis" 	k="8" />
<hkern g1="t" 	g2="T" 	k="75" />
<hkern g1="t" 	g2="Y,Yacute,Ydieresis" 	k="29" />
<hkern g1="t" 	g2="guillemotleft,guilsinglleft" 	k="30" />
<hkern g1="t" 	g2="hyphen,endash,emdash" 	k="23" />
<hkern g1="t" 	g2="guillemotright,guilsinglright" 	k="17" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="J" 	k="17" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="T" 	k="87" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="W" 	k="15" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="w" 	g2="J" 	k="26" />
<hkern g1="w" 	g2="T" 	k="69" />
<hkern g1="w" 	g2="Y,Yacute,Ydieresis" 	k="45" />
<hkern g1="w" 	g2="d,q" 	k="5" />
<hkern g1="w" 	g2="g" 	k="7" />
<hkern g1="w" 	g2="guillemotleft,guilsinglleft" 	k="6" />
<hkern g1="w" 	g2="hyphen,endash,emdash" 	k="8" />
<hkern g1="w" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="5" />
<hkern g1="w" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="16" />
<hkern g1="w" 	g2="Z" 	k="16" />
<hkern g1="w" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="28" />
<hkern g1="w" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="5" />
<hkern g1="w" 	g2="s" 	k="4" />
<hkern g1="y,yacute,ydieresis" 	g2="J" 	k="26" />
<hkern g1="y,yacute,ydieresis" 	g2="T" 	k="67" />
<hkern g1="y,yacute,ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="41" />
<hkern g1="y,yacute,ydieresis" 	g2="d,q" 	k="9" />
<hkern g1="y,yacute,ydieresis" 	g2="g" 	k="10" />
<hkern g1="y,yacute,ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="12" />
<hkern g1="y,yacute,ydieresis" 	g2="hyphen,endash,emdash" 	k="14" />
<hkern g1="y,yacute,ydieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="9" />
<hkern g1="y,yacute,ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="18" />
<hkern g1="y,yacute,ydieresis" 	g2="Z" 	k="16" />
<hkern g1="y,yacute,ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="36" />
<hkern g1="y,yacute,ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="9" />
<hkern g1="y,yacute,ydieresis" 	g2="s" 	k="7" />
<hkern g1="z" 	g2="J" 	k="7" />
<hkern g1="z" 	g2="T" 	k="86" />
<hkern g1="z" 	g2="W" 	k="6" />
<hkern g1="z" 	g2="Y,Yacute,Ydieresis" 	k="51" />
<hkern g1="z" 	g2="d,q" 	k="4" />
<hkern g1="z" 	g2="guillemotleft,guilsinglleft" 	k="26" />
<hkern g1="z" 	g2="hyphen,endash,emdash" 	k="32" />
<hkern g1="z" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="3" />
</font>
</defs></svg> 