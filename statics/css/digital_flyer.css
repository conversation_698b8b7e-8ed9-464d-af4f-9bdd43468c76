/*
    html, body
*/

html,
body{
    font-size: 100%;
    line-height: normal;

    margin: 0;
    padding: 0;

    height: 100%;
    width: 100%;
}

/*
    boxsising
*/

html{
    box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
}

html *,
html * :before,
html * :after{
    box-sizing: inherit;
    -moz-box-sizing: inherit;
    -webkit-box-sizing: inherit;
}

/*
    fonts
*/

@font-face {
    font-family: 'Proxima Nova';
    src: url(../digital_flyer/fonts/proximanova-light-webfont.eot);
    src: url(../digital_flyer/fonts/proximanova-light-webfont.eot?#iefix) format('embedded-opentype'),
         url(../digital_flyer/fonts/proximanova-light-webfont.woff2) format('woff2'),
         url(../digital_flyer/fonts/proximanova-light-webfont.woff) format('woff'),
         url(../digital_flyer/fonts/proximanova-light-webfont.ttf) format('truetype'),
         url(../digital_flyer/fonts/proximanova-light-webfont.svg#proxima_novalight) format('svg');
    font-weight: 300;
    font-style: normal;
}

@font-face {
    font-family: 'Proxima Nova';
    src: url(../digital_flyer/fonts/proximanova-regular-webfont.eot);
    src: url(../digital_flyer/fonts/proximanova-regular-webfont.eot?#iefix) format('embedded-opentype'),
         url(../digital_flyer/fonts/proximanova-regular-webfont.woff2) format('woff2'),
         url(../digital_flyer/fonts/proximanova-regular-webfont.woff) format('woff'),
         url(../digital_flyer/fonts/proximanova-regular-webfont.ttf) format('truetype'),
         url(../digital_flyer/fonts/proximanova-regular-webfont.svg#proxima_novaregular) format('svg');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'Proxima Nova';
    src: url(../digital_flyer/fonts/proximanova-semibold-webfont.eot);
    src: url(../digital_flyer/fonts/proximanova-semibold-webfont.eot?#iefix) format('embedded-opentype'),
         url(../digital_flyer/fonts/proximanova-semibold-webfont.woff2) format('woff2'),
         url(../digital_flyer/fonts/proximanova-semibold-webfont.woff) format('woff'),
         url(../digital_flyer/fonts/proximanova-semibold-webfont.ttf) format('truetype'),
         url(../digital_flyer/fonts/proximanova-semibold-webfont.svg#proxima_novasemibold) format('svg');
    font-weight: 500;
    font-style: normal;
}

@font-face {
    font-family: 'HelveticaNeueLTStd-BdCn';
    src: url(../digital_flyer/fonts/33614A_0_0.eot);
    src: url(../digital_flyer/fonts/33614A_0_0.eot?#iefix) format('embedded-opentype'),
         url(../digital_flyer/fonts/33614A_0_0.woff2) format('woff2'),
         url(../digital_flyer/fonts/33614A_0_0.woff) format('woff'),
         url(../digital_flyer/fonts/33614A_0_0.ttf) format('truetype'),
         url(../digital_flyer/fonts/33614A_0_0.svg#wf) format('svg');
}

@font-face {
    font-family: 'Titillium Web';
    src: url(../digital_flyer/fonts/titilliumweb-regular-webfont.eot);
    src: url(../digital_flyer/fonts/titilliumweb-regular-webfont.eot?#iefix) format('embedded-opentype'),
         url(../digital_flyer/fonts/titilliumweb-regular-webfont.woff2) format('woff2'),
         url(../digital_flyer/fonts/titilliumweb-regular-webfont.woff) format('woff'),
         url(../digital_flyer/fonts/titilliumweb-regular-webfont.ttf) format('truetype'),
         url(../digital_flyer/fonts/titilliumweb-regular-webfont.svg#titillium_webregular) format('svg');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'Titillium Web';
    src: url(../digital_flyer/fonts/titilliumweb-semibold-webfont.eot);
    src: url(../digital_flyer/fonts/titilliumweb-semibold-webfont.eot?#iefix) format('embedded-opentype'),
         url(../digital_flyer/fonts/titilliumweb-semibold-webfont.woff2) format('woff2'),
         url(../digital_flyer/fonts/titilliumweb-semibold-webfont.woff) format('woff'),
         url(../digital_flyer/fonts/titilliumweb-semibold-webfont.ttf) format('truetype'),
         url(../digital_flyer/fonts/titilliumweb-semibold-webfont.svg#titillium_websemibold) format('svg');
    font-weight: 500;
    font-style: normal;
}

@font-face {
    font-family: 'Titillium Web';
    src: url(../digital_flyer/fonts/titilliumweb-bold-webfont.eot);
    src: url(../digital_flyer/fonts/titilliumweb-bold-webfont.eot?#iefix) format('embedded-opentype'),
         url(../digital_flyer/fonts/titilliumweb-bold-webfont.woff2) format('woff2'),
         url(../digital_flyer/fonts/titilliumweb-bold-webfont.woff) format('woff'),
         url(../digital_flyer/fonts/titilliumweb-bold-webfont.ttf) format('truetype'),
         url(../digital_flyer/fonts/titilliumweb-bold-webfont.svg#titillium_webbold) format('svg');
    font-weight: 600;
    font-style: normal;
}

/*
    headlines
*/

h1,	h2,
h3,	h4,
h5,	h6{
    color: inherit;
    font-size: inherit;
    font-weight: normal;
    line-height: inherit
}

/*
    vertical margin
*/

h1,	h2,
h3,	h4,
h5,	h6,
ul, ol,
p, hr,
figure,
blockquote{
    margin: 0;
}

/*
    list
*/

ul, ol{
    list-style: none;
    padding-left: 0;
}

/*
    image
*/

img{
    vertical-align: middle;
}

/*
    digital flyer

    font-family: 'Proxima Nova', sans-serif;
    font-family: 'HelveticaNeueLTStd-BdCn', sans-serif;
    font-family: 'Titillium Web', sans-serif;
*/

.digitalFlyer{
    color: #FFFFFF;

    background-color: #000000;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100%;
    overflow: hidden;
    position: relative;

    height: 1080px;
    width: 1080px;
}

    /*
        header
    */

    .digitalFlyer__header{
        position: absolute; top: 120px; left: 100px; z-index: 10;
        width: 540px;
    }
        .digitalFlyer__header h1{
            font-family: 'HelveticaNeueLTStd-BdCn', sans-serif;
            font-size: 56px;
            line-height: 60px;
            text-transform: uppercase;

            margin-bottom: 10px;
        }
        .digitalFlyer__header h1:last-child{

            margin-bottom: 0;
        }
        .digitalFlyer__header p{
            font-size: 24px;
            line-height: 30px;
            font-family: 'Proxima Nova', sans-serif;
        }

    /*
        list
    */

    .digitalFlyer__list{
        position: absolute; top: 370px; left: 100px; z-index: 10;
        width: 540px;
    }
        .digitalFlyer__list__item{
            font-family: 'HelveticaNeueLTStd-BdCn', sans-serif;
            font-size: 40px;
            line-height: 50px;
            text-transform: uppercase;

            margin-bottom: 30px;
            padding-left: 110px;
            position: relative;
        }
        .digitalFlyer__list__item:last-child{
            margin-bottom: 0;
        }
            .digitalFlyer__list__item ._number{
                border: 1.5px solid #2db5b0;
                border-radius: 50%;

                position: absolute; left: 0; top: 50%; z-index: 10;
                transform: translateY(-50%);

                height: 75px;
                width: 75px;
            }
                .digitalFlyer__list__item ._number span{
                    color: #2db5b0;
                    font-family: 'Proxima Nova', sans-serif;
                    font-size: 35px;
                    font-weight: 600;
                    line-height: 1;

                    position: absolute; top: 50%; left: 50%; z-index: 10;
                    transform: translate(-50%,-50%);
                }

    /*
        headline
    */

    .digitalFlyer__headline{
        font-family: 'HelveticaNeueLTStd-BdCn', sans-serif;
        font-size: 66px;
        line-height: 70px;
        text-transform: uppercase;

        border-top: 2px solid #2db5b0;
        border-bottom: 2px solid #2db5b0;
        border-left: 2px solid #2db5b0;
        border-radius: 3px;
        padding: 60px;
        position: absolute; top: 370px; left: 100px; z-index: 0;
        width: 980px;
    }
        .digitalFlyer__headline img{
            position: absolute; bottom: -60px; left: 120px; z-index: 10;

            height: 80px;
            width: auto;
        }

    /*
        review
    */

    .digitalFlyer__review{
        text-align: center;
        position: absolute; top: 100px; left: 100px; z-index: 0;
        height: 720px;
        width: 880px;
    }
        .digitalFlyer__review h1{
            font-family: 'HelveticaNeueLTStd-BdCn', sans-serif;
            font-size: 56px;
            line-height: 60px;
            text-transform: uppercase;

            display: table;
            vertical-align: middle;

            height: 120px;
            width: 100%;
        }
        .digitalFlyer__review h1 span{
            display: table-cell;
            vertical-align: middle;
        }
        .digitalFlyer__review p{
            font-family: 'Proxima Nova', sans-serif;
            font-size: 50px;
            font-style: italic;
            line-height: 60px;

            height: 350px;
        }
            .digitalFlyer__review p span{
                font-style: normal;
            }
        .digitalFlyer__review ul{
            position: absolute; bottom: 40px; left: 285px; z-index: 10;
        }
        .digitalFlyer__review ul li{
            display: inline-block;
            margin-left: 5px;
            margin-right: 5px;
        }
        .digitalFlyer__review ul li img{
            height: 50px;
            width: 52px;
        }

        .digitalFlyer__review__line{
            background: rgba( 255,255,255,0.5 );
            margin: 60px auto;

            height: 1px;
            width: 220px;
        }

    /*
        subdomain
    */

    .digitalFlyer__subdomain{
        display: table;

        position: absolute; bottom: 120px; left: 0; z-index: 10;
        height: 140px;
        width: 100%;
    }
        .digitalFlyer__subdomain__content{
            font-family: 'Proxima Nova', sans-serif;
            font-size: 41px;
            font-weight: 500;
            line-height: 1;
            text-transform: uppercase;

            display: table-cell;
            vertical-align: middle;

            padding-left: 60px;
            padding-right: 60px;
        }

    .digitalFlyer__subdomain.-green{
        background: #2db5b0;
    }
        .digitalFlyer__subdomain.-green .digitalFlyer__subdomain__content{
            color: #FFFFFF;
        }

    .digitalFlyer__subdomain.-white{
        background: #FFFFFF;
    }
        .digitalFlyer__subdomain.-white .digitalFlyer__subdomain__content{
            color: #000000;
        }

    /*
        footer
    */

    .digitalFlyer__footer{
        background: #000000;
        padding: 40px 60px;

        position: absolute; bottom: 0; left: 0; z-index: 10;
        width: 100%;
    }
        .digitalFlyer__footer:after{
            content: '' !important;
            display: table !important;
            clear: both !important
        }

        /*
            copy
        */

        .digitalFlyer__footer__copy{
            float: left;
        }

            .digitalFlyer__footer__copy ._logo{
                height: 40px;
                width: auto;
            }
            .digitalFlyer__phone__cover__content ._logo img{
                height: 100%;
                width: 100%;
            }
            .digitalFlyer__footer__copy ._separator{
                background: #4d4d4d;
                margin-left: 20px;
                margin-right: 20px;

                height: 40px;
                width: 1px;
            }
            .digitalFlyer__footer__copy ._tagline{
                font-family: 'Proxima Nova', sans-serif;
                font-size: 20px;
                font-weight: 500;
                line-height: 1;
            }

            .digitalFlyer__footer__copy ._logo,
            .digitalFlyer__footer__copy ._separator,
            .digitalFlyer__footer__copy ._tagline{
                display: inline-block;
                vertical-align: middle;
            }

        /*
            download
        */

        .digitalFlyer__footer__download{
            float: right;
        }
            .digitalFlyer__footer__download ._icons{


                height: 40px;
                width: auto;
            }
            .digitalFlyer__footer__download ._separator{
                background: #4d4d4d;
                margin-left: 40px;
                margin-right: 40px;

                height: 40px;
                width: 1px;
            }
            .digitalFlyer__footer__download ._url{
                font-family: 'Proxima Nova', sans-serif;
                font-size: 22px;
                font-weight: 500;
                line-height: 1;
                text-transform: uppercase;
            }

            .digitalFlyer__footer__download ._icons,
            .digitalFlyer__footer__download ._separator,
            .digitalFlyer__footer__download ._url{
                display: inline-block;
                vertical-align: middle;
            }

    /*
        phone
    */

    .digitalFlyer__phone{
        background-image: url( ../digital_flyer/images/<EMAIL> );
        background-position: center;
        background-repeat: no-repeat;
        background-size: 100%;

        height: 726px;
        width: 358px;

        position: absolute; bottom: 170px; right: 60px; z-index: 5;
    }

        .digitalFlyer__phone__screen{
            background: #FFFFFF;
            position: absolute; top: 88px; left: 22px; z-index: 10;

            height: 552px;
            width: 312px;
        }

        .digitalFlyer__phone__bar img{
            height: 20px;
            width: auto;
        }

        .digitalFlyer__phone__cover{
            background-color: #000000;
            background-position: center;
            background-repeat: no-repeat;
            background-size: 100%;
            display: table;
            position: relative;

            height: 176px;
            width: 100%;
        }
            .digitalFlyer__phone__cover ._back,
            .digitalFlyer__phone__cover ._menu{
                position: absolute; top: 15px; z-index: 10;

                height: 18px;
                width: auto;
            }
            .digitalFlyer__phone__cover ._back{
                left: 15px;
            }
            .digitalFlyer__phone__cover ._menu{
                right: 15px;
            }

            .digitalFlyer__phone__cover__content{
                text-align: center;

                display: table-cell;
                vertical-align: middle;

                padding-left: 30px;
                padding-right: 30px;

                width: 312px;
            }
                .digitalFlyer__phone__cover__content ._logo{
                    background: #FFFFFF;
                    margin-bottom: 10px;

                    height: 56px;
                    width: 56px;
                }
                .digitalFlyer__phone__cover__content ._name{
                    font-family: 'Titillium Web', sans-serif;
                    font-size: 20px;
                    line-height: 24px;
                    text-transform: uppercase;
                }

                .digitalFlyer__phone__cover__content ._logo,
                .digitalFlyer__phone__cover__content ._name{
                    display: inline-block;
                }

        .digitalFlyer__phone__menu{
            background: #000000;
            padding-bottom: 5px;
        }
            .digitalFlyer__phone__menu div{
                color: #E1E1E1;
                font-family: 'Proxima Nova', sans-serif;
                font-size: 11px;
                line-height: 1;
                text-align: center;
                text-transform: uppercase;

                display: inline-block;
                width: 25%;
            }
            .digitalFlyer__phone__menu div span{
                border-bottom: 2px solid #000000;
                display: inline-block;
                padding-top: 5px;
                padding-bottom: 5px;
            }
            .digitalFlyer__phone__menu div:first-child span{
                border-bottom: 2px solid #00A39E;
                font-weight: 500;
            }

        .digitalFlyer__phone__category{
            color: #000000;
            font-family: 'Proxima Nova', sans-serif;
            font-size: 12px;
            line-height: 1;
            text-transform: uppercase;

            background: #F6F6F5;
            border-bottom: 1px solid #e2e2e2;
            padding: 14px 15px 14px 15px;
            position: relative;
        }

        .digitalFlyer__phone__service{
            font-family: 'Proxima Nova', sans-serif;

            background: #FFFFFF;
            border-bottom: 1px solid #e2e2e2;
            padding: 13px 15px;
        }

        .digitalFlyer__phone__service__name{
            color: #383734;
            font-size: 16px;
            font-weight: 500;
            line-height: 20px;
            text-align: left;

            width: 150px;
        }

        .digitalFlyer__phone__service__details{
            text-align: right;
            width: 132px;
        }
            .digitalFlyer__phone__service__details ._price{
                color: #383734;
                font-size: 16px;
                font-weight: 500;
                line-height: 1;

                margin-right: 15px;
            }
                .digitalFlyer__phone__service__details ._price span{
                    color: #8C8B88;
                    font-size: 12px;
                }

            .digitalFlyer__phone__service__details ._button{
                color: #ffffff;
                font-size: 12px;
                font-weight: 400;
                line-height: 1;

                background: #00A39E;
                padding: 8px 10px;
            }

            .digitalFlyer__phone__service__details ._price,
            .digitalFlyer__phone__service__details ._button{
                display: inline-block;
                vertical-align: top;
            }

        .digitalFlyer__phone__service__name,
        .digitalFlyer__phone__service__details{
            display: inline-block;
            vertical-align: top;
        }

/*
    facebook
*/

.digitalFlyer.-facebook{
    height: 628px;
    width: 1200px;
}
.digitalFlyer.-facebook .digitalFlyer__subdomain{
    bottom: 0;
}
.digitalFlyer.-facebook .digitalFlyer__headline{
    top: 70px;
}
.digitalFlyer.-facebook .digitalFlyer__phone{
    bottom: -140px;
}
