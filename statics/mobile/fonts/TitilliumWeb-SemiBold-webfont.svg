<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="titillium_websemibold" horiz-adv-x="1146" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="450" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="681" />
<glyph unicode=" "  horiz-adv-x="450" />
<glyph unicode="&#x09;" horiz-adv-x="450" />
<glyph unicode="&#xa0;" horiz-adv-x="450" />
<glyph unicode="!" horiz-adv-x="544" d="M152 0v287h239v-287h-239zM174 494l-20 909h237l-20 -909h-197z" />
<glyph unicode="&#x22;" horiz-adv-x="808" d="M674 928h-195l-8 475h215zM328 928h-197l-8 475h217z" />
<glyph unicode="#" d="M1106 328h-209v-328h-195v328h-258v-328h-194v328h-209v186h209v311h-209v187h209v346h194v-346h258v346h195v-346h209v-187h-209v-311h209v-186zM702 514v311h-258v-311h258z" />
<glyph unicode="$" d="M1030 401q0 -213 -122 -318.5t-330 -105.5h-17l-29 -237q-131 6 -131 14l29 232q-131 12 -254 37l-41 8l23 176q162 -23 297 -31l55 424q-213 57 -301 141t-88 264.5t119.5 274.5t330.5 94h37l35 275h131l-37 -285l262 -37l-18 -176q-145 16 -266 25l-51 -396 q207 -59 286.5 -137t79.5 -242zM340 1006q0 -76 42 -115t155 -76l47 367q-244 -4 -244 -176zM809 389q0 70 -38 109t-134 67l-51 -393q223 8 223 217z" />
<glyph unicode="%" d="M283 -12l454 1427l127 -43l-454 -1425zM41 1083q0 291 236.5 291t236.5 -291q0 -147 -61.5 -221t-175 -74t-175 74t-61.5 221zM209 1083q0 -82 15.5 -118.5t54 -36.5t54 36.5t15.5 118.5t-15.5 118t-54 36t-54 -36t-15.5 -118zM633 272q0 291 236.5 291t236.5 -291 q0 -147 -61.5 -221t-175 -74t-175 74t-61.5 221zM814 388q-15 -36 -15 -117t15 -118.5t54 -37.5t54.5 36.5t15.5 119.5t-14.5 118t-54.5 35t-55 -36z" />
<glyph unicode="&#x26;" horiz-adv-x="1425" d="M357.5 1366q105.5 82 292 82t286.5 -83t100 -229.5t-65.5 -230.5t-231.5 -180l250 -248q14 39 28.5 134.5t18.5 158.5l215 -4q-29 -250 -100 -420l244 -223l-127 -146l-232 201q-68 -94 -180.5 -147.5t-247.5 -53.5q-289 0 -409.5 109t-120.5 322q0 170 71.5 263 t231.5 152q-80 92 -104.5 155.5t-24.5 164.5q0 141 105.5 223zM584 176q98 0 180 34t121 93l-387 387q-102 -39 -148.5 -100.5t-46.5 -163.5q0 -250 281 -250zM477 1098q0 -100 92 -199l45 -47q109 61 153 114.5t44 137.5q0 156 -167 156t-167 -162z" />
<glyph unicode="'" horiz-adv-x="460" d="M131 928l-6 475h217l-14 -475h-197z" />
<glyph unicode="(" horiz-adv-x="591" d="M334 616.5q0 -178.5 48 -396.5t97 -347l49 -129h-211q-29 47 -73.5 156.5t-75.5 207t-55.5 237.5t-24.5 310t57.5 399.5t114.5 354.5l57 127h211q-70 -176 -132 -458.5t-62 -461z" />
<glyph unicode=")" horiz-adv-x="591" d="M504 616.5q0 -131.5 -23.5 -269.5t-58.5 -241q-70 -217 -123 -319l-25 -43h-211q23 53 55 142t86 320.5t54 410t-48 408.5t-97 372l-50 139h211q29 -53 74 -172t76 -222.5t55.5 -248.5t24.5 -276.5z" />
<glyph unicode="*" horiz-adv-x="866" d="M782 1032h-260l80 -250l-127 -39l-84 252l-215 -161l-82 104l219 162l-213 151l80 111l211 -154l82 252l131 -43l-80 -254h258v-131z" />
<glyph unicode="+" d="M106 414v200h365v367h201v-367h368v-200h-368v-371h-201v371h-365z" />
<glyph unicode="," horiz-adv-x="503" d="M53 -252l99 506h245l-164 -506h-180z" />
<glyph unicode="-" horiz-adv-x="862" d="M125 451v202h612v-202h-612z" />
<glyph unicode="." horiz-adv-x="489" d="M125 0v295h240v-295h-240z" />
<glyph unicode="/" horiz-adv-x="907" d="M66 18l583 1444l197 -71l-586 -1442z" />
<glyph unicode="0" d="M573 1374q256 0 383 -163.5t127 -541.5t-128 -535t-383 -157t-383 156t-128 533.5t128 542.5t384 165zM573.5 1176q-147.5 0 -212 -112t-64.5 -392.5t66.5 -388t211 -107.5t209 107.5t64.5 388t-63.5 392.5t-211 112z" />
<glyph unicode="1" d="M829 1352v-1352h-227v1094l-317 -205l-105 170l441 293h208z" />
<glyph unicode="2" d="M1008 0h-871v195l334 344q152 156 213 243.5t61 193t-59 150.5t-194 45q-127 0 -281 -28l-51 -8l-15 180q199 59 412 59q424 0 424 -377q0 -147 -64.5 -255.5t-230.5 -262.5l-289 -280h611v-199z" />
<glyph unicode="3" d="M135 1309q190 66 417.5 65.5t331 -87.5t103.5 -275q0 -82 -31.5 -147.5t-64.5 -95.5t-82 -62q109 -51 157 -111.5t48 -196.5q0 -215 -107.5 -318.5t-343.5 -103.5q-92 0 -202.5 15.5t-176.5 32.5l-63 16l14 176q225 -41 397 -41q246 2 246 217q0 98 -63.5 149.5 t-167.5 53.5h-273v190h273q76 0 140.5 62.5t64.5 152t-56.5 132.5t-154.5 43q-162 0 -334 -29l-53 -10z" />
<glyph unicode="4" d="M690 0v242h-608v174l356 936h252l-377 -912h377v398h230v-398h147v-198h-147v-242h-230z" />
<glyph unicode="5" d="M991 1352v-201h-626l-35 -356q139 63 274 63q434 0 434 -405q0 -229 -119.5 -352.5t-338.5 -123.5q-92 0 -209 17.5t-187 34.5l-69 18l24 172q242 -41 414 -41q119 0 184.5 65.5t65.5 186.5t-57.5 174t-149.5 53q-166 0 -270 -41l-37 -14l-144 31l37 719h809z" />
<glyph unicode="6" d="M1004 1147q-199 29 -361 29t-241.5 -100.5t-82.5 -280.5l48 16q147 47 247 47q459 0 459 -430q0 -221 -127 -336t-370.5 -115t-367.5 181.5t-124 517.5q0 698 549 698q154 0 332 -37l59 -12zM356 612l-39 -14q4 -422 271 -422q121 0 185.5 65.5t64.5 182.5t-63.5 175 t-177.5 58t-241 -45z" />
<glyph unicode="7" d="M150 1147v205h851v-271l-497 -1104l-213 62l481 1046v62h-622z" />
<glyph unicode="8" d="M569.5 1374q225.5 0 359.5 -93t134 -267q0 -123 -38 -185.5t-142 -128.5q104 -68 152 -138t48 -195q0 -205 -141 -297.5t-373 -92.5q-508 0 -508 367q0 137 47.5 211t149.5 145q-96 63 -135 132t-39 184q0 172 130 265t355.5 93zM305 393q0 -211 267.5 -211t267.5 215 q0 141 -172 195h-197q-166 -54 -166 -199zM819 981q0 188 -245.5 188t-245.5 -188q0 -123 143 -195h197q151 72 151 195z" />
<glyph unicode="9" d="M508 176q311 0 315 393l-49 -16q-156 -53 -250 -53q-221 0 -337.5 105.5t-116.5 316.5t130 331.5t353 120.5q256 0 381 -182t125 -538.5t-140.5 -516.5t-410.5 -160q-154 0 -332 37l-59 13l20 178q199 -29 371 -29zM543 700q117 0 239 45l43 15q-4 416 -272 416 q-117 0 -182.5 -68t-65.5 -184q0 -224 238 -224z" />
<glyph unicode=":" horiz-adv-x="489" d="M125 621v294h240v-294h-240zM125 0v295h240v-295h-240z" />
<glyph unicode=";" horiz-adv-x="544" d="M172 254h246l-164 -506h-180zM158 621v294h239v-294h-239z" />
<glyph unicode="&#x3c;" d="M971 772l-602 -254l602 -266v-229l-840 399v184l840 393v-227z" />
<glyph unicode="=" d="M125 616v201h897v-201h-897zM125 211v201h897v-201h-897z" />
<glyph unicode="&#x3e;" d="M778 518l-602 254v227l840 -393v-184l-840 -399v229z" />
<glyph unicode="?" horiz-adv-x="903" d="M831 1094q0 -137 -33.5 -209t-138 -154t-138.5 -130t-34 -103v-68h-180q-49 63 -49 160q0 51 39 102t139.5 134t133 129t32.5 122q0 150 -217 150q-123 0 -258 -29l-47 -10l-12 170q190 68 346 67q215 0 316 -78.5t101 -252.5zM272 0v287h238v-287h-238z" />
<glyph unicode="@" horiz-adv-x="1996" d="M1923 625v-21q0 -338 -94 -474t-285 -136q-92 0 -156.5 29.5t-82.5 58.5l-19 31q-180 -117 -343 -117t-260 113.5t-97 389t90 402.5t311 127q82 0 176 -37l33 -14v29h221v-377q0 -311 21 -379q6 -25 22.5 -43.5t33.5 -22.5t60 -4t76 29q70 63 70 395v23q0 350 -159 507.5 t-513 157.5t-535.5 -190.5t-181.5 -582.5t167 -569t552 -177l293 18l10 -194q-188 -18 -303 -19q-236 0 -403.5 46t-290.5 155q-246 215 -246 756q0 481 247 716.5t695 235.5q891 0 891 -862zM977 190q86 0 233 70q-14 82 -14 277v266q-102 31 -164 31q-131 0 -176 -75 t-45 -251q0 -318 166 -318z" />
<glyph unicode="A" horiz-adv-x="1226" d="M41 0l346 1403h453l348 -1403h-230l-75 301h-539l-76 -301h-227zM563 1212l-174 -710h449l-172 710h-103z" />
<glyph unicode="B" horiz-adv-x="1253" d="M162 1403h532q215 0 321.5 -87t106.5 -278q0 -125 -40 -196.5t-126 -118.5q201 -78 201 -326q0 -397 -446 -397h-549v1403zM694 612h-305v-415h309q113 0 169.5 47t56.5 161.5t-66 160.5t-164 46zM682 1206h-293v-401h301q201 0 201 207q0 194 -209 194z" />
<glyph unicode="C" horiz-adv-x="1114" d="M633 -23q-307 0 -417 170t-110 559.5t111 554t416 164.5q182 0 401 -51l-8 -184q-184 33 -368.5 33t-250 -109.5t-65.5 -412t63.5 -412t248 -109.5t372.5 31l6 -188q-211 -46 -399 -46z" />
<glyph unicode="D" horiz-adv-x="1312" d="M647 0h-485v1403h485q324 0 442.5 -164t118.5 -514q0 -178 -23.5 -302t-82.5 -224q-117 -199 -455 -199zM973 725q0 254 -63.5 366.5t-262.5 112.5h-258v-1003h258q203 0 271 149q33 76 44 162t11 213z" />
<glyph unicode="E" horiz-adv-x="1132" d="M162 0v1403h885v-199h-658v-397h535v-197h-535v-409h658v-201h-885z" />
<glyph unicode="F" horiz-adv-x="1083" d="M162 0v1403h870v-199h-643v-473h535v-199h-535v-532h-227z" />
<glyph unicode="G" horiz-adv-x="1255" d="M733 537v200h400v-712q-55 -14 -224.5 -31t-244.5 -17q-319 0 -440.5 176.5t-121.5 552t124 547.5t429 172q180 0 406 -41l72 -14l-9 -180q-248 33 -444.5 33t-269 -111t-72.5 -410t68.5 -412.5t277.5 -113.5q150 0 223 19v342h-174z" />
<glyph unicode="H" horiz-adv-x="1384" d="M995 0v606h-606v-606h-227v1403h227v-598h606v598h230v-1403h-230z" />
<glyph unicode="I" horiz-adv-x="550" d="M162 0v1403h227v-1403h-227z" />
<glyph unicode="J" horiz-adv-x="606" d="M39 -145v200q109 0 148.5 33t39.5 145v1170h226l2 -1186q0 -219 -92.5 -290.5t-323.5 -71.5z" />
<glyph unicode="K" horiz-adv-x="1193" d="M389 0h-227v1403h227v-633l209 16l287 617h260l-346 -705l360 -698h-264l-297 586l-209 -15v-571z" />
<glyph unicode="L" horiz-adv-x="972" d="M948 0h-786v1403h227v-1200h559v-203z" />
<glyph unicode="M" horiz-adv-x="1740" d="M162 0v1403h395l313 -1096l314 1096h397v-1403h-229v1151h-31l-332 -1094h-237l-332 1094h-31v-1151h-227z" />
<glyph unicode="N" horiz-adv-x="1404" d="M162 0v1403h397l426 -1202h31v1202h227v-1403h-389l-436 1204h-29v-1204h-227z" />
<glyph unicode="O" horiz-adv-x="1343" d="M671.5 176q196.5 0 265.5 119t69 398.5t-71 406.5t-263.5 127t-264 -127t-71.5 -404.5t69.5 -398.5t266 -121zM671.5 -23q-317.5 0 -444.5 173.5t-127 544t128 550.5t443.5 180t442.5 -179t127 -551.5t-126 -545t-443.5 -172.5z" />
<glyph unicode="P" horiz-adv-x="1210" d="M676 436h-287v-436h-227v1403h514q477 0 477 -471q0 -242 -119.5 -369t-357.5 -127zM389 633h285q246 0 246 299q0 143 -59.5 208.5t-186.5 65.5h-285v-573z" />
<glyph unicode="Q" horiz-adv-x="1343" d="M672 -23q-319 0 -445.5 172.5t-126.5 544t128 551.5t443.5 180t442.5 -179t127 -552q0 -246 -52 -397.5t-171 -230.5l172 -277l-211 -98l-182 299q-39 -13 -125 -13zM671.5 176q196.5 0 265.5 118t69 398.5t-71 407.5t-263.5 127t-264 -127t-71.5 -405.5t69.5 -398.5 t266 -120z" />
<glyph unicode="R" horiz-adv-x="1265" d="M389 500v-500h-227v1403h532q475 0 475 -447q0 -299 -229 -405l231 -551h-249l-203 500h-330zM936 954q0 252 -242 252h-305v-510h309q123 0 180.5 70.5t57.5 187.5z" />
<glyph unicode="S" horiz-adv-x="1112" d="M573 1227q-260 0 -260 -183q0 -94 61.5 -134t271.5 -98t296 -139t86 -255q0 -221 -126 -331t-343 -110q-174 0 -385 41l-74 15l23 182q276 -37 422 -37q254 0 254 225q0 88 -57.5 130t-258 92.5t-299 138.5t-98.5 278.5t124 287.5t343 97q158 0 373 -36l71 -13l-18 -184 q-287 33 -406 33z" />
<glyph unicode="T" horiz-adv-x="1077" d="M27 1200v203h1024v-203h-396v-1200h-229v1200h-399z" />
<glyph unicode="U" horiz-adv-x="1331" d="M379 449q0 -272 286.5 -272.5t286.5 272.5v954h230v-950q0 -248 -130 -362t-387 -114t-386 114t-129 362v950h229v-954z" />
<glyph unicode="V" horiz-adv-x="1216" d="M938 1403h238l-345 -1403h-446l-344 1403h238l278 -1202h102z" />
<glyph unicode="W" horiz-adv-x="1865" d="M49 1403h240l200 -1206h43l267 1202h266l266 -1202h45l201 1206h240l-277 -1403h-366l-242 1124l-240 -1124h-368z" />
<glyph unicode="X" horiz-adv-x="1155" d="M283 1403l301 -553l305 553h239l-415 -713l415 -690h-254l-301 528l-307 -528h-239l417 678l-417 725h256z" />
<glyph unicode="Y" horiz-adv-x="1124" d="M678 0h-229v578l-441 825h256l297 -602l299 602h254l-436 -825v-578z" />
<glyph unicode="Z" horiz-adv-x="1093" d="M86 1204v199h922v-219l-652 -946v-39h652v-199h-922v217l649 946v41h-649z" />
<glyph unicode="[" horiz-adv-x="704" d="M627 1534v-199h-252v-1388h252v-199h-477v1786h477z" />
<glyph unicode="\" horiz-adv-x="964" d="M901 27l-194 -82l-644 1427l193 86z" />
<glyph unicode="]" horiz-adv-x="704" d="M78 1335v199h477v-1786h-477v199h252v1388h-252z" />
<glyph unicode="^" d="M819 647l-254 488l-256 -488h-233l389 705h192l396 -705h-234z" />
<glyph unicode="_" horiz-adv-x="1280" d="M203 -166h876v-190h-876v190z" />
<glyph unicode="`" horiz-adv-x="520" d="M49 1532l483 -207l-53 -145l-499 159z" />
<glyph unicode="a" horiz-adv-x="1038" d="M893 709v-467q2 -45 23.5 -66.5t66.5 -28.5l-6 -170q-176 0 -272 76q-164 -76 -330 -76q-305 0 -305 326q0 156 83 225.5t255 83.5l264 23v74q0 82 -36 114.5t-106 32.5q-131 0 -327 -16l-66 -4l-8 157q223 53 410.5 53.5t270.5 -80.5t83 -257zM434 455 q-141 -12 -141 -153.5t125 -141.5q102 0 217 33l37 12v272z" />
<glyph unicode="b" horiz-adv-x="1097" d="M612 1047q211 0 304.5 -118t93.5 -417t-111 -417t-389 -118q-96 0 -305 19l-70 6v1448h221v-465q141 62 256 62zM510 176q164 0 218 77t54 261t-46 259t-150 75q-98 0 -197 -31l-33 -10v-623q113 -8 154 -8z" />
<glyph unicode="c" horiz-adv-x="905" d="M518 1047q109 0 256 -29l51 -10l-8 -177q-162 16 -239 17q-156 0 -209.5 -69.5t-53.5 -262t51.5 -266.5t213.5 -74l239 17l6 -179q-207 -37 -311 -37q-233 0 -328.5 126t-95.5 413t100.5 409t327.5 122z" />
<glyph unicode="d" horiz-adv-x="1105" d="M971 1450v-1450h-221v53q-150 -76 -275 -76q-201 0 -294 121t-93 405.5t103.5 414t322.5 129.5q74 0 234 -27v430h223zM713 213l35 14v607q-123 20 -228 20q-207 0 -207 -344q0 -188 48.5 -261t153 -73t198.5 37z" />
<glyph unicode="e" horiz-adv-x="1040" d="M864 184l58 6l4 -165q-233 -47 -414 -48q-227 0 -326.5 125t-99.5 400q0 545 444 545q430 0 431 -469l-15 -160h-635q2 -127 55.5 -186.5t198.5 -59.5t299 12zM741 590q0 152 -48 212t-162.5 60t-167 -63.5t-54.5 -208.5h432z" />
<glyph unicode="f" horiz-adv-x="708" d="M403 834v-834h-221v834h-119v190h119v66q0 219 63.5 301t225.5 82l221 -21l-2 -182q-100 4 -166.5 4t-93.5 -40t-27 -147v-63h271v-190h-271z" />
<glyph unicode="g" horiz-adv-x="1075" d="M1030 -139q0 -336 -485 -336q-233 0 -346 63.5t-113 231.5q0 76 37 130t119 118q-68 45 -68 149q0 41 55 133l19 31q-147 88 -148 307q0 184 111 270t299 86q90 0 178 -20l31 -6l317 8v-180l-170 10q55 -72 56 -168q0 -201 -101.5 -277.5t-316.5 -76.5q-53 0 -90 8 q-29 -70 -29 -107.5t38 -52t183 -16.5q244 -2 334 -65.5t90 -239.5zM305 -160q0 -72 57.5 -102.5t196.5 -30.5q248 0 248 141q0 80 -44 101.5t-175 23.5l-201 13q-45 -37 -63.5 -69t-18.5 -77zM367 550q45 -44 145 -44t144 44t44 138t-45 138t-145 44q-188 0 -188 -182 q0 -94 45 -138z" />
<glyph unicode="h" horiz-adv-x="1118" d="M358 0h-223v1450h223v-479q152 76 281 76q205 0 279.5 -116t74.5 -382v-549h-223v543q0 166 -35 235.5t-147 69.5q-98 0 -197 -33l-33 -12v-803z" />
<glyph unicode="i" horiz-adv-x="493" d="M135 0v1024h223v-1024h-223zM135 1198v236h223v-236h-223z" />
<glyph unicode="j" horiz-adv-x="495" d="M137 39v985h221v-987q0 -201 -70.5 -297t-283.5 -205l-88 166q143 90 182 146.5t39 191.5zM137 1198v236h221v-236h-221z" />
<glyph unicode="k" horiz-adv-x="1021" d="M358 0h-223v1450h223v-838l127 13l242 399h250l-295 -479l311 -545h-252l-249 432l-134 -14v-418z" />
<glyph unicode="l" horiz-adv-x="518" d="M147 0v1450h224v-1450h-224z" />
<glyph unicode="m" horiz-adv-x="1716" d="M358 0h-223v1024h221v-63q145 86 267 86q178 0 260 -101q186 100 370.5 100.5t260 -113.5t75.5 -384v-549h-221v543q0 166 -34 235.5t-140 69.5q-92 0 -199 -41l-34 -14q16 -41 16 -263v-530h-221v526q0 182 -33 252t-143 70q-102 0 -191 -41l-31 -12v-795z" />
<glyph unicode="n" horiz-adv-x="1118" d="M358 0h-223v1024h221v-63q150 86 283 86q205 0 279.5 -116t74.5 -382v-549h-221v543q0 166 -36 235.5t-146 69.5q-104 0 -201 -41l-31 -12v-795z" />
<glyph unicode="o" horiz-adv-x="1085" d="M542.5 1047q245.5 0 351 -133.5t105.5 -399.5t-102 -401.5t-354 -135.5t-354.5 135.5t-102.5 401.5t105.5 399.5t351 133.5zM543 166q137 0 184 82t47 268t-50 264t-181 78t-181.5 -78t-50.5 -264t47.5 -268t184.5 -82z" />
<glyph unicode="p" horiz-adv-x="1099" d="M135 -440v1464h221v-63q141 86 265 86q203 0 297 -124t94 -416t-107.5 -411t-351.5 -119q-84 0 -195 19v-436h-223zM582 848q-96 0 -193 -43l-31 -14v-605q82 -16 181 -16q139 0 192 80t53 272q1 326 -202 326z" />
<glyph unicode="q" horiz-adv-x="1095" d="M489 -23q-211 0 -306 119t-95 417t111.5 416t386.5 118q100 0 307 -19l68 -6v-1462h-222v479q-137 -62 -250 -62zM586 854q-162 0 -218.5 -81t-56.5 -263t50.5 -258t154.5 -76q96 0 193 31l30 10v629q-98 8 -153 8z" />
<glyph unicode="r" horiz-adv-x="739" d="M135 0v1024h221v-123q174 113 349 146v-224q-176 -35 -302 -90l-45 -18v-715h-223z" />
<glyph unicode="s" horiz-adv-x="960" d="M846 813q-242 33 -350.5 33t-150.5 -25.5t-42 -81t46 -78t217 -52t243 -93t72 -225.5t-103.5 -238t-302.5 -76q-125 0 -315 35l-64 11l8 186q246 -33 354.5 -33t154.5 26.5t46 88t-44 85t-210.5 51.5t-244.5 87t-78 217t107.5 235.5t275.5 77.5q131 0 321 -32l64 -13z " />
<glyph unicode="t" horiz-adv-x="733" d="M686 834h-283v-451q0 -125 18.5 -166t94.5 -41l168 6l10 -178q-137 -27 -209 -27q-174 0 -238.5 80t-64.5 301v476h-131v190h131v297h221v-297h283v-190z" />
<glyph unicode="u" horiz-adv-x="1112" d="M756 1024h221v-1024h-221v63q-150 -86 -277 -86q-211 0 -282.5 114t-71.5 398v535h223v-537q0 -184 31 -247.5t143 -63.5q111 0 203 41l31 12v795z" />
<glyph unicode="v" horiz-adv-x="1005" d="M39 1024h233l197 -834h66l204 834h228l-267 -1024h-397z" />
<glyph unicode="w" horiz-adv-x="1572" d="M55 1024h219l162 -834h41l195 814h229l195 -814h41l159 834h222l-218 -1024h-354l-160 702l-159 -702h-355z" />
<glyph unicode="x" horiz-adv-x="950" d="M37 1024h237l201 -354l203 354h237l-305 -504l305 -520h-237l-203 350l-201 -350h-237l297 514z" />
<glyph unicode="y" horiz-adv-x="1007" d="M41 1024h219l217 -834h55l218 834h221l-389 -1464h-220l123 440h-176z" />
<glyph unicode="z" horiz-adv-x="929" d="M84 825v199h760v-199l-492 -626h492v-199h-760v199l494 626h-494z" />
<glyph unicode="{" horiz-adv-x="731" d="M467 1178l14 -256q0 -129 -43 -186.5t-180 -94.5q135 -35 179 -97.5t44 -193.5l-14 -239q0 -88 39 -136.5t145 -54.5v-188q-219 8 -310 89t-91 265l14 248q0 158 -225 219v174q117 27 171 77t54 130l-14 260q0 193 91 271.5t312 86.5l2 -188q-109 -8 -148.5 -53 t-39.5 -133z" />
<glyph unicode="|" horiz-adv-x="516" d="M147 -440v1890h222v-1890h-222z" />
<glyph unicode="}" horiz-adv-x="731" d="M264 111l-14 239q0 131 44 193.5t179 97.5q-137 37 -180 94.5t-43 186.5l14 256q0 88 -40 133t-148 53l2 188q221 -8 312 -87t91 -271l-14 -260q0 -80 54 -130t171 -77v-174q-225 -61 -225 -219l14 -248q0 -184 -91 -265t-310 -89v188q106 6 145 54.5t39 136.5z" />
<glyph unicode="~" d="M781.5 371q-56.5 0 -215 52t-197.5 52t-93.5 -21.5t-89.5 -43.5l-34 -21l-21 180q127 106 246 107q57 0 210.5 -52.5t194.5 -52.5q72 0 181 64l34 20l19 -178q-37 -37 -107.5 -71.5t-127 -34.5z" />
<glyph unicode="&#xa1;" horiz-adv-x="507" d="M375 1024v-287h-240v287h240zM352 530l21 -909h-238l21 909h196z" />
<glyph unicode="&#xa2;" d="M561 -160v226q-186 10 -278.5 114.5t-92.5 329.5q0 422 369 448v222h186v-230l181 -24l-6 -170q-162 10 -250 10q-139 0 -195.5 -59.5t-56.5 -197.5t56 -194.5t202 -56.5l246 10l6 -170q-98 -20 -183 -24v-234h-184z" />
<glyph unicode="&#xa3;" d="M934 1155q-150 18 -243 18.5t-122.5 -41.5t-29.5 -153v-107h327v-190h-327v-492h280l148 33l34 -186l-165 -37h-678v190h161v492h-135v190h135v117q0 223 76 303t248 80q121 0 254 -31l43 -10z" />
<glyph unicode="&#xa4;" d="M760 176q-88 -49 -187.5 -49t-187.5 49l-145 -147l-154 153l147 146q-49 82 -49 184t49 190l-147 148l154 154l145 -148q88 49 187.5 49t187.5 -49l147 148l154 -154l-148 -148q49 -88 49.5 -187t-49.5 -187l148 -146l-154 -153zM573.5 315q81.5 0 141 59.5t59.5 141.5 t-59.5 141.5t-141 59.5t-141 -59.5t-59.5 -141.5t59.5 -141.5t141 -59.5z" />
<glyph unicode="&#xa5;" d="M119 616v189h229l-321 547h254l294 -484l293 484h254l-319 -547h225v-189h-330l-8 -36v-93h338v-188h-338v-299h-227v299h-344v188h344v93l-6 36h-338z" />
<glyph unicode="&#xa6;" horiz-adv-x="524" d="M152 1450h221v-764h-221v764zM152 336h221v-776h-221v776z" />
<glyph unicode="&#xa7;" horiz-adv-x="1042" d="M887 1094q-227 33 -339 32.5t-160 -36t-48 -112.5t59.5 -107.5t233.5 -68.5t242.5 -97.5t68.5 -204t-96 -277.5q76 -66 76 -235q0 -342 -420 -342q-111 0 -311 32l-62 11l23 180q221 -31 333 -31q223 0 224 154q0 72 -51.5 98.5t-143.5 47t-96 22.5q-182 41 -257 105.5 t-75 207.5q0 74 42 154t85 114q-90 66 -90 224q0 354 422 354q135 0 293 -33l57 -10zM362 666q-51 -76 -51 -151t36 -104.5t146.5 -55t182.5 -52.5q12 16 27.5 68.5t15.5 110t-41 87t-174.5 62.5t-141.5 35z" />
<glyph unicode="&#xa8;" horiz-adv-x="520" d="M-45 1243v234h213v-234h-213zM383 1243v234h213v-234h-213z" />
<glyph unicode="&#xa9;" horiz-adv-x="1318" d="M263 482q-157 165 -157 409t157 406.5t399.5 162.5t396 -166t153.5 -408.5t-153.5 -405.5t-396 -163t-399.5 165zM217 888.5q0 -192.5 126 -327.5t315.5 -135t316.5 134t127 327.5t-128 330t-315.5 136.5t-314.5 -136.5t-127 -329zM678 1219q78 0 162 -27l-13 -148 q-74 14 -134 14.5t-79.5 -35.5t-19.5 -129t20.5 -134t73.5 -41l139 12l13 -141q-66 -33 -185.5 -33t-177 75t-57.5 257t59.5 256t198.5 74z" />
<glyph unicode="&#xaa;" horiz-adv-x="825" d="M676 1128v-284q16 -20 47 -25l-4 -139q-113 0 -155 14.5t-66 46.5q-98 -59 -196.5 -59t-152 56.5t-53.5 152.5q0 180 234 192l155 9v47q0 53 -94 53l-254 -14l-6 129q143 43 276.5 43t201 -48.5t67.5 -173.5zM362 963q-74 -6 -73.5 -69t67.5 -63q51 0 111 25l18 8v107z " />
<glyph unicode="&#xab;" horiz-adv-x="1175" d="M514 676l-238 -170l238 -193v-227l-428 332v166l428 317v-225zM1053 676l-238 -170l238 -193v-227l-428 332v166l428 317v-225z" />
<glyph unicode="&#xac;" d="M125 719h885v-494h-201v295h-684v199z" />
<glyph unicode="&#xad;" horiz-adv-x="862" d="M125 451v202h612v-202h-612z" />
<glyph unicode="&#xae;" horiz-adv-x="1318" d="M264 482q-158 165 -158 409t157 406.5t399.5 162.5t396 -166t153.5 -408.5t-153.5 -405.5t-395 -163t-399.5 165zM217 888.5q0 -192.5 128 -327.5t316.5 -135t314.5 134t126 327.5t-128 330t-315.5 136.5t-314.5 -136.5t-127 -329zM586 774v-203h-164v633h256 q233 0 233 -205q0 -78 -19.5 -121.5t-70.5 -76.5l99 -230h-172l-76 203h-86zM584 1079v-182h84q86 0 86 91t-97 91h-73z" />
<glyph unicode="&#xaf;" horiz-adv-x="520" d="M-18 1247v168h577v-168h-577z" />
<glyph unicode="&#xb0;" d="M274 1126.5q0 133.5 83 216t216.5 82.5t216 -82.5t82.5 -216t-82.5 -215.5t-216 -82t-216.5 82t-83 215.5zM406 1126.5q0 -75.5 46 -123t121.5 -47.5t123.5 47.5t48 123t-48 123.5t-123.5 48t-121.5 -48t-46 -123.5z" />
<glyph unicode="&#xb1;" d="M106 573v201h365v236h201v-236h368v-201h-368v-229h-201v229h-365zM106 256h934v-201h-934v201z" />
<glyph unicode="&#xb2;" horiz-adv-x="573" d="M504 979h-453v154l162 145q98 84 98 139q0 51 -86 51l-162 -14l-8 164q137 20 241.5 20t154 -46t49.5 -132t-27 -134t-92 -99l-100 -86h223v-162z" />
<glyph unicode="&#xb3;" horiz-adv-x="573" d="M274 1638q233 0 234 -176q0 -109 -70 -147q84 -33 84 -154q0 -203 -231 -203l-242 17l12 158q121 -14 198 -14.5t77 57.5q0 53 -72 53h-135v147h133q25 0 41 14.5t16 39.5q0 49 -71 49l-178 -13l-13 154q133 18 217 18z" />
<glyph unicode="&#xb4;" horiz-adv-x="548" d="M29 1325l483 207l70 -193l-500 -159z" />
<glyph unicode="&#xb5;" d="M778 1024h221v-1024h-221v63q-150 -86 -276 -86q-80 0 -131 15v-432h-224v1464h224v-586q2 -152 34.5 -207t139.5 -55q111 0 203 41l30 12v795z" />
<glyph unicode="&#xb6;" horiz-adv-x="1220" d="M825 0v1212h-174v-1212h-192v621h-15q-168 0 -274.5 108.5t-106.5 280.5t108 282.5t278 110.5h675v-191h-106v-1212h-193z" />
<glyph unicode="&#xb7;" horiz-adv-x="489" d="M125 397v295h240v-295h-240z" />
<glyph unicode="&#xb8;" horiz-adv-x="540" d="M411.5 -123q57.5 -35 57.5 -139.5t-56.5 -157.5t-156.5 -53q-78 0 -158 14l-26 6l8 134q70 -4 113 -5q84 0 83 64q0 31 -19 43t-64 12h-64v207h96v-88q129 -2 186.5 -37z" />
<glyph unicode="&#xb9;" horiz-adv-x="573" d="M418 1618v-639h-180v440l-115 -78l-84 127l215 150h164z" />
<glyph unicode="&#xba;" horiz-adv-x="825" d="M412.5 1350q318.5 0 318.5 -334t-318.5 -334t-318.5 334t318.5 334zM413.5 858q65.5 0 90 37t24.5 123t-23.5 122t-90 36t-94 -37t-27.5 -121t27.5 -122t93 -38z" />
<glyph unicode="&#xbb;" horiz-adv-x="1177" d="M899 506l-237 170v225l428 -317v-166l-428 -332v227zM360 506l-237 170v225l428 -317v-166l-428 -332v227z" />
<glyph unicode="&#xbc;" horiz-adv-x="1110" d="M432 1618v-639h-180v440l-115 -78l-84 127l215 150h164zM66 59l804 1293l97 -62l-805 -1296zM840 -205v84h-279v150l107 405h200l-125 -393h97l20 182h158v-182h37v-162h-37v-84h-178z" />
<glyph unicode="&#xbd;" horiz-adv-x="1097" d="M436 1618v-639h-180v440l-115 -78l-84 127l215 150h164zM63 59l805 1293l97 -62l-805 -1296zM1026 -205h-453v154l162 145q98 84 99 139q0 51 -86 52l-162 -15l-8 164q137 20 241.5 20.5t153.5 -46t49 -132.5t-26.5 -134t-92.5 -99l-100 -86h223v-162z" />
<glyph unicode="&#xbe;" horiz-adv-x="1138" d="M309 1638q233 0 234 -176q0 -109 -70 -147q84 -33 84 -154q0 -203 -231 -203l-242 17l12 158q121 -14 198 -14.5t77 57.5q0 53 -72 53h-135v147h133q25 0 41 14.5t16 39.5q0 49 -71 49l-179 -13l-12 154q133 18 217 18zM96 59l805 1293l96 -62l-804 -1296zM872 -205v84 h-278v150l106 405h201l-125 -393h96l21 182h158v-182h36v-162h-36v-84h-179z" />
<glyph unicode="&#xbf;" horiz-adv-x="894" d="M66 -70q0 137 33.5 209t138 154t138.5 130t34 103v68h180q49 -63 49 -160q0 -51 -39 -102t-139.5 -134t-133 -129t-32.5 -122q0 -150 217 -150q123 0 258 29l47 10l12 -170q-190 -68 -346 -67q-215 0 -316 78.5t-101 252.5zM625 1024v-287h-238v287h238z" />
<glyph unicode="&#xc0;" horiz-adv-x="1226" d="M41 0l346 1403h453l348 -1403h-230l-75 301h-539l-76 -301h-227zM563 1212l-174 -710h449l-172 710h-103zM381 1923l483 -211l-57 -156l-498 168z" />
<glyph unicode="&#xc1;" horiz-adv-x="1226" d="M41 0l346 1403h453l348 -1403h-230l-75 301h-539l-76 -301h-227zM563 1212l-174 -710h449l-172 710h-103zM352 1712l484 211l71 -199l-497 -168z" />
<glyph unicode="&#xc2;" horiz-adv-x="1226" d="M41 0l346 1403h453l348 -1403h-230l-75 301h-539l-76 -301h-227zM563 1212l-174 -710h449l-172 710h-103zM240 1589l274 293h193l274 -293h-238l-131 137l-133 -137h-239z" />
<glyph unicode="&#xc3;" horiz-adv-x="1226" d="M41 0l346 1403h453l348 -1403h-230l-75 301h-539l-76 -301h-227zM563 1212l-174 -710h449l-172 710h-103zM770 1591q-49 0 -178 51.5t-156 51.5q-51 0 -131 -68l-26 -22l-50 159q12 14 32 36t74 56.5t107.5 34.5t179.5 -51t148 -51q45 0 127 67l29 23l49 -162 q-31 -43 -93.5 -84t-111.5 -41z" />
<glyph unicode="&#xc4;" horiz-adv-x="1226" d="M41 0l346 1403h453l348 -1403h-230l-75 301h-539l-76 -301h-227zM563 1212l-174 -710h449l-172 710h-103zM291 1604v233h213v-233h-213zM719 1604v233h213v-233h-213z" />
<glyph unicode="&#xc5;" horiz-adv-x="1226" d="M893 1497q0 -78 -43 -137l338 -1360h-230l-75 301h-539l-76 -301h-227l336 1360q-43 59 -43 137q0 111 80 175.5t199.5 64.5t199.5 -64.5t80 -175.5zM563 1212l-174 -710h449l-172 710h-103zM489 1497q0 -45 33 -69.5t90.5 -24.5t91 24.5t33.5 69.5t-33.5 70.5t-91 25.5 t-90.5 -25.5t-33 -70.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="1787" d="M817 0v291h-469l-86 -291h-231l413 1419h1254v-219h-656v-371h533v-215h-533v-395h656v-219h-881zM606 1200l-200 -690h411l2 690h-213z" />
<glyph unicode="&#xc7;" horiz-adv-x="1114" d="M837.5 -123q57.5 -35 57.5 -139.5t-56.5 -157.5t-156.5 -53q-78 0 -158 14l-26 6l8 134q70 -4 112 -5q84 0 84 64q0 31 -19 43t-65 12h-63v187q-258 18 -353.5 189t-95.5 548t111 541.5t416 164.5q182 0 401 -51l-8 -184q-184 33 -368.5 33t-250 -109.5t-65.5 -412 t63.5 -412t248 -109.5t372.5 31l6 -188q-188 -41 -381 -46v-63q129 -2 186.5 -37z" />
<glyph unicode="&#xc8;" horiz-adv-x="1132" d="M162 0v1403h885v-199h-658v-397h535v-197h-535v-409h658v-201h-885zM383 1923l483 -211l-57 -156l-498 168z" />
<glyph unicode="&#xc9;" horiz-adv-x="1132" d="M162 0v1403h885v-199h-658v-397h535v-197h-535v-409h658v-201h-885zM336 1712l483 211l72 -199l-498 -168z" />
<glyph unicode="&#xca;" horiz-adv-x="1132" d="M162 0v1403h885v-199h-658v-397h535v-197h-535v-409h658v-201h-885zM236 1589l274 293h192l275 -293h-238l-131 137l-133 -137h-239z" />
<glyph unicode="&#xcb;" horiz-adv-x="1132" d="M162 0v1403h885v-199h-658v-397h535v-197h-535v-409h658v-201h-885zM283 1604v233h213v-233h-213zM711 1604v233h213v-233h-213z" />
<glyph unicode="&#xcc;" horiz-adv-x="550" d="M162 0v1403h227v-1403h-227zM25 1923l483 -211l-57 -156l-498 168z" />
<glyph unicode="&#xcd;" horiz-adv-x="550" d="M162 0v1403h227v-1403h-227zM20 1712l484 211l71 -199l-497 -168z" />
<glyph unicode="&#xce;" horiz-adv-x="550" d="M162 0v1403h227v-1403h-227zM-100 1589l274 293h193l274 -293h-238l-131 137l-133 -137h-239z" />
<glyph unicode="&#xcf;" horiz-adv-x="550" d="M162 0v1403h227v-1403h-227zM-45 1604v233h213v-233h-213zM383 1604v233h213v-233h-213z" />
<glyph unicode="&#xd0;" horiz-adv-x="1318" d="M47 598v217h123v604h481q332 0 453 -178q61 -92 85.5 -212t24.5 -297t-23.5 -303t-82.5 -228q-119 -201 -457 -201h-481v598h-123zM979 735q0 244 -64.5 355.5t-263.5 111.5h-256v-387h291v-217h-291v-381h256q205 0 271 150q35 76 46 159.5t11 208.5z" />
<glyph unicode="&#xd1;" horiz-adv-x="1404" d="M162 0v1403h397l426 -1202h31v1202h227v-1403h-389l-436 1204h-29v-1204h-227zM862 1591q-49 0 -178 51.5t-156 51.5q-51 0 -131 -68l-26 -22l-49 159q12 14 31.5 36t73.5 56.5t107.5 34.5t179.5 -51t148 -51q45 0 129 67l27 23l49 -162q-31 -43 -93.5 -84t-111.5 -41z " />
<glyph unicode="&#xd2;" horiz-adv-x="1343" d="M671.5 176q196.5 0 265.5 119t69 398.5t-71 406.5t-263.5 127t-264 -127t-71.5 -404.5t69.5 -398.5t266 -121zM671.5 -23q-317.5 0 -444.5 173.5t-127 544t128 550.5t443.5 180t442.5 -179t127 -551.5t-126 -545t-443.5 -172.5zM438 1923l484 -211l-58 -156l-497 168z " />
<glyph unicode="&#xd3;" horiz-adv-x="1343" d="M671.5 176q196.5 0 265.5 119t69 398.5t-71 406.5t-263.5 127t-264 -127t-71.5 -404.5t69.5 -398.5t266 -121zM671.5 -23q-317.5 0 -444.5 173.5t-127 544t128 550.5t443.5 180t442.5 -179t127 -551.5t-126 -545t-443.5 -172.5zM371 1712l483 211l72 -199l-498 -168z" />
<glyph unicode="&#xd4;" horiz-adv-x="1343" d="M671.5 176q196.5 0 265.5 119t69 398.5t-71 406.5t-263.5 127t-264 -127t-71.5 -404.5t69.5 -398.5t266 -121zM671.5 -23q-317.5 0 -444.5 173.5t-127 544t128 550.5t443.5 180t442.5 -179t127 -551.5t-126 -545t-443.5 -172.5zM303 1589l275 293h192l274 -293h-237 l-131 137l-133 -137h-240z" />
<glyph unicode="&#xd5;" horiz-adv-x="1343" d="M671.5 176q196.5 0 265.5 119t69 398.5t-71 406.5t-263.5 127t-264 -127t-71.5 -404.5t69.5 -398.5t266 -121zM671.5 -23q-317.5 0 -444.5 173.5t-127 544t128 550.5t443.5 180t442.5 -179t127 -551.5t-126 -545t-443.5 -172.5zM835.5 1591q-49.5 0 -178.5 51.5 t-155 51.5q-51 0 -131 -68l-27 -22l-49 159q12 14 31.5 36t74 56.5t107.5 34.5t179 -51t149 -51q45 0 127 67l28 23l49 -162q-31 -43 -93 -84t-111.5 -41z" />
<glyph unicode="&#xd6;" horiz-adv-x="1343" d="M671.5 176q196.5 0 265.5 119t69 398.5t-71 406.5t-263.5 127t-264 -127t-71.5 -404.5t69.5 -398.5t266 -121zM671.5 -23q-317.5 0 -444.5 173.5t-127 544t128 550.5t443.5 180t442.5 -179t127 -551.5t-126 -545t-443.5 -172.5zM352 1604v233h213v-233h-213zM780 1604 v233h213v-233h-213z" />
<glyph unicode="&#xd7;" d="M270 956l303 -307l306 309l139 -141l-309 -305l309 -303l-141 -141l-304 309l-303 -309l-143 141l309 303l-307 303z" />
<glyph unicode="&#xd8;" horiz-adv-x="1343" d="M672 -23q-123 0 -219 25l-117 -252l-164 80l119 254q-190 154 -191 610q0 371 128 551t444 180q125 0 229 -32l119 256l168 -72l-127 -272q180 -164 180 -611q0 -373 -126 -545t-443 -172zM407.5 1100q-71.5 -127 -71.5 -401.5t61 -387.5l416 893q-61 23 -141 23 q-193 0 -264.5 -127zM672 176q197 0 265.5 119t68.5 386t-56 388l-407 -876q59 -17 129 -17z" />
<glyph unicode="&#xd9;" horiz-adv-x="1331" d="M379 449q0 -272 286.5 -272.5t286.5 272.5v954h230v-950q0 -248 -130 -362t-387 -114t-386 114t-129 362v950h229v-954zM449 1923l483 -211l-58 -156l-497 168z" />
<glyph unicode="&#xda;" horiz-adv-x="1331" d="M379 449q0 -272 286.5 -272.5t286.5 272.5v954h230v-950q0 -248 -130 -362t-387 -114t-386 114t-129 362v950h229v-954zM379 1712l483 211l72 -199l-498 -168z" />
<glyph unicode="&#xdb;" horiz-adv-x="1331" d="M379 449q0 -272 286.5 -272.5t286.5 272.5v954h230v-950q0 -248 -130 -362t-387 -114t-386 114t-129 362v950h229v-954zM295 1589l274 293h193l274 -293h-237l-131 137l-133 -137h-240z" />
<glyph unicode="&#xdc;" horiz-adv-x="1331" d="M379 449q0 -272 286.5 -272.5t286.5 272.5v954h230v-950q0 -248 -130 -362t-387 -114t-386 114t-129 362v950h229v-954zM346 1604v233h213v-233h-213zM774 1604v233h213v-233h-213z" />
<glyph unicode="&#xdd;" horiz-adv-x="1124" d="M678 0h-229v578l-441 825h256l297 -602l299 602h254l-436 -825v-578zM303 1712l483 211l72 -199l-498 -168z" />
<glyph unicode="&#xde;" horiz-adv-x="1222" d="M674 442q125 0 187.5 76t62.5 210t-61.5 196.5t-188.5 62.5h-285v-545h285zM676 221h-287v-221h-227v1419h227v-215h287q479 0 479 -473q0 -244 -121.5 -377t-357.5 -133z" />
<glyph unicode="&#xdf;" horiz-adv-x="1218" d="M358 0h-223v1090q0 209 102.5 296t334 87t338 -73t106.5 -239q0 -109 -42 -163t-128 -93t-108.5 -57.5t-22.5 -47t33.5 -54t167 -89t189.5 -127t56 -182.5q0 -207 -94 -289t-319 -82q-117 0 -248 29l-47 10l8 181q188 -23 284 -23q186 0 187 141q0 61 -36 94t-167 95 q-137 59 -190.5 119.5t-53.5 153.5t37 144.5t122 87t113.5 64.5t28.5 86.5t-45 87t-166.5 29.5t-169 -43t-47.5 -154v-1079z" />
<glyph unicode="&#xe0;" horiz-adv-x="1038" d="M893 709v-467q2 -45 23.5 -66.5t66.5 -28.5l-6 -170q-176 0 -272 76q-164 -76 -330 -76q-305 0 -305 326q0 156 83 225.5t255 83.5l264 23v74q0 82 -36 114.5t-106 32.5q-131 0 -327 -16l-66 -4l-8 157q223 53 410.5 53.5t270.5 -80.5t83 -257zM434 455 q-141 -12 -141 -153.5t125 -141.5q102 0 217 33l37 12v272zM297 1532l483 -207l-53 -145l-500 159z" />
<glyph unicode="&#xe1;" horiz-adv-x="1038" d="M893 709v-467q2 -45 23.5 -66.5t66.5 -28.5l-6 -170q-176 0 -272 76q-164 -76 -330 -76q-305 0 -305 326q0 156 83 225.5t255 83.5l264 23v74q0 82 -36 114.5t-106 32.5q-131 0 -327 -16l-66 -4l-8 157q223 53 410.5 53.5t270.5 -80.5t83 -257zM434 455 q-141 -12 -141 -153.5t125 -141.5q102 0 217 33l37 12v272zM215 1325l483 207l70 -193l-500 -159z" />
<glyph unicode="&#xe2;" horiz-adv-x="1038" d="M893 709v-467q2 -45 23.5 -66.5t66.5 -28.5l-6 -170q-176 0 -272 76q-164 -76 -330 -76q-305 0 -305 326q0 156 83 225.5t255 83.5l264 23v74q0 82 -36 114.5t-106 32.5q-131 0 -327 -16l-66 -4l-8 157q223 53 410.5 53.5t270.5 -80.5t83 -257zM434 455 q-141 -12 -141 -153.5t125 -141.5q102 0 217 33l37 12v272zM180 1198l256 299h142l260 -299h-209l-119 156l-119 -156h-211z" />
<glyph unicode="&#xe3;" horiz-adv-x="1038" d="M893 709v-467q2 -45 23.5 -66.5t66.5 -28.5l-6 -170q-176 0 -272 76q-164 -76 -330 -76q-305 0 -305 326q0 156 83 225.5t255 83.5l264 23v74q0 82 -36 114.5t-106 32.5q-131 0 -327 -16l-66 -4l-8 157q223 53 410.5 53.5t270.5 -80.5t83 -257zM434 455 q-141 -12 -141 -153.5t125 -141.5q102 0 217 33l37 12v272zM701.5 1255.5q-52.5 -32.5 -95.5 -32.5t-142 41t-128 41q-49 0 -129 -52l-27 -18l-49 129l33 33q18 18 71.5 50t97.5 32t143 -41t124 -41q45 0 129 55l27 18l49 -131l-31 -32q-20 -19 -72.5 -51.5z" />
<glyph unicode="&#xe4;" horiz-adv-x="1038" d="M893 709v-467q2 -45 23.5 -66.5t66.5 -28.5l-6 -170q-176 0 -272 76q-164 -76 -330 -76q-305 0 -305 326q0 156 83 225.5t255 83.5l264 23v74q0 82 -36 114.5t-106 32.5q-131 0 -327 -16l-66 -4l-8 157q223 53 410.5 53.5t270.5 -80.5t83 -257zM434 455 q-141 -12 -141 -153.5t125 -141.5q102 0 217 33l37 12v272zM170 1243v234h213v-234h-213zM598 1243v234h213v-234h-213z" />
<glyph unicode="&#xe5;" horiz-adv-x="1038" d="M893 709v-467q2 -45 23.5 -66.5t66.5 -28.5l-6 -170q-176 0 -272 76q-164 -76 -330 -76q-305 0 -305 326q0 156 83 225.5t255 83.5l264 23v74q0 82 -36 114.5t-106 32.5q-131 0 -327 -16l-66 -4l-8 157q223 53 410.5 53.5t270.5 -80.5t83 -257zM434 455 q-141 -12 -141 -153.5t125 -141.5q102 0 217 33l37 12v272zM370.5 1149.5q-65.5 64.5 -65.5 162t65.5 163t162 65.5t163 -66.5t66.5 -163t-66.5 -161t-163 -64.5t-162 64.5zM460.5 1385.5q-28.5 -29.5 -28.5 -73.5t28.5 -74t73 -30t73 30t28.5 74t-28.5 73.5t-73 29.5 t-73 -29.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1624" d="M1446 193l59 6l4 -174q-236 -47 -428 -47.5t-295 102.5l-55 -25q-176 -78 -369 -78q-143 0 -217.5 86.5t-74.5 245t87 226t275 81.5l240 19v65q0 66 -36 100t-101 34q-154 0 -326 -17l-66 -6l-8 197q260 37 432 36.5t252 -106.5q100 106 305 106q418 0 418 -466l-14 -162 h-635q2 -123 55 -179.5t199.5 -56t298.5 12.5zM403 168q92 0 184.5 23.5t98.5 27.5q-16 96 -16 244l-236 -17q-141 -10 -141.5 -144t110.5 -134zM893 590h430q0 139 -49 198.5t-163 59.5t-166 -61.5t-52 -196.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="905" d="M702.5 -123q57.5 -35 57.5 -139.5t-56.5 -157.5t-156.5 -53q-78 0 -158 14l-27 6l9 134q70 -4 112 -5q84 0 84 64q0 31 -19.5 43t-64.5 12h-63v189q-180 23 -255 148.5t-75 398t100.5 394.5t327.5 122q109 0 256 -29l51 -10l-8 -177q-162 16 -239 17q-156 0 -209.5 -69.5 t-53.5 -262t51.5 -266.5t213.5 -74l239 17l6 -179q-205 -37 -309 -37v-63q129 -2 186.5 -37z" />
<glyph unicode="&#xe8;" horiz-adv-x="1040" d="M864 184l58 6l4 -165q-233 -47 -414 -48q-227 0 -326.5 125t-99.5 400q0 545 444 545q430 0 431 -469l-15 -160h-635q2 -127 55.5 -186.5t198.5 -59.5t299 12zM741 590q0 152 -48 212t-162.5 60t-167 -63.5t-54.5 -208.5h432zM305 1532l483 -207l-53 -145l-499 159z" />
<glyph unicode="&#xe9;" horiz-adv-x="1040" d="M864 184l58 6l4 -165q-233 -47 -414 -48q-227 0 -326.5 125t-99.5 400q0 545 444 545q430 0 431 -469l-15 -160h-635q2 -127 55.5 -186.5t198.5 -59.5t299 12zM741 590q0 152 -48 212t-162.5 60t-167 -63.5t-54.5 -208.5h432zM250 1325l483 207l70 -193l-500 -159z" />
<glyph unicode="&#xea;" horiz-adv-x="1040" d="M864 184l58 6l4 -165q-233 -47 -414 -48q-227 0 -326.5 125t-99.5 400q0 545 444 545q430 0 431 -469l-15 -160h-635q2 -127 55.5 -186.5t198.5 -59.5t299 12zM741 590q0 152 -48 212t-162.5 60t-167 -63.5t-54.5 -208.5h432zM203 1198l256 299h141l260 -299h-209 l-119 156l-118 -156h-211z" />
<glyph unicode="&#xeb;" horiz-adv-x="1040" d="M864 184l58 6l4 -165q-233 -47 -414 -48q-227 0 -326.5 125t-99.5 400q0 545 444 545q430 0 431 -469l-15 -160h-635q2 -127 55.5 -186.5t198.5 -59.5t299 12zM741 590q0 152 -48 212t-162.5 60t-167 -63.5t-54.5 -208.5h432zM205 1243v234h213v-234h-213zM633 1243v234 h213v-234h-213z" />
<glyph unicode="&#xec;" horiz-adv-x="493" d="M135 0v1024h223v-1024h-223zM-51 1532l483 -207l-53 -145l-500 159z" />
<glyph unicode="&#xed;" horiz-adv-x="493" d="M70 1325l483 207l70 -193l-500 -159zM135 1024h223v-1024h-223v1024z" />
<glyph unicode="&#xee;" horiz-adv-x="493" d="M135 1024h223v-1024h-223v1024zM-90 1198l256 299h141l260 -299h-209l-118 156l-119 -156h-211z" />
<glyph unicode="&#xef;" horiz-adv-x="493" d="M135 1024h223v-1024h-223v1024zM-82 1243v234h213v-234h-213zM346 1243v234h213v-234h-213z" />
<glyph unicode="&#xf0;" horiz-adv-x="1144" d="M772 1296q281 -186 281 -576t-117 -564t-383 -174q-229 0 -350 115.5t-121 333.5t111.5 339t326.5 121q131 0 258 -47l41 -15q-10 113 -62 196t-167 149l-254 -170l-92 129l174 116q-90 31 -238 64l35 160q229 -31 395 -95l201 135l94 -129zM535 692q-106 0 -163 -71.5 t-57 -180.5q0 -258 238 -258q137 0 200.5 103.5t65.5 349.5q-137 57 -284 57z" />
<glyph unicode="&#xf1;" horiz-adv-x="1118" d="M135 0v1024h221v-63l41 22q41 20 112 42t130 22q205 0 279.5 -116t74.5 -382v-549h-221v543q0 166 -36 235.5t-150.5 69.5t-227.5 -53v-795h-223zM806 1255.5q-52 -32.5 -95 -32.5t-142.5 41t-126.5 41q-51 0 -131 -52l-26 -18l-49 129l30 33q20 18 73.5 50t97.5 32 t143.5 -41t124.5 -41q45 0 126 55l29 18l49 -131l-32 -32q-19 -19 -71 -51.5z" />
<glyph unicode="&#xf2;" horiz-adv-x="1085" d="M542.5 1047q245.5 0 351 -133.5t105.5 -399.5t-102 -401.5t-354 -135.5t-354.5 135.5t-102.5 401.5t105.5 399.5t351 133.5zM543 166q137 0 184 82t47 268t-50 264t-181 78t-181.5 -78t-50.5 -264t47.5 -268t184.5 -82zM338 1532l483 -207l-53 -145l-500 159z" />
<glyph unicode="&#xf3;" horiz-adv-x="1085" d="M542.5 1047q245.5 0 351 -133.5t105.5 -399.5t-102 -401.5t-354 -135.5t-354.5 135.5t-102.5 401.5t105.5 399.5t351 133.5zM543 166q137 0 184 82t47 268t-50 264t-181 78t-181.5 -78t-50.5 -264t47.5 -268t184.5 -82zM270 1325l484 207l69 -193l-499 -159z" />
<glyph unicode="&#xf4;" horiz-adv-x="1085" d="M542.5 1047q245.5 0 351 -133.5t105.5 -399.5t-102 -401.5t-354 -135.5t-354.5 135.5t-102.5 401.5t105.5 399.5t351 133.5zM543 166q137 0 184 82t47 268t-50 264t-181 78t-181.5 -78t-50.5 -264t47.5 -268t184.5 -82zM205 1198l256 299h141l260 -299h-209l-118 156 l-119 -156h-211z" />
<glyph unicode="&#xf5;" horiz-adv-x="1085" d="M542.5 1047q245.5 0 351 -133.5t105.5 -399.5t-102 -401.5t-354 -135.5t-354.5 135.5t-102.5 401.5t105.5 399.5t351 133.5zM543 166q137 0 184 82t47 268t-50 264t-181 78t-181.5 -78t-50.5 -264t47.5 -268t184.5 -82zM769 1255.5q-52 -32.5 -95 -32.5t-142.5 41 t-125.5 41q-51 0 -132 -52l-26 -18l-49 129l30 33q20 18 73.5 50t97.5 32t143.5 -41t124.5 -41q45 0 127 55l28 18l49 -131l-32 -32q-19 -19 -71 -51.5z" />
<glyph unicode="&#xf6;" horiz-adv-x="1085" d="M542.5 1047q245.5 0 351 -133.5t105.5 -399.5t-102 -401.5t-354 -135.5t-354.5 135.5t-102.5 401.5t105.5 399.5t351 133.5zM543 166q137 0 184 82t47 268t-50 264t-181 78t-181.5 -78t-50.5 -264t47.5 -268t184.5 -82zM219 1243v234h213v-234h-213zM647 1243v234h213 v-234h-213z" />
<glyph unicode="&#xf7;" d="M215 494v225h139v158h437v-158h141v-225h-717zM461 47v242h221v-242h-221z" />
<glyph unicode="&#xf8;" horiz-adv-x="1085" d="M543 1047q53 0 131 -13l84 207l139 -51l-86 -207q188 -113 188 -465q0 -270 -102 -405.5t-354 -135.5q-78 0 -140 13l-88 -215l-137 51l90 215q-182 117 -182 477q0 262 105.5 395.5t351.5 133.5zM543 166q137 0 184 82t47 270.5t-53 263.5l-248 -610q31 -6 70 -6z M543 858q-131 0 -181.5 -78t-50.5 -271.5t49 -264.5l244 608q-26 6 -61 6z" />
<glyph unicode="&#xf9;" horiz-adv-x="1112" d="M756 1024h221v-1024h-221v63q-150 -86 -277 -86q-211 0 -282.5 114t-71.5 398v535h223v-537q0 -184 31 -247.5t143 -63.5q111 0 203 41l31 12v795zM285 1532l483 -207l-53 -145l-500 159z" />
<glyph unicode="&#xfa;" horiz-adv-x="1112" d="M977 1024v-1024h-221v63l-41 -22q-41 -20 -109.5 -42t-126.5 -22q-211 0 -282.5 114t-71.5 398v535h223v-537q0 -184 31 -247.5t148.5 -63.5t228.5 53v795h221zM289 1325l483 207l70 -193l-500 -159z" />
<glyph unicode="&#xfb;" horiz-adv-x="1112" d="M977 1024v-1024h-221v63l-41 -22q-41 -20 -109.5 -42t-126.5 -22q-211 0 -282.5 114t-71.5 398v535h223v-537q0 -184 31 -247.5t148.5 -63.5t228.5 53v795h221zM188 1198l256 299h142l260 -299h-209l-119 156l-119 -156h-211z" />
<glyph unicode="&#xfc;" horiz-adv-x="1112" d="M977 1024v-1024h-221v63l-41 -22q-41 -20 -109.5 -42t-126.5 -22q-211 0 -282.5 114t-71.5 398v535h223v-537q0 -184 31 -247.5t148.5 -63.5t228.5 53v795h221zM240 1243v234h213v-234h-213zM668 1243v234h213v-234h-213z" />
<glyph unicode="&#xfd;" horiz-adv-x="1007" d="M41 1024h219l217 -834h55l218 834h221l-389 -1464h-220l123 440h-176zM279 1325l483 207l69 -193l-499 -159z" />
<glyph unicode="&#xfe;" horiz-adv-x="1097" d="M528 170q152 0 204 80t52 263t-47 259t-151 76q-96 0 -195 -31l-33 -10v-625q109 -12 170 -12zM612 1047q213 0 305.5 -118t92.5 -416t-105.5 -417t-359.5 -119l-187 15v-432h-223v1890h223v-465q141 62 254 62z" />
<glyph unicode="&#xff;" horiz-adv-x="1007" d="M41 1024h219l217 -834h55l218 834h221l-389 -1464h-220l123 440h-176zM184 1243v234h213v-234h-213zM612 1243v234h213v-234h-213z" />
<glyph unicode="&#x104;" horiz-adv-x="1226" d="M1032 -187.5q0 -31.5 20.5 -53t53.5 -21.5l92 10l21 -162q-109 -20 -194 -20t-143.5 51t-58.5 139q0 123 140 244h-5l-75 301h-539l-76 -301h-227l346 1403h453l348 -1403h-29q-45 -31 -86 -93.5t-41 -94zM563 1212l-174 -710h449l-172 710h-103z" />
<glyph unicode="&#x105;" horiz-adv-x="1036" d="M893 709v-467q2 -45 23.5 -66.5t66.5 -28.5l-6 -170q-51 0 -74 3q-106 -106 -106 -164q0 -35 20.5 -56.5t52.5 -21.5l93 10l20 -162q-109 -20 -193.5 -20t-143 51t-58.5 128t42 142.5t83 100.5l41 32q-27 14 -49 33q-164 -76 -330 -76q-305 0 -305 326q0 156 83 225.5 t255 83.5l264 23v74q0 82 -36 114.5t-106 32.5q-131 0 -327 -16l-66 -4l-8 157q223 53 410.5 53.5t270.5 -80.5t83 -257zM434 455q-141 -12 -141 -153.5t125 -141.5q102 0 217 33l37 12v272z" />
<glyph unicode="&#x106;" horiz-adv-x="1114" d="M633 -23q-307 0 -417 170t-110 559.5t111 554t416 164.5q182 0 401 -51l-8 -184q-184 33 -368.5 33t-250 -109.5t-65.5 -412t63.5 -412t248 -109.5t372.5 31l6 -188q-211 -46 -399 -46zM332 1712l483 211l72 -199l-498 -168z" />
<glyph unicode="&#x107;" horiz-adv-x="905" d="M518 1047q109 0 256 -29l51 -10l-8 -177q-162 16 -239 17q-156 0 -209.5 -69.5t-53.5 -262t51.5 -266.5t213.5 -74l239 17l6 -179q-207 -37 -311 -37q-233 0 -328.5 126t-95.5 413t100.5 409t327.5 122zM225 1325l484 207l69 -193l-499 -159z" />
<glyph unicode="&#x118;" horiz-adv-x="1132" d="M162 0v1403h885v-199h-658v-397h535v-197h-535v-409h658v-201h-60q-45 -31 -86 -93.5t-41 -94t20.5 -53t53.5 -21.5l92 10l21 -162q-109 -20 -194 -20t-143.5 51t-58.5 139q0 123 140 244h-629z" />
<glyph unicode="&#x119;" horiz-adv-x="1042" d="M709 -184q0 -35 20.5 -56.5t52.5 -21.5l92 10l21 -162q-109 -20 -194 -20t-143 51t-58 150.5t121 216.5q-59 -6 -109 -7q-227 0 -326.5 125t-99.5 400q0 545 444 545q430 0 431 -469l-15 -160h-635q2 -127 55.5 -186.5t178.5 -59.5q166 0 319 12l58 6l4 -165 q-49 -10 -113 -21l27 -2q-131 -116 -131 -186zM741 590q0 152 -48 212t-162.5 60t-167 -63.5t-54.5 -208.5h432z" />
<glyph unicode="&#x131;" horiz-adv-x="493" d="M135 0v1024h223v-1024h-223z" />
<glyph unicode="&#x141;" horiz-adv-x="987" d="M963 0h-787v492l-98 -68l-109 151l207 144v684h227v-524l242 170l109 -150l-351 -248v-448h560v-203z" />
<glyph unicode="&#x142;" horiz-adv-x="716" d="M240 0v500l-119 -82l-109 151l228 158v723h221v-567l139 98l109 -152l-248 -174v-655h-221z" />
<glyph unicode="&#x143;" horiz-adv-x="1404" d="M162 0v1403h397l426 -1202h31v1202h227v-1403h-389l-436 1204h-29v-1204h-227zM430 1712l483 211l72 -199l-498 -168z" />
<glyph unicode="&#x144;" horiz-adv-x="1118" d="M135 0v1024h221v-63l41 22q41 20 112 42t130 22q205 0 279.5 -116t74.5 -382v-549h-221v543q0 166 -36 235.5t-150.5 69.5t-227.5 -53v-795h-223zM311 1325l484 207l69 -193l-499 -159z" />
<glyph unicode="&#x152;" horiz-adv-x="1867" d="M1778 0h-873q-147 -23 -286.5 -23t-245 44.5t-163.5 140.5t-82 224t-24 321q0 395 119 565t418 170q121 0 260 -23h877v-219h-652v-371h529v-217h-529v-393h652v-219zM678 201q61 0 223 14v987q-168 14 -229 15q-193 0 -262.5 -109t-69.5 -399.5t66.5 -399t271.5 -108.5z " />
<glyph unicode="&#x153;" horiz-adv-x="1728" d="M1552 193l58 6l4 -174q-233 -47 -414 -48q-231 0 -321 160q-92 -160 -339 -160t-350.5 135.5t-103.5 401.5t105.5 398t355.5 132t340 -186q96 186 321.5 186t333 -114.5t107.5 -351.5l-17 -162h-635q2 -123 55.5 -179.5t199.5 -56t300 12.5zM359.5 257 q48.5 -79 184.5 -79t183 79t47 255t-52 255t-180 79t-179.5 -76t-51.5 -255t48.5 -258zM997 590h430q0 141 -48 199.5t-161.5 58.5t-167 -62.5t-53.5 -195.5z" />
<glyph unicode="&#x15a;" horiz-adv-x="1112" d="M573 1227q-260 0 -260 -183q0 -94 61.5 -134t271.5 -98t296 -139t86 -255q0 -221 -126 -331t-343 -110q-174 0 -385 41l-74 15l23 182q276 -37 422 -37q254 0 254 225q0 88 -57.5 130t-258 92.5t-299 138.5t-98.5 278.5t124 287.5t343 97q158 0 373 -36l71 -13l-18 -184 q-287 33 -406 33zM313 1712l484 211l71 -199l-497 -168z" />
<glyph unicode="&#x15b;" horiz-adv-x="960" d="M846 813q-242 33 -350.5 33t-150.5 -25.5t-42 -81t46 -78t217 -52t243 -93t72 -225.5t-103.5 -238t-302.5 -76q-125 0 -315 35l-64 11l8 186q246 -33 354.5 -33t154.5 26.5t46 88t-44 85t-210.5 51.5t-244.5 87t-78 217t107.5 235.5t275.5 77.5q131 0 321 -32l64 -13z M207 1325l483 207l70 -193l-500 -159z" />
<glyph unicode="&#x178;" horiz-adv-x="1124" d="M678 0h-229v578l-441 825h256l297 -602l299 602h254l-436 -825v-578zM238 1604v233h213v-233h-213zM666 1604v233h213v-233h-213z" />
<glyph unicode="&#x179;" horiz-adv-x="1093" d="M86 1204v199h922v-219l-652 -946v-39h652v-199h-922v217l649 946v41h-649zM276 1712l484 211l71 -199l-497 -168z" />
<glyph unicode="&#x17a;" horiz-adv-x="929" d="M84 1024h760v-199l-492 -626h492v-199h-760v199l494 626h-494v199zM195 1325l483 207l70 -193l-500 -159z" />
<glyph unicode="&#x17b;" horiz-adv-x="1093" d="M86 1204v199h922v-219l-652 -946v-39h652v-199h-922v217l649 946v41h-649zM436 1602v233h221v-233h-221z" />
<glyph unicode="&#x17c;" horiz-adv-x="929" d="M84 1024h760v-199l-492 -626h492v-199h-760v199l494 626h-494v199zM352 1186v233h221v-233h-221z" />
<glyph unicode="&#x192;" d="M623 -102q0 -209 -76 -291t-248 -82q-74 0 -180 16l-33 6v185q129 -8 188 -8q125 0 125 178v870h-118v191h118v75q0 209 61.5 304.5t227.5 95.5q53 0 180 -17l39 -6v-182q-92 4 -160.5 4t-96 -42t-27.5 -140v-92h268v-191h-268v-874z" />
<glyph unicode="&#x2c6;" horiz-adv-x="520" d="M-45 1198l256 299h141l260 -299h-209l-118 156l-119 -156h-211z" />
<glyph unicode="&#x2c7;" horiz-adv-x="520" d="M217 1198l-256 299h209l121 -154l119 154h208l-260 -299h-141z" />
<glyph unicode="&#x2d8;" horiz-adv-x="520" d="M179 1380q40 -41 106.5 -41t105.5 42t43 110h172q-12 -129 -97 -217t-224.5 -88t-225.5 88t-98 217h172q6 -70 46 -111z" />
<glyph unicode="&#x2d9;" horiz-adv-x="520" d="M150 1186v233h221v-233h-221z" />
<glyph unicode="&#x2da;" horiz-adv-x="520" d="M104.5 1149.5q-65.5 64.5 -65.5 162t65.5 163t161.5 65.5t163 -66.5t67 -163t-67 -161t-163 -64.5t-161.5 64.5zM194.5 1385.5q-28.5 -29.5 -28.5 -73.5t28.5 -74t72.5 -30t73 30t29 74t-29 73.5t-73 29.5t-72.5 -29.5z" />
<glyph unicode="&#x2db;" horiz-adv-x="950" d="M631 -184q0 -35 20.5 -56.5t53.5 -21.5l92 10l20 -162q-109 -20 -193.5 -20t-143 51t-58.5 128t41 142.5t82 100.5l43 32l174 -18q-131 -116 -131 -186z" />
<glyph unicode="&#x2dc;" horiz-adv-x="520" d="M519 1255.5q-52 -32.5 -95 -32.5t-142.5 41t-125.5 41q-51 0 -131 -52l-27 -18l-49 129l31 33q20 18 73 50t97.5 32t143.5 -41t124 -41q45 0 127 55l28 18l50 -131l-33 -32q-19 -19 -71 -51.5z" />
<glyph unicode="&#x2dd;" horiz-adv-x="520" d="M305 1282l264 344l174 -117l-276 -340zM-102 1282l264 342l174 -115l-277 -340z" />
<glyph unicode="&#x3c0;" d="M690 315v519h-237l-56 -834h-223l64 834q-72 0 -168 -21l-29 -6v182q127 35 299 35h500q125 0 213 20l30 7v-185q-53 -23 -172 -28v-523q0 -80 32 -110.5t126 -30.5v-188q-127 0 -197.5 12t-113.5 52t-55.5 99.5t-12.5 165.5z" />
<glyph unicode="&#x2000;" horiz-adv-x="961" />
<glyph unicode="&#x2001;" horiz-adv-x="1923" />
<glyph unicode="&#x2002;" horiz-adv-x="961" />
<glyph unicode="&#x2003;" horiz-adv-x="1923" />
<glyph unicode="&#x2004;" horiz-adv-x="641" />
<glyph unicode="&#x2005;" horiz-adv-x="480" />
<glyph unicode="&#x2006;" horiz-adv-x="320" />
<glyph unicode="&#x2007;" horiz-adv-x="320" />
<glyph unicode="&#x2008;" horiz-adv-x="240" />
<glyph unicode="&#x2009;" horiz-adv-x="384" />
<glyph unicode="&#x200a;" horiz-adv-x="106" />
<glyph unicode="&#x2010;" horiz-adv-x="862" d="M125 451v202h612v-202h-612z" />
<glyph unicode="&#x2011;" horiz-adv-x="862" d="M125 451v202h612v-202h-612z" />
<glyph unicode="&#x2012;" horiz-adv-x="862" d="M125 451v202h612v-202h-612z" />
<glyph unicode="&#x2013;" horiz-adv-x="1273" d="M125 641h1024v-190h-1024v190z" />
<glyph unicode="&#x2014;" horiz-adv-x="2297" d="M125 641h2048v-190h-2048v190z" />
<glyph unicode="&#x2018;" horiz-adv-x="495" d="M412 1415l-93 -481h-233l156 481h170z" />
<glyph unicode="&#x2019;" horiz-adv-x="487" d="M96 936l92 481h234l-156 -481h-170z" />
<glyph unicode="&#x201a;" horiz-adv-x="489" d="M117 -117l45 232h110l-73 -232h-82z" />
<glyph unicode="&#x201c;" horiz-adv-x="845" d="M762 1415l-92 -481h-234l156 481h170zM412 1415l-93 -481h-233l156 481h170z" />
<glyph unicode="&#x201d;" horiz-adv-x="843" d="M96 938l92 481h234l-156 -481h-170zM451 938l92 481h233l-155 -481h-170z" />
<glyph unicode="&#x201e;" horiz-adv-x="806" d="M338 236l-92 -482h-234l156 482h170zM694 236l-92 -482h-233l155 482h170z" />
<glyph unicode="&#x2020;" horiz-adv-x="980" d="M63 834v190h316v379h223v-379h318v-190h-318l-16 -984h-191l-16 984h-316z" />
<glyph unicode="&#x2021;" horiz-adv-x="1056" d="M418 -150v379h-318v191h318v414h-318v190h318v379h221v-379h315v-190h-315v-414h317v-191h-317v-379h-221z" />
<glyph unicode="&#x2022;" horiz-adv-x="942" d="M215 211v577h512v-577h-512z" />
<glyph unicode="&#x2026;" horiz-adv-x="1531" d="M125 0v295h240v-295h-240zM647 0v295h240v-295h-240zM1167 0v295h240v-295h-240z" />
<glyph unicode="&#x202f;" horiz-adv-x="384" />
<glyph unicode="&#x2030;" horiz-adv-x="1722" d="M250 1201q-14 -36 -14 -118t14 -118.5t54 -36.5t54.5 35.5t14.5 119.5t-14.5 119t-54.5 35t-54 -36zM309 -12l455 1427l127 -43l-455 -1425zM68 1083q0 291 236.5 291t236.5 -291q0 -147 -61.5 -221t-175.5 -74t-175 74t-61 221zM895 -23q-236 0 -236 293t237 293 t237 -291q0 -147 -61.5 -221t-176.5 -74zM840.5 388q-15.5 -36 -15.5 -117t15.5 -118.5t54.5 -37.5t54.5 37.5t15.5 118.5t-15.5 117t-54.5 36t-54.5 -36zM1186 272q0 291 236.5 291t236.5 -291q0 -147 -61.5 -221t-175 -74t-175 74t-61.5 221zM1367 388q-15 -36 -15 -117 t15 -118.5t54 -37.5t54.5 36.5t15.5 119.5t-14.5 118t-54.5 35t-55 -36z" />
<glyph unicode="&#x2039;" horiz-adv-x="636" d="M514 676l-238 -170l238 -193v-227l-428 332v166l428 317v-225z" />
<glyph unicode="&#x203a;" horiz-adv-x="636" d="M358 520l-237 170v225l428 -317v-164l-428 -334v228z" />
<glyph unicode="&#x2044;" horiz-adv-x="0" d="M-459 59l805 1293l96 -62l-804 -1296z" />
<glyph unicode="&#x205f;" horiz-adv-x="480" />
<glyph unicode="&#x20ac;" d="M57 748v172h127q29 240 141.5 347t354.5 107q158 0 387 -49l-8 -178q-166 31 -328 31t-229.5 -58.5t-87.5 -199.5h528v-172h-543v-162h543v-172h-528q23 -131 90 -185.5t212.5 -54.5t342.5 29l6 -180q-203 -45 -385 -46q-238 0 -350.5 105.5t-143.5 331.5h-129v172h117 q-2 29 -2 90v72h-115z" />
<glyph unicode="&#x2122;" horiz-adv-x="1368" d="M174 1169v138h410v-138h-107v-489h-151v489h-152zM641 678v629h182l107 -369l114 369h181v-629h-142v401l-98 -364h-102l-101 364v-401h-141z" />
<glyph unicode="&#x2202;" d="M541 1473q264 0 383.5 -180.5t119.5 -565.5t-117.5 -565t-381.5 -180q-229 0 -350 115.5t-121 335.5t115.5 339t324.5 119q139 0 252 -41l41 -14q-10 248 -75.5 347t-229.5 99q-66 0 -146 -20.5t-127 -40.5l-47 -19l-8 174q174 97 367 97zM807 647q-115 51 -249 51 t-190.5 -69.5t-56.5 -188.5q0 -268 234 -268q133 0 197.5 114.5t64.5 360.5z" />
<glyph unicode="&#x2206;" d="M1049 0h-951v172l285 1180h381l285 -1184v-168zM600 1163h-53l-225 -973h501z" />
<glyph unicode="&#x220f;" d="M729 -352v1704h-315v-1704h-228v1704h-123v198h1022v-198h-129v-1704h-227z" />
<glyph unicode="&#x2211;" d="M74 1550h997v-200h-756v-37l478 -617v-157l-478 -652v-41h756v-198h-997v250l512 720l-512 682v250z" />
<glyph unicode="&#x221a;" d="M4 707v200h301l221 -989h25l389 1690h225l-452 -1880h-357l-235 979h-117z" />
<glyph unicode="&#x221e;" d="M330 248q-133 0 -203 89t-70 260t69 261t200 90q84 0 143 -43t104 -141q45 98 104.5 141t143.5 43q131 0 200 -89t69 -261q0 -350 -273 -350q-82 0 -140.5 43t-103.5 139q-47 -102 -102 -142t-141 -40zM358 756q-96 0 -96 -158t96 -158q47 0 74 36t66 122 q-29 59 -41.5 83t-40 49.5t-58.5 25.5zM788 440q96 0 96.5 158t-96.5 158q-31 0 -58.5 -25.5t-39.5 -49.5t-41 -83q8 -27 19.5 -43t20.5 -33.5t13.5 -23.5t13.5 -18.5t16 -16.5t18 -12q13 -11 38 -11z" />
<glyph unicode="&#x222b;" d="M682 -25q0 -203 -77 -287.5t-218 -84.5q-55 0 -160 16l-30 6l8 185q63 -6 127.5 -6.5t92.5 38.5t28 138v1218q0 225 66.5 312t228.5 87q59 0 170 -16l32 -6l-8 -184q-70 8 -137.5 8t-95 -45t-27.5 -156v-1223z" />
<glyph unicode="&#x2248;" d="M781.5 547q-56.5 0 -216 52t-198.5 52t-94.5 -21.5t-90.5 -43.5l-35 -21l-20 180q127 106 248 107q59 0 212.5 -52t194.5 -52q74 0 183 63l34 20l19 -178q-37 -37 -108.5 -71.5t-128 -34.5zM781.5 127q-56.5 0 -216 52t-198.5 52t-94.5 -21.5t-90.5 -41.5l-35 -23 l-20 181q127 106 248 106q59 0 212.5 -52t194.5 -52t94.5 20.5t88.5 40.5l34 23l19 -179q-37 -37 -108.5 -71.5t-128 -34.5z" />
<glyph unicode="&#x2260;" d="M123 616v201h477l139 318l174 -70l-108 -248h215v-201h-303l-90 -204h393v-201h-481l-131 -301l-175 70l101 231h-211v201h299l90 204h-389z" />
<glyph unicode="&#x2264;" d="M999 823l-614 -147l614 -162v-217l-864 272v195l864 276v-217zM135 41v201h864v-201h-864z" />
<glyph unicode="&#x2265;" d="M762 676l-615 147v217l865 -276v-195l-865 -272v217zM1012 242v-201h-865v201h865z" />
<glyph unicode="&#x25ca;" d="M422 352l-324 316l324 331h305l322 -331l-322 -316h-305zM594 506l229 162l-229 178h-41l-231 -178l231 -162h41z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1024" d="M0 1024h1024v-1024h-1024v1024z" />
<hkern u1="&#x22;" u2="&#xf0;" k="23" />
<hkern u1="&#x22;" u2="&#xef;" k="-27" />
<hkern u1="&#x22;" u2="&#xee;" k="-18" />
<hkern u1="&#x22;" u2="&#xec;" k="-47" />
<hkern u1="&#x22;" u2="&#xc6;" k="92" />
<hkern u1="&#x22;" u2="&#x40;" k="23" />
<hkern u1="&#x22;" u2="&#x2f;" k="129" />
<hkern u1="&#x22;" u2="&#x26;" k="49" />
<hkern u1="&#x26;" u2="&#x201d;" k="82" />
<hkern u1="&#x26;" u2="&#x2019;" k="82" />
<hkern u1="&#x26;" u2="&#x178;" k="102" />
<hkern u1="&#x26;" u2="&#x152;" k="6" />
<hkern u1="&#x26;" u2="&#x106;" k="6" />
<hkern u1="&#x26;" u2="&#xff;" k="10" />
<hkern u1="&#x26;" u2="&#xfd;" k="10" />
<hkern u1="&#x26;" u2="&#xdd;" k="102" />
<hkern u1="&#x26;" u2="&#xdc;" k="6" />
<hkern u1="&#x26;" u2="&#xdb;" k="6" />
<hkern u1="&#x26;" u2="&#xda;" k="6" />
<hkern u1="&#x26;" u2="&#xd9;" k="6" />
<hkern u1="&#x26;" u2="&#xd8;" k="6" />
<hkern u1="&#x26;" u2="&#xd6;" k="6" />
<hkern u1="&#x26;" u2="&#xd5;" k="6" />
<hkern u1="&#x26;" u2="&#xd4;" k="6" />
<hkern u1="&#x26;" u2="&#xd3;" k="6" />
<hkern u1="&#x26;" u2="&#xd2;" k="6" />
<hkern u1="&#x26;" u2="&#xc7;" k="6" />
<hkern u1="&#x26;" u2="y" k="10" />
<hkern u1="&#x26;" u2="w" k="8" />
<hkern u1="&#x26;" u2="v" k="10" />
<hkern u1="&#x26;" u2="t" k="6" />
<hkern u1="&#x26;" u2="Y" k="102" />
<hkern u1="&#x26;" u2="W" k="33" />
<hkern u1="&#x26;" u2="V" k="55" />
<hkern u1="&#x26;" u2="U" k="6" />
<hkern u1="&#x26;" u2="T" k="76" />
<hkern u1="&#x26;" u2="Q" k="6" />
<hkern u1="&#x26;" u2="O" k="6" />
<hkern u1="&#x26;" u2="G" k="6" />
<hkern u1="&#x26;" u2="C" k="6" />
<hkern u1="&#x26;" u2="&#x27;" k="88" />
<hkern u1="&#x26;" u2="&#x22;" k="88" />
<hkern u1="&#x27;" u2="&#xf0;" k="23" />
<hkern u1="&#x27;" u2="&#xef;" k="-27" />
<hkern u1="&#x27;" u2="&#xee;" k="-18" />
<hkern u1="&#x27;" u2="&#xec;" k="-47" />
<hkern u1="&#x27;" u2="&#xc6;" k="92" />
<hkern u1="&#x27;" u2="&#x40;" k="23" />
<hkern u1="&#x27;" u2="&#x2f;" k="129" />
<hkern u1="&#x27;" u2="&#x26;" k="49" />
<hkern u1="&#x28;" u2="&#x15b;" k="14" />
<hkern u1="&#x28;" u2="&#x153;" k="39" />
<hkern u1="&#x28;" u2="&#x152;" k="29" />
<hkern u1="&#x28;" u2="&#x144;" k="16" />
<hkern u1="&#x28;" u2="&#x119;" k="39" />
<hkern u1="&#x28;" u2="&#x107;" k="39" />
<hkern u1="&#x28;" u2="&#x106;" k="27" />
<hkern u1="&#x28;" u2="&#x105;" k="18" />
<hkern u1="&#x28;" u2="&#xff;" k="14" />
<hkern u1="&#x28;" u2="&#xfd;" k="14" />
<hkern u1="&#x28;" u2="&#xfc;" k="29" />
<hkern u1="&#x28;" u2="&#xfb;" k="29" />
<hkern u1="&#x28;" u2="&#xfa;" k="29" />
<hkern u1="&#x28;" u2="&#xf9;" k="29" />
<hkern u1="&#x28;" u2="&#xf8;" k="39" />
<hkern u1="&#x28;" u2="&#xf6;" k="39" />
<hkern u1="&#x28;" u2="&#xf5;" k="39" />
<hkern u1="&#x28;" u2="&#xf4;" k="39" />
<hkern u1="&#x28;" u2="&#xf3;" k="39" />
<hkern u1="&#x28;" u2="&#xf2;" k="39" />
<hkern u1="&#x28;" u2="&#xf1;" k="16" />
<hkern u1="&#x28;" u2="&#xf0;" k="16" />
<hkern u1="&#x28;" u2="&#xef;" k="-57" />
<hkern u1="&#x28;" u2="&#xec;" k="-43" />
<hkern u1="&#x28;" u2="&#xeb;" k="39" />
<hkern u1="&#x28;" u2="&#xea;" k="39" />
<hkern u1="&#x28;" u2="&#xe9;" k="39" />
<hkern u1="&#x28;" u2="&#xe8;" k="39" />
<hkern u1="&#x28;" u2="&#xe7;" k="39" />
<hkern u1="&#x28;" u2="&#xe6;" k="18" />
<hkern u1="&#x28;" u2="&#xe5;" k="18" />
<hkern u1="&#x28;" u2="&#xe4;" k="18" />
<hkern u1="&#x28;" u2="&#xe3;" k="18" />
<hkern u1="&#x28;" u2="&#xe2;" k="18" />
<hkern u1="&#x28;" u2="&#xe1;" k="18" />
<hkern u1="&#x28;" u2="&#xe0;" k="18" />
<hkern u1="&#x28;" u2="&#xd8;" k="29" />
<hkern u1="&#x28;" u2="&#xd6;" k="29" />
<hkern u1="&#x28;" u2="&#xd5;" k="29" />
<hkern u1="&#x28;" u2="&#xd4;" k="29" />
<hkern u1="&#x28;" u2="&#xd3;" k="29" />
<hkern u1="&#x28;" u2="&#xd2;" k="29" />
<hkern u1="&#x28;" u2="&#xc7;" k="27" />
<hkern u1="&#x28;" u2="&#x7b;" k="25" />
<hkern u1="&#x28;" u2="y" k="14" />
<hkern u1="&#x28;" u2="w" k="20" />
<hkern u1="&#x28;" u2="v" k="14" />
<hkern u1="&#x28;" u2="u" k="29" />
<hkern u1="&#x28;" u2="s" k="14" />
<hkern u1="&#x28;" u2="r" k="16" />
<hkern u1="&#x28;" u2="q" k="37" />
<hkern u1="&#x28;" u2="p" k="16" />
<hkern u1="&#x28;" u2="o" k="39" />
<hkern u1="&#x28;" u2="n" k="16" />
<hkern u1="&#x28;" u2="m" k="16" />
<hkern u1="&#x28;" u2="j" k="-33" />
<hkern u1="&#x28;" u2="f" k="14" />
<hkern u1="&#x28;" u2="e" k="39" />
<hkern u1="&#x28;" u2="d" k="37" />
<hkern u1="&#x28;" u2="c" k="39" />
<hkern u1="&#x28;" u2="a" k="18" />
<hkern u1="&#x28;" u2="Q" k="29" />
<hkern u1="&#x28;" u2="O" k="29" />
<hkern u1="&#x28;" u2="G" k="29" />
<hkern u1="&#x28;" u2="C" k="27" />
<hkern u1="&#x29;" u2="&#x7d;" k="8" />
<hkern u1="&#x29;" u2="]" k="8" />
<hkern u1="&#x2a;" u2="&#x17b;" k="10" />
<hkern u1="&#x2a;" u2="&#x179;" k="10" />
<hkern u1="&#x2a;" u2="&#x15b;" k="23" />
<hkern u1="&#x2a;" u2="&#x153;" k="33" />
<hkern u1="&#x2a;" u2="&#x119;" k="33" />
<hkern u1="&#x2a;" u2="&#x107;" k="33" />
<hkern u1="&#x2a;" u2="&#x104;" k="68" />
<hkern u1="&#x2a;" u2="&#xf8;" k="33" />
<hkern u1="&#x2a;" u2="&#xf6;" k="33" />
<hkern u1="&#x2a;" u2="&#xf5;" k="33" />
<hkern u1="&#x2a;" u2="&#xf4;" k="33" />
<hkern u1="&#x2a;" u2="&#xf3;" k="33" />
<hkern u1="&#x2a;" u2="&#xf2;" k="33" />
<hkern u1="&#x2a;" u2="&#xf0;" k="31" />
<hkern u1="&#x2a;" u2="&#xef;" k="-61" />
<hkern u1="&#x2a;" u2="&#xee;" k="-80" />
<hkern u1="&#x2a;" u2="&#xec;" k="-37" />
<hkern u1="&#x2a;" u2="&#xeb;" k="33" />
<hkern u1="&#x2a;" u2="&#xea;" k="33" />
<hkern u1="&#x2a;" u2="&#xe9;" k="33" />
<hkern u1="&#x2a;" u2="&#xe8;" k="33" />
<hkern u1="&#x2a;" u2="&#xe7;" k="33" />
<hkern u1="&#x2a;" u2="&#xc6;" k="84" />
<hkern u1="&#x2a;" u2="&#xc5;" k="68" />
<hkern u1="&#x2a;" u2="&#xc4;" k="68" />
<hkern u1="&#x2a;" u2="&#xc3;" k="68" />
<hkern u1="&#x2a;" u2="&#xc2;" k="68" />
<hkern u1="&#x2a;" u2="&#xc1;" k="68" />
<hkern u1="&#x2a;" u2="&#xc0;" k="68" />
<hkern u1="&#x2a;" u2="s" k="23" />
<hkern u1="&#x2a;" u2="q" k="39" />
<hkern u1="&#x2a;" u2="o" k="33" />
<hkern u1="&#x2a;" u2="g" k="29" />
<hkern u1="&#x2a;" u2="e" k="33" />
<hkern u1="&#x2a;" u2="d" k="39" />
<hkern u1="&#x2a;" u2="c" k="33" />
<hkern u1="&#x2a;" u2="Z" k="10" />
<hkern u1="&#x2a;" u2="T" k="-14" />
<hkern u1="&#x2a;" u2="J" k="31" />
<hkern u1="&#x2a;" u2="A" k="68" />
<hkern u1="&#x2c;" u2="v" k="57" />
<hkern u1="&#x2c;" u2="f" k="20" />
<hkern u1="&#x2c;" u2="V" k="92" />
<hkern u1="&#x2d;" u2="&#xc6;" k="27" />
<hkern u1="&#x2d;" u2="x" k="53" />
<hkern u1="&#x2d;" u2="v" k="23" />
<hkern u1="&#x2d;" u2="f" k="23" />
<hkern u1="&#x2d;" u2="X" k="72" />
<hkern u1="&#x2d;" u2="V" k="55" />
<hkern u1="&#x2e;" u2="v" k="57" />
<hkern u1="&#x2e;" u2="f" k="20" />
<hkern u1="&#x2e;" u2="V" k="92" />
<hkern u1="&#x2f;" u2="&#x17c;" k="27" />
<hkern u1="&#x2f;" u2="&#x17a;" k="27" />
<hkern u1="&#x2f;" u2="&#x15b;" k="57" />
<hkern u1="&#x2f;" u2="&#x153;" k="72" />
<hkern u1="&#x2f;" u2="&#x152;" k="23" />
<hkern u1="&#x2f;" u2="&#x144;" k="43" />
<hkern u1="&#x2f;" u2="&#x119;" k="72" />
<hkern u1="&#x2f;" u2="&#x107;" k="72" />
<hkern u1="&#x2f;" u2="&#x106;" k="18" />
<hkern u1="&#x2f;" u2="&#x105;" k="51" />
<hkern u1="&#x2f;" u2="&#x104;" k="82" />
<hkern u1="&#x2f;" u2="&#xff;" k="20" />
<hkern u1="&#x2f;" u2="&#xfd;" k="20" />
<hkern u1="&#x2f;" u2="&#xfc;" k="39" />
<hkern u1="&#x2f;" u2="&#xfb;" k="39" />
<hkern u1="&#x2f;" u2="&#xfa;" k="39" />
<hkern u1="&#x2f;" u2="&#xf9;" k="39" />
<hkern u1="&#x2f;" u2="&#xf8;" k="72" />
<hkern u1="&#x2f;" u2="&#xf6;" k="72" />
<hkern u1="&#x2f;" u2="&#xf5;" k="72" />
<hkern u1="&#x2f;" u2="&#xf4;" k="72" />
<hkern u1="&#x2f;" u2="&#xf3;" k="72" />
<hkern u1="&#x2f;" u2="&#xf2;" k="72" />
<hkern u1="&#x2f;" u2="&#xf1;" k="43" />
<hkern u1="&#x2f;" u2="&#xf0;" k="25" />
<hkern u1="&#x2f;" u2="&#xef;" k="-74" />
<hkern u1="&#x2f;" u2="&#xec;" k="-86" />
<hkern u1="&#x2f;" u2="&#xeb;" k="72" />
<hkern u1="&#x2f;" u2="&#xea;" k="72" />
<hkern u1="&#x2f;" u2="&#xe9;" k="72" />
<hkern u1="&#x2f;" u2="&#xe8;" k="72" />
<hkern u1="&#x2f;" u2="&#xe7;" k="72" />
<hkern u1="&#x2f;" u2="&#xe6;" k="51" />
<hkern u1="&#x2f;" u2="&#xe5;" k="51" />
<hkern u1="&#x2f;" u2="&#xe4;" k="51" />
<hkern u1="&#x2f;" u2="&#xe3;" k="51" />
<hkern u1="&#x2f;" u2="&#xe2;" k="51" />
<hkern u1="&#x2f;" u2="&#xe1;" k="51" />
<hkern u1="&#x2f;" u2="&#xe0;" k="51" />
<hkern u1="&#x2f;" u2="&#xd8;" k="23" />
<hkern u1="&#x2f;" u2="&#xd6;" k="23" />
<hkern u1="&#x2f;" u2="&#xd5;" k="23" />
<hkern u1="&#x2f;" u2="&#xd4;" k="23" />
<hkern u1="&#x2f;" u2="&#xd3;" k="23" />
<hkern u1="&#x2f;" u2="&#xd2;" k="23" />
<hkern u1="&#x2f;" u2="&#xc7;" k="18" />
<hkern u1="&#x2f;" u2="&#xc6;" k="96" />
<hkern u1="&#x2f;" u2="&#xc5;" k="82" />
<hkern u1="&#x2f;" u2="&#xc4;" k="82" />
<hkern u1="&#x2f;" u2="&#xc3;" k="82" />
<hkern u1="&#x2f;" u2="&#xc2;" k="82" />
<hkern u1="&#x2f;" u2="&#xc1;" k="82" />
<hkern u1="&#x2f;" u2="&#xc0;" k="82" />
<hkern u1="&#x2f;" u2="z" k="27" />
<hkern u1="&#x2f;" u2="y" k="20" />
<hkern u1="&#x2f;" u2="w" k="18" />
<hkern u1="&#x2f;" u2="v" k="20" />
<hkern u1="&#x2f;" u2="u" k="39" />
<hkern u1="&#x2f;" u2="s" k="57" />
<hkern u1="&#x2f;" u2="r" k="43" />
<hkern u1="&#x2f;" u2="q" k="72" />
<hkern u1="&#x2f;" u2="p" k="43" />
<hkern u1="&#x2f;" u2="o" k="72" />
<hkern u1="&#x2f;" u2="n" k="43" />
<hkern u1="&#x2f;" u2="m" k="43" />
<hkern u1="&#x2f;" u2="g" k="68" />
<hkern u1="&#x2f;" u2="e" k="72" />
<hkern u1="&#x2f;" u2="d" k="72" />
<hkern u1="&#x2f;" u2="c" k="72" />
<hkern u1="&#x2f;" u2="a" k="51" />
<hkern u1="&#x2f;" u2="Q" k="23" />
<hkern u1="&#x2f;" u2="O" k="23" />
<hkern u1="&#x2f;" u2="J" k="33" />
<hkern u1="&#x2f;" u2="G" k="23" />
<hkern u1="&#x2f;" u2="C" k="18" />
<hkern u1="&#x2f;" u2="A" k="82" />
<hkern u1="&#x2f;" u2="&#x2f;" k="598" />
<hkern u1="&#x3a;" u2="V" k="25" />
<hkern u1="&#x3b;" u2="V" k="25" />
<hkern u1="&#x40;" u2="&#x178;" k="55" />
<hkern u1="&#x40;" u2="&#xdd;" k="55" />
<hkern u1="&#x40;" u2="&#xc6;" k="6" />
<hkern u1="&#x40;" u2="Y" k="55" />
<hkern u1="&#x40;" u2="V" k="6" />
<hkern u1="&#x40;" u2="T" k="14" />
<hkern u1="&#x40;" u2="J" k="6" />
<hkern u1="A" u2="&#x2122;" k="72" />
<hkern u1="A" u2="&#xf0;" k="10" />
<hkern u1="A" u2="&#xae;" k="43" />
<hkern u1="A" u2="&#x7d;" k="18" />
<hkern u1="A" u2="v" k="33" />
<hkern u1="A" u2="f" k="16" />
<hkern u1="A" u2="]" k="20" />
<hkern u1="A" u2="\" k="90" />
<hkern u1="A" u2="V" k="57" />
<hkern u1="A" u2="&#x3f;" k="39" />
<hkern u1="A" u2="&#x2a;" k="63" />
<hkern u1="B" u2="&#x178;" k="47" />
<hkern u1="B" u2="&#x104;" k="18" />
<hkern u1="B" u2="&#xdd;" k="47" />
<hkern u1="B" u2="&#xc6;" k="25" />
<hkern u1="B" u2="&#xc5;" k="18" />
<hkern u1="B" u2="&#xc4;" k="18" />
<hkern u1="B" u2="&#xc3;" k="18" />
<hkern u1="B" u2="&#xc2;" k="18" />
<hkern u1="B" u2="&#xc1;" k="18" />
<hkern u1="B" u2="&#xc0;" k="18" />
<hkern u1="B" u2="&#x7d;" k="18" />
<hkern u1="B" u2="g" k="16" />
<hkern u1="B" u2="]" k="39" />
<hkern u1="B" u2="\" k="25" />
<hkern u1="B" u2="Y" k="47" />
<hkern u1="B" u2="X" k="25" />
<hkern u1="B" u2="V" k="20" />
<hkern u1="B" u2="T" k="14" />
<hkern u1="B" u2="J" k="14" />
<hkern u1="B" u2="A" k="18" />
<hkern u1="B" u2="&#x3f;" k="6" />
<hkern u1="C" u2="&#xf0;" k="14" />
<hkern u1="C" u2="&#xef;" k="-53" />
<hkern u1="C" u2="&#xee;" k="-43" />
<hkern u1="C" u2="&#xec;" k="-80" />
<hkern u1="C" u2="&#xae;" k="10" />
<hkern u1="C" u2="v" k="20" />
<hkern u1="C" u2="f" k="10" />
<hkern u1="D" u2="&#xc6;" k="31" />
<hkern u1="D" u2="&#x7d;" k="37" />
<hkern u1="D" u2="]" k="47" />
<hkern u1="D" u2="\" k="27" />
<hkern u1="D" u2="X" k="39" />
<hkern u1="D" u2="V" k="23" />
<hkern u1="D" u2="&#x3f;" k="8" />
<hkern u1="D" u2="&#x2f;" k="23" />
<hkern u1="D" u2="&#x29;" k="25" />
<hkern u1="E" u2="&#xf0;" k="14" />
<hkern u1="E" u2="&#xef;" k="-49" />
<hkern u1="E" u2="&#xee;" k="-43" />
<hkern u1="E" u2="&#xec;" k="-80" />
<hkern u1="E" u2="v" k="10" />
<hkern u1="F" u2="&#x2026;" k="121" />
<hkern u1="F" u2="&#x201e;" k="121" />
<hkern u1="F" u2="&#x201a;" k="121" />
<hkern u1="F" u2="&#x2014;" k="14" />
<hkern u1="F" u2="&#x2013;" k="14" />
<hkern u1="F" u2="&#x17c;" k="29" />
<hkern u1="F" u2="&#x17a;" k="29" />
<hkern u1="F" u2="&#x15b;" k="35" />
<hkern u1="F" u2="&#x15a;" k="8" />
<hkern u1="F" u2="&#x153;" k="35" />
<hkern u1="F" u2="&#x152;" k="8" />
<hkern u1="F" u2="&#x144;" k="39" />
<hkern u1="F" u2="&#x131;" k="39" />
<hkern u1="F" u2="&#x119;" k="35" />
<hkern u1="F" u2="&#x107;" k="35" />
<hkern u1="F" u2="&#x106;" k="8" />
<hkern u1="F" u2="&#x105;" k="49" />
<hkern u1="F" u2="&#x104;" k="53" />
<hkern u1="F" u2="&#xff;" k="18" />
<hkern u1="F" u2="&#xfd;" k="18" />
<hkern u1="F" u2="&#xfc;" k="31" />
<hkern u1="F" u2="&#xfb;" k="31" />
<hkern u1="F" u2="&#xfa;" k="31" />
<hkern u1="F" u2="&#xf9;" k="31" />
<hkern u1="F" u2="&#xf8;" k="35" />
<hkern u1="F" u2="&#xf6;" k="35" />
<hkern u1="F" u2="&#xf5;" k="35" />
<hkern u1="F" u2="&#xf4;" k="35" />
<hkern u1="F" u2="&#xf3;" k="35" />
<hkern u1="F" u2="&#xf2;" k="35" />
<hkern u1="F" u2="&#xf1;" k="39" />
<hkern u1="F" u2="&#xf0;" k="33" />
<hkern u1="F" u2="&#xef;" k="-82" />
<hkern u1="F" u2="&#xee;" k="-70" />
<hkern u1="F" u2="&#xec;" k="-117" />
<hkern u1="F" u2="&#xeb;" k="35" />
<hkern u1="F" u2="&#xea;" k="35" />
<hkern u1="F" u2="&#xe9;" k="35" />
<hkern u1="F" u2="&#xe8;" k="35" />
<hkern u1="F" u2="&#xe7;" k="35" />
<hkern u1="F" u2="&#xe6;" k="49" />
<hkern u1="F" u2="&#xe5;" k="49" />
<hkern u1="F" u2="&#xe4;" k="49" />
<hkern u1="F" u2="&#xe3;" k="49" />
<hkern u1="F" u2="&#xe2;" k="49" />
<hkern u1="F" u2="&#xe1;" k="49" />
<hkern u1="F" u2="&#xe0;" k="49" />
<hkern u1="F" u2="&#xd8;" k="8" />
<hkern u1="F" u2="&#xd6;" k="8" />
<hkern u1="F" u2="&#xd5;" k="8" />
<hkern u1="F" u2="&#xd4;" k="8" />
<hkern u1="F" u2="&#xd3;" k="8" />
<hkern u1="F" u2="&#xd2;" k="8" />
<hkern u1="F" u2="&#xc7;" k="8" />
<hkern u1="F" u2="&#xc6;" k="76" />
<hkern u1="F" u2="&#xc5;" k="53" />
<hkern u1="F" u2="&#xc4;" k="53" />
<hkern u1="F" u2="&#xc3;" k="53" />
<hkern u1="F" u2="&#xc2;" k="53" />
<hkern u1="F" u2="&#xc1;" k="53" />
<hkern u1="F" u2="&#xc0;" k="53" />
<hkern u1="F" u2="z" k="29" />
<hkern u1="F" u2="y" k="18" />
<hkern u1="F" u2="x" k="29" />
<hkern u1="F" u2="w" k="18" />
<hkern u1="F" u2="v" k="10" />
<hkern u1="F" u2="u" k="31" />
<hkern u1="F" u2="t" k="6" />
<hkern u1="F" u2="s" k="35" />
<hkern u1="F" u2="r" k="39" />
<hkern u1="F" u2="q" k="39" />
<hkern u1="F" u2="p" k="39" />
<hkern u1="F" u2="o" k="35" />
<hkern u1="F" u2="n" k="39" />
<hkern u1="F" u2="m" k="39" />
<hkern u1="F" u2="g" k="43" />
<hkern u1="F" u2="f" k="12" />
<hkern u1="F" u2="e" k="35" />
<hkern u1="F" u2="d" k="39" />
<hkern u1="F" u2="c" k="35" />
<hkern u1="F" u2="a" k="49" />
<hkern u1="F" u2="S" k="8" />
<hkern u1="F" u2="Q" k="8" />
<hkern u1="F" u2="O" k="8" />
<hkern u1="F" u2="J" k="29" />
<hkern u1="F" u2="G" k="8" />
<hkern u1="F" u2="C" k="8" />
<hkern u1="F" u2="A" k="53" />
<hkern u1="F" u2="&#x2f;" k="72" />
<hkern u1="F" u2="&#x2e;" k="121" />
<hkern u1="F" u2="&#x2d;" k="14" />
<hkern u1="F" u2="&#x2c;" k="121" />
<hkern u1="G" u2="&#xef;" k="-23" />
<hkern u1="G" u2="&#xee;" k="-14" />
<hkern u1="G" u2="&#xec;" k="-43" />
<hkern u1="G" u2="v" k="12" />
<hkern u1="G" u2="f" k="12" />
<hkern u1="G" u2="\" k="14" />
<hkern u1="G" u2="V" k="18" />
<hkern u1="H" u2="&#xf0;" k="12" />
<hkern u1="H" u2="&#xec;" k="-12" />
<hkern u1="H" u2="f" k="8" />
<hkern u1="I" u2="&#xf0;" k="12" />
<hkern u1="I" u2="&#xec;" k="-12" />
<hkern u1="I" u2="f" k="8" />
<hkern u1="J" u2="&#xf0;" k="10" />
<hkern u1="J" u2="&#xec;" k="-16" />
<hkern u1="J" u2="f" k="8" />
<hkern u1="K" u2="&#xf0;" k="23" />
<hkern u1="K" u2="&#xef;" k="-84" />
<hkern u1="K" u2="&#xee;" k="-8" />
<hkern u1="K" u2="&#xec;" k="-98" />
<hkern u1="K" u2="&#xae;" k="10" />
<hkern u1="K" u2="v" k="41" />
<hkern u1="K" u2="f" k="16" />
<hkern u1="L" u2="&#x2122;" k="168" />
<hkern u1="L" u2="&#xb7;" k="49" />
<hkern u1="L" u2="&#xae;" k="145" />
<hkern u1="L" u2="&#x7d;" k="10" />
<hkern u1="L" u2="v" k="78" />
<hkern u1="L" u2="f" k="12" />
<hkern u1="L" u2="]" k="12" />
<hkern u1="L" u2="\" k="145" />
<hkern u1="L" u2="V" k="117" />
<hkern u1="L" u2="&#x3f;" k="12" />
<hkern u1="L" u2="&#x2a;" k="166" />
<hkern u1="M" u2="&#xf0;" k="12" />
<hkern u1="M" u2="&#xec;" k="-12" />
<hkern u1="M" u2="f" k="8" />
<hkern u1="N" u2="&#xf0;" k="12" />
<hkern u1="N" u2="&#xec;" k="-12" />
<hkern u1="N" u2="f" k="8" />
<hkern u1="O" u2="&#xc6;" k="29" />
<hkern u1="O" u2="&#x7d;" k="35" />
<hkern u1="O" u2="]" k="49" />
<hkern u1="O" u2="\" k="29" />
<hkern u1="O" u2="X" k="37" />
<hkern u1="O" u2="V" k="23" />
<hkern u1="O" u2="&#x3f;" k="6" />
<hkern u1="O" u2="&#x2f;" k="20" />
<hkern u1="O" u2="&#x29;" k="20" />
<hkern u1="P" u2="&#x2039;" k="8" />
<hkern u1="P" u2="&#x2026;" k="143" />
<hkern u1="P" u2="&#x201e;" k="143" />
<hkern u1="P" u2="&#x201a;" k="143" />
<hkern u1="P" u2="&#x2014;" k="8" />
<hkern u1="P" u2="&#x2013;" k="8" />
<hkern u1="P" u2="&#x17b;" k="6" />
<hkern u1="P" u2="&#x179;" k="6" />
<hkern u1="P" u2="&#x178;" k="41" />
<hkern u1="P" u2="&#x105;" k="10" />
<hkern u1="P" u2="&#x104;" k="49" />
<hkern u1="P" u2="&#xf0;" k="29" />
<hkern u1="P" u2="&#xef;" k="-12" />
<hkern u1="P" u2="&#xee;" k="-25" />
<hkern u1="P" u2="&#xec;" k="-16" />
<hkern u1="P" u2="&#xe6;" k="10" />
<hkern u1="P" u2="&#xe5;" k="10" />
<hkern u1="P" u2="&#xe4;" k="10" />
<hkern u1="P" u2="&#xe3;" k="10" />
<hkern u1="P" u2="&#xe2;" k="10" />
<hkern u1="P" u2="&#xe1;" k="10" />
<hkern u1="P" u2="&#xe0;" k="10" />
<hkern u1="P" u2="&#xdd;" k="41" />
<hkern u1="P" u2="&#xc6;" k="61" />
<hkern u1="P" u2="&#xc5;" k="49" />
<hkern u1="P" u2="&#xc4;" k="49" />
<hkern u1="P" u2="&#xc3;" k="49" />
<hkern u1="P" u2="&#xc2;" k="49" />
<hkern u1="P" u2="&#xc1;" k="49" />
<hkern u1="P" u2="&#xc0;" k="49" />
<hkern u1="P" u2="&#xab;" k="8" />
<hkern u1="P" u2="&#x7d;" k="31" />
<hkern u1="P" u2="a" k="10" />
<hkern u1="P" u2="]" k="35" />
<hkern u1="P" u2="\" k="18" />
<hkern u1="P" u2="Z" k="6" />
<hkern u1="P" u2="Y" k="41" />
<hkern u1="P" u2="X" k="35" />
<hkern u1="P" u2="V" k="14" />
<hkern u1="P" u2="J" k="33" />
<hkern u1="P" u2="A" k="49" />
<hkern u1="P" u2="&#x2f;" k="76" />
<hkern u1="P" u2="&#x2e;" k="143" />
<hkern u1="P" u2="&#x2d;" k="8" />
<hkern u1="P" u2="&#x2c;" k="143" />
<hkern u1="P" u2="&#x29;" k="14" />
<hkern u1="Q" u2="&#xc6;" k="29" />
<hkern u1="Q" u2="&#x7d;" k="35" />
<hkern u1="Q" u2="]" k="49" />
<hkern u1="Q" u2="\" k="29" />
<hkern u1="Q" u2="X" k="37" />
<hkern u1="Q" u2="V" k="23" />
<hkern u1="Q" u2="&#x3f;" k="6" />
<hkern u1="Q" u2="&#x2f;" k="20" />
<hkern u1="Q" u2="&#x29;" k="20" />
<hkern u1="R" u2="&#xf0;" k="27" />
<hkern u1="R" u2="&#xc6;" k="18" />
<hkern u1="R" u2="&#x7d;" k="14" />
<hkern u1="R" u2="]" k="16" />
<hkern u1="R" u2="\" k="23" />
<hkern u1="R" u2="X" k="8" />
<hkern u1="R" u2="V" k="20" />
<hkern u1="S" u2="&#xef;" k="-31" />
<hkern u1="S" u2="&#xee;" k="-12" />
<hkern u1="S" u2="&#xec;" k="-49" />
<hkern u1="S" u2="&#xc6;" k="23" />
<hkern u1="S" u2="x" k="18" />
<hkern u1="S" u2="v" k="16" />
<hkern u1="S" u2="f" k="16" />
<hkern u1="S" u2="X" k="10" />
<hkern u1="S" u2="V" k="18" />
<hkern u1="T" u2="&#x131;" k="137" />
<hkern u1="T" u2="&#xf0;" k="51" />
<hkern u1="T" u2="&#xef;" k="-104" />
<hkern u1="T" u2="&#xee;" k="-84" />
<hkern u1="T" u2="&#xec;" k="-141" />
<hkern u1="T" u2="&#xe4;" k="135" />
<hkern u1="T" u2="&#xe3;" k="111" />
<hkern u1="T" u2="&#xc6;" k="104" />
<hkern u1="T" u2="&#xae;" k="8" />
<hkern u1="T" u2="x" k="121" />
<hkern u1="T" u2="v" k="117" />
<hkern u1="T" u2="f" k="31" />
<hkern u1="T" u2="&#x40;" k="41" />
<hkern u1="T" u2="&#x2f;" k="102" />
<hkern u1="T" u2="&#x26;" k="31" />
<hkern u1="U" u2="&#xf0;" k="12" />
<hkern u1="U" u2="&#xec;" k="-20" />
<hkern u1="U" u2="&#xc6;" k="16" />
<hkern u1="U" u2="f" k="8" />
<hkern u1="U" u2="&#x2f;" k="25" />
<hkern u1="V" u2="&#x203a;" k="31" />
<hkern u1="V" u2="&#x2039;" k="53" />
<hkern u1="V" u2="&#x2026;" k="92" />
<hkern u1="V" u2="&#x201e;" k="92" />
<hkern u1="V" u2="&#x201a;" k="92" />
<hkern u1="V" u2="&#x2014;" k="55" />
<hkern u1="V" u2="&#x2013;" k="55" />
<hkern u1="V" u2="&#x17c;" k="27" />
<hkern u1="V" u2="&#x17a;" k="27" />
<hkern u1="V" u2="&#x15b;" k="47" />
<hkern u1="V" u2="&#x15a;" k="16" />
<hkern u1="V" u2="&#x153;" k="61" />
<hkern u1="V" u2="&#x152;" k="23" />
<hkern u1="V" u2="&#x144;" k="47" />
<hkern u1="V" u2="&#x131;" k="47" />
<hkern u1="V" u2="&#x119;" k="61" />
<hkern u1="V" u2="&#x107;" k="61" />
<hkern u1="V" u2="&#x106;" k="20" />
<hkern u1="V" u2="&#x105;" k="51" />
<hkern u1="V" u2="&#x104;" k="57" />
<hkern u1="V" u2="&#xff;" k="14" />
<hkern u1="V" u2="&#xfd;" k="14" />
<hkern u1="V" u2="&#xfc;" k="41" />
<hkern u1="V" u2="&#xfb;" k="41" />
<hkern u1="V" u2="&#xfa;" k="41" />
<hkern u1="V" u2="&#xf9;" k="41" />
<hkern u1="V" u2="&#xf8;" k="61" />
<hkern u1="V" u2="&#xf6;" k="61" />
<hkern u1="V" u2="&#xf5;" k="61" />
<hkern u1="V" u2="&#xf4;" k="61" />
<hkern u1="V" u2="&#xf3;" k="61" />
<hkern u1="V" u2="&#xf2;" k="61" />
<hkern u1="V" u2="&#xf1;" k="47" />
<hkern u1="V" u2="&#xf0;" k="47" />
<hkern u1="V" u2="&#xef;" k="-92" />
<hkern u1="V" u2="&#xee;" k="-45" />
<hkern u1="V" u2="&#xec;" k="-115" />
<hkern u1="V" u2="&#xeb;" k="61" />
<hkern u1="V" u2="&#xea;" k="61" />
<hkern u1="V" u2="&#xe9;" k="61" />
<hkern u1="V" u2="&#xe8;" k="61" />
<hkern u1="V" u2="&#xe7;" k="61" />
<hkern u1="V" u2="&#xe6;" k="51" />
<hkern u1="V" u2="&#xe5;" k="51" />
<hkern u1="V" u2="&#xe4;" k="51" />
<hkern u1="V" u2="&#xe3;" k="51" />
<hkern u1="V" u2="&#xe2;" k="51" />
<hkern u1="V" u2="&#xe1;" k="51" />
<hkern u1="V" u2="&#xe0;" k="51" />
<hkern u1="V" u2="&#xd8;" k="23" />
<hkern u1="V" u2="&#xd6;" k="23" />
<hkern u1="V" u2="&#xd5;" k="23" />
<hkern u1="V" u2="&#xd4;" k="23" />
<hkern u1="V" u2="&#xd3;" k="23" />
<hkern u1="V" u2="&#xd2;" k="23" />
<hkern u1="V" u2="&#xc7;" k="20" />
<hkern u1="V" u2="&#xc6;" k="66" />
<hkern u1="V" u2="&#xc5;" k="57" />
<hkern u1="V" u2="&#xc4;" k="57" />
<hkern u1="V" u2="&#xc3;" k="57" />
<hkern u1="V" u2="&#xc2;" k="57" />
<hkern u1="V" u2="&#xc1;" k="57" />
<hkern u1="V" u2="&#xc0;" k="57" />
<hkern u1="V" u2="&#xbb;" k="31" />
<hkern u1="V" u2="&#xae;" k="6" />
<hkern u1="V" u2="&#xab;" k="53" />
<hkern u1="V" u2="z" k="27" />
<hkern u1="V" u2="y" k="14" />
<hkern u1="V" u2="x" k="14" />
<hkern u1="V" u2="w" k="18" />
<hkern u1="V" u2="v" k="14" />
<hkern u1="V" u2="u" k="41" />
<hkern u1="V" u2="s" k="47" />
<hkern u1="V" u2="r" k="47" />
<hkern u1="V" u2="q" k="61" />
<hkern u1="V" u2="p" k="47" />
<hkern u1="V" u2="o" k="61" />
<hkern u1="V" u2="n" k="47" />
<hkern u1="V" u2="m" k="47" />
<hkern u1="V" u2="g" k="68" />
<hkern u1="V" u2="f" k="10" />
<hkern u1="V" u2="e" k="61" />
<hkern u1="V" u2="d" k="61" />
<hkern u1="V" u2="c" k="61" />
<hkern u1="V" u2="a" k="51" />
<hkern u1="V" u2="S" k="16" />
<hkern u1="V" u2="Q" k="23" />
<hkern u1="V" u2="O" k="23" />
<hkern u1="V" u2="J" k="37" />
<hkern u1="V" u2="G" k="23" />
<hkern u1="V" u2="C" k="20" />
<hkern u1="V" u2="A" k="57" />
<hkern u1="V" u2="&#x40;" k="27" />
<hkern u1="V" u2="&#x3b;" k="25" />
<hkern u1="V" u2="&#x3a;" k="25" />
<hkern u1="V" u2="&#x2f;" k="84" />
<hkern u1="V" u2="&#x2e;" k="92" />
<hkern u1="V" u2="&#x2d;" k="55" />
<hkern u1="V" u2="&#x2c;" k="92" />
<hkern u1="V" u2="&#x26;" k="33" />
<hkern u1="W" u2="&#x131;" k="29" />
<hkern u1="W" u2="&#xf0;" k="31" />
<hkern u1="W" u2="&#xef;" k="-80" />
<hkern u1="W" u2="&#xee;" k="-45" />
<hkern u1="W" u2="&#xec;" k="-104" />
<hkern u1="W" u2="&#xc6;" k="51" />
<hkern u1="W" u2="&#x2f;" k="57" />
<hkern u1="W" u2="&#x26;" k="6" />
<hkern u1="X" u2="&#x2039;" k="41" />
<hkern u1="X" u2="&#x2014;" k="72" />
<hkern u1="X" u2="&#x2013;" k="72" />
<hkern u1="X" u2="&#x153;" k="49" />
<hkern u1="X" u2="&#x152;" k="37" />
<hkern u1="X" u2="&#x144;" k="8" />
<hkern u1="X" u2="&#x119;" k="49" />
<hkern u1="X" u2="&#x107;" k="49" />
<hkern u1="X" u2="&#x106;" k="35" />
<hkern u1="X" u2="&#x105;" k="8" />
<hkern u1="X" u2="&#xff;" k="51" />
<hkern u1="X" u2="&#xfd;" k="51" />
<hkern u1="X" u2="&#xfc;" k="37" />
<hkern u1="X" u2="&#xfb;" k="37" />
<hkern u1="X" u2="&#xfa;" k="37" />
<hkern u1="X" u2="&#xf9;" k="37" />
<hkern u1="X" u2="&#xf8;" k="49" />
<hkern u1="X" u2="&#xf6;" k="49" />
<hkern u1="X" u2="&#xf5;" k="49" />
<hkern u1="X" u2="&#xf4;" k="49" />
<hkern u1="X" u2="&#xf3;" k="49" />
<hkern u1="X" u2="&#xf2;" k="49" />
<hkern u1="X" u2="&#xf1;" k="8" />
<hkern u1="X" u2="&#xf0;" k="31" />
<hkern u1="X" u2="&#xef;" k="-104" />
<hkern u1="X" u2="&#xee;" k="-20" />
<hkern u1="X" u2="&#xec;" k="-115" />
<hkern u1="X" u2="&#xeb;" k="49" />
<hkern u1="X" u2="&#xea;" k="49" />
<hkern u1="X" u2="&#xe9;" k="49" />
<hkern u1="X" u2="&#xe8;" k="49" />
<hkern u1="X" u2="&#xe7;" k="49" />
<hkern u1="X" u2="&#xe6;" k="8" />
<hkern u1="X" u2="&#xe5;" k="8" />
<hkern u1="X" u2="&#xe4;" k="8" />
<hkern u1="X" u2="&#xe3;" k="8" />
<hkern u1="X" u2="&#xe2;" k="8" />
<hkern u1="X" u2="&#xe1;" k="8" />
<hkern u1="X" u2="&#xe0;" k="8" />
<hkern u1="X" u2="&#xd8;" k="37" />
<hkern u1="X" u2="&#xd6;" k="37" />
<hkern u1="X" u2="&#xd5;" k="37" />
<hkern u1="X" u2="&#xd4;" k="37" />
<hkern u1="X" u2="&#xd3;" k="37" />
<hkern u1="X" u2="&#xd2;" k="37" />
<hkern u1="X" u2="&#xc7;" k="35" />
<hkern u1="X" u2="&#xae;" k="8" />
<hkern u1="X" u2="&#xab;" k="41" />
<hkern u1="X" u2="y" k="51" />
<hkern u1="X" u2="w" k="51" />
<hkern u1="X" u2="v" k="49" />
<hkern u1="X" u2="u" k="37" />
<hkern u1="X" u2="t" k="20" />
<hkern u1="X" u2="r" k="8" />
<hkern u1="X" u2="q" k="39" />
<hkern u1="X" u2="p" k="8" />
<hkern u1="X" u2="o" k="49" />
<hkern u1="X" u2="n" k="8" />
<hkern u1="X" u2="m" k="8" />
<hkern u1="X" u2="g" k="37" />
<hkern u1="X" u2="f" k="14" />
<hkern u1="X" u2="e" k="49" />
<hkern u1="X" u2="d" k="39" />
<hkern u1="X" u2="c" k="49" />
<hkern u1="X" u2="a" k="8" />
<hkern u1="X" u2="Q" k="37" />
<hkern u1="X" u2="O" k="37" />
<hkern u1="X" u2="G" k="37" />
<hkern u1="X" u2="C" k="35" />
<hkern u1="X" u2="&#x2d;" k="72" />
<hkern u1="Y" u2="&#x142;" k="12" />
<hkern u1="Y" u2="&#x131;" k="123" />
<hkern u1="Y" u2="&#xff;" k="57" />
<hkern u1="Y" u2="&#xf0;" k="66" />
<hkern u1="Y" u2="&#xef;" k="-121" />
<hkern u1="Y" u2="&#xee;" k="-33" />
<hkern u1="Y" u2="&#xec;" k="-129" />
<hkern u1="Y" u2="&#xeb;" k="131" />
<hkern u1="Y" u2="&#xe4;" k="100" />
<hkern u1="Y" u2="&#xe3;" k="86" />
<hkern u1="Y" u2="&#xdf;" k="20" />
<hkern u1="Y" u2="&#xc6;" k="115" />
<hkern u1="Y" u2="&#xae;" k="41" />
<hkern u1="Y" u2="x" k="76" />
<hkern u1="Y" u2="v" k="74" />
<hkern u1="Y" u2="f" k="43" />
<hkern u1="Y" u2="&#x40;" k="74" />
<hkern u1="Y" u2="&#x2f;" k="133" />
<hkern u1="Y" u2="&#x2a;" k="-8" />
<hkern u1="Y" u2="&#x26;" k="66" />
<hkern u1="Z" u2="&#xf0;" k="16" />
<hkern u1="Z" u2="&#xef;" k="-45" />
<hkern u1="Z" u2="&#xee;" k="-47" />
<hkern u1="Z" u2="&#xec;" k="-82" />
<hkern u1="Z" u2="&#xae;" k="6" />
<hkern u1="Z" u2="v" k="18" />
<hkern u1="Z" u2="f" k="10" />
<hkern u1="[" u2="&#x17c;" k="18" />
<hkern u1="[" u2="&#x17a;" k="18" />
<hkern u1="[" u2="&#x15b;" k="23" />
<hkern u1="[" u2="&#x15a;" k="10" />
<hkern u1="[" u2="&#x153;" k="66" />
<hkern u1="[" u2="&#x152;" k="49" />
<hkern u1="[" u2="&#x144;" k="23" />
<hkern u1="[" u2="&#x119;" k="66" />
<hkern u1="[" u2="&#x107;" k="66" />
<hkern u1="[" u2="&#x106;" k="41" />
<hkern u1="[" u2="&#x105;" k="47" />
<hkern u1="[" u2="&#x104;" k="20" />
<hkern u1="[" u2="&#xff;" k="49" />
<hkern u1="[" u2="&#xfd;" k="49" />
<hkern u1="[" u2="&#xfc;" k="53" />
<hkern u1="[" u2="&#xfb;" k="53" />
<hkern u1="[" u2="&#xfa;" k="53" />
<hkern u1="[" u2="&#xf9;" k="53" />
<hkern u1="[" u2="&#xf8;" k="66" />
<hkern u1="[" u2="&#xf6;" k="66" />
<hkern u1="[" u2="&#xf5;" k="66" />
<hkern u1="[" u2="&#xf4;" k="66" />
<hkern u1="[" u2="&#xf3;" k="66" />
<hkern u1="[" u2="&#xf2;" k="66" />
<hkern u1="[" u2="&#xf1;" k="23" />
<hkern u1="[" u2="&#xf0;" k="25" />
<hkern u1="[" u2="&#xef;" k="-57" />
<hkern u1="[" u2="&#xec;" k="-90" />
<hkern u1="[" u2="&#xeb;" k="66" />
<hkern u1="[" u2="&#xea;" k="66" />
<hkern u1="[" u2="&#xe9;" k="66" />
<hkern u1="[" u2="&#xe8;" k="66" />
<hkern u1="[" u2="&#xe7;" k="66" />
<hkern u1="[" u2="&#xe6;" k="47" />
<hkern u1="[" u2="&#xe5;" k="47" />
<hkern u1="[" u2="&#xe4;" k="47" />
<hkern u1="[" u2="&#xe3;" k="47" />
<hkern u1="[" u2="&#xe2;" k="47" />
<hkern u1="[" u2="&#xe1;" k="47" />
<hkern u1="[" u2="&#xe0;" k="47" />
<hkern u1="[" u2="&#xd8;" k="49" />
<hkern u1="[" u2="&#xd6;" k="49" />
<hkern u1="[" u2="&#xd5;" k="49" />
<hkern u1="[" u2="&#xd4;" k="49" />
<hkern u1="[" u2="&#xd3;" k="49" />
<hkern u1="[" u2="&#xd2;" k="49" />
<hkern u1="[" u2="&#xc7;" k="41" />
<hkern u1="[" u2="&#xc6;" k="20" />
<hkern u1="[" u2="&#xc5;" k="20" />
<hkern u1="[" u2="&#xc4;" k="20" />
<hkern u1="[" u2="&#xc3;" k="20" />
<hkern u1="[" u2="&#xc2;" k="20" />
<hkern u1="[" u2="&#xc1;" k="20" />
<hkern u1="[" u2="&#xc0;" k="20" />
<hkern u1="[" u2="&#x7b;" k="39" />
<hkern u1="[" u2="z" k="18" />
<hkern u1="[" u2="y" k="49" />
<hkern u1="[" u2="x" k="16" />
<hkern u1="[" u2="w" k="53" />
<hkern u1="[" u2="v" k="51" />
<hkern u1="[" u2="u" k="53" />
<hkern u1="[" u2="t" k="35" />
<hkern u1="[" u2="s" k="23" />
<hkern u1="[" u2="r" k="23" />
<hkern u1="[" u2="q" k="63" />
<hkern u1="[" u2="p" k="23" />
<hkern u1="[" u2="o" k="66" />
<hkern u1="[" u2="n" k="23" />
<hkern u1="[" u2="m" k="23" />
<hkern u1="[" u2="j" k="-14" />
<hkern u1="[" u2="f" k="25" />
<hkern u1="[" u2="e" k="66" />
<hkern u1="[" u2="d" k="63" />
<hkern u1="[" u2="c" k="66" />
<hkern u1="[" u2="a" k="47" />
<hkern u1="[" u2="S" k="10" />
<hkern u1="[" u2="Q" k="49" />
<hkern u1="[" u2="O" k="49" />
<hkern u1="[" u2="G" k="49" />
<hkern u1="[" u2="C" k="41" />
<hkern u1="[" u2="A" k="20" />
<hkern u1="[" u2="&#x28;" k="8" />
<hkern u1="\" u2="&#x201d;" k="139" />
<hkern u1="\" u2="&#x2019;" k="139" />
<hkern u1="\" u2="&#x178;" k="143" />
<hkern u1="\" u2="&#x15a;" k="14" />
<hkern u1="\" u2="&#x153;" k="14" />
<hkern u1="\" u2="&#x152;" k="31" />
<hkern u1="\" u2="&#x119;" k="14" />
<hkern u1="\" u2="&#x107;" k="14" />
<hkern u1="\" u2="&#x106;" k="29" />
<hkern u1="\" u2="&#xff;" k="47" />
<hkern u1="\" u2="&#xfd;" k="47" />
<hkern u1="\" u2="&#xf8;" k="14" />
<hkern u1="\" u2="&#xf6;" k="14" />
<hkern u1="\" u2="&#xf5;" k="14" />
<hkern u1="\" u2="&#xf4;" k="14" />
<hkern u1="\" u2="&#xf3;" k="14" />
<hkern u1="\" u2="&#xf2;" k="14" />
<hkern u1="\" u2="&#xeb;" k="14" />
<hkern u1="\" u2="&#xea;" k="14" />
<hkern u1="\" u2="&#xe9;" k="14" />
<hkern u1="\" u2="&#xe8;" k="14" />
<hkern u1="\" u2="&#xe7;" k="14" />
<hkern u1="\" u2="&#xdd;" k="143" />
<hkern u1="\" u2="&#xdc;" k="33" />
<hkern u1="\" u2="&#xdb;" k="33" />
<hkern u1="\" u2="&#xda;" k="33" />
<hkern u1="\" u2="&#xd9;" k="33" />
<hkern u1="\" u2="&#xd8;" k="31" />
<hkern u1="\" u2="&#xd6;" k="31" />
<hkern u1="\" u2="&#xd5;" k="31" />
<hkern u1="\" u2="&#xd4;" k="31" />
<hkern u1="\" u2="&#xd3;" k="31" />
<hkern u1="\" u2="&#xd2;" k="31" />
<hkern u1="\" u2="&#xc7;" k="29" />
<hkern u1="\" u2="y" k="47" />
<hkern u1="\" u2="w" k="37" />
<hkern u1="\" u2="v" k="45" />
<hkern u1="\" u2="t" k="29" />
<hkern u1="\" u2="o" k="14" />
<hkern u1="\" u2="f" k="18" />
<hkern u1="\" u2="e" k="14" />
<hkern u1="\" u2="c" k="14" />
<hkern u1="\" u2="Y" k="143" />
<hkern u1="\" u2="W" k="66" />
<hkern u1="\" u2="V" k="92" />
<hkern u1="\" u2="U" k="33" />
<hkern u1="\" u2="T" k="115" />
<hkern u1="\" u2="S" k="14" />
<hkern u1="\" u2="Q" k="31" />
<hkern u1="\" u2="O" k="31" />
<hkern u1="\" u2="G" k="31" />
<hkern u1="\" u2="C" k="29" />
<hkern u1="\" u2="&#x27;" k="145" />
<hkern u1="\" u2="&#x22;" k="145" />
<hkern u1="a" u2="&#x2122;" k="29" />
<hkern u1="a" u2="&#x7d;" k="8" />
<hkern u1="a" u2="v" k="10" />
<hkern u1="a" u2="]" k="10" />
<hkern u1="a" u2="\" k="82" />
<hkern u1="a" u2="V" k="55" />
<hkern u1="a" u2="&#x3f;" k="29" />
<hkern u1="a" u2="&#x2a;" k="12" />
<hkern u1="b" u2="&#x2122;" k="33" />
<hkern u1="b" u2="&#xc6;" k="14" />
<hkern u1="b" u2="&#x7d;" k="51" />
<hkern u1="b" u2="x" k="20" />
<hkern u1="b" u2="v" k="14" />
<hkern u1="b" u2="]" k="63" />
<hkern u1="b" u2="\" k="78" />
<hkern u1="b" u2="X" k="43" />
<hkern u1="b" u2="V" k="59" />
<hkern u1="b" u2="&#x3f;" k="41" />
<hkern u1="b" u2="&#x2a;" k="16" />
<hkern u1="b" u2="&#x29;" k="37" />
<hkern u1="c" u2="&#xf0;" k="18" />
<hkern u1="c" u2="&#x7d;" k="14" />
<hkern u1="c" u2="]" k="16" />
<hkern u1="c" u2="\" k="37" />
<hkern u1="c" u2="V" k="25" />
<hkern u1="c" u2="&#x3f;" k="8" />
<hkern u1="d" u2="&#xef;" k="-12" />
<hkern u1="d" u2="&#xec;" k="-27" />
<hkern u1="e" u2="&#x2122;" k="27" />
<hkern u1="e" u2="&#xc6;" k="10" />
<hkern u1="e" u2="&#x7d;" k="37" />
<hkern u1="e" u2="v" k="14" />
<hkern u1="e" u2="]" k="23" />
<hkern u1="e" u2="\" k="74" />
<hkern u1="e" u2="X" k="8" />
<hkern u1="e" u2="V" k="57" />
<hkern u1="e" u2="&#x3f;" k="31" />
<hkern u1="e" u2="&#x29;" k="18" />
<hkern u1="f" u2="&#x203a;" k="18" />
<hkern u1="f" u2="&#x2039;" k="57" />
<hkern u1="f" u2="&#x2026;" k="76" />
<hkern u1="f" u2="&#x201e;" k="76" />
<hkern u1="f" u2="&#x201a;" k="76" />
<hkern u1="f" u2="&#x2014;" k="74" />
<hkern u1="f" u2="&#x2013;" k="74" />
<hkern u1="f" u2="&#x17b;" k="10" />
<hkern u1="f" u2="&#x179;" k="10" />
<hkern u1="f" u2="&#x178;" k="-6" />
<hkern u1="f" u2="&#x153;" k="12" />
<hkern u1="f" u2="&#x119;" k="12" />
<hkern u1="f" u2="&#x107;" k="12" />
<hkern u1="f" u2="&#x104;" k="47" />
<hkern u1="f" u2="&#xf8;" k="12" />
<hkern u1="f" u2="&#xf6;" k="12" />
<hkern u1="f" u2="&#xf5;" k="12" />
<hkern u1="f" u2="&#xf4;" k="12" />
<hkern u1="f" u2="&#xf3;" k="12" />
<hkern u1="f" u2="&#xf2;" k="12" />
<hkern u1="f" u2="&#xf0;" k="53" />
<hkern u1="f" u2="&#xef;" k="-90" />
<hkern u1="f" u2="&#xee;" k="-68" />
<hkern u1="f" u2="&#xec;" k="-147" />
<hkern u1="f" u2="&#xeb;" k="12" />
<hkern u1="f" u2="&#xea;" k="12" />
<hkern u1="f" u2="&#xe9;" k="12" />
<hkern u1="f" u2="&#xe8;" k="12" />
<hkern u1="f" u2="&#xe7;" k="12" />
<hkern u1="f" u2="&#xdd;" k="-6" />
<hkern u1="f" u2="&#xc6;" k="57" />
<hkern u1="f" u2="&#xc5;" k="47" />
<hkern u1="f" u2="&#xc4;" k="47" />
<hkern u1="f" u2="&#xc3;" k="47" />
<hkern u1="f" u2="&#xc2;" k="47" />
<hkern u1="f" u2="&#xc1;" k="47" />
<hkern u1="f" u2="&#xc0;" k="47" />
<hkern u1="f" u2="&#xbb;" k="18" />
<hkern u1="f" u2="&#xab;" k="57" />
<hkern u1="f" u2="q" k="8" />
<hkern u1="f" u2="o" k="12" />
<hkern u1="f" u2="e" k="12" />
<hkern u1="f" u2="d" k="8" />
<hkern u1="f" u2="c" k="12" />
<hkern u1="f" u2="Z" k="10" />
<hkern u1="f" u2="Y" k="-6" />
<hkern u1="f" u2="X" k="12" />
<hkern u1="f" u2="T" k="27" />
<hkern u1="f" u2="J" k="31" />
<hkern u1="f" u2="A" k="47" />
<hkern u1="f" u2="&#x2f;" k="53" />
<hkern u1="f" u2="&#x2e;" k="76" />
<hkern u1="f" u2="&#x2d;" k="74" />
<hkern u1="f" u2="&#x2c;" k="76" />
<hkern u1="f" u2="&#x26;" k="8" />
<hkern u1="g" u2="&#xf0;" k="12" />
<hkern u1="g" u2="j" k="-45" />
<hkern u1="g" u2="\" k="23" />
<hkern u1="g" u2="V" k="8" />
<hkern u1="h" u2="&#x2122;" k="31" />
<hkern u1="h" u2="&#x7d;" k="20" />
<hkern u1="h" u2="v" k="10" />
<hkern u1="h" u2="]" k="23" />
<hkern u1="h" u2="\" k="78" />
<hkern u1="h" u2="V" k="57" />
<hkern u1="h" u2="&#x3f;" k="35" />
<hkern u1="h" u2="&#x2a;" k="12" />
<hkern u1="h" u2="&#x29;" k="16" />
<hkern u1="i" u2="&#xef;" k="-12" />
<hkern u1="i" u2="&#xec;" k="-27" />
<hkern u1="j" u2="&#xef;" k="-12" />
<hkern u1="j" u2="&#xec;" k="-27" />
<hkern u1="k" u2="&#xf0;" k="29" />
<hkern u1="k" u2="&#x7d;" k="12" />
<hkern u1="k" u2="]" k="14" />
<hkern u1="k" u2="\" k="27" />
<hkern u1="k" u2="V" k="16" />
<hkern u1="k" u2="&#x3f;" k="6" />
<hkern u1="l" u2="&#xec;" k="-18" />
<hkern u1="l" u2="&#xb7;" k="123" />
<hkern u1="m" u2="&#x2122;" k="31" />
<hkern u1="m" u2="&#x7d;" k="20" />
<hkern u1="m" u2="v" k="10" />
<hkern u1="m" u2="]" k="23" />
<hkern u1="m" u2="\" k="78" />
<hkern u1="m" u2="V" k="57" />
<hkern u1="m" u2="&#x3f;" k="35" />
<hkern u1="m" u2="&#x2a;" k="12" />
<hkern u1="m" u2="&#x29;" k="16" />
<hkern u1="n" u2="&#x2122;" k="31" />
<hkern u1="n" u2="&#x7d;" k="20" />
<hkern u1="n" u2="v" k="10" />
<hkern u1="n" u2="]" k="23" />
<hkern u1="n" u2="\" k="78" />
<hkern u1="n" u2="V" k="57" />
<hkern u1="n" u2="&#x3f;" k="35" />
<hkern u1="n" u2="&#x2a;" k="12" />
<hkern u1="n" u2="&#x29;" k="16" />
<hkern u1="o" u2="&#x2122;" k="31" />
<hkern u1="o" u2="&#xc6;" k="14" />
<hkern u1="o" u2="&#x7d;" k="51" />
<hkern u1="o" u2="x" k="23" />
<hkern u1="o" u2="v" k="16" />
<hkern u1="o" u2="]" k="66" />
<hkern u1="o" u2="\" k="80" />
<hkern u1="o" u2="X" k="49" />
<hkern u1="o" u2="V" k="61" />
<hkern u1="o" u2="&#x3f;" k="39" />
<hkern u1="o" u2="&#x2a;" k="12" />
<hkern u1="o" u2="&#x29;" k="39" />
<hkern u1="p" u2="&#x2122;" k="33" />
<hkern u1="p" u2="&#xc6;" k="14" />
<hkern u1="p" u2="&#x7d;" k="51" />
<hkern u1="p" u2="x" k="20" />
<hkern u1="p" u2="v" k="14" />
<hkern u1="p" u2="]" k="63" />
<hkern u1="p" u2="\" k="78" />
<hkern u1="p" u2="X" k="43" />
<hkern u1="p" u2="V" k="59" />
<hkern u1="p" u2="&#x3f;" k="41" />
<hkern u1="p" u2="&#x2a;" k="16" />
<hkern u1="p" u2="&#x29;" k="37" />
<hkern u1="q" u2="&#x2122;" k="20" />
<hkern u1="q" u2="&#x7d;" k="20" />
<hkern u1="q" u2="]" k="23" />
<hkern u1="q" u2="\" k="53" />
<hkern u1="q" u2="X" k="8" />
<hkern u1="q" u2="V" k="47" />
<hkern u1="q" u2="&#x3f;" k="10" />
<hkern u1="q" u2="&#x29;" k="16" />
<hkern u1="r" u2="&#xf0;" k="57" />
<hkern u1="r" u2="&#xc6;" k="74" />
<hkern u1="r" u2="&#x7d;" k="35" />
<hkern u1="r" u2="]" k="47" />
<hkern u1="r" u2="\" k="16" />
<hkern u1="r" u2="X" k="57" />
<hkern u1="r" u2="&#x2f;" k="68" />
<hkern u1="r" u2="&#x29;" k="14" />
<hkern u1="r" u2="&#x26;" k="8" />
<hkern u1="s" u2="&#x2122;" k="23" />
<hkern u1="s" u2="&#xc6;" k="10" />
<hkern u1="s" u2="&#x7d;" k="39" />
<hkern u1="s" u2="v" k="12" />
<hkern u1="s" u2="]" k="49" />
<hkern u1="s" u2="\" k="53" />
<hkern u1="s" u2="X" k="12" />
<hkern u1="s" u2="V" k="41" />
<hkern u1="s" u2="&#x3f;" k="10" />
<hkern u1="s" u2="&#x29;" k="20" />
<hkern u1="t" u2="&#x7d;" k="8" />
<hkern u1="t" u2="]" k="10" />
<hkern u1="t" u2="\" k="27" />
<hkern u1="t" u2="V" k="8" />
<hkern u1="u" u2="&#x2122;" k="20" />
<hkern u1="u" u2="&#x7d;" k="20" />
<hkern u1="u" u2="]" k="23" />
<hkern u1="u" u2="\" k="53" />
<hkern u1="u" u2="X" k="8" />
<hkern u1="u" u2="V" k="47" />
<hkern u1="u" u2="&#x3f;" k="10" />
<hkern u1="u" u2="&#x29;" k="16" />
<hkern u1="v" u2="&#x2039;" k="23" />
<hkern u1="v" u2="&#x2026;" k="57" />
<hkern u1="v" u2="&#x201e;" k="57" />
<hkern u1="v" u2="&#x201a;" k="57" />
<hkern u1="v" u2="&#x2014;" k="23" />
<hkern u1="v" u2="&#x2013;" k="23" />
<hkern u1="v" u2="&#x17b;" k="23" />
<hkern u1="v" u2="&#x179;" k="23" />
<hkern u1="v" u2="&#x178;" k="74" />
<hkern u1="v" u2="&#x15b;" k="12" />
<hkern u1="v" u2="&#x153;" k="16" />
<hkern u1="v" u2="&#x119;" k="16" />
<hkern u1="v" u2="&#x107;" k="16" />
<hkern u1="v" u2="&#x105;" k="14" />
<hkern u1="v" u2="&#x104;" k="33" />
<hkern u1="v" u2="&#xf8;" k="16" />
<hkern u1="v" u2="&#xf6;" k="16" />
<hkern u1="v" u2="&#xf5;" k="16" />
<hkern u1="v" u2="&#xf4;" k="16" />
<hkern u1="v" u2="&#xf3;" k="16" />
<hkern u1="v" u2="&#xf2;" k="16" />
<hkern u1="v" u2="&#xf0;" k="25" />
<hkern u1="v" u2="&#xeb;" k="16" />
<hkern u1="v" u2="&#xea;" k="16" />
<hkern u1="v" u2="&#xe9;" k="16" />
<hkern u1="v" u2="&#xe8;" k="16" />
<hkern u1="v" u2="&#xe7;" k="16" />
<hkern u1="v" u2="&#xe6;" k="14" />
<hkern u1="v" u2="&#xe5;" k="14" />
<hkern u1="v" u2="&#xe4;" k="14" />
<hkern u1="v" u2="&#xe3;" k="14" />
<hkern u1="v" u2="&#xe2;" k="14" />
<hkern u1="v" u2="&#xe1;" k="14" />
<hkern u1="v" u2="&#xe0;" k="14" />
<hkern u1="v" u2="&#xdd;" k="74" />
<hkern u1="v" u2="&#xc6;" k="39" />
<hkern u1="v" u2="&#xc5;" k="33" />
<hkern u1="v" u2="&#xc4;" k="33" />
<hkern u1="v" u2="&#xc3;" k="33" />
<hkern u1="v" u2="&#xc2;" k="33" />
<hkern u1="v" u2="&#xc1;" k="33" />
<hkern u1="v" u2="&#xc0;" k="33" />
<hkern u1="v" u2="&#xab;" k="23" />
<hkern u1="v" u2="&#x7d;" k="39" />
<hkern u1="v" u2="s" k="12" />
<hkern u1="v" u2="q" k="14" />
<hkern u1="v" u2="o" k="16" />
<hkern u1="v" u2="g" k="16" />
<hkern u1="v" u2="e" k="16" />
<hkern u1="v" u2="d" k="14" />
<hkern u1="v" u2="c" k="16" />
<hkern u1="v" u2="a" k="14" />
<hkern u1="v" u2="]" k="51" />
<hkern u1="v" u2="\" k="27" />
<hkern u1="v" u2="Z" k="23" />
<hkern u1="v" u2="Y" k="74" />
<hkern u1="v" u2="X" k="49" />
<hkern u1="v" u2="V" k="14" />
<hkern u1="v" u2="T" k="119" />
<hkern u1="v" u2="J" k="37" />
<hkern u1="v" u2="A" k="33" />
<hkern u1="v" u2="&#x3f;" k="8" />
<hkern u1="v" u2="&#x2f;" k="39" />
<hkern u1="v" u2="&#x2e;" k="57" />
<hkern u1="v" u2="&#x2d;" k="23" />
<hkern u1="v" u2="&#x2c;" k="57" />
<hkern u1="v" u2="&#x29;" k="16" />
<hkern u1="w" u2="&#xf0;" k="16" />
<hkern u1="w" u2="&#xc6;" k="33" />
<hkern u1="w" u2="&#x7d;" k="43" />
<hkern u1="w" u2="]" k="53" />
<hkern u1="w" u2="\" k="27" />
<hkern u1="w" u2="X" k="49" />
<hkern u1="w" u2="V" k="18" />
<hkern u1="w" u2="&#x3f;" k="8" />
<hkern u1="w" u2="&#x2f;" k="31" />
<hkern u1="w" u2="&#x29;" k="20" />
<hkern u1="x" u2="&#x2039;" k="47" />
<hkern u1="x" u2="&#x2014;" k="55" />
<hkern u1="x" u2="&#x2013;" k="55" />
<hkern u1="x" u2="&#x178;" k="72" />
<hkern u1="x" u2="&#x153;" k="23" />
<hkern u1="x" u2="&#x119;" k="23" />
<hkern u1="x" u2="&#x107;" k="23" />
<hkern u1="x" u2="&#xf8;" k="23" />
<hkern u1="x" u2="&#xf6;" k="23" />
<hkern u1="x" u2="&#xf5;" k="23" />
<hkern u1="x" u2="&#xf4;" k="23" />
<hkern u1="x" u2="&#xf3;" k="23" />
<hkern u1="x" u2="&#xf2;" k="23" />
<hkern u1="x" u2="&#xf0;" k="35" />
<hkern u1="x" u2="&#xeb;" k="23" />
<hkern u1="x" u2="&#xea;" k="23" />
<hkern u1="x" u2="&#xe9;" k="23" />
<hkern u1="x" u2="&#xe8;" k="23" />
<hkern u1="x" u2="&#xe7;" k="23" />
<hkern u1="x" u2="&#xdd;" k="72" />
<hkern u1="x" u2="&#xab;" k="47" />
<hkern u1="x" u2="&#x7d;" k="14" />
<hkern u1="x" u2="q" k="23" />
<hkern u1="x" u2="o" k="23" />
<hkern u1="x" u2="g" k="18" />
<hkern u1="x" u2="e" k="23" />
<hkern u1="x" u2="d" k="23" />
<hkern u1="x" u2="c" k="23" />
<hkern u1="x" u2="]" k="16" />
<hkern u1="x" u2="\" k="23" />
<hkern u1="x" u2="Y" k="72" />
<hkern u1="x" u2="V" k="12" />
<hkern u1="x" u2="T" k="119" />
<hkern u1="x" u2="&#x2d;" k="55" />
<hkern u1="y" u2="&#xf0;" k="27" />
<hkern u1="y" u2="&#xc6;" k="39" />
<hkern u1="y" u2="&#x7d;" k="35" />
<hkern u1="y" u2="]" k="47" />
<hkern u1="y" u2="\" k="27" />
<hkern u1="y" u2="X" k="49" />
<hkern u1="y" u2="V" k="14" />
<hkern u1="y" u2="&#x3f;" k="8" />
<hkern u1="y" u2="&#x2f;" k="41" />
<hkern u1="z" u2="&#xf0;" k="12" />
<hkern u1="z" u2="&#x7d;" k="16" />
<hkern u1="z" u2="]" k="18" />
<hkern u1="z" u2="\" k="39" />
<hkern u1="z" u2="V" k="27" />
<hkern u1="z" u2="&#x3f;" k="8" />
<hkern u1="&#x7b;" u2="&#x17c;" k="16" />
<hkern u1="&#x7b;" u2="&#x17a;" k="16" />
<hkern u1="&#x7b;" u2="&#x15b;" k="20" />
<hkern u1="&#x7b;" u2="&#x15a;" k="10" />
<hkern u1="&#x7b;" u2="&#x153;" k="51" />
<hkern u1="&#x7b;" u2="&#x152;" k="39" />
<hkern u1="&#x7b;" u2="&#x144;" k="20" />
<hkern u1="&#x7b;" u2="&#x119;" k="51" />
<hkern u1="&#x7b;" u2="&#x107;" k="51" />
<hkern u1="&#x7b;" u2="&#x106;" k="35" />
<hkern u1="&#x7b;" u2="&#x105;" k="39" />
<hkern u1="&#x7b;" u2="&#x104;" k="18" />
<hkern u1="&#x7b;" u2="&#xff;" k="37" />
<hkern u1="&#x7b;" u2="&#xfd;" k="37" />
<hkern u1="&#x7b;" u2="&#xfc;" k="45" />
<hkern u1="&#x7b;" u2="&#xfb;" k="45" />
<hkern u1="&#x7b;" u2="&#xfa;" k="45" />
<hkern u1="&#x7b;" u2="&#xf9;" k="45" />
<hkern u1="&#x7b;" u2="&#xf8;" k="51" />
<hkern u1="&#x7b;" u2="&#xf6;" k="51" />
<hkern u1="&#x7b;" u2="&#xf5;" k="51" />
<hkern u1="&#x7b;" u2="&#xf4;" k="51" />
<hkern u1="&#x7b;" u2="&#xf3;" k="51" />
<hkern u1="&#x7b;" u2="&#xf2;" k="51" />
<hkern u1="&#x7b;" u2="&#xf1;" k="20" />
<hkern u1="&#x7b;" u2="&#xf0;" k="8" />
<hkern u1="&#x7b;" u2="&#xef;" k="-55" />
<hkern u1="&#x7b;" u2="&#xec;" k="-86" />
<hkern u1="&#x7b;" u2="&#xeb;" k="51" />
<hkern u1="&#x7b;" u2="&#xea;" k="51" />
<hkern u1="&#x7b;" u2="&#xe9;" k="51" />
<hkern u1="&#x7b;" u2="&#xe8;" k="51" />
<hkern u1="&#x7b;" u2="&#xe7;" k="51" />
<hkern u1="&#x7b;" u2="&#xe6;" k="39" />
<hkern u1="&#x7b;" u2="&#xe5;" k="39" />
<hkern u1="&#x7b;" u2="&#xe4;" k="39" />
<hkern u1="&#x7b;" u2="&#xe3;" k="39" />
<hkern u1="&#x7b;" u2="&#xe2;" k="39" />
<hkern u1="&#x7b;" u2="&#xe1;" k="39" />
<hkern u1="&#x7b;" u2="&#xe0;" k="39" />
<hkern u1="&#x7b;" u2="&#xd8;" k="39" />
<hkern u1="&#x7b;" u2="&#xd6;" k="39" />
<hkern u1="&#x7b;" u2="&#xd5;" k="39" />
<hkern u1="&#x7b;" u2="&#xd4;" k="39" />
<hkern u1="&#x7b;" u2="&#xd3;" k="39" />
<hkern u1="&#x7b;" u2="&#xd2;" k="39" />
<hkern u1="&#x7b;" u2="&#xc7;" k="35" />
<hkern u1="&#x7b;" u2="&#xc6;" k="18" />
<hkern u1="&#x7b;" u2="&#xc5;" k="18" />
<hkern u1="&#x7b;" u2="&#xc4;" k="18" />
<hkern u1="&#x7b;" u2="&#xc3;" k="18" />
<hkern u1="&#x7b;" u2="&#xc2;" k="18" />
<hkern u1="&#x7b;" u2="&#xc1;" k="18" />
<hkern u1="&#x7b;" u2="&#xc0;" k="18" />
<hkern u1="&#x7b;" u2="&#x7b;" k="31" />
<hkern u1="&#x7b;" u2="z" k="16" />
<hkern u1="&#x7b;" u2="y" k="37" />
<hkern u1="&#x7b;" u2="x" k="14" />
<hkern u1="&#x7b;" u2="w" k="43" />
<hkern u1="&#x7b;" u2="v" k="37" />
<hkern u1="&#x7b;" u2="u" k="45" />
<hkern u1="&#x7b;" u2="t" k="16" />
<hkern u1="&#x7b;" u2="s" k="20" />
<hkern u1="&#x7b;" u2="r" k="20" />
<hkern u1="&#x7b;" u2="q" k="51" />
<hkern u1="&#x7b;" u2="p" k="20" />
<hkern u1="&#x7b;" u2="o" k="51" />
<hkern u1="&#x7b;" u2="n" k="20" />
<hkern u1="&#x7b;" u2="m" k="20" />
<hkern u1="&#x7b;" u2="j" k="-33" />
<hkern u1="&#x7b;" u2="f" k="10" />
<hkern u1="&#x7b;" u2="e" k="51" />
<hkern u1="&#x7b;" u2="d" k="51" />
<hkern u1="&#x7b;" u2="c" k="51" />
<hkern u1="&#x7b;" u2="a" k="39" />
<hkern u1="&#x7b;" u2="S" k="10" />
<hkern u1="&#x7b;" u2="Q" k="39" />
<hkern u1="&#x7b;" u2="O" k="39" />
<hkern u1="&#x7b;" u2="G" k="39" />
<hkern u1="&#x7b;" u2="C" k="35" />
<hkern u1="&#x7b;" u2="A" k="18" />
<hkern u1="&#x7b;" u2="&#x28;" k="8" />
<hkern u1="&#x7c;" u2="&#xec;" k="-23" />
<hkern u1="&#x7d;" u2="&#x7d;" k="31" />
<hkern u1="&#x7d;" u2="]" k="39" />
<hkern u1="&#x7d;" u2="&#x29;" k="25" />
<hkern u1="&#xa1;" u2="&#x178;" k="59" />
<hkern u1="&#xa1;" u2="&#xdd;" k="59" />
<hkern u1="&#xa1;" u2="Y" k="59" />
<hkern u1="&#xa1;" u2="V" k="6" />
<hkern u1="&#xa1;" u2="T" k="78" />
<hkern u1="&#xab;" u2="V" k="31" />
<hkern u1="&#xae;" u2="&#x17b;" k="12" />
<hkern u1="&#xae;" u2="&#x179;" k="12" />
<hkern u1="&#xae;" u2="&#x178;" k="43" />
<hkern u1="&#xae;" u2="&#x104;" k="47" />
<hkern u1="&#xae;" u2="&#xdd;" k="43" />
<hkern u1="&#xae;" u2="&#xc6;" k="59" />
<hkern u1="&#xae;" u2="&#xc5;" k="47" />
<hkern u1="&#xae;" u2="&#xc4;" k="47" />
<hkern u1="&#xae;" u2="&#xc3;" k="47" />
<hkern u1="&#xae;" u2="&#xc2;" k="47" />
<hkern u1="&#xae;" u2="&#xc1;" k="47" />
<hkern u1="&#xae;" u2="&#xc0;" k="47" />
<hkern u1="&#xae;" u2="Z" k="12" />
<hkern u1="&#xae;" u2="Y" k="43" />
<hkern u1="&#xae;" u2="X" k="8" />
<hkern u1="&#xae;" u2="V" k="6" />
<hkern u1="&#xae;" u2="T" k="8" />
<hkern u1="&#xae;" u2="J" k="37" />
<hkern u1="&#xae;" u2="A" k="47" />
<hkern u1="&#xb7;" u2="&#x142;" k="123" />
<hkern u1="&#xb7;" u2="l" k="123" />
<hkern u1="&#xbb;" u2="&#x141;" k="-10" />
<hkern u1="&#xbb;" u2="&#xc6;" k="6" />
<hkern u1="&#xbb;" u2="x" k="47" />
<hkern u1="&#xbb;" u2="v" k="6" />
<hkern u1="&#xbb;" u2="f" k="14" />
<hkern u1="&#xbb;" u2="X" k="45" />
<hkern u1="&#xbb;" u2="V" k="51" />
<hkern u1="&#xbf;" u2="&#x17c;" k="47" />
<hkern u1="&#xbf;" u2="&#x17b;" k="51" />
<hkern u1="&#xbf;" u2="&#x17a;" k="47" />
<hkern u1="&#xbf;" u2="&#x179;" k="51" />
<hkern u1="&#xbf;" u2="&#x178;" k="123" />
<hkern u1="&#xbf;" u2="&#x15b;" k="51" />
<hkern u1="&#xbf;" u2="&#x15a;" k="39" />
<hkern u1="&#xbf;" u2="&#x153;" k="55" />
<hkern u1="&#xbf;" u2="&#x152;" k="45" />
<hkern u1="&#xbf;" u2="&#x144;" k="49" />
<hkern u1="&#xbf;" u2="&#x143;" k="41" />
<hkern u1="&#xbf;" u2="&#x142;" k="49" />
<hkern u1="&#xbf;" u2="&#x141;" k="41" />
<hkern u1="&#xbf;" u2="&#x131;" k="49" />
<hkern u1="&#xbf;" u2="&#x119;" k="55" />
<hkern u1="&#xbf;" u2="&#x118;" k="41" />
<hkern u1="&#xbf;" u2="&#x107;" k="55" />
<hkern u1="&#xbf;" u2="&#x106;" k="43" />
<hkern u1="&#xbf;" u2="&#x105;" k="53" />
<hkern u1="&#xbf;" u2="&#x104;" k="53" />
<hkern u1="&#xbf;" u2="&#xff;" k="55" />
<hkern u1="&#xbf;" u2="&#xfe;" k="49" />
<hkern u1="&#xbf;" u2="&#xfd;" k="55" />
<hkern u1="&#xbf;" u2="&#xfc;" k="51" />
<hkern u1="&#xbf;" u2="&#xfb;" k="51" />
<hkern u1="&#xbf;" u2="&#xfa;" k="51" />
<hkern u1="&#xbf;" u2="&#xf9;" k="51" />
<hkern u1="&#xbf;" u2="&#xf8;" k="55" />
<hkern u1="&#xbf;" u2="&#xf6;" k="55" />
<hkern u1="&#xbf;" u2="&#xf5;" k="55" />
<hkern u1="&#xbf;" u2="&#xf4;" k="55" />
<hkern u1="&#xbf;" u2="&#xf3;" k="55" />
<hkern u1="&#xbf;" u2="&#xf2;" k="55" />
<hkern u1="&#xbf;" u2="&#xf1;" k="49" />
<hkern u1="&#xbf;" u2="&#xf0;" k="57" />
<hkern u1="&#xbf;" u2="&#xef;" k="49" />
<hkern u1="&#xbf;" u2="&#xee;" k="49" />
<hkern u1="&#xbf;" u2="&#xed;" k="49" />
<hkern u1="&#xbf;" u2="&#xec;" k="49" />
<hkern u1="&#xbf;" u2="&#xeb;" k="55" />
<hkern u1="&#xbf;" u2="&#xea;" k="55" />
<hkern u1="&#xbf;" u2="&#xe9;" k="55" />
<hkern u1="&#xbf;" u2="&#xe8;" k="55" />
<hkern u1="&#xbf;" u2="&#xe7;" k="55" />
<hkern u1="&#xbf;" u2="&#xe6;" k="53" />
<hkern u1="&#xbf;" u2="&#xe5;" k="53" />
<hkern u1="&#xbf;" u2="&#xe4;" k="53" />
<hkern u1="&#xbf;" u2="&#xe3;" k="53" />
<hkern u1="&#xbf;" u2="&#xe2;" k="53" />
<hkern u1="&#xbf;" u2="&#xe1;" k="53" />
<hkern u1="&#xbf;" u2="&#xe0;" k="53" />
<hkern u1="&#xbf;" u2="&#xdf;" k="49" />
<hkern u1="&#xbf;" u2="&#xde;" k="41" />
<hkern u1="&#xbf;" u2="&#xdd;" k="123" />
<hkern u1="&#xbf;" u2="&#xdc;" k="47" />
<hkern u1="&#xbf;" u2="&#xdb;" k="47" />
<hkern u1="&#xbf;" u2="&#xda;" k="47" />
<hkern u1="&#xbf;" u2="&#xd9;" k="47" />
<hkern u1="&#xbf;" u2="&#xd8;" k="45" />
<hkern u1="&#xbf;" u2="&#xd6;" k="45" />
<hkern u1="&#xbf;" u2="&#xd5;" k="45" />
<hkern u1="&#xbf;" u2="&#xd4;" k="45" />
<hkern u1="&#xbf;" u2="&#xd3;" k="45" />
<hkern u1="&#xbf;" u2="&#xd2;" k="45" />
<hkern u1="&#xbf;" u2="&#xd1;" k="41" />
<hkern u1="&#xbf;" u2="&#xd0;" k="41" />
<hkern u1="&#xbf;" u2="&#xcf;" k="41" />
<hkern u1="&#xbf;" u2="&#xce;" k="41" />
<hkern u1="&#xbf;" u2="&#xcd;" k="41" />
<hkern u1="&#xbf;" u2="&#xcc;" k="41" />
<hkern u1="&#xbf;" u2="&#xcb;" k="41" />
<hkern u1="&#xbf;" u2="&#xca;" k="41" />
<hkern u1="&#xbf;" u2="&#xc9;" k="41" />
<hkern u1="&#xbf;" u2="&#xc8;" k="41" />
<hkern u1="&#xbf;" u2="&#xc7;" k="43" />
<hkern u1="&#xbf;" u2="&#xc6;" k="55" />
<hkern u1="&#xbf;" u2="&#xc5;" k="53" />
<hkern u1="&#xbf;" u2="&#xc4;" k="53" />
<hkern u1="&#xbf;" u2="&#xc3;" k="53" />
<hkern u1="&#xbf;" u2="&#xc2;" k="53" />
<hkern u1="&#xbf;" u2="&#xc1;" k="53" />
<hkern u1="&#xbf;" u2="&#xc0;" k="53" />
<hkern u1="&#xbf;" u2="z" k="47" />
<hkern u1="&#xbf;" u2="y" k="55" />
<hkern u1="&#xbf;" u2="x" k="43" />
<hkern u1="&#xbf;" u2="w" k="53" />
<hkern u1="&#xbf;" u2="v" k="57" />
<hkern u1="&#xbf;" u2="u" k="51" />
<hkern u1="&#xbf;" u2="t" k="49" />
<hkern u1="&#xbf;" u2="s" k="51" />
<hkern u1="&#xbf;" u2="r" k="49" />
<hkern u1="&#xbf;" u2="q" k="55" />
<hkern u1="&#xbf;" u2="p" k="49" />
<hkern u1="&#xbf;" u2="o" k="55" />
<hkern u1="&#xbf;" u2="n" k="49" />
<hkern u1="&#xbf;" u2="m" k="49" />
<hkern u1="&#xbf;" u2="l" k="49" />
<hkern u1="&#xbf;" u2="k" k="49" />
<hkern u1="&#xbf;" u2="j" k="49" />
<hkern u1="&#xbf;" u2="i" k="49" />
<hkern u1="&#xbf;" u2="h" k="49" />
<hkern u1="&#xbf;" u2="f" k="47" />
<hkern u1="&#xbf;" u2="e" k="55" />
<hkern u1="&#xbf;" u2="d" k="55" />
<hkern u1="&#xbf;" u2="c" k="55" />
<hkern u1="&#xbf;" u2="b" k="49" />
<hkern u1="&#xbf;" u2="a" k="53" />
<hkern u1="&#xbf;" u2="Z" k="51" />
<hkern u1="&#xbf;" u2="Y" k="123" />
<hkern u1="&#xbf;" u2="X" k="53" />
<hkern u1="&#xbf;" u2="W" k="63" />
<hkern u1="&#xbf;" u2="V" k="78" />
<hkern u1="&#xbf;" u2="U" k="47" />
<hkern u1="&#xbf;" u2="T" k="139" />
<hkern u1="&#xbf;" u2="S" k="39" />
<hkern u1="&#xbf;" u2="R" k="41" />
<hkern u1="&#xbf;" u2="Q" k="45" />
<hkern u1="&#xbf;" u2="P" k="41" />
<hkern u1="&#xbf;" u2="O" k="45" />
<hkern u1="&#xbf;" u2="N" k="41" />
<hkern u1="&#xbf;" u2="M" k="41" />
<hkern u1="&#xbf;" u2="L" k="41" />
<hkern u1="&#xbf;" u2="K" k="41" />
<hkern u1="&#xbf;" u2="J" k="12" />
<hkern u1="&#xbf;" u2="I" k="41" />
<hkern u1="&#xbf;" u2="H" k="41" />
<hkern u1="&#xbf;" u2="G" k="45" />
<hkern u1="&#xbf;" u2="F" k="41" />
<hkern u1="&#xbf;" u2="E" k="41" />
<hkern u1="&#xbf;" u2="D" k="41" />
<hkern u1="&#xbf;" u2="C" k="43" />
<hkern u1="&#xbf;" u2="B" k="41" />
<hkern u1="&#xbf;" u2="A" k="53" />
<hkern u1="&#xc0;" u2="&#x2122;" k="72" />
<hkern u1="&#xc0;" u2="&#xf0;" k="10" />
<hkern u1="&#xc0;" u2="&#xae;" k="43" />
<hkern u1="&#xc0;" u2="&#x7d;" k="18" />
<hkern u1="&#xc0;" u2="v" k="33" />
<hkern u1="&#xc0;" u2="f" k="16" />
<hkern u1="&#xc0;" u2="]" k="20" />
<hkern u1="&#xc0;" u2="\" k="90" />
<hkern u1="&#xc0;" u2="V" k="57" />
<hkern u1="&#xc0;" u2="&#x3f;" k="39" />
<hkern u1="&#xc0;" u2="&#x2a;" k="63" />
<hkern u1="&#xc1;" u2="&#x2122;" k="72" />
<hkern u1="&#xc1;" u2="&#xf0;" k="10" />
<hkern u1="&#xc1;" u2="&#xae;" k="43" />
<hkern u1="&#xc1;" u2="&#x7d;" k="18" />
<hkern u1="&#xc1;" u2="v" k="33" />
<hkern u1="&#xc1;" u2="f" k="16" />
<hkern u1="&#xc1;" u2="]" k="20" />
<hkern u1="&#xc1;" u2="\" k="90" />
<hkern u1="&#xc1;" u2="V" k="57" />
<hkern u1="&#xc1;" u2="&#x3f;" k="39" />
<hkern u1="&#xc1;" u2="&#x2a;" k="63" />
<hkern u1="&#xc2;" u2="&#x2122;" k="72" />
<hkern u1="&#xc2;" u2="&#xf0;" k="10" />
<hkern u1="&#xc2;" u2="&#xae;" k="43" />
<hkern u1="&#xc2;" u2="&#x7d;" k="18" />
<hkern u1="&#xc2;" u2="v" k="33" />
<hkern u1="&#xc2;" u2="f" k="16" />
<hkern u1="&#xc2;" u2="]" k="20" />
<hkern u1="&#xc2;" u2="\" k="90" />
<hkern u1="&#xc2;" u2="V" k="57" />
<hkern u1="&#xc2;" u2="&#x3f;" k="39" />
<hkern u1="&#xc2;" u2="&#x2a;" k="63" />
<hkern u1="&#xc3;" u2="&#x2122;" k="72" />
<hkern u1="&#xc3;" u2="&#xf0;" k="10" />
<hkern u1="&#xc3;" u2="&#xae;" k="43" />
<hkern u1="&#xc3;" u2="&#x7d;" k="18" />
<hkern u1="&#xc3;" u2="v" k="33" />
<hkern u1="&#xc3;" u2="f" k="16" />
<hkern u1="&#xc3;" u2="]" k="20" />
<hkern u1="&#xc3;" u2="\" k="90" />
<hkern u1="&#xc3;" u2="V" k="57" />
<hkern u1="&#xc3;" u2="&#x3f;" k="39" />
<hkern u1="&#xc3;" u2="&#x2a;" k="63" />
<hkern u1="&#xc4;" u2="&#x2122;" k="72" />
<hkern u1="&#xc4;" u2="&#xf0;" k="10" />
<hkern u1="&#xc4;" u2="&#xae;" k="43" />
<hkern u1="&#xc4;" u2="&#x7d;" k="18" />
<hkern u1="&#xc4;" u2="v" k="33" />
<hkern u1="&#xc4;" u2="f" k="16" />
<hkern u1="&#xc4;" u2="]" k="20" />
<hkern u1="&#xc4;" u2="\" k="90" />
<hkern u1="&#xc4;" u2="V" k="57" />
<hkern u1="&#xc4;" u2="&#x3f;" k="39" />
<hkern u1="&#xc4;" u2="&#x2a;" k="63" />
<hkern u1="&#xc5;" u2="&#x2122;" k="72" />
<hkern u1="&#xc5;" u2="&#xf0;" k="10" />
<hkern u1="&#xc5;" u2="&#xae;" k="43" />
<hkern u1="&#xc5;" u2="&#x7d;" k="18" />
<hkern u1="&#xc5;" u2="v" k="33" />
<hkern u1="&#xc5;" u2="f" k="16" />
<hkern u1="&#xc5;" u2="]" k="20" />
<hkern u1="&#xc5;" u2="\" k="90" />
<hkern u1="&#xc5;" u2="V" k="57" />
<hkern u1="&#xc5;" u2="&#x3f;" k="39" />
<hkern u1="&#xc5;" u2="&#x2a;" k="63" />
<hkern u1="&#xc6;" u2="&#xf0;" k="14" />
<hkern u1="&#xc6;" u2="&#xef;" k="-49" />
<hkern u1="&#xc6;" u2="&#xee;" k="-43" />
<hkern u1="&#xc6;" u2="&#xec;" k="-80" />
<hkern u1="&#xc6;" u2="v" k="10" />
<hkern u1="&#xc7;" u2="&#xf0;" k="14" />
<hkern u1="&#xc7;" u2="&#xef;" k="-53" />
<hkern u1="&#xc7;" u2="&#xee;" k="-43" />
<hkern u1="&#xc7;" u2="&#xec;" k="-80" />
<hkern u1="&#xc7;" u2="&#xae;" k="10" />
<hkern u1="&#xc7;" u2="v" k="20" />
<hkern u1="&#xc7;" u2="f" k="10" />
<hkern u1="&#xc8;" u2="&#xf0;" k="14" />
<hkern u1="&#xc8;" u2="&#xef;" k="-49" />
<hkern u1="&#xc8;" u2="&#xee;" k="-43" />
<hkern u1="&#xc8;" u2="&#xec;" k="-80" />
<hkern u1="&#xc8;" u2="v" k="10" />
<hkern u1="&#xc9;" u2="&#xf0;" k="14" />
<hkern u1="&#xc9;" u2="&#xef;" k="-49" />
<hkern u1="&#xc9;" u2="&#xee;" k="-43" />
<hkern u1="&#xc9;" u2="&#xec;" k="-80" />
<hkern u1="&#xc9;" u2="v" k="10" />
<hkern u1="&#xca;" u2="&#xf0;" k="14" />
<hkern u1="&#xca;" u2="&#xef;" k="-49" />
<hkern u1="&#xca;" u2="&#xee;" k="-43" />
<hkern u1="&#xca;" u2="&#xec;" k="-80" />
<hkern u1="&#xca;" u2="v" k="10" />
<hkern u1="&#xcb;" u2="&#xf0;" k="14" />
<hkern u1="&#xcb;" u2="&#xef;" k="-49" />
<hkern u1="&#xcb;" u2="&#xee;" k="-43" />
<hkern u1="&#xcb;" u2="&#xec;" k="-80" />
<hkern u1="&#xcb;" u2="v" k="10" />
<hkern u1="&#xcc;" u2="&#xf0;" k="12" />
<hkern u1="&#xcc;" u2="&#xec;" k="-12" />
<hkern u1="&#xcc;" u2="f" k="8" />
<hkern u1="&#xcd;" u2="&#xf0;" k="12" />
<hkern u1="&#xcd;" u2="&#xec;" k="-12" />
<hkern u1="&#xcd;" u2="f" k="8" />
<hkern u1="&#xce;" u2="&#xf0;" k="12" />
<hkern u1="&#xce;" u2="&#xec;" k="-12" />
<hkern u1="&#xce;" u2="f" k="8" />
<hkern u1="&#xcf;" u2="&#xf0;" k="12" />
<hkern u1="&#xcf;" u2="&#xec;" k="-12" />
<hkern u1="&#xcf;" u2="f" k="8" />
<hkern u1="&#xd0;" u2="&#xc6;" k="31" />
<hkern u1="&#xd0;" u2="&#x7d;" k="37" />
<hkern u1="&#xd0;" u2="]" k="47" />
<hkern u1="&#xd0;" u2="\" k="27" />
<hkern u1="&#xd0;" u2="X" k="39" />
<hkern u1="&#xd0;" u2="V" k="23" />
<hkern u1="&#xd0;" u2="&#x3f;" k="8" />
<hkern u1="&#xd0;" u2="&#x2f;" k="23" />
<hkern u1="&#xd0;" u2="&#x29;" k="25" />
<hkern u1="&#xd1;" u2="&#xf0;" k="12" />
<hkern u1="&#xd1;" u2="&#xec;" k="-12" />
<hkern u1="&#xd1;" u2="f" k="8" />
<hkern u1="&#xd2;" u2="&#xc6;" k="29" />
<hkern u1="&#xd2;" u2="&#x7d;" k="35" />
<hkern u1="&#xd2;" u2="]" k="49" />
<hkern u1="&#xd2;" u2="\" k="29" />
<hkern u1="&#xd2;" u2="X" k="37" />
<hkern u1="&#xd2;" u2="V" k="23" />
<hkern u1="&#xd2;" u2="&#x3f;" k="6" />
<hkern u1="&#xd2;" u2="&#x2f;" k="20" />
<hkern u1="&#xd2;" u2="&#x29;" k="20" />
<hkern u1="&#xd3;" u2="&#xc6;" k="29" />
<hkern u1="&#xd3;" u2="&#x7d;" k="35" />
<hkern u1="&#xd3;" u2="]" k="49" />
<hkern u1="&#xd3;" u2="\" k="29" />
<hkern u1="&#xd3;" u2="X" k="37" />
<hkern u1="&#xd3;" u2="V" k="23" />
<hkern u1="&#xd3;" u2="&#x3f;" k="6" />
<hkern u1="&#xd3;" u2="&#x2f;" k="20" />
<hkern u1="&#xd3;" u2="&#x29;" k="20" />
<hkern u1="&#xd4;" u2="&#xc6;" k="29" />
<hkern u1="&#xd4;" u2="&#x7d;" k="35" />
<hkern u1="&#xd4;" u2="]" k="49" />
<hkern u1="&#xd4;" u2="\" k="29" />
<hkern u1="&#xd4;" u2="X" k="37" />
<hkern u1="&#xd4;" u2="V" k="23" />
<hkern u1="&#xd4;" u2="&#x3f;" k="6" />
<hkern u1="&#xd4;" u2="&#x2f;" k="20" />
<hkern u1="&#xd4;" u2="&#x29;" k="20" />
<hkern u1="&#xd5;" u2="&#xc6;" k="29" />
<hkern u1="&#xd5;" u2="&#x7d;" k="35" />
<hkern u1="&#xd5;" u2="]" k="49" />
<hkern u1="&#xd5;" u2="\" k="29" />
<hkern u1="&#xd5;" u2="X" k="37" />
<hkern u1="&#xd5;" u2="V" k="23" />
<hkern u1="&#xd5;" u2="&#x3f;" k="6" />
<hkern u1="&#xd5;" u2="&#x2f;" k="20" />
<hkern u1="&#xd5;" u2="&#x29;" k="20" />
<hkern u1="&#xd6;" u2="&#xc6;" k="29" />
<hkern u1="&#xd6;" u2="&#x7d;" k="35" />
<hkern u1="&#xd6;" u2="]" k="49" />
<hkern u1="&#xd6;" u2="\" k="29" />
<hkern u1="&#xd6;" u2="X" k="37" />
<hkern u1="&#xd6;" u2="V" k="23" />
<hkern u1="&#xd6;" u2="&#x3f;" k="6" />
<hkern u1="&#xd6;" u2="&#x2f;" k="20" />
<hkern u1="&#xd6;" u2="&#x29;" k="20" />
<hkern u1="&#xd8;" u2="&#xc6;" k="29" />
<hkern u1="&#xd8;" u2="&#x7d;" k="35" />
<hkern u1="&#xd8;" u2="]" k="49" />
<hkern u1="&#xd8;" u2="\" k="29" />
<hkern u1="&#xd8;" u2="X" k="37" />
<hkern u1="&#xd8;" u2="V" k="23" />
<hkern u1="&#xd8;" u2="&#x3f;" k="6" />
<hkern u1="&#xd8;" u2="&#x2f;" k="20" />
<hkern u1="&#xd8;" u2="&#x29;" k="20" />
<hkern u1="&#xd9;" u2="&#xf0;" k="12" />
<hkern u1="&#xd9;" u2="&#xec;" k="-20" />
<hkern u1="&#xd9;" u2="&#xc6;" k="16" />
<hkern u1="&#xd9;" u2="f" k="8" />
<hkern u1="&#xd9;" u2="&#x2f;" k="25" />
<hkern u1="&#xda;" u2="&#xf0;" k="12" />
<hkern u1="&#xda;" u2="&#xec;" k="-20" />
<hkern u1="&#xda;" u2="&#xc6;" k="16" />
<hkern u1="&#xda;" u2="f" k="8" />
<hkern u1="&#xda;" u2="&#x2f;" k="25" />
<hkern u1="&#xdb;" u2="&#xf0;" k="12" />
<hkern u1="&#xdb;" u2="&#xec;" k="-20" />
<hkern u1="&#xdb;" u2="&#xc6;" k="16" />
<hkern u1="&#xdb;" u2="f" k="8" />
<hkern u1="&#xdb;" u2="&#x2f;" k="25" />
<hkern u1="&#xdc;" u2="&#xf0;" k="12" />
<hkern u1="&#xdc;" u2="&#xec;" k="-20" />
<hkern u1="&#xdc;" u2="&#xc6;" k="16" />
<hkern u1="&#xdc;" u2="f" k="8" />
<hkern u1="&#xdc;" u2="&#x2f;" k="25" />
<hkern u1="&#xdd;" u2="&#x142;" k="12" />
<hkern u1="&#xdd;" u2="&#x131;" k="123" />
<hkern u1="&#xdd;" u2="&#xff;" k="57" />
<hkern u1="&#xdd;" u2="&#xf0;" k="66" />
<hkern u1="&#xdd;" u2="&#xef;" k="-121" />
<hkern u1="&#xdd;" u2="&#xee;" k="-33" />
<hkern u1="&#xdd;" u2="&#xec;" k="-129" />
<hkern u1="&#xdd;" u2="&#xeb;" k="131" />
<hkern u1="&#xdd;" u2="&#xe4;" k="100" />
<hkern u1="&#xdd;" u2="&#xe3;" k="86" />
<hkern u1="&#xdd;" u2="&#xdf;" k="20" />
<hkern u1="&#xdd;" u2="&#xc6;" k="115" />
<hkern u1="&#xdd;" u2="&#xae;" k="41" />
<hkern u1="&#xdd;" u2="x" k="76" />
<hkern u1="&#xdd;" u2="v" k="74" />
<hkern u1="&#xdd;" u2="f" k="43" />
<hkern u1="&#xdd;" u2="&#x40;" k="74" />
<hkern u1="&#xdd;" u2="&#x2f;" k="133" />
<hkern u1="&#xdd;" u2="&#x2a;" k="-8" />
<hkern u1="&#xdd;" u2="&#x26;" k="66" />
<hkern u1="&#xde;" u2="&#x2122;" k="6" />
<hkern u1="&#xde;" u2="&#x2026;" k="49" />
<hkern u1="&#xde;" u2="&#x201e;" k="49" />
<hkern u1="&#xde;" u2="&#x201a;" k="49" />
<hkern u1="&#xde;" u2="&#x17b;" k="29" />
<hkern u1="&#xde;" u2="&#x179;" k="29" />
<hkern u1="&#xde;" u2="&#x178;" k="76" />
<hkern u1="&#xde;" u2="&#x104;" k="33" />
<hkern u1="&#xde;" u2="&#xdd;" k="76" />
<hkern u1="&#xde;" u2="&#xc6;" k="39" />
<hkern u1="&#xde;" u2="&#xc5;" k="33" />
<hkern u1="&#xde;" u2="&#xc4;" k="33" />
<hkern u1="&#xde;" u2="&#xc3;" k="33" />
<hkern u1="&#xde;" u2="&#xc2;" k="33" />
<hkern u1="&#xde;" u2="&#xc1;" k="33" />
<hkern u1="&#xde;" u2="&#xc0;" k="33" />
<hkern u1="&#xde;" u2="&#x7d;" k="47" />
<hkern u1="&#xde;" u2="]" k="66" />
<hkern u1="&#xde;" u2="\" k="43" />
<hkern u1="&#xde;" u2="Z" k="29" />
<hkern u1="&#xde;" u2="Y" k="76" />
<hkern u1="&#xde;" u2="X" k="74" />
<hkern u1="&#xde;" u2="W" k="12" />
<hkern u1="&#xde;" u2="V" k="29" />
<hkern u1="&#xde;" u2="T" k="66" />
<hkern u1="&#xde;" u2="J" k="29" />
<hkern u1="&#xde;" u2="A" k="33" />
<hkern u1="&#xde;" u2="&#x3f;" k="16" />
<hkern u1="&#xde;" u2="&#x2f;" k="43" />
<hkern u1="&#xde;" u2="&#x2e;" k="49" />
<hkern u1="&#xde;" u2="&#x2c;" k="49" />
<hkern u1="&#xde;" u2="&#x29;" k="33" />
<hkern u1="&#xdf;" u2="&#x2122;" k="20" />
<hkern u1="&#xdf;" u2="&#x201d;" k="27" />
<hkern u1="&#xdf;" u2="&#x201c;" k="31" />
<hkern u1="&#xdf;" u2="&#x2019;" k="27" />
<hkern u1="&#xdf;" u2="&#x2018;" k="31" />
<hkern u1="&#xdf;" u2="&#x178;" k="78" />
<hkern u1="&#xdf;" u2="&#xff;" k="31" />
<hkern u1="&#xdf;" u2="&#xfd;" k="31" />
<hkern u1="&#xdf;" u2="&#xdd;" k="78" />
<hkern u1="&#xdf;" u2="&#xdc;" k="8" />
<hkern u1="&#xdf;" u2="&#xdb;" k="8" />
<hkern u1="&#xdf;" u2="&#xda;" k="8" />
<hkern u1="&#xdf;" u2="&#xd9;" k="8" />
<hkern u1="&#xdf;" u2="&#xae;" k="25" />
<hkern u1="&#xdf;" u2="&#x7d;" k="14" />
<hkern u1="&#xdf;" u2="y" k="31" />
<hkern u1="&#xdf;" u2="x" k="12" />
<hkern u1="&#xdf;" u2="w" k="16" />
<hkern u1="&#xdf;" u2="v" k="27" />
<hkern u1="&#xdf;" u2="t" k="14" />
<hkern u1="&#xdf;" u2="g" k="10" />
<hkern u1="&#xdf;" u2="f" k="12" />
<hkern u1="&#xdf;" u2="]" k="31" />
<hkern u1="&#xdf;" u2="\" k="41" />
<hkern u1="&#xdf;" u2="Y" k="78" />
<hkern u1="&#xdf;" u2="X" k="10" />
<hkern u1="&#xdf;" u2="W" k="33" />
<hkern u1="&#xdf;" u2="V" k="49" />
<hkern u1="&#xdf;" u2="U" k="8" />
<hkern u1="&#xdf;" u2="T" k="47" />
<hkern u1="&#xdf;" u2="J" k="18" />
<hkern u1="&#xdf;" u2="&#x3f;" k="8" />
<hkern u1="&#xdf;" u2="&#x2a;" k="27" />
<hkern u1="&#xdf;" u2="&#x27;" k="25" />
<hkern u1="&#xdf;" u2="&#x22;" k="25" />
<hkern u1="&#xe0;" u2="&#x2122;" k="29" />
<hkern u1="&#xe0;" u2="&#x7d;" k="8" />
<hkern u1="&#xe0;" u2="v" k="10" />
<hkern u1="&#xe0;" u2="]" k="10" />
<hkern u1="&#xe0;" u2="\" k="82" />
<hkern u1="&#xe0;" u2="V" k="55" />
<hkern u1="&#xe0;" u2="&#x3f;" k="29" />
<hkern u1="&#xe0;" u2="&#x2a;" k="12" />
<hkern u1="&#xe1;" u2="&#x2122;" k="29" />
<hkern u1="&#xe1;" u2="&#x7d;" k="8" />
<hkern u1="&#xe1;" u2="v" k="10" />
<hkern u1="&#xe1;" u2="]" k="10" />
<hkern u1="&#xe1;" u2="\" k="82" />
<hkern u1="&#xe1;" u2="V" k="55" />
<hkern u1="&#xe1;" u2="&#x3f;" k="29" />
<hkern u1="&#xe1;" u2="&#x2a;" k="12" />
<hkern u1="&#xe2;" u2="&#x2122;" k="29" />
<hkern u1="&#xe2;" u2="&#x7d;" k="8" />
<hkern u1="&#xe2;" u2="v" k="10" />
<hkern u1="&#xe2;" u2="]" k="10" />
<hkern u1="&#xe2;" u2="\" k="82" />
<hkern u1="&#xe2;" u2="V" k="55" />
<hkern u1="&#xe2;" u2="&#x3f;" k="29" />
<hkern u1="&#xe2;" u2="&#x2a;" k="12" />
<hkern u1="&#xe3;" u2="&#x2122;" k="29" />
<hkern u1="&#xe3;" u2="&#x7d;" k="8" />
<hkern u1="&#xe3;" u2="v" k="10" />
<hkern u1="&#xe3;" u2="]" k="10" />
<hkern u1="&#xe3;" u2="\" k="82" />
<hkern u1="&#xe3;" u2="V" k="55" />
<hkern u1="&#xe3;" u2="&#x3f;" k="29" />
<hkern u1="&#xe3;" u2="&#x2a;" k="12" />
<hkern u1="&#xe4;" u2="&#x2122;" k="29" />
<hkern u1="&#xe4;" u2="&#x7d;" k="8" />
<hkern u1="&#xe4;" u2="v" k="10" />
<hkern u1="&#xe4;" u2="]" k="10" />
<hkern u1="&#xe4;" u2="\" k="82" />
<hkern u1="&#xe4;" u2="V" k="55" />
<hkern u1="&#xe4;" u2="&#x3f;" k="29" />
<hkern u1="&#xe4;" u2="&#x2a;" k="12" />
<hkern u1="&#xe5;" u2="&#x2122;" k="29" />
<hkern u1="&#xe5;" u2="&#x7d;" k="8" />
<hkern u1="&#xe5;" u2="v" k="10" />
<hkern u1="&#xe5;" u2="]" k="10" />
<hkern u1="&#xe5;" u2="\" k="82" />
<hkern u1="&#xe5;" u2="V" k="55" />
<hkern u1="&#xe5;" u2="&#x3f;" k="29" />
<hkern u1="&#xe5;" u2="&#x2a;" k="12" />
<hkern u1="&#xe6;" u2="&#x2122;" k="27" />
<hkern u1="&#xe6;" u2="&#xc6;" k="10" />
<hkern u1="&#xe6;" u2="&#x7d;" k="37" />
<hkern u1="&#xe6;" u2="v" k="14" />
<hkern u1="&#xe6;" u2="]" k="23" />
<hkern u1="&#xe6;" u2="\" k="74" />
<hkern u1="&#xe6;" u2="X" k="8" />
<hkern u1="&#xe6;" u2="V" k="57" />
<hkern u1="&#xe6;" u2="&#x3f;" k="31" />
<hkern u1="&#xe6;" u2="&#x29;" k="18" />
<hkern u1="&#xe7;" u2="&#xf0;" k="18" />
<hkern u1="&#xe7;" u2="&#x7d;" k="14" />
<hkern u1="&#xe7;" u2="]" k="16" />
<hkern u1="&#xe7;" u2="\" k="37" />
<hkern u1="&#xe7;" u2="V" k="25" />
<hkern u1="&#xe7;" u2="&#x3f;" k="8" />
<hkern u1="&#xe8;" u2="&#x2122;" k="27" />
<hkern u1="&#xe8;" u2="&#xc6;" k="10" />
<hkern u1="&#xe8;" u2="&#x7d;" k="37" />
<hkern u1="&#xe8;" u2="v" k="14" />
<hkern u1="&#xe8;" u2="]" k="23" />
<hkern u1="&#xe8;" u2="\" k="74" />
<hkern u1="&#xe8;" u2="X" k="8" />
<hkern u1="&#xe8;" u2="V" k="57" />
<hkern u1="&#xe8;" u2="&#x3f;" k="31" />
<hkern u1="&#xe8;" u2="&#x29;" k="18" />
<hkern u1="&#xe9;" u2="&#x2122;" k="27" />
<hkern u1="&#xe9;" u2="&#xc6;" k="10" />
<hkern u1="&#xe9;" u2="&#x7d;" k="37" />
<hkern u1="&#xe9;" u2="v" k="14" />
<hkern u1="&#xe9;" u2="]" k="23" />
<hkern u1="&#xe9;" u2="\" k="74" />
<hkern u1="&#xe9;" u2="X" k="8" />
<hkern u1="&#xe9;" u2="V" k="57" />
<hkern u1="&#xe9;" u2="&#x3f;" k="31" />
<hkern u1="&#xe9;" u2="&#x29;" k="18" />
<hkern u1="&#xea;" u2="&#x2122;" k="27" />
<hkern u1="&#xea;" u2="&#xc6;" k="10" />
<hkern u1="&#xea;" u2="&#x7d;" k="37" />
<hkern u1="&#xea;" u2="v" k="14" />
<hkern u1="&#xea;" u2="]" k="23" />
<hkern u1="&#xea;" u2="\" k="74" />
<hkern u1="&#xea;" u2="X" k="8" />
<hkern u1="&#xea;" u2="V" k="57" />
<hkern u1="&#xea;" u2="&#x3f;" k="31" />
<hkern u1="&#xea;" u2="&#x29;" k="18" />
<hkern u1="&#xeb;" u2="&#x2122;" k="27" />
<hkern u1="&#xeb;" u2="&#xc6;" k="10" />
<hkern u1="&#xeb;" u2="&#x7d;" k="37" />
<hkern u1="&#xeb;" u2="v" k="14" />
<hkern u1="&#xeb;" u2="]" k="23" />
<hkern u1="&#xeb;" u2="\" k="74" />
<hkern u1="&#xeb;" u2="X" k="8" />
<hkern u1="&#xeb;" u2="V" k="57" />
<hkern u1="&#xeb;" u2="&#x3f;" k="31" />
<hkern u1="&#xeb;" u2="&#x29;" k="18" />
<hkern u1="&#xec;" u2="&#xef;" k="-12" />
<hkern u1="&#xec;" u2="&#xec;" k="-27" />
<hkern u1="&#xed;" u2="&#x2122;" k="-55" />
<hkern u1="&#xed;" u2="&#x201d;" k="-12" />
<hkern u1="&#xed;" u2="&#x2019;" k="-12" />
<hkern u1="&#xed;" u2="&#x142;" k="-27" />
<hkern u1="&#xed;" u2="&#x131;" k="-37" />
<hkern u1="&#xed;" u2="&#xfe;" k="-37" />
<hkern u1="&#xed;" u2="&#xef;" k="-12" />
<hkern u1="&#xed;" u2="&#xee;" k="-37" />
<hkern u1="&#xed;" u2="&#xed;" k="-37" />
<hkern u1="&#xed;" u2="&#xec;" k="-27" />
<hkern u1="&#xed;" u2="&#xdf;" k="-37" />
<hkern u1="&#xed;" u2="&#x7d;" k="-94" />
<hkern u1="&#xed;" u2="&#x7c;" k="-33" />
<hkern u1="&#xed;" u2="l" k="-27" />
<hkern u1="&#xed;" u2="k" k="-37" />
<hkern u1="&#xed;" u2="j" k="-37" />
<hkern u1="&#xed;" u2="i" k="-37" />
<hkern u1="&#xed;" u2="h" k="-37" />
<hkern u1="&#xed;" u2="b" k="-37" />
<hkern u1="&#xed;" u2="]" k="-98" />
<hkern u1="&#xed;" u2="\" k="-96" />
<hkern u1="&#xed;" u2="&#x3f;" k="-102" />
<hkern u1="&#xed;" u2="&#x2a;" k="-74" />
<hkern u1="&#xed;" u2="&#x29;" k="-53" />
<hkern u1="&#xed;" u2="&#x27;" k="-51" />
<hkern u1="&#xed;" u2="&#x22;" k="-51" />
<hkern u1="&#xed;" u2="&#x21;" k="-31" />
<hkern u1="&#xee;" u2="&#x2122;" k="-10" />
<hkern u1="&#xee;" u2="&#xef;" k="-12" />
<hkern u1="&#xee;" u2="&#xec;" k="-27" />
<hkern u1="&#xee;" u2="&#x3f;" k="-39" />
<hkern u1="&#xee;" u2="&#x2a;" k="-55" />
<hkern u1="&#xef;" u2="&#x2122;" k="-14" />
<hkern u1="&#xef;" u2="&#xef;" k="-12" />
<hkern u1="&#xef;" u2="&#xec;" k="-27" />
<hkern u1="&#xef;" u2="&#x7d;" k="-41" />
<hkern u1="&#xef;" u2="]" k="-43" />
<hkern u1="&#xef;" u2="\" k="-53" />
<hkern u1="&#xef;" u2="&#x3f;" k="-51" />
<hkern u1="&#xef;" u2="&#x2a;" k="-68" />
<hkern u1="&#xef;" u2="&#x29;" k="-43" />
<hkern u1="&#xef;" u2="&#x27;" k="-8" />
<hkern u1="&#xef;" u2="&#x22;" k="-8" />
<hkern u1="&#xf0;" u2="&#x2122;" k="6" />
<hkern u1="&#xf0;" u2="&#x2026;" k="8" />
<hkern u1="&#xf0;" u2="&#x201e;" k="8" />
<hkern u1="&#xf0;" u2="&#x201a;" k="8" />
<hkern u1="&#xf0;" u2="&#x17b;" k="25" />
<hkern u1="&#xf0;" u2="&#x179;" k="25" />
<hkern u1="&#xf0;" u2="&#x178;" k="76" />
<hkern u1="&#xf0;" u2="&#x143;" k="10" />
<hkern u1="&#xf0;" u2="&#x141;" k="10" />
<hkern u1="&#xf0;" u2="&#x118;" k="10" />
<hkern u1="&#xf0;" u2="&#x104;" k="16" />
<hkern u1="&#xf0;" u2="&#xff;" k="12" />
<hkern u1="&#xf0;" u2="&#xfd;" k="12" />
<hkern u1="&#xf0;" u2="&#xde;" k="10" />
<hkern u1="&#xf0;" u2="&#xdd;" k="76" />
<hkern u1="&#xf0;" u2="&#xd1;" k="10" />
<hkern u1="&#xf0;" u2="&#xd0;" k="10" />
<hkern u1="&#xf0;" u2="&#xcf;" k="10" />
<hkern u1="&#xf0;" u2="&#xce;" k="10" />
<hkern u1="&#xf0;" u2="&#xcd;" k="10" />
<hkern u1="&#xf0;" u2="&#xcc;" k="10" />
<hkern u1="&#xf0;" u2="&#xcb;" k="10" />
<hkern u1="&#xf0;" u2="&#xca;" k="10" />
<hkern u1="&#xf0;" u2="&#xc9;" k="10" />
<hkern u1="&#xf0;" u2="&#xc8;" k="10" />
<hkern u1="&#xf0;" u2="&#xc6;" k="20" />
<hkern u1="&#xf0;" u2="&#xc5;" k="16" />
<hkern u1="&#xf0;" u2="&#xc4;" k="16" />
<hkern u1="&#xf0;" u2="&#xc3;" k="16" />
<hkern u1="&#xf0;" u2="&#xc2;" k="16" />
<hkern u1="&#xf0;" u2="&#xc1;" k="16" />
<hkern u1="&#xf0;" u2="&#xc0;" k="16" />
<hkern u1="&#xf0;" u2="&#x7d;" k="35" />
<hkern u1="&#xf0;" u2="y" k="12" />
<hkern u1="&#xf0;" u2="x" k="10" />
<hkern u1="&#xf0;" u2="v" k="10" />
<hkern u1="&#xf0;" u2="]" k="43" />
<hkern u1="&#xf0;" u2="\" k="37" />
<hkern u1="&#xf0;" u2="Z" k="25" />
<hkern u1="&#xf0;" u2="Y" k="76" />
<hkern u1="&#xf0;" u2="X" k="57" />
<hkern u1="&#xf0;" u2="W" k="20" />
<hkern u1="&#xf0;" u2="V" k="35" />
<hkern u1="&#xf0;" u2="T" k="70" />
<hkern u1="&#xf0;" u2="R" k="10" />
<hkern u1="&#xf0;" u2="P" k="10" />
<hkern u1="&#xf0;" u2="N" k="10" />
<hkern u1="&#xf0;" u2="M" k="10" />
<hkern u1="&#xf0;" u2="L" k="10" />
<hkern u1="&#xf0;" u2="K" k="10" />
<hkern u1="&#xf0;" u2="J" k="39" />
<hkern u1="&#xf0;" u2="I" k="10" />
<hkern u1="&#xf0;" u2="H" k="10" />
<hkern u1="&#xf0;" u2="F" k="10" />
<hkern u1="&#xf0;" u2="E" k="10" />
<hkern u1="&#xf0;" u2="D" k="10" />
<hkern u1="&#xf0;" u2="B" k="10" />
<hkern u1="&#xf0;" u2="A" k="16" />
<hkern u1="&#xf0;" u2="&#x3f;" k="10" />
<hkern u1="&#xf0;" u2="&#x2f;" k="18" />
<hkern u1="&#xf0;" u2="&#x2e;" k="8" />
<hkern u1="&#xf0;" u2="&#x2c;" k="8" />
<hkern u1="&#xf0;" u2="&#x29;" k="31" />
<hkern u1="&#xf1;" u2="&#x2122;" k="31" />
<hkern u1="&#xf1;" u2="&#x7d;" k="20" />
<hkern u1="&#xf1;" u2="v" k="10" />
<hkern u1="&#xf1;" u2="]" k="23" />
<hkern u1="&#xf1;" u2="\" k="78" />
<hkern u1="&#xf1;" u2="V" k="57" />
<hkern u1="&#xf1;" u2="&#x3f;" k="35" />
<hkern u1="&#xf1;" u2="&#x2a;" k="12" />
<hkern u1="&#xf1;" u2="&#x29;" k="16" />
<hkern u1="&#xf2;" u2="&#x2122;" k="31" />
<hkern u1="&#xf2;" u2="&#xc6;" k="14" />
<hkern u1="&#xf2;" u2="&#x7d;" k="51" />
<hkern u1="&#xf2;" u2="x" k="23" />
<hkern u1="&#xf2;" u2="v" k="16" />
<hkern u1="&#xf2;" u2="]" k="66" />
<hkern u1="&#xf2;" u2="\" k="80" />
<hkern u1="&#xf2;" u2="X" k="49" />
<hkern u1="&#xf2;" u2="V" k="61" />
<hkern u1="&#xf2;" u2="&#x3f;" k="39" />
<hkern u1="&#xf2;" u2="&#x2a;" k="12" />
<hkern u1="&#xf2;" u2="&#x29;" k="39" />
<hkern u1="&#xf3;" u2="&#x2122;" k="31" />
<hkern u1="&#xf3;" u2="&#xc6;" k="14" />
<hkern u1="&#xf3;" u2="&#x7d;" k="51" />
<hkern u1="&#xf3;" u2="x" k="23" />
<hkern u1="&#xf3;" u2="v" k="16" />
<hkern u1="&#xf3;" u2="]" k="66" />
<hkern u1="&#xf3;" u2="\" k="80" />
<hkern u1="&#xf3;" u2="X" k="49" />
<hkern u1="&#xf3;" u2="V" k="61" />
<hkern u1="&#xf3;" u2="&#x3f;" k="39" />
<hkern u1="&#xf3;" u2="&#x2a;" k="12" />
<hkern u1="&#xf3;" u2="&#x29;" k="39" />
<hkern u1="&#xf4;" u2="&#x2122;" k="31" />
<hkern u1="&#xf4;" u2="&#xc6;" k="14" />
<hkern u1="&#xf4;" u2="&#x7d;" k="51" />
<hkern u1="&#xf4;" u2="x" k="23" />
<hkern u1="&#xf4;" u2="v" k="16" />
<hkern u1="&#xf4;" u2="]" k="66" />
<hkern u1="&#xf4;" u2="\" k="80" />
<hkern u1="&#xf4;" u2="X" k="49" />
<hkern u1="&#xf4;" u2="V" k="61" />
<hkern u1="&#xf4;" u2="&#x3f;" k="39" />
<hkern u1="&#xf4;" u2="&#x2a;" k="12" />
<hkern u1="&#xf4;" u2="&#x29;" k="39" />
<hkern u1="&#xf5;" u2="&#x2122;" k="31" />
<hkern u1="&#xf5;" u2="&#xc6;" k="14" />
<hkern u1="&#xf5;" u2="&#x7d;" k="51" />
<hkern u1="&#xf5;" u2="x" k="23" />
<hkern u1="&#xf5;" u2="v" k="16" />
<hkern u1="&#xf5;" u2="]" k="66" />
<hkern u1="&#xf5;" u2="\" k="80" />
<hkern u1="&#xf5;" u2="X" k="49" />
<hkern u1="&#xf5;" u2="V" k="61" />
<hkern u1="&#xf5;" u2="&#x3f;" k="39" />
<hkern u1="&#xf5;" u2="&#x2a;" k="12" />
<hkern u1="&#xf5;" u2="&#x29;" k="39" />
<hkern u1="&#xf6;" u2="&#x2122;" k="31" />
<hkern u1="&#xf6;" u2="&#xc6;" k="14" />
<hkern u1="&#xf6;" u2="&#x7d;" k="51" />
<hkern u1="&#xf6;" u2="x" k="23" />
<hkern u1="&#xf6;" u2="v" k="16" />
<hkern u1="&#xf6;" u2="]" k="66" />
<hkern u1="&#xf6;" u2="\" k="80" />
<hkern u1="&#xf6;" u2="X" k="49" />
<hkern u1="&#xf6;" u2="V" k="61" />
<hkern u1="&#xf6;" u2="&#x3f;" k="39" />
<hkern u1="&#xf6;" u2="&#x2a;" k="12" />
<hkern u1="&#xf6;" u2="&#x29;" k="39" />
<hkern u1="&#xf8;" u2="&#x2122;" k="31" />
<hkern u1="&#xf8;" u2="&#xc6;" k="14" />
<hkern u1="&#xf8;" u2="&#x7d;" k="51" />
<hkern u1="&#xf8;" u2="x" k="23" />
<hkern u1="&#xf8;" u2="v" k="16" />
<hkern u1="&#xf8;" u2="]" k="66" />
<hkern u1="&#xf8;" u2="\" k="80" />
<hkern u1="&#xf8;" u2="X" k="49" />
<hkern u1="&#xf8;" u2="V" k="61" />
<hkern u1="&#xf8;" u2="&#x3f;" k="39" />
<hkern u1="&#xf8;" u2="&#x2a;" k="12" />
<hkern u1="&#xf8;" u2="&#x29;" k="39" />
<hkern u1="&#xf9;" u2="&#x2122;" k="20" />
<hkern u1="&#xf9;" u2="&#x7d;" k="20" />
<hkern u1="&#xf9;" u2="]" k="23" />
<hkern u1="&#xf9;" u2="\" k="53" />
<hkern u1="&#xf9;" u2="X" k="8" />
<hkern u1="&#xf9;" u2="V" k="47" />
<hkern u1="&#xf9;" u2="&#x3f;" k="10" />
<hkern u1="&#xf9;" u2="&#x29;" k="16" />
<hkern u1="&#xfa;" u2="&#x2122;" k="20" />
<hkern u1="&#xfa;" u2="&#x7d;" k="20" />
<hkern u1="&#xfa;" u2="]" k="23" />
<hkern u1="&#xfa;" u2="\" k="53" />
<hkern u1="&#xfa;" u2="X" k="8" />
<hkern u1="&#xfa;" u2="V" k="47" />
<hkern u1="&#xfa;" u2="&#x3f;" k="10" />
<hkern u1="&#xfa;" u2="&#x29;" k="16" />
<hkern u1="&#xfb;" u2="&#x2122;" k="20" />
<hkern u1="&#xfb;" u2="&#x7d;" k="20" />
<hkern u1="&#xfb;" u2="]" k="23" />
<hkern u1="&#xfb;" u2="\" k="53" />
<hkern u1="&#xfb;" u2="X" k="8" />
<hkern u1="&#xfb;" u2="V" k="47" />
<hkern u1="&#xfb;" u2="&#x3f;" k="10" />
<hkern u1="&#xfb;" u2="&#x29;" k="16" />
<hkern u1="&#xfc;" u2="&#x2122;" k="20" />
<hkern u1="&#xfc;" u2="&#x7d;" k="20" />
<hkern u1="&#xfc;" u2="]" k="23" />
<hkern u1="&#xfc;" u2="\" k="53" />
<hkern u1="&#xfc;" u2="X" k="8" />
<hkern u1="&#xfc;" u2="V" k="47" />
<hkern u1="&#xfc;" u2="&#x3f;" k="10" />
<hkern u1="&#xfc;" u2="&#x29;" k="16" />
<hkern u1="&#xfd;" u2="&#xf0;" k="27" />
<hkern u1="&#xfd;" u2="&#xc6;" k="39" />
<hkern u1="&#xfd;" u2="&#x7d;" k="35" />
<hkern u1="&#xfd;" u2="]" k="47" />
<hkern u1="&#xfd;" u2="\" k="27" />
<hkern u1="&#xfd;" u2="X" k="49" />
<hkern u1="&#xfd;" u2="V" k="14" />
<hkern u1="&#xfd;" u2="&#x3f;" k="8" />
<hkern u1="&#xfd;" u2="&#x2f;" k="41" />
<hkern u1="&#xfe;" u2="&#x2122;" k="33" />
<hkern u1="&#xfe;" u2="&#xc6;" k="14" />
<hkern u1="&#xfe;" u2="&#x7d;" k="51" />
<hkern u1="&#xfe;" u2="x" k="20" />
<hkern u1="&#xfe;" u2="v" k="14" />
<hkern u1="&#xfe;" u2="]" k="63" />
<hkern u1="&#xfe;" u2="\" k="78" />
<hkern u1="&#xfe;" u2="X" k="43" />
<hkern u1="&#xfe;" u2="V" k="59" />
<hkern u1="&#xfe;" u2="&#x3f;" k="41" />
<hkern u1="&#xfe;" u2="&#x2a;" k="16" />
<hkern u1="&#xfe;" u2="&#x29;" k="37" />
<hkern u1="&#xff;" u2="&#xf0;" k="27" />
<hkern u1="&#xff;" u2="&#xc6;" k="39" />
<hkern u1="&#xff;" u2="&#x7d;" k="35" />
<hkern u1="&#xff;" u2="]" k="47" />
<hkern u1="&#xff;" u2="\" k="27" />
<hkern u1="&#xff;" u2="X" k="49" />
<hkern u1="&#xff;" u2="V" k="14" />
<hkern u1="&#xff;" u2="&#x3f;" k="8" />
<hkern u1="&#xff;" u2="&#x2f;" k="41" />
<hkern u1="&#x104;" u2="&#x2122;" k="72" />
<hkern u1="&#x104;" u2="&#x201e;" k="-23" />
<hkern u1="&#x104;" u2="&#xf0;" k="10" />
<hkern u1="&#x104;" u2="&#xae;" k="43" />
<hkern u1="&#x104;" u2="&#x7d;" k="18" />
<hkern u1="&#x104;" u2="v" k="33" />
<hkern u1="&#x104;" u2="j" k="-109" />
<hkern u1="&#x104;" u2="f" k="16" />
<hkern u1="&#x104;" u2="]" k="20" />
<hkern u1="&#x104;" u2="\" k="90" />
<hkern u1="&#x104;" u2="V" k="57" />
<hkern u1="&#x104;" u2="&#x3f;" k="39" />
<hkern u1="&#x104;" u2="&#x2a;" k="63" />
<hkern u1="&#x105;" u2="&#x2122;" k="29" />
<hkern u1="&#x105;" u2="&#x7d;" k="8" />
<hkern u1="&#x105;" u2="v" k="10" />
<hkern u1="&#x105;" u2="j" k="-63" />
<hkern u1="&#x105;" u2="]" k="10" />
<hkern u1="&#x105;" u2="\" k="82" />
<hkern u1="&#x105;" u2="V" k="55" />
<hkern u1="&#x105;" u2="&#x3f;" k="29" />
<hkern u1="&#x105;" u2="&#x2a;" k="12" />
<hkern u1="&#x106;" u2="&#xf0;" k="14" />
<hkern u1="&#x106;" u2="&#xef;" k="-53" />
<hkern u1="&#x106;" u2="&#xee;" k="-43" />
<hkern u1="&#x106;" u2="&#xec;" k="-80" />
<hkern u1="&#x106;" u2="&#xae;" k="10" />
<hkern u1="&#x106;" u2="v" k="20" />
<hkern u1="&#x106;" u2="f" k="10" />
<hkern u1="&#x107;" u2="&#xf0;" k="18" />
<hkern u1="&#x107;" u2="&#x7d;" k="14" />
<hkern u1="&#x107;" u2="]" k="16" />
<hkern u1="&#x107;" u2="\" k="37" />
<hkern u1="&#x107;" u2="V" k="25" />
<hkern u1="&#x107;" u2="&#x3f;" k="8" />
<hkern u1="&#x118;" u2="&#xf0;" k="14" />
<hkern u1="&#x118;" u2="&#xef;" k="-49" />
<hkern u1="&#x118;" u2="&#xee;" k="-43" />
<hkern u1="&#x118;" u2="&#xec;" k="-80" />
<hkern u1="&#x118;" u2="v" k="10" />
<hkern u1="&#x118;" u2="j" k="-33" />
<hkern u1="&#x119;" u2="&#x2122;" k="27" />
<hkern u1="&#x119;" u2="&#xc6;" k="10" />
<hkern u1="&#x119;" u2="&#x7d;" k="37" />
<hkern u1="&#x119;" u2="v" k="14" />
<hkern u1="&#x119;" u2="]" k="23" />
<hkern u1="&#x119;" u2="\" k="74" />
<hkern u1="&#x119;" u2="X" k="8" />
<hkern u1="&#x119;" u2="V" k="57" />
<hkern u1="&#x119;" u2="&#x3f;" k="31" />
<hkern u1="&#x119;" u2="&#x29;" k="18" />
<hkern u1="&#x131;" u2="&#xef;" k="-12" />
<hkern u1="&#x131;" u2="&#xec;" k="-27" />
<hkern u1="&#x141;" u2="&#x2122;" k="168" />
<hkern u1="&#x141;" u2="&#xb7;" k="49" />
<hkern u1="&#x141;" u2="&#xae;" k="145" />
<hkern u1="&#x141;" u2="&#x7d;" k="10" />
<hkern u1="&#x141;" u2="v" k="78" />
<hkern u1="&#x141;" u2="f" k="12" />
<hkern u1="&#x141;" u2="]" k="12" />
<hkern u1="&#x141;" u2="\" k="145" />
<hkern u1="&#x141;" u2="V" k="117" />
<hkern u1="&#x141;" u2="&#x3f;" k="12" />
<hkern u1="&#x141;" u2="&#x2a;" k="166" />
<hkern u1="&#x142;" u2="&#xec;" k="-18" />
<hkern u1="&#x142;" u2="&#xb7;" k="123" />
<hkern u1="&#x143;" u2="&#xf0;" k="12" />
<hkern u1="&#x143;" u2="&#xec;" k="-12" />
<hkern u1="&#x143;" u2="f" k="8" />
<hkern u1="&#x144;" u2="&#x2122;" k="31" />
<hkern u1="&#x144;" u2="&#x7d;" k="20" />
<hkern u1="&#x144;" u2="v" k="10" />
<hkern u1="&#x144;" u2="]" k="23" />
<hkern u1="&#x144;" u2="\" k="78" />
<hkern u1="&#x144;" u2="V" k="57" />
<hkern u1="&#x144;" u2="&#x3f;" k="35" />
<hkern u1="&#x144;" u2="&#x2a;" k="12" />
<hkern u1="&#x144;" u2="&#x29;" k="16" />
<hkern u1="&#x152;" u2="&#xf0;" k="14" />
<hkern u1="&#x152;" u2="&#xef;" k="-49" />
<hkern u1="&#x152;" u2="&#xee;" k="-43" />
<hkern u1="&#x152;" u2="&#xec;" k="-80" />
<hkern u1="&#x152;" u2="v" k="10" />
<hkern u1="&#x153;" u2="&#x2122;" k="27" />
<hkern u1="&#x153;" u2="&#xc6;" k="10" />
<hkern u1="&#x153;" u2="&#x7d;" k="37" />
<hkern u1="&#x153;" u2="v" k="14" />
<hkern u1="&#x153;" u2="]" k="23" />
<hkern u1="&#x153;" u2="\" k="74" />
<hkern u1="&#x153;" u2="X" k="8" />
<hkern u1="&#x153;" u2="V" k="57" />
<hkern u1="&#x153;" u2="&#x3f;" k="31" />
<hkern u1="&#x153;" u2="&#x29;" k="18" />
<hkern u1="&#x15a;" u2="&#xef;" k="-31" />
<hkern u1="&#x15a;" u2="&#xee;" k="-12" />
<hkern u1="&#x15a;" u2="&#xec;" k="-49" />
<hkern u1="&#x15a;" u2="&#xc6;" k="23" />
<hkern u1="&#x15a;" u2="x" k="18" />
<hkern u1="&#x15a;" u2="v" k="16" />
<hkern u1="&#x15a;" u2="f" k="16" />
<hkern u1="&#x15a;" u2="X" k="10" />
<hkern u1="&#x15a;" u2="V" k="18" />
<hkern u1="&#x15b;" u2="&#x2122;" k="23" />
<hkern u1="&#x15b;" u2="&#xc6;" k="10" />
<hkern u1="&#x15b;" u2="&#x7d;" k="39" />
<hkern u1="&#x15b;" u2="v" k="12" />
<hkern u1="&#x15b;" u2="]" k="49" />
<hkern u1="&#x15b;" u2="\" k="53" />
<hkern u1="&#x15b;" u2="X" k="12" />
<hkern u1="&#x15b;" u2="V" k="41" />
<hkern u1="&#x15b;" u2="&#x3f;" k="10" />
<hkern u1="&#x15b;" u2="&#x29;" k="20" />
<hkern u1="&#x178;" u2="&#x142;" k="12" />
<hkern u1="&#x178;" u2="&#x131;" k="123" />
<hkern u1="&#x178;" u2="&#xff;" k="57" />
<hkern u1="&#x178;" u2="&#xf0;" k="66" />
<hkern u1="&#x178;" u2="&#xef;" k="-121" />
<hkern u1="&#x178;" u2="&#xee;" k="-33" />
<hkern u1="&#x178;" u2="&#xec;" k="-129" />
<hkern u1="&#x178;" u2="&#xeb;" k="131" />
<hkern u1="&#x178;" u2="&#xe4;" k="100" />
<hkern u1="&#x178;" u2="&#xe3;" k="86" />
<hkern u1="&#x178;" u2="&#xdf;" k="20" />
<hkern u1="&#x178;" u2="&#xc6;" k="115" />
<hkern u1="&#x178;" u2="&#xae;" k="41" />
<hkern u1="&#x178;" u2="x" k="76" />
<hkern u1="&#x178;" u2="v" k="74" />
<hkern u1="&#x178;" u2="f" k="43" />
<hkern u1="&#x178;" u2="&#x40;" k="74" />
<hkern u1="&#x178;" u2="&#x2f;" k="133" />
<hkern u1="&#x178;" u2="&#x2a;" k="-8" />
<hkern u1="&#x178;" u2="&#x26;" k="66" />
<hkern u1="&#x179;" u2="&#xf0;" k="16" />
<hkern u1="&#x179;" u2="&#xef;" k="-45" />
<hkern u1="&#x179;" u2="&#xee;" k="-47" />
<hkern u1="&#x179;" u2="&#xec;" k="-82" />
<hkern u1="&#x179;" u2="&#xae;" k="6" />
<hkern u1="&#x179;" u2="v" k="18" />
<hkern u1="&#x179;" u2="f" k="10" />
<hkern u1="&#x17a;" u2="&#xf0;" k="12" />
<hkern u1="&#x17a;" u2="&#x7d;" k="16" />
<hkern u1="&#x17a;" u2="]" k="18" />
<hkern u1="&#x17a;" u2="\" k="39" />
<hkern u1="&#x17a;" u2="V" k="27" />
<hkern u1="&#x17a;" u2="&#x3f;" k="8" />
<hkern u1="&#x17b;" u2="&#xf0;" k="16" />
<hkern u1="&#x17b;" u2="&#xef;" k="-45" />
<hkern u1="&#x17b;" u2="&#xee;" k="-47" />
<hkern u1="&#x17b;" u2="&#xec;" k="-82" />
<hkern u1="&#x17b;" u2="&#xae;" k="6" />
<hkern u1="&#x17b;" u2="v" k="18" />
<hkern u1="&#x17b;" u2="f" k="10" />
<hkern u1="&#x17c;" u2="&#xf0;" k="12" />
<hkern u1="&#x17c;" u2="&#x7d;" k="16" />
<hkern u1="&#x17c;" u2="]" k="18" />
<hkern u1="&#x17c;" u2="\" k="39" />
<hkern u1="&#x17c;" u2="V" k="27" />
<hkern u1="&#x17c;" u2="&#x3f;" k="8" />
<hkern u1="&#x2013;" u2="&#xc6;" k="27" />
<hkern u1="&#x2013;" u2="x" k="53" />
<hkern u1="&#x2013;" u2="v" k="23" />
<hkern u1="&#x2013;" u2="f" k="23" />
<hkern u1="&#x2013;" u2="X" k="72" />
<hkern u1="&#x2013;" u2="V" k="55" />
<hkern u1="&#x2014;" u2="&#xc6;" k="27" />
<hkern u1="&#x2014;" u2="x" k="53" />
<hkern u1="&#x2014;" u2="v" k="23" />
<hkern u1="&#x2014;" u2="f" k="23" />
<hkern u1="&#x2014;" u2="X" k="72" />
<hkern u1="&#x2014;" u2="V" k="55" />
<hkern u1="&#x2018;" u2="&#xf0;" k="8" />
<hkern u1="&#x2018;" u2="&#xef;" k="-55" />
<hkern u1="&#x2018;" u2="&#xee;" k="-23" />
<hkern u1="&#x2018;" u2="&#xec;" k="-74" />
<hkern u1="&#x2018;" u2="&#xc6;" k="100" />
<hkern u1="&#x2019;" u2="&#xf0;" k="8" />
<hkern u1="&#x2019;" u2="&#xef;" k="-70" />
<hkern u1="&#x2019;" u2="&#xee;" k="-14" />
<hkern u1="&#x2019;" u2="&#xec;" k="-80" />
<hkern u1="&#x2019;" u2="&#xc6;" k="109" />
<hkern u1="&#x2019;" u2="&#x40;" k="49" />
<hkern u1="&#x2019;" u2="&#x2f;" k="147" />
<hkern u1="&#x2019;" u2="&#x26;" k="55" />
<hkern u1="&#x201a;" u2="v" k="57" />
<hkern u1="&#x201a;" u2="f" k="20" />
<hkern u1="&#x201a;" u2="V" k="92" />
<hkern u1="&#x201c;" u2="&#xf0;" k="8" />
<hkern u1="&#x201c;" u2="&#xef;" k="-55" />
<hkern u1="&#x201c;" u2="&#xee;" k="-23" />
<hkern u1="&#x201c;" u2="&#xec;" k="-74" />
<hkern u1="&#x201c;" u2="&#xc6;" k="100" />
<hkern u1="&#x201d;" u2="&#xf0;" k="8" />
<hkern u1="&#x201d;" u2="&#xef;" k="-70" />
<hkern u1="&#x201d;" u2="&#xee;" k="-14" />
<hkern u1="&#x201d;" u2="&#xec;" k="-80" />
<hkern u1="&#x201d;" u2="&#xc6;" k="109" />
<hkern u1="&#x201d;" u2="&#x40;" k="49" />
<hkern u1="&#x201d;" u2="&#x2f;" k="147" />
<hkern u1="&#x201d;" u2="&#x26;" k="55" />
<hkern u1="&#x201e;" u2="v" k="57" />
<hkern u1="&#x201e;" u2="f" k="20" />
<hkern u1="&#x201e;" u2="V" k="92" />
<hkern u1="&#x2039;" u2="V" k="31" />
<hkern u1="&#x203a;" u2="&#x141;" k="-10" />
<hkern u1="&#x203a;" u2="&#xc6;" k="6" />
<hkern u1="&#x203a;" u2="x" k="47" />
<hkern u1="&#x203a;" u2="v" k="6" />
<hkern u1="&#x203a;" u2="f" k="14" />
<hkern u1="&#x203a;" u2="X" k="45" />
<hkern u1="&#x203a;" u2="V" k="51" />
<hkern u1="&#x2122;" u2="&#x104;" k="51" />
<hkern u1="&#x2122;" u2="&#xef;" k="-53" />
<hkern u1="&#x2122;" u2="&#xee;" k="-53" />
<hkern u1="&#x2122;" u2="&#xec;" k="-68" />
<hkern u1="&#x2122;" u2="&#xc6;" k="63" />
<hkern u1="&#x2122;" u2="&#xc5;" k="51" />
<hkern u1="&#x2122;" u2="&#xc4;" k="51" />
<hkern u1="&#x2122;" u2="&#xc3;" k="51" />
<hkern u1="&#x2122;" u2="&#xc2;" k="51" />
<hkern u1="&#x2122;" u2="&#xc1;" k="51" />
<hkern u1="&#x2122;" u2="&#xc0;" k="51" />
<hkern u1="&#x2122;" u2="J" k="33" />
<hkern u1="&#x2122;" u2="A" k="51" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	g2="C,Ccedilla,Cacute" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="23" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	g2="S,Sacute" 	k="14" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	g2="T" 	k="84" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="12" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	g2="W" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	g2="Y,Yacute,Ydieresis" 	k="104" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	g2="d,q" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	g2="g" 	k="16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	g2="hyphen,endash,emdash" 	k="8" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,eogonek,oe" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	g2="quoteleft,quotedblleft" 	k="66" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	g2="quoteright,quotedblright" 	k="68" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	g2="quotedbl,quotesingle" 	k="72" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	g2="t" 	k="27" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	g2="w" 	k="29" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	g2="y,yacute,ydieresis" 	k="33" />
<hkern g1="C,Ccedilla,Cacute" 	g2="C,Ccedilla,Cacute" 	k="6" />
<hkern g1="C,Ccedilla,Cacute" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="16" />
<hkern g1="C,Ccedilla,Cacute" 	g2="d,q" 	k="16" />
<hkern g1="C,Ccedilla,Cacute" 	g2="g" 	k="29" />
<hkern g1="C,Ccedilla,Cacute" 	g2="hyphen,endash,emdash" 	k="49" />
<hkern g1="C,Ccedilla,Cacute" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,eogonek,oe" 	k="18" />
<hkern g1="C,Ccedilla,Cacute" 	g2="w" 	k="23" />
<hkern g1="C,Ccedilla,Cacute" 	g2="y,yacute,ydieresis" 	k="18" />
<hkern g1="C,Ccedilla,Cacute" 	g2="guillemotleft,guilsinglleft" 	k="14" />
<hkern g1="C,Ccedilla,Cacute" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="16" />
<hkern g1="D,Eth" 	g2="T" 	k="27" />
<hkern g1="D,Eth" 	g2="Y,Yacute,Ydieresis" 	k="55" />
<hkern g1="D,Eth" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	k="25" />
<hkern g1="D,Eth" 	g2="J" 	k="25" />
<hkern g1="D,Eth" 	g2="Z,Zacute,Zdotaccent" 	k="6" />
<hkern g1="D,Eth" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Eogonek,OE" 	g2="C,Ccedilla,Cacute" 	k="12" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Eogonek,OE" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Eogonek,OE" 	g2="d,q" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Eogonek,OE" 	g2="g" 	k="27" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Eogonek,OE" 	g2="hyphen,endash,emdash" 	k="35" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Eogonek,OE" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,eogonek,oe" 	k="14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Eogonek,OE" 	g2="t" 	k="6" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Eogonek,OE" 	g2="w" 	k="18" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Eogonek,OE" 	g2="y,yacute,ydieresis" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Eogonek,OE" 	g2="guillemotleft,guilsinglleft" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,Eogonek,OE" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="6" />
<hkern g1="G" 	g2="Y,Yacute,Ydieresis" 	k="33" />
<hkern g1="G" 	g2="g" 	k="12" />
<hkern g1="G" 	g2="w" 	k="14" />
<hkern g1="G" 	g2="y,yacute,ydieresis" 	k="12" />
<hkern g1="G" 	g2="J" 	k="8" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Nacute" 	g2="d,q" 	k="10" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Nacute" 	g2="g" 	k="18" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Nacute" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,eogonek,oe" 	k="12" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Nacute" 	g2="w" 	k="8" />
<hkern g1="H,I,M,N,Igrave,Iacute,Icircumflex,Idieresis,Ntilde,Nacute" 	g2="J" 	k="6" />
<hkern g1="J" 	g2="d,q" 	k="10" />
<hkern g1="J" 	g2="g" 	k="16" />
<hkern g1="J" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,eogonek,oe" 	k="10" />
<hkern g1="J" 	g2="w" 	k="8" />
<hkern g1="K" 	g2="C,Ccedilla,Cacute" 	k="18" />
<hkern g1="K" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="23" />
<hkern g1="K" 	g2="d,q" 	k="29" />
<hkern g1="K" 	g2="g" 	k="31" />
<hkern g1="K" 	g2="hyphen,endash,emdash" 	k="61" />
<hkern g1="K" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,eogonek,oe" 	k="33" />
<hkern g1="K" 	g2="t" 	k="20" />
<hkern g1="K" 	g2="w" 	k="45" />
<hkern g1="K" 	g2="y,yacute,ydieresis" 	k="43" />
<hkern g1="K" 	g2="guillemotleft,guilsinglleft" 	k="39" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="K" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,aogonek" 	k="8" />
<hkern g1="K" 	g2="m,n,p,r,ntilde,nacute" 	k="10" />
<hkern g1="L,Lslash" 	g2="C,Ccedilla,Cacute" 	k="14" />
<hkern g1="L,Lslash" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="25" />
<hkern g1="L,Lslash" 	g2="T" 	k="172" />
<hkern g1="L,Lslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="18" />
<hkern g1="L,Lslash" 	g2="W" 	k="90" />
<hkern g1="L,Lslash" 	g2="Y,Yacute,Ydieresis" 	k="178" />
<hkern g1="L,Lslash" 	g2="g" 	k="8" />
<hkern g1="L,Lslash" 	g2="hyphen,endash,emdash" 	k="121" />
<hkern g1="L,Lslash" 	g2="quoteleft,quotedblleft" 	k="168" />
<hkern g1="L,Lslash" 	g2="quoteright,quotedblright" 	k="166" />
<hkern g1="L,Lslash" 	g2="quotedbl,quotesingle" 	k="168" />
<hkern g1="L,Lslash" 	g2="t" 	k="29" />
<hkern g1="L,Lslash" 	g2="w" 	k="53" />
<hkern g1="L,Lslash" 	g2="y,yacute,ydieresis" 	k="80" />
<hkern g1="L,Lslash" 	g2="guillemotleft,guilsinglleft" 	k="35" />
<hkern g1="L,Lslash" 	g2="guillemotright,guilsinglright" 	k="12" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="27" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="57" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	k="23" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="J" 	k="23" />
<hkern g1="O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="10" />
<hkern g1="R" 	g2="T" 	k="8" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="45" />
<hkern g1="R" 	g2="d,q" 	k="16" />
<hkern g1="R" 	g2="g" 	k="16" />
<hkern g1="R" 	g2="hyphen,endash,emdash" 	k="8" />
<hkern g1="R" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,eogonek,oe" 	k="16" />
<hkern g1="R" 	g2="guillemotleft,guilsinglleft" 	k="12" />
<hkern g1="R" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	k="14" />
<hkern g1="R" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,aogonek" 	k="8" />
<hkern g1="S,Sacute" 	g2="Y,Yacute,Ydieresis" 	k="31" />
<hkern g1="S,Sacute" 	g2="g" 	k="14" />
<hkern g1="S,Sacute" 	g2="t" 	k="14" />
<hkern g1="S,Sacute" 	g2="w" 	k="16" />
<hkern g1="S,Sacute" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="S,Sacute" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	k="18" />
<hkern g1="S,Sacute" 	g2="J" 	k="12" />
<hkern g1="T" 	g2="C,Ccedilla,Cacute" 	k="12" />
<hkern g1="T" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="25" />
<hkern g1="T" 	g2="d,q" 	k="158" />
<hkern g1="T" 	g2="g" 	k="176" />
<hkern g1="T" 	g2="hyphen,endash,emdash" 	k="123" />
<hkern g1="T" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,eogonek,oe" 	k="162" />
<hkern g1="T" 	g2="t" 	k="35" />
<hkern g1="T" 	g2="w" 	k="119" />
<hkern g1="T" 	g2="y,yacute,ydieresis" 	k="119" />
<hkern g1="T" 	g2="guillemotleft,guilsinglleft" 	k="119" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="139" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	k="84" />
<hkern g1="T" 	g2="J" 	k="29" />
<hkern g1="T" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="121" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,aogonek" 	k="162" />
<hkern g1="T" 	g2="m,n,p,r,ntilde,nacute" 	k="137" />
<hkern g1="T" 	g2="guillemotright,guilsinglright" 	k="106" />
<hkern g1="T" 	g2="b,thorn" 	k="6" />
<hkern g1="T" 	g2="colon,semicolon" 	k="104" />
<hkern g1="T" 	g2="s,sacute" 	k="158" />
<hkern g1="T" 	g2="z,zacute,zdotaccent" 	k="158" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="d,q" 	k="12" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="g" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,eogonek,oe" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="12" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	k="12" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="J" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,aogonek" 	k="8" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="m,n,p,r,ntilde,nacute" 	k="8" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="s,sacute" 	k="10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="z,zacute,zdotaccent" 	k="8" />
<hkern g1="W" 	g2="d,q" 	k="39" />
<hkern g1="W" 	g2="g" 	k="45" />
<hkern g1="W" 	g2="hyphen,endash,emdash" 	k="35" />
<hkern g1="W" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,eogonek,oe" 	k="41" />
<hkern g1="W" 	g2="guillemotleft,guilsinglleft" 	k="33" />
<hkern g1="W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="23" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	k="41" />
<hkern g1="W" 	g2="J" 	k="25" />
<hkern g1="W" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="66" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,aogonek" 	k="31" />
<hkern g1="W" 	g2="m,n,p,r,ntilde,nacute" 	k="29" />
<hkern g1="W" 	g2="guillemotright,guilsinglright" 	k="6" />
<hkern g1="W" 	g2="s,sacute" 	k="29" />
<hkern g1="W" 	g2="z,zacute,zdotaccent" 	k="10" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,Ccedilla,Cacute" 	k="53" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="59" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="S,Sacute" 	k="37" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="d,q" 	k="141" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="150" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="hyphen,endash,emdash" 	k="137" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,eogonek,oe" 	k="143" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="35" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="86" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="y,yacute,ydieresis" 	k="78" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="127" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="113" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	k="104" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="J" 	k="55" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="147" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,aogonek" 	k="131" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="m,n,p,r,ntilde,nacute" 	k="123" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="guillemotright,guilsinglright" 	k="86" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s,sacute" 	k="143" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z,zacute,zdotaccent" 	k="100" />
<hkern g1="Z,Zacute,Zdotaccent" 	g2="d,q" 	k="18" />
<hkern g1="Z,Zacute,Zdotaccent" 	g2="g" 	k="31" />
<hkern g1="Z,Zacute,Zdotaccent" 	g2="hyphen,endash,emdash" 	k="49" />
<hkern g1="Z,Zacute,Zdotaccent" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,eogonek,oe" 	k="18" />
<hkern g1="Z,Zacute,Zdotaccent" 	g2="w" 	k="23" />
<hkern g1="Z,Zacute,Zdotaccent" 	g2="y,yacute,ydieresis" 	k="18" />
<hkern g1="Z,Zacute,Zdotaccent" 	g2="guillemotleft,guilsinglleft" 	k="14" />
<hkern g1="Z,Zacute,Zdotaccent" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="18" />
<hkern g1="Z,Zacute,Zdotaccent" 	g2="m,n,p,r,ntilde,nacute" 	k="10" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,aogonek" 	g2="T" 	k="150" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,aogonek" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="14" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,aogonek" 	g2="W" 	k="35" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,aogonek" 	g2="Y,Yacute,Ydieresis" 	k="133" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,aogonek" 	g2="quotedbl,quotesingle" 	k="14" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,aogonek" 	g2="w" 	k="6" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring,aogonek" 	g2="y,yacute,ydieresis" 	k="12" />
<hkern g1="b,p,thorn" 	g2="S,Sacute" 	k="12" />
<hkern g1="b,p,thorn" 	g2="T" 	k="160" />
<hkern g1="b,p,thorn" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="8" />
<hkern g1="b,p,thorn" 	g2="W" 	k="37" />
<hkern g1="b,p,thorn" 	g2="Y,Yacute,Ydieresis" 	k="137" />
<hkern g1="b,p,thorn" 	g2="quoteleft,quotedblleft" 	k="14" />
<hkern g1="b,p,thorn" 	g2="quoteright,quotedblright" 	k="14" />
<hkern g1="b,p,thorn" 	g2="quotedbl,quotesingle" 	k="29" />
<hkern g1="b,p,thorn" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="b,p,thorn" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	k="12" />
<hkern g1="b,p,thorn" 	g2="J" 	k="35" />
<hkern g1="b,p,thorn" 	g2="Z,Zacute,Zdotaccent" 	k="18" />
<hkern g1="b,p,thorn" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Eogonek,Lslash,Nacute" 	k="10" />
<hkern g1="c,ccedilla,cacute" 	g2="T" 	k="176" />
<hkern g1="c,ccedilla,cacute" 	g2="W" 	k="8" />
<hkern g1="c,ccedilla,cacute" 	g2="Y,Yacute,Ydieresis" 	k="100" />
<hkern g1="c,ccedilla,cacute" 	g2="g" 	k="10" />
<hkern g1="c,ccedilla,cacute" 	g2="hyphen,endash,emdash" 	k="55" />
<hkern g1="c,ccedilla,cacute" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,eogonek,oe" 	k="10" />
<hkern g1="c,ccedilla,cacute" 	g2="guillemotleft,guilsinglleft" 	k="43" />
<hkern g1="c,ccedilla,cacute" 	g2="J" 	k="6" />
<hkern g1="c,ccedilla,cacute" 	g2="guillemotright,guilsinglright" 	k="6" />
<hkern g1="colon,semicolon" 	g2="T" 	k="104" />
<hkern g1="colon,semicolon" 	g2="Y,Yacute,Ydieresis" 	k="82" />
<hkern g1="d" 	g2="J" 	k="14" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,eogonek,oe" 	g2="T" 	k="166" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,eogonek,oe" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="8" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,eogonek,oe" 	g2="W" 	k="33" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,eogonek,oe" 	g2="Y,Yacute,Ydieresis" 	k="166" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,eogonek,oe" 	g2="quoteleft,quotedblleft" 	k="8" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,eogonek,oe" 	g2="quoteright,quotedblright" 	k="8" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,eogonek,oe" 	g2="quotedbl,quotesingle" 	k="23" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,eogonek,oe" 	g2="y,yacute,ydieresis" 	k="16" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,eogonek,oe" 	g2="J" 	k="14" />
<hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,eogonek,oe" 	g2="Z,Zacute,Zdotaccent" 	k="10" />
<hkern g1="g" 	g2="T" 	k="123" />
<hkern g1="g" 	g2="Y,Yacute,Ydieresis" 	k="66" />
<hkern g1="g" 	g2="hyphen,endash,emdash" 	k="23" />
<hkern g1="g" 	g2="guillemotleft,guilsinglleft" 	k="14" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="T" 	k="113" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="W" 	k="6" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="Y,Yacute,Ydieresis" 	k="88" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="quoteright,quotedblright" 	k="31" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="quotedbl,quotesingle" 	k="33" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="t" 	k="6" />
<hkern g1="guillemotleft,guilsinglleft" 	g2="J" 	k="16" />
<hkern g1="guillemotright,guilsinglright" 	g2="S,Sacute" 	k="14" />
<hkern g1="guillemotright,guilsinglright" 	g2="T" 	k="119" />
<hkern g1="guillemotright,guilsinglright" 	g2="W" 	k="31" />
<hkern g1="guillemotright,guilsinglright" 	g2="Y,Yacute,Ydieresis" 	k="123" />
<hkern g1="guillemotright,guilsinglright" 	g2="quoteright,quotedblright" 	k="76" />
<hkern g1="guillemotright,guilsinglright" 	g2="quotedbl,quotesingle" 	k="86" />
<hkern g1="guillemotright,guilsinglright" 	g2="t" 	k="6" />
<hkern g1="guillemotright,guilsinglright" 	g2="w" 	k="6" />
<hkern g1="guillemotright,guilsinglright" 	g2="y,yacute,ydieresis" 	k="25" />
<hkern g1="guillemotright,guilsinglright" 	g2="J" 	k="16" />
<hkern g1="guillemotright,guilsinglright" 	g2="Z,Zacute,Zdotaccent" 	k="10" />
<hkern g1="guillemotright,guilsinglright" 	g2="z,zacute,zdotaccent" 	k="37" />
<hkern g1="hyphen,endash,emdash" 	g2="S,Sacute" 	k="45" />
<hkern g1="hyphen,endash,emdash" 	g2="T" 	k="123" />
<hkern g1="hyphen,endash,emdash" 	g2="W" 	k="35" />
<hkern g1="hyphen,endash,emdash" 	g2="Y,Yacute,Ydieresis" 	k="137" />
<hkern g1="hyphen,endash,emdash" 	g2="quoteright,quotedblright" 	k="125" />
<hkern g1="hyphen,endash,emdash" 	g2="quotedbl,quotesingle" 	k="135" />
<hkern g1="hyphen,endash,emdash" 	g2="t" 	k="25" />
<hkern g1="hyphen,endash,emdash" 	g2="w" 	k="6" />
<hkern g1="hyphen,endash,emdash" 	g2="y,yacute,ydieresis" 	k="25" />
<hkern g1="hyphen,endash,emdash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	k="8" />
<hkern g1="hyphen,endash,emdash" 	g2="J" 	k="45" />
<hkern g1="hyphen,endash,emdash" 	g2="Z,Zacute,Zdotaccent" 	k="47" />
<hkern g1="hyphen,endash,emdash" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,aogonek" 	k="8" />
<hkern g1="hyphen,endash,emdash" 	g2="z,zacute,zdotaccent" 	k="49" />
<hkern g1="i,j,igrave,iacute,icircumflex,idieresis,dotlessi" 	g2="J" 	k="14" />
<hkern g1="k" 	g2="T" 	k="129" />
<hkern g1="k" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="8" />
<hkern g1="k" 	g2="Y,Yacute,Ydieresis" 	k="78" />
<hkern g1="k" 	g2="d,q" 	k="18" />
<hkern g1="k" 	g2="g" 	k="16" />
<hkern g1="k" 	g2="hyphen,endash,emdash" 	k="59" />
<hkern g1="k" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,eogonek,oe" 	k="23" />
<hkern g1="k" 	g2="guillemotleft,guilsinglleft" 	k="47" />
<hkern g1="l,lslash" 	g2="J" 	k="14" />
<hkern g1="h,m,n,ntilde,nacute" 	g2="S,Sacute" 	k="10" />
<hkern g1="h,m,n,ntilde,nacute" 	g2="T" 	k="166" />
<hkern g1="h,m,n,ntilde,nacute" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="12" />
<hkern g1="h,m,n,ntilde,nacute" 	g2="W" 	k="37" />
<hkern g1="h,m,n,ntilde,nacute" 	g2="Y,Yacute,Ydieresis" 	k="139" />
<hkern g1="h,m,n,ntilde,nacute" 	g2="quoteleft,quotedblleft" 	k="6" />
<hkern g1="h,m,n,ntilde,nacute" 	g2="quoteright,quotedblright" 	k="8" />
<hkern g1="h,m,n,ntilde,nacute" 	g2="quotedbl,quotesingle" 	k="23" />
<hkern g1="h,m,n,ntilde,nacute" 	g2="w" 	k="8" />
<hkern g1="h,m,n,ntilde,nacute" 	g2="y,yacute,ydieresis" 	k="10" />
<hkern g1="h,m,n,ntilde,nacute" 	g2="J" 	k="16" />
<hkern g1="h,m,n,ntilde,nacute" 	g2="Z,Zacute,Zdotaccent" 	k="10" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="S,Sacute" 	k="16" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="T" 	k="166" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="W" 	k="41" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="Y,Yacute,Ydieresis" 	k="143" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteleft,quotedblleft" 	k="8" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quotedbl,quotesingle" 	k="27" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="y,yacute,ydieresis" 	k="18" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	k="10" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="J" 	k="37" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="Z,Zacute,Zdotaccent" 	k="18" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Eogonek,Lslash,Nacute" 	k="12" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="C,Ccedilla,Cacute" 	k="10" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="T" 	k="123" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="W" 	k="66" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="Y,Yacute,Ydieresis" 	k="147" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="hyphen,endash,emdash" 	k="31" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="quoteleft,quotedblleft" 	k="260" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="quoteright,quotedblright" 	k="266" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="quotedbl,quotesingle" 	k="276" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="t" 	k="33" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="w" 	k="45" />
<hkern g1="comma,period,quotesinglbase,quotedblbase" 	g2="y,yacute,ydieresis" 	k="61" />
<hkern g1="quoteleft,quotedblleft" 	g2="d,q" 	k="47" />
<hkern g1="quoteleft,quotedblleft" 	g2="g" 	k="31" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,eogonek,oe" 	k="35" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	k="82" />
<hkern g1="quoteleft,quotedblleft" 	g2="J" 	k="33" />
<hkern g1="quoteleft,quotedblleft" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="285" />
<hkern g1="quoteleft,quotedblleft" 	g2="s,sacute" 	k="25" />
<hkern g1="quoteright,quotedblright" 	g2="G,O,Q,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="d,q" 	k="59" />
<hkern g1="quoteright,quotedblright" 	g2="g" 	k="41" />
<hkern g1="quoteright,quotedblright" 	g2="hyphen,endash,emdash" 	k="160" />
<hkern g1="quoteright,quotedblright" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,eogonek,oe" 	k="49" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotleft,guilsinglleft" 	k="123" />
<hkern g1="quoteright,quotedblright" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	k="88" />
<hkern g1="quoteright,quotedblright" 	g2="J" 	k="33" />
<hkern g1="quoteright,quotedblright" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="301" />
<hkern g1="quoteright,quotedblright" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,aogonek" 	k="20" />
<hkern g1="quoteright,quotedblright" 	g2="m,n,p,r,ntilde,nacute" 	k="12" />
<hkern g1="quoteright,quotedblright" 	g2="guillemotright,guilsinglright" 	k="55" />
<hkern g1="quoteright,quotedblright" 	g2="colon,semicolon" 	k="18" />
<hkern g1="quoteright,quotedblright" 	g2="s,sacute" 	k="35" />
<hkern g1="quotedbl,quotesingle" 	g2="d,q" 	k="33" />
<hkern g1="quotedbl,quotesingle" 	g2="g" 	k="20" />
<hkern g1="quotedbl,quotesingle" 	g2="hyphen,endash,emdash" 	k="137" />
<hkern g1="quotedbl,quotesingle" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,eogonek,oe" 	k="27" />
<hkern g1="quotedbl,quotesingle" 	g2="guillemotleft,guilsinglleft" 	k="92" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	k="74" />
<hkern g1="quotedbl,quotesingle" 	g2="J" 	k="35" />
<hkern g1="quotedbl,quotesingle" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="276" />
<hkern g1="quotedbl,quotesingle" 	g2="guillemotright,guilsinglright" 	k="33" />
<hkern g1="r" 	g2="T" 	k="111" />
<hkern g1="r" 	g2="Y,Yacute,Ydieresis" 	k="49" />
<hkern g1="r" 	g2="d,q" 	k="10" />
<hkern g1="r" 	g2="g" 	k="6" />
<hkern g1="r" 	g2="hyphen,endash,emdash" 	k="72" />
<hkern g1="r" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,eogonek,oe" 	k="8" />
<hkern g1="r" 	g2="guillemotleft,guilsinglleft" 	k="66" />
<hkern g1="r" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	k="55" />
<hkern g1="r" 	g2="J" 	k="43" />
<hkern g1="r" 	g2="Z,Zacute,Zdotaccent" 	k="23" />
<hkern g1="r" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="109" />
<hkern g1="r" 	g2="guillemotright,guilsinglright" 	k="20" />
<hkern g1="s,sacute" 	g2="T" 	k="160" />
<hkern g1="s,sacute" 	g2="W" 	k="25" />
<hkern g1="s,sacute" 	g2="Y,Yacute,Ydieresis" 	k="109" />
<hkern g1="s,sacute" 	g2="hyphen,endash,emdash" 	k="8" />
<hkern g1="s,sacute" 	g2="y,yacute,ydieresis" 	k="14" />
<hkern g1="s,sacute" 	g2="J" 	k="18" />
<hkern g1="t" 	g2="T" 	k="121" />
<hkern g1="t" 	g2="Y,Yacute,Ydieresis" 	k="63" />
<hkern g1="t" 	g2="hyphen,endash,emdash" 	k="20" />
<hkern g1="t" 	g2="guillemotleft,guilsinglleft" 	k="39" />
<hkern g1="t" 	g2="guillemotright,guilsinglright" 	k="16" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="T" 	k="139" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="8" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="W" 	k="29" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="Y,Yacute,Ydieresis" 	k="123" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="J" 	k="14" />
<hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis" 	g2="Z,Zacute,Zdotaccent" 	k="8" />
<hkern g1="w" 	g2="T" 	k="119" />
<hkern g1="w" 	g2="Y,Yacute,Ydieresis" 	k="86" />
<hkern g1="w" 	g2="g" 	k="10" />
<hkern g1="w" 	g2="hyphen,endash,emdash" 	k="6" />
<hkern g1="w" 	g2="guillemotleft,guilsinglleft" 	k="6" />
<hkern g1="w" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	k="29" />
<hkern g1="w" 	g2="J" 	k="37" />
<hkern g1="w" 	g2="Z,Zacute,Zdotaccent" 	k="27" />
<hkern g1="w" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="45" />
<hkern g1="w" 	g2="B,D,E,F,H,I,K,L,M,N,P,R,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,Thorn,Eogonek,Lslash,Nacute" 	k="8" />
<hkern g1="y,yacute,ydieresis" 	g2="T" 	k="119" />
<hkern g1="y,yacute,ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="76" />
<hkern g1="y,yacute,ydieresis" 	g2="d,q" 	k="18" />
<hkern g1="y,yacute,ydieresis" 	g2="g" 	k="18" />
<hkern g1="y,yacute,ydieresis" 	g2="hyphen,endash,emdash" 	k="25" />
<hkern g1="y,yacute,ydieresis" 	g2="c,e,o,ccedilla,egrave,eacute,ecircumflex,edieresis,ograve,oacute,ocircumflex,otilde,odieresis,oslash,cacute,eogonek,oe" 	k="18" />
<hkern g1="y,yacute,ydieresis" 	g2="guillemotleft,guilsinglleft" 	k="25" />
<hkern g1="y,yacute,ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,Aogonek" 	k="33" />
<hkern g1="y,yacute,ydieresis" 	g2="J" 	k="37" />
<hkern g1="y,yacute,ydieresis" 	g2="Z,Zacute,Zdotaccent" 	k="23" />
<hkern g1="y,yacute,ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="61" />
<hkern g1="y,yacute,ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,aogonek" 	k="16" />
<hkern g1="y,yacute,ydieresis" 	g2="s,sacute" 	k="14" />
<hkern g1="z,zacute,zdotaccent" 	g2="T" 	k="160" />
<hkern g1="z,zacute,zdotaccent" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="8" />
<hkern g1="z,zacute,zdotaccent" 	g2="W" 	k="12" />
<hkern g1="z,zacute,zdotaccent" 	g2="Y,Yacute,Ydieresis" 	k="104" />
<hkern g1="z,zacute,zdotaccent" 	g2="hyphen,endash,emdash" 	k="45" />
<hkern g1="z,zacute,zdotaccent" 	g2="guillemotleft,guilsinglleft" 	k="37" />
<hkern g1="z,zacute,zdotaccent" 	g2="J" 	k="6" />
</font>
</defs></svg> 