from urllib.parse import unquote

from bo_obs.datadog.enums import BooksyTeams
from django.db import transaction
from django.db.utils import IntegrityError
from django.utils.translation import gettext as _
from rest_framework import status

from lib.deeplink import Deep<PERSON><PERSON><PERSON>ache
from lib.feature_flag.feature import AuthPass<PERSON><PERSON>hangeRequiredFlag
from service.tools import <PERSON>questHandler, session
from webapps.marketplace.models import BusinessNetwork
from webapps.user.forms import PasswordChangeForm
from webapps.user.models import User


# warnings disabled because endpoints are DEPRECATED
# pylint: disable=too-many-arguments,unused-argument


class TokenMixin:
    token_name = 'activation'

    def invalid_token_response(self):
        self.set_header('Content-Type', 'text/html')
        self.render('token_invalid.html')

    def check_user(self, user_id, token):
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            self.invalid_token_response()
            return

        function_name = f'check_{self.token_name}_token'
        if not getattr(user, function_name)(token):
            self.invalid_token_response()
            return

        return user


class Change<PERSON><PERSON><PERSON><PERSON><PERSON>(RequestH<PERSON>ler, TokenMixin):
    """
    swagger:
        summary: HTML activate account
        parameters:
            - name: booking_param
              type: string
              paramType: path
              description: template name - c or b
                (customer or business) + (yellow, green)
                so cyellow, cgreen, bgreen, etc.
            - name: user_id
              type: integer
              paramType: path
            - name: new_email
              type: string
              paramType: path
            - name: email_change_token
              type: string
              paramType: path
    """

    token_name = 'email_change'

    @session(api_key_required=False)
    def get(self, booking_param, user_id, new_email, email_change_token, x_api_country=None):
        """Change user's email if data are valid."""
        new_email = unquote(new_email)
        user = self.check_user(user_id, new_email, email_change_token)
        if not user or user.is_staff:
            return

        # user's email is changed already, just save it

        # TODO: integrity error checking when such email already exists
        with transaction.atomic():
            # pylint: disable=no-else-return
            try:
                user.save()
            except IntegrityError:
                self.set_status(status.HTTP_400_BAD_REQUEST)
                self.set_header('Content-Type', 'text/html')
                self.render('email_already_exists.html', user=user)
                return
            else:
                user.delete_all_user_sessions()

        self.set_status(status.HTTP_200_OK)
        self.set_header('Content-Type', 'text/html')
        self.render('email_changed.html')

    def check_user(self, user_id, new_email, token):
        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            self.invalid_token_response()
            return

        user.email = new_email.lower()

        function_name = f'check_{self.token_name}_token'
        if not getattr(user, function_name)(token):
            self.invalid_token_response()
            return

        return user


class PasswordResetHandler(RequestHandler, TokenMixin):
    """
    swagger:
        summary: HTML activate account
        parameters:
            - name: booking_param
              type: string
              paramType: path
              description: template name - c or b
                (customer or business) + (yellow, green)
                so cyellow, cgreen, bgreen, etc.
            - name: user_id
              type: integer
              paramType: path
            - name: password_reset_token
              type: string
              paramType: path
    """

    token_name = 'password_reset'

    PHRASES = {
        'change_title': _('Change password'),
        'change_sub_title': _('Set your password...'),
        'new_password_title': _('New password'),
        'retype_new_password_title': _('Confirm new password'),
        'submit_title': _('Set New Password'),
        'open_booksy_title': _('Go to Booksy'),
        'password_changed_title': _('Your password has been changed'),
        'password_changed_text': _(
            'You can now go to Booksy and login with your new password',
        ),
        'show': _('Show'),
        'hide': _('Hide'),
    }

    @session(api_key_required=False)
    def get(self, booking_param, user_id, password_reset_token, x_api_country=None):
        """Display the change password form"""
        if not (user := User.objects.filter(id=user_id).first()):
            return self.invalid_token_response()

        if not user.validate_password_reset_token(password_reset_token):
            return self.invalid_token_response()

        self.set_header(name='Referrer-Policy', value='no-referrer')
        link = self.links.get(booking_param, '')
        form = PasswordChangeForm()
        self.display_form(form, open_booksy=link)

    @session(api_key_required=False, optional_login=True)
    def post(self, booking_param, user_id, password_reset_token, x_api_country=None):
        if not (user := User.objects.filter(id=user_id).first()):
            return self.invalid_token_response()

        if not user.validate_password_reset_token(password_reset_token):
            return self.invalid_token_response()

        if user.is_staff:
            return self.invalid_token_response()

        link = self.links.get(booking_param, '')

        form = self.get_form()
        if not form.is_valid():
            self.display_form(form, open_booksy=link)
            return

        self.set_status(status.HTTP_200_OK)
        self.set_header('Content-Type', 'text/html')

        if AuthPasswordChangeRequiredFlag():
            user.password_change_required = False

        with transaction.atomic():
            user.set_password(form.cleaned_data['password1'])
            user.save()
            if user.validate_password_reset_token(password_reset_token):
                user.mark_password_reset_token_completed(password_reset_token)

        # delete ALL user sessions
        user.delete_all_user_sessions()

        self.render(
            'password_changed.html',
            open_booksy=link,
            phareses=self.PHRASES,
        )

    @property
    def links(self):
        return {
            'c': DeepLinkCache.get(key='base_customer_app'),
            'b': DeepLinkCache.get(key='base'),
        }

    def get_form(self):
        form = PasswordChangeForm(
            {
                'password1': self.get_argument('password1', default=''),
                'password2': self.get_argument('password2', default=''),
            }
        )

        return form

    def display_form(self, form, open_booksy=None):
        form_error = ''
        if '__all__' in form.errors:
            form_error = form.errors['__all__'][0]

        self.set_status(status.HTTP_200_OK)
        self.set_header('Content-Type', 'text/html')
        self.render(
            'password_change_form.html',
            form=form,
            form_error=form_error,
            open_booksy=open_booksy,
            phareses=self.PHRASES,
        )


class BusinessNetworkHandler(RequestHandler):
    """
    swagger:
        summary: gets business ids of network
        parameters:
            - name: name
              description: name of business network
              type: string
              paramType: path
    """

    booksy_teams = (BooksyTeams.CUSTOMER_SEARCH,)

    @session()
    def get(self, name):
        business_network = BusinessNetwork.objects.filter(slug=name).first()
        if business_network:
            business_ids = business_network.business_ids
            name = business_network.name
        else:
            business_ids = []
            name = ''

        self.finish_with_json(
            status.HTTP_200_OK,
            {
                'business_ids': business_ids,
                'name': name,
            },
        )
