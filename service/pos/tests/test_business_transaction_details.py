from datetime import timed<PERSON><PERSON>

import pytest
from dateutil import tz
from django.db import connection
from django.utils.translation import gettext_lazy as _
from mock import patch
from model_bakery import baker

from lib.test_utils import (
    create_subbooking,
    increase_appointment_next_id,
    user_recipe,
)
from lib.tools import tznow
from service.pos.business_transactions import BusinessTransactionDetailsHandler
from service.pos.tests import create_receipt
from service.pos.tests.common import POSTestsMixin
from service.tests import BaseAsyncHTTPTest
from webapps.adyen.models import Card, Cardholder
from webapps.booking.models import Appointment
from webapps.business.models import (
    Business,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.pos.baker_recipes import payment_method_recipe
from webapps.pos.enums import PaymentTypeEnum, receipt_status as rs
from webapps.pos.models import POS, PaymentType, TaxRate, Tip, Transaction, TransactionTaxSubtotal
from webapps.sequencing_number.enums import SALES_DOCUMENT
from webapps.sequencing_number.models import (
    SequenceRecord,
    SequenceRecordsSettings,
)
from webapps.warehouse.models import (
    Commodity,
    Supply,
    SupplyRow,
    Warehouse,
    WarehouseDocumentType,
)


class BusinessTransactionDetailsHandlerTestCase(POSTestsMixin, BaseAsyncHTTPTest):

    def setUp(self):
        increase_appointment_next_id()
        super().setUp()

    def _create_objects(self):
        business = self.business
        pos = baker.make(
            POS,
            business=business,
            active=True,
            tips_enabled=True,
        )
        self.create_default_tax_rate_20(pos)
        warehouse = self.create_warehouse(self.business)
        supply = self.create_supply(self.business)
        commodity = baker.make(
            Commodity,
            business=self.business,
            net_price=740.77,
            gross_price=740.77,
        )
        baker.make(
            SupplyRow,
            supply=supply,
            net_price=740.77,
            gross_price=740.77,
            tax=0.0,
            warehouse=warehouse,
            commodity=commodity,
        )
        bci = baker.make(
            BusinessCustomerInfo,
            business=business,
            first_name='Jan',
            last_name='Matejko',
            cell_phone='1234567890',
        )
        return bci, business, pos, commodity

    def _handle_biz_tz(self, get_location_name_mock, get_timezone_mock):
        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'  # pylint: disable=protected-access
        get_timezone_mock.return_value = biz_tz
        get_location_name_mock.return_value = 'zxc'

    def _prepare_booking(self, business):
        booking, *_ = create_subbooking(
            business=business,
            booking_kws=dict(  # pylint:disable=use-dict-literal
                type=Appointment.TYPE.BUSINESS,
                booked_from=tznow() - timedelta(hours=1),
                booked_till=tznow(),
                status=Appointment.STATUS.FINISHED,
                source=self.biz_booking_src,
                updated_by=self.user,
            ),
        )
        return booking

    def _get_url(self, business, txn_id, **query):
        end_url = '?' if query else ''
        url = f'/business_api/me/businesses/{business.id}/pos/transactions/{txn_id}{end_url}'
        for key, value in list(query.items()):
            url += f'{key}={value}&'

        return url

    def _get_put_request(self, bci, booking, payment_type_code, product, tip=0):
        return {
            'payment_type_code': payment_type_code,
            'dry_run': False,
            'customer_data': '',
            'customer_id': None,
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': booking.id,
                    'item_price': 200,
                    'discount_rate': 50,
                }
            ],
            'tip_rate': tip,
            'discount_rate': 10,
            'products': [{'discount_rate': 0, 'product_id': product.id, 'quantity': 1}],
            'customer_card_id': bci.id,
        }

    def _get_new_put_request(self, bci, booking, payment_type_code, product, tip):
        return {
            'payment_type_code': payment_type_code,
            'dry_run': False,
            'customer_data': '',
            'customer_id': None,
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': booking.id,
                    'item_price': 200,
                    'discount_rate': 50,
                }
            ],
            'tip': {
                'rate': tip,
                'type': 'P',
            },
            'discount_rate': 10,
            'products': [{'discount_rate': 0, 'product_id': product.id, 'quantity': 1}],
            'customer_card_id': bci.id,
        }

    # pylint: disable=too-many-statements
    @pytest.mark.django_db
    @patch.object(Business, 'get_timezone')
    @patch.object(BusinessTransactionDetailsHandler, 'business_with_advanced_staffer')
    def test_get(self, business_with_staffer_mock, get_timezone_mock):
        pos = baker.make(
            POS,
            business=self.business,
            active=True,
            tips_enabled=True,
        )
        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'  # pylint: disable=protected-access
        get_timezone_mock.return_value = biz_tz
        business_with_staffer_mock.return_value = self.business
        booking = self._prepare_booking(self.business)
        create_receipt(pos, booking, rs.PAYMENT_SUCCESS)

        txn = Transaction.objects.get()
        txn.total = 79.99
        txn.discount_amount = 0
        txn.subtotal = 79.99
        txn.tip_amount = 0
        txn.service_tax_mode = POS.POS_TAX_MODE__EXCLUDED
        txn.save()

        baker.make(
            TransactionTaxSubtotal,
            transaction=txn,
            tax_rate=0,
            tax_amount=0,
            subtotal_type=TransactionTaxSubtotal.SUBTOTAL_TYPE__SERVICES,
        )

        receipt = txn.latest_receipt

        # Update amount in receipt
        cursor = connection.cursor()
        cursor.execute(
            """
            UPDATE pos_receipt
            SET already_paid=79.99
            WHERE receipt_id=%s;
        """,
            (receipt.id,),
        )

        pr = receipt.payment_rows.get()
        pr.amount = txn.total
        pr.save()

        resp = self.fetch(self._get_url(self.business, txn.id))

        assert resp.code == 200

        data = resp.json['transaction']

        assert data['actions']['cancel_deposit'] is False
        assert data['actions']['cancel_payment'] is False
        assert data['actions']['charge_deposit'] is False
        assert data['actions']['send_receipt'] is False
        assert data['actions']['set_payment_type'] is False

        assert data['customer_card_id'] is None
        assert data['customer_data'] == ''
        assert data['customer_id'] is None
        assert data['customer_info'] is None

        assert len(data['receipts']) == 1
        assert data['receipts'][0]['created'] == receipt.created.strftime('%Y-%m-%dT%H:%M')
        assert data['receipts'][0]['card_last_digits'] is None
        assert data['receipts'][0]['card_type'] is None
        assert data['receipts'][0]['id'] == receipt.id
        assert data['receipts'][0]['payment_type'] == {'code': 'cash', 'label': 'Cash'}
        assert data['receipts'][0]['pnref'] is None
        assert data['receipts'][0]['provider'] is None
        sequence = SequenceRecord.objects.filter(
            business=self.business,
            related_document_id=receipt.transaction.id,
        ).first()
        assert data['receipts'][0]['receipt_number'] == sequence.assigned_number
        assert data['receipts'][0]['status_code'] == receipt.status_code
        assert data['receipts'][0]['status_type'] == receipt.status_type

        assert len(data['summaries']) == 4
        assert data['summaries'][0]['label'] == 'Subtotal'
        assert data['summaries'][0]['text'] == '$79.99'
        assert data['summaries'][0]['type'] == 'subtotal'

        # Show when 0
        assert data['summaries'][1]['label'] == 'Discount'
        assert data['summaries'][1]['text'] == '$0.00'
        assert data['summaries'][1]['type'] == 'discount'

        # Show when 0
        assert data['summaries'][2]['label'] == 'Taxes & Fees'
        assert data['summaries'][2]['text'] == '$0.00'
        assert data['summaries'][2]['type'] == 'taxes'

        # Show when 0
        assert data['summaries'][3]['label'] == _('Tip')
        assert data['summaries'][3]['text'] == '$0.00'
        assert data['summaries'][3]['type'] == 'tip'

        assert data['appointment_status'] == 'F'
        assert data['multibooking'] == booking.appointment_id
        assert data['business_address'] == txn.business_address
        assert data['business_name'] == txn.business_name
        assert data['created'] == txn.created.strftime('%Y-%m-%dT%H:%M')
        assert data['updated'] == txn.updated.strftime('%Y-%m-%dT%H:%M')
        assert data['deposit_info'] is None
        assert data['discount_rate'] == 0
        assert data['id'] == txn.id
        assert data['booking'] is None
        assert data['rows'] == []
        assert data['total'] == '$79.99'
        assert data['transaction_type'] == txn.transaction_type
        assert 'commissions_enabled' in data

        # show tip when 0
        assert data['tip'] == {
            "amount_remaining": 0.0,
            "rate": "0.00",
            "type": "P",
            "label": "No Tip",
            "amount": "$0.00",
            "amount_unformatted": "0.00",
        }
        assert not data['is_family_and_friends']

    # pylint: disable=too-many-statements
    @pytest.mark.django_db
    @patch.object(Business, 'get_timezone')
    @patch.object(BusinessTransactionDetailsHandler, 'business_with_advanced_staffer')
    def test_get_included(self, business_with_staffer_mock, get_timezone_mock):
        pos = baker.make(
            POS,
            business=self.business,
            active=True,
            tips_enabled=True,
        )
        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'  # pylint: disable=protected-access
        get_timezone_mock.return_value = biz_tz
        business_with_staffer_mock.return_value = self.business
        booking = self._prepare_booking(self.business)
        create_receipt(pos, booking, rs.PAYMENT_SUCCESS)

        txn = Transaction.objects.get()
        txn.total = 79.99
        txn.discount_amount = 0
        txn.subtotal = 79.99
        txn.tip_amount = 0
        txn.service_tax_mode = POS.POS_TAX_MODE__INCLUDED
        txn.save()

        baker.make(
            TransactionTaxSubtotal,
            transaction=txn,
            tax_rate=0,
            tax_amount=0,
            subtotal_type=TransactionTaxSubtotal.SUBTOTAL_TYPE__SERVICES,
        )

        receipt = txn.latest_receipt

        # Update amount in receipt
        cursor = connection.cursor()
        cursor.execute(
            """
            UPDATE pos_receipt
            SET already_paid=79.99
            WHERE receipt_id=%s;
        """,
            (receipt.id,),
        )

        pr = receipt.payment_rows.get()
        pr.amount = txn.total
        pr.save()

        resp = self.fetch(self._get_url(self.business, txn.id))

        assert resp.code == 200

        data = resp.json['transaction']
        assert len(data['summaries']) == 3
        assert data['summaries'][0]['label'] == 'Subtotal'
        assert data['summaries'][0]['text'] == '$79.99'
        assert data['summaries'][0]['type'] == 'subtotal'

        # Show when 0
        assert data['summaries'][1]['label'] == 'Discount'
        assert data['summaries'][1]['text'] == '$0.00'
        assert data['summaries'][1]['type'] == 'discount'

        # Show when 0
        assert data['summaries'][2]['label'] == _('Tip')
        assert data['summaries'][2]['text'] == '$0.00'
        assert data['summaries'][2]['type'] == 'tip'

    # pylint: disable=too-many-statements
    @pytest.mark.django_db
    @patch.object(Business, 'get_timezone')
    @patch.object(BusinessTransactionDetailsHandler, 'business_with_advanced_staffer')
    def test_get_unfull(self, business_with_staffer_mock, get_timezone_mock):
        pos = baker.make(
            POS,
            business=self.business,
            active=True,
            tips_enabled=True,
        )
        baker.make(
            SequenceRecordsSettings,
            business=self.business,
            type=SALES_DOCUMENT,
            enabled=False,
        )
        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'  # pylint: disable=protected-access
        get_timezone_mock.return_value = biz_tz
        business_with_staffer_mock.return_value = self.business
        booking = self._prepare_booking(self.business)
        create_receipt(pos, booking, rs.PAYMENT_SUCCESS)

        txn = Transaction.objects.get()
        txn.discount_amount = 0
        txn.subtotal = txn.total
        txn.tip_amount = 0
        txn.service_tax_mode = POS.POS_TAX_MODE__EXCLUDED
        txn.save()

        baker.make(
            TransactionTaxSubtotal,
            transaction=txn,
            tax_rate=0,
            tax_amount=0,
            subtotal_type=TransactionTaxSubtotal.SUBTOTAL_TYPE__SERVICES,
        )

        receipt = txn.latest_receipt

        # Update amount in receipt
        cursor = connection.cursor()
        cursor.execute(
            """
            UPDATE pos_receipt
            SET already_paid=51.99
            WHERE receipt_id=%s;
        """,
            (receipt.id,),
        )

        pr = receipt.payment_rows.get()
        pr.amount = 51.99
        pr.save()

        resp = self.fetch(self._get_url(self.business, txn.id))

        assert resp.code == 200

        data = resp.json['transaction']

        assert data['actions']['cancel_deposit'] is False
        assert data['actions']['cancel_payment'] is False
        assert data['actions']['charge_deposit'] is False
        assert data['actions']['send_receipt'] is False
        assert data['actions']['set_payment_type'] is False

        assert data['customer_card_id'] is None
        assert data['customer_data'] == ''
        assert data['customer_id'] is None
        assert data['customer_info'] is None

        assert len(data['receipts']) == 1
        assert data['receipts'][0]['created'] == receipt.created.strftime('%Y-%m-%dT%H:%M')
        assert data['receipts'][0]['card_last_digits'] is None
        assert data['receipts'][0]['card_type'] is None
        assert data['receipts'][0]['id'] == receipt.id
        assert data['receipts'][0]['payment_type'] == {'code': 'cash', 'label': 'Cash'}
        assert data['receipts'][0]['pnref'] is None
        assert data['receipts'][0]['provider'] is None
        sequence = SequenceRecord.objects.filter(
            business=self.business,
            related_document_id=receipt.transaction.id,
        ).first()
        assert sequence is None
        assert data['receipts'][0]['status_code'] == receipt.status_code
        assert data['receipts'][0]['status_type'] == receipt.status_type

        assert len(data['summaries']) == 4
        assert data['summaries'][0]['label'] == 'Subtotal'
        assert data['summaries'][0]['text'] == '$99.99'
        assert data['summaries'][0]['type'] == 'subtotal'

        assert data['summaries'][1]['label'] == 'Discount'
        assert data['summaries'][1]['text'] == '$0.00'
        assert data['summaries'][1]['type'] == 'discount'

        assert data['summaries'][2]['label'] == 'Taxes & Fees'
        assert data['summaries'][2]['text'] == '$0.00'
        assert data['summaries'][2]['type'] == 'taxes'

        assert data['summaries'][3]['label'] == _('Tip')
        assert data['summaries'][3]['text'] == '$0.00'
        assert data['summaries'][3]['type'] == 'tip'

        assert data['appointment_status'] == 'F'
        assert data['multibooking'] == booking.appointment_id
        assert data['business_address'] == txn.business_address
        assert data['business_name'] == txn.business_name
        assert data['created'] == txn.created.strftime('%Y-%m-%dT%H:%M')
        assert data['updated'] == txn.updated.strftime('%Y-%m-%dT%H:%M')
        assert data['deposit_info'] is None
        assert data['discount_rate'] == 0
        assert data['id'] == txn.id
        assert data['booking'] is None
        assert data['rows'] == []
        assert data['total'] == '$51.99'
        assert data['transaction_type'] == txn.transaction_type
        assert 'commissions_enabled' in data
        assert data['tip'] == {
            "amount_remaining": 0.0,
            "rate": "0.00",
            "type": "P",
            "label": "No Tip",
            "amount": "$0.00",
            "amount_unformatted": "0.00",
        }

    @pytest.mark.django_db
    @patch.object(Business, 'get_timezone')
    @patch.object(BusinessTransactionDetailsHandler, 'business_with_advanced_staffer')
    def test_get_show_zero_discount(
        self,
        business_with_staffer_mock,
        get_timezone_mock,
    ):
        pos = baker.make(
            POS,
            business=self.business,
            active=True,
            tips_enabled=True,
        )
        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'  # pylint: disable=protected-access
        get_timezone_mock.return_value = biz_tz
        business_with_staffer_mock.return_value = self.business
        booking = self._prepare_booking(self.business)
        create_receipt(pos, booking, rs.PAYMENT_SUCCESS)

        txn = Transaction.objects.get()
        txn.total = 100
        txn.discount_amount = 0
        txn.subtotal = 100
        txn.tip_amount = 0
        txn.service_tax_mode = POS.POS_TAX_MODE__EXCLUDED
        txn.save()

        baker.make(
            TransactionTaxSubtotal,
            transaction=txn,
            tax_rate=0,
            tax_amount=0,
            subtotal_type=TransactionTaxSubtotal.SUBTOTAL_TYPE__SERVICES,
        )

        url = self._get_url(self.business, txn.id, show_zero_discount=1)
        resp = self.fetch(url)

        assert resp.code == 200

        data = resp.json['transaction']

        assert len(data['summaries']) == 4
        assert data['summaries'][0]['label'] == 'Subtotal'
        assert data['summaries'][0]['text'] == '$100.00'
        assert data['summaries'][0]['type'] == 'subtotal'

        # Show when 0
        assert data['summaries'][1]['label'] == 'Discount'
        assert data['summaries'][1]['text'] == '$0.00'
        assert data['summaries'][1]['type'] == 'discount'

        # Show when 0
        assert data['summaries'][2]['label'] == 'Taxes & Fees'
        assert data['summaries'][2]['text'] == '$0.00'
        assert data['summaries'][2]['type'] == 'taxes'

        # Show when 0
        assert data['summaries'][3]['label'] == _('Tip')
        assert data['summaries'][3]['text'] == '$0.00'
        assert data['summaries'][3]['type'] == 'tip'

        # show tip when 0
        assert data['tip'] == {
            "amount_remaining": 0.0,
            "rate": "0.00",
            "type": "P",
            "label": "No Tip",
            "amount": "$0.00",
            "amount_unformatted": "0.00",
        }

    @pytest.mark.django_db
    @patch.object(Business, 'get_location_name')
    @patch.object(Business, 'get_timezone')
    @patch.object(BusinessTransactionDetailsHandler, 'business_with_advanced_staffer')
    def test_put(
        self,
        business_with_advanced_staffer_mock,
        get_timezone_mock,
        get_location_name_mock,
    ):
        bci, business, pos, product = self._create_objects()
        self._handle_biz_tz(get_location_name_mock, get_timezone_mock)
        business_with_advanced_staffer_mock.return_value = business
        booking = self._prepare_booking(business)
        create_receipt(pos, booking, rs.PAYMENT_SUCCESS)
        txn = Transaction.objects.get()
        first_receipt_number = txn.latest_receipt.receipt_number
        baker.make(PaymentType, code=PaymentTypeEnum.CHECK, pos=pos)

        payment_type_code = PaymentTypeEnum.CHECK
        req = self._get_put_request(bci, booking, payment_type_code, product)
        url = self._get_url(business, txn.id)

        resp = self.fetch(url, body=req, method='PUT')

        assert resp.code == 200

        data = resp.json['transaction']

        # ((booking $200 - 50%) + (product $740.77)) - 10%
        assert data['total'] == '$756.69'
        assert data['multibooking'] == booking.appointment_id
        assert data['discount_rate'] == 10

        old_txn = Transaction.objects.get(id=txn.id)
        assert old_txn.latest_receipt.status_code == rs.ARCHIVED

        assert data['customer_card_id'] == bci.id
        assert data['customer_info']['email'] == bci.email
        assert data['customer_info']['full_name'] == 'Jan Matejko'
        assert data['customer_info']['id'] == bci.id
        assert data['customer_info']['phone'] == bci.cell_phone
        assert data['customer_info']['autopay_enabled'] is None
        assert data['customer_info']['has_pay_by_app'] is False

        new_txn = Transaction.objects.get(id=data['id'])
        assert new_txn.latest_receipt.status_code == rs.PAYMENT_SUCCESS
        assert new_txn.parent_txn == old_txn
        assert new_txn.charge_date == old_txn.charge_date
        assert old_txn.latest_receipt.receipt_number == first_receipt_number
        assert new_txn.latest_receipt.receipt_number != first_receipt_number

    @pytest.mark.django_db
    @patch.object(Business, 'get_location_name')
    @patch.object(Business, 'get_timezone')
    @patch.object(BusinessTransactionDetailsHandler, 'business_with_advanced_staffer')
    def test_put_client_card_labels_should_be_displayed(
        self,
        business_with_advanced_staffer_mock,
        get_timezone_mock,
        get_location_name_mock,
    ):
        bci, business, pos, product = self._create_objects()
        self._handle_biz_tz(get_location_name_mock, get_timezone_mock)
        business_with_advanced_staffer_mock.return_value = business
        booking = self._prepare_booking(business)
        create_receipt(pos, booking, rs.PAYMENT_SUCCESS)
        txn = Transaction.objects.get()
        baker.make(PaymentType, code=PaymentTypeEnum.CHECK, pos=pos)
        bci_user = user_recipe.make(payment_auto_accept=True, cell_phone=bci.cell_phone)
        bci.user = bci_user
        bci.save()
        payment_method_recipe.make(user=bci_user)
        card_holder = baker.make(
            Cardholder, email=bci_user.email, shopper_reference=f'exampleshopper-us-{bci_user.id}'
        )
        baker.make(Card, cardholder=card_holder, active=True)

        payment_type_code = PaymentTypeEnum.CHECK
        req = self._get_put_request(bci, booking, payment_type_code, product)
        url = self._get_url(business, txn.id)

        resp = self.fetch(url, body=req, method='PUT')

        assert resp.code == 200

        data = resp.json['transaction']

        assert data['customer_info']['autopay_enabled'] is True
        assert data['customer_info']['has_pay_by_app'] is True

    @pytest.mark.django_db
    @patch.object(Business, 'get_location_name')
    @patch.object(Business, 'get_timezone')
    @patch.object(BusinessTransactionDetailsHandler, 'business_with_advanced_staffer')
    def test_put_pba_transaction(
        self,
        business_with_advanced_staffer_mock,
        get_timezone_mock,
        get_location_name_mock,
    ):
        bci, business, pos, product = self._create_objects()
        self._handle_biz_tz(get_location_name_mock, get_timezone_mock)
        business_with_advanced_staffer_mock.return_value = business
        booking = self._prepare_booking(business)
        create_receipt(
            pos,
            booking,
            rs.PAYMENT_SUCCESS,
            payment_type_code=PaymentTypeEnum.PAY_BY_APP,
        )
        txn_id = Transaction.objects.get().id
        baker.make(PaymentType, code='cash', pos=pos)

        payment_type_code = PaymentTypeEnum.CASH
        req = self._get_put_request(bci, booking, payment_type_code, product)

        url = self._get_url(business, txn_id)
        resp = self.fetch(url, body=req, method='PUT')

        assert resp.code == 400

    @pytest.mark.django_db
    @patch.object(Business, 'get_location_name')
    @patch.object(Business, 'get_timezone')
    @patch.object(BusinessTransactionDetailsHandler, 'business_with_advanced_staffer')
    def test_put_not_paid_transaction(
        self,
        business_with_advanced_staffer_mock,
        get_timezone_mock,
        get_location_name_mock,
    ):
        bci, business, pos, product = self._create_objects()
        self._handle_biz_tz(get_location_name_mock, get_timezone_mock)
        business_with_advanced_staffer_mock.return_value = business
        booking = self._prepare_booking(business)
        create_receipt(pos, booking, rs.PAYMENT_CANCELED)
        txn_id = Transaction.objects.get().id

        payment_type_code = PaymentTypeEnum.CASH
        req = self._get_put_request(bci, booking, payment_type_code, product)

        url = self._get_url(business, txn_id)
        resp = self.fetch(url, body=req, method='PUT')

        assert resp.code == 200  # Now cancelled receipts are editable

    @pytest.mark.django_db
    @patch.object(Business, 'get_location_name')
    @patch.object(Business, 'get_timezone')
    @patch.object(BusinessTransactionDetailsHandler, 'business_with_manager')
    def test_delete(
        self,
        business_with_manager_mock,
        get_timezone_mock,
        get_location_name_mock,
    ):
        _, business, pos, _ = self._create_objects()
        self._handle_biz_tz(get_location_name_mock, get_timezone_mock)
        business_with_manager_mock.return_value = business
        booking = self._prepare_booking(business)
        create_receipt(pos, booking, rs.PAYMENT_SUCCESS)
        txn_id = Transaction.objects.get().id

        url = self._get_url(business, txn_id)
        resp = self.fetch(url, method='DELETE')

        assert resp.code == 200

        txn = Transaction.objects.get()
        assert txn.latest_receipt.status_code == rs.ARCHIVED


@pytest.mark.django_db
class BusinessTransactionDetailsHandlerTipsTestCase(BaseAsyncHTTPTest):

    def setUp(self):
        increase_appointment_next_id()

        super().setUp()
        self.pos = baker.make(
            POS,
            business=self.business,
            active=True,
            service_tax_mode=POS.POS_TAX_MODE__INCLUDED,
            product_tax_mode=POS.POS_TAX_MODE__INCLUDED,
            voucher_tax_mode=POS.POS_TAX_MODE__INCLUDED,
        )
        baker.make(TaxRate, pos=self.pos, default_for_service=True, rate=20)
        warehouse = baker.make(
            Warehouse,
            business=self.business,
            is_default=True,
        )
        supply = baker.make(
            Supply,
            business=self.business,
            type=WarehouseDocumentType.PZ,
        )
        self.commodity = baker.make(Commodity, business=self.business)
        baker.make(
            SupplyRow,
            supply=supply,
            net_price=740.77,
            gross_price=740.77,
            tax=0.0,
            warehouse=warehouse,
            commodity=self.commodity,
        )
        self.bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='Jan',
            last_name='Matejko',
            cell_phone='1234567890',
        )

        self.tip = baker.make(Tip, pos=self.pos, default=True, rate=61)

        self.booking, *_ = create_subbooking(
            business=self.business,
            booking_kws=dict(  # pylint:disable=use-dict-literal
                type=Appointment.TYPE.BUSINESS,
                booked_from=tznow() - timedelta(hours=1),
                booked_till=tznow(),
                status=Appointment.STATUS.FINISHED,
                source=self.biz_booking_src,
                updated_by=self.user,
            ),
        )

        self.txn = create_receipt(self.pos, self.booking, rs.PAYMENT_SUCCESS)
        baker.make(PaymentType, code=PaymentTypeEnum.CHECK, pos=self.pos)
        self.payment_type_code = PaymentTypeEnum.CHECK

    @staticmethod
    def _handle_biz_tz(get_location_name_mock, get_timezone_mock):
        biz_tz = tz.gettz('UTC')
        biz_tz._long_name = 'UTC'  # pylint: disable=protected-access
        get_timezone_mock.return_value = biz_tz
        get_location_name_mock.return_value = 'zxc'

    @staticmethod
    def _get_url(business, txn_id, **query):
        url_end = '?' if query else ''
        url = f'/business_api/me/businesses/{business.id}/pos/transactions/{txn_id}{url_end}'
        for key, value in list(query.items()):
            url += f'{key}={value}&'

        return url

    @staticmethod
    def _get_put_request(bci, booking, payment_type_code, product, tip=0):
        return {
            'payment_type_code': payment_type_code,
            'dry_run': False,
            'customer_data': '',
            'customer_id': None,
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': booking.id,
                    'item_price': 200,
                    'discount_rate': 50,
                }
            ],
            'tip_rate': tip,
            'discount_rate': 10,
            'products': [{'discount_rate': 0, 'product_id': product.id, 'quantity': 1}],
            'customer_card_id': bci.id,
        }

    @staticmethod
    def _get_new_put_request(bci, booking, payment_type_code, product, tip):
        return {
            'payment_type_code': payment_type_code,
            'dry_run': False,
            'customer_data': '',
            'customer_id': None,
            'transaction_type': 'P',
            'bookings': [
                {
                    'booking_id': booking.id,
                    'item_price': 200,
                    'discount_rate': 50,
                }
            ],
            'tip': {
                'rate': tip,
                'type': 'P',
            },
            'discount_rate': 10,
            'products': [{'discount_rate': 0, 'product_id': product.id, 'quantity': 1}],
            'customer_card_id': bci.id,
        }

    @patch.object(Business, 'get_location_name')
    @patch.object(Business, 'get_timezone')
    @patch.object(BusinessTransactionDetailsHandler, 'business_with_advanced_staffer')
    def test_get(
        self,
        business_with_staffer_mock,
        get_timezone_mock,
        get_location_name_mock,
    ):
        self._handle_biz_tz(get_location_name_mock, get_timezone_mock)
        business_with_staffer_mock.return_value = self.business

        resp = self.fetch(self._get_url(self.business, self.txn.id))
        assert resp.code == 200

        data = resp.json['transaction']
        assert 'tip' not in data

        assert len(data['summaries']) == 2
        assert data['summaries'] == [
            {"type": "label", "label": "Subtotal", "text": None},
            {
                "type": "discount",
                "label": "Discount",
                "text": "$0.00",
                "amount": 0.0,
                "value_float": 0.0,
            },
        ]

    @patch.object(Business, 'get_location_name')
    @patch.object(Business, 'get_timezone')
    @patch.object(BusinessTransactionDetailsHandler, 'business_with_advanced_staffer')
    def test_get_with_tips(
        self,
        business_with_staffer_mock,
        get_timezone_mock,
        get_location_name_mock,
    ):
        self._handle_biz_tz(get_location_name_mock, get_timezone_mock)
        business_with_staffer_mock.return_value = self.business

        self.pos.tips_enabled = True
        self.pos.save()

        resp = self.fetch(self._get_url(self.business, self.txn.id))
        assert resp.code == 200

        data = resp.json['transaction']
        assert data['tip']['rate'] == '0.00'
        assert data['tip']['type'] == 'P'

        assert len(data['summaries']) == 3
        assert data['summaries'] == [
            {"type": "label", "label": "Subtotal", "text": None},
            {
                "type": "discount",
                "label": "Discount",
                "text": "$0.00",
                "amount": 0.0,
                "value_float": 0.0,
            },
            {
                "type": "tip",
                "label": _("Tip"),
                "text": "$0.00",
                "value_float": 0.0,
                "tip_base": None,
            },
        ]

    @patch.object(Business, 'get_location_name')
    @patch.object(Business, 'get_timezone')
    @patch.object(BusinessTransactionDetailsHandler, 'business_with_advanced_staffer')
    def test_put_tips_turned_off(
        self,
        business_with_staffer_mock,
        get_timezone_mock,
        get_location_name_mock,
    ):
        self._handle_biz_tz(get_location_name_mock, get_timezone_mock)
        business_with_staffer_mock.return_value = self.business

        req = self._get_put_request(self.bci, self.booking, self.payment_type_code, self.commodity)
        url = self._get_url(self.business, self.txn.id)

        resp = self.fetch(url, body=req, method='PUT')
        assert resp.code == 200

        data = resp.json['transaction']
        assert 'tip' not in data
        assert len(data['summaries']) == 2
        assert data['summaries'] == [
            {'label': 'Subtotal', 'text': '$100.00', 'type': 'subtotal', 'value_float': 100.0},
            {
                'amount': 10.0,
                'label': 'Discount',
                'text': '-$10.00',
                'type': 'discount',
                'value_float': 10.0,
            },
        ]

    @patch.object(Business, 'get_location_name')
    @patch.object(Business, 'get_timezone')
    @patch.object(BusinessTransactionDetailsHandler, 'business_with_advanced_staffer')
    def test_put_with_tips_old_format(
        self,
        business_with_staffer_mock,
        get_timezone_mock,
        get_location_name_mock,
    ):
        # Turn on tips
        self.pos.tips_enabled = True
        self.pos.save()

        self._handle_biz_tz(get_location_name_mock, get_timezone_mock)
        business_with_staffer_mock.return_value = self.business

        req = self._get_put_request(
            self.bci, self.booking, self.payment_type_code, self.commodity, 10
        )
        url = self._get_url(self.business, self.txn.id)

        resp = self.fetch(url, body=req, method='PUT')
        assert resp.code == 200

        data = resp.json['transaction']

        # ignore tip_rate key
        assert data['tip']['rate'] == '61.00'
        assert data['tip']['type'] == 'P'

    @patch.object(Business, 'get_location_name')
    @patch.object(Business, 'get_timezone')
    @patch.object(BusinessTransactionDetailsHandler, 'business_with_advanced_staffer')
    def test_put_with_tips_new_format(
        self,
        business_with_staffer_mock,
        get_timezone_mock,
        get_location_name_mock,
    ):
        # Turn on tips
        self.pos.tips_enabled = True
        self.pos.save()

        self._handle_biz_tz(get_location_name_mock, get_timezone_mock)
        business_with_staffer_mock.return_value = self.business
        req = self._get_new_put_request(
            self.bci, self.booking, self.payment_type_code, self.commodity, 10
        )
        url = self._get_url(self.business, self.txn.id)

        resp = self.fetch(url, body=req, method='PUT')

        assert resp.code == 200
        data = resp.json['transaction']

        assert data['tip']['rate'] == '10.00'
        assert data['tip']['type'] == 'P'

        assert len(data['summaries']) == 3
        assert data['summaries'][2]['label'] == _('Tip')
        assert data['summaries'][2]['text'] == '$10.00'
        assert data['summaries'][2]['type'] == 'tip'

    @patch.object(Business, 'get_location_name')
    @patch.object(Business, 'get_timezone')
    @patch.object(BusinessTransactionDetailsHandler, 'business_with_advanced_staffer')
    def test_put_with_zero_value(
        self,
        business_with_staffer_mock,
        get_timezone_mock,
        get_location_name_mock,
    ):
        # Turn on tips
        self.pos.tips_enabled = True
        self.pos.save()

        self._handle_biz_tz(get_location_name_mock, get_timezone_mock)
        business_with_staffer_mock.return_value = self.business
        req = self._get_new_put_request(
            self.bci, self.booking, self.payment_type_code, self.commodity, tip=0
        )
        url = self._get_url(self.business, self.txn.id)

        resp = self.fetch(url, body=req, method='PUT')

        assert resp.code == 200
        data = resp.json['transaction']

        assert data['tip']['rate'] == '0.00'
        assert data['tip']['type'] == 'P'

        assert len(data['summaries']) == 3
        assert data['summaries'][2]['label'] == _('Tip')
        assert data['summaries'][2]['text'] == '$0.00'
        assert data['summaries'][2]['type'] == 'tip'

    @patch.object(Business, 'get_location_name')
    @patch.object(Business, 'get_timezone')
    @patch.object(BusinessTransactionDetailsHandler, 'business_with_advanced_staffer')
    def test_put_with_tips_new_format_rest_of(
        self,
        business_with_staffer_mock,
        get_timezone_mock,
        get_location_name_mock,
    ):
        # Turn on tips
        self.pos.tips_enabled = True
        self.pos.save()

        self._handle_biz_tz(get_location_name_mock, get_timezone_mock)
        business_with_staffer_mock.return_value = self.business
        req = self._get_new_put_request(
            self.bci, self.booking, self.payment_type_code, self.commodity, 10
        )

        # Change tip to RestOf type
        req['tip'] = {
            'rate': 800,
            'type': 'R',
        }

        url = self._get_url(self.business, self.txn.id)

        resp = self.fetch(url, body=req, method='PUT')

        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'invalid_choice'

    @patch.object(Business, 'get_location_name')
    @patch.object(Business, 'get_timezone')
    @patch.object(BusinessTransactionDetailsHandler, 'business_with_advanced_staffer')
    def test_put_with_tips_new_format_by_hand(
        self,
        business_with_staffer_mock,
        get_timezone_mock,
        get_location_name_mock,
    ):
        # Turn on tips
        self.pos.tips_enabled = True
        self.pos.save()

        self._handle_biz_tz(get_location_name_mock, get_timezone_mock)
        business_with_staffer_mock.return_value = self.business
        req = self._get_new_put_request(
            self.bci, self.booking, self.payment_type_code, self.commodity, 10
        )

        # Change tip to RestOf type
        req['tip'] = {
            'rate': 133,
            'type': 'H',
        }

        url = self._get_url(self.business, self.txn.id)

        resp = self.fetch(url, body=req, method='PUT')

        assert resp.code == 200
        data = resp.json['transaction']

        assert data['tip']['rate'] == '133.00'
        assert data['tip']['type'] == 'H'

        assert len(data['summaries']) == 3
        assert data['summaries'][2]['label'] == _('Tip')
        assert data['summaries'][2]['text'] == '$133.00'
        assert data['summaries'][2]['type'] == 'tip'
