import datetime
from decimal import Decimal

import pytest
from dateutil.relativedelta import relativedelta
from model_bakery import baker, seq
from model_bakery.recipe import Recipe
from rest_framework import status

from service.tests import BaseAsyncHTTPTest
from webapps.business.baker_recipes import service_variant_recipe
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.pos.enums import PaymentTypeEnum
from webapps.pos.models import (
    POS,
    PaymentType,
    TaxRate,
    Transaction,
    TransactionRow,
)
from webapps.voucher.helpers import create_simple_redeem
from webapps.voucher.models import (
    Voucher,
    VoucherTemplate,
    VoucherTemplateServiceVariant,
    VoucherServiceVariant,
)


@pytest.mark.django_db
@pytest.mark.usefixtures('default_voucher_background')
class CustomerVoucherListHandlerTests(BaseAsyncHTTPTest):
    url = '/customer_api/me/vouchers/'

    def setUp(self):
        super().setUp()
        self.pos = baker.make(POS, business=self.business)
        self.giftcard_template = baker.make(
            VoucherTemplate,
            pos=self.pos,
            type=Voucher.VOUCHER_TYPE__EGIFT_CARD,
        )
        self.voucher_recipe = Recipe(Voucher, code=seq(''), voucher_template=self.giftcard_template)

    def _make_voucher(self, data):
        transaction = baker.make(Transaction, pos=self.pos, customer=self.user)
        voucher = self.voucher_recipe.make(**data)
        baker.make(TransactionRow, transaction=transaction, voucher=voucher)
        return voucher

    def test_get(self):
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            user=self.user,
        )

        v_1 = self._make_voucher(
            dict(
                status=Voucher.ACTIVE,
                current_balance=Decimal('10.00'),
                valid_till=datetime.date.today(),
                customer=bci,
            )
        )
        v_2 = self._make_voucher(
            dict(
                status=Voucher.EXPIRED,  # expired
                current_balance=Decimal('10.00'),
                valid_till=datetime.date.today(),
                customer=bci,
            )
        )
        v_3 = self._make_voucher(
            dict(
                status=Voucher.ACTIVE,
                current_balance=Decimal('10.00'),
                valid_till=datetime.date.today(),
                customer=bci,
            )
        )
        v_4 = self._make_voucher(
            dict(
                status=Voucher.ACTIVE,
                current_balance=Decimal('0.00'),  # zero balance
                valid_till=datetime.date.today(),
                customer=bci,
            )
        )
        v_5 = self._make_voucher(
            dict(
                status=Voucher.ACTIVE,
                current_balance=Decimal('10.00'),
                valid_till=datetime.date.today() - datetime.timedelta(days=1),
                customer=bci,
            )
        )

        # Expected order:
        # 1) v_3 (valid, newer)
        # 2) v_1
        # 3) v_5 (was valid till yesterday)
        # 4) v_4 (zero balance)
        # 5) v_2 (expired status)

        response = self.fetch(self.url, method='GET')
        assert response.code == status.HTTP_200_OK
        ids = [v['id'] for v in response.json['vouchers']]
        assert ids == [v_3.id, v_1.id, v_5.id, v_4.id, v_2.id]

    def test_get_voucher_service_variant_source(self):
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            user=self.user,
        )

        voucher = self._make_voucher(
            dict(
                status=Voucher.ACTIVE,
                current_balance=Decimal('10.00'),
                valid_till=datetime.date.today(),
                customer=bci,
            )
        )
        VoucherTemplateServiceVariant.objects.create(
            service_variant=service_variant_recipe.make(), voucher_template=voucher.voucher_template
        )
        voucher_service_variant = VoucherServiceVariant.objects.create(
            service_variant=service_variant_recipe.make(duration=relativedelta(minutes=16)),
            voucher=voucher,
            amount=2,
            item_price=19.28,
            initial_amount=30,
        )

        url = self.url.format(voucher_id=voucher.id)

        resp = self.fetch(url, method='GET')

        assert resp.code == status.HTTP_200_OK

        self.assertDictEqual(
            resp.json['vouchers'][0]['services'][0],
            {
                'service_variant_id': voucher_service_variant.service_variant.id,
                'name': voucher_service_variant.service_variant.service.name,
                'service_variant_duration': 16,
                'service_variant_name': voucher_service_variant.service_variant.label,
            },
        )


@pytest.mark.django_db
@pytest.mark.usefixtures('default_voucher_background')
class CustomerVoucherDetailsHandlerTests(BaseAsyncHTTPTest):
    url = '/customer_api/me/vouchers/{voucher_id}/'

    def setUp(self):
        super().setUp()
        self.pos = baker.make(POS, business=self.business)
        self.voucher_recipe = Recipe(
            Voucher,
            code=seq(''),
            pos=self.pos,
        )

    def _make_voucher(self, data):
        transaction = baker.make(Transaction, pos=self.pos, customer=self.user)
        voucher = self.voucher_recipe.make(**data)
        baker.make(TransactionRow, transaction=transaction, voucher=voucher)
        return voucher

    def test_get(self):
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            user=self.user,
        )

        voucher = self._make_voucher(
            dict(
                status=Voucher.ACTIVE,
                current_balance=Decimal('10.00'),
                valid_till=datetime.date.today(),
                customer=bci,
            )
        )
        url = self.url.format(voucher_id=voucher.id)
        response = self.fetch(url, method='GET')
        assert response.code == status.HTTP_200_OK
        assert response.json

    def test_get_voucher_service_variant_source(self):
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            user=self.user,
        )

        voucher = self._make_voucher(
            dict(
                status=Voucher.ACTIVE,
                current_balance=Decimal('10.00'),
                valid_till=datetime.date.today(),
                customer=bci,
            )
        )
        VoucherTemplateServiceVariant.objects.create(
            service_variant=service_variant_recipe.make(), voucher_template=voucher.voucher_template
        )
        voucher_service_variant = VoucherServiceVariant.objects.create(
            service_variant=service_variant_recipe.make(duration=relativedelta(minutes=16)),
            voucher=voucher,
            amount=2,
            item_price=19.28,
            initial_amount=30,
        )

        url = self.url.format(voucher_id=voucher.id)

        resp = self.fetch(url, method='GET')

        assert resp.code == status.HTTP_200_OK

        self.assertDictEqual(
            resp.json['voucher']['services'][0],
            {
                'service_variant_id': voucher_service_variant.service_variant.id,
                'name': voucher_service_variant.service_variant.service.name,
                'service_variant_duration': 16,
                'service_variant_name': voucher_service_variant.service_variant.label,
            },
        )

    def test_get_with_redeem(self):
        baker.make(
            TaxRate,
            pos=self.pos,
            rate=20,
            default_for_service=True,
            default_for_product=True,
        )
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.EGIFT_CARD)

        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            user=self.user,
        )

        voucher = self._make_voucher(
            dict(
                status=Voucher.ACTIVE,
                current_balance=Decimal('10.00'),
                valid_till=datetime.date.today(),
                customer=bci,
                voucher_template=baker.make(VoucherTemplate, type=Voucher.VOUCHER_TYPE__EGIFT_CARD),
            )
        )

        create_simple_redeem(
            voucher=voucher,
            amount=Decimal('2.00'),
            operator=self.user,
            services=None,
            selected_register_id=None,
        )

        url = self.url.format(voucher_id=voucher.id)
        response = self.fetch(url, method='GET')
        assert response.code == status.HTTP_200_OK
        assert response.json
