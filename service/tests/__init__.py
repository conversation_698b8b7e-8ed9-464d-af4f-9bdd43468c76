import json

import pytest
import requests
from django.conf import settings
from django.contrib.auth.hashers import make_password
from django.core.cache import cache
from django.test import override_settings
from django.utils.crypto import get_random_string
from django.utils.translation import (
    get_language,
    gettext as _,
)
from model_bakery import baker
from tornado.testing import AsyncHTTPTestCase

import versions
from lib.baker_utils import get_or_create_booking_source
from lib.elasticsearch.tools import ESJSONEncoder
from lib.test_utils import timezone_for_tests, BaseTestUtilMixin
from lib.tools import l_b
from service import run
from webapps.booking.models import BookingSources
from webapps.business.models import Business, Resource
from webapps.business.searchables.tests.utils import (
    MIDDLE_OF_DESERT,
    generate_business_name,
)
from webapps.business.staffer_name_generator import generate_first_last_name
from webapps.business_related.models import SecuritySettings
from webapps.ecommerce.models import EcommercePermission
from webapps.ecommerce.enums import EcommercePermissionsEnum
from webapps.elasticsearch.tests.elasticsearch_test_helpers import ElasticSearchTestCaseMixin
from webapps.search_engine_tuning.models import UserTuning
from webapps.structure.models import Region
from webapps.user.elasticsearch.user import UserDocument
from webapps.user.enums import AuthOriginEnum
from webapps.user.models import User, UserProfile
from webapps.pos.baker_recipes import default_pos_recipe
from webapps.payment_gateway.scripts import create_default_wallets


class TornadoFetchTestCase(BaseTestUtilMixin, AsyncHTTPTestCase):
    content_type = 'application/json; charset=UTF-8'
    x_api_country = None  # default: settings.API_COUNTRY
    customer_api_key = 'customer_key'
    business_api_key = 'biz_key'
    internal_api_key = 'internal_key'
    performance_test_api_key = 'performance_test_key'
    time_zone_name = None

    def get_app(self):
        return run.tornado_app

    def get_x_api_country(self):
        return self.x_api_country or settings.API_COUNTRY

    def get_headers(self, path):
        if '/customer_api/' in path:
            x_api_key = self.customer_api_key
        elif '/business_api/' in path:
            x_api_key = self.business_api_key
        elif '/internal/' in path:
            x_api_key = self.internal_api_key
        else:
            x_api_key = self.business_api_key

        return {
            'Content-Type': self.content_type,
            'X-API-Country': self.get_x_api_country(),
            'X-API-KEY': x_api_key,
            'X-ACCESS-TOKEN': self.session.session_key,
        }

    def fetch(self, path, **kwargs):

        if not (path.startswith('/api/') or path.startswith('/core/v')):
            path = f'/core/v{versions.VERSION}{path}'

        method = kwargs.pop('method', 'GET')

        if 'headers' in kwargs:
            headers = kwargs.pop('headers')
        else:
            headers = self.get_headers(path)

        if 'extra_headers' in kwargs:
            headers.update(kwargs.pop('extra_headers'))

        args = kwargs.pop('args', None)
        if args:
            args = '&'.join([f'{i}={j}' for i, j in list(args.items())])
            path = f'{path}?{args}'

        if issubclass(type(kwargs.get('body')), dict) or isinstance(kwargs.get('body'), dict):
            kwargs['body'] = json.dumps(kwargs['body'], cls=ESJSONEncoder)

        if not kwargs.pop('skip_basic_auth', False):
            kwargs['auth_username'], kwargs['auth_password'] = settings.GOOGLE_BOOKING_API_AUTH
            kwargs['auth_mode'] = 'basic'

        print(f'URL:\n{path}\nHEADERS:\n{headers!r}\nBODY:\n{kwargs.get("body")!r}')

        resp = super().fetch(path, method=method, headers=headers, **kwargs)

        print(f'RESPONSE CODE: {resp.code}\nRESPONSE:\n{resp.body!r}')

        try:
            resp.json = json.loads(l_b(resp.body))
        except (ValueError, TypeError):
            pass

        return resp


class BaseAsyncHTTPTest(TornadoFetchTestCase):
    """
    Look at my base class, my base class is amazing.
    Give it a lick! [Mmm! It tastes just like raisins.]
    """

    def get_customer_booking_source(self):
        source = getattr(self, 'customer_booking_src', None)
        if source:
            return source
        self.customer_booking_src = get_or_create_booking_source(
            app_type=BookingSources.CUSTOMER_APP,
            api_key=self.customer_api_key,
        )
        return self.customer_booking_src

    def reindex_user(self):
        self.user.profiles.get_or_create(
            profile_type=UserProfile.Type.CUSTOMER,
            defaults={
                'source': self.customer_booking_src,
            },
        )
        UserTuning.update_multiple_tunings([self.user.id])
        UserDocument.refresh_index()

    @override_settings(RATE_LIMIT_TIMEOUT=0)
    def setUp(self):
        from webapps.booking.tests.test_appointment_base import BaseTestAppointment

        time_zone_name = self.get_time_zone_name()
        first_name, last_name = generate_first_last_name()
        self.user = baker.prepare(
            User,
            first_name=first_name,
            last_name=last_name,
            email="<EMAIL>",
            password=make_password('some_password'),
        )
        self.user.set_password(self.user.password)
        self.user.save()
        self.biz_booking_src = get_or_create_booking_source(
            app_type=BookingSources.BUSINESS_APP,
            api_key=self.business_api_key,
        )
        self.internal_booking_src = get_or_create_booking_source(
            app_type=BookingSources.INTERNAL_APP,
            api_key=self.internal_api_key,
        )
        self.customer_booking_src = self.get_customer_booking_source()

        baker.make(
            UserProfile,
            user=self.user,
            source=None,
            city=None,
            profile_type=UserProfile.Type.BUSINESS,
            language=get_language(),
        )

        default_pos_recipe.make()
        create_default_wallets()

        self.region = baker.make(
            Region,
            name='A',
            type=Region.Type.ZIP,
            time_zone_name=time_zone_name,
        )
        name = generate_business_name()
        name_short = name.rsplit(':', maxsplit=1)[-1].strip()
        self.business: Business = baker.make(
            Business,
            owner=self.user,
            booking_mode=Business.BookingMode.MANUAL,
            name=name,
            name_short=name_short,
            address='asdf',
            address2='asdf2',
            city='Warszawa najlepsze miasto świata',
            active=True,
            status=Business.Status.PAID,
            region=self.region,
            time_zone_name=time_zone_name,
            latitude=MIDDLE_OF_DESERT[0],
            longitude=MIDDLE_OF_DESERT[1],
        )
        if not isinstance(self, BaseTestAppointment):
            # create owner for business
            # if class do not inherited from BaseTestAppointment
            # because in it  different owner is created; see
            # webapps/booking/tests/test_appointment_base.py:192
            #
            self.owner = baker.make(
                Resource,
                visible=True,
                type=Resource.STAFF,
                business=self.business,
                staff_access_level=Resource.STAFF_ACCESS_LEVEL_OWNER,
                staff_user=self.user,
                staff_email=self.user.email,
                reviews_rank_avg=3.1415,
                position='Senior Barber',
            )
            baker.make(
                EcommercePermission,
                resource=self.owner,
                permission=EcommercePermissionsEnum.STORE_AVAILABLE,
            )
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        return super().setUp()

    @pytest.fixture(autouse=True)
    def capsys(self, capsys):
        self.capsys = capsys

    def check_errors_in_logs(self):
        _, err = self.capsys.readouterr()
        assert 'STATUS: 500' not in err
        assert 'AssertionError:' not in err

    @property
    def logged_in_user_id(self):
        return self.session._session_cache['user_id']  # pylint: disable=protected-access


class ElasticBaseTestCase(BaseAsyncHTTPTest, ElasticSearchTestCaseMixin): ...


class ApiClient(BaseAsyncHTTPTest):
    """ApiClient for use in pytest functional tests

    Clean Client, without models. Configure models using fixtures.
    """

    content_type = 'application/json; charset=UTF-8'
    x_api_country = None  # default: settings.API_COUNTRY
    customer_api_key = 'customer_key'
    business_api_key = 'biz_key'
    internal_api_key = 'internal_key'

    def set_session(self, user: User):
        self.session = user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        self.biz_booking_src = get_or_create_booking_source(
            app_type=BookingSources.BUSINESS_APP,
            api_key=self.business_api_key,
        )
        self.internal_booking_src = get_or_create_booking_source(
            app_type=BookingSources.INTERNAL_APP,
            api_key=self.internal_api_key,
        )
        self.customer_booking_src = get_or_create_booking_source(
            app_type=BookingSources.CUSTOMER_APP,
            api_key=self.customer_api_key,
        )

    def setUp(self):
        # dont use db in here
        return AsyncHTTPTestCase.setUp(self)

    def tearDown(self):
        return AsyncHTTPTestCase.tearDown(self)


def dict_assert(actual, expected, strict=False):
    """
    Util to test actual_dict and expected_dict

    If strict==True then any difference between (no keys in one of
     tested values) actual_dict and expected_dict result in AssertionError

    If strict==False then only difference in listed values will
     raise AssertionError
    """
    # pylint: disable=too-many-branches,broad-exception-raised
    if isinstance(actual, list):
        if type(actual) != type(expected):  # pylint: disable=unidiomatic-typecheck
            message = 'AssertionError actual and expected values are different types'
            raise AssertionError(message)
        for index, item in enumerate(actual):
            dict_assert(item, expected[index])
        return
    if strict:
        actual_dict_keys = set(actual.keys())
        expected_dict_keys = set(expected.keys())
        try:
            assert actual_dict_keys == expected_dict_keys
        except AssertionError:
            difference = actual_dict_keys - expected_dict_keys
            difference.update(expected_dict_keys - actual_dict_keys)
            raise AssertionError(f'Missing keys: {difference}')  # pylint: disable=W0707

    for key, expected_value in expected.items():
        if isinstance(expected_value, dict):
            dict_assert(
                actual[key],
                expected[key],
                strict=strict,
            )
        elif actual is None:
            assert actual == expected_value
        else:
            actual_value = actual[key]
            if isinstance(actual_value, (list, tuple)):
                if not isinstance(expected_value, (list, tuple)):
                    raise AssertionError(f'AssertionError for key {key}, different types')
                if len(actual_value) != len(expected_value):
                    message = (
                        f'AssertionError for key = {key} '
                        f'expected_lenght = {len(expected_value)} '
                        f'actual_lenght = {len(actual_value)}'
                    )
                    raise AssertionError(message)
                for index, row in enumerate(actual_value):
                    if isinstance(row, (list, tuple, dict)):
                        dict_assert(actual_value[index], expected_value[index])
                    else:
                        _simple_assert(actual_value, expected_value, key)
            else:
                _simple_assert(actual_value, expected_value, key)


def _simple_assert(actual_value, expected_value, key):
    try:
        assert actual_value == expected_value
    except AssertionError as e:
        message = f'AssertionError for key {key}, {actual_value} != {expected_value}'
        raise AssertionError(message) from e  # pylint: disable=broad-exception-raised


def get_request(url, valid=True, data=None):
    if valid:
        path = f'{settings.PROJECT_PATH}/statics/img/placeholders/barbers.jpg'
    else:
        path = f'{settings.PROJECT_PATH}/statics/css/admin_login.css'
    with open(path, 'rb') as file_stream:
        img = file_stream.read()
    files = {'image.jpg': img}
    if not data:
        data = {'category': 'logo'}
    return requests.Request(url=f'http://localhost/{url}', files=files, data=data).prepare()


class SecuritySettingsMixin:
    @staticmethod
    def assert_ip_not_allowed_error_description(response):
        error_description = response.json['errors'][0]['description']
        assert error_description == _('Access Denied')

    @staticmethod
    def prepare_security_settings(user, business=None):
        business = business or baker.make(
            Business,
            active=True,
            status=Business.Status.TRIAL,
            deleted=None,
        )
        baker.make(Resource, business=business, staff_user=user)
        baker.make(
            SecuritySettings,
            business=business,
            allowed_ips=['************/24'],
        )
        return business

    def prepare_headers_with_real_ip(self, real_ip):
        headers = self.get_headers(self.url)
        headers['x-real-ip'] = real_ip
        return headers


def get_cache_format():
    return f'throttle_{get_random_string(5)}_%(scope)s_%(ident)s'


def clear_cache_format(cache_format):
    cache_format = cache_format.rsplit('_', 2)[0]
    cache.delete_many(cache.keys(f'{cache_format}_*'))
