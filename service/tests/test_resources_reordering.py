#!/usr/bin/env python
import datetime
import json
from time import sleep

import pytest
from freezegun import freeze_time
from model_bakery import baker
from parameterized import parameterized
from pytest_django import asserts

from lib.tools import l_b
from lib.feature_flag.feature.business import IncrementalResourceOrderingUpdateFlag
from lib.tests.utils import override_eppo_feature_flag
from service.tests import BaseAsyncHTTPTest
from service.resources import ResourceRichHandler
from webapps.business.elasticsearch import ResourceDocument
from webapps.business.enums import ResourceType
from webapps.business.models import (
    Business,
    Resource,
)


@pytest.mark.django_db
class ResourcesReorderingTestCase(BaseAsyncHTTPTest):
    def test_resources_reordering(self):
        res_0 = baker.make(Resource, business=self.business, order=0)
        res_1 = baker.make(Resource, business=self.business, order=1)

        # LEGACY - use list as request and response body
        url = f'/business_api/me/businesses/{self.business.id}/resources/'

        body = [
            {
                'id': res_0.id,
                'order': res_1.order,
            },
            {
                'id': res_1.id,
                'order': res_0.order,
            },
        ]
        response = self.fetch(url, method='PUT', body=json.dumps(body))

        assert response.code == 200

        resp_json = json.loads(l_b(response.body))
        assert isinstance(resp_json, list)
        res_0.refresh_from_db()
        res_1.refresh_from_db()
        assert res_0.order == 1
        assert res_1.order == 0

        # CURRENT - use dicts for request and response body
        url = f'/business_api/me/businesses/{self.business.id}/resources/?use_objects=true'

        body = {
            'ordering': [
                {
                    'id': res_0.id,
                    'order': res_1.order,
                },
                {
                    'id': res_1.id,
                    'order': res_0.order,
                },
            ],
        }
        response = self.fetch(url, method='PUT', body=body)

        assert response.code == 200

        resp_json = json.loads(l_b(response.body))
        assert isinstance(resp_json, dict)
        assert 'ordering' in resp_json
        assert isinstance(resp_json['ordering'], list)
        res_0.refresh_from_db()
        res_1.refresh_from_db()
        assert res_0.order == 0
        assert res_1.order == 1

    @parameterized.expand(
        [
            (start, end, resource_type)
            for start, end in (
                (0, 19),
                (18, 19),
            )
            for resource_type in ResourceType
        ]
    )
    @override_eppo_feature_flag({IncrementalResourceOrderingUpdateFlag.flag_name: True})
    def test_incremental_resources_reordering(self, start, end, resource_type):
        resources = baker.make(
            Resource,
            business=self.business,
            type=resource_type,
            order=iter(range(20)),
            _quantity=20,
        )
        assert [r.order for r in resources] == list(range(len(resources)))

        resources.insert(end, resources.pop(start))

        url = f'/business_api/me/businesses/{self.business.id}/resources/?use_objects=true'

        body = {
            'ordering': [{'id': r.id, 'order': i} for i, r in enumerate(resources)],
        }

        with freeze_time('2024-10-10'):
            response = self.fetch(url, method='PUT', body=body)
        assert response.code == 200

        resp_json = json.loads(l_b(response.body))
        assert resp_json['ordering'] == body['ordering']

        updated_set = datetime.datetime(2024, 10, 10, 0, 0, tzinfo=datetime.timezone.utc)
        updated_ordering = {o['id']: o['order'] for o in body['ordering']}

        for res in self.business.resources.filter(type=resource_type, id__in=updated_ordering):
            assert res.order == updated_ordering[res.id]
            if start <= res.order <= end:
                assert res.updated == updated_set
            else:
                assert res.updated != updated_set

    @override_eppo_feature_flag({IncrementalResourceOrderingUpdateFlag.flag_name: True})
    def test_overlapping_orders(self):
        """Ensure that overlapping order indices between Staff and
        Appliance won't be affected during reordering."""
        staffers = baker.make(
            Resource,
            business=self.business,
            type=Resource.STAFF,
            order=iter(range(3)),
            _quantity=3,
        )

        appliance = baker.make(
            Resource,
            business=self.business,
            type=Resource.APPLIANCE,
            order=iter(range(3)),
            _quantity=3,
        )

        staffers.insert(0, staffers.pop(2))

        url = f'/business_api/me/businesses/{self.business.id}/resources/?use_objects=true'

        # STAFFERS PART
        staffers_body = {
            'ordering': [{'id': r.id, 'order': i} for i, r in enumerate(staffers)],
        }

        with freeze_time('2024-10-10'):
            response = self.fetch(url, method='PUT', body=staffers_body)
        assert response.code == 200

        resp_json = json.loads(l_b(response.body))
        assert resp_json['ordering'] == staffers_body['ordering']

        updated_set = datetime.datetime(2024, 10, 10, 0, 0, tzinfo=datetime.timezone.utc)
        updated_ordering = {o['id']: o['order'] for o in staffers_body['ordering']}

        for res in self.business.resources.filter(type=Resource.STAFF, id__in=updated_ordering):
            assert res.order == updated_ordering[res.id]
            assert res.updated == updated_set

        for s in staffers:
            s.refresh_from_db()

        for a in appliance:
            orig_order = a.order
            orig_updated = a.updated
            a.refresh_from_db()
            assert a.order == orig_order
            assert a.updated == orig_updated

        # APPLIANCE PART
        appliance.insert(0, appliance.pop(2))
        appliance_body = {
            'ordering': [{'id': r.id, 'order': i} for i, r in enumerate(appliance)],
        }

        with freeze_time('2024-10-11'):
            response = self.fetch(url, method='PUT', body=appliance_body)
        assert response.code == 200

        resp_json = json.loads(l_b(response.body))
        assert resp_json['ordering'] == appliance_body['ordering']

        updated_set = datetime.datetime(2024, 10, 11, 0, 0, tzinfo=datetime.timezone.utc)
        updated_ordering = {o['id']: o['order'] for o in appliance_body['ordering']}

        for res in self.business.resources.filter(type=Resource.APPLIANCE, id__in=updated_ordering):
            assert res.order == updated_ordering[res.id]
            assert res.updated == updated_set

        for s in staffers:
            orig_order = s.order
            orig_updated = s.updated
            s.refresh_from_db()
            assert s.order == orig_order
            assert s.updated == orig_updated

    @override_eppo_feature_flag({IncrementalResourceOrderingUpdateFlag.flag_name: True})
    def test_mixin_resource_types_is_prohibited(self):
        resources = baker.make(
            Resource,
            business=self.business,
            type=iter([Resource.STAFF, Resource.STAFF, Resource.APPLIANCE]),
            order=iter(range(3)),
            _quantity=3,
        )
        assert [r.order for r in resources] == list(range(len(resources)))

        resources.insert(0, resources.pop(2))

        url = f'/business_api/me/businesses/{self.business.id}/resources/?use_objects=true'

        body = {
            'ordering': [{'id': r.id, 'order': i} for i, r in enumerate(resources)],
        }

        response = self.fetch(url, method='PUT', body=body)
        assert response.code == 400


@pytest.mark.django_db
@freeze_time('2024-10-10')
def test_update_staff_ordering_n_queries():
    business = baker.make(
        Business,
    )
    resources = baker.make(
        Resource, business=business, type=Resource.STAFF, order=iter(range(20)), _quantity=20
    )
    new_ordering = [{'id': r.id, 'order': r.order} for r in resources]

    # Worst case scenario: changing the order of all resources
    new_ordering.insert(0, new_ordering.pop())

    for i, res in enumerate(new_ordering):
        res['order'] = i

    with asserts.assertNumQueries(2):  # pylint: disable=not-context-manager
        ResourceRichHandler.update_staff_ordering(None, new_ordering, business)

    updated_set = datetime.datetime(2024, 10, 10, 0, 0, tzinfo=datetime.timezone.utc)
    updated_ordering = {o['id']: o['order'] for o in new_ordering}

    for res in business.resources.filter(type=Resource.STAFF):
        assert res.order == updated_ordering[res.id]
        assert res.updated == updated_set


@pytest.mark.django_db
def test_update_staff_ordering_updates_order_in_elastic():
    business = baker.make(
        Business,
    )
    resources = baker.make(
        Resource, business=business, type=Resource.STAFF, order=iter(range(5)), _quantity=5
    )
    new_ordering = [{'id': r.id, 'order': r.order} for r in resources]

    # Worst case scenario: changing the order of all resources
    new_ordering.insert(0, new_ordering.pop())

    for i, res in enumerate(new_ordering):
        res['order'] = i

    ResourceRichHandler.update_staff_ordering(None, new_ordering, business)

    sleep(0.2)

    updated_ordering = {o['id']: o['order'] for o in new_ordering}

    for i, res_doc in enumerate(
        ResourceDocument.get_queryset(ids=[r.id for r in resources]), start=1
    ):
        assert updated_ordering[res_doc.id] == res_doc.order

    assert i == 5
