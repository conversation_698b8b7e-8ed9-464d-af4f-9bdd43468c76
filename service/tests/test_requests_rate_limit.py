from unittest.mock import patch

import mock
import pytest
from django.test import override_settings
from model_bakery import baker

from country_config.enums import Country
from service.mixins.throttling import BooksyRateThrottle
from service.tests import BaseAsyncHTTPTest, clear_cache_format, get_cache_format


@pytest.mark.django_db
class TestRequestsRateLimit(BaseAsyncHTTPTest):

    @patch.dict('service.mixins.throttling.THROTTLE_RATES', {'login': '1/minute'})
    @mock.patch('service.mixins.throttling.is_throttle_whitelist_ip', return_value=False)
    @mock.patch(
        'service.mixins.throttling.' 'BooksyScopedRateThrottle.cache_format',
        new_callable=mock.PropertyMock(return_value=get_cache_format()),
    )
    def test_login_rate_limit(self, cache_format_mock, ip_mock):
        url = '/business_api/account/login/'
        kwargs = dict(
            method='POST',
            headers=self.get_headers(url),
            body={"email": self.user.email, "password": "some_password", "access_level": ""},
        )
        assert self.fetch(url, **kwargs).code == 400
        resp = self.fetch(url, **kwargs)
        assert resp.code == 429
        assert resp.reason == 'Too Many Requests'
        assert resp.json == {
            'errors': [
                {
                    'code': 'too_many_requests',
                    'description': 'Too many requests. Try again soon.',
                },
            ],
        }
        self.check_errors_in_logs()
        clear_cache_format(cache_format_mock)

    def test_no_required_access_token(self):
        url = '/customer_api/account/password_reset/'

        kwargs = dict(
            method='POST', headers=self.get_headers(url), body=dict(email=self.user.email)
        )

        assert self.fetch(url, **kwargs).code == 200
        assert self.fetch(url, **kwargs).code == 200
        self.check_errors_in_logs()

    @mock.patch(
        'service.mixins.throttling.BooksyRateThrottle.rate',
        new_callable=mock.PropertyMock(
            return_value='1/minute',
        ),
    )
    @mock.patch('service.mixins.throttling.is_throttle_whitelist_ip', return_value=False)
    @mock.patch(
        'service.mixins.throttling.BooksyRateThrottle.cache_format',
        new_callable=mock.PropertyMock(return_value=get_cache_format()),
    )
    def test_too_many_requests(self, cache_format_mock, ip_mock, rate_mock):
        url = '/customer_api/account/password_change/'
        kwargs = dict(
            method='PUT', headers=self.get_headers(url), body=dict(password='new_password1')
        )

        assert self.fetch(url, **kwargs).code == 200
        resp = self.fetch(url, **kwargs)
        assert BooksyRateThrottle.cache_format == cache_format_mock
        assert resp.code == 429
        assert resp.reason == 'Too Many Requests'
        assert resp.json == {
            'errors': [
                {
                    'code': 'too_many_requests',
                    'description': 'Too many requests. Try again soon.',
                },
            ],
        }
        self.check_errors_in_logs()
        clear_cache_format(cache_format_mock)

    @patch.dict('service.mixins.throttling.THROTTLE_RATES', {'reset_password': '1/minute'})
    @mock.patch('service.mixins.throttling.is_throttle_whitelist_ip', return_value=False)
    @mock.patch(
        'service.mixins.throttling.' 'BooksyScopedRateThrottle.cache_format',
        new_callable=mock.PropertyMock(return_value=get_cache_format()),
    )
    @override_settings(API_COUNTRY=Country.US)
    def test_business_reset_password_rate_limit(self, cache_format_mock, ip_mock):
        url = '/business_api/account/password_reset/'
        user_0 = baker.make_recipe('webapps.user.user_recipe')
        user_0.cell_phone = '**********'
        user_0.save()
        kwargs = dict(
            method='POST',
            headers=self.get_headers(url),
            body={
                'email': user_0.email,
            },
        )

        assert self.fetch(url, **kwargs).code == 200
        resp = self.fetch(url, **kwargs)
        assert resp.code == 429
        assert resp.reason == 'Too Many Requests'
        assert resp.json == {
            'errors': [
                {
                    'code': 'too_many_requests',
                    'description': 'Too many requests. Try again soon.',
                },
            ],
        }
        self.check_errors_in_logs()
        clear_cache_format(cache_format_mock)

    @patch.dict('service.mixins.throttling.THROTTLE_RATES', {'reset_password': '1/minute'})
    @mock.patch('service.mixins.throttling.is_throttle_whitelist_ip', return_value=False)
    @mock.patch(
        'service.mixins.throttling.' 'BooksyScopedRateThrottle.cache_format',
        new_callable=mock.PropertyMock(return_value=get_cache_format()),
    )
    def test_customer_reset_password_rate_limit(self, cache_format_mock, ip_mock):
        url = '/customer_api/account/password_reset/'
        user_0 = baker.make_recipe('webapps.user.user_recipe')
        kwargs = dict(
            method='POST',
            headers=self.get_headers(url),
            body={
                'email': user_0.email,
            },
        )

        assert self.fetch(url, **kwargs).code == 200
        resp = self.fetch(url, **kwargs)
        assert resp.code == 429
        assert resp.reason == 'Too Many Requests'
        assert resp.json == {
            'errors': [
                {
                    'code': 'too_many_requests',
                    'description': 'Too many requests. Try again soon.',
                },
            ],
        }
        self.check_errors_in_logs()
        clear_cache_format(cache_format_mock)

    @mock.patch(
        'service.mixins.throttling.BooksyRateThrottle.rate',
        new_callable=mock.PropertyMock(
            return_value='0/minute',
        ),
    )
    @mock.patch(
        'service.mixins.throttling.BooksyRateThrottle.cache_format',
        new_callable=mock.PropertyMock(return_value=get_cache_format()),
    )
    @mock.patch('service.mixins.throttling.is_throttle_whitelist_ip', return_value=False)
    def test_early_finish(self, ip_mock, cache_format_mock, rate_mock):
        url = f'/business_api/me/resources/{self.owner.id}/'
        body = {
            'type': 'S',
            'name': 'NewName',
            'staff_email': self.owner.staff_email,
        }

        response = self.fetch(url, method='PUT', body=body)

        assert response.code == 429
        assert response.reason == 'Too Many Requests'
        assert response.json == {
            'errors': [
                {
                    'code': 'too_many_requests',
                    'description': 'Too many requests. Try again soon.',
                },
            ],
        }
        self.check_errors_in_logs()
        clear_cache_format(cache_format_mock)
