from datetime import datetime

import pytest
from django.conf import settings
from model_bakery import baker
from parameterized import parameterized

from country_config import Country
from lib.tools import UTC
from service.billing.tests import BillingEndpointTestCase
from webapps.billing.models import (
    BillingCycle,
)
from webapps.business.models import (
    Business,
)
from webapps.purchase.models import SubscriptionBuyer


@pytest.mark.freeze_time(datetime(2021, 3, 15, 12, tzinfo=UTC))
@pytest.mark.django_db
class TestBillingListInvoicesHandler(BillingEndpointTestCase):
    url = '/business_api/me/businesses/{business_id}/billing/invoices/'

    @parameterized.expand(BillingEndpointTestCase.billing_access_owner_only)
    def test_access(self, scenario, response_codes):
        self._test_access__scenarios(
            scenario=scenario,
            response_codes=response_codes,
            request_method='GET',
        )

    def test_access_wrong_business_status(self):
        self._test_access__wrong_business_status('GET')

    def test_get_ok(self):
        baker.make(
            BillingCycle,
            business_id=self.business.id,
            subscription__currency='USD',
            date_end=datetime(2021, 3, 10, tzinfo=UTC),
            _quantity=3,
        )
        baker.make(
            BillingCycle,
            subscription__currency='USD',
            business_id=self.business.id,
            date_end=datetime(2021, 3, 16, tzinfo=UTC),
            _quantity=2,
        )
        #  other business
        baker.make(
            BillingCycle,
            subscription__currency='USD',
            date_end=datetime(2021, 3, 10, tzinfo=UTC),
            _quantity=2,
        )
        baker.make(
            BillingCycle,
            subscription__currency='USD',
            business_id=self.business.id,
            date_start=datetime(2021, 3, 16, tzinfo=UTC),
            _quantity=2,
        )
        baker.make(
            SubscriptionBuyer,
            businesses=[self.business],
            entity_name='Example Co.',
        )
        response = self.fetch(self.formatted_url, method='GET')
        self.assertEqual(response.code, 200)

        self.assertIn('invoices', response.json)
        self.assertEqual(len(response.json['invoices']), 5)

    def test_get_ok_with_query(self):
        baker.make(
            BillingCycle,
            subscription__currency='USD',
            business_id=self.business.id,
            date_start=datetime(2021, 2, 10, tzinfo=UTC),
            date_end=datetime(2021, 3, 10, tzinfo=UTC),
            _quantity=10,
        )
        baker.make(
            BillingCycle,
            subscription__currency='USD',
            business_id=self.business.id,
            date_start=datetime(2021, 1, 10, tzinfo=UTC),
            date_end=datetime(2021, 2, 10, tzinfo=UTC),
            _quantity=5,
        )
        baker.make(
            BillingCycle,
            subscription__currency='USD',
            business_id=self.business.id,
            date_start=datetime(2020, 12, 10, tzinfo=UTC),
            date_end=datetime(2021, 1, 10, tzinfo=UTC),
            _quantity=8,
        )
        baker.make(
            BillingCycle,
            subscription__currency='USD',
            business_id=self.business.id,
            date_start=datetime(2020, 11, 10, tzinfo=UTC),
            date_end=datetime(2020, 12, 10, tzinfo=UTC),
            _quantity=7,
        )
        baker.make(
            SubscriptionBuyer,
            businesses=[self.business],
            entity_name='Example Co.',
        )
        # without arguments, return 20
        response = self.fetch(self.formatted_url, method='GET')
        self.assertEqual(response.code, 200)
        self.assertIn('invoices', response.json)
        self.assertEqual(len(response.json['invoices']), 20)
        self.assertEqual(
            response.json['invoices'][-1].get('invoice_number'),
            f'{self.business.id}/2020/12',
        )

        # page 1, 13 records
        response = self.fetch(self.formatted_url, method='GET', args={'per_page': 13})
        self.assertEqual(response.code, 200)
        self.assertIn('invoices', response.json)
        self.assertEqual(len(response.json['invoices']), 13)
        self.assertEqual(
            response.json['invoices'][-1].get('invoice_number'),
            f'{self.business.id}/2021/01',
        )

        # page 1, 25 records
        response = self.fetch(self.formatted_url, method='GET', args={'per_page': 25})
        self.assertEqual(response.code, 200)
        self.assertIn('invoices', response.json)
        self.assertEqual(len(response.json['invoices']), 25)
        self.assertEqual(
            response.json['invoices'][-1].get('invoice_number'),
            f'{self.business.id}/2020/11',
        )

        # page 2, 15 records
        response = self.fetch(self.formatted_url, method='GET', args={'per_page': 7, 'page': 2})
        self.assertEqual(response.code, 200)
        self.assertIn('invoices', response.json)
        self.assertEqual(len(response.json['invoices']), 7)
        self.assertEqual(
            response.json['invoices'][-1].get('invoice_number'),
            f'{self.business.id}/2021/01',
        )

    def test_put(self):
        response = self.fetch(self.formatted_url, method='PUT', body={})
        self.assertEqual(response.code, 405)

    def test_post(self):
        response = self.fetch(self.formatted_url, method='POST', body={})
        self.assertEqual(response.code, 405)

    def test_patch(self):
        response = self.fetch(self.formatted_url, method='PATCH', body={})
        self.assertEqual(response.code, 405)


@pytest.mark.freeze_time(datetime(2021, 3, 15, 12, tzinfo=UTC))
@pytest.mark.django_db
class TestBillingInvoiceDetailsHandler(BillingEndpointTestCase):
    url = '/business_api/me/businesses/{business_id}/billing/invoices/{{invoice_id}}/?'

    def setUp(self, **kwargs):
        super().setUp(**kwargs)
        self.billing_cycle = baker.make(
            BillingCycle,
            subscription__currency='USD',
            business_id=self.business.id,
            date_start=datetime(2021, 2, 10, tzinfo=UTC),
            date_end=datetime(2021, 3, 10, tzinfo=UTC),
        )
        baker.make(
            SubscriptionBuyer,
            businesses=[self.business],
            entity_name='Example Co.',
        )
        self._url = self.formatted_url
        self.formatted_url = self.formatted_url.format(invoice_id=self.billing_cycle.id)

    @parameterized.expand(BillingEndpointTestCase.billing_access_owner_only)
    def test_access(self, scenario, response_codes):
        self._test_access__scenarios(
            scenario=scenario,
            response_codes=response_codes,
            request_method='GET',
        )

    def test_access_wrong_business_status(self):
        self._test_access__wrong_business_status('GET')

    def test_get_ok(self):
        response = self.fetch(self.formatted_url, method='GET')
        self.assertEqual(response.code, 200)

        self.assertIn('buyer', response.json)
        self.assertIn('seller', response.json)
        self.assertIn('invoice', response.json)
        self.assertEqual(
            response.json['seller'],
            dict(
                entity_name='Booksy Inc.',
                tax_id='37-1784710',
                country='United States',
                invoice_address=dict(
                    address_details1='515 N State Street, Suite 460',
                    zipcode='60654',
                    city='Chicago, IL',
                ),
            ),
        )
        self.assertEqual(response.json['buyer'].get('entity_name'), 'Example Co.')
        invoice = response.json['invoice']
        self.assertEqual(invoice.get('date_start'), '2021-02-10T00:00:00Z')
        self.assertEqual(invoice.get('date_end'), '2021-03-10T00:00:00')
        self.assertEqual(invoice.get('invoice_number'), f'{self.business.id}/2021/02')
        self.assertEqual(invoice.get('invoice_id'), self.billing_cycle.id)

        # check booksy seller settings
        self.assertDictEqual(
            settings.BOOKSY_SELLER_PER_COUNTRY,
            {
                Country.FR: {
                    'entity name': 'KIUTE SAS',
                    'tax_id': 'FR92799889704',
                    'invoice_address': {
                        'address_details1': '37 Rue Bergère',
                        'zipcode': '75009',
                        'city': 'Paris',
                    },
                    'country': 'France',
                },
                Country.PL: {
                    'entity_name': 'Booksy International sp. z o.o.',
                    'tax_id': 'PL9512381607',
                    'invoice_address': {
                        'address_details1': 'ul. Prosta 67, piętro 28',
                        'zipcode': '00-838',
                        'city': 'Warszawa',
                    },
                    'country': 'Polska',
                },
                Country.US: {
                    'entity_name': 'Booksy Inc.',
                    'tax_id': '37-1784710',
                    'invoice_address': {
                        'address_details1': '515 N State Street, Suite 460',
                        'zipcode': '60654',
                        'city': 'Chicago, IL',
                    },
                    'country': 'United States',
                },
            },
        )

    def test_get_no_billing_cycle(self):
        self.billing_cycle.delete()
        response = self.fetch(self.formatted_url, method='GET')
        self.assertEqual(response.code, 404)

    def test_get__billing_cycle_assigned_to_other_business(self):
        self.billing_cycle = baker.make(
            BillingCycle,
            subscription__currency='USD',
            business=baker.make(Business),
            date_start=datetime(2021, 2, 10, tzinfo=UTC),
            date_end=datetime(2021, 3, 10, tzinfo=UTC),
        )
        formatted_url = self._url.format(invoice_id=self.billing_cycle.id)
        response = self.fetch(formatted_url, method='GET')
        self.assertEqual(response.code, 404)

    def test_put(self):
        response = self.fetch(self.formatted_url, method='PUT', body={})
        self.assertEqual(response.code, 405)

    def test_post(self):
        response = self.fetch(self.formatted_url, method='POST', body={})
        self.assertEqual(response.code, 405)

    def test_patch(self):
        response = self.fetch(self.formatted_url, method='PATCH', body={})
        self.assertEqual(response.code, 405)
