from bo_obs.datadog.enums import BooksyTeams
from rest_framework import status

from lib.cache import lru_booksy_cache
from lib.tools import get_object_or_404
from service.tools import RequestHandler, json_request, session
from webapps.business.models import Business
from webapps.business_related.models import SafetyRule
from webapps.business_related.serializers import (
    BusinessSafetyRulesResponseSerializer,
    BusinessSafetyRulesSerializer,
    SafetyRulesSerializer,
)
from webapps.business_related.tasks import send_pdf_with_safety_rules


class BusinessSafetyRulesHandler(RequestHandler):

    @session(login_required=True, api_key_required=True)
    def get(self, business_id):
        """swagger:
        summary: Get SafetyRules for business.
        type: BusinessSafetyRulesResponse
        parameters:
            - name: business_id
              type: integer
              paramType: path
        :swagger
        swaggerModels:
            BusinessSafetyRulesResponse:
                id: BusinessSafetyRulesResponse
                properties:
                    rules:
                        type: array
                        items:
                            type: RuleItem
                    business:
                        type: BusinessRulesObject

            CustomerApiSafetyRulesResponse:
                id: CustomerApiSafetyRulesResponse
                properties:
                    rules:
                        type: array
                        items:
                            type: RuleItem
                    safety_note_text:
                        type: string
                    updated:
                        type: string
                        format: date-time
                        description: latest safety_note or rules update datetime

            RuleItem:
                id: RuleItem
                properties:
                    id:
                        type: integer
                    text:
                        type: number
                    updated:
                        type: string

            BusinessRulesObject:
                id: BusinessRulesObject
                properties:
                    safety_rules:
                        type: array
                        items:
                            type: integer
                    safety_note_text:
                        type: string
        :swaggerModels
        """

        business = self.business_with_advanced_staffer(business_id)

        self.finish_with_json(
            status.HTTP_200_OK,
            {
                'rules': SafetyRulesSerializer(
                    instance=SafetyRule.objects.all().order_by('id'),
                    many=True,
                ).data,
                'business': BusinessSafetyRulesSerializer(instance=business).data,
            },
        )

    @session(login_required=True, api_key_required=True)
    @json_request
    def put(self, business_id):
        """swagger:
        summary: Update SafetyRules for business
        type: BusinessSafetyRulesResponse
        parameters:
            - name: business_id
              type: integer
              paramType: path
            - name: body
              type: BusinessRulesObject
              paramType: body
              required: true
        :swagger
        """

        business = self.business_with_advanced_staffer(business_id)

        serializer = BusinessSafetyRulesSerializer(
            instance=business,
            data=self.data,
        )
        self.validate_serializer(serializer)
        serializer.save()
        send_pdf_with_safety_rules.delay(business_id)

        self.finish_with_json(
            status.HTTP_200_OK,
            {
                'rules': SafetyRulesSerializer(
                    instance=SafetyRule.objects.all().order_by('id'),
                    many=True,
                ).data,
                'business': BusinessSafetyRulesSerializer(instance=business).data,
            },
        )
        get_business_safety_rules.cache_clear(business_id)


class CustomerSafetyRulesHandler(RequestHandler):
    booksy_teams = (BooksyTeams.CUSTOMER_ENGAGEMENT,)

    @session(optional_login=True)
    def get(self, business_id):
        """swagger:
        summary: Get SafetyRules for business.
        type: CustomerApiSafetyRulesResponse
        parameters:
            - name: business_id
              type: integer
              paramType: path
        :swagger
        """

        self.finish_with_json(status.HTTP_200_OK, get_business_safety_rules(business_id))


@lru_booksy_cache(timeout=10 * 60, skip_in_pytest=False)
def get_business_safety_rules(business_id):
    business = get_object_or_404(
        Business,
        id=business_id,
        __select_related=('note',),
        __prefetch_related=('safety_rules',),
    )
    return BusinessSafetyRulesResponseSerializer(instance=business).data
