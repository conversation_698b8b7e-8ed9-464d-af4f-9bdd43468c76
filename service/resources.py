import json
from collections import defaultdict
from datetime import timedelta
from typing import List

from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from django.db import IntegrityError, transaction
from django.db.models import Max, Q
from django.db.models.query import Prefetch
from django.utils.encoding import force_str
from django.utils.translation import gettext as _
from rest_framework import serializers

import lib.tools
from lib import segment_analytics
from lib.db import using_db_for_reads, READ_ONLY_DB
from lib.feature_flag.feature.business import IncrementalResourceOrderingUpdateFlag
from lib.serializers import IdAndOrderSerializer, OrderingSerializer
from service.business.validators import NotEnoughStaffersError, validate_remove_staffer
from service.mixins.paginator import PaginatorMixin
from service.mixins.validation import SkipExtraValidationMixin
from service.photo import ObjectPhotoHandler
from service.signals import resource_reorder_signal
from service.tools import RequestHand<PERSON>, ServiceError, json_request, session
from webapps.booking.models import Appointment
from webapps.business.business_visibility.exceptions import (
    BusinessVisibilityError,
    InvitesError,
    LastAvailableResourceError,
)
from webapps.business.business_visibility.validations import InvitesValidation
from webapps.business.models import Business, Resource, Service
from webapps.business.serializers.serializers import (
    ApplianceResourceCreatorSerializer,
    FullServiceVariantSerializer,
    ResourceAllDayTimeOffSerializer,
    ResourceCreateUpdateSerializer,
    ResourceCreatorSerializer,
    ResourceReadSerializer,
    ServiceSerializer,
    StaffResourceCreatorSerializer,
    get_resource_create_update_serializer_class,
)
from webapps.business.services.validations import LastAvailableResourceValidation
from webapps.ecommerce.adapters import EcommercePermissionAdapter
from webapps.exceptions import BaseValidationError
from webapps.feeds.enums import EventType
from webapps.feeds.utils import update_to_external_partners
from webapps.images.enums import ImageTypeEnum
from webapps.public_partners.models import ResourceMetadata
from webapps.purchase.models import Subscription
from webapps.schedule.models import copy_resource_hours_to_business
from webapps.schedule.ports import (
    create_resource_time_off,
    deprecated_get_resource_time_offs,
)
from webapps.segment.tasks import analytics_staffer_created_task


class ResourceCRUDBase(RequestHandler):

    def can_have_staffer_charges(self, business):
        """Returns True if latest subscription plan purchased by Business
        can have staffer charges"""
        if (
            settings.CHARGE_FOR_STAFFERS_ENABLED
            and business.payment_source == Business.PaymentSource.BRAINTREE
        ):
            last_subscription = (
                Subscription.objects.filter(
                    Q(Q(expiry__isnull=True) | Q(expiry__gte=lib.tools.tznow())),
                    business_id=business.id,
                    source=Business.PaymentSource.BRAINTREE,
                )
                .order_by('-expiry', '-start', '-id')
                .select_related('product')
                .first()
            )
            if (
                last_subscription
                and last_subscription.product.charge_for_staffers
                and last_subscription.is_payable()
            ):
                return True
        return False

    def get_create_success_message(self, business):
        if (
            # Manager can't see subscription info
            self.access_level == Resource.STAFF_ACCESS_LEVEL_OWNER
            and self.can_have_staffer_charges(business)
        ):
            # TODO: Replace!!!
            return ''
        else:
            # Default will be used
            return ''

    def get_delete_success_message(self, business):
        if (
            # Manager can't see subscription info
            self.access_level == Resource.STAFF_ACCESS_LEVEL_OWNER
            and self.can_have_staffer_charges(business)
        ):
            # TODO: Replace!!!
            return ''
        else:
            # Default will be used
            return ''


class ResourceHandler(ResourceCRUDBase, SkipExtraValidationMixin):
    """
    swagger:
        parameters:
            - name: resource_id
              description: Business resource id
              type: integer
              paramType: path
    """

    booksy_teams = (BooksyTeams.PROVIDER_CALENDAR,)

    @staticmethod
    def get_serializer_class(resource_type):
        if resource_type == Resource.STAFF:
            serializer_class = StaffResourceCreatorSerializer
        elif resource_type == Resource.APPLIANCE:
            serializer_class = ApplianceResourceCreatorSerializer
        else:
            serializer_class = ResourceCreatorSerializer

        return serializer_class

    @session(login_required=True)
    def get(self, resource_id):
        """
        swagger:
            type: ResourceDetailsWrapper
        """
        resource = self.get_object_or_404(Resource, id=resource_id)
        self.business_with_staffer(resource.business, __check_region=False)
        self.finish_with_json(
            200,
            {
                'resource': self.get_serializer_class(resource.type)(
                    instance=resource,
                    context={'user': self.user, 'business': resource.business},
                ).data,
            },
        )

    @json_request
    @session(login_required=True)
    def put(self, resource_id):
        """
        swagger:
            type: ResourceDetailsWrapper
            parameters:
                - name: body
                  paramType: body
                  type: ResourceDetails
                - name: skip_extra_validation
                  description: Whether to skip extra validation if true
                  type: boolean
                  paramType: query
        """
        resource = self.get_object_or_404(Resource, id=resource_id)
        business = resource.business
        self.business_with_manager(business, __check_region=False)
        self.data['business'] = business.id
        # for one resource business ovewrite open hours

        serializer_class = get_resource_create_update_serializer_class(business.id)
        serializer = serializer_class(
            instance=resource,
            data=self.data,
            partial=True,
            context={
                'user': self.user,
                'business': business,
                'send_invitation': self.data.pop('send_invitation', False),
            },
        )

        validated_data = self.validate_serializer(serializer)
        go_offline = False

        if self.skip_extra_validation is not None and business.visible:
            resource_visibility = validated_data.get('visible')

            if resource_visibility is False:
                errors: List[BusinessVisibilityError] = []
                try:
                    LastAvailableResourceValidation(resource).validate()
                except LastAvailableResourceError as last_available_res_err:
                    try:
                        # Invites validation is important only if we are disabling
                        # last available staff
                        InvitesValidation(business).validate()
                    except InvitesError as invites_err:
                        if not self.skip_extra_validation:
                            errors.append(invites_err)

                    if not self.skip_extra_validation:
                        errors.append(last_available_res_err)
                    else:
                        go_offline = True

                if errors:
                    return self.return_error([er.asdict() for er in errors])

        # save changes and switch business visibility if no available services
        with transaction.atomic():
            resource = serializer.save()

            if go_offline:
                business.go_offline()

        business_hours_update = self.data.get('overwrite_biz_opening_hours')
        if business_hours_update:
            # overwrite business hours
            copy_resource_hours_to_business(
                business_id=business.id,
                resource_id=resource.id,
                tz=business.get_timezone(),
            )

        self.finish_with_json(
            200,
            {
                'resource': serializer.data,
                'business_hours_update': business_hours_update,
            },
        )

    @session(login_required=True)
    def delete(self, resource_id):
        """
        swagger:
            summary: Delete Resource
            parameters:
                - name: skip_extra_validation
                  description: Whether to skip extra validation if true
                  type: boolean
                  paramType: query
        """
        resource = self.get_object_or_404(Resource, id=resource_id)
        self.business_with_manager(resource.business, __check_region=False)

        business = resource.business
        go_offline = False

        if self.skip_extra_validation is not None:
            errors: List[BaseValidationError] = []
            if business.visible:
                try:
                    LastAvailableResourceValidation(resource).validate()
                except LastAvailableResourceError as last_available_res_err:
                    try:
                        # Invites validation is important only if we are disabling
                        # last available staff
                        InvitesValidation(business).validate()
                    except InvitesError as invites_err:
                        if not self.skip_extra_validation:
                            errors.append(invites_err)

                    if not self.skip_extra_validation:
                        errors.append(last_available_res_err)
                    else:
                        go_offline = True

            if not self.skip_extra_validation:
                try:
                    validate_remove_staffer(business)
                except NotEnoughStaffersError as error:
                    errors.append(error)

            if errors:
                return self.return_error([er.asdict() for er in errors])

        self.quick_assert(
            business.resources.filter(active=True).count() > 1,
            ('required', 'validation', 'resource'),
            _('At least one staff or resource is required.'),
        )

        tz = resource.business.get_timezone()
        upcoming_bookings = resource.subbookings.filter(
            deleted__isnull=True,
            appointment__type__in=Appointment.TYPES_BOOKABLE,
            booked_till__gt=lib.tools.tznow(tz=tz),
        ).exclude(
            appointment__status__in=(
                Appointment.STATUS.CANCELED,
                Appointment.STATUS.DECLINED,
                Appointment.STATUS.NOSHOW,
                Appointment.STATUS.REJECTED,
            )
        )
        self.quick_assert(
            not upcoming_bookings.exists(),
            ('conflict', 'validation', None),
            _('Staff or Resource has upcoming bookings scheduled'),
        )

        # get all services active not deleted services
        services_qs = resource.services.filter(
            active=True,
            deleted__isnull=True,
            combo_type__isnull=True,
        )
        # count how many active resources perform each service
        services = services_qs.prefetch_related(
            Prefetch(
                'resources',
                queryset=Resource.objects.filter(active=True, deleted__isnull=True).only(
                    'id',
                ),
                to_attr='active_resource',
            )
        )
        # filter services that have one or less active resources
        abandoned_services = [s for s in services if len(s.active_resources) <= 1]
        # assert that no services will be left without a resource
        # after current resource will be deleted
        type_r = _(u'Staffer') if resource.type == Resource.STAFF else _(u'Resource')
        self.quick_assert(
            not abandoned_services,
            ('required', 'validation', 'resource'),
            _(
                '{} can not be deleted '
                'because service: \'{}\' will '
                'not be performed by anyone'
            ).format(
                type_r,
                (abandoned_services[0].name if abandoned_services else ''),
            ),
        )

        # get all active and not deleted variants
        service_variants_qs = (
            resource.service_variants.annotate_is_combo()
            .prefetch_related('resources', 'service__resources')
            .filter(active=True, deleted__isnull=True, is_combo=False)
        )

        orphan_variants = [
            *{variant for variant in service_variants_qs if variant.has_only_one_resource}
        ]

        if orphan_variants:
            orphan_combo_services = list(
                {
                    combo_service
                    for variant in orphan_variants
                    for combo_service in variant.get_related_combo_services()
                }
            )

            if self.skip_extra_validation:
                services_and_variants = defaultdict(list)
                for v in orphan_variants:
                    services_and_variants[v.service].append(v)

                with transaction.atomic():
                    for service, variants in services_and_variants.items():
                        if service.active_variants.count() == len(variants):
                            service.safe_delete()
                        else:
                            for v in variants:
                                v.safe_delete()
                    for combo_service in orphan_combo_services:
                        combo_service.safe_delete()
            else:
                variant_data = FullServiceVariantSerializer(orphan_variants, many=True).data
                combo_data = ServiceSerializer(orphan_combo_services, many=True).data

                message = _(
                    '%(resource_type)s can not be deleted because variants belong to services: '
                    '%(services)s will not be performed by anyone'
                ) % {
                    "resource_type": type_r,
                    "services": ", ".join({variant.service.name for variant in orphan_variants}),
                }
                self.quick_error(
                    ('last_service_staffer_deletion_error', 'validation', 'non_field_error'),
                    message,
                    extra={'data': {'variants': variant_data, 'combo_services': combo_data}},
                )

        # this is done BEFORE upcoming_bookings.exists() because we want
        # to take away Staffer access to the system (in case of abuse, ...)
        if resource.staff_user:
            resource.staff_user.delete_all_user_sessions()
            resource.staff_user = None
            resource.save()

        with transaction.atomic():
            # mark as inactive immediately
            resource.soft_delete()
            resource.visible = False
            resource.save(update_fields=['visible'])

            if go_offline:
                business.go_offline()

        # <editor-fold desc="early_finish section">
        update_to_external_partners(EventType.STAFFER, business, staffer=resource)
        # </editor-fold>
        self.finish_with_json(200, {'message': self.get_delete_success_message(business)})


class ResourcePhotoHandler(ObjectPhotoHandler):
    booksy_teams = (BooksyTeams.PROVIDER_CALENDAR,)
    image_type = ImageTypeEnum.RESOURCE
    booksy_teams = (BooksyTeams.PROVIDER_CALENDAR,)

    def get_validated_instance(self, instance_id):
        resource = self.get_object_or_404(Resource, id=instance_id)
        self.business_with_manager(resource.business, __check_region=False)
        return resource, resource.business_id


class ResourceInviteHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @session(login_required=True)
    def post(self, resource_id):
        """
        swagger:
            summary: Resend Staffer invitation email.
            notes: |
                Staffer must have previously created account.
                Only Owner user (as opposed by Staffer user) can do this.
            parameters:
                - name: resource_id
                  description: Business resource id
                  type: integer
                  paramType: path
                  required: true
        """
        resource = self.get_object_or_404(Resource, id=resource_id)
        self.business_with_manager(resource.business, __check_region=False)

        lib.tools.assert_quick(
            resource.staff_user is not None,
            ('required', 'validation', 'resource.staff_user'),
            _('Staff already has an account'),
        )

        resource.staff_user_invite()

        self.finish({})


class ResourceRichHandler(PaginatorMixin, ResourceCRUDBase):
    booksy_teams = (BooksyTeams.PROVIDER_CALENDAR,)
    page_name = 'resources_page'
    per_page_name = 'resources_per_page'
    default_per_page_setting = 'RESOURCES_PER_PAGE'

    @json_request
    @session(login_required=True)
    def post(self, business_id):
        """
        swagger:
            summary: Create new Business Resource
            parameters:
                - name: business_id
                  description: Business id
                  type: integer
                  paramType: path
                - name: resources_page
                  description: Results page
                  paramType: query
                  required: False
                  type: integer
                  defaultValue: 1
                - name: resources_per_page
                  description: How many results per page to return
                  paramType: query
                  required: False
                  type: integer
                  default_from_const: settings.RESOURCES_PER_PAGE
                - name: body
                  paramType: body
                  type: ResourceDetails
            type: ResourceDetailsWrapper
        """
        self.data['business'] = business_id
        self.data['active'] = True

        business = self.business_with_manager(business_id, __check_region=False)

        order = self.data.get('order')
        if order is None or order == '':
            aggregation = Resource.objects.filter(business_id=business_id).aggregate(Max('order'))
            self.data['order'] = (aggregation['order__max'] or 0) + 1

        self.data['business'] = business.id
        send_invitation = self.data.pop('send_invitation', False)
        serializer = ResourceCreateUpdateSerializer(
            data=self.data,
            partial=True,
            context={
                'user': self.user,
                'business': business,
                'send_invitation': send_invitation,
            },
        )
        self.validate_serializer(serializer)
        resource = serializer.save()

        resp = {'resource': serializer.data, 'message': self.get_create_success_message(business)}
        # <editor-fold desc="early_finish section">
        segment_api = segment_analytics.get_segment_api(
            business=business,
            source=self.booking_source,
        )
        segment_api.resource_created(resource)
        analytics_staffer_created_task.delay(
            business_id=business_id,
            resource_id=resource.id,
            services_ids=serializer.validated_data.get('services') or [],
            invited=(self.data.pop('staff_user_exists', False) or send_invitation),
            context={
                'session_user_id': self.user.id,
                'source_id': self.booking_source.id,
            },
        )
        # </editor-fold>
        self.finish_with_json(201, resp)

    @session(login_required=True)
    def get(self, business_id):
        """
        swagger:
            summary: Get list of Business Resources
            notes:
                First resource of a newly created Business (the owner)
                is lazily created by this handler.
                This resource can not be created earlier,
                because it has to copy open hours from Business.
            parameters:
                - name: business_id
                  description: Business ID
                  type: integer
                  paramType: path
                - name: type
                  description: Resource type
                  type: string
                  enum_from_const: business.Resource.RESOURCE_TYPES
                  paramType: query
                - name: service_id
                  description: Service ID
                  type: integer
                  paramType: query
            type: ResourceDetailsListing
        """
        business, resource_type, service_id = self.validate(business_id)
        self.parse_page_values_from_get()

        res_filter = [
            Q(business__id=business_id),
            Q(active=True),
        ]
        if resource_type:
            res_filter.append(Q(type=resource_type))
        if service_id:
            res_filter.append(Q(services__id=service_id))
        if self.access_level in Resource.STAFF_ACCESS_LEVELS_LIMIT_CALENDAR:
            res_filter.append(Q(staff_user=self.user) | Q(type=Resource.APPLIANCE))

        ##### LAZY CREATION OF RESOURCE FROM OWNER #17051 #####
        try:
            business.create_resource_from_owner()
        except IntegrityError as exc:
            # sync problem - someone already did this concurrently
            self._debug("IntegrityError -> create_resource_from_owner", _exception=exc)
        with using_db_for_reads(READ_ONLY_DB):
            # NOTE: ordering is necessary for pagination to work properly
            resources_q = (
                Resource.objects.filter(*res_filter)
                .select_related(
                    'photo',
                )
                .annotate(**EcommercePermissionAdapter.has_store_access_annotation())
                .with_partner_app_data(
                    ResourceMetadata,
                )
                .order_by('order', 'id')
            )
            resources = resources_q[self.offset : self.limit]

            serializer = ResourceReadSerializer(
                instance=resources,
                many=True,
                context={
                    'user_id': self.user.id,
                },
            )

            out_dict = {
                'resources': serializer.data,
                'resources_per_page': self.resources_per_page,
                'resources_count': resources_q.count(),
            }
            self.finish(json.dumps(out_dict))

    def validate(self, business_id):
        """Check validity of the arguments.

        TODO: use Django forms.

        """
        errors = []
        service_id = None
        resource_type = None

        # check if business_id is valid
        business = self.business_with_staffer(business_id, __check_region=False)

        # check if type is valid
        if 'type' in self.request.arguments:
            resource_type = force_str(self.request.arguments['type'][0]).upper()
            if resource_type not in dict(Resource.RESOURCE_TYPES):
                errors.append(
                    {
                        'code': 'invalid',
                        'type': 'validation',
                        'field': 'type',
                        'description': _('Invalid type value'),
                    }
                )

        # check if service_id is valid
        if 'service_id' in self.request.arguments:
            service_id = force_str(self.request.arguments['service_id'][0])
            try:
                business.services.get(id=service_id)
            except (ValueError, Service.DoesNotExist):
                errors.append(
                    {
                        'code': 'invalid',
                        'type': 'validation',
                        'field': 'service_id',
                        'description': _('Invalid service'),
                    }
                )

        # raise some errors
        if errors:
            raise ServiceError(code=400, errors=errors)

        return business, resource_type, service_id

    @json_request
    @session(login_required=True)
    def put(self, business_id):
        """
        swagger:
            summary: Move (change orders) of Resource
            parameters:
              - name: business_id
                description: Business id
                type: integer
                paramType: path
              - name: body
                description:
                paramType: body
                type: Ordering
              - name: use_objects
                description:
                    should request and response be an object rather than a list
                paramType: query
                defaultValue: True
                required: False
                type: boolean
            type: Ordering
        """
        business = self.business_with_manager(business_id, __check_region=False)

        use_objects = self._check_flag('use_objects', default=False)
        if use_objects:
            serializer = OrderingSerializer(data=self.data)
        else:
            serializer = IdAndOrderSerializer(data=self.data, many=True)
        validated_data = self.validate_serializer(serializer)
        ordering = validated_data['ordering'] if use_objects else validated_data

        if IncrementalResourceOrderingUpdateFlag():
            self.update_staff_ordering(ordering, business)
        else:
            for el in ordering:
                business.resources.filter(id=el['id']).update(
                    order=el['order'],
                    updated=lib.tools.tznow(),
                )

            # <editor-fold desc="early_finish section">
            resource_reorder_signal.send(
                sender=Resource, instance=dict(resource_ids=[el['id'] for el in ordering])
            )
            # </editor-fold>

        self.finish_with_json(200, validated_data)

    def update_staff_ordering(self, ordering, business):
        new_ordering = {o['id']: o['order'] for o in ordering}
        # N.B. even though business_id seems redundant cause it's not used,
        # it's still needed for Django to retrieve all the necessary information in one query.
        # Otherwise it would perform N queries.
        original_resources = list(
            business.resources.filter(id__in=new_ordering).only(
                'id', 'business_id', 'type', 'order', 'updated'
            )
        )

        if len({o.type for o in original_resources}) > 1:
            raise ServiceError(
                code=400,
                errors=[
                    {
                        'code': 'invalid',
                        'type': 'validation',
                        'field': 'ordering',
                        'description': _(
                            'Cannot mix staffer and appliance ids when trying to reorder'
                        ),
                    }
                ],
            )

        updated = lib.tools.tznow()
        staffers_to_update = []
        for staffer in original_resources:
            if new_ordering[staffer.id] != staffer.order:
                staffer.order = new_ordering[staffer.id]
                staffer.updated = updated
                staffers_to_update.append(staffer)

        Resource.objects.bulk_update(staffers_to_update, ['order', 'updated'])

        resource_reorder_signal.send(
            sender=Resource, instance=dict(resource_ids=[s.id for s in staffers_to_update])
        )


class TimeOffCRUDSerializer(serializers.Serializer):
    id = serializers.IntegerField(required=False)
    off_from = serializers.DateField()
    off_till = serializers.DateField()

    def to_internal_value(self, data):
        # strip time
        if 'off_from' in data:
            data['off_from'] = data['off_from'][:10]
        if 'off_till' in data:
            data['off_till'] = data['off_till'][:10]
        return super(TimeOffCRUDSerializer, self).to_internal_value(data)

    def validate(self, attrs):
        if attrs['off_till'] < attrs['off_from']:
            raise serializers.ValidationError(_('Till is greater then from'))
        delta = attrs['off_till'] - attrs['off_from']
        if delta >= timedelta(days=365):
            raise serializers.ValidationError(
                _('Time off must be less than one calendar year'),
            )
        return attrs


class ResourceTimeOffHandler(RequestHandler):
    """
    swagger:
        summary: DEPRECATED (all day only) time offs
        type: AllDayTimeOffsWrapper
        parameters:
            - name: resource_id
              description: Business resource id
              type: integer
              paramType: path
    :swagger

    swaggerModels:
        AllDayTimeOffsWrapper:
            id: AllDayTimeOffsWrapper
            properties:
                time_offs:
                    type: array
                    items:
                        type: AllDayTimeOff

    :swaggerModels
    """

    booksy_teams = (BooksyTeams.PROVIDER_CALENDAR,)

    @session(login_required=True)
    def get(self, resource_id):
        """
        swagger:
        summary: DEPRECATED (all day only) time offs
        """
        resource = self.get_object_or_404(Resource, id=resource_id)
        business = self.business_with_manager(resource.business)

        time_offs = deprecated_get_resource_time_offs(
            resource_id=resource.id,
            business=business,
        )

        self.finish(
            {
                'time_offs': ResourceAllDayTimeOffSerializer(
                    many=True,
                    instance=time_offs,
                ).data,
            }
        )

    @json_request
    @session(login_required=True)
    def post(self, resource_id):
        """
        swagger:
            summary: DEPRECATED - Create staffer (all day) time off.
            parameters:
              - name: body
                description: JSON with TimeOff
                paramType: body
                type: AllDayTimeOff
        """
        resource = self.get_object_or_404(Resource, id=resource_id)
        business = self.business_with_manager(resource.business)

        serializer = TimeOffCRUDSerializer(data=self.data)
        self.validate_serializer(serializer)

        data = serializer.validated_data

        time_off = create_resource_time_off(
            resource_id=resource.id,
            business_id=business.id,
            date_from=data['off_from'],
            date_till=data['off_till'],
        )

        # FINISH
        self.set_status(201)
        self.finish(
            {
                'time_off': ResourceAllDayTimeOffSerializer(
                    instance=time_off,
                    context={
                        'business': business,
                        'booking_source': self.booking_source,
                    },
                ).data,
            }
        )
