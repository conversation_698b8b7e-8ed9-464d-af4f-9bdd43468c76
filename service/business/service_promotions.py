"""Services promotions."""

from bo_obs.datadog.enums import BooksyTeams

from service.business.feature_status.service_promotions import (
    promotion_service_variants,
)
from service.tools import (
    RequestHandler,
    json_request,
    session,
)
from webapps.business.enums import PriceType
from webapps.business.models import SERVICE_VARIANT_HAPPY_HOURS, ServiceVariant
from webapps.business.serializers import (
    FlashSalePromotionSerializer,
    HappyHourPromotionSerializer,
    LastMinutePromotionSerializer,
)
from webapps.business.service_promotions import ServicePromotions


class PromotionHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @classmethod
    def service_variants_with_prices(cls, business):
        queryset = ServiceVariant.objects.filter(
            service__business=business,
            service__active=True,
            active=True,
            type__in=PriceType.has_price(),
        ).select_related(
            'service',
            'service__service_category',
        )

        return queryset

    @session(login_required=True)
    @json_request
    def get(self, business_id):
        """
        swagger:
            summary: Returns all active booking promotions.
            parameters:
                - name: business_id
                  description: id of business
                  type: integer
                  paramType: path
                  required: true
            type: PromotionServiceVariants
        """
        business = self.business_with_manager(business_id, __check_region=False)
        response = promotion_service_variants(business.id)
        response['flash_sale'] = bool(ServicePromotions.flash_sale_promotion(business))
        response['last_minute'] = bool(ServicePromotions.last_minute_promotion(business))
        response['happy_hours'] = ServicePromotions.has_active_promotion(
            business=business,
            promotion_type=SERVICE_VARIANT_HAPPY_HOURS,
        )

        self.finish_with_json(200, response)


class LastMinuteHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @session(login_required=True)
    @json_request
    def get(self, business_id):
        """
        swagger:
            summary: Get current active Last Minute promotion
            parameters:
                - name: business_id
                  description: id of business
                  type: integer
                  paramType: path
                  required: true
            type: LastMinutePromotionResponse
        """
        business = self.business_with_reception(business_id, __check_region=False)
        promotion = ServicePromotions.last_minute_promotion(business)
        if not promotion:
            return self.finish_with_json(200, {})

        serializer = LastMinutePromotionSerializer(
            promotion,
            context={
                'business': business,
                'booking_source': self.booking_source,
            },
        )

        return self.finish_with_json(200, serializer.data)

    @session(login_required=True)
    @json_request
    def post(self, business_id):
        """
        swagger:
            summary: Create new Last Minute promotion
            parameters:
                - name: business_id
                  description: id of business
                  type: integer
                  paramType: path
                  required: true
                - name: body
                  description:
                  paramType: body
                  type: LastMinutePromotionRequest
            type: LastMinutePromotionResponse
        """
        business = self.business_with_reception(business_id, __check_region=False)

        serializer = LastMinutePromotionSerializer(
            data=self.data,
            context={
                'business': business,
                'booking_source': self.booking_source,
                'user': self.user,
            },
        )
        self.validate_serializer(serializer)
        serializer.save()

        self.finish_with_json(200, serializer.data)

    @session(login_required=True)
    @json_request
    def delete(self, business_id):
        """
        swagger:
            summary: Disable Last Minute promotion
            parameters:
                - name: business_id
                  description: id of business
                  type: integer
                  paramType: path
                  required: true
        """
        business = self.business_with_reception(business_id, __check_region=False)
        result = ServicePromotions.disable_last_minute(
            business=business,
            log_entry={
                'action': 'disable',
                'user_id': self.user.id,
                'source_id': self.booking_source.id,
            },
        )

        return self.finish_with_json(200, {'deleted': result})


class FlashSaleHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @session(login_required=True)
    @json_request
    def get(self, business_id):
        """
        swagger:
            summary: Get current active Flash Sale promotion
            parameters:
                - name: business_id
                  description: id of business
                  type: integer
                  paramType: path
                  required: true
            type: FlashSalePromotionResponse
        """
        business = self.business_with_reception(business_id, __check_region=False)
        promotion = ServicePromotions.flash_sale_promotion(business)

        if not promotion:
            return self.finish_with_json(200, {})

        serializer = FlashSalePromotionSerializer(
            promotion,
            context={
                'business': business,
                'booking_source': self.booking_source,
            },
        )

        return self.finish_with_json(200, serializer.data)

    @session(login_required=True)
    @json_request
    def post(self, business_id):
        """
        swagger:
            summary: Create new Flash Sale promotion
            parameters:
                - name: business_id
                  description: id of business
                  type: integer
                  paramType: path
                  required: true
                - name: body
                  description:
                  paramType: body
                  type: FlashSalePromotionRequest
            type: FlashSalePromotionResponse
        """
        business = self.business_with_reception(business_id, __check_region=False)

        serializer = FlashSalePromotionSerializer(
            data=self.data,
            context={
                'business': business,
                'booking_source': self.booking_source,
                'user': self.user,
            },
        )
        self.validate_serializer(serializer)
        serializer.save()

        self.finish_with_json(200, serializer.data)

    @session(login_required=True)
    @json_request
    def delete(self, business_id):
        """
        swagger:
            summary: Disable Flash Sale promotion
            parameters:
                - name: business_id
                  description: id of business
                  type: integer
                  paramType: path
                  required: true
        """
        business = self.business_with_reception(business_id, __check_region=False)
        result = ServicePromotions.disable_flash_sale(
            business=business,
            log_entry={
                'action': 'disable',
                'user_id': self.user.id,
                'source_id': self.booking_source.id,
            },
        )
        return self.finish_with_json(200, {'deleted': result})


class HappyHoursHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @session(login_required=True)
    @json_request
    def get(self, business_id):
        """
        swagger:
            summary: Get current active Happy Hours promotion
            parameters:
                - name: business_id
                  description: id of business
                  type: integer
                  paramType: path
                  required: true
            type: HappyHoursPromotionResponse
        """
        business = self.business_with_reception(business_id, __check_region=False)
        promotion = ServicePromotions.happy_hours_promotion(business)
        if not promotion:
            return self.finish_with_json(200, {})

        serializer = HappyHourPromotionSerializer(
            promotion,
            context={
                'business': business,
                'booking_source': self.booking_source,
            },
            many=True,
        )

        return self.finish_with_json(
            200,
            {
                'happy_hours': serializer.data,
                'blast_message': serializer.blast_message(serializer.data),
                'blast_template': serializer.blast_template(serializer.data),
            },
        )

    @session(login_required=True)
    @json_request
    def post(self, business_id):
        """
        swagger:
            summary: Create new Happy Hours promotion
            parameters:
                - name: business_id
                  description: id of business
                  type: integer
                  paramType: path
                  required: true
                - name: body
                  description:
                  paramType: body
                  type: HappyHoursPromotionRequest
            type: HappyHoursPromotionResponse
        """
        business = self.business_with_reception(business_id, __check_region=False)

        serializer = HappyHourPromotionSerializer(
            data=self.data['happy_hours'],
            context={
                'business': business,
                'booking_source': self.booking_source,
                'user': self.user,
            },
            many=True,
        )
        self.validate_serializer(serializer)
        serializer.save()

        self.finish_with_json(
            200,
            {
                'happy_hours': serializer.data,
                'blast_message': serializer.blast_message(serializer.data),
                'blast_template': serializer.blast_template(serializer.data),
            },
        )

    @session(login_required=True)
    @json_request
    def put(self, business_id):
        """
        swagger:
            summary: Adds new Happy Hours promotion to existing
            parameters:
                - name: business_id
                  description: id of business
                  type: integer
                  paramType: path
                  required: true
                - name: body
                  description:
                  paramType: body
                  type: HappyHoursPromotionRequest
            type: HappyHoursPromotionResponse
        """
        business = self.business_with_reception(business_id, __check_region=False)
        serializer = HappyHourPromotionSerializer(
            data=self.data['happy_hours'],
            instance=ServicePromotions.happy_hours_promotion(business),
            context={
                'business': business,
                'booking_source': self.booking_source,
                'user': self.user,
            },
            many=True,
        )
        self.validate_serializer(serializer)
        serializer.save()

        self.finish_with_json(
            200,
            {
                'happy_hours': serializer.data,
                'blast_message': serializer.blast_message(serializer.data),
                'blast_template': serializer.blast_template(serializer.data),
            },
        )

    @session(login_required=True)
    @json_request
    def delete(self, business_id):
        """
        swagger:
            summary: Disable Happy Hours promotion
            parameters:
                - name: business_id
                  description: id of business
                  type: integer
                  paramType: path
                  required: true
                - name: days_of_week
                  description: |
                    list of days of week (type should be array of ints)
                  type: integer
                  paramType: query
                  required: false
        """
        business = self.business_with_reception(business_id, __check_region=False)
        days_of_week = self.request.arguments.get('days_of_week', [])
        result = ServicePromotions.disable_happy_hours(
            business=business,
            days_of_week=days_of_week,
            log_entry={
                'action': 'disable',
                'user_id': self.user.id,
                'source_id': self.booking_source.id,
            },
        )

        self.finish_with_json(200, {'deleted': result})
