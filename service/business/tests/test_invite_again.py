from datetime import (
    datetime,
    timedelta,
)

import pytest
import pytz
from django.conf import settings
from django.db import connection
from django.db.models import Q
from django.test import TestCase, override_settings
from django.test.utils import CaptureQueriesContext
from freezegun import freeze_time
from mock import patch
from mock.mock import MagicMock
from model_bakery import baker
from segment.analytics import Client

from country_config import Country
from lib.baker_utils import get_or_create_booking_source
from lib.deeplink.branchio.client import BranchIOClient
from lib.elasticsearch.consts import ESDocType
from lib.enums import SMSTypeEnum
from lib.feature_flag.feature.security import BlockCrossBorderInviteSMSFlag

from lib.invite import CustomerInvitationR<PERSON>ult

from lib.test_utils import create_subbooking, spy_mock, user_recipe
from lib.tests.utils import override_eppo_feature_flag
from lib.tools import (
    id_to_external_api,
    sget,
    tznow,
)
from service.business.enums import InviteStatusesEnum
from service.tests import BaseAsyncHTTPTest, dict_assert
from webapps.booking.models import Appointment, BookingSources
from webapps.business.elasticsearch.business_customer import BusinessCustomerDocument
from webapps.business.models import (
    Business,
    Resource,
)
from webapps.business.models.bci import (
    BusinessCustomerInfo,
)
from webapps.consts import (
    ANDROID,
    FRONTDESK,
)
from webapps.elasticsearch.elastic import ELASTIC
from webapps.notification.elasticsearch import NotificationHistoryDocument
from webapps.notification.models import FreeSMSBunch, NotificationSchedule
from webapps.search_engine_tuning.models import BusinessCustomerTuning
from webapps.segment.consts import UserRoleEnum
from webapps.segment.enums import BooksyAppVersions, DeviceTypeName
from webapps.user.models import User, UserProfile


class InviteAgainBaseTest(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{}/invite_again/?'

    def setUp(self):
        super().setUp()
        self.index_bci = ELASTIC.indices[ESDocType.BUSINESS_CUSTOMER]

        self.other_business_bci = baker.make(
            BusinessCustomerInfo,
            business=baker.make(Business),
            first_name='Jan',
            last_name='Czarodziej',
            cell_phone='1234567890',
            user=self.user,
        )
        self.other_business_bci.reindex(refresh_index=True)
        self.bcis_to_invite = []
        self.bcis_to_invite.append(
            baker.make(
                BusinessCustomerInfo,
                business=self.business,
                first_name='Jan',
                last_name='Czarodziej',
                cell_phone='+***********',
            ),
        )
        self.bcis_to_invite.append(
            baker.make(
                BusinessCustomerInfo,
                business=self.business,
                first_name='Janek',
                last_name='Magik',
                cell_phone='+***********',
            ),
        )
        self.bcis_to_invite.append(
            baker.make(
                BusinessCustomerInfo,
                business=self.business,
                first_name='Jakub',
                last_name='Magik',
                cell_phone='+***********',
            ),
        )
        self.bcis = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='Aaaaa',
            last_name='Bbbbb',
            _quantity=7,
        )
        self.bcis.extend(self.bcis_to_invite)
        self._update_tunings()
        self.customer_android = get_or_create_booking_source(
            name=ANDROID,
            app_type=BookingSources.CUSTOMER_APP,
        )

    def _update_tunings(self):
        BusinessCustomerTuning.update_multiple_tunings(
            list(BusinessCustomerInfo.objects.all().values_list('id', flat=True))
        )

    def _reindex_bci(self, bcis=None):
        if not bcis:
            bcis = BusinessCustomerInfo.objects.all()
        for bci in bcis:
            bci.reindex(refresh_index=True)

    def _check_invitations_for_recipients(self, history_documents):
        q_filters = Q()
        for history_document in history_documents:
            q_filters |= Q(
                id=history_document.customer_card_id,
                cell_phone=history_document.recipient_phone,
            )

        assert BusinessCustomerInfo.objects.filter(q_filters).filter(
            invited=True,
        ).count() == len(history_documents)

    def _check_invite_attrs(self, resp: dict, bci_id: id, invite_status, can_invite: bool):
        customers = self.list_to_dict(resp.json['customers'], 'id')
        assert customers[bci_id]['invite_status'] == invite_status
        assert customers[bci_id]['can_invite'] is can_invite

    @staticmethod
    def list_to_dict(list_of_objects, key):
        objects_dict = {}
        for obj in list_of_objects:
            objects_dict[sget(obj, [key])] = obj
        return objects_dict


@pytest.mark.django_db
class InviteAgainBusinessCustomerInfoListHandlerTestCase(InviteAgainBaseTest):

    def test_get_multiple_bci(self):
        resp = self._get_multiple_bci_common_test_part()

        self._check_invite_attrs(resp, self.bcis[1].id, InviteStatusesEnum.RECENTLY_INVITED, False)
        self._check_invite_attrs(resp, self.bcis[2].id, InviteStatusesEnum.NO_RESPOND, False)
        self._check_invite_attrs(resp, self.bcis[3].id, InviteStatusesEnum.NOT_INVITED, False)
        self._check_invite_attrs(resp, self.bcis[4].id, InviteStatusesEnum.NO_BOOKINGS, False)
        self._check_invite_attrs(resp, self.bcis[5].id, InviteStatusesEnum.NOT_INVITED, False)
        self._check_invite_attrs(resp, self.bcis[6].id, InviteStatusesEnum.NOT_INVITED, False)
        self._check_invite_attrs(resp, self.bcis[7].id, InviteStatusesEnum.NOT_INVITED, True)
        self._check_invite_attrs(resp, self.bcis[8].id, InviteStatusesEnum.NOT_INVITED, True)
        self._check_invite_attrs(resp, self.bcis[9].id, InviteStatusesEnum.NOT_INVITED, True)

    @override_settings(API_COUNTRY=Country.PL)
    @override_settings(SMS_LIMITS_IN_SUBSCRIPTION=True)
    @patch(
        'webapps.purchase.cache._get_sms_allowance',
        MagicMock(
            return_value={
                'config': {
                    SMSTypeEnum.INVITATION: 6,
                    SMSTypeEnum.MARKETING: 0,
                    SMSTypeEnum.SYSTEM: 0,
                },
                'prepaid_sms_count': 500,
                'plan_prepaid_sms_count': 500,
            },
        ),
    )
    def test_get_multiple_bci_country_with_limits(self):
        baker.make(
            FreeSMSBunch,
            business=self.business,
            cell_phone=self.bcis_to_invite[0].cell_phone,
            sms_count=6,
            sms_type=SMSTypeEnum.INVITATION.value,
        )
        resp = self._get_multiple_bci_common_test_part()

        self._check_invite_attrs(resp, self.bcis[1].id, InviteStatusesEnum.RECENTLY_INVITED, False)
        self._check_invite_attrs(resp, self.bcis[2].id, InviteStatusesEnum.NO_RESPOND, False)
        self._check_invite_attrs(resp, self.bcis[3].id, InviteStatusesEnum.NOT_INVITED, False)
        self._check_invite_attrs(resp, self.bcis[4].id, InviteStatusesEnum.NO_BOOKINGS, False)
        self._check_invite_attrs(resp, self.bcis[5].id, InviteStatusesEnum.NOT_INVITED, False)
        self._check_invite_attrs(resp, self.bcis[6].id, InviteStatusesEnum.NOT_INVITED, False)
        self._check_invite_attrs(
            resp,
            self.bcis[7].id,
            InviteStatusesEnum.NOT_INVITED,
            False,  # because of sms limit per client
        )
        self._check_invite_attrs(resp, self.bcis[8].id, InviteStatusesEnum.NOT_INVITED, True)
        self._check_invite_attrs(resp, self.bcis[9].id, InviteStatusesEnum.NOT_INVITED, True)

    def _get_multiple_bci_common_test_part(self) -> dict:
        booking, _, __ = create_subbooking(
            booking_kws={
                'booked_for': self.bcis[0],
                'status': Appointment.STATUS.FINISHED,
            },
            business=self.business,
            source=self.customer_android,
            booking_type=Appointment.TYPE.CUSTOMER,
        )
        booking.resources.add(baker.make(Resource))
        self.bcis[1].invited = True
        self.bcis[1].last_invite = tznow()
        self.bcis[1].save()
        self.bcis[2].invited = True
        self.bcis[2].last_invite = tznow() - timedelta(days=100)
        self.bcis[2].save()
        self.bcis[4].invited = True
        self.bcis[4].last_invite = tznow() - timedelta(days=100)
        self.bcis[4].user = baker.make(User)
        self.bcis[4].save()

        self._update_tunings()
        self._reindex_bci(self.bcis)

        resp = self.fetch(self.url.format(self.business.id), method='GET')

        assert resp.code == 200
        assert set(resp.json.keys()).issuperset(
            {
                'count',
                'customers',
                'import_in_progress',
                'invite_again_in_progress',
                'page',
                'per_page',
            }
        )
        dict_assert(
            resp.json,
            {
                'count': 9,
                'page': 1,
                'per_page': 20,
            },
        )
        assert len(resp.json['customers']) == 9

        assert set(resp.json['customers'][0].keys()).issuperset(
            {
                '_id',
                'cell_phone',
                'email',
                'first_name',
                'id',
                'is_user',
                'last_name',
                'meta',
                'photo_url',
                'invite_status',
                'can_invite',
            }
        )
        return resp

    def test_get_multiple_bci_visibility_delayed(self):
        self.business.set_delayed_activation_day(tznow() + timedelta(days=3))
        booking, _, __ = create_subbooking(
            booking_kws={
                'booked_for': self.bcis[0],
                'status': Appointment.STATUS.FINISHED,
            },
            business=self.business,
            source=self.customer_android,
            booking_type=Appointment.TYPE.CUSTOMER,
        )
        booking.resources.add(baker.make(Resource))
        self.bcis[1].invited = True
        self.bcis[1].last_invite = tznow()
        self.bcis[1].save()
        self.bcis[2].invited = True
        self.bcis[2].last_invite = tznow() - timedelta(days=100)
        self.bcis[2].save()
        self.bcis[4].invited = True
        self.bcis[4].last_invite = tznow() - timedelta(days=100)
        self.bcis[4].user = baker.make(User)
        self.bcis[4].save()
        self.business.add_bci_to_delayed_invitation_exclusion(self.bcis[5].id)

        self._update_tunings()
        self._reindex_bci(self.bcis)

        resp = self.fetch(self.url.format(self.business.id), method='GET')

        assert resp.code == 200
        assert set(resp.json.keys()).issuperset({'customers', 'count', 'page', 'per_page'})
        dict_assert(
            resp.json,
            {
                'count': 9,
                'page': 1,
                'per_page': 20,
            },
        )
        assert len(resp.json['customers']) == 9

        assert set(resp.json['customers'][0].keys()).issuperset(
            {
                '_id',
                'cell_phone',
                'email',
                'first_name',
                'id',
                'is_user',
                'last_name',
                'meta',
                'photo_url',
                'invite_status',
                'can_invite',
            }
        )

        self._check_invite_attrs(resp, self.bcis[1].id, InviteStatusesEnum.INVITE_PENDING, False)
        self._check_invite_attrs(resp, self.bcis[2].id, InviteStatusesEnum.INVITE_PENDING, False)
        self._check_invite_attrs(resp, self.bcis[3].id, InviteStatusesEnum.INVITE_PENDING, False)
        self._check_invite_attrs(resp, self.bcis[4].id, InviteStatusesEnum.INVITE_PENDING, False)
        self._check_invite_attrs(resp, self.bcis[5].id, InviteStatusesEnum.NOT_INVITED, True)

    def test_get_multiple_bci_with_query_0(self):
        booking, _, __ = create_subbooking(
            booking_kws={
                'booked_for': self.bcis[0],
                'status': Appointment.STATUS.FINISHED,
            },
            business=self.business,
            booking_type=Appointment.TYPE.CUSTOMER,
        )
        booking.resources.add(baker.make(Resource))
        self._reindex_bci(self.bcis)

        url = self.url.format(self.business.id) + 'query=Janek'
        resp = self.fetch(url, method='GET')

        assert resp.code == 200
        assert set(resp.json.keys()).issuperset({'customers', 'count', 'page', 'per_page'})
        dict_assert(
            resp.json,
            {
                'count': 1,
                'page': 1,
                'per_page': 20,
            },
        )
        assert len(resp.json['customers']) == 1

    def test_get_multiple_bci_with_query_1(self):
        booking, _, __ = create_subbooking(
            booking_kws={
                'booked_for': self.bcis[0],
                'status': Appointment.STATUS.FINISHED,
            },
            business=self.business,
            booking_type=Appointment.TYPE.CUSTOMER,
        )
        booking.resources.add(baker.make(Resource))
        self._reindex_bci(self.bcis)

        url = self.url.format(self.business.id) + 'query=Magik'
        resp = self.fetch(url, method='GET')

        assert resp.code == 200
        assert set(resp.json.keys()).issuperset({'customers', 'count', 'page', 'per_page'})
        dict_assert(
            resp.json,
            {
                'count': 2,
                'page': 1,
                'per_page': 20,
            },
        )
        assert len(resp.json['customers']) == 2

    def test_get_multiple_bci_some_blacklisted(self):
        booking, _, __ = create_subbooking(
            booking_kws={
                'booked_for': self.bcis[0],
                'status': Appointment.STATUS.FINISHED,
            },
            business=self.business,
            source=self.customer_android,
            booking_type=Appointment.TYPE.CUSTOMER,
        )
        booking.resources.add(baker.make(Resource))

        self.bcis[3].blacklisted = True
        self.bcis[3].save()
        self.bcis[4].blacklisted = True
        self.bcis[4].save()

        self._update_tunings()
        for bci in self.bcis:
            bci.reindex(refresh_index=True)

        resp = self.fetch(self.url.format(self.business.id), method='GET')

        assert resp.code == 200
        assert set(resp.json.keys()).issuperset({'customers', 'count', 'page', 'per_page'})
        dict_assert(
            resp.json,
            {
                'count': 7,
                'page': 1,
                'per_page': 20,
            },
        )
        assert len(resp.json['customers']) == 7

    def test_get_multiple_bci_some_invitation_possible_false(self):
        self._update_tunings()
        self._reindex_bci(self.bcis)
        doc = self.bcis[0].get_document()
        doc.invitation_possible = False
        doc.save()
        BusinessCustomerDocument.refresh_index()

        resp = self.fetch(self.url.format(self.business.id), method='GET')

        assert resp.code == 200
        assert set(resp.json.keys()).issuperset({'customers', 'count', 'page', 'per_page'})
        dict_assert(
            resp.json,
            {
                'count': 10,
                'page': 1,
                'per_page': 20,
            },
        )
        assert len([x for x in resp.json['customers'] if x['can_invite']]) == 3
        assert len([x for x in resp.json['customers'] if not x['can_invite']]) == 7

    def test_get_pagination(self):
        self.bcis = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='Aaaaa',
            last_name='Bbbbb',
            _quantity=30,
        )
        self._reindex_bci(self.bcis)

        resp_0 = self.fetch(self.url.format(self.business.id), method='GET')

        assert resp_0.code == 200
        assert set(resp_0.json.keys()).issuperset({'customers', 'count', 'page', 'per_page'})
        dict_assert(
            resp_0.json,
            {
                'count': 30,
                'page': 1,
                'per_page': 20,
            },
        )
        assert len(resp_0.json['customers']) == 20
        resp_1 = self.fetch(
            self.url.format(self.business.id) + 'page=2',
            method='GET',
        )

        dict_assert(
            resp_1.json,
            {
                'count': 30,
                'page': 2,
                'per_page': 20,
            },
        )
        assert len(resp_1.json['customers']) == 10

    @patch('service.business.utils.InviteAgainImportCacheMixin.delete_invite_again_in_progress')
    @patch('service.business.utils.InviteAgainImportCacheMixin.set_invite_again_in_progress')
    def test_post_multiple_bci(
        self,
        mock_set_invite_again_in_progress,
        mock_delete_invite_again_in_progress,
    ):
        history_documents = (
            NotificationHistoryDocument.search()
            .query(
                'term',
                task_id=f'invitation:invitation_sms:business_id={self.business.id}',
            )
            .execute()
        )
        assert len(history_documents) == 0
        bcis_to_invite = [bci.id for bci in self.bcis]
        bcis_to_invite.append(self.other_business_bci.id)
        self._reindex_bci()

        resp = self.fetch(
            self.url.format(self.business.id),
            method='POST',
            body={
                'bcis': bcis_to_invite,
            },
        )

        assert resp.code == 200
        assert 'task_id' in resp.json
        dict_assert(
            resp.json,
            {
                'invite_count': 10,
            },
        )
        assert (
            BusinessCustomerInfo.objects.filter(
                id__in=bcis_to_invite,
                invited=True,
            ).count()
            == 3
        )  # because only 3 of them have correct phone
        assert mock_set_invite_again_in_progress.call_count == 1
        assert mock_delete_invite_again_in_progress.call_count == 1
        NotificationHistoryDocument.tasks_refresh()
        history_documents = (
            NotificationHistoryDocument.search()
            .query(
                'term',
                task_id=f'invitation:invitation_sms:business_id={self.business.id}',
            )
            .execute()
        )
        assert len(history_documents) == 3
        self._check_invitations_for_recipients(history_documents)

    def test_invite_bci_with_invalid_document(self):
        bci = self.bcis_to_invite[0]
        doc = bci.get_document()
        doc.invitation_possible = False
        doc.save()
        resp = self.fetch(
            self.url.format(self.business.id),
            method='POST',
            body={
                'bcis': [bci.id],
            },
        )
        assert resp.code == 200
        NotificationHistoryDocument.tasks_refresh()
        history_documents = (
            NotificationHistoryDocument.search()
            .query(
                'term',
                task_id=f'invitation:invitation_sms:business_id={self.business.id}',
            )
            .execute()
        )
        assert len(history_documents) == 1

    def test_invite_all_with_invalid_document(self):
        bci = self.bcis_to_invite[0]
        doc = bci.get_document()
        doc.invitation_possible = False
        doc.save()
        resp = self.fetch(
            self.url.format(self.business.id),
            method='POST',
            body={
                'invite_all': True,
            },
        )
        assert resp.code == 200
        NotificationHistoryDocument.tasks_refresh()
        history_documents = (
            NotificationHistoryDocument.search()
            .query(
                'term',
                task_id=f'invitation:invitation_sms:business_id={self.business.id}',
            )
            .execute()
        )
        assert len(history_documents) == 0

    @patch('service.business.utils.InviteAgainImportCacheMixin.delete_invite_again_in_progress')
    @patch('service.business.utils.InviteAgainImportCacheMixin.set_invite_again_in_progress')
    def test_invite_again_flow_with_post_multiple_bci_and_get_and_duplicated_phones_in_bcis(
        self,
        mock_set_invite_again_in_progress,
        mock_delete_invite_again_in_progress,
    ):
        bci_with_duplicated_phone = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='Jakub',
            last_name='Magikowski',
            cell_phone='+***********',  # same number as other card
            user=baker.make(User),
        )
        self._update_tunings()
        history_documents = (
            NotificationHistoryDocument.search()
            .query(
                'term',
                task_id=f'invitation:invitation_sms:business_id={self.business.id}',
            )
            .execute()
        )
        assert len(history_documents) == 0

        self._reindex_bci()
        resp_get_0 = self.fetch(self.url.format(self.business.id), method='GET')
        assert resp_get_0.code == 200
        assert len([cus for cus in resp_get_0.json['customers'] if cus['can_invite']]) == 4

        bcis_to_invite = [bci.id for bci in self.bcis]
        bcis_to_invite.append(self.other_business_bci.id)

        resp_post_0 = self.fetch(
            self.url.format(self.business.id),
            method='POST',
            body={
                'bcis': bcis_to_invite,
            },
        )

        assert resp_post_0.code == 200
        assert 'task_id' in resp_post_0.json
        dict_assert(
            resp_post_0.json,
            {
                'invite_count': 10,
            },
        )
        assert (
            BusinessCustomerInfo.objects.filter(
                business=self.business,
                invited=True,
            ).count()
            == 3
        )  # because only 3 of them have correct phone
        assert mock_set_invite_again_in_progress.call_count == 1
        assert mock_delete_invite_again_in_progress.call_count == 1
        NotificationHistoryDocument.tasks_refresh()
        history_documents = (
            NotificationHistoryDocument.search()
            .query(
                'term',
                task_id=f'invitation:invitation_sms:business_id={self.business.id}',
            )
            .execute()
        )
        assert len(history_documents) == 3
        self._check_invitations_for_recipients(history_documents)

        self._reindex_bci()

        resp_get_1 = self.fetch(self.url.format(self.business.id), method='GET')
        assert resp_get_1.code == 200
        assert len([cus for cus in resp_get_1.json['customers'] if cus['can_invite']]) == 1

        resp_post_1 = self.fetch(
            self.url.format(self.business.id),
            method='POST',
            body={
                'bcis': [bci_with_duplicated_phone.id],
            },
        )
        dict_assert(
            resp_post_1.json,
            {
                'invite_count': 1,
            },
        )
        bci_with_duplicated_phone.refresh_from_db()
        assert bci_with_duplicated_phone.invited

        assert (
            BusinessCustomerInfo.objects.filter(
                business=self.business,
                invited=True,
            ).count()
            == 4
        )
        self.index_bci.refresh()
        resp_get_2 = self.fetch(self.url.format(self.business.id), method='GET')
        assert resp_get_2.code == 200
        assert len([cus for cus in resp_get_2.json['customers'] if cus['can_invite']]) == 0

    @freeze_time(datetime(2020, 12, 31, 10, 15, tzinfo=pytz.UTC))
    @patch('lib.tagmanager.client.DISABLED_DURING_PYTESTS', False)
    @patch('lib.tagmanager.client.GTMClient._request_api')
    def test_post_multiple_bci_delayed_activation(self, request_gtm_api_mock):
        from webapps.segment.enums import InviteImportSource, InviteAction

        bcis_to_invite = [bci.id for bci in self.bcis]
        self.business.set_delayed_activation_day(tznow() + timedelta(days=3))
        self.business.add_bci_to_delayed_invitation_exclusion(
            bcis_to_invite[0],
        )
        bcis_to_invite.append(self.other_business_bci.id)

        before_excluded_bci_ids = self.business.get_excluded_delayed_invitations_bci_ids()
        assert before_excluded_bci_ids == [bcis_to_invite[0]]

        resp = self.fetch(
            self.url.format(self.business.id),
            method='POST',
            body={
                'bcis': bcis_to_invite,
                'client_id': '304810080.1611830900',
                'page_source': 'onboarding',
            },
        )

        assert resp.code == 200
        dict_assert(
            resp.json,
            {
                'invite_count': 10,
            },
        )
        assert (
            BusinessCustomerInfo.objects.filter(
                id__in=bcis_to_invite,
                invited=True,
            ).count()
            == 0
        )  # because business is waiting for activation
        self.business.refresh_from_db()
        after_excluded_bci_ids = self.business.get_excluded_delayed_invitations_bci_ids()
        assert after_excluded_bci_ids == []

        assert request_gtm_api_mock.call_count == 1
        dict_assert(
            request_gtm_api_mock.call_args_list[0][1],
            {
                'endpoint': '/p/collect',
                'method': 'post',
                'payload': {
                    'firebase_auth': {
                        'client_id': '304810080.1611830900',
                    },
                    'events': [
                        {
                            'name': 'Clients_Action',
                            'params': {
                                'email': self.business.owner.email,
                                'frontend_action': InviteAction.INVITE_TO_BOOK,
                                'event_action': 'postponed_invites_queued',
                                'event_date': '20201231',
                                'import_source': InviteImportSource.NO_IMPORT,
                                'page_source': 'onboarding',
                                'postponed': True,
                                'screen_name': 'server',
                            },
                        }
                    ],
                    'user_id': id_to_external_api(self.business.owner.id),
                    'user_properties': {
                        'business_id': {'value': id_to_external_api(self.business.id)},
                        'country': {'value': settings.API_COUNTRY},
                        'offer_type': {'value': Business.Package.UNKNOWN.label},
                        'business_phone': {'value': ''},
                        'user_role': {'value': UserRoleEnum.OWNER},
                        'logged_in_user_id': {'value': id_to_external_api(self.user.id)},
                    },
                },
            },
        )

    @patch.object(Client, 'track')
    @patch.object(BranchIOClient, 'track_event')
    @freeze_time(datetime(2020, 12, 31, 10, 15, tzinfo=pytz.UTC))
    @patch('lib.tagmanager.client.DISABLED_DURING_PYTESTS', False)
    @patch('lib.tagmanager.client.GTMClient._request_api')
    def test_post_multiple_bci_invite_all(
        self, request_api_mock, branchio_track_mock, analytics_track_mock
    ):
        from webapps.segment.enums import InviteImportSource

        self.bcis_to_invite[0].last_invite = tznow()
        self.bcis_to_invite[0].invited = True
        self.bcis_to_invite[0].save()

        self._reindex_bci()

        assert (
            BusinessCustomerInfo.objects.filter(
                business=self.business,
                invited=True,
            ).count()
            == 1
        )

        self.biz_booking_src.name = FRONTDESK
        self.biz_booking_src.save()
        spy__bci_invite = spy_mock(BusinessCustomerInfo.invite)

        with patch(
            'webapps.business.models.bci.BusinessCustomerInfo.invite',
            spy__bci_invite,
        ):
            resp = self.fetch(
                self.url.format(self.business.id),
                method='POST',
                body={
                    'invite_all': True,
                    'client_id': '304810080.1611830900',
                    'page_source': 'onboarding',
                },
            )

            dict_assert(
                request_api_mock.call_args_list[0][1],
                {
                    'endpoint': '/p/collect',
                    'method': 'post',
                    'payload': {
                        'firebase_auth': {
                            'client_id': '304810080.1611830900',
                        },
                        'events': [
                            {
                                'name': 'Invite_All_Clicked',
                                'params': {
                                    'email_invites_sent': 0,
                                    'event_date': '20201231',
                                    'sms_invites_sent': 2,
                                    'total_invites_sent': 2,
                                    'email': self.business.owner.email,
                                    'page_source': 'onboarding',
                                    'event_action': 'invite_to_book',
                                },
                            }
                        ],
                        'user_id': id_to_external_api(self.business.owner.id),
                        'user_properties': {
                            'business_id': {'value': id_to_external_api(self.business.id)},
                            'country': {'value': settings.API_COUNTRY},
                            'offer_type': {'value': Business.Package.UNKNOWN.label},
                            'business_phone': {'value': ''},
                            'app_version': {'value': BooksyAppVersions.B30},
                            'device_type': {'value': DeviceTypeName.DESKTOP},
                            'user_role': {'value': UserRoleEnum.OWNER},
                            'logged_in_user_id': {'value': id_to_external_api(self.user.id)},
                        },
                    },
                },
            )
            dict_assert(
                request_api_mock.call_args_list[1][1],
                {
                    'endpoint': '/p/collect',
                    'method': 'post',
                    'payload': {
                        'firebase_auth': {
                            'client_id': '304810080.1611830900',
                        },
                        'events': [
                            {
                                'name': 'Invite_Process_Completed',
                                'params': {
                                    'email_invites_sent': 0,
                                    'event_date': '20201231',
                                    'import_source': InviteImportSource.NO_IMPORT,
                                    'postponed': False,
                                    'sms_invites_sent': 2,
                                    'total_invites_sent': 2,
                                    'email': self.business.owner.email,
                                    'page_source': 'onboarding',
                                    'event_action': 'invite_to_book',
                                },
                            }
                        ],
                        'user_id': id_to_external_api(self.business.owner.id),
                        'user_properties': {
                            'business_id': {'value': id_to_external_api(self.business.id)},
                            'country': {'value': settings.API_COUNTRY},
                            'offer_type': {'value': Business.Package.UNKNOWN.label},
                            'business_phone': {'value': ''},
                            'app_version': {'value': BooksyAppVersions.B30},
                            'device_type': {'value': DeviceTypeName.DESKTOP},
                            'user_role': {'value': UserRoleEnum.OWNER},
                            'logged_in_user_id': {'value': id_to_external_api(self.user.id)},
                        },
                    },
                },
            )
            dict_assert(
                analytics_track_mock.call_args_list[0][1],
                {
                    'event': 'Invite_Process_Completed',
                    'properties': {
                        'event_date': '20201231',
                        'postponed': False,
                        'total_invites_sent': 2,
                        'page_source': 'onboarding',
                        'event_action': 'invite_to_book',
                    },
                },
            )
            dict_assert(
                branchio_track_mock.call_args_list[0][1],
                {
                    'event_name': 'Invite_Process_Completed',
                    'event_data': {
                        'event_date': '20201231',
                        'postponed': False,
                        'total_invites_sent': 2,
                        'page_source': 'onboarding',
                        'event_action': 'invite_to_book',
                    },
                },
            )

        assert resp.code == 200
        dict_assert(
            resp.json,
            {
                'invite_count': 2,
            },
        )
        # because 1 invited before and 2 of them have correct phone
        assert (
            BusinessCustomerInfo.objects.filter(
                business=self.business,
                invited=True,
            ).count()
            == 3
        )
        assert (
            len(
                [
                    result
                    for result in spy__bci_invite.mock.result
                    if result == CustomerInvitationResult.SUCCESS_SMS
                ]
            )
            == 2
        )  # 2 new invitations :-)

    def test_post_multiple_bci_invite_all_long_list(self):
        self.bcis.append(
            baker.make(
                BusinessCustomerInfo,
                business=self.business,
                first_name='BBBBB',
                last_name='BBBBBBB',
                cell_phone='+***********',
                last_invite=tznow() - timedelta(days=3),
            )
        )
        self.bcis_to_invite.append(
            baker.make(
                BusinessCustomerInfo,
                business=self.business,
                first_name='CCCCC',
                last_name='CCCCCCC',
                cell_phone='+***********',
                last_invite=tznow() - timedelta(days=31),
            )
        )
        self.bcis_to_invite.append(
            baker.make(
                BusinessCustomerInfo,
                business=self.business,
                first_name='CCCCC',
                last_name='CCCCCCD',
                cell_phone='+***********',
                last_invite=tznow() - timedelta(days=31),
            )
        )
        self.bcis_to_invite.append(
            baker.make(
                BusinessCustomerInfo,
                business=self.business,
                first_name='CCCCC',
                last_name='CCCCCCE',
                cell_phone='+***********',
                last_invite=tznow() - timedelta(days=31),
            )
        )
        self.bcis_to_invite.append(
            baker.make(
                BusinessCustomerInfo,
                business=self.business,
                first_name='CCCCC',
                last_name='CCCCCCF',
                cell_phone='+***********',
                last_invite=tznow() - timedelta(days=31),
            )
        )
        self.bcis_to_invite.append(
            baker.make(
                BusinessCustomerInfo,
                business=self.business,
                first_name='CCCCC',
                last_name='CCCCCCG',
                cell_phone='+***********',
                last_invite=tznow() - timedelta(days=31),
            )
        )
        self.bcis_to_invite.append(
            baker.make(
                BusinessCustomerInfo,
                business=self.business,
                first_name='CCCCC',
                last_name='CCCCCCH',
                cell_phone='+***********',
                last_invite=tznow() - timedelta(days=31),
            )
        )
        self.bcis_to_invite.append(
            baker.make(
                BusinessCustomerInfo,
                business=self.business,
                first_name='CCCCC',
                last_name='CCCCCCI',
                cell_phone='+***********',
                last_invite=tznow() - timedelta(days=31),
            )
        )
        self.bcis_to_invite.append(
            baker.make(
                BusinessCustomerInfo,
                business=self.business,
                first_name='CCCCC',
                last_name='CCCCCCJ',
                cell_phone='+***********',
                last_invite=tznow() - timedelta(days=31),
            )
        )

        assert self.bcis_to_invite[0].last_invite is None
        assert self.bcis_to_invite[1].last_invite is None
        assert self.bcis_to_invite[2].last_invite is None

        self._reindex_bci()

        spy__bci_invite = spy_mock(BusinessCustomerInfo.invite)

        with patch(
            'webapps.business.models.bci.BusinessCustomerInfo.invite',
            spy__bci_invite,
        ):
            resp = self.fetch(
                self.url.format(self.business.id),
                method='POST',
                body={
                    'invite_all': True,
                },
            )

        assert resp.code == 200
        for bci in self.bcis_to_invite:
            bci.refresh_from_db()
            assert bci.last_invite.date() == tznow().date()

        dict_assert(
            resp.json,
            {
                'invite_count': 11,
            },
        )  # more than first page

        BusinessCustomerDocument.refresh_index()

        spy__bci_invite = spy_mock(BusinessCustomerInfo.invite)

        with patch(
            'webapps.business.models.bci.BusinessCustomerInfo.invite',
            spy__bci_invite,
        ):
            resp = self.fetch(
                self.url.format(self.business.id),
                method='POST',
                body={
                    'invite_all': True,
                },
            )
        assert resp.json['invite_count'] == 0  # because 7 without phones and emails

    def test_post_multiple_bci_invite_all_long_list_with_test_owner(self):
        # We want to have one test bci, one non-test bci and at least one bci with no email.
        self.assertGreaterEqual(len(self.bcis_to_invite), 3)

        invited_bcis_count = BusinessCustomerInfo.objects.filter(
            business=self.business,
            last_invite__isnull=False,
        ).count()
        self.assertEqual(invited_bcis_count, 0)

        self.business.owner.email = '<EMAIL>'
        self.business.owner.save()

        # Setting the owner's email to a test one makes the business invisible.
        # It prevents any invites from happening and we cannot test the invite flow for bcis.
        self.business.visible = True
        self.business.save()

        for index, bci in enumerate(self.bcis_to_invite):
            self.assertIsNone(bci.last_invite)

            if index == 0:  # a test customer, to be invited
                bci.email = '<EMAIL>'
            elif index == 1:  # not a test customer, not to be invited
                bci.email = '<EMAIL>'
            else:  # we don't know (no email), not to be invited
                bci.email = ''

            bci.save()

        self._reindex_bci()

        spy__bci_invite = spy_mock(BusinessCustomerInfo.invite)
        with patch('webapps.business.models.bci.BusinessCustomerInfo.invite', spy__bci_invite):
            resp = self.fetch(
                self.url.format(self.business.id),
                method='POST',
                body={
                    'invite_all': True,
                },
            )

        self.assertEqual(resp.code, 200)

        for index, bci in enumerate(self.bcis_to_invite):
            bci.refresh_from_db()
            if index == 0:  # a test customer, should have been invited
                self.assertIsNotNone(bci.last_invite)
            else:  # not a test customer or we don't know, should not have been invited
                self.assertIsNone(bci.last_invite)

        invited_bcis_count = BusinessCustomerInfo.objects.filter(
            business=self.business,
            last_invite__isnull=False,
        ).count()
        self.assertEqual(invited_bcis_count, 1)
        self.assertEqual(resp.json['invite_count'], 1)

        self.assertEqual(spy__bci_invite.mock.call_count, 1)
        called_bci = spy__bci_invite.mock.call_args_list[0][0][0]
        self.assertEqual(called_bci.id, self.bcis_to_invite[0].id)

    @patch('service.business.invite_again.invite_again_existing_customers_task')
    def test_post_invite_all_num_queries(self, mock_invite_again_task):
        bcis = [
            baker.make(
                BusinessCustomerInfo,
                business=self.business,
                user=user_recipe.make(),
                first_name='Aaaaa',
                last_name='Bbbbb',
            ),
            baker.make(
                BusinessCustomerInfo,
                business=self.business,
                user=user_recipe.make(),
                first_name='Aaaaa',
                last_name='Bbbbb',
            ),
        ]
        for bci in bcis:
            baker.make(UserProfile, user=bci.user, profile_type=UserProfile.Type.CUSTOMER)
        self._reindex_bci(bcis)

        spy__bci_invite = spy_mock(BusinessCustomerInfo.invite)
        with (
            patch(
                'webapps.business.models.bci.BusinessCustomerInfo.invite',
                spy__bci_invite,
            ),
            CaptureQueriesContext(connection) as queries,
        ):
            self.fetch(
                self.url.format(self.business.id),
                method='POST',
                body={
                    'invite_all': True,
                },
                extra_headers={'X-User-Pseudo-ID': 'test_1'},
            )
        assert len(queries.captured_queries) == 12

    @override_settings(API_COUNTRY=Country.PL)
    @override_settings(SMS_LIMITS_IN_SUBSCRIPTION=True)
    @patch(
        'webapps.purchase.cache._get_sms_allowance',
        MagicMock(
            return_value={
                'config': {
                    SMSTypeEnum.INVITATION: 6,
                    SMSTypeEnum.MARKETING: 0,
                    SMSTypeEnum.SYSTEM: 0,
                },
                'prepaid_sms_count': 500,
                'plan_prepaid_sms_count': 500,
            },
        ),
    )
    def test_post_multiple_bci_invite_with_limits(self):
        baker.make(
            FreeSMSBunch,
            business=self.business,
            cell_phone=self.bcis_to_invite[0].cell_phone,
            sms_count=6,
            sms_type=SMSTypeEnum.INVITATION.value,
        )
        bci = self.bcis_to_invite[0]
        assert bci.last_invite is None

        self._reindex_bci()
        resp = self.fetch(
            self.url.format(self.business.id),
            method='POST',
            body={
                'invite_all': True,
            },
        )

        assert resp.code == 200
        bci.refresh_from_db()
        assert bci.last_invite is None
        assert (
            BusinessCustomerInfo.objects.filter(
                business=self.business,
                last_invite__isnull=False,
            ).count()
            == 2
        )
        assert resp.json['invite_count'] == 3

    def test_post_multiple_bci_invite_all_with_query_0(self):
        bcis_to_invite = [bci.id for bci in self.bcis]
        bcis_to_invite.append(self.other_business_bci.id)
        self._reindex_bci(self.bcis)

        spy__bci_invite = spy_mock(BusinessCustomerInfo.invite)

        with patch(
            'webapps.business.models.bci.BusinessCustomerInfo.invite',
            spy__bci_invite,
        ):
            resp = self.fetch(
                self.url.format(self.business.id),
                method='POST',
                body={
                    'invite_all': True,
                    'query': 'Janek',
                },
            )

        assert resp.code == 200
        dict_assert(
            resp.json,
            {
                'invite_count': 1,
            },
        )
        assert (
            BusinessCustomerInfo.objects.filter(
                business=self.business,
                invited=True,
            ).count()
            == 1
        )  # 3 have correct phone, 1 found by query search

    def test_post_multiple_bci_invite_all_with_query_1(self):
        bcis_to_invite = [bci.id for bci in self.bcis]
        bcis_to_invite.append(self.other_business_bci.id)
        self._reindex_bci(self.bcis)

        spy__bci_invite = spy_mock(BusinessCustomerInfo.invite)

        with patch(
            'webapps.business.models.bci.BusinessCustomerInfo.invite',
            spy__bci_invite,
        ):
            resp = self.fetch(
                self.url.format(self.business.id),
                method='POST',
                body={
                    'invite_all': True,
                    'query': 'Magik',
                },
            )

        assert resp.code == 200
        dict_assert(
            resp.json,
            {
                'invite_count': 2,
            },
        )
        assert (
            BusinessCustomerInfo.objects.filter(
                business=self.business,
                invited=True,
            ).count()
            == 2
        )  # 3 have correct phone, 2 found by query search

    def test_post_multiple_bci_some_last_invited_yesterday(self):
        self.bcis_to_invite[0].last_invite = tznow() - timedelta(days=1)
        self.bcis_to_invite[0].invited = True
        self.bcis_to_invite[0].save()

        bcis_to_invite = [bci.id for bci in self.bcis]
        bcis_to_invite.append(self.other_business_bci.id)

        spy__bci_invite = spy_mock(BusinessCustomerInfo.invite)

        with patch(
            'webapps.business.models.bci.BusinessCustomerInfo.invite',
            spy__bci_invite,
        ):
            resp = self.fetch(
                self.url.format(self.business.id),
                method='POST',
                body={
                    'bcis': bcis_to_invite,
                },
            )

        assert resp.code == 200
        dict_assert(
            resp.json,
            {
                'invite_count': 9,
            },
        )
        assert (
            BusinessCustomerInfo.objects.filter(
                id__in=bcis_to_invite,
                invited=True,
            ).count()
            == 3
        )  # 3 correct phone, 1 invited yesterday
        assert spy__bci_invite.mock.result.count(CustomerInvitationResult.SUCCESS_SMS)

        assert (
            BusinessCustomerInfo.objects.filter(
                id__in=bcis_to_invite,
                invited=True,
            )
            .exclude(
                id=self.bcis_to_invite[0].id,
            )
            .first()
            .last_invite
            is not None
        )

    def test_post_multiple_bci_some_not_reindexed_properly(self):
        for bci in self.bcis_to_invite:
            bci.reindex(refresh_index=True)

        self.bcis_to_invite[0].invited = True
        self.bcis_to_invite[0].last_invite = tznow() - timedelta(days=10)
        self.bcis_to_invite[0].save()  # no reindex
        self.bcis_to_invite[1].invited = True
        self.bcis_to_invite[1].last_invite = tznow() - timedelta(days=10)
        self.bcis_to_invite[1].save()  # no reindex

        resp_get_0 = self.fetch(self.url.format(self.business.id), method='GET')

        assert resp_get_0.code == 200
        customers_get_0 = resp_get_0.json['customers']
        assert customers_get_0[0]['invite_status'] == (InviteStatusesEnum.NOT_INVITED)
        assert customers_get_0[1]['invite_status'] == (InviteStatusesEnum.NOT_INVITED)
        assert customers_get_0[2]['invite_status'] == (InviteStatusesEnum.NOT_INVITED)

        bcis_to_invite = [bci.id for bci in self.bcis_to_invite]

        resp = self.fetch(
            self.url.format(self.business.id),
            method='POST',
            body={
                'bcis': bcis_to_invite,
            },
        )
        # only bci with correct data in db is invited now
        assert resp.json['invite_count'] == len(bcis_to_invite) - 2
        BusinessCustomerDocument.refresh_index()

        resp_get_1 = self.fetch(self.url.format(self.business.id), method='GET')
        assert resp_get_1.code == 200
        customers_get_1 = resp_get_1.json['customers']
        assert customers_get_1[0]['invite_status'] == (
            InviteStatusesEnum.RECENTLY_INVITED  # reindexed properly
        )
        assert customers_get_1[1]['invite_status'] == (
            InviteStatusesEnum.RECENTLY_INVITED  # reindexed properly
        )
        assert customers_get_1[2]['invite_status'] == (
            InviteStatusesEnum.RECENTLY_INVITED  # reindexed properly
        )

    @patch('service.business.utils.InviteAgainImportCacheMixin.delete_invite_again_in_progress')
    @patch('service.business.utils.InviteAgainImportCacheMixin.set_invite_again_in_progress')
    def test_post_multiple_bci_inactive_business(
        self,
        mock_set_invite_again_in_progress,
        mock_delete_invite_again_in_progress,
    ):
        self.business.status = Business.Status.TRIAL_BLOCKED
        self.business.save()
        bcis_to_invite = [bci.id for bci in self.bcis]
        bcis_to_invite.append(self.other_business_bci.id)

        resp = self.fetch(
            self.url.format(self.business.id),
            method='POST',
            body={
                'bcis': bcis_to_invite,
            },
        )

        assert resp.code == 400
        assert resp.json['error'] == 'Your subscription has expired.'
        assert (
            BusinessCustomerInfo.objects.filter(
                id__in=bcis_to_invite,
                invited=True,
            ).count()
            == 0
        )  # because only 3 of them have correct phone
        assert mock_set_invite_again_in_progress.call_count == 0
        assert mock_delete_invite_again_in_progress.call_count == 0

    @patch('service.business.utils.InviteAgainImportCacheMixin.delete_invite_again_in_progress')
    @patch('service.business.utils.InviteAgainImportCacheMixin.set_invite_again_in_progress')
    def test_post_no_bci(
        self,
        mock_set_invite_again_in_progress,
        mock_delete_invite_again_in_progress,
    ):
        bcis_to_invite = [bci.id for bci in self.bcis]
        bcis_to_invite.append(self.other_business_bci.id)

        resp = self.fetch(
            self.url.format(self.business.id),
            method='POST',
            body={
                # empty body
            },
        )

        assert resp.code == 200
        dict_assert(
            resp.json,
            {
                'invite_count': 0,
            },
        )
        assert (
            BusinessCustomerInfo.objects.filter(
                id__in=bcis_to_invite,
                invited=True,
            ).count()
            == 0
        )  # because only 3 of them have correct phone
        assert mock_set_invite_again_in_progress.call_count == 0
        assert mock_delete_invite_again_in_progress.call_count == 0


@pytest.mark.django_db
class CrossBorderInviteAgainTestCase(InviteAgainBaseTest, TestCase):
    url = '/business_api/me/businesses/{}/invite_again/?'

    @patch('service.business.utils.InviteAgainImportCacheMixin.delete_invite_again_in_progress')
    @patch('service.business.utils.InviteAgainImportCacheMixin.set_invite_again_in_progress')
    @override_eppo_feature_flag({BlockCrossBorderInviteSMSFlag.flag_name: True})
    def test_post_multiple_bci_cross_border_flag_on(
        self,
        mock_set_invite_again_in_progress,
        mock_delete_invite_again_in_progress,
    ):
        history_documents = (
            NotificationHistoryDocument.search()
            .query(
                'term',
                task_id=f'invitation:invitation_sms:business_id={self.business.id}',
            )
            .execute()
        )
        assert len(history_documents) == 0
        bcis_to_invite = [bci.id for bci in self.bcis]
        bcis_to_invite.append(self.other_business_bci.id)
        self._reindex_bci()

        resp = self.fetch(
            self.url.format(self.business.id),
            method='POST',
            body={
                'bcis': bcis_to_invite,
            },
        )

        assert resp.code == 200
        assert 'task_id' in resp.json
        dict_assert(
            resp.json,
            {
                'invite_count': 10,
            },
        )
        assert (
            BusinessCustomerInfo.objects.filter(
                id__in=bcis_to_invite,
                invited=True,
            ).count()
            == 0
        )
        assert mock_set_invite_again_in_progress.call_count == 1
        assert mock_delete_invite_again_in_progress.call_count == 1
        NotificationHistoryDocument.tasks_refresh()
        history_documents = (
            NotificationHistoryDocument.search()
            .query(
                'term',
                task_id=f'invitation:invitation_sms:business_id={self.business.id}',
            )
            .execute()
        )
        assert len(history_documents) == 0
        self._check_invitations_for_recipients(history_documents)

    @patch('service.business.utils.InviteAgainImportCacheMixin.delete_invite_again_in_progress')
    @patch('service.business.utils.InviteAgainImportCacheMixin.set_invite_again_in_progress')
    @override_eppo_feature_flag({BlockCrossBorderInviteSMSFlag.flag_name: True})
    def test_post_single_bci_with_phone_from_another_business_invitation_cross_border_flag_on(
        self,
        mock_set_invite_again_in_progress,
        mock_delete_invite_again_in_progress,
    ):
        phone_number = '+***********'
        test_bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='Kuba',
            last_name='Czarnoksieznik',
            cell_phone=phone_number,
        )
        self.bcis_to_invite.append(test_bci)
        self.bcis.append(test_bci)

        history_documents = (
            NotificationHistoryDocument.search()
            .query(
                'term',
                task_id=f'invitation:invitation_sms:business_id={self.business.id}',
            )
            .execute()
        )
        assert len(history_documents) == 0

        self._reindex_bci()
        self._update_tunings()

        resp = self.fetch(
            self.url.format(self.business.id),
            method='POST',
            body={
                'bcis': [test_bci.id],
            },
        )

        assert resp.code == 200
        assert 'task_id' in resp.json
        dict_assert(
            resp.json,
            {
                'invite_count': 1,
            },
        )
        assert not (
            BusinessCustomerInfo.objects.filter(
                id=test_bci.id,
                invited=True,
            ).exists()
        )

        assert mock_set_invite_again_in_progress.call_count == 1
        assert mock_delete_invite_again_in_progress.call_count == 1
        NotificationHistoryDocument.tasks_refresh()
        history_documents = (
            NotificationHistoryDocument.search()
            .query(
                'term',
                task_id=f'invitation:invitation_sms:business_id={self.business.id}',
            )
            .execute()
        )
        assert len(history_documents) == 0
        self._check_invitations_for_recipients(history_documents)
        assert not NotificationSchedule.objects.filter(
            task_id__icontains=f'InvitationReminderNotification,{phone_number}',
        ).exists()

    def _check_schedules(self, phone_number, difference_in_days):
        schedules_to_check = NotificationSchedule.objects.filter(
            task_id__icontains=f'InvitationReminderNotification,{self.business.id},{phone_number}',
        )
        assert schedules_to_check.count() == 0
