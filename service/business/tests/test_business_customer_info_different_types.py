from datetime import datetime

from mock import ANY
import responses
from rest_framework import status
import pytest

from service.tests import BaseAsyncHTTPTest
from webapps.business.baker_recipes import (
    bci_recipe,
    pet_bci_recipe,
    region_recipe,
    vehicle_bci_recipe,
)
from webapps.business.enums import CustomerCardType
from webapps.family_and_friends.baker_recipes import inactive_bci_relation_recipe
from webapps.user.baker_recipes import customer_user


class BusinessCustomerInfoTestCaseMixin(BaseAsyncHTTPTest):
    @staticmethod
    def details_url(business_id, bci_id):
        return f'/business_api/me/businesses/{business_id}/customers/{bci_id}'


@pytest.mark.django_db
class BusinessCustomerInfoHandlerTypesTestCase(BusinessCustomerInfoTestCaseMixin):
    def setUp(self):
        super().setUp()
        self.pet_bci = pet_bci_recipe.make(
            business=self.business,
            first_name='<PERSON>',
            last_name='le <PERSON><PERSON>',
            allergens='peanuts',
            birthday=datetime.fromisoformat('2011-11-04'),
        )
        self.pet_url = self.details_url(self.business.id, self.pet_bci.id)
        self.vehicle_bci = vehicle_bci_recipe.make(
            business=self.business,
            first_name='James',
            last_name='Bond',
            allergens='cats',
            birthday=datetime.fromisoformat('2011-11-04'),
        )
        self.vehicle_url = self.details_url(self.business.id, self.vehicle_bci.id)
        self.expected_common_merge_data_get = {
            'id': ANY,
            'business': ANY,
            'user': None,
            'address_line_1': '',
            'address_line_2': '',
            'city': None,
            'zipcode': None,
            'blacklisted': False,
            'visible_in_business': True,
            'created': ANY,
            'bookmarked': False,
            'photo': None,
            'region': None,
            'more_photos': [],
            'attached_files': [],
            'discount': 0,
            'web_communication_agreement': False,
            'processing_consent': False,
            'trusted': False,
            'tags': [],
            'client_type': 'UN',
            'tax_id': None,
            'from_promo': False,
            'has_active_gift_cards': False,
            'is_active_member': True,
            'has_cell_phone': True,
            'has_email': True,
            'badge': None,
        }
        self.expected_common_merge_data_put = {
            'id': ANY,
            'business': ANY,
            'user': None,
            'address_line_1': '',
            'address_line_2': '',
            'blacklisted': False,
            'visible_in_business': True,
            'created': ANY,
            'bookmarked': False,
            'photo': None,
            'region': None,
            'more_photos': [],
            'attached_files': [],
            'discount': 0,
            'web_communication_agreement': False,
            'processing_consent': False,
            'trusted': False,
            'client_type': 'UN',
            'from_promo': False,
            'has_active_gift_cards': False,
            'is_active_member': True,
            'has_cell_phone': True,
            'has_email': True,
        }

    def test_pet_get(self):
        resp = self.fetch(self.pet_url, method=responses.GET)
        self.assertEqual(resp.code, status.HTTP_200_OK)
        expected_merged_data = self.expected_common_merge_data_get | {
            'first_name': 'Charles',
            'last_name': 'le Chien',
            'full_name': 'Charles, Dog, Poodle',
            'email': self.pet_bci.email,
            'cell_phone': self.pet_bci.cell_phone,
            'birthday': '2011-11-04',
            'short_birthday': '1104',
            'allergens': 'peanuts',
            'type_data': {
                'card_type': CustomerCardType.PET,
                'additional_data': {
                    'breed': 'Poodle',
                    'weight': 13,
                    'pet_type': 'Dog',
                    'additional_info': 'good boy',
                },
            },
            'business_secret_note': '',
            'a_to_z': 'Charles, Dog, Poodle',
            'a_to_z_letter': 'C',
        }
        self.assertEqual(resp.json['customer']['merged_data'], expected_merged_data)

    def test_edit_pet(self):
        body = {
            'business_secret_note': 'not so good really',
            'type_data': {
                'card_type': CustomerCardType.PET,
                'additional_data': {
                    'pet_type': 'dog?',
                    'breed': 'poodle(?)',
                    'weight': 14,
                    'additional_info': 'not so good really',
                },
            },
        }

        resp = self.fetch(self.pet_url, body=body, method=responses.PUT)
        self.assertEqual(resp.code, status.HTTP_200_OK)
        expected_merged_data = self.expected_common_merge_data_put | {
            'business_secret_note': 'not so good really',
            'type_data': {
                'card_type': CustomerCardType.PET,
                'additional_data': {
                    'pet_type': 'dog?',
                    'breed': 'poodle(?)',
                    'weight': 14,
                    'additional_info': 'good boy',
                },
            },
            'first_name': 'Charles',
            'last_name': 'le Chien',
            'full_name': 'Charles, dog?, poodle(?)',
            'email': self.pet_bci.email,
            'cell_phone': self.pet_bci.cell_phone,
            'birthday': '2011-11-04',
            'short_birthday': 1104,
            'allergens': 'peanuts',
            'a_to_z': 'Charles, dog?, poodle(?)',
            'a_to_z_letter': 'C',
        }
        self.assertEqual(resp.json['merged_data'], expected_merged_data)

    def test_edit_pet_num_in_string(self):
        body = {
            'business_secret_note': 'not so good really',
            'type_data': {
                'card_type': CustomerCardType.PET,
                'additional_data': {
                    'pet_type': 'dog?',
                    'breed': 'poodle(?)',
                    'weight': '14',
                    'additional_info': 'not so good really',
                },
            },
        }

        resp = self.fetch(self.pet_url, body=body, method=responses.PUT)
        self.assertEqual(resp.code, status.HTTP_200_OK)
        expected_merged_data = self.expected_common_merge_data_put | {
            'business_secret_note': 'not so good really',
            'type_data': {
                'card_type': CustomerCardType.PET,
                'additional_data': {
                    'pet_type': 'dog?',
                    'breed': 'poodle(?)',
                    'weight': 14,
                    'additional_info': 'good boy',
                },
            },
            'first_name': 'Charles',
            'last_name': 'le Chien',
            'full_name': 'Charles, dog?, poodle(?)',
            'email': self.pet_bci.email,
            'cell_phone': self.pet_bci.cell_phone,
            'birthday': '2011-11-04',
            'short_birthday': 1104,
            'allergens': 'peanuts',
            'a_to_z': 'Charles, dog?, poodle(?)',
            'a_to_z_letter': 'C',
        }
        self.assertEqual(resp.json['merged_data'], expected_merged_data)

    def test_edit_pet_no_new_additional_data(self):
        body = {
            'business_secret_note': 'not so good really',
        }

        resp = self.fetch(self.pet_url, body=body, method=responses.PUT)
        self.assertEqual(resp.code, status.HTTP_200_OK)
        expected_merged_data = self.expected_common_merge_data_put | {
            'first_name': 'Charles',
            'last_name': 'le Chien',
            'full_name': 'Charles, Dog, Poodle',
            'email': self.pet_bci.email,
            'cell_phone': self.pet_bci.cell_phone,
            'birthday': '2011-11-04',
            'short_birthday': 1104,
            'allergens': 'peanuts',
            'type_data': {
                'card_type': CustomerCardType.PET,
                'additional_data': {
                    'breed': 'Poodle',
                    'weight': 13,
                    'pet_type': 'Dog',
                    'additional_info': 'good boy',
                },
            },
            'business_secret_note': 'not so good really',
            'a_to_z': 'Charles, Dog, Poodle',
            'a_to_z_letter': 'C',
        }
        self.assertEqual(resp.json['merged_data'], expected_merged_data)

    def test_vehicle_get(self):
        resp = self.fetch(self.vehicle_url, method=responses.GET)
        self.assertEqual(resp.code, status.HTTP_200_OK)
        expected_merged_data = self.expected_common_merge_data_get | {
            'first_name': '',
            'last_name': '',
            'full_name': 'BMT 216A, Aston Martin DB5',
            'email': self.vehicle_bci.email,
            'cell_phone': self.vehicle_bci.cell_phone,
            'birthday': None,
            'short_birthday': None,
            'allergens': '',
            'type_data': {
                'card_type': CustomerCardType.VEHICLE,
                'additional_data': {
                    'registration_number': 'BMT 216A',
                    'manufacturer': 'Aston Martin',
                    'model': 'DB5',
                    'year': 1964,
                    'vin_number': 'DP5/216/1',
                    'additional_info': 'from the older one',
                },
            },
            'business_secret_note': '',
            'a_to_z': 'BMT 216A, Aston Martin DB5',
            'a_to_z_letter': 'B',
        }
        self.assertEqual(resp.json['customer']['merged_data'], expected_merged_data)

    def test_edit_pet_wrong_data(self):
        body = {
            'type_data': {
                'card_type': CustomerCardType.PET,
                'additional_data': {
                    'pet_type': 'dog?',
                    'breed': 'Poodle',
                    'weight': 'heavy',
                    'additional_info': 'good boy',
                },
            },
        }

        resp = self.fetch(self.pet_url, body=body, method=responses.PUT)
        self.assertEqual(resp.code, status.HTTP_400_BAD_REQUEST)

    def test_edit_vehicle_wrong_data_blank(self):
        body = {
            'type_data': {
                'card_type': CustomerCardType.VEHICLE,
                'additional_data': {'registration_number': ''},
            },
        }

        resp = self.fetch(self.vehicle_url, body=body, method=responses.PUT)
        self.assertEqual(resp.code, status.HTTP_400_BAD_REQUEST)
        self.assertDictEqual(
            resp.json['errors'][0],
            {
                'code': 'blank',
                'field': 'registration_number',
                'type': 'validation',
                'description': 'This field may not be blank.',
            },
        )

    def test_edit_vehicle_wrong_data_null(self):
        body = {
            'type_data': {
                'card_type': CustomerCardType.VEHICLE,
                'additional_data': {'registration_number': None},
            },
        }

        resp = self.fetch(self.vehicle_url, body=body, method=responses.PUT)
        self.assertEqual(resp.code, status.HTTP_400_BAD_REQUEST)
        self.assertDictEqual(
            resp.json['errors'][0],
            {
                'code': 'null',
                'field': 'registration_number',
                'type': 'validation',
                'description': 'This field may not be null.',
            },
        )

    def test_edit_pet_name_missing(self):
        body = {
            'first_name': None,
            'type_data': {
                'card_type': CustomerCardType.PET,
                'additional_data': {
                    'pet_type': 'dog?',
                    'breed': 'Poodle',
                    'weight': 120,
                    'additional_info': 'good boy',
                },
            },
        }

        resp = self.fetch(self.pet_url, body=body, method=responses.PUT)
        self.assertEqual(resp.code, status.HTTP_400_BAD_REQUEST)

    def test_edit_vehicle_wrong_year(self):
        body = {
            'type_data': {
                'card_type': CustomerCardType.VEHICLE,
                'additional_data': {
                    'registration_number': 'BMT 216A',
                    'manufacturer': 'Aston Martin',
                    'model': 'DB5',
                    'year': 3200,
                    'vin_number': 'DP5/216/1',
                    'additional_info': 'from the older one',
                },
            },
        }

        resp = self.fetch(self.vehicle_url, body=body, method=responses.PUT)
        self.assertEqual(resp.code, status.HTTP_400_BAD_REQUEST)
        self.assertDictEqual(
            resp.json['errors'][0],
            {
                'code': 'max_value',
                'field': 'year',
                'type': 'validation',
                'description': 'Ensure this value is less than or equal to '
                f'{datetime.today().year}.',
            },
        )

    def test_edit_vehicle_year_in_string(self):
        body = {
            'type_data': {
                'card_type': CustomerCardType.VEHICLE,
                'additional_data': {
                    'registration_number': 'BMT 216A',
                    'manufacturer': 'Aston Martin',
                    'model': 'DB5',
                    'year': '1950',
                    'vin_number': 'DP5/216/1',
                    'additional_info': 'from the older one',
                },
            },
        }

        resp = self.fetch(self.vehicle_url, body=body, method=responses.PUT)
        self.assertEqual(resp.code, status.HTTP_200_OK)
        expected_merged_data = self.expected_common_merge_data_put | {
            'first_name': '',
            'last_name': '',
            'full_name': 'BMT 216A, Aston Martin DB5',
            'email': self.vehicle_bci.email,
            'cell_phone': self.vehicle_bci.cell_phone,
            'allergens': '',
            'type_data': {
                'card_type': CustomerCardType.VEHICLE,
                'additional_data': {
                    'registration_number': 'BMT 216A',
                    'manufacturer': 'Aston Martin',
                    'model': 'DB5',
                    'year': 1950,
                    'vin_number': 'DP5/216/1',
                    'additional_info': 'from the older one',
                },
            },
            'business_secret_note': '',
            'a_to_z': 'BMT 216A, Aston Martin DB5',
            'a_to_z_letter': 'B',
        }
        self.assertEqual(resp.json['merged_data'], expected_merged_data)

    def test_pet_with_owner_get(self):
        parent = customer_user.make()
        profile = parent.customer_profile
        profile.city = 'Paris'
        profile.address_line_1 = '118 Boulevard Saint-Germain'
        profile.address_line_2 = '55'
        profile.zipcode = '24160'
        profile.region = region_recipe.make()
        profile.save()
        parent.refresh_from_db()

        parent_bci = bci_recipe.make(user=parent, business=self.business)

        profile.city = 'Paris'
        profile.address_line_1 = '118 Boulevard Saint-Germain'
        profile.address_line_2 = '55'
        profile.zipcode = '24160'
        profile.region = region_recipe.make()
        profile.save()
        parent.refresh_from_db()

        inactive_bci_relation_recipe.make(parent_bci=parent_bci, member_bci=self.pet_bci)
        resp = self.fetch(self.pet_url, method=responses.GET)
        self.assertEqual(resp.code, status.HTTP_200_OK)
        expected_merged_data = self.expected_common_merge_data_get | {
            'first_name': 'Charles',
            'last_name': 'le Chien',
            'full_name': 'Charles, Dog, Poodle',
            'email': parent_bci.email,
            'cell_phone': parent_bci.cell_phone,
            'birthday': '2011-11-04',
            'short_birthday': '1104',
            'allergens': 'peanuts',
            'type_data': {
                'card_type': CustomerCardType.PET,
                'additional_data': {
                    'breed': 'Poodle',
                    'weight': 13,
                    'pet_type': 'Dog',
                    'additional_info': 'good boy',
                },
            },
            'business_secret_note': '',
            'a_to_z': 'Charles, Dog, Poodle',
            'a_to_z_letter': 'C',
            'is_active_member': False,
            'parent_full_name': 'TestFirstName TestLastName',
            'member_parent_combined_name': 'Charles, Dog, Poodle • TestFirstName TestLastName',
        }
        self.assertEqual(resp.json['customer']['merged_data'], expected_merged_data)

        resp = self.fetch(self.details_url(self.business.id, parent_bci.id), method=responses.GET)
        expected_merged_data = self.expected_common_merge_data_get | {
            'first_name': 'TestFirstName',
            'last_name': 'TestLastName',
            'full_name': 'TestFirstName TestLastName',
            'email': parent_bci.email,
            'cell_phone': parent_bci.cell_phone,
            'birthday': None,
            'short_birthday': None,
            'allergens': '',
            'type_data': {
                'card_type': CustomerCardType.PERSON,
                'additional_data': {},
            },
            'business_secret_note': '',
            'a_to_z': 'TestFirstName TestLastName',
            'a_to_z_letter': 'T',
            'is_active_member': True,
            'user': parent_bci.user.id,
            'about_me': None,
            'accepts_push': False,
            'apartment_number': '',
            'badge': 'is_user',
            'bithday_date_int': None,
            'latitude': None,
            'longitude': None,
            'marketing_agreement': None,
            'privacy_policy_agreement': None,
        }
        self.assertEqual(resp.json['customer']['merged_data'], expected_merged_data)
