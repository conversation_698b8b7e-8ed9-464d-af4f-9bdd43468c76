import json

import pytest
from dateutil.relativedelta import relativedelta
from django.conf import settings
from mock import (
    MagicMock,
    patch,
)
from model_bakery import baker
from rest_framework import status
from segment.analytics import Client

from lib.db import UsingDBForReadsRouter, _threadlocal as db_thread
from lib.tools import id_to_external_api, l_b, tznow
from service.tests import (
    BaseAsyncHTTPTest,
    dict_assert,
)
from webapps.booking.models import BookingSources
from webapps.business.baker_recipes import (
    treatment_recipe,
    service_variant_recipe,
)
from webapps.business.enums import (
    ComboPricing,
    ComboType,
    PriceType,
)
from webapps.business.models import (
    Business,
    BusinessCategory,
    ComboMembership,
    Resource,
    Service,
    ServiceVariantChangelog,
    ServicesWordclouds,
    ServiceVariant,
    ServiceVariantPayment,
)
from webapps.consts import FRONTDESK
from webapps.onboarding_space.public import OnboardingSpaceMetricsEnum
from webapps.pos.enums import PaymentTypeEnum
from webapps.pos.models import POS, PaymentType
from webapps.segment.consts import UserRoleEnum
from webapps.user.enums import AuthOriginEnum
from webapps.user.models import User
from webapps.warehouse.models import (
    Commodity,
    VolumeMeasure,
    Warehouse,
    WarehouseFormula,
    WarehouseFormulaRow,
)


@pytest.mark.django_db
class ServicesHandlerTestCase(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{}/services/'

    maxDiff = None

    _base_request_body = {
        'name': 'Neutral Service Name',
        'padding_time': 0,
        'note_to_customer': 'oh hi',
        'description': 'ciach ciach',
        'tax_rate': 10,
    }

    def setUp(self):
        super().setUp()

        primary_category = baker.make(BusinessCategory)
        self.business.primary_category = primary_category
        self.business.owner.cell_phone = '*********'
        self.business.owner.save()
        self.business.save()
        pos = baker.make(
            POS,
            business=self.business,
            pay_by_app_status=POS.PAY_BY_APP_ENABLED,
        )
        baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.PAY_BY_APP)
        BookingSources.objects.filter(api_key=self.business_api_key).update(name=FRONTDESK)

    @patch('service.business.services.segment_analytics.get_segment_api')
    def test_get_with_formula(self, get_segment_api_mock):
        segment_api = MagicMock()
        get_segment_api_mock.return_value = segment_api

        service = baker.make(Service, business=self.business)
        service_variant = baker.make(
            ServiceVariant,
            service=service,
            duration=relativedelta(minutes=15),
        )

        warehouse = baker.make(
            Warehouse,
            business=self.business,
        )
        measure = baker.make(
            VolumeMeasure,
            businesses=[self.business],
            label='Test Measure',
            symbol='TM',
            standard=True,
        )
        commodity_1 = baker.make(
            Commodity,
            business=self.business,
            volume_unit=measure,
        )
        commodity_2 = baker.make(
            Commodity,
            business=self.business,
            archived=True,
        )
        formula_row_0 = baker.make(
            WarehouseFormulaRow,
            commodity=commodity_1,
            count=2,
            warehouse=warehouse,
        )
        formula_row_1 = baker.make(
            WarehouseFormulaRow,
            commodity=commodity_2,
            count=5,
            warehouse=warehouse,
        )
        baker.make(
            WarehouseFormula,
            service_variants=[service_variant],
            rows=[formula_row_0, formula_row_1],
        )

        url = self.url.format(self.business.id)

        response = self.fetch(url, method='GET')

        assert response.code == status.HTTP_200_OK

        resp_json = response.json
        services = resp_json['services']
        assert len(services) == 1
        resp_service = services[0]
        assert resp_service['id'] == service.id
        assert resp_service['name'] == service.name

        resp_variants = resp_service['variants']
        assert len(resp_variants) == 1

        resp_variant = resp_variants[0]
        assert resp_variant['id'] == service_variant.id
        assert 'formula' in resp_variant
        resp_formula = resp_variant['formula']
        assert resp_formula

        assert resp_formula[0]['id'] == formula_row_0.id
        assert resp_formula[0]['count'] == formula_row_0.count
        assert resp_formula[0]['warehouse'] == formula_row_0.warehouse.id
        assert isinstance(resp_formula[0]['commodity'], dict)
        assert not resp_formula[0]['commodity']['archived']

        assert resp_formula[1]['id'] == formula_row_1.id
        assert resp_formula[1]['count'] == formula_row_1.count
        assert resp_formula[1]['warehouse'] == formula_row_1.warehouse.id
        isinstance(resp_formula[1]['commodity'], dict)
        assert resp_formula[1]['commodity']['archived']

    @patch('service.business.services.segment_analytics.get_segment_api')
    def test_create(self, get_segment_api_mock):
        # pylint: disable=too-many-statements
        segment_api = MagicMock()
        get_segment_api_mock.return_value = segment_api

        resource = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_user=self.user,
        )

        url = self.url.format(self.business.id)

        body = self.get_request_body(
            **{
                'resources': [resource.id],
                'variants': [
                    {
                        'time_slot_interval': 15,
                        'price': 10,
                        'duration': 60,
                        'type': PriceType.FIXED,
                    }
                ],
            }
        )

        response = self.fetch(url, method='POST', body=body)

        assert response.code == status.HTTP_201_CREATED

        resp_json = json.loads(l_b(response.body))['service']

        assert resp_json['description'] == 'ciach ciach'
        assert resp_json['gap_time'] is None
        assert resp_json['name'] == 'Neutral Service Name'
        assert resp_json['note_to_customer'] == 'oh hi'
        assert resp_json['resources'] == [resource.id]
        assert resp_json['tax_rate'] == '10.00'
        assert resp_json['padding_time'] == 0
        assert resp_json['padding_type'] is None
        assert resp_json['parallel_clients'] == 1
        assert resp_json['wordcloud'] is None
        assert resp_json['variants'][0]['price'] == '10.00'
        assert resp_json['variants'][0]['time_slot_interval'] == 15
        assert resp_json['variants'][0]['type'] == PriceType.FIXED
        assert resp_json['variants'][0]['no_show_protection'] is None
        assert resp_json['variants'][0]['duration'] == 60
        assert resp_json['variants'][0]['gap_hole_duration'] == 0
        assert resp_json['variants'][0]['gap_hole_start_after'] == 0
        assert resp_json['variants'][0]['formula'] == []
        assert not resp_json['is_treatment_selected_by_user']
        assert resp_json['treatment'] is None

        service = Service.objects.get()
        assert service.description == 'ciach ciach'
        assert service.gap_time == relativedelta()
        assert service.name == 'Neutral Service Name'
        assert service.note == 'oh hi'
        assert service.active is True
        assert service.active_staffers == [resource]
        assert service.tax_rate == 10
        assert service.padding_time == relativedelta()
        assert service.padding_type is None
        assert service.parallel_clients == 1
        assert service.wordcloud is None
        assert not service.is_treatment_selected_by_user
        assert service.treatment is None

        variant = service.service_variants.first()
        assert variant.price == 10
        assert variant.time_slot_interval == relativedelta(minutes=15)
        assert variant.type == PriceType.FIXED
        assert variant.has_prepayment is False
        assert variant.has_cancellation_fee is False
        assert variant.duration == relativedelta(minutes=60)
        assert variant.gap_hole_duration == relativedelta()
        assert variant.gap_hole_start_after == relativedelta()

        assert segment_api.service_created.call_count == 1

    @patch('service.business.services.segment_analytics.get_segment_api')
    def test_create_with_formula(self, get_segment_api_mock):
        segment_api = MagicMock()
        get_segment_api_mock.return_value = segment_api

        resource = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_user=self.user,
        )
        warehouse = baker.make(Warehouse, business=self.business)
        service = baker.make(Service, business=self.business)
        baker.make(ServiceVariant, duration='0100', service=service)
        measure = baker.make(
            VolumeMeasure,
            businesses=[self.business],
            label='Test Measure',
            symbol='TM',
            standard=True,
        )
        commodity_1 = baker.make(
            Commodity,
            business=self.business,
            volume_unit=measure,
        )
        commodity_2 = baker.make(
            Commodity,
            business=self.business,
        )

        url = self.url.format(self.business.id)

        body = self.get_request_body(
            **{
                'resources': [resource.id],
                'variants': [
                    {
                        'time_slot_interval': 15,
                        'price': 10,
                        'duration': 60,
                        'type': PriceType.FIXED,
                        'formula': [
                            {
                                'commodity': commodity_1.id,
                                'count': 154,
                                'warehouse': warehouse.id,
                            },
                            {
                                'commodity': commodity_2.id,
                                'count': 615,
                                'warehouse': warehouse.id,
                            },
                        ],
                    }
                ],
            }
        )

        response = self.fetch(url, method='POST', body=body)

        assert response.code == status.HTTP_201_CREATED
        rows = response.json['service']['variants'][0]['formula']
        assert len(rows) == 2
        assert rows[0]['count'] == 154
        assert rows[0]['commodity']['id'] == commodity_1.id
        assert 'name' in rows[0]['commodity']
        assert 'volume_unit' in rows[0]['commodity']
        volume_unit = rows[0]['commodity']['volume_unit']
        dict_assert(
            volume_unit,
            {
                'id': measure.id,
                'symbol': measure.symbol,
                'label': measure.label,
            },
        )
        assert rows[0]['warehouse'] == warehouse.id
        assert rows[1]['count'] == 615
        assert rows[1]['commodity']['id'] == commodity_2.id
        assert 'name' in rows[1]['commodity']
        assert 'volume_unit' in rows[1]['commodity']
        assert rows[1]['warehouse'] == warehouse.id

        formula_row_db_obj = WarehouseFormulaRow.objects.get(id=rows[0]['id'])

        formula_db_object = formula_row_db_obj.formulas.first()
        assert (
            formula_db_object.service_variants.first().id
            == response.json['service']['variants'][0]['id']
        )

    @patch('service.business.services.segment_analytics.get_segment_api')
    def test_create_with_formula_with_archived_commodity(self, get_segment_api_mock):
        segment_api = MagicMock()
        get_segment_api_mock.return_value = segment_api

        warehouse = baker.make(Warehouse)
        commodity_1 = baker.make(
            Commodity,
            business=self.business,
        )
        commodity_2 = baker.make(
            Commodity,
            business=self.business,
            archived=True,
        )

        url = self.url.format(self.business.id)

        body = {
            'name': 'Some service',
            'padding_time': 0,
            'note_to_customer': 'note',
            'description': 'service description',
            'tax_rate': 10,
            'resources': [],
            'variants': [
                {
                    'time_slot_interval': 15,
                    'price': 10,
                    'duration': 60,
                    'type': PriceType.FIXED,
                    'formula': [
                        {
                            'commodity': commodity_1.id,
                            'count': 154,
                            'warehouse': warehouse.id,
                        },
                        {
                            'commodity': commodity_2.id,
                            'count': 615,
                            'warehouse': warehouse.id,
                        },
                    ],
                }
            ],
        }

        response = self.fetch(url, method='POST', body=body)

        assert response.code == status.HTTP_400_BAD_REQUEST
        assert response.json['errors'][0]['field'] == 'variants'
        assert response.json['errors'][0]['code'] == 'invalid'

    @patch('service.business.services.segment_analytics.get_segment_api')
    def test_create_with_deprecated_deposit(self, get_segment_api_mock):
        segment_api = MagicMock()
        get_segment_api_mock.return_value = segment_api

        resource = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_user=self.user,
        )

        url = self.url.format(self.business.id)

        body = self.get_request_body(
            **{
                'resources': [resource.id],
                'variants': [
                    {
                        'time_slot_interval': 15,
                        'price': 10,
                        'duration': 60,
                        'deposit': 10,
                        'type': PriceType.FIXED,
                    }
                ],
            }
        )

        response = self.fetch(url, method='POST', body=body)

        assert response.code == status.HTTP_201_CREATED

        resp_json = json.loads(l_b(response.body))['service']

        assert resp_json['description'] == 'ciach ciach'
        assert resp_json['gap_time'] is None
        assert resp_json['name'] == 'Neutral Service Name'
        assert resp_json['note_to_customer'] == 'oh hi'
        assert resp_json['resources'] == [resource.id]
        assert resp_json['tax_rate'] == '10.00'
        assert resp_json['padding_time'] == 0
        assert resp_json['padding_type'] is None
        assert resp_json['parallel_clients'] == 1
        assert resp_json['wordcloud'] is None
        assert resp_json['variants'][0]['price'] == '10.00'
        assert resp_json['variants'][0]['time_slot_interval'] == 15
        assert resp_json['variants'][0]['type'] == PriceType.FIXED
        assert resp_json['variants'][0]['no_show_protection'] is None
        assert resp_json['variants'][0]['duration'] == 60
        assert resp_json['variants'][0]['gap_hole_duration'] == 0
        assert resp_json['variants'][0]['gap_hole_start_after'] == 0

        service = Service.objects.get()
        assert service.description == 'ciach ciach'
        assert service.gap_time == relativedelta()
        assert service.name == 'Neutral Service Name'
        assert service.note == 'oh hi'
        assert service.active is True
        assert service.active_staffers == [resource]
        assert service.tax_rate == 10
        assert service.padding_time == relativedelta()
        assert service.padding_type is None
        assert service.parallel_clients == 1
        assert service.wordcloud is None

        variant = service.service_variants.first()
        assert variant.price == 10
        assert variant.time_slot_interval == relativedelta(minutes=15)
        assert variant.type == PriceType.FIXED
        assert variant.has_prepayment is False
        assert variant.has_cancellation_fee is False
        assert variant.duration == relativedelta(minutes=60)
        assert variant.gap_hole_duration == relativedelta()
        assert variant.gap_hole_start_after == relativedelta()

        assert segment_api.service_created.call_count == 1

    # pylint: disable=too-many-statements
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    @patch('service.business.services.segment_analytics.get_segment_api')
    def test_create_with_gap_time(
        self,
        get_segment_api_mock,
        analytics_track_mock,
        analytics_identify_mock,
    ):
        segment_api = MagicMock()
        get_segment_api_mock.return_value = segment_api

        resource = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_user=self.user,
        )
        wordcloud = baker.make(ServicesWordclouds)

        url = self.url.format(self.business.id)

        body = self.get_request_body(
            **{
                'resources': [resource.id],
                'gap_time': 140,
                'wordcloud': wordcloud.id,
                'variants': [
                    {
                        'time_slot_interval': 15,
                        'price': 10,
                        'duration': 140,
                        'type': PriceType.FIXED,
                        'gap_hole_duration': 70,
                        'gap_hole_start_after': 10,
                    }
                ],
            }
        )

        response = self.fetch(url, method='POST', body=body)

        assert response.code == status.HTTP_201_CREATED

        resp_json = json.loads(l_b(response.body))['service']

        assert resp_json['description'] == 'ciach ciach'
        assert resp_json['gap_time'] == 140
        assert resp_json['name'] == 'Neutral Service Name'
        assert resp_json['note_to_customer'] == 'oh hi'
        assert resp_json['resources'] == [resource.id]
        assert resp_json['tax_rate'] == '10.00'
        assert resp_json['padding_time'] == 0
        assert resp_json['padding_type'] is None
        assert resp_json['parallel_clients'] == 1
        assert resp_json['wordcloud'] == wordcloud.id
        assert resp_json['variants'][0]['price'] == '10.00'
        assert resp_json['variants'][0]['time_slot_interval'] == 15
        assert resp_json['variants'][0]['type'] == PriceType.FIXED
        assert resp_json['variants'][0]['no_show_protection'] is None
        assert resp_json['variants'][0]['duration'] == 140
        assert resp_json['variants'][0]['gap_hole_duration'] == 70
        assert resp_json['variants'][0]['gap_hole_start_after'] == 10

        service = Service.objects.get()
        assert service.description == 'ciach ciach'
        assert service.gap_time == relativedelta(minutes=140)
        assert service.name == 'Neutral Service Name'
        assert service.note == 'oh hi'
        assert service.active is True
        assert service.active_staffers == [resource]
        assert service.tax_rate == 10
        assert service.padding_time == relativedelta()
        assert service.padding_type is None
        assert service.parallel_clients == 1
        assert service.wordcloud == wordcloud

        variant = service.service_variants.first()
        assert variant.price == 10
        assert variant.time_slot_interval == relativedelta(minutes=15)
        assert variant.type == PriceType.FIXED
        assert variant.has_prepayment is False
        assert variant.has_cancellation_fee is False
        assert variant.duration == relativedelta(minutes=140)
        assert variant.gap_hole_duration == relativedelta(minutes=70)
        assert variant.gap_hole_start_after == relativedelta(minutes=10)

        assert segment_api.service_created.call_count == 1
        assert analytics_track_mock.call_count == 0
        assert analytics_identify_mock.call_count == 0

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    @patch('service.business.services.segment_analytics.get_segment_api')
    def test_create_with_no_show_protection_pre_payment(
        self,
        get_segment_api_mock,
        analytics_track_mock,
        analytics_identify_mock,
    ):
        payment_type = ServiceVariantPayment.PRE_PAYMENT_TYPE
        segment_api = MagicMock()
        get_segment_api_mock.return_value = segment_api

        resource = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_user=self.user,
        )
        service = baker.make(Service, business=self.business)
        baker.make(ServiceVariant, duration='0100', service=service)

        url = self.url.format(self.business.id)

        body = self.get_request_body(
            **{
                'resources': [resource.id],
                'variants': [
                    {
                        'time_slot_interval': 15,
                        'price': 10,
                        'duration': 60,
                        'type': PriceType.FIXED,
                        'no_show_protection': {
                            "type": payment_type,
                            "percentage": 50,
                        },
                    },
                ],
            }
        )

        response = self.fetch(url, method='POST', body=body)
        assert response.code == status.HTTP_201_CREATED
        no_show_protection = response.json['service']['variants'][0]['no_show_protection']
        dict_assert(
            no_show_protection,
            {'type': payment_type, 'percentage': 50},
        )
        variant = ServiceVariant.objects.get(
            id=response.json['service']['variants'][0]['id'],
        )
        assert variant.payment.payment_type == payment_type
        assert analytics_track_mock.call_count == 1
        assert analytics_identify_mock.call_count == 1
        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {
                'event': 'Protection_Service_Enabled',
                'properties': {
                    'email': self.business.owner.email,
                    'country': settings.API_COUNTRY,
                    'business_id': id_to_external_api(self.business.id),
                    'booking_protection_enabled': True,
                },
            },
        )
        dict_assert(
            analytics_identify_mock.call_args_list[0][1]['traits'],
            {
                'country': settings.API_COUNTRY,
                'email': self.business.owner.email,
                'user_role': UserRoleEnum.OWNER.value,
                'phone': self.business.owner.cell_phone,
                'offer_type': Business.Package(
                    self.business.package
                ).label,  # pylint: disable=no-value-for-parameter
                'business_id': id_to_external_api(self.business.id),
                'booking_protection_enabled': True,
                'services_with_protection': ServiceVariant.objects.filter(
                    active=True,
                    service__business_id=self.business.id,
                    payment__isnull=False,
                ).count(),
            },
        )

    @patch('service.business.services.segment_analytics.get_segment_api')
    def test_create_with_no_show_protection_cancellation_fee(
        self,
        get_segment_api_mock,
    ):
        payment_type = ServiceVariantPayment.CANCELLATION_FEE_TYPE
        segment_api = MagicMock()
        get_segment_api_mock.return_value = segment_api

        resource = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_user=self.user,
        )
        service = baker.make(Service, business=self.business)
        baker.make(ServiceVariant, duration='0100', service=service)

        url = self.url.format(self.business.id)

        body = self.get_request_body(
            **{
                'resources': [resource.id],
                'variants': [
                    {
                        'time_slot_interval': 15,
                        'price': 10,
                        'duration': 60,
                        'type': PriceType.FIXED,
                    },
                ],
                'no_show_protection': {
                    "type": payment_type,
                    "percentage": 50,
                },
            }
        )

        response = self.fetch(url, method='POST', body=body)
        assert response.code == status.HTTP_201_CREATED

        expected_no_show_protection = {'type': payment_type, 'percentage': 50}

        resp_json = response.json

        variant_no_show_protection = resp_json['service']['variants'][0]['no_show_protection']
        service_no_show_protection = resp_json['service']['no_show_protection']
        dict_assert(variant_no_show_protection, expected_no_show_protection)
        dict_assert(service_no_show_protection, expected_no_show_protection)
        variant = ServiceVariant.objects.get(
            id=resp_json['service']['variants'][0]['id'],
        )
        assert variant.payment.payment_type == payment_type

    @patch('service.business.services.segment_analytics.get_segment_api')
    def test_create_with_no_show_protection_few_variants(
        self,
        get_segment_api_mock,
    ):
        segment_api = MagicMock()
        get_segment_api_mock.return_value = segment_api

        resource = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_user=self.user,
        )
        service = baker.make(Service, business=self.business)
        baker.make(ServiceVariant, duration='0100', service=service)

        url = self.url.format(self.business.id)

        body = self.get_request_body(
            **{
                'resources': [resource.id],
                'variants': [
                    {
                        'time_slot_interval': 15,
                        'price': 10,
                        'duration': 20,
                        'type': PriceType.FIXED,
                        'no_show_protection': {
                            "type": ServiceVariantPayment.CANCELLATION_FEE_TYPE,
                            "percentage": 50,
                        },
                    },
                    {
                        'time_slot_interval': 15,
                        'price': 20,
                        'duration': 40,
                        'type': PriceType.FIXED,
                        'no_show_protection': {
                            "type": ServiceVariantPayment.PRE_PAYMENT_TYPE,
                            "percentage": 40,
                        },
                    },
                    {
                        'time_slot_interval': 15,
                        'price': 30,
                        'duration': 60,
                        'type': PriceType.FIXED,
                        # just no no_show_protection
                    },
                ],
            }
        )

        response = self.fetch(url, method='POST', body=body)
        assert response.code == status.HTTP_201_CREATED
        variant_0 = ServiceVariant.objects.get(
            id=response.json['service']['variants'][0]['id'],
        )
        variant_1 = ServiceVariant.objects.get(
            id=response.json['service']['variants'][1]['id'],
        )
        variant_2 = ServiceVariant.objects.get(
            id=response.json['service']['variants'][2]['id'],
        )
        no_show_protection_percentage_0 = variant_0.payment.payment_amount / variant_0.price
        no_show_protection_percentage_1 = variant_1.payment.payment_amount / variant_1.price
        no_show_protection_percentage_2 = variant_2.payment.payment_amount / variant_2.price
        assert (
            0.5
            == no_show_protection_percentage_0
            == no_show_protection_percentage_1
            == no_show_protection_percentage_2
        )

    @patch('service.business.services.segment_analytics.get_segment_api')
    def test_create_with_no_show_protection_cancellation_fee_no_variant(
        self,
        get_segment_api_mock,
    ):
        payment_type = ServiceVariantPayment.CANCELLATION_FEE_TYPE
        segment_api = MagicMock()
        get_segment_api_mock.return_value = segment_api

        resource = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_user=self.user,
        )
        service = baker.make(Service, business=self.business)
        baker.make(ServiceVariant, duration='0100', service=service)

        url = self.url.format(self.business.id)

        body = self.get_request_body(
            **{
                'resources': [resource.id],
                'variants': [],  # cause of error
                'no_show_protection': {
                    "type": payment_type,
                    "percentage": 50,
                },
            }
        )

        response = self.fetch(url, method='POST', body=body)
        assert response.code == status.HTTP_400_BAD_REQUEST
        assert (
            response.json['errors'][0]['description'] == 'At least one service variant is required'
        )

    @patch('service.business.services.segment_analytics.get_segment_api')
    def test_create_with_no_show_protection_few_variants_one_free(
        self,
        get_segment_api_mock,
    ):
        segment_api = MagicMock()
        get_segment_api_mock.return_value = segment_api

        resource = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_user=self.user,
        )
        service = baker.make(Service, business=self.business)
        baker.make(ServiceVariant, duration='0100', service=service)

        url = self.url.format(self.business.id)

        body = self.get_request_body(
            **{
                'resources': [resource.id],
                'variants': [
                    {
                        'time_slot_interval': 15,
                        'price': 10,
                        'duration': 20,
                        'type': PriceType.FIXED,
                    },
                    {
                        'time_slot_interval': 15,
                        'price': 20,
                        'duration': 40,
                        'type': PriceType.FIXED,
                    },
                    {
                        'time_slot_interval': 15,
                        'price': None,
                        'duration': 60,
                        'type': PriceType.FREE,
                    },
                ],
                'no_show_protection': {
                    "type": ServiceVariantPayment.CANCELLATION_FEE_TYPE,
                    "percentage": 50,
                },
            }
        )

        response = self.fetch(url, method='POST', body=body)
        assert response.code == status.HTTP_400_BAD_REQUEST
        dict_assert(
            response.json,
            {
                'errors': [
                    {
                        'field': 'non_field_errors',
                        'description': "Can't set no show protection for variants without price",
                        'code': 'user_decision',
                    }
                ],
            },
        )

        body['no_show_protection']['skip_variant_if_no_price'] = True
        response = self.fetch(url, method='POST', body=body)
        assert response.code == status.HTTP_201_CREATED

        variant_0 = ServiceVariant.objects.get(
            id=response.json['service']['variants'][0]['id'],
        )
        variant_1 = ServiceVariant.objects.get(
            id=response.json['service']['variants'][1]['id'],
        )
        variant_2 = ServiceVariant.objects.get(
            id=response.json['service']['variants'][2]['id'],
        )
        no_show_protection_percentage_0 = variant_0.payment.payment_amount / variant_0.price
        no_show_protection_percentage_1 = variant_1.payment.payment_amount / variant_1.price
        assert 0.5 == no_show_protection_percentage_0 == no_show_protection_percentage_1
        assert variant_2.price is None
        assert not ServiceVariantPayment.objects.filter(
            service_variant=variant_2,
        ).exists()

    # based on https://booksy.atlassian.net/browse/HAM-1316
    @patch('service.business.services.segment_analytics.get_segment_api')
    def test_create_with_pp_on_whole_service(self, get_segment_api_mock):
        segment_api = MagicMock()
        get_segment_api_mock.return_value = segment_api

        resource = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_user=self.user,
        )

        url = self.url.format(self.business.id)

        body = self.get_request_body(
            **{
                'treatment': 255,
                'is_available_for_customer_booking': True,
                'is_traveling_service': False,
                'description_type': 'M',
                'resources': [resource.id],
                'variants': [
                    {
                        'staffers': [resource.id],
                        'time_slot_interval': 15,
                        'price': 10,
                        'duration': 60,
                        'type': PriceType.FIXED,
                    }
                ],
                'is_treatment_selected_by_user': True,
                'no_show_protection': {
                    'percentage': 50,
                    'skip_variant_if_no_price': False,
                    'type': ServiceVariantPayment.PRE_PAYMENT_TYPE,
                },
                'is_online_service': False,
            }
        )
        response = self.fetch(url, method='POST', body=body)
        assert response.code == status.HTTP_201_CREATED
        service_variant = ServiceVariant.objects.get(
            id=response.json['service']['variants'][0]['id'],
        )
        assert service_variant.payment.payment_type == ServiceVariantPayment.PRE_PAYMENT_TYPE

    @patch('service.business.services.segment_analytics.get_segment_api')
    def test_create_fetches_user_and_business_from_primary_db(self, get_segment_api_mock):
        segment_api = MagicMock()
        get_segment_api_mock.return_value = segment_api

        resource = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_user=self.user,
        )

        url = self.url.format(self.business.id)

        body = self.get_request_body(
            **{
                'resources': [resource.id],
                'variants': [
                    {
                        'time_slot_interval': 15,
                        'price': 10,
                        'duration': 60,
                        'type': PriceType.FIXED,
                    }
                ],
            }
        )

        user_read_from_primary_db_flag = [False]
        business_read_from_primary_db_flag = [False]

        with patch.object(UsingDBForReadsRouter, 'db_for_read') as db_for_read_method:
            # pylint: disable=dangerous-default-value
            def mock_read_only_db(
                model,
                *args,
                user_read_from_primary_db_flag=user_read_from_primary_db_flag,
                **kwargs,
            ):
                db_for_read_only = getattr(db_thread, 'DB_FOR_READ_ONLY', [])
                if 'read-only' not in db_for_read_only:
                    if User in type.mro(model):
                        user_read_from_primary_db_flag[0] = True
                    elif Business in type.mro(model):
                        business_read_from_primary_db_flag[0] = True
                if db_for_read_only and db_for_read_only[-1] in settings.DATABASES:
                    return db_for_read_only[-1]

            # pylint: enable=dangerous-default-value

            db_for_read_method.side_effect = mock_read_only_db
            response = self.fetch(url, method='POST', body=body)

            assert response.code == status.HTTP_201_CREATED
            assert user_read_from_primary_db_flag[0]
            assert business_read_from_primary_db_flag[0]

    @patch('service.business.services.segment_analytics.get_segment_api')
    def test_create_combo_service__services_price(self, get_segment_api_mock):
        url = self.url.format(self.business.id)

        staffer = baker.make(
            Resource,
            business=self.business,
        )
        service = baker.make(
            Service,
            name="Nazwa podstawowa",
            business=self.business,
            gap_time=relativedelta(minutes=0),
            padding_time=relativedelta(minutes=30),
        )

        service_variant_child_1 = ServiceVariant.objects.create(
            service=service,
            duration=relativedelta(minutes=30),
            price=100,
            valid_from=tznow(),
            type=PriceType.FIXED,
            time_slot_interval=relativedelta(minutes=30),
        )

        service_variant_child_2 = ServiceVariant.objects.create(
            service=service,
            duration=relativedelta(minutes=50),
            price=150,
            valid_from=tznow(),
            type=PriceType.FIXED,
            time_slot_interval=relativedelta(minutes=30),
        )

        body = dict(
            name='Test service',
            resources=[staffer.id],
            combo_type=ComboType.SEQUENCE,
            variants=[
                dict(
                    combo_pricing=ComboPricing.SERVICES,
                    type=None,
                    price=None,
                    duration=30,
                    combo_children=[
                        dict(service_variant=dict(id=service_variant_child_1.id), gap_time=15),
                        dict(service_variant=dict(id=service_variant_child_2.id)),
                    ],
                )
            ],
        )

        response = self.fetch(url, method='POST', body=body)
        self.assertEqual(response.code, status.HTTP_201_CREATED)
        self.assertEqual(response.json['service']['combo_type'], ComboType.SEQUENCE)

        variant_data = response.json['service']['variants'][0]
        expected_variant_data = {
            'combo_pricing': ComboPricing.SERVICES,
            'price': '250.00',
            'type': PriceType.FIXED,
            'service_price': '$250.00',
        }
        self.assertDictEqual(
            {key: variant_data[key] for key in ['combo_pricing', 'type', 'price', 'service_price']},
            expected_variant_data,
        )

        combo_children_data = variant_data['combo_children']
        expected_combo_children_data = [
            {
                'price': '100.00',
                'type': PriceType.FIXED,
                'service_price': '$100.00',
                'gap_time': 15,
            },
            {
                'price': '150.00',
                'type': PriceType.FIXED,
                'service_price': '$150.00',
                'gap_time': 0,
            },
        ]
        self.assertListEqual(
            [
                {
                    key: combo_child_data[key]
                    for key in ['price', 'type', 'service_price', 'gap_time']
                }
                for combo_child_data in combo_children_data
            ],
            expected_combo_children_data,
        )

    @patch('service.business.services.segment_analytics.get_segment_api')
    def test_create_combo_service__custom_price(self, get_segment_api_mock):
        url = self.url.format(self.business.id)

        staffer = baker.make(
            Resource,
            business=self.business,
        )
        service = baker.make(
            Service,
            name="Nazwa podstawowa",
            business=self.business,
            gap_time=relativedelta(minutes=0),
            padding_time=relativedelta(minutes=30),
        )

        service_variant_child_1 = ServiceVariant.objects.create(
            service=service,
            duration=relativedelta(minutes=30),
            price=100,
            valid_from=tznow(),
            type=PriceType.FIXED,
            time_slot_interval=relativedelta(minutes=30),
        )

        service_variant_child_2 = ServiceVariant.objects.create(
            service=service,
            duration=relativedelta(minutes=50),
            price=150,
            valid_from=tznow(),
            type=PriceType.FIXED,
            time_slot_interval=relativedelta(minutes=30),
        )

        body = {
            'name': 'Test service',
            'resources': [staffer.id],
            'combo_type': ComboType.SEQUENCE,
            'variants': [
                {
                    'combo_pricing': ComboPricing.CUSTOM,
                    'price': None,
                    'type': None,
                    'duration': 30,
                    'combo_children': [
                        {
                            'service_variant': {'id': service_variant_child_1.id},
                            'gap_time': 15,
                            'price': 10,
                            'type': PriceType.FIXED,
                        },
                        {
                            'service_variant': {'id': service_variant_child_2.id},
                            'price': 5,
                            'type': PriceType.STARTS_AT,
                        },
                    ],
                }
            ],
        }

        response = self.fetch(url, method='POST', body=body)
        self.assertEqual(response.code, status.HTTP_201_CREATED)
        self.assertEqual(response.json['service']['combo_type'], ComboType.SEQUENCE)

        variant_data = response.json['service']['variants'][0]
        expected_variant_data = {
            'combo_pricing': ComboPricing.CUSTOM,
            'price': '15.00',
            'type': PriceType.STARTS_AT,
            'service_price': '$15.00+',
        }
        self.assertDictEqual(
            {key: variant_data[key] for key in ['combo_pricing', 'price', 'type', 'service_price']},
            expected_variant_data,
        )

        combo_children_data = variant_data['combo_children']
        expected_combo_children_data = [
            {
                'price': '10.00',
                'type': PriceType.FIXED,
                'service_price': '$10.00',
                'gap_time': 15,
            },
            {
                'price': '5.00',
                'type': PriceType.STARTS_AT,
                'service_price': '$5.00+',
                'gap_time': 0,
            },
        ]
        self.assertListEqual(
            [
                {
                    key: combo_child_data[key]
                    for key in ['price', 'type', 'service_price', 'gap_time']
                }
                for combo_child_data in combo_children_data
            ],
            expected_combo_children_data,
        )
        self.assertEqual(
            ServiceVariantChangelog.objects.filter(service_variant=variant_data['id'])
            .first()
            .data['custom_combo_price'],
            '15.00',
        )

    @patch('service.business.services.segment_analytics.get_segment_api')
    def test_create_traveling_combo_service(self, get_segment_api_mock):
        url = self.url.format(self.business.id)

        staffer = baker.make(
            Resource,
            business=self.business,
        )
        service = baker.make(
            Service,
            name='Traveling service',
            business=self.business,
            gap_time=relativedelta(minutes=0),
            padding_time=relativedelta(minutes=30),
            is_traveling_service=True,
        )

        service_variant_1 = ServiceVariant.objects.create(
            service=service,
            duration=relativedelta(minutes=30),
            price=100,
            valid_from=tznow(),
            type=PriceType.FIXED,
            time_slot_interval=relativedelta(minutes=30),
        )

        service_variant_2 = ServiceVariant.objects.create(
            service=service,
            duration=relativedelta(minutes=50),
            price=150,
            valid_from=tznow(),
            type=PriceType.FIXED,
            time_slot_interval=relativedelta(minutes=30),
        )

        body = dict(
            name='Traveling combo service',
            resources=[staffer.id],
            combo_type=ComboType.SEQUENCE,
            variants=[
                dict(
                    combo_pricing=ComboPricing.SERVICES,
                    combo_children=[
                        dict(service_variant=dict(id=service_variant_1.id)),
                        dict(service_variant=dict(id=service_variant_2.id)),
                    ],
                )
            ],
        )

        response = self.fetch(url, method='POST', body=body)
        assert response.code == status.HTTP_201_CREATED
        assert response.json['service']['is_traveling_service'] is True

    @patch('service.business.services.segment_analytics.get_segment_api')
    def test_create_online_combo_service(self, get_segment_api_mock):
        url = self.url.format(self.business.id)

        staffer = baker.make(
            Resource,
            business=self.business,
        )
        service = baker.make(
            Service,
            name='Service',
            business=self.business,
            gap_time=relativedelta(minutes=0),
            padding_time=relativedelta(minutes=30),
            is_online_service=True,
        )

        service_variant_1 = ServiceVariant.objects.create(
            service=service,
            duration=relativedelta(minutes=30),
            price=100,
            valid_from=tznow(),
            type=PriceType.FIXED,
            time_slot_interval=relativedelta(minutes=30),
        )

        service_variant_2 = ServiceVariant.objects.create(
            service=service,
            duration=relativedelta(minutes=50),
            price=150,
            valid_from=tznow(),
            type=PriceType.FIXED,
            time_slot_interval=relativedelta(minutes=30),
        )

        body = dict(
            name='Test service',
            resources=[staffer.id],
            combo_type=ComboType.SEQUENCE,
            variants=[
                dict(
                    combo_pricing=ComboPricing.CUSTOM,
                    price=None,
                    type=None,
                    duration=None,
                    combo_children=[
                        dict(service_variant=dict(id=service_variant_1.id), gap_time=15),
                        dict(service_variant=dict(id=service_variant_2.id), type=PriceType.FREE),
                    ],
                )
            ],
        )

        response = self.fetch(url, method='POST', body=body)
        assert response.code == status.HTTP_201_CREATED

        variant = response.json['service']['variants'][0]
        assert variant['combo_children'][0]['type'] == PriceType.DONT_SHOW
        assert variant['combo_children'][1]['type'] == PriceType.FREE

    def test_create_with_treatment_selected_by_user(self):
        treatment = treatment_recipe.make()
        resource = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_user=self.user,
        )

        url = self.url.format(self.business.id)

        body = self.get_request_body(
            **{
                'resources': [resource.id],
                'variants': [
                    {
                        'time_slot_interval': 15,
                        'price': 10,
                        'duration': 60,
                        'type': PriceType.FIXED,
                    }
                ],
                'treatment': treatment.id,
                'is_treatment_selected_by_user': True,
            }
        )

        response = self.fetch(url, args={'use_service_type': 1}, method='POST', body=body)
        assert response.code == status.HTTP_201_CREATED

        resp_json = json.loads(l_b(response.body))['service']
        assert resp_json['is_treatment_selected_by_user']
        assert resp_json['treatment'] == treatment.id

        service = Service.objects.get()
        assert service.is_treatment_selected_by_user
        assert service.treatment.id == treatment.id

    def test_create_with_treatment_not_selected_by_user(self):
        treatment = treatment_recipe.make()
        resource = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_user=self.user,
        )

        url = self.url.format(self.business.id)

        body = self.get_request_body(
            **{
                'resources': [resource.id],
                'variants': [
                    {
                        'time_slot_interval': 15,
                        'price': 10,
                        'duration': 60,
                        'type': PriceType.FIXED,
                    }
                ],
                'treatment': treatment.id,
            }
        )

        response = self.fetch(url, method='POST', body=body)
        assert response.code == status.HTTP_201_CREATED

        resp_json = json.loads(l_b(response.body))['service']
        assert not resp_json['is_treatment_selected_by_user']
        assert resp_json['treatment'] is None

        service = Service.objects.get()
        assert not service.is_treatment_selected_by_user
        assert service.treatment.id == treatment.id

    def test_create_with_missing_field_is_treatment_selected_by_user(self):
        treatment = treatment_recipe.make()
        resource = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_user=self.user,
        )

        url = self.url.format(self.business.id)

        body = self.get_request_body(
            **{
                'resources': [resource.id],
                'variants': [
                    {
                        'time_slot_interval': 15,
                        'price': 10,
                        'duration': 60,
                        'type': PriceType.FIXED,
                    }
                ],
                'treatment': treatment.id,
            }
        )

        response = self.fetch(url, args={'use_service_type': 1}, method='POST', body=body)
        assert response.code == status.HTTP_400_BAD_REQUEST

    def get_request_body(self, **kwargs):
        body = dict(self._base_request_body)
        body.update(**kwargs)
        return body

    def test_get_service_with_service_variant_and_combo(self):
        service = baker.make(Service, business=self.business, order=0)
        combo_service_variant = service_variant_recipe.make(
            service=service, label='ServiceVariantCombo'
        )
        child_service_variant = service_variant_recipe.make(
            service=service, label='ServiceVariantChild'
        )
        ComboMembership.objects.create(
            combo=combo_service_variant,
            child=child_service_variant,
            order=1,
        )

        url = self.url.format(self.business.id)
        response = self.fetch(url, method='GET')

        self.assertEqual(response.code, status.HTTP_200_OK)
        variant_ids = []
        for variant in response.json['services'][0]['variants']:
            variant_id = variant['id']
            if variant_id == child_service_variant.id:
                self.assertEqual(variant['label'], 'ServiceVariantChild')
            elif variant_id == combo_service_variant.id:
                self.assertEqual(variant['label'], 'ServiceVariantCombo')
                self.assertEqual(
                    variant['combo_children'][0]['service_variant']['label'], 'ServiceVariantChild'
                )
            variant_ids.append(variant_id)
        self.assertEqual(
            set(variant_ids), set([combo_service_variant.id, child_service_variant.id])
        )

    def test_create_service_as_superadmin(self):
        superuser_name = 'super.admin'
        url = self.url.format(self.business.id)
        self.session = self.user.create_session(
            origin=AuthOriginEnum.BOOKSY,
            fingerprint='',
            superuser_name=superuser_name,
        )
        resource = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_user=self.user,
        )
        body = self.get_request_body(
            **{
                'resources': [resource.id],
                'variants': [
                    {
                        'time_slot_interval': 15,
                        'price': 10,
                        'duration': 60,
                        'type': PriceType.FIXED,
                    }
                ],
            }
        )

        # super user doesn't exists
        response = self.fetch(url, method='POST', body=body)
        assert response.code == status.HTTP_404_NOT_FOUND

        # superuser saved in session only with name (used in core session store)
        baker.make(User, email=f'{superuser_name}@booksy.com')
        response = self.fetch(url, method='POST', body=body)
        assert response.code == status.HTTP_201_CREATED

        # superuser saved in session with full email (used in Auth session store)
        self.session = self.user.create_session(
            origin=AuthOriginEnum.BOOKSY,
            fingerprint='',
            superuser_name=f'{superuser_name}@booksy.com',
        )
        response = self.fetch(url, method='POST', body=body)
        assert response.code == status.HTTP_201_CREATED

    @patch(
        'webapps.onboarding_space.application.services.onboarding_space.'
        'OnboardingSpaceService.increment_metric'
    )
    @patch('service.business.services.segment_analytics.get_segment_api')
    def test_onboarding_space_increment_services(self, get_segment_api_mock, increment_metric_mock):
        self.business.status = Business.Status.PAID
        self.business.save()

        segment_api = MagicMock()
        get_segment_api_mock.return_value = segment_api

        resource = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_user=self.user,
        )
        url = self.url.format(self.business.id)
        body = self.get_request_body(
            **{
                'resources': [resource.id],
                'variants': [
                    {
                        'time_slot_interval': 45,
                        'price': 500,
                        'duration': 60,
                        'type': PriceType.FIXED,
                    }
                ],
            }
        )
        response = self.fetch(url, method='POST', body=body)
        assert response.code == status.HTTP_201_CREATED
        assert increment_metric_mock.call_count == 1
        assert increment_metric_mock.call_args[1]['business_id'] == self.business.id
        assert increment_metric_mock.call_args[1]['metric'] == OnboardingSpaceMetricsEnum.SERVICES

    # pylint: disable=line-too-long
    @patch(
        'webapps.onboarding_space.application.services.onboarding_space.OnboardingSpaceService.mark_profile_setup_step_completed'
    )
    def test_onboarding_space_mark_step_completed_business_in_setup(self, mark_step_completed_mock):
        self.business.status = Business.Status.SETUP
        self.business.save()
        resource = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_user=self.user,
        )
        url = self.url.format(self.business.id)
        body = self.get_request_body(
            resources=[resource.id],
            variants=[
                {
                    'time_slot_interval': 45,
                    'price': 500,
                    'duration': 60,
                    'type': PriceType.FIXED,
                }
            ],
        )
        response = self.fetch(url, method='POST', body=body)
        assert response.code == status.HTTP_201_CREATED
        assert mark_step_completed_mock.call_count == 0
