import datetime
from collections import defaultdict
from functools import reduce
from typing import List, Optional

from bo_obs.datadog.enums import BooksyTeams
from django.db import IntegrityError, transaction
from django.db.models import Max
from django.utils.translation import gettext as _
from rest_framework import serializers, status

import lib.tools
from lib import segment_analytics
from lib.feature_flag.feature.services import ServiceCategoriesMigrationFlag
from lib.locks import ServiceVariantLock
from lib.serializers import IdAndOrderSerializer, OrderingSerializer
from service.mixins.validation import SkipExtraValidationMixin  # pylint: disable=no-name-in-module
from service.tools import (  # pylint: disable=no-name-in-module
    ConditionalRequestMixin,
    RequestHandler,
    RequestLockMixin,
    ServiceTypeMarketingServiceCategoriesHandlerMixin,
    json_request,
    session,
)
from webapps.business.business_visibility.exceptions import (
    BusinessVisibilityError,
    InvitesError,
    LastAvailableServiceError,
)
from webapps.business.business_visibility.validations import InvitesValidation
from webapps.business.events import service_changed_event
from webapps.business.models import (
    Resource,
    Service,
    ServiceCategory,
    ServiceVariant,
    ServiceVariantPayment,
)
from webapps.business.serializers import (
    ServiceCategorySerializer,
    ServiceSerializer,
    ServiceSerializerWriter,
)
from webapps.business.serializers.serializers import ServicePriceSerializer
from webapps.business.services.validations import LastAvailableServiceValidation
from webapps.feeds.enums import EventType
from webapps.feeds.utils import update_to_external_partners
from webapps.onboarding_space.public import onboarding_space_service, OnboardingSpaceMetricsEnum
from webapps.photo.models import ServicePhoto
from webapps.public_partners.models import ServiceMetadata
from webapps.user.models import User


class ServiceValidateMixin:
    def validate_resource_ids(self):
        if 'resource_id' not in self.request.arguments:
            return []
        resource_ids = self.request.arguments.get('resource_id', [])

        self.quick_assert(
            all(rid.isdigit() for rid in resource_ids),
            ('required', 'validation', 'resource_id'),
            _('Resource ID must be an integer.'),
        )

        return resource_ids


class GetServicesRequestSerializer(serializers.Serializer):
    resource_id = serializers.ListField(child=serializers.IntegerField(), required=False)
    with_combos = serializers.BooleanField(required=False)
    combos_only = serializers.BooleanField(required=False)


class ServiceCategoriesHandler(
    ConditionalRequestMixin,
    ServiceValidateMixin,
    ServiceTypeMarketingServiceCategoriesHandlerMixin,
    RequestHandler,
):
    # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING,)
    __tornado_migration_feature_flag__ = ServiceCategoriesMigrationFlag
    __tornado_migration_allowed_methods__ = ['GET']

    @staticmethod
    def get_last_modified(business_id) -> Optional[datetime.datetime]:
        querysets = [
            ServiceCategory.all_objects.filter(business_id=business_id),
            Service.all_objects.filter(business_id=business_id),
            ServiceVariant.all_objects.filter(service__business_id=business_id),
            ServicePhoto.all_objects.filter(service__business_id=business_id),
            Resource.all_objects.filter(business_id=business_id),
        ]
        last_modified_queryset = reduce(
            lambda a, b: a.union(b),
            [
                queryset.annotate(
                    last_modified=Max('updated'),
                ).values_list('last_modified', flat=True)
                for queryset in querysets
            ],
        )
        return max(filter(None, last_modified_queryset), default=None)

    @session(login_required=True)
    def get(self, business_id):
        """
        swagger:
            summary: Return Service Categories
            type: ServiceCategoriesWrapper
            required:
                - business_id
            parameters:
                - name: business_id
                  description: Business id
                  type: integer
                  paramType: path
                - name: resource_id
                  description: Filter by Resource id
                  paramType: query
                  type: integer
                - name: with_combos
                  description: return all services, including service combos
                  paramType: query
                  type: integer
                - name: combos_only
                  description: return only combos
                  paramType: query
                  type: integer
                - name: If-Modified-Since
                  type: string
                  paramType: header
                  description: >
                    HTTP date format: 'Wdy, DD Mon YYYY HH:MM:SS GMT'
                    Example: 'Mon, 15 Nov 2021 13:37:55 GMT'
                    See: RFC7231 Section *******.
                    https://datatracker.ietf.org/doc/html/rfc7231#section-*******
        """
        business = self.business_with_staffer(business_id, __check_region=False)
        self.check_if_not_modified(self.get_last_modified(business_id))

        data = self._prepare_get_arguments(list_values=['resource_id'])
        serializer = GetServicesRequestSerializer(data=data)
        validated_data = self.validate_serializer(serializer)

        resource_ids = validated_data.get('resource_id')
        # filter by resource_ids sent in GET
        service_filter = {"resources__in": resource_ids} if resource_ids else {}

        if self.access_level in Resource.STAFF_ACCESS_LEVELS_LIMIT_CALENDAR:
            # filter by services handled by staff
            service_filter['service_variants__resources'] = self.user_staffer

        # apps without combo support get
        if validated_data.get('combos_only'):
            service_filter['combo_type__isnull'] = False
        elif not validated_data.get('with_combos'):
            service_filter['combo_type__isnull'] = True

        if not self.user_staffer:
            ##### LAZY CREATION OF RESOURCE FROM OWNER #17051 #####
            try:
                business.create_resource_from_owner()
            except IntegrityError as exc:
                # sync problem - someone already did this concurrently
                self._debug("IntegrityError -> create_resource_from_owner", _exception=exc)

        service_categories = ServiceCategory.objects.filter(
            business=business_id,
        ).order_by('order')
        services = (
            Service.objects.order_by('order')
            .filter(business=business_id, active=True, **service_filter)
            .prefetch_related(*ServiceSerializer.get_prefetches())
            .with_partner_app_data(ServiceMetadata)
            .annotate_no_show_protection_payments()
            .annotate_is_traveling_service()
            .annotate_combo_parents_count()
            .select_related('business')
            .with_treatment()
        )
        services_by_id = {service.id: service for service in services}
        service_variants_by_id = {
            service_variant.id: service_variant
            for service in services
            for service_variant in service.active_variants
        }

        # to escape additional queries for already loaded objects in serializer...
        def set_related_objects(service, _services_by_id):
            service.business = business
            for variant in service.active_variants:
                variant.service = service
                for through in variant.combo_children_through.all():
                    if through.child_id not in service_variants_by_id:
                        continue
                    through.child = service_variants_by_id[through.child_id]

        services_by_category_id = defaultdict(list)
        for service in services:
            set_related_objects(service, services_by_id)
            services_by_category_id[service.service_category_id].append(service)

        for category in service_categories:
            category.business = business
            category.active_services = services_by_category_id[category.id]
            for service in category.active_services:
                set_related_objects(service, services_by_id)

        # NoShowProtectionServiceField._get_service_payment_obj() was fetching ServiceVariantPayment
        # instances for each Service in a loop. We prefetch SVP payments for Services before to
        # avoid that.
        services_ids = [service.id for service in services_by_category_id[None]] + [
            service.id for category in service_categories for service in category.active_services
        ]
        payments_qs = (
            ServiceVariantPayment.objects.filter(
                service_variant__service_id__in=services_ids,
                service_variant__deleted__isnull=True,
                service_variant__active=True,
                service_variant__price__isnull=False,
            )
            .select_related('service_variant')
            .distinct('service_variant__service_id')
        )
        service_payments_map = {
            payment.service_variant.service_id: payment for payment in payments_qs
        }

        uncategorized_services_data = ServiceSerializer(
            instance=services_by_category_id[None],
            many=True,
            context={
                'default_questions': (
                    business.primary_category and business.primary_category.questions
                ),
                'prefetched_service_payments': service_payments_map,
            },
        ).data

        service_categories_data = ServiceCategorySerializer(
            many=True,
            instance=service_categories,
            context={
                'default_questions': (
                    business.primary_category and business.primary_category.questions
                ),
                'prefetched_service_payments': service_payments_map,
            },
        ).data

        ret = {
            'service_categories':
            # uncategorized services go to default service
            [
                {
                    'name': _('Not categorised'),
                    'show_first': ServiceCategory.SHOW_FIRST_ALL,
                    'services': uncategorized_services_data,
                }
            ]
            +
            # categorized services follow
            service_categories_data,
            'service_type_marketing': self.get_service_type_marketing_data(business_id=business_id),
        }

        self.finish_with_json(status.HTTP_200_OK, ret)

    @json_request
    @session(login_required=True)
    def post(self, business_id):
        """
        swagger:
            summary: Create ServiceCategory
            type: ServiceCategoryWrapper
            parameters:
                - name: business_id
                  description: Business id
                  type: integer
                  paramType: path
                - name: body
                  description:
                  paramType: body
                  type: ServiceCategory

        """
        business = self.business_with_manager(
            business_id,
            __only='id',
            __check_region=False,
        )

        self.quick_assert(
            self.data.get('name'),
            ('required', 'validation', 'name'),
            _('Field name is required.'),
        )

        self.quick_assert(
            len(self.data['name']) <= 50,
            ('required', 'validation', 'name'),
            _('Name too long, at most 50 characters are allowed.'),
        )

        order = self.data.get('order')
        if order is None or order == '':
            self.data['order'] = (
                ServiceCategory.objects.filter(business_id=business).aggregate(Max('order'))[
                    'order__max'
                ]
                or 0
            ) + 1

        service_category = ServiceCategory.objects.create(
            business=business,
            name=self.data['name'],
            order=self.data['order'],
            show_first=self.data.get('show_first', ServiceCategory.SHOW_FIRST_DEFAULT_VALUE),
        )

        ret = {
            "service_category": ServiceCategorySerializer(
                instance=service_category,
                context={
                    'single_category': business.is_single_category,
                    'default_questions': (
                        service_category.business.primary_category
                        and service_category.business.primary_category.questions
                    ),
                },
            ).data,
        }
        self.finish_with_json(status.HTTP_201_CREATED, ret)

    @json_request
    @session(login_required=True)
    def put(self, business_id):
        """Move (change order) of ServiceCategories

        swagger:
            summary: Move (change order) of ServiceCategories
            parameters:
              - name: business_id
                description: Business id
                type: integer
                paramType: path
              - name: body
                description:
                paramType: body
                type: Ordering
              - name: use_objects
                description:
                    should request and response be an object rather than a list
                paramType: query
                defaultValue: True
                required: False
                type: boolean
            type: Ordering

        """
        business = self.business_with_manager(business_id, __check_region=False)

        use_objects = self._check_flag('use_objects', default=False)
        if use_objects:
            serializer = OrderingSerializer(data=self.data)
        else:
            serializer = IdAndOrderSerializer(data=self.data, many=True)
        validated_data = self.validate_serializer(serializer)
        ordering = validated_data['ordering'] if use_objects else validated_data

        for service_category in ordering:
            business.service_categories.filter(
                id=service_category['id'],
            ).update(order=service_category['order'], updated=lib.tools.tznow())
        business.bump_document()

        self.finish_with_json(status.HTTP_200_OK, validated_data)


class ServiceCategoryHandler(RequestHandler):
    @session(login_required=True)
    def get(self, business_id, service_category_id):
        """
        swagger:
            summary: Get ServiceCategory Details
            type: ServiceCategoryWrapper
            parameters:
                - name: business_id
                  description: Business id
                  type: integer
                  paramType: path
                - name: service_category_id
                  description: Business service category id
                  type: integer
                  paramType: path

        """
        self.business_with_manager(business_id, __check_region=False)

        service_category = self.get_object_or_404(
            ServiceCategory, business=business_id, id=service_category_id
        )

        business = service_category.business

        ret = {
            "service_category": ServiceCategorySerializer(
                instance=service_category,
                context={
                    'single_category': business.is_single_category,
                    'default_questions': (
                        business.primary_category and business.primary_category.questions
                    ),
                },
            ).data,
        }
        self.finish_with_json(status.HTTP_200_OK, ret)

    @json_request
    @session(login_required=True)
    def put(self, business_id, service_category_id):
        """
        swagger:
            summary: Update ServiceCategory
            type: ServiceCategoryWrapper
            parameters:
                - name: business_id
                  description: Business id
                  type: integer
                  paramType: path
                - name: service_category_id
                  description: Business service category id
                  type: integer
                  paramType: path
                - name: body
                  description:
                  paramType: body
                  type: ServiceCategory

        """
        self.business_with_manager(business_id, __check_region=False)

        service_category = self.get_object_or_404(
            ServiceCategory, business=business_id, id=service_category_id
        )

        if 'name' in self.data:
            self.quick_assert(
                self.data.get('name'),
                ('required', 'validation', 'name'),
                _('Field name is required.'),
            )

        self.quick_assert(
            len(self.data.get('name', '')) <= 50,
            ('required', 'validation', 'name'),
            _('Name too long, at most 50 characters are allowed.'),
        )

        modified = False
        for name, value in list(self.data.items()):
            if name in ('name', 'order', 'show_first'):
                setattr(service_category, name, value)
                modified = True

        if modified:
            service_category.save()
        business = service_category.business

        ret = {
            "service_category": ServiceCategorySerializer(
                instance=service_category,
                context={
                    'single_category': business.is_single_category,
                    'default_questions': (
                        business.primary_category and business.primary_category.questions
                    ),
                },
            ).data,
        }
        self.finish_with_json(status.HTTP_200_OK, ret)

    @session(login_required=True)
    def delete(self, business_id, service_category_id):
        """
        swagger:
            summary: Delete ServiceCategory
            parameters:
                - name: business_id
                  description: Business id
                  type: integer
                  paramType: path
                - name: service_category_id
                  description: Business service category id
                  type: integer
                  paramType: path

        """
        self.business_with_manager(business_id, __check_region=False)

        service_category = self.get_object_or_404(
            ServiceCategory, business=business_id, id=service_category_id
        )

        try:
            with transaction.atomic():
                order = (
                    Service.objects.filter(
                        business_id=business_id, service_category__isnull=True
                    ).aggregate(Max('order'))['order__max']
                    or 0
                )
                for service in service_category.services.order_by().all():
                    order += 1
                    service.service_category = None
                    service.order = order
                    service.save()
                service_category.soft_delete()
        except IntegrityError:
            self.set_status(status.HTTP_500_INTERNAL_SERVER_ERROR)
        else:
            self.set_status(status.HTTP_200_OK)

        self.finish({})


class ServiceRequestSerializer(serializers.Serializer):
    skip_combo_validation = serializers.BooleanField(required=False)


class UseServiceTypeSerializer(serializers.Serializer):
    use_service_type = serializers.BooleanField(required=False, default=False)


class ServiceHandlerPutRequestSerializer(ServiceRequestSerializer, UseServiceTypeSerializer):
    pass


class SuperUserMixin:

    def get_superuser_or_404(self) -> User:
        if self.user and self.session and self.session.get('superuser'):
            superuser_name = self.session.get('superuser')
            if not superuser_name.endswith('@booksy.com'):
                # in auth is stored as email, after fully migrate to booksy-auth remove this "if"
                superuser_name = f'{superuser_name}@booksy.com'
            return self.get_object_or_404(User, email=superuser_name)


class ServicesHandler(
    RequestHandler,
    SuperUserMixin,
):
    # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.PROVIDER_CONVERSION,)

    @session(login_required=True)
    def get(self, business_id):
        """
        swagger:
            summary: Get List of Services. DEPRECATED. Use Service Categories
            parameters:
                - name: business_id
                  description: Business id
                  type: integer
                  paramType: path
                - name: resource_id
                  description: Filter by Resource id
                  paramType: query
                  type: integer
            type: ServicesWrapper

        """
        business = self.business_with_staffer(
            business_id,
            __only='id',
            __check_region=False,
        )
        data = self._prepare_get_arguments(list_values=['resource_id'])
        serializer = GetServicesRequestSerializer(data=data)
        validated_data = self.validate_serializer(serializer)

        resource_ids = validated_data.get('resource_id')
        service_filter = {"resources__in": resource_ids} if resource_ids else {}

        services = (
            Service.objects.filter(
                business=business,
                active=True,
                **service_filter,
            )
            .prefetch_related(*ServiceSerializer.get_prefetches())
            .with_partner_app_data(ServiceMetadata)
            .annotate_no_show_protection_payments()
            .with_treatment()
        )

        ret = {
            'services': ServiceSerializer(
                instance=services,
                many=True,
                context={
                    'single_category': business.is_single_category,
                    'default_questions': (
                        business.primary_category and business.primary_category.questions
                    ),
                },
            ).data,
        }
        self.finish_with_json(status.HTTP_200_OK, ret)

    @json_request
    @session(login_required=True)
    def post(self, business_id):
        """
        swagger:
            summary: Create Service
            parameters:
                - name: business_id
                  description: Business id
                  type: integer
                  paramType: path
                - name: use_service_type
                  description: >
                    If 1, the fields 'treatment' and 'is_treatment_selected_by_user' are required
                  type: boolean
                  paramType: query
                  defaultValue: 0
                  required: False
                - name: body
                  description:
                  paramType: body
                  type: Service
            type: ServiceWrapper

        """
        business = self.business_with_manager(
            business_id,
            __only='id',
            __check_region=False,
        )

        data = self._prepare_get_arguments()
        serializer = UseServiceTypeSerializer(data=data)
        validated_data = self.validate_serializer(serializer)

        self.data['business'] = business_id
        self.data['active'] = True
        self.data['order'] = self.data.get(
            'order',
            (
                Service.objects.filter(business=business, service_category__isnull=True).aggregate(
                    Max('order')
                )['order__max']
                or 0
            )
            + 1,
        )
        self.user = User.objects.get(id=self.user.id)
        superuser = self.get_superuser_or_404()

        service_serializer = ServiceSerializerWriter(
            data=self.data,
            context={
                'user': self.user,
                'superuser': superuser,
                'pos': business.pos,
                'pos_pay_by_app_enabled': business.pos_pay_by_app_enabled,
                'business': business,
                'use_service_type': validated_data['use_service_type'],
            },
        )
        self.validate_serializer(service_serializer)
        service = service_serializer.save()

        # <editor-fold desc="early_finish section">
        segment_api = segment_analytics.get_segment_api(
            business=business,
            source=self.booking_source,
        )
        segment_api.service_created(service.name)
        # </editor-fold>

        onboarding_space_service.increment_metric(
            business_id=business.id, metric=OnboardingSpaceMetricsEnum.SERVICES
        )

        ret = {
            'service': ServiceSerializer(
                instance=service,
                context={
                    'single_category': business.is_single_category,
                    'default_questions': (
                        business.primary_category and business.primary_category.questions
                    ),
                },
            ).data,
        }
        self.finish_with_json(status.HTTP_201_CREATED, ret)

    @json_request
    @session(login_required=True)
    def put(self, business_id):
        """
        swagger:
            summary: Move (change order and category) of Services
            parameters:
              - name: business_id
                description: Business id
                type: integer
                paramType: path
              - name: body
                description:
                paramType: body
                type: ServicesOrdering
              - name: use_objects
                description:
                    should request and response be an object rather than a list
                paramType: query
                defaultValue: True
                required: False
                type: boolean
            type: ServicesOrdering
        """
        business = self.business_with_manager(business_id, __check_region=False)

        class ServiceOrderSerializer(IdAndOrderSerializer):
            service_category_id = serializers.IntegerField(required=False, allow_null=True)

        class ServicesOrderingSerializer(serializers.Serializer):
            ordering = ServiceOrderSerializer(required=True, many=True)

        use_objects = self._check_flag('use_objects', default=False)
        if use_objects:
            serializer = ServicesOrderingSerializer(data=self.data)
        else:
            serializer = ServiceOrderSerializer(data=self.data, many=True)
        validated_data = self.validate_serializer(serializer)
        ordering = validated_data['ordering'] if use_objects else validated_data

        for service in ordering:
            business.services.filter(
                id=service['id'],
            ).update(
                order=service['order'],
                service_category_id=service.get('service_category_id'),
                updated=lib.tools.tznow(),
            )
        business.bump_document()

        self.finish_with_json(status.HTTP_200_OK, validated_data)


class ServiceHandler(
    RequestHandler,
    SkipExtraValidationMixin,
    RequestLockMixin,
    SuperUserMixin,
):
    # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.PROVIDER_CONVERSION,)

    @session(login_required=True)
    def get(self, business_id, service_id):
        """
        swagger:
            summary: Get Service Details
            type: ServiceWrapper
            parameters:
                - name: business_id
                  description: Business id
                  type: integer
                  paramType: path
                - name: service_id
                  description: Business Service id
                  type: integer
                  paramType: path

        """
        business = self.business_with_manager(
            business_id,
            __only='id',
            __check_region=False,
        )

        service = Service.objects.filter(id=service_id, business=business).with_treatment().first()
        self.quick_assert_404(service)

        ret = {
            'service': ServiceSerializer(
                instance=service,
                context={
                    'single_category': business.is_single_category,
                    'default_questions': (
                        business.primary_category and business.primary_category.questions
                    ),
                },
            ).data,
        }
        self.finish_with_json(status.HTTP_200_OK, ret)

    @json_request
    @session(login_required=True)
    def put(self, business_id, service_id):
        """
        swagger:
            summary: Update Service
            type: ServiceWrapper
            parameters:
                - name: business_id
                  description: Business id
                  type: integer
                  paramType: path
                - name: service_id
                  description: Business Service id
                  type: integer
                  paramType: path
                - name: skip_combo_validation
                  description: >
                    Remove combo services associated with service variants of this service
                  type: string
                  paramType: query
                - name: use_service_type
                  description: >
                    If 1, the fields 'treatment' and 'is_treatment_selected_by_user' are required
                  type: boolean
                  paramType: query
                  defaultValue: 0
                  required: False
                - name: body
                  paramType: body
                  type: Service
                - name: skip_extra_validation
                  description: Whether to skip extra validation if true
                  type: boolean
                  paramType: query

        """
        business = self.business_with_manager(business_id, __check_region=False)

        data = self._prepare_get_arguments()
        serializer = ServiceHandlerPutRequestSerializer(data=data)
        validated_data = self.validate_serializer(serializer)

        self.data['business'] = business_id
        self.data['order'] = self.data.get('order', 0)
        self.data['active'] = True

        instance = self.get_object_or_404(
            Service,
            business_id=business_id,
            id=service_id,
        )

        self.data['id'] = service_id
        self.data['name'] = self.data.get('name', instance.name)
        superuser = self.get_superuser_or_404()

        service_serializer = ServiceSerializerWriter(
            data=self.data,
            instance=instance,
            context={
                'user': self.user,
                'superuser': superuser,
                'pos': business.pos,
                'pos_pay_by_app_enabled': business.pos_pay_by_app_enabled,
                'business': business,
                'skip_combo_validation': validated_data.get('skip_combo_validation'),
                'use_service_type': validated_data['use_service_type'],
            },
        )

        validated_data = self.validate_serializer(service_serializer)

        self._acquire_lock(
            lock_id=business_id,
            lock_class=ServiceVariantLock,
            error_description=_('Could not perform action'),
        )

        go_offline = False

        if self.skip_extra_validation is not None and business.visible:
            service_visibility = validated_data.get('is_available_for_customer_booking')

            if service_visibility is False:

                errors: List[BusinessVisibilityError] = []
                try:
                    LastAvailableServiceValidation(instance).validate()
                except LastAvailableServiceError as last_available_serv_err:
                    try:
                        # Invites validation is important only if we are disabling
                        # last available service
                        InvitesValidation(business).validate()
                    except InvitesError as invites_err:
                        if not self.skip_extra_validation:
                            errors.append(invites_err)

                    if not self.skip_extra_validation:
                        errors.append(last_available_serv_err)
                    else:
                        go_offline = True

                if errors:
                    return self.return_error([er.asdict() for er in errors])

        # save changes and switch business visibility if no available services
        with transaction.atomic():
            instance = service_serializer.save()

            if go_offline:
                business.go_offline()

        update_to_external_partners(
            EventType.SERVICE,
            business=business,
            service=instance,
            ids_to_create=[],
            ids_to_update=list(instance.active_variants.values_list('id', flat=True)),
            ids_to_delete=[],
        )

        # <editor-fold desc="early_finish section">
        service_changed_event.send(instance)
        # </editor-fold>

        ret = {
            'service': ServiceSerializer(
                instance=instance,
                context={
                    'single_category': business.is_single_category,
                    'default_questions': (
                        business.primary_category and business.primary_category.questions
                    ),
                },
            ).data
        }
        self.finish_with_json(status.HTTP_200_OK, ret)

    @session(login_required=True)
    def delete(self, business_id, service_id):
        """
        swagger:
            summary: Delete Service
            parameters:
                - name: business_id
                  description: Business id
                  type: integer
                  paramType: path
                - name: service_id
                  description: Business Service id
                  type: integer
                  paramType: path
                - name: skip_combo_validation
                  description: Remove combo services associated with this service
                  type: string
                  paramType: query
                - name: skip_extra_validation
                  description: Whether to skip extra validation if true
                  type: boolean
                  paramType: query
        """
        business = self.business_with_manager(business_id, __check_region=False)

        data = self._prepare_get_arguments()
        serializer = ServiceRequestSerializer(data=data)
        validated_data = self.validate_serializer(serializer)

        instance = self.get_object_or_404(
            Service,
            business_id=business_id,
            id=service_id,
        )

        combo_services = instance.get_related_combo_services().only('id')
        if combo_services and not validated_data.get('skip_combo_validation'):
            ret_dict = {
                'errors': [
                    {
                        'code': 'existing_dependent_combo',
                        'description': _('This service is assigned to {} combo services').format(
                            len(combo_services)
                        ),
                    },
                ],
            }
            return self.finish_with_json(status.HTTP_400_BAD_REQUEST, ret_dict)

        go_offline = False
        if self.skip_extra_validation is not None and business.visible:
            # validations related to business visibility

            errors: List[BusinessVisibilityError] = []
            try:
                LastAvailableServiceValidation(instance).validate()
            except LastAvailableServiceError as last_available_serv_err:
                try:
                    # Invites validation is important only if we are disabling
                    # last available service
                    InvitesValidation(business).validate()
                except InvitesError as invites_err:
                    if not self.skip_extra_validation:
                        errors.append(invites_err)

                if not self.skip_extra_validation:
                    errors.append(last_available_serv_err)
                else:
                    go_offline = True

            if errors:
                return self.return_error([er.asdict() for er in errors])

        update_to_external_partners(
            EventType.SERVICE,
            business=business,
            service=instance,
            ids_to_create=[],
            ids_to_update=[],
            ids_to_delete=list(instance.active_variants.values_list('id', flat=True)),
        )

        # <editor-fold desc="early_finish section">
        segment_api = segment_analytics.get_segment_api(business)
        segment_api.service_deleted(instance)
        # </editor-fold>

        deleted_services_ids = set()

        for combo_service in combo_services:
            combo_service.safe_delete()
            if combo_service.deleted:
                deleted_services_ids.add(combo_service.id)

        with transaction.atomic():
            instance.safe_delete()

            if go_offline:
                business.go_offline()

        if instance.deleted:
            deleted_services_ids.add(instance.id)
        onboarding_space_service.decrement_metric(
            business_id=business.id,
            metric=OnboardingSpaceMetricsEnum.SERVICES,
            count=len(deleted_services_ids),
        )
        self.finish_with_json(status.HTTP_200_OK, {})


class PriceHandler(RequestHandler):
    def calculate_price(self, data):
        service_price_serializer = ServicePriceSerializer(data=data)
        self.validate_serializer(service_price_serializer)

        return service_price_serializer.calculate_final_price(
            service_price_serializer.validated_data
        )


class ServicesPriceHandler(PriceHandler):  # pylint: disable=too-many-ancestors
    @json_request
    def post(self):
        """
        swagger:
            summary: Get price of services, addons
            parameters:
                - name: body
                  description: Service, Addons prices
                  paramType: body
                  type: ServicePriceData
        """
        result = self.calculate_price(self.data)
        ret = {
            "price": result.value,
            "type": result.price_type,
            "service_price": result.price_only,
        }
        self.finish_with_json(status.HTTP_200_OK, ret)


class BusinessServicesPriceHandler(PriceHandler):  # pylint: disable=too-many-ancestors
    @json_request
    def post(self, *args):
        """
        swagger:
            summary: Get price of services, addons
            parameters:
                - name: body
                  description: Service, Addons prices
                  paramType: body
                  type: ServicePriceData
        """
        result = self.calculate_price(self.data)
        ret = {
            "price": result.value,
            "type": result.price_type,
            "service_price": str(result),
        }
        self.finish_with_json(status.HTTP_200_OK, ret)
