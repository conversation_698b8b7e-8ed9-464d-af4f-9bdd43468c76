import os

from django.utils.translation import gettext_lazy as _
from google.api_core.exceptions import PermissionDenied
from google.cloud import recaptchaenterprise_v1
from rest_framework.permissions import BasePermission

from lib.datadog.tools import is_datadog_synthetics_test
from lib.feature_flag.feature.security import ReCaptchaMinScoreFlag
from lib.tools import quick_assert, sget
from service.mixins.throttling import THROTTLE_WHITELIST
from webapps import consts


class GoogleRecaptchaValidator:
    def __init__(self):
        self.client = recaptchaenterprise_v1.RecaptchaEnterpriseServiceClient()
        project_id = os.environ.get('GOOGLE_PROJECT_ID')
        self.project_name = f'projects/{project_id}'
        self.recaptcha_valid = None
        self.recaptcha_score = None

    def validate(self, site_key: str, token: str):
        if not site_key or not token:
            self.recaptcha_valid = False
            self.recaptcha_score = -1
            return False

        event = recaptchaenterprise_v1.Event()
        event.site_key = site_key
        event.token = token
        assessment = recaptchaenterprise_v1.Assessment()
        assessment.event = event
        request = recaptchaenterprise_v1.CreateAssessmentRequest()
        request.assessment = assessment
        request.parent = self.project_name

        try:
            response = self.client.create_assessment(request)
        except PermissionDenied:
            self.recaptcha_valid = False
            self.recaptcha_score = -1
            return False

        self.recaptcha_valid = sget(response, ['token_properties', 'valid'], fallback=False)
        self.recaptcha_score = sget(response, ['risk_analysis', 'score'], fallback=0)
        return self.recaptcha_valid and self.recaptcha_score > ReCaptchaMinScoreFlag()


class IsRecaptchaValidated(BasePermission):
    def has_permission(self, request, view):
        action = view.action
        booking_source_name = view.booking_source.name

        if (recaptcha_settings := view.RECAPTCHA_SETTINGS) and action in recaptcha_settings.keys():
            recaptcha_action_settings = recaptcha_settings[action]

            if recaptcha_action_settings['feature_flag']():
                RecaptchaRequestValidator(
                    booking_source_name=booking_source_name,
                    request=request,
                ).validate()

        return True


class RecaptchaRequestValidator:
    SERVICE_NAME = 'ReCaptcha'

    def __init__(self, booking_source_name, request):
        self.request = request
        self.headers = request.headers
        self.booking_source_name = booking_source_name
        self.user_agent = self.headers.get('User-Agent').lower()
        self.recaptcha_token = self.headers.get('x-recaptcha-token')
        self.recaptcha_site_key = self.headers.get('x-recaptcha-site-key')
        self.recaptcha_valid = None
        self.recaptcha_score = None

    def is_office_ip(self):
        return self.headers.get('X-Real-IP') in THROTTLE_WHITELIST

    def validate(self):
        if (
            self.booking_source_name in (consts.WEB_OAUTH, consts.PERFORMANCE_TEST)
            or self.is_office_ip()
            or is_datadog_synthetics_test(self.headers)
            or 'huawei' in self.user_agent
        ):
            return

        google_captcha_validator = GoogleRecaptchaValidator()
        captcha_verified = google_captcha_validator.validate(
            token=self.recaptcha_token, site_key=self.recaptcha_site_key
        )
        self.recaptcha_score = google_captcha_validator.recaptcha_score
        self.recaptcha_valid = google_captcha_validator.recaptcha_valid

        quick_assert(
            captcha_verified,
            ('invalid', 'validation', None),
            _("Request blocked by antibot protection."),
        )
