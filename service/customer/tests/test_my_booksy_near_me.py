# -*- coding: utf-8 -*-
import datetime as dt

import pytest

from lib.elasticsearch.consts import ESIndex, K<PERSON>OMETER, MILE
from service.customer.my_booksy import MyBooksyTopRated
from webapps.business.searchables.serializers import (
    BusinessDocument,
)
from webapps.images.elasticsearch import ImageDocument
from webapps.images.enums import ImageTypeEnum

TEST_DATETIME = dt.datetime(2019, 4, 17, 9)
WARSAW_COORDINATE = dict(lat=52.232989, lon=20.990810)


@pytest.fixture(scope='module')
def make_business(clean_index_module_fixture):
    index = clean_index_module_fixture(ESIndex.BUSINESS)

    business_id = 1
    BusinessDocument(
        id=business_id,
        name='Salon in center',
        visible=True,
        join='business',
        business_location=dict(
            coordinate=WARSAW_COORDINATE,
        ),
        female_weight=0,
        primary_female_weight=0,
        is_b_listing=False,
        meta=dict(
            id=f'business:{business_id}',
            routing=f'{business_id}',
        ),
    ).save()
    ImageDocument(
        image_id=business_id,
        business_id=business_id,
        category=ImageTypeEnum.BIZ_PHOTO,
        is_cover_photo=True,
        join=dict(name='image', parent='business:{}'.format(business_id)),
        meta=dict(
            routing=f'{business_id}',
        ),
    ).save()

    business_id = 11
    BusinessDocument(
        id=business_id,
        name='In radius salon',
        visible=True,
        join='business',
        business_location=dict(
            coordinate=dict(lat=52.234615, lon=20.993346),
        ),
        female_weight=0,
        primary_female_weight=0,
        is_b_listing=False,
        meta=dict(
            id=f'business:{business_id}',
            routing=f'{business_id}',
        ),
    ).save()
    ImageDocument(
        image_id=business_id,
        business_id=business_id,
        category=ImageTypeEnum.BIZ_PHOTO,
        is_cover_photo=True,
        join=dict(name='image', parent='business:{}'.format(business_id)),
        meta=dict(
            routing=f'{business_id}',
        ),
    ).save()

    business_id = 12
    BusinessDocument(
        id=business_id,
        name='In radius female salon',
        visible=True,
        join='business',
        business_location=dict(
            coordinate=dict(lat=52.234615, lon=20.993346),
        ),
        female_weight=100,
        primary_female_weight=100,
        is_b_listing=False,
        meta=dict(
            id=f'business:{business_id}',
            routing=f'{business_id}',
        ),
    ).save()
    ImageDocument(
        image_id=business_id,
        business_id=business_id,
        category=ImageTypeEnum.BIZ_PHOTO,
        is_cover_photo=True,
        join=dict(name='image', parent='business:{}'.format(business_id)),
        meta=dict(
            routing=f'{business_id}',
        ),
    ).save()

    business_id = 2
    BusinessDocument(
        id=business_id,
        name='Further in radius salon',
        visible=True,
        join='business',
        business_location=dict(
            coordinate=dict(lat=52.234948, lon=20.996361),
        ),
        female_weight=0,
        primary_female_weight=0,
        is_b_listing=False,
        meta=dict(
            id=f'business:{business_id}',
            routing=f'{business_id}',
        ),
    ).save()
    ImageDocument(
        image_id=business_id,
        business_id=business_id,
        category=ImageTypeEnum.BIZ_PHOTO,
        is_cover_photo=True,
        join=dict(name='image', parent='business:{}'.format(business_id)),
        meta=dict(
            routing=f'{business_id}',
        ),
    ).save()

    business_id = 3
    BusinessDocument(
        id=business_id,
        name='In US radius salon',
        visible=True,
        join='business',
        business_location=dict(
            coordinate=dict(lat=52.224356, lon=20.997646),
        ),
        regions=[dict(id=1)],
        female_weight=0,
        primary_female_weight=0,
        is_b_listing=False,
        meta=dict(
            id=f'business:{business_id}',
            routing=f'{business_id}',
        ),
    ).save()
    ImageDocument(
        image_id=business_id,
        business_id=business_id,
        category=ImageTypeEnum.BIZ_PHOTO,
        is_cover_photo=True,
        join=dict(name='image', parent='business:{}'.format(business_id)),
        meta=dict(
            routing=f'{business_id}',
        ),
    ).save()

    business_id = 4
    BusinessDocument(
        id=business_id,
        name='Place beyond the limits of knowledge',
        visible=True,
        join='business',
        business_location=dict(
            coordinate=dict(lat=52.225345, lon=21.028159),
        ),
        female_weight=0,
        primary_female_weight=0,
        is_b_listing=False,
        meta=dict(
            id=f'business:{business_id}',
            routing=f'{business_id}',
        ),
    ).save()
    ImageDocument(
        image_id=business_id,
        business_id=business_id,
        category=ImageTypeEnum.BIZ_PHOTO,
        is_cover_photo=True,
        join=dict(name='image', parent='business:{}'.format(business_id)),
        meta=dict(
            routing=f'{business_id}',
        ),
    ).save()
    index.refresh()


def test_near_me__missing_geo_location():
    result = MyBooksyTopRated.get_near_me_businesses(None, None, [])
    assert result is None


def test_near_me__no_businesses_in_location():
    result = MyBooksyTopRated.get_near_me_businesses(dict(lat=0, lon=0), None, [])
    assert result is None


@pytest.mark.freeze_time(TEST_DATETIME)
@pytest.mark.usefixtures('make_business')
def test_near_me__result_with_not_enough_businesses():
    result = MyBooksyTopRated.get_near_me_businesses(WARSAW_COORDINATE, None, None, 1)
    assert result is None


@pytest.mark.parametrize(
    (
        'gender',
        'featured_businesses_ids',
        'expected',
    ),
    (
        pytest.param(
            None,
            None,
            [1, 11, 12, 2],
            id='Happy path',
        ),
        pytest.param(
            None,
            [1],
            [11, 12, 2],
            id='With business exclusion',
        ),
        pytest.param(
            'M',
            None,
            [1, 11, 2],
            id='Gender specific query',
        ),
    ),
)
@pytest.mark.freeze_time(TEST_DATETIME)
@pytest.mark.usefixtures('make_business')
def test_near_me__valid_request(gender, featured_businesses_ids, expected):
    result = MyBooksyTopRated.get_near_me_businesses(
        WARSAW_COORDINATE, gender, featured_businesses_ids, KILOMETER
    )

    result_businesses_ids = [elem['id'] for elem in result.get('places', [{}])]
    assert result_businesses_ids == expected


@pytest.mark.freeze_time(TEST_DATETIME)
@pytest.mark.usefixtures('make_business')
def test_near_me__us_valid_request():
    result = MyBooksyTopRated.get_near_me_businesses(WARSAW_COORDINATE, None, None, MILE)
    result_businesses_ids = [elem['id'] for elem in result.get('places', [])]
    assert result_businesses_ids == [1, 11, 12, 2, 3]
