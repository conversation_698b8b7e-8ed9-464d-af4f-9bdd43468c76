import datetime
import uuid
from decimal import Decimal

import pytest
from model_bakery import baker
from rest_framework import status

from service.tests import (
    BaseAsyncHTTPTest,
    dict_assert,
)
from service.warehouse.tests.common import CommodityTestsMixin
from webapps.business.models import Resource
from webapps.ecommerce.services.order import create_stock_order
from webapps.pos.models import TaxRate
from webapps.warehouse.models import (
    Barcode,
    BaseWarehouseDocument,
    Commodity,
    CommodityStockLevel,
    StocktakingDocument,
    StocktakingDocumentRow,
    Supplier,
    Supply,
    SupplyRow,
    VolumeMeasure,
    Warehouse,
    WarehouseDocument,
    WarehouseDocumentRow,
    WarehouseDocumentType,
)


@pytest.mark.django_db
class GenericDocumentsListHandlerTestCase(CommodityTestsMixin, BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{business_id}/warehouse/documents'

    def setUp(self):
        super().setUp()
        self.warehouse = baker.make(Warehouse, business=self.business)
        self.warehouse_2 = baker.make(Warehouse, business=self.business)
        self.staffer = baker.make(
            Resource,
            business=self.business,
            type=Resource.STAFF,
        )
        self.commodity = baker.make(
            Commodity,
            business=self.business,
            total_pack_capacity=10,
        )
        baker.make(
            CommodityStockLevel,
            commodity=self.commodity,
            warehouse=self.warehouse,
            remaining_volume=10,
        )
        self.commodity_2 = baker.make(
            Commodity,
            business=self.business,
            total_pack_capacity=10,
        )
        self.supplier = baker.make(Supplier, business=self.business)
        self.supplier_2 = baker.make(Supplier, business=self.business)

        self.supply = baker.make(
            Supply,
            business=self.business,
            type=WarehouseDocumentType.PZ,
            issuing_staffer=self.staffer,
            issue_date=datetime.date(2019, 8, 2),
            supplier_name='Some supplier',
            manually_assigned_number='TEST/2019',
        )
        baker.make(
            SupplyRow,
            supply=self.supply,
            commodity=self.commodity,
            commodity_name='AAA',
            warehouse=self.warehouse,
            quantity=1,
        )

        self.document = baker.make(
            WarehouseDocument,
            warehouse=self.warehouse,
            type=WarehouseDocumentType.MM,
            issue_date=datetime.date(2019, 8, 3),
            issuing_staffer=self.staffer,
            warehouse_to=self.warehouse_2,
        )
        baker.make(
            WarehouseDocumentRow,
            document=self.document,
            commodity=self.commodity,
            commodity_name='ABB',
            quantity=2,
            is_full_package_expenditure=False,
        )

    def _create_stocktaking_document(self):
        self.stocktaking = self.create_inw_document(
            self.warehouse,
            self.staffer,
        )
        baker.make(
            StocktakingDocumentRow,
            stocktaking=self.stocktaking,
            commodity=self.commodity,
            commodity_name='ABB',
            initial_volume=10,
            quantity_inventoried=8,
            is_full_package_inventoried=False,
        )

    def tearDown(self):
        Supply.objects.filter(order__isnull=False).delete()
        BaseWarehouseDocument.objects.all().delete()
        self.staffer.refresh_from_db()
        super().tearDown()

    def test_get(self):
        self._create_stocktaking_document()
        url = self.url.format(business_id=self.business.id)
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert resp.json['documents_count'] == 3

        # Documents are sorted with: -issued_date, -id
        # Document created later should be first
        first_doc = resp.json['documents'][0]
        second_doc = resp.json['documents'][1]
        third_doc = resp.json['documents'][2]

        # Check document-specific fields
        assert first_doc.get('type') == WarehouseDocumentType.MM
        assert first_doc.get('warehouse_to') == self.document.warehouse_to.id
        assert first_doc.get('number')  # auto generated doc number

        assert second_doc.get('type') == WarehouseDocumentType.PZ
        assert second_doc.get('supplier_name') == 'Some supplier'
        assert second_doc.get('number') == self.supply.manually_assigned_number

        assert third_doc.get('type') == WarehouseDocumentType.INW
        assert third_doc.get('number')  # auto generated doc number

        stock_level = CommodityStockLevel.objects.filter(
            commodity=self.commodity,
            warehouse=self.warehouse,
        ).first()
        assert '_document' in stock_level.history_change[-1]
        _document = stock_level.history_change[-1]['_document']
        assert _document['document_type'] == 'MM'

    def test_get_without_hidden_documents(self):
        document_wz = baker.make(
            WarehouseDocument,
            warehouse=self.warehouse,
            type=WarehouseDocumentType.WZ,
            issue_date=datetime.date(2019, 8, 3),
            issuing_staffer=self.staffer,
            warehouse_to=self.warehouse_2,
        )
        document_wz_hidden = baker.make(
            WarehouseDocument,
            warehouse=self.warehouse,
            type=WarehouseDocumentType.WZ,
            issue_date=datetime.date(2019, 8, 3),
            issuing_staffer=self.staffer,
            warehouse_to=self.warehouse_2,
            hidden=True,
        )

        url = self.url.format(business_id=self.business.id)
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert resp.json['documents_count'] == 3
        documents_ids = [x['id'] for x in resp.json['documents']]
        assert document_wz.id in documents_ids
        assert self.supply.id in documents_ids
        assert self.document.id in documents_ids
        assert document_wz_hidden.id not in documents_ids

    def test_get_search(self):
        self._create_stocktaking_document()
        url = self.url.format(business_id=self.business.id)
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert resp.json['documents_count'] == 3

        # Search by document number
        resp = self.fetch(
            url + f'?query={self.supply.manually_assigned_number}',
            method='GET',
        )
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert resp.json['documents_count'] == 1
        assert resp.json['documents'][0]['id'] == self.supply.id

        # Search by commodity name
        resp = self.fetch(url + '?query=ABB', method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert resp.json['documents_count'] == 2
        assert resp.json['documents'][0]['id'] == self.document.id
        assert resp.json['documents'][1]['id'] == self.stocktaking.id

        resp = self.fetch(url + '?query=yyy', method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert resp.json['documents_count'] == 0

        # Search with filtering
        resp = self.fetch(url + '?query=AAA&type=PZ', method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert resp.json['documents_count'] == 1
        assert resp.json['documents'][0]['id'] == self.supply.id

    def test_get_with_filtering(self):  # pylint: disable=too-many-statements
        self._create_stocktaking_document()
        url = self.url.format(business_id=self.business.id)
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert resp.json['documents_count'] == 3

        # Filtering by type
        resp = self.fetch(url + '?type=PZ', method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert resp.json['documents_count'] == 1
        assert resp.json['documents'][0]['id'] == self.supply.id

        resp = self.fetch(url + '?type=MM', method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert resp.json['documents_count'] == 1
        assert resp.json['documents'][0]['id'] == self.document.id

        resp = self.fetch(url + '?type=INW', method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert resp.json['documents_count'] == 1
        assert resp.json['documents'][0]['id'] == self.stocktaking.id

        resp = self.fetch(url + '?type=RW', method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert resp.json['documents_count'] == 0

        # Filtering by issue date
        resp = self.fetch(url + '?issue_date__lte=2019-08-03', method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert resp.json['documents_count'] == 3

        resp = self.fetch(url + '?issue_date__lte=2019-08-02', method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert resp.json['documents_count'] == 2

        resp = self.fetch(url + '?issue_date__lte=2019-08-01', method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert resp.json['documents_count'] == 1

        resp = self.fetch(url + '?issue_date__lte=2019-07-31', method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert resp.json['documents_count'] == 0

        # Filtering by issue date
        resp = self.fetch(url + '?issue_date__gte=2019-08-04', method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert resp.json['documents_count'] == 0

        resp = self.fetch(url + '?issue_date__gte=2019-08-03', method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert resp.json['documents_count'] == 1

        resp = self.fetch(url + '?issue_date__gte=2019-08-02', method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert resp.json['documents_count'] == 2

        resp = self.fetch(url + '?issue_date__gte=2019-08-01', method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert resp.json['documents_count'] == 3

    def test_post_supply(self):
        order = baker.make(
            Supply,
            business=self.business,
            type=WarehouseDocumentType.ORD,
            issuing_staffer=self.staffer,
            issue_date=datetime.date(2019, 8, 1),
        )

        valid_supply_data = {
            "type": "PZ",
            "supplier": self.supplier.id,
            "issuing_staffer": self.staffer.id,
            "numbers_from_suppliers": "",
            "order": order.id,
            'note': 'post_supply',
            "rows": [
                {
                    "commodity": self.commodity.id,
                    "warehouse": self.warehouse.id,
                    "quantity": 1,
                    "unit_price": 0,
                },
                {
                    "commodity": self.commodity_2.id,
                    "warehouse": self.warehouse.id,
                    "quantity": 1,
                    "unit_price": 0,
                },
            ],
        }
        url = self.url.format(business_id=self.business.id)
        resp = self.fetch(url, method='POST', body=valid_supply_data)
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json == {}
        supply = Supply.objects.filter(note='post_supply').first()
        assert supply.supplier == self.supplier
        assert supply.rows.count() == 2

    def test_post_ecommerce_supply(self):
        order = baker.make(
            Supply,
            business=self.business,
            type=WarehouseDocumentType.ORD,
            issuing_staffer=self.staffer,
            issue_date=datetime.date(2019, 8, 1),
        )
        tax_rate = baker.make(TaxRate, rate="23.00")
        self.commodity.ecommerce_catalog_product_id = 123
        self.commodity.tax_rate = tax_rate
        self.commodity.gross_price = "123.00"
        self.commodity.net_price = "100.00"
        self.commodity.save()

        valid_supply_data = {
            "type": "PZ",
            "supplier": self.supplier.id,
            "issuing_staffer": self.staffer.id,
            "numbers_from_suppliers": "",
            "order": order.id,
            'note': 'post_supply',
            "rows": [
                {
                    "commodity": self.commodity.id,
                    "warehouse": self.warehouse.id,
                    "quantity": 1,
                    "unit_price": 0,
                },
                {
                    "commodity": self.commodity_2.id,
                    "warehouse": self.warehouse.id,
                    "quantity": 1,
                    "unit_price": 0,
                },
            ],
        }
        url = self.url.format(business_id=self.business.id)
        resp = self.fetch(url, method='POST', body=valid_supply_data)
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json == {}
        supply = Supply.objects.filter(note='post_supply').first()
        assert supply.supplier == self.supplier
        assert supply.rows.count() == 2
        for row in supply.rows.all():
            if row.commodity.ecommerce_catalog_product_id:
                assert row.tax == Decimal("23.00")
                assert row.tax_rate == Decimal("23.00")
            else:
                assert row.tax == Decimal("0.00")
                assert row.tax_rate == Decimal("0.00")

    def test_post_supply_with_negative_value(self):
        order = baker.make(
            Supply,
            business=self.business,
            type=WarehouseDocumentType.ORD,
            issuing_staffer=self.staffer,
            issue_date=datetime.date(2019, 8, 1),
        )

        valid_supply_data = {
            "type": "PZ",
            "supplier": self.supplier.id,
            "issuing_staffer": self.staffer.id,
            "numbers_from_suppliers": "",
            "order": order.id,
            "rows": [
                {
                    "commodity": self.commodity.id,
                    "warehouse": self.warehouse.id,
                    "quantity": -10,
                    "unit_price": 0,
                },
                {
                    "commodity": self.commodity_2.id,
                    "warehouse": self.warehouse.id,
                    "quantity": 0,
                    "unit_price": 0,
                },
            ],
        }
        url = self.url.format(business_id=self.business.id)
        resp = self.fetch(url, method='POST', body=valid_supply_data)
        assert resp.code == status.HTTP_400_BAD_REQUEST
        assert resp.json
        dict_assert(
            resp.json,
            {
                'errors': [
                    {
                        'field': 'rows.0.quantity',
                        'description': 'Ensure this value is greater than or equal to 1.',
                        'code': 'min_value',
                    },
                    {
                        'field': 'rows.1.quantity',
                        'description': 'Ensure this value is greater than or equal to 1.',
                        'code': 'min_value',
                    },
                ],
            },
        )

    def test_post_order_with_email(self):
        from django.core import mail

        supplier = baker.make(Supplier, business=self.business, email='')

        valid_supply_data = {
            "type": "ORD",
            "supplier": supplier.id,
            "issuing_staffer": self.staffer.id,
            "numbers_from_suppliers": "",
            "send_email_to_supplier": True,
            "rows": [
                {
                    "commodity": self.commodity.id,
                    "warehouse": self.warehouse.id,
                    "quantity": 1,
                    "unit_price": 0,
                },
                {
                    "commodity": self.commodity_2.id,
                    "warehouse": self.warehouse.id,
                    "quantity": 2,
                    "unit_price": 0,
                },
            ],
        }
        url = self.url.format(business_id=self.business.id)

        # Supplier has no email -> email should not be sent
        resp = self.fetch(url, method='POST', body=valid_supply_data)
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json == {}
        assert len(mail.outbox) == 0

        # send_email_to_supplier flag is not present -> email should not be sent
        supplier.email = '<EMAIL>'
        supplier.save()
        data = valid_supply_data.copy()
        del data['send_email_to_supplier']
        resp = self.fetch(url, method='POST', body=data)
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json == {}
        assert len(mail.outbox) == 0

        # Supplier has email and send_email_to_supplier is set to True
        resp = self.fetch(url, method='POST', body=valid_supply_data)
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json == {}
        assert len(mail.outbox) == 1
        assert supplier.email in mail.outbox[0].to
        assert mail.outbox[0].attachments

    def test_post_order_without_email_commodity_supplier_add(self):
        supplier = baker.make(Supplier, business=self.business, email='')
        some_commodity = baker.make(
            Commodity,
            business=self.business,
            total_pack_capacity=10,
        )
        some_commodity.save()

        valid_supply_data = {
            "type": "ORD",
            "supplier": supplier.id,
            "issuing_staffer": self.staffer.id,
            "numbers_from_suppliers": "",
            "send_email_to_supplier": False,
            "rows": [
                {
                    "commodity": some_commodity.id,
                    "warehouse": self.warehouse.id,
                    "quantity": 1,
                    "unit_price": 0,
                },
            ],
        }
        assert some_commodity.suppliers.all().count() == 0
        url = self.url.format(business_id=self.business.id)
        self.fetch(url, method='POST', body=valid_supply_data)
        assert some_commodity.suppliers.all().count() == 1
        some_commodity.refresh_from_db()
        dict_assert(
            some_commodity.history_change[1]['change']['suppliers'],
            {
                'old_value': [],
                'new_value': [supplier.id],
            },
        )

    def test_post_supply_to_stocktaked_warehouse(self):
        warehouse_3 = baker.make(Warehouse, business=self.business, name='Store')
        order = baker.make(
            Supply,
            business=self.business,
            type=WarehouseDocumentType.ORD,
            issuing_staffer=self.staffer,
            issue_date=datetime.date(2019, 8, 1),
        )
        baker.make(
            StocktakingDocument,
            warehouse=warehouse_3,
            type=WarehouseDocumentType.INW,
            issue_date=datetime.date(2019, 1, 1),
            issuing_staffer=self.staffer,
        )

        valid_supply_data = {
            "type": "PZ",
            "supplier": self.supplier.id,
            "issuing_staffer": self.staffer.id,
            "numbers_from_suppliers": "",
            "order": order.id,
            "rows": [
                {
                    "commodity": self.commodity.id,
                    "warehouse": warehouse_3.id,
                    "quantity": 1,
                    "unit_price": 0,
                },
                {
                    "commodity": self.commodity_2.id,
                    "warehouse": warehouse_3.id,
                    "quantity": 1,
                    "unit_price": 0,
                },
            ],
        }
        url = self.url.format(business_id=self.business.id)
        resp = self.fetch(url, method='POST', body=valid_supply_data)
        assert resp.code == 400
        dict_assert(
            resp.json['errors'][0],
            {
                'code': 'not allowed',
                'description': 'Warehouse Store is during stocktaking',
            },
        )
        assert resp.json['errors']

    def test_post_warehouse_document(self):
        valid_warehouse_data = {
            "type": "MM",
            "issue_date": "2019-07-11",
            "issuing_staffer": self.staffer.id,
            "confirming_staffer": self.staffer.id,
            "warehouse": self.warehouse.id,
            "transaction": None,
            "booking": None,
            "note": "Some note post MM",
            "warehouse_to": self.warehouse_2.id,
            "rows": [
                {
                    "commodity_name": "Some commodity",
                    "commodity": self.commodity.id,
                    "quantity": "1.00",
                    "net_price": "10.12",
                    "gross_price": "12.45",
                    "tax": "5.78",
                    "is_full_package_expenditure": False,
                }
            ],
        }

        url = self.url.format(business_id=self.business.id)
        resp = self.fetch(url, method='POST', body=valid_warehouse_data)
        assert resp.code == status.HTTP_201_CREATED, resp
        assert resp.json == {}
        document = WarehouseDocument.objects.get(
            note="Some note post MM",
        )
        assert document.number
        all_rows = document.rows.all()
        assert all_rows.count() == 1
        assert all_rows[0].commodity == self.commodity

        stock_level = CommodityStockLevel.objects.filter(
            commodity=self.commodity,
            warehouse=self.warehouse,
        ).first()
        assert '_document' in stock_level.history_change[-1]
        _document = stock_level.history_change[-1]['_document']
        assert _document['document_type'] == 'MM'

    def test_post_warehouse_document_with_negative_value(self):
        valid_warehouse_data = {
            "type": "MM",
            "issue_date": "2019-07-11",
            "issuing_staffer": self.staffer.id,
            "confirming_staffer": self.staffer.id,
            "warehouse": self.warehouse.id,
            "transaction": None,
            "booking": None,
            "note": "Some note",
            "warehouse_to": self.warehouse_2.id,
            "rows": [
                {
                    "commodity_name": "Some commodity",
                    "commodity": self.commodity.id,
                    "quantity": "-30.53",
                    "net_price": "-10.12",
                    "gross_price": "-12.45",
                    "tax": "-5.78",
                    "is_full_package_expenditure": False,
                }
            ],
        }

        url = self.url.format(business_id=self.business.id)
        resp = self.fetch(url, method='POST', body=valid_warehouse_data)
        assert resp.code == status.HTTP_400_BAD_REQUEST, resp
        dict_assert(
            resp.json,
            {
                'errors': [
                    {
                        'field': 'rows.0.quantity',
                        'description': 'Ensure this value is greater than or equal to 1.',
                        'code': 'min_value',
                    },
                    {
                        'field': 'rows.0.net_price',
                        'description': 'Ensure this value is greater than or equal to 0.00.',
                        'code': 'min_value',
                    },
                    {
                        'field': 'rows.0.gross_price',
                        'description': 'Ensure this value is greater than or equal to 0.00.',
                        'code': 'min_value',
                    },
                    {
                        'field': 'rows.0.tax',
                        'description': 'Ensure this value is greater than or equal to 0.00.',
                        'code': 'min_value',
                    },
                ]
            },
        )

    def test_post_warehouse_document_stocktaked_warehouses(self):
        warehouse_3 = baker.make(Warehouse, business=self.business, name='Store')
        baker.make(
            CommodityStockLevel,
            commodity=self.commodity,
            warehouse=warehouse_3,
            remaining_volume=10,
        )
        baker.make(
            StocktakingDocument,
            warehouse=warehouse_3,
            type=WarehouseDocumentType.INW,
            issue_date=datetime.date(2019, 1, 1),
            issuing_staffer=self.staffer,
        )
        valid_warehouse_data = {
            "type": "MM",
            "issue_date": "2019-07-11",
            "issuing_staffer": self.staffer.id,
            "confirming_staffer": self.staffer.id,
            "warehouse": self.warehouse.id,
            "transaction": None,
            "booking": None,
            "note": "Some note",
            "warehouse_to": warehouse_3.id,
            "rows": [
                {
                    "commodity_name": "Some commodity",
                    "commodity": self.commodity.id,
                    "quantity": "1.00",
                    "net_price": "10.123",
                    "gross_price": "12.456",
                    "tax": "5.789",
                    "is_full_package_expenditure": False,
                }
            ],
        }

        url = self.url.format(business_id=self.business.id)
        resp = self.fetch(url, method='POST', body=valid_warehouse_data)
        assert resp.code == 400
        dict_assert(
            resp.json['errors'][0],
            {
                'code': 'invalid',
                'field': 'warehouse_to',
            },
        )

        valid_warehouse_data['warehouse'] = warehouse_3.id
        valid_warehouse_data['warehouse_to'] = self.warehouse.id

        url = self.url.format(business_id=self.business.id)
        resp = self.fetch(url, method='POST', body=valid_warehouse_data)
        assert resp.code == 400
        dict_assert(
            resp.json['errors'][0],
            {
                'code': 'invalid',
                'field': 'warehouse',
            },
        )

    def test_post_stocktaking_document(self):
        valid_stocktaking_data = {
            "type": "INW",
            "issue_date": "2019-06-13",
            "issuing_staffer": self.staffer.id,
            "confirming_staffer": self.staffer.id,
            "warehouse": self.warehouse.id,
            "note": "Stocktaking with empty response",
            "rows": [
                {
                    "commodity": self.commodity.id,
                    "quantity_inventoried": "13.00",
                    "is_full_package_inventoried": False,
                    "description": "Some description",
                },
                {
                    "commodity": self.commodity_2.id,
                    "quantity_inventoried": "10.00",
                    "is_full_package_inventoried": False,
                    "description": "No existing stock level",
                },
            ],
        }

        url = self.url.format(business_id=self.business.id)
        resp = self.fetch(url, method='POST', body=valid_stocktaking_data)
        assert resp.code == status.HTTP_201_CREATED, resp
        assert resp.json == {}
        stocktaking_document = StocktakingDocument.objects.filter(
            note='Stocktaking with empty response',
            warehouse=self.warehouse,
            type='INW',
        ).first()
        assert stocktaking_document
        rows = stocktaking_document.rows.all()
        assert rows.filter(
            commodity=self.commodity,
            quantity_inventoried=Decimal("13.00"),
        ).exists()
        assert rows.filter(
            commodity=self.commodity_2,
            quantity_inventoried=Decimal("10.00"),
        ).exists()

    def test_post_stocktaking_document_with_negative_value(self):
        valid_stocktaking_data = {
            "type": "INW",
            "issue_date": "2019-06-13",
            "issuing_staffer": self.staffer.id,
            "confirming_staffer": self.staffer.id,
            "warehouse": self.warehouse.id,
            "note": "Another note",
            "rows": [
                {
                    "commodity": self.commodity.id,
                    "quantity_inventoried": "-13.00",
                    "is_full_package_inventoried": False,
                    "description": "Some description",
                }
            ],
        }

        url = self.url.format(business_id=self.business.id)
        resp = self.fetch(url, method='POST', body=valid_stocktaking_data)
        assert resp.code == status.HTTP_400_BAD_REQUEST, resp
        dict_assert(
            resp.json,
            {
                'errors': [
                    {
                        'field': 'rows.0.quantity_inventoried',
                        'description': 'Ensure this value is greater than or equal to 0.',
                        'code': 'min_value',
                    }
                ],
            },
        )

    def test_post_stocktaking_document_stocktaked_warehouse(self):
        warehouse_3 = baker.make(Warehouse, business=self.business, name='Store')
        baker.make(
            StocktakingDocument,
            warehouse=warehouse_3,
            type=WarehouseDocumentType.INW,
            issue_date=datetime.date(2019, 1, 1),
            issuing_staffer=self.staffer,
        )
        valid_stocktaking_data = {
            "type": "INW",
            "issue_date": "2019-06-13",
            "issuing_staffer": self.staffer.id,
            "confirming_staffer": self.staffer.id,
            "warehouse": warehouse_3.id,
            "note": "Another note",
            "rows": [
                {
                    "commodity": self.commodity.id,
                    "quantity_inventoried": "13.00",
                    "is_full_package_inventoried": False,
                    "description": "Some description",
                }
            ],
        }

        url = self.url.format(business_id=self.business.id)
        resp = self.fetch(url, method='POST', body=valid_stocktaking_data)
        assert resp.code == 400
        dict_assert(
            resp.json['errors'][0],
            {
                'code': 'not allowed',
                'description': 'Warehouse is already during stocktaking',
            },
        )


@pytest.mark.django_db
class GenericDocumentsHandlerTests(
    CommodityTestsMixin, BaseAsyncHTTPTest
):  # pylint: disable=too-many-instance-attributes
    url = '/business_api/me/businesses/{business_id}/warehouse/documents/{document_id}'  # pylint: disable=line-too-long

    def setUp(self):
        super().setUp()
        self.warehouse = baker.make(Warehouse, business=self.business)
        warehouse_2 = baker.make(Warehouse, business=self.business)
        self.staffer = baker.make(
            Resource,
            business=self.business,
            type=Resource.STAFF,
        )
        self.volume_unit = baker.make(
            VolumeMeasure,
            label='VolumeLabel',
            symbol='VL',
        )
        self.barcode_0 = baker.make(
            Barcode,
            business=self.business,
            type=Barcode.CODE128,
            code='some_barcode',
        )
        self.barcode_1 = baker.make(
            Barcode,
            business=self.business,
            type=Barcode.CODE128,
            code='other_barcode',
        )
        self.commodity = baker.make(
            Commodity,
            business=self.business,
            total_pack_capacity=10,
            volume_unit=self.volume_unit,
            barcodes=[self.barcode_0, self.barcode_1],
            net_price=Decimal('11.11'),
            gross_price=Decimal('12.12'),
        )
        self.commodity_2 = baker.make(
            Commodity,
            business=self.business,
            total_pack_capacity=10,
        )
        self.commodity_3 = baker.make(
            Commodity,
            business=self.business,
            total_pack_capacity=10,
        )
        baker.make(
            CommodityStockLevel,
            commodity=self.commodity,
            warehouse=self.warehouse,
            remaining_volume=10,
        )
        self.supplier = baker.make(
            Supplier,
            business=self.business,
            email='<EMAIL>',
        )
        baker.make(
            Supplier,
            business=self.business,
        )
        self.order = baker.make(
            Supply,
            business=self.business,
            type=WarehouseDocumentType.ORD,
            issuing_staffer=self.staffer,
        )
        self.order_row = baker.make(
            SupplyRow,
            supply=self.order,
            commodity=self.commodity,
            commodity_name='AAA',
            warehouse=self.warehouse,
            quantity=1,
        )
        self.supply = baker.make(
            Supply,
            business=self.business,
            type=WarehouseDocumentType.PZ,
            supplier=self.supplier,
            issuing_staffer=self.staffer,
            issue_date=datetime.date(2019, 8, 2),
            manually_assigned_number='TEST/2019',
            billing_address='Billing address',
            shipping_address='Shipping address',
        )
        baker.make(
            SupplyRow,
            supply=self.supply,
            commodity=self.commodity,
            commodity_name='AAA',
            warehouse=self.warehouse,
            quantity=1,
            net_price=Decimal('7.70'),
            gross_price=Decimal('10.00'),
            tax=Decimal('2.30'),
            tax_rate=Decimal('23.00'),
        )

        self.document = baker.make(
            WarehouseDocument,
            warehouse=self.warehouse,
            type=WarehouseDocumentType.MM,
            issue_date=datetime.date(2019, 8, 3),
            issuing_staffer=self.staffer,
            warehouse_to=warehouse_2,
        )
        baker.make(
            WarehouseDocumentRow,
            document=self.document,
            commodity=self.commodity,
            commodity_name='ABB',
            quantity=2,
            is_full_package_expenditure=False,
        )

    def _create_stocktaking_documents(self):
        self.stocktaking1 = baker.make(
            StocktakingDocument,
            warehouse=self.warehouse,
            type=WarehouseDocumentType.INW,
            issue_date=datetime.date(2019, 1, 1),
            issuing_staffer=self.staffer,
            acceptance_date=datetime.date(2019, 1, 3),
        )
        baker.make(
            StocktakingDocumentRow,
            stocktaking=self.stocktaking1,
            commodity=self.commodity,
            commodity_name='ABB',
            initial_volume=10,
            quantity_inventoried=30,
            is_full_package_inventoried=True,
            description='test description',
        )
        self.stocktaking2 = self.create_inw_document(
            self.warehouse,
            self.staffer,
        )
        baker.make(
            StocktakingDocumentRow,
            stocktaking=self.stocktaking2,
            commodity=self.commodity,
            commodity_name='ABB',
            initial_volume=10,
            quantity_inventoried=8,
            is_full_package_inventoried=False,
        )

    def tearDown(self):
        BaseWarehouseDocument.objects.all().delete()
        self.staffer.refresh_from_db()
        super().tearDown()

    def test_get(self):
        url = self.url.format(
            business_id=self.business.id,
            document_id=self.document.id,
        )
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert resp.json['type'] == WarehouseDocumentType.MM
        assert resp.json['warehouse_to'] == self.document.warehouse_to.id

        url = self.url.format(
            business_id=self.business.id,
            document_id=self.supply.id,
        )
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert resp.json['type'] == WarehouseDocumentType.PZ
        assert resp.json['supplier'] == self.supplier.id
        assert resp.json['billing_address'] == 'Billing address'
        assert resp.json['shipping_address'] == 'Shipping address'
        assert resp.json['supplier_email'] == '<EMAIL>'

        stock_level = CommodityStockLevel.objects.filter(
            commodity=self.commodity,
            warehouse=self.warehouse,
        ).first()
        assert '_document' in stock_level.history_change[-1]
        _document = stock_level.history_change[-1]['_document']
        assert _document['document_type'] == 'MM'

        self._create_stocktaking_documents()
        url = self.url.format(
            business_id=self.business.id,
            document_id=self.stocktaking1.id,
        )
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json
        assert resp.json['type'] == WarehouseDocumentType.INW
        assert resp.json['acceptance_date'] == '2019-01-03'

        stock_level = CommodityStockLevel.objects.filter(
            commodity=self.commodity,
            warehouse=self.warehouse,
        ).first()
        assert '_document' in stock_level.history_change[-1]
        _document = stock_level.history_change[-1]['_document']
        assert _document['document_type'] == 'INW'

    def test_ecommerce_order_document(self):
        order_data = {
            "supplier_order_id": "supplier-order-id-123",
            "billing_address": "Billing Address",
            "shipping_address": "Shipping Address",
            "created_at": "2024-04-22T00:00:00Z",
            "user_id": self.user.id,
        }
        order_document = create_stock_order(
            self.business, self.supplier, str(uuid.uuid4()), order_data
        )
        url = self.url.format(
            business_id=self.business.id,
            document_id=order_document.id,
        )
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK
        dict_assert(
            resp.json,
            {
                'id': order_document.id,
                'type': 'ORD',
                'supplier': self.supplier.id,
                'supplier_name': self.supplier.name,
                'number': order_document.manually_assigned_number,
                'manually_assigned_number': order_document.manually_assigned_number,
                'ecommerce_order_id': str(order_document.ecommerce_order_id),
            },
        )

    def test_get_stocktaking_document(self):
        self._create_stocktaking_documents()
        url = self.url.format(
            business_id=self.business.id,
            document_id=self.stocktaking1.id,
        )
        resp = self.fetch(url, method='GET')
        assert resp.code == status.HTTP_200_OK
        dict_assert(
            resp.json,
            {
                'warehouse': self.warehouse.id,
                'acceptance_date': '2019-01-03',
                'rows_count': 1,
                'id': self.stocktaking1.id,
                'type': 'INW',
                'number': 'STR/1',
                'issue_date': '2019-01-01',
                'issuing_staffer': self.staffer.id,
                'confirming_staffer': None,
                'manually_assigned_number': None,
                'note': None,
                'rows': [
                    {
                        'id': self.stocktaking1.rows.first().id,
                        'commodity_name': 'ABB',
                        # barcodes
                        'commodity': str(self.commodity.id),
                        '_commodity': {
                            'tax_rate': None,
                            'total_pack_capacity': 10,
                        },
                        'initial_volume': '10.00',
                        'initial_full_packages': 1,
                        'initial_volume_in_last_open_package': 0.0,
                        'net_price': '11.11',
                        'gross_price': '12.12',
                        'quantity_inventoried': '30.00',
                        'unit': {
                            'id': self.volume_unit.id,
                            'label': 'VolumeLabel',
                            'symbol': 'VL',
                        },
                        'is_full_package_inventoried': True,
                        'description': 'test description',
                    }
                ],
            },
        )
        barcodes = resp.json['rows'][0]['barcodes']
        assert self.barcode_0.code in barcodes
        assert self.barcode_1.code in barcodes

    def test_delete(self):
        self._create_stocktaking_documents()
        # Deletion of order is allowed
        url = self.url.format(
            business_id=self.business.id,
            document_id=self.order.id,
        )
        resp = self.fetch(url, method='DELETE')
        assert resp.code == status.HTTP_200_OK
        self.order.refresh_from_db()
        assert self.order.deleted

        # Deletion of warehouse document is not allowed
        # if warehouse or warehouse_to are stocktaked
        url = self.url.format(
            business_id=self.business.id,
            document_id=self.document.id,
        )
        resp = self.fetch(url, method='DELETE')
        assert resp.code == status.HTTP_400_BAD_REQUEST
        self.document.refresh_from_db()
        assert not self.document.deleted

        # Deletion of not accepted stocktaking is allowed
        url = self.url.format(
            business_id=self.business.id,
            document_id=self.stocktaking2.id,
        )
        resp = self.fetch(url, method='DELETE')
        assert resp.code == status.HTTP_200_OK
        self.stocktaking2.refresh_from_db()
        assert self.stocktaking2.deleted

        # Deletion of warehouse document is allowed
        # if warehouse or warehouse_to aren't stocktaked
        url = self.url.format(
            business_id=self.business.id,
            document_id=self.document.id,
        )
        resp = self.fetch(url, method='DELETE')
        assert resp.code == status.HTTP_200_OK
        self.document.refresh_from_db()
        assert self.document.deleted

        # Deletion of Supply should fail
        url = self.url.format(
            business_id=self.business.id,
            document_id=self.supply.id,
        )
        resp = self.fetch(url, method='DELETE')
        assert resp.code == status.HTTP_400_BAD_REQUEST
        self.supply.refresh_from_db()
        assert not self.supply.deleted

        # Deletion of accepted stocktaking should fail
        url = self.url.format(
            business_id=self.business.id,
            document_id=self.stocktaking1.id,
        )
        resp = self.fetch(url, method='DELETE')
        assert resp.code == status.HTTP_400_BAD_REQUEST
        self.stocktaking1.refresh_from_db()
        assert not self.stocktaking1.deleted

    def test_put_supply(self):
        order_row_2 = baker.make(
            SupplyRow,
            supply=self.order,
            commodity=self.commodity,
            commodity_name='AAA',
            warehouse=self.warehouse,
            quantity=1,
        )

        data = {
            "type": "ORD",
            "supplier": self.supplier.id,
            "issuing_staffer": self.supply.issuing_staffer.id,
            "numbers_from_suppliers": "",
            'note': 'test_put_supply',
            "rows": [
                {
                    # Update existing row 1
                    "id": self.order_row.id,
                    "commodity": self.order_row.commodity.id,
                    "quantity": 2,
                    "net_price": 15,
                    "gross_price": 16.5,
                    "tax": 1.5,
                    "warehouse": self.order_row.warehouse.id,
                },
                {
                    # Create new row 3
                    "commodity": self.commodity_2.id,
                    "quantity": 1,
                    "net_price": 50,
                    "gross_price": 51.5,
                    "tax": 1.5,
                    "warehouse": self.warehouse.id,
                },
                # Existing row 2 will be deleted
            ],
        }
        url = self.url.format(
            business_id=self.business.id,
            document_id=self.order.id,
        )
        resp = self.fetch(url, method='PUT', body=data)
        assert resp.code == status.HTTP_200_OK, resp
        assert resp.json == {}

        document = Supply.objects.get(note='test_put_supply')
        assert document.id == self.order.id
        assert document.supplier == self.supplier
        assert document.rows.count() == 2

        assert self.order_row in document.rows.all()
        assert order_row_2.id not in document.rows.all()

        self.order_row.refresh_from_db()
        assert self.order_row.quantity == 2
        assert self.order_row.net_price == Decimal('15.00')
        assert self.order_row.gross_price == Decimal('16.50')
        assert self.order_row.tax == Decimal('1.50')

        row3 = document.rows.all().last()
        assert row3.quantity == 1
        assert row3.net_price == Decimal('50.00')
        assert row3.gross_price == Decimal('51.50')
        assert row3.tax == Decimal('1.50')

        # test history
        stock_level_history_url = (
            '/business_api/me/businesses/{business_id}/commodity/'
            '{commodity_id}/warehouse/{warehouse_id}/history/?'
        )

        stock_level_history_url = stock_level_history_url.format(
            business_id=self.business.id,
            commodity_id=self.commodity.id,
            warehouse_id=self.warehouse.id,
        )

        history_resp = self.fetch(stock_level_history_url)
        history_json = history_resp.json
        history_element = history_json['stock_level']['history'][0]
        assert 'Greatest' not in str(history_element['change']['remaining_volume']['new_value'])
        assert 'Greatest' not in str(history_element['change']['remaining_volume']['old_value'])

    def test_put_supply_negative_value(self):
        baker.make(
            SupplyRow,
            supply=self.order,
            commodity=self.commodity,
            commodity_name='AAA',
            warehouse=self.warehouse,
            quantity=1,
        )

        data = {
            "type": "ORD",
            "supplier": self.supplier.id,
            "issuing_staffer": self.supply.issuing_staffer.id,
            "numbers_from_suppliers": "",
            "rows": [
                {
                    # Update existing row 1
                    "id": self.order_row.id,
                    "commodity": self.order_row.commodity.id,
                    "quantity": -2,
                    "net_price": -15,
                    "gross_price": -16.5,
                    "tax": -1.5,
                    "warehouse": self.order_row.warehouse.id,
                },
                {
                    # Create new row 3
                    "commodity": self.commodity_2.id,
                    "quantity": -1,
                    "net_price": -50,
                    "gross_price": -51.5,
                    "tax": -1.5,
                    "warehouse": self.warehouse.id,
                },
                # Existing row 2 will be deleted
            ],
        }
        url = self.url.format(
            business_id=self.business.id,
            document_id=self.order.id,
        )
        resp = self.fetch(url, method='PUT', body=data)
        assert resp.code == status.HTTP_400_BAD_REQUEST, resp
        errors = resp.json['errors']
        dict_assert(
            errors,
            [
                {
                    'field': 'rows.0.quantity',
                    'description': 'Ensure this value is greater than or equal to 1.',
                    'code': 'min_value',
                },
                {
                    'field': 'rows.0.net_price',
                    'description': 'Ensure this value is greater than or equal to 0.00.',
                    'code': 'min_value',
                },
                {
                    'field': 'rows.0.gross_price',
                    'description': 'Ensure this value is greater than or equal to 0.00.',
                    'code': 'min_value',
                },
                {
                    'field': 'rows.0.tax',
                    'description': 'Ensure this value is greater than or equal to 0.00.',
                    'code': 'min_value',
                },
                {
                    'field': 'rows.1.quantity',
                    'description': 'Ensure this value is greater than or equal to 1.',
                    'code': 'min_value',
                },
                {
                    'field': 'rows.1.net_price',
                    'description': 'Ensure this value is greater than or equal to 0.00.',
                    'code': 'min_value',
                },
                {
                    'field': 'rows.1.gross_price',
                    'description': 'Ensure this value is greater than or equal to 0.00.',
                    'code': 'min_value',
                },
                {
                    'field': 'rows.1.tax',
                    'description': 'Ensure this value is greater than or equal to 0.00.',
                    'code': 'min_value',
                },
            ],
        )

    def test_put_order_with_email(self):
        from django.core import mail

        valid_supply_data = {
            "id": self.order.id,
            "type": "ORD",
            "supplier": self.supplier.id,
            "issuing_staffer": self.staffer.id,
            "numbers_from_suppliers": "",
            "rows": [
                {
                    "commodity": self.commodity.id,
                    "warehouse": self.warehouse.id,
                    "quantity": 1,
                    "unit_price": 0,
                },
            ],
        }
        url = self.url.format(
            business_id=self.business.id,
            document_id=self.order.id,
        )
        # Order does not have supply
        assert not hasattr(self.order, 'supply')

        # There is no send_email_to_supplier flag -> mail should not be sent
        resp = self.fetch(url, method='PUT', body=valid_supply_data)
        assert resp.code == status.HTTP_200_OK
        assert len(mail.outbox) == 0

        # send_email_to_supplier = False -> mail should not be sent
        valid_supply_data['send_email_to_supplier'] = False
        resp = self.fetch(url, method='PUT', body=valid_supply_data)
        assert resp.code == status.HTTP_200_OK
        assert len(mail.outbox) == 0

        # send_email_to_supplier = True -> mail should be sent
        valid_supply_data['send_email_to_supplier'] = True
        resp = self.fetch(url, method='PUT', body=valid_supply_data)
        assert resp.code == status.HTTP_200_OK
        assert len(mail.outbox) == 1
        resp = self.fetch(url, method='PUT', body=valid_supply_data)
        assert resp.code == status.HTTP_200_OK
        assert len(mail.outbox) == 2
        assert mail.outbox[0].attachments
        assert mail.outbox[1].attachments

    def test_put_warehouse_document(self):
        document = baker.make(
            WarehouseDocument,
            warehouse=self.warehouse,
            type='RW',
        )
        row_1 = baker.make(
            WarehouseDocumentRow,
            commodity=self.commodity,
            document=document,
            quantity=0,
        )
        row_2 = baker.make(
            WarehouseDocumentRow,
            commodity=self.commodity,
            document=document,
            quantity=0,
        )

        data = {
            "type": "RW",
            "issue_date": "2019-07-11",
            "issuing_staffer": self.staffer.id,
            "warehouse": self.warehouse.id,
            "transaction": None,
            "booking": None,
            "note": "test_put_warehouse_document",
            "warehouse_to": None,
            "rows": [
                {
                    # update existing row 1
                    "id": row_1.id,
                    "commodity_name": "AAA",
                    "commodity": self.commodity.id,
                    "quantity": "1.00",
                    "net_price": "10.12",
                    "gross_price": "12.45",
                    "tax": "5.78",
                    "is_full_package_expenditure": False,
                },
                {
                    # create new row 3
                    "commodity_name": "CCC",
                    "commodity": self.commodity.id,
                    "quantity": "1.00",
                    "net_price": "1.12",
                    "gross_price": "2.45",
                    "tax": "3.78",
                    "is_full_package_expenditure": False,
                },
                # existing row 2 will be deleted
            ],
        }

        url = self.url.format(
            business_id=self.business.id,
            document_id=document.id,
        )
        resp = self.fetch(url, method='PUT', body=data)
        assert resp.code == status.HTTP_200_OK
        assert resp.json == {}
        document = WarehouseDocument.objects.get(
            note='test_put_warehouse_document',
        )
        assert document.rows.all().count() == 2
        assert row_1 in document.rows.all()
        assert row_2.id not in document.rows.all(), 'Row 2 should be deleted'

        row_1.refresh_from_db()
        assert row_1.commodity_name == 'AAA'
        assert row_1.quantity == Decimal('1.00')
        assert row_1.net_price == Decimal('10.12')

        row_3 = WarehouseDocumentRow.objects.get(commodity_name='CCC')
        assert row_3.quantity == Decimal('1.00')
        assert row_3.net_price == Decimal('1.12')

        stock_level = CommodityStockLevel.objects.filter(
            commodity=self.commodity,
            warehouse=self.warehouse,
        ).first()
        last_history = stock_level.history_change[-1]
        assert last_history['_document']['document_type'] == 'RW'

        # warehouse stocktaking should block updating document
        self._create_stocktaking_documents()
        resp = self.fetch(url, method='PUT', body=data)
        assert resp.code == status.HTTP_400_BAD_REQUEST

        stock_level = CommodityStockLevel.objects.filter(
            commodity=self.commodity,
            warehouse=self.warehouse,
        ).first()
        last_history = stock_level.history_change[-1]
        assert last_history['_document']['document_type'] == 'INW'

    def test_put_warehouse_document_quantity_0(self):
        document = baker.make(
            WarehouseDocument,
            warehouse=self.warehouse,
            type='RW',
        )
        row_1 = baker.make(
            WarehouseDocumentRow,
            commodity=self.commodity,
            document=document,
            quantity=0,
        )
        baker.make(
            WarehouseDocumentRow,
            commodity=self.commodity,
            document=document,
            quantity=0,
        )

        data = {
            "type": "RW",
            "issue_date": "2019-07-11",
            "issuing_staffer": self.staffer.id,
            "warehouse": self.warehouse.id,
            "transaction": None,
            "booking": None,
            "note": "Some note",
            "warehouse_to": None,
            "rows": [
                {
                    # update existing row 1
                    "id": row_1.id,
                    "commodity_name": "AAA",
                    "commodity": self.commodity.id,
                    "quantity": "0.00",
                    "net_price": "10.12",
                    "gross_price": "12.45",
                    "tax": "5.78",
                    "is_full_package_expenditure": False,
                },
                {
                    # create new row 3
                    "commodity_name": "CCC",
                    "commodity": self.commodity.id,
                    "quantity": "0.00",
                    "net_price": "1.12",
                    "gross_price": "2.45",
                    "tax": "3.78",
                    "is_full_package_expenditure": False,
                },
                # existing row 2 will be deleted
            ],
        }

        url = self.url.format(
            business_id=self.business.id,
            document_id=document.id,
        )
        resp = self.fetch(url, method='PUT', body=data)
        assert resp.code == status.HTTP_400_BAD_REQUEST
        dict_assert(
            resp.json['errors'],
            [
                {
                    'field': 'rows.0.quantity',
                    'description': 'Ensure this value is greater than or equal to 1.',
                    'code': 'min_value',
                },
                {
                    'field': 'rows.1.quantity',
                    'description': 'Ensure this value is greater than or equal to 1.',
                    'code': 'min_value',
                },
            ],
        )

    def test_put_stocktaking_document(self):
        stocktaking = self.create_inw_document(self.warehouse, self.staffer)
        row1 = baker.make(
            StocktakingDocumentRow,
            stocktaking=stocktaking,
            commodity=self.commodity,
            commodity_name='ABB',
            initial_volume=10,
            quantity_inventoried=3,
            is_full_package_inventoried=True,
        )
        row2 = baker.make(
            StocktakingDocumentRow,
            stocktaking=stocktaking,
            commodity=self.commodity_2,
            commodity_name='BBA',
            initial_volume=30,
            quantity_inventoried=34,
            is_full_package_inventoried=False,
        )

        data = {
            "type": "INW",
            "issue_date": "2019-08-11",
            "issuing_staffer": self.staffer.id,
            "confirming_staffer": self.staffer.id,
            "warehouse": self.warehouse.id,
            "note": "Some note put stocktaking document",
            "rows": [
                {
                    # update existing row 1
                    "id": row1.id,
                    "commodity": row1.commodity.id,
                    "quantity_inventoried": "5.00",
                    "description": "Some description",
                    "is_full_package_inventoried": True,
                },
                {
                    # create new row 3
                    "commodity": self.commodity_3.id,
                    "quantity_inventoried": "15.00",
                    "is_full_package_inventoried": False,
                },
                # existing row 1 will be deleted
            ],
        }

        url = self.url.format(
            business_id=self.business.id,
            document_id=stocktaking.id,
        )
        resp = self.fetch(url, method='PUT', body=data)
        assert resp.code == status.HTTP_200_OK
        assert resp.json == {}
        document = StocktakingDocument.objects.get(
            note="Some note put stocktaking document",
        )
        assert document.rows.all().count() == 2
        assert row1 in document.rows.all()  # updated row
        assert row2 not in document.rows.all(), 'Row 2 should be deleted'

        row1.refresh_from_db()
        assert row1.quantity_inventoried == Decimal("5.00")
        assert row1.description == "Some description"
        assert row1.is_full_package_inventoried is True

        row2.refresh_from_db()
        assert row2.quantity_inventoried == Decimal('34.00')
        assert row2.is_full_package_inventoried is False

        row3 = document.rows.all().last()
        assert row3.quantity_inventoried == Decimal('15.00')
        assert row3.is_full_package_inventoried is False

    def test_accept_stocktaking_document(self):
        commodity = baker.make(
            Commodity,
            business=self.business,
            total_pack_capacity=10,
        )
        stock_level = baker.make(
            CommodityStockLevel,
            commodity=commodity,
            warehouse=self.warehouse,
            remaining_volume=10,
        )
        stocktaking = self.create_inw_document(self.warehouse, self.staffer)
        row = baker.make(
            StocktakingDocumentRow,
            stocktaking=stocktaking,
            commodity=commodity,
            commodity_name='ABB',
            initial_volume=10,
            quantity_inventoried=3,
            is_full_package_inventoried=True,
        )

        data = {
            "type": "INW",
            "issue_date": "2019-08-11",
            "issuing_staffer": self.staffer.id,
            "confirming_staffer": self.staffer.id,
            "warehouse": self.warehouse.id,
            "note": "test_accept_stocktaking_document",
            "acceptance": True,
            "rows": [
                {
                    "id": row.id,
                    "commodity": row.commodity.id,
                    "quantity_inventoried": "5.00",
                    "is_full_package_inventoried": True,
                }
            ],
        }

        url = self.url.format(
            business_id=self.business.id,
            document_id=stocktaking.id,
        )
        resp = self.fetch(url, method='PUT', body=data)
        assert resp.code == status.HTTP_200_OK
        assert resp.json == {}
        document = StocktakingDocument.objects.get(
            note='test_accept_stocktaking_document',
        )
        assert document.acceptance_date
        assert document.rows.filter(deleted__isnull=True).count() == 1

        stock_level.refresh_from_db()

        assert stock_level.remaining_volume == Decimal(50)

        resp = self.fetch(url, method='PUT', body=data)
        # Changing accepted stocktaking should fail
        assert resp.code == status.HTTP_400_BAD_REQUEST

    def test_accept_stocktaking_document_negative_value(self):
        commodity = baker.make(
            Commodity,
            business=self.business,
            total_pack_capacity=10,
        )
        baker.make(
            CommodityStockLevel,
            commodity=commodity,
            warehouse=self.warehouse,
            remaining_volume=10,
        )
        stocktaking = self.create_inw_document(self.warehouse, self.staffer)
        row = baker.make(
            StocktakingDocumentRow,
            stocktaking=stocktaking,
            commodity=commodity,
            commodity_name='ABB',
            initial_volume=10,
            quantity_inventoried=3,
            is_full_package_inventoried=True,
        )

        data = {
            "type": "INW",
            "issue_date": "2019-08-11",
            "issuing_staffer": self.staffer.id,
            "confirming_staffer": self.staffer.id,
            "warehouse": self.warehouse.id,
            "note": "Some note",
            "acceptance": True,
            "rows": [
                {
                    "id": row.id,
                    "commodity": row.commodity.id,
                    "quantity_inventoried": "-5.00",
                    "is_full_package_inventoried": True,
                }
            ],
        }

        url = self.url.format(
            business_id=self.business.id,
            document_id=stocktaking.id,
        )
        resp = self.fetch(url, method='PUT', body=data)
        assert resp.code == status.HTTP_400_BAD_REQUEST
        dict_assert(
            resp.json,
            {
                'errors': [
                    {
                        'field': 'rows.0.quantity_inventoried',
                        'description': 'Ensure this value is greater than or equal to 0.',
                        'code': 'min_value',
                    }
                ],
            },
        )
