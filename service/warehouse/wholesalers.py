from bo_obs.datadog.enums import BooksyTeams
from service.tools import (
    RequestHandler,
    session,
)
from webapps.warehouse.models import Wholesaler
from webapps.warehouse.serializers.wholesaler import (
    WholesalerDetailsSerializer,
)


class WholesalersHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True)
    def get(self, business_id):
        """Get list of Wholesalers.

        swagger:
            summary: Get list of Wholesalers
            type: WholesalersResponse
            parameters:
              - name: business_id
                description: Business id
                type: integer
                paramType: path
        :swagger
        """
        business = self.business_with_reception(business_id)
        wholesalers = Wholesaler.objects.all().order_by('name')
        serializer = WholesalerDetailsSerializer(
            instance=wholesalers,
            many=True,
            context={
                'business': business,
            },
        )
        self.finish_with_json(
            200,
            {
                'wholesalers': serializer.data,
            },
        )
