import datetime
from datetime import timed<PERSON>ta

import pytest
from dateutil.relativedelta import relativedelta
from freezegun import freeze_time
from mock.mock import patch
from model_bakery import baker
from pytz import UTC
from rest_framework import status
from segment.analytics import Client
from tornado.httpclient import HTTPResponse

import settings
from lib.deeplink import generate_deeplink
from lib.deeplink.deeplinks_ms.ports import generate_deeplink_ms
from lib.feature_flag.feature import (
    Send1stTimePaymentLinkNotificationReceivedEventFlag,
)
from lib.feature_flag.feature.payment import (
    PrepaymentsForBusinessAppointmentAutoPay,
    PrepaymentsForBusinessAppointmentEnabled,
)
from lib.test_utils import spy_mock
from lib.tests.utils import override_feature_flag
from lib.tools import tznow, id_to_external_api
from service.booking.tests import get_before_and_after
from service.booking.tests.test_create_appointment._base import (
    BaseAppointmentWithPrepaymentTestCase,
)
from service.tests import dict_assert
from webapps.booking.enums import (
    AppointmentCustomerMode as ACMode,
)
from webapps.booking.enums import (
    AppointmentStatus,
    RepeatEndType,
    RepeatType,
)
from webapps.booking.models import Appointment
from webapps.booking.tests.utils import create_appointment
from webapps.business.baker_recipes import bci_recipe, business_recipe
from webapps.business.models import (
    Resource,
    Service,
    ServiceVariant,
    Business,
)
from webapps.market_pay.models import AccountHolder
from webapps.notification.enums import ScheduleState
from webapps.notification.models import NotificationSchedule
from webapps.pos import enums
from webapps.pos.enums import receipt_status
from webapps.pos.models import (
    Receipt,
    Transaction,
    POS,
    PaymentMethod,
)
from webapps.pos.notifications import (
    NotificationWithPaymentLinkForPrepayment,
    PrepaymentPreCancelNotification,
)
from webapps.pos.provider.fake import _CARDS
from webapps.pos.tasks import autopay_for_business_prepayment_task
from webapps.segment.enums import AnalyticEventEnums
from webapps.stripe_integration.models import StripeAccount

NOT_ELIGIBLE_ERROR = {
    'field': 'with_prepayment',
    'description': 'This appointment is not eligible for a deposit. Check details and try again.',
    'code': 'invalid',
}


@pytest.mark.django_db
@override_feature_flag({PrepaymentsForBusinessAppointmentEnabled: True})
class CreateBusinessAppointmentWithPrepaymentTestCase(BaseAppointmentWithPrepaymentTestCase):
    def test_create_booking_in_future_with_will_and_adyen_account(self):
        booked_from, booked_till, variant, staffer = self._standard_setup_for_prepayment()
        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant,
            staffer=staffer,
            with_prepayment=True,
        )
        baker.make(AccountHolder, pos=self.business.pos, ever_passed_kyc=True)

        url_dry_run = self.get_path(self.business.id, dry_run=True)
        resp = self.fetch(url_dry_run, method='POST', body=body)

        assert resp.json['appointment']['prepayment_available'] is True
        assert resp.json['appointment']['prepayment_toggle_visible'] is True
        assert resp.json['appointment']['actions']['change']
        assert (
            resp.json['appointment']['subbookings'][0]['actions']
            == resp.json['appointment']['actions']
        )

        url = self.get_path(self.business.id)
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED

        assert resp.json['appointment']['prepayment_available'] is False  # because already created
        assert not resp.json['appointment']['actions']['change']
        assert (
            resp.json['appointment']['subbookings'][0]['actions']
            == resp.json['appointment']['actions']
        )
        self._standard_check(resp, booked_from, booked_till, Appointment.STATUS.PENDING_PAYMENT)
        self._check_payment_details(resp)

        appointment_id = resp.json['appointment']['appointment_uid']
        self._check_notification_scheduled(appointment_id)

    def _autopay_test_common(self, card):
        booked_from, booked_till, variant, staffer = self._standard_setup_for_prepayment()
        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant,
            staffer=staffer,
            with_prepayment=True,
        )
        baker.make(AccountHolder, pos=self.business.pos, ever_passed_kyc=True)
        baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=card,
            user=self.bci.user,
            default=True,
        )

        url = self.get_path(self.business.id)
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self._check_payment_details(resp)

        appointment_id = resp.json['appointment']['appointment_uid']
        # Notification should be scheduled for appointment.created + 3min. Checking min delay =2min
        self._check_notification_scheduled(appointment_id, min_delay=2)

        # Run autopay task
        autopay_for_business_prepayment_task.apply(
            kwargs={
                'appointment_id': appointment_id,
                'device_data': '',
            },
        )
        appointment = Appointment.objects.filter(id=appointment_id).first()
        transaction = appointment.transactions.last()
        return appointment, transaction

    @patch(
        'webapps.pos.services.PaymentRowService.'
        'map_payment_provider_code_into_payment_provider_enum'
    )
    @override_feature_flag({PrepaymentsForBusinessAppointmentAutoPay: True})
    def test_create_booking_in_future_with_will__autopay_success(self, payment_provider_mock):
        payment_provider_mock.return_value = 'fake'
        # working card
        appointment, transaction = self._autopay_test_common(_CARDS[0]['last_digits'])
        # No need to check if notification is skipped since appt status is changed to accepted.
        assert appointment.status == AppointmentStatus.ACCEPTED
        assert transaction.latest_receipt.status_code == receipt_status.PREPAYMENT_SUCCESS

    @patch(
        'webapps.pos.services.PaymentRowService.'
        'map_payment_provider_code_into_payment_provider_enum'
    )
    @override_feature_flag(
        {
            PrepaymentsForBusinessAppointmentAutoPay: True,
        }
    )
    def test_create_booking_in_future_with_will__autopay_failed(self, payment_provider_mock):
        payment_provider_mock.return_value = 'fake'
        # failing card
        appointment, transaction = self._autopay_test_common(_CARDS[10]['last_digits'])

        # Check if statuses are ok for manual retry (standard retry done by customer through app)
        assert appointment.status == AppointmentStatus.PENDING_PAYMENT
        assert transaction.latest_receipt.status_code == receipt_status.PREPAYMENT_FAILED

    def test_create_booking_with_and_without_prepayment_flag(self):
        booked_from, booked_till, variant, staffer = self._standard_setup_for_prepayment()
        baker.make(AccountHolder, pos=self.business.pos, ever_passed_kyc=True)

        # with_prepayment not in the request
        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant,
            staffer=staffer,
        )
        url_dry_run = self.get_path(self.business.id, dry_run=True)
        resp = self.fetch(url_dry_run, method='POST', body=body)
        assert resp.json['appointment']['prepayment_available'] is True
        assert resp.json['appointment']['with_prepayment'] is False  # default value

        # with_prepayment=False in the request
        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant,
            staffer=staffer,
            with_prepayment=False,
        )
        url_dry_run = self.get_path(self.business.id, dry_run=True)
        resp = self.fetch(url_dry_run, method='POST', body=body)
        assert resp.json['appointment']['prepayment_available'] is True
        assert resp.json['appointment']['with_prepayment'] is False  # value provided in request

        # with_prepayment=True in the request
        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant,
            staffer=staffer,
            with_prepayment=True,
        )
        url_dry_run = self.get_path(self.business.id, dry_run=True)
        resp = self.fetch(url_dry_run, method='POST', body=body)
        assert resp.json['appointment']['prepayment_available'] is True
        assert resp.json['appointment']['with_prepayment'] is True  # value provided in request

    def test_create_booking_with_and_without_prepayment_flag_transitions(self):
        booked_from, booked_till, variant_1, staffer_1 = self._standard_setup_for_prepayment()
        _, _, variant_2, staffer_2 = self._standard_setup()
        baker.make(AccountHolder, pos=self.business.pos, ever_passed_kyc=True)
        # 1. Service with prepayment available -->
        # 2. Service with prepayment available, with_prepayment flag set to False -->
        # 3. Service change (no prepayment available) -->
        # 4. Back to original service --> with_prepayment check

        # 1.
        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant_1,
            staffer=staffer_1,
            with_prepayment=True,
        )
        url_dry_run = self.get_path(self.business.id, dry_run=True)
        resp = self.fetch(url_dry_run, method='POST', body=body)
        assert resp.json['appointment']['prepayment_available'] is True
        assert resp.json['appointment']['with_prepayment'] is True
        # 2.
        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant_1,
            staffer=staffer_1,
            with_prepayment=False,  # change
        )
        url_dry_run = self.get_path(self.business.id, dry_run=True)
        resp = self.fetch(url_dry_run, method='POST', body=body)
        assert resp.json['appointment']['prepayment_available'] is True
        assert resp.json['appointment']['with_prepayment'] is False
        # 3.
        prev_with_prepayment = resp.json['appointment']['with_prepayment']
        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant_2,
            staffer=staffer_2,
            with_prepayment=prev_with_prepayment,
        )
        url_dry_run = self.get_path(self.business.id, dry_run=True)
        resp = self.fetch(url_dry_run, method='POST', body=body)
        assert resp.json['appointment']['prepayment_available'] is False
        assert resp.json['appointment']['with_prepayment'] is False
        # 4.
        prev_with_prepayment = resp.json['appointment']['with_prepayment']
        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant_1,
            staffer=staffer_1,
            with_prepayment=prev_with_prepayment,
        )
        url_dry_run = self.get_path(self.business.id, dry_run=True)
        resp = self.fetch(url_dry_run, method='POST', body=body)
        assert resp.json['appointment']['prepayment_available'] is True
        assert resp.json['appointment']['with_prepayment'] is False

        self._check_no_notification_created()

    @freeze_time(datetime.datetime(2022, 9, 26, 11, 0, 0))
    @override_feature_flag({Send1stTimePaymentLinkNotificationReceivedEventFlag: True})
    @patch.object(Client, 'track')
    @patch('webapps.notification.channels.send_sms')
    def test_create_booking_check_prepayment_notification_scheduled_and_sent(
        self, mocked_send_sms, analytics_track_mock
    ):
        booked_from, booked_till, variant, staffer = self._standard_setup_for_prepayment()
        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant,
            staffer=staffer,
            with_prepayment=True,
        )
        baker.make(AccountHolder, pos=self.business.pos, ever_passed_kyc=True)

        url = self.get_path(self.business.id)
        spy__generate_deeplink = spy_mock(generate_deeplink_ms)
        with patch('lib.deeplink.adapters.generate_deeplink_ms', spy__generate_deeplink):
            resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        assert mocked_send_sms.call_count == 0  # because it is scheduled in the future
        assert spy__generate_deeplink.mock.call_count == 0
        app_id = resp.json['appointment']['appointment_uid']

        # pylint: disable=line-too-long
        notification = NotificationSchedule.objects.filter(
            task_id=f'NotificationWithPaymentLinkForPrepayment,call_for_prepayment:appointment_id={app_id}',
            state=ScheduleState.PENDING,
        ).first()

        assert notification
        assert tznow() == notification.scheduled - datetime.timedelta(minutes=1)
        assert not self.bci.first_payment_link_received

        # check pre-cancel notification (1 hour before auto-cancel)
        notification_pre_cancel = NotificationSchedule.objects.filter(
            task_id=f'PrepaymentPreCancelNotification,prepayment_reminder:appointment_id={app_id}',
            state=ScheduleState.PENDING,
        ).first()

        assert notification_pre_cancel
        assert tznow() == notification_pre_cancel.scheduled - datetime.timedelta(hours=11)

        with patch('lib.deeplink.adapters.generate_deeplink_ms', spy__generate_deeplink):
            NotificationWithPaymentLinkForPrepayment(sender=None, appointment_id=app_id).send()

        self.bci.refresh_from_db()
        assert spy__generate_deeplink.mock.call_count == 2
        assert mocked_send_sms.call_count == 1
        assert self.bci.first_payment_link_received

        with patch('lib.deeplink.adapters.generate_deeplink_ms', spy__generate_deeplink):
            PrepaymentPreCancelNotification(sender=None, appointment_id=app_id).send()
        assert spy__generate_deeplink.mock.call_count == 4
        assert mocked_send_sms.call_count == 2

        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {
                'event': AnalyticEventEnums.PAYMENT_LINK_RECEIVED_FIRST_TIME.value,
                'properties': {
                    'user_id': id_to_external_api(self.bci.user_id),
                    'country': settings.API_COUNTRY,
                    'appointment_id': id_to_external_api(app_id),
                },
            },
        )

    @freeze_time(datetime.datetime(2022, 9, 26, 11, 0, 0))
    @override_feature_flag({Send1stTimePaymentLinkNotificationReceivedEventFlag: True})
    @patch.object(Client, 'track')
    def test_create_booking_and_prepayment_notification_event_already_send_before(
        self, analytics_track_mock
    ):
        booked_from, booked_till, variant, staffer = self._standard_setup_for_prepayment()
        body = self._get_standard_body(
            booked_from,
            booked_till,
            variant,
            staffer,
            with_prepayment=True,
        )
        baker.make(AccountHolder, pos=self.business.pos, ever_passed_kyc=True)
        bci_recipe.make(
            user=self.bci.user,
            business=business_recipe.make(),
            first_payment_link_received=tznow() - datetime.timedelta(days=5),
        )

        url = self.get_path(self.business.id)
        resp = self.fetch(url, method='POST', body=body)
        app_id = resp.json['appointment']['appointment_uid']
        NotificationWithPaymentLinkForPrepayment(sender=None, appointment_id=app_id).send()
        analytics_track_mock.assert_not_called()

    @freeze_time(datetime.datetime(2022, 9, 26, 11, 0, 0))
    @override_feature_flag({Send1stTimePaymentLinkNotificationReceivedEventFlag: False})
    @patch.object(Client, 'track')
    def test_create_booking_and_prepayment_notification_event_not_send_flag_off(
        self, analytics_track_mock
    ):
        booked_from, booked_till, variant, staffer = self._standard_setup_for_prepayment()
        body = self._get_standard_body(
            booked_from,
            booked_till,
            variant,
            staffer,
            with_prepayment=True,
        )
        baker.make(AccountHolder, pos=self.business.pos, ever_passed_kyc=True)
        url = self.get_path(self.business.id)
        resp = self.fetch(url, method='POST', body=body)
        app_id = resp.json['appointment']['appointment_uid']
        NotificationWithPaymentLinkForPrepayment(sender=None, appointment_id=app_id).send()
        analytics_track_mock.assert_not_called()

    def _check_prepayment_notification_scheduled_but_appt_status_changed(
        self,
        new_appt_status,
        mocked_send_sms,
        analytics_track_mock,
    ):
        booked_from, booked_till, variant, staffer = self._standard_setup_for_prepayment()
        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant,
            staffer=staffer,
            with_prepayment=True,
        )
        baker.make(AccountHolder, pos=self.business.pos, ever_passed_kyc=True)

        url = self.get_path(self.business.id)
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        app_id = resp.json['appointment']['appointment_uid']

        # pylint: disable=line-too-long
        notification = NotificationSchedule.objects.filter(
            task_id=f'NotificationWithPaymentLinkForPrepayment,call_for_prepayment:appointment_id={app_id}',
            state=ScheduleState.PENDING,
        ).first()
        assert notification
        assert tznow() == notification.scheduled - datetime.timedelta(minutes=1)

        notification_pre_cancel = NotificationSchedule.objects.filter(
            task_id=f'PrepaymentPreCancelNotification,prepayment_reminder:appointment_id={app_id}',
            state=ScheduleState.PENDING,
        ).first()

        assert notification_pre_cancel
        assert tznow() == notification_pre_cancel.scheduled - datetime.timedelta(hours=11)

        # Appointment status changed to accepted (within 11hrs from created date)
        # or cancelled (within 1min from created date). Notification should not be send.
        appointment = Appointment.objects.filter(id=app_id).first()
        appointment.status = new_appt_status
        appointment.save()

        spy__generate_deeplink = spy_mock(generate_deeplink)
        with patch('lib.deeplink.adapters.generate_deeplink', spy__generate_deeplink):
            NotificationWithPaymentLinkForPrepayment(sender=None, appointment_id=app_id).send()

        assert spy__generate_deeplink.mock.call_count == 0
        assert mocked_send_sms.call_count == 0
        analytics_track_mock.assert_not_called()

        with patch('lib.deeplink.adapters.generate_deeplink', spy__generate_deeplink):
            PrepaymentPreCancelNotification(sender=None, appointment_id=app_id).send()

        assert spy__generate_deeplink.mock.call_count == 0
        assert mocked_send_sms.call_count == 0

    @freeze_time(datetime.datetime(2022, 9, 26, 11, 0, 0))
    @patch.object(Client, 'track')
    @patch('webapps.notification.channels.send_sms')
    def test_create_booking_check_prepayment_notification_scheduled_but_cancelled_status(
        self,
        mocked_send_sms,
        analytics_track_mock,
    ):
        self._check_prepayment_notification_scheduled_but_appt_status_changed(
            AppointmentStatus.CANCELED,
            mocked_send_sms,
            analytics_track_mock,
        )

    @freeze_time(datetime.datetime(2022, 9, 26, 11, 0, 0))
    @patch.object(Client, 'track')
    @patch('webapps.notification.channels.send_sms')
    def test_create_booking_check_prepayment_notification_scheduled_but_accepted_status(
        self,
        mocked_send_sms,
        analytics_track_mock,
    ):
        self._check_prepayment_notification_scheduled_but_appt_status_changed(
            AppointmentStatus.ACCEPTED,
            mocked_send_sms,
            analytics_track_mock,
        )

    @freeze_time(datetime.datetime(2022, 9, 26, 11, 0, 0))
    @patch('webapps.notification.base.ChannelSelector.get_recipients_by_channel', return_value={})
    @patch('webapps.notification.channels.send_sms')
    def test_create_booking_check_prepayment_notification_scheduled_but_no_channel_recipients(
        self, mocked_send_sms, mocked_recipents
    ):
        booked_from, booked_till, variant, staffer = self._standard_setup_for_prepayment()
        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant,
            staffer=staffer,
            with_prepayment=True,
        )
        baker.make(AccountHolder, pos=self.business.pos, ever_passed_kyc=True)

        url = self.get_path(self.business.id)
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        app_id = resp.json['appointment']['appointment_uid']

        # pylint: disable=line-too-long
        notification = NotificationSchedule.objects.filter(
            task_id=f'NotificationWithPaymentLinkForPrepayment,call_for_prepayment:appointment_id={app_id}',
            state=ScheduleState.PENDING,
        ).first()

        assert notification
        assert tznow() == notification.scheduled - datetime.timedelta(minutes=1)

        spy__generate_deeplink = spy_mock(generate_deeplink)
        with patch('lib.deeplink.adapters.generate_deeplink', spy__generate_deeplink):
            NotificationWithPaymentLinkForPrepayment(sender=None, appointment_id=app_id).send()

        assert spy__generate_deeplink.mock.call_count == 0
        assert mocked_send_sms.call_count == 0

        notification.refresh_from_db()
        assert notification.state == ScheduleState.SKIPPED

    def test_create_booking_in_future_with_will_and_adyen_account_without_customer_user(self):
        self.bci.user = None
        self.bci.save()

        booked_from, booked_till, variant, staffer = self._standard_setup_for_prepayment()
        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant,
            staffer=staffer,
            with_prepayment=True,
        )
        baker.make(AccountHolder, pos=self.business.pos, ever_passed_kyc=True)

        url_dry_run = self.get_path(self.business.id, dry_run=True)
        resp = self.fetch(url_dry_run, method='POST', body=body)
        assert resp.code == status.HTTP_400_BAD_REQUEST
        self._check_no_notification_created()
        self._check_error_with_prepayment(resp)

    def test_create_booking_in_future_with_will_and_adyen_account_with_trusted_customer(self):
        self.bci.trusted = True
        self.bci.save()

        booked_from, booked_till, variant, staffer = self._standard_setup_for_prepayment()
        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant,
            staffer=staffer,
        )
        baker.make(AccountHolder, pos=self.business.pos, ever_passed_kyc=True)

        # Check for prepayment_available = False
        url_dry_run = self.get_path(self.business.id, dry_run=True)
        resp = self.fetch(url_dry_run, method='POST', body=body)
        assert resp.json['appointment']['prepayment_available'] is False
        assert resp.json['appointment']['with_prepayment'] is False
        assert resp.json['appointment']['actions']['change']
        assert resp.json['appointment']['status'] == Appointment.STATUS.ACCEPTED
        self._check_no_notification_created()

        # Return error when forcing for prepayment for trusted client
        body['with_prepayment'] = True
        resp = self.fetch(url_dry_run, method='POST', body=body)
        assert resp.code == status.HTTP_400_BAD_REQUEST
        self._check_error_with_prepayment(resp)

        # Check when with_prepayment = False
        body['with_prepayment'] = False
        resp = self.fetch(url_dry_run, method='POST', body=body)
        assert resp.json['appointment']['prepayment_available'] is False
        assert resp.json['appointment']['with_prepayment'] is False
        assert resp.json['appointment']['status'] == Appointment.STATUS.ACCEPTED

        # Check final booking
        body['with_prepayment'] = False
        url = self.get_path(self.business.id)
        resp = self.fetch(url, method='POST', body=body)
        assert resp.json['appointment']['prepayment_available'] is False
        assert resp.json['appointment']['with_prepayment'] is False
        self._standard_check(resp, booked_from, booked_till, Appointment.STATUS.ACCEPTED)
        self._check_no_notification_created()

    def test_create_booking_in_future_with_will_and_adyen_account_without_service_prepayment(self):
        variant = baker.make(
            ServiceVariant,
            duration='0100',
            service=baker.make(Service, business=self.business, gap_time=relativedelta(minutes=60)),
            gap_hole_duration=relativedelta(minutes=30),
            gap_hole_start_after=relativedelta(minutes=5),
        )
        staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            active=True,
            business=self.business,
        )
        variant.service.add_staffers([staffer])
        booked_from = get_before_and_after()[0]
        booked_till = booked_from + timedelta(hours=1)

        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant,
            staffer=staffer,
            with_prepayment=True,
        )
        baker.make(AccountHolder, pos=self.business.pos, ever_passed_kyc=True)

        url_dry_run = self.get_path(self.business.id, dry_run=True)
        resp = self.fetch(url_dry_run, method='POST', body=body)
        assert resp.code == status.HTTP_400_BAD_REQUEST
        self._check_no_notification_created()
        self._check_error_with_prepayment(resp)

    def test_create_repeating_booking_with_prepayment_not_possible(self):
        booked_from, booked_till, variant, staffer = self._standard_setup_for_prepayment()
        repeat_till = booked_till + timedelta(days=7)
        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant,
            staffer=staffer,
        )
        body['new_repeating'] = {
            'repeat': RepeatType.EVERY_WEEK,
            'end_type': RepeatEndType.AFTER_N_BOOKINGS,
            'repeat_till': repeat_till.isoformat(),
            'repeat_number': 10,
            'starting_on': booked_from.isoformat(),
            'ending_on': (booked_from + timedelta(days=63)).isoformat(),
            'actual_repeats': 10,
        }
        baker.make(AccountHolder, pos=self.business.pos, ever_passed_kyc=True)

        # Return prepayment_available=False when no with_prepayment defined
        url_dry_run = self.get_path(self.business.id, dry_run=True)
        resp = self.fetch(url_dry_run, method='POST', body=body)
        assert resp.json['appointment']['prepayment_available'] is False

        # Return error when forcing for prepayment for repeating booking (dry_run)
        body['with_prepayment'] = True
        url_dry_run = self.get_path(self.business.id, dry_run=True)
        resp = self.fetch(url_dry_run, method='POST', body=body)
        assert resp.code == status.HTTP_400_BAD_REQUEST
        self._check_no_notification_created()
        self._check_error_with_prepayment(resp)

        # Return error when forcing for prepayment for repeating booking
        url = self.get_path(self.business.id)
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_400_BAD_REQUEST
        self._check_no_notification_created()
        self._check_error_with_prepayment(resp)

    def test_create_booking_in_future_with_will_and_stripe_account(self):
        booked_from, booked_till, variant, staffer = self._standard_setup_for_prepayment()
        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant,
            staffer=staffer,
            with_prepayment=True,
        )
        baker.make(StripeAccount, pos=self.business.pos, kyc_verified_at_least_once=True)

        url_dry_run = self.get_path(self.business.id, dry_run=True)
        resp = self.fetch(url_dry_run, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED

        assert resp.json['appointment']['prepayment_available'] is True
        assert resp.json['appointment']['prepayment_deadline'] is None
        assert resp.json['appointment']['actions']['change']

        url = self.get_path(self.business.id)
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED

        assert resp.json['appointment']['prepayment_available'] is False  # because already created
        assert not resp.json['appointment']['actions']['change']
        self._standard_check(resp, booked_from, booked_till, Appointment.STATUS.PENDING_PAYMENT)
        self._check_payment_details(resp)
        appointment_id = resp.json['appointment']['appointment_uid']
        self._check_notification_scheduled(appointment_id)

    def test_create_booking_in_future_with_will_and_stripe_account_combo_service(self):
        booked_from, booked_till, variant, staffer = self._standard_setup_for_prepayment(
            is_combo=True,
        )
        url_dry_run = self.get_path(self.business.id, dry_run=True)
        baker.make(StripeAccount, pos=self.business.pos, kyc_verified_at_least_once=True)
        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant,
            staffer=staffer,
            with_prepayment=False,
            with_service_variant=False,
        )
        resp = self.fetch(url_dry_run, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED

        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant,
            staffer=staffer,
            with_prepayment=False,
        )
        resp = self.fetch(url_dry_run, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED

        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant,
            staffer=staffer,
            with_prepayment=True,
        )

        resp = self.fetch(url_dry_run, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED

        assert resp.json['appointment']['prepayment_available'] is True
        assert resp.json['appointment']['prepayment_deadline'] is None
        assert resp.json['appointment']['actions']['change']

        self._add_combo_children_fields_to_body(body, booked_from, variant)

        url = self.get_path(self.business.id)
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        json_body = resp.json

        assert json_body['appointment']['prepayment_available'] is False  # because already created
        assert not json_body['appointment']['actions']['change']
        assert json_body['appointment']['customer']['mode'] == ACMode.CUSTOMER_CARD
        appointment = Appointment.objects.filter(
            id=json_body['appointment']['appointment_uid'],
        ).first()
        assert appointment
        assert len(appointment.subbookings) == 1
        assert len(appointment.subbookings[0].combo_children) == 3

        payment_info = json_body['appointment']['payment_info']
        transaction_info = payment_info['transaction_info']
        assert transaction_info['status_type'] == receipt_status.STATUS_TYPE__CALL_FOR_DEPOSIT
        assert len(transaction_info['payment_rows']) == 1
        assert transaction_info['status_code'] == receipt_status.CALL_FOR_PREPAYMENT
        assert transaction_info['total'] == '$45.00'
        assert transaction_info['already_paid'] == '$19.98'
        assert transaction_info['remaining'] == '$25.02'

        appointment_id = json_body['appointment']['appointment_uid']
        self._check_notification_scheduled(appointment_id)

    def test_create_booking_in_future_with_will_and_without_kyc_at_any_provider(self):
        booked_from, booked_till, variant, staffer = self._standard_setup_for_prepayment()
        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant,
            staffer=staffer,
            with_prepayment=True,
        )

        url_dry_run = self.get_path(self.business.id, dry_run=True)
        resp = self.fetch(url_dry_run, method='POST', body=body)
        self._check_error_with_prepayment(resp)

        url = self.get_path(self.business.id)
        resp = self.fetch(url, method='POST', body=body)
        self._check_error_with_prepayment(resp)
        self._check_no_notification_created()

    @override_feature_flag({PrepaymentsForBusinessAppointmentEnabled: False})
    def test_create_booking_in_future_with_will_featureflag_disabled(self):
        booked_from, booked_till, variant, staffer = self._standard_setup_for_prepayment()
        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant,
            staffer=staffer,
            with_prepayment=True,
        )

        url_dry_run = self.get_path(self.business.id, dry_run=True)
        resp = self.fetch(url_dry_run, method='POST', body=body)
        self._check_error_with_prepayment(resp)

        url = self.get_path(self.business.id)
        resp = self.fetch(url, method='POST', body=body)
        self._check_no_notification_created()
        self._check_error_with_prepayment(resp)

    @override_feature_flag({PrepaymentsForBusinessAppointmentEnabled: False})
    def test_prepayment_toggle_visible_response_field_is_false(self):
        booked_from, booked_till, variant, staffer = self._standard_setup_for_prepayment()
        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant,
            staffer=staffer,
            with_prepayment=False,
        )

        url_dry_run = self.get_path(self.business.id, dry_run=True)
        resp = self.fetch(url_dry_run, method='POST', body=body)
        assert resp.json['appointment']['prepayment_toggle_visible'] is False

    def test_create_booking_in_future_without_will(self):
        booked_from, booked_till, variant, staffer = self._standard_setup_for_prepayment()
        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant,
            staffer=staffer,
            with_prepayment=False,
        )

        url = self.get_path(self.business.id)

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self._check_no_notification_created()
        self._standard_check(resp, booked_from, booked_till, Appointment.STATUS.ACCEPTED)

    def test_create_booking_in_future_old_app_no_parameter(self):
        booked_from, booked_till, variant, staffer = self._standard_setup_for_prepayment()
        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant,
            staffer=staffer,
            # no 'with_prepayment' parameter
        )
        assert 'with_prepayment' not in body

        url = self.get_path(self.business.id)

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self._check_no_notification_created()
        self._standard_check(resp, booked_from, booked_till, Appointment.STATUS.ACCEPTED)

    @freeze_time(datetime.datetime(2022, 9, 26, 11, 0, 0))
    def test_create_booking_in_too_near_future_with_will(self):
        _, __, variant, staffer = self._standard_setup_for_prepayment()
        booked_from = datetime.datetime(2022, 9, 26, 12, 0, 0, tzinfo=UTC)
        booked_till = booked_from + datetime.timedelta(hours=1)

        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant,
            staffer=staffer,
            with_prepayment=True,
        )

        url = self.get_path(self.business.id)

        resp = self.fetch(url, method='POST', body=body)
        self._check_no_notification_created()
        self._check_error_with_prepayment(resp)

    @freeze_time(datetime.datetime(2022, 9, 26, 11, 0, 0))
    def test_create_booking_in_near_future_without_will(self):
        _, __, variant, staffer = self._standard_setup_for_prepayment()
        booked_from = datetime.datetime(2022, 9, 26, 12, 0, 0, tzinfo=UTC)
        booked_till = booked_from + datetime.timedelta(hours=1)

        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant,
            staffer=staffer,
            with_prepayment=False,
        )

        url = self.get_path(self.business.id)

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self._check_no_notification_created()
        self._standard_check(
            resp,
            booked_from,
            booked_till,
            Appointment.STATUS.ACCEPTED,
        )

    @freeze_time(datetime.datetime(2022, 9, 26, 11, 0, 0))
    def test_create_booking_in_near_future_without_will_old_app(self):
        _, __, variant, staffer = self._standard_setup_for_prepayment()
        booked_from = datetime.datetime(2022, 9, 26, 12, 0, 0, tzinfo=UTC)
        booked_till = booked_from + datetime.timedelta(hours=1)

        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant,
            staffer=staffer,
            # no 'with_prepayment' parameter
        )

        url = self.get_path(self.business.id)

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self._check_no_notification_created()
        self._standard_check(
            resp,
            booked_from,
            booked_till,
            Appointment.STATUS.ACCEPTED,
        )

    @freeze_time(datetime.datetime(2022, 9, 26, 11, 0, 0))
    def test_create_booking_past_with_will(self):
        _, __, variant, staffer = self._standard_setup_for_prepayment()
        booked_from = datetime.datetime(2022, 9, 26, 9, 0, 0, tzinfo=UTC)
        booked_till = booked_from + datetime.timedelta(hours=1)

        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant,
            staffer=staffer,
            with_prepayment=True,
        )

        url = self.get_path(self.business.id)

        resp = self.fetch(url, method='POST', body=body)
        self._check_no_notification_created()
        self._check_error_with_prepayment(resp)

    @freeze_time(datetime.datetime(2022, 9, 26, 11, 0, 0))
    def test_create_booking_past_without_will(self):
        _, __, variant, staffer = self._standard_setup_for_prepayment()
        booked_from = datetime.datetime(2022, 9, 26, 9, 0, 0, tzinfo=UTC)
        booked_till = booked_from + datetime.timedelta(hours=1)

        body = self._get_standard_body(
            booked_from=booked_from,
            booked_till=booked_till,
            variant=variant,
            staffer=staffer,
        )

        url = self.get_path(self.business.id)

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self._check_no_notification_created()
        self._standard_check(
            resp,
            booked_from,
            booked_till,
            Appointment.STATUS.FINISHED,
        )

    def _check_error_with_prepayment(self, resp: HTTPResponse):
        assert resp.code == status.HTTP_400_BAD_REQUEST
        dict_assert(
            resp.json,
            {
                'errors': [
                    NOT_ELIGIBLE_ERROR,
                ],
            },
        )
        assert not Appointment.objects.filter(
            business_id=self.business.id,
        ).exists()


@pytest.mark.django_db
@override_feature_flag({PrepaymentsForBusinessAppointmentEnabled: True})
@pytest.mark.parametrize(
    'appt_type, allowed',
    [
        (Appointment.TYPE.CUSTOMER, False),
        (Appointment.TYPE.BUSINESS, True),
    ],
)
def test_customer_appointment__prepayment_retry_payment_not_available(appt_type, allowed):
    """
    Make sure that retry_payment action is only available for business prepayments (payment links)
    since it is not needed nor covered by fronts for customer prepayment appointments.
    """
    business = baker.make(Business)
    pos = baker.make(POS, business=business)
    appointment = create_appointment(
        type=appt_type,
        status=Appointment.STATUS.PENDING_PAYMENT,
        business=business,
    )
    receipt = baker.make(Receipt, status_code=receipt_status.PREPAYMENT_FAILED)
    transaction = baker.make(
        Transaction,
        pos=pos,
        appointment=appointment,
        transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        latest_receipt=receipt,
    )
    assert transaction.action_allowed(action=enums.CUSTOMER_ACTION__RETRY_PAYMENT) == allowed
