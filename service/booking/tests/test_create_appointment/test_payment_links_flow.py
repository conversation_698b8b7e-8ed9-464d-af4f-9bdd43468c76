import datetime
from _decimal import Decimal

import mock
import pytest
import responses
from django.test import override_settings
from mock.mock import patch
from model_bakery import baker
from rest_framework import status

from lib.deeplink.deeplinks_ms.ports import generate_deeplink_ms
from lib.feature_flag.feature.payment import (
    PrepaymentsForBusinessAppointmentEnabled,
)
from lib.point_of_sale.enums import BasketPaymentStatus
from lib.test_utils import spy_mock
from lib.tests.utils import override_feature_flag
from lib.tools import sget_v2
from lib.tools import tznow
from service.booking.tests.test_create_appointment._base import (
    BaseAppointmentWithPrepaymentTestCase,
)
from service.tests import dict_assert
from webapps.booking.baker_recipes.baker_recipes_appointment import (
    booking_recipe,
)
from webapps.booking.enums import BookingAction
from webapps.booking.models import Appointment, SubBooking
from webapps.booking.tasks import exceeded_payment_deadline_task
from webapps.booking.tests.test_exceeded_payment_deadline_task import _create_appointment
from webapps.consts import HOURS_TO_PAY_FOR_PREPAYMENT
from webapps.market_pay.models import AccountHolder
from webapps.notification.channels import EmailChannel
from webapps.notification.elasticsearch import NotificationHistoryDocument
from webapps.notification.scenarios import BookingChangedScenario
from webapps.point_of_sale.models import BasketPayment
from webapps.pos import enums
from webapps.pos.enums import receipt_status
from webapps.pos.models import (
    PaymentMethod,
    PaymentRow,
    Receipt,
    Tip,
    Transaction,
)
from webapps.pos.notifications import PrepaymentNotPaidNotification
from webapps.pos.provider.fake import _CARDS
from webapps.stripe_integration.models import StripeAccount
from webapps.user.enums import AuthOriginEnum

MY_BOOKSY_PATH = '/customer_api/my_booksy/'
BOOKINGS_LIST_PATH = '/customer_api/me/bookings/?'


@mock.patch('service.customer.my_booksy.is_in_experiment', return_value=False)
@pytest.mark.django_db
@override_feature_flag({PrepaymentsForBusinessAppointmentEnabled: True})
class CreateBusinessAppointmentWithPrepaymentTestCase(BaseAppointmentWithPrepaymentTestCase):
    def setUp(self):
        super().setUp()
        (
            self.booked_from,
            self.booked_till,
            self.variant,
            self.staffer,
        ) = self._standard_setup_for_prepayment()
        baker.make(Tip, pos=self.business.pos, rate=0, default=True)
        baker.make(Tip, pos=self.business.pos, rate=10, default=False)

    @override_settings(
        POS__APPLE_PAY=True,
        POS__GOOGLE_PAY=True,
    )
    def test_flow_create_booking_in_future_with_will_and_adyen_account(self, *_):
        self._test_flow_create_booking_in_future_with_will_and_adyen_account(True)

    def _test_flow_create_booking_in_future_with_will_and_adyen_account(
        self, external_partners_available, *_
    ):
        body = self._get_standard_body(
            self.booked_from,
            self.booked_till,
            self.variant,
            self.staffer,
            with_prepayment=True,
        )
        baker.make(AccountHolder, pos=self.business.pos, ever_passed_kyc=True)

        url_dry_run = self.get_path(self.business.id, dry_run=True)
        resp = self.fetch(url_dry_run, method='POST', body=body)

        assert resp.json['appointment']['prepayment_available'] is True

        url = self.get_path(self.business.id)
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED

        assert resp.json['appointment']['prepayment_available'] is False  # because already created
        self._standard_check(
            resp=resp,
            booked_from=self.booked_from,
            booked_till=self.booked_till,
            expected_status=Appointment.STATUS.PENDING_PAYMENT,
        )
        self._check_payment_details(resp)
        self._check_if_details_update_not_change_status(resp.json['appointment'])

        appointment_id = resp.json['appointment']['appointment_uid']
        self._check_notification_scheduled(appointment_id)
        self._test_appointment_details_and_payment(
            appointment_id=resp.json['appointment']['appointment_uid'],
            external_partners_available=external_partners_available,
        )

    def test_flow_create_booking_in_future_with_will_and_adyen_account_retried_payment(self, *_):
        booked_from, booked_till, variant, staffer = self._standard_setup_for_prepayment()
        body = self._get_standard_body(
            booked_from,
            booked_till,
            variant,
            staffer,
            with_prepayment=True,
        )
        baker.make(AccountHolder, pos=self.business.pos, ever_passed_kyc=True)

        url_dry_run = self.get_path(self.business.id, dry_run=True)
        self.fetch(url_dry_run, method='POST', body=body)

        url = self.get_path(self.business.id)
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        self._check_payment_details(resp)

        self._test_failed_payment_and_repayment(
            appointment_id=resp.json['appointment']['appointment_uid'],
        )

    def test_flow_create_booking_in_future_with_will_and_stripe_account(self, *_):
        """
        We are not sure that this test was valid in any time but just to be sure we are
        still testing stage I this way.
        """
        body = self._get_standard_body(
            self.booked_from,
            self.booked_till,
            self.variant,
            self.staffer,
            with_prepayment=True,
        )
        baker.make(StripeAccount, pos=self.business.pos, kyc_verified_at_least_once=True)

        url_dry_run = self.get_path(self.business.id, dry_run=True)
        resp = self.fetch(url_dry_run, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED

        assert resp.json['appointment']['prepayment_available'] is True
        assert resp.json['appointment']['prepayment_deadline'] is None

        url = self.get_path(self.business.id)
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED

        assert resp.json['appointment']['prepayment_available'] is False  # because already created
        self._standard_check(
            resp=resp,
            booked_from=self.booked_from,
            booked_till=self.booked_till,
            expected_status=Appointment.STATUS.PENDING_PAYMENT,
        )
        self._check_payment_details(resp)
        self._check_if_details_update_not_change_status(resp.json['appointment'])
        appointment_id = resp.json['appointment']['appointment_uid']
        self._check_notification_scheduled(appointment_id)
        self._test_appointment_details_and_payment(
            appointment_id=appointment_id,
        )

    def test_flow_customer_cancels_business_booking__adyen_account(self, *_):
        booked_from, booked_till, variant, staffer = self._standard_setup_for_prepayment()
        body = self._get_standard_body(
            booked_from,
            booked_till,
            variant,
            staffer,
            with_prepayment=True,
        )
        baker.make(AccountHolder, pos=self.business.pos, ever_passed_kyc=True)
        url = self.get_path(self.business.id)
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED

        appointment_id = resp.json['appointment']['appointment_uid']
        self._check_customer_cancel_flow(appointment_id=appointment_id, resp=resp)

    def test_flow_business_cancels_business_booking__adyen_account(self, *_):
        booked_from, booked_till, variant, staffer = self._standard_setup_for_prepayment()
        body = self._get_standard_body(
            booked_from,
            booked_till,
            variant,
            staffer,
            with_prepayment=True,
        )
        baker.make(AccountHolder, pos=self.business.pos, ever_passed_kyc=True)
        url = self.get_path(self.business.id)
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED

        appointment_id = resp.json['appointment']['appointment_uid']
        self._check_business_cancel_flow(appointment_id=appointment_id, resp=resp)

    @patch('webapps.notification.channels.send_email')
    @patch('webapps.notification.channels.send_sms')
    def test_notification_sent_after_automatic_appt_cancellation(
        self, mocked_send_sms, mocked_send_email, *_
    ):
        appt = _create_appointment()
        appt.created = tznow() - datetime.timedelta(hours=12, minutes=20)
        appt.booked_for = self.bci
        booking = booking_recipe.prepare(appointment=appt)
        booking.save(override=True)
        appt.save()

        spy__generate_deeplink = spy_mock(generate_deeplink_ms)
        with patch('lib.deeplink.adapters.generate_deeplink_ms', spy__generate_deeplink):
            exceeded_payment_deadline_task.run()

        assert spy__generate_deeplink.mock.call_count == 2
        assert mocked_send_sms.call_count == 1
        assert mocked_send_email.call_count == 1

        notification = PrepaymentNotPaidNotification(None, appointment_id=appt.id)
        email_content = EmailChannel(notification).get_content()
        assert (
            'was charged to your card and deducted from your total due after the appointment. '
            'This amount is not refundable.' not in email_content
        )

    def test_update_payment_rows_payment_link_status(self, *_):
        payment_type = self.pos.payment_types.first()
        transaction = baker.make(
            Transaction,
            pos=self.pos,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        receipt = baker.make(
            Receipt,
            transaction=transaction,
            status_code=receipt_status.PREPAYMENT_SUCCESS,
        )
        transaction.latest_receipt = receipt
        transaction.save()
        payment_row = PaymentRow.create_with_status(
            receipt=receipt,
            status=receipt_status.PREPAYMENT_FAILED,
            payment_type=payment_type,
            amount=Decimal(10),
            payment_link=True,
        )
        payment_row.save()

        transaction.update_payment_rows(receipt_status.PAYMENT_CANCELED)
        for pr in PaymentRow.objects.filter(receipt__transaction_id=transaction.id):
            assert pr.payment_link

    def _check_if_details_update_not_change_status(self, put_body: dict):
        appt_id = put_body['appointment_uid']
        appointment = Appointment.objects.filter(id=appt_id).first()
        self.assertEqual(appointment.status, Appointment.STATUS.PENDING_PAYMENT)

        put_body['overbooking'] = False
        put_body['_notify_about_reschedule'] = False
        put_body['with_prepayment'] = False
        put_url = f'/business_api/me/businesses/{self.business.id}/appointments/{appt_id}/'
        self.fetch(put_url, body=put_body, method='PUT')

        appointment.refresh_from_db()
        self.assertEqual(appointment.status, Appointment.STATUS.PENDING_PAYMENT)

    # pylint: disable=too-many-statements
    @responses.activate
    def test_flow_create_booking_in_future_with_will_and_stripe_stage_ii_account(self, *_):
        self._prepare_stripe_account()

        booked_from, booked_till, variant, staffer = self._standard_setup_for_prepayment()
        body = self._get_standard_body(
            booked_from,
            booked_till,
            variant,
            staffer,
            with_prepayment=True,
        )
        url_dry_run = self.get_path(self.business.id, dry_run=True)
        resp = self.fetch(url_dry_run, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED

        assert resp.json['appointment']['prepayment_available'] is True
        assert resp.json['appointment']['prepayment_deadline'] is None

        url = self.get_path(self.business.id)
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED

        assert resp.json['appointment']['prepayment_available'] is False  # because already created
        self._standard_check(resp, booked_from, booked_till, Appointment.STATUS.PENDING_PAYMENT)
        self._check_payment_details(resp)
        appointment_id = resp.json['appointment']['appointment_uid']
        self._check_notification_scheduled(appointment_id)
        self._test_appointment_details_and_payment(
            appointment_id=appointment_id,
        )

    @responses.activate
    def test_flow_business_cancels_business_booking__stripe_account(self, *_):
        self._prepare_stripe_account()

        booked_from, booked_till, variant, staffer = self._standard_setup_for_prepayment()
        body = self._get_standard_body(
            booked_from,
            booked_till,
            variant,
            staffer,
            with_prepayment=True,
        )
        url = self.get_path(self.business.id)
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED

        appointment_id = resp.json['appointment']['appointment_uid']
        self._check_business_cancel_flow(appointment_id=appointment_id, resp=resp, pos_stripe=True)

    @responses.activate
    def test_flow_customer_cancels_business_booking__stripe_account(self, *_):
        self._prepare_stripe_account()

        booked_from, booked_till, variant, staffer = self._standard_setup_for_prepayment()
        body = self._get_standard_body(
            booked_from,
            booked_till,
            variant,
            staffer,
            with_prepayment=True,
        )
        url = self.get_path(self.business.id)
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED

        appointment_id = resp.json['appointment']['appointment_uid']
        self._check_customer_cancel_flow(appointment_id=appointment_id, resp=resp, pos_stripe=True)

    # pylint: disable=too-many-locals
    def _test_appointment_details_and_payment(
        self,
        appointment_id: int,
        external_partners_available=False,
    ):
        '''Flow test with other handlers'''
        from webapps.booking.models import BookingChange

        appointment_db_object = Appointment.objects.filter(id=appointment_id).first()

        appointment_details_path = self.get_path_details(appointment_id)
        customer_user = self.bci.user
        customer_session = customer_user.create_session(
            origin=AuthOriginEnum.BOOKSY, fingerprint=''
        )
        headers = self.get_headers(appointment_details_path)
        headers['X-ACCESS-TOKEN'] = customer_session.session_key
        resp = self.fetch(
            path=appointment_details_path,
            method='GET',
            headers=headers,
        )
        payment_info = resp.json['appointment']['payment_info']
        transaction_id = payment_info['transaction_id']
        actions = resp.json['appointment']['actions']
        assert transaction_id
        transaction_info = payment_info['transaction_info']
        assert transaction_info
        assert (
            transaction_info['prepayment_deadline']
            == (
                appointment_db_object.created
                + datetime.timedelta(hours=HOURS_TO_PAY_FOR_PREPAYMENT)
            )
            .astimezone(self.business.get_timezone())
            .isoformat()[:16]
        )
        assert len(transaction_info['payment_rows']) == 1
        assert payment_info['transaction_info']
        assert len(payment_info['transaction_info']['payment_rows']) == 1
        self.check_possible_customer_actions(actions)

        resp_actions = self.fetch(path=BOOKINGS_LIST_PATH, method='GET', headers=headers)
        actions = resp_actions.json['bookings'][0]['actions']
        self.check_possible_customer_actions(actions)

        booking = SubBooking.objects.filter(appointment_id=appointment_id).first()
        date = booking.booked_from.date().isoformat()
        calendar_actions = self.fetch(
            path=self.get_path_calendar(
                business_id=self.business.id, start_date=date, end_date=date
            ),
            method='GET',
        )
        actions = calendar_actions.json['bookings'][str(booking.id)]['actions']
        self.check_possible_business_actions(actions)

        resp_my_booksy = self.fetch(path=MY_BOOKSY_PATH, method='GET', headers=headers).json
        assert resp_my_booksy['booking_box']
        assert resp_my_booksy['booking_box']['appointment_uid'] == appointment_id

        assert resp_my_booksy['prepayment_notification']['appointment_uid'] == appointment_id

        path_transaction_details_customer = self.get_path_transaction_details_customer(
            transaction_id=transaction_id,
        )
        headers = self.get_headers(appointment_details_path)
        headers['X-ACCESS-TOKEN'] = customer_session.session_key

        resp_transaction_details = self.fetch(
            path=path_transaction_details_customer,
            method='GET',
            headers=headers,
        )
        assert resp_transaction_details.code == status.HTTP_200_OK
        resp_json = resp_transaction_details.json
        self._check_tips(resp_json)
        self._check_external_partners(
            resp_json=resp_json,
            result_apple_pay=external_partners_available,
            result_google_pay=external_partners_available,
        )
        receipts = resp_json['transaction']['receipts']
        assert len(receipts) == 1
        receipt = receipts[0]
        assert len(receipt['payment_rows']) == 1
        dict_assert(
            receipt,
            {
                'already_paid': '$9.99',
                'remaining': '$7.31',
                'remaining_unformatted': '7.31',
                'total': '$17.30',
            },
        )
        assert (
            resp_transaction_details.json['transaction']['deposit_policy']
            == self.business.pos.deposit_policy
        )

        path_transaction_action = self.get_path_transaction_action(transaction_id)

        headers = self.get_headers(appointment_details_path)
        headers['X-ACCESS-TOKEN'] = customer_session.session_key

        # add a card
        payment_method_card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=customer_user,
        )

        resp_transaction_pay = self.fetch(
            path=path_transaction_action,
            method='POST',
            headers=headers,
            body={
                'action': enums.CUSTOMER_ACTION__MAKE_PAYMENT,
                'payment_method': payment_method_card.id,
                'row_id': sget_v2(
                    resp_transaction_details.json,
                    [
                        'transaction',
                        'payment_rows',
                        0,
                        'id',
                    ],
                ),
            },
        )
        assert resp_transaction_pay.code == status.HTTP_200_OK
        transaction = Transaction.objects.filter(id=transaction_id).first()
        assert transaction.latest_receipt.status_code == receipt_status.PREPAYMENT_SUCCESS
        appointment = Appointment.objects.filter(id=appointment_id).first()
        assert appointment.status == Appointment.STATUS.ACCEPTED

        assert BookingChange.objects.filter(
            changed_by=BookingChange.BY_CUSTOMER,
            metadata__icontains=BookingChangedScenario.CUSTOMER_PAID_FOR_AWAITING_PREPAYMENT,
            status=Appointment.STATUS.ACCEPTED,
            appointment_id=appointment_id,
        ).exists()

        for pr in PaymentRow.objects.filter(receipt__transaction_id=transaction_id):
            assert pr.payment_link

        appt_action_path = self.get_path_appointment__customer_action(appointment_id=appointment_id)
        headers = self.get_headers(appt_action_path)
        headers['X-ACCESS-TOKEN'] = customer_session.session_key

        #  cancel prepaid appointment
        # pylint: disable=protected-access
        self.fetch(
            path=appt_action_path,
            method='POST',
            headers=headers,
            body={
                '_version': appointment._version,
                'action': BookingAction.CANCEL,
                'no_thumbs': True,
            },
        )

        email_content = NotificationHistoryDocument.tasks_get(
            task_id=f'booking_changed:business_booking_cancel:appointment_id={appointment_id}'
        )[0].content

        assert (
            'was charged to your card and deducted from your total due after the appointment. '
            'This amount is not refundable.' not in email_content
        )

    def _check_business_cancel_flow(self, appointment_id: int, resp: dict, pos_stripe=False):
        appointment_cancel_path = self.get_path_appointment__business_action(
            self.business.id,
            appointment_id,
        )
        cancel_body = {
            'action': 'cancel',
            'business_note': '',
            '_version': resp.json['appointment']['_version'],
        }
        cancel_resp = self.fetch(path=appointment_cancel_path, method='POST', body=cancel_body)

        assert cancel_resp.code == 200
        assert cancel_resp.json['appointment']['status'] == Appointment.STATUS.CANCELED
        assert not cancel_resp.json['appointment']['payment_info']['payable']
        assert (
            cancel_resp.json['appointment']['payment_info']['transaction_info']['status_code']
            == receipt_status.PAYMENT_CANCELED
        )
        transaction_from_db = Transaction.objects.get(appointment_id=appointment_id)
        assert transaction_from_db.latest_receipt.status_code == receipt_status.PAYMENT_CANCELED
        if pos_stripe:
            # Check BasketPayment - new POS
            basketpayment_from_db = BasketPayment.objects.get(
                basket_id=transaction_from_db.basket_id
            )
            assert basketpayment_from_db.status == BasketPaymentStatus.CANCELED

    def _check_customer_cancel_flow(self, appointment_id: int, resp: dict, pos_stripe=False):
        appointment_cancel_path = self.get_path_appointment__customer_action(appointment_id)
        headers = self.get_headers(appointment_cancel_path)
        customer_user = self.bci.user
        customer_session = customer_user.create_session(
            origin=AuthOriginEnum.BOOKSY, fingerprint=''
        )
        headers['X-ACCESS-TOKEN'] = customer_session.session_key
        cancel_body = {
            'action': 'cancel',
            'id': resp.json['appointment']['appointment_id'],
            '_version': resp.json['appointment']['_version'],
        }
        cancel_resp = self.fetch(
            path=appointment_cancel_path, method='POST', headers=headers, body=cancel_body
        )

        assert cancel_resp.code == 200
        assert cancel_resp.json['appointment']['status'] == Appointment.STATUS.CANCELED
        assert not cancel_resp.json['appointment']['payment_info']['payable']
        # Check transaction model - old POS
        assert (
            cancel_resp.json['appointment']['payment_info']['transaction_info']['status_code']
            == receipt_status.PAYMENT_CANCELED
        )
        transaction_from_db = Transaction.objects.get(appointment_id=appointment_id)
        assert transaction_from_db.latest_receipt.status_code == receipt_status.PAYMENT_CANCELED
        if pos_stripe:
            # Check BasketPayment - new POS
            basketpayment_from_db = BasketPayment.objects.get(
                basket_id=transaction_from_db.basket_id
            )
            assert basketpayment_from_db.status == BasketPaymentStatus.CANCELED

    def _test_failed_payment_and_repayment(self, appointment_id: int):
        """Flow test with other handlers - failed transaction & successful repayment"""
        from webapps.booking.models import BookingChange

        customer_user = self.bci.user
        customer_session = customer_user.create_session(
            origin=AuthOriginEnum.BOOKSY, fingerprint=''
        )

        appointment_details_path = self.get_path_details(appointment_id)
        headers = self.get_headers(appointment_details_path)
        headers['X-ACCESS-TOKEN'] = customer_session.session_key
        resp = self.fetch(path=appointment_details_path, method='GET', headers=headers)

        first_transaction_id = resp.json['appointment']['payment_info']['transaction_id']
        assert first_transaction_id

        path_transaction_details_customer = self.get_path_transaction_details_customer(
            transaction_id=first_transaction_id,
        )
        resp_transaction_details = self.fetch(
            path=path_transaction_details_customer, method='GET', headers=headers
        )
        assert resp_transaction_details.code == status.HTTP_200_OK
        assert resp_transaction_details.json['transaction']['actions']['make_payment']
        assert not resp_transaction_details.json['transaction']['actions']['retry_payment']
        receipts = resp_transaction_details.json['transaction']['receipts']
        assert len(receipts) == 1
        receipt = receipts[0]
        assert len(receipt['payment_rows']) == 1
        dict_assert(
            receipt,
            {
                'already_paid': '$9.99',
                'remaining': '$7.31',
                'remaining_unformatted': '7.31',
                'total': '$17.30',
            },
        )

        payment_method_failing_card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[10]['last_digits'],  # failing card
            user=customer_user,
        )

        # 1. Try to pay with invalid card
        resp_transaction_pay = self.fetch(
            path=self.get_path_transaction_action(first_transaction_id),
            method='POST',
            headers=headers,
            body={
                'action': enums.CUSTOMER_ACTION__MAKE_PAYMENT,
                'payment_method': payment_method_failing_card.id,
                'row_id': sget_v2(
                    resp_transaction_details.json, ['transaction', 'payment_rows', 0, 'id']
                ),
            },
        )
        # 2. Failed transaction & appointment still awaiting prepayment

        resp_my_booksy = self.fetch(path=MY_BOOKSY_PATH, method='GET', headers=headers).json
        assert resp_my_booksy['booking_box']
        assert resp_my_booksy['booking_box']['appointment_uid'] == appointment_id

        assert resp_transaction_pay.code == status.HTTP_200_OK
        transaction = Transaction.objects.filter(id=first_transaction_id).first()
        assert transaction.latest_receipt.status_code == receipt_status.PREPAYMENT_FAILED
        appointment = Appointment.objects.filter(id=appointment_id).first()
        assert appointment.status == Appointment.STATUS.PENDING_PAYMENT

        retry_resp_transaction_details_last_receipt = self.fetch(
            path=self.get_path_transaction_details_last_receipt_customer(first_transaction_id),
            method='GET',
            headers=headers,
        )
        assert retry_resp_transaction_details_last_receipt.code == status.HTTP_200_OK
        assert (
            retry_resp_transaction_details_last_receipt.json['receipt']['status_code']
            == receipt_status.PREPAYMENT_FAILED
        )
        assert not retry_resp_transaction_details_last_receipt.json['actions']['make_payment']
        assert retry_resp_transaction_details_last_receipt.json['actions']['retry_payment']

        # 3. Try to pay with correct card
        payment_method_card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=customer_user,
        )
        retry_path_transaction_action = self.get_path_transaction_action(first_transaction_id)
        retry_resp_transaction_pay = self.fetch(
            path=retry_path_transaction_action,
            method='POST',
            headers=headers,
            body={
                'action': enums.CUSTOMER_ACTION__RETRY_PAYMENT,
                'payment_method': payment_method_card.id,
                'row_id': sget_v2(
                    retry_resp_transaction_details_last_receipt.json,
                    ['receipt', 'payment_rows', 0, 'id'],
                ),
            },
        )

        assert retry_resp_transaction_pay.code == status.HTTP_200_OK
        retry_transaction_id = retry_resp_transaction_pay.json['transaction']['id']
        assert not retry_transaction_id == first_transaction_id
        transaction = Transaction.objects.filter(id=retry_transaction_id).first()
        assert transaction.latest_receipt.status_code == receipt_status.PREPAYMENT_SUCCESS
        appointment = Appointment.objects.filter(id=appointment_id).first()
        assert appointment.status == Appointment.STATUS.ACCEPTED

        retry_resp_transaction_details_last_receipt = self.fetch(
            path=self.get_path_transaction_details_last_receipt_customer(retry_transaction_id),
            method='GET',
            headers=headers,
        )
        assert retry_resp_transaction_details_last_receipt.code == status.HTTP_200_OK
        assert (
            retry_resp_transaction_details_last_receipt.json['receipt']['status_code']
            == receipt_status.PREPAYMENT_SUCCESS
        )
        resp_my_booksy = self.fetch(path=MY_BOOKSY_PATH, method='GET', headers=headers).json
        assert resp_my_booksy['booking_box']
        assert resp_my_booksy['booking_box']['appointment_uid'] == appointment_id

        assert BookingChange.objects.filter(
            changed_by=BookingChange.BY_CUSTOMER,
            metadata__icontains=BookingChangedScenario.CUSTOMER_PAID_FOR_AWAITING_PREPAYMENT,
            status=Appointment.STATUS.ACCEPTED,
            appointment_id=appointment_id,
        ).exists()

        for pr in PaymentRow.objects.filter(receipt__transaction_id=transaction.id):
            assert pr.payment_link

        transaction_from_db = Transaction.objects.get(id=retry_transaction_id)
        assert transaction_from_db.latest_receipt.status_code == receipt_status.PREPAYMENT_SUCCESS

    def _check_tips(self, response_json: dict):
        assert response_json['transaction']['tip']
        tip_choices = response_json['transaction']['tip_choices']
        assert len(tip_choices) == 2
        dict_assert(
            tip_choices[0],
            {
                'rate': '0.00',
                'label': 'No Tip',
                'amount': '$0.00',
                'selected': True,
                'default': True,
            },
        )
        dict_assert(
            tip_choices[1],
            {
                'rate': '10.00',
                'label': '10%',
                'amount': '$1.73',
                'selected': False,
                'default': False,
            },
        )

    def _check_external_partners(
        self,
        resp_json: dict,
        result_google_pay: bool = False,
        result_apple_pay: bool = False,
    ):
        dict_assert(
            resp_json['external_partners'],
            {
                'google_pay': result_google_pay,
                'apple_pay': result_apple_pay,
            },
        )

    @staticmethod
    def get_path_details(appointment_id: int) -> str:
        return f"/customer_api/me/appointments/{appointment_id}/?"

    @staticmethod
    def get_path_transaction_action(transaction_id: int) -> str:
        return f'/customer_api/me/transactions/{transaction_id}/action/?'

    @staticmethod
    def get_path_transaction_details_customer(transaction_id: int) -> str:
        return f'/customer_api/me/transactions/{transaction_id}/?stardust=true'

    @staticmethod
    def get_path_appointment__customer_action(appointment_id: int) -> str:
        return f'/customer_api/me/appointments/{appointment_id}/action/?'

    @staticmethod
    def get_path_transaction_details_last_receipt_customer(transaction_id: int) -> str:
        return f'/customer_api/me/transactions/{transaction_id}/last_receipt/?'

    @staticmethod
    def get_path_calendar(business_id: int, start_date: str, end_date: str) -> str:
        return (
            f'/business_api/me/businesses/{business_id}/calendar/?start_date={start_date}&'
            f'end_date={end_date}&include_unconfirmed=True'
        )
