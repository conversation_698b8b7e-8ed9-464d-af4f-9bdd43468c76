import datetime
from datetime import time

import pytest
from dateutil.relativedelta import relativedelta
from django.test import override_settings
from mock import (
    patch,
)
from model_bakery import baker
from pytz import UTC
from rest_framework import status
from segment.analytics import Client

from lib.feature_flag.feature.booking import TrackAppointmentCreatedAnalytics
from lib.test_utils import (
    increase_appointment_next_id,
)
from lib.tests.utils import override_eppo_feature_flag
from service.tests import BaseAsyncHTTPTest, dict_assert
from webapps.booking.enums import (
    AppointmentCustomerMode as ACMode,
    AppointmentTypeSM as AT,
)
from webapps.booking.models import Appointment, BookingChange
from webapps.booking.tests.test_appointment_base import (
    BaseTestAppointment,
    build_custappt_data_family_and_friends,
)
from webapps.business.enums import PriceType
from webapps.business.models import (
    Business,
    Service,
    ServiceVariant,
    ServiceVariantPayment,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.family_and_friends.baker_recipes import invitation_recipe, member_recipe
from webapps.family_and_friends.enums import FamilyAndFriendsRoleEnum, RelationshipType
from webapps.family_and_friends.models import MemberAppointment, MemberTransaction
from webapps.family_and_friends.tests.utils import TestFamilyAndFriendsMixin
from webapps.notification.models import (
    NotificationSchedule,
)
from webapps.pos.enums import PaymentTypeEnum
from webapps.pos.models import (
    POS,
    PaymentMethod,
    PaymentType,
    Transaction,
)
from webapps.pos.provider.fake import _CARDS
from webapps.public_partners.baker_recipes import booksy_partner, partner_permission_business
from webapps.user.enums import AuthOriginEnum
from webapps.user.models import UserProfile

TEST_DATETIME = datetime.datetime(2018, 1, 1, tzinfo=UTC)

# Huge part of the test is copied from test_create_appointment with just added Family & Friends
# cases.


class AppointmentTestCaseMixin:
    @staticmethod
    def appointments_url(business_id):
        return f'/customer_api/me/appointments/business/{business_id}/'

    @staticmethod
    def business_appointments_url(business_id, appointment_id):
        return f'/business_api/me/businesses/{business_id}/appointments/{appointment_id}'

    @staticmethod
    def book_again_url():
        return '/customer_api/me/book_again/'

    @staticmethod
    def change_appointments_url(appointment_id):
        return f'/customer_api/me/appointments/{appointment_id}/'


@pytest.mark.django_db
@pytest.mark.freeze_time(datetime.datetime(2021, 3, 15, 12, tzinfo=UTC))
@override_settings(FAMILY_AND_FRIENDS_ENABLED=True)
class LegacyCreateCustomerAppointmentTestCase(
    AppointmentTestCaseMixin, BaseAsyncHTTPTest, BaseTestAppointment, TestFamilyAndFriendsMixin
):
    def setUp(self):
        super().setUp()
        self.user.profiles.get_or_create(profile_type=UserProfile.Type.CUSTOMER)
        self.business.booking_mode = Business.BookingMode.AUTO
        self.business.save()
        pos = baker.make(POS, business=self.business, active=True)
        baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.PREPAYMENT)
        baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.PAY_BY_APP)
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        increase_appointment_next_id()
        self.parent = member_recipe.make(
            user_profile=self.user.customer_profile,
            email=self.user.email,
            cell_phone=self.user.cell_phone,
        )
        _, self.member = self.create_inactive_member(parent=self.parent)
        _, self.member_active = self.create_confirmed_booksy(parent=self.parent)

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    @override_eppo_feature_flag({TrackAppointmentCreatedAnalytics.flag_name: True})
    def test_handler_custappt_create_single_for_inactive(
        self,
        analytics_track_mock,
        analytics_identify_mock,
    ):
        self.user.cell_phone = '+***********'
        self.user.save()
        url = self.appointments_url(self.business.id)
        body = build_custappt_data_family_and_friends(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
            member_id=self.member.id,
        )

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == 201
        self._test_martech_events_for_inactive(analytics_track_mock, analytics_identify_mock)
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1

        # CreateAppointmentMetadata
        assert resp.json['meta']['first'] is None
        assert resp.json['meta']['first_cross'] is None
        assert resp.json['meta']['cross'] is None

        # BookingChanges and NotificationSchedules
        appt_id = resp.json['appointment']['appointment_uid']
        assert (
            BookingChange.objects.filter(
                appointment_id=appt_id,
            ).count()
            == 1
        )
        assert NotificationSchedule.objects.filter(
            task_id=f'booking_changed:customer_booking_confirmation:appointment_id={appt_id}',
        ).exists()
        assert NotificationSchedule.objects.filter(
            task_id=f'booking_changed:business_booking_confirmation:appointment_id={appt_id}',
        ).exists()

        parent_bci = self.parent.bcis.filter(business=self.business).first()
        assert self.member.bcis.filter(business=self.business).exists()
        assert parent_bci.appointments_booked_by_me.filter(appointment__id=appt_id).exists()

        member_bci = self.member.bcis.filter(business=self.business).first()
        assert member_bci.appointments_booked_for_me.filter(appointment__id=appt_id).exists()
        self.session.user = self.staffer
        business_url = self.business_appointments_url(self.business.id, appt_id)
        resp = self.fetch(business_url, method='GET')
        assert resp.json['customer']['business_customer']['first_name'] == self.member.first_name
        assert resp.json['customer']['business_customer']['email'] == ''
        relation = member_bci.relations_to_parents.filter(parent_bci=parent_bci).first()
        assert relation.relationship_type == RelationshipType.CHILD
        assert relation.use_parent_data

    def _test_martech_events_for_inactive(self, analytics_track_mock, analytics_identify_mock):
        assert analytics_track_mock.call_count == 4
        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {
                'event': 'CB_Created_For_Customer',
            },
        )
        cb_created_for_customer_properties_keys = analytics_track_mock.call_args_list[0][1][
            'properties'
        ].keys()
        assert set(cb_created_for_customer_properties_keys).issuperset(
            {
                'booked_from',
                'business_id',
                'business_name',
                'business_postal_code',
                'business_primary_category',
                'category_id',
                'category_name',
                'country',
                'device_type',
                'email',
                'focus_area',
                'booking_score',
                'energy_booking',
                'service_id',
                'service_name',
                'service_price',
                'source',
                'staff_id',
                'urban_area',
                'urban_subarea',
                'currency',
                'is_xCB_xCategory',
                'is_xCB',
                'appointment_type',
                'appointment_id',
                'last_booking_latitude',
                'last_booking_longitude',
                'family_and_friends_role',
            }
        )
        cb_created = analytics_track_mock.call_args_list[1][1]
        cb_created_1st = analytics_track_mock.call_args_list[2][1]
        self.assertEqual(cb_created['event'], 'CB_Created_For_Business')
        self.assertEqual(
            cb_created['properties']['family_and_friends_role'],
            FamilyAndFriendsRoleEnum.CREATOR,
        )
        self.assertEqual(cb_created_1st['event'], '1st_CB_Created_For_Business')
        self.assertEqual(
            cb_created_1st['properties']['family_and_friends_role'],
            FamilyAndFriendsRoleEnum.CREATOR,
        )
        self.assertEqual(analytics_identify_mock.call_count, 3)

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    @override_eppo_feature_flag({TrackAppointmentCreatedAnalytics.flag_name: True})
    def test_handler_custappt_create_single_for_active(
        self,
        analytics_track_mock,
        analytics_identify_mock,
    ):
        self.user.cell_phone = '+***********'
        self.user.save()
        url = self.appointments_url(self.business.id)
        body = build_custappt_data_family_and_friends(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
            member_id=self.member_active.id,
        )

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == 201
        self._test_martech_events(analytics_track_mock, analytics_identify_mock)
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1

        # CreateAppointmentMetadata
        assert resp.json['meta']['first'] is True
        assert resp.json['meta']['first_cross'] is False
        assert resp.json['meta']['cross'] is False

        # BookingChanges and NotificationSchedules
        appt_id = resp.json['appointment']['appointment_uid']
        assert (
            BookingChange.objects.filter(
                appointment_id=appt_id,
            ).count()
            == 1
        )
        assert NotificationSchedule.objects.filter(
            task_id=f'booking_changed:customer_booking_confirmation:appointment_id={appt_id}',
        ).exists()
        assert NotificationSchedule.objects.filter(
            task_id=f'booking_changed:business_booking_confirmation:appointment_id={appt_id}',
        ).exists()

        parent_bci = self.parent.bcis.filter(business=self.business).first()
        assert self.member_active.bcis.filter(business=self.business).exists()
        assert parent_bci.appointments_booked_by_me.filter(appointment__id=appt_id).exists()

        member_bci = self.member_active.bcis.filter(business=self.business).first()
        assert member_bci.appointments_booked_for_me.filter(appointment__id=appt_id).exists()
        self.session.user = self.staffer
        business_url = self.business_appointments_url(self.business.id, appt_id)
        resp = self.fetch(business_url, method='GET')
        assert (
            resp.json['customer']['customer_profile']['full_name']
            == self.member_active.user_profile.user.full_name
        )
        assert (
            resp.json['customer']['customer_profile']['email']
            == self.member_active.user_profile.user.email
        )

    def _test_martech_events(self, analytics_track_mock, analytics_identify_mock):
        assert analytics_track_mock.call_count == 5
        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {
                'event': 'CB_Created_For_Customer',
            },
        )
        cb_created_for_customer_properties_keys = analytics_track_mock.call_args_list[0][1][
            'properties'
        ].keys()
        assert set(cb_created_for_customer_properties_keys).issuperset(
            {
                'booked_from',
                'business_id',
                'business_name',
                'business_postal_code',
                'business_primary_category',
                'category_id',
                'category_name',
                'country',
                'device_type',
                'email',
                'focus_area',
                'booking_score',
                'energy_booking',
                'service_id',
                'service_name',
                'service_price',
                'source',
                'staff_id',
                'urban_area',
                'urban_subarea',
                'control_group',
                'user_role',
                'user_id',
                'phone',
                'currency',
                'is_xCB_xCategory',
                'is_xCB',
                'appointment_type',
                'appointment_id',
                'last_booking_latitude',
                'last_booking_longitude',
                'family_and_friends_role',
            }
        )
        dict_assert(
            analytics_track_mock.call_args_list[1][1],
            {
                'event': '1st_CB_Created_For_Customer',
            },
        )
        first_cb_created_for_customer_properties_keys = set(
            analytics_track_mock.call_args_list[1][1]['properties'].keys()
        )

        print(
            first_cb_created_for_customer_properties_keys.difference(
                {
                    'booked_from',
                    'business_id',
                    'business_name',
                    'business_postal_code',
                    'business_primary_category',
                    'category_id',
                    'category_name',
                    'country',
                    'device_type',
                    'email',
                    'service_id',
                    'service_name',
                    'service_price',
                    'source',
                    'staff_id',
                    'control_group',
                    'booking_score',
                    'energy_booking',
                    'appointment_id',
                }
            )
        )
        assert first_cb_created_for_customer_properties_keys.issuperset(
            {
                'booked_from',
                'business_id',
                'business_name',
                'business_postal_code',
                'business_primary_category',
                'category_id',
                'category_name',
                'country',
                'device_type',
                'email',
                'service_id',
                'service_name',
                'service_price',
                'source',
                'staff_id',
                'control_group',
                'booking_score',
                'energy_booking',
                'appointment_id',
                'family_and_friends_role',
            }
        )
        cb_created = analytics_track_mock.call_args_list[2][1]
        cb_created_1st = analytics_track_mock.call_args_list[3][1]
        self.assertEqual(cb_created['event'], 'CB_Created_For_Business')
        self.assertEqual(
            cb_created['properties']['family_and_friends_role'],
            FamilyAndFriendsRoleEnum.MEMBER,
        )
        self.assertEqual(cb_created_1st['event'], '1st_CB_Created_For_Business')
        self.assertEqual(
            cb_created_1st['properties']['family_and_friends_role'],
            FamilyAndFriendsRoleEnum.MEMBER,
        )
        self.assertEqual(analytics_identify_mock.call_count, 4)

    def test_handler_custappt_create_with_deposit_discount_for_inactive(self):
        assert self.business.pos_pay_by_app_enabled
        with_deposit = self.get_deposit()

        url = self.appointments_url(self.business.id)

        body = build_custappt_data_family_and_friends(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
            member_id=self.member.id,
        )

        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='Jan',
            last_name='Matejko',
            cell_phone='**********',
            user=self.user,
            discount=20,
        )
        self.parent.bcis.add(bci)
        self.parent.save()

        body['customer'] = {
            'mode': ACMode.CUSTOMER_CARD,
            'id': bci.id,
        }

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == 201
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1
        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        assert deposit_info
        assert (
            deposit_info['id']
            == Transaction.objects.get(
                appointment_id=resp.json['appointment']['appointment_uid'],
                transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
            ).id
        )
        assert deposit_info['already_paid'] == '$13.84'
        assert deposit_info['total'] == '$13.84'

        assert MemberTransaction.objects.filter(
            transaction__id=deposit_info['id'],
            member=self.parent,
            bci=self.parent.bcis.filter(business=self.business).get(),
        ).exists()

    def test_handler_custappt_create_with_deposit_discount_for_active(self):
        with_deposit = self.get_deposit()

        url = self.appointments_url(self.business.id)

        body = build_custappt_data_family_and_friends(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
            member_id=self.member_active.id,
        )

        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='Jan',
            last_name='Matejko',
            cell_phone='**********',
            user=self.user,
            discount=20,
        )
        self.parent.bcis.add(bci)
        self.parent.save()

        body['customer'] = {
            'mode': ACMode.CUSTOMER_CARD,
            'id': bci.id,
        }

        # add a member card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.member_active.user_profile.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)
        # card doesn't exist
        assert resp.code == 400

        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == 201
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1
        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        assert deposit_info
        assert (
            deposit_info['id']
            == Transaction.objects.get(
                appointment_id=resp.json['appointment']['appointment_uid'],
                transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
            ).id
        )
        assert deposit_info['already_paid'] == '$13.84'
        assert deposit_info['total'] == '$13.84'

        assert MemberTransaction.objects.filter(
            transaction__id=deposit_info['id'],
            member=self.parent,
            bci=self.parent.bcis.filter(business=self.business).get(),
        ).exists()

    def test_handler_custappt_create_with_prepayment_with_inactive(self):
        with_prepayment = self.get_prepayment()

        url = self.appointments_url(self.business.id)
        body = build_custappt_data_family_and_friends(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
            member_id=self.member.id,
        )
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        assert resp.code == 201
        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        assert deposit_info is None
        transaction_info = resp.json['appointment']['payment_info']['transaction_info']
        assert (
            transaction_info['id']
            == Transaction.objects.get(
                appointment_id=resp.json['appointment']['appointment_uid'],
                transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            ).id
        )
        parent_bci = self.parent.bcis.filter(business=self.business).first()
        assert self.member.bcis.filter(business=self.business).exists()
        member_appointment = parent_bci.appointments_booked_by_me.filter(
            appointment__id=resp.json['appointment']['appointment_uid']
        ).first()
        appointment = member_appointment.appointment
        assert appointment.booked_for == self.member.bcis.filter(business=self.business).get()
        assert MemberTransaction.objects.filter(
            transaction__id=transaction_info['id'],
            member=self.parent,
            bci=self.parent.bcis.filter(business=self.business).get(),
        ).exists()

    def test_handler_custappt_create_with_prepayment_with_active(self):
        with_prepayment = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_prepayment.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=9.99,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data_family_and_friends(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
            member_id=self.member_active.id,
        )
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        assert resp.code == 201
        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        assert deposit_info is None
        transaction_info = resp.json['appointment']['payment_info']['transaction_info']
        assert (
            transaction_info['id']
            == Transaction.objects.get(
                appointment_id=resp.json['appointment']['appointment_uid'],
                transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            ).id
        )
        parent_bci = self.parent.bcis.filter(business=self.business).first()
        assert self.member_active.bcis.filter(business=self.business).exists()
        member_appointment = parent_bci.appointments_booked_by_me.filter(
            appointment__id=resp.json['appointment']['appointment_uid']
        ).first()
        appointment = member_appointment.appointment
        assert (
            appointment.booked_for == self.member_active.bcis.filter(business=self.business).get()
        )
        assert MemberTransaction.objects.filter(
            transaction__id=transaction_info['id'],
            member=self.parent,
            bci=self.parent.bcis.filter(business=self.business).get(),
        ).exists()

    def test_handler_custappt_create_with_prepayment_with_inactive_with_discount(self):
        with_prepayment = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=40.20,
        )
        with_prepayment.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=9.99,
        )
        parent_bci, member_bci = self.create_inactive_member_bcis(
            parent=self.parent, business=self.business
        )
        member_bci.discount = 50
        member_bci.save()
        parent_bci.discount = 30
        parent_bci.save()
        url = self.appointments_url(self.business.id)
        body = build_custappt_data_family_and_friends(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
            member_id=member_bci.memberprofile_set.first().id,
        )
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)

        assert resp.code == 201
        assert resp.json['appointment_payment']['prepayment'] == '6.99'
        assert resp.json['appointment']['subbookings'][0]['service_promotion']['_price'] == 28.14
        assert (
            resp.json['appointment']['subbookings'][0]['service_promotion']['discount_value'] == 30
        )
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        assert resp.code == 201
        assert resp.json['appointment_payment']['prepayment'] == '6.99'
        assert resp.json['appointment']['subbookings'][0]['service_promotion']['_price'] == 28.14
        assert (
            resp.json['appointment']['subbookings'][0]['service_promotion']['discount_value'] == 30
        )

    def test_handler_custappt_create_with_prepayment_with_active_with_discount(self):
        with_prepayment = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=40.20,
        )
        with_prepayment.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=9.99,
        )
        parent_bci, member_bci = self.create_active_members_bcis(
            parent=self.parent, business=self.business
        )
        member_bci.discount = 50
        member_bci.save()
        parent_bci.discount = 30
        parent_bci.save()
        url = self.appointments_url(self.business.id)
        body = build_custappt_data_family_and_friends(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
            member_id=member_bci.memberprofile_set.first().id,
        )
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)

        assert resp.code == 201
        assert resp.json['appointment_payment']['prepayment'] == '6.99'
        assert resp.json['appointment']['subbookings'][0]['service_promotion']['_price'] == 28.14
        assert (
            resp.json['appointment']['subbookings'][0]['service_promotion']['discount_value'] == 30
        )
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        assert resp.code == 201
        assert resp.json['appointment_payment']['prepayment'] == '6.99'
        assert resp.json['appointment']['subbookings'][0]['service_promotion']['_price'] == 28.14
        assert (
            resp.json['appointment']['subbookings'][0]['service_promotion']['discount_value'] == 30
        )

    def test_handler_custappt_create_with_both(self):
        assert self.business.pos_pay_by_app_enabled
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=9.99,
        )

        # Create second service with prepayment
        service2 = baker.make(Service, business=self.business)
        with_prepayment = baker.make(
            ServiceVariant,
            duration='3600',
            service=service2,
            type=PriceType.FIXED,
            time_slot_interval='0300',
            price=100.99,
        )
        service2.add_staffers([self.staffer])
        self.service_variant_payment2 = baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=80.17,
        )

        url = self.appointments_url(self.business.id)
        body = build_custappt_data_family_and_friends(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
            member_id=self.member.id,
        )

        body2 = build_custappt_data_family_and_friends(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(11, 0)),
            staffer=self.staffer,
            recurring=False,
            member_id=self.member.id,
        )

        body['subbookings'] = body['subbookings'] + body2['subbookings']

        resp = self.fetch(url, method='POST', body=body)

        # oh no! credit card is required
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'missing_method'

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == 201
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '9.99'
        assert appointment_payment['prepayment_total'] == '80.17'
        appointment = resp.json['appointment']
        assert appointment['family_and_friends']['booked_for']['member_id'] == self.member.id
        assert appointment['family_and_friends']['booked_for']['full_name'] == self.member.full_name
        assert appointment['family_and_friends']['booked_by']['member_id'] == self.parent.id
        assert appointment['family_and_friends']['booked_by']['full_name'] == self.parent.full_name
        assert appointment['family_and_friends']['booked_by']['user_id'] == self.user.id

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == 201
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 2

        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        assert deposit_info
        assert (
            deposit_info['id']
            == Transaction.objects.get(
                appointment_id=resp.json['appointment']['appointment_id'],
                transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
            ).id
        )

        transaction_info = resp.json['appointment']['payment_info']['transaction_info']
        assert (
            transaction_info['id']
            == Transaction.objects.get(
                appointment_id=resp.json['appointment']['appointment_id'],
                transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            ).id
        )
        assert MemberTransaction.objects.filter(
            transaction__id=deposit_info['id'],
            member=self.parent,
            bci=self.parent.bcis.filter(business=self.business).get(),
        ).exists()
        assert MemberTransaction.objects.filter(
            transaction__id=transaction_info['id'],
            member=self.parent,
            bci=self.parent.bcis.filter(business=self.business).get(),
        ).exists()

    def test_handler_custappt_create_with_both_discount(self):
        assert self.business.pos_pay_by_app_enabled
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=9.99,
        )

        # Create second service with prepayment
        service2 = baker.make(Service, business=self.business)
        with_prepayment = baker.make(
            ServiceVariant,
            duration='3600',
            service=service2,
            type=PriceType.FIXED,
            time_slot_interval='0300',
            price=100.99,
        )
        service2.add_staffers([self.staffer])
        self.service_variant_payment2 = baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=80.17,
        )

        url = self.appointments_url(self.business.id)
        parent_bci, member_bci = self.create_active_members_bcis(
            parent=self.parent, business=self.business
        )
        member_bci.discount = 50
        member_bci.save()
        parent_bci.discount = 30
        parent_bci.save()
        member = member_bci.memberprofile_set.first()
        body = build_custappt_data_family_and_friends(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
            member_id=member.id,
        )

        body2 = build_custappt_data_family_and_friends(
            variant=with_prepayment,
            booked_from=self._dt_from_hour(time(11, 0)),
            staffer=self.staffer,
            recurring=False,
            member_id=member.id,
        )

        body['subbookings'] = body['subbookings'] + body2['subbookings']

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == 201
        assert resp.json['appointment_payment']['cancellation_fee'] == '6.99'
        assert resp.json['appointment_payment']['prepayment'] == '56.12'
        subbookings = resp.json['appointment']['subbookings']
        assert subbookings[0]['service_promotion']['_price'] == 12.11
        assert subbookings[0]['service_promotion']['discount_value'] == 30
        assert subbookings[1]['service_promotion']['_price'] == 70.69
        assert subbookings[1]['service_promotion']['discount_value'] == 30

    def test_handler_custappt_create_with_active_deposit_with_discount(self):
        assert self.business.pos_pay_by_app_enabled
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=9.99,
        )

        url = self.appointments_url(self.business.id)
        parent_bci, member_bci = self.create_active_members_bcis(
            parent=self.parent, business=self.business
        )
        member_bci.discount = 50
        member_bci.save()
        parent_bci.discount = 30
        parent_bci.save()
        member = member_bci.memberprofile_set.first()
        body = build_custappt_data_family_and_friends(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
            member_id=member.id,
        )

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == 201
        assert resp.json['appointment_payment']['cancellation_fee'] == '6.99'
        subbookings = resp.json['appointment']['subbookings']
        assert subbookings[0]['service_promotion']['_price'] == 12.11
        assert subbookings[0]['service_promotion']['discount_value'] == 30

    def test_handler_multibooking_with_deposit(self):
        assert self.business.pos_pay_by_app_enabled
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        with_deposit.add_staffers([self.staffer])

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=9.99,
        )

        # Create second service with prepayment
        service2 = baker.make(Service, business=self.business)
        sv2 = baker.make(
            ServiceVariant,
            duration='3600',
            service=service2,
            type=PriceType.FIXED,
            time_slot_interval='0300',
            price=100.99,
        )
        service2.add_staffers([self.staffer])

        url = self.appointments_url(self.business.id)
        body = build_custappt_data_family_and_friends(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
            member_id=self.member.id,
        )

        body2 = build_custappt_data_family_and_friends(
            variant=sv2,
            booked_from=self._dt_from_hour(time(11, 0)),
            staffer=self.staffer,
            recurring=False,
            member_id=self.member.id,
        )

        body['subbookings'] = body['subbookings'] + body2['subbookings']

        resp = self.fetch(url, method='POST', body=body)

        # oh no! credit card is required
        assert resp.code == 400
        assert len(resp.json['errors']) == 1
        assert resp.json['errors'][0]['code'] == 'missing_method'

        # dry run for deposit needed
        body['dry_run'] = True
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == 201
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '9.99'
        assert appointment_payment['prepayment_total'] == '0.00'

        # add a card
        card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        body['payment_method'] = card.id
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        # now it works!
        assert resp.code == 201
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 2

        deposit_info = resp.json['appointment']['payment_info']['deposit_info']
        assert (
            deposit_info['id']
            == Transaction.objects.get(
                appointment_id=resp.json['appointment']['appointment_id'],
                transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
            ).id
        )

        transaction_info = resp.json['appointment']['payment_info']['transaction_info']
        assert transaction_info is None

        assert MemberTransaction.objects.filter(
            transaction__id=deposit_info['id'],
            member=self.parent,
            bci=self.parent.bcis.filter(business=self.business).get(),
        ).exists()

    def test_book_again(self):
        url = self.appointments_url(self.business.id)
        body = build_custappt_data_family_and_friends(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
            member_id=self.member_active.id,
        )

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED

        body = {
            'appointment_type': AT.SINGLE,
            'appointment_id': resp.json['appointment']['appointment_id'],
        }
        resp = self.fetch(self.book_again_url(), body=body, method='POST')
        assert resp.code == status.HTTP_201_CREATED
        family_and_friends = resp.json['appointment']['family_and_friends']
        assert family_and_friends['booked_for']['member_id'] == self.member_active.id
        assert family_and_friends['booked_for']['full_name'] == self.member_active.full_name
        assert family_and_friends['booked_by']['member_id'] == self.parent.id
        assert family_and_friends['booked_by']['full_name'] == self.parent.full_name

        user = self.member_active.user_profile.user
        self.session = user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        resp = self.fetch(self.book_again_url(), body=body, method='POST')
        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['family_and_friends'] is None

    def test_dry_run_for_inactive(self):
        url = self.appointments_url(self.business.id)
        body = build_custappt_data_family_and_friends(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
            member_id=self.member.id,
        )
        body['dry_run'] = True

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        family_and_friends = resp.json['appointment']['family_and_friends']
        assert family_and_friends['booked_for']['member_id'] == self.member.id
        assert family_and_friends['booked_for']['full_name'] == self.member.full_name
        assert family_and_friends['booked_by']['member_id'] == self.parent.id
        assert family_and_friends['booked_by']['full_name'] == self.parent.full_name
        assert resp.json['appointment']['appointment_uid'] is None

    def test_dry_run_for_active(self):
        url = self.appointments_url(self.business.id)
        body = build_custappt_data_family_and_friends(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
            member_id=self.member_active.id,
        )
        body['dry_run'] = True

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        family_and_friends = resp.json['appointment']['family_and_friends']
        assert family_and_friends['booked_for']['member_id'] == self.member_active.id
        assert family_and_friends['booked_for']['full_name'] == self.member_active.full_name
        assert family_and_friends['booked_by']['member_id'] == self.parent.id
        assert family_and_friends['booked_by']['full_name'] == self.parent.full_name
        assert resp.json['appointment']['appointment_uid'] is None

    def test_create_appointment_for_inactive_in_merger_forward_business(self):
        url = self.appointments_url(self.business.id)
        body = build_custappt_data_family_and_friends(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
            member_id=self.member.id,
        )
        body['dry_run'] = True
        partner = booksy_partner.make(merger_forward=True)
        partner_permission_business.make(business=self.business, partner=partner)
        assert self.business.partnerpermissionbusiness_set.filter(
            partner__merger_forward=True
        ).exists()
        resp = self.fetch(url, method='POST', body=body)
        self.assertEqual(resp.code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            resp.json['errors'][0]['description'],
            'Unable to book an appointment for Family & Friends members. '
            'This business does not support this feature.',
        )

    @override_settings(FAMILY_AND_FRIENDS_ENABLED=False)
    def test_error_if_family_and_friends_disabled(self):
        url = self.appointments_url(self.business.id)
        body = build_custappt_data_family_and_friends(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
            member_id=self.member.id,
        )

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_400_BAD_REQUEST

    def test_recurring_for_inactive(self):
        url = self.appointments_url(self.business.id)
        body = build_custappt_data_family_and_friends(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=True,
            member_id=self.member.id,
        )

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        family_and_friends = resp.json['appointment']['family_and_friends']
        assert family_and_friends['booked_for']['member_id'] == self.member.id
        assert family_and_friends['booked_for']['full_name'] == self.member.full_name
        assert family_and_friends['booked_by']['member_id'] == self.parent.id
        assert family_and_friends['booked_by']['full_name'] == self.parent.full_name

    def test_change_inactive_customer(self):
        url = self.appointments_url(self.business.id)
        _, member2 = self.create_inactive_member(parent=self.parent)
        body = build_custappt_data_family_and_friends(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
            member_id=self.member.id,
        )

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        family_and_friends = resp.json['appointment']['family_and_friends']
        assert family_and_friends['booked_for']['member_id'] == self.member.id
        assert family_and_friends['booked_for']['full_name'] == self.member.full_name
        assert family_and_friends['booked_by']['member_id'] == self.parent.id
        assert family_and_friends['booked_by']['full_name'] == self.parent.full_name

        appointment_id = resp.json['appointment']['appointment_uid']
        assert appointment_id
        url = self.change_appointments_url(appointment_id)
        body['book_for_family_member']['member'] = member2.id
        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == status.HTTP_200_OK
        assert appointment_id == resp.json['appointment']['appointment_uid']
        family_and_friends = resp.json['appointment']['family_and_friends']
        assert family_and_friends['booked_for']['member_id'] == member2.id
        assert family_and_friends['booked_for']['full_name'] == member2.full_name
        assert family_and_friends['booked_by']['member_id'] == self.parent.id
        assert family_and_friends['booked_by']['full_name'] == self.parent.full_name
        assert MemberAppointment.objects.filter(appointment__id=appointment_id).count() == 1
        member2_bci = member2.bcis.filter(business=self.business).first()
        assert member2_bci.appointments_booked_for_me.first().appointment.id == appointment_id

    def test_change_appointment_with_inactive_with_pending_invitation(self):
        url = self.appointments_url(self.business.id)
        body = build_custappt_data_family_and_friends(
            self.variant,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
            member_id=self.member.id,
        )

        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED

        invitation_recipe.make(member=self.member, parent=self.parent)
        self.member.email = '<EMAIL>'
        self.member.save()
        body['customer_note'] = 'dumdum'
        appointment_id = resp.json['appointment']['appointment_uid']
        url = self.change_appointments_url(appointment_id)
        resp = self.fetch(url, method='PUT', body=body)
        assert resp.code == status.HTTP_200_OK
        assert appointment_id == resp.json['appointment']['appointment_uid']
        family_and_friends = resp.json['appointment']['family_and_friends']
        assert family_and_friends['booked_for']['member_id'] == self.member.id
        assert family_and_friends['booked_for']['full_name'] == self.member.full_name
        assert family_and_friends['booked_by']['member_id'] == self.parent.id
        assert family_and_friends['booked_by']['full_name'] == self.parent.full_name
        assert MemberAppointment.objects.filter(appointment__id=appointment_id).count() == 1
        appointment = Appointment.objects.get(id=appointment_id)
        assert appointment.customer_email == self.user.email
        assert appointment.booked_for.user is None
        assert not appointment.booked_for.email

    def test_create_trusted_deposit(self):
        parent_bci, member_bci = self.create_inactive_member_bcis(
            business=self.business, parent=self.parent, member=self.member
        )
        parent_bci.trusted = True
        parent_bci.save()
        member_bci.trusted = True
        member_bci.save()

        body = build_custappt_data_family_and_friends(
            variant=self.get_deposit(),
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
            member_id=self.member.id,
        )

        # dry run for deposit needed
        body['dry_run'] = True
        url = self.appointments_url(self.business.id)
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '0.00'
        assert appointment_payment['prepayment_total'] == '0.00'

        # Without dry_run
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        assert resp.code == status.HTTP_201_CREATED
        assert resp.json['appointment']['business']['id'] == self.business.id
        assert len(resp.json['appointment']['subbookings']) == 1
        assert resp.json['appointment']['payment_info']['deposit_info'] is None

    def test_deposit_trusted_parent_and_not_trusted_member(self):
        parent_bci, member_bci = self.create_inactive_member_bcis(
            business=self.business, parent=self.parent, member=self.member
        )
        parent_bci.trusted = True
        parent_bci.save()
        member_bci.trusted = False
        member_bci.save()

        body = build_custappt_data_family_and_friends(
            variant=self.get_deposit(),
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
            member_id=self.member.id,
        )

        # dry run for deposit needed
        body['dry_run'] = True
        url = self.appointments_url(self.business.id)
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '17.30'
        assert appointment_payment['prepayment_total'] == '0.00'

        # Without dry_run
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        assert resp.code == status.HTTP_400_BAD_REQUEST

    def test_deposit_not_trusted_parent_and_trusted_member(self):
        parent_bci, member_bci = self.create_inactive_member_bcis(
            business=self.business, parent=self.parent, member=self.member
        )
        parent_bci.trusted = False
        parent_bci.save()
        member_bci.trusted = True
        member_bci.save()

        body = build_custappt_data_family_and_friends(
            variant=self.get_deposit(),
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
            member_id=self.member.id,
        )

        # dry run for deposit needed
        body['dry_run'] = True
        url = self.appointments_url(self.business.id)
        resp = self.fetch(url, method='POST', body=body)
        assert resp.code == status.HTTP_201_CREATED
        appointment_payment = resp.json['appointment_payment']
        assert appointment_payment['cancellation_fee_total'] == '17.30'
        assert appointment_payment['prepayment_total'] == '0.00'

        # Without dry_run
        body['dry_run'] = False
        resp = self.fetch(url, method='POST', body=body)

        assert resp.code == status.HTTP_400_BAD_REQUEST

    def _get_service_variant(self) -> ServiceVariant:
        variant = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )
        variant.add_staffers([self.staffer])
        return variant

    def get_deposit(self) -> ServiceVariant:
        with_deposit = self._get_service_variant()
        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=17.30,
        )
        return with_deposit

    def get_prepayment(self) -> ServiceVariant:
        with_prepayment = self._get_service_variant()
        baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=9.99,
        )
        return with_prepayment
