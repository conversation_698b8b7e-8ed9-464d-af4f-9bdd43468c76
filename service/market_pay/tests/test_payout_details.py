from decimal import Decimal

import pytest

from model_bakery import baker

from service.tests import BaseAsyncHTTPTest
from webapps.market_pay.models import AccountHolder, Payout
from webapps.pos.enums import PaymentTypeEnum, receipt_status
from webapps.pos.models import (
    PaymentType,
    PaymentRow,
    POS,
    Receipt,
    Transaction,
)


@pytest.mark.django_db
class PayoutDetailsTestCase(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{}/market_pay/payout/{}'

    def test_payout_details_response(self):
        pos = baker.make(
            POS,
            business=self.business,
            active=True,
            commissions_enabled=True,
        )
        account_holder = baker.make(
            AccountHolder,
            pos=pos,
        )
        payout = baker.make(
            Payout,
            account_holder=account_holder,
            amount=12.34,
            psp_reference='123456',
            payment_refs=['us-reference-123'],
        )

        transaction = baker.make(
            Transaction,
            pos=pos,
            total=Decimal('200'),
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        receipt = baker.make(
            Receipt,
            transaction=transaction,
            already_paid=Decimal('100'),
            status_code=receipt_status.PREPAYMENT_SUCCESS,
        )

        payment_type = baker.make(
            PaymentType,
            pos=pos,
            code=PaymentTypeEnum.PREPAYMENT,
        )
        baker.make(
            PaymentRow,
            receipt=receipt,
            payout_reference='123456',
            pnref='us-reference-123',
            payment_type=payment_type,
            marketpay_splits={
                'txn_fee': 10,
                'provision': 30,
            },
            amount=20,
            status=receipt_status.PREPAYMENT_SUCCESS,
        )

        resp = self.fetch(
            self.url.format(
                self.business.id,
                payout.id,
            ),
            method='GET',
        ).json

        payments = resp['payments']
        cash_flows = resp['cash_flows']
        payment_types = set(entry['payment_type'] for entry in payments)
        assert 'Deposits' in payment_types
        assert 'Processing Fee' in payment_types
        assert resp['transactions_count'] == 1
        assert payments[0]['payment_type_code'] == 'prepayments'
        assert payments[0]['total'] == '$20.00'
        assert payments[1]['payment_type_code'] == 'processing_fee'
        assert payments[1]['total'] == '-$0.40'

        assert cash_flows[0]['fund_transfer'] is None
        assert cash_flows[0]['payment_row']['amount_text'] == '$20.00'
        assert cash_flows[0]['payment_row']['amount'] == '20.00'
        assert cash_flows[0]['payment_row']['payment_type_code'] == 'prepayment'
        assert 'customer_info' in cash_flows[0]['payment_row']
        assert 'appointment_id' in cash_flows[0]['payment_row']
        assert cash_flows[0]['payment_row']['transaction_id'] == transaction.id
        assert cash_flows[0]['payment_row']['transaction_type'] == 'P'
        assert cash_flows[0]['payment_row']['short_status'] == 'prepayment'
        assert cash_flows[0]['payment_row']['short_status_label'] == 'Deposit'
