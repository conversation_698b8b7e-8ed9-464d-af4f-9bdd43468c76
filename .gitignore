*.swp
celerybeat-schedule
celerybeat-schedule.db
celeryd@*
celerybeat.pid
# xcode noise
*.mode1v3
*.pbxuser
*.perspective
*.perspectivev3
*.pyc
*~.nib/
build/*
*.xcodeproj/project.xcworkspace/xcuserdata/*
*.xcodeproj/xcuserdata/*
xcuserdata
*.o
*.soz

# PyCharm
.idea/*
events/.idea/
pytestdebug.log
.pytest_cache
.pytest_cache/*
__pycache__/

# Textmate - if you build your xcode projects with it
*.tm_build_errors

# old skool
.svn

# osx noise
.DS_Store

#dexy
docs/.dexy
docs/output-site

# cache
*.lock
.cache
.cache/*

full-swagger.json

devapps/*

config/admin.yaml

# media
media
.directory
junk/*


# coverage.py
htmlcov/
.coverage
/templates/booksy/trans/marketing.html
/templates/scenarios/messages.html
/templates/scenarios/messages_with_deeplinks.html
/templates/scenarios/messages_with_deeplinks_new.html

# ignore schema
/schema.sql

# ignore pre_generated_file
/statics/json/quick_report/*.json

# ignore pre_generated_file
# /data/umbrella_venues/
# ingore local certs
webapps/public_partners/data
# ignore instagram feeds
/data/feeds/instagram/*
/statics/sitemap.json
# ignore local certs
/data/elka/apm/apm_local.crt
# docker
.docker/esdata/
.docker/api/
.docker/pgdata/
.docker_volume/

notebooks/.ipynb_checkpoints
notebooks/*/.ipynb_checkpoints

webapps/feeds/google/go/server/.idea/
webapps/feeds/google/go/server/config.json
webapps/feeds/google/go/.idea/

# hypothesis cache
.hypothesis/

# secrets files
master.key
secrets.env
GOOGLE_APPLICATION_CREDENTIALS

# IntelliJ IDEA file
core.iml

# Pyenv
.python-version
.venv

# VSCode
.vscode/*

.env
