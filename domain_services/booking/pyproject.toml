[tool.poetry]
name = "booking"
version = "0.1.0"
description = ""
authors = [
    "<PERSON><PERSON><PERSON> <maciej.k<PERSON><PERSON><PERSON>@booksy.com>",
]
readme = "README.md"

[tool.poetry.dependencies]
python = "3.12.6"
bitarray = "2.9.3"
pydantic = "2.9.2"

[tool.poetry.group.dev.dependencies]
pytest = "8.3.4"
pre-commit = "4.2.0"
pylint = "3.3.4"


[tool.poetry.group.monolith.dependencies]
poetry-plugin-export = "1.8.0"
requirements-parser = "0.11.0"
setuptools = "69.5.1"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.pytest.ini_options]
# currently empty, but required to override root monolith value

[tool.isort]
src_paths = ["domain_services"]
line_length = 100
profile = "black"
include_trailing_comma = true
multi_line_output = 3
force_grid_wrap = 0
use_parentheses = true
