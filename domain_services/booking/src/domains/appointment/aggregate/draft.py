import uuid
from datetime import date, datetime, time
from typing import Any, Protocol

from webapps.booking.models import BookingSources
from webapps.user.models import User

from ...calendar.dto import StaffersAvailability
from ..dto.draft import Draft, DraftItem


class DraftAggregate(Protocol):
    NO_STAFFER_PREFERENCE: int = -1

    def create(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        business_id: int,
        booked_from: datetime,
        service_variant_id: int,
        user: User | None = None,
        fingerprint: str | None = None,
        user_agent: str | None = None,
        booking_source: BookingSources | None = None,
        staffer_id: int | None = None,
        **_: Any,
    ) -> Draft: ...

    def claim_draft(
        self,
        draft: Draft,
        user_id: int,
    ) -> Draft: ...

    def change_staffer(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        draft: Draft,
        draft_item_id: uuid.UUID,
        staffer_id: int,
        staffers_availability: StaffersAvailability,
        user: User | None = None,
        fingerprint: str | None = None,
        user_agent: str | None = None,
        booking_source: BookingSources | None = None,
        **_: Any,
    ) -> Draft: ...

    def change_time_slot(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        draft: Draft,
        slot_date: date,
        slot_time: time,
        user: User | None = None,
        fingerprint: str | None = None,
        user_agent: str | None = None,
        booking_source: BookingSources | None = None,
        **_: Any,
    ) -> Draft: ...

    def add_service(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        draft: Draft,
        service_variant_id: int,
        staffer_id: int | None,
        user: User | None = None,
        fingerprint: str | None = None,
        user_agent: str | None = None,
        booking_source: BookingSources | None = None,
        **_: Any,
    ) -> Draft: ...

    def add_customer_note(
        self,
        draft: Draft,
        customer_note: str,
        **_: Any,
    ) -> Draft: ...

    # TODO: hide when merged with calendar service or extract item aggregate
    def get_item(
        self,
        draft: Draft,
        draft_item_id: uuid.UUID,
    ) -> DraftItem: ...
