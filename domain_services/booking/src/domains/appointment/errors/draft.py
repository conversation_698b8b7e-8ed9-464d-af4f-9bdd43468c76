import uuid


class DraftNotFoundError(Exception):
    def __init__(self, draft_id: uuid.UUID) -> None:
        super().__init__(f"Draft {draft_id} not found")


class DraftItemNotFoundError(Exception):
    def __init__(self, draft_item_id: uuid.UUID) -> None:
        super().__init__(f"Draft item {draft_item_id} not found")


class DraftVersionMismatchError(Exception):
    def __init__(
        self,
        draft_id: uuid.UUID,
        version: uuid.UUID,
        requested_version: uuid.UUID,
    ) -> None:
        super().__init__(
            f"Draft {draft_id} version {version} "
            f"does not match requested version {requested_version}"
        )


class CreateDraftFailedError(Exception):
    def __init__(self, message: str) -> None:
        super().__init__(f"Creating draft failed: {message}")


class ChangeDraftStafferFailedError(Exception):
    def __init__(self, message: str) -> None:
        super().__init__(f"Changing draft's staffer failed: {message}")


class ChangeDraftStafferError(Exception):
    def __init__(self, draft_item_id: uuid.UUID, staffer_id: int) -> None:
        super().__init__("Staffer cannot be set")
        self.draft_item_id = draft_item_id
        self.staffer_id = staffer_id


class ChangeDraftStafferNoTimeSlotError(ChangeDraftStafferError): ...


class ChangeDraftStafferInvalidStafferError(ChangeDraftStafferError): ...


class ChangeDraftTimeslotFailedError(Exception):
    def __init__(self, message: str) -> None:
        super().__init__(f"Changing draft's timeslot failed: {message}")


class AddDraftServiceFailedError(Exception):
    def __init__(self, message: str) -> None:
        super().__init__(f"Adding service to draft failed: {message}")


class DraftBelongsToAnotherUserError(Exception):
    def __init__(self, draft_id: uuid.UUID) -> None:
        super().__init__(f'Draft {draft_id} belongs to another user')
