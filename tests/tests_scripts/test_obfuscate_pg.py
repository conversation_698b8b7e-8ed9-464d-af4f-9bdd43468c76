import os

from django.conf import settings
from django.contrib.sessions.models import Session
from django.db import connection, DEFAULT_DB_ALIAS
from django.db.models import Q
from django.test import TransactionTestCase
from mock import patch, MagicMock
from model_bakery import baker

from webapps import consts
from webapps.adyen.models import (
    Auth,
    Capture,
    Card,
    Cardholder,
    Disable,
    Notification,
    Refund as AdyenRefund,
)
from webapps.billing.models import (
    BillingCreditCardInfo,
    BillingCreditCardInfoHistory,
    BillingPaymentMethod,
)
from webapps.booking.baker_recipes import booking_recipe
from webapps.booking.enums import RepeatType
from webapps.booking.models import Appointment, BookingChange, BookingSources, RepeatingBooking
from webapps.braintree_app.models import BraintreeTransaction
from webapps.business.models import Business, Resource
from webapps.business.models.bci import BusinessCustomerInfo, DelayedBookmarkEvent
from webapps.business.models.business_change import BusinessChange
from webapps.notification.models import (
    NotificationHistory,
    NotificationSMSCodes,
    NotificationSchedule,
    Reciever,
)
from webapps.pos.models import (
    BankAccount,
    POS,
    PaymentMethod as POSPaymentMethod,
    Transaction,
)
from webapps.printer_api.models import PrinterSessionCache
from webapps.purchase.models import (
    Invoice,
    Subscription,
    SubscriptionListing,
    SubscriptionTransaction,
)
from webapps.stripe_app.models import (
    Charge,
    Customer as StripeCustomer,
    PaymentIntent,
    PaymentMethod as StripePaymentMethod,
    Refund as StripeRefund,
    SetupIntent,
)
from webapps.user.models import (
    EmailToken,
    User,
    UserSessionCache,
)

MAIL = '<EMAIL>'
PHONE = '*********'
BOOKING_SOURCES = (
    (consts.IPHONE, BookingSources.CUSTOMER_APP),
    (consts.IPHONE, BookingSources.BUSINESS_APP),
    (consts.WIDGET, BookingSources.CUSTOMER_APP),
    (consts.WIDGET, BookingSources.BUSINESS_APP),
    (consts.WEB, BookingSources.CUSTOMER_APP),
    (consts.WEB, BookingSources.BUSINESS_APP),
    (consts.INTERNAL, BookingSources.INTERNAL_APP),
    (consts.ANDROID, BookingSources.CUSTOMER_APP),
    (consts.ANDROID, BookingSources.BUSINESS_APP),
    (consts.IMPORTERPARP, BookingSources.BUSINESS_APP),
    (consts.FACEBOOK, BookingSources.CUSTOMER_APP),
    (consts.INSTAGRAM, BookingSources.CUSTOMER_APP),
    (consts.LAVITO, BookingSources.BUSINESS_APP),
    (consts.LAVITO, BookingSources.CUSTOMER_APP),
    (consts.LAVITO, BookingSources.INTERNAL_APP),
    (consts.DWIT, BookingSources.BUSINESS_APP),
    (consts.LANDING_PAGE, BookingSources.BUSINESS_APP),
    (consts.LANDING_PAGE, BookingSources.CUSTOMER_APP),
    (consts.PERFORMANCE_TEST, BookingSources.BUSINESS_APP),
    (consts.PERFORMANCE_TEST, BookingSources.CUSTOMER_APP),
)
BUSINESS_PAYMENT_SOURCES = [
    Business.PaymentSource.BRAINTREE,
    Business.PaymentSource.ITUNES,
    Business.PaymentSource.PLAY,
]


class BaseTestObfuscate(TransactionTestCase):
    # pylint: disable=too-many-statements
    databases = {DEFAULT_DB_ALIAS, "payments-database"}

    @staticmethod
    @patch('webapps.purchase.models.base.AppleGoogleSubscriptionEventMessage', MagicMock())
    def setUp():
        for index, booking_source in enumerate(BOOKING_SOURCES):
            baker.make(
                BookingSources,
                api_key=f'TEST_API_KEY_{index}',
                name=booking_source[0],
                app_type=booking_source[1],
            )
        booking_source = BookingSources.objects.first()

        session = baker.make(Session)

        baker.make(UserSessionCache, session=session)

        baker.make(PrinterSessionCache)

        baker.make(
            NotificationSchedule,
            parameters={
                "appointment_id": 2215415,
                "skip_email": MAIL,
                "scenario_name": "booking_changed",
            },
        )

        baker.make(NotificationHistory, recipient_phone=PHONE, recipient_email=MAIL)

        baker.make(NotificationSMSCodes)

        baker.make(POSPaymentMethod)

        baker.make(BankAccount)

        baker.make(Transaction, customer_data=f'Name Surname, {PHONE}, {MAIL}')

        baker.make(POS, business=baker.make(Business))

        cardholder = baker.make(Cardholder)

        card = baker.make(Card, cardholder=cardholder)

        baker.make(Disable, card=card)

        auth = baker.make(Auth, card=card)

        baker.make(Capture, auth=auth)

        baker.make(AdyenRefund, auth=auth)

        baker.make(Notification)

        with patch('webapps.purchase.tasks.brain_tree.BraintreeFetchPlansTask.delay'):
            baker.make(SubscriptionListing, braintree_id='braintree_id')
        baker.make(SubscriptionListing, apple_id='apple_id')
        baker.make(SubscriptionListing, google_id='google_id')

        with patch('webapps.purchase.tasks.segment.SegmentStatusChange.delay'):
            for source in Business.PaymentSource:
                baker.make(Subscription, source=source)

        for payment_source in BUSINESS_PAYMENT_SOURCES:
            baker.make(
                Business,
                payment_source=payment_source,
                phone=PHONE,
                invoice_address='Totally Valid Adress, 10',
                invoice_email=MAIL,
                alert_phone=PHONE,
                integrations={'key1': 'value1', 'key2': 'value2', 'key3': 'value3'},
                source_email=MAIL,
            )

        transaction = baker.make(SubscriptionTransaction)

        baker.make(Invoice, transaction=transaction)

        baker.make(
            User,
            email=MAIL,
            cell_phone=PHONE,
            home_phone=PHONE,
            work_phone=PHONE,
            facebook_id='fb_id',
        )

        baker.make(BusinessCustomerInfo, email=MAIL, cell_phone=PHONE)

        appointment = baker.make(
            Appointment, customer_email=MAIL, customer_phone=PHONE, source=booking_source
        )

        resource = baker.make(Resource, staff_email=MAIL, staff_cell_phone=PHONE)

        baker.make(Reciever, resources=[resource])

        for status in RepeatType:
            baker.make(RepeatingBooking, repeat=status)

        stripe_customer = baker.make(StripeCustomer)

        setup_intent = baker.make(SetupIntent, customer=stripe_customer)

        payment_method = baker.make(
            StripePaymentMethod, customer=stripe_customer, setup_intent=setup_intent
        )

        payment_intent = baker.make(
            PaymentIntent, customer=stripe_customer, payment_method=payment_method
        )

        charge = baker.make(
            Charge,
            customer=stripe_customer,
            payment_method=payment_method,
            payment_intent=payment_intent,
        )

        baker.make(StripeRefund, payment_intent=payment_intent, charge=charge)

        baker.make(BraintreeTransaction)

        baker.make(BillingCreditCardInfoHistory)

        baker.make(
            BillingCreditCardInfo,
            cardholder_name="Name Surname",
            first_6_digits='000000',
            last_4_digits='0000',
            address_line_1="Adress Line 1",
            address_line_2="Adress Line 2",
            city="City",
            zipcode="ZIP-CODE",
            country="COUNTRY",
        )

        baker.make(BillingPaymentMethod, token='00000')

        subbooking = booking_recipe.prepare(appointment=appointment)
        subbooking.save(override=True)
        baker.make(
            BookingChange,
            appointment_id=appointment.id,
            subbooking_id=subbooking.id,
            customer_phone=PHONE,
            customer_email=MAIL,
            changed_user_email=MAIL,
        )

        baker.make(BusinessChange)

        baker.make(DelayedBookmarkEvent)

        baker.make(EmailToken)

    @staticmethod
    def test_obfuscate():
        with open(
            os.path.join(settings.PROJECT_PATH, 'cliapps', 'dangerous', 'obfuscate_db.sql'),
            encoding="UTF-8",
        ) as file:
            obf_sql = file.read()
        with connection.cursor() as cursor:
            cursor.execute(obf_sql)

        assert BookingSources.objects.filter(api_key__contains='TEST_API_KEY_').count() == 0
        assert UserSessionCache.objects.count() == 0
        assert Session.objects.count() == 0
        assert PrinterSessionCache.objects.count() == 0
        assert NotificationSchedule.objects.filter(parameters__icontains=MAIL).count() == 0
        assert (
            NotificationHistory.objects.filter(
                Q(recipient_phone=PHONE) | Q(recipient_email=MAIL)
            ).count()
            == 0
        )
        assert NotificationSMSCodes.objects.count() == 0
        assert POSPaymentMethod.objects.count() == 0
        assert BankAccount.objects.count() == 0
        assert (
            Transaction.objects.filter(customer_data=f'Name Surname, {PHONE}, {MAIL}').count() == 0
        )
        assert Cardholder.objects.count() == 0
        assert Card.all_objects.count() == 0
        assert Disable.objects.count() == 0
        assert Auth.objects.count() == 0
        assert Capture.objects.count() == 0
        assert AdyenRefund.objects.count() == 0
        assert Notification.objects.count() == 0
        assert (
            SubscriptionListing.objects.filter(
                Q(apple_id='apple_id') | Q(braintree_id='braintree_id') | Q(google_id='google_id')
            ).count()
            == 0
        )
        assert Subscription.objects.exclude(source=Business.PaymentSource.OFFLINE).count() == 0
        assert (
            Business.objects.filter(
                Q(payment_source__in=BUSINESS_PAYMENT_SOURCES)
                | Q(phone=PHONE)
                | Q(invoice_address='Totally Valid Adress, 10')
                | Q(invoice_email=MAIL)
                | Q(alert_phone=PHONE)
                | Q(integrations={'key1': 'value1', 'key2': 'value2', 'key3': 'value3'})
                | Q(source_email=MAIL)
            ).count()
            == 0
        )
        assert SubscriptionTransaction.objects.count() == 0
        assert Invoice.objects.count() == 0
        assert (
            User.objects.filter(
                Q(email=MAIL)
                | Q(cell_phone=PHONE)
                | Q(home_phone=PHONE)
                | Q(work_phone=PHONE)
                | Q(facebook_id='fb_id')
            ).count()
            == 0
        )
        assert BusinessCustomerInfo.objects.filter(Q(email=MAIL) | Q(cell_phone=PHONE)).count() == 0
        assert (
            Appointment.objects.filter(Q(customer_email=MAIL) | Q(customer_phone=PHONE)).count()
            == 0
        )
        assert Resource.objects.filter(Q(staff_email=MAIL) | Q(staff_cell_phone=PHONE)).count() == 0
        assert Reciever.objects.count() == 0
        assert Reciever.resources.through.objects.count() == 0
        assert RepeatingBooking.objects.exclude(repeat=RepeatType.CUSTOM).count() == 0
        assert StripeCustomer.objects.count() == 0
        assert SetupIntent.objects.count() == 0
        assert StripePaymentMethod.objects.count() == 0
        assert PaymentIntent.objects.count() == 0
        assert Charge.objects.count() == 0
        assert StripeRefund.objects.count() == 0
        assert BraintreeTransaction.objects.count() == 0
        assert BillingCreditCardInfoHistory.objects.count() == 0
        assert (
            BillingCreditCardInfo.objects.filter(
                Q(cardholder_name="Name Surname")
                | Q(first_6_digits='000000')
                | Q(last_4_digits='0000')
                | Q(address_line_1="Adress Line 1")
                | Q(address_line_2="Adress Line 2")
                | Q(city="City")
                | Q(zipcode="ZIP-CODE")
                | Q(country="COUNTRY")
            ).count()
            == 0
        )
        assert BillingPaymentMethod.objects.filter(token='00000').count() == 0
        assert (
            BookingChange.objects.filter(
                Q(customer_phone=PHONE) | Q(customer_email=MAIL) | Q(changed_user_email=MAIL)
            ).count()
            == 0
        )
        assert BusinessChange.objects.count() == 0
        assert DelayedBookmarkEvent.objects.count() == 0
        assert EmailToken.objects.count() == 0
