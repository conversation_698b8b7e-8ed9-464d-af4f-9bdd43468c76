[importlinter]
root_packages=
    webapps
    v2
# Optional:
include_external_packages = True

[importlinter:contract:check_imports_payment_gateway]
name = Check imports of payment_gateway
type = forbidden
source_modules =
    webapps
forbidden_modules =
    webapps.payment_gateway
ignore_imports =
    # allow importing ports
    webapps.** -> webapps.payment_gateway.ports

    # allow importing internally
    webapps.payment_gateway.** -> webapps.payment_gateway.**
    webapps.payments.views.** -> webapps.payment_gateway.**

    # whitelist (will be fixed successively) - DO NOT ADD NEW IGNORES
    # write your code without importing anything from this app
    webapps.admin_extra.tests.payment_providers.test_stripe_transfer_funds -> webapps.payment_gateway.models
    webapps.adyen.tests.test_notifications -> webapps.payment_gateway.models
    webapps.business.tests.test_service_promotions_CD_FS_HH_LM -> webapps.payment_gateway.scripts
    webapps.payment_providers.actions -> webapps.payment_gateway.models
    webapps.payment_providers.management.commands.tests.test_validate_cards_using_payment_history -> webapps.payment_gateway.models
    webapps.payment_providers.scripts -> webapps.payment_gateway.models
    webapps.payment_providers.tasks -> webapps.payment_gateway.models
    webapps.payment_providers.tests.stripe.test_stripe_scripts -> webapps.payment_gateway.models
    webapps.payment_providers.tests.stripe.test_stripe_tokenized_pm -> webapps.payment_gateway.models
    webapps.payment_providers.tests.stripe.test_synchronize_stripe_objects -> webapps.payment_gateway.models
    webapps.payment_providers.tests.test_common_tokenized_pm -> webapps.payment_gateway.models
    webapps.payment_providers.tests.test_tasks -> webapps.payment_gateway.models
    webapps.payment_providers.tests.test_tokenized_payment_method_warning_circles -> webapps.payment_gateway.models
    webapps.payment_providers.management.commands.create_pos_pricing_stripe_for_mobile -> webapps.payment_gateway.models
    webapps.payments.actions -> webapps.payment_gateway.models
    webapps.payments.tests.test_notifications -> webapps.payment_gateway.models
    webapps.pos.actions -> webapps.payment_gateway.models
    webapps.pos.serializers -> webapps.payment_gateway.consts
    webapps.pos.services -> webapps.payment_gateway.models
    webapps.pos.tests -> webapps.payment_gateway.scripts
    webapps.pos.tests.pos_refactor -> webapps.payment_gateway.models
    webapps.pos.tests.pos_refactor -> webapps.payment_gateway.scripts
    webapps.pos.tests.pos_refactor.bcr.base -> webapps.payment_gateway.models
    webapps.pos.tests.pos_refactor.bcr.test_stripe_stage_2 -> webapps.payment_gateway.models
    webapps.pos.tests.pos_refactor.cf.base -> webapps.payment_gateway.models
    webapps.pos.tests.pos_refactor.blik.base -> webapps.payment_gateway.models
    webapps.pos.tests.pos_refactor.cf.test_stripe_stage_2 -> webapps.payment_gateway.models
    webapps.pos.tests.pos_refactor.gift_card_code.test_booksy_gift_card -> webapps.payment_gateway.models
    webapps.pos.tests.pos_refactor.pba.base -> webapps.payment_gateway.models
    webapps.pos.tests.pos_refactor.prepayment.test_stripe_stage_2 -> webapps.payment_gateway.models
    webapps.pos.tests.pos_refactor.prepayment.base -> webapps.payment_gateway.models
    webapps.pos.tests.pos_refactor.keyed_in_payment.base -> webapps.payment_gateway.models
    webapps.pos.tests.pos_refactor.keyed_in_payment.test_stripe_stage_2 -> webapps.payment_gateway.models
    webapps.pos.tests.pos_refactor.split.base -> webapps.payment_gateway.models
    webapps.pos.tests.pos_refactor.test_cash -> webapps.payment_gateway.models
    webapps.pos.tests.pos_refactor.test_paying_offline -> webapps.payment_gateway.models
    webapps.pos.tests.pos_refactor.test_square -> webapps.payment_gateway.models
    webapps.pos.tests.pos_refactor.test_various -> webapps.payment_gateway.models
    webapps.pos.tests.test_actions -> webapps.payment_gateway.models
    webapps.pos.tests.test_update_transaction_with_booking -> webapps.payment_gateway.scripts
    webapps.pos.tools -> webapps.payment_gateway.consts
    webapps.pos.tools -> webapps.payment_gateway.models
    webapps.pos.tools -> webapps.payment_gateway.services.wallet
    webapps.stripe_integration.actions -> webapps.payment_gateway.models
    webapps.stripe_integration.provider -> webapps.payment_gateway.models
    webapps.stripe_integration.provider -> webapps.payment_gateway.services.wallet
    webapps.stripe_integration.services -> webapps.payment_gateway.models
    webapps.stripe_integration.tests.test_synchronize_stripe_customer -> webapps.payment_gateway.models
    # DO NOT ADD NEW IGNORES - write your code without importing anything from this app

allow_indirect_imports = true


[importlinter:contract:check_imports_point_of_sale]
name = Check imports of point_of_sale
type = forbidden
source_modules =
    webapps
forbidden_modules =
    webapps.point_of_sale
ignore_imports =
    # allow importing ports
    webapps.** -> webapps.point_of_sale.ports

    # allow importing internally
    webapps.point_of_sale.** -> webapps.point_of_sale.**

    # whitelist (will be fixed successively) - DO NOT ADD NEW IGNORES
    # write your code without importing anything from this app
    webapps.french_certification.actions -> webapps.point_of_sale.models
    webapps.french_certification.filters -> webapps.point_of_sale.models
    webapps.french_certification.serializers -> webapps.point_of_sale.models
    webapps.french_certification.services.fiscal_receipt -> webapps.point_of_sale.models
    webapps.french_certification.tests.common -> webapps.point_of_sale.models
    webapps.french_certification.tests.subscribers.test_loyalty_program -> webapps.point_of_sale.models
    webapps.french_certification.tests.test_actions -> webapps.point_of_sale.models
    webapps.french_certification.tests.test_fiscal_receipt_payments -> webapps.point_of_sale.models
    webapps.french_certification.tests.test_fiscal_receipt_payments -> webapps.point_of_sale.services.basket_payment
    webapps.french_certification.tests.test_fiscal_receipt -> webapps.point_of_sale.models
    webapps.french_certification.tests.test_integrations.test_utils -> webapps.point_of_sale.models
    webapps.french_certification.tests.test_models -> webapps.point_of_sale.models
    webapps.french_certification.tests.test_serializers -> webapps.point_of_sale.models
    webapps.french_certification.services.tests.test_close_period -> webapps.point_of_sale.models
    webapps.french_certification.services.tests.test_fiscal_receipt -> webapps.point_of_sale.models
    webapps.french_certification.services.tests.test_integrity -> webapps.point_of_sale.models
    webapps.french_certification.services.tests.test_jet -> webapps.point_of_sale.models
    webapps.french_certification.services.tests.test_treasury_operation -> webapps.point_of_sale.models
    webapps.french_certification.tests.test_tasks -> webapps.point_of_sale.models
    webapps.french_certification.tests.test_views -> webapps.point_of_sale.models
    webapps.french_certification.utils -> webapps.point_of_sale.models
    webapps.invoicing.tests.test_ports -> webapps.point_of_sale.models
    webapps.payments.tests.end_to_end.test_make_payment -> webapps.point_of_sale.models
    webapps.payments.tests.test_payment_views -> webapps.point_of_sale.models
    webapps.pos.provider.proxy -> webapps.point_of_sale.adapters
    webapps.pos.provider.proxy -> webapps.point_of_sale.exceptions
    webapps.pos.provider.proxy -> webapps.point_of_sale.models
    webapps.pos.provider.proxy -> webapps.point_of_sale.services.basket_payment
    webapps.pos.provider.proxy -> webapps.point_of_sale.services.basket_payment_analytics
    webapps.pos.provider.proxy -> webapps.point_of_sale.services.cancellation_fee_auth
    webapps.pos.provider.proxy -> webapps.point_of_sale.services.cancellation_fee_auth_analytics
    webapps.pos.refund -> webapps.point_of_sale.exceptions
    webapps.pos.refund -> webapps.point_of_sale.models
    webapps.pos.refund -> webapps.point_of_sale.services.basket_payment
    webapps.pos.serializers -> webapps.point_of_sale.services.basket
    webapps.pos.serializers -> webapps.point_of_sale.services.cancellation_fee_auth
    webapps.pos.services -> webapps.point_of_sale.exceptions
    webapps.pos.services -> webapps.point_of_sale.models
    webapps.pos.services -> webapps.point_of_sale.services.basket
    webapps.pos.services -> webapps.point_of_sale.services.basket_item
    webapps.pos.services -> webapps.point_of_sale.services.basket_payment
    webapps.pos.services -> webapps.point_of_sale.services.basket_tip
    webapps.pos.services -> webapps.point_of_sale.services.cancellation_fee_auth
    webapps.pos.scripts -> webapps.point_of_sale.models
    webapps.pos.tasks -> webapps.point_of_sale.models
    webapps.pos.tasks -> webapps.point_of_sale.services.basket
    webapps.pos.tasks -> webapps.point_of_sale.services.basket_payment_analytics
    webapps.pos.tests.pos_refactor -> webapps.point_of_sale.models
    webapps.pos.tests.pos_refactor.bcr.base -> webapps.point_of_sale.models
    webapps.pos.tests.pos_refactor.bcr.test_stripe_stage_2 -> webapps.point_of_sale.models
    webapps.pos.tests.pos_refactor.blik.base -> webapps.point_of_sale.models
    webapps.pos.tests.pos_refactor.cf.base -> webapps.point_of_sale.models
    webapps.pos.tests.pos_refactor.gift_card_code.test_booksy_gift_card -> webapps.point_of_sale.models
    webapps.pos.tests.pos_refactor.pba.base -> webapps.point_of_sale.models
    webapps.pos.tests.pos_refactor.prepayment.base -> webapps.point_of_sale.models
    webapps.pos.tests.pos_refactor.keyed_in_payment.base -> webapps.point_of_sale.models
    webapps.pos.tests.pos_refactor.keyed_in_payment.test_stripe_stage_2 -> webapps.point_of_sale.models
    webapps.pos.tests.pos_refactor.split.base -> webapps.point_of_sale.models
    webapps.pos.tests.pos_refactor.test_cash -> webapps.point_of_sale.models
    webapps.pos.tests.pos_refactor.test_paying_offline -> webapps.point_of_sale.models
    webapps.pos.tests.pos_refactor.test_proxy_provider -> webapps.point_of_sale.models
    webapps.pos.tests.pos_refactor.test_selling_things -> webapps.point_of_sale.models
    webapps.pos.tests.pos_refactor.test_services -> webapps.point_of_sale.models
    webapps.pos.tests.pos_refactor.test_square -> webapps.point_of_sale.models
    webapps.pos.tests.pos_refactor.test_tips -> webapps.point_of_sale.models
    webapps.pos.tests.pos_refactor.test_various -> webapps.point_of_sale.models
    webapps.pos.tests.test_actions -> webapps.point_of_sale.models
    webapps.pos.tests.test_scripts -> webapps.point_of_sale.models
    webapps.stats_and_reports.reports.sales_reports.tests.test_sales_log -> webapps.point_of_sale.models
    webapps.stats_and_reports.reports.sales_reports.tests.test_sales_summary -> webapps.point_of_sale.models
    webapps.stripe_integration.provider -> webapps.point_of_sale.adapters
    webapps.stripe_integration.provider -> webapps.point_of_sale.models
    webapps.stripe_integration.services -> webapps.point_of_sale.models
    # DO NOT ADD NEW IGNORES - write your code without importing anything from this app

allow_indirect_imports = true


[importlinter:contract:check_imports_payment_providers]
name = Check imports of payment_providers
type = forbidden
source_modules =
    webapps
forbidden_modules =
    webapps.payment_providers
ignore_imports =
    # allow importing ports
    webapps.** -> webapps.payment_providers.ports.**

    # allow importing internally
    webapps.payment_providers.** -> webapps.payment_providers.**

    # whitelist (will be fixed successively) - DO NOT ADD NEW IGNORES
    # write your code without importing anything from this app
    webapps.adyen.tests.test_notifications -> webapps.payment_providers.models
    webapps.business_calendar.views.tests.test_banners -> webapps.payment_providers.models
    webapps.payment_gateway.scripts -> webapps.payment_providers.models
    webapps.payment_gateway.services.fee -> webapps.payment_providers.consts.adyen
    webapps.payment_gateway.tests.grpc.test_booksy_payment_for_gift_card -> webapps.payment_providers.models
    webapps.payment_gateway.tests.services.test_fee -> webapps.payment_providers.consts.adyen
    webapps.payment_gateway.tests.services.test_fee -> webapps.payment_providers.services.stripe.stripe
    webapps.payment_gateway.tests.test_scripts -> webapps.payment_providers.models
    webapps.payments.actions -> webapps.payment_providers.models
    webapps.payments.notifications -> webapps.payment_providers.models
    webapps.payments.tests.test_account_holder_views -> webapps.payment_providers.models
    webapps.payments.tests.test_account_management_views -> webapps.payment_providers.tests.helpers
    webapps.payments.tests.test_otp -> webapps.payment_providers.tests.helpers
    webapps.payments.tests.test_notifications -> webapps.payment_providers.models
    webapps.payments.tests.test_tokenized_pm_views -> webapps.payment_providers.models
    webapps.payments.views.account_management -> webapps.payment_providers.exceptions.common
    webapps.payments.views.tests.test_customer_check_balance_transaction_status -> webapps.payment_providers.models
    webapps.payments.tests.end_to_end.test_make_payment -> webapps.payment_providers.models
    webapps.payments.tests.test_payment_views -> webapps.payment_providers.models
    webapps.pos.actions -> webapps.payment_providers.models
    webapps.pos.services -> webapps.payment_providers.models
    webapps.pos.tests.pos_refactor -> webapps.payment_providers.models
    webapps.pos.tests.pos_refactor.bcr.base -> webapps.payment_providers.consts.stripe
    webapps.pos.tests.pos_refactor.bcr.base -> webapps.payment_providers.models
    webapps.pos.tests.pos_refactor.bcr.test_stripe_stage_2 -> webapps.payment_providers.consts.stripe
    webapps.pos.tests.pos_refactor.bcr.test_stripe_stage_2 -> webapps.payment_providers.consts.stripe_error_code
    webapps.pos.tests.pos_refactor.bcr.test_stripe_stage_2 -> webapps.payment_providers.models
    webapps.pos.tests.pos_refactor.blik.test_stripe_stage_2 -> webapps.payment_providers.consts.stripe_error_code
    webapps.pos.tests.pos_refactor.blik.base -> webapps.payment_providers.models
    webapps.pos.tests.pos_refactor.cf.test_stripe_stage_2 -> webapps.payment_providers.models
    webapps.pos.tests.pos_refactor.cf.test_stripe_stage_2 -> webapps.payment_providers.consts.stripe
    webapps.pos.tests.pos_refactor.gift_card_code.test_booksy_gift_card -> webapps.payment_providers.models
    webapps.pos.tests.pos_refactor.helpers_adyen -> webapps.payment_providers.consts.adyen
    webapps.pos.tests.pos_refactor.helpers_adyen -> webapps.payment_providers.models
    webapps.pos.tests.pos_refactor.helpers_adyen -> webapps.payment_providers.services.notification
    webapps.pos.tests.pos_refactor.helpers_stripe -> webapps.payment_providers.models
    webapps.pos.tests.pos_refactor.helpers_stripe -> webapps.payment_providers.consts.stripe
    webapps.pos.tests.pos_refactor.pba.test_stripe_stage_2 -> webapps.payment_providers.consts.stripe_error_code
    webapps.pos.tests.pos_refactor.pba.test_stripe_stage_2 -> webapps.payment_providers.models
    webapps.pos.tests.pos_refactor.pba.test_stripe_stage_2 -> webapps.payment_providers.services.stripe.stripe
    webapps.pos.tests.pos_refactor.pba.test_stripe_stage_2 -> webapps.payment_providers.consts.stripe
    webapps.pos.tests.pos_refactor.prepayment.base -> webapps.payment_providers.consts.stripe
    webapps.pos.tests.pos_refactor.prepayment.base -> webapps.payment_providers.models
    webapps.pos.tests.pos_refactor.prepayment.test_stripe_stage_2 -> webapps.payment_providers.consts.stripe_error_code
    webapps.pos.tests.pos_refactor.prepayment.test_stripe_stage_2 -> webapps.payment_providers.models
    webapps.pos.tests.pos_refactor.prepayment.test_stripe_stage_2 -> webapps.payment_providers.consts.stripe
    webapps.pos.tests.pos_refactor.keyed_in_payment.base -> webapps.payment_providers.consts.stripe
    webapps.pos.tests.pos_refactor.keyed_in_payment.base -> webapps.payment_providers.models
    webapps.pos.tests.pos_refactor.keyed_in_payment.test_business_appoitment_keyed_in_payment -> webapps.payment_providers.models
    webapps.pos.tests.pos_refactor.keyed_in_payment.test_checkout_keyed_in_payment -> webapps.payment_providers.models
    webapps.pos.tests.pos_refactor.keyed_in_payment.test_stripe_stage_2 -> webapps.payment_providers.consts.stripe
    webapps.pos.tests.pos_refactor.keyed_in_payment.test_stripe_stage_2 -> webapps.payment_providers.consts.stripe_error_code
    webapps.pos.tests.pos_refactor.keyed_in_payment.test_stripe_stage_2 -> webapps.payment_providers.models
    webapps.pos.tests.pos_refactor.split.test_stripe_stage_2 -> webapps.payment_providers.consts.stripe
    webapps.stripe_integration.actions -> webapps.payment_providers.models
    webapps.stripe_integration.provider -> webapps.payment_providers.models
    webapps.stripe_integration.tests.test_connect_webhooks -> webapps.payment_providers.models
    webapps.stripe_integration.tests.test_synchronize_stripe_customer -> webapps.payment_providers.models
    webapps.stripe_integration.tests.test_tools -> webapps.payment_providers.tests.stripe.sample_stripe_notifications
    webapps.stripe_integration.tools -> webapps.payment_providers.models
    webapps.stripe_integration.views -> webapps.payment_providers.consts.stripe
    webapps.stripe_integration.views -> webapps.payment_providers.models.stripe
    webapps.stripe_integration.views -> webapps.payment_providers.views.webhooks.stripe
    webapps.stripe_integration.services -> webapps.payment_providers.models
    # DO NOT ADD NEW IGNORES - write your code without importing anything from this app

allow_indirect_imports = true


[importlinter:contract:check_imports_business_consents]
name = Check imports of business_consents app
type = forbidden
source_modules =
    webapps
forbidden_modules =
    webapps.business_consents
ignore_imports =
    # allow importing internally
    webapps.business_consents.** -> webapps.business_consents.**
    # allow importing ports
    webapps.** -> webapps.business_consents.ports
    webapps.** -> webapps.business_consents.enums
    webapps.** -> webapps.business_consents.entities

    webapps.business_calendar.views.tests.test_banners -> webapps.business_consents.models

allow_indirect_imports = true

[importlinter:contract:onboarding_space_api]
name = Check imports of onboarding_space app - import only from public.py
type = forbidden
source_modules =
    webapps
forbidden_modules =
    webapps.onboarding_space.**
ignore_imports =
    webapps.onboarding_space.** -> webapps.onboarding_space.**
    webapps.** -> webapps.onboarding_space.public

allow_indirect_imports = true


[importlinter:contract:v2_shared_hexagonal]
name = Check imports of v2.shared.hexagonal module - import only from public modules
type = forbidden
source_modules =
    booksy
    cliapps
    country_config
    debug_tools
    drf_api_utils
    drf_api
    grpc_api
    lib_drf
    lib
    merger_grpc
    monitoring
    service
    settings
    webapps
forbidden_modules =
    v2.shared.hexagonal
ignore_imports =
    ** -> v2.shared.hexagonal.adapters
    ** -> v2.shared.hexagonal.mocks
    ** -> v2.shared.hexagonal.ports
allow_indirect_imports = false


[importlinter:contract:google_business_profile-layers]
name = GBP layers
type = layers
layers =
    webapps.google_business_profile.infrastructure : webapps.google_business_profile.interfaces : webapps.google_business_profile.presentation : webapps.google_business_profile.containers
    webapps.google_business_profile.application
    webapps.google_business_profile.domain
