from booksy_proto_invoicing.grpc.v1 import merchant_pb2_grpc
from grpc_health.v1 import health_pb2_grpc
from grpc_health.v1.health import HealthServicer

from booksy.booksy_auth_interface_pb2_grpc import add_BooksyAuthServicer_to_server
from grpc_api.interceptors.server import MonitoringInterceptor
from webapps.booksy_gift_cards.notifications.api.grpc import (
    notifications_pb2_grpc as bgc_notifications_pb2_grpc,
    notifications_servicer as bgc_notifications_servicer,
)
from webapps.booksy_gift_cards.pdf_generator.grpc import (
    pdf_generator_servicer as bgc_pdf_generator_servicer,
)
from webapps.booksy_gift_cards.pdf_generator.grpc.protobufs import (
    pdf_generator_pb2_grpc as bgc_pdf_generator_pb2_grpc,
)
from webapps.business.protobuf.core import (
    business_details_pb2_grpc,
    ecommerce_business_details_pb2_grpc,
    loyalty_program_pb2_grpc,
)
from webapps.business.servicers import business_details, loyalty_program, ecommerce_business_details
from webapps.navision.servicers import merchant
from webapps.payment_gateway.protobuf.core import (
    booksy_payment_pb2_grpc,
    booksy_gift_card_payment_pb2_grpc,
)
from webapps.payment_gateway.servicers import booksy_payment, booksy_gift_card
from webapps.session.protobuf.core import check_session_pb2_grpc
from webapps.session.protobuf.core import get_business_user_session_info_pb2_grpc
from webapps.session.protobuf.core import get_user_access_level_info_pb2_grpc
from webapps.session.servicers import check_session
from webapps.session.servicers import get_business_user_session_info
from webapps.session.servicers import get_user_access_level_info
from webapps.session.servicers.booksy_auth_interface import BooksyAuthInterface
from webapps.user.protobuf.core import user_details_pb2_grpc
from webapps.user.servicers import user_details
from webapps.booksy_gift_cards.appointment_related_info.protobufs import (
    gift_card_appointment_related_info_pb2_grpc,
)
from webapps.booksy_gift_cards.appointment_related_info.servicer import gift_card_related_info
from webapps.booksy_gift_cards.bgc_business_status.protobufs import bgc_business_status_pb2_grpc
from webapps.booksy_gift_cards.bgc_business_status.servicer import bgc_business_status

urlpatterns = []


def grpc_handlers(server):
    if hasattr(server, '_state'):
        MonitoringInterceptor.get_active_rpc_getter(
            lambda: server._state.active_rpc_count  # pylint: disable=protected-access
        )

    check_session_pb2_grpc.add_CheckSessionExistsServicer_to_server(
        check_session.CheckUserSessionExistsServicer(),
        server,
    )
    get_business_user_session_info_pb2_grpc.add_GetBusinessUserSessionInfoServicer_to_server(
        get_business_user_session_info.GetBusinessUserSessionInfoService(),
        server,
    )
    business_details_pb2_grpc.add_BusinessDetailsServicer_to_server(
        business_details.BusinessDetailsReaderService(),
        server,
    )
    merchant_pb2_grpc.add_MerchantServiceServicer_to_server(
        merchant.MerchantServiceServicer(),
        server,
    )
    health_pb2_grpc.add_HealthServicer_to_server(HealthServicer(), server)
    loyalty_program_pb2_grpc.add_LoyaltyProgramServicer_to_server(
        loyalty_program.LoyaltyProgramServicer(),
        server,
    )
    user_details_pb2_grpc.add_UserDetailsServicer_to_server(
        user_details.UserDetailsService(), server
    )
    booksy_payment_pb2_grpc.add_BooksyPaymentServicer_to_server(
        booksy_payment.BooksyPaymentServicer(), server
    )
    ecommerce_business_details_pb2_grpc.add_EcommerceBusinessDetailsServicer_to_server(
        ecommerce_business_details.EcommerceBusinessDetailsServicer(), server
    )
    add_BooksyAuthServicer_to_server(BooksyAuthInterface(), server)
    bgc_notifications_pb2_grpc.add_NotificationsServicer_to_server(
        bgc_notifications_servicer.NotificationsServicer(), server
    )
    bgc_pdf_generator_pb2_grpc.add_PDFGeneratorServicer_to_server(
        bgc_pdf_generator_servicer.PDFGeneratorService(), server
    )
    booksy_gift_card_payment_pb2_grpc.add_PaymentDetailsServicer_to_server(
        booksy_gift_card.PaymentDetailsServicer(), server
    )
    # pylint: disable=line-too-long
    gift_card_appointment_related_info_pb2_grpc.add_BooksyGiftCardAppointmentDetailsServicer_to_server(
        gift_card_related_info.BooksyGiftCardAppointmentDetailsServicer(), server
    )
    # pylint: enable=line-too-long
    bgc_business_status_pb2_grpc.add_BGCBusinessStatusServicer_to_server(
        bgc_business_status.BGCBusinessStatusServicer(), server
    )
    get_user_access_level_info_pb2_grpc.add_GetUserAccessLevelInfoServicer_to_server(
        get_user_access_level_info.GetUserAccessLevelInfoService(),
        server,
    )
