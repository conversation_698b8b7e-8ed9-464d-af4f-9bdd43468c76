#!/usr/bin/env python
import os
from logging.config import dictConfig

from celery.signals import setup_logging
from django.conf import settings

from lib.monkeypatching import (
    monkeypatch_py3_10_asyncio_lock_loop_deprecation,
    monkeypatch_py3_10_collections,
    patch_celery_on_tick,
    patch_softtimelimit_sighandler,
)

monkeypatch_py3_10_asyncio_lock_loop_deprecation()  # noqa
monkeypatch_py3_10_collections()  # noqa
patch_celery_on_tick()  # noqa
patch_softtimelimit_sighandler()  # noqa
# pylint: disable=wrong-import-position
from lib.celery_tools import make_celery
from lib.probes import celery_probes  # pylint: disable=unused-import
from lib.celery_utils import celery_signals  # pylint: disable=unused-import

# pylint: enable=wrong-import-position

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')

priority_booksy_app = make_celery('celery_app', 'settings.celery.PriorityQueuesCeleryConfig')
push_booksy_app = make_celery('celery_app', 'settings.celery.PushNotificationsQueuesCeleryConfig')
regular_booksy_app = make_celery('celery_app', 'settings.celery.RegularQueuesCeleryConfig')
segment_booksy_app = make_celery('celery_app', 'settings.celery.SegmentQueuesCeleryConfig')
index_booksy_app = make_celery('celery_app', 'settings.celery.IndexQueuesCeleryConfig')
all_queues_booksy_app = make_celery('celery_app', 'settings.celery.AllQueuesCeleryConfig')
# App with an empty queue that handles beat scheduler.
celerybeat_booksy_app = make_celery('celery_app', 'settings.celery.EmptyQueuesCeleryConfig')


@setup_logging.connect
def config_loggers(*args, **kwargs):
    dictConfig(settings.LOGGING)
