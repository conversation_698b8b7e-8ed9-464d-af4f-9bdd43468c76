import typing as t

import elasticsearch_dsl as dsl
from django.db import models
from elasticsearch_dsl.connections import connections

from lib.elasticsearch.helpers import bulk
from lib.es_history.context import UseContext, use_context
from lib.es_history.queue import HistoryQueue, QueueItem
from lib.es_history.utils import get_user_data, make_history_document
from lib.tools import tznow

if t.TYPE_CHECKING:
    # pylint: disable=unused-import
    from webapps.user.models import User

T = t.TypeVar('T', covariant=True, bound=models.Model)


class HistoryDescriptor(t.Generic[T]):
    def __get__(self, instance: T, cls: t.Type[T]) -> 'HistoryManager[T]':
        return HistoryManager(cls, instance)


class HistoryManager(t.Generic[T]):
    def __init__(self, model: t.Type[T], instance: t.Optional[T] = None) -> None:
        self.model = model
        self.instance = instance
        self.queue = HistoryQueue(model)

    def create(
        self,
        before: dict[str, t.Any],
        after: dict[str, t.Any],
        *,
        pk: t.Optional[t.Any] = None,
        user: t.Optional[t.Union['User', UseContext]] = use_context,
        endpoint: t.Optional[str] = None,
        use_celery: bool = True,
    ) -> t.Optional[dsl.Document]:
        """
        Create new history document.

        :param before: Model's state before change.
        :param after: Model's state after change.
        :param pk: Primary key of history's object. Optional when used from instance.
        :param user: User that triggered the change. By default user is sourced from
        Django/Tornado request.
        :param endpoint: Name of the endpoint where change was made.
        :param use_celery: Whether to save history in background using Celery.
        :return: Created history document or None if no changes were detected or use_celery is True
        :raises ValueError: if used on instance without primary key set and without specifying pk
        """
        if self.instance is None or self.instance.pk is None:
            if pk is None:
                raise ValueError('.create() must be used on instance with primary key set.')
        else:
            pk = self.instance.pk

        updated = tznow()
        user_data = get_user_data(user)

        if use_celery:
            record = QueueItem(
                pk=pk,
                before=before,
                after=after,
                updated=updated,
                user=user_data,
                endpoint=endpoint,
            )
            self.queue.enqueue((record,))
        else:
            document = make_history_document(
                self.model.history_document_class,
                pk,
                before,
                after,
                updated=updated,
                user_data=user_data,
                endpoint=endpoint,
            )

            if document is None:
                return

            document.save()

            return document

    def bulk_create(
        self,
        instances: t.Iterable[T],
        before_map: dict[int, dict[str, t.Any]],
        *,
        fields: t.Optional[t.Collection[str]] = None,
        user: t.Optional[t.Union['User', UseContext]] = use_context,
        endpoint: t.Optional[str] = None,
        use_celery: bool = True,
    ) -> tuple[int, dict]:
        """
        Create new history document in bulk.

        :param instances: Iterable of changed model instances.
        :param before_map: Mapping of model's primary keys to states before change.
        :param fields: A collection of document's serializer fields to include.
        This is passed to instances' extract_state method.
        :param user: User that triggered the change. By default user is sourced from
        Django/Tornado request.
        :param endpoint: Name of the endpoint where change was made.
        :param use_celery: Whether to save history in background using Celery.
        :return: Tuple of number of successfully created documents and list of ES errors
        or None if use_celery is True
        """

        def get_before_state(pk):
            before = before_map.get(pk, {})
            if fields is not None:
                return {field: value for field, value in before.items() if field in fields}
            return before

        updated = tznow()
        user_data = get_user_data(user)

        if use_celery:
            records = (
                QueueItem(
                    pk=instance.pk,
                    before=get_before_state(instance.pk),
                    after=instance.extract_state(fields),
                    updated=updated,
                    user=user_data,
                    endpoint=endpoint,
                )
                for instance in instances
            )
            self.queue.enqueue(records)
        else:
            documents = (
                make_history_document(
                    self.model.history_document_class,
                    instance.pk,
                    get_before_state(instance.pk),
                    instance.extract_state(fields),
                    updated=updated,
                    user_data=get_user_data(user),
                    endpoint=endpoint,
                )
                for instance in instances
            )
            actions = (document.to_dict(include_meta=True) for document in documents if document)
            connection = connections.get_connection()
            return bulk(connection, actions)

    def search(self) -> dsl.Search:
        """
        Creates new search on this model's history. If used on instance history is
        filtered by primary key.

        :return: a Search object
        """
        search = self.model.history_document_class.search()

        if self.instance and self.instance.pk is not None:
            search = search.filter('term', pk=self.instance.pk)

        return search
