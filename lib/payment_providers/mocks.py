import uuid
from datetime import date

from django.conf import settings
from mock.mock import MagicMock

from lib.payment_providers.entities import (
    PaymentEntity,
    TokenizedPaymentMethodEntity,
    ProviderAccountDetails,
    PayoutMethodDetails,
    BankAccount,
    PersonalDetails,
    StripeAccountDetails,
    UserInputKYC,
)
from lib.payment_providers.enums import (
    TokenizedPaymentMethodType,
    PaymentStatus,
    PaymentMethodType,
    ProviderAccountHolderStatus,
    PayoutMethodStatus,
    PayoutMethodType,
    StripeAccountType,
    StripeAccountBusinessType,
)
from lib.payments.enums import PaymentProviderCode, PaymentError
from webapps.business_consents.enums import ConsentCode
from webapps.payment_providers.models import TokenizedPaymentMethod
from webapps.payments.views.account_management import (
    get_available_payout_method_types,
    get_available_fast_payout_method_types,
)
from webapps.pos.enums.bank_account_number_form import BANK_ACCOUNT_NUMBER_FORM
from webapps.stripe_integration.enums import FastPayoutStatus


def get_payment_object_mock() -> MagicMock:
    payment_object_mock = MagicMock()
    payment_object_mock.payment_method = TokenizedPaymentMethodType.CARD
    payment_object_mock.status = PaymentStatus.NEW
    payment_object_mock.provider_code = PaymentProviderCode.STRIPE
    payment_object_mock.entity = PaymentEntity(
        id=uuid.uuid4(),
        error_code=PaymentError.GENERIC_ERROR,
        auto_capture=True,
        fee_amount=1,
        amount=100,
        status=PaymentStatus.AUTHORIZATION_FAILED,
    )
    payment_object_mock.get_error_code_display.return_value = PaymentError.GENERIC_ERROR.label
    return payment_object_mock


def get_invalid_tokenized_payment_method_mock() -> MagicMock:
    tokenized_payment_method_mock = MagicMock()
    tokenized_payment_method_mock.internal_status = 'I'
    tokenized_payment_method_mock.entity = TokenizedPaymentMethodEntity(
        id=uuid.uuid4(),
        customer=uuid.uuid4(),
        provider_code=PaymentProviderCode.STRIPE,
        method_type=PaymentMethodType.CARD,
        default=False,
        details={
            'bin': '411111',
            'alias': 'E425578841744385',
            'brand': 'visa',
            'expiry_year': 2030,
            'last_digits': '1111',
            'expiry_month': 3,
            'cardholder_name': 'Card Holder',
        },
        internal_status=TokenizedPaymentMethod.INTERNAL_STATUS.INVALID,
    )
    tokenized_payment_method_mock.customer.id = uuid.uuid4()
    return tokenized_payment_method_mock


def get_valid_tokenized_payment_method_mock() -> MagicMock:
    tokenized_payment_method_mock = MagicMock()
    tokenized_payment_method_mock.internal_status = 'I'
    tokenized_payment_method_mock.entity = TokenizedPaymentMethodEntity(
        id=uuid.uuid4(),
        customer=uuid.uuid4(),
        provider_code=PaymentProviderCode.STRIPE,
        method_type=PaymentMethodType.CARD,
        default=False,
        details={
            'bin': '411111',
            'alias': 'E425578841744385',
            'brand': 'visa',
            'expiry_year': 2030,
            'last_digits': '1111',
            'expiry_month': 3,
            'cardholder_name': 'Card Holder',
        },
        internal_status=TokenizedPaymentMethod.INTERNAL_STATUS.VALID,
    )
    tokenized_payment_method_mock.customer.id = uuid.uuid4()
    return tokenized_payment_method_mock


def get_expired_tokenized_payment_method_mock() -> MagicMock:
    tokenized_payment_method_mock = MagicMock()
    tokenized_payment_method_mock.internal_status = 'E'
    tokenized_payment_method_mock.entity = TokenizedPaymentMethodEntity(
        id=uuid.uuid4(),
        customer=uuid.uuid4(),
        provider_code=PaymentProviderCode.STRIPE,
        method_type=PaymentMethodType.CARD,
        default=False,
        details={
            'bin': '411111',
            'alias': 'E425578841744385',
            'brand': 'visa',
            'expiry_year': 2030,
            'last_digits': '1111',
            'expiry_month': 3,
            'cardholder_name': 'Card Holder',
        },
        internal_status=TokenizedPaymentMethod.INTERNAL_STATUS.EXPIRED,
    )
    return tokenized_payment_method_mock


def get_expires_soon_tokenized_payment_method_mock() -> MagicMock:
    tokenized_payment_method_mock = MagicMock()
    tokenized_payment_method_mock.internal_status = 'S'
    tokenized_payment_method_mock.default = True
    tokenized_payment_method_mock.entity = TokenizedPaymentMethodEntity(
        id=uuid.uuid4(),
        customer=uuid.uuid4(),
        provider_code=PaymentProviderCode.STRIPE,
        method_type=PaymentMethodType.CARD,
        default=True,
        details={
            'bin': '411111',
            'alias': 'E425578841744385',
            'brand': 'visa',
            'expiry_year': 2030,
            'last_digits': '1111',
            'expiry_month': 3,
            'cardholder_name': 'Card Holder',
        },
        internal_status=TokenizedPaymentMethod.INTERNAL_STATUS.EXPIRES_SOON,
    )
    return tokenized_payment_method_mock


def get_provider_account_details_mock() -> ProviderAccountDetails:
    provider_account_details_mock = ProviderAccountDetails(
        payment_provider_code=PaymentProviderCode.STRIPE,
        status=ProviderAccountHolderStatus.VERIFIED,
        fast_payouts_status=FastPayoutStatus.AVAILABLE,
        payments_enabled=True,
        payouts_enabled=True,
        kyc_errors=[],
        payout_methods=[
            PayoutMethodDetails(
                method_type=PayoutMethodType.BANK_ACCOUNT,
                status=PayoutMethodStatus.ACTIVE,
                error_code=None,
                card=None,
                bank_account=BankAccount(
                    last_digits="1234",
                    account_holder_name="John Smith",
                    bank_name="Chase",
                    routing_number="*********",
                    country='us',
                ),
                token="12345",
                is_default_for_regular_payouts=True,
                is_default_for_fast_payouts=False,
                has_fast_payout_capability=True,
            )
        ],
        personal_details=PersonalDetails(
            first_name="John", last_name="Smith", date_of_birth=date(1985, 5, 12), ssn_provided=True
        ),
        company_details=None,
        provider_specific=StripeAccountDetails(
            account_type=StripeAccountType.CUSTOM,
            business_type=StripeAccountBusinessType.INDIVIDUAL,
            kyc_required_threshold=3_000_00,
        ),
        available_payout_method_types=get_available_payout_method_types(),
        available_fast_payout_method_types=get_available_fast_payout_method_types(),
        bank_account_number_form=BANK_ACCOUNT_NUMBER_FORM.get(settings.API_COUNTRY),
        kyc_verified_at_least_once=True,
        external_id='external_id',
        user_input_kyc=UserInputKYC(show=False, consent_code=ConsentCode.US_USER_INPUT_KYC),
    )
    return provider_account_details_mock


def get_provider_account_details_turned_off_mock() -> ProviderAccountDetails:
    return ProviderAccountDetails(
        payment_provider_code=PaymentProviderCode.STRIPE,
        status=ProviderAccountHolderStatus.TURNED_OFF,
        fast_payouts_status=FastPayoutStatus.MISSING_KYC,
        payments_enabled=False,
        payouts_enabled=False,
        kyc_errors=[],
        payout_methods=[],
        personal_details=PersonalDetails(
            first_name="John", last_name="Smith", date_of_birth=date(1985, 5, 12), ssn_provided=True
        ),
        company_details=None,
        provider_specific=StripeAccountDetails(
            account_type=StripeAccountType.CUSTOM,
            business_type=StripeAccountBusinessType.INDIVIDUAL,
            kyc_required_threshold=3_000_00,
        ),
        available_payout_method_types=get_available_payout_method_types(),
        available_fast_payout_method_types=get_available_fast_payout_method_types(),
        bank_account_number_form=BANK_ACCOUNT_NUMBER_FORM.get(settings.API_COUNTRY),
        kyc_verified_at_least_once=False,
        external_id='external_id',
        user_input_kyc=UserInputKYC(show=False, consent_code=ConsentCode.US_USER_INPUT_KYC),
    )


def get_provider_account_details_kyc_pending_info_missing_mock() -> ProviderAccountDetails:
    return ProviderAccountDetails(
        payment_provider_code=PaymentProviderCode.STRIPE,
        status=ProviderAccountHolderStatus.VERIFICATION_PENDING,
        fast_payouts_status=FastPayoutStatus.MISSING_KYC,
        payments_enabled=False,
        payouts_enabled=False,
        kyc_errors=[],
        payout_methods=[],
        personal_details=PersonalDetails(
            first_name="John", last_name="Smith", date_of_birth=date(1985, 5, 12), ssn_provided=True
        ),
        company_details=None,
        provider_specific=StripeAccountDetails(
            account_type=StripeAccountType.CUSTOM,
            business_type=StripeAccountBusinessType.INDIVIDUAL,
            kyc_required_threshold=3_000_00,
        ),
        available_payout_method_types=get_available_payout_method_types(),
        available_fast_payout_method_types=get_available_fast_payout_method_types(),
        bank_account_number_form=BANK_ACCOUNT_NUMBER_FORM.get(settings.API_COUNTRY),
        kyc_verified_at_least_once=False,
        external_id='external_id',
        user_input_kyc=UserInputKYC(show=False, consent_code=ConsentCode.US_USER_INPUT_KYC),
    )


def get_provider_account_details_kyc_pending_mock() -> ProviderAccountDetails:
    return ProviderAccountDetails(
        payment_provider_code=PaymentProviderCode.STRIPE,
        status=ProviderAccountHolderStatus.VERIFICATION_PENDING,
        fast_payouts_status=FastPayoutStatus.MISSING_KYC,
        payments_enabled=False,
        payouts_enabled=False,
        kyc_errors=[],
        payout_methods=[
            PayoutMethodDetails(
                method_type=PayoutMethodType.BANK_ACCOUNT,
                status=PayoutMethodStatus.ACTIVE,
                error_code=None,
                card=None,
                bank_account=BankAccount(
                    last_digits="1234",
                    account_holder_name="John Smith",
                    bank_name="Chase",
                    routing_number="*********",
                    country='us',
                ),
                token="12345",
                is_default_for_regular_payouts=True,
                is_default_for_fast_payouts=False,
                has_fast_payout_capability=True,
            )
        ],
        personal_details=PersonalDetails(
            first_name="John", last_name="Smith", date_of_birth=date(1985, 5, 12), ssn_provided=True
        ),
        company_details=None,
        provider_specific=StripeAccountDetails(
            account_type=StripeAccountType.CUSTOM,
            business_type=StripeAccountBusinessType.INDIVIDUAL,
            kyc_required_threshold=3_000_00,
        ),
        available_payout_method_types=get_available_payout_method_types(),
        available_fast_payout_method_types=get_available_fast_payout_method_types(),
        bank_account_number_form=BANK_ACCOUNT_NUMBER_FORM.get(settings.API_COUNTRY),
        kyc_verified_at_least_once=False,
        external_id='external_id',
        user_input_kyc=UserInputKYC(show=False, consent_code=ConsentCode.US_USER_INPUT_KYC),
    )


def get_provider_account_details_not_verified_mock() -> ProviderAccountDetails:
    return ProviderAccountDetails(
        payment_provider_code=PaymentProviderCode.STRIPE,
        status=ProviderAccountHolderStatus.NOT_VERIFIED,
        fast_payouts_status=FastPayoutStatus.MISSING_KYC,
        payments_enabled=False,
        payouts_enabled=False,
        kyc_errors=[],
        payout_methods=[],
        personal_details=PersonalDetails(
            first_name="John", last_name="Smith", date_of_birth=date(1985, 5, 12), ssn_provided=True
        ),
        company_details=None,
        provider_specific=StripeAccountDetails(
            account_type=StripeAccountType.CUSTOM,
            business_type=StripeAccountBusinessType.INDIVIDUAL,
            kyc_required_threshold=3_000_00,
        ),
        available_payout_method_types=get_available_payout_method_types(),
        available_fast_payout_method_types=get_available_fast_payout_method_types(),
        bank_account_number_form=BANK_ACCOUNT_NUMBER_FORM.get(settings.API_COUNTRY),
        kyc_verified_at_least_once=False,
        external_id='external_id',
        user_input_kyc=UserInputKYC(show=False, consent_code=ConsentCode.US_USER_INPUT_KYC),
    )
