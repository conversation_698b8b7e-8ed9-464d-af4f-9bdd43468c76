from rest_framework import serializers
from rest_framework.fields import empty

MAX_SHORTTEXT_FIELD_LENGTH = 86
MAX_PARAGRAPH_FIELD_LENGTH = 1000


class ShortText(serializers.CharField):
    def __init__(self, **kwargs):
        kwargs['max_length'] = kwargs.get('max_length', MAX_SHORTTEXT_FIELD_LENGTH)
        kwargs['required'] = kwargs.get('required', False)
        kwargs['allow_null'] = kwargs.get('allow_null', True)
        kwargs['allow_blank'] = kwargs.get('allow_blank', True)
        super().__init__(**kwargs)


class MandatoryShortText(ShortText):
    def __init__(self, **kwargs):
        kwargs['required'] = kwargs.get('required', True)
        kwargs['allow_null'] = kwargs.get('allow_null', False)
        kwargs['allow_blank'] = kwargs.get('allow_blank', False)
        super().__init__(**kwargs)

    def run_validation(self, data=empty):
        """
        override standard null/blank error codes with `required` code for `required=True` fields;
        this override is because front apps expect a `required` error code also in cases if field
        exists in the request, but is empty;
        """
        if not data and self.required:
            self.fail('required')
        return super().run_validation(data)


class Paragraph(serializers.CharField):
    def __init__(self, **kwargs):
        kwargs['max_length'] = kwargs.get('max_length', MAX_PARAGRAPH_FIELD_LENGTH)
        kwargs['allow_null'] = kwargs.get('allow_null', True)
        kwargs['required'] = kwargs.get('required', False)
        kwargs['allow_blank'] = kwargs.get('allow_blank', True)
        super().__init__(**kwargs)
