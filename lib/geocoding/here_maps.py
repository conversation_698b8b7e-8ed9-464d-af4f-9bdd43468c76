import logging
from collections import OrderedDict
from typing import Callable, Optional, Sequence, Tuple

import requests
from django.conf import settings
from django.core.cache import cache
from django.utils.datastructures import OrderedSet
from django.utils.translation import gettext as _
from iso3166 import countries as iso_countries
from requests_oauthlib import OAuth1
from slugify import slugify

from country_config import CountryConfig
from country_config.enums import Country
from lib.enums import IntEnum
from lib.feature_flag.adapter import UserData


from lib.cache import lru_booksy_cache
from lib.elasticsearch.consts import ESDocType, REGION_MATCH_SCORE
from lib.elasticsearch.tools import get_by_id
from lib.feature_flag.feature import (
    HereAPICacheExpiryTimeFlag,
    LogHereApiCalls,
)
from lib.geocoding.base import BaseGeocoder, GeocodingError
from lib.geocoding.primitives import BoundingBox
from lib.geocoding.utils import format_address, format_street
from lib.tools import (
    sget,
)
from webapps.structure.enums import RegionType

_street_hints_log = logging.getLogger('booksy.street_hints')
_full_logger = logging.getLogger('booksy.full')
here_api_call_logger = logging.getLogger('booksy.here_api_call')

OAUTH2_TOKEN_URL = 'https://account.api.here.com/oauth2/token'
ACCESS_TOKEN_CACHE_KEY = 'here_maps_access_token'

GEOCODE_URL = 'https://geocode.search.hereapi.com/v1/geocode'
LOOKUP_URL = 'https://lookup.search.hereapi.com/v1/lookup'
REVERSE_GEOCODE_URL = 'https://revgeocode.search.hereapi.com/v1/revgeocode'
AUTOCOMPLETE_URL = 'https://autocomplete.search.hereapi.com/v1/autocomplete'
AUTOSUGGEST_URL = 'https://autosuggest.search.hereapi.com/v1/autosuggest'


class HereAPICallCacheExpiryTimeEnum(IntEnum):
    HOUR = 60 * 60
    DAY = 60 * 60 * 24
    WEEK = 60 * 60 * 24 * 7


HERE_MAPS_COUNTRIES_MAPPING = {
    # US & GB contains their colonies as well, and FR have overseas departments
    'USA': 'USA,PRI,VIR,GUM,MNP,WSM',
    'GBR': 'GBR,IMN,BMU,GGY,JEY,GIB',
    'FRA': 'FRA,GUF,GLP,MTQ,MYT,REU',
}


ORDER_AUTOCOMPLETE_LOCALITY = [
    ('postalCodePoint', None),
    ('locality', 'postalCode'),
    ('locality', 'city'),
    ('locality', 'district'),
    ('locality', 'subdistrict'),
    ('locality', None),
    ('place', None),
    ('houseNumber', 'PA'),
    ('houseNumber', 'interpolated'),
    ('houseNumber', None),
    ('street', None),
    ('intersection', None),
    ('administrativeArea', 'county'),
    ('administrativeArea', 'state'),
    ('administrativeArea', 'country'),
    ('administrativeArea', None),
]

ORDER_AUTOCOMPLETE_ADDREESS = [
    ('place', None),
    ('houseNumber', 'PA'),
    ('houseNumber', 'interpolated'),
    ('houseNumber', None),
    ('street', None),
    ('intersection', None),
    ('postalCodePoint', None),
    ('locality', 'postalCode'),
    ('locality', 'subdistrict'),
    ('locality', 'district'),
    ('locality', 'city'),
    ('locality', None),
    ('administrativeArea', 'county'),
    ('administrativeArea', 'state'),
    ('administrativeArea', 'country'),
    ('administrativeArea', None),
]

# from most detailed result, to least
_RESULT_TYPES_ORDER = (
    'place',
    'PA',
    'interpolated',
    'houseNumber',
    'street',
    'intersection',
    'postalCodePoint',
    'postalCode',
    'subdistrict',
    'district',
    'city',
    'locality',
    'county',
    'state',
    'country',
    'administrativeArea',
)
_ADDRESS_FIELDS_ORDER = (
    'postalCode',
    # 'houseNumber',
    'street',
    'subdistrict',
    'district',
    'city',
    'county',
    'state',
    'countryName',
)

# Mapping of address fields and their equivalent RegionTypes's
_ADDRESS_FIELD_TO_REGION_TYPE_MAPPING = {
    'postalCode': [
        RegionType.ZIP,
    ],
    'street': [
        RegionType.NEIGHBORHOOD,
    ],
    'subdistrict': [
        RegionType.NEIGHBORHOOD,
    ],
    'district': [
        RegionType.BOROUGH,
        RegionType.NEIGHBORHOOD,
        RegionType.VILLAGE,
        RegionType.COMMUNITY,  # ?
    ],
    'city': [
        RegionType.COMMUNITY,
        RegionType.CITY,
        RegionType.METROPOLIS,
        RegionType.VILLAGE,
    ],
    'county': [
        RegionType.COUNTY,
        RegionType.METROPOLIS,
        RegionType.REGION,
        RegionType.VOIVODESHIP,
        RegionType.STATE,
    ],
    'state': [
        RegionType.REGION,
        RegionType.VOIVODESHIP,
        RegionType.STATE,
    ],
    'countryName': [
        RegionType.COUNTRY,
    ],
}

# Mapping of result type/subtype to first sensible address field
_RESULT_TYPE_TO_ADDRESS_FIELD = {
    'place': 'street',
    'houseNumber': 'street',
    'street': 'street',
    'intersection': 'street',
    'postalCodePoint': 'postalCode',
    'postalCode': 'postalCode',
    'district': 'district',
    'subdistrict': 'subdistrict',
    'city': 'city',
    'state': 'state',
    'country': 'countryName',
}

_RESULT_TYPE_TO_MATCH_TYPE_MAPPING = {
    'PA': 'street',
    'interpolated': 'street',
    'houseNumber': 'street',
    'street': 'street',
    'intersection': 'street',
    'place': 'street',
    'postalCode': 'zip',
    'postalCodePoint': 'zip',
    'district': 'neighborhood',
    'subdistrict': 'neighborhood',
    'county': 'neighborhood',  # ?
    'city': 'city',
    'state': 'state',
    'country': 'country',
}

# We want bounding-box for streets to include nearby buildings [in meters].
_STREET_EXPAND_DISTANCE = 250


_auth = None


class HereMapsAuthentication:
    """
    This class is responsible for authenticating with the HERE platform via OAuth.
    """

    def __init__(self, access_key_id: str, access_key_secret: str) -> None:
        self.access_key_id = access_key_id
        self.access_key_secret = access_key_secret

    @classmethod
    def get_default(cls):
        global _auth  # pylint: disable=global-statement
        if not _auth:
            _auth = cls(settings.HERE_MAPS_ACCESS_KEY_ID, settings.HERE_MAPS_ACCESS_KEY_SECRET)

        return _auth

    @property
    def access_token(self) -> Optional[str]:
        """
        Return the current token or requests a new one if needed.
        :return: a valid token
        """
        access_token = cache.get(ACCESS_TOKEN_CACHE_KEY, default=None)
        if not access_token:
            access_token = self._generate_access_token()

        return access_token

    def _generate_access_token(self) -> str:
        """
        Authenticate with the HERE account service and retrieve a new token.
        """
        oauth = OAuth1(
            self.access_key_id,
            client_secret=self.access_key_secret,
            signature_method='HMAC-SHA256',
        )
        response = requests.post(
            url=OAUTH2_TOKEN_URL,
            headers={'Content-Type': 'application/x-www-form-urlencoded'},
            data='grant_type=client_credentials',
            auth=oauth,
            timeout=settings.REQUESTS_TIMEOUT,
        )
        response.raise_for_status()

        response_json = response.json()
        access_token = response_json['access_token']
        expires_in = int(response_json['expires_in'])
        cache.set(ACCESS_TOKEN_CACHE_KEY, access_token, timeout=expires_in - 100)

        return access_token


def get_in_country_param(country: Country | None = None) -> str:
    # country code for here maps
    iso3166_country_code = iso_countries.get(country or settings.API_COUNTRY).alpha3
    country_param = HERE_MAPS_COUNTRIES_MAPPING.get(iso3166_country_code, iso3166_country_code)
    return f'countryCode:{country_param}'


def get_language_param():
    language_country = settings.LANGUAGE_CODE.split('-')
    language_country[0] = language_country[0].lower()
    if len(language_country) > 1:
        language_country[1] = language_country[1].upper()

    return '-'.join(language_country)


def _get_result_type_tuple(item: dict) -> Tuple[Optional[str], Optional[str]]:
    """
    Return items resultType and its subtype
    """
    result_type = item.get('resultType')
    subtype = item.get(f'{result_type}Type') if result_type else None

    return result_type, subtype


def _get_equivalent_match_type(
    result_type: Optional[str], result_subtype: Optional[str]
) -> Optional[str]:
    """
    Return closest match-type - an equivalent of result-type in Google Maps.
    """
    match_type = _RESULT_TYPE_TO_MATCH_TYPE_MAPPING.get(result_type)
    match_type = _RESULT_TYPE_TO_MATCH_TYPE_MAPPING.get(result_subtype, match_type)

    return match_type


def _prepare_sort_func(order: Sequence[tuple], default: int = 1000) -> Callable[[dict], int]:
    order_dict = {result_type_tuple: index for index, result_type_tuple in enumerate(order)}

    def func(item: dict) -> int:
        result_type_tuple = _get_result_type_tuple(item)
        if result_type_tuple in order_dict:
            return order_dict[result_type_tuple]

        return order_dict.get((result_type_tuple[0], None), default)

    return func


def here_api_call(url, params, **kwargs):
    user_data = UserData(custom={'url': url})
    expiry_time = HereAPICacheExpiryTimeFlag(user_data)

    if expiry_time == HereAPICacheExpiryTimeFlag.Values.DAY:
        return _cached_here_api_call_day(url, params, **kwargs)
    if expiry_time == HereAPICacheExpiryTimeFlag.Values.WEEK:
        return _cached_here_api_call_week(url, params, **kwargs)
    return _cached_here_api_call(url, params, **kwargs)


@lru_booksy_cache(timeout=HereAPICallCacheExpiryTimeEnum.HOUR, skip_in_pytest=True)
def _cached_here_api_call(url, params, **kwargs):
    return _here_api_call(url, params, **kwargs)


@lru_booksy_cache(timeout=HereAPICallCacheExpiryTimeEnum.DAY, skip_in_pytest=True)
def _cached_here_api_call_day(url, params, **kwargs):
    return _here_api_call(url, params, **kwargs)


@lru_booksy_cache(timeout=HereAPICallCacheExpiryTimeEnum.WEEK, skip_in_pytest=True)
def _cached_here_api_call_week(url, params, **kwargs):
    return _here_api_call(url, params, **kwargs)


def _here_api_call(url, params, **kwargs):
    kwargs.setdefault('timeout', settings.REQUESTS_TIMEOUT)

    if LogHereApiCalls():
        here_api_call_logger.warning('_here_api_call(url=%s, params=%s)', url, params)
    auth = HereMapsAuthentication.get_default()

    params = params.copy()
    headers = {'Authorization': f'Bearer {auth.access_token}'}

    try:
        response = requests.get(
            url, params=params, headers=headers, **kwargs
        )  # pylint: disable=missing-timeout
        response.raise_for_status()
        return response
    except requests.RequestException as error:
        _full_logger.error(
            '[ERROR] HERE Maps. Request exception: %s\nQuery params: %r', error, params
        )
        raise


def country_code_to_alpha2(alpha3_country_code):
    """
    Convert alpha3 country-code into alpha2 code.
    """
    iso_country = None
    if alpha3_country_code:
        try:
            iso_country = iso_countries.get(alpha3_country_code)
        except KeyError:
            pass
    return iso_country.alpha2.lower() if iso_country else None


def format_label(label: str, result_type=None, country_code=None) -> str:
    """
    Here Maps label is in format:
      Country, City, ..., Street and house number

    Drop country name and reverse this list.
    """
    label_parts = label.split(', ')

    if len(label_parts) <= 1:
        return label

    label_parts = label_parts[:0:-1]  # remove country and reverse

    if result_type in ('street', 'houseNumber'):
        label_parts[0] = format_street(label_parts[0], country_code=country_code)

    return ', '.join(OrderedSet(label_parts))


def get_deprecated_label(item: dict) -> str:
    result_type, result_subtype = _get_result_type_tuple(item)
    label = item.get('title', '')
    address = item.get('address', {})
    city = address.get('city')
    country_code = country_code_to_alpha2(address.get('countryCode'))

    if result_type == 'locality':
        if result_subtype == 'city' and city:
            return city

        if result_subtype in ('district', 'subdistrict') and address.get(result_subtype) and city:
            return f'{address[result_subtype]}, {city}'

    return format_label(
        label,
        result_type=result_type,
        country_code=country_code,
    )


def get_bounding_box(item) -> BoundingBox | None:
    if not item.get('mapView'):
        return None

    result_type = _get_result_type_tuple(item)[0]
    if result_type in ('place', 'houseNumber', 'intersection', 'postalCodePoint'):
        return None

    bounding_box = BoundingBox(
        item['mapView']['north'],
        item['mapView']['east'],
        item['mapView']['south'],
        item['mapView']['west'],
    )
    bounding_box.normalize()

    if result_type == 'street':
        bounding_box = bounding_box.expand(_STREET_EXPAND_DISTANCE)

    return bounding_box


def _get_label_parts(item: dict) -> list[str]:
    """
    Return most detailed part of the address first, followed by city, state and country.

    If users address is known, try to make labels shorter.
    """
    reference_country_code = settings.API_COUNTRY

    result_type, result_subtype = _get_result_type_tuple(item)
    title = item.get('title', '')
    address = item.get('address', {}).copy()
    country_code = country_code_to_alpha2(address.get('countryCode'))

    fields = OrderedSet()
    use_title = False
    try:
        use_state_code = CountryConfig(country_code).use_state_code
    except (KeyError, ValueError):
        use_state_code = False

    match result_type:
        case 'administrativeArea':
            fields.add(result_subtype)

        case 'locality':
            fields.add(result_subtype)

        case 'street':
            fields.add(result_type)

        case 'postalCodePoint':
            fields.add('postalCode')

        case 'houseNumber':
            address['_houseNumber'] = format_address(
                address.get('street', ''),
                address.get('houseNumber', ''),
                country_code=country_code,
            )
            fields.add('_houseNumber')

        case 'intersection':
            address['_intersection'] = ' & '.join(address.get('streets', []))
            fields.add('_intersection')

        case _:
            use_title = True

    if not use_title:
        fields.add('district')
        fields.add('city')
        fields.add('stateCode' if use_state_code and 'stateCode' in address else 'state')

        if country_code != reference_country_code:
            fields.add('countryName')

        parts = []
        for field in fields:
            part = address.get(field)
            if not part:
                continue

            if parts and parts[-1] == part:
                # We can't reliably determine region type label to include in brackets. It could
                # be done per country basis. HereMaps does include region type in brackets, but
                # it's not consistent. For simplicity just skip name repetitions.
                continue

            parts.append(part)

        if parts:
            return parts

    return format_label(
        title,
        result_type=result_type,
        country_code=country_code,
    ).split(', ')


def get_label_and_formatted_address(item: dict) -> tuple[str | None, str | None, str | None]:
    """
    Return label (place name), formatted address and full label.
    """
    label_parts = _get_label_parts(item)

    try:
        label = label_parts[0]
    except IndexError:
        label = None

    formatted_address = ', '.join(label_parts[1:])
    full_label = ', '.join(label_parts)

    return label or None, formatted_address or None, full_label or None


def _lookup_zipcode(result):
    from lib.geocoding import get_geocoder
    from service.other.serializers import GeoReverseSerializer

    result_type = result.get('resultType')

    if result_type and result_type not in (
        'postalCode',
        'street',
        'houseNumber',
    ):
        return None

    try:
        secondary_geo_decoder = get_geocoder(GeoReverseSerializer.BUILT_IN_DECODER)
        tmp = secondary_geo_decoder.reverse_geocode(
            latitude=result['position']['lat'], longitude=result['position']['lng']
        )
        return tmp['zip']
    except GeocodingError:
        pass

    return None


def format_postal_code(value: str, country_code=None):
    if not value:
        return value

    if country_code is None:
        country_code = settings.API_COUNTRY
    if country_code == 'us':
        value = value.split('-')[0]

    return value


def _format_coordinates(latitude: float, longitude: float):
    return f'{latitude:.6f},{longitude:.6f}'


class Geocoder(BaseGeocoder):
    codename = 'here'

    # pylint: disable=too-many-arguments, arguments-differ
    @classmethod
    def geocode(
        cls, address=None, address2=None, zipcode=None, city=None, country=None
    ):  # pylint: disable=too-many-positional-arguments
        """
        Pylint duplicate-code (R0801)
        It's impossible to disable duplicate code check
        on portions of code. Pylint with the current configuration
        ignores 'min-similarity-lines=10'.
        Open issue on pylint's github:
        https://github.com/PyCQA/pylint/issues/214
        """

        components = [address, address2, city, zipcode]

        address_fmt = ', '.join(
            x.decode('utf-8') if not isinstance(x, str) else x for x in components if x
        )

        if not address_fmt:
            raise GeocodingError('geocode: at least one address entry is required')

        response_json, result = cls.get_first_result(address_fmt)
        address = result.get('address', {})
        postal_code = address.get('postalCode', '')
        street = address.get('street', '')
        house_number = address.get('houseNumber', '')
        city = address.get('city', '')
        country = address.get('countryName', '')
        country_code = country_code_to_alpha2(address.get('countryCode'))
        postal_code = format_postal_code(postal_code)

        if 'position' in result:
            if cls.should_dismiss_zip(country_code):
                postal_code = _lookup_zipcode(result) or zipcode

            return cls.format_geocode_response(
                latitude=result['position']['lat'],
                longitude=result['position']['lng'],
                zipcode=postal_code,
                address=format_address(street, house_number, country_code=country_code),
                city=city,
                country=country,
            )
        raise GeocodingError(f'unknown response format, {response_json}')

    # pylint: disable=too-many-arguments, too-many-branches
    @classmethod
    def get_first_result(
        cls,
        address_fmt=None,
        location_id=None,
        country_code=None,
        lat=None,
        lon=None,
        languages=None,
    ):  # pylint: disable=too-many-positional-arguments
        from webapps.structure.models import Region

        if not (address_fmt or location_id or (lat is not None and lon is not None)):
            raise GeocodingError(
                _(
                    'We have not found the address you have entered. '
                    'Make sure the city and zip code are correct.'
                )
            )
        params = {}
        if location_id:
            params['id'] = location_id
            url = LOOKUP_URL
        elif address_fmt:
            params['q'] = address_fmt
            params['in'] = get_in_country_param(country_code)
            params['limit'] = 1
            url = GEOCODE_URL
        else:
            params['limit'] = 1
            url = REVERSE_GEOCODE_URL

        if lon is not None and lat is not None:
            params['at'] = _format_coordinates(lat, lon)

        if languages:
            params['lang'] = ','.join(languages)

        response = here_api_call(url, params)
        if not response or response.status_code != 200:
            raise GeocodingError(
                _('We are unable to find your address at this time. Please try again later.')
            )
        response_json = response.json()

        if location_id:
            item = response_json
        else:
            item = response_json['items'][0] if response_json['items'] else None

        if not item:
            if address_fmt and cls.should_dismiss_zip(country_code or settings.API_COUNTRY):
                region = Region.objects.filter(
                    name=address_fmt,
                    type=RegionType.ZIP,
                ).first()
                if region is not None:
                    return response_json, {
                        'position': {
                            'lat': region.latitude,
                            'lng': region.longitude,
                        },
                        'address': {
                            'city': region.get_parent_name_by_type(
                                RegionType.CITY,
                            ),
                            'postalCode': region.name,
                        },
                    }

            raise GeocodingError(
                _(
                    'We have not found the address you have entered. '
                    'Make sure the city and zip code are correct.'
                )
            )

        address = item.get('address', {})
        country_code = country_code_to_alpha2(address.get('countryCode'))

        if cls.should_dismiss_zip(country_code):
            address['postalCode'] = _lookup_zipcode(item)

        return response_json, item

    @classmethod
    def reverse_geocode(cls, latitude=None, longitude=None, raw=False, **__):
        if latitude is None or longitude is None:
            raise GeocodingError('reverse_geocode: lat/lng is required')

        params = {
            'at': _format_coordinates(latitude, longitude),
            'mode': 'retrieveAddresses',
            'limit': 10,
        }
        response = here_api_call(
            REVERSE_GEOCODE_URL,
            params,
        )
        if not response or response.status_code != 200:
            raise GeocodingError(
                _('We are unable to find your address at this time. Please try again later.')
            )

        response_json = response.json()
        if not response_json['items']:
            raise GeocodingError(
                _(
                    'We have not found the address you have entered. '
                    'Make sure the city and zip code are correct.'
                )
            )

        results = response_json['items']
        filtered_results = [
            result for result in results if result.get('resultType') == 'houseNumber'
        ]
        if filtered_results:
            result = filtered_results[0]
        else:
            result = results[0]
        if raw:
            return result

        address = result.get('address', {})
        postal_code = address.get('postalCode', '')
        street = address.get('street', '')
        house_number = address.get('houseNumber', '')
        city = address.get('city', '')
        country = address.get('countryName', '')
        state = address.get('stateCode', '')
        country_code = country_code_to_alpha2(address.get('countryCode'))
        postal_code = format_postal_code(postal_code, country_code=country_code)

        label, formatted_address, full_label = get_label_and_formatted_address(result)

        if cls.should_dismiss_zip(country_code):
            postal_code = _lookup_zipcode(result)

        return {
            'address': format_address(street, house_number, country_code=country_code),
            'city': city,
            'zip': postal_code,
            'country': country,
            'country_code': country_code,
            'state': state,
            'label': label,
            'formatted_address': formatted_address,
            'full_label': full_label,
        }


# pylint: disable=too-many-instance-attributes
class AutoComplete:

    # v7 documentation
    # https://developer.here.com/documentation/geocoding-search-api/dev_guide/topics/endpoint
    # -autocomplete-brief.html

    # pylint: disable=too-many-arguments
    def __init__(
        self,
        query,
        lat=None,
        lon=None,
        advanced=False,
        filter_with_zip=False,
        max_results=None,  # pylint: disable=unused-argument
        only_zip=False,
        only_city=False,
        only_street=False,
        here_order_match_types=None,
        country_code=None,
        languages=None,
    ):  # pylint: disable=too-many-positional-arguments
        self.query = query
        self.lat = lat
        self.lon = lon
        self.advanced = advanced
        self.filter_with_zip = filter_with_zip
        self.max_results = 10  # default by here is 5, max is 20
        self.only_zip = only_zip
        self.only_city = only_city
        self.only_street = only_street
        self.country_code = country_code
        self.languages = languages
        self.here_order_match_types = here_order_match_types

    # pylint: disable=too-many-branches
    def get_data(self):
        dismiss_zip = Geocoder.should_dismiss_zip(settings.API_COUNTRY)
        if self.only_zip and dismiss_zip:
            items = self._get_suggestions_builtin()
        else:
            params = self._get_request_params()
            response = here_api_call(AUTOCOMPLETE_URL, params)
            if not response:
                return

            try:
                data = response.json()
            except ValueError:
                _street_hints_log.exception(
                    'response error: status=%r text=%r', response.status_code, response.text
                )
                return

            items = self._get_suggestions(data)
            if not items and dismiss_zip:
                items = self._get_suggestions_builtin()

        hints = OrderedDict()
        for item in items:
            deprecated_label = self._get_hint(item)

            label, formatted_address, full_label = get_label_and_formatted_address(item)
            if not label:
                continue

            hints[deprecated_label] = {
                'location_id': item['id'],
                'label': label,
                'formatted_address': formatted_address,
                'full_label': full_label,
                'hint': deprecated_label,
            }

        if not self.advanced:  # deprecated
            return [hint['hint'] for hint in hints.values()]

        return list(hints.values())

    def _get_request_params(self):
        params = {
            'q': self.query,
            'in': get_in_country_param(self.country_code),
        }

        if self.lat is not None and self.lon is not None:
            params['at'] = f'{self.lat},{self.lon}'
        if self.max_results:
            params['limit'] = self.max_results
        if self.only_zip:
            params['types'] = 'postalCode'
        elif self.only_city:
            params['types'] = 'city'
        # elif self.only_street:  # TODO: find a suitable replacement
        #     params['types'] = 'area'

        if self.languages:
            params['lang'] = ','.join(self.languages)

        return params

    def _get_suggestions_builtin(self):
        from webapps.structure.searchables import RegionSearchable

        data = {
            'text': self.query,
            'type': [RegionType.ZIP],
        }
        if self.lat is not None and self.lon is not None:
            data['latitude'] = self.lat
            data['longitude'] = self.lon

        response = (
            RegionSearchable(
                ESDocType.REGION,
            )
            .params(_source_includes=['name'], size=self.max_results or 10)
            .execute(data)
        )

        return [
            {
                'id': None,
                'title': hit.name,
                'address': {'postalCode': hit.name},
            }
            for hit in response.hits
        ]

    def _get_suggestions(self, data):
        items = data.get('items', [])
        if self.here_order_match_types:
            items = sorted(
                items,
                key=_prepare_sort_func(self.here_order_match_types),
            )

        tmp = []
        for item in items:
            address = item.get('address', {})
            country_code = country_code_to_alpha2(address.get('countryCode'))

            if Geocoder.should_dismiss_zip(country_code):
                if item.get('localityType') == 'postalCode':
                    continue
                if 'postalCode' in address:
                    del address['postalCode']

            tmp.append(item)

        items = tmp

        if self.filter_with_zip:
            items = [item for item in items if item['address'].get('postalCode')] or items

        return items

    def _get_hint(self, item: dict) -> str:
        address = item.get('address', {})

        if self.only_zip:
            hint = address.get('postalCode', '')
        elif self.only_city:
            hint = address.get('city', '')
        elif self.only_street:
            country_code = country_code_to_alpha2(address.get('countryCode'))
            hint = format_address(
                address.get('street', ''),
                address.get('houseNumber', ''),
                country_code=country_code,
            )
        else:
            hint = get_deprecated_label(item)

        return hint


class AutoSuggest:

    # v7 documentation
    # https://www.here.com/docs/bundle/geocoding-and-search-api-v7-api-reference/page/index.html#
    # /paths/~1autosuggest/get

    def __init__(
        self,
        query,
        lat=None,
        lon=None,
        advanced=False,
        max_results=10,  # default by here is 20, max is 100
        country_code=None,
        languages=None,
    ):  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self.query = query
        self.lat = lat
        self.lon = lon
        self.advanced = advanced
        self.max_results = max_results
        self.country_code = country_code
        self.languages = languages

    def get_data(self):
        params = self._get_request_params()
        response = here_api_call(AUTOSUGGEST_URL, params)
        if not response:
            return

        try:
            data = response.json()
        except ValueError:
            _street_hints_log.exception(
                'response error: status=%r text=%r', response.status_code, response.text
            )
            return

        items = self._get_suggestions(data)

        hints = OrderedDict()
        for item in items:
            deprecated_label = self._get_hint(item)

            label, formatted_address, full_label = get_label_and_formatted_address(item)
            if not label:
                continue

            if not formatted_address and item.get('resultType') != 'administrativeArea':
                continue

            hints[deprecated_label] = {
                'location_id': item['id'],
                'label': label,
                'formatted_address': formatted_address,
                'full_label': full_label,
                'hint': deprecated_label,
            }

        if not self.advanced:
            return [hint['hint'] for hint in hints.values()]

        return list(hints.values())[: self.max_results]

    def _get_request_params(self):
        params = {
            'q': self.query,
            'in': get_in_country_param(self.country_code),
            'limit': min(self.max_results * 2, 100),
        }

        if self.lat is not None and self.lon is not None:
            params['at'] = f'{self.lat},{self.lon}'

        if self.languages:
            params['lang'] = ','.join(self.languages)

        return params

    def _get_suggestions(self, data):
        items = data.get('items', [])
        filtered_items = []
        for item in items:
            if item.get('resultType') == 'place':
                continue

            address = item.get('address', {})
            country_code = country_code_to_alpha2(address.get('countryCode'))

            if Geocoder.should_dismiss_zip(country_code):
                if item.get('localityType') == 'postalCode':
                    continue
                if 'postalCode' in address:
                    del address['postalCode']

            # `title` in autosuggest has inverse order than in `autocomplete`,
            # it may not have dielectric characters, but the address `label` do.
            title = address.get('label') or item.get('title') or ''
            item['title'] = ', '.join(title.split(', ')[::-1])

            filtered_items.append(item)

        return filtered_items

    def _get_hint(self, item: dict) -> str:
        return get_deprecated_label(item)


class ResolveGeocoder(Geocoder):

    @staticmethod
    def _trigger_location_entered_event(item: dict, context: 'AnalyticsContext') -> None:
        from webapps.segment.tasks import analytics_location_entered_gtm_task

        result_type, result_subtype = _get_result_type_tuple(item)
        analytics_location_entered_gtm_task.delay(
            here_maps_type=result_subtype or result_type,
            context=context.serialize(),
        )

    def get_data(
        self,
        query=None,
        location_id=None,
        lat=None,
        lon=None,
        context=None,
        languages=None,
    ):
        # pylint: disable=too-many-arguments,too-many-positional-arguments,too-many-locals
        from webapps.booking.models import BookingSources
        from webapps.structure.models import get_operating_country
        from webapps.structure.searchables import RegionCoordinateNameTypeSearchable

        try:
            _response_json, item = self.get_first_result(
                query, location_id, lat=lat, lon=lon, languages=languages
            )
        except GeocodingError:
            errors = [
                {
                    'field': 'address',
                    'code': 'address',
                    'description': _("Wrong Address"),
                }
            ]
            return {
                'match': None,
                'location': None,
                'errors': errors,
            }

        # default fallback to country
        fallback_region_doc = get_by_id(
            f'region:{get_operating_country().id}',
            document_type=ESDocType.REGION,
        )
        region_doc = fallback_region_doc

        result_type, result_subtype = _get_result_type_tuple(item)
        # matching_region_types = _get_equivalent_region_types(result_type, result_subtype)
        match_type = _get_equivalent_match_type(result_type, result_subtype)

        address = item.get('address', {})
        postal_code = address.get('postal_code', '')
        country_code = country_code_to_alpha2(address.get('countryCode'))
        postal_code = format_postal_code(postal_code, country_code)

        if (
            match_type in ['zip', 'street']
            and postal_code
            and not settings.COUNTRY_CONFIG.no_zipcodes
        ):
            region_doc = self._lookup_zip_region(postal_code)
        else:
            regions_query_type = self._build_regions_query_type(item)
            if not regions_query_type:
                # list of region is empty so search will
                # fail to empty searchable, so fall softly
                # return default region and log error
                _street_hints_log.error(
                    '[ERROR] HERE Maps. No matched regions query: %r; '
                    'Match_type: %s; Location_id: %s; Result %s',
                    query,
                    match_type,
                    location_id,
                    item,
                )
            else:
                data = {
                    'main_query': query,
                    'main_type': match_type,
                    'location_geo': f"{item['position']['lat']},{item['position']['lng']}",
                    'regions_query_type': regions_query_type,
                }
                searchable = RegionCoordinateNameTypeSearchable(
                    ESDocType.REGION,
                )

                resp = searchable.params(
                    size=1,
                ).execute(data)
                region_doc = resp.hits[0] if resp.hits.total.value else region_doc

        if region_doc and match_type != 'street':
            match_type = region_doc['type']

        address = item.get('address', {})
        street = address.get('street', '')
        house_number = address.get('houseNumber', '')
        postal_code = address.get('postalCode', '')
        city = address.get('city', '')
        state = address.get('state', '')
        country_code = country_code_to_alpha2(address.get('countryCode'))
        postal_code = format_postal_code(postal_code, country_code)

        if self.should_dismiss_zip(country_code):
            postal_code = _lookup_zipcode(item)

        if sget(context, ['source', 'app_type']) == BookingSources.CUSTOMER_APP:
            self._trigger_location_entered_event(item, context)

        bounding_box = get_bounding_box(item)
        label, formatted_address, full_label = get_label_and_formatted_address(item)

        return {
            'match': {
                'latitude': item['position']['lat'],
                'longitude': item['position']['lng'],
                'formatted_address': self._format_address(item),
                'type': match_type,
                'bounding_box': (
                    {
                        'north': bounding_box.north,
                        'east': bounding_box.east,
                        'south': bounding_box.south,
                        'west': bounding_box.west,
                    }
                    if bounding_box
                    else None
                ),
                'here_maps_type': result_subtype or result_type,
            },
            'location': region_doc,  # TODO: sanitize, we leak internal fields
            'label': label,
            'formatted_address': formatted_address,
            'full_label': full_label,
            'street': format_address(street, house_number, country_code=country_code),
            'city': city,
            'state': state,
            'country_code': country_code,
            'zip': postal_code,
            'address': address,
        }

    @staticmethod
    def get_elastic_filter_types(match_type):
        if match_type == 'neighborhood':
            filter_types = [
                'neighborhood',
                'borough',
            ]
        else:
            elastic_search_types = [
                'city',
                'village',
                'county',
                'region',
                'state',
            ]
            if match_type in elastic_search_types:
                index = elastic_search_types.index(match_type)
            else:
                index = 0

            filter_types = elastic_search_types[index:]
        return filter_types

    # pylint: disable=too-many-return-statements
    @staticmethod
    def get_region_by_latlon(result, query, filter_types, match_type=None):
        from webapps.structure.searchables import RegionInfoSearchable

        # look up nearest matching regions
        regions = (
            RegionInfoSearchable(
                ESDocType.REGION,
            )
            .params(size=100)
            .execute(
                {
                    'type': filter_types,
                    'latitude': result['position']['lat'],
                    'longitude': result['position']['lng'],
                    'distance': '10000km',
                },
            )
        )
        if not regions:
            return

        if settings.API_COUNTRY == Country.US and match_type in ['state', 'city']:
            # exception for NY, SF and etc
            if match_type == 'city':
                query = result['address']['city']

            for region in regions:
                if slugify(query) in region['search_name']:
                    return region

        # exact match
        for region in regions:
            if slugify(query) == slugify(region['name']):
                return region

        # soft match
        for region in regions:
            if slugify(query) in slugify(region['name']) or slugify(region['name']) in slugify(
                query
            ):
                return region

        for t_order in filter_types:
            for region in regions:
                if region.type == t_order:
                    return region

        return regions[0]

    @staticmethod
    def _lookup_zip_region(postal_code):
        from webapps.structure.searchables import RegionSearchable

        if not postal_code:
            return

        regions = (
            RegionSearchable(
                ESDocType.REGION,
            )
            .params(size=1)
            .execute(
                {
                    'type': ['zip'],
                    'text': postal_code,
                }
            )
        )
        if regions:
            return regions[0]
        return

    @staticmethod
    def _format_address(item):
        """
        Remove country name form address['label']
        """
        address = item.get('address', {})
        formatted_address = address['label']
        country_name = address.get('countryName')
        if country_name:
            formatted_address = formatted_address.replace(country_name, '').rstrip(', ')
        return formatted_address

    @classmethod
    def _build_regions_query_type(cls, item: dict) -> list[dict]:
        """
        Split item`s address into query parts.

        Returned value is tailored for `regions_query_type` field of region searchable.
        """
        result_type, result_subtype = _get_result_type_tuple(item)
        address = item.get('address', {})
        fields = list(_ADDRESS_FIELDS_ORDER)
        first_field_index = None

        try:
            first_field_index = fields.index(_RESULT_TYPE_TO_ADDRESS_FIELD.get(result_type))
        except ValueError:
            pass

        try:
            first_field_index = fields.index(_RESULT_TYPE_TO_ADDRESS_FIELD.get(result_subtype))
        except ValueError:
            pass

        if first_field_index:
            fields = fields[first_field_index:]

        field_weight_norm = len(fields)
        query_parts = []

        for field_index, field_name in enumerate(fields):
            query = address.get(field_name)
            if not query:
                continue

            region_types = _ADDRESS_FIELD_TO_REGION_TYPE_MAPPING.get(field_name)
            if not region_types:
                _street_hints_log.error(
                    '[ERROR] HERE Maps. Missing mapping for field "%s" in '
                    '_ADDRESS_FIELD_TO_REGION_TYPE_MAPPING',
                    field_name,
                )
                continue

            weight = get_region_es_score(
                region_index=field_index,
                number_candidates=field_weight_norm,
            )

            query_parts.append(
                {
                    'query': query,
                    'weight': weight,
                    'types': region_types,
                }
            )

        return query_parts


def get_region_es_score(*, region_index: int, number_candidates: int) -> float:
    """Calculate score for each region from array.
    Region with highest index has highest score
    """
    if number_candidates == 0:
        # return max score
        raise RuntimeError(
            'Could not assign score to zero number of regions'
        )  # pylint: disable=broad-exception-raised

    score = REGION_MATCH_SCORE * (float(number_candidates - region_index) / number_candidates)
    if region_index != 0:
        # more aggressive slop for not main type
        score = max(score - 0.3 * score, 5)
    return score
