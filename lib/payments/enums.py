from django.utils.translation import gettext_lazy as _

from lib.enums import StrChoicesEnum, StrEnum


class PaymentProviderCode(StrChoicesEnum):
    ADYEN = 'adyen', _('Adyen')
    STRIPE = 'stripe', _('Stripe')
    BOOKSY_GIFT_CARDS = 'booksy_gift_cards', _('Booksy Gift Cards')


class PaymentError(StrChoicesEnum):
    """
    Purpose of this enum is to provide a meaningful code for the client,
    not a technical one nor one for debugging
    (for debugging, more specific error codes are stored in payment_providers app)
    these codes may overlap with those from PSPs, but it is not a rule that they are the same!
    """

    CONNECTION_ERROR = (
        'connection_error',
        _(
            'Connection Error. Please try again later.',
        ),
    )
    GENERIC_ERROR = 'generic_error', _(
        'Declined. Try again or try another payment method.',
    )
    GENERIC_PAYMENT_ERROR = 'generic_payment_error', _(
        'Declined. Try again or try another payment method.',
    )
    GENERIC_CARD_ERROR = 'generic_card_error', _(
        'Declined. Try again or try another payment method.',
    )
    NOT_PERMITTED = 'not_permitted', _(
        "Declined. Try another payment method.",
    )
    FRAUDULENT = 'fraudulent', _(
        'Declined. Try again or try another payment method.',
    )
    RESTRICTED_CARD = 'restricted_card', _(
        'Declined. Try again or try another payment method.',
    )
    MERCHANT_BLACKLIST = 'merchant_blacklist', _(
        "Declined. Try another payment method.",
    )
    REENTER_TRANSACTION = 'reenter_transaction', _(
        'Declined. Try again or try another payment method.',
    )
    BANK_ACCOUNT_VERIFICATION_FAILED = 'bank_account_verification_failed', _(
        'Declined. Try another payment method.',
    )
    DEBIT_NOT_AUTHORIZED = 'debit_not_authorized', _(
        'Declined. Contact the customer.',
    )
    REFER_TO_CUSTOMER = 'refer_to_customer', _(
        'Declined. Contact the customer.',
    )
    ROUTING_NUMBER_INVALID = 'routing_number_invalid', _(
        "Declined. The bank routing number isn't valid.",
    )
    ISSUER_NOT_AVAILABLE = 'issuer_not_available', _(
        'Declined. Try again or try another payment method.',
    )
    BANK_ACCOUNT_INVALID = 'bank_account_invalid', _(
        'Declined. Try another payment method.',
    )
    PROCESSING_ERROR_CARD = 'processing_error_card', _(
        'Declined. Try again or try another payment method.',
    )
    AUTHENTICATION_REQUIRED = 'authentication_required', _(
        'Declined. Try another payment method.',
    )
    TRANSFERS_NOT_ALLOWED = 'transfers_not_allowed', _(
        "Transfer can't be completed. Contact Booksy Support Team.",
    )
    STRIPE_ACCOUNT_PROBLEM = 'stripe_account_problem', _(
        "There's an issue with your Stripe account. Contact Stripe support team.",
    )
    ACCOUNT_COUNTRY_INVALID_ADDRESS = 'account_country_invalid_address', _(
        "There's an issue with the address. "
        "The account and the business must be located in the same country.",
    )
    COUNTRY_UNSUPPORTED = 'country_unsupported', _(
        "Accounts can't be set up in the country you selected yet. "
        "Choose from the currently supported countries.",
    )
    CARD_NOT_SUPPORTED = 'card_not_supported', _(
        "Declined. This type of card isn't supported. Try another payment method.",
    )
    CURRENCY_NOT_SUPPORTED = 'currency_not_supported', _(
        "Declined. This card doesn't support transactions in this currency.",
    )
    INVALID_ACCOUNT_CARD = 'invalid_account_card', _(
        'Declined. Try another payment method.',
    )
    INVALID_BLIK_CODE = 'invalid_blik_code', _(
        'Invalid Blik Code.',
    )
    ACCOUNT_INVALID = 'account_invalid', ""
    ACCOUNT_NOT_VERIFIED_YET = 'account_not_verified_yet', _(
        "Your account hasn't been activated.",
    )
    EXPIRED_CARD = 'expired_card', _('This card has expired. Try another payment method.')
    INCORRECT_CVC = 'incorrect_cvc', _(
        'Declined. Verify the CVC/CVV code and try again, or use another payment method.',
    )
    EXPIRED_CARD_AT_APPOINTMENT = (
        'expired_card_at_appointment',
        _(
            'This card will be expired at the time of the appointment, so you cannot make a '
            'booking with a cancellation fee.Try using another payment method.'
        ),
    )
    INCORRECT_NUMBER = 'incorrect_number', _(
        'The card number you entered is incorrect. Verify the number and try again.',
    )
    TAX_ID_INVALID = 'tax_id_invalid', _(
        'The tax ID number you entered is invalid.'
        'It must be at least 9 digits. Verify the ID and try again.',
    )
    TAXES_CALCULATION_FAILED = 'taxes_calculation_failed', ""
    INVALID_EXPIRY_MONTH = 'invalid_expiry_month', _(
        'The expiration month is invalid. Verify and try again.',
    )
    INVALID_EXPIRY_YEAR = 'invalid_expiry_year', _(
        'The expiration year is invalid. Verify and try again.',
    )
    INVALID_CHARACTERS = 'invalid_characters', _(
        'You entered invalid characters. Verify and try again.',
    )
    INCORRECT_PIN = 'incorrect_pin', _(
        'The PIN you entered is incorrect. Verify and try again.',
    )
    INCORRECT_ZIP = 'incorrect_zip', _(
        'The ZIP/Postal Code you entered is incorrect. Verify and try again.',
    )
    PIN_REQUIRED = 'pin_required', _(
        'Declined. Try another payment method.',
    )
    CARD_DECLINE_RATE_LIMIT_EXCEEDED = 'card_decline_rate_limit_exceeded', _(
        'Declined. Try another payment method.',
    )
    CARD_DECLINED = 'card_declined', _(
        'Declined. Try again or try another payment method.',
    )
    PHONE_NUMBER_REQUIRED = 'phone_number_required', ""
    EMAIL_INVALID = 'email_invalid', _(
        'The email address is invalid. Verify and try again.',
    )
    INCORRECT_ADDRESS = 'incorrect_address', _(
        "The mailing address is incorrect. Verify and try again.",
    )
    STATE_UNSUPPORTED = 'state_unsupported', _(
        'Declined. Try another payment method.',
    )
    PIN_TRY_EXCEEDED = 'pin_try_exceeded', _(
        'Declined. Try another payment method.',
    )
    TERMINAL_LOCATION_COUNTRY_UNSUPPORTED = 'terminal_location_country_unsupported', ""
    INSUFFICIENT_FUNDS = 'insufficient_funds', _(
        'Declined. Try another payment method.',
    )
    AMOUNT_TOO_LARGE = 'amount_too_large', _(
        'Declined. The total is higher than the maximum allowed.'
        'Enter a lower amount and try again, or use another payment method.',
    )
    AMOUNT_TOO_SMALL = 'amount_too_small', _(
        'Declined. The total is lower than the minimum allowed.'
        'Enter a higher amount and try again, or use another payment method.',
    )
    INVALID_AMOUNT = 'invalid_amount', _(
        'Declined. Try another payment method.',
    )
    CHARGE_ALREADY_CAPTURED = 'charge_already_captured', _(
        "This transaction has already been processed.",
    )
    CHARGE_ALREADY_REFUNDED = 'charge_already_refunded', _(
        "The transaction you're trying to refund has already been refunded.",
    )
    TECHNICAL_PROBLEM = 'technical_problem', _(
        "There's been an error and this operation couldn't be completed. Try again.",
    )
    INSTANT_PAYOUTS_UNSUPPORTED = 'instant_payouts_unsupported', ""
    PAYOUTS_NOT_ALLOWED = 'payouts_not_allowed', ""
    TIMEOUT = 'timeout', _('Your request has timed out. Try again.')
    ORDER_CREATION_FAILED = 'order_creation_failed', _(
        "This order couldn't be created. Verify and try again. ",
    )
    ORDER_REQUIRED_SETTINGS = 'order_required_settings', _(
        'This order is missing information. Verify and try again.',
    )
    ORDER_STATUS_INVALID = 'order_status_invalid', ""
    SHIPPING_CALCULATION_FAILED = 'shipping_calculation_failed', _(
        "The shipping calculation couldn't be completed. Verify and try again.",
    )
    OUT_OF_INVENTORY = 'out_of_inventory', _(
        "Something on this order is out of stock. Verify and try again.",
    )
    PAYMENT_EXPIRED = 'payment_expired', _(
        'Declined. Try again or try another payment method.',
    )
    PAYMENT_FAILED = 'payment_failed', _(
        'Declined. Try again or try another payment method.',
    )
    URL_INVALID = 'url_invalid', _(
        'The URL entered is invalid. Verify and try again.',
    )
    THREE_D_SECURE_PROBLEM = 'three_d_secure_problem', _(
        'Declined. Try another payment method.',
    )
    BOOKSY_GIFT_CARD_PAYMENT_FAILED = 'booksy_gift_card_payment_failed', _(
        'Failed to debt booksy gift card. Try again or try another payment method.',
    )
    # TTP (Tap to Pay) specific errors
    CARD_READ_TIMED_OUT = 'card_read_timed_out', _(
        'Payment could not be completed because the transaction timed out, please try again.',
    )
    TAP_TO_PAY_DEVICE_TAMPERED = 'tap_to_pay_device_tampered', _(
        'We detected a possible security issue. '
        'This could happen if parts of your device have been replaced or if the device\'s software was modified.',
    )
    TAP_TO_PAY_NFC_DISABLED = 'tap_to_pay_nfc_disabled', _(
        'NFC must be enabled to process Tap to Pay transactions. Please check your settings and try again.',
    )
    LOCATION_SERVICES_DISABLED = 'location_services_disabled', _(
        'Location services must be enabled to process Tap to Pay transactions. Please check your settings and try again.',
    )
    TAP_TO_PAY_INSECURE_ENVIRONMENT = 'tap_to_pay_insecure_environment', _(
        'We detected a possible security issue. '
        'Please ensure screen recording is off, camera is not active, and developer options are disabled in your settings.',
    )


class PayoutError(StrChoicesEnum):
    ACCOUNT_CLOSED = (
        "account_closed",
        _("The bank account has been closed."),
    )
    ACCOUNT_FROZEN = (
        "account_frozen",
        _("The bank account has been frozen."),
    )
    BANK_ACCOUNT_RESTRICTED = (
        "bank_account_restricted",
        _(
            "The bank account has restrictions on either the type,"
            " or the number, of payouts allowed. This normally indicates that the bank account"
            " is a savings or other non-checking account."
        ),
    )
    BANK_OWNERSHIP_CHANGED = (
        "bank_ownership_changed",
        _(
            "The destination bank account is no longer valid"
            " because its branch has changed ownership."
        ),
    )
    COULD_NOT_PROCESS = (
        "could_not_process",
        _("The bank could not process this payout."),
    )
    DEBIT_NOT_AUTHORIZED = (
        "debit_not_authorized",
        _("Debit transactions are not approved on the bank account."),
    )
    DECLINED = (
        "declined",
        _("The bank has declined this transfer. Please contact the bank before retrying."),
    )
    INSUFFICIENT_FUNDS = (
        "insufficient_funds",
        _("Your provider account has insufficient funds to cover the payout."),
    )
    INVALID_ACCOUNT_NUMBER = (
        "invalid_account_number",
        _("The routing number seems correct, but the account number is invalid."),
    )
    INCORRECT_ACCOUNT_HOLDER_NAME = (
        "incorrect_account_holder_name",
        _("Your bank notified us that the bank account holder name on file is incorrect."),
    )
    INCORRECT_ACCOUNT_HOLDER_ADDRESS = (
        "incorrect_account_holder_address",
        _("Your bank notified us that the bank account holder address on file is incorrect."),
    )
    INCORRECT_ACCOUNT_HOLDER_TAX_ID = (
        "incorrect_account_holder_tax_id",
        _("Your bank notified us that the bank account holder tax ID on file is incorrect."),
    )
    INVALID_CURRENCY = (
        "invalid_currency",
        _(
            "The bank was unable to process this payout because of its currency."
            " This is probably because the bank account cannot accept payments in that currency."
        ),
    )
    NO_ACCOUNT = (
        "no_account",
        _(
            "The bank account details on file are probably incorrect."
            " No bank account could be located with those details."
        ),
    )
    UNSUPPORTED_CARD = (
        "unsupported_card",
        _("The bank no longer supports payouts to this card."),
    )
    GENERIC_ERROR = ("generic_error", _("Payout failed. Please contact support."))  # catchall


class PayoutStatus(StrChoicesEnum):
    IN_PAYMENT_PROCESSOR = 'in_payment_processor', _('Waiting to be sent by payment processor')
    IN_TRANSIT = 'in_transit', _('In transit to destination bank account')
    ARRIVED = 'arrived', _('Payout should be visible on destination bank account')
    CANCELED = 'canceled', _('Canceled')
    FAILED = 'failed', _('Failed')


class PayoutType(StrChoicesEnum):
    FAST = 'fast', _('Fast (also known as instant)')
    REGULAR = 'regular', _('Regular')


class RefundError(StrChoicesEnum):
    ALREADY_REFUNDED = 'already_refunded', _("This payment was already refunded")
    EXPIRED = 'expired', _("This payment can't be refunded anymore")
    INVALID_REFUND_AMOUNT = 'invalid_amount', _("Invalid refund amount")
    MISSING_BALANCE = 'missing_balance', _("There is not enough balance to create a refund")
    INVALID_PAYMENT_STATUS = 'invalid_payment_status', _("This payment cannot be refunded")


class PaymentsTileBackground(StrEnum):
    BCR_TILE = 'bcr_tile'
    MP_TILE = 'mp_tile'
    NSP_TILE = 'nsp_tile'
    BALANCE_TILE = 'balance_tile'


class TokenizedPaymentMethodInternalStatus(StrChoicesEnum):
    VALID = 'V', _('Valid')
    INVALID = 'I', _('Invalid')
    EXPIRED = 'E', _('Expired')
    EXPIRES_SOON = 'S', _('Expires Soon')


class Currency(StrEnum):
    EUR = 'eur'
    USD = 'usd'
    PLN = 'pln'
    GBP = 'gbp'


class PaymentSummaryRowsEnum(StrChoicesEnum):
    TOTAL = 'total', _('Total:')
    GIFT_CARD = 'gift_card', _('Gift Card:')
    TO_PAY_NOW = 'pay_now', _('To pay now:')
    TO_PAY_LATER = 'pay_later', _('To pay later:')


class BGCReportingCategoryEnum(StrEnum):
    CHARGE_BGC = 'charge_BGC'
    TRANSFER_MERCHANT_BGC = 'transfer_merchant_BGC'
    REFUND_BGC = 'refund_BGC'
