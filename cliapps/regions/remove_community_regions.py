#!/usr/bin/env python
# pylint: disable=broad-exception-raised
import argparse
import django

django.setup()  # noqa

# pylint: disable=wrong-import-position
from django.conf import settings
from django.db import transaction
from django.db.models import Subquery, OuterRef, Count, Q, F, IntegerField

from country_config import Country
from webapps.structure.enums import RegionType
from webapps.structure.models import RegionGraph, Region

# pylint: enable=wrong-import-position


def print_variables():
    var = Region.objects.filter(
        Q(type=RegionType.CITY) | Q(type=RegionType.VILLAGE),
        immediate_parents__type=RegionType.COMMUNITY,
    ).count()
    print(
        f"Region.objects.filter(Q(type=RegionType.CITY) | "
        f"Q(type=RegionType.VILLAGE),immediate_parents__"
        f"type=RegionType.COMMUNITY,).count(): {var}"
    )
    var = Region.objects.filter(
        Q(type=RegionType.CITY) | Q(type=RegionType.VILLAGE),
        immediate_parents__type=RegionType.COUNTY,
    ).count()
    print(
        f"Region.objects.filter(Q(type=RegionType.CITY) | "
        f"Q(type=RegionType.VILLAGE),immediate_parents__"
        f"type=RegionType.COUNTY,).count(): {var}"
    )
    var = Region.objects.filter(type=RegionType.COMMUNITY).count()
    print(f"Region.objects.filter(type=RegionType.COMMUNITY).count(): {var}")


def remove_community_regions(dry_run=True):
    print("=== START REMOVING COMMUNITY REGIONS SCRIPT ===")
    print("== starting values ==")
    print_variables()

    with transaction.atomic():
        trojmiasto = Region.objects.get(name='Trójmiasto')
        trojmiasto_cities = trojmiasto.immediate_children.filter(type=RegionType.CITY).count()
        print(f"Cities under Trójmiasto: {trojmiasto_cities}")
        trojmiasto.type = RegionType.METROPOLIS
        trojmiasto.save(update_fields=['type'])

        # Cleaning duplicated community graphs
        duplicated_graphs = (
            RegionGraph.objects.filter(
                Q(related_region__type=RegionType.CITY)
                | Q(related_region__type=RegionType.VILLAGE),
                region__type=RegionType.COMMUNITY,
            )
            .annotate(
                graph_region_occurences=Subquery(
                    RegionGraph.objects.filter(
                        related_region=OuterRef('related_region'),
                        region__type='community',
                    )
                    .order_by()
                    .values('related_region')
                    .annotate(
                        c=Count('*'),
                    )
                    .values('c')[:1],
                    output_field=IntegerField(),
                )
            )
            .filter(graph_region_occurences__gt=1)
        )
        region_graphs = {}
        for graph in duplicated_graphs:
            region_graphs[graph.related_region_id] = graph
        for graph in region_graphs.values():
            graph.delete()
        print(f"Removed duplicated communities: {len(region_graphs)}")

        RegionGraph.objects.filter(
            Q(related_region__type=RegionType.CITY) | Q(related_region__type=RegionType.VILLAGE),
            region__type=RegionType.COMMUNITY,
        ).annotate(
            county=Subquery(
                RegionGraph.objects.filter(
                    related_region=OuterRef('region_id'),
                    region__type=RegionType.COUNTY,
                ).values('region_id')[:1]
            ),
        ).update(
            region=F('county')
        )

        Region.objects.filter(type=RegionType.COMMUNITY).delete()
        RegionGraph.objects.filter(
            region__type=RegionType.STATE,
            related_region__type=RegionType.CITY,
        ).delete()
        print("== ending values ==")
        print_variables()
        print("=== FINISH REMOVING COMMUNITY REGIONS SCRIPT ===")
        if dry_run:
            # Rollback
            raise Exception


if __name__ == "__main__":
    if settings.API_COUNTRY is not Country.PL:
        raise Exception("Only PL country is implemented!")
    parser = argparse.ArgumentParser()
    parser.add_argument('--dry-run', action='store_true')
    args = parser.parse_args()
    remove_community_regions(dry_run=args.dry_run)
