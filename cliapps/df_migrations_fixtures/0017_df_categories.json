[{"model": "df_creator.digitalflyercategory", "pk": 3, "fields": {"type": "availability", "image": 15001217, "codename": "availability", "order": 4}}, {"model": "df_creator.digitalflyercategory", "pk": 4, "fields": {"type": "marketing", "image": 15001218, "codename": "marketing", "order": 0}}, {"model": "df_creator.digitalflyercategory", "pk": 8, "fields": {"type": "special_occasions", "image": 15001219, "codename": "special_occasions", "order": 2}}, {"model": "df_creator.digitalflyercategory", "pk": 9, "fields": {"type": "reviews", "image": 16166190, "codename": "reviews", "order": 1}}]