from lib.enums import StrEnum


class SecretName(StrEnum):
    # default
    CORE_DEFAULT_DB_USER = 'CORE_DEFAULT_DB_USER'
    CORE_DEFAULT_DB_PASSWORD = 'CORE_DEFAULT_DB_PASSWORD'
    CORE_READ_ONLY_DB_USER = 'CORE_READ_ONLY_DB_USER'
    CORE_READ_ONLY_DB_PASSWORD = 'CORE_READ_ONLY_DB_PASSWORD'
    CORE_REPORTS_DB_USER = 'CORE_REPORTS_DB_USER'
    CORE_REPORTS_DB_PASSWORD = 'CORE_REPORTS_DB_PASSWORD'
    CORE_REPORTS_READ_ONLY_DB_USER = 'CORE_REPORTS_READ_ONLY_DB_USER'
    CORE_REPORTS_READ_ONLY_DB_PASSWORD = 'CORE_REPORTS_READ_ONLY_DB_PASSWORD'
    CORE_ADYEN_DB_USER = 'CORE_ADYEN_DB_USER'
    CORE_ADYEN_DB_PASSWORD = 'CORE_ADYEN_DB_PASSWORD'
    PAYMENT_DB_USER = 'PAYMENT_DB_USER'
    PAYMENT_DB_PASSWORD = 'PAYMENT_DB_PASSWORD'
    DRAFTS_DB_USER = 'DRAFTS_DB_USER'
    DRAFTS_DB_PASSWORD = 'DRAFTS_DB_PASSWORD'
    # admin
    ADMIN_DB_USER = 'ADMIN_DB_USER'
    ADMIN_DB_PASSWORD = 'ADMIN_DB_PASSWORD'
    # celery
    CELERY_DB_USER = 'CELERY_DB_USER'
    CELERY_DB_PASSWORD = 'CELERY_DB_PASSWORD'
    # grpc
    GRPC_API_DB_USER = 'GRPC_API_DB_USER'
    GRPC_API_DB_PASSWORD = 'GRPC_API_DB_PASSWORD'
    # public_api
    PUBLIC_API_DB_USER = 'PUBLIC_API_DB_USER'
    PUBLIC_API_DB_PASSWORD = 'PUBLIC_API_DB_PASSWORD'
    # reports_api
    REPORTS_API_DB_USER = 'REPORTS_API_DB_USER'
    REPORTS_API_DB_PASSWORD = 'REPORTS_API_DB_PASSWORD'
    # search_api
    SEARCH_API_DB_USER = 'SEARCH_API_DB_USER'
    SEARCH_API_DB_PASSWORD = 'SEARCH_API_DB_PASSWORD'
