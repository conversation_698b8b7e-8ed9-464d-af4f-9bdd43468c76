import json
import os
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import List, Callable

from encrypted_secrets import (
    load_secrets,
    YAMLFormatException,
    SecretsManager as EncryptedSecretsManager,
)
from glom import glom
from google.cloud import secretmanager as google_secret_manager

from lib.check_connectivity import ensure_connectivity


@dataclass
class GCPSecretsConfig:
    project_id: str
    json_blobs: List[str] = field(default_factory=list)
    region_prefix: str = ''
    should_fetch_condition: Callable[[str], bool] | None = None


class GCPClientRegistry:
    _instances = []

    @classmethod
    def register(cls, client):
        cls._instances.append(client)

    @classmethod
    def close_all(cls):
        for client in cls._instances:
            client.close()
        cls._instances.clear()


class BaseSecretsProvider(ABC):
    @abstractmethod
    def get(self, key: str, default_value: str = '') -> str:
        pass

    def read_secret(self, key: str, default_value: str = '') -> str:
        return self.get(key, default_value)


class GCPClientHandler:
    def __init__(self, project_id: str):
        self.project_id = project_id
        self.client = None
        self.pid = os.getpid()
        GCPClientRegistry.register(self)

    def _reinit_client(self):
        if self.client:
            self.client.transport.close()
        self.client = google_secret_manager.SecretManagerServiceClient()

    def ensure_client(self):
        current_pid = os.getpid()
        if current_pid != self.pid or self.client is None:
            self._reinit_client()
            self.pid = current_pid

    def fetch_secret(self, secret_name: str) -> str | None:
        name = f"projects/{self.project_id}/secrets/{secret_name}/versions/latest"
        try:
            value = self.client.access_secret_version(request={'name': name}).payload.data.decode(
                'UTF-8'
            )
            return value
        except Exception:  # pylint: disable=broad-exception-caught
            return None

    def close(self):
        if self.client:
            self.client.transport.close()
            del self.client
            self.client = None


class GCPBlobSecretsProvider(BaseSecretsProvider):
    def __init__(self, config: GCPSecretsConfig):
        self.client_handler = GCPClientHandler(config.project_id)
        self.json_blobs = config.json_blobs or []
        self.blob_secrets = {}

    def _load_json_blobs(self):

        ensure_connectivity()
        self.client_handler.ensure_client()

        for secret_name in self.json_blobs:
            payload = self.client_handler.fetch_secret(secret_name)
            if payload:
                try:
                    parsed_secret = json.loads(payload)
                    self.blob_secrets.update(parsed_secret)
                except json.JSONDecodeError:
                    continue

    def get(self, key, default_value=''):
        if not self.blob_secrets and self.json_blobs:
            self._load_json_blobs()

        if key in self.blob_secrets:
            return self.blob_secrets[key]
        return default_value


class GCPKVSecretsProvider(BaseSecretsProvider):
    def __init__(self, config: GCPSecretsConfig):
        self.client_handler = GCPClientHandler(config.project_id)
        self.region_prefix = config.region_prefix
        self.should_fetch_condition = config.should_fetch_condition

    def get(self, key, default_value=''):
        ensure_connectivity()
        self.client_handler.ensure_client()

        if self.should_fetch_condition and not self.should_fetch_condition(key):
            return default_value

        full_key = f"{self.region_prefix}{key}" if self.region_prefix else key
        result = self.client_handler.fetch_secret(full_key)

        if result is not None:
            return result
        return default_value


class GCPAdaptiveSecretsProvider(BaseSecretsProvider):
    def __init__(self, config: GCPSecretsConfig):
        if config.json_blobs:
            self.backend = GCPBlobSecretsProvider(config)
        else:
            self.backend = GCPKVSecretsProvider(config)

    def get(self, key, default_value=''):
        return self.backend.get(key, default_value)


class DjangoSecretsProvider(BaseSecretsProvider):
    def __init__(self, encrypted_file_path: str, master_key: str):
        self.file_path = encrypted_file_path
        self.master_key = master_key
        self.secrets = None

    def get(self, key, default_value=''):
        if self.secrets is None:
            self._load()

        try:
            secret_value = glom(
                self.secrets,
                key,
            )
        except KeyError:
            secret_value = default_value

        return self._replace_vars(secret_value)

    def _load(self):
        if self.secrets is None:
            try:
                load_secrets(
                    encrypted_secrets_file_path=self.file_path,
                    key=self.master_key,
                )
                self.secrets = EncryptedSecretsManager.secrets
            except YAMLFormatException:
                print("\n\n\nMALFORMED YAML IN ENCRYPTED SECRETS\n\n\n")

    @staticmethod
    def _replace_vars(value):
        for key in [
            'BOOKSY_COUNTRY_CODE',
            'BOOKSY_REDIS_DB',
            'BASE_URL',
            'ENVIRONMENT_DOMAIN',
            'NAMESPACE',
        ]:
            if key in os.environ:
                value = str(value).replace(f'${key}', os.environ[key])
        return value


class DummySecretsProvider(BaseSecretsProvider):
    def get(self, key, default_value=''):
        return key
