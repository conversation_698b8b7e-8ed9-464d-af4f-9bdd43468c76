from env_secrets.v2.secrets_providers import (
    GCPKVS<PERSON>retsProvider,
    GCPSecretsConfig,
)
from env_secrets.v2.tests.dummy_gcp_client_handler import DummyGCPClientHandler

prd_secrets = {
    'es-core-NAVISION_OAUTH2_KEY': 'PRD_NAVISION_SECRET_KEY_ES',
    'gb-core-ADYEN_SECRET': 'PRD_ADYEN_SECRET_GB',
    'ADYEN_SECRET': 'PRD_ADYEN_SECRET_GENERAL',
    'FETCHED_SECRET': 'PRD_FETCHED_SECRET',
}


def should_fetch(secret_name: str) -> bool:
    return secret_name == 'FETCHED_SECRET'


def test_gcp_kv_secrets_provider():
    config = GCPSecretsConfig(project_id='dummy-id')
    provider = GCPKVSecretsProvider(config)
    provider.client_handler = DummyGCPClientHandler(prd_secrets)
    assert provider.get('ADYEN_SECRET') == 'PRD_ADYEN_SECRET_GENERAL'


def test_gcp_kv_secrets_provider_prefixed_secret():
    config = GCPSecretsConfig(project_id='dummy-id', region_prefix="gb-core-")
    provider = GCPKVSecretsProvider(config)
    provider.client_handler = DummyGCPClientHandler(prd_secrets)
    assert provider.get('ADYEN_SECRET') == 'PRD_ADYEN_SECRET_GB'


def test_gcp_kv_secrets_provider_should_fetch():
    config = GCPSecretsConfig(project_id='dummy-id', should_fetch_condition=should_fetch)
    provider = GCPKVSecretsProvider(config)
    provider.client_handler = DummyGCPClientHandler(prd_secrets)
    assert provider.get('FETCHED_SECRET', 'default') == 'PRD_FETCHED_SECRET'
    assert provider.get('ADYEN_SECRET', 'default') == 'default'
