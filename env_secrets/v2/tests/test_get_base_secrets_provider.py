import os

from mock import patch

from env_secrets.v2.base_core_secret_provider_factory import get_base_secrets_provider
from env_secrets.v2.secrets_providers import (
    DjangoSecrets<PERSON>rovider,
    GCPKVSecretsProvider,
    GCPBlobSecretsProvider,
    DummySecretsProvider,
    GCPAdaptiveSecretsProvider,
)


@patch.dict(
    os.environ,
    {
        'BOOKSY_VARIANT': 'live',
        'SECRET_MANAGER_PROJECT_ID': 'bks-secrets-prd',
    },
)
def test_live_secrets_provider_selection():
    get_base_secrets_provider.cache_clear()
    provider = get_base_secrets_provider()
    assert isinstance(provider, GCPAdaptiveSecretsProvider)
    assert isinstance(provider.backend, GCPKVSecretsProvider)
    assert provider.backend.client_handler.project_id == 'bks-secrets-prd'
    assert provider.backend.should_fetch_condition is not None


@patch.dict(
    os.environ,
    {
        'BOOKSY_VARIANT': 'test',
        'BOOKSY_COUNTRY_CODE': 'fr',
        'DJANGO_MASTER_KEY': '',
    },
    clear=True,
)
def test_t1_secrets_provider_selection():
    get_base_secrets_provider.cache_clear()
    provider = get_base_secrets_provider()
    assert isinstance(provider, GCPAdaptiveSecretsProvider)
    assert isinstance(provider.backend, GCPBlobSecretsProvider)
    assert provider.backend.json_blobs == ['core-shared', 'core-shared-2', 'fr-core']
    assert provider.backend.client_handler.project_id == 'bks-secrets-dev'


@patch.dict(
    os.environ,
    {
        'BOOKSY_VARIANT': 'local',
        'DJANGO_MASTER_KEY': '12345',
    },
)
def test_django_secrets_provider_selection():
    get_base_secrets_provider.cache_clear()
    provider = get_base_secrets_provider()
    assert isinstance(provider, DjangoSecretsProvider)
    assert provider.master_key == '12345'


@patch.dict(
    os.environ,
    {
        'BOOKSY_VARIANT': 'local',
    },
    clear=True,
)
def test_dev_secrets_provider_selection():
    get_base_secrets_provider.cache_clear()
    provider = get_base_secrets_provider()
    assert isinstance(provider, DummySecretsProvider)
