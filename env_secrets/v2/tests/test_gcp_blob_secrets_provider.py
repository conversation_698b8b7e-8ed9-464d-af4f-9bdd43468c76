from env_secrets.v2.secrets_providers import GCPBlobSecrets<PERSON><PERSON><PERSON>, GCPSecretsConfig
from env_secrets.v2.tests.dummy_gcp_client_handler import DummyGCPClientHandler


SHARED_JSON = '{"name": "<PERSON>", "surname": "<PERSON>"}'
COUNTRY_SPECIFIC_JSON = '{"surname": "<PERSON><PERSON>", "occupation": "chemist"}'

BLOB_SECRETS = {
    'core-shared': SHARED_JSON,
    'us-core': COUNTRY_SPECIFIC_JSON,
}


def test_gcp_blob_secrets_provider():
    config = GCPSecretsConfig(project_id='dummy-id', json_blobs=['core-shared', 'us-core'])
    provider = GCPBlobSecretsProvider(config)
    provider.client_handler = DummyGCPClientHandler(BLOB_SECRETS)

    assert provider.read_secret('name') == '<PERSON>'
    # the "surname" secret from the default secrets has been overwritten by country specific secret
    assert provider.read_secret('surname') == '<PERSON><PERSON>'
    # the "occupation" secret come from a country specific secrets
    assert provider.read_secret('occupation') == 'chemist'
