from mock import patch

from env_secrets.v2.base_core_secret_provider_factory import should_fetch_secret_condition_factory

SECRETS_STRUCTURE = {
    'NAVISION_OAUTH2_KEY': {'dev': 'pl,us', 'prd': 'es'},
    'ADYEN_SECRET': {'dev': '', 'prd': 'gb'},
}


@patch(
    'env_secrets.v2.base_core_secret_provider_factory.json.load',
    return_value=SECRETS_STRUCTURE,
)
@patch(
    'env_secrets.v2.base_core_secret_provider_factory.open',
)
def test_should_fetch_condition_factory(open_mock, secrets_structure_mock):
    condition = should_fetch_secret_condition_factory('dummy_dir', 'live', 'es')
    assert condition('NAVISION_OAUTH2_KEY') is True
    assert condition('ADYEN_SECRET') is False
