import pytest

from env_secrets.v2.secrets_providers import (
    GCPKVSecretsProvider,
    GCPSecretsConfig,
    GCPAdaptiveSecretsProvider,
    GCPBlobSecretsProvider,
)
from env_secrets.v2.tests.dummy_gcp_client_handler import DummyGCPClientHandler

SHARED_JSON = '{"name": "<PERSON>", "surname": "<PERSON>"}'

SECRETS = {
    'core-shared': SHARED_JSON,
    'db_password': 'supersecure',
}


@pytest.mark.parametrize(
    'config, expected_class, key, expected_value',
    [
        (
            GCPSecretsConfig(project_id='dummy-id', json_blobs=['core-shared']),
            GCPBlobSecretsProvider,
            'name',
            '<PERSON>',
        ),
        (
            GCPSecretsConfig(
                project_id='dummy-id',
            ),
            GCPKVSecretsProvider,
            'db_password',
            'supersecure',
        ),
    ],
)
def test_gcp_adaptive_secrets_provider(config, expected_class, key, expected_value):
    provider = GCPAdaptiveSecretsProvider(config)
    assert isinstance(provider.backend, expected_class)
    provider.backend.client_handler = DummyGCPClientHandler(SECRETS)
    assert provider.get(key) == expected_value
