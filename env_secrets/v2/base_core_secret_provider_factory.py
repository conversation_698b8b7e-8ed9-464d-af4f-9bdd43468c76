import json
import os
from functools import lru_cache

from env_secrets.v2.secrets_providers import (
    GCPAdaptiveSecretsProvider,
    DummySecretsProvider,
    DjangoSecretsProvider,
    GCPSecretsConfig,
    BaseSecretsProvider,
)


def should_fetch_secret_condition_factory(project_dir: str, booksy_variant: str, country: str):
    @lru_cache
    def secrets_structure():
        path = os.path.join(project_dir, 'env_secrets', 'secrets_structure.json')
        with open(path, 'rb') as file:
            return json.load(file)

    def should_fetch_condition(secret_name: str) -> bool:
        environment = 'prd' if booksy_variant == 'live' else 'dev'
        try:
            return country in secrets_structure()[secret_name][environment]
        except KeyError:
            return False

    return should_fetch_condition


@lru_cache(maxsize=1)
def get_base_secrets_provider() -> BaseSecretsProvider:
    variant = os.environ.get('BOOKSY_VARIANT')
    project_id = os.environ.get('SECRET_MANAGER_PROJECT_ID', 'bks-secrets-dev')
    api_country = os.environ.get('BOOKSY_COUNTRY_CODE')
    project_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

    if variant == 'live':
        return GCPAdaptiveSecretsProvider(
            GCPSecretsConfig(
                project_id=project_id,
                region_prefix=f'{api_country}-core-',
                should_fetch_condition=should_fetch_secret_condition_factory(
                    project_path, variant, api_country
                ),
            )
        )
    if variant == 'test' and 'PYTEST' not in os.environ:
        return GCPAdaptiveSecretsProvider(
            GCPSecretsConfig(
                project_id=project_id,
                json_blobs=["core-shared", "core-shared-2", f"{api_country}-core"],
            )
        )
    if master_key := os.environ.get('DJANGO_MASTER_KEY'):
        return DjangoSecretsProvider(
            encrypted_file_path=os.path.join(project_path, 'secrets.yml.enc'), master_key=master_key
        )
    return DummySecretsProvider()
