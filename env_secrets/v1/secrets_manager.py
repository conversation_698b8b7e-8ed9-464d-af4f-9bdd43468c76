import json
import os

from functools import lru_cache
from encrypted_secrets import (
    load_secrets,
    SecretsManager as EncryptedSecretsManager,
    YAMLFormatException,
)
from glom import glom
from google.cloud import secretmanager as google_secret_manager


class SecretsManager:
    """
    Handles secrets based on the deployment level
    """

    def __init__(self):
        variant = os.environ.get('BOOKSY_VARIANT')

        if variant == 'live':
            self.provider = ProductionSecretsProvider
        elif variant == 'test' and self.not_pytest():
            self.provider = DevSecretsProvider
        elif os.environ.get('DJANGO_MASTER_KEY'):
            self.provider = DjangoSecretsProvider
        else:
            self.provider = DummySecretsProvider

        self.provider.load_all_secrets()
        self.provider.project_dir = os.path.dirname(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        )
        self.provider.environment = 'prd' if variant == 'live' else 'dev'

    @staticmethod
    def not_pytest():
        return 'PYTEST' not in os.environ


class DjangoSecretsProvider:
    secrets = None

    @classmethod
    def load_all_secrets(cls):
        if not cls.secrets:
            try:
                project_dir = os.path.dirname(
                    os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                )
                django_master_key = os.environ.get('DJANGO_MASTER_KEY')

                load_secrets(
                    encrypted_secrets_file_path=f'{project_dir}/secrets.yml.enc',
                    key=django_master_key,
                )
                cls.secrets = EncryptedSecretsManager.secrets
            except YAMLFormatException:
                print("\n\n\nMALFORMED YAML IN ENCRYPTED SECRETS\n\n\n")

    @classmethod
    def replace_vars(cls, value):
        for key in [
            'BOOKSY_COUNTRY_CODE',
            'BOOKSY_REDIS_DB',
            'BASE_URL',
            'ENVIRONMENT_DOMAIN',
            'NAMESPACE',
        ]:
            if key not in os.environ:
                continue
            value = str(value).replace(f'${key}', os.environ[key])
        return value

    @classmethod
    def read_secret(cls, secret_name: str, default_value: str = '') -> str:
        try:
            secret_value = glom(
                cls.secrets,
                secret_name,
            )
        except KeyError:
            secret_value = default_value

        return cls.replace_vars(secret_value)


class DevSecretsProvider(DjangoSecretsProvider):
    @classmethod
    def load_all_secrets(cls):
        if not cls.secrets:
            cls.secrets = {}
            for secret in ('core-shared', 'core-shared-2'):
                cls.secrets.update(cls._read_json_secrets(secret))

            api_country = os.environ.get('BOOKSY_COUNTRY_CODE')
            country_specific_secrets = cls._read_json_secrets(f'{api_country}-core')
            cls.secrets.update(country_specific_secrets)

    @classmethod
    def _read_json_secrets(cls, secret_name):
        google_cloud_secrets = cls._read_google_cloud_secret(secret_name)
        return json.loads(google_cloud_secrets)

    @classmethod
    def _read_google_cloud_secret(cls, secret_name):
        project_id = os.environ.get('SECRET_MANAGER_PROJECT_ID', 'bks-secrets-dev')
        client = google_secret_manager.SecretManagerServiceClient()
        name = f"projects/{project_id}/secrets/{secret_name}/versions/latest"
        response = client.access_secret_version(request={'name': name}).payload.data.decode('UTF-8')
        client.transport.close()
        return response


class ProductionSecretsProvider(DjangoSecretsProvider):
    pid = -1
    secret_manager_client = None

    @classmethod
    def load_all_secrets(cls) -> None:
        cls.pid = os.getpid()
        if cls.secret_manager_client is None:
            cls.secret_manager_client = google_secret_manager.SecretManagerServiceClient()

    @classmethod
    @lru_cache
    def secrets_structure(cls):
        return json.loads(
            open(f'{cls.project_dir}/env_secrets/secrets_structure.json', 'rb').read()
        )

    @classmethod
    def secret_exists_in_country(cls, country: str, secret_name: str) -> bool:
        try:
            exists = country in cls.secrets_structure()[secret_name][cls.environment]
        except KeyError:
            exists = False
        return exists

    @classmethod
    def close_client(cls):
        if cls.secret_manager_client is not None:
            cls.secret_manager_client.transport.close()
            del cls.secret_manager_client
            cls.secret_manager_client = None

    @classmethod
    def read_secret(cls, secret_name: str, default_value: str = '') -> str:
        if os.getpid() != cls.pid or cls.secret_manager_client is None:
            cls.load_all_secrets()

        project_id = os.environ.get('SECRET_MANAGER_PROJECT_ID', 'bks-secrets-dev')
        api_country = os.environ.get('BOOKSY_COUNTRY_CODE')
        response = default_value

        if cls.secret_exists_in_country(country=api_country, secret_name=secret_name):
            from lib.check_connectivity import ensure_connectivity

            ensure_connectivity()

            name = f"projects/{project_id}/secrets/{api_country}-core-{secret_name}/versions/latest"
            response = cls.secret_manager_client.access_secret_version(
                request={'name': name}
            ).payload.data.decode('UTF-8')
            # You can learn more about the secret manager by reading the official documentation
            # https://cloud.google.com/secret-manager/docs/creating-and-accessing-secrets#access

        return response


class DummySecretsProvider:
    secrets = {}

    @classmethod
    def read_secret(
        cls, secret_name: str, default_value: str = ''  # pylint: disable=unused-argument
    ) -> str:
        return secret_name

    @classmethod
    def load_all_secrets(cls):
        pass
