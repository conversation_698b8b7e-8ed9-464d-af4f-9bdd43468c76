import os

from mock import patch
from env_secrets.v1.secrets_manager import DjangoSecretsProvider


@patch.dict(
    os.environ,
    {
        'BOOKSY_COUNTRY_CODE': 'pl',
        'BOOKSY_REDIS_DB': '1',
        'BASE_URL': 'pytest.booksy.pm',
        'ENVIRONMENT_DOMAIN': 'pytest2.booksy.pm',
        'NAMESPACE': 'namespace_x',
    },
)
def test_replace_env_variables():
    provider = DjangoSecretsProvider

    assert provider.replace_vars('booksy-$BOOKSY_COUNTRY_CODE') == 'booksy-pl'
    assert provider.replace_vars('redis-$BOOKSY_REDIS_DB') == 'redis-1'
    assert provider.replace_vars('https://$BASE_URL/') == 'https://pytest.booksy.pm/'
    assert provider.replace_vars('https://$ENVIRONMENT_DOMAIN/') == 'https://pytest2.booksy.pm/'
    assert provider.replace_vars('test-$NAMESPACE-test') == 'test-namespace_x-test'
    assert provider.replace_vars('jesse-$NON_EXISTING') == 'jesse-$NON_EXISTING'


@patch('encrypted_secrets.load_secrets')
def test_read_secret(_):
    provider = DjangoSecretsProvider
    if provider.secrets is None:
        provider.secrets = {'test_key': 'test_value'}
    else:
        provider.secrets.update({'test_key': 'test_value'})

    assert provider.read_secret('non_existing_key') == ''
    assert (
        provider.read_secret(
            'non_existing_key',
            default_value='DEFAULT_VALUE',
        )
        == 'DEFAULT_VALUE'
    )
    assert provider.read_secret('test_key') == 'test_value'
