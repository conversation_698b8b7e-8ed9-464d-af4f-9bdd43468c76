import os

from mock import patch
from env_secrets.v1.secrets_manager import (
    Django<PERSON><PERSON><PERSON><PERSON><PERSON>ider,
    SecretsManager,
    ProductionSecretsProvider,
    DummySecretsProvider,
)


@patch.dict(
    os.environ,
    {
        'BOOKSY_VARIANT': 'live',
    },
)
@patch('env_secrets.v1.secrets_manager.ProductionSecretsProvider.load_all_secrets')
def test_live_secrets_provider_selection(load_all_secrets_mock):
    assert SecretsManager().provider is ProductionSecretsProvider
    load_all_secrets_mock.assert_called_once()


@patch.dict(
    os.environ,
    {
        'BOOKSY_VARIANT': 'local',
        'DJANGO_MASTER_KEY': '12345',
    },
)
@patch('env_secrets.v1.secrets_manager.DjangoSecretsProvider.load_all_secrets')
def test_django_secrets_provider_selection(load_all_secrets_mock):
    assert SecretsManager().provider is DjangoSecretsProvider
    load_all_secrets_mock.assert_called_once()


@patch.dict(
    os.environ,
    {
        'BOOKSY_VARIANT': 'local',
        'DJANGO_MASTER_KEY': '',
    },
)
@patch('env_secrets.v1.secrets_manager.DummySecretsProvider.load_all_secrets')
def test_dev_secrets_provider_selection(load_all_secrets_mock):
    assert SecretsManager().provider is DummySecretsProvider
    load_all_secrets_mock.assert_called_once()
