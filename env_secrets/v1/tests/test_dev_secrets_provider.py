import os

from mock import patch
from env_secrets.v1.secrets_manager import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def read_google_secret_side_effect(secret_name):
    shared_json = '{"name": "<PERSON>", "surname": "<PERSON>"}'
    country_specific_json = '{"surname": "<PERSON><PERSON>", "occupation": "chemist"}'

    return shared_json if secret_name == 'core-shared' else country_specific_json


@patch.dict(
    os.environ,
    {'BOOKSY_COUNTRY_CODE': 'pl', 'BOOKSY_VARIANT': 'test'},
)
@patch(
    'env_secrets.v1.secrets_manager.DevSecretsProvider._read_google_cloud_secret',
    side_effect=read_google_secret_side_effect,
)
@patch(
    'env_secrets.v1.secrets_manager.SecretsManager.not_pytest',
    return_value=True,
)
def test_google_secrets_provider_secrets_merging(_pytest_mock, _read_google_cloud_secret_mock):
    DevSecretsProvider.secrets = None
    provider = SecretsManager().provider

    assert provider is DevSecrets<PERSON><PERSON>ider
    # the "name" secret come from the default secrets
    assert provider.read_secret('name') == 'Walter'
    # the "surname" secret from the default secrets has been overwritten by country specific secret
    assert provider.read_secret('surname') == '<PERSON>man'
    # the "occupation" secret come from a country specific secrets
    assert provider.read_secret('occupation') == 'chemist'
