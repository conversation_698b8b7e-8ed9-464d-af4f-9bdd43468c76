import os

from mock import patch, <PERSON><PERSON>, MagicMock
from env_secrets.v1.secrets_manager import SecretsManager, ProductionSecretsProvider

prd_secrets = {
    'es-core-NAVISION_OAUTH2_KEY': 'PRD_NAVISION_SECRET_KEY_ES',
    'gb-core-ADYEN_SECRET': 'PRD_ADYEN_SECRET_GB',
}


class FakeSecretManagerClient:
    def __init__(self, secrets):
        self.secrets = secrets

    def access_secret_version(self, request):
        secret_name = request['name'].split('/')[3]
        return Mock(
            payload=Mock(data=Mock(decode=MagicMock(return_value=self.secrets[secret_name])))
        )

    @classmethod
    def secrets_structure(cls):
        return {
            "NAVISION_OAUTH2_KEY": {"dev": "pl,us", "prd": "es"},
            "ADYEN_SECRET": {"dev": "", "prd": "gb"},
        }


def google_provider(secrets):
    ProductionSecretsProvider.load_all_secrets = MagicMock()

    provider = SecretsManager().provider
    provider.secret_manager_client = FakeSecretManagerClient(secrets=secrets)
    provider.secrets_structure = FakeSecretManagerClient.secrets_structure

    return provider


@patch.dict(
    os.environ,
    {'BOOKSY_VARIANT': 'live', 'BOOKSY_COUNTRY_CODE': 'es'},
)
def test_google_secrets_provider_prd():
    assert (
        google_provider(secrets=prd_secrets).read_secret('NAVISION_OAUTH2_KEY')
        == 'PRD_NAVISION_SECRET_KEY_ES'
    )
