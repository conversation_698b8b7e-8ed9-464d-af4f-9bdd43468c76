.dockerignore
.gitlab-ci.yml
.gitignore
.git
# ignore file from pytest
**/__pycache__/
**/.pytest_cache/
**/.cache/
# ignore compiled
**/*.pyc
.docker_volume/

# Ignore deploy directory
deploy/*

# Allow files and directories from deploy directory
!deploy/action
!deploy/conf
!deploy/init-script.sh
!deploy/init-script-prod.sh
!deploy/gcp-dev-init-script.sh
.venv/*
venv/*

# PY-2066 for TBD
!deploy/tbd-init-db.sh
!deploy/tbd-init-elastic.sh
!deploy/tbd-init-execute-scripts.sh
!deploy/tbd-init-other.sh
.env
