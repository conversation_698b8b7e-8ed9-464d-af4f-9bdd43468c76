import typing as t

from django.db.models import F
from rest_framework.serializers import Serializer

from drf_api.service.help_center.providers import AbstractDataProvider
from webapps.help_center.models import Category


class BaseDataHandler:
    @classmethod
    def serialized_data(cls, *args, **kwargs) -> t.Optional[t.Dict]:
        raise NotImplementedError


class CategoriesDataHandler(BaseDataHandler):
    @classmethod
    def serialized_data(  # pylint: disable=arguments-differ
        cls,
        serializer_class: t.Type[Serializer],
        language: str,
        excluded_codenames: t.Optional[list] = None,
    ) -> t.Dict:
        base_qs = Category.objects.filter(
            parent=None,
            contents__language=language,
            contents__deleted__isnull=True,
            id__in=Category.root_category_ids_with_localized_articles(language),
        )
        if excluded_codenames:
            base_qs = base_qs.exclude(codename__in=excluded_codenames)

        results = (
            base_qs.annotate(name=F('contents__name'))
            .order_by('order', 'name')
            .values('id', 'name')
        )
        return serializer_class(results, many=True).data


class InstanceDataHandler(BaseDataHandler):
    @classmethod
    def serialized_data(  # pylint: disable=arguments-differ
        cls,
        instance,
        data_provider_class: t.Type[AbstractDataProvider],
        serializer_class: t.Type[Serializer],
        language: str,
    ) -> t.Optional[t.Dict]:
        data_provider = data_provider_class(instance=instance, language=language)
        if data_provider.has_incomplete_data():
            return None

        return serializer_class(data_provider).data
