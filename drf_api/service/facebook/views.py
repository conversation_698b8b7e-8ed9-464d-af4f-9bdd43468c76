from logging import getLogger

from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from django.utils.translation import gettext as _
from rest_framework import status
from rest_framework.exceptions import PermissionDenied
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import Request, Response

from drf_api.base_views import BaseBooksyNoSessionGenericAPIView, BaseBooksySessionGenericAPIView
from drf_api.mixins import ExtendedLogsMixin
from drf_api.service.business.utils import access
from drf_api.service.facebook.serializers import (
    FacebookConnectBusinessRequestSerializer,
    FacebookConnectCustomerRequestSerializer,
    FacebookGraphSignInCustomerRequestSerializer,
    FacebookSignInBusinessRequestSerializer,
    FacebookSignInCustomerRequestSerializer,
)
from drf_api.service.facebook.tasks import upload_facebook_photo_task
from drf_api.service.facebook.tools import (
    FacebookGraphData,
    FacebookJWTDecodeException,
    FacebookJwtToken,
    download_picture,
    get_facebook_graph_data,
    get_verified_token,
)
from lib.email_internal import is_private_email
from lib.feature_flag.feature import CustomerQuickSignInUpFlag
from lib.feature_flag.feature.customer import (
    AutomaticAccountLinkingFlag,
    RefactorSaveRegisterAgreements,
    FixAutomaticAccountLinkingCellPhoneFlag,
)
from lib.tools import tznow
from service.exceptions import ServiceError
from service.mixins.throttling import get_django_user_ip
from service.mixins.validation import validate_serializer
from service.tools import RequestHandler, is_allowed_request_for_business_ids
from webapps.business.events import business_user_logged_in
from webapps.notification.scenarios import AccountAddedScenario, start_scenario
from webapps.photo.models import Photo
from webapps.segment.tasks import analytics_customer_registration_completed_task
from webapps.user.enums import AuthOriginEnum
from webapps.user.models import User, UserProfile
from webapps.user.serializers import (
    BusinessFacebookSignInSerializer,
    CustomerFacebookSignInSerializer,
    CustomerFacebookSignInSerializerOld,
)
from webapps.versum_migration.user_connection.customer_registration import (
    versum_migration_handle_customer_registration_completed,
)

logger = getLogger('booksy.facebook')


def _get_profile_key(profile: UserProfile) -> str:
    return 'customer' if profile.profile_type == UserProfile.Type.CUSTOMER else 'account'


class FacebookSignInMixin:
    client_id = None

    @property
    def allow_account_linking(self) -> bool:
        return False

    def verify_id_token(self, id_token: str, nonce: str) -> FacebookJwtToken:
        """Verifies an ID token"""
        try:
            return get_verified_token(id_token, nonce, self.client_id)
        except FacebookJWTDecodeException as e:
            logger.warning(
                'Facebook invalid encoded token',
                extra={
                    'id_token': id_token,
                },
            )
            raise ServiceError(
                code=status.HTTP_400_BAD_REQUEST,
                errors=[
                    {
                        'code': 'facebook_token_verification_error',
                        'description': str(e),
                    }
                ],
            ) from e

    def get_validated_data(self, data: dict) -> dict:
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        return serializer.validated_data

    def handle_whitelisting_ip_access_for_staffer(self, user: User) -> None:
        """Based on `service.tools.RequestHandler.handle_whitelisting_ip_access_for_staffer`
        but specific to DRF and requires user object as argument."""
        # pylint: disable=protected-access
        business_ids = RequestHandler._get_user_business_ids(user)
        if not is_allowed_request_for_business_ids(business_ids, get_django_user_ip(self.request)):
            raise PermissionDenied

    def get_or_create_user(self, data: dict) -> (User, bool):
        decoded_token = self.verify_id_token(data['id_token'], data['nonce'])
        user_data = {k: data[k] for k in ['cell_phone', 'sms_code'] if k in data}
        return self._get_or_create_user(decoded_token, user_data)

    def _get_or_create_user(
        self, decoded_token: FacebookJwtToken, data: dict
    ) -> (User, UserProfile, bool, str):
        facebook_id = decoded_token.sub
        user: User = User.objects.filter(facebook_id=facebook_id).first()
        connected_with = ''
        if self.allow_account_linking and AutomaticAccountLinkingFlag():
            qset = User.objects.filter(
                email=decoded_token.email,
                cell_phone__isnull=False,
                profiles__profile_type=UserProfile.Type.CUSTOMER,
            )
            if FixAutomaticAccountLinkingCellPhoneFlag():
                qset = qset.exclude(cell_phone='')
            if not user and (user := qset.first()):
                logger.info(
                    '[FACEBOOK ACCOUNT CONNECT] account connected with '
                    'existing account (user id: %s)',
                    user.id,
                )
                user.facebook_id = facebook_id
                user.last_login = tznow()
                user.save()
                user.initiate_password_reset(
                    self.profile_type, self.booking_source, self._get_language()
                )
                user.delete_all_user_sessions()
                connected_with = 'facebook'
        else:
            if not user and User.objects.filter(email=decoded_token.email).exists():
                logger.warning(
                    'Facebook invalid decoded token',
                    extra={
                        'id_token': decoded_token,
                    },
                )
                raise ServiceError(
                    code=status.HTTP_400_BAD_REQUEST,
                    errors=[
                        {
                            'code': 'facebook_is_not_connected_with_profile',
                            'description': _(
                                'Facebook account is not connected with your profile.'
                            ),
                        }
                    ],
                )
        if created := user is None:
            user_data = {
                'email': decoded_token.email,
                'first_name': decoded_token.given_name,
                'last_name': decoded_token.family_name,
                'facebook_id': facebook_id,
            } | data
            if RefactorSaveRegisterAgreements():
                user_data['user_agreements'] = self.request.data.get('user_agreements')
            serializer = self.user_serializer_class(
                data=user_data,
                context={
                    'booking_source': self.booking_source,
                    'language': self._get_language(),
                },
            )
            try:
                validate_serializer(serializer)
            except ServiceError:
                logger.warning(
                    'Facebook invalid decoded token',
                    extra={
                        'id_token': decoded_token,
                    },
                )
                raise

            user = serializer.save()

        profile, profile_created = self._get_or_create_userprofile(user)
        if created or profile_created:
            upload_facebook_photo_task.delay(profile.id, decoded_token.picture)

        return user, profile, created, connected_with

    def _get_language(self):
        return self.language or settings.LANGUAGE_CODE[:2].lower()

    def _get_or_create_userprofile(self, user):
        profile, created = UserProfile.objects.get_or_create(
            user=user,
            profile_type=self.profile_type,
            defaults={
                'language': self._get_language(),
                'source': self.booking_source,
            },
        )
        # created is True only if user exists already with different profile type
        return profile, created

    def _get_or_create_userprofile_old(self, user, picture_url):
        # pylint: disable=duplicate-code
        profile, created = UserProfile.objects.get_or_create(
            user=user,
            profile_type=self.profile_type,
            defaults={
                'language': self._get_language(),
                'source': self.booking_source,
            },
        )
        # pylint: enable=duplicate-code
        if created and (photo := download_picture(picture_url)):
            profile.photo = Photo.save_from_base64(photo)
            profile.save()
            profile.migrate_photo_to_s3()

        return profile

    def _user_logged_in(self, user_id: int):
        if self.profile_type == UserProfile.Type.BUSINESS:
            business_user_logged_in.send(self.__class__, user_id=user_id)

    def registration_completed_tasks(
        self,
        user: User,
        profile: UserProfile,
        invited_by_business_id: str = None,
    ):
        analytics_customer_registration_completed_task.delay(
            user_id=profile.user_id,
            invited_by_business_id=invited_by_business_id,
            context={
                'session_user_id': profile.user_id,
                'source_id': self.booking_source.id,
            },
        )
        versum_migration_handle_customer_registration_completed(user_id=user.id)

    @staticmethod
    def get_response(
        created, session, profile, access_rights, connected_with, user=None
    ):  # pylint: disable=too-many-positional-arguments, too-many-arguments
        profile_key = _get_profile_key(profile)
        formatted_account = user.format_account(profile) if user else profile.format_account()
        return Response(
            data={
                'access_token': session.session_key,
                profile_key: formatted_account,
                'access_rights': access_rights,
                'superuser': False,  # always will be false for this endpoint
                'password_change_required': bool(connected_with),
                'connected_with': connected_with,
                'is_gdpr_first_run': not created,
            },
            status=status.HTTP_201_CREATED if created else status.HTTP_200_OK,
        )

    def _verify_email(self, email: str):
        if email and email.lower() != self.user.email.lower():
            raise ServiceError(
                code=status.HTTP_400_BAD_REQUEST,
                errors=[
                    {
                        'code': 'cant_connect_facebook_to_profile',
                        'description': _('The email address does not match your account'),
                    }
                ],
            )


class FacebookSignInBaseView(
    FacebookSignInMixin,
    ExtendedLogsMixin,
    BaseBooksyNoSessionGenericAPIView,
):
    pass


class FacebookSignInUpCustomerView(FacebookSignInBaseView):
    booksy_teams = (BooksyTeams.CUSTOMER_ONBOARDING,)
    client_id = settings.FACEBOOK_CUSTOMER_CLIENT_ID
    serializer_class = FacebookSignInCustomerRequestSerializer
    user_serializer_class = CustomerFacebookSignInSerializerOld

    @property
    def allow_account_linking(self) -> bool:
        return True

    def post(self, request: Request, *args, **kwargs) -> Response:
        """
        Log in with Facebook JWT<br>
        <br>
        If you want to login send only _id_token_ and _nonce_.<br>
        If you want to register user, fields _sms_code_ and _cell_phone_ are required.<br>
        Automatic connecting facebook account to an existing user is currently not available.<br>
        """
        self.log_sign_in_attempt()
        if RefactorSaveRegisterAgreements():
            self.user_serializer_class = CustomerFacebookSignInSerializer

        data = self.get_validated_data(request.data)
        user, profile, created, connected_with = self.get_or_create_user(data)
        session = user.create_session(origin=AuthOriginEnum.FACEBOOK, fingerprint=self.fingerprint)
        self.log_sign_in_success(user.id, facebook_id=user.facebook_id)
        if created:
            start_scenario(AccountAddedScenario, user_profile=profile)
            self.registration_completed_tasks(user, profile, data['invited_by_business_id'])

        return self.get_response(
            created, session, profile, None, connected_with=connected_with, user=user
        )


class FacebookGraphSignInUpCustomerView(FacebookSignInBaseView):
    booksy_teams = (BooksyTeams.CUSTOMER_ONBOARDING,)
    client_id = settings.FACEBOOK_CUSTOMER_CLIENT_ID
    serializer_class = FacebookGraphSignInCustomerRequestSerializer
    user_serializer_class = CustomerFacebookSignInSerializer

    def _get_or_create_user(  # pylint: disable=arguments-renamed
        self,
        graph_data: FacebookGraphData,
        request_data: dict = None,
    ) -> (User, UserProfile, bool, str):
        facebook_id = graph_data.id
        user: User = User.objects.filter(facebook_id=facebook_id).first()
        connected_with = ''
        if AutomaticAccountLinkingFlag():
            qset = User.objects.filter(
                email=graph_data.email,
                cell_phone__isnull=False,
                profiles__profile_type=UserProfile.Type.CUSTOMER,
            )
            if FixAutomaticAccountLinkingCellPhoneFlag():
                qset = qset.exclude(cell_phone='')
            if not user and (user := qset.first()):
                logger.info(
                    '[FACEBOOK ACCOUNT CONNECT] account connected with '
                    'existing account (user id: %s)',
                    user.id,
                )
                user.facebook_id = facebook_id
                user.last_login = tznow()
                user.save()
                user.initiate_password_reset(
                    self.profile_type, self.booking_source, self._get_language()
                )
                user.delete_all_user_sessions()
                connected_with = 'facebook'
        else:
            if not user and User.objects.filter(email=graph_data.email).exists():
                raise ServiceError(
                    code=status.HTTP_400_BAD_REQUEST,
                    errors=[
                        {
                            'code': 'facebook_is_not_connected_with_profile',
                            'description': _(
                                'Facebook account is not connected with your profile.'
                            ),
                        }
                    ],
                )
        if created := user is None:
            user_data = {
                'email': graph_data.email,
                'first_name': graph_data.first_name,
                'last_name': graph_data.last_name,
                'facebook_id': facebook_id,
                'user_agreements': self.request.data.get('user_agreements'),
            } | request_data
            serializer = self.user_serializer_class(
                data=user_data,
                context={
                    'booking_source': self.booking_source,
                    'language': self._get_language(),
                },
            )
            try:
                validate_serializer(serializer)
            except ServiceError:
                logger.warning(
                    'Facebook invalid graph data',
                    extra={
                        'graph_data': graph_data,
                    },
                )
                raise

            user = serializer.save()

        profile, profile_created = self._get_or_create_userprofile(user)
        if created or profile_created:
            upload_facebook_photo_task.delay(profile.id, graph_data.picture['data']['url'])

        return user, profile, created, connected_with

    def get_or_create_user(
        self, data: FacebookGraphData, request_data: dict = None
    ) -> (User, UserProfile, bool, str):
        return self._get_or_create_user(graph_data=data, request_data=request_data)

    def post(self, request: Request, *args, **kwargs) -> Response:
        """
        Log in with Facebook JWT<br>
        <br>
        If you want to login send only _id_token_ and _nonce_.<br>
        If you want to register user, fields _sms_code_ and _cell_phone_ are required.<br>
        Automatic connecting facebook account to an existing user is currently not available.<br>
        """
        if not RefactorSaveRegisterAgreements():
            return Response(data=None, status=status.HTTP_423_LOCKED)

        self.log_sign_in_attempt()
        access_token = request.data['fb_access_token']
        graph_data: FacebookGraphData = get_facebook_graph_data(access_token)
        validated_data = self.get_validated_data(request.data | graph_data.as_dict())
        user, profile, created, connected_with = self.get_or_create_user(
            graph_data, request_data=validated_data
        )
        session = user.create_session(origin=AuthOriginEnum.FACEBOOK, fingerprint=self.fingerprint)
        self.log_sign_in_success(user.id, facebook_id=user.facebook_id)
        if created:
            start_scenario(AccountAddedScenario, user_profile=profile)
            self.registration_completed_tasks(
                user, profile, validated_data['invited_by_business_id']
            )

        return self.get_response(
            created, session, profile, None, connected_with=connected_with, user=user
        )


class FacebookSignInUpBusinessView(FacebookSignInBaseView):
    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING,)
    client_id = settings.FACEBOOK_BIZ_CLIENT_ID
    serializer_class = FacebookSignInBusinessRequestSerializer
    user_serializer_class = BusinessFacebookSignInSerializer

    def post(self, request: Request, *args, **kwargs) -> Response:
        """
        Facebook sign in V2 with JWT handling for limited login
        <br><br>
        Automatic connecting facebook account to an existing user is currently not available.<br>
        """
        data = self.get_validated_data(request.data)
        user, profile, created, connected_with = self.get_or_create_user(data)
        self.handle_whitelisting_ip_access_for_staffer(user=user)
        access_level = access.get_access_level(user=user, profile_type=self.profile_type)
        access_rights = access.get_access_rights(
            profile_type=self.profile_type, access_level=access_level
        )
        session = user.create_session(origin=AuthOriginEnum.FACEBOOK, fingerprint=self.fingerprint)
        self._user_logged_in(user_id=user.id)

        return self.get_response(
            created, session, profile, access_rights, connected_with=connected_with
        )


class BaseFacebookConnectView(FacebookSignInMixin, BaseBooksySessionGenericAPIView):
    permission_classes = (IsAuthenticated,)
    serializer_class = None
    client_id = None

    def post(self, request: Request, *args, **kwargs) -> Response:
        """
        Connect logged user with Facebook<br>
        """
        data = self.get_validated_data(request.data)
        token = self.verify_id_token(data['id_token'], data['nonce'])
        user_id = User.objects.filter(facebook_id=token.sub).values_list('id', flat=True).first()
        try:
            self._verify_email(token.email)
        except ServiceError:
            logger.warning(
                'Facebook invalid decoded token',
                extra={
                    'id_token': data['id_token'],
                },
            )
            raise
        if user_id:
            if user_id == self.user.id:
                message = _('Facebook account has already been connected with your account.')
            else:
                message = _('Facebook account has already been connected with another account.')
            raise ServiceError(
                code=status.HTTP_400_BAD_REQUEST,
                errors=[
                    {
                        'code': 'cant_connect_facebook_to_profile',
                        'description': message,
                    }
                ],
            )

        if token.email and CustomerQuickSignInUpFlag() and is_private_email(self.user.email):
            self.user.email = token.email.lower()
        self.user.facebook_id = token.sub
        self.user.save()

        profile = self.user.profile
        if not profile.photo_id:
            upload_facebook_photo_task.delay(profile.id, token.picture)

        return Response(
            status=status.HTTP_200_OK,
            data={
                _get_profile_key(profile): self.user.format_account(profile),
            },
        )

    def _verify_email(self, email: str):
        # pylint: disable=duplicate-code
        error_message = None
        email = email and email.lower()
        if CustomerQuickSignInUpFlag() and is_private_email(self.user.email):
            if User.objects.filter(email=email).exclude(id=self.user.id).exists():
                error_message = _(
                    'The email address from the social media account is assigned to '
                    'another Booksy account'
                )
        elif email != self.user.email.lower():
            error_message = _('The email address does not match your account')

        # pylint: enable=duplicate-code
        if error_message:
            raise ServiceError(
                code=status.HTTP_400_BAD_REQUEST,
                errors=[
                    {
                        'code': 'cant_connect_facebook_to_profile',
                        'description': error_message,
                    }
                ],
            )


class FacebookCustomerConnectView(BaseFacebookConnectView):
    booksy_teams = (BooksyTeams.CUSTOMER_ONBOARDING,)
    client_id = settings.FACEBOOK_CUSTOMER_CLIENT_ID
    serializer_class = FacebookConnectCustomerRequestSerializer


class FacebookBusinessConnectView(BaseFacebookConnectView):
    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING,)
    client_id = settings.FACEBOOK_BIZ_CLIENT_ID
    serializer_class = FacebookConnectBusinessRequestSerializer
