from urllib.parse import urljoin

from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from rest_framework.views import Response

from country_config import CountryConfig, LANGUAGE_CODES
from drf_api.base_views import BaseBooksyNoSessionApiView
from drf_api.service.other.routing.serializers import ConfigQuerySerializer
from drf_api.service.other.routing.utils import get_force_update_options
from lib.booksy_sms import parse_phone_number
from lib.cache import lru_booksy_cache
from lib.feature_flag.adapter import UserData
from lib.feature_flag.enums import SubjectType
from lib.feature_flag.feature import BooksyGiftcardsEnabledFlag
from lib.feature_flag.feature.integrations import RemoveQualarooBizAPIKeyFlag
from lib.feature_flag.feature.launch import AppLaunchTypeFlag
from lib.feature_flag.feature.security import (
    DisableCaptchaForAccountExistEndpointFlag,
    ReCaptchaCustomerFlag,
    HCaptchaAndroidFlag,
    HCaptchaBusinessAndroidFlag,
    HCaptchaBusinessFlag,
    HCaptchaFlag,
    HCaptchaBusinessiOSFlag,
    HCaptchaiOSFlag,
    HCaptchaBusinessFrontdeskFlag,
)
from lib.french_certification.utils import french_certification_enabled
from service.customer.hcaptcha import get_hcaptcha_protected_endpoints
from service.business.hcaptcha import get_hcaptcha_business_endpoints_registry
from webapps.business.utils import get_migration_date, get_migration_phase
from webapps.kill_switch.models import KillSwitch
from webapps.pos.tools import get_bank_account_choices
from webapps.purchase.braintree.utils import Braintree3DSVerificationManager
from webapps.voucher.utils import VoucherMigrationConfig


class ConfigMixin:
    @staticmethod
    @lru_booksy_cache(timeout=60 * 60, skip_in_pytest=True)
    def get_config(include_locale):
        # pylint: disable=line-too-long
        from webapps.structure.models import get_operating_country
        from webapps.structure.serializers import RegionSimpleSerializer

        country = get_operating_country()

        cs_phone = parse_phone_number(settings.COUNTRY_CONFIG.cs_phone)
        cs_working_hours = settings.COUNTRY_CONFIG.cs_working_hours

        # get sms settings
        marketing_night_adjust = settings.SMS_SETTINGS_PER_COUNTRY.get(
            settings.API_COUNTRY,
            settings.SMS_SETTINGS_DEFAULT,
        ).get(
            'marketing_night_adjust',
            settings.SMS_SETTINGS_DEFAULT['marketing_night_adjust'],
        )
        gdpr = settings.GDPR_COUNTRIES.get(
            settings.API_COUNTRY,
            settings.GDPR_COUNTRIES.get('default', False),
        )
        referral_c2b = settings.C2B_REFERRAL and KillSwitch.alive(KillSwitch.System.C2B_REFERRAL)

        ret = {
            'brand': 'booksy',  # deprecated
            'deployment_level': settings.DEPLOYMENT_LEVEL,
            # urls
            'marketplace_url': settings.MARKETPLACE_URL.rstrip('/') + '/',
            'marketplace_url_with_default_locale': (
                f'{urljoin(settings.MARKETPLACE_URL, settings.MARKETPLACE_LANG_COUNTRY)}/'
            ),
            'web-biz': settings.BIZ_WEB_2_APP_URL,  # deprecated
            'web-biz20': settings.BIZ_WEB_2_APP_URL,
            'web-user-reset': settings.WEB_USER_RESET_URL,
            'frontdesk_url': settings.FRONTDESK_APP_URL,
            'widget': settings.WIDGET_URL,
            'widget_2021': settings.WIDGET_2021_URL,
            'js-utils': '',  # backward compatibility with Marketplace
            'public_api': {
                'main_url': settings.PUBLIC_API_URL,
                'oauth2_authorize_url': settings.PUBLIC_API_OAUTH2_AUTHORIZE_URL,
                'oauth2_token_url': settings.PUBLIC_API_OAUTH2_TOKEN_URL,
            },
            'master': {
                'domain': f'.{settings.BOOKSY_DOMAIN}',
            },
            'elearning_web': settings.ELEARNING_WEB_URL,
            'elearning': settings.ELEARNING_MOBILE_URL,
            'gdpr': gdpr,
            'gdpr_annex': settings.GDPR_ANNEX,
            'stripe_order_url': settings.STRIPE_ORDER_URL,
            'stripe_public_key': settings.STRIPE_PUBLIC_API_KEY,
            # features
            'business_demo': False,
            'businesses_multi_categories': settings.BUSINESSES_MULTI_CATEGORIES,
            'family_and_friends_enabled': settings.FAMILY_AND_FRIENDS_ENABLED,
            'feedback': True,
            'iap_android': settings.IAP_ANDROID,
            'iap_braintree': settings.IAP_BRAINTREE,
            'iap_ios': settings.IAP_IOS,
            'marketing_night_adjust': marketing_night_adjust,
            'multibooking': True,
            'migration_phase': get_migration_phase(),
            'migration_phase_last_date': get_migration_date(),
            'pos': settings.POS,
            'pos__pay_by_app': settings.POS__PAY_BY_APP,
            'pos__stripe_terminal': settings.POS__STRIPE_TERMINAL,
            'pos__tap_to_pay': settings.POS__TAP_TO_PAY,
            'pos__tap_to_pay_min_ios_version': settings.POS__TAP_TO_PAY_MIN_IOS_VERSION,
            'pos__keyed_in_payment': settings.POS__KEYED_IN_PAYMENT,
            'pos__fast_payouts': settings.POS__FAST_PAYOUTS,
            'pos__prepayments': settings.POS__PREPAYMENTS,
            'pos__refunds': settings.POS__REFUNDS,
            'pos__registers': True,
            'pos__registers_reopen': True,
            'pos__registers_shared': True,
            'pos__show_add_card_popup': False,
            'pos__show_splash': False,
            'pos__bsx': settings.POS__BSX,
            'pos__booksy_pay': settings.POS__BOOKSY_PAY,
            'pos__blik': settings.POS__BLIK,
            'unverified_push_payments': settings.UNVERIFIED_PUSH_PAYMENTS,
            # Push 2 pay has been removed
            'push_2_pay': False,
            'repeating_booking': True,
            'repeating_booking_two_weeks': True,
            'review_replies': True,
            'show_trial_info': settings.SHOW_TRIAL_INFO,
            'sms_management': True,
            'sms_limits_in_subscription': settings.SMS_LIMITS_IN_SUBSCRIPTION,
            'sms_registration_code_required': settings.SMS_REGISTRATION_CODE_REQUIRED,
            'market_pay': settings.MARKET_PAY_ENABLED,
            'market_pay_b2b_referral': settings.MARKET_PAY_B2B_REFERRAL_ENABLED,
            'invoices_enabled': settings.INVOICES_ENABLED,
            'vouchers_enabled': settings.VOUCHERS_ENABLED,
            'donations_enabled': False,  # deprecated
            'donations_modal_enabled': False,  # deprecated
            'donations_promo': False,  # deprecated
            'b2b_referral': settings.B2B_REFERRAL_ENABLED,
            'registration_closed': settings.CUSTOMER_REGISTRATION_CLOSED,
            'business_registration_closed': settings.BUSINESS_REGISTRATION_CLOSED,
            'customer_registration_closed': settings.CUSTOMER_REGISTRATION_CLOSED,
            'whitelist_sms': list(settings.WHITELIST_SMS),
            'whitelist_sms_for_registration': list(settings.WHITELIST_SMS_FOR_REGISTRATION_CODES),
            'prefix_code_to_country_code': settings.PREFIX_CODE_TO_COUNTRY_CODE,
            'auto_notify_about_reschedule': KillSwitch.alive(
                KillSwitch.System.AUTO_NOTIFY_ABOUT_RESCHEDULE,
            ),
            # DEPRECATED self-service - in use till 3.0 went live
            'self_service_enabled': settings.BOOST.ENABLED,
            'self_service_cancelable': settings.BOOST.CANCELABLE,
            'self_service_claim_age': settings.BOOST.CLAIM_OVERDUE_AGE,
            'self_service_claim_deadline': settings.BOOST.CLAIM_DEADLINE,
            # boost
            'boost_claim_age': settings.BOOST.CLAIM_OVERDUE_AGE,
            'boost_claim_deadline': settings.BOOST.CLAIM_DEADLINE,
            'boost_tax': settings.BOOST.TAX,
            # external apps
            # intercom is deprecated; left empty values for iOS compat
            'intercomio_app': {'id': '', 'web_key': '', 'ios_key': '', 'android_key': ''},
            # always use prod keys in branch_io
            'branchio_app': {
                'cust_key': settings.BRANCH_IO_KEY_CUS_PRD,
                'biz_key': settings.BRANCH_IO_KEY_BIZ_PRD,
            },
            'adyen_app': {
                'account_code': settings.ADYEN_ACCOUNT,
                'merchant_account': settings.ADYEN_MERCHANT_ACCOUNT,
                'market_pay_merchant_account': settings.ADYEN_MARKET_PAY_MERCHANT_ACCOUNT,
                'market_pay_account_code': settings.MARKET_PAY_MERCHANT_ACCOUNT_CODE,
                'form_key': settings.ADYEN_FORM_KEY,
                'form_lib_id': settings.ADYEN_FORM_LIB_ID,
                'form_lib_path': settings.ADYEN_FORM_LIB_PATH,
                '3dsecure': settings.ADYEN_3DS_ENABLED,
            },
            'avs': {  # settings related to Address Verification System
                'avs_enabled': settings.MARKET_PAY_AVS_ENABLED,
                'avs_zipcode_regexp': settings.AVS_ZIPCODE_REGEXP,
            },
            'google_app': {
                'api_key': settings.GOOGLE_API_KEY,
            },
            'qualaroo': {
                'cus_api_key': settings.QUALAROO_CUS_API_KEY,
                'biz_api_key': settings.QUALAROO_BIZ_API_KEY,
            },
            'ld_env': settings.LAUNCH_DARKLY_ENVIRONMENT,
            'eppo_env': settings.EPPO_ENVIRONMENT,
            # general
            'thumbnails': [f'{size}x{size}' for size in settings.PHOTO_THUMBNAIL_SIZES],
            'service_colors': settings.SERVICE_COLORS,
            'service_color_palettes': settings.SERVICE_COLOR_PALETTES,
            # regions
            'distance_unit': settings.COUNTRY_DISTANCE_UNIT,
            'supported_regions': [RegionSimpleSerializer(country).data],
            'main_regions': RegionSimpleSerializer(country.get_main_regions(), many=True).data,
            'country': {
                'code': settings.COUNTRY_CONFIG.country_code,
                'name': settings.COUNTRY_CONFIG.name,
                'latitude': settings.COUNTRY_CONFIG.latitude,
                'longitude': settings.COUNTRY_CONFIG.longitude,
                'zoom': settings.COUNTRY_CONFIG.zoom,
                'address_format': settings.COUNTRY_CONFIG.address_format,
                'no_zipcodes': settings.COUNTRY_CONFIG.no_zipcodes,
                'zipcode_regexp': settings.COUNTRY_CONFIG.zipcode_regexp,
            },
            'big_country': settings.BIG_COUNTRY,
            'default_locale': settings.CURRENCY_LOCALE.split('.')[0],
            # customer support
            'cs_phone': {
                'call': cs_phone.global_short,
                'display': cs_phone.global_nice,
            },
            'cs_email': settings.COUNTRY_CONFIG.cs_email,
            'cs_email_biz': settings.COUNTRY_CONFIG.cs_email_biz,
            'cs_working_hours': {
                'from': cs_working_hours[0],
                'till': cs_working_hours[1],
            },
            'complaint_email': settings.BOOST.COMPLAINT_EMAIL,
            # pos
            'pos_default_payment_provider': settings.POS__DEFAULT_PAYMENT_PROVIDER,
            'pos_default_payment_provider_v2': settings.POS__DEFAULT_PAYMENT_PROVIDER_V2,
            'bank_account_types': get_bank_account_choices(),
            'parallel_clients': settings.PARALLEL_CLIENTS,
            'first_day_of_week': settings.COUNTRY_CONFIG.first_day_of_week,
            'max_message_blast_parts': settings.MAX_MESSAGE_BLAST_PARTS,
            # autoresponder
            'sms_autoresponder_interval': 14,  # deprecated
            'autoresponder': False,  # deprecated
            'call_autoresponder_enabled': False,  # deprecated
            'sms_autoresponder_enabled': False,  # deprecated
            # referral_c2b
            'referral_c2b': referral_c2b,
            'referral_c2b_reward_short': (
                settings.C2B_REWARD_TEXT.get('short', '') if referral_c2b else None
            ),
            'referral_c2b_reward_long': (
                settings.C2B_REWARD_TEXT.get('long', '') if referral_c2b else None
            ),
            'referral_c2b_min_subscriptions_paid': settings.C2B_REQUIRED_PAYING_MONTHS,
            'helpshift_app': settings.HELPSHIFT_APP_SETTINGS,
            # payments
            '3d_secure_enabled': Braintree3DSVerificationManager.is_3d_secure_enabled(),
            'expose_gross_price': settings.EXPOSE_GROSS_PRICE,
            # booksy billing 3.0
            'billing_suspend_enabled': KillSwitch.alive(KillSwitch.System.BILLING_SUSPEND_ENABLED),
            'switch_to_stripe': KillSwitch.alive(KillSwitch.System.BILLING_FORCE_SWITCH_TO_STRIPE),
            # onboarding
            'onboarding_enabled': settings.ONBOARDING_ENABLED,
            'countries_with_open_registration': settings.COUNTRIES_WITH_OPEN_REGISTRATION,
            'can_create_umbrella': settings.CAN_CREATE_UMBRELLA,
            'soft_limit_in_km_of_business_geolocation_change': settings.SOFT_LIMIT_IN_KM_OF_BUSINESS_GEOLOCATION_CHANGE,
            'daytime_thresholds': settings.DAYTIME_THRESHOLDS,
            'web_tools_url': settings.WEB_TOOLS_URL,
            'iterable_api_key': settings.ITERABLE_JWT_API_KEY,
            'embedded_messages_placement_ids': settings.EMBEDDED_MESSAGES_PLACEMENT_IDS,
            'trial_duration': settings.STATUS_FLOW__TRIAL_DURATION,
        }
        ret.update(ConfigView.currency_locale_config())
        ret.update(ConfigView.mass_locale_config())
        if include_locale:
            ret.update(ConfigView.locale_config())
        if settings.GDPR_ANNEX:
            ret['gdpr_annex_email'] = settings.GDPR_ANNEX_EMAIL

        ret['dsa_compliant'] = settings.DSA_COMPLIANT.get(
            settings.API_COUNTRY, settings.DSA_NON_COMPLAINT_VALUE
        )

        if RemoveQualarooBizAPIKeyFlag():
            ret['qualaroo'].pop('biz_api_key')

        return ret

    @staticmethod
    def get_locale_numeric_config():
        return settings.COUNTRY_CONFIG.currency_data

    @staticmethod
    def currency_locale_config():
        local_currency = ConfigView.get_locale_numeric_config()
        locale = {
            'symbol': local_currency['currency_symbol'],
            'decimal_length': local_currency['frac_digits'],
            'code': local_currency['int_curr_symbol'].strip(),
            'decimal_separator': local_currency['mon_decimal_point'],
            'group_separator': local_currency['mon_thousands_sep'],
            'precedes': local_currency['p_cs_precedes'] == 1,
            'space': local_currency['p_sep_by_space'] == 1,
            'negative_sign': local_currency['negative_sign'],
        }
        return {
            'default_currency': locale['code'],
            'currency': {locale['code']: locale},
        }

    @staticmethod
    def mass_locale_config():
        local_separator_style = ConfigView.get_locale_numeric_config()
        locale = {
            'symbol': settings.COUNTRY_MASS_UNIT,
            'decimal_length': settings.MASS_VALUE__DECIMAL_LENGTH,
            'decimal_separator': local_separator_style['decimal_point'],
            'group_separator': local_separator_style['thousands_sep'],
            'precedes': False,
            'space': True,
        }
        return {
            'default_mass_unit': locale['symbol'],
            'mass_unit': {locale['symbol']: locale},
        }

    @staticmethod
    def locale_config():
        """datetime locales"""
        loc = {}
        for loc_country in settings.LANG_TO_LOCALE_PER_COUNTRY.values():
            key = loc_country.get(settings.API_COUNTRY, loc_country['default'])
            country = key.split('_')[1].lower()
            if country not in LANGUAGE_CODES:
                continue
            loc[key] = {'id': key}
            loc[key].update(settings.DATETIME_LOCALE['default'])
            loc[key].update(settings.DATETIME_LOCALE[key])
            loc[key].update({'distance_unit': 'm'})

            cconf = CountryConfig(country)
            loc[key]['first_day_of_the_week'] = cconf.first_day_of_week
            # always return current currency - it's fixed by API_COUNTRY
            loc[key]['default_currency'] = settings.CURRENCY_CODE
            loc[key]['default_mass_unit'] = settings.COUNTRY_MASS_UNIT
            loc[key]['zipcode_regexp'] = settings.COUNTRY_CONFIG.zipcode_regexp

        return {
            'locale': loc,
        }

    @staticmethod
    def get_real_time_config(fingerprint):
        recaptcha_customer_flag = ReCaptchaCustomerFlag()
        cx_disable_captcha_for_account_exist = DisableCaptchaForAccountExistEndpointFlag()
        app_launch_type = AppLaunchTypeFlag(
            user=(
                UserData(
                    subject_key=fingerprint,
                    subject_type=SubjectType.FINGERPRINT.value,
                )
                if fingerprint
                else None
            )
        )

        hcaptcha_endpoints = []
        if HCaptchaFlag():
            hcaptcha_endpoints += get_hcaptcha_protected_endpoints()
        if HCaptchaBusinessFlag():
            hcaptcha_endpoints += get_hcaptcha_business_endpoints_registry()

        return {
            # Force update options
            **get_force_update_options(),
            # French certification
            'french_certification_enabled': french_certification_enabled(),
            # recaptcha
            'recaptcha_site_keys': {
                'android': settings.RECAPTCHA_SITE_KEY_ANDROID,
                'ios': settings.RECAPTCHA_SITE_KEY_IOS,
                'web': settings.RECAPTCHA_SITE_KEY_WEB,
            },
            'recaptcha_enabled': recaptcha_customer_flag,
            'recaptcha': {
                'sms_registration': recaptcha_customer_flag,
                'login': recaptcha_customer_flag,
                'password_reset': recaptcha_customer_flag,
                'email_change': recaptcha_customer_flag,
                'account_exists': (
                    False if cx_disable_captcha_for_account_exist else recaptcha_customer_flag
                ),
                'jwt_password_reset': recaptcha_customer_flag,
                'family_member_invitation': recaptcha_customer_flag,
            },
            # hcaptcha
            'hcaptcha_site_keys': {
                'android': settings.HCAPTCHA_SITE_KEY_ANDROID,
                'ios': settings.HCAPTCHA_SITE_KEY_IOS,
                'web': settings.HCAPTCHA_SITE_KEY_WEB,
                'android_biz': settings.HCAPTCHA_SITE_KEY_ANDROID_BUSINESS,
                'ios_biz': settings.HCAPTCHA_SITE_KEY_IOS_BUSINESS,
                'web_biz': settings.HCAPTCHA_SITE_KEY_WEB_BUSINESS,
            },
            'hcaptcha': hcaptcha_endpoints,
            'hcaptcha_customer_ios_enabled': HCaptchaiOSFlag(),
            'hcaptcha_customer_android_enabled': HCaptchaAndroidFlag(),
            'hcaptcha_business_android_enabled': HCaptchaBusinessAndroidFlag(),
            'hcaptcha_business_ios_enabled': HCaptchaBusinessiOSFlag(),
            'hcaptcha_business_frontdesk_enabled': HCaptchaBusinessFrontdeskFlag(),
            'vouchers_online_sale': VoucherMigrationConfig.is_voucher_online_sale_enabled(),
            'voucher_migration_mode': VoucherMigrationConfig.get_flag_value(),
            'booksy_gift_cards_enabled': BooksyGiftcardsEnabledFlag(),
            'app_launch_type': app_launch_type,
        }


class ConfigView(ConfigMixin, BaseBooksyNoSessionApiView):
    API_KEY_REQUIRED = False
    query_serializer_class = ConfigQuerySerializer
    booksy_teams = (BooksyTeams.DEFAULT_MAINTENANCE,)

    def get(self, request):
        """
        It's info about server setup and some server dependent data.
        <pre>Urls for: widget, web-biz.

        Locale declarations and default one:
           first_day_of_the_week - 0 for Sunday and 1 for Monday
           date_* (ymwd - year month weekday day)
           time_* (hms - hour minute second)
           distance_unit - 'mi' or 'km'
           default_currency - one of declared for example 'USD'

        Currency definitions:
           code - 3 chars currency code
           decimal_separator - for example ',' 3,00
           group_separator - for example '.' 3.000,00
           symbol - for example 'KSH', '$', 'zl'
           decimal_length - for example 3  10,000
           precedes - flag 0 is 10$ and 1 is $10
           negative_sign - '-' in some currencies it's '~' so ~10,00
        </pre>
        """
        serializer = self.query_serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        config = self.get_config(**serializer.validated_data)
        config.update(self.get_real_time_config(fingerprint=self.fingerprint))
        return Response(config)
