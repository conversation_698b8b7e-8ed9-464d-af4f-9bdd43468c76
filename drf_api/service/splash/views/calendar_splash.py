import datetime
from logging import getLogger

from django.conf import settings

from bo_obs.datadog.enums import BooksyTeams

from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.serializers import ValidationError

from drf_api.base_views import BaseBooksySessionGenericAPIView
from drf_api.service.business.validators.access import (
    get_business_staffer_validator,
)
from drf_api.service.splash.serializers import (
    ChangePrepaymentSerializer,
)
from drf_api.service.user.serializer import EmptySerializer
from lib.db import READ_ONLY_DB, using_db_for_reads
from lib.feature_flag.adapter import UserData
from lib.feature_flag.enums import AppDomains, SubjectType
from lib.feature_flag.feature.booksy_pay import BusinessBooksyPayOnboardingFlag
from lib.feature_flag.feature.boost import BoostPreSuspensionWarningBackendFlag
from lib.feature_flag.feature.monetisation import EnablePremiumHoursSplashFlag
from lib.feature_flag.feature.payment import (
    NoShowProtectionSplashFlag,
    NoShowProtectionThresholdsSplashFlag,
    HigherPrepaymentSplashFlag,
    KeepPrepaymentSplashFlag,
)
from lib.tools import sget_v2, tznow
from lib.x_version_compatibility.compatibilities.booksy_pay import (
    BusinessBooksyPayOnboardingCompatibility,
)
from webapps.boost.models import BoostBan, BoostFraudSuspicion
from webapps.business.enums import NoShowProtectionType, RateType
from webapps.business.models import Business, Resource, ServiceVariantPayment
from webapps.consts import MOBILE_SOURCES
from webapps.premium_services.public import PeakHourServiceFactory
from webapps.pos.enums.splash import SplashType
from webapps.pos.models import (
    Splash,
    NoShowSplash,
    HigherPrepaymentSplash,
    HigherPrepaymentSplashDecision,
    KeepPrepaymentSplash,
    ServiceVariant,
    TapToPayAppetiteSplash,
    TapToPayAppetiteExperiment,
)
from webapps.pos.no_show.calculate_lost_money import (
    calculate_lost_money,
    AVERAGE_MONTHLY_LOST_MONEY,
)
from webapps.pos.serializers import SplashSerializer, NoShowFeeSerializer, UpdateNoShowFeeSerializer


log = getLogger('booksy.calendar_splash')


class CalendarSplash:
    def __init__(self, request, user, business, source_name):
        self.request = request
        self.user = user
        self.business = business
        self.source_name = source_name

    def show(self) -> Response | None:
        raise NotImplementedError

    @staticmethod
    def _create_splash(request: Request, business: Business, splash_type: SplashType) -> bool:
        _, created = Splash.objects.get_or_create(
            business=business,
            operator=request.user,
            defaults={'seen_at': tznow()},
            type=splash_type,
        )
        return created

    @property
    def is_mobile(self):
        return self.source_name in MOBILE_SOURCES


class HigherPrepaymentCalendarSplash(CalendarSplash):
    def _get_current_prepayment_level(self) -> set:
        qs = (
            ServiceVariantPayment.objects.filter_only_active()
            .filter(
                service_variant__service__business=self.business,
                payment_type=NoShowProtectionType.PREPAYMENT,
                saving_type=RateType.PERCENTAGE,
            )
            .select_related('service_variant')
        )
        prepayment_rates = {round(prepayment.rate) for prepayment in qs}
        return prepayment_rates

    def show(self) -> Response | None:
        splash_config = HigherPrepaymentSplashFlag()
        if (
            not splash_config
            or HigherPrepaymentSplashDecision.objects.filter(pos=self.business.pos).exists()
        ):
            return

        if (
            not HigherPrepaymentSplash.objects.filter(
                seen_at__gte=tznow() - datetime.timedelta(days=1),
                business=self.business,
                operator_id=self.user,
            ).exists()
            and HigherPrepaymentSplash.objects.filter(
                business=self.business,
                operator_id=self.user,
            ).count()
            < 3
        ):
            rates = self._get_current_prepayment_level()
            if not rates or len(rates) > 1:
                return
            rate = rates.pop()
            if rate > 50:
                return

            data = SplashSerializer(
                instance={
                    'splash': SplashType.SPLASH_HIGHER_PREPAYMENT,
                    'additional_data': {
                        'current_prepayment_level': rate,
                        'suggested_prepayment_level': 50 if rate < 50 else 75,
                        'due_date': splash_config.get('due_date'),
                    },
                }
            ).data
            HigherPrepaymentSplash.objects.create(
                business=self.business,
                operator=self.user,
                seen_at=tznow(),
            )
            return Response(data=data)


class KeepPrepaymentCalendarSplash(CalendarSplash):
    def _get_current_prepayment_level(self) -> set:
        qs = (
            ServiceVariantPayment.objects.filter_only_active()
            .filter(
                service_variant__service__business=self.business,
                payment_type=NoShowProtectionType.PREPAYMENT,
                saving_type=RateType.PERCENTAGE,
            )
            .select_related('service_variant')
        )
        prepayment_rates = {round(prepayment.rate) for prepayment in qs}
        return prepayment_rates

    def show(self) -> Response | None:
        if (
            not KeepPrepaymentSplashFlag()
            or KeepPrepaymentSplash.objects.filter(business=self.business).exists()
        ):
            return

        if HigherPrepaymentSplashDecision.objects.filter(
            pos=self.business.pos, decision=HigherPrepaymentSplashDecision.Decision.DATE
        ).exists():
            rates = self._get_current_prepayment_level()
            if not rates or len(rates) > 1:
                return
            rate = rates.pop()
            if rate < 50:
                suggested_rate = 50
            elif rate < 75:
                suggested_rate = 75
            else:
                suggested_rate = 100

            data = SplashSerializer(
                instance={
                    'splash': SplashType.SPLASH_KEEP_PREPAYMENT,
                    'additional_data': {
                        'current_prepayment_level': rate,
                        'suggested_prepayment_level': suggested_rate,
                    },
                }
            ).data
            KeepPrepaymentSplash.objects.create(
                business=self.business,
                operator=self.user,
                seen_at=tznow(),
            )
            return Response(data=data)


class NoShowProtectionSplash(CalendarSplash):
    def show(self) -> Response | None:
        if self.user != self.business.owner:
            return None

        if (
            NoShowProtectionSplashFlag()
            and not NoShowSplash.objects.filter(
                seen_at__gte=tznow() - datetime.timedelta(days=1),
                business=self.business,
                operator_id=self.user,
                type=SplashType.SPLASH_NO_SHOWS,
            ).exists()
            and NoShowSplash.objects.filter(
                business=self.business,
                operator_id=self.user,
                type=SplashType.SPLASH_NO_SHOWS,
            ).count()
            <= 3
        ):
            no_show_lost_money_data = self._get_splash_data_for_no_show_protection(self.business.id)
            if no_show_lost_money_data:
                no_show_lost_money_splash = SplashSerializer(
                    instance={
                        'splash': SplashType.SPLASH_NO_SHOWS,
                        'additional_data': no_show_lost_money_data,
                    }
                ).data
                NoShowSplash.objects.create(
                    business=self.business,
                    operator=self.user,
                    seen_at=tznow(),
                    type=SplashType.SPLASH_NO_SHOWS,
                )
                return Response(data=no_show_lost_money_splash)

    def _get_splash_data_for_no_show_protection(self, business_pk):
        no_show_lost_money_thresholds = NoShowProtectionThresholdsSplashFlag()
        if (  # pylint: disable=unsupported-membership-test
            settings.API_COUNTRY not in no_show_lost_money_thresholds
        ):
            return

        try:
            no_show_lost_money_data = calculate_lost_money(business_pk)
        except Exception as e:  # pylint: disable=broad-exception-caught
            log.warning("Can't calculate no show lost money: %s", e)
            return

        if (  # pylint: disable = unsubscriptable-object
            no_show_lost_money_data.get(AVERAGE_MONTHLY_LOST_MONEY, 0)
            >= no_show_lost_money_thresholds[settings.API_COUNTRY]
        ):
            return no_show_lost_money_data


class TapToPayAppetiteExperimentSplash(CalendarSplash):
    def show(self) -> Response | None:
        if self.user != self.business.owner:
            return None

        if not self.is_mobile:
            return None

        if (
            TapToPayAppetiteExperiment.objects.filter(business=self.business, opt_in=False).exists()
            and not TapToPayAppetiteSplash.objects.filter(
                seen_at__gte=tznow() - datetime.timedelta(days=1),
                business=self.business,
                operator_id=self.user,
            ).exists()
            and TapToPayAppetiteSplash.objects.filter(
                business=self.business,
                operator_id=self.user,
            ).count()
            < 2
        ):
            TapToPayAppetiteSplash.objects.create(
                business=self.business,
                operator=self.user,
                seen_at=tznow(),
            )
            return Response(
                data=SplashSerializer(
                    instance={'splash': SplashType.SPLASH_TAP_TO_PAY_APPETITE}
                ).data
            )


class BoostPreSuspensionWarningSplash(CalendarSplash):
    def _should_show(self):
        return bool(
            settings.BOOST.BANS_ENABLED
            and self.business.boost_status in Business.BoostStatus.active_statuses()
            and BoostPreSuspensionWarningBackendFlag(
                UserData(
                    subject_key=self.business.id,
                    subject_type=SubjectType.BUSINESS_ID.value,
                )
            )
            and self.user.get_staffer_access_level(self.business)
            in {
                Resource.STAFF_ACCESS_LEVEL_OWNER,
                Resource.STAFF_ACCESS_LEVEL_MANAGER,
            }
            and BoostFraudSuspicion.objects.filter(
                business_id=self.business.id,
                warning_visible=True,
            ).exists()
            and BoostBan.get_current_ban(self.business.id) is None
        )

    def show(self) -> Response | None:
        if not self._should_show():
            return None

        splash_created = self._create_splash(
            self.request,
            self.business,
            SplashType.SPLASH_BOOST_PRE_SUSPENSION_WARNING,
        )
        if splash_created:
            response_serializer = SplashSerializer(
                instance={
                    'splash': SplashType.SPLASH_BOOST_PRE_SUSPENSION_WARNING,
                    'additional_data': {
                        'boost_terms': (
                            f'{settings.FRONTDESK_APP_URL}boost-terms/{settings.API_COUNTRY}.html'
                        ),
                    },
                }
            )
            return Response(data=response_serializer.data)

        return None


class BusinessBooksyPayOnboardingCalendarSplash(CalendarSplash):
    splash_type = SplashType.SPLASH_PX_BOOKSY_PAY_ONBOARDING

    def show(self) -> Response | None:  # pylint: disable=R0911
        if BusinessBooksyPayOnboardingCompatibility(self.request) is False:
            return None

        if not BusinessBooksyPayOnboardingFlag(
            (
                UserData(
                    subject_key=self.business.id,
                    subject_type=SubjectType.BUSINESS_ID.value,
                    app_domain=AppDomains.PROVIDER.value,
                )
            )
        ):
            return None

        with using_db_for_reads(READ_ONLY_DB):
            if self.user != self.business.owner:
                return None

            if not self.business.booksy_pay_available:
                return None

        if self._create_splash(self.request, self.business, self.splash_type):
            return Response(data=SplashSerializer(instance={'splash': self.splash_type}).data)


class PremiumHoursSplash(CalendarSplash):
    def _should_show(self) -> bool:
        with using_db_for_reads(READ_ONLY_DB):
            if self.user.get_staffer_access_level(self.business) not in {
                Resource.STAFF_ACCESS_LEVEL_OWNER,
                Resource.STAFF_ACCESS_LEVEL_MANAGER,
            }:
                return False

            user_data = UserData(
                subject_key=self.business.id,
                subject_type=SubjectType.BUSINESS_ID.value,
                app_domain=AppDomains.PROVIDER.value,
            )
            if not EnablePremiumHoursSplashFlag(user_data):
                return False

            peak_hour_service = PeakHourServiceFactory.get_service()
            return not bool(peak_hour_service.get_active_services(self.business.id))

    def show(self) -> Response | None:
        if not self._should_show():
            return None

        splash_created = self._create_splash(
            self.request,
            self.business,
            SplashType.SPLASH_PREMIUM_HOURS,
        )
        if splash_created:
            response_serializer = SplashSerializer(
                instance={'splash': SplashType.SPLASH_PREMIUM_HOURS}
            )
            return Response(data=response_serializer.data)


class CalendarSplashView(BaseBooksySessionGenericAPIView):
    permission_classes = (IsAuthenticated,)
    booksy_teams = (
        BooksyTeams.PROVIDER_ENGAGEMENT,
        BooksyTeams.PROVIDER_ONBOARDING,
        BooksyTeams.PAYMENT_PROCESSING,
        BooksyTeams.PAYMENT_NEXUS,
    )
    serializer_class = SplashSerializer
    splash_priority: list[type[CalendarSplash]] = [
        BoostPreSuspensionWarningSplash,
        KeepPrepaymentCalendarSplash,
        TapToPayAppetiteExperimentSplash,
        HigherPrepaymentCalendarSplash,
        NoShowProtectionSplash,
        BusinessBooksyPayOnboardingCalendarSplash,
        PremiumHoursSplash,
    ]

    def get(self, request, business_pk):  # pylint: disable=too-many-return-statements
        no_splash = SplashSerializer(instance={'splash': None}).data

        validator = get_business_staffer_validator(
            business=business_pk,
            request=request,
            user=request.user,
        )
        validator.validate()
        business = validator.fetcher.business

        if business.not_published:
            return Response(data=no_splash)

        for splash in self.splash_priority:
            splash_obj = splash(
                request=request,
                user=request.user,
                business=business,
                source_name=self.booking_source.name,
            )
            if response := splash_obj.show():
                return response
        return Response(data=no_splash)


class ChangePrepaymentView(BaseBooksySessionGenericAPIView):
    """
    Changes level (%) of prepayment on all services with prepayment
    """

    permission_classes = (IsAuthenticated,)
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)
    serializer_class = ChangePrepaymentSerializer

    def _get_service_variants_prices(self, business) -> dict:
        # return a dict with all active service_variants with related services and prepayments
        services_ids = (
            business.services.filter(
                deleted__isnull=True,
                active=True,
            )
            .values_list('id', flat=True)
            .distinct()
        )

        service_variant_prices = ServiceVariant.objects.filter(
            active=True,
            deleted__isnull=True,
            service_id__in=services_ids,
            payment__payment_type=NoShowProtectionType.PREPAYMENT,
        ).values_list('id', 'price', 'service__name')

        return {sv_id: (price, name) for sv_id, price, name in service_variant_prices}

    def prepare_services(self, data, payment_percentage):
        # set new prepayment rate and filter out services with CF
        updated_services = []
        for category in data['service_categories']:
            all_services = category.pop('services', [])
            for service_data in all_services:
                if (
                    sget_v2(service_data, ['no_show_protection', 'type'])
                    == NoShowProtectionType.PREPAYMENT
                ):
                    service_data['no_show_protection'] = {
                        'type': NoShowProtectionType.PREPAYMENT,
                        'percentage': payment_percentage,
                    }
                    updated_services.append(service_data)

        return updated_services

    def _change_prepayment_level(self, business, new_rate: int):
        """
        Takes all services with prepayments and applies new prepayment rate to them.
        This is similar to what
        NoShowProtectionFeeServiceHandler and NoShowProtectionFeeAllServicesHandler do
        """

        pos = business.pos
        service_variant_prices = self._get_service_variants_prices(business)
        data = NoShowFeeSerializer(
            instance=pos,
            context={
                'business': business,
            },
        ).data

        # we need to filter out CF from data - we want to apply the change only for prepayments
        updated_services = self.prepare_services(data, new_rate)

        serializer = UpdateNoShowFeeSerializer(
            data={
                'services': updated_services,
            },
            instance=pos,
            context={
                'business': business,
                'service_prices': service_variant_prices,
            },
        )

        serializer.is_valid(raise_exception=True)
        serializer.save(operator_id=self.user.id)

    def post(self, request, business_pk):
        validator = get_business_staffer_validator(
            business=business_pk,
            request=request,
            user=request.user,
        )
        validator.validate()
        business = validator.fetcher.business

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        try:
            self._change_prepayment_level(business, serializer.validated_data['rate'])
        except ValidationError as e:
            return Response(
                data={
                    "errors": [
                        {
                            "code": "invalid",
                            "description": e.detail[0] if isinstance(e.detail, list) else e.detail,
                        }
                    ]
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        return Response(status=status.HTTP_200_OK, data={})


class TapToPayAppetiteAcceptView(BaseBooksySessionGenericAPIView):
    """
    Saves in TapToPayAppetiteExperiment object if business clicked opt in
    for Tap To Pay appetite splash
    """

    permission_classes = (IsAuthenticated,)
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)
    serializer_class = EmptySerializer

    def post(self, request, business_pk):
        validator = get_business_staffer_validator(
            business=business_pk,
            request=request,
            user=request.user,
        )
        validator.validate()
        business = validator.fetcher.business

        tap_to_pay_appetite = TapToPayAppetiteExperiment.objects.filter(business=business).first()

        if tap_to_pay_appetite:
            tap_to_pay_appetite.opt_in = True
            tap_to_pay_appetite.save()
            return Response(status=status.HTTP_200_OK, data={})
        return Response(status=status.HTTP_404_NOT_FOUND)
