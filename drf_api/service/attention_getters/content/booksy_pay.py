# pylint: disable=line-too-long
# pylint: disable=consider-using-f-string
from dataclasses import dataclass

from django.utils.translation import gettext_lazy as _

from drf_api.service.attention_getters.consts.booksy_pay import ANY
from drf_api.service.attention_getters.content.base import (
    AttentionGetter,
    AttentionGetterButton,
    AttentionGetterButtons,
    AttentionGetterContent,
    AttentionGetterContentFactory,
    AttentionGetterImage,
    AttentionGettersContent,
)
from drf_api.service.attention_getters.enums.base import (
    AttentionGetterName,
    AttentionGetterStep,
    AttentionGetterType,
)
from drf_api.service.attention_getters.enums.booksy_pay import (
    AnalyticsModalType,
    AnalyticsModalTypeVersion,
    BooksyPayAttentionGetterImageType,
    BooksyPayAttentionGetterName,
)
from webapps.booksy_pay.cashback import CASHBACK_VARIANTS
from webapps.booksy_pay.consts import BP_AVAILABLE__DAYS_BEFORE_APPT
from webapps.booksy_pay.enums import BooksyPayCashbackType


class BooksyPayAttentionGetterContentFactory(AttentionGetterContentFactory):
    _registry: dict[
        tuple[AttentionGetterType, AttentionGetterStep | None, AttentionGetterName], AttentionGetter
    ] = {}
    attention_getter_type: AttentionGetterType = AttentionGetterType.BOOKSY_PAY


class Buttons:
    COMPLETE_PAYMENT = AttentionGetterButton(
        canonical_name='complete_payment_button', display_name=_('Complete payment')
    )
    NOT_NOW = AttentionGetterButton(canonical_name='not_now_button', display_name=_('Not now'))
    DONE = AttentionGetterButton(canonical_name='done_button', display_name=_('Done'))


class BooksyPayAttentionGettersContentBeforePayment(AttentionGettersContent):
    """
    Note:
        If there are no variants, register just the control group one. It will be served by default.
    """

    @classmethod
    def _register_intro_non_cashback(cls) -> None:
        """
        It will be used by the control group.
        """
        BooksyPayAttentionGetterContentFactory.register(
            step=AttentionGetterStep.BEFORE_PAYMENT,
            name=BooksyPayAttentionGetterName.INTRO_BP_OPEN,
            content_callable=lambda: AttentionGetterContent(
                title=_('Introducing Booksy Pay'),
                line_1=_(
                    'Payments just got even easier with Booksy Pay, our new service that allows you to complete payment for your bookings right in the app.'
                ),
                line_2=_('It’s quick, contactless, and secure.'),
                line_3=_('Save time and complete your booking payment now.'),
                policy_url=None,
                policy_url_text=None,
                images=[AttentionGetterImage(type=BooksyPayAttentionGetterImageType.INTRO)],
                buttons=AttentionGetterButtons(
                    primary=Buttons.COMPLETE_PAYMENT,
                    secondary=Buttons.NOT_NOW,
                ),
            ),
        )

        BooksyPayAttentionGetterContentFactory.register(
            step=AttentionGetterStep.BEFORE_PAYMENT,
            name=BooksyPayAttentionGetterName.INTRO_BP_CLOSED,
            content_callable=lambda: AttentionGetterContent(
                title=_('Introducing Booksy Pay'),
                line_1=_(
                    'Payments just got even easier with Booksy Pay, our new service that allows you to complete payment for your bookings right in the app.'
                ),
                line_2=_('It’s quick, contactless, and secure.'),
                line_3=_(
                    'You can complete your payment up to {days_before_appt} days before your booking or after it’s done.'
                ).format(
                    days_before_appt=BP_AVAILABLE__DAYS_BEFORE_APPT,
                ),
                policy_url=None,
                policy_url_text=None,
                images=[AttentionGetterImage(type=BooksyPayAttentionGetterImageType.INTRO)],
                buttons=AttentionGetterButtons(
                    primary=Buttons.DONE,
                ),
            ),
        )

    @classmethod
    def _register_intro_cashback(cls) -> None:
        """
        A generic BP intro attention getter used for multiple cashback variants
        """
        BooksyPayAttentionGetterContentFactory.register(
            step=AttentionGetterStep.BEFORE_PAYMENT,
            name=BooksyPayAttentionGetterName.INTRO_BP_CLOSED_CASHBACK,
            content_callable=lambda: AttentionGetterContent(
                title=_('Introducing Booksy Pay'),
                line_1=_(
                    'Payments just got even easier with Booksy Pay, our new service that allows you to complete payment for your bookings right in the app.'
                ),
                line_2=_(
                    'It’s quick, contactless, and secure and you can earn cashback just by using it.'
                ),
                line_3=_(
                    'You can complete your payment up to {days_before_appt} days before your booking or after it’s done.'
                ).format(
                    days_before_appt=BP_AVAILABLE__DAYS_BEFORE_APPT,
                ),
                policy_url=None,
                policy_url_text=None,
                images=[
                    AttentionGetterImage(type=BooksyPayAttentionGetterImageType.INTRO_CASHBACK)
                ],
                buttons=AttentionGetterButtons(
                    primary=Buttons.DONE,
                ),
            ),
        )

    @classmethod
    def _register_cashback_initial(cls, cashback_variants) -> None:
        """
        Registers the initial version of cashback (so called `default`), which is used
        when cashback experiment is disabled and the BooksyPayCashbackPromoFlag ff is on.
        """
        if not (cashback_variant := cashback_variants.get(BooksyPayCashbackType.DEFAULT)):
            return

        BooksyPayAttentionGetterContentFactory.register(
            step=AttentionGetterStep.BEFORE_PAYMENT,
            name=BooksyPayAttentionGetterName.CASHBACK_INITIAL,
            content_callable=lambda: AttentionGetterContent(
                title=_(
                    'Complete payment with Booksy Pay and get {amount} {currency} cashback!'
                ).format(
                    amount=round(cashback_variant.amount),
                    currency=cashback_variant.currency,
                ),
                line_1=_(
                    'When you complete a booking of at least {min_booking_amount} {currency} and pay through the Booksy app. The promotion applies only to the first paid appointment.'
                ).format(
                    min_booking_amount=round(cashback_variant.min_booking_amount),
                    currency=cashback_variant.currency,
                ),
                line_2=None,
                line_3=None,
                policy_url='https://booksy.com/pl-pl/p/cashback?content-only=1',
                policy_url_text=_('Cashback Policy'),
                images=[AttentionGetterImage(type=BooksyPayAttentionGetterImageType.CASHBACK)],
                buttons=AttentionGetterButtons(
                    primary=Buttons.COMPLETE_PAYMENT,
                    secondary=Buttons.NOT_NOW,
                ),
            ),
        )

    @classmethod
    def _register_cashback_variant_a(cls, cashback_variants) -> None:
        """
        It will be used by the group A.
        """
        if not (cashback_variant := cashback_variants.get(BooksyPayCashbackType.VARIANT_A)):
            return

        BooksyPayAttentionGetterContentFactory.register(
            step=AttentionGetterStep.BEFORE_PAYMENT,
            name=BooksyPayAttentionGetterName.CASHBACK_VA,
            content_callable=lambda: AttentionGetterContent(
                title=_(
                    'Complete payment with Booksy Pay and get {amount} {currency} cashback!'
                ).format(
                    amount=round(cashback_variant.amount),
                    currency=cashback_variant.currency,
                ),
                line_1=_(
                    'Enjoy {amount} {currency} cashback when you complete a booking of at least {min_booking_amount} {currency} '
                    'and pay with Visa or Mastercard in the Booksy app.'
                ).format(
                    amount=round(cashback_variant.amount),
                    min_booking_amount=round(cashback_variant.min_booking_amount),
                    currency=cashback_variant.currency,
                ),
                line_2=_(
                    'The promotion applies only to appointments paid for during the promotion term.* '
                    'Cashback is one-time only.'
                ),
                line_3=_(
                    '*Promotion is valid from 03/12/2024 to 31/12/2024 or until the promotion budget is used up.'
                ),
                policy_url='https://booksy.com/pl-pl/p/cashback?variant=a&content-only=1',
                policy_url_text=_('Cashback Policy'),
                images=[AttentionGetterImage(type=BooksyPayAttentionGetterImageType.CASHBACK)],
                buttons=AttentionGetterButtons(
                    primary=Buttons.COMPLETE_PAYMENT,
                    secondary=Buttons.NOT_NOW,
                ),
            ),
        )

    @classmethod
    def _register_cashback_variant_b(cls, cashback_variants: dict) -> None:
        """
        It will be used by the group B.
        """
        if not (cashback_variant := cashback_variants.get(BooksyPayCashbackType.VARIANT_B)):
            return

        BooksyPayAttentionGetterContentFactory.register(
            step=AttentionGetterStep.BEFORE_PAYMENT,
            name=BooksyPayAttentionGetterName.CASHBACK_VB,
            content_callable=lambda: AttentionGetterContent(
                title=_('Complete payment with Booksy Pay and get {amount}% cashback!').format(
                    amount=round(cashback_variant.amount)
                ),
                line_1=_(
                    'Enjoy {amount}% cashback when you complete a booking and pay via Visa or Mastercard in the Booksy app. '
                    'The maximum cashback is {max_cashback_amount} {currency}'
                ).format(
                    amount=round(cashback_variant.amount),
                    currency=cashback_variant.currency,
                    max_cashback_amount=round(cashback_variant.max_cashback_amount),
                ),
                line_2=_(
                    'The promotion applies only to appointments paid for during the promotion term.* '
                    'Cashback is one-time only.'
                ),
                line_3=_(
                    '*Promotion is valid from 03/12/2024 to 31/12/2024 or until the promotion budget is used up.'
                ),
                policy_url='https://booksy.com/pl-pl/p/cashback?variant=b&content-only=1',
                policy_url_text=_('Cashback Policy'),
                images=[AttentionGetterImage(type=BooksyPayAttentionGetterImageType.CASHBACK)],
                buttons=AttentionGetterButtons(
                    primary=Buttons.COMPLETE_PAYMENT,
                    secondary=Buttons.NOT_NOW,
                ),
            ),
        )

    @classmethod
    def _register_cashback_variant_c(cls, cashback_variants: dict) -> None:
        """
        It will be used by the group C.
        """
        if not (cashback_variant := cashback_variants.get(BooksyPayCashbackType.VARIANT_C)):
            return

        BooksyPayAttentionGetterContentFactory.register(
            step=AttentionGetterStep.BEFORE_PAYMENT,
            name=BooksyPayAttentionGetterName.CASHBACK_VC_APPT_1,
            content_callable=lambda: AttentionGetterContent(
                title=_(
                    'Complete payment with Booksy Pay and get {amount} {currency} cashback!'
                ).format(
                    amount=round(cashback_variant.amount),
                    currency=cashback_variant.currency,
                ),
                line_1=_(
                    'Enjoy {amount} {currency} cashback when you complete payments for two bookings and pay via Visa or Mastercard in the Booksy app. '
                    'The minimum value of each appointment must be at least {min_booking_amount} {currency}.'
                ).format(
                    amount=round(cashback_variant.amount),
                    currency=cashback_variant.currency,
                    min_booking_amount=round(cashback_variant.min_booking_amount),
                ),
                line_2=_(
                    'The promotion applies only to appointments paid for during the promotion term.* '
                    'Cashback is one-time only.'
                ),
                line_3=_(
                    '*Promotion is valid from 03/12/2024 to 31/12/2024 or until the promotion budget is used up.'
                ),
                policy_url='https://booksy.com/pl-pl/p/cashback?variant=c&content-only=1',
                policy_url_text=_('Cashback Policy'),
                images=[AttentionGetterImage(type=BooksyPayAttentionGetterImageType.CASHBACK)],
                buttons=AttentionGetterButtons(
                    primary=Buttons.COMPLETE_PAYMENT,
                    secondary=Buttons.NOT_NOW,
                ),
            ),
        )

        BooksyPayAttentionGetterContentFactory.register(
            step=AttentionGetterStep.BEFORE_PAYMENT,
            name=BooksyPayAttentionGetterName.CASHBACK_VC_APPT_2,
            content_callable=lambda: AttentionGetterContent(
                title=_(
                    'Complete second payment with Booksy Pay and get {amount} {currency} cashback'
                ).format(
                    amount=round(cashback_variant.amount),
                    currency=cashback_variant.currency,
                ),
                line_1=_(
                    'Enjoy {amount} {currency} cashback when you complete payments for two bookings and pay via Visa or Mastercard in the Booksy app. '
                    'The minimum value of each appointment must be at least {min_booking_amount} {currency}.'
                ).format(
                    amount=round(cashback_variant.amount),
                    currency=cashback_variant.currency,
                    min_booking_amount=round(cashback_variant.min_booking_amount),
                ),
                line_2=_(
                    'The promotion applies only to appointments paid for during the promotion term.* '
                    'Cashback is one-time only.'
                ),
                line_3=_(
                    '*Promotion is valid from 03/12/2024 to 31/12/2024 or until the promotion budget is used up.'
                ),
                policy_url='https://booksy.com/pl-pl/p/cashback?variant=c&content-only=1',
                policy_url_text=_('Cashback Policy'),
                images=[AttentionGetterImage(type=BooksyPayAttentionGetterImageType.CASHBACK)],
                buttons=AttentionGetterButtons(
                    primary=Buttons.COMPLETE_PAYMENT,
                    secondary=Buttons.NOT_NOW,
                ),
            ),
        )

    @classmethod
    def register(cls, *args, **kwargs) -> None:
        cashback_variants = kwargs['cashback_variants']

        cls._register_intro_non_cashback()

        if cashback_variants:
            cls._register_intro_cashback()
            cls._register_cashback_initial(cashback_variants)
            cls._register_cashback_variant_a(cashback_variants)
            cls._register_cashback_variant_b(cashback_variants)
            cls._register_cashback_variant_c(cashback_variants)


class BooksyPayAttentionGettersContentAfterPayment(AttentionGettersContent):
    """
    Note:
        If there are no variants, no need to register anything.
    """

    @classmethod
    def _register_cashback_variant_c(cls, cashback_variants: dict) -> None:
        """
        It will be used by the group C.
        """
        if not (cashback_variant := cashback_variants.get(BooksyPayCashbackType.VARIANT_C)):
            return

        BooksyPayAttentionGetterContentFactory.register(
            step=AttentionGetterStep.AFTER_PAYMENT,
            name=BooksyPayAttentionGetterName.CASHBACK_VC_AFTER_FIRST_PAYMENT,
            content_callable=lambda: AttentionGetterContent(
                title=_('Complete another payment to get {amount} {currency} cashback').format(
                    amount=round(cashback_variant.amount),
                    currency=cashback_variant.currency,
                ),
                line_1=_(
                    'Enjoy {amount} {currency} cashback when you complete payments for two bookings and pay via card with Booksy Pay.'
                ).format(
                    amount=round(cashback_variant.amount),
                    currency=cashback_variant.currency,
                ),
                line_2=_(
                    'Once we have verified that all the necessary conditions have been met, your cashback will be sent to the same payment method you used to make your booking within 7 calendar days.'
                ),
                line_3=None,
                policy_url=None,
                policy_url_text=None,
                images=[AttentionGetterImage(type=BooksyPayAttentionGetterImageType.CASHBACK)],
                buttons=AttentionGetterButtons(
                    primary=Buttons.DONE,
                ),
            ),
        )

    @classmethod
    def _register_cashback_on_the_way(cls) -> None:
        BooksyPayAttentionGetterContentFactory.register(
            step=AttentionGetterStep.AFTER_PAYMENT,
            name=BooksyPayAttentionGetterName.CASHBACK_ON_THE_WAY,
            content_callable=lambda: AttentionGetterContent(
                title=_('Your cashback is on the way'),
                line_1=_(
                    'Once we have verified that all the necessary conditions have been met, your cashback will be sent to the same card you used to pay for your booking within 7 calendar days.'
                ),
                line_2=None,
                line_3=None,
                policy_url=None,
                policy_url_text=None,
                images=[AttentionGetterImage(type=BooksyPayAttentionGetterImageType.CASHBACK)],
                buttons=AttentionGetterButtons(
                    primary=Buttons.DONE,
                ),
            ),
        )

    @classmethod
    def register(cls, *args, **kwargs) -> None:
        cashback_variants = kwargs['cashback_variants']

        if cashback_variants:
            cls._register_cashback_variant_c(cashback_variants)
            cls._register_cashback_on_the_way()


class BooksyPayAttentionGettersContentBookingConfirmation(AttentionGettersContent):
    """
    Note:
        If there are no variants, register just the non-cashback BP intro. It will be served by default.
    """

    @classmethod
    def _register_intro_non_cashback(cls) -> None:
        """
        It will be used by the control group.
        """
        BooksyPayAttentionGetterContentFactory.register(
            step=AttentionGetterStep.BOOKING_CONFIRMATION,
            name=BooksyPayAttentionGetterName.INTRO_BP_OPEN,
            content_callable=lambda: AttentionGetterContent(
                title='Complete your payment with Booksy Pay and get everything set!',
                line_1=None,
                line_2=None,
                line_3=None,
                policy_url=None,
                policy_url_text=None,
                images=[AttentionGetterImage(type=BooksyPayAttentionGetterImageType.INTRO)],
                buttons=AttentionGetterButtons(
                    primary=Buttons.COMPLETE_PAYMENT,
                    secondary=Buttons.NOT_NOW,
                ),
            ),
        )

        BooksyPayAttentionGetterContentFactory.register(
            step=AttentionGetterStep.BOOKING_CONFIRMATION,
            name=BooksyPayAttentionGetterName.INTRO_BP_CLOSED,
            content_callable=lambda: AttentionGetterContent(
                title='Meet Booksy Pay!',
                line_1='Pay for bookings quickly, securely, and contactless. Pay anytime up to 10 days before your appointment.',
                line_2=None,
                line_3=None,
                policy_url=None,
                policy_url_text=None,
                images=[AttentionGetterImage(type=BooksyPayAttentionGetterImageType.INTRO)],
                buttons=AttentionGetterButtons(
                    primary=Buttons.DONE,
                ),
            ),
        )

    @classmethod
    def _register_intro_cashback(cls) -> None:
        """
        A generic BP intro attention getter used for multiple cashback variants
        """
        BooksyPayAttentionGetterContentFactory.register(
            step=AttentionGetterStep.BOOKING_CONFIRMATION,
            name=BooksyPayAttentionGetterName.INTRO_BP_CLOSED_CASHBACK,
            content_callable=lambda: AttentionGetterContent(
                title='Meet Booksy Pay!',
                line_1=(
                    'Pay for bookings quickly, securely, and contactless - anytime up to 10 days before your appointment, and get cashback!'
                ),
                line_2=None,
                line_3=None,
                policy_url=None,
                policy_url_text=None,
                images=[
                    AttentionGetterImage(type=BooksyPayAttentionGetterImageType.INTRO_CASHBACK)
                ],
                buttons=AttentionGetterButtons(
                    primary=Buttons.DONE,
                ),
            ),
        )

    @classmethod
    def _register_cashback_initial(cls, cashback_variants) -> None:
        """
        Registers the initial version of cashback (so called `default`), which is used
        when cashback experiment is disabled and the BooksyPayCashbackPromoFlag ff is on.
        """
        if not (cashback_variant := cashback_variants.get(BooksyPayCashbackType.DEFAULT)):
            return

        BooksyPayAttentionGetterContentFactory.register(
            step=AttentionGetterStep.BOOKING_CONFIRMATION,
            name=BooksyPayAttentionGetterName.CASHBACK_INITIAL,
            content_callable=lambda: AttentionGetterContent(
                title=(
                    'Complete payment with Booksy Pay and get {amount} {currency} cashback'
                ).format(
                    amount=round(cashback_variant.amount),
                    currency=cashback_variant.currency,
                ),
                line_1='Applies only to the first paid appointment.',
                line_2=None,
                line_3=None,
                policy_url='https://booksy.com/pl-pl/p/cashback?content-only=1',
                policy_url_text=_('Cashback Policy'),
                images=[AttentionGetterImage(type=BooksyPayAttentionGetterImageType.CASHBACK)],
                buttons=AttentionGetterButtons(
                    primary=Buttons.COMPLETE_PAYMENT,
                    secondary=Buttons.NOT_NOW,
                ),
            ),
        )

    @classmethod
    def register(cls, *args, **kwargs) -> None:
        """
        NOTE:
            The final version of cashback is unknown yet. It depends on the cashback experiment.
            Therefore, only the default/initial version is registered here.
        """
        cashback_variants = kwargs['cashback_variants']

        cls._register_intro_non_cashback()

        if cashback_variants:
            cls._register_intro_cashback()
            cls._register_cashback_initial(cashback_variants)


@dataclass(frozen=True)
class AttentionGetterAnalytics:
    modal_type: AnalyticsModalType
    modal_type_version: AnalyticsModalTypeVersion | None = None


# The mapping defined as follows:
# BooksyPayAttentionGetterName: {BooksyPayCashbackType: AttentionGetterAnalytics}
ATTENTION_GETTER_NAME_TO_ANALYTICS = {
    BooksyPayAttentionGetterName.INTRO_BP_OPEN: {
        ANY: AttentionGetterAnalytics(
            modal_type=AnalyticsModalType.INTRODUCTION_WITH_PAYMENT,
            modal_type_version=None,
        ),
    },
    BooksyPayAttentionGetterName.INTRO_BP_CLOSED: {
        ANY: AttentionGetterAnalytics(
            modal_type=AnalyticsModalType.INTRODUCTION,
            modal_type_version=None,
        ),
    },
    BooksyPayAttentionGetterName.INTRO_BP_CLOSED_CASHBACK: {
        ANY: AttentionGetterAnalytics(
            modal_type=AnalyticsModalType.INTRODUCTION,
            modal_type_version=None,
        ),
    },
    BooksyPayAttentionGetterName.CASHBACK_INITIAL: {
        ANY: AttentionGetterAnalytics(
            modal_type=AnalyticsModalType.CASHBACK,
            modal_type_version=None,
        ),
    },
    BooksyPayAttentionGetterName.CASHBACK_VA: {
        ANY: AttentionGetterAnalytics(
            modal_type=AnalyticsModalType.CASHBACK,
            modal_type_version=AnalyticsModalTypeVersion.A,
        ),
    },
    BooksyPayAttentionGetterName.CASHBACK_VB: {
        ANY: AttentionGetterAnalytics(
            modal_type=AnalyticsModalType.CASHBACK,
            modal_type_version=AnalyticsModalTypeVersion.B,
        ),
    },
    BooksyPayAttentionGetterName.CASHBACK_VC_APPT_1: {
        ANY: AttentionGetterAnalytics(
            modal_type=AnalyticsModalType.CASHBACK,
            modal_type_version=AnalyticsModalTypeVersion.C1,
        ),
    },
    BooksyPayAttentionGetterName.CASHBACK_VC_APPT_2: {
        ANY: AttentionGetterAnalytics(
            modal_type=AnalyticsModalType.CASHBACK,
            modal_type_version=AnalyticsModalTypeVersion.C2,
        ),
    },
    BooksyPayAttentionGetterName.CASHBACK_VC_AFTER_FIRST_PAYMENT: {
        ANY: AttentionGetterAnalytics(
            modal_type=AnalyticsModalType.CASHBACK_AFTER_PAYMENT,
            modal_type_version=AnalyticsModalTypeVersion.C1,
        ),
    },
    BooksyPayAttentionGetterName.CASHBACK_ON_THE_WAY: {
        ANY: AttentionGetterAnalytics(
            modal_type=AnalyticsModalType.CASHBACK_AFTER_PAYMENT,
            modal_type_version=None,
        ),
        BooksyPayCashbackType.VARIANT_C: AttentionGetterAnalytics(
            modal_type=AnalyticsModalType.CASHBACK_AFTER_PAYMENT,
            modal_type_version=AnalyticsModalTypeVersion.C2,
        ),
    },
}

BooksyPayAttentionGettersContentBeforePayment.register(cashback_variants=CASHBACK_VARIANTS)
BooksyPayAttentionGettersContentAfterPayment.register(cashback_variants=CASHBACK_VARIANTS)
BooksyPayAttentionGettersContentBookingConfirmation.register(cashback_variants=CASHBACK_VARIANTS)
