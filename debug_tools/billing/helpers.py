import typing as t

from debug_tools.utils import logger
from webapps.billing.enums import ProductType, SubscriptionStatus
from webapps.billing.models import (
    BillingProduct,
    BillingProductOffer,
    BillingSubscription,
)
from webapps.business.models import Business


def log_subscription_info(subscription):
    _info_elements = [
        f'status: {SubscriptionStatus.choices_map().get(subscription.status)}',
        f'balance: {subscription.balance}',
        f'start - expiry: '
        f'{subscription.date_start} - {subscription.date_expiry or "without expiry"}',
        f'next_billing_date: {subscription.next_billing_date}',
        f'paid_through_date: {subscription.paid_through_date}',
    ]
    _info = '\n'.join(
        ['Subscription info:'] + ['\t' + e for e in _info_elements],
    )
    logger.info(_info)


def log_business_info(business):
    _info_elements = [
        f'status: {Business.Status.choices_map().get(business.status)}',
        f'payment source: {Business.PaymentSource.choices_map().get(business.payment_source)}',
        f'paid till: {business.paid_till}',
        f'has new billing: {business.has_new_billing}',
        f'registration source: {business.registration_source}',
    ]
    _info = '\n'.join(
        ['Business info:'] + ['\t' + e for e in _info_elements],
    )
    logger.info(_info)


class BillingHelper:
    @classmethod
    def is_new_billing(cls, business: Business):
        business = cls.get_business(business)
        return all(
            [
                business.has_new_billing,
                business.payment_source == Business.PaymentSource.BRAINTREE_BILLING,
            ]
        )

    @classmethod
    def get_business(
        cls,
        business: t.Union[int, Business],
    ) -> Business:
        if isinstance(business, int):
            business = Business.objects.get(pk=business)
        logger.info('Selected business: {}'.format(business))
        return business

    @staticmethod
    def get_sub_for_business(business: t.Union[int, Business]):
        business = BillingHelper.get_business(business)
        if not (
            subscription := BillingSubscription.get_current_or_pending_subscription(business.id)
        ):
            subscription = (
                BillingSubscription.objects.filter(business=business).order_by('-id').first()
            )
        if not subscription:
            logger.info('No subscription for business {} was found'.format(business))

        logger.info('Selected billing subscription: {}'.format(subscription))
        return subscription

    @staticmethod
    def get_subscription(subscription: t.Union[int, BillingSubscription]):
        if isinstance(subscription, int):
            return BillingSubscription.objects.get(id=subscription)
        logger.info('Selected billing subscription: {}'.format(subscription))
        return subscription

    @classmethod
    def get_offer(cls, offer: t.Union[int, BillingProductOffer]):
        if isinstance(offer, int):
            offer = BillingProductOffer.objects.get(id=offer)
        logger.info('Selected Billing Product offer {}'.format(offer))
        return offer

    @classmethod
    def get_current_offer(cls):
        offer = BillingProductOffer.objects.filter(default=True).order_by('-id').first()
        logger.info('Selected Billing Product default offer {}'.format(offer))
        return offer

    @classmethod
    def get_saas_product(cls, offer: t.Union[int, BillingProductOffer]):
        offer = cls.get_offer(offer)
        valid_products = (
            offer.offer_items.filter(active=True, product__active=True)
            .select_related('product')
            .only('product_id', 'product__product_type')
        )
        saas_id = next(
            (x.product_id for x in valid_products if x.product.product_type == ProductType.SAAS),
            None,
        )
        if saas := BillingProduct.objects.filter(id=saas_id).first():
            logger.info('Selected Billing Active SaaS product {}'.format(saas))
            return saas
        logger.info('No SaaS product for offer {offer}'.format(offer=offer))
