# pylint: disable = consider-using-f-string

import typing as t
from datetime import timed<PERSON><PERSON>
from decimal import Decimal

import braintree
from django.conf import settings

from country_config import Country
from debug_tools.billing.helpers import BillingHelper, log_business_info, log_subscription_info
from debug_tools.utils import BaseDebugTools, log_error, log_results, logger
from lib.tools import convert_date_str_to_aware_dt, id_to_external_api, tznow
from webapps.billing.billing_cycle_switch import BillingCycleSwitch
from webapps.billing.business_status import ReferenceSubscriptionData, compute_business_status
from webapps.billing.enums import TransactionStatus
from webapps.billing.models import BillingSubscription, BillingTransaction
from webapps.billing.payment_processor import PaymentProcessor
from webapps.billing.retry_charge import RetryCharge
from webapps.billing.subscription_creator import SubscriptionCreator
from webapps.billing.tasks import switch_billing_cycles_task
from webapps.booking.models import BookingSources
from webapps.braintree_app.payment_processor import BraintreePaymentMethodResult
from webapps.braintree_app.utils import parse_errors
from webapps.business.models import Business
from webapps.navision.enums import InvoiceDetailsFormsEnum
from webapps.navision.invoice_details_forms_serializers import (
    MandatoryTaxInvoiceDetailsSerializer,
    OptionalTaxInvoiceDetailsSerializer,
    TaxIfVATRegisteredInvoiceDetailsSerializer,
    TwoFieldsTaxIdInvoiceDetailsSerializer,
)
from webapps.notification.models import NotificationSMSStatistics
from webapps.structure.models import Region


def _create_failed_transaction(subscription):
    return BillingTransaction.objects.create(
        business_id=subscription.business_id,
        subscription=subscription,
        amount=subscription.balance,
        currency=subscription.currency,
        status=TransactionStatus.FAILED,
        billing_cycle_id=subscription.latest_cycle.id,
    )


def _set_as_blocked(
    subscription, balance=Decimal('100'), next_billing_date=None, with_transaction=True
):
    BillingCycleSwitch.handle_unsuccessful_charge(
        subscription,
        next_billing_date=(next_billing_date or subscription.latest_cycle.date_end),
        charge_amount=balance,
    )
    if with_transaction:
        _create_failed_transaction(subscription)
    subscription.refresh_from_db()
    logger.info('Set subscription as blocked')
    log_subscription_info(subscription)
    return subscription


def _set_as_active(subscription, next_billing_date):
    BillingCycleSwitch.handle_successful_charge(
        subscription,
        next_billing_date=next_billing_date,
        charge_amount=subscription.balance,
    )
    subscription.refresh_from_db()
    logger.info('Set subscription as active')
    log_subscription_info(subscription)
    return subscription


class BillingTools(BaseDebugTools):
    TAX_ID_PER_COUNTRY = {
        Country.GB: 'GB123456789',
        Country.PL: '4444444444',
        Country.ES: 'ESA1234567U',
        Country.IT: 'IT00123456789',
        Country.NL: 'NL999999999B01',
        Country.IE: 'IE1234567FA',
        Country.DE: 'DE999999999',
        Country.PT: 'PT123456789',
        Country.SE: 'SE012345678901',
    }

    @classmethod
    @property
    def supported(cls):
        return {
            cls.set_billing,
            cls.change_registration_source,
            cls.retry_charge,
            cls.add_payment_method,
            cls.create_subscription,
            cls.set_poa,
            cls.set_pob,
            cls.set_paid,
            cls.add_or_get_buyer,
        }

    @classmethod
    def set_billing(cls, business: t.Union[int, Business]):
        """
            Set business to Merchant billing eligible.

            params:
                business: id or business object

        returns: business
        """
        business = BillingHelper.get_business(business)
        if BillingHelper.is_new_billing(business):
            logger.info('Business is already switched to new billing.')
            return business
        business.has_new_billing = True
        business.payment_source = Business.PaymentSource.BRAINTREE_BILLING
        business.save()
        logger.info('Set up business to new billing.')
        log_business_info(business)
        return business

    @classmethod
    def change_registration_source(
        cls, business: t.Union[int, Business], registration_source_name: str
    ):
        """
            Change registration source to given one.

        params:
            business: id or business object
            registration_source_name: name of registration source. Options:
                - Web
                - Biz 3.0 Web
                - Biz 3.0 Tablet Android
                - Biz 3.0 Tablet iOS
                - Widget
                - Android
                - iPhone
                - Internal
                - GoogleFeeds
                - Facebook
                - YelpFeeds
                - GrouponFeeds
                - Instagram
                - Lavito
                - PartnersFacebook
                - PartnersGroupon
                - DealWithIt
                - LandingPage
                - PerformanceTest
                - PublicApi
                - Admin
                - Salesforce

        returns: None
        """
        business = BillingHelper.get_business(business)
        business.registration_source = BookingSources.objects.filter(
            name=registration_source_name
        ).first()
        business.save()
        logger.info('Set up business registration source to %s.', registration_source_name)
        return business

    @classmethod
    def retry_charge(cls, business: t.Union[int, Business]):
        """
            Auto retry charge for POB business and blocked subscription (only).
            [Warning] Deletes all Billing transactions connected with subscription

        params:
            business: id or business object

        returns: None
        """
        logger.info('Retrying auto charge for business')
        business = BillingHelper.get_business(business)
        subscription = BillingHelper.get_sub_for_business(business)
        if subscription:
            if subscription.can_be_recharged:
                BillingTransaction.all_objects.filter(subscription=subscription).delete()
                results = RetryCharge.retry_for_subscription(subscription)
                if 'error_code' in results:
                    log_error(results)
                    return
                compute_business_status(business_id=business.id)
                subscription.refresh_from_db()
                log_results(results=results)
                business.refresh_from_db()
            else:
                logger.info('Nothing to recharge on subscription: %s', str(subscription))
            log_subscription_info(subscription)
            log_business_info(business)
        else:
            logger.info('No subscription for business: %s. Skipping.', str(business))
            log_business_info(business)

    @classmethod
    def add_payment_method(cls, business: t.Union[int, Business]):
        """
        Adds billing payment method for business.

        If you want to decide which payment method to add, select nonce from
        https://developer.paypal.com/braintree/docs/reference/general/testing/python#nonces-representing-cards

        params:

            business: id or business object
        returns: results from payment processor
        """

        business = BillingHelper.get_business(business)
        PaymentProcessor.get_or_create_customer(business)
        nonce = (
            'fake-three-d-secure-visa-full-authentication-nonce'
            if settings.BRAINTREE_3D_SECURE_ELIGIBLE
            else 'fake-valid-visa-nonce'
        )

        city = Region.objects.filter(type=Region.Type.CITY).first()
        zipcode = Region.objects.filter(type=Region.Type.ZIP).first()
        payment_method_data = {
            'payment_method_nonce': nonce,
            'customer_id': id_to_external_api(business.id),
            'billing_address': {
                "street_address": "Sesame street 123",
                "extended_address": "1st Floor",
                "postal_code": zipcode.name,
                "locality": city.name,
                "country_name": Country.get_label(settings.API_COUNTRY.upper()),
            },
            'options': {
                'make_default': True,
                'fail_on_duplicate_payment_method': False,
            },
        }

        logger.info('Adding payment method...')

        braintree_result = braintree.PaymentMethod.create(payment_method_data)

        if not braintree_result.is_success:
            errors = parse_errors(braintree_result)
            results = BraintreePaymentMethodResult(
                is_success=False,
                errors=errors,
                message=braintree_result.message,
            )
            log_error(results=results)
            return results

        results = BraintreePaymentMethodResult(
            is_success=True,
            payment_method=braintree_result.payment_method,
        )

        PaymentProcessor._save_payment_method(  # pylint: disable=protected-access
            business_id=business.id,
            payment_method_result=results,
        )
        log_results(results=payment_method_data)
        return results

    @classmethod
    def create_subscription(
        cls,
        business: t.Union[int, Business],
        subscription_start: t.Optional[str] = None,
        add_payment_method: t.Optional[bool] = True,
    ):
        """
        Create active billing subscription for business.

        params:
            business: id or business object
            subscription_start (optional, default: now): when sub should start.
                Helps to create PENDING subscriptions. format: YYYY-MM-DD
            add_payment_method (optional, default: True): add payment method for business.
                Creates new payment method every time.

        returns: billing subscription
        """

        business = cls.set_billing(business)
        if (sub := BillingSubscription.get_current_or_pending_subscription(business.id)) and (
            (sub.is_pending and subscription_start)
            or (not sub.is_pending and not subscription_start)
        ):
            log_results(message='Subscription for business already exists, nothing to do')
            log_subscription_info(sub)
            return sub

        offer = BillingHelper.get_current_offer()
        if not offer:
            results = {
                'error': 'No active offer!',
            }
        elif not (saas := BillingHelper.get_saas_product(offer.id)):
            results = {
                'error': f'No active SaaS product for offer {offer}!',
            }
        else:
            if add_payment_method:
                cls.add_payment_method(business)
            cls.add_or_get_buyer(business)
            if subscription_start:
                subscription_start = convert_date_str_to_aware_dt(subscription_start, '%Y-%m-%d')
            results = SubscriptionCreator.new_purchase(
                business_id=business.id,
                offer_id=offer.id,
                product_ids=[saas.id],
                subscription_start=subscription_start,
            )
        if subscription_id := results.get('subscription_id'):
            log_results('Subscription created!', results)
            sub = BillingHelper.get_subscription(subscription_id)
            log_subscription_info(sub)
            return sub
        log_error(results)
        return results

    @classmethod
    def set_paid(
        cls,
        business: t.Union[int, Business],
        next_billing_date_days: t.Optional[int] = 30,
        add_payment_method: t.Optional[bool] = True,
    ):
        """
        Set selected business to Paid Active status.

        params:

            business: id or business object
            next_billing_date_days (optional, default 30):
                How many days to next billing date should be set.
            add_payment_method (optional, default: True): add payment method for business.
                Creates new payment method every time.

        returns: business
        """

        business = cls.set_billing(business)
        subscription = BillingHelper.get_sub_for_business(business)
        next_billing_date = tznow() + timedelta(days=next_billing_date_days)
        if subscription:
            _set_as_active(subscription, next_billing_date)
        else:
            subscription = cls.create_subscription(business, add_payment_method=add_payment_method)
            subscription.next_billing_date = next_billing_date
            subscription.save()

        compute_business_status(business_id=business.id)
        business.refresh_from_db()
        log_results(message='Business set to Paid Active.')
        log_business_info(business)
        log_subscription_info(subscription)
        return business

    @classmethod
    def set_poa(cls, business: t.Union[int, Business], overdue_days: int = 1):
        """
        Set selected business to POA (Paid Overdue Active) status.

        params:

            business: id or business object
            overdue_days (optional, default 1):
                How many days business is on POA.

        returns: business
        """

        business = cls.set_billing(business)
        subscription = BillingHelper.get_sub_for_business(business)
        _set_as_blocked(subscription)

        _now = tznow()
        subscription.paid_through_date = _now - timedelta(days=overdue_days)
        subscription.save()
        business.paid_till = subscription.paid_through_date
        business.save()
        compute_business_status(business_id=business.id)
        business.refresh_from_db()
        log_results(message='Business set to POA.')
        log_business_info(business)
        log_subscription_info(subscription)
        return business

    @classmethod
    def set_pob(cls, business: t.Union[int, Business], account_lock_in_days: int = 7):
        """
        Set selected business to POB (Paid Overdue Blocked) status.

        params:

            business: id or business object
            account_lock_in_days (optional, default 1):
                How many days to account locked subscription should be set

        returns: business
        """

        business = cls.set_billing(business)
        subscription = BillingHelper.get_sub_for_business(business)
        _set_as_blocked(
            subscription,
            next_billing_date=(
                tznow()
                + subscription.payment_period
                - timedelta(
                    days=(settings.STATUS_FLOW__BLOCKED_OVERDUE_AFTER_DAYS - account_lock_in_days)
                )
            ),
        )

        business.paid_till = None
        business.save()

        reference_data = ReferenceSubscriptionData(
            id=subscription.id,
            paid_through_date=(
                tznow() - timedelta(days=settings.STATUS_FLOW__BLOCKED_OVERDUE_AFTER_DAYS + 1)
            ),
        )
        compute_business_status(business_id=business.id, reference_data=reference_data)
        business.refresh_from_db()
        log_results(message='Business set to POB.')
        log_business_info(business)
        log_subscription_info(subscription)
        return business

    @classmethod
    def add_or_get_buyer(
        cls,
        business: t.Union[int, Business],
        vat_registered=False,
        tax_id=None,
    ):
        """
        Adds subscription buyer for business.
        If buyer exists, returns one.

        params:

            business: id or business object
            tax_id (optional): tax_id of Business.
                Needs to be valid. Country-specific. if empty, valid one will be added.
            vat_registered (optional). False by default - on certain countries
                (like GB or PL, so-called Invoicing Path 2,
                where tax ID is mandatory if VAT registered),
                if it is set to True and no tax_id is passed,
                the tax_id will be set automatically.
                If we do not support invoicing for a country,
                the so-called Path 3 is used, where tax_id and vat_registered are fully optional.

        returns: buyer
        """
        form = settings.SUPPORTED_INVOICE_FORM
        business = BillingHelper.get_business(business)
        if form == InvoiceDetailsFormsEnum.PATH_1:
            tax_id = tax_id or cls.TAX_ID_PER_COUNTRY.get(settings.API_COUNTRY)
            serializer_cls = MandatoryTaxInvoiceDetailsSerializer
        elif form == InvoiceDetailsFormsEnum.PATH_2:
            if vat_registered and tax_id is None:
                tax_id = cls.TAX_ID_PER_COUNTRY.get(settings.API_COUNTRY)
            serializer_cls = TaxIfVATRegisteredInvoiceDetailsSerializer
        elif form == InvoiceDetailsFormsEnum.TWO_TAX_ID_FIELDS:
            serializer_cls = TwoFieldsTaxIdInvoiceDetailsSerializer
        else:
            serializer_cls = OptionalTaxInvoiceDetailsSerializer

        if business.buyer:
            buyer_data = serializer_cls(business.buyer).data['data']
            log_results('Buyer found! returning existing buyer.', results=buyer_data)
            return business.buyer
        data = {
            'business_id': business.id,
            'vat_registered': vat_registered,
            'address_details': 'Fifth Avenue 33 flat 35',
            'city': 'Las Testeras',
            'zipcode': Region.objects.filter(type=Region.Type.ZIP).first().name,
            'entity_name': business.name or 'Headless barber',
            'invoice_email': business.owner.email or '<EMAIL>',
            'tax_id': tax_id,
        }
        context = {'business': business}
        serializer = serializer_cls(data=data, context=context)
        if serializer.is_valid():
            buyer = serializer.save()
            buyer_data = serializer.validated_data
            log_results(message='Buyer created!', results=buyer_data)
            return buyer

        log_error(f'Buyer data errors: {serializer.errors}')

    @classmethod
    def shift_cycle(
        cls,
        business: t.Union[int, Business],
        switch_cycle_task: bool = False,
    ):
        """
        Shifting billing subscription in time by given number of default payment periods.

        params:
            business: id or business object
            switch_cycle_task: run celery switch billing cycles task, default False

        returns: billing subscriptions belong to given business
        """
        try:
            business = BillingHelper.get_business(business)
        except BillingSubscription.DoesNotExist:
            log_error('No matching business')
            return

        if not (subscriptions := business.billing_subscriptions.all()):
            log_error('Business has no subscritions')
            return

        for subscription in subscriptions.all():
            delta = subscription.payment_period

            subscription.date_start -= delta
            subscription.next_billing_date -= delta
            subscription.paid_through_date -= delta

            if subscription.date_expiry:
                subscription.date_expiry -= delta

            if subscription.date_end:
                subscription.date_end -= delta

            subscription.save()

            for subscribed_product in subscription.subscribed_products.all():
                subscribed_product.date_start -= delta
                if subscribed_product.date_end:
                    subscribed_product.date_end -= delta

                subscribed_product.save()

            for billing_cycle in subscription.billing_cycles.all():
                sms_notifications = NotificationSMSStatistics.objects.filter(
                    business_id=business.id,
                    date__gte=billing_cycle.date_start,
                    date__lte=billing_cycle.date_end,
                )
                for notification in sms_notifications:
                    notification.date -= delta
                    notification.save()

                billing_cycle.date_start -= delta
                billing_cycle.date_end -= delta
                billing_cycle.save()

            for transaction in subscription.transactions.all():
                BillingTransaction.objects.filter(id=transaction.id).update(
                    created=transaction.created - delta
                )

            subscription.business.paid_till -= delta
            subscription.business.save()

        if switch_cycle_task:
            switch_billing_cycles_task()

        for subscription in (
            subscriptions := BillingSubscription.objects.filter(business_id=business.id)
        ):
            log_subscription_info(subscription)

        return subscriptions
