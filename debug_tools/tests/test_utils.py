from unittest.mock import call, patch

from debug_tools.utils import BaseDebugTools
from webapps.admin_extra.tests import DjangoTestCase


class DummyTestingUtils(BaseDebugTools):
    @classmethod
    @property
    def supported(cls):
        return {
            cls.method1,
            cls.method2,
        }

    @classmethod
    def method1(cls):
        """This is method1 docs.

        return: method1 called.
        """
        return 'method1 called'

    def method2(self):
        return 'method2 called'

    @classmethod
    def other(cls):
        return 'other called'


class TestBaseTestingUtils(DjangoTestCase):
    @patch('debug_tools.utils.logger')
    def test_list_methods(self, logger_mock):
        DummyTestingUtils.methods  # pylint: disable=pointless-statement
        self.assertEqual(logger_mock.info.call_count, 2)
        self.assertIn(call('DummyTestingUtils.method1'), logger_mock.info.mock_calls)
        self.assertIn(call('DummyTestingUtils.method2'), logger_mock.info.mock_calls)

    @patch('debug_tools.utils.logger')
    def test_help_works(self, logger_mock):
        DummyTestingUtils.help('method1')
        self.assertIn(call('This is method1 docs.'), logger_mock.info.mock_calls)

        DummyTestingUtils.help('method2')
        self.assertEqual(logger_mock.info.mock_calls[-1], call(''))
