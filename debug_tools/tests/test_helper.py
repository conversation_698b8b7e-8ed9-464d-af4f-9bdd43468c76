from datetime import timedel<PERSON>

from model_bakery import baker
from parameterized import parameterized

from debug_tools.billing.helpers import BillingHelper
from lib.tools import tznow
from webapps.admin_extra.tests import DjangoTestCase
from webapps.billing.enums import ProductType
from webapps.billing.models import (
    BillingProduct,
    BillingProductOffer,
    BillingProductOfferItem,
    BillingSubscription,
)
from webapps.braintree_app.tests import gen_func
from webapps.business.models import Business

baker.generators.add('lib.interval.fields.IntervalField', gen_func)


class TestBillingHelper(DjangoTestCase):
    def setUp(self):
        self.business = baker.make(Business)

    @parameterized.expand(
        [
            ('get_business', Business),
            ('get_subscription', BillingSubscription),
            ('get_offer', BillingProductOffer),
        ]
    )
    def test_getters(self, function_name, model):
        function = getattr(BillingHelper, function_name)
        obj = baker.make(model)
        self.assertEqual(
            function(obj),
            obj,
        )
        self.assertEqual(
            function(obj.id),
            obj,
        )
        with self.assertRaises(
            model.DoesNotExist,
        ):
            function(model.objects.order_by('-id').first().id + 1)

    def test_get_sub_for_business__one_sub(self):
        subscription = baker.make(
            BillingSubscription,
            business=self.business,
        )
        self.assertEqual(
            BillingHelper.get_sub_for_business(self.business),
            subscription,
        )

    def test_get_sub_for_business__multiple_subs(self):
        current_sub = baker.make(
            BillingSubscription,
            business=self.business,
            date_start=tznow() - timedelta(days=1),
        )
        # future sub
        baker.make(
            BillingSubscription,
            business=self.business,
            date_start=tznow() + timedelta(days=1),
        )
        self.assertEqual(
            BillingHelper.get_sub_for_business(self.business),
            current_sub,
        )

    def test_get_sub_for_business__no_sub(self):
        # sub for other business
        baker.make(
            BillingSubscription,
        )
        self.assertIsNone(
            BillingHelper.get_sub_for_business(self.business),
        )

    def test_get_current_offer__one_offer(self):
        offer = baker.make(BillingProductOffer, default=True)
        self.assertEqual(
            BillingHelper.get_current_offer(),
            offer,
        )

    def test_get_current_offer__multiple_offers(self):
        baker.make(BillingProductOffer, default=True)
        last_offer = baker.make(BillingProductOffer, default=True)
        self.assertEqual(
            BillingHelper.get_current_offer(),
            last_offer,
        )

    def test_get_current_offer__no_active_offers(self):
        baker.make(BillingProductOffer, default=False, _quantity=5)
        self.assertIsNone(
            BillingHelper.get_current_offer(),
        )

    def test_get_saas_product__with_saas(self):
        offer = baker.make(BillingProductOffer)
        saas = baker.make(
            BillingProduct,
            product_type=ProductType.SAAS,
            active=True,
        )
        baker.make(
            BillingProductOfferItem,
            offer=offer,
            product=saas,
            active=True,
        )
        self.assertEqual(
            BillingHelper.get_saas_product(offer),
            saas,
        )

    def test_get_saas_product__no_saas(self):
        offer = baker.make(BillingProductOffer)
        other_product = baker.make(
            BillingProduct,
            product_type=ProductType.POSTPAID_SMS,
        )
        baker.make(
            BillingProductOfferItem,
            offer=offer,
            product=other_product,
        )
        self.assertIsNone(
            BillingHelper.get_saas_product(offer),
        )

    def test_is_new_billing(self):
        self.business.has_new_billing = False
        self.business.payment_source = Business.PaymentSource.OFFLINE
        self.assertFalse(BillingHelper.is_new_billing(self.business))
        self.business.has_new_billing = True
        self.assertFalse(BillingHelper.is_new_billing(self.business))
        self.business.payment_source = Business.PaymentSource.BRAINTREE_BILLING
        self.assertTrue(BillingHelper.is_new_billing(self.business))
