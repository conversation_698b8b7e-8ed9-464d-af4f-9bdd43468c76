from datetime import datetime, timedelta
from decimal import Decimal
from unittest.mock import call, patch

import pytz
from dateutil.relativedelta import relativedelta
from django.test import override_settings
from freezegun import freeze_time
from model_bakery import baker
from parameterized import parameterized
from pytz import UTC

from country_config.enums import Country
from debug_tools.billing.tools import BillingTools
from lib.tools import tznow
from webapps.admin_extra.tests import DjangoTestCase
from webapps.billing.enums import ProductType, SubscriptionStatus, TransactionStatus
from webapps.billing.models import (
    BillingCycle,
    BillingProduct,
    BillingProductOffer,
    BillingProductOfferItem,
    BillingSubscribedProduct,
    BillingSubscription,
)
from webapps.billing.models.payment.transaction import BillingTransaction
from webapps.billing.payment_processor import PaymentProcessor, PaymentProcessorResult
from webapps.billing.retry_charge import RetryCharge
from webapps.billing.serializers import OverdueDetailsSerializer
from webapps.billing.subscription_creator import SubscriptionCreator
from webapps.booking.models import BookingSources
from webapps.braintree_app.tests import gen_func
from webapps.braintree_app.tests.utils import payment_method_credit_card_success
from webapps.business.models import Business
from webapps.navision.baker_recipes import navision_settings_recipe
from webapps.navision.enums import InvoiceDetailsFormsEnum
from webapps.purchase.models import SubscriptionBuyer
from webapps.structure.models import Region

baker.generators.add('lib.interval.fields.IntervalField', gen_func)


@freeze_time(datetime(2021, 10, 1, tzinfo=pytz.UTC))
class TestBillingTools(DjangoTestCase):
    def setUp(self):
        self.business = baker.make(
            Business, has_new_billing=True, name='Test entity', owner__email=''
        )
        baker.make(Region, type=Region.Type.CITY, name='Beverly Hills')
        baker.make(Region, type=Region.Type.ZIP, name='90210')

    def test_set_billing(self):
        self.business.payment_source = Business.PaymentSource.UNKNOWN
        self.business.has_new_billing = False
        self.business.save()
        BillingTools.set_billing(self.business)

        self.business.refresh_from_db()
        self.assertEqual(
            self.business.payment_source,
            Business.PaymentSource.BRAINTREE_BILLING,
        )
        self.assertTrue(self.business.has_new_billing)

    @patch.object(PaymentProcessor, 'get_or_create_customer')
    @patch('braintree.PaymentMethod.create')
    def test_add_payment_method(
        self,
        create_payment_method_mock,
        get_or_create_customer_mock,
    ):
        create_payment_method_mock.return_value = payment_method_credit_card_success()
        get_or_create_customer_mock.return_value = PaymentProcessorResult(
            is_success=True, message='Success!'
        )

        with override_settings(BRAINTREE_3D_SECURE_ELIGIBLE=True):
            BillingTools.add_payment_method(self.business)

        with override_settings(BRAINTREE_3D_SECURE_ELIGIBLE=False):
            BillingTools.add_payment_method(self.business)
            BillingTools.add_payment_method(self.business.id)  # check if can pass id

        self.assertEqual(get_or_create_customer_mock.call_count, 3)
        self.assertEqual(create_payment_method_mock.call_count, 3)
        self.assertEqual(
            create_payment_method_mock.mock_calls[0],
            call(
                {
                    'payment_method_nonce': 'fake-three-d-secure-visa-full-authentication-nonce',
                    'customer_id': f'dev-us-{self.business.id}',
                    'billing_address': {
                        'street_address': 'Sesame street 123',
                        'extended_address': '1st Floor',
                        'postal_code': '90210',
                        'locality': 'Beverly Hills',
                        'country_name': 'United States',
                    },
                    'options': {'make_default': True, 'fail_on_duplicate_payment_method': False},
                }
            ),
        )
        self.assertEqual(
            create_payment_method_mock.mock_calls[1].args[0]['payment_method_nonce'],
            'fake-valid-visa-nonce',
        )
        self.assertEqual(
            create_payment_method_mock.mock_calls[2].args[0]['payment_method_nonce'],
            'fake-valid-visa-nonce',
        )

    def test_change_registration_source(self):
        web = baker.make(BookingSources, name='Web')
        frontdesk = baker.make(BookingSources, name='Biz 3.0 Web')

        BillingTools.change_registration_source(self.business, 'Web')
        self.assertEqual(self.business.registration_source, web)
        BillingTools.change_registration_source(self.business.id, 'Biz 3.0 Web')
        self.business.refresh_from_db()
        self.assertEqual(self.business.registration_source, frontdesk)

    @staticmethod
    def make_subscription_mock(
        business_id,
        *args,
        **kwargs,
    ):
        subscription = baker.make(
            BillingSubscription,
            business_id=business_id,
        )
        return dict(
            subscription_id=subscription.id,
            message='Ok!',
        )

    @patch.object(RetryCharge, 'retry_for_subscription')
    def test_retry_charges(self, auto_retry_charges_mock):
        baker.make(
            BillingSubscription,
            business=self.business,
            date_start=tznow() - timedelta(days=5),
            status=SubscriptionStatus.BLOCKED,
            balance=10,
        )
        BillingTools.retry_charge(self.business)
        self.assertEqual(auto_retry_charges_mock.call_count, 1)

    @patch.object(BillingTools, 'add_or_get_buyer')
    @patch.object(BillingTools, 'add_payment_method')
    @patch.object(SubscriptionCreator, 'new_purchase')
    def test_create_subscription__success(
        self,
        new_purchase_mock,
        add_payment_method_mock,
        add_buyer_mock,
    ):
        new_purchase_mock.side_effect = self.make_subscription_mock
        offer = baker.make(BillingProductOffer, default=True)
        saas = baker.make(
            BillingProduct,
            product_type=ProductType.SAAS,
            active=True,
            payment_period=relativedelta(months=1),
        )
        baker.make(
            BillingProductOfferItem,
            offer=offer,
            product=saas,
            active=True,
        )
        subscription = BillingTools.create_subscription(
            self.business,
        )
        subscription2 = BillingTools.create_subscription(
            self.business.id,
            subscription_start='2021-10-31',
            add_payment_method=False,
        )  # check if can pass id
        self.assertEqual(new_purchase_mock.call_count, 2)
        self.assertEqual(add_payment_method_mock.call_count, 1)
        self.assertEqual(add_buyer_mock.call_count, 2)
        self.assertEqual(
            new_purchase_mock.mock_calls,
            [
                call(
                    business_id=self.business.id,
                    offer_id=offer.id,
                    product_ids=[saas.id],
                    subscription_start=None,
                ),
                call(
                    business_id=self.business.id,
                    offer_id=offer.id,
                    product_ids=[saas.id],
                    subscription_start=datetime(2021, 10, 31, tzinfo=pytz.UTC),
                ),
            ],
        )
        self.assertIsInstance(subscription, BillingSubscription)
        self.assertIsInstance(subscription2, BillingSubscription)

    @patch.object(BillingTools, 'add_payment_method')
    @patch.object(SubscriptionCreator, 'new_purchase')
    def test_create_subscription__failed_purchase(
        self,
        new_purchase_mock,
        add_payment_method_mock,
    ):
        new_purchase_mock.return_value = dict(
            message='Error occured',
        )
        offer = baker.make(BillingProductOffer, default=True)
        saas = baker.make(
            BillingProduct,
            product_type=ProductType.SAAS,
            active=True,
            payment_period=relativedelta(months=1),
        )
        baker.make(
            BillingProductOfferItem,
            offer=offer,
            product=saas,
            active=True,
        )
        results = BillingTools.create_subscription(self.business)
        self.assertEqual(
            results,
            dict(
                message='Error occured',
            ),
        )

    @patch.object(BillingTools, 'add_payment_method')
    @patch.object(SubscriptionCreator, 'new_purchase')
    def test_create_subscription__already_exists(
        self,
        new_purchase_mock,
        add_payment_method,
    ):
        new_purchase_mock.side_effect = self.make_subscription_mock
        offer = baker.make(BillingProductOffer, default=True)
        saas = baker.make(
            BillingProduct,
            product_type=ProductType.SAAS,
            active=True,
            payment_period=relativedelta(months=1),
        )
        baker.make(
            BillingProductOfferItem,
            offer=offer,
            product=saas,
            active=True,
        )

        business1 = baker.make(Business, has_new_billing=True, name='Test entity')
        expired_sub = baker.make(
            BillingSubscription,
            business=business1,
            date_start=tznow() - timedelta(days=5),
            date_expiry=tznow() - timedelta(days=2),
        )
        self.assertNotEqual(
            BillingTools.create_subscription(business1),
            expired_sub,
        )

        business2 = baker.make(Business, has_new_billing=True, name='Test entity')
        current_sub = baker.make(
            BillingSubscription,
            business=business2,
            date_start=tznow() - timedelta(days=5),
        )
        self.assertEqual(
            BillingTools.create_subscription(business2),
            current_sub,
        )

        business3 = baker.make(Business, has_new_billing=True, name='Test entity')
        pending_sub = baker.make(
            BillingSubscription,
            business=business3,
            date_start=tznow() + timedelta(days=1),
        )
        self.assertEqual(
            BillingTools.create_subscription(
                business3,
                subscription_start=tznow() + timedelta(days=4),
            ),
            pending_sub,
        )

        self.assertEqual(new_purchase_mock.call_count, 1)
        self.assertEqual(
            new_purchase_mock.mock_calls[0].kwargs.get('business_id'),
            business1.id,
        )

    @patch.object(SubscriptionCreator, 'new_purchase')
    def test_create_subscription__no_offer(
        self,
        new_purchase_mock,
    ):
        results = BillingTools.create_subscription(self.business)
        self.assertEqual(new_purchase_mock.call_count, 0)
        self.assertEqual(
            results,
            dict(
                error='No active offer!',
            ),
        )

    @patch.object(SubscriptionCreator, 'new_purchase')
    def test_create_subscription__no_saas_product(
        self,
        new_purchase_mock,
    ):
        offer = baker.make(BillingProductOffer, default=True)
        other_product = baker.make(
            BillingProduct,
            product_type=ProductType.POSTPAID_SMS,
            active=True,
            payment_period=relativedelta(months=1),
        )
        baker.make(
            BillingProductOfferItem,
            offer=offer,
            product=other_product,
            active=True,
        )
        results = BillingTools.create_subscription(self.business)
        self.assertEqual(new_purchase_mock.call_count, 0)
        self.assertRegex(
            results.get('error'),
            'No active SaaS product for offer.*',
        )

    @patch.object(BillingTools, 'create_subscription')
    @freeze_time(datetime(2021, 7, 7, tzinfo=UTC))
    def test_set_paid__new_subscription(self, create_subscription_mock):
        create_subscription_mock.return_value = baker.make(
            BillingSubscription,
            business=self.business,
            status=SubscriptionStatus.ACTIVE,
        )
        self.business.status = Business.Status.BLOCKED_OVERDUE
        self.business.save()
        BillingTools.set_paid(self.business, next_billing_date_days=15)
        self.business.refresh_from_db()
        self.assertEqual(BillingSubscription.objects.count(), 1)
        subscription = BillingSubscription.objects.first()
        self.assertEqual(subscription.status, SubscriptionStatus.ACTIVE)
        self.assertEqual(subscription.balance, Decimal(0))
        self.assertEqual(
            subscription.next_billing_date,
            datetime(2021, 7, 22, tzinfo=UTC),
            # 23 = 7 + 15 days
        )
        self.assertEqual(self.business.status, Business.Status.PAID)

    @patch.object(BillingTools, 'add_payment_method')
    @freeze_time(datetime(2021, 7, 7, tzinfo=UTC))
    def test_set_paid__existing_subscription(self, add_payment_method_mock):
        subscription = baker.make(
            BillingSubscription,
            business=self.business,
            status=SubscriptionStatus.BLOCKED,
            balance=10,
        )
        self.business.status = Business.Status.BLOCKED_OVERDUE
        self.business.save()
        baker.make(
            BillingCycle,
            date_end=tznow() + timedelta(days=15),
            subscription=subscription,
            business=self.business,
        )
        BillingTools.set_paid(self.business, next_billing_date_days=15)
        subscription.refresh_from_db()
        self.business.refresh_from_db()
        self.assertEqual(subscription.status, SubscriptionStatus.ACTIVE)
        self.assertEqual(subscription.balance, Decimal(0))
        self.assertEqual(
            subscription.next_billing_date,
            datetime(2021, 7, 22, tzinfo=UTC),
            # 23 = 7 + 15 days
        )
        self.assertEqual(self.business.status, Business.Status.PAID)
        self.assertEqual(add_payment_method_mock.call_count, 0)

    def test_set_poa(self):
        subscription = baker.make(
            BillingSubscription,
            business=self.business,
            status=SubscriptionStatus.ACTIVE,
            balance=0,
        )
        self.business.status = Business.Status.PAID
        self.business.save()
        baker.make(
            BillingCycle,
            date_end=tznow() + timedelta(days=15),
            subscription=subscription,
            business=self.business,
        )
        BillingTools.set_poa(self.business)
        subscription.refresh_from_db()
        self.business.refresh_from_db()
        self.assertEqual(subscription.status, SubscriptionStatus.BLOCKED)
        self.assertGreater(subscription.balance, 0)
        self.assertEqual(self.business.status, Business.Status.OVERDUE)
        self.assertTrue(
            BillingTransaction.objects.filter(
                business_id=self.business.id,
                status=TransactionStatus.FAILED,
                amount=subscription.balance,
            ).exists()
        )

    @freeze_time(datetime(2021, 7, 7, tzinfo=UTC))
    def test_set_pob(self):
        subscription = baker.make(
            BillingSubscription,
            business=self.business,
            status=SubscriptionStatus.ACTIVE,
            balance=0,
        )
        self.business.status = Business.Status.PAID
        self.business.save()
        baker.make(
            BillingCycle,
            date_end=tznow(),
            subscription=subscription,
            business=self.business,
        )
        BillingTools.set_pob(self.business, account_lock_in_days=7)

        subscription.refresh_from_db()
        self.business.refresh_from_db()
        self.assertEqual(subscription.status, SubscriptionStatus.BLOCKED)
        self.assertGreater(subscription.balance, 0)
        self.assertEqual(self.business.status, Business.Status.BLOCKED_OVERDUE)
        self.assertEqual(OverdueDetailsSerializer.get_account_lock_in_days(subscription), 7)

        self.assertTrue(
            BillingTransaction.objects.filter(
                business_id=self.business.id,
                status=TransactionStatus.FAILED,
                amount=subscription.balance,
            ).exists()
        )

    @parameterized.expand(
        [
            (InvoiceDetailsFormsEnum.PATH_1, Country.ES, 'ESA1234567U', True, 'ESA1234567U'),
            (InvoiceDetailsFormsEnum.PATH_1, Country.ES, None, True, 'ESA1234567U'),
            (InvoiceDetailsFormsEnum.PATH_2, Country.GB, None, False, None),
            (InvoiceDetailsFormsEnum.PATH_2, Country.GB, 'GB123456789', True, 'GB123456789'),
            (InvoiceDetailsFormsEnum.PATH_2, Country.GB, None, True, 'GB123456789'),
            (InvoiceDetailsFormsEnum.PATH_2, Country.PL, None, False, None),
            (InvoiceDetailsFormsEnum.PATH_2, Country.PL, '**********', True, '**********'),
            (InvoiceDetailsFormsEnum.PATH_2, Country.PL, None, True, '**********'),
            (InvoiceDetailsFormsEnum.PATH_3, Country.US, None, False, None),
            (None, Country.MX, '1234567', False, '1234567'),
            (None, Country.MX, None, True, None),
        ]
    )
    @patch('webapps.navision.tasks.subscription_buyer.update_entity_type_task.delay')
    def test_add_buyer_per_invoicing_path(
        self, inv_path, country, tax_id, vat_registered, expected_tax_id, update_entity_type_mock
    ):
        with override_settings(SUPPORTED_INVOICE_FORM=inv_path, API_COUNTRY=country):
            buyer = BillingTools.add_or_get_buyer(
                self.business, tax_id=tax_id, vat_registered=vat_registered
            )

            self.business.refresh_from_db()
            self.assertEqual(SubscriptionBuyer.objects.count(), 1)
            self.assertEqual(self.business.buyer_id, buyer.id)
            self.assertEqual(self.business.buyer.tax_id, expected_tax_id)
            self.assertIsNotNone(buyer.entity_name)
            self.assertIsNotNone(buyer.invoice_email)
            self.assertIsNotNone(buyer.invoice_address)

    @parameterized.expand(
        [
            (InvoiceDetailsFormsEnum.PATH_1, Country.ES, 'ESA1234567U', True, 'ESA1234567U'),
            (InvoiceDetailsFormsEnum.PATH_1, Country.ES, None, True, 'ESA1234567U'),
            (InvoiceDetailsFormsEnum.PATH_2, Country.GB, None, False, None),
            (InvoiceDetailsFormsEnum.PATH_2, Country.GB, 'GB123456789', True, 'GB123456789'),
            (InvoiceDetailsFormsEnum.PATH_2, Country.GB, None, True, 'GB123456789'),
            (InvoiceDetailsFormsEnum.PATH_2, Country.PL, None, False, None),
            (InvoiceDetailsFormsEnum.PATH_2, Country.PL, '**********', True, '**********'),
            (InvoiceDetailsFormsEnum.PATH_2, Country.PL, None, True, '**********'),
            (InvoiceDetailsFormsEnum.PATH_3, Country.US, None, False, None),
            (None, Country.MX, '1234567', False, '1234567'),
            (None, Country.MX, None, True, None),
        ]
    )
    @patch('webapps.navision.tasks.subscription_buyer.update_entity_type_task.delay')
    def test_add_buyer_called_twice_all_paths(
        self, inv_path, country, tax_id, vat_registered, expected_tax_id, update_entity_type_mock
    ):
        with override_settings(SUPPORTED_INVOICE_FORM=inv_path, API_COUNTRY=country):
            navision_settings_recipe.make()
            BillingTools.add_or_get_buyer(
                self.business, tax_id=tax_id, vat_registered=vat_registered
            )
            self.business.refresh_from_db()
            buyer = BillingTools.add_or_get_buyer(self.business)

            self.business.refresh_from_db()
            self.assertEqual(SubscriptionBuyer.objects.count(), 1)

            self.assertEqual(self.business.buyer_id, buyer.id)
            self.assertEqual(self.business.buyer.tax_id, expected_tax_id)
            self.assertIsNotNone(buyer.entity_name)
            self.assertIsNotNone(buyer.invoice_email)
            self.assertIsNotNone(buyer.invoice_address)

    def test_shift_cycle(self):
        self.business.paid_till = tznow() + relativedelta(months=2)
        self.business.save()
        offer = baker.make(BillingProductOffer, default=True)
        staffer_saas = baker.make(
            BillingProduct,
            product_type=ProductType.STAFFER_SAAS,
        )
        postpaid_sms = baker.make(
            BillingProduct,
            product_type=ProductType.POSTPAID_SMS,
        )
        saas = baker.make(
            BillingProduct,
            product_type=ProductType.SAAS,
            active=True,
            payment_period=relativedelta(months=1),
            staff_add_on_id=staffer_saas.id,
            sms_add_on_id=postpaid_sms.id,
        )
        baker.make(
            BillingProductOfferItem,
            offer=offer,
            product=saas,
            active=True,
        )
        baker.make(
            BillingProductOfferItem,
            offer=offer,
            product=staffer_saas,
            active=True,
        )
        baker.make(
            BillingProductOfferItem,
            offer=offer,
            product=postpaid_sms,
            active=True,
        )

        subscription = baker.make(
            BillingSubscription,
            business_id=self.business.id,
            offer_id=offer.id,
            date_start=tznow(),
            next_billing_date=tznow() + relativedelta(months=1),
            paid_through_date=tznow() + relativedelta(months=1),
            date_expiry=tznow() + relativedelta(months=2),
            balance=Decimal(0),
            status=SubscriptionStatus.ACTIVE,
        )
        billing_cycle = baker.make(
            BillingCycle,
            subscription_id=subscription.id,
            business_id=self.business.id,
            date_start=subscription.date_start,
            date_end=subscription.date_start + relativedelta(months=1),
        )
        subscribed_saas = baker.make(
            BillingSubscribedProduct,
            subscription_id=subscription.id,
            business_id=self.business.id,
            product_id=saas.id,
            product_type=ProductType.SAAS,
            sms_add_on_id=postpaid_sms.id,
            date_start=subscription.date_start,
            date_end=subscription.date_start + relativedelta(months=1),
        )
        subscribed_saas2 = baker.make(
            BillingSubscribedProduct,
            subscription_id=subscription.id,
            business_id=self.business.id,
            product_id=saas.id,
            product_type=ProductType.SAAS,
            sms_add_on_id=postpaid_sms.id,
            date_start=subscribed_saas.date_end + relativedelta(days=1),
            date_end=subscription.date_expiry,
        )
        subscribed_sms = baker.make(
            BillingSubscribedProduct,
            subscription_id=subscription.id,
            business_id=self.business.id,
            product_id=postpaid_sms.id,
            product_type=ProductType.POSTPAID_SMS,
            date_start=subscription.date_start,
        )
        subscribed_staffer = baker.make(
            BillingSubscribedProduct,
            subscription_id=subscription.id,
            business_id=self.business.id,
            product_id=saas.id,
            product_type=ProductType.STAFFER_SAAS,
            quantity=1,
            sms_add_on_id=postpaid_sms.id,
            date_start=subscription.date_start,
        )
        transaction = baker.make(
            BillingTransaction,
            business_id=self.business.id,
            billing_cycle_id=billing_cycle.id,
            subscription_id=subscription.id,
        )

        subscription = BillingTools.shift_cycle(
            business=self.business, switch_cycle_task=False
        ).first()

        subscribed_saas.refresh_from_db()
        subscribed_saas2.refresh_from_db()
        subscribed_sms.refresh_from_db()
        subscribed_staffer.refresh_from_db()
        transaction.refresh_from_db()
        self.business.refresh_from_db()

        expected_date = tznow() - relativedelta(months=1)

        self.assertEqual(self.business.paid_till, expected_date + relativedelta(months=2))

        self.assertEqual(subscription.date_start, expected_date)
        self.assertEqual(subscription.paid_through_date, expected_date + relativedelta(months=1))
        self.assertEqual(subscription.next_billing_date, expected_date + relativedelta(months=1))
        self.assertEqual(subscription.date_expiry, expected_date + relativedelta(months=2))

        self.assertEqual(subscribed_sms.date_start, expected_date)
        self.assertEqual(subscribed_saas.date_start, expected_date)
        self.assertEqual(
            subscribed_saas2.date_start, subscribed_saas.date_end + relativedelta(days=1)
        )
        self.assertEqual(subscribed_saas2.date_end, expected_date + relativedelta(months=2))
        self.assertEqual(subscribed_staffer.date_start, expected_date)
        self.assertEqual(transaction.created, expected_date)
