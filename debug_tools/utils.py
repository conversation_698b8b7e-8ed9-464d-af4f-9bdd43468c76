import logging

logger = logging.getLogger('booksy.tools')


class BaseDebugTools:
    @classmethod
    @property
    def supported(cls):
        return {}

    @classmethod
    @property
    def methods(cls):
        for method in cls.supported:
            logger.info(
                '{klass}.{method}'.format(
                    klass=cls.__name__,
                    method=method.__name__,
                )
            )

    @classmethod
    def help(cls, method):
        for line in (getattr(cls, method).__doc__ or '').split('\n'):
            logger.info(line)


def log_error(results=None):
    logger.info('Something went wrong!')
    logger.info('Results:')
    if not results:
        return
    logger.info(results)


def log_results(message='Done!', results=None):
    logger.info(message)
    if not results:
        return
    logger.info('Results:')
    logger.info(results)
