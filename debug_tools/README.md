# [WARNING] Those tools are not intended to be used on live environment.

# Debug tools

## purpose

Debug tools were developed to support testers to ommit time consuming setups of
merchant account (Merchant Billing project).
Thanks to those tools testers should have possibility to set up merchant account 
to desired state to run testing scenarios faster.

Further development of those tools is more than desired :)

## usage

First, we need to select debug tool we want to use.
List of available tools (will be extended in future):
- BillingTools

Then we can list methods that are supported in specific tool:
```python
BillingTools.methods
```
> {'BillingTools.set_billing'}

If we want more help about method and usage, we type:
```python
BillingTools.help('set_billing')
```
> Set business to Merchant billing eligible.
>    returns: business

If there are some parameters to pass (e.g. business_id) it should be mentioned in help.
Some of the parameters are optional, if not passed, default ones will be used.

Finally, to run tool method, simply pass

```python
BillingTools.set_billing(12345) # 12345 is business_id (one and only one of parameters
```

Details what method is currently doing will be printed on console screen.
 