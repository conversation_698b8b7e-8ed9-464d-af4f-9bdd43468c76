[uwsgi]
http = :8888
master = True
processes = 2
#tornado = 50
#greenlet = True
socket = /home/<USER>/code/sock.sock
chdir = /home/<USER>/code
; Defines a logger to user for uWSGI server logs
logger = uwsgilogger stdio
; Handle only uWSGI server logs (not starting with curly brace)
log-route = uwsgilogger ^(?!{).*
log-prefix = [booksy_uwsgi_logs]
disable-logging = true
log-date = 1
log-4xx = false
log-5xx = true
log-slow = 2000
buffer-size = 30000
py-call-osafterfork = true
harakiri-graceful-signal = 31
harakiri-graceful-timeout = 1
harakiri = 10
harakiri-verbose = true
threads=2
reload-mercy = 5
worker-reload-mercy = 8
vacuum = True
single-interpreter = True
need-app = True
listen = 120
close-on-exec = True
#need-plugin = python3
wsgi-file = service/run.py
stats = /home/<USER>/code/stats.socket
py-tracebacker = /home/<USER>/code/uwsgi-tracebacker-socket
ignore-sigpipe = True
ignore-write-errors = True
enable-threads = True
lazy-apps = False
py-call-uwsgi-fork-hooks = True
reload-on-rss = 600
thunder-lock = True
backtrace-depth = 1000
strict=true