{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {{ title }}
{% endblock email_title %}

{% block email_content %}
    <div style="text-align: left; padding: 0 20px 20px 20px;">
        <p style="color: rgb(42, 44, 50); font-family: 'Proxima Nova'; font-size: 14px; font-weight: 400; line-height: 20px; text-align: left;">
            {{ _("We noticed multiple invalid OTP code attempts on your account, and as a precaution, your account has been temporarily locked. Please ensure you're entering the correct code.") }}
            <br>
            {{ _("If you believe this was an error or need assistance, feel free to contact our support team.") }}
        </p>
        <p style="font-family: 'Proxima Nova'; font-size: 14px; font-weight: 400; line-height: 20px; text-align: left;">
            {{ _("If you did not attempt to change any details in your account, we advise you to reset your password and contact customer service.") }}
        </p>
    </div>

{% endblock email_content %}
