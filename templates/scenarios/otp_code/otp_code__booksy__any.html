{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {{ title }}
{% endblock email_title %}

{% block email_content %}
    <div style="text-align: left; padding: 0 20px 20px 20px;">
        <p style="width: 420px; height: 32px; top: 133px; left: 40px; gap: 0px; opacity: 0px; font-family: 'Proxima Nova'; font-size: 24px; font-weight: 700; line-height: 32px; text-align: left;">
            {{ _("Your {code_size}-{code_type} code").format(code_size=code_size, code_type=code_type) }}
        </p>
        <p style="color: rgb(42, 44, 50); font-family: 'Proxima Nova'; font-size: 14px; font-weight: 400; line-height: 20px; text-align: left;">
            {{ _("Booksy requires you to verify your identity with a verification code when updating your personal information to keep your account safe. Here is your verification code:") }}
        </p>
        <strong style="color: #218CAC; font-family: 'Proxima Nova'; font-size: 32px; font-weight: 700; line-height: 40px; text-align: left;">
            {{ otp_code }}
        </strong>
        <p style="font-family: 'Proxima Nova'; font-size: 14px; font-weight: 400; line-height: 20px; text-align: left;">
            {{ _("You should enter this code where requested on the Booksy app.") }}
        </p>
        <p style="font-family: 'Proxima Nova'; font-size: 14px; font-weight: 400; line-height: 20px; text-align: left;">
            <strong>{{ _("This code is valid for the next {valid_time_min} minutes, do not share it with anyone.").format(valid_time_min=valid_time_min) }}</strong>
        </p>
        <p style="font-family: 'Proxima Nova'; font-size: 14px; font-weight: 400; line-height: 20px; text-align: left;">
            {{ _("If you did not attempt to change any details in your account, we advise you to reset your password and contact customer service.") }}
        </p>
    </div>

{% endblock email_content %}
