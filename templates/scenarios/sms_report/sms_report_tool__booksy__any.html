{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {{ _('{blast_template_name} delivery report').format(blast_template_name=self.template_name) }}
{% endblock email_title %}

{% block email_content %}
    <div style="background-color:#f9f9f9; padding: 20px;">
        <div style="font: 300 18px 'Proxima Nova', Arial; color: #999; padding-bottom: 10px;">
            {{ _('The messages with various body messages')}}<br/>
            {{ _('have been sent to {recipients_count} recipients.').format(recipients_count=recipients_count) }}<br/>
            {{ _('{sent_count} messages sent successfully').format(sent_count=sent_count) }}<br/>
            {{ _('{no_sent_count} messages has not been sent due to errors or client opt-out').format(no_sent_count=no_sent_count) }}<br/>
        </div>
        <div style="font: 400 12px 'Proxima Nova', <PERSON><PERSON>; color: #666; padding-bottom: 10px;">
            {{ _('Please see detailed report attached.') }}
        </div>
    </div>
{% endblock email_content %}
