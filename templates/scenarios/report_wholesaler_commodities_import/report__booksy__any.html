{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {{ _('Wholesaler commodities import report') }}
{% endblock email_title %}

{% block email_content %}
    <div style="background-color:#f9f9f9; padding: 20px;">
        <div style="font: 300 18px 'Proxima Nova', Arial; color: #999; padding-bottom: 10px;">
            {{ _('This is generated report from import commodities. Total number of commodities {total_count}.').format(total_count=total_count) }}<br/>
            {{ _('{imported_count} commodities were imported successfully').format(imported_count=imported_count) }}<br/>
            {{ _('{with_warnings} commodities were imported with warnings').format(with_warnings=with_warnings) }}<br/>
            {{ _('{omitted_count} commodities has not been imported due to errors in provided file.').format(omitted_count=omitted_count) }}<br/>
        </div>
        <div style="font: 400 12px 'Proxima Nova', <PERSON><PERSON>; color: #666; padding-bottom: 10px;">
            {{ _('Please see detailed report attached.') }}
        </div>
    </div>
{% endblock email_content %}
