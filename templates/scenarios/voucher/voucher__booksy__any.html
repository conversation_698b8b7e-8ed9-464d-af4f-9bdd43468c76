{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {{ title }}
{% endblock email_title %}

{% block email_content %}
    <div style="text-align: center; padding: 0 20px 20px 20px;">
        <p style="color: rgb(42, 44, 50);
            font-family: Muli-ExtraBold, 'Helvetica Neue', Helvetica, Arial, sans-serif;
            font-size: 37px;
            font-weight: 800;
            height: 46px;
            letter-spacing: -1.88px;
            text-align: center;">
            <strong>
                {% if voucher.buyer and voucher.buyer.first_name %}
                    {# @formatter:off #}
                    {{ _('Thank you') }},&nbsp;{{ voucher.buyer.first_name }}!
                    {# @formatter:on #}
                {% else %}
                    {{ _('Thank you') }}!
                {% endif %}
            </strong>
        </p>
        <p style="color: rgb(42, 44, 50);
            font-family: 'Muli-Regular', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            font-size: 22px;
            font-weight: normal;
            height: 36px;
            letter-spacing: -0.48px;
            line-height: 36px;
            text-align: center;">
            {{ business_name }}&nbsp;{{ _("received your {} payment!").format(voucher_type_label) }}
        </p>
        <br/>
        <div style="text-align: center;
                height: 180px;
                padding-top: 40px;
                background-image: url('{{ S3_STATIC_HOST_URL }}scenarios/voucher-full.png');
                background-position: top center;
                background-repeat: no-repeat;
                background-size: 335px 240px;
                ">
            <div style="color: white;">
                <p style="font-family: 'Muli-Regular', 'Helvetica Neue', Helvetica, Arial, sans-serif;
                    font-size: 50px;
                    height: 50px;
                    margin: 0 0 14px;">
                    {{ voucher_value_no_minor }}&nbsp;
                </p>
                <p style="font-family: 'ProximaNova-Semibold', Monaco, 'Courier New', Courier, monospace;
                    margin: 0 0 11px;
                    font-size: 10px;
                    font-weight: bold;
                    letter-spacing: 10px;
                    text-transform: uppercase;
                    ">
                    {% if voucher.voucher_template.type == VoucherType.EGIFT_CARD %}
                        {{ _('Giftcard') }}
                    {% elif voucher.voucher_template.type == VoucherType.MEMBERSHIP %}
                        {{ _('Membership') }}
                    {% elif voucher.voucher_template.type == VoucherType.PACKAGE %}
                        {{ _('Package') }}
                    {% endif %}
                </p>
                <p style="font-family: 'ProximaNova-Semibold', Monaco, 'Courier New', Courier, monospace;
                    font-size: 11px;
                    font-weight: 600;
                    height: 36px;
                    letter-spacing: 4.81px;
                    line-height: 36px;
                    text-align: center;
                    margin: 0;">
                    {{ voucher.code }}
                </p>
            </div>
        </div>
    </div>

    <div style="text-align: center;
            padding: 20px;
            border-bottom: 1px solid rgba(100, 100, 100, 0.1);
            color: rgb(42, 44, 50);
            font-family: 'Muli-Medium', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            font-size: 15px;
            font-weight: 500;
            line-height: 26px;">
        <p>
            {{ activated_message }}<br/>
            {{ redeem_message }}
        </p>
        <p><strong>{{ _('Enjoy your service!') }}</strong></p>
    </div>

    <div style="padding: 20px 48px; font-family: 'Muli-Medium', 'Helvetica Neue', Helvetica, Arial, sans-serif;">
        <p style="color: rgb(146, 146, 146);
            font-size: 13px;
            font-weight: 500;
            text-align: center;
            text-transform: uppercase;
            height: 34px;">
            {{ details_below_note }}
        </p>
        <table style="width: 100%;
            color: rgb(42, 44, 50);
            font-size: 15px;
            font-weight: 500;">
            <tr>
                <td style="padding-bottom: 16px;">
                    {{ _('{} balance').format(voucher_type_label) }}
                </td>
                <td style="padding-bottom: 16px; text-align: right; font-weight: bold;">
                    <strong>{{ current_balance }} / {{ voucher_value }}</strong>
                </td>
            </tr>
            <tr>
                <td style="padding-bottom: 16px;">
                    {{ _('{} number').format(voucher_type_label) }}
                </td>
                <td style="padding-bottom: 16px; text-align: right; font-weight: bold;">
                    <strong>{{ voucher.code }}</strong>
                </td>
            </tr>
            <tr>
                <td style="padding-bottom: 16px;">
                    {{ _('Valid through') }}
                </td>
                <td style="padding-bottom: 16px; text-align: right; font-weight: bold;">
                    {% if voucher.voucher_template.valid_till == UNLIMITED %}
                        <strong>{{ _('Open-end') }}</strong>
                    {% else %}
                        <strong>{{ voucher_valid_till }}</strong>
                    {% endif %}
                </td>
            </tr>
        </table>

        <p style="color: rgb(146, 146, 146);
            font-family: 'Muli-Regular', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            font-size: 13px;
            font-weight: normal;
            text-align: center;
            margin-top: 32px;">
            {{ purchase_note }}
        </p>
    </div>
{% endblock email_content %}
