{% extends "email_base_plain__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {{ _('Booksy’s Adyen Integration Report') }}
{% endblock email_title %}

{% block email_content %}
    <div style="background-color:#f9f9f9; padding: 20px;">
        <div style="font: 300 18px 'Proxima Nova', Arial; color: #999; padding-bottom: 10px;">
            {{ _('Dear') }} {{ recipient_name }},<br/>
            {{ _('Below is the daily Adyen integration report for day ') }}{{ report_date }}{{ _(', country code: ') }}{{ api_country }}.<br/><br/>

            <div style="text-align: center;">
                <table  border="1" cellpadding="0" cellspacing="0" style="max-width: 590px; width: 100%; padding: 15px;">
                    <tr><td><b>{{ _('Notification type') }}</b></td><td><b>{{ _('Count') }}</b></td></tr>
                {% for (notification_type, notifications_count) in adyen_notifications %}
                  <tr>
                      <td>{{ notification_type }}</td>
                      <td>{{ notifications_count }}</td>
                  </tr>
                {% endfor %}
                </table>
            </div>

            <br/>{{ _('Additionally we have registered following number of payouts within our database: ') }}{{ payouts_count }}<br/>
        </div>
    </div>
{% endblock email_content %}
