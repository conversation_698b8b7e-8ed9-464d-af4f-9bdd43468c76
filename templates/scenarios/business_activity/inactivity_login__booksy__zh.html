{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}像顾客一样经常查看Booksy{% endblock email_title %}

{% block email_content %}

    {% call macros.paragraph(bold=True) %}
        您好,
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
       您尚未登录账户 {{ last_login_days }}天了。
        这会导致与顾客的预约冲突，因为没人在检查日历或与顾客交流。
    {% endcall %}

    {% call macros.paragraph() %}
        我们建议您每天检查日历。
    {% endcall %}

    {% call macros.paragraph() %}
        {% call macros.link(url=business_webbiz_url) %}
            登录电邮
        {% endcall %}
    {% endcall %}

{% endblock email_content %}
