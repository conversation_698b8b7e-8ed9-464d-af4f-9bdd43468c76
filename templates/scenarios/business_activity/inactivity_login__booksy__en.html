{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}Check Booksy as frequently as your clients do{% endblock email_title %}

{% block email_content %}

    {% call macros.paragraph(bold=True) %}
        Hello,
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        You haven’t logged in to your account for: {{ last_login_days }} days.
        This can lead to scheduling conflicts with your clients, as nobody is checking the calendar or communicating with clients.
    {% endcall %}

    {% call macros.paragraph() %}
        We suggest checking your calendar every day.
    {% endcall %}

    {% call macros.paragraph() %}
        {% call macros.link(url=business_webbiz_url) %}
            Login
        {% endcall %}
    {% endcall %}

{% endblock email_content %}
