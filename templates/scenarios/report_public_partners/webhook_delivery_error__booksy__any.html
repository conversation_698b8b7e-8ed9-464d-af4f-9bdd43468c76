{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {{ _('{title}').format(title=title)}}
{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        {{ _('Hello, ') }}
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        {{ _('The maximum number of retries for delivering webhook has been exceeded.') }}
    {% endcall %}

    {% call macros.li_table() %}
        {% call macros.li() %}
            {{ _('Booksy environment: {booksy_environment}').format(booksy_environment=booksy_environment) }}
        {% endcall %}

        {% call macros.li() %}
            {{ _('Application: {application_name}').format(application_name=application_name) }}
        {% endcall %}

        {% call macros.li() %}
            {{ _('Webhook event: {webhook_event}').format(webhook_event=webhook_event) }}
        {% endcall %}

        {% call macros.li() %}
            {{ _('Webhook URL: {webhook_url}').format(webhook_url=webhook_url) }}
        {% endcall %}

        {% call macros.li() %}
            {{ _('Webhook log UUID: {webhook_log_uuid}').format(webhook_log_uuid=webhook_log_uuid) }}
        {% endcall %}

        {% if business_uuid %}
            {% call macros.li() %}
                {{ _('Business UUID: {business_uuid}').format(business_uuid=business_uuid) }}
            {% endcall %}
        {% endif %}

        {% if resource_uuid %}
            {% call macros.li() %}
                {{ _('Resource UUID: {resource_uuid}').format(resource_uuid=resource_uuid) }}
            {% endcall %}
        {% endif %}
    {% endcall %}
{% endblock email_content %}
