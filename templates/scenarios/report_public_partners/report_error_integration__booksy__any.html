{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {{ _('{title}').format(title=title)}}
{% endblock email_title %}

{% block email_content %}
    <div style="background-color:#f9f9f9; padding: 20px;">
        <div style="font: 300 18px 'Proxima Nova', Arial; color: #999; padding-bottom: 10px;">
            {{ _('Integration with {partner_name} {country_code}-{deployment_level} failed. {message}.').format(
                partner_name=partner_name,
                country_code=country_code,
                deployment_level=deployment_level,
                message=message,
                ) }}<br/>
        </div>
    </div>
{% endblock email_content %}
