{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}Booksy hat ein Sicherheitsproblem entdeckt{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        Booksy hat ein mögliches Sicherheitsproblem in Deinem Account entdeckt{{ business_name }}.
    {% endcall %}

    {% call macros.paragraph() %}
        {% if staffer_name is not none %}   
            Nutzer <b>{{ staffer_name }}</b> hat zu viele Kontaktdaten deiner Kunden geöffnet: <b>{{ staffer_access_count }}</b> pro Stunde.
        {% else %}
            Ein Mitarbeiter hat im Rezeptionsmodus zu viele Kontaktdaten deiner Kunden geöffnet: <b>{{ staffer_access_count }}</b> pro Stunde.
        {% endif %}
    {% endcall %}

    {% call macros.paragraph() %}
        Dein aktuelles Sicherheitslimit bei Booksy beträgt {{ business_locked_limit_hourly }} Kontakte pro Stunde.
    {% endcall %}

    {% call macros.paragraph() %}
        Du kannst die Höchstzahl offener Kontakte hier erhöhen:
        Booksy-App &rarr; Anbieter &rarr; Optionen &rarr; Sicherheitswarnung.
    {% endcall %}

    {% call macros.paragraph() %}
        Hast du Fragen?<br />
        Kannst du gern
        {% call macros.link(url="https://booksy.com/biz/en-us/contact.html") %}
            unser Team kontaktieren
        {% endcall %}.
    {% endcall %}
{% endblock email_content %}
