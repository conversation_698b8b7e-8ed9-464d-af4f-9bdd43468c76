{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}Security issue detected by Booksy{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        Booksy detected possible security issue on your account {{ business_name }}.
    {% endcall %}

    {% call macros.paragraph() %}
        {% if staffer_name is not none %}   
            User <b>{{ staffer_name }}</b> has opened too many contact details of your customers: <b>{{ staffer_access_count }}</b> per hour.
        {% else %}
            Staff member using Reception Mode has opened too many contact details of your customers: <b>{{ staffer_access_count }}</b> per hour.
        {% endif %}
    {% endcall %}

    {% call macros.paragraph() %}
        Your current security limit in Booksy is {{ business_locked_limit_hourly }} contacts per hour.
    {% endcall %}

    {% call macros.paragraph() %}
        You can increase the maximum number of open contact details here:
        Booksy app &rarr; Business &rarr; Options &rarr; Security alert.
    {% endcall %}

    {% call macros.paragraph() %}
        Any questions?<br />
        Feel free to
        {% call macros.link(url="https://booksy.com/biz/en-us/contact.html") %}
            contact our team
        {% endcall %}.
    {% endcall %}
{% endblock email_content %}
