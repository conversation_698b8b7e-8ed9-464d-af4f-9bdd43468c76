{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% set BOOKSY_CONTACT_URL = BOOKSY_URL_B2B + "contact.html" %}

{% block email_title %}{{ _('Invitation to Booksy') }}{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        {{ _('Hi') }} {{ staffer_name }}!
    {% endcall %}

    {% call macros.paragraph() %}
        {{ _('Good news!') }}
        {{ _('Your team at %(business_name)s is using Booksy to create a rhythm and has invited you to get on board.')|format(business_name=business_name) }}
        {{ _('To join them, download the app, login, and then start booking.') }}
        {{ _('Here are just a few of the things you can look forward to:') }}

        <ul>
            <li>{{ _('24/7 self-service bookings for your customers') }}</li>
            <li>{{ _('All of your client information, in the palm of your hand') }}</li>
            <li>{{ _('Checkout features, even when you’re on the go') }}</li>
            <li>{{ _('Access to individual Performance Snapshots') }}</li>
            <li>{{ _('Tools to help you show off your work') }}</li>
        </ul>
    {% endcall %}

    {% call macros.paragraph(center=True) %}
        {% call macros.button(url="https://dl.booksy.com/4RoxaiaS7hb") %}
            {{ _('Get the App') }}
        {% endcall %}
        <br />
    {% endcall %}

    {% call macros.paragraph() %}
        {{ _('Once you’ve downloaded the app, use the login information below to get started.') }}
        {{ _('Feel free to change your password if you’d like.') }}
    {% endcall %}

    {% call macros.paragraph(color="#a0a0a0", indent=True) %}
        <b>{{ _('Username') }}:</b> <b style="color: #26cbc5; text-decoration: underline;">{{ staffer_login }}</b><br />
        <b>{{ _('Password') }}:</b> <b style="color: #26cbc5; text-decoration: underline;">{{ staffer_password }}</b><br />
        <br />
    {% endcall %}

    {% call macros.paragraph() %}
        {{ _('If you prefer to access your appointments from a computer you can also') }}
        {% call macros.link(url="https://booksy.com/pro") %}
            {{ _('login to Booksy') }}
        {% endcall %} {{ _('via any web browser') }}.
        {{ _('And, if you need help along the way, don’t hesitate to get in touch with our') }}
        {% call macros.link(url=BOOKSY_CONTACT_URL) %}
            {{ _('Support Team') }}
        {% endcall %}.<br/><br/>

        {{ _("Here for you,") }}<br/>
        {{ _("Booksy") }}
    {% endcall %}
{% endblock email_content %}
