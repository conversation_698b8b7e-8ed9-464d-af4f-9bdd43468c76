{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {% if first_email %}
        Review your service experience
    {% else %}
        You have 30 days to write a review
    {% endif %}
{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        Hello,
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        Please write a review of your past visit with {{ business.name }}.
    {% endcall %}


    {% call macros.button(url=business_review_url, image_name="stars.png") %}
        Review
    {% endcall %}

    {% call macros.paragraph() %}
        Please note that we appreciate honest and reliable reviews.
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
            Our suggestions:
    {% endcall %}

    {% call macros.li_table() %}
        {% call macros.li() %}
            advantages, disadvantages, and recommendations
        {% endcall %}

        {% call macros.li() %}
            benefits, drawbacks, and advice on what to avoid
        {% endcall %}

        {% call macros.li() %}
            opinion about the price and/or quality of the service
        {% endcall %}

        {% call macros.li() %}
            willingness to become a return customer or recommend the service to a friend
        {% endcall %}
    {% endcall %}
{% endblock email_content %}
