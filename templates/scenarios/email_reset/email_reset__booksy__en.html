{% if customer_profile_type %}
    {% extends "email_base_customer__booksy__any.html" %}
{% else %}
    {% extends "email_base_business__booksy__any.html" %}
{% endif %}

{% import "email_macros.html" as macros %}

{% block email_title %}Changing your Booksy email address{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        Hello,
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        We’ve received a request to change your email address.
    {% endcall %}

    {% call macros.paragraph() %}
        If you did not request this email change, please ignore and delete this email.
    {% endcall %}

    {% call macros.button(url=email_change_url,disable_mandrill=True) %}
        Change email
    {% endcall %}
{% endblock email_content %}
