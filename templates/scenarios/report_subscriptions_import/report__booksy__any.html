{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {{ _('Subscriptions import report') }}
{% endblock email_title %}

{% block email_content %}
    <div style="background-color:#f9f9f9; padding: 20px;">
        <div style="font: 300 18px 'Proxima Nova', Arial; color: #999; padding-bottom: 10px;">
            {{ _('This is generated report from import subscriptions. Processed rows: {}.').format(total_count) }}<br/>
            {{ _('- {} rows were imported without any errors or warnings,').format(success_count) }}<br/>
            {{ _('- {} rows were imported with warnings,').format(warnings_count) }}<br/>
            {{ _('- {} rows have not been imported due to errors in provided file.').format(omitted_count) }}<br/>
            {% if global_errors %}
                <br/>
                {{ _('Errors and warnings summary:') }}<br/>
                {% for error in global_errors %}- {{ error }}<br/>{% endfor %}
            {% endif %}
        </div>
        <div style="font: 400 12px 'Proxima Nova', Arial; color: #666; padding-bottom: 10px;">
            {{ _('Please see detailed report attached.') }}
        </div>
    </div>
{% endblock email_content %}
