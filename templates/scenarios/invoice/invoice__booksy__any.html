{% extends "email_base_business__booksy__any.html" %}

{% block email_content -%}
<div style="margin: 20px; width: 620px;">
<p>INVOICE for your Booksy account number: #{{ country_code.lower() }}-{{ invoice.transaction.business_id }}</p>
<p>Thank you for being a client!</p>
<br>
<p>SUMMARY:
{% if invoice.payment_method['type'] == 'card' -%}
<p>Your credit card ({{ invoice.payment_method['display'] }}) has been charged {{ invoice.charge_amount }}.</p>
{% elif invoice.payment_method['type'] == 'paypal' -%}
<p>Your PayPal account ({{ invoice.payment_method['display'] }}) has been charged {{ invoice.charge_amount }}.</p>
{% else -%}
<p>You have been charged {{ invoice.charge_amount }}.</p>
{% endif -%}
<p>This charge covers your account until {{ invoice.covers_until | date("DATE_FORMAT") }}.<br>
You will be billed again at that time for the next month.</p>
<p>{{ invoice.transaction.business.owner.full_name }} is the owner on your account.</p>
<br>
<p>NEED MORE HELP?</p>
<p>Contact us: <a href="mailto:{{ COUNTRY_CS_EMAIL }}">{{ COUNTRY_CS_EMAIL }}</a><br>
or call <a href="tel:{{ COUNTRY_CS_PHONE.global_short }}">{{ COUNTRY_CS_PHONE.global_nice }}</a><br>
We're always here to help you!
</p>
<br>



<table style="border-top: 1px dashed #000;border-bottom: 1px dashed #000; width:100%; padding: 10px 0;">
    <tr>
        <td colspan="2"><br>INVOICE</td>
    </tr>
    <tr>
        <td>{{ invoice.created_at | date("DATETIME_FORMAT") }}</td>
        <td style="text-align: right">Invoice #: {{ country_code }}-{{ invoice.invoice_id }}</td>
    </tr>
    <tr>
        <td colspan="2">Transaction ID: {{ invoice.transaction.id }}<br><br></td>
    </tr>
</table>
<br>

<p>
BOOKSY INC<br>
19C Trolley Square Wilmington, DE 19806<br>
United States of America
</p>  

<p>Bill to:</p>

<p>{{ invoice.invoice_address | replace('\n', '<br>') }}</p>
<br>


<table style="border-bottom: 1px dashed #000; width:100%; padding: 10px 0;">
    <tr>
        <td style="width: 25% ">Quantity</td>
        <td style="width: 70% ">Description</td>
        <td style="width: 15%; text-align: right">Price</td>
    </tr>
</table>
<table style="width:100%; padding: 10px 0;">
    <tr>
        <td style="width: 25%; vertical-align: top;">{{ invoice.recurrence }}</td>
        <td style="width: 60%; vertical-align: top;">
            {{ invoice.product.name }}<br>
            From {{ invoice.covers_from | date("DATE_FORMAT") }} to {{ invoice.covers_until | date("DATE_FORMAT") }}.          
        </td>
        <td style="width: 15%; text-align: right;vertical-align: top;">{{ invoice.charge_amount }}</td>
    </tr>
</table>
<table style="width:100%; padding: 10px 0;">
    <tr>
        <td style="width: 60%">&nbsp;</td>
        <td  style="width: 25%; text-align: right;  vertical-align: top; border-top: 1px dashed #000;  padding: 10px 0;">
            Total:
        </td>
        <td style="width: 15%; text-align: right; vertical-align: top; border-top: 1px dashed #000; padding: 10px 0;">
			{{ invoice.charge_amount }}
        </td>
    </tr>
</table>		
<table style="border-bottom: 1px dashed #000; width:100%; padding: 10px 0;">
    <tr>
        <td>
            {% if invoice.payment_method['type'] == 'card' -%}
Payment method: Credit Card<br>
{% elif invoice.payment_method['type'] == 'paypal' -%}
Payment method: PayPal<br>
{% elif invoice.payment_method['type'] == 'mobile' -%}
Payment method: {{ invoice.payment_method['display'] }}<br>
{% endif -%}
Amount paid: {{ invoice.charge_amount }}<br>
{% if invoice.payment_method['type'] == 'card' -%}
Credit card billed: {{ invoice.payment_method['display'] }}<br>
{% elif invoice.payment_method['type'] == 'paypal' -%}
PayPal account billed: {{ invoice.payment_method['display'] }}<br>
{% endif -%}            
        </td>
    </tr>
</table>	

<p>THANK YOU</p>
    <p>Thanks again for your business!</p>
</div>
{% endblock %}
