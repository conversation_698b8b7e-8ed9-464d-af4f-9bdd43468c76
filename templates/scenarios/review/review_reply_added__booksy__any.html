{% extends "email_base_customer__booksy__any.html" %}

{% block email_title %}{{ business_name }} {{ _("has replied to your review") }}{% endblock email_title %}

{% block email_content %}
  <div style="padding-left: 25px; padding-right: 25px">
  <h1 class="font-xl font-bold text-center pb-5 pt-1"
      style="font-size: 25px; font-weight: bold; line-height: 1.5; margin: 0; padding-bottom: 40px; padding-top: 8px; text-align: center;">
    {{ _("Hello!") }}
    <br/>
    {{ business_name }} {{ _("has replied to your review.") }}
  </h1>
  <table border="0" cellspacing="0" cellpadding="0" class="card" align="center" style="margin-bottom: 8px; width: 100%;">
    <tr>
      <td>
        <table border="0" cellspacing="0" cellpadding="0" class="card__body" style="width: 100%;">
          <tr>
            <td>
              <table class="card__content card__content--rounded-top card__content--rounded-bottom" border="0" cellspacing="0" cellpadding="0" style="background-color: #ffffff; border: 1px solid #ececec; border-bottom-left-radius: 4px; border-bottom-right-radius: 4px; border-top: 1px solid #ececec; border-top-left-radius: 4px; border-top-right-radius: 4px; width: 100%;">
                <tr>
                  <td class="pl-3 pr-3 pt-4 pb-4"
                      style="padding-bottom: 32px; padding-left: 24px; padding-right: 24px; padding-top: 32px;">
                    <table border="0" cellspacing="0" cellpadding="0" class="w-100" style="width: 100%;">
                      <tr>
                        <td class="font-s pb-3" style="font-size: 14px; padding-bottom: 24px;">
                          {% if review_services %}
                            {% for service in review_services %}
                              {{ service }}
                              {% if not loop.last %},{% endif %}
                            {% endfor %}
                          {% endif %}
                          {% if review_staffers %}
                            <br>
                            <span class="color-medium" style="color: #6d6e72;"> {{ _("by") }}
                              {% for staff in review_staffers %}
                                {{ staff }}
                                {% if not loop.last %}, {% endif %}
                              {% endfor %}
                            </span>
                          {% endif %}
                        </td>
                        <td class="pb-3" valign="top" align="right" style="padding-bottom: 24px;">
                          {% for n in range(5) %}
                            {% if n < review_rank %}
                              <img src="{{ STATIC_FULL_URL }}mail/solid-star-active.png" class width="16" height="16">
                            {% else %}
                              <img src="{{ STATIC_FULL_URL }}mail/solid-star-grey.png" class width="16" height="16">
                            {% endif %}
                          {% endfor %}
                        </td>
                      </tr>
                    </table>
                    {{ review_content }}
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
  <table border="0" cellspacing="0" cellpadding="0" class="w-100" style="width: 100%;">
    <tr>
      <td class="font-bold pb-3 pt-5" style="font-weight: bold; padding-bottom: 24px; padding-top: 40px;">
        {{ _("Response:") }}
      </td>
    </tr>
    <tr>
      <td class="pb-5" style="padding-bottom: 40px;"> {{ review_reply }} </td>
    </tr>
  </table>
  <table border="0" cellpadding="0" cellspacing="0" class="button-container" style="width: 100%; padding-bottom: 40px;">
    <tbody>
    <tr>
      <td align="center">
        <table border="0" cellpadding="0" cellspacing="0" class="button"
               style="background-color: #0aa2ac; border-radius: 8px; font-weight: bold; width: auto;">
          <tbody>
          <tr>
            <td align="center" valign="top">
              <a href="{{ review_url }}" target="_blank" title style="color: inherit; text-decoration: none;">
                <table border="0" cellspacing="0" cellpadding="0" class>
                  <tr>
                    <td class="button__content"
                        style="border-radius: 8px; color: #fff; font-weight: bold; padding: 12px 32px; text-decoration: none; white-space: nowrap;">
                      {{ _("See your review") }}
                    </td>
                  </tr>
                </table>
              </a>
            </td>
          </tr>
          </tbody>
        </table>
      </td>
    </tr>
    </tbody>
  </table>
  </div>
{% endblock email_content %}
