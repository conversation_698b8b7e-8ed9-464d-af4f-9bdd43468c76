{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}A new review of your business appeared on Booksy{% endblock email_title %}

{% block email_content %}

    {% call macros.paragraph(bold=True) %}
        Hello,
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        Your business has just received a review from one of your clients:
    {% endcall %}

    {% if service_name %}
        {% call macros.paragraph() %}
            This review regards the service: {{ service_name }}
            {% if booking_date and booking_time %}
                performed on: {{ booking_date }} at {{ booking_time }}
            {% endif %}
            {% if staff_names %}
                by staff member: {{ staff_names }}
            {% endif %}
            .
        {% endcall %}
    {% endif %}

    {% call macros.paragraph() %}
        The client has rated this appointment as: {{ review_rank }} on a 1-to-5 scale.
    {% endcall %}

    {% if review_text %}
        {% call macros.paragraph() %}
            Review content: <b>{{ review_title }}</b> {{ review_content }}
        {% endcall %}
    {% endif %}

    {% if review_rank > 3 %}
        {% call macros.paragraph() %}
            Congratulations! Keep collecting high ratings from your clients.
            This will have positive influence on your market position.
        {% endcall %}
    {% else %}
        {% call macros.paragraph() %}
            Every review, even a not-so-positive one, gives you a chance to improve your business.
            You can contact the client and offer them a solution.
        {% endcall %}

        {% call macros.paragraph(bold=True) %}
            Client posting this review:
        {% endcall %}

        {% call macros.li_table() %}
            {% call macros.li() %}
                {{ customer_name }}
            {% endcall %}
            {% call macros.li() %}
                {{ customer_phone }}
            {% endcall %}
            {% call macros.li() %}
                {{ customer_email }}
            {% endcall %}
        {% endcall %}

        {% call macros.paragraph() %}
            Please remember that engaging in an argument with your client is bad business.
            Our experience shows it almost always leads to them having an even worse opinion about your business.
        {% endcall %}

    {% endif %}


    {% if review_replies %}
        {% call macros.paragraph(lines_after=0) %}
            You can comment on every review of your business:
            {% call macros.button(url=reviews_url, lines_after=0, padding_top=40, padding_bottom=5) %}
                Reply to review
            {% endcall %}
        {% endcall %}
    {% endif %}

{% endblock email_content %}
