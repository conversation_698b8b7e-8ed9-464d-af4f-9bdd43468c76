{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}Auf Booksy ist eine neue Bewertung für deine Firma erschienen{% endblock email_title %}

{% block email_content %}

    {% call macros.paragraph(bold=True) %}
        Hallo,
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        Deine Firma hat gerade eine Bewertung von einem Kunden erhalten:
    {% endcall %}

    {% if service_name %}
        {% call macros.paragraph() %}
            Diese Bewertung betrifft die Dienstleistung: {{ service_name }}
            {% if booking_date and booking_time %}
                ausgeführt am: {{ booking_date }} um {{ booking_time }}
            {% endif %}
            {% if staff_names %}
                durch Mitarbeiter: {{ staff_names }}
            {% endif %}
            .
        {% endcall %}
    {% endif %}

    {% call macros.paragraph() %}
        Der Kunde hat diesen Termin bewertet mit: {{ review_rank }} auf einer Skala von 1 bis 5.
    {% endcall %}

    {% if review_text %}
        {% call macros.paragraph() %}
            Inhalt der Bewertung: <b>{{ review_title }}</b> {{ review_content }}
        {% endcall %}
    {% endif %}

    {% if review_rank > 3 %}
        {% call macros.paragraph() %}
            Gratulation! Sammle weiter hohe Bewertungen von deinen Kunden.
            Das wird sich positiv auf deine Marktposition auswirken.
        {% endcall %}
    {% else %}
        {% call macros.paragraph() %}
            Jede Bewertung – selbst eine nicht so positive – ist eine Chance auf Mehrgeschäft für dich.
            Du kannst Kontakt mit dem Kunden aufnehmen und ihm eine Lösung anbieten. 
        {% endcall %}

        {% call macros.paragraph(bold=True) %}
            Kunde, der diese Bewertung verfasst hat:
        {% endcall %}

        {% call macros.li_table() %}
            {% call macros.li() %}
                {{ customer_name }}
            {% endcall %}
            {% call macros.li() %}
                {{ customer_phone }}
            {% endcall %}
            {% call macros.li() %}
                {{ customer_email }}
            {% endcall %}
        {% endcall %}

        {% call macros.paragraph() %}
            Denk daran, dass es schlecht für das Geschäft ist, wenn du mit deinem Kunden in Streit gerätst. 
            Unserer Erfahrung nach führt das fast immer dazu, dass sich seine Meinung über deine Firma noch mehr verschlechtert.
        {% endcall %}

    {% endif %}

{% endblock email_content %}
