{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}Un nouvel avis concernant votre établissement vient d’être publié sur Booksy{% endblock email_title %}

{% block email_content %}

    {% call macros.paragraph(bold=True) %}
        Bonjour,
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        Votre établissement vient de recevoir un nouvel avis d’un de vos clients:
    {% endcall %}

    {% if service_name %}
        {% call macros.paragraph() %}
            Cet avis concerne la prestation {{ service_name }}
            {% if booking_date and booking_time %}
                réalisée le {{ booking_date }} à {{ booking_time }}
            {% endif %}
            {% if staff_names %}
                par : {{ staff_names }}
            {% endif %}
            .
        {% endcall %}
    {% endif %}

    {% call macros.paragraph() %}
        Le client a laissé la note de : {{ review_rank }} sur une échelle de 1 à 5.
    {% endcall %}

    {% if review_text %}
        {% call macros.paragraph() %}
            Avis complet : <b>{{ review_title }}</b> {{ review_content }}
        {% endcall %}
    {% endif %}

    {% if review_rank > 3 %}
        {% call macros.paragraph() %}
          Félicitations ! Continuez à faire de votre mieux pour récolter les notes les plus élevées possible.
            Ces avis ont une influence très positive pour votre positionnement sur le marché.
        {% endcall %}
    {% else %}
        {% call macros.paragraph() %}
          Chaque avis, même les moins positifs, vous donne l'opportunité d’améliorer vos services.
          Vous pouvez contacter vos clients à tout moment et leur proposer une solution si besoin.

        {% endcall %}

        {% call macros.paragraph(bold=True) %}
            Au sujet des clients qui postent des avis :
        {% endcall %}

        {% call macros.li_table() %}
            {% call macros.li() %}
                {{ customer_name }}
            {% endcall %}
            {% call macros.li() %}
                {{ customer_phone }}
            {% endcall %}
            {% call macros.li() %}
                {{ customer_email }}
            {% endcall %}
        {% endcall %}

        {% call macros.paragraph() %}
            Gardez en tête qu’il ne sert à rien d’essayer de se justifier ou d’entrer dans une argumentation pour répondre à un avis. Notre expérience montre que cela aggrave généralement la situation.
        {% endcall %}

    {% endif %}


    {% if review_replies %}
        {% call macros.paragraph(lines_after=0) %}
            Répondez à chaque commentaire laissé sur votre établissement en veillant à le faire avec le plus de diplomatie possible !
            {% call macros.button(url=reviews_url, lines_after=0, padding_top=40, padding_bottom=5) %}
                Répondez à commentaire
            {% endcall %}
        {% endcall %}
    {% endif %}

{% endblock email_content %}
