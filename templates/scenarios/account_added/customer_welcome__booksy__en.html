{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}Welcome to Booksy{% endblock email_title %}

{% block email_content %}

    {% call macros.paragraph(bold=True) %}
        Hello ,
    {% endcall %}

    {% call macros.paragraph() %}
        We're thrilled that you've joined Booksy!
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        Now you can:
    {% endcall %}

    {% call macros.li_table() %}
        {% call macros.li() %}
            Book appointments for services, at any time, 24/7
        {% endcall %}

        {% call macros.li() %}
            View real-time availability for services you want to book
        {% endcall %}

        {% call macros.li() %}
            Write a review of a service and recommend the business to other Booksy  users
        {% endcall %}

        {% call macros.li() %}
            Find the best professional or business in your local area or chosen city
        {% endcall %}

        {% call macros.li() %}
            Select the staff person you want
        {% endcall %}

        {% call macros.li() %}
            Select a business based on their profile
        {% endcall %}

        {% call macros.li() %}
            Easily cancel or reschedule your appointment directly from the app,
            and the business or selected professional will be notified.
        {% endcall %}
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        Bookmark your favorite businesses in the app, so that they are always easy to find.
    {% endcall %}

{% endblock email_content %}
