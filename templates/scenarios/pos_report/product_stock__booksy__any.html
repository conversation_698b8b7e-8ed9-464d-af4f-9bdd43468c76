{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    Booksy: {{ _("Product Stock Change Log") }} - {{ product.name }}
{% endblock email_title %}

{% block email_content %}
    <div style="background-color:#f9f9f9; padding: 20px;">
        <div style="font: 300 18px 'Proxima Nova', Arial; color: #999; padding-bottom: 10px;">
            {{ _("Please find attached report of product stock change log") }}
        </div>
        <div style="font: 400 12px 'Proxima Nova', Arial; color: #666; padding-bottom: 10px;">
            {{ _("Product Stock Change Log") }}
            - {{ product.name }}
        </div>
    </div>
{% endblock email_content %}
