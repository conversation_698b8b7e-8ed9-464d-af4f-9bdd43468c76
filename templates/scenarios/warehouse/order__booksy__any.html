{% extends "email_base.html" %}
{% set BOOKSY_URL = BOOKSY_URL_B2B %}
{% set HEADER = HEADER_B2B %}
{% set BG_COLOR = COLOR_B2B %}
{% set FOOTER_DATA = FOOTER_DATA_B2B %}
{% set IOS_APP_URL = APPLE_B2B_APP_URL %}
{% set ANDROID_APP_URL = ANDROID_B2B_APP_URL %}

{% block email_title %}{{ supply.business.name }} - {{ _('Order no') }} {{ supply.number }}{% endblock email_title %}

{% block email_broad_content %}
  <tr>
    <td valign="top">
      <table border="0" cellpadding="0" cellspacing="0" width="100%">
        <tr>
          <td valign="top" style="background: #fff;">
            <div style="padding: 35px 0;">
              <p>{{ _('Hello') }},</p>
              <p>{{ _('There is a new order for you. See details below.') }}</p>

              <table style="width: 100%;">
                <tr>
                  <td>
                    <strong>{{ _('Ordering salon') }}:</strong>
                    <div style="padding-top: 5px; padding-left: 10px;">
                      {{ supply.business.official_name or supply.business.name }}<br/>
                      {% if supply.business.address %}
                        {{ supply.business.address }}
                        <br/>
                      {% endif %}
                      {% if supply.business.address2 %}
                        {{ supply.business.address2 }}
                        <br/>
                      {% endif %}
                      {{ supply.business.region.full_name or supply.business.zipcode|default('') }}
                      {{ supply.business.city }}<br/>
                      {% if supply.business.phone %}
                        {{ supply.business.phone }}
                        <br/>
                      {% endif %}
                    </div>
                  </td>
                  <td>
                    {% if invoice_details %}
                      <strong>{{ _('Details for the invoice') }}:</strong>
                      <div style="padding-top: 5px; padding-left: 10px;">
                        {{ invoice_details.name }}<br/>
                        {{ invoice_details.address }}<br/>
                        {{ invoice_details.zip_code }} {{ invoice_details.city }}<br/>
                        {{ _('Tax ID') }}: {{ invoice_details.tax_id_number }}<br/>
                      </div>
                    {% endif %}
                  </td>
                </tr>
              </table>

              <p>
                <strong>{{ _('Order details') }}:</strong>
              </p>
              <div style="padding-left: 10px;">
                <table style="width: 100%; text-align: left;">
                  <thead>
                  <tr style="">
                    <th>{{ _('No.') }}</th>
                    <th>{{ _('Name') }}</th>
                    <th>{{ _('Brand') }}</th>
                    <th>{{ _('Quantity') }}</th>
                  </tr>
                  </thead>
                  <tbody>
                  {% for row in supply_rows %}
                    <tr>
                      <td>{{ loop.index }}</td>
                      <td>
                        {{ row.commodity_name }}
                        {% if row.unit_symbol %}
                          {# @formatter:off #}
                          ({{ row.total_pack_capacity }} {{ row.unit_symbol }})
                          {# @formatter:on #}
                        {% endif %}
                      </td>
                      <td>{{ row.brand_name|default('', True) }}</td>
                      <td>{{ row.total_quantity }}</td>
                    </tr>
                  {% endfor %}
                  </tbody>
                </table>
              </div>
            </div>
          </td>
        </tr>
        <tr>
          <td valign="top"
              style="background: #f3f3f3; height: 1px;">
          </td>
        </tr>
      </table>
    </td>
  </tr>
{% endblock email_broad_content %}