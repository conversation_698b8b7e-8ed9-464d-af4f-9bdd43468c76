{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {{ _('{title}').format(title=title)}}
{% endblock email_title %}

{% block email_content %}
    <div style="background-color:#f9f9f9; padding: 20px;">
        <div style="font: 300 18px 'Proxima Nova', Arial; color: #999; padding-bottom: 10px;">
            {{ _('Object {object_type}: {country_code}-{deployment_level}-{pk}
            in  was marked as
            inappropriate
            due to "{reason}". Please modify it {url}').format(
                object_type=object_type,
                pk=pk,
                country_code=country_code,
                deployment_level=deployment_level,
                reason=reason,
                url=url,
            ) }}<br/>
        </div>

        <div style="font: 400 12px 'Proxima Nova', Arial; color: #666; padding-bottom: 10px;">
            {{ _('{}-{}: {}-{}').format(country_code, deployment_level, profile_type, user_id)}}
            {{ _('Reporter: {} {}').format(user_email, user_full_name)}}
            {{ _('Report source: {}').format(report_source)}}
            {% if reviewer_email %}
            {{ _('Reviewer: {}').format(reviewer_email)}}
            {% endif %}
            {% if associated_business_id %}
            {{ _('Associated business email: {}').format(business_email)}}
            {{ _('Associated business name: {}').format(business_name)}}
            {{ _('<a href="{}">{}-{}</a>').format(
            associated_business_url,
            country_code,
            associated_business_id
            )}}
            {% endif %}
        </div>
    </div>
{% endblock email_content %}


{% block footer %}
{% endblock footer %}