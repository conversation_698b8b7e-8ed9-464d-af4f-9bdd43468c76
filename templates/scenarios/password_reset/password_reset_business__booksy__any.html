{% extends "email_base_business__booksy__any.html" %}

{% import "email_macros.html" as macros %}

{% block email_title %}{{ _("Changing your Booksy password") }}{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        {{ _("Hello,") }}
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        {{ _("We've received a request to change your password to Booksy.com.") }}
    {% endcall %}

    {% call macros.paragraph() %}
        {{ _("If you did not request this password change, please ignore and delete this email.") }}
        {{ _("Your current password will remain valid.") }}
    {% endcall %}

    {% call macros.button(url=password_reset_url,disable_mandrill=True, background_color=COLOR_B2B) %}
        {{ _("Change password") }}
    {% endcall %}
{% endblock email_content %}

