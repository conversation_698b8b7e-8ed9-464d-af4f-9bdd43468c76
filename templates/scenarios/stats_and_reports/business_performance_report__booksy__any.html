{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {{ title }}
{% endblock email_title %}

{% block email_content %}
    <div style="padding: 15px;">
        {% call macros.paragraph() %}
            {{ _('Per your request please see attached for a summary of your business performance for {} - {}. Inside you will find:').format(date_from, date_till) }}
        {% endcall %}
        {% call macros.li_table() %}
            {% for worksheet in report_worksheets %}
                {% call macros.li() %}
                    {{ worksheet.original_title }}
                {% endcall %}
            {% endfor %}
        {% endcall %}
        {% call macros.paragraph() %}
            {{ _('Please find attached Detailed Business Performance Report for dates selected. ') }}
        {% endcall %}
    </div>
{% endblock email_content %}
