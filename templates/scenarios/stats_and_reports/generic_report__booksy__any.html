{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {{ email_subject }}
{% endblock email_title %}

{% block email_content %}
    <div style="padding: 15px;">
        {% call macros.paragraph() %}
            {{ _('Please find attached {} for dates selected {} - {}.').format(report_title, date_from, date_till) }}
        {% endcall %}
    </div>
{% endblock email_content %}
