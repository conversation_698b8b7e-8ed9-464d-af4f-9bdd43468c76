{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {{ _('GDPR data export') }}
{% endblock email_title %}

{% block email_content %}
    <div style="background-color:#f9f9f9; padding: 20px;">
        <div style="font: 300 18px 'Proxima Nova', Arial; color: #999; padding-bottom: 10px;">
            {{ _('Dear Booksy user,') }} <br/><br/>
            {{ _('In response to your request we have prepared the detailed report about your business data.') }}
        </div>
        <div style="font: 400 12px 'Proxima Nova', Arial; color: #666; padding-bottom: 10px;">
            {{ _('Please see .txt file attached.') }}
        </div>
    </div>
{% endblock email_content %}