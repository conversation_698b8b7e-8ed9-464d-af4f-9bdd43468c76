{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {{ title }}
{% endblock email_title %}

{% block email_content %}
    <div>
        {% if invoice.buyer and invoice.buyer.customer and invoice.buyer.customer.first_name %}
            <p>{{ _('Hey') }} {{ invoice.buyer.customer.first_name }},</p>
        {% else %}
            <p>{{ _('Hey') }},</p>
        {% endif %}
        <p>{{ _('We enclose an invoice that has been issued for you.') }}</p>
        <p>{{ _('Enjoy!<br> Team Booksy') }}</p>
    </div>
{% endblock email_content %}
