{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}{{ _("Welcome back on Booksy!") }}{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        {{ _("Hi") }},
    {% endcall %}

    {% call macros.paragraph() %}
        {{ _("We miss you at Booksy! Since you've been gone, your business has been getting a&nbsp;lot of attention on our platform.") }}
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
      {{ _("Recently, %(pageviews_count)s people have viewed your profile. And  %(unique_users_count)s people have made requests for online booking.")|format(pageviews_count=pageviews_count, unique_users_count=unique_users_count) }}
    {% endcall %}

    {% call macros.paragraph() %}
        {{ _("<a href='%(deeplink)s'>Click here</a> so you don't lose these customers and begin working with us again today.")|format(deeplink=deeplink) }}
    {% endcall %}

{% endblock email_content %}