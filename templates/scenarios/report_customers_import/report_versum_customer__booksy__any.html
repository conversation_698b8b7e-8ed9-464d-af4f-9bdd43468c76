{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {{ _('Import from Versum. Imported data customers, services, bookings report.') }}
{% endblock email_title %}

{% block email_content %}
    <div style="background-color:#f9f9f9; padding: 20px;">
        {% if errors %}
        <div style="font: 300 18px 'Proxima Nova', Arial; color: #999; padding-bottom: 10px;">
            {{ _('This is generated report from Versum data import.') }}<br/>
            {{ _('An error occured during the import. Please check specification of each file data file.') }}<br/>
            {{ _('Probably the format of some data-file is changed.') }}<br/><br/>
            {% for error in errors %}
                <p class="errornote alert alert-error">
                    {{ error }}
                </p>
            {% endfor %}
        </div>
        {% else %}
        <div style="font: 300 18px 'Proxima Nova', <PERSON>l; color: #999; padding-bottom: 10px;">
            {{ _('This is generated report from Versum data import.') }}<br/>
            {{ _('{customers} were processed and validated.').format(customers=customers) }}<br/>
            {{ _('Please be aware that some customers may be imported with missing data due to validation process.') }}<br/>
            {{ _('{services} services were processed and validated.').format(services=services) }}<br/>
            {{ _('Note that all services will be assigned to existing staffers.') }}<br/>
            {{ _('The total number of imported bookings is: {bookings}.').format(bookings=bookings) }}<br/>
        </div>
        {% endif %}
    </div>
{% endblock email_content %}
