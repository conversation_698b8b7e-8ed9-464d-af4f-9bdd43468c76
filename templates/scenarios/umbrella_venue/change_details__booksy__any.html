{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {{ _('Change Details Request Umbrella Venues from: {full_name}').format(full_name=full_name) }}
{% endblock email_title %}

{% block email_content %}
    <div style="background-color:#f9f9f9; padding: 20px;">
        <div style="font: 300 18px 'Proxima Nova', Arial; color: #999; padding-bottom: 10px;">
            {{ _('Change Request from {full_name}.').format(full_name=full_name) }}<br/>
            {{ _('For umbrella-venue business {umbrella_venue_name} ({umbrella_venue_id})').format(umbrella_venue_name=umbrella_venue_name,umbrella_venue_id=umbrella_venue_id) }}<br/>
            {{ _('Suggested details for "{umbrella_venue_name}": "{change_request_body}"').format(umbrella_venue_name=umbrella_venue_name, change_request_body=change_request_body) }}<br/>
        </div>
        <div style="font: 400 12px 'Proxima Nova', <PERSON><PERSON>; color: #666; padding-bottom: 10px;">
            ---------------------------
            Domain: {{ booksy_domain }}
            Country: {{ country_code }}
            UserAgent: {{ user_agent }}
            X-Version: {{ x_version }}
            X-Fingerprint: {{ x_fingerprint }}
            IP: {{ ip }}
            {% if user %}
                User: {{ user.id }}
                {% if user.email or user.phone %} ||
                    Contact info: {{ user.email }} {{ user.cell_phone }}
                {% endif %}
            {% endif %}
        </div>
    </div>
{% endblock email_content %}