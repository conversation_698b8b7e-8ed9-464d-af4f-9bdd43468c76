---
booking_finished:
    review_request__booksy__any.push: >
        {{ _("Please share your opinion about your visit at {business_name} with other Booksy users!").format(business_name=business.name) }}

    tipping_experiment__booksy__any.push: >
        {{ _("Show your appreciation for your recent service by adding a tip.") }}

booking_changed:
    _account_invitation__booksy__any.sms: >
        {% if not booking_info.customer_has_account %}
          {{ _("You can also book appointments through the Booksy app on your smart phone. Get started today! booksy.com/download") }}
        {% endif %}

    booking_reminder__booksy__any.sms: >
        {{ _("You’re scheduled with {business_name_short} at {booking_date_short} {booking_time}.").format(business_name_short=booking_info.business_name_short,
        booking_date_short=booking_info.booking_date_short, booking_time=booking_info.booking_time) }}
        {% if booking_info.customer_has_account %}
            {{ _("To make changes to your appointment, please visit this link: {booking_deeplink}").format(booking_deeplink=booking_info.booking_sms_deeplink) }}
        {% else %}
            << booking_changed/_account_invitation__booksy__any.sms >>
        {% endif %}

    booking_reminder__booksy__any.push: >
        {{ _("Appointment reminder: You’re scheduled at {booking_date_short} {booking_time} with {business_name}. If you need to reschedule or cancel your appointment, you can do it here.").format(booking_date_short=booking_info.booking_date_short,
        booking_time=booking_info.booking_time, business_name_short=booking_info.business_name_short, business_name=booking_info.business_name) }}

    customer_booksy_pay_before_appointment_started_reminder__booksy__any.sms: >
        {% if booksy_pay_cashback_info %}
            {{ _("Don't forget your appointment today! {booksy_pay_cashback_info}").format(booksy_pay_cashback_info=booksy_pay_cashback_info) }}
        {% else %}
            {{ _("Don't forget your appointment today! Pay via card with Booksy Pay!") }}
        {% endif %}

    customer_booksy_pay_before_appointment_started_reminder__booksy__any.push: >
        {% if booksy_pay_cashback_info %}
            {{ _("Don't forget your appointment today! {booksy_pay_cashback_info}").format(booksy_pay_cashback_info=booksy_pay_cashback_info) }}
        {% else %}
            {{ _("Don't forget your appointment today! Pay via card with Booksy Pay!") }}
        {% endif %}

    customer_booksy_pay_before_appointment_finished_reminder__booksy__any.push: >
        {% if booksy_pay_cashback_info %}
            {{ _("Your appointment is about to finish! {booksy_pay_cashback_info}").format(booksy_pay_cashback_info=booksy_pay_cashback_info) }}
        {% else %}
            {{ _("Your appointment is about to finish! Pay via card with Booksy Pay!") }}
        {% endif %}
        
    customer_tap_to_pay_before_appointment_started_reminder__booksy__any.push: >
        {{ _("Your appointment is coming up soon. Pay securely with your card for a quick and easy checkout, just Tap to Pay!") }}

    customer_ttp_or_bcr_availability_before_appointment_started_reminder__booksy__any.push: >
        {{ _("Your appointment is coming up soon. Pay securely via card for a quick and easy checkout.") }}

    business_booking_confirmation__booksy__any.push: >
        {% if booking_info.business_is_automatic or booking_info.booking_by_business %}
            {% if booking_info.customer_name %}
                {{ _("You have received a new booking from {customer_name} on {booking_date} at {booking_time}.").format(business_name=booking_info.business.name, customer_name=booking_info.customer_name, booking_date=booking_info.booking_date, booking_time=booking_info.booking_time) }}
            {% else %}
                {{ _("You have received a new booking on {booking_date} at {booking_time}.").format(booking_date=booking_info.booking_date, booking_time=booking_info.booking_time ) }}
            {% endif %}
        {% else %}
            {{ _("{customer_name} is waiting for your confirmation! Booked on {booking_date} at {booking_time}.").format(customer_name=booking_info.customer_name, booking_date=booking_info.booking_date, booking_time=booking_info.booking_time) }}
        {% endif %}

    business_booking_cancel__booksy__any.push: >
        {{ _("{customer_name} cancelled his/her appointment on: {booking_date} at {booking_time}").format(customer_name=booking_info.customer_name,
        booking_date=booking_info.booking_date, booking_time=booking_info.booking_time) }}

    business_booking_rescheduled__booksy__any.push: >
        {% if booking_info.business_is_automatic %}
            {{ _("{customer_name} appointment has been rescheduled to {booking_date} at {booking_time}. The system accepted this change automatically.").format(customer_name=booking_info.customer_name, booking_date=booking_info.booking_date, booking_time=booking_info.booking_time) }}
        {% else %}
             {{ _("{customer_name} wants to reschedule appointment. Please accept or reject the request, so that the customer is notified.").format(customer_name=booking_info.customer_name) }}
        {% endif %}

    business_booking_rescheduled_manual__booksy__any.push: >
        {{ _("{customer_name} wants to reschedule appointment. Please accept or reject the request, so that the customer is notified.").format(customer_name=booking_info.customer_name)

    customer_booking_cancel__booksy__any.push: >
        {{ _("Your appointment{booking_additional_info} with {business_name} on {booking_date} at {booking_time} has been cancelled.").format(business_name=booking_info.business_name,
        booking_date=booking_info.booking_date, booking_time=booking_info.booking_time, booking_additional_info=booking_info.additional_booking_info) }}

    customer_booking_cancel__booksy__any.sms: >
        {{ _("Your appointment{booking_additional_info} with {business_name_short} on {booking_date_short} at {booking_time} has been cancelled.").format(business_name_short=booking_info.business_name_short,
        booking_date_short=booking_info.booking_date_short, booking_time=booking_info.booking_time, booking_additional_info=booking_info.additional_booking_info) }}

    customer_booking_confirmation__booksy__any.push: >
        {% if booking_info.is_booksy_pay_payment_window_open %}
            {% if booksy_pay_cashback_info %}
                {{ _("Your appointment{booking_additional_info} with {business_name} on {booking_date} at {booking_time} has been confirmed. {booksy_pay_cashback_info}").format(business_name=booking_info.business_name, booking_date=booking_info.booking_date, booking_time=booking_info.booking_time, booking_additional_info=booking_info.additional_booking_info, booksy_pay_cashback_info=booksy_pay_cashback_info) }}
            {% else %}
                {{ _("Your appointment{booking_additional_info} with {business_name} on {booking_date} at {booking_time} has been confirmed. Pay via card with Booksy Pay!").format(business_name=booking_info.business_name, booking_date=booking_info.booking_date, booking_time=booking_info.booking_time, booking_additional_info=booking_info.additional_booking_info) }}
            {% endif %}
        {% else %}
            {{ _("Your appointment{booking_additional_info} with {business_name} on {booking_date} at {booking_time} has been confirmed.").format(business_name=booking_info.business_name,
            booking_date=booking_info.booking_date, booking_time=booking_info.booking_time, booking_additional_info=booking_info.additional_booking_info) }}
        {% endif %}

    customer_booking_confirmation__booksy__any.sms: >
        {% if booking_info.is_booksy_pay_payment_window_open %}
            {% if booksy_pay_cashback_info %}
                {{ _("Your appointment{booking_additional_info} with {business_name} on {booking_date} at {booking_time} has been confirmed. {booksy_pay_cashback_info}").format(business_name=booking_info.business_name, booking_date=booking_info.booking_date, booking_time=booking_info.booking_time, booking_additional_info=booking_info.additional_booking_info, booksy_pay_cashback_info=booksy_pay_cashback_info) }}
            {% else %}
                {{ _("Your appointment{booking_additional_info} with {business_name} on {booking_date} at {booking_time} has been confirmed. Pay via card with Booksy Pay!").format(business_name=booking_info.business_name, booking_date=booking_info.booking_date, booking_time=booking_info.booking_time, booking_additional_info=booking_info.additional_booking_info) }}
            {% endif %}
        {% else %}
            {{ _("Your appointment{booking_additional_info} with {business_name} on {booking_date} at {booking_time} has been confirmed.").format(business_name=booking_info.business_name,
            booking_date=booking_info.booking_date, booking_time=booking_info.booking_time, booking_additional_info=booking_info.additional_booking_info) }}
        {% endif %}

    customer_booking_decline_manual__booksy__any.push: >
        {{ _("Unfortunately {business_name} can not accept your appointment at the date given.").format(business_name=booking_info.business_name) }}
        {{ _("Please try to pick another date.") }}

    customer_booking_rescheduled_auto__booksy__any.push: >
        {{ _("{business_name} has rescheduled your appointment{booking_additional_info} to {booking_date} at {booking_time}. If you need to cancel, you can do so through Booksy.").format(business_name=booking_info.business_name,
        booking_date=booking_info.booking_date, booking_time=booking_info.booking_time, booking_additional_info=booking_info.additional_booking_info) }}

    customer_booking_rescheduled_auto__booksy__any.sms: >
        {{ _("{business_name_short} has rescheduled your appointment{booking_additional_info} to {booking_date} at {booking_time}.").format(business_name_short=booking_info.business_name_short,
        booking_date=booking_info.booking_date, booking_time=booking_info.booking_time, booking_additional_info=booking_info.additional_booking_info) }}
        {% if booking_info.customer_has_account and booking_info.business_is_visible %}
            {{ _("If you need to cancel, you can do so through Booksy.") }}
        {% else %}
            {{ _("If you need to cancel your appointment, you can call {business_phone_short}.").format(business_phone_short=booking_info.business_phone_short) }}
        {% endif %}
        << booking_changed/_account_invitation__booksy__any.sms >>

    customer_booking_reschedule_request__booksy__any.push: >
        {{ _("Your appointment{booking_additional_info} with {business_name} on {booking_date} at {booking_time} requires additional confirmation.").format(business_name=booking_info.business_name,
        booking_date=booking_info.booking_date, booking_time=booking_info.booking_time, booking_additional_info=booking_info.additional_booking_info) }}

    customer_booking_reschedule_request__booksy__any.sms: >
        {% if user %}
            {{ _("{business_name_short} suggests an appointment{booking_additional_info} change to {booking_date} at {booking_time}. Please accept the new appointment or cancel the service {booking_marketplace_url} Thank you.").format(business_name_short=booking_info.business_name_short, booking_date=booking_info.booking_date, booking_time=booking_info.booking_time, booking_marketplace_url=booking_info.booking_sms_deeplink, booking_additional_info=booking_info.additional_booking_info) }}
        {% else %}
            {{ _("{business_name_short} suggests an appointment{booking_additional_info} change from {previous_booking_date} at {previous_booking_time} to {booking_date} at {booking_time}. Please contact them by phone to confirm the appointment. {business_phone_short}.").format(business_name_short=booking_info.business_name_short, previous_booking_date=booking_info.previous_booking_date, previous_booking_time=booking_info.previous_booking_time, booking_date=booking_info.booking_date, booking_time=booking_info.booking_time, business_phone_short=booking_info.business_phone_short, booking_additional_info=booking_info.additional_booking_info) }}
        {% endif %}
        << booking_changed/_account_invitation__booksy__any.sms >>

    business_booking_reschedule_response__booksy__any.push: >
        {% if reschedule_confirmed %}
        {{ _("{customer_name} accepted the new appointment time you proposed. Your calendar has been updated.").format(customer_name=booking_info.customer_name) }}
        {% else %}
        {{ _("{customer_name} rejected the new appointment time you proposed. Your calendar has been updated.").format(customer_name=booking_info.customer_name) }}
        {% endif %}


registration:
    verification_sms__booksy__any.sms: >
        {{ _("Use this code {sms_code} to verify your phone number.").format(sms_code=sms_code) }}

    verification_sms_android__booksy__any.sms: >
        {{ ("{prefix} " + _("Use this code {sms_code} to verify your phone number.") +" {app_hash_id}").format(prefix=prefix, sms_code=sms_code, app_hash_id=app_hash_id)}}


marketplace_messages:
    marketplace_bundle_accept_landing_page__booksy__any.sms: >
        {{ _("Are you ready for this? Get ready for appointment booking bliss. Head to the app store to download Booksy Biz!\n"
        "{deeplink}").format(deeplink=deeplink) }}


sms_limits:
    demo__booksy__any.sms: >
        {{ _("You have reached the limit of free text reminders. "
        "Switch to paid version of Booksy to enable this functionality") }}

    free__booksy__any.sms: >
        {{ _("You have reached the limit of free SMS messages this month. "
        "If you need more messages, please enable paid SMS messages in your account settings.") }}

    paid080__booksy__any.sms: >
        {{ _("You have used {percent} of your SMS messages this month. "
        "You can change your monthly limit in your account settings.").format(percent='80%') }}

    paid100__booksy__any.sms: >
        {{ _("You have used {percent} of your SMS messages this month. "
        "You can change your monthly limit in your account settings.").format(percent='100%') }}


password_reset:
    password_reset_business__booksy__any.sms: >
        {{ _("We have noticed an attempt to change the password for your Booksy account") }}


access_rights:
    staffer_locked_access_alert__booksy__any.sms: >
        {{ _("Security issue detected by Booksy. Check your e-mail.") }}


review:
    review_added__booksy__any.push: >
        {{ _("You received a new review from {customer_name}.").format(customer_name=customer_name) }}


invitation:
    sms_invitation__booksy__any.sms: >
        {{ _("Download Booksy app to find and book beauty services: "
        "{download_link}").format(download_link=download_link) }}


message_templates:
    invite_customers_sms-name__booksy__any.sms: >
        {{ _("Invitation to Booksy") }}
    invite_customers_sms-text__booksy__any.sms: >
        {{ _("You can now book your appointments with {business_name_short} 24/7 using Booksy: {branchio_url}\nSee you soon!").format(branchio_url=branchio_url, business_name_short=business_name_short) }}

    invite_customers_reminder_sms-name__booksy__any.sms: >
        {{ _("Invite reminder") }}
    invite_customers_reminder_sms-text__booksy__any.sms: >
        {{ _("You can now book all of your appointments with {business_name_short} using Booksy. Here's your 24/7 access to our calendar: {branchio_url}.\nSee you soon!").format(branchio_url=branchio_url, business_name_short=business_name_short) }}

no_show_proposition:
    cancellation_fee_first_encouragement__booksy__any.push: >
        {{ _("Start using cancellation fees and deposits to reduce no-shows!") }}
    cancellation_fee_second_encouragement__booksy__any.sms: >
        {{ _("No-Show? Not Anymore! Start using a no-show protection!\n") }}
        {{ branchio_url }}

voucher:
    send_voucher_to_customer__booksy__any.sms: >
        {{ _("This is your {voucher_type} code from {business_name}. "
        "Please provide it during checkout. Code: {voucher_code}").format(voucher_type=voucher_type, business_name=business_name, voucher_code=voucher_code) }}

booking_reactivation:
    post_message__booksy__any.sms: >
        {{ _(
          "To be added to waitlist for {treatment_name} "
          "click {waitlist_deeplink} or to find other available providers "
          "click {other_businesses_deeplink}".format(
            treatment_name=treatment_name,
            waitlist_deeplink=waitlist_deeplink,
            other_businesses_deeplink=other_businesses_deeplink,
          )
        ) }}
