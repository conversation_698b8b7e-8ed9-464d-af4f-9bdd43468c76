{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
  {{ _('Booksy’s Information Obligation') }}
{% endblock email_title %}

{% block email_content %}

  <table class="content" border="0" cellspacing="0" cellpadding="0" style="padding: 32px 48px;">
    <tr>
      <td>
        <h1 class="font-xl font-bold text-center pb-5 pt-1" style="font-size: 25px; font-weight: bold; line-height: 1.5; margin: 0; padding-bottom: 40px; padding-top: 8px; text-align: center;">{{ _("Hello") }}</h1>
        <p class="pb-4" style="line-height: 1.5; margin: 0; padding-bottom: 32px;">{{ _("Please be advised that in order to access online booking and other Booksy functions, you authorize the processing of your personal data.") }}</p>
        <p class="pb-4" style="line-height: 1.5; margin: 0; padding-bottom: 32px;">{{ _("You can find more detailed information") }}<a class="link" href="{{ BOOKSY_PRIVACY_URL_B2C }}" style="color: #0aa2ac; text-decoration: underline;">{{_("here")}}</a>.</p>
        <p style="line-height: 1.5; margin: 0;">{{ _("Thanks,") }}
          <br>{{ _("The Booksy Team") }}
        </p>
      </td>
    </tr>
  </table>

{% endblock email_content %}
