<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>{{ title }}</title>
<style>

 @media only screen and (max-width: 480px) {
     #ts1, #ts2 {
         width: 100% !important;
     }

     #ts1 {
         width: 100% !important;
         border-right: none !important;
         border-bottom: #E1E1E1 1px solid;
         padding-bottom: 20px
     }

     body {
         background: none !important;
         padding: 0 !important;
     }
 }
</style>
</head>

<body>


<table border="0" cellpadding="0" cellspacing="0"  width="100%">
            <tr>
                <td align="center" valign="top">

                    <table align="center" border="0" cellpadding="0" cellspacing="0" style="background: #FFFFFF; width: 100%; max-width: 500px; border-radius: 3px;" width="500">
                        <tr>
                            <td align="left" valign="top">

                                <!--CONTENT -->

                                <div >
                                    <div style="color: #702506 ;font-family: 'Proxima Nova', 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 24px; line-height: 30px; text-align: center;  font-weight: 300;">
                                     Marketplace transaction {{ mt.id }} failed

                                    </div>

                                    <div style="color: #702506 ;font-family: 'Proxima Nova', 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 24px; line-height: 30px; text-align: center;  font-weight: 300;">
                                     History of transaction:
                                    </div>
                                     <table border="0" cellpadding="0" cellspacing="0" width="100%" style="padding: 30px 4%;">
                                         {% for status in transactions %}
                                         <tr>
                                            <td align="left" valign="top" style="padding-bottom: 30px;">

                                                <div style="color: #702506 ;font-family: 'Proxima Nova', 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 24px; line-height: 30px; text-align: center;  font-weight: 300;">
                                                     {{ status.status }}
                                                </div>
                                                <div style="color: #383734;font-family: 'Proxima Nova', 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 22px; font-weight: 300;">
                                                    merchant_id: {{ mt.business.id }}
                                                </div>
                                                <div style="color: #383734;font-family: 'Proxima Nova', 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 22px; font-weight: 300;">
                                                    merchant_name: {{ mt.business.name }}
                                                </div>
                                                <div style="color: #383734;font-family: 'Proxima Nova', 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 22px; font-weight: 300;">
                                                    braintree_id: {{status.braintree_id}}
                                                </div>
                                                <div style="color: #383734;font-family: 'Proxima Nova', 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 22px; font-weight: 300;">
                                                    charge_date: {{status.charge_date}}
                                                </div>
                                                <div style="color: #383734;font-family: 'Proxima Nova', 'Helvetica Neue', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 22px; font-weight: 300;">
                                                    errors: {{status.errors}}
                                                </div>
                                                <hr>
                                            </td>
                                         </tr>
                                         {% endfor %}
                                     </table>
                                </div>


                                <!--CONTENT -->


                            </td>
                        </tr>
                    </table>

                </td>
            </tr>
        </table>

</body>
</html>
