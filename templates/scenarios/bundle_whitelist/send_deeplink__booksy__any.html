{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}No Booksy Subscription Fees for 12 months{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(lines_after=0, center=True) %}
        <img src="{{ STATIC_FULL_URL }}scenarios/bundle_whitelist.gif" title="Booksy" alt="Booksy" height="560" width="560" />
    {% endcall %}
    {% call macros.paragraph() %}
        We’re committed to helping Bay Area businesses grow, so for a limited time we're offering a <b>Free 12 months of Booksy Biz + 80% off Booksy Boost.</b>
    {% endcall %}
    {% call macros.button(url=url) %}
        LET'S DO THIS
    {% endcall %}
{% endblock email_content %}

{% block app_store_url %}{{ url }}{% endblock app_store_url %}

{% block google_play_url %}{{ url }}{% endblock google_play_url %}
