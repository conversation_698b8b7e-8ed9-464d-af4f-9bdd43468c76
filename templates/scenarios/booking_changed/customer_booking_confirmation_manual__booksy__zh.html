{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}您的预约已确认{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        {{ booking_info.business_name }}已确认了您的预约。
    {% endcall %}

    {{ macros.booking_info(booking_info, to='C') }}

    {% call macros.pos_add_card_infobox(link=booking_info.customer_add_card_link) %}{% endcall %}

    {% call macros.paragraph() %}
        若想修改或取消预约，请登录:
        {% call macros.link(url=booking_info.booking_marketplace_url) %}
            booksy.com
        {% endcall %}
        并选择“我的预约”。
    {% endcall %}

    {% if show_disclosure_obligation_agreement %}
        {% call macros.paragraph() %}
            {{ disclosure_obligation_agreement_description }}
            {% call macros.link(url=disclosure_obligation_agreement_url) %}
                {{ disclosure_obligation_agreement_url_description }}
            {% endcall %}
            {{ disclosure_obligation_agreement_description2 }}
            {% call macros.link(url=disclosure_obligation_agreement_url2) %}
                {{ disclosure_obligation_agreement_url_description2 }}
            {% endcall %}
            {{ disclosure_obligation_agreement_description3 }}
        {% endcall %}
    {% endif %}
{% endblock email_content %}
