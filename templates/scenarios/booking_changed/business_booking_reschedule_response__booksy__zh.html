<p>{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {{ booking_info.customer_name }}:
    {% if reschedule_confirmed %}确认了{% else %}拒绝了{% endif %}
    建议的新预约时间
{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        顾客<b>{{ booking_info.customer_name }}</b>
        {% if reschedule_confirmed %}<b>确认了</b>{% else %}<b>拒绝了</b>{% endif %}
         您建议的新预约时间。
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}

    {% call macros.paragraph() %}
        修改已在日历中更新。
    {% endcall %}
{% endblock email_content %}</p>
