{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}{{ _("Your booking has been cancelled") }}{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        {{ _("Hello,") }}
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
         {{ _("Your appointment {booking_service_name_full}{additional_booking_info} at {business_name} on {booking_date} at {booking_time} has been cancelled.").format(booking_service_name_full=booking_info.booking_service_name_full, business_name=booking_info.business_name, booking_date=booking_info.booking_date, booking_time=booking_info.booking_time, additional_booking_info=booking_info.additional_booking_info) }}
    {% endcall %}

    {% if booking_info.booking.business_note %}
        {% call macros.paragraph() %}
            {{ _("Reason for booking cancellation: <b>{QUOTATION_START}{business_note}{QUOTATION_END}</b>").format(QUOTATION_START=QUOTATION_START, business_note=booking_info.booking.business_note, QUOTATION_END=QUOTATION_END) }}
        {% endcall %}
    {% endif %}

    {{ macros.booking_info(booking_info, to='C', show_booking=False, show_map=False) }}

    {% call macros.button(url=booking_info.business_email_deeplink) %}
        {{ _("Try another date") }}
    {% endcall %}

{% endblock email_content %}
