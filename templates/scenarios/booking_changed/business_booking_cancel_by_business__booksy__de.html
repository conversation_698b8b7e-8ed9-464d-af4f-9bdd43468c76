{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}hat den Termin am: {{ booking_info.booking_date }} {{ booking_info.booking_time }}{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        <b>{{ booking_info.booking_last_modified_by }}</b> storniert.
        hat deinen Termin für <b>{{ booking_info.customer_name }}</b>
        <b>{{ booking_info.booking_service_name_full }}</b>
        am: <b>{{ booking_info.booking_date }}</b> um <b>{{ booking_info.booking_time }}</b> storniert.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}
{% endblock email_content %}
