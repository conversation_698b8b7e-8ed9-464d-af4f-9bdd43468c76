<p>{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}{{ booking_info.customer_name }}: changed his/her booking and is awaiting a confirmation{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        Hello,
    {% endcall %}

    {% call macros.paragraph() %}
        <b>{{ booking_info.customer_name }}</b> has rescheduled their appointment for <b>{{ booking_info.booking_service_name_full }}</b> to another time.
    {% endcall %}

    {% call macros.paragraph() %}
        Your calendar is set to manual mode, so the client is awaiting your confirmation of the new appointment.
    {% endcall %}

    {% call macros.paragraph() %}
        <b>New, unconfirmed appointment date:</b> {{ booking_info.booking_date }} at: {{ booking_info.booking_time }}<br>
        {% if booking_info.previous_booking_date and booking_info.previous_booking_time %}
            <b>Previous appointment date:</b> {{ booking_info.previous_booking_date }} at: {{ booking_info.previous_booking_time }}
        {% endif %}
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        You can confirm this new time:
    {% endcall %}

    {% call macros.li_table() %}
        {% call macros.li() %}
            through the website
            {% call macros.link(url=booking_info.business_webbiz_url) %}
                biz.booksy.com
            {% endcall %}
        {% endcall %}
        {% call macros.li() %}
            through the Booksy Biz app on your phone: check the DASHBOARD, find the client and accept or reject the new appointment time
        {% endcall %}
    {% endcall %}

    {% call macros.paragraph() %}
        After your confirmation, the previous appointment time will be made available on the calendar.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}
{% endblock email_content %}</p>
