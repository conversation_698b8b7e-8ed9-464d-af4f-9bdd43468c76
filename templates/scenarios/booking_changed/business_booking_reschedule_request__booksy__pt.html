<p>{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}Agendamento alterado em: {{ booking_info.previous_booking_date }} {{ booking_info.previous_booking_time }}{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        <b>{{ booking_info.booking_last_modified_by }}</b> mudou seu agendamento de

        <b>{{ booking_info.previous_booked_from_booking_date }}</b> das <b>{{ booking_info.previous_booked_from_booking_time }}</b> até às <b>{{ booking_info.previous_booked_till_booking_time }}</b>
        para
        <b>{{ booking_info.booked_from_booking_date }}</b> das <b>{{ booking_info.booked_from_booking_time }}</b> até às <b>{{ booking_info.booked_till_booking_time }}</b>
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}
{% endblock email_content %}</p>
