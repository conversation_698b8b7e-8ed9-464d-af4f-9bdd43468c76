{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}您修改与{{ booking_info.business_name }}的预约请求已发送{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        您好,
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        您与{{ booking_info.business_name }}的预约修改请求已发送，正在等待接受。
    {% endcall %}

    {{ macros.booking_info(booking_info, to='C') }}
{% endblock email_content %}
