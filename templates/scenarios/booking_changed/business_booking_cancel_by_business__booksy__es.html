{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}Cita cancelada el: {{ booking_info.booking_date }} {{ booking_info.booking_time }}{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        <b>{{ booking_info.booking_last_modified_by }}</b>
        cancelada tu cita para <b>{{ booking_info.customer_name }}</b>
        <b>{{ booking_info.booking_service_name_full }}</b>
        el: <b>{{ booking_info.booking_date }}</b> a las <b>{{ booking_info.booking_time }}</b>.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}
{% endblock email_content %}
