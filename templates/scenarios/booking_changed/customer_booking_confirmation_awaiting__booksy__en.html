{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}Your booking is awaiting confirmation{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        Hello,
    {% endcall %}

    {% call macros.paragraph() %}
        <b>{{ booking_info.business_name }}</b>
        has received your request to book an appointment for:
        <b>{{ booking_info.booking_service_name_full }}</b>
        on <b>{{ booking_info.booking_date }}</b> at <b>{{ booking_info.booking_time }}</b>.
    {% endcall %}

    {% call macros.paragraph() %}
        The service provider must confirm every booking request.
        You'll receive the status of your booking in a separate email. 
    {% endcall %}

    {{ macros.booking_info(booking_info, to='C') }}

    {% call macros.pos_add_card_infobox(link=booking_info.customer_add_card_link) %}{% endcall %}
{% endblock email_content %}
