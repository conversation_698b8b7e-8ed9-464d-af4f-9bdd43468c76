<p>{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {{ booking_info.customer_name }}:
    {% if reschedule_confirmed %}確定しました{% else %}断りましたrejected{% endif %}
    新しい確定予約を提案しました
{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        お客様 <b>{{ booking_info.customer_name }}</b>
        {% if reschedule_confirmed %}<b>確定しました</b>{% else %}<b>断りました</b>{% endif %}
        あなたの新しく提案した予約時間を。
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}

    {% call macros.paragraph() %}
        変更された予約内容でカレンダーを更新しました。
    {% endcall %}
{% endblock email_content %}</p>
