{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}Odwołanie wizyty z dnia {{ booking_info.booking_date }} {{ booking_info.booking_time }}{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        <b>{{ booking_info.booking_last_modified_by }}</b>
        odwołał twoją usługę
        <b>{{ booking_info.booking_service_name_full }}</b>
        w dniu <b>{{ booking_info.booking_date }}</b> o godzinie <b>{{ booking_info.booking_time }}</b>.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}
{% endblock email_content %}
