{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}Ваш запрос на изменение бронирования у {{ booking_info.business_name }} отправлен{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        Здравствуйте!
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        Ваш запрос на перенос бронирования у: {{ booking_info.business_name }} отправлен и подтверждает принятия.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='C') }}
{% endblock email_content %}
