<p>{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {{ booking_info.customer_name }}:
    {% if reschedule_confirmed %}potwierdzenie{% else %}odr<PERSON><PERSON>nie{% endif %}
    propozycji zmiany terminu
{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        Klient <b>{{ booking_info.customer_name }}</b>
        {% if reschedule_confirmed %}<b>potwierdził</b>{% else %}<b>odr<PERSON>cił</b>{% endif %}
        Twoją propozycję zmiany terminu wizyty.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}

    {% call macros.paragraph() %}
        Odpowiednie zmiany zostały naniesione w kalendarzu.
    {% endcall %}
{% endblock email_content %}</p>
