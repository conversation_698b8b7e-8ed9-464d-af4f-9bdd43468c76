<p>{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}{{ booking_info.customer_name }}:  hat seine/ihre Buchung geändert und wartet auf Bestätigung{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        Hallo,
    {% endcall %}

    {% call macros.paragraph() %}
        <b>{{ booking_info.customer_name }}</b> hat seinen Termin für <b>{{ booking_info.booking_service_name_full }}</b> auf einen anderen Zeitpunkt verschoben.
    {% endcall %}

    {% call macros.paragraph() %}
        Dein <PERSON> ist auf den manuellen Modus eingestellt, der Kunde wartet daher darauf, dass du den neuen Termin bestätigst.
    {% endcall %}

    {% call macros.paragraph() %}
        <b>Neuer unbestätigter Termin:</b> {{ booking_info.booking_date }} um: {{ booking_info.booking_time }}<br>
        {% if booking_info.previous_booking_date and booking_info.previous_booking_time %}
            <b>Bisheriger Termin:</b> {{ booking_info.previous_booking_date }} um: {{ booking_info.previous_booking_time }}
        {% endif %}
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        Du kannst diesen neuen Zeitpunkt bestätigen:
    {% endcall %}

    {% call macros.li_table() %}
        {% call macros.li() %}
            über die Website
            {% call macros.link(url=booking_info.business_webbiz_url) %}
                biz.booksy.com
            {% endcall %}
        {% endcall %}
        {% call macros.li() %}
            über die Booksy Biz-App auf deinem Smartphone: in der ÜBERSICHT den Kunden aufrufen und den neuen Termin annehmen oder ablehnen
        {% endcall %}
    {% endcall %}

    {% call macros.paragraph() %}
        Nach deiner Bestätigung wird bisherige Termin im Kalender wieder verfügbar gemacht.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}
{% endblock email_content %}</p>
