{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {% if booking_info.customer_name %}
        {{ booking_info.customer_name }}:新预约{{ booking_info.booking_date }} {{ booking_info.booking_time }}
    {% else %}
        新预约：{{ booking_info.booking_date }} {{ booking_info.booking_time }}
    {% endif %}
{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        {% if booking_info.business_is_automatic or booking_info.booking_by_business %}
            {% if booking_info.customer_name %}
                {{ booking_info.customer_name }}:新预约
            {% else %}
                新预约!
            {% endif %}
        {% else %}
             您的顾客正在等待预约确认!
        {% endif %}
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}

{% endblock email_content %}
