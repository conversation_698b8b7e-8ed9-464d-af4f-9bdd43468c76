<p>{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}修改于{{ booking_info.previous_booking_date }} {{ booking_info.previous_booking_time }}{% endblock email_title %}的预约

{% block email_content %}
    {% call macros.paragraph() %}
        <b>{{ booking_info.booking_last_modified_by }}</b>将您的预约从

        <b>{{ booking_info.previous_booked_from_booking_date }}</b>从<b>{{ booking_info.previous_booked_from_booking_time }}</b>到<b>{{ booking_info.previous_booked_till_booking_time }}</b>
        修改为
        <b>{{ booking_info.booked_from_booking_date }}</b>从<b>{{ booking_info.booked_from_booking_time }}</b>到<b>{{ booking_info.booked_till_booking_time }}</b>。
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}
{% endblock email_content %}</p>
