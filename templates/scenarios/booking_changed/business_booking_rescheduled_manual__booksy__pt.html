<p>{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}{{ booking_info.customer_name }}: alterou sua reserva e está aguardando uma confirmação{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        Olá,
    {% endcall %}

    {% call macros.paragraph() %}
        <b>{{ booking_info.customer_name }}</b> remarcou seu atendimento de <b>{{ booking_info.booking_service_name_full }}</b> para uma outro horário.
    {% endcall %}

    {% call macros.paragraph() %}
        Seu calendário está definido no modo manual, assim o cliente fica aguardando a confirmação do novo atendimento.
    {% endcall %}

    {% call macros.paragraph() %}
        <b>Nova data, não confirmada, de atendimento: </b> {{ booking_info.booking_date }} às: {{ booking_info.booking_time }}<br>
        {% if booking_info.previous_booking_date and booking_info.previous_booking_time %}
            <b>Data anterior de atendimento: </b> {{ booking_info.previous_booking_date }} às: {{ booking_info.previous_booking_time }}
        {% endif %}
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        Você pode confirmar este novo horário:
    {% endcall %}

    {% call macros.li_table() %}
        {% call macros.li() %}
            pelo site {% call macros.link(url=booking_info.business_webbiz_url) %}
                biz.booksy.com
            {% endcall %}
        {% endcall %}
        {% call macros.li() %}
            pelo aplicativo Booksy Biz de seu telefone: verifique no PAINEL GERENCIADOR, localize o cliente e aceite ou recuse o novo horário de atendimento
        {% endcall %}
    {% endcall %}

    {% call macros.paragraph() %}
        Após a confirmação, o horário de atendimento anterior ficará disponível no calendário.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}
{% endblock email_content %}</p>
