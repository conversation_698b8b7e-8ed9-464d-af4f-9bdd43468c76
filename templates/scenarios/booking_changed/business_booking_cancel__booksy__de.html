{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}{{ booking_info.customer_name }}: hat den Termin am: {{ booking_info.booking_date }} {{ booking_info.booking_time }}{% endblock email_title %}storniert.

{% block email_content %}
    {% call macros.paragraph() %}
        <b>{{ booking_info.customer_name }}</b>
        hat den Termin für
        <b>{{ booking_info.booking_service_name_full }}</b>
        am: <b>{{ booking_info.booking_date }}</b> um <b>{{ booking_info.booking_time }}</b> storniert.
    {% endcall %}

    {% call macros.paragraph() %}
        Der Termin wurde aus deinem Kalender entfernt und steht wieder für eine neue Buchung zur Verfügung.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}
{% endblock email_content %}
