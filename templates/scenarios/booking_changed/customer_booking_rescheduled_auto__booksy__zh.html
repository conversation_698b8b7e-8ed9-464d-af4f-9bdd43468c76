{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}{{ booking_info.business_name }}已修改了您的预约{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        <b>{{ booking_info.business_name }}</b>已修改了您于
        <b>{{ booking_info.previous_booking_date }}</b> 在<b>{{ booking_info.previous_booking_time }}</b>的预约。
    {% endcall %}

    {% call macros.paragraph() %}
        若需要取消预约，您可以
        {% if booking_info.customer_has_account %}
           通过如下方式操作：
            {% call macros.link(url=booking_info.booking_marketplace_url) %}
                booksy.com
            {% endcall %}.
        {% else %}
           或呼叫
            {{ booking_info.business_phone }}.
        {% endif %}
    {% endcall %}

    {{ macros.booking_info(booking_info, to='C') }}
{% endblock email_content %}
