{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}您的预约正在等待确认{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        您好,
    {% endcall %}

    {% call macros.paragraph() %}
        <b>{{ booking_info.business_name }}</b>
        已收到了您的如下预约请求:
        <b>{{ booking_info.booking_service_name_full }}</b>服务，
        于<b>{{ booking_info.booking_date }}</b>在<b>{{ booking_info.booking_time }}</b>。
    {% endcall %}

    {% call macros.paragraph() %}
        商家必须确认每一个预约请求。
        您将在另一份电邮中收到预约状态提示。 
    {% endcall %}

    {{ macros.booking_info(booking_info, to='C') }}

    {% call macros.pos_add_card_infobox(link=booking_info.customer_add_card_link) %}{% endcall %}
{% endblock email_content %}
