{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}リマインダー、以下の予約が入っています: {{ booking_info.business_name }}{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        こんにちは,
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        あなたの以下の予約について、リマインドさせて頂きます {{ booking_info.booking_date }} 場所 {{ booking_info.booking_time }} in {{ booking_info.business_name }}.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='C', show_changed=False) }}

    {% call macros.pos_add_card_infobox(link=booking_info.customer_add_card_link) %}{% endcall %}

    {% call macros.paragraph() %}
        もし、この予約の取消や再スケージュールの必要がある場合
        {% call macros.link(url=booking_info.booking_marketplace_url) %}
            click here
        {% endcall %}.
    {% endcall %}
{% endblock email_content %}
