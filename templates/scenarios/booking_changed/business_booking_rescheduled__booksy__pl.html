<p>{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {% if booking_info.business_is_automatic %}
        {{ booking_info.customer_name }}: z<PERSON><PERSON><PERSON> rezerwację
    {% else %}
        {{ booking_info.customer_name }}: zmie<PERSON>ł rezerwację i czeka na potwierdzenie
    {% endif %}
{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        <b>{{ booking_info.customer_name }}</b> prz<PERSON>unął swoją wizytę {{ booking_info.booking_service_name_full }} z dnia
        <b>{{ booking_info.previous_booking_date }}</b>
        <b>{{ booking_info.previous_booking_time }}</b>
        na inny termin.
    {% endcall %}

    {% if booking_info.business_is_automatic %}
        {% call macros.paragraph() %}
            <b>Nowy termin wizyty:</b> 
        {% endcall %}
    {% else %}
        {% call macros.paragraph() %}
            Ponie<PERSON>ż masz ustawiony kalendarz w tryb manualny klient czeka na potwierdzenie nowego terminu.
        {% endcall %}

        {% call macros.paragraph(bold=True) %}
            Możesz potwierdzić ten termin za pomocą:
        {% endcall %}

        {% call macros.li_table() %}
            {% call macros.li() %}
                strony internetowej
                {% call macros.link(url=booking_info.business_webbiz_url) %}
                    biz.booksy.com
                {% endcall %}
            {% endcall %}
            {% call macros.li() %}
                aplikacji na telefonie: sprawdź zakładkę „Tablica”, znajdź klienta i potwierdź lub odrzuć nowy termin wizyty
            {% endcall %}
        {% endcall %}

        {% call macros.paragraph() %}
            Po potwierdzeniu poprzedni termin wizyty będzie uwolniony i będzie ponownie czekać na rezerwację innego klienta.
        {% endcall %}
    {% endif %}

    {{ macros.booking_info(booking_info, to='B') }}
{% endblock email_content %}</p>
