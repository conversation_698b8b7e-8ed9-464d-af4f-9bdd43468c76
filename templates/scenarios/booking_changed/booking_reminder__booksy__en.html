{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}Reminder about the appointment with: {{ booking_info.business_name }}{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        Hello,
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        We'd like to remind you about your appointment on {{ booking_info.booking_date }} at {{ booking_info.booking_time }} at {{ booking_info.business_name }}.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='C', show_changed=False) }}

    {% call macros.pos_add_card_infobox(link=booking_info.customer_add_card_link) %}{% endcall %}

    {% call macros.paragraph() %}
        If you need to reschedule or cancel the appointment
        {% call macros.link(url=booking_info.booking_marketplace_url) %}
            click here
        {% endcall %}.
    {% endcall %}
{% endblock email_content %}
