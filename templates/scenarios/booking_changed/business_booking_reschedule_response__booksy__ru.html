{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {{ booking_info.customer_name }}:
    {% if reschedule_confirmed %}подтверждено{% else %}отклонено{% endif %}
    предложено новое время визита
{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        Клиент <b>{{ booking_info.customer_name }}</b>
        {% if reschedule_confirmed %}<b>подтвердил</b>{% else %}<b>отклонил</b>{% endif %}
        предложенное вами новое время визита.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}

    {% call macros.paragraph() %}
        Изменения обновлены в календаре.
    {% endcall %}
{% endblock email_content %}
