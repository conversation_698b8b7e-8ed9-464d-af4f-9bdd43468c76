{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}Twoja prośba o przesunięcie rezerwacji w {{ booking_info.business_name }} została przesłana{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        Dzień dobry,
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        Twoja prośba o zmianę terminu wizyty w {{ booking_info.business_name }} została przesłana i czeka na akceptację.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='C') }}
{% endblock email_content %}
