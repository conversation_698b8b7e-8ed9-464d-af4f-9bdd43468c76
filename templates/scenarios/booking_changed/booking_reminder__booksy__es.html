{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}Recordatorio acerca de la cita con: {{ booking_info.business_name }}{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        Hola.
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        Te recordamos que tienes una cita el {{ booking_info.booking_date }} a las {{ booking_info.booking_time }} en {{ booking_info.business_name }}.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='C', show_changed=False) }}

    {% call macros.pos_add_card_infobox(link=booking_info.customer_add_card_link) %}{% endcall %}

    {% call macros.paragraph() %}
        Si necesitas reprogramarla o cancelarla,
        {% call macros.link(url=booking_info.booking_marketplace_url) %}
            haz clic aquí
        {% endcall %}.
    {% endcall %}
{% endblock email_content %}
