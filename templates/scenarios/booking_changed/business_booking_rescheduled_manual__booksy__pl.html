<p>{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}{{ booking_info.customer_name }}: zmienił rezerwację i czeka na potwierdzenie{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        Dzie<PERSON> dobry,
    {% endcall %}

    {% call macros.paragraph() %}
        <b>{{ booking_info.customer_name }}</b> przesunął swoją wizytę <b>{{ booking_info.booking_service_name_full }}</b> na inny termin.
    {% endcall %}

    {% call macros.paragraph() %}
        Ponieważ masz ustawiony kalendarz w tryb manualny klient czeka na potwierdzenie nowego terminu.
    {% endcall %}

    {% call macros.paragraph() %}
        <b>Nowy, niepotwierdzony termin wizyty:</b> {{ booking_info.booking_date }} o godzinie {{ booking_info.booking_time }}<br>
        {% if booking_info.previous_booking_date and booking_info.previous_booking_time %}
            <b>Poprzedni termin wizyty:</b> {{ booking_info.previous_booking_date }} o godzinie {{ booking_info.previous_booking_time }}
        {% endif %}
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        Możesz potwierdzić ten termin za pomocą:
    {% endcall %}

    {% call macros.li_table() %}
        {% call macros.li() %}
            strony internetowej
            {% call macros.link(url=booking_info.business_webbiz_url) %}
                biz.booksy.com
            {% endcall %}
        {% endcall %}
        {% call macros.li() %}
            aplikacji na telefonie: sprawdź zakładkę „Tablica”, znajdź klienta i potwierdź lub odrzuć nowy termin wizyty
        {% endcall %}
    {% endcall %}

    {% call macros.paragraph() %}
        Po potwierdzeniu poprzedni termin wizyty będzie uwolniony i będzie ponownie czekać na rezerwację innego klienta.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}
{% endblock email_content %}</p>
