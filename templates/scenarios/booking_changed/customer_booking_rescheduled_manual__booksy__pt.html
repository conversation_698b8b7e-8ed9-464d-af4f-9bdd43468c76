{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}O seu pedido para alteração de reserva com {{ booking_info.business_name }} foi enviado{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        Ol<PERSON>,
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        O seu pedido de reagendamento de reserva com: {{ booking_info.business_name }} foi enviado e está aguardando aprovação.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='C') }}
{% endblock email_content %}
