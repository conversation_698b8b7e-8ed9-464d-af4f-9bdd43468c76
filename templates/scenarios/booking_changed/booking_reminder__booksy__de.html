{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}Erinnerung an den Termin bei: {{ booking_info.business_name }}{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        Hallo,
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        Wir möchten dich gern an deinen Termin am {{ booking_info.booking_date }} um {{ booking_info.booking_time }} bei {{ booking_info.business_name }} erinnern.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='C', show_changed=False) }}

    {% call macros.pos_add_card_infobox(link=booking_info.customer_add_card_link) %}{% endcall %}

    {% call macros.paragraph() %}
        Wenn du den Termin verschieben oder stornieren musst
        {% call macros.link(url=booking_info.booking_marketplace_url) %}
            click here
        {% endcall %}.
    {% endcall %}
{% endblock email_content %}
