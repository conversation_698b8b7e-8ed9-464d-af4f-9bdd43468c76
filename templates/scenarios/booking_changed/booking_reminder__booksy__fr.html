{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}Votre rendez-vous au salon: {{ booking_info.business_name }}{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        Bonjour,
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        Nous vous rappelons votre rendez-vous le {{ booking_info.booking_date }} à {{ booking_info.booking_time }} au salon {{ booking_info.business_name }}.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='C', show_changed=False) }}

    {% call macros.pos_add_card_infobox(link=booking_info.customer_add_card_link) %}{% endcall %}

    {% call macros.paragraph() %}
        Pour reporter ou annuler un rendez-vous,
        {% call macros.link(url=booking_info.booking_marketplace_url) %}
            cliquez ici
        {% endcall %}.
    {% endcall %}
{% endblock email_content %}
