<p>{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}Changed appointment on: {{ booking_info.previous_booking_date }} {{ booking_info.previous_booking_time }}{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        <b>{{ booking_info.booking_last_modified_by }}</b> changed your appointment from

        <b>{{ booking_info.previous_booked_from_booking_date }}</b> at <b>{{ booking_info.previous_booked_from_booking_time }}</b> till <b>{{ booking_info.previous_booked_till_booking_time }}</b>
        to
        <b>{{ booking_info.booked_from_booking_date }}</b> at <b>{{ booking_info.booked_from_booking_time }}</b> till <b>{{ booking_info.booked_till_booking_time }}</b>.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}
{% endblock email_content %}</p>
