<p>{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}Cita cambiada el: {{ booking_info.previous_booking_date }} {{ booking_info.previous_booking_time }}{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        <b>{{ booking_info.booking_last_modified_by }}</b> cambió tu cita del

        <b>{{ booking_info.previous_booked_from_booking_date }}</b> a las <b>{{ booking_info.previous_booked_from_booking_time }}</b> hasta <b>{{ booking_info.previous_booked_till_booking_time }}</b>
        para el
        <b>{{ booking_info.booked_from_booking_date }}</b> a las <b>{{ booking_info.booked_from_booking_time }}</b> hasta <b>{{ booking_info.booked_till_booking_time }}</b>
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}
{% endblock email_content %}</p>
