{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}Twoja rezerwacja czeka na potwierdzenie{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        <PERSON><PERSON><PERSON> dobry,
    {% endcall %}

    {% call macros.paragraph() %}
        Firma <b>{{ booking_info.business_name }}</b>
        otrzymała Twoją prośbę o wizytę na usługę 
        <b>{{ booking_info.booking_service_name_full }}</b>
        w dniu <b>{{ booking_info.booking_date }}</b> o godzinie <b>{{ booking_info.booking_time }}</b>.
    {% endcall %}

    {% call macros.paragraph() %}
        Usługodawca chce potwierdzać każdą rezerwację indywidualnie.
        Ostateczne potwierdzenie rezerwacji otrzymasz w osobnym mailu.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='C') }}

    {% call macros.pos_add_card_infobox(link=booking_info.customer_add_card_link) %}{% endcall %}
{% endblock email_content %}

