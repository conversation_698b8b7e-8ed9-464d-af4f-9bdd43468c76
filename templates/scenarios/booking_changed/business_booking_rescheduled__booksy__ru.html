{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {% if booking_info.business_is_automatic %}
        {{ booking_info.customer_name }}: изменил(а) свое бронирование
    {% else %}
        {{ booking_info.customer_name }}: внес(ла) изменения в свое бронирование и ожидает подтверждения
    {% endif %}
{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        <b>{{ booking_info.customer_name }}</b> перенес(ла) свой визит для оказания следующих услуг: <b>{{ booking_info.booking_service_name_full }}</b> на другое время.
    {% endcall %}

    {% if booking_info.business_is_automatic %}
        {% call macros.paragraph() %}
            <b>Новая дата визита:</b> 
        {% endcall %}
    {% else %}
        {% call macros.paragraph() %}
            Ваш календарь настроен на ручной режим, поэтому клиент ожидает вашего подтверждения нового бронирования.
        {% endcall %}

        {% call macros.paragraph(bold=True) %}
            Вы можете подтвердить это новое время:
        {% endcall %}

        {% call macros.li_table() %}
            {% call macros.li() %}
                через веб-сайт
                {% call macros.link(url=booking_info.business_webbiz_url) %}
                    biz.booksy.com
                {% endcall %}
            {% endcall %}
            {% call macros.li() %}
                через приложение Booksy Biz на своем телефоне: проверьте ПАНЕЛЬ, найдите клиента и примите или отклоните новое время визита
            {% endcall %}
        {% endcall %}

        {% call macros.paragraph() %}
            После вашего подтверждения предыдущее время визита станет доступным для бронирования в календаре.
        {% endcall %}
    {% endif %}

    {{ macros.booking_info(booking_info, to='B') }}
{% endblock email_content %}
