<p>{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {{ booking_info.customer_name }}:
    {% if reschedule_confirmed %}a confirmé{% else %}a décliné{% endif %}
    l'heure proposée pour le nouveau rendez-vous
{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        Votre client <b>{{ booking_info.customer_name }}</b>
        {% if reschedule_confirmed %}<b>a confirmé</b>{% else %}<b>a décliné</b>{% endif %}
        l'heure proposée pour le nouveau rendez-vous.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}

    {% call macros.paragraph() %}
        Les modifications ont été ajoutées à l'agenda.
    {% endcall %}
{% endblock email_content %}</p>
