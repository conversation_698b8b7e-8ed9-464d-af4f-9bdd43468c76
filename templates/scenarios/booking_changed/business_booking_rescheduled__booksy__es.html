<p>{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {% if booking_info.business_is_automatic %}
        {{ booking_info.customer_name }}: ha modificado su reserva
    {% else %}
        {{ booking_info.customer_name }}: ha modificado su reserva y espera confirmación
    {% endif %}
{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        <b>{{ booking_info.customer_name }}</b> ha modificado la hora de su cita.
    {% endcall %}


    {% if booking_info.business_is_automatic %}
        {% call macros.paragraph() %}
            <b>Nueva fecha de la cita:</b> 
        {% endcall %}
    {% else %}
        {% call macros.paragraph() %}
            El calendario está configurado en modo manual, así que el cliente está esperando que le confirmes la nueva cita.
        {% endcall %}

        {% call macros.paragraph(bold=True) %}
            Puedes confirmar esta nueva hora:
        {% endcall %}

        {% call macros.li_table() %}
            {% call macros.li() %}
                A través del sitio web <a href="%7B%7B%20booking_info.business_webbiz_url%20%7D%7D" style="color: #26cbc5; text-decoration: underline;">biz.booksy.com</a>
            {% endcall %}
            {% call macros.li() %}
                A través de la aplicación Booksy Biz de tu teléfono: comprueba el PANEL, busca al cliente y acepta o rechaza la nueva hora de la cita
            {% endcall %}
        {% endcall %}

        {% call macros.paragraph() %}
            Tras la confirmación, la hora anterior de la cita quedará libre en el calendario.
        {% endcall %}
    {% endif %}

    {{ macros.booking_info(booking_info, to='B') }}
{% endblock email_content %}</p>
