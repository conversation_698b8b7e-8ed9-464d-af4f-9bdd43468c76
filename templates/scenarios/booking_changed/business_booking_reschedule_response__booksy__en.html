<p>{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {{ booking_info.customer_name }}:
    {% if reschedule_confirmed %}confirmed{% else %}rejected{% endif %}
    the new appointment time proposed
{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        Client <b>{{ booking_info.customer_name }}</b>
        {% if reschedule_confirmed %}<b>confirmed</b>{% else %}<b>rejected</b>{% endif %}
        your new appointment time proposed.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}

    {% call macros.paragraph() %}
        The changes have been updated in the calendar.
    {% endcall %}
{% endblock email_content %}</p>
