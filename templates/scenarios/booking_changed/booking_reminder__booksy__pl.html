{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}Przypomnienie o wizycie - {{ booking_info.business_name }}{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        Dzie<PERSON> dobry,
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        Przypominamy o Twojej wizycie w {{ booking_info.booking_date }} o godzinie {{ booking_info.booking_time }} - {{ booking_info.business_name }}.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='C', show_changed=False) }}

    {% call macros.pos_add_card_infobox(link=booking_info.customer_add_card_link) %}{% endcall %}

    {% call macros.paragraph() %}
        Je<PERSON><PERSON> nie moż<PERSON>z się pojawić prosimy
        {% call macros.link(url=booking_info.booking_marketplace_url) %}
            od<PERSON><PERSON><PERSON> wizytę lub wybierz inny termin
        {% endcall %}.
    {% endcall %}
{% endblock email_content %}
