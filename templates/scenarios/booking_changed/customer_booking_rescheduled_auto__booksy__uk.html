<p>{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}{{ booking_info.business_name }} ваш візит перенесено{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        <b>{{ booking_info.business_name }}</b> переносить ваш візит із
        <b>{{ booking_info.previous_booking_date }}</b> о <b>{{ booking_info.previous_booking_time }}</b>.
    {% endcall %}

    {% call macros.paragraph() %}
        Якщо хочете скасувати, ви можете

        {% if booking_info.customer_has_account %}
            зробити це в
            {% call macros.link(url=booking_info.booking_marketplace_url) %}
                Booksy.
            {% endcall %}.
        {% else %}
            дзвонити за номером
            {{ booking_info.business_phone }}.
        {% endif %}
    {% endcall %}

    {{ macros.booking_info(booking_info, to='C') }}
{% endblock email_content %}</p>
