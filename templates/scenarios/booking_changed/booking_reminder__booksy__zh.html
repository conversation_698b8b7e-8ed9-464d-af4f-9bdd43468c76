{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}预约提醒: {{ booking_info.business_name }}{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        您好,
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        我们想提醒您的预约于{{ booking_info.booking_date }}在{{ booking_info.booking_time }}与{{ booking_info.business_name }}的预约。
    {% endcall %}

    {{ macros.booking_info(booking_info, to='C', show_changed=False) }}

    {% call macros.pos_add_card_infobox(link=booking_info.customer_add_card_link) %}{% endcall %}

    {% call macros.paragraph() %}
        如果您需要重新预约或取消预约
        {% call macros.link(url=booking_info.booking_marketplace_url) %}
            点击此处
        {% endcall %}.
    {% endcall %}
{% endblock email_content %}
