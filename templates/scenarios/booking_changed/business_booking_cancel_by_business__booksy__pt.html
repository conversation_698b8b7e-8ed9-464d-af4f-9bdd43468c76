{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}Agendamento cancelado em: {{ booking_info.booking_date }} {{ booking_info.booking_time }}{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        <b>{{ booking_info.booking_last_modified_by }}</b>
        cancelou o seu atendimento para <b>{{ booking_info.customer_name }}</b>
        <b>{{ booking_info.booking_service_name_full }}</b>
        em: <b>{{ booking_info.booking_date }}</b> às <b>{{ booking_info.booking_time }}</b>.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}
{% endblock email_content %}
