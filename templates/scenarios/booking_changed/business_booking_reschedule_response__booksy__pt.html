<p>{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {{ booking_info.customer_name }}:
    {% if reschedule_confirmed %}confirmado{% else %}recusado{% endif %}
    o novo horário de atendimento proposto
{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        Cliente <b>{{ booking_info.customer_name }}</b>
        {% if reschedule_confirmed %}<b>confirmado</b>{% else %}<b>recusado</b>{% endif %}
        seu novo horário de atendimento proposto.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}

    {% call macros.paragraph() %}
        As alterações foram atualizadas no calendário.
    {% endcall %}
{% endblock email_content %}</p>
