<p>{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {% if booking_info.business_is_automatic %}
        {{ booking_info.customer_name }}:修改了预约
    {% else %}
        {{ booking_info.customer_name }}:修改了预约，正在等待确认
    {% endif %}
{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        <b>{{ booking_info.customer_name }}</b>已修改了预约时间。
    {% endcall %}

    {% if booking_info.business_is_automatic %}
        {% call macros.paragraph() %}
            <b>新的预约日期:</b> 
        {% endcall %}
    {% else %}
        {% call macros.paragraph() %}
            您的日历设置为手动模式，所以顾客正在等待您确认新预约。
        {% endcall %}

        {% call macros.paragraph(bold=True) %}
            您可以通过如下方式确认新时间:
        {% endcall %}

        {% call macros.li_table() %}
            {% call macros.li() %}
               网页
                {% call macros.link(url=booking_info.business_webbiz_url) %}
                    biz.booksy.com
                {% endcall %}
            {% endcall %}
            {% call macros.li() %}
                您手机上的Booksy（商家版）APP：检查首页，找到顾客，然后接受或拒绝新的预约时间
            {% endcall %}
        {% endcall %}

        {% call macros.paragraph() %}
            您确认后，之前的预约时间将在日历上标为“可服务”。
        {% endcall %}
    {% endif %}

    {{ macros.booking_info(booking_info, to='B') }}
{% endblock email_content %}</p>
