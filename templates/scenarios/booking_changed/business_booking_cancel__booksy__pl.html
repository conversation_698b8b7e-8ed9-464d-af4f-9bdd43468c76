{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}{{ booking_info.customer_name }}: odwo<PERSON>ł wizytę z dnia {{ booking_info.booking_date }} {{ booking_info.booking_time }}{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        Klient <b>{{ booking_info.customer_name }}</b>
        odwołał swoją usługę
        <b>{{ booking_info.booking_service_name_full }}</b>
        w dniu <b>{{ booking_info.booking_date }}</b> o godzinie <b>{{ booking_info.booking_time }}</b>.
    {% endcall %}

    {% call macros.paragraph() %}
        Dzięki Booksy wiesz, że ten klient nie przyjdzie na pewno na umówioną wizytę i możesz w tym czasie obsłużyć innego klienta.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}
{% endblock email_content %}
