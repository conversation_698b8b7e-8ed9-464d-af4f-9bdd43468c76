<p>{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}{{ booking_info.customer_name }}: ha modificado su reserva y espera confirmación{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        Hola.
    {% endcall %}

    {% call macros.paragraph() %}
        <b>{{ booking_info.customer_name }}</b> ha modificado la hora de su cita para <b>{{ booking_info.booking_service_name_full }}</b>.
    {% endcall %}

    {% call macros.paragraph() %}
        El calendario está configurado en modo manual, así que el cliente está esperando que le confirmes la nueva cita.
    {% endcall %}

    {% call macros.paragraph() %}
        <b>Nueva fecha de la cita sin confirmar:</b> {{ booking_info.booking_date }} a las: {{ booking_info.booking_time }}<br>
        {% if booking_info.previous_booking_date and booking_info.previous_booking_time %}
            <b>Fecha anterior de la cita:</b> {{ booking_info.previous_booking_date }} a las: {{ booking_info.previous_booking_time }}
        {% endif %}
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        Puedes confirmar esta nueva hora:
    {% endcall %}

    {% call macros.li_table() %}
        {% call macros.li() %}
            A través del sitio web
            {% call macros.link(url=booking_info.business_webbiz_url) %}
                biz.booksy.com
            {% endcall %}
        {% endcall %}
        {% call macros.li() %}
            A través de la aplicación Booksy Biz de tu teléfono: comprueba el PANEL, busca al cliente y acepta o rechaza la nueva hora de la cita
        {% endcall %}
    {% endcall %}

    {% call macros.paragraph() %}
        Tras la confirmación, la hora anterior de la cita quedará libre en el calendario.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}
{% endblock email_content %}</p>
