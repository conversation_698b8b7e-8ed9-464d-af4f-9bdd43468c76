<p>{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {{ booking_info.customer_name }}:
    hat den vorgeschlagenen neuen Termin
    {% if reschedule_confirmed %}bestätigt{% else %}abgelehnt{% endif %}
{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        Der Kunde <b>{{ booking_info.customer_name }}</b>
        hat den vorgeschlagenen neuen Termin
        <b>{% if reschedule_confirmed %}bestätigt{% else %}abgelehnt{% endif %}</b>.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}

    {% call macros.paragraph() %}
        Der Kalender wurde mit den Änderungen aktualisiert.
    {% endcall %}
{% endblock email_content %}</p>
