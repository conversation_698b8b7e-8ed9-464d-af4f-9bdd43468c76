{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}Deine Anfrage zur Änderung des Termins bei {{ booking_info.business_name }} wurde versandt{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        Hall<PERSON>,
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        Deine Anfrage zur Verschiebung des Termins bei: {{ booking_info.business_name }} wurde versandt und wartet auf Bestätigung.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='C') }}
{% endblock email_content %}
