{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {% if booking_info.customer_name %}
        {{ booking_info.customer_name }}: neue Buchung {{ booking_info.booking_date }} {{ booking_info.booking_time }}
    {% else %}
        Neue Buchung {{ booking_info.booking_date }} {{ booking_info.booking_time }}
    {% endif %}
{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        {% if booking_info.business_is_automatic or booking_info.booking_by_business %}
            {{ booking_info.customer_name }}: neue Buchung
        {% else %}
            Dein Kunde wartet auf die Buchungsbestätigung!
        {% endif %}
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}

{% endblock email_content %}
