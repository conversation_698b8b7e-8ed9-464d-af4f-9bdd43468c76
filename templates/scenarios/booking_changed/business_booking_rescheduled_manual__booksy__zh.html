<p>{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}{{ booking_info.customer_name }}:修改了预约，并正在等待您的确认{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        您好,
    {% endcall %}

    {% call macros.paragraph() %}
        <b>{{ booking_info.customer_name }}</b>已修改了<b>{{ booking_info.booking_service_name_full }}</b> 的预约时间。
    {% endcall %}

    {% call macros.paragraph() %}
          您的日历设置为手动模式，所以顾客正在等待您确认新预约。
    {% endcall %}

    {% call macros.paragraph() %}
        <b>新的未确认预约于</b> {{ booking_info.booking_date }}在{{ booking_info.booking_time }}<br>
        {% if booking_info.previous_booking_date and booking_info.previous_booking_time %}
            <b>之前的预约于</b> {{ booking_info.previous_booking_date }}在{{ booking_info.previous_booking_time }}
        {% endif %}
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        您可通过如下方式确认该新时间:
    {% endcall %}

    {% call macros.li_table() %}
        {% call macros.li() %}
           网页
            {% call macros.link(url=booking_info.business_webbiz_url) %}
                biz.booksy.com
            {% endcall %}
        {% endcall %}
        {% call macros.li() %}
             您手机上的Booksy（商家版）APP：检查首页，找到顾客，然后接受或拒绝新的预约时间
        {% endcall %}
    {% endcall %}

    {% call macros.paragraph() %}
        您确认后，之前的预约时间将在日历上标为“可服务”。
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}
{% endblock email_content %}</p>
