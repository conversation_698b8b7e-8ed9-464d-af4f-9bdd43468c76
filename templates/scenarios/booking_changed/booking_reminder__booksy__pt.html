{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}Lembrete sobre o atendimento com: {{ booking_info.business_name }}{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        Olá,
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        Gostaríamos de lembrá-lo sobre o seu atendimento no dia  {{ booking_info.booking_date }} às {{ booking_info.booking_time }} no(a) {{ booking_info.business_name }}.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='C', show_changed=False) }}

    {% call macros.pos_add_card_infobox(link=booking_info.customer_add_card_link) %}{% endcall %}

    {% call macros.paragraph() %}
        Se você precisar reagendar ou cancelar o atendimento
        {% call macros.link(url=booking_info.booking_marketplace_url) %}
            click here
        {% endcall %}.
    {% endcall %}
{% endblock email_content %}
