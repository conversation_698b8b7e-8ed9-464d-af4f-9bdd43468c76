<p>{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {{ booking_info.customer_name }}:
    {% if reschedule_confirmed %}ha confirmado{% else %}ha rechazado{% endif %}
    el cambio de hora propuesto para la cita
{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        El cliente <b>{{ booking_info.customer_name }}</b>
        {% if reschedule_confirmed %}<b>ha confirmado</b>{% else %}<b>ha rechazado</b>{% endif %}
        el cambio de hora de cita que has propuesto.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}

    {% call macros.paragraph() %}
        Los cambios se han introducido en el calendario.
    {% endcall %}
{% endblock email_content %}</p>
