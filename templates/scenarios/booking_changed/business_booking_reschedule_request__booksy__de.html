<p>{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}hat den Termin am: {{ booking_info.previous_booking_date }} {{ booking_info.previous_booking_time }}{% endblock email_title %} geändert.

{% block email_content %}
    {% call macros.paragraph() %}
        <b>{{ booking_info.booking_last_modified_by }}</b> hat deinen Termin von

        <b>{{ booking_info.previous_booked_from_booking_date }}</b> von <b>{{ booking_info.previous_booked_from_booking_time }}</b> bis <b>{{ booking_info.previous_booked_till_booking_time }}</b>
        auf
        <b>{{ booking_info.booked_from_booking_date }}</b> von <b>{{ booking_info.booked_from_booking_time }}</b> bis <b>{{ booking_info.booked_till_booking_time }}</b> geändert.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}
{% endblock email_content %}</p>
