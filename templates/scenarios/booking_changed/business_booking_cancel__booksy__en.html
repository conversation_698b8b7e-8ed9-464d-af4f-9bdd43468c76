{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}{{ booking_info.customer_name }}: cancelled  appointment on: {{ booking_info.booking_date }} {{ booking_info.booking_time }}{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        <b>{{ booking_info.customer_name }}</b>
        cancelled appointment for
        <b>{{ booking_info.booking_service_name_full }}</b>
        on: <b>{{ booking_info.booking_date }}</b> at <b>{{ booking_info.booking_time }}</b>.
    {% endcall %}

    {% call macros.paragraph() %}
        The appointment has been removed from your calendar and made available for another booking.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}
{% endblock email_content %}
