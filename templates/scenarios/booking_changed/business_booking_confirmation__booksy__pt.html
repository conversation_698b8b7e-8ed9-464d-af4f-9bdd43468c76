{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {% if booking_info.customer_name %}
        {{ booking_info.customer_name }}: nova reserva {{ booking_info.booking_date }} {{ booking_info.booking_time }}
    {% else %}
        Nova reserva {{ booking_info.booking_date }} {{ booking_info.booking_time }}
    {% endif %}
{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        {% if booking_info.business_is_automatic or booking_info.booking_by_business %}
            {{ booking_info.customer_name }}: nova reserva
        {% else %}
            O seu cliente está aguardando a confirmação de reserva!
        {% endif %}
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}

{% endblock email_content %}
