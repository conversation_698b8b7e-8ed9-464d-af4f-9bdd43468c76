<p>{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}{{ booking_info.customer_name }}: a modifié son rendez-vous et est en attente de votre confirmation{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        Bonjour,
    {% endcall %}

    {% call macros.paragraph() %}
        <b>{{ booking_info.customer_name }}</b> a reporté le rendez-vous pour <b>{{ booking_info.booking_service_name_full }}</b> à un horaire différent.
    {% endcall %}

    {% call macros.paragraph() %}
        Votre agenda est réglé en mode manuel, le client attend votre confirmation pour son nouveau rendez-vous.
    {% endcall %}

    {% call macros.paragraph() %}
        <b>Nouvelle date de rendez-vous non confirmée:</b> le {{ booking_info.booking_date }} à {{ booking_info.booking_time }}<br>
        {% if booking_info.previous_booking_date and booking_info.previous_booking_time %}
            <b>Date du rendez-vous précédent:</b> le {{ booking_info.previous_booking_date }} à {{ booking_info.previous_booking_time }}
        {% endif %}
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        Confirmez ce nouvel horaire
    {% endcall %}

    {% call macros.li_table() %}
        {% call macros.li() %}
            via le site web
            {% call macros.link(url=booking_info.business_webbiz_url) %}
                booksy.com/pro
            {% endcall %}
        {% endcall %}
        {% call macros.li() %}
            via l'application Booksy Biz sur votre téléphone : consultez votre agenda, trouvez le client et acceptez ou refusez la nouvelle heure de rendez-vous
        {% endcall %}
    {% endcall %}

    {% call macros.paragraph() %}
        Après confirmation, l'heure de rendez-vous précédente sera rendue disponible sur l'agenda.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}
{% endblock email_content %}</p>
