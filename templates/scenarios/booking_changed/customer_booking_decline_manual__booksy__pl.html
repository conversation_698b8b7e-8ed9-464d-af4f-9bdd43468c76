{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}{{ booking_info.business_name_short }} nie zaakceptował terminu wizyty{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        <PERSON><PERSON><PERSON> dobry,
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        Twoja zmiana terminu usługi {{ booking_info.booking_service_name_full }} - {{ booking_info.business_name }} w dniu {{ booking_info.booking_date }} o godzinie {{ booking_info.booking_time }}  nie została zaakceptowana.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='C', show_booking=False, show_map=False) }}

    {% call macros.button(url=booking_info.business_email_deeplink) %}
        Umów się na inny termin
    {% endcall %}

{% endblock email_content %}
