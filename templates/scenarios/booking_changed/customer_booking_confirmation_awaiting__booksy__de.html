{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}Deine Buchung wartet auf Bestätigung{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        Hall<PERSON>,
    {% endcall %}

    {% call macros.paragraph() %}
        <b>{{ booking_info.business_name }}</b>
        hat deine Anfrage zur Buchung eines Termins für
        <b>{{ booking_info.booking_service_name_full }}</b>
        am <b>{{ booking_info.booking_date }}</b> um <b>{{ booking_info.booking_time }} erhalten</b>.
    {% endcall %}

    {% call macros.paragraph() %}
        Der Anbieter muss jede Buchungsanfrage bestätigen.
        Du wirst mit einer gesonderten E-Mail über den Status deiner Buchung informiert. 
    {% endcall %}

    {{ macros.booking_info(booking_info, to='C') }}

    {% call macros.pos_add_card_infobox(link=booking_info.customer_add_card_link) %}{% endcall %}
{% endblock email_content %}
