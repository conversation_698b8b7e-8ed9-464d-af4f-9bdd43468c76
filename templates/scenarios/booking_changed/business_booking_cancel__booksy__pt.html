{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}{{ booking_info.customer_name }}: agendamento cancelado em: {{ booking_info.booking_date }} {{ booking_info.booking_time }}{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        <b>{{ booking_info.customer_name }}</b>
        agendamento cancelado para
        <b>{{ booking_info.booking_service_name_full }}</b>
        em: <b>{{ booking_info.booking_date }}</b> às <b>{{ booking_info.booking_time }}</b>.
    {% endcall %}

    {% call macros.paragraph() %}
        O agendamento foi removido do seu calendário e o horário está disponível para outro atendimento.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='B') }}
{% endblock email_content %}
