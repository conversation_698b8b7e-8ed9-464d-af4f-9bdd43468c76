{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}A sua reserva está aguardando confirmação{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        Ol<PERSON>,
    {% endcall %}

    {% call macros.paragraph() %}
        <b>{{ booking_info.business_name }}</b>
        recebeu o seu pedido para agendar um atendimento para:
        <b>{{ booking_info.booking_service_name_full }}</b>
        no dia <b>{{ booking_info.booking_date }}</b> às <b>{{ booking_info.booking_time }}</b>.
    {% endcall %}

    {% call macros.paragraph() %}
        O prestador de serviços deve confirmar cada agendamento.
        Você receberá o status de sua reserva em um e-mail separado. 
    {% endcall %}

    {{ macros.booking_info(booking_info, to='C') }}

    {% call macros.pos_add_card_infobox(link=booking_info.customer_add_card_link) %}{% endcall %}
{% endblock email_content %}
