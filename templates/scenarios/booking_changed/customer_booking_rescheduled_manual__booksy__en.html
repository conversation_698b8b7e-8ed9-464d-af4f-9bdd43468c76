{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}Your request to change the booking with {{ booking_info.business_name }} has been sent{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        Hello,
    {% endcall %}

    {% call macros.paragraph(bold=True) %}
        Your request to reschedule the booking with: {{ booking_info.business_name }} has been sent and is awaiting acceptance.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='C') }}
{% endblock email_content %}
