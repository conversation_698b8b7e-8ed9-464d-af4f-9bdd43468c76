<p>{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {% if booking_info.business_is_automatic %}
        {{ booking_info.customer_name }} a modifié sa réservation
    {% else %}
        {{ booking_info.customer_name }} a modifié sa réservation et est en attente de confirmation
    {% endif %}
{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        <b>{{ booking_info.customer_name }}</b> a reporté le rendez-vous pour <b>{{ booking_info.booking_service_name_full }}</b> a un horaire différent.
    {% endcall %}
    {% call macros.paragraph() %}
        <b>Date initiale du rendez-vous:</b> {{ booking_info.previous_booking_date }} at: {{ booking_info.previous_booking_time }}.
    {% endcall %}

    {% if booking_info.business_is_automatic %}
        {% call macros.paragraph() %}
            <b>Nouvelle date du rendez-vous:</b> 
        {% endcall %}
    {% else %}
        {% call macros.paragraph() %}
            Votre agenda est réglé en mode manuel, le client attend votre confirmation pour son nouveau rendez-vous.
        {% endcall %}

        {% call macros.paragraph(bold=True) %}
            Confirmez ce nouvel horaire
        {% endcall %}

        {% call macros.li_table() %}
            {% call macros.li() %}
                via le site
                {% call macros.link(url=booking_info.business_webbiz_url) %}
                    booksy.com/pro
                {% endcall %}
            {% endcall %}
            {% call macros.li() %}
                ou via l'application Booksy Biz sur votre téléphone: consultez votre agenda, trouvez votre client et confirmez ou déclinez le nouvel horaire de rendez-vous.
            {% endcall %}
        {% endcall %}

        {% call macros.paragraph() %}
            Une fois confirmé, l'heure du rendez-vous initial sera libérée et à nouveau disponible à la réservation sur votre agenda.
        {% endcall %}
    {% endif %}

    {{ macros.booking_info(booking_info, to='B') }}
{% endblock email_content %}</p>
