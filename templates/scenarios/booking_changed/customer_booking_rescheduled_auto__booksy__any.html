{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}{{ _("{business_name} has rescheduled your appointment").format(business_name=booking_info.business_name)}}{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        {{ _("<b>{business_name}</b> has rescheduled your appointment{additional_booking_info} from <b>{previous_booking_date}</b> at <b>{previous_booking_time}</b>.").format(business_name=booking_info.business_name, additional_booking_info=booking_info.additional_booking_info, previous_booking_date=booking_info.previous_booking_date, previous_booking_time=booking_info.previous_booking_time) }}
    {% endcall %}

    {% call macros.paragraph() %}
        {{ _("If you need to cancel, you can") }}

        {% if booking_info.customer_has_account %}
            {{ _("do so through") }}
            {% call macros.link(url=booking_info.booking_marketplace_url) %}
                {{ _("Booksy.") }}
            {% endcall %}.
        {% else %}
            {{ _("call {business_phone}.").format(business_phone=booking_info.business_phone) }}
        {% endif %}
    {% endcall %}

    {{ macros.booking_info(booking_info, to='C') }}
{% endblock email_content %}
