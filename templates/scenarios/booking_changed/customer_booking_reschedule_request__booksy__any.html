{% extends "email_base_customer__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}{{ _("Business has changed the date of your appointment and is waiting for confirmation") }}{% endblock email_title %}

{% block email_content %}

    {% call macros.paragraph() %}
        {{ _("Please go to") }}{% call macros.link(url=booking_info.booking_marketplace_url) %}
            {{ _("booksy.com") }}
        {% endcall %} {{ _("or the Booksy app to confirm or change the proposed date and time.") }}
        {{ _("If we don't receive confirmation of the proposed change, your appointment may be cancelled.") }}
    {% endcall %}

    {% call macros.paragraph() %}
        {{ _("New date of appointment{additional_booking_info} <b>{booking_date}</b> at: <b>{booking_time}</b>.").format(additional_booking_info=booking_info.additional_booking_info, booking_date=booking_info.booking_date, booking_time=booking_info.booking_time) }}
    {% endcall %}

    {{ macros.booking_info(booking_info, to='C') }}
{% endblock email_content %}
