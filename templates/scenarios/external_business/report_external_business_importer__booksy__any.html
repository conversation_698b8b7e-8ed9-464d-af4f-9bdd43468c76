{% extends "email_base_business__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {{ _('Import BListing businesses report') }}
{% endblock email_title %}

{% block email_content %}
    <div style="background-color:#f9f9f9; padding: 20px;">
        {% if error %}
        <div style="font: 300 18px 'Proxima Nova', Arial; color: #999; padding-bottom: 10px;">
            {{ _('This is generated report from B-Listing data import.') }}<br/>
            {{ _('An error occured during the import. Please check specification of each file data file.') }}<br/>
            {{ _('Probably the format of some data-file is changed.') }}<br/>
        </div>
        <table id="report_preview_table" class="table pre-td">
            <tr>
                <th>business_name</th>
                <th>error</th>
            </tr>
            {% for row in external_businesses %}
                {% if row.error %}
                    <tr>
                        <td style="background-color: #E62020; font-size: 14px; color: white; face: 'Verdana'">{{ row.business_name }}</td>
                        <td style="background-color: #E62020; font-size: 14px; color: white; face: 'Verdana'">{{ row.error }}</td>
                    </tr>
                {% endif %}
            {% endfor %}
        </table>
        {% else %}
        <div style="font: 300 18px 'Proxima Nova', Arial; color: #999; padding-bottom: 10px;">
            {{ _('This is generated report from B-Listing data import.') }}<br/>
            {{ _('{external_businesses} were processed and validated.').format(external_businesses=external_businesses|length) }}<br/>
            {{ _('Please be aware that some businesses may be imported with missing data due to validation process.') }}<br/>
        </div>
        {% endif %}
    </div>
{% endblock email_content %}
