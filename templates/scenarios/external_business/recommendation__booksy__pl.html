{% extends "email_base_plain__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}Ch<PERSON><PERSON> zarezerwować usługę przez Booksy{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        <PERSON><PERSON><PERSON><PERSON>,
    {% endcall %}

    {% call macros.paragraph() %}
        W<PERSON><PERSON><PERSON>, że nie jesteście jeszcze na Booksy.
    {% endcall %}

    {% call macros.paragraph() %}
        Jeś<PERSON> do<PERSON> to mógłbym umawiać się u Was na wizyty
        przez aplikacje na telefonie komórkowym :)
    {% endcall %}

    {% call macros.paragraph() %}
        Tu jest link do rejestracji:
        {% call macros.link(url=registration_url) %}{{ registration_url }}{% endcall %}
    {% endcall %}

    {% call macros.paragraph() %}
        Wystar<PERSON>y potwierdzić, że chcesz dołączyć.
    {% endcall %}

    {% call macros.paragraph() %}
        Pozdrawiam<br/>
        Wasz fan :)
    {% endcall %}

{% endblock email_content %}
