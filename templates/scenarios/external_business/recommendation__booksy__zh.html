{% extends "email_base_plain__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}我想通过Booksy预约{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        您好,
    {% endcall %}

    {% call macros.paragraph() %}
        我注意到你们还没有注册Booksy。
    {% endcall %}

    {% call macros.paragraph() %}
        如果能加入，让我与您预约，那就太棒了 :)
    {% endcall %}

    {% call macros.paragraph() %}
        这是您的主页链接:
        {% call macros.link(url=registration_url) %}{{ registration_url }}{% endcall %}
    {% endcall %}

    {% call macros.paragraph() %}
        请认领该主页，我会立即预约。
    {% endcall %}

    {% call macros.paragraph() %}
        祝好<br/>
        您的粉丝顾客{% block email_title %} :)
    {% endcall %}

{% endblock email_content %}
