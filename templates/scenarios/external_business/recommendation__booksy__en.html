{% extends "email_base_plain__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}I would like to book an appointment with you through Booksy{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        Hi,
    {% endcall %}

    {% call macros.paragraph() %}
        I've noticed you guys are not on Booksy yet.
    {% endcall %}

    {% call macros.paragraph() %}
        Would be cool if you could join so I can finally make bookings with you :)
    {% endcall %}

    {% call macros.paragraph() %}
        Here is a link to your profile:
        {% call macros.link(url=registration_url) %}{{ registration_url }}{% endcall %}
    {% endcall %}

    {% call macros.paragraph() %}
        Just claim it and I'll book right away.
    {% endcall %}

    {% call macros.paragraph() %}
        cheers<br/>
        Your fan customer :)
    {% endcall %}

{% endblock email_content %}
