{% extends "email_base_plain__booksy__any.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}Iさんが、 Booksyを通じて予約を希望しています{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        こんにちは,
    {% endcall %}

    {% call macros.paragraph() %}
        彼らが未だBooksyを使っていないことを、あなたに通知しています。
    {% endcall %}

    {% call macros.paragraph() %}
        私があなたに予約を入れられるように、あなたがBooksyに登録してくれると良いなぁ :)
    {% endcall %}

    {% call macros.paragraph() %}
        ここに、あなたのプロフィールへのリンクがあります:
        {% call macros.link(url=registration_url) %}{{ registration_url }}{% endcall %}
    {% endcall %}

    {% call macros.paragraph() %}
        ただ単純にそれを受け取ってください、直ぐに私が予約を入れます。
    {% endcall %}

    {% call macros.paragraph() %}
        おめでとうございます<br/>
        ななたのファンの顧客です　:)
    {% endcall %}

{% endblock email_content %}
