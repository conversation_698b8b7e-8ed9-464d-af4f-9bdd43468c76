{% extends "admin/base_site.html" %}
{% load i18n static admin_modify suit_tags admin_urls %}

{% block extrahead %}
    {{ block.super }}
    <script type="text/javascript" src="{% url 'admin:jsi18n' %}"></script>
    <script src="{% static 'admin/js/vendor/jquery/jquery.js' %}"></script>

    {% if is_braintree %}
        <script src="https://js.braintreegateway.com/web/dropin/1.23.0/js/dropin.min.js"></script>
    {% elif is_stripe %}
        <script src="https://js.stripe.com/v3/"></script>
    {% endif %}
{% endblock %}

{% block extrastyle %}
  {{ block.super }}
    <style>
        label {
            margin-top: 10px;
        }

        h2, h3 {
            margin-left: 0;
            padding-left: 0;
        }

        h2 {
            margin-bottom: 20px;
        }

        hr {
            border-top: 1px solid #9f9f9f;
        }

        .top-15 {
            margin-top: 15px;
        }

        .bottom-50 {
            margin-bottom: 50px;
        }

        .braintree-sheet {
            max-height: 800px;
        }

        table#transactions th {
            padding: 10px;
        }
        .checkbox
        {
            float:left;
            margin-right: 20px;
            margin-top: 6px;
        }
        .checkbox_name
        {
            float:left;
            margin-top: 3px;
            margin-right: 3px;
        }
    </style>
{% endblock %}

{% block title %}Add new payment method{% endblock title %}

{% block coltype %}{% if ordered_objects %}colMS{% else %}colM{% endif %}{% endblock %}

{% block content %}

    <h3>Add new payment method</h3>

{% if is_braintree %}
    <div id="payment_method_add">
        <div id="payment_method_alert" class="alert" style="display: none"></div>
        <div id="dropin-container"></div>
        <button id="submit-button" class="top-15">Add payment method</button>
        <script>
          var button = document.querySelector('#submit-button');
          braintree.dropin.create({
            authorization: '{{ client_token }}',
            container: '#dropin-container',
            card: {
              cvv: {
                required: true,
              },
              cardholderName: {
                required: true
              },
            },
            translations: {
              payWithCard: 'Credit card / debit card details'
            }
          }, function (createErr, instance) {
            button.addEventListener('click', function () {
              $('.custom_fields_group').removeClass('braintree-form__field-group--has-error');
              $('.custom_fields_group .braintree-form__field-error').html('');
              $('#payment_method_alert').html('Please wait...').addClass('alert-info').show();
              if (
              $('#custom_fields__street_address').val()===""
              || $('#custom_fields__postal_code').val()===""
              || $('#custom_fields__locality').val()===""
                ) {
              alert('Fields: "Address line 1", "Zip code" and "City" cannot be empty')
                    return false;
              }
              instance.requestPaymentMethod(function (requestPaymentMethodErr, payload) {
                if (requestPaymentMethodErr) {
                  $('#payment_method_alert')
                    .html('Please fill in data / correct errors below')
                    .removeClass('alert-info').addClass('alert-error');
                } else {
                  $.ajax({
                    url: "{% url 'admin:add_payment_method' business.id %}",
                    type: 'post',
                    data: JSON.stringify({
                      payment_method_type: 'C', // credit card
                      nonce: payload.nonce,
                      billing_address: {
                        street_address: $('#custom_fields__street_address').val(),
                        extended_address: $('#custom_fields__extended_address').val(),
                        postal_code: $('#custom_fields__postal_code').val(),
                        locality: $('#custom_fields__locality').val(),
                        country_name: $('#custom_fields__country_name').val(),
                      },
                    }),
                    contentType: 'application/json; charset=utf-8',
                    headers: {
                      'X-CSRFToken': '{{ csrf_token }}',
                    },
                    success: function (response) {
                      $('#payment_method_alert')
                        .html('Payment method was successfuly added to Braintree. Redirecting in 5 seconds ...')
                        .removeClass('alert-info').addClass('alert-success');
                      setTimeout(function () {
                          window.location.href = '{% url 'admin:billing_billingbusiness_change' business.id %}';
                          //location.reload(true);
                      }, 5000);
                    },
                    error: function (response) {
                      console.log(response);
                      var response_json = response.responseJSON || {};
                      var billing_address_errors = response_json['billing_address'] || {};
                      $('#payment_method_alert').html(JSON.stringify(response_json))
                        .removeClass('alert-info').addClass('alert-error');
                      $.each(billing_address_errors, function (key, value) {
                        var field_group = $('#custom_fields__' + key).closest('.custom_fields_group');
                        field_group.addClass('braintree-form__field-group--has-error');
                        field_group.find('.braintree-form__field-error').html(value[0]);
                      });
                    }
                  });
                }
              });
            });
            $('div[data-braintree-id="card"]').find('div.braintree-sheet__content')
              .append('{{ add_form_extra_fields }}')
            $('#custom_fields__locality, #custom_fields__street_address, #custom_fields__postal_code')
              .parents('label')
              .find('.braintree-form__label')
              .each(function () {
                $(this).html($(this).html() + '*');
              })
          });
        </script>
    </div>
{% elif is_stripe %}
        {% if client_token %}
        <form id="payment-form">
          <div id="message" class="alert" style="display: none">
            <!-- Display error message to your customers here -->
          </div>
          <div id="payment-element">
            <!-- Elements will create form elements here -->
          </div>
          <button id="submit">Add payment method</button>
          <div id="error-message">
            <!-- Display error message to your customers here -->
          </div>
        </form>

          <script>
            const stripe = Stripe('{{ public_api_key }}');
            const options = {
              clientSecret: '{{ client_token }}',
            };

            const elements = stripe.elements(options);
            const paymentElement = elements.create('payment');
            paymentElement.mount('#payment-element');

            const form = document.getElementById('payment-form');

            form.addEventListener('submit', async (event) => {
              event.preventDefault();

              const {error} = await stripe.confirmSetup({
                elements,
                confirmParams: {
                  return_url: '{{ request.build_absolute_uri }}',
                },
                redirect: "if_required"
              });

              if (error) {
                  const message = $('#message');
                  message.show();
                  message.html(error.message);
              } else {
                stripe.retrieveSetupIntent('{{ client_token }}').then(({setupIntent}) => {
                  const message = $('#message');
                  message.show();
                  switch (setupIntent.status) {
                    case 'succeeded': {
                      $.ajax({
                        url: "{% url 'admin:add_payment_method' business.id %}",
                        type: 'post',
                        data: JSON.stringify({
                          setup_id: setupIntent.id,
                          payment_id: setupIntent.payment_method,
                          default: true,
                        }),
                        contentType: 'application/json; charset=utf-8',
                        headers: {
                          'X-CSRFToken': '{{ csrf_token }}',
                        },
                        success: function (response) {
                          message.html('Payment method was successfuly added to Stripe. Redirecting in 5 seconds ...');
                          setTimeout(function () {
                              window.location.href = '{% url 'admin:billing_billingbusiness_change' business.id %}';
                          }, 5000);
                        },
                        error: function (response) {
                          console.log(response);
                          message.html('Check that the payment method has been added correctly. Backend errors: ' + JSON.stringify(response.responseJSON || {}));
                        }
                      });
                      break;
                    }
                    case 'processing': {
                      message.html("Processing payment details. We'll update you when processing is complete.");
                      break;
                    }
                    case 'requires_payment_method': {
                      message.html('Failed to process payment details. Please try another payment method.');
                      break;
                    }
                  }
                });
              }
            });
          </script>
        {% else %}
          <div class="alert">
            Error creating Stripe token:
            <code>{{ setup_error|striptags }}</code>
          </div>
        {% endif %}
{% endif %}

{% endblock content %}
