{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}Boost claims processing{% endblock title %}

{% block extrastyle %}
{{ block.super }}
<link rel="stylesheet" type="text/css" href="{% static "admin/css/forms.css" %}" />
{% endblock extrastyle %}

{% block content %}


<h2>Help</h2>

<ol>
    <li>Filling form
        <ul>
            <li>Download template file: <br>
                <form action="" class="form-horizontal">
                    <button type="submit" name="download_template" class="btn btn-success">
                        <i class="icon-download icon-white"></i> Download template XLSX
                    </button>
                </form>
            </li>
            <li>Fill file fields:
                <ul>
                  <li>Column <code>A</code> - Boost Appointment ID of the pending claim. This Column will be read.</li>
                  <li>
                    Column <code>M</code> - Says if the claim should be accepted or declined. If the cell contains <code>BOOKSY</code> it means that this Boost Appointment (this claim) <b>should be declined</b> with <b>Found Business on Booksy</b> reason.
                    If the cell in column <code>M</code> contains any other word than <code>BOOKSY</code> this claim (Boost Appointment) should be accepted.
                  </li>
                </ul>
            </li>
        </ul>
    </li>
    <li>Upload the file in XSLX format.</li>
    <li>Use <strong>Validate file</strong> to check if the uploaded file contains any errors.</li>
    <li>If the file doesn’t contain any errors, use <strong>Process claims</strong> button to start processing.</li>
    <li>When the tool finishes the processing you will see the summary message with the results of claims acceptance/declining and the detailed report will be sent to your email.</li>
</ol>

<br />

{% include 'admin/custom_views/boost_mass_tools_form.html' %}

{% endblock content %}
