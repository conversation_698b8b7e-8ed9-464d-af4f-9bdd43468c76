
{% extends "admin/base_site.html" %}
{% load i18n l10n admin_urls %}

{% block breadcrumbs %}
  <ul class="breadcrumb">
    <li><a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
      <span class="divider">&raquo;</span></li>
    <li><a href="{% url 'admin:reviews_review_change' review.id %}">{{ review|truncatewords:"18" }}</a>
      <span class="divider">&raquo;</span></li>
          <li class="active">
        {% if add %}{% trans 'Add' %} {{ opts.verbose_name }}{% else %}{{ original|truncatewords:"18" }}{% endif %}
      </li>
    <li class="active">{% trans 'Mark selected review as fake' %}</li>
  </ul>
{% endblock %}

{% block content %}
  {% if perms_needed or protected %}
    {% if perms_lacking %}
      <div class="alert alert-error">
        {% blocktrans %}Marking as fake selected {{ review }} would result in deleting related objects, but your account doesn't have permission to delete the following types of objects:{% endblocktrans %}
        <ul>
          {% for obj in perms_lacking %}
            <li>{{ obj }}</li>
          {% endfor %}
        </ul>
      </div>
    {% endif %}
    {% if protected %}
      <div class="alert alert-error">
        {% blocktrans %}Marking as fake selected {{ review }} would require deleting the following protected related objects:{% endblocktrans %}
        <ul>
          {% for obj in protected %}
            <li>{{ obj }}</li>
          {% endfor %}
        </ul>
      </div>
    {% endif %}
  {% else %}

    <form action="" method="post">{% csrf_token %}
      {% for obj in queryset %}
        <input type="hidden" name="{{ action_checkbox_name }}" value="{{ obj.pk|unlocalize }}"/>
      {% endfor %}
      <input type="hidden" name="action" value="delete_selected"/>
      <input type="hidden" name="post" value="yes"/>

      <div class="alert alert-block alert-error">
        <h4 class="alert-heading">{% trans 'Confirm marking selected review as fake' %}</h4>
        <p>
          {% blocktrans %}Are you sure you want to mark this review as fake: <b>{{ review }}</b>? {% endblocktrans %}</p>
        <br>
        <div>
          <input type="submit" value="{% trans "Yes, I'm sure" %}" class="btn btn-danger"/>
        </div>
      </div>
    </form>

  {% endif %}
{% endblock %}
