{% extends 'admin/base_site.html' %}
{% load i18n static admin_modify suit_tags admin_urls %}
{% block title %}{{title}}{% endblock title %}
{% block content %}

<div class="form-horizontal">
    <fieldset class="module aligned with-legend" style="margin-top: 0;">
        <div class="control-group form-row field-row">
            <div>
                <div class="control-label">Task id: </div>
                <div class="controls">{{ task_id }}</div>
            </div>
            <div>
                <div class="control-label">Status<br><strong>Disclaimer</strong>: `SUCCESS` means there were no exceptions in the task. Always check "Task data" section for more information. </div>
                <div class="controls">{{ status }}</div>
            </div>
            <div>
                <div class="control-label">Is ready: </div>
                <div class="controls">{{ is_ready }}</div>
            </div>
            <div>
                <div class="control-label">Is successful: </div>
                <div class="controls">{{ is_successful }}</div>
            </div>
            <div>
                <div class="control-label">Is ignored: </div>
                <div class="controls">{{ is_ignored }}</div>
            </div>
            <div>
                <div class="control-label">Task data: </div>
                <div class="controls">
                {% if data %}<pre>{{ data }}</pre>{% endif %}
                </div>
            </div>
        </div> 
    </fieldset>
</div> 
<div style="margin-top: 20px;"><a href="{{request.META.HTTP_REFERER}}">Go back</a></div>
{% endblock content %}
