{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}Warehouse Commodities Import{% endblock title %}

{% block extrastyle %}
{{ block.super }}
<link rel="stylesheet" type="text/css" href="{% static "admin/css/forms.css" %}" />
{% endblock extrastyle %}

{% block content %}

<style>
tbody::before
{
  content: '';
  display: block;
  height: 15px;
}

table.pre-td td {
    white-space: pre;
}
td.my_error{
    font-size: 14px;
    background-color: #E62020;
    color: white;
    face: "Verdana";
  }
td.my_warning{
    font-size: 14px;
    background-color: #DAA520;
    face: "Verdana";
  }
td.my_success{
    font-size: 14px;
    background-color: #00FF7F;
    face: "Verdana";
  }
  .message{
    font-size: 14px;
    face: "Verdana";
  }
  .red_color{
    font-size: 14px;
    face: "Verdana";
    color: #E62020;
  }
   .green_color{
    font-size: 14px;
    face: "Verdana";
    color: darkgreen;
  }
   .normal-white-space {
    white-space: normal !important;
   }
td.my_warning div:nth-last-child(n+2){
    margin-bottom: 5px;
    padding-bottom: 5px;
    border-bottom: solid 1px #eee;
}
</style>

<h2>Help</h2>

<ol>
    <li>Filling form
        <ul>
            <li>Download template file: <br>
                <form action="" class="form-horizontal">
                    <button type="submit" name="download_template" class="btn btn-success">
                        <i class="icon-download icon-white"></i> Download template XLSX
                    </button>
                </form>
            </li>
            <li>Fill file fields <strong>according</strong> to the template. First Row must have the same name of columns as templates:
                <ul>
                    <li><code>A</code> Commodity name (name - <code>text field</code>).</li>
                    <li><code>B</code> Commodity code (sku - <code>text field</code>)</li>
                    <li><code>C</code> Commodity type (type - <code>text field</code>) (acceptable values: <code>r</code> - Retail, <code>p</code> - Professional)</li>
                    <li><code>D</code> Commodity net price (net_price - <code>decimal field</code>)</li>
                    <li><code>E</code> Commodity tax rate (tax_rate - <code>decimal field</code>). Please note this field will be validated with real tax rates available for business. If no match will be found this commodity will not be imported</li>
                    <li><code>F</code> Commodity barcode type (barcode_type - <code>text field</code>) (acceptable values:
                        {% if barcode_types %}
                        {% for barcode_type, barcode_name in barcode_types %}
                        <code>{{ barcode_type }}</code>
                        {% endfor %}
                        {% else%}
                        code39, code128, ean2, ean5, ean8, ean13, upc, itf14, msi, pharmacode, codabar
                        {% endif %}
                        )
                    </li>
                    <li><code>G</code> Commodity barcode (barcode - <code>text field</code>)</li>
                    <li><code>H</code> Commodity volume unit (volume_unit - <code>text field</code>). Please note this field will be validated with real volume units available for business.
                        You can use the full or short name. If not given, the default value "{{ default_volume_unit }}" will be set. If given, but no match is found, this commodity will not be imported</li>
                    <li><code>I</code> Commodity pack capacity (total_pack_capacity - <code>int field</code>)</li>
                    <li><code>J</code> Commodity category (category - <code>text field</code>)</li>
                    <li><code>K</code> Commodity supplier (supplier - <code>text field</code>)</li>
                    <li><code>L</code> Commodity brand (brand - <code>text field</code>)</li>
                    <li><code>M</code> Commodity description (description - <code>text field</code>)</li>
                </ul>
            </li>
            <li>The order of columns is not important. Only <strong>names</strong> of fields in first row are important.</li>
            <li>Commodities without <code>name</code>, <code>code</code>, <code>tax rate</code> and <code>volume unit</code> will <strong>not</strong> be imported.</li>
        </ul>
    </li>
    <li>If you want firstly check correctness of data please check option <i>dry-run</i>.
        Note that import with option dry-run can handle only small files. With no more than 1000 commodities</li>
    <li>
        It is recommended to download example file of Commodities:<br>
        <form action="" class="form-horizontal">
        <button type="submit" name="download_example" class="btn btn-success">
                    <i class="icon-download icon-white"></i> Download example XLSX
        </button>
        </form>
    <li>Please note that only files in <strong>xlsx</strong> format will be accepted</li>
</ol>

<br /><br /><br />
<h2>Form</h2>

{% for error in errors %}
    <div class="alert alert-danger" role="alert">
        <span class="glyphicon glyphicon-exclamation-sign" aria-hidden="true"></span>
        <span class="sr-only">Error:</span>
        {{ error }}
    </div><br>
{% endfor %}

<form action="" method="POST" enctype="multipart/form-data">
    {% csrf_token %}
    {{ form.as_table }}<br>
    <input type="submit" name="import" value="Import" class="btn btn-primary" />
</form>

{% if commodities %}
<p class="green_color"><strong>{{ num_commodities_import }}</strong> will be imported.</p>
<p class="red_color"><strong>{{ omitted }}</strong> commodities will be <strong>omitted</strong>.</p>
<p class="message">Total number of commodities <strong>{{total_num_com}}</strong></p>
    <table id="report_preview_table" class="table pre-td">
        <tr>
            <th>Messages</th>
            <th>Name</th>
            <th>SKU</th>
            <th>Type</th>
            <th>Net price</th>
            <th>Tax rate</th>
            <th>Barcode type</th>
            <th>Barcode</th>
            <th>Volume unit</th>
            <th>Total pack capacity</th>
            <th>Category</th>
            <th>Supplier</th>
            <th>Brand</th>
            <th>Description</th>
        </tr>
        {% for row in commodities %}

        {% if row.error %}
        <tr class="my_error">
            <td class="my_error max200 normal-white-space">{{ row.error }}</td>
            <td class="my_error">{{ row.name }}</td>
            <td class="my_error">{{ row.sku }}</td>
            <td class="my_error">{{ row.type }}</td>
            <td class="my_error">{{ row.net_price }}</td>
            <td class="my_error">{{ row.tax_rate }}</td>
            <td class="my_error">{{ row.barcode_type }}</td>
            <td class="my_error">{{ row.barcode }}</td>
            <td class="my_error">{{ row.volume_unit }}</td>
            <td class="my_error">{{ row.total_pack_capacity }}</td>
            <td class="my_error">{{ row.category }}</td>
            <td class="my_error">{{ row.supplier }}</td>
            <td class="my_error">{{ row.brand }}</td>
            <td class="my_error">{{ row.description }}</td>
        </tr>
        {% elif row.warnings %}
        <tr>
            <td class="my_warning max200 normal-white-space">
                {% for warning in row.warnings %}
                <div>{{ warning }}</div>
                {% endfor %}
            </td>
            <td class="my_warning">{{ row.name }}</td>
            <td class="my_warning">{{ row.sku }}</td>
            <td class="my_warning">{{ row.type }}</td>
            <td class="my_warning">{{ row.net_price }}</td>
            <td class="my_warning">{{ row.tax_rate }}</td>
            <td class="my_warning">{{ row.barcode_type }}</td>
            <td class="my_warning">{{ row.barcode }}</td>
            <td class="my_warning">{{ row.volume_unit }}</td>
            <td class="my_warning">{{ row.total_pack_capacity }}</td>
            <td class="my_warning">{{ row.category }}</td>
            <td class="my_warning">{{ row.supplier }}</td>
            <td class="my_warning">{{ row.brand }}</td>
            <td class="my_warning">{{ row.description }}</td>
        </tr>
        {% else%}
        <tr>
            <td class="my_success max200 normal-white-space">Success</td>
            <td class="my_success">{{ row.name }}</td>
            <td class="my_success">{{ row.sku }}</td>
            <td class="my_success">{{ row.type }}</td>
            <td class="my_success">{{ row.net_price }}</td>
            <td class="my_success">{{ row.tax_rate }}</td>
            <td class="my_success">{{ row.barcode_type }}</td>
            <td class="my_success">{{ row.barcode }}</td>
            <td class="my_success">{{ row.volume_unit }}</td>
            <td class="my_success">{{ row.total_pack_capacity }}</td>
            <td class="my_success">{{ row.category }}</td>
            <td class="my_success">{{ row.supplier }}</td>
            <td class="my_success">{{ row.brand }}</td>
            <td class="my_success">{{ row.description }}</td>
        </tr>
        {% endif %}

        {% endfor %}
    </table>
{% endif %}


{% endblock content %}
