<div id="id_{{ name }}_fields_container" style="column-count: 2;">
    {% for field in fields %}
        <div>
            {% if field.is_boolean %}
                <input type="checkbox" id="id_{{ field.name }}" name="{{ field.name }}"
                        {% if field.value %} checked {% endif %}
                       data-store="{{ field.store|lower }}" style="margin-top: -2px;"
                       {% if field.is_disabled %} disabled {% endif %}
                />
                <label for="id_{{ field.name }}" style="display: inline; padding-left: 5px;">
                    <b>{{ field.descr }}</b> ({{ field.name }})
                </label>
            {% else %}
                <label for="id_{{ field.name }}" style="display: inline; padding-right: 5px; padding-left: 18px;">
                    <b>{{ field.descr }}</b> ({{ field.name }}):
                </label>
                <input type="text" id="id_{{ field.name }}" name="{{ field.name }}" value="{{ field.value }}"
                       style="width: 80px;"
                       {% if field.is_disabled %} disabled {% endif %}
                />
            {% endif %}
        </div>
    {% endfor %}
</div>

<!-- actual custom_data json value is stored in hidden textarea element -->
<textarea id="id_{{ name }}" name="{{ name }}" style="display: none;">{{ data }}</textarea>

<script>
    /**
     * Handler that transforms input changes into actual Json data
     * @param store Indicates the value that should be stored in db (any other values won't be saved)
     */
    function updateJsonData(key, value, store = null) {
        let textareaEl = document.getElementById('id_{{ name }}');
        let jsonData = JSON.parse(textareaEl.value);
        if (store === null && value === '' || store !== null && value !== store) {
            // if current value differs from the value we want to store in db -> remove it from json data
            delete jsonData[key];
        } else {
            jsonData[key] = typeof value !== 'boolean' && Number(value) ? Number(value) : value;
        }
        textareaEl.value = JSON.stringify(jsonData);
    }

    // Setup onchange handler for inputs
    (function () {
        let fields_container = $('#id_{{ name }}_fields_container');
        fields_container.find('input[type=checkbox]').change(function (event) {
            updateJsonData(event.target.name, event.target.checked, event.target.dataset.store == 'true');
        });
        fields_container.find('input[type=text]').change(function (event) {
            updateJsonData(event.target.name, event.target.value);
        });
    })();
</script>