{# slimmed down version of date_filter.html from django-admin-rangefilter #}

{% load i18n rangefilter_compat %}
<link rel="stylesheet" type="text/css" href="{% static 'admin/css/widgets.css' %}">

{% comment %}
Force load jsi18n, issues #5
https://github.com/django/django/blob/stable/1.10.x/django/contrib/admin/templates/admin/change_list.html#L7
{% endcomment %}
<script type="text/javascript" src="{% url 'admin:jsi18n' %}"></script>

<script>
    {% comment %}
    // Code below makes sure that the DateTimeShortcuts.js is loaded exactly once
    // regardless the presence of AdminDateWidget
    // How it worked:
    //  - First Django loads the model formset with predefined widgets for different
    //    field types. If there's a date based field, then it loads the AdminDateWidget
    //    and it's required media to context under {{media.js}} in admin/change_list.html.
    //    (Note: it accumulates media in django.forms.widgets.Media object,
    //    which prevents duplicates, but the DateRangeFilter is not included yet
    //    since it's not model field related.
    //    List of predefined widgets is in django.contrib.admin.options.FORMFIELD_FOR_DBFIELD_DEFAULTS)
    //  - After that Django starts rendering forms, which have the {{form.media}}
    //    tag. Only then the DjangoRangeFilter.get_media is called and rendered,
    //    which creates the duplicates.
    // How it works:
    //  - first step is the same, if there's a AdminDateWidget to be loaded then
    //    nothing changes
    //  - DOM gets rendered and if the AdminDateWidget was rendered then
    //    the DateTimeShortcuts.js is initiated which sets the window.DateTimeShortcuts.
    //    Otherwise, the window.DateTimeShortcuts is undefined.
    //  - The lines below check if the DateTimeShortcuts has been set and if not
    //    then the DateTimeShortcuts.js and calendar.js is rendered
    //
    //  https://github.com/silentsokolov/django-admin-rangefilter/issues/9
    //
    // Django 2.1
    //  https://github.com/silentsokolov/django-admin-rangefilter/issues/21
    {% endcomment %}
    django.jQuery('document').ready(function () {
        if (!('DateTimeShortcuts' in window)) {
            django.jQuery.when(
                {% for m in spec.form.js %}
                    django.jQuery.getScript('{{m}}'),
                {% endfor %}
                django.jQuery.Deferred(function( deferred ){
                    django.jQuery( deferred.resolve );
                })
            ).done(function(){
                DateTimeShortcuts.init()

                for (const input_type of ['gte', 'lte']) {
                  const date_input = document.getElementById(`id_{{ title }}__range__${input_type}_0`)
                  DateTimeShortcuts.addCalendar(date_input)
                  const time_input = document.getElementById(`id_{{ title }}__range__${input_type}_1`)
                  DateTimeShortcuts.addClock(time_input)
                }

                // Delete today/now links. They take space and are redundant.
                document
                  .querySelectorAll('span.datetimeshortcuts>a:not([id])')
                  .forEach(e => e.remove())
            });
        }
    });
</script>

{{ spec.form }}
