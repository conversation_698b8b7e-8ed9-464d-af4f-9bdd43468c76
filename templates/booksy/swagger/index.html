<!DOCTYPE html>
<html>
<head>
  <title>Swagger UI</title>
  <link href='//fonts.googleapis.com/css?family=Droid+Sans:400,700' rel='stylesheet' type='text/css'/>
  <link href='{{ STATIC_URL }}swagger/css/reset.css' media='screen' rel='stylesheet' type='text/css'/>
  <link href='{{ STATIC_URL }}swagger/css/screen.css' media='screen' rel='stylesheet' type='text/css'/>
  <link href='{{ STATIC_URL }}swagger/css/reset.css' media='print' rel='stylesheet' type='text/css'/>
  <link href='{{ STATIC_URL }}swagger/css/screen.css' media='print' rel='stylesheet' type='text/css'/>
  <script type="text/javascript" src="{{ STATIC_URL }}swagger/lib/shred.bundle.js"></script>
  <script src='{{ STATIC_URL }}swagger/lib/jquery-1.8.0.min.js' type='text/javascript'></script>
  <script src='{{ STATIC_URL }}swagger/lib/jquery.slideto.min.js' type='text/javascript'></script>
  <script src='{{ STATIC_URL }}swagger/lib/jquery.wiggle.min.js' type='text/javascript'></script>
  <script src='{{ STATIC_URL }}swagger/lib/jquery.ba-bbq.min.js' type='text/javascript'></script>
  <script src='{{ STATIC_URL }}swagger/lib/handlebars-1.0.0.js' type='text/javascript'></script>
  <script src='{{ STATIC_URL }}swagger/lib/underscore-min.js' type='text/javascript'></script>
  <script src='{{ STATIC_URL }}swagger/lib/backbone-min.js' type='text/javascript'></script>
  <script src='{{ STATIC_URL }}swagger/lib/swagger.js' type='text/javascript'></script>
  <script src='{{ STATIC_URL }}swagger/swagger-ui.js' type='text/javascript'></script>
  <script src='{{ STATIC_URL }}swagger/lib/highlight.7.3.pack.js' type='text/javascript'></script>

  <!-- enabling this will enable oauth2 implicit scope support -->
  <script src='{{ STATIC_URL }}swagger/lib/swagger-oauth.js' type='text/javascript'></script>

  <script type="text/javascript">
    $(function () {
      window.swaggerUi = new SwaggerUi({
        url: window.location.toString().split('#')[0].replace(/\/+$/, '') + "/api",
        dom_id: "swagger-ui-container",
        supportedSubmitMethods: ['get', 'post', 'put', 'delete'],
        onComplete: function(swaggerApi, swaggerUi){
          log("Loaded SwaggerUI");
          if(typeof initOAuth == "function") {
            /*
            initOAuth({
              clientId: "your-client-id",
              realm: "your-realms",
              appName: "your-app-name"
            });
            */
          }
          $('pre code').each(function(i, e) {
            hljs.highlightBlock(e)
          });
        },
        onFailure: function(data) {
          log("Unable to Load SwaggerUI");
        },
        docExpansion: "none",
        sorter : "alpha"
      });

      window.swaggerUi.load();
  });
  </script>
</head>

<body class="swagger-section">
    <div id="message-bar" class="swagger-ui-wrap">&nbsp;</div>
    <div id="swagger-ui-container" class="swagger-ui-wrap"></div>
</body>
</html>
