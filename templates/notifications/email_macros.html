{% macro li(bullet="&bull;") -%}
    <tr>
        <td width="15" valign="top">
            <span style="color: #26cbc5;">{{ bullet }}</span>
        </td>
        <td>
            {{ caller() }}
        </td>
    </tr>
{%- endmacro %}


{% macro li_table(lines_after=2, color="#5e5e5e") -%}
    <table border="0" cellpadding="0" cellspacing="0" width="100%" style="color: {{  color }}; font-family: arial; font-size: 12px; line-height: 26px;">
        {{ caller() }}
    </table>
    {%- for i in range(lines_after) %}<br />{%- endfor %}
{%- endmacro %}


{% macro paragraph(lines_after=2, bold=False, center=False, indent=False, color="#5e5e5e", font_size="16px") -%}
    {% if indent %}<div style="padding-left: 50px">{% endif %}
    {% if center %}<center>{% endif %}
    <span style="color: {{ color }}; font-family: arial; font-size: {{ font_size }}; line-height: 26px;">
        {% if bold %}<b>{% endif %}
        {{ caller() }}
        {% if bold %}</b>{% endif %}
    </span>
    {% if center %}</center>{% endif %}
    {% if indent %}</div>{% endif %}
    {%- for i in range(lines_after) %}<br />{%- endfor %}
{%- endmacro %}


{% macro button(url, image_name=None, lines_after=2, background_color=COLOR_B2C, show_image=True, disable_mandrill=False, border_radius_without_image=3, font_size_without_image=12) -%}
    {% if image_name %}
        <table border="0" cellpadding="0" cellspacing="0" style="border: 1px solid {{ background_color }}; border-radius: 32px; -webkit-border-radius: 32px; background: #fff;">
            <tr>
                {% if show_image %}
                <td valign="middle">
                    <a href="{{ url }}" style="color: #fff; display: inline-block; font-family: arial; font-size: 12px; line-height: normal; padding: 10px 5px 10px 15px; text-decoration: none;"{% if disable_mandrill %} mc:disable-tracking{% endif %}>
                        <img src="{{ STATIC_FULL_URL }}scenarios/{{ image_name }}" />
                    </a>
                </td>
                {%  endif %}
                <td valign="middle">
                    <a href="{{ url }}" style="color: {{ background_color }}; display: inline-block; font-family: arial; font-size: 16px; line-height: normal; padding: 10px 15px 10px {% if show_image %}5px{% else %}15px{% endif %}; text-decoration: none;"{% if disable_mandrill %} mc:disable-tracking{% endif %}>
                            {{ caller() }}
                    </a>
                </td>
            </tr>
        </table>
    {% else %}
      <div style="text-align: center">
        <a href="{{ url }}" style="background: {{ background_color }}; border-radius: {{ border_radius_without_image }}px; -webkit-border-radius: {{ border_radius_without_image }}px; color: #fff; display: inline-block; font-family: arial; font-size: {{ font_size_without_image }}px; line-height: normal; padding: 16px 30px 16px 30px; text-decoration: none;" {% if disable_mandrill %} mc:disable-tracking{% endif %}>
          <b>
            {{ caller() }}
          </b>
        </a>
      </div>
    {% endif %}
    {%- for i in range(lines_after) %}<br />{%- endfor %}
{%- endmacro %}


{% macro extended_button(url, lines_after=2, background_color=COLOR_B2C, disable_mandrill=False, display_style="inline-block", border_radius="3px", font_size="12px", color="#fff", additional="") -%}
      <div style="text-align: center">
        <a href="{{ url }}" style="background: {{ background_color }}; border-radius: {{ border_radius }}; -webkit-border-radius: {{ border_radius }}; color: {{ color }}; display: {{ display_style }}; font-family: arial; font-size: {{ font_size }}; line-height: normal; padding: 16px 30px 16px 30px; text-decoration: none; {{ additional }}" {% if disable_mandrill %} mc:disable-tracking{% endif %}>
          <b>
            {{ caller() }}
          </b>
        </a>
      </div>
     {%- for i in range(lines_after) %}<br />{%- endfor %}
{%- endmacro %}


{% macro link(url, lines_after=0) -%}
    <a href="{{ url }}" style="color: #26cbc5; text-decoration: underline;">
        <b>
            {{ caller() }}
        </b>
    </a>
    {%- for i in range(lines_after) %}<br />{%- endfor %}
{%- endmacro %}


{% macro booking_info(booking_info, to=None, show_business=None, show_customer=None, show_booking=None, show_map=None, show_changed=True, show_deposit=True, show_booksy_pay=None) -%}
{% if to == "B" %}
    {% set do_show_business = False %}
    {% set do_show_customer = True %}
    {% set do_show_booking = True %}
    {% set do_show_map = False %}
    {% set show_deposit = False %}
{% elif to == "C" %}
    {% set do_show_business = True %}
    {% set do_show_customer = False %}
    {% set do_show_booking = True %}
    {% set do_show_map = True %}
{% endif %}

{% if booking_info.template_mode %}
    {% set do_show_business = False %}
    {% set do_show_customer = False %}
    {% set do_show_booking = False %}
    {% set do_show_map = False %}
    [[booking_info]]
{% endif %}

{% if show_business is not none %}
    {% set do_show_business = show_business %}
{%endif %}
{% if show_customer is not none %}
    {% set do_show_customer = show_customer %}
{%endif %}
{% if show_booking is not none %}
    {% set do_show_booking = show_booking %}
{%endif %}
{% if show_map is not none %}
    {% set do_show_map = show_map %}
{%endif %}
{% if show_booksy_pay is not none %}
    {% set do_show_booksy_pay = show_booksy_pay %}
{%endif %}

<div style="line-height: 18px">
    {% if do_show_business %}
        <div style="background-color:#f4f4f4; padding: 15px 20px; border: 1px solid #fff;-webkit-box-shadow:  1px 3px 3px #ccc !important;-moz-box-shadow:  1px 3px 3px #ccc !important;box-shadow:  1px 3px 3px #ccc !important;">
            <div><b>{{ booking_info.business_name }}</b></div>
            <div>{{ booking_info.business_address }}</div>
        </div>
    {% endif %}

    {% if
        do_show_customer and
        (
            booking_info.customer_name or
            (booking_info.customer_phone and not booking_info.customer_data_locked) or
            (booking_info.customer_email and not booking_info.customer_data_locked)
        )
    %}
        <div style="background-color:#f4f4f4; padding: 15px;">
            {% if booking_info.customer_name != '---' %}
                <div style="font-size: 16px; color: #666;">{{ booking_info.customer_name }}</div>
            {% endif %}

            {% if booking_info.customer_phone and not booking_info.customer_data_locked %}
                <div style="font-size: 12px;  color: #999;">{{ booking_info.customer_phone }}</div>
            {% endif %}

            {% if booking_info.customer_email and not booking_info.customer_data_locked %}
                <div>
                    <a href="mailto:{{ booking_info.customer_email }}" style="color: #26cbc5; text-decoration: underline;">
                        {{ booking_info.customer_email }}
                    </a>
                </div>
            {% endif %}
        </div>
    {% endif %}


    <div style="padding: 15px 0 10px 0;">
        <b>{{ booking_info.bookings_info.booked_from_date }}, {{ booking_info.bookings_info.booked_from_time }} - {{ booking_info.bookings_info.booked_till_time }}</b>
        {% if booking_info.bookings_info.repeat_text %}
            <div style="color: red">({{ booking_info.bookings_info.repeat_text }})</div>
        {% endif %}
        {% if booking_info.bookings_info.repeat_text_changed and booking_info.bookings_info.repeat_text_previous %}
            <div style="color: #888">
                <del>({{ booking_info.bookings_info.repeat_text_previous }})</del>
            </div>
        {% endif %}
    </div>
    {% if
        show_changed and
        (
            booking_info.bookings_info.booked_from_date_changed or
            booking_info.bookings_info.booked_from_time_changed or
            booking_info.bookings_info.booked_till_time_changed
        )
    %}
        <div style="color: #888">
            {% if booking_info.bookings_info.booked_from_date_changed %}
                <del>{{ booking_info.bookings_info.booked_from_date_previous }},</del>
            {% endif %}
            {% if
                booking_info.bookings_info.booked_from_time_changed or
                booking_info.bookings_info.booked_till_time_changed
            %}
                <del>{{ booking_info.bookings_info.booked_from_time_previous }} - {{ booking_info.bookings_info.booked_till_time_previous }}</del>
            {% endif %}
        </div>
    {% endif %}
    <div style="background: #9D9D9D; height: 1px; line-height:1px; font-size: 1px;" ></div>
    {% for subbooking in booking_info.bookings_info.subbookings %}
        {% if show_changed and subbooking.change == 'deleted' %}
            {% set text_style = 'style="color: #888"' %}
        {% else %}
            {% set text_style = '' %}
        {% endif %}
        {% if subbooking.wait_time %}
            <center style="line-height: 18px; padding: 5px 0; border-bottom: solid 1px #ccc; font-size: 80%; ">
                {{  _("Waiting time:") }}
                {{ subbooking.wait_time }}
            </center>
        {% endif%}
        <table style="border-bottom: solid 1px #ccc;  line-height: 18px; font-size: 12px; width: 100%; border-collapse:collapse;" cellpadding="0" cellspacing="0">
            <tr>
                <td width="80px" valign="middle" style="padding: 10px 0; vertical-align: middle;">
                    {% if subbooking.staffer_photo %}
                        <img
                            src="{{ subbooking.staffer_photo }}{% if show_changed and subbooking.change == 'deleted'%}#opacity{% endif %}"
                            style="background-color: #bbb; border-radius: 50px; width: 80px; height: 80px; border: solid 1px #bbb; vertical-align: middle;"
                        />
                    {% else %}
                        <img
                            src="{{ STATIC_FULL_URL }}img/userBlank.png{% if subbooking.change == 'deleted'%}#opacity{% endif %}"
                            style="background-color: #bbb; border-radius: 50px; width: 80px; height: 80px;   border: solid 1px #bbb; vertical-align: middle;"
                        />
                    {% endif %}
                </td>
                <td style="padding-left: 20px; text-align: left;" valign="middle">
                    <div {{ text_style }}>
                        {{ subbooking.name }}
                    </div>

                    {% if show_changed and subbooking.name_changed %}
                        <div style="color: #aaa">
                            <del>{{ subbooking.name_previous }}</del>
                        </div>
                    {% endif %}

                    <div {{ text_style }}>
                        {% if subbooking.price %}
                            {{ subbooking.price }},
                        {% endif %}
                        {{ subbooking.booked_from_time }} - {{ subbooking.booked_till_time }}
                    </div>

                    {% if
                        show_changed and
                        (
                            subbooking.price_changed or
                            subbooking.booked_from_time_changed or
                            subbooking.booked_till_time_changed
                        )
                    %}
                        <div style="color: #aaa">
                            {% if subbooking.price_changed %}
                                <s>{{ subbooking.price_previous }},</s>
                            {% endif %}
                            {% if subbooking.booked_from_time_changed or subbooking.booked_till_time_changed %}
                                <s>{{ subbooking.booked_from_time_previous }} - {{ subbooking.booked_till_time_previous }}</s>
                            {% endif %}
                        </div>
                    {% endif %}
                    <div {{ text_style }}>
                        {{  _("with") }}
                        {{ subbooking.staffers }}
                    </div>
                    {% if show_changed and subbooking.staffers_changed %}
                        <div style="color: #aaa">
                            <del>{{ subbooking.staffers_previous }}</del>
                        </div>
                    {% endif %}
                </td>
            </tr>
            {% if
                show_changed and
                (subbooking.change == 'deleted' or subbooking.change == 'added')
            %}
                <tr>
                    <td colspan="2">
                        {% if subbooking.change == 'deleted' %}
                            <div style="color: #999; font-size: 90%;">
                                {% if LANGUAGE == "pl" %}zmiana: usunięcie usługi{% else %}change: booking deleted{% endif %}
                            </div>
                        {% endif %}
                        {% if subbooking.change == 'added' %}
                            <div style="color: #999; font-size: 90%;">
                                {% if LANGUAGE == "pl" %}zmiana: dodanie usługi{% else %}change: booking added{% endif %}
                            </div>
                        {% endif %}
                    </td>
                </tr>
            {% endif %}
        </table>
    {% endfor %}

    {% if show_deposit %}
        {% if booking_info.bookings_info.deposit_info %}
            <div style="padding-bottom: 15px; padding-top: 15px;">
                {{ booking_info.bookings_info.deposit_info.info }}
                <br/>
                {{ booking_info.bookings_info.deposit_info.cancel }}
            </div>
        {% endif %}
    {% endif %}

    {% if
        booking_info.bookings_info.customer_note or
        (show_changed and booking_info.bookings_info.customer_note_previous)
    %}
        <br/>
        <div style="font-size: 10px; line-height: 110%; color: #000; padding-bottom: 5px;">
                {{  _("A note from the customer:") }}
        </div>
    {% endif %}

    {% if booking_info.bookings_info.customer_note %}
        <div>{{ booking_info.bookings_info.customer_note }}</div>
        {% if booking_info.bookings_info.customer_note_previous %}
            {% if show_changed and booking_info.bookings_info.customer_note_changed %}
                <div style="color: #949494; padding-bottom: 5px"><del>{{ booking_info.bookings_info.customer_note_previous }}</del></div>
            {% endif %}
        {% else %}
            {% if show_changed and booking_info.bookings_info.customer_note_changed %}
                <div style="font-size: 10px; color: #949494; padding-bottom: 15px">
                    {% if LANGUAGE == "pl" %}zmiana: dodanie notatki{% else %}change: note added{% endif %}
                </div>
            {% endif %}
        {% endif %}
    {% else %}
        {% if show_changed and booking_info.bookings_info.customer_note_previous %}
            <div style="color: #cccccc">{{ booking_info.bookings_info.customer_note_previous }}</div>
            <div style="font-size: 10px; color: #949494; padding-bottom: 15px;">
                {% if LANGUAGE == "pl" %}zmiana: usunięcie notatki{% else %}change: note deleted{% endif %}
            </div>
        {% endif %}
    {% endif %}


    {% if
        booking_info.bookings_info.business_note or
        (show_changed and booking_info.bookings_info.business_note_previous)
    %}
        <br/>
        <div style="font-size: 10px; line-height: 110%; color: #000; padding-bottom: 5px;">
                {{  _("A note from the business:") }}
        </div>
    {% endif %}

    {% if booking_info.bookings_info.business_note %}
        <div style="padding-bottom: 15px;">{{ booking_info.bookings_info.business_note }}</div>
        {% if booking_info.bookings_info.business_note_previous %}
            {% if show_changed and booking_info.bookings_info.business_note_changed %}
                <div style="color: #949494; padding-bottom: 5px"><del>{{ booking_info.bookings_info.business_note_previous }}</del></div>
            {% endif %}
        {% else %}
            {% if show_changed and booking_info.bookings_info.business_note_changed %}
                <div style="font-size: 10px; color: #949494; padding-bottom: 15px">
                    {% if LANGUAGE == "pl" %}zmiana: dodanie notatki{% else %}change: note added{% endif %}
                </div>
            {% endif %}
        {% endif %}
    {% else %}
        {% if show_changed and booking_info.bookings_info.business_note_previous %}
            <div style="color: #cccccc">{{ booking_info.bookings_info.business_note_previous }}</div>
            <div style="font-size: 10px; color: #949494; padding-bottom: 15px">
                {% if LANGUAGE == "pl" %}zmiana: usunięcie notatki{% else %}change: note deleted{% endif %}
            </div>
        {% endif %}
    {% endif %}

    {% if do_show_map or do_show_booksy_pay and booking_info.is_booksy_pay_payment_window_open  %}
        <div style="text-align: center; padding-top: 20px">
        {% if do_show_booksy_pay and booking_info.is_booksy_pay_payment_window_open %}
            <div style="display: inline-block; padding-right: 10px;">
                {% call button(url=booking_info.booking_marketplace_url, border_radius_without_image=32, font_size_without_image=17) %}
                    {{ _("Pay now") }}
                {% endcall %}
            </div>  
        {% endif %}
        {% if do_show_map %}
            <div style="display: inline-block;">
                {% call button(url=booking_info.business_map_url, image_name="map.png") %}
                    {{ _("Get directions") }}
                {% endcall %}
            </div>
        {% endif %}
        </div>
    {% endif %}
</div>
<br />
{%- endmacro %}


{% macro booking_changes(booking_info) -%}
    {#
    {% call paragraph() %}
        <ul>
            {% if booking_info.previous_booked_from_booking_date != booking_info.booked_from_booking_date %}
                <li><b>{{ booking_info.previous_booked_from_booking_date }}</b> -> <b>{{ booking_info.booked_from_booking_date }}</b></li>
            {% endif %}
            {% if booking_info.previous_booked_from_booking_time != booking_info.booked_from_booking_time %}
                <li><b>{{ booking_info.previous_booked_from_booking_time }}</b> -> <b>{{ booking_info.booked_from_booking_time }}</b></li>
            {% endif %}
            {% if booking_info.previous_booking_service_duration != booking_info.booking_service_duration %}
                <li><b>{{ booking_info.previous_booking_service_duration }}</b> -> <b>{{ booking_info.booking_service_duration }}</b></li>
            {% endif %}
            {% if booking_info.previous_booking_price != booking_info.booking_price %}
                <li><b>{{ booking_info.previous_booking_price }}</b> -> <b>{{ booking_info.booking_price }}</b></li>
            {% endif %}

            {% if booking_info.previous_booking_service_name_full != booking_info.booking_service_name_full %}
                <li><b>{{ booking_info.previous_booking_service_name_full }}</b> -> <b>{{ booking_info.booking_service_name_full }}</b></li>
            {% endif %}

            {% if booking_info.previous_booking_staff_names != booking_info.booking_staff_names %}
                <li><b>{{ booking_info.previous_booking_staff_names }}</b> -> <b>{{ booking_info.booking_staff_names }}</b></li>
            {% endif %}

            {% if booking_info.previous_booking_business_note != booking_info.booking_business_note %}
                <li>
                    <b>{{ QUOTATION_START }}{{ booking_info.previous_booking_business_note }}{{ QUOTATION_END }}</b>
                    ->
                    <b>{{ QUOTATION_START }}{{ booking_info.booking_business_note }}{{ QUOTATION_END }}</b>
                </li>
            {% endif %}
            {% if booking_info.previous_booking_customer_note != booking_info.booking_customer_note %}
                <li>
                    <b>{{ QUOTATION_START }}{{ booking_info.previous_booking_customer_note }}{{ QUOTATION_END }}</b>
                    ->
                    <b>{{ QUOTATION_START }}{{ booking_info.booking_customer_note }}{{ QUOTATION_END }}</b>
                </li>
            {% endif %}
        </ul>
    {% endcall %}
    #}
{%- endmacro %}


{% macro pos_add_card_infobox(link) -%}
    {{ caller() }}
    {% if link %}
        <div style="background-color:#f4f4f4; padding: 15px 20px;">
            <div style="text-transform: uppercase">
                <b>{% if LANGUAGE == "pl" %}Płatność aplikacją{% else %}Mobile payments{% endif %}</b>
            </div>
            <div style="margin-bottom: 15px">
                {% if LANGUAGE == "pl" %}Płatność za wizytę bez gotówki? Zapłać przez aplikację{% else %}Would you like to pay with your phone after the visit? No cash required!{% endif %}
            </div>
            {% call button(url=link, image_name=True, show_image=False, lines_after=0) %}
                {% if LANGUAGE == "pl" %}dodaj kartę{% else %}Add your card{% endif %}
            {% endcall %}
        </div>
        <br/>
    {% endif %}
{%- endmacro %}
