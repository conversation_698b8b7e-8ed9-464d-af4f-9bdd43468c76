{% extends "email_base_business.html" %}

{% block email_title %}
    {{ _('Cha<PERSON><PERSON>! Someone ordered a gift card.') }}
{% endblock email_title %}

{% block email_content %}
    <div style="text-align: center;
            color: rgb(42, 44, 50);
            height: 355px;
            background-image: url('{{ S3_STATIC_HOST_URL }}scenarios/voucher-money-background.png');
            background-position: bottom center;
            background-repeat: no-repeat;
            ">
        <div>
            <p style="font-family: 'Muli-ExtraBold', 'Helvetica Neue', Helvetica, Arial, sans-serif;
                font-size: 37px;
                font-weight: 800;
                height: 46px;
                letter-spacing: -1.88px;">
                {{ _('Hey') }} {{ owner_name }},
            </p>
            <p style="font-family: 'Muli-Regular', 'Helvetica Neue', Helvetica, Arial, sans-serif;
                font-size: 22px;
                font-weight: normal;
                height: 108px;
                letter-spacing: -0.48px;
                line-height: 36px;">
                {{ customer_first_name }}&nbsp;{{ _("just ordered a gift card from you!") }}
            </p>

            <p style="font-size: 50px;
                color: white;
                margin-top: 50px;">
                {{ voucher_value_no_minor }}
            </p>
        </div>
    </div>

    <div style="text-align: center;
            padding: 20px 48px;
            color: rgb(42, 44, 50);
            font-family: 'Muli-Medium', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            font-size: 15px;
            font-weight: 500;
            line-height: 26px;
            border-bottom: 1px solid rgba(100, 100, 100, 0.1);">
        <p>
            {{ _('What’s next? Be on the lookout for payment from your client (you specified where they should pay you in the Payment Details section within the app). Once you get paid, go to the “Gift Cards > Orders” and find the corresponding order to confirm payment manually. When this happens, your client will receive their gift card directly to their email.') }}
        </p>
        <p style="font-family: 'Muli-Bold', 'Helvetica Neue', Helvetica, Arial, sans-serif; font-weight: bold;">
            {{ _('Your friends at Booksy') }}
        </p>
    </div>

    <div style="padding: 20px 48px;">
        <p style="color: rgb(146, 146, 146);
            font-family: Muli-Medium, 'Helvetica Neue', Helvetica, Arial, sans-serif;
            font-size: 13px;
            font-weight: 500;
            height: 34px;
            line-height: 20px;
            text-align: center;
            text-transform: uppercase;">
            {{ _("Order details below") }}
        </p>
        <table style="width: 100%;
            color: rgb(42, 44, 50);
            font-family: Muli-Medium, 'Helvetica Neue', Helvetica, Arial, sans-serif;
            font-size: 15px;">
            <tr>
                <td style="padding-bottom: 16px;">{{ _('Client name') }}</td>
                <td style="padding-bottom: 16px; text-align: right;">
                    <strong>{{ customer_full_name }}</strong>
                </td>
            </tr>
            <tr>
                <td style="padding-bottom: 16px;">{{ _('Gift Card name') }}</td>
                <td style="padding-bottom: 16px; text-align: right;">
                    <strong>{{ voucher_name }}</strong>
                </td>
            </tr>
            <tr>
                <td style="padding-bottom: 16px;">{{ _('Gift Card value') }}</td>
                <td style="padding-bottom: 16px; text-align: right;">
                    <strong>{{ voucher_value }}</strong>
                </td>
            </tr>
            <tr>
                <td style="padding-bottom: 16px;">{{ _('Gift Card price') }}</td>
                <td style="padding-bottom: 16px; text-align: right;">
                    <strong>{{ voucher_price }}</strong>
                </td>
            </tr>
            <tr>
                <td style="padding-bottom: 16px;">{{ _('Order date') }}</td>
                <td style="padding-bottom: 16px; text-align: right;">
                    <strong>{{ order_created }}</strong>
                </td>
            </tr>
        </table>

        <p style="color: rgb(146, 146, 146);
            font-family: 'Muli-Regular', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            font-size: 13px;
            font-weight: normal;
            height: 67px;
            line-height: 20px;
            text-align: center;">
            {{ _("Note: Payment for these gift cards is not facilitated by Booksy. Your client will only receive the gift card once they pay you via your personal platform (Venmo, Apple Pay, PayPal, etc) followed by you manually confirming payment in your Booksy Biz app.") }}
        </p>
    </div>
{% endblock email_content %}
