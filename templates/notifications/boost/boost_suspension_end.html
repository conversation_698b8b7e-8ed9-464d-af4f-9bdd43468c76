{% extends "email_base_business.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}{{ _("Boost has been reactivated") }}{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        {% if LANGUAGE == "pl" %}
            <PERSON><PERSON><PERSON> dobry,
        {% else %}
            {{ _("Hi,") }}
        {% endif %}
    {% endcall %}

    {% call macros.paragraph() %}
        {{ _("The Boost service has been reactivated after a 30-day suspension resulting from a violation of the Boost Terms &amp; Conditions.") }}
    {% endcall %}

    {% call macros.paragraph() %}
        {{ _("Please review the <a href='{boost_terms_url}' target='_blank'>Boost Terms &amp; Conditions</a> as well as the <a href='{boost_landing_page_url}' target='_blank'>page with key information about the Boost service</a>.").format(boost_terms_url=boost_terms_url, boost_landing_page_url=boost_landing_page_url) }}
    {% endcall %}

    {% call macros.paragraph() %}
        {{ _("This is an automatically generated message. Please do not reply. If you have any questions, feel free to contact us on <a href='{support_url}' target='_blank'>chat</a>.").format(support_url=support_url) }}
    {% endcall %}

    {% call macros.paragraph() %}
        {{ _("Here for you,") }}<br/>
        Booksy
    {% endcall %}
{% endblock email_content %}