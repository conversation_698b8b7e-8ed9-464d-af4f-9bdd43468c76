{% extends "email_base_business.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}{{_('Action Needed: Contact Support Team')}}{% endblock email_title %}
{% block extrahead %}{{_('Update your payment method to continue using Boost')}}{% endblock %}
{% block email_content %}
    {% call macros.paragraph() %}
        <p>{{_('Hi')}} {{ recipient_name }},</p>
        <p>
            {{_("We're having an issue processing your Boost fees with the account or card you have on file. In order to continue using Boost, please contact our Support Team to verify your payment details are up to date. If we continue to be unable to process the Boost fees, <PERSON>ost will be turned off.")}}
        </p>
        <p>
            {{_("To contact our Support Team <a href='{link_contact}'>click here</a>.").format(link_contact=link_contact) }}
        </p>
        <p>{{_('Here for you')}},<br>Booksy</p>
    {% endcall %}

{% endblock email_content %}
