{% extends "email_base_business.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}{{ _("Access to Boost suspended") }}{% endblock email_title %}

{% block email_content %}
    {% set active_till = ban.active_till | date("DATE_FORMAT") %}

    {% call macros.paragraph() %}
        {% if LANGUAGE == "pl" %}
            <PERSON><PERSON><PERSON> dobry,
        {% else %}
            {{ _("Hi,") }}
        {% endif %}
    {% endcall %}

    {% call macros.paragraph() %}
        {{ _("Your account has been temporarily suspended from accessing the Boost service for 30 days until {active_till} due to a violation of the <a href='{boost_terms_url}' target='_blank'>Boost Terms &amp; Conditions</a>, specifically related to actions that lead to the avoidance or lowering of Boost commissions.").format(active_till=active_till, boost_terms_url=boost_terms_url) }}
    {% endcall %}

    {% if ban.invoice_required %}
        {% call macros.paragraph() %}
            {{ _("Within 15 business days you will receive an invoice for unpaid Boost fees. Once received, you have 7 days to complete payment. Payment is required so that Boost can be automatically reactivated after the suspension period.") }}
        {% endcall %}
    {% endif %}

    {% call macros.paragraph() %}
        {{ _("This is an automatically generated message. Please do not reply. If you have any questions, feel free to contact us via email at <a href='mailto:{support_email}' target='_blank'>{support_email}</a>.").format(support_email=support_email) }}
    {% endcall %}

    {% call macros.paragraph() %}
        {{ _("Here for you,") }}<br/>
        Booksy
    {% endcall %}
{% endblock email_content %}