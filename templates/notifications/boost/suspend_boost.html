{% extends "email_base_business.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}{{_('<PERSON>ost suspension request ')}}{{business_id}}{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        {{_('The merchant {} wants to suspend <PERSON>ost. The Boost suspension request was created at {}').format(business_id, datetime) }}
    {% endcall %}
{% endblock email_content %}
