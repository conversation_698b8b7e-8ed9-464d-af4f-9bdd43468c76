{% extends "email_base_business.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}{{ _("Boost service terminated") }}{% endblock email_title %}

{% block email_content %}
    {% set active_till = ban.active_till | date("DATE_FORMAT") %}

    {% call macros.paragraph() %}
        {% if LANGUAGE == "pl" %}
            <PERSON>zie<PERSON> dobry,
        {% else %}
            {{ _("Hi,") }}
        {% endif %}
    {% endcall %}

    {% call macros.paragraph() %}
        {{ _("Your Booksy Boost service was terminated with immediate effect due to a violation of the <a href='{boost_terms_url}' target='_blank'>Boost Terms &amp; Conditions</a>, specifically related to actions that lead to the avoidance or lowering of Boost commissions.").format(boost_terms_url=boost_terms_url) }}
    {% endcall %}

    {% call macros.paragraph() %}
        {{ _("You will be able to reactivate <PERSON>ost after {active_till}. Until then, your access to the Boost service will remain suspended.").format(active_till=active_till) }}
    {% endcall %}

    {% if ban.invoice_required %}
        {% call macros.paragraph() %}
            {{ _("Within 15 business days you will receive an invoice for the unpaid Boost fees. Once received, you have 7 days to complete payment. Payment is required to reactivate Boost.") }}
        {% endcall %}
    {% endif %}

    {% call macros.paragraph() %}
        {{ _("This is an automatically generated message. Please do not reply. If you have any questions, feel free to contact us via email at <a href='mailto:{support_email}' target='_blank'>{support_email}</a>.").format(support_email=support_email) }}
    {% endcall %}

    {% call macros.paragraph() %}
        {{ _("Here for you,") }}<br/>
        Booksy
    {% endcall %}
{% endblock email_content %}