{% extends "email_base_business.html" %}

{% block email_title %}
    {{ title }}
{% endblock email_title %}

{% block email_content %}
    {% set total_messages_count = push_recipients_count + sms_recipients_count + email_recipients_count %}
    <div style="
            color: rgb(42, 44, 50);
            font-size: 15px;
            font-family: 'Muli-Medium', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            text-align: center;
            line-height: 26px;
            margin: auto;
            font-weight: bold;
    ">
        {{ message_title }}
    </div>
    <div style="text-align: center">
        <div style="
            padding-top: 20px;
            width: 504px;
            color: rgb(42, 44, 50);
            font-size: 15px;
            font-family: 'Muli-Medium', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            text-align: center;
            line-height: 26px;
            margin: auto;
        ">
            {{ _('The message has been sent via:') }}<br/>
            <table style="margin: auto;">
                {% if message_blast.channel_push %}
                <tr><td>{{ _('Push: {push_recipients_count} messages').format(push_recipients_count=push_recipients_count) }}</td></tr>
                {% endif %}
                {% if message_blast.channel_sms %}
                <tr><td>{{ _('SMS: {sms_recipients_count} messages').format(sms_recipients_count=sms_recipients_count) }}{% if message_blast.out_of_sms %} ({{ _("You've run out of SMS limit") }}) {% endif %}</td></tr>
                {% endif %}
                {% if message_blast.channel_email %}
                <tr><td>{{ _('Email: {email_recipients_count} messages').format(email_recipients_count=email_recipients_count) }}</td></tr>
                {% endif %}
            </table>

            {{ _('In total {total_messages_count} messages were sent.').format(total_messages_count=total_messages_count) }}
            {{ _('Booksy system allows up to {max_spam_count} different messages to be sent to a client per month').format (max_spam_count=max_spam_count, channel_name=channel_name) }}<br/>
        </div>
        <div style="
            padding-top: 32px;
            width: 504px;
            color: rgb(42, 44, 50);
            font-size: 15px;
            font-family: 'Muli-Medium', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            text-align: center;
            line-height: 26px;
            margin: auto;
        ">
            {{ _('Please see detailed report attached.') }}
        </div>
    </div>
{% endblock email_content %}
