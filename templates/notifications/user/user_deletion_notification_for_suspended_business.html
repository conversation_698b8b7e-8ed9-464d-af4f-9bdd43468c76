{% extends "email_base_business.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    [SUSPENDED]{{ _("Booksy - Client data removal request") }} [{{ country }}] [{{ customer_id }}]
{% endblock email_title %}

{% block email_content %}
    <p>{{ _("Dear Booksy Biz user,") }}</p>
    <p>{{ _("We are contacting you to inform you that your client {first_name} {last_name} {email} has requested the removal of their personal data from Booksy.").format(first_name=first_name, last_name=last_name, email=email) }}</p>
    <p>{{ _("As processor of your clients’ personal data held & processed through the Booksy app, we are assisting you in fulfilling your obligation to respond to clients in regard to the exercise of their GDPR rights, including the right to erasure of personal data.") }}</p>
    <p>{{ _("As such we notify you that within {delete_account_after_days} days, the personal data of the aforementioned client will be anonymised within your Booksy business app and you will not be able to identify them.").format(delete_account_after_days=delete_account_after_days) }}</p>
    <p>{{ _("You may download the personal data from your Booksy business application, however please note that in accordance with the GDPR, you may only continue to store the personal data of this client if you have a valid processing reason (as outlined in Article 6 of GDPR).") }}</p>
    <p>{{ _("After the removal of this client's personal data, should you need to re-enter it again for any purpose, you must ensure that you have a legal basis for the processing of this personal data and that you have the client's consent to send any marketing content to them, should you wish to do so.") }}</p>
    <p>{{ _("Please also ensure that within these {delete_account_after_days} days you return any deposits the client may have made.").format(delete_account_after_days=delete_account_after_days) }}</p>
    <p>{{ _("Please note this is an automated message. If you require any additional support with this request and/or would like to contact our GDPR department or Data Protection Officer directly please email") }} {{ gdpr_email }}.</p>
    <p>{{ _("Kind regards,") }}</p>
    <p>{{ _("Booksy GDPR team") }}</p>
{% endblock email_content %}
