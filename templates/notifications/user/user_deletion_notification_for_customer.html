{% extends "email_base_customer.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
    {{ _("Booksy - Your account removal request") }} [{{ country }}] [{{ customer_id }}]
{% endblock email_title %}

{% block email_content %}
    <p>{{ _("Dear Booksy user,") }}</p>
    <p>{{ _("Thank you for contacting us regarding the removal of your account and personal data.") }}</p>
    <p>{{ _("We confirm that we have received your request and that we shall request on your behalf for this data to be additionally removed from the following providers with which you have made appointments via the Booksy app:") }} </p>
    <p>
        <ul>
            {% for provider in providers %}
                <li>{{ provider }}</li>
            {% endfor %}
        </ul>
    </p>
    <p>{{ _("Within {delete_account_after_days} days you will receive confirmation of the removal of your personal data processed and held by Booksy and the associated providers.").format(delete_account_after_days=delete_account_after_days) }}</p>
    <p>{{ _("Please note this is an automated message. If you would like to contact our GDPR department or Data Protection Officer directly please email {gdpr_email}.").format(gdpr_email=gdpr_email) }}</p>
    <p>{{ _("Kind regards,") }}</p>
    <p>{{ _("Booksy GDPR team") }}</p>
{% endblock email_content %}
