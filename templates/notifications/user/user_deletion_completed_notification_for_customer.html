{% extends "email_base_customer.html" %}
{% import "email_macros.html" as macros %}


{% block email_title %} {{ _('Booksy - Account removal confirmation ') }}[{{ country_code }}] [{{ user_id }}]{% endblock email_title %}

{% block email_content %}
<p>{{ _("Dear Booksy user,") }}</p>

<p> {{ _("We are contacting you to confirm the removal of your account and personal data from the Booksy app as requested.") }}</p>

<p> {{ _("Additionally, we confirm that we have requested on your behalf for this data to be removed from any providers with which you have made appointments via the Booksy app. If you had previously given marketing consent to Booksy, you will no longer receive any marketing content from us.") }}</p>

<p> {{ _("Please note this is an automated message. If you would like to contact our GDPR department or Data Protection Officer directly please email ")}} {{ gdpr_email }}.  </p>
<p>{{ _("Kind regards,") }}</p>
<p>{{ _("Booksy GDPR team") }}</p>

{% endblock email_content %}
