{% extends "email_base_customer.html" %}
{% import "email_macros.html" as macros %}
{% set HEADER = "header-booksy-gift-card-500px.png" %}

{% block email_title %}
    {{ _("Hey, it’s your Booksy Gift Card!") }} &#127873;
{% endblock email_title %}

{% block preheader %}
    {{ _("Treat yourself to something special!") }}
    &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj
    &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj
    &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj
    &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj
    &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj
    &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj
    &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj
    &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj
    &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj
    &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj &zwnj
{% endblock %}

{% block content_style %}padding:  0 0 20px 0;{% endblock content_style %}

{% block email_content %}
    <div style="font-family: 'Proxima Nova', sans-serif; font-size: 16px; font-style: normal; margin: 40px;">
        <div style="margin-bottom: 32px;">
            <div style="font-size: 24px; font-weight: 700; margin-bottom: 16px; margin-top: -40px;">
                {{ _("Hi,") }}
            </div>
            {{ _("We are happy to let you know that your payment has been received and <span style='color:#218CAC;font-weight:700'>your Gift Card</span> is now <strong>ready to use.</strong> Explore thousands of beauty businesses and book your appointment today!") }}
        </div>
        {% if sender_name %}
            <div style="margin-top: 12px; margin-bottom: 12px;">
                <div style="line-height: 24px; margin-bottom: 4px;">
                    {{ _("Sender name") }}
                </div>
                <div style="font-weight: 600; line-height: 24px;">
                    {{ sender_name }}
                </div>
            </div>
            <hr style="border: 1px #E6E6E6 solid;">
        {% endif %}
        {% if recipient_name %}
            <div style="margin-top: 12px; margin-bottom: 12px;">
                <div style="line-height: 24px; margin-bottom: 4px;">
                    {{ _("Recipient name") }}
                </div>
                <div style="font-weight: 600; line-height: 24px;">
                    {{ recipient_name }}
                </div>
            </div>
            <hr style="border: 1px #E6E6E6 solid;">
        {% endif %}
        {% if message %}
            <div style="margin-top: 12px; margin-bottom: 12px;">
                <div style="line-height: 24px; margin-bottom: 4px;">
                    {{ _("Message") }}
                </div>
                <div style="font-weight: 600; line-height: 24px;">
                    {{ message }}
                </div>
            </div>
            <hr style="border: 1px #E6E6E6 solid;">
        {% endif %}
    </div>

    <div style="font-family: 'Proxima Nova', sans-serif; font-size: 16px; font-style: normal;">
        <div id="gift-card" style="height: 188px; margin: 0 8px; color: #fff; background-size: contain; background-repeat: no-repeat; border-radius: 12px; padding: 14px 80px; background-image: url('{{ S3_STATIC_HOST_URL }}scenarios/{{ email_image }}');">
            <div style="display: flex;">
                <div style="width: 80px; height: 20px; background-image: url('{{ S3_STATIC_HOST_URL }}scenarios/logo-white.png'); background-size: contain; background-repeat: no-repeat;"></div>
                <span style="font-size: 14px; line-height: 20px; margin-left: auto;">
                    {{ _("Value") }}
                </span>
            </div>
            <div style="font-size: 24px; font-weight: 700; line-height: 32px; text-align: right; margin-bottom: 12px;">
                {{ card_value|format_currency }}
            </div>
            <div style="width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; font-size: 24px; font-weight: 700; line-height: 32px; margin-bottom: 4px;">
                {{ _("Gift card") }}
            </div>
            <div style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; font-size: 14px; font-weight: 400; line-height: 20px; margin-bottom: 24px;">
                {{ _("Valid for all services") }}
            </div>
            <div style="font-size: 14px; font-weight: 400; line-height: 20px; text-align: right;">
                {{ _("Expires after") }}
            </div>
            <div style="font-size: 18px; font-weight: 600; line-height: 24px; text-align: right;">
                {{ _("12 months") }}
            </div>
        </div>
    </div>

    <div style="font-family: 'Proxima Nova', sans-serif; font-size: 16px; font-style: normal; margin: 40px;">
        <div style="margin-top: 10px; margin-bottom: 10px;">
            <span style="line-height: 24px;">
                {{ _("Code") }}
            </span>
            <span style="font-weight: 600; line-height: 24px; float: right;">
                {{ card_code[:4] }}-{{ card_code[4:8] }}-{{ card_code[8:12] }}
            </span>
        </div>
        <hr style="border: 1px #E6E6E6 solid;">
        <div style="margin-top: 10px; margin-bottom: 10px;">
            <span style="line-height: 24px;">
                {{ _("Value") }}
            </span>
            <span style="font-weight: 600; line-height: 24px; float: right;">
                {{ card_value|format_currency }}
            </span>
        </div>
        <hr style="border: 1px #E6E6E6 solid;">
        <div style="margin-top: 10px; margin-bottom: 10px;">
            <span style="line-height: 24px;">
                {{ _("Expires after") }}
            </span>
            <span style="font-weight: 600; line-height: 24px; float: right;">
                {{ card_expiration_date.strftime("%d/%m/%Y") }}
            </span>
        </div>
        <hr style="border: 1px #E6E6E6 solid;">
        <div style="font-size: 14px; font-weight: 600; line-height: 20px; margin-top: 20px;">
            {{ _("Upon first use, the card will be assigned to the account of the person who used it.") }}
        </div>
    </div>

    <hr style="border: 1px #E6E6E6 solid;">

    <div style="font-family: 'Proxima Nova', sans-serif; font-size: 16px; font-style: normal; margin: 40px; margin-bottom: 0;">
        <div style="margin-top: 12px; margin-bottom: 12px;">
            <div style="font-size: 18px; font-weight: 700; line-height: 24px; margin-bottom: 8px;">
                {{ _("How can I use it?") }}
            </div>
            <div style="font-size: 14px; line-height: 20px;">
                {{ _("To use your Booksy gift card, simply enter the card code when booking an appointment on Booksy.com or in the mobile app.") }}
            </div>
        </div>
        <hr style="border: 0;">
        <div style="margin-top: 12px; margin-bottom: 12px;">
            <div style="font-size: 18px; font-weight: 700; line-height: 24px; margin-bottom: 8px;">
                {{ _("Where can I redeem it?") }}
            </div>
            <div style="font-size: 14px; line-height: 20px;">
                {{ _("When searching through businesses, those that accept Booksy gift cards will have a badge displayed next to them. You can also filter your search to only show these businesses using the “Booksy gift cards filter”.") }}
            </div>
        </div>
        <hr style="border: 0;">
        <div style="margin-top: 12px; margin-bottom: 12px;">
            <div style="font-size: 18px; font-weight: 700; line-height: 24px; margin-bottom: 8px;">
                {{ _("A gift idea") }}
            </div>
            <div style="font-size: 14px; line-height: 20px;">
                {{ _("Give the gift of Booksy by printing off this gift card. You can also send this PDF document by email or through any app.") }}
            </div>
        </div>
        <hr style="border: 0;">
        <div style="margin-top: 12px; margin-bottom: 12px;">
            <div style="font-size: 18px; font-weight: 700; line-height: 24px; margin-bottom: 8px;">
                {{ _("Gift card return") }}
            </div>
            <div style="font-size: 14px; line-height: 20px;">
                {{ _("A Booksy gift card can be returned within 14 days from the purchase date by the original purchaser. To be eligible for return, this card, or any related promotional card must not have been used in any part. To return a gift card, please send an <NAME_EMAIL> from the email address used for the purchase, including the 32-character serial number of the card. Gift card serial number:") }}  {{card_external_id}}.
            </div>
        </div>
    </div>
{% endblock email_content %}
