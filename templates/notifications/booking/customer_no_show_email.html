{% extends "email_base_customer.html" %}

{% block email_title %}
    {{ _('Your absence on') }}
    {{ appointment.booked_from.astimezone(timezone) | date("DATETIME_FORMAT") }}
{% endblock email_title %}

{% block email_content %}
    <div style="padding: 0 40px; color: rgb(51, 51, 51);
        font-family: SFProDisplay-Regular, 'Helvetica Neue', Arial, sans-serif;
        font-size: 14px;
        font-weight: normal;"
    >
        <p>{{ _('Hello') }} {{ customer_name }}!</p>

        <p>{{ _('We are sorry that you were absent from the following appointment:') }}</p>
        <p>
            {{ appointment.booked_from.astimezone(timezone) | date("DATETIME_FORMAT") }}
            {{ _('at {business_name}').format(business_name=business_name) }}
        </p>

        {% if no_show_protection_info %}
            <p>{{ _('According to the business cancellation policy, business may charge the amount of cancellation fee for this appointment.') }}</p>
        {% endif %}

        <p>{{ _("If you can't make it for the appointment, please cancel it in advance.") }}</p>
        <p>{{ _('Best,<br>Booksy') }}</p>
    </div>
{% endblock email_content %}
