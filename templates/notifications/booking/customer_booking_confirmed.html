{% extends "email_base_customer.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}
{% if booking_info.is_booksy_pay_payment_window_open %}
    {{ _("Your booking has been confirmed. Pay easily via Booksy and get all set!") }}
{% else %}
    {{ _("Your booking has been confirmed") }}
{% endif %}
{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph(bold=True) %}
        {{ booking_info.business_name }} has confirmed your booking{{additional_member_info}}.
    {% endcall %}

    {{ macros.booking_info(booking_info, to='C') }}

    {% call macros.pos_add_card_infobox(link=booking_info.customer_add_card_link) %}{% endcall %}

    {% call macros.paragraph() %}
        To change or cancel a booking, log into:
        {% call macros.link(url=booking_info.booking_marketplace_url) %}
            booksy.com
        {% endcall %}
        and select the "My bookings" bookmark.
    {% endcall %}

    {% if show_disclosure_obligation_agreement %}
        {% call macros.paragraph() %}
            {{ disclosure_obligation_agreement_description }}
            {% call macros.link(url=disclosure_obligation_agreement_url) %}
                {{ disclosure_obligation_agreement_url_description }}
            {% endcall %}
            {{ disclosure_obligation_agreement_description2 }}
            {% call macros.link(url=disclosure_obligation_agreement_url2) %}
                {{ disclosure_obligation_agreement_url_description2 }}
            {% endcall %}
            {{ disclosure_obligation_agreement_description3 }}
        {% endcall %}
    {% endif %}
{% endblock email_content %}
