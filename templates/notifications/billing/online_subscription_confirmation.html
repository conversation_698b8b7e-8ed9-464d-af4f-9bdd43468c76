{% extends "email_base_business.html" %}
{% import "email_macros.html" as macros %}

{% block email_title %}{{_('Your card has been successfully added')}}{% endblock email_title %}

{% block email_content %}
    {% call macros.paragraph() %}
        <p><h3><b>{{_('Your card has been successfully added')}}</b></h3></p></br>
        <p>{{_('Good news! You no longer need to remember to pay your invoices on time. And on top of that, each month, you’ll get 100 marketing text messages for free.')}}</p></br>
        <p>{{_('As you saved your card on file, your billing period starts today. If you received any invoices earlier which still need to be paid, please do so. For any overpayment resulting from changing the billing date, you’ll receive a correcting invoice within 14 days from adding your card.')}}</p></br>
        <p>{{_('A few more things we’d like you to know:')}}</p></br>
        <p>
            <ol>
                <li>{{_('<b>Your billing period</b> will always start on {subscription_start_date} day of each month. It’s the date when subscription payments will be charged to your card. You will receive a text message with information that your card has been charged.').format(subscription_start_date=subscription_start_date)}}</li></br>
                <li>{{_('The monthly payment (charged to your card) will include:')}}</li>
                    <ol type="a">
                        {% if discount_periods %}
                            <li>{{_('discounted amount for Booksy Biz account for the first {discount_periods} billing periods: {discounted_saas_price} gross and after the discounted period: {sass_price} gross').format(discount_periods=discount_periods, discounted_saas_price=discounted_saas_price, sass_price=saas_price)}}</li>
                        {% else %}
                            <li>{{_('Booksy Biz account: {saas_price} gross,').format(saas_price=saas_price)}}</li>
                        {% endif %}
                        <li>{{_('amount for each active staff member account: {staff_saas_price} gross - {staff_qty}, in total: {staff_total_price} gross,').format(staff_saas_price=staff_saas_price, staff_qty=staff_qty, staff_total_price=staff_total_price)}}</li>
                        <li>{{_('100 marketing text messages for free ({sms_price} gross/text message).').format(sms_price=sms_price)}}</li>
                    </ol></br>
                <li>{{_('<b>Invoices for subscription and additional marketing text messages </b> will be issued <b> within 10 days </b> of charging your card. These are two separate documents which we’ll <NAME_EMAIL> to the billing email address you provided.')}}</li></br>
                <li>{{_('<b>If you add or remove a staff member</b> during your billing period, the amount due will be updated starting from the next billing period.')}}</li></br>
                <li>{{_('Please make sure that you have sufficient funds on your account on the billing date. Otherwise, repeated attempts to charge your card will be made on the following days. If they’re still not successful, your account will be blocked after 14 days.')}}</li></br>
                {% if boost_enabled %}
                    <li>{{_('<b>Boost commission</b> will be charged to your card on an ongoing basis, collectively each day for Boost appointments that took place on that day.')}}</li></br>
                    <li>{{_('Invoice for Booksy Boost will be issued within 15 days of the end of the billing period (which is the same as for subscriptions) and sent by <NAME_EMAIL> to the billing address you provided.')}}</li></br>
                    <li>{{_('Please remember to checkout your appointments or mark them as no-shows <b>on the same working day</b> so that we can calculate commissions correctly.')}}</li></br>
                    <li>{{_('In case an appointment is incorrectly qualified for Boost commission, make sure to claim it in a timely manner. If your claim is accepted, the commission will be refunded to the card that was charged. The <b>deadline for claims is 7 days after the end of the billing period</b> but we recommend that you don’t wait until the last moment.')}}</li></br>
                    <li>{{_("You can find more information on Boost <a href='https://booksy.com/biz/en-us/faq#how-does-boost-work'>HERE</a>.")}}</li></br>
                    <li>{{_('<b>NOTE:</b> On the day your card is saved on file, your previous billing period for Boost ends. If you want to make any claims, you have <b>7 days</b> to do so. The last invoice for your previous billing period will be sent to you within 15 days.')}}</li></br>
                {% endif %}
            </ol>
        </p>
    {% endcall %}
{% endblock email_content %}
