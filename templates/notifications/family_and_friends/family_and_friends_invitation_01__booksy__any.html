{% extends "family_and_friends/family_and_friends_invitation_base__booksy__any.html" %}
{% import "email_macros.html" as macros %}
{% block email_title %} {{ _('{} has invited you to join their Booksy Family & Friends network').format(parent_name.strip()) }}{% endblock %}



{% block email_content %}
  <div style="background-color: #f8f8f8">
    <div style="padding-top: 32px; text-align: center;">
      <img alt="family and friends" src="{{STATIC_FULL_URL}}mail/family_and_friends/family_and_friends.png" />
    </div>


    {% call macros.paragraph(center=True, font_size="32px", color="#202020", lines_after=1) %}
      <div style="font-family: ProximaNova-Bold; font-weight: bold; padding-top: 16px; line-height: 40px; width: 300px">
      {{ _('Join {}’s Family & Friends!').format(parent_first_name) }}
      </div>
    {% endcall %}
  </div>
  <div style="padding: 30px">

    {% call macros.paragraph(center=True, color="#202020", lines_after=1) %}
      {{ _('<strong>{}</strong> has invited you to join their Family & Friends! Sign in or sign up to Booksy to accept the invitation. After the invitation is accepted they will be able to book and manage appointments on your behalf.').format(parent_name.strip()) }}
    {% endcall %}


    {% call macros.paragraph(center=True, lines_after=2) %}
      {% call macros.extended_button(url=learn_more_url, lines_after=0, background_color="#ffffff", border_radius="32px", font_size="16px", color="#000000", additional="border: 2px solid #e7e7e7;") %}
        {{ _('Learn more') }}
      {% endcall %}
    {% endcall %}
    {% call macros.paragraph(center=True, lines_after=0) %}
    <div style="width:  398px; height: 48px; padding-bottom: 16px;">
      {% call macros.extended_button(url=accept_url, display_style="block", lines_after=0,background_color="#00a5ae", border_radius="8px", font_size="16px") %}
        {{ _('Join Booksy Family & Friends') }}
      {% endcall %}
    </div>
  {% endcall %}


    {% call macros.paragraph(center=True) %}
      {% if is_gdpr %}
       {{ _('You can find more information ') }}
        {% call macros.link(url=information_obligation_url) %}
          {{ _('here.') }}
        {% endcall %}
      {% endif %}
    {% endcall %}

    {% call macros.paragraph(font_size="14px", center=True) %}
      {% if is_gdpr %}
          <div id="gdpr-additional-message">
             {{_('By clicking the Join Family & Friends button above, you consent to sharing your personal data with businesses at which appointments will be scheduled for you. The data you provide will be used for the purpose of scheduling bookings and sending you notifications related to them.')}}
          </div>
      {% endif %}
    {% endcall %}


  </div>

{% endblock email_content %}
