{% if short_status in ["success", "charged", "refunded"] %}
    {% set color='#44B560' %}{% set background='#ECF7EF' %}{# green #}
{% elif short_status in ["call_for_payment", "authorized", "awaiting"] %}
    {% set color='#FFAB00' %}{% set background='#FFF6E5' %}{# orange #}
{% elif short_status in ["failed", "chargeback"] %}
    {% set color='#FF1100' %}{% set background='#FFE7E5' %}{# red #}
{% elif short_status in ["archived", "canceled"] %}
    {% set color='#8C8B88' %}{% set background='#F4F4F3' %}{# gray #}
{% else %}
    {% set color='#FFAB00' %}{% set background='#FFF6E5' %}{# orange #}
{% endif %}

{% set color_epsilon='#605F5D' %}
{% set color_beta='#8c8b88' %}

    <div style="width: 100%;  color: #383734; font-family: 'Proxima Nova', Arial; font-size: 11px; line-height: 18px; font-weight: 400; margin: 15px auto 0;">
    <center>
        <div style="padding: 15px 15px 0px 15px; border-top: 2px solid #eee; border-left: 2px solid #eee; border-right: 2px solid #eee; background: #fff;">
            <table style="width: 100%; margin-bottom: 10px; color: {{ color_epsilon }};"
                   cellpadding="0" cellspacing="0">
                <tr>
                    <td>
                        <div style="">{{ transaction.business_name }}</div>
                        <div style="">{{ transaction.business_address }}</div>
                    </td>
                </tr>
            </table>

            <table style="width: 100%; padding-bottom: 10px;" cellpadding="0" cellspacing="0">
                <tr>
                    <td style="background: #E1E1E1; height: 1px;"></td>
                </tr>
            </table>

            <table  style="width: 100%; color: {{ color_epsilon }};"  cellpadding="0" cellspacing="0">
                <tr>
                    <td style="padding-bottom: 15px; font-size: 11px">
                        {% if transaction.customer_data %}
                            <div style="">{{ transaction.customer_data.replace(', ', '<br/>') }}</div>
                        {% endif %}
                    </td>
                    <td style="vertical-align: top; text-align: right;">
                         <span>{{ receipt_created | date("DATE_FORMAT") }}</span>
                        <div>
                            <span style="line-height: 12px; display: inline-block; text-align: right; font-size: 1.1em; font-weight: 600; text-transform: uppercase; color: {{ color }}; border-radius: 2px; padding: 6px 0px 2px 0px; min-width: 100px;">
                                {{ short_status_label }}
                            </span>
                        </div>
                        <div>
                            <span style="font-size: 0.9em; color: #FFAB00;">
                                {% if short_status_description %}
                                    {{ short_status_description }}
                                {% endif %}&nbsp;
                            </span>
                        </div>
                    </td>
                </tr>
            </table>

    <table style="width: 100%;" cellpadding="0" cellspacing="0">
        <thead >
            <tr>
                <th style="width: 70%; text-align: left; font-weight: 400; font-size: 10px; border-bottom: 1px solid #E1E1E1; color: #c3c1bc; text-transform: uppercase;">
                    {{ _("Item") }}
                </th>
                <th style="width: 10%;  border-bottom: 1px solid #E1E1E1;">&nbsp;

                </th>
                <th style="text-align: right;width: 20%;  font-weight: 400; font-size: 10px; border-bottom: 1px solid #E1E1E1; color: #c3c1bc; text-transform: uppercase;">
                    {{ _("Amount") }}
                </th>
            </tr>
        </thead>
        <tbody>
        {% for row in transaction.rows %}
            <tr>
                <td style="text-align: left;  border-bottom: 1px solid #E1E1E1; padding: 10px 0; vertical-align: top">
                    {{ row.name_line_1 }}
                    {% if row.name_line_2 %}
                        <br><span style="color: #8c8b88; font-size: 11px;"> {{ row.name_line_2 }}</span>
                    {% endif %}
                </td>
                <td style="text-align: right; padding: 10px;  border-bottom: 1px solid #E1E1E1; vertical-align: top">
                    x{{ row.quantity }}
                </td>
                <td style="text-align: right; padding: 10px 0;  border-bottom: 1px solid #E1E1E1; vertical-align: top;">
                    {{ row.formatted_total }}
                </td>
            </tr>
        {% endfor %}
       </tbody>
    </table>

        <table  style="width: 100%; padding: 15px 0 0 0;"  cellpadding="0" cellspacing="0">
            {% for summary in transaction.summaries %}
                <tr>
                    <td style="color: #8c8b88; text-align: right; padding: 0 10px 10px 10px; width: 80%; text-transform: uppercase">{{ summary.label }}</td>
                    <td style="text-align: right; padding: 0 0 10px 0; width: 20%;">{{ summary.text }}</td>
                </tr>
            {% endfor %}
        </table>


        <table  style="width: 100%; border-top: 1px solid #E1E1E1; margin-bottom: 15px; padding-top: 15px"  cellpadding="0" cellspacing="0">
            <tr>
                <td style="color: #8c8b88; text-align: right; padding: 0 10px 10px 10px; width: 80%; text-transform: uppercase;">
                    {% if latest_receipt_serialized.remaining %}
                        {{ _("Remaining") }}
                    {% else %}
                        {{ _("Total") }}
                    {% endif %}
                </td>
                <td style="text-align: right; padding: 0 0 10px 0; width: 20%; font-size: 20px;">
                    {% if latest_receipt_serialized.remaining  %}
                        {{ latest_receipt_serialized.remaining  }}
                    {% else %}
                        {{ latest_receipt_serialized.total }}
                    {% endif %}
                </td>
            </tr>
        </table>

        <table style="width: 100%; padding: 15px 0; color: {{ color_beta }}" cellpadding="0" cellspacing="0">
            <tr>
                {% if show_fiscal_disclaimer %}
                <td>
                    {% if latest_receipt_serialized.receipt_number %}
                    {{ _("Receipt") }}: {{ latest_receipt_serialized.receipt_number }} •
                    {% endif %}
                    {{ receipt_created | date("DATETIME_FORMAT") }}
                </td>
                {% else %}
                <td>
                    {{ _("Receipt") }}: {{ latest_receipt.assigned_number }}, ID: {{ instance.id }} • {{ receipt_created | date("DATETIME_FORMAT") }}
                </td>
                {% endif %}
            </tr>
        </table>

        <table  style="width: 100%; padding: 15px 0; color: {{ color_beta }};"  cellpadding="0" cellspacing="0">
            {% for row in payment_rows %}
                <tr style="margin-bottom: 5px;">
                    <td style="text-align: left; width: 60%; border-top: 1px solid #E1E1E1;">
                        {{ row.status_formatted }} • {{ row.label }} • {{ row.created | date("DATETIME_FORMAT")}}
                    </td>
                    {% if row.status == 'refunded' %}
                        <td style="text-align: right; width: 20%; border-top: 1px solid #E1E1E1; color: #f10;">
                            -{{ row.amount_text }}
                        </td>
                    {% else %}
                        <td style="text-align: right; width: 20%; border-top: 1px solid #E1E1E1;">
                            {{ row.amount_text }}
                        </td>
                    {% endif %}
                </tr>
            {% endfor %}
        </table>

        {% if transaction.receipt_footer_line_1 or transaction.receipt_footer_line_2  %}
            <table style="width: 100%; padding: 0 0 15px 0;" cellpadding="0" cellspacing="0">
                <tr>
                    <td style="background: #E1E1E1; height: 1px;"></td>
                </tr>
            </table>

            <table style="width: 100%; padding: 5px 0;"
                   cellpadding="0" cellspacing="0">
                <tr>
                    <td>
                        {% if transaction.receipt_footer_line_1 %}
                            <div style="text-align: center; font: normal 10px/14px 'Proxima Nova', Arial; line-height: normal">{{ transaction.receipt_footer_line_1 }}</div>
                        {% endif %}
                        {% if transaction.receipt_footer_line_2 %}
                            <div style="text-align: center; font: normal 10px/14px 'Proxima Nova', Arial; line-height: normal">{{ transaction.receipt_footer_line_2 }}</div>
                        {% endif %}
                    </td>
                </tr>
            </table>
        {% endif %}

        <div>
            {% if show_fiscal_disclaimer %}
                <br/>
                <p style="font: normal 10px/14px 'Proxima Nova', Arial; line-height: normal">
                    {{ _('The above confirmation of sale is not a fiscal receipt.') }}
                </p>
            {% else %}
                {% if show_polish_fiscal_disclaimer %}
                    <br/>
                    <p style="font: normal 10px/14px 'Proxima Nova', Arial; line-height: normal">
                        Powyższe potwierdzenie sprzedaży nie jest paragonem fiskalnym
                    </p>
                {% endif %}
            {% endif %}
        </div>
    </div><img src="{{ STATIC_FULL_URL }}scenarios/claws.png" style="width: 100%; vertical-align: top;" alt="">
    </center>
    </div>
