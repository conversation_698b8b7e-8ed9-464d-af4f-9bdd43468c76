{% extends "KYC_business_base.html" %}

{% block email_title %}{{ _("Chargeback dispute reversed") }}{% endblock email_title %}

{% block email_content %}
<center>
    <table align="center" border="0" cellpadding="0" cellspacing="0" height="100%" id="bodyTable"
           style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;height: 100%;margin: 0;padding: 0;width: 100%;"
           width="100%">
        <tr>
            <td align="center" id="bodyCell"
                style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;height: 100%;margin: 0;padding: 10px;width: 100%;border-top: 0;"
                valign="top">
                <!-- BEGIN TEMPLATE // -->
                <!--[if (gte mso 9)|(IE)]>
                <table align="center" border="0" cellspacing="0" cellpadding="0" width="500" style="width:500px;">
                    <tr>
                        <td align="center" valign="top" width="500" style="width:500px;">
                <![endif]-->
                <table border="0" cellpadding="0" cellspacing="0" class="templateContainer"
                       style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;border: 0;max-width: 500px !important;"
                       width="100%">
                    <tr>
                        <td id="templatePreheader"
                            style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-image: none;background-repeat: no-repeat;background-position: center;background-size: cover;border-top: 0;border-bottom: 0;padding-top: 9px;padding-bottom: 9px;"
                            valign="top">
                            <table border="0" cellpadding="0" cellspacing="0" class="mcnTextBlock"
                                   style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"
                                   width="100%">
                                <tbody class="mcnTextBlockOuter">
                                <tr>
                                    <td class="mcnTextBlockInner"
                                        style="padding-top: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"
                                        valign="top">
                                        <!--[if mso]>
                                        <table align="left" border="0" cellspacing="0" cellpadding="0" width="100%"
                                               style="width:100%;">
                                            <tr>
                                        <![endif]-->

                                        <!--[if mso]>
                                        <td valign="top" width="500" style="width:500px;">
                                        <![endif]-->
                                        <table align="left" border="0" cellpadding="0" cellspacing="0"
                                               class="mcnTextContentContainer"
                                               style="max-width: 100%;min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"
                                               width="100%">
                                        </table>
                                        <!--[if mso]>
                                        </td>
                                        <![endif]-->

                                        <!--[if mso]>
                                        </tr>
                                        </table>
                                        <![endif]-->
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td id="templateBody"
                            style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-image: none;background-repeat: no-repeat;background-position: center;background-size: cover;border-top: 0;padding-top: 0;padding-bottom: 9px;"
                            valign="top">
                            <table border="0" cellpadding="0" cellspacing="0" class="mcnTextBlock"
                                   style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"
                                   width="100%">
                                <tbody class="mcnTextBlockOuter">
                                <tr>
                                    <td class="mcnTextBlockInner"
                                        style="padding-top: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"
                                        valign="top">
                                        <!--[if mso]>
                                        <table align="left" border="0" cellspacing="0" cellpadding="0" width="100%"
                                               style="width:100%;">
                                            <tr>
                                        <![endif]-->

                                        <!--[if mso]>
                                        <td valign="top" width="500" style="width:500px;">
                                        <![endif]-->
                                        <table align="left" border="0" cellpadding="0" cellspacing="0"
                                               class="mcnTextContentContainer"
                                               style="max-width: 100%;min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"
                                               width="100%">
                                            <tbody>
                                            <tr>

                                                <td class="mcnTextContent"
                                                    style="padding-top: 0;padding-right: 0px;padding-bottom: 9px;padding-left: 0px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;word-break: break-word;color: #202020;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;"
                                                    valign="top">

                                                    <div style="text-align: left;">
                                                        <br>
                                                        <table border="1" width="90%" style="margin-inline: 5%; margin-left: auto; margin-right: auto;">
                                                            <tbody>
                                                            <tr>
                                                                <td><b>{{ _("Type of service") }}</b></td>
                                                                <td>{{ service_name }}</td>
                                                            </tr>
                                                            <tr>
                                                                <td><b>{{ _("Booking date and time") }}</b></td>
                                                                <td>{{ booking_date }} {{ booking_time }}</td>
                                                            </tr>
                                                            <tr>
                                                                <td><b>{{ _("Transaction date and time") }}</b></td>
                                                                <td>{{ transaction_date }} {{ transaction_time }}</td>
                                                            </tr>
                                                            <tr>
                                                                <td><b>{{ _("Transaction amount and currency") }}</b></td>
                                                                <td>{{ amount }}</td>
                                                            </tr>
                                                            <tr>
                                                                <td><b>{{ _("Booking reference") }}</b></td>
                                                                <td>{{ booking_reference }}</td>
                                                            </tr>
                                                            <tr>
                                                                <td><b>{{ _("Customer ID (email, reference number, ...)") }}</b></td>
                                                                <td>{{ customer_email }} {{ customer_phone }}</td>
                                                            </tr>
                                                            <tr>
                                                                <td><b>{{ _("Card scheme") }}</b></td>
                                                                <td>{{ card_scheme }}</td>
                                                            </tr>
                                                            <tr>
                                                                <td><b>{{ _("Dispute reason code") }}</b></td>
                                                                <td>{{ reason_code }}</td>
                                                            </tr>
                                                            <tr>
                                                                <td><b>{{ _("Dispute reference") }}</b></td>
                                                                <td>{{ dispute_psp_reference }}</td>
                                                            </tr>
                                                            </tbody>
                                                        </table>
                                                        <br>
                                                        {{ _("We want to inform you that the above disputed transaction was successfully defended, therefore the transaction amount in question will be sent to you in the next payout.") }}<br>
                                                        <br>
                                                        {{ _("Should you have any additional questions, <NAME_EMAIL>.") }}<br>
                                                        <br>
                                                    </div>

                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                        <!--[if mso]>
                                        </td>
                                        <![endif]-->

                                        <!--[if mso]>
                                        </tr>
                                        </table>
                                        <![endif]-->
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <table border="0" cellpadding="0" cellspacing="0" class="mcnTextBlock"
                                   style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"
                                   width="100%">
                                <tbody class="mcnTextBlockOuter">
                                <tr>
                                    <td class="mcnTextBlockInner"
                                        style="padding-top: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"
                                        valign="top">
                                        <!--[if mso]>
                                        <table align="left" border="0" cellspacing="0" cellpadding="0" width="100%"
                                               style="width:100%;">
                                            <tr>
                                        <![endif]-->

                                        <!--[if mso]>
                                        <td valign="top" width="500" style="width:500px;">
                                        <![endif]-->
                                        <table align="left" border="0" cellpadding="0" cellspacing="0"
                                               class="mcnTextContentContainer"
                                               style="max-width: 100%;min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"
                                               width="100%">
                                            <tbody>
                                            <tr>

                                                <td class="mcnTextContent"
                                                    style="padding-top: 0;padding-right: 0px;padding-bottom: 9px;padding-left: 0px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;word-break: break-word;color: #202020;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;"
                                                    valign="top">


                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                        <!--[if mso]>
                                        </td>
                                        <![endif]-->

                                        <!--[if mso]>
                                        </tr>
                                        </table>
                                        <![endif]-->
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td id="templateFooter"
                            style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-image: none;background-repeat: no-repeat;background-position: center;background-size: cover;border-top: 0;border-bottom: 0;padding-top: 9px;padding-bottom: 9px;"
                            valign="top"></td>
                    </tr>
                </table>
                <!--[if (gte mso 9)|(IE)]>
                </td>
                </tr>
                </table>
                <![endif]-->
                <!-- // END TEMPLATE -->
            </td>
        </tr>
    </table>
</center>
{% endblock email_content %}