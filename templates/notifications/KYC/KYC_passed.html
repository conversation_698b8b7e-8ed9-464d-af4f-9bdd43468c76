{% extends "KYC_business_base.html" %}

{% block email_title %}{{ _("You’ve just set up Booksy Mobile Payments") }}{% endblock email_title %}

{% block email_content %}
<center>
  <table align="center" border="0" cellpadding="0" cellspacing="0" height="100%" id="bodyTable" width="100%">
    <tr>
      <td align="center" id="bodyCell" valign="top">
        <!-- BEGIN TEMPLATE // -->
        <!--[if (gte mso 9)|(IE)]>
        <table align="center" border="0" cellspacing="0" cellpadding="0" width="500" style="width:500px;">
          <tr>
            <td align="center" valign="top" width="500" style="width:500px;">
        <![endif]-->
        <table border="0" cellpadding="0" cellspacing="0" class="templateContainer" width="100%" style="width: 500px">
          <tr>
            <td id="templatePreheader" valign="top">
              <table border="0" cellpadding="0" cellspacing="0" class="mcnTextBlock" style="min-width:100%;" width="100%">
                <tbody class="mcnTextBlockOuter">
                <tr>
                  <td class="mcnTextBlockInner" style="padding-top:9px;" valign="top">
                    <!--[if mso]>
                    <table align="left" border="0" cellspacing="0" cellpadding="0" width="100%" style="width:100%;">
                      <tr>
                    <![endif]-->

                    <!--[if mso]>
                    <td valign="top" width="500" style="width:500px;">
                    <![endif]-->
                    <table align="left" border="0" cellpadding="0" cellspacing="0" class="mcnTextContentContainer" style="max-width:100%; min-width:100%;" width="100%">
                      <tbody>
                      </tbody>
                    </table>
                    <!--[if mso]>
                    </td>
                    <![endif]-->

                    <!--[if mso]>
                    </tr>
                    </table>
                    <![endif]-->
                  </td>
                </tr>
                </tbody>
              </table>
            </td>
          </tr>
          <tr>
            <td id="templateHeader" valign="top">
              <table border="0" cellpadding="0" cellspacing="0" class="mcnImageBlock" style="min-width:100%;" width="100%">
                <tbody class="mcnImageBlockOuter">
                <tr>
                  <td class="mcnImageBlockInner" valign="top">
                    <table align="left" border="0" cellpadding="0" cellspacing="0" class="mcnImageContentContainer" style="min-width:100%;" width="100%">
                      <tbody>
                      <tr>
                        <td class="mcnImageContent" style="padding-top: 0; padding-bottom: 0; text-align:center;" valign="top">
                          <img align="center" alt="{{ _("Booksy.com") }}" src="{{ STATIC_FULL_URL }}mail/payments-welcome-email.gif" style="max-width:500px; padding-bottom: 0; display: inline !important; vertical-align: bottom;" width="500">
                        </td>
                      </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
                </tbody>
              </table>
              <table border="0" cellpadding="0" cellspacing="0" class="mcnTextBlock" style="min-width:100%;" width="100%">
                <tbody class="mcnTextBlockOuter">
                <tr>
                  <td class="mcnTextBlockInner" style="padding-top:9px;" valign="top">
                    <!--[if mso]>
                    <table align="left" border="0" cellspacing="0" cellpadding="0" width="100%" style="width:100%;">
                      <tr>
                    <![endif]-->

                    <!--[if mso]>
                    <td valign="top" width="500" style="width:500px;">
                    <![endif]-->
                    <table align="left" border="0" cellpadding="0" cellspacing="0" class="mcnTextContentContainer" style="max-width:100%; min-width:100%;" width="100%">
                      <tbody>
                      <tr>

                        <td class="mcnTextContent" style="padding: 0px 0px 9px; line-height: 200%;" valign="top">

                          <span style="font-size:28px"><strong>{{ _("Hurray! You've enabled mobile payments") }}</strong></span>
                        </td>
                      </tr>
                      </tbody>
                    </table>
                    <!--[if mso]>
                    </td>
                    <![endif]-->

                    <!--[if mso]>
                    </tr>
                    </table>
                    <![endif]-->
                  </td>
                </tr>
                </tbody>
              </table>
              <table border="0" cellpadding="0" cellspacing="0" class="mcnBoxedTextBlock" style="min-width:100%;" width="100%">
                <!--[if gte mso 9]>
                <table align="center" border="0" cellspacing="0" cellpadding="0" width="100%">
                <![endif]-->
                <tbody class="mcnBoxedTextBlockOuter">
                <tr>
                  <td class="mcnBoxedTextBlockInner" valign="top">

                    <!--[if gte mso 9]>
                    <td align="center" valign="top" ">
                    <![endif]-->
                    <table align="left" border="0" cellpadding="0" cellspacing="0" class="mcnBoxedTextContentContainer" style="min-width:100%;" width="100%">
                      <tbody>
                      <tr>

                        <td style="padding-top:9px; padding-left:0px; padding-bottom:9px; padding-right:0px;">

                          <table border="0" cellspacing="0" class="mcnTextContentContainer" style="min-width: 100% !important;background-color: #EBEBEB;" width="100%">
                            <tbody>
                            <tr>
                              <td class="mcnTextContent" style="padding: 18px;color: #F2F2F2;font-family: Helvetica;font-size: 14px;font-weight: normal;text-align: center;" valign="top">
                                <div style="text-align: left;"><span style="color:#00A3AD"><span style="font-size:24px"><strong>{{ _("Use deposits to guarantee your income") }}</strong></span></span><br>
                                  <br>
                                  <span style="font-size:16px"><font color="#000000">{{ _("Protect yourself against no-shows with Booksy users' favorite Payments feature. Deposits charge your clients a portion of the service price when they book an appointment. Three cheers for guaranteed income, regardless of no-shows and late cancellations!") }}</font></span>
                                </div>

                              </td>
                            </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                      </tbody>
                    </table>
                    <!--[if gte mso 9]>
                    </td>
                    <![endif]-->

                    <!--[if gte mso 9]>
                    </tr>
                    </table>
                    <![endif]-->
                  </td>
                </tr>
                </tbody>
              </table>
            </td>
          </tr>
          <tr>
            <td id="templateBody" valign="top">
              <table border="0" cellpadding="0" cellspacing="0" class="mcnBoxedTextBlock" style="min-width:100%;" width="100%">
                <!--[if gte mso 9]>
                <table align="center" border="0" cellspacing="0" cellpadding="0" width="100%">
                <![endif]-->
                <tbody class="mcnBoxedTextBlockOuter">
                <tr>
                  <td class="mcnBoxedTextBlockInner" valign="top">

                    <!--[if gte mso 9]>
                    <td align="center" valign="top" ">
                    <![endif]-->
                    <table align="left" border="0" cellpadding="0" cellspacing="0" class="mcnBoxedTextContentContainer" style="min-width:100%;" width="100%">
                      <tbody>
                      <tr>

                        <td style="padding-top:9px; padding-left:0px; padding-bottom:9px; padding-right:0px;">

                          <table border="0" cellspacing="0" class="mcnTextContentContainer" style="min-width:100% !important;" width="100%">
                            <tbody>
                            <tr>
                              <td class="mcnTextContent" style="padding: 18px;color: #2A2C32;font-family: &quot;Helvetica Neue&quot;, Helvetica, Arial, Verdana, sans-serif;font-size: 14px;font-weight: normal;text-align: center;" valign="top">
                                <div style="text-align: left;"><span style="font-size:16px"><strong>{{ _("Don't forget about your additional mobile payments perks:") }}</strong></span><br>
                                  <br>
                                  <span style="color:#00A3Ad"><span style="font-size:20px"><strong>{{ _("Experience a faster, more organized checkout process") }}</strong></span></span><br>
                                  <span style="font-size:16px">{{ _("After appointments, use <strong>Mobile Payments</strong> to charge your client’s stored credit card directly – no hardware, devices or gadgets needed. You can accept Google Pay and Apple Pay right in your Booksy Biz app.") }}</span><br>
                                  <br>
                                  <span style="font-size:20px"><strong><span style="color:#00a3ad">{{ _("Set up Cancellation Fees for additional no-show protection") }}</span></strong></span><br>
                                  <span style="font-size:16px">{{ _("Set up <strong>Cancellation Fees</strong> to automatically or manually charge clients if they miss their appointment or cancel on short notice.") }}</span></div>

                              </td>
                            </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                      </tbody>
                    </table>
                    <!--[if gte mso 9]>
                    </td>
                    <![endif]-->

                    <!--[if gte mso 9]>
                    </tr>
                    </table>
                    <![endif]-->
                  </td>
                </tr>
                </tbody>
              </table>
            </td>
          </tr>
          <tr>
            <td id="templateFooter" valign="top"></td>
          </tr>
        </table>
        <!--[if (gte mso 9)|(IE)]>
        </td>
        </tr>
        </table>
        <![endif]-->
        <!-- // END TEMPLATE -->
      </td>
    </tr>
  </table>
</center>
{% endblock email_content %}