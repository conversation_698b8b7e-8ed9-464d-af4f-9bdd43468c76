{% extends "KYC_business_base.html" %}

{% block email_title %}{{ _("Daily Payout Summary") }}{% endblock email_title %}

{% block email_content %}
  <body>
    <center>
      <table align="center" border="0" cellpadding="0" cellspacing="0" width="100%" id="bodyTable" style="height:100%;">
        <tr>
          <td align="center" valign="top" id="bodyCell">
            <!-- BEGIN TEMPLATE // -->
            <!--[if (gte mso 9)|(IE)]>
            <table align="center" border="0" cellspacing="0" cellpadding="0" width="500" style="width:500px;">
              <tr>
                <td align="center" valign="top" width="500" style="width:500px;">
                  <![endif]-->
            <table border="0" cellpadding="0" cellspacing="0" width="100%" class="templateContainer">
              <tr>
                <td valign="top" id="templatePreheader">
                  <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock" style="min-width:100%;">
                    <tbody class="mcnTextBlockOuter">
                      <tr>
                        <td valign="top" class="mcnTextBlockInner" style="padding-top:9px;">
                          <!--[if mso]>
                          <table align="left" border="0" cellspacing="0" cellpadding="0" width="100%" style="width:100%;">
                            <tr>
                              <![endif]-->

                          <!--[if mso]>
                          <td valign="top" width="500" style="width:500px;">
                            <![endif]-->
                          <table align="left" border="0" cellpadding="0" cellspacing="0" style="max-width:100%;min-width:100%;" width="100%" class="mcnTextContentContainer">
                            <tbody>
                              <tr>
                                <td valign="top" class="mcnTextContent" style="padding:0px 0px 9px;text-align:center;">


                                </td>
                              </tr>
                            </tbody>
                          </table>
                          <!--[if mso]>
                        </td>
                        <![endif]-->
                          <!--[if mso]>
                        </tr>
                      </table>
                      <![endif]-->
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
              <tr>
                <td valign="top" id="templateBody">
                  <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock" style="min-width:100%;">
                    <tbody class="mcnTextBlockOuter">
                      <tr>
                        <td valign="top" class="mcnTextBlockInner" style="padding-top:9px;">
                          <!--[if mso]>
                          <table align="left" border="0" cellspacing="0" cellpadding="0" width="100%" style="width:100%;">
                            <tr>
                              <![endif]-->

                          <!--[if mso]>
                          <td valign="top" width="500" style="width:500px;">
                            <![endif]-->
                          <table align="left" border="0" cellpadding="0" cellspacing="0" style="max-width:100%;min-width:100%;" width="100%" class="mcnTextContentContainer">
                            <tbody>
                              <tr>
                                <td valign="top" class="mcnTextContent" style="padding-top:0;padding-right:0px;padding-bottom:9px;padding-left:0px;">

                                  <div style="text-align:center;">
                                    <span style="font-size:18px"><strong><span style="color:#05cfa6">{{ _("You have money from online payments headed your way 💸 ") }}</span></strong></span>
                                    <br>
                                    <br><span style="font-size:42px"><strong><span style="color:#05cfa6">{{ total_amount_formatted }}</span></strong></span>
                                    <br><span style="font-size:14px"><span style="color:#000000">{{ _("Total Payment at checkout Collected on %s") % date }}</span></span>
                                    <br>
                                    <br><span style="font-size:14px"><span style="color:#000000">{{ _("The money from these online payments will be deposited in your bank account within <strong>2-5 business days</strong>. A detailed report of your online payments is attached.") }}</span></span>

                                  </div>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnDividerBlock" style="min-width:100%;">
                    <tbody class="mcnDividerBlockOuter">
                      <tr>
                        <td class="mcnDividerBlockInner" style="min-width:100%;padding:18px;">
                          <table class="mcnDividerContent" border="0" cellpadding="0" cellspacing="0" width="100%" style="min-width:100%;border-top:1px solid #EAEAEA;">
                            <tbody>
                              <tr>
                                <td>
                                  <span></span>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                          <!--
                          <td class="mcnDividerBlockInner" style="padding:18px;">
                            <hr class="mcnDividerContent" style="border-bottom-width:0;border-left-width:0;border-right-width:0;margin-top:0;margin-right:0;margin-bottom:0;margin-left:0;" />
                            -->
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock" style="min-width:100%;">
                    <tbody class="mcnTextBlockOuter">
                      <tr>
                        <td valign="top" class="mcnTextBlockInner" style="padding-top:9px;">
                          <!--[if mso]>
                          <table align="left" border="0" cellspacing="0" cellpadding="0" width="100%" style="width:100%;">
                            <tr>
                              <![endif]-->

                          <!--[if mso]>
                          <td valign="top" width="500" style="width:500px;">
                            <![endif]-->
                          <table align="left" border="0" cellpadding="0" cellspacing="0" style="max-width:70%;min-width:70%;" width="70%" class="mcnTextContentContainer">
                            <tbody>
                              <tr>
                                <td valign="top" class="mcnTextContent" style="padding-top:0;padding-right:0px;padding-bottom:9px;padding-left:0px;">

                                  <span style="color:#A9A9A9">{{ date }}</span>
                                  <br>
                                  <br><strong><span style="font-size:10px">{{ _("INCOME") }}</span></strong>
                                  <br>
                                  <table border="0" cellpadding="0" cellspacing="0"  style="max-width:100%;min-width:100%;" width="100%">
                                  <tbody>
                                      {% for income in incomes %}
                                          <tr style="font-size:14px">
                                              <td width="50%">
                                                  {{ income.payment_type }}
                                              </td>
                                              <td width="20%">
                                                  x{{ income.quantity }}
                                              </td>
                                              <td width="30%" align="right">
                                                  {{ income.total_formatted }}
                                              </td>
                                          </tr>
                                      {% endfor %}
                                      </tbody>
                                  </table>

                                  <br>
                                  <br><strong style="font-size:10px">{{ _("COSTS") }}</strong>
                                  <br>
                                  <table border="0" cellpadding="0" cellspacing="0"  style="max-width:100%;min-width:100%;" width="100%">
                                      <tbody>
                                      {% for cost in costs %}
                                          <tr style="font-size:14px">
                                              <td width="70%">
                                                  {{ cost.payment_type }}
                                              </td>
                                              <td width="30%" align="right">
                                                  {{ cost.total_formatted }}
                                              </td>
                                          </tr>
                                      {% endfor %}
                                      </tbody>
                                  </table>
                                  <br>
                                  <br>
                                  <table border="0" cellpadding="0" cellspacing="0"  style="max-width:100%;min-width:100%;" width="100%">
                                      <tbody>
                                          <tr>
                                              <td width="70%">
                                                  <strong>{{ _("TOTAL") }}</strong>
                                              </td>
                                              <td width="30%" align="right">
                                                  {{ total_amount_formatted }}
                                              </td>
                                          </tr>
                                      </tbody>
                                  </table>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                          <!--[if mso]>
                        </td>
                        <![endif]-->
                          <!--[if mso]>
                        </tr>
                      </table>
                      <![endif]-->
                        </td>
                      </tr>
                    </tbody>
                  </table>

                  <div style="font-size:12px;">{{ _("This money will be available in your bank account within 2-5 business days.") }}</div>

                  <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnDividerBlock" style="min-width:100%;">
                    <tbody class="mcnDividerBlockOuter">
                      <tr>
                        <td class="mcnDividerBlockInner" style="min-width:100%;padding:18px;">
                          <table class="mcnDividerContent" border="0" cellpadding="0" cellspacing="0" width="100%" style="min-width:100%;border-top:1px solid #EAEAEA;">
                            <tbody>
                              <tr>
                                <td>
                                  <span></span>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                          <!--
                          <td class="mcnDividerBlockInner" style="padding:18px;">
                            <hr class="mcnDividerContent" style="border-bottom-width:0;border-left-width:0;border-right-width:0;margin-top:0;margin-right:0;margin-bottom:0;margin-left:0;" />
                            -->
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
              <tr>
                <td valign="top" id="templateFooter"></td>
              </tr>
            </table>
            <!--[if (gte mso 9)|(IE)]>
          </td>
        </tr>
      </table>
      <![endif]-->
            <!-- // END TEMPLATE -->
          </td>
        </tr>
      </table>
    </center>
{% endblock email_content %}