{% extends "KYC_customer_base.html" %}

{% block email_title %}{{ _("Refund Has Been Issued") }}{% endblock email_title %}

{% block content_style %}padding:20px 0;text-align: center;{% endblock content_style %}

{% block email_content %}
  <center>
  <table align="center" border="0" cellpadding="0" cellspacing="0" height="100%" id="bodyTable" width="100%">
    <tr>
      <td align="center" id="bodyCell" valign="top">
        <!-- BEGIN TEMPLATE // -->
        <!--[if (gte mso 9)|(IE)]>
        <table align="center" border="0" cellspacing="0" cellpadding="0" width="500" style="width:500px;">
          <tr>
            <td align="center" valign="top" width="500" style="width:500px;">
        <![endif]-->
        <table border="0" cellpadding="0" cellspacing="0" class="templateContainer" width="100%" style="width: 500px">
          <tr>
            <td id="templatePreheader" valign="top">
              <table border="0" cellpadding="0" cellspacing="0" class="mcnTextBlock" style="min-width:100%;" width="100%">
                <tbody class="mcnTextBlockOuter">
                <tr>
                  <td class="mcnTextBlockInner" style="padding-top:9px;" valign="top">
                    <!--[if mso]>
                    <table align="left" border="0" cellspacing="0" cellpadding="0" width="100%" style="width:100%;">
                      <tr>
                    <![endif]-->

                    <!--[if mso]>
                    <td valign="top" width="500" style="width:500px;">
                    <![endif]-->
                    <table align="left" border="0" cellpadding="0" cellspacing="0" class="mcnTextContentContainer" style="max-width:100%; min-width:100%;" width="100%">
                    </table>
                    <!--[if mso]>
                    </td>
                    <![endif]-->

                    <!--[if mso]>
                    </tr>
                    </table>
                    <![endif]-->
                  </td>
                </tr>
                </tbody>
              </table>
            </td>
          </tr>
          <tr>
            <td id="templateHeader" valign="top">
              <table border="0" cellpadding="0" cellspacing="0" class="mcnTextBlock" style="min-width:100%;" width="100%">
                <tbody class="mcnTextBlockOuter">
                <tr>
                  <td class="mcnTextBlockInner" style="padding-top:9px;" valign="top">
                    <!--[if mso]>
                    <table align="left" border="0" cellspacing="0" cellpadding="0" width="100%" style="width:100%;">
                      <tr>
                    <![endif]-->

                    <!--[if mso]>
                    <td valign="top" width="500" style="width:500px;">
                    <![endif]-->
                    <table align="left" border="0" cellpadding="0" cellspacing="0" class="mcnTextContentContainer" style="max-width:100%; min-width:100%;" width="100%">
                      <tbody>
                      <tr>

                        <td class="mcnTextContent" style="padding-top:0; padding-right:0px; padding-bottom:9px; padding-left:0px;" valign="top">


                        </td>
                      </tr>
                      </tbody>
                    </table>
                    <!--[if mso]>
                    </td>
                    <![endif]-->

                    <!--[if mso]>
                    </tr>
                    </table>
                    <![endif]-->
                  </td>
                </tr>
                </tbody>
              </table>
            </td>
          </tr>
          <tr>
            <td id="templateBody" valign="top">
              <table border="0" cellpadding="0" cellspacing="0" class="mcnTextBlock" style="min-width:100%;" width="100%">
                <tbody class="mcnTextBlockOuter">
                <tr>
                  <td class="mcnTextBlockInner" style="padding-top:9px;" valign="top">
                    <!--[if mso]>
                    <table align="left" border="0" cellspacing="0" cellpadding="0" width="100%" style="width:100%;">
                      <tr>
                    <![endif]-->

                    <!--[if mso]>
                    <td valign="top" width="500" style="width:500px;">
                    <![endif]-->
                    <table align="left" border="0" cellpadding="0" cellspacing="0" class="mcnTextContentContainer" style="max-width:100%; min-width:100%;" width="100%">
                      <tbody>
                      <tr>

                        <td class="mcnTextContent" style="padding-top:0; padding-right:0px; padding-bottom:9px; padding-left:0px;" valign="top">

                          <div style="text-align: left;">{{ _("A refund of %(amount)s has been issued to your card ending in %(card_last_digits)s for your purchase at %(business_name)s") % {'amount': amount, 'card_last_digits': card_last_digits, 'business_name': business_name} }}</div>

                        </td>
                      </tr>
                      </tbody>
                    </table>
                    <!--[if mso]>
                    </td>
                    <![endif]-->

                    <!--[if mso]>
                    </tr>
                    </table>
                    <![endif]-->
                  </td>
                </tr>
                </tbody>
              </table>
              <table border="0" cellpadding="0" cellspacing="0" class="mcnImageBlock" style="min-width:100%;" width="100%">
                <tbody class="mcnImageBlockOuter">
                <tr>
                  <td class="mcnImageBlockInner" valign="top">
                    <table align="left" border="0" cellpadding="0" cellspacing="0" class="mcnImageContentContainer" style="min-width:100%;" width="100%">
                      <tbody>
                      <tr>
                        <td class="mcnImageContent" style="padding-top: 0; padding-bottom: 0; text-align:center;" valign="top">
                          <div align="center">
                            {% include 'KYC/receipt_body.html' %}
                          </div>
                        </td>
                      </tr>
                      </tbody>
                    </table>
                  </td>
                </tr>
                </tbody>
              </table>
              <table border="0" cellpadding="0" cellspacing="0" class="mcnTextBlock" style="min-width:100%;" width="100%">
                <tbody class="mcnTextBlockOuter">
                <tr>
                  <td class="mcnTextBlockInner" style="padding-top:9px;" valign="top">
                    <!--[if mso]>
                    <table align="left" border="0" cellspacing="0" cellpadding="0" width="100%" style="width:100%;">
                      <tr>
                    <![endif]-->

                    <!--[if mso]>
                    <td valign="top" width="500" style="width:500px;">
                    <![endif]-->
                    <table align="left" border="0" cellpadding="0" cellspacing="0" class="mcnTextContentContainer" style="max-width:100%; min-width:100%;" width="100%">
                      <tbody>
                      <tr>

                        <td class="mcnTextContent" style="padding-top:0; padding-right:0px; padding-bottom:9px; padding-left:0px;" valign="top">

                          <br>
                          {{ _("The refund can take up to 5 business days for the refund to be reflected in your account.") }}<br>
                          <br>
                          {{ _("Thanks,%s The Booksy Team") % "<br>" }}
                        </td>
                      </tr>
                      </tbody>
                    </table>
                    <!--[if mso]>
                    </td>
                    <![endif]-->

                    <!--[if mso]>
                    </tr>
                    </table>
                    <![endif]-->
                  </td>
                </tr>
                </tbody>
              </table>
              <table border="0" cellpadding="0" cellspacing="0" class="mcnTextBlock" style="min-width:100%;" width="100%">
                <tbody class="mcnTextBlockOuter">
                <tr>
                  <td class="mcnTextBlockInner" style="padding-top:9px;" valign="top">
                    <!--[if mso]>
                    <table align="left" border="0" cellspacing="0" cellpadding="0" width="100%" style="width:100%;">
                      <tr>
                    <![endif]-->

                    <!--[if mso]>
                    <td valign="top" width="500" style="width:500px;">
                    <![endif]-->
                    <table align="left" border="0" cellpadding="0" cellspacing="0" class="mcnTextContentContainer" style="max-width:100%; min-width:100%;" width="100%">
                      <tbody>
                      <tr>

                        <td class="mcnTextContent" style="padding-top:0; padding-right:0px; padding-bottom:9px; padding-left:0px;" valign="top">

                        </td>
                      </tr>
                      </tbody>
                    </table>
                    <!--[if mso]>
                    </td>
                    <![endif]-->

                    <!--[if mso]>
                    </tr>
                    </table>
                    <![endif]-->
                  </td>
                </tr>
                </tbody>
              </table>
            </td>
          </tr>
          <tr>
            <td id="templateFooter" valign="top"></td>
          </tr>
        </table>
        <!--[if (gte mso 9)|(IE)]>
        </td>
        </tr>
        </table>
        <![endif]-->
        <!-- // END TEMPLATE -->
      </td>
    </tr>
  </table>
</center>
{% endblock email_content %}