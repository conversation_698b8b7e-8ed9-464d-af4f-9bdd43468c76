{% extends "KYC_business_base.html" %}

{% block email_title %}{{ _("Refund confirmation") }}{% endblock email_title %}

{% block email_content %}
<center>
    <table align="center" border="0" cellpadding="0" cellspacing="0" height="100%" width="100%" id="bodyTable">
        <tr>
            <td align="center" valign="top" id="bodyCell">
                <!-- BEGIN TEMPLATE // -->
                <!--[if (gte mso 9)|(IE)]>
                <table align="center" border="0" cellspacing="0" cellpadding="0" width="500" style="width:500px;">
                    <tr>
                        <td align="center" valign="top" width="500" style="width:500px;">
                <![endif]-->
                <table border="0" cellpadding="0" cellspacing="0" width="100%" class="templateContainer">
                    <tr>
                        <td valign="top" id="templatePreheader">
                            <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock"
                                   style="min-width:100%;">
                                <tbody class="mcnTextBlockOuter">
                                <tr>
                                    <td valign="top" class="mcnTextBlockInner" style="padding-top:9px;">
                                        <!--[if mso]>
                                        <table align="left" border="0" cellspacing="0" cellpadding="0" width="100%"
                                               style="width:100%;">
                                            <tr>
                                        <![endif]-->

                                        <!--[if mso]>
                                        <td valign="top" width="500" style="width:500px;">
                                        <![endif]-->
                                        <table align="left" border="0" cellpadding="0" cellspacing="0"
                                               style="max-width:100%; min-width:100%;" width="100%"
                                               class="mcnTextContentContainer">
                                            <tbody>
                                            <tr>

                                                <td valign="top" class="mcnTextContent"
                                                    style="padding: 0px 0px 9px; text-align: center;">

                                                    <a href="*|ARCHIVE|*" target="_blank">View this email in your
                                                        browser</a>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                        <!--[if mso]>
                                        </td>
                                        <![endif]-->

                                        <!--[if mso]>
                                        </tr>
                                        </table>
                                        <![endif]-->
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td valign="top" id="templateBody">
                            <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock"
                                   style="min-width:100%;">
                                <tbody class="mcnTextBlockOuter">
                                <tr>
                                    <td valign="top" class="mcnTextBlockInner" style="padding-top:9px;">
                                        <!--[if mso]>
                                        <table align="left" border="0" cellspacing="0" cellpadding="0" width="100%"
                                               style="width:100%;">
                                            <tr>
                                        <![endif]-->

                                        <!--[if mso]>
                                        <td valign="top" width="500" style="width:500px;">
                                        <![endif]-->
                                        <table align="left" border="0" cellpadding="0" cellspacing="0"
                                               style="max-width:100%; min-width:100%;" width="100%"
                                               class="mcnTextContentContainer">
                                            <tbody>
                                            <tr>

                                                <td valign="top" class="mcnTextContent"
                                                    style="padding-top:0; padding-right:0px; padding-bottom:9px; padding-left:0px;">

                                                    <div style="text-align: left;">{{ _("Hi %(client_name)s") %
                                                        {'client_name': business_owner_name} }},<br>
                                                        <br>
                                                        {{ _("The refund process has been initiated for %(client_name)s regarding the&nbsp;charge from %(date)s.") % {'client_name': client_name, 'date': transaction_date} }}
                                                        {{ _("You will be refunding the entire cost of their service, plus an additional processing fee.") }}<br>
                                                        <br>
                                                        {{ _("The details of the refund are below. Feel free to contact Booksy's Customer Support team if you have any questions.") }}
                                                    </div>

                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                        <!--[if mso]>
                                        </td>
                                        <![endif]-->

                                        <!--[if mso]>
                                        </tr>
                                        </table>
                                        <![endif]-->
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnDividerBlock"
                                   style="min-width:100%;">
                                <tbody class="mcnDividerBlockOuter">
                                <tr>
                                    <td class="mcnDividerBlockInner" style="min-width:100%; padding:18px;">
                                        <table class="mcnDividerContent" border="0" cellpadding="0" cellspacing="0"
                                               width="100%" style="min-width: 100%;border-top: 2px solid #EAEAEA;">
                                            <tbody>
                                            <tr>
                                                <td>
                                                    <span></span>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                        <!--
                                                        <td class="mcnDividerBlockInner" style="padding: 18px;">
                                                        <hr class="mcnDividerContent" style="border-bottom-color:none; border-left-color:none; border-right-color:none; border-bottom-width:0; border-left-width:0; border-right-width:0; margin-top:0; margin-right:0; margin-bottom:0; margin-left:0;" />
                                        -->
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock"
                                   style="min-width:100%;">
                                <tbody class="mcnTextBlockOuter">
                                <tr>
                                    <td valign="top" class="mcnTextBlockInner" style="padding-top:9px;">
                                        <!--[if mso]>
                                        <table align="left" border="0" cellspacing="0" cellpadding="0" width="100%"
                                               style="width:100%;">
                                            <tr>
                                        <![endif]-->

                                        <!--[if mso]>
                                        <td valign="top" width="500" style="width:500px;">
                                        <![endif]-->
                                        <table align="left" border="0" cellpadding="0" cellspacing="0"
                                               style="max-width:100%; min-width:100%;" width="100%"
                                               class="mcnTextContentContainer">
                                            <tbody>
                                            <tr>

                                                <td valign="top" class="mcnTextContent"
                                                    style="padding-top:0; padding-right:0px; padding-bottom:9px; padding-left:0px;">

                                                    <span style="color:#A9A9A9">{{ _("REFUND DETAILS") }}</span><br>
                                                    <br>
                                                    {{ user_name }}<br>
                                                    {% for service in services %}
                                                    {{ service }}<br>
                                                    {% endfor %}
                                                    {{ booking_date }}<br>
                                                    <br>
                                                    <table>
                                                        <tbody>
                                                        <tr>
                                                            <td width="300">{{ _("Service Charge") }}</td>
                                                            <td width="100" align="right">{{ amount }}</td>
                                                        </tr>
                                                        <tr>
                                                            <td>{{ _("Processing Fee") }}</td>
                                                            <td width="100" align="right">{{ refund_fee }}</td>
                                                        </tr>
                                                        <tr>
                                                            <td style="padding-top: 20px"><strong>{{ _("TOTAL REFUND CHARGE") }}</strong></td>
                                                            <td width="100" align="right"><strong>{{ total_amount
                                                                }}</strong></td>
                                                        </tr>
                                                        </tbody>
                                                    </table>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                        <!--[if mso]>
                                        </td>
                                        <![endif]-->

                                        <!--[if mso]>
                                        </tr>
                                        </table>
                                        <![endif]-->
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnDividerBlock"
                                   style="min-width:100%;">
                                <tbody class="mcnDividerBlockOuter">
                                <tr>
                                    <td class="mcnDividerBlockInner" style="min-width:100%; padding:18px;">
                                        <table class="mcnDividerContent" border="0" cellpadding="0" cellspacing="0"
                                               width="100%" style="min-width: 100%;border-top: 2px solid #EAEAEA;">
                                            <tbody>
                                            <tr>
                                                <td>
                                                    <span></span>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                        <!--
                                                        <td class="mcnDividerBlockInner" style="padding: 18px;">
                                                        <hr class="mcnDividerContent" style="border-bottom-color:none; border-left-color:none; border-right-color:none; border-bottom-width:0; border-left-width:0; border-right-width:0; margin-top:0; margin-right:0; margin-bottom:0; margin-left:0;" />
                                        -->
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock"
                                   style="min-width:100%;">
                                <tbody class="mcnTextBlockOuter">
                                <tr>
                                    <td valign="top" class="mcnTextBlockInner" style="padding-top:9px;">
                                        <!--[if mso]>
                                        <table align="left" border="0" cellspacing="0" cellpadding="0" width="100%"
                                               style="width:100%;">
                                            <tr>
                                        <![endif]-->

                                        <!--[if mso]>
                                        <td valign="top" width="500" style="width:500px;">
                                        <![endif]-->
                                        <table align="left" border="0" cellpadding="0" cellspacing="0"
                                               style="max-width:100%; min-width:100%;" width="100%"
                                               class="mcnTextContentContainer">
                                            <tbody>
                                            <tr>

                                                <td valign="top" class="mcnTextContent"
                                                    style="padding-top:0; padding-right:0px; padding-bottom:9px; padding-left:0px;">


                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                        <!--[if mso]>
                                        </td>
                                        <![endif]-->

                                        <!--[if mso]>
                                        </tr>
                                        </table>
                                        <![endif]-->
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td valign="top" id="templateFooter"></td>
                    </tr>
                </table>
                <!--[if (gte mso 9)|(IE)]>
                </td>
                </tr>
                </table>
                <![endif]-->
                <!-- // END TEMPLATE -->
            </td>
        </tr>
    </table>
</center>
{% endblock email_content %}