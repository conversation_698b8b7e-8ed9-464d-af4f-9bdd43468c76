{% extends "KYC_business_base.html" %}

{% block email_title %}{{ _("Your account failed to be verified") }}{% endblock email_title %}

{% block email_content %}
<center>
    <table align="center" border="0" cellpadding="0" cellspacing="0" height="100%" id="bodyTable"
           style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;height: 100%;margin: 0;padding: 0;width: 100%;"
           width="100%">
        <tr>
            <td align="center" id="bodyCell"
                style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;height: 100%;margin: 0;padding: 10px;width: 100%;border-top: 0;"
                valign="top">
                <!-- BEGIN TEMPLATE // -->
                <!--[if (gte mso 9)|(IE)]>
                <table align="center" border="0" cellspacing="0" cellpadding="0" width="500" style="width:500px;">
                    <tr>
                        <td align="center" valign="top" width="500" style="width:500px;">
                <![endif]-->
                <table border="0" cellpadding="0" cellspacing="0" class="templateContainer"
                       style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;border: 0;max-width: 500px !important;"
                       width="100%">
                    <tr>
                        <td id="templatePreheader"
                            style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-image: none;background-repeat: no-repeat;background-position: center;background-size: cover;border-top: 0;border-bottom: 0;padding-top: 9px;padding-bottom: 9px;"
                            valign="top">
                            <table border="0" cellpadding="0" cellspacing="0" class="mcnTextBlock"
                                   style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"
                                   width="100%">
                                <tbody class="mcnTextBlockOuter">
                                <tr>
                                    <td class="mcnTextBlockInner"
                                        style="padding-top: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"
                                        valign="top">
                                        <!--[if mso]>
                                        <table align="left" border="0" cellspacing="0" cellpadding="0" width="100%"
                                               style="width:100%;">
                                            <tr>
                                        <![endif]-->

                                        <!--[if mso]>
                                        <td valign="top" width="500" style="width:500px;">
                                        <![endif]-->
                                        <table align="left" border="0" cellpadding="0" cellspacing="0"
                                               class="mcnTextContentContainer"
                                               style="max-width: 100%;min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"
                                               width="100%">
                                        </table>
                                        <!--[if mso]>
                                        </td>
                                        <![endif]-->

                                        <!--[if mso]>
                                        </tr>
                                        </table>
                                        <![endif]-->
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td id="templateBody"
                            style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-image: none;background-repeat: no-repeat;background-position: center;background-size: cover;border-top: 0;padding-top: 0;padding-bottom: 9px;"
                            valign="top">
                            <table border="0" cellpadding="0" cellspacing="0" class="mcnTextBlock"
                                   style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"
                                   width="100%">
                                <tbody class="mcnTextBlockOuter">
                                <tr> 
                                    <td class="mcnTextBlockInner"
                                        style="padding-top: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"
                                        valign="top">
                                        <!--[if mso]>
                                        <table align="left" border="0" cellspacing="0" cellpadding="0" width="100%"
                                               style="width:100%;">
                                            <tr>
                                        <![endif]-->

                                        <!--[if mso]>
                                        <td valign="top" width="500" style="width:500px;">
                                        <![endif]-->
                                        <table align="left" border="0" cellpadding="0" cellspacing="0"
                                               class="mcnTextContentContainer"
                                               style="max-width: 100%;min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"
                                               width="100%">
                                            <tbody>
                                            <tr>

                                                <td class="mcnTextContent"
                                                    style="padding-top: 0;padding-right: 0px;padding-bottom: 9px;padding-left: 0px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;word-break: break-word;color: #202020;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;"
                                                    valign="top">

                                                    <div style="text-align: left;">
                                                        {{ _("You are one step away from processing Mobile Payments with Booksy! There must be an error in the information you have submitted. Therefore, our system was not able to verify your account.") }}<br>
                                                        <br>
                                                        {{ _("You can correct the information by following the steps below:") }}<br>
                                                        <br>
                                                        {{ _("Head to the Payments Settings section of your app. (Booksy App &gt; More &gt; Mobile Payments &gt; Payments Settings) and check each of the following:") }}<br>
                                                        <br>
                                                        1) {{ _("Check that your bank details, personal data or company data (for businesses) are correct and correspond to the account holder") }}<br>
                                                        <br>
                                                        2) {{ _("Check that all ID numbers provided (SSN, Tax ID/ EIN) are correct and match legal documentation of the account holder") }}<br>
                                                        <br>
                                                        3) {{ _("Ensure that the document ID you upload matches the bank account holder (for individuals) or business owner (for businesses) and the bank account statement corresponds to the bank account details you provided") }}<br>
                                                        <br>
                                                        4) {{ _("Ensure you've uploaded a valid photo ID (Passport, Driver's License or National Identity Card") }}<br>
                                                        <br>
                                                        5) {{ _("Ensure you've uploaded a photo of your bank statement or voided check showing clearly the bank logo. Your routing number, account number, and account holder name should be visible in the photo.") }}<br>
                                                        <br>
                                                        {{ _("If you need help at any point, just contact Booksy Customer Success via the \"Help\" button in your Booksy app") }}<br>
                                                        <br>
                                                    </div>

                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                        <!--[if mso]>
                                        </td>
                                        <![endif]-->

                                        <!--[if mso]>
                                        </tr>
                                        </table>
                                        <![endif]-->
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                            <table border="0" cellpadding="0" cellspacing="0" class="mcnTextBlock"
                                   style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"
                                   width="100%">
                                <tbody class="mcnTextBlockOuter">
                                <tr>
                                    <td class="mcnTextBlockInner"
                                        style="padding-top: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"
                                        valign="top">
                                        <!--[if mso]>
                                        <table align="left" border="0" cellspacing="0" cellpadding="0" width="100%"
                                               style="width:100%;">
                                            <tr>
                                        <![endif]-->

                                        <!--[if mso]>
                                        <td valign="top" width="500" style="width:500px;">
                                        <![endif]-->
                                        <table align="left" border="0" cellpadding="0" cellspacing="0"
                                               class="mcnTextContentContainer"
                                               style="max-width: 100%;min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;"
                                               width="100%">
                                            <tbody>
                                            <tr>

                                                <td class="mcnTextContent"
                                                    style="padding-top: 0;padding-right: 0px;padding-bottom: 9px;padding-left: 0px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;word-break: break-word;color: #202020;font-family: Helvetica;font-size: 16px;line-height: 150%;text-align: left;"
                                                    valign="top">


                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                        <!--[if mso]>
                                        </td>
                                        <![endif]-->

                                        <!--[if mso]>
                                        </tr>
                                        </table>
                                        <![endif]-->
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td id="templateFooter"
                            style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-image: none;background-repeat: no-repeat;background-position: center;background-size: cover;border-top: 0;border-bottom: 0;padding-top: 9px;padding-bottom: 9px;"
                            valign="top"></td>
                    </tr>
                </table>
                <!--[if (gte mso 9)|(IE)]>
                </td>
                </tr>
                </table>
                <![endif]-->
                <!-- // END TEMPLATE -->
            </td>
        </tr>
    </table>
</center>
{% endblock email_content %}