# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: booksy/pubsub/business/customer.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n%booksy/pubsub/business/customer.proto\x12\rbooksy.pubsub\"\xfe\x01\n\x18\x42\x61sicCustomerDataChanged\x12\x0e\n\x06\x62\x63i_id\x18\x01 \x01(\r\x12\x13\n\x0b\x62usiness_id\x18\x02 \x01(\r\x12\x0f\n\x07user_id\x18\x03 \x01(\r\x12\x12\n\nfirst_name\x18\x04 \x01(\t\x12\x11\n\tlast_name\x18\x05 \x01(\t\x12\x11\n\tphoto_url\x18\x06 \x01(\t\x12\x10\n\x08\x62irthday\x18\x07 \x01(\t\x12\x1b\n\x13visible_in_business\x18\x08 \x01(\x08\x12\x0f\n\x07\x63reated\x18\t \x01(\x03\x12\x0f\n\x07\x64\x65leted\x18\n \x01(\x03\x12\r\n\x05\x65mail\x18\x0b \x01(\t\x12\x12\n\ncell_phone\x18\x0c \x01(\tb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'booksy.pubsub.business.customer_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _globals['_BASICCUSTOMERDATACHANGED']._serialized_start=57
  _globals['_BASICCUSTOMERDATACHANGED']._serialized_end=311
# @@protoc_insertion_point(module_scope)
