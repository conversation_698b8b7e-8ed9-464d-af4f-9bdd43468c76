# -*- coding: utf-8 -*-
import json
import os


HOUSE_NUMBER_FIRST_REGEXP = r'^(\d.*?)\s+(?=\w{2})'
HOUSE_NUMBER_LAST_REGEXP = r'(?<=\w{2})\s+(\d.*?)$'
HOUSE_NUMBER_AFTER_HASH_REGEXP = r'(?<=\w{2})\s+[#]?(\d.*?)$'
HOUSE_NUMBER_AFTER_COMMA_REGEXP = r'(?<=\w{2})[,]?\s+(\d.*?)$'
HOUSE_NUMBER_AFTER_NO_REGEXP = r'(?<=\w{2})\s+((No|no)[\s\.]+)?(\d.*?)$'

LINE_1_NUMBER_FIRST_FORMAT = '{{ house_number }} {{ street }}'
LINE_1_NUMBER_LAST_FORMAT = '{{ street }} {{ house_number }}'
LINE_1_NUMBER_AFTER_HASH_FORMAT = '{{ street }} #{{ house_number }}'
LINE_1_NUMBER_AFTER_COMMA_FORMAT = '{{ street }}, {{ house_number }}'
LINE_1_NUMBER_AFTER_NO_FORMAT = '{{ street }} No. {{ house_number }}'

LINE_1_NUMBER_FIRST_REGEXP = r'^(?P<house_number>\d.*?)\s+(?P<street>\w{2}.*?)$'
LINE_1_NUMBER_FIRST_REGEXP_EN = r'^(?P<house_number>\d.*?)\s+(?P<street>([NSEW]\.?\s+)?\w{2}.*?)$'
LINE_1_NUMBER_LAST_REGEXP = r'^(?P<street>.*?)(?<=\w{2})\s+(?P<house_number>\d.*?)$'
LINE_1_NUMBER_AFTER_HASH_REGEXP = r'^(?P<street>.*?)(?<=\w{2})\s+(#\s*)?(?P<house_number>\d.*?)$'
LINE_1_NUMBER_AFTER_COMMA_REGEXP = r'^(?P<street>.*?)(?<=\w{2})(\s*,)?\s+(?P<house_number>\d.*?)$'
LINE_1_NUMBER_AFTER_NO_REGEXP = (
    r'^(?P<street>.*?)(?<=\w{2})\s+(No[\.]?)?\s*(?P<house_number>\d.*?)$'
)


BASE_INFO = {
    # us - USA
    'us': {
        'name': 'USA',
        'latitude': 39.828127,
        'longitude': -98.579404,
        'zoom': 3,
        'address_format': 'number_first',
        'use_state_code': True,
        'no_zipcodes': False,
        'zipcode_regexp': r'^[0-9]{5}$',
        'line_1_format': LINE_1_NUMBER_FIRST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_FIRST_REGEXP_EN,
        'multiline_address_format': [
            '{{ line_1 }} {{ line_2 }}',
            '{{ locality }}{% if locality %}, {% endif %}{{ state_code or state }} {{ zip }}',
            '{% if country_code != reference_country_code %}{{ country }}{% endif %}',
        ],
    },
    # pl - Polska
    'pl': {
        'name': 'Polska',
        'latitude': 52.069311,
        'longitude': 19.480222,
        'zoom': 5,
        'address_format': 'number_last',
        'no_zipcodes': False,
        'zipcode_regexp': r'^\d{2}-?\d{3}$',
        'line_1_format': LINE_1_NUMBER_LAST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_LAST_REGEXP,
        'street_regexp': r'^(ulica|ul[\.]?)\b(?P<street>.*)',
        'multiline_address_format': [
            '{{ line_1 }}, {{ line_2 }}',
            '{{ zip }} {{ locality }}',
        ],
    },
    # SCANDINAVIA
    # se - Sweden
    'se': {
        'name': 'Sverige',
        'latitude': 62.1983366,
        'longitude': 17.567198,
        'zoom': 4,
        'address_format': 'number_last',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_LAST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_LAST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }}, {{ line_2 }}',
            '{{ zip }} {{ locality }}',
        ],
    },
    # no - Norway
    'no': {
        'name': 'Norge',
        'latitude': 64.5783089,
        'longitude': 17.888237,
        'zoom': 4,
        'address_format': 'number_last',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_LAST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_LAST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }}, {{ line_2 }}',
            '{{ zip }} {{ locality }}',
        ],
    },
    # dk - Denmark
    'dk': {
        'name': 'Danmark',
        'latitude': 55.9396761,
        'longitude': 9.5155848,
        'zoom': 6,
        'address_format': 'number_last',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_LAST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_LAST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }}, {{ line_2 }}',
            '{{ zip }} {{ locality }}',
        ],
    },
    # fi - Finland :)
    'fi': {
        'name': 'Suomi',
        'latitude': 64.9146659,
        'longitude': 26.0672554,
        'zoom': 4,
        'address_format': 'number_last',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_LAST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_LAST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }}, {{ line_2 }}',
            '{{ zip }} {{ locality }}',
        ],
    },
    # COMMONWEALTH
    # gb - Great Britain
    'gb': {
        'name': 'United Kingdom',
        'latitude': 55.3617609,
        'longitude': -3.4433238,
        'zoom': 5,
        'address_format': 'number_first',
        'no_zipcodes': False,
        'zipcode_regexp': r'^[a-z0-9A-Z ]{5,8}$',
        'line_1_format': LINE_1_NUMBER_FIRST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_FIRST_REGEXP_EN,
        'multiline_address_format': [
            '{{ line_1 }} {{ line_2 }}',
            '{{ locality }}{% if locality %}, {% endif %}{{ state }} {{ zip }}',
            '{% if country_code != reference_country_code %}{{ country }}{% endif %}',
        ],
    },
    # ie - Ireland
    'ie': {
        'name': 'Ireland',
        'latitude': 53.4152431,
        'longitude': -8.2390307,
        'zoom': 6,
        'address_format': 'number_first',
        'no_zipcodes': False,
        'zipcode_regexp': r'(?:^[AC-FHKNPRTV-Y][0-9]{2}|D6W)[ -]?[0-9AC-FHKNPRTV-Y]{4}$',
        'line_1_format': LINE_1_NUMBER_FIRST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_FIRST_REGEXP_EN,
        'multiline_address_format': [
            '{{ line_1 }} {{ line_2 }}',
            '{{ locality }}{% if locality %}, {% endif %}{{ state }} {{ zip }}',
        ],
    },
    # au - Australia
    'au': {
        'name': 'Australia',
        'latitude': -26.4390742,
        'longitude': 133.281323,
        'zoom': 3,
        'address_format': 'number_first',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_FIRST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_FIRST_REGEXP_EN,
        'multiline_address_format': [
            '{{ line_1 }} {{ line_2 }}',
            '{{ locality }}{% if locality %}, {% endif %}{{ state }} {{ zip }}',
        ],
    },
    # ng - Nigeria
    'ng': {
        'name': 'Nigeria',
        'latitude': 9.077751,
        'longitude': 8.6774569,
        'zoom': 5,
        'address_format': 'number_first',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_FIRST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_FIRST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }} {{ line_2 }}',
            '{{ locality }}{% if locality %}, {% endif %}{{ state }} {{ zip }}',
        ],
    },
    # nz - New Zealand
    'nz': {
        'name': 'New Zealand',
        'latitude': -43.3744881,
        'longitude': 172.4662705,
        'zoom': 4,
        'address_format': 'number_first',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_FIRST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_FIRST_REGEXP_EN,
        'multiline_address_format': [
            '{{ line_1 }} {{ line_2 }}',
            '{{ locality }}{% if locality %}, {% endif %}{{ state }} {{ zip }}',
        ],
    },
    # ca - Canada
    'ca': {
        'name': 'Canada',
        'latitude': 56,
        'longitude': -96,
        'zoom': 3,
        'address_format': 'number_first',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_FIRST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_FIRST_REGEXP_EN,
        'multiline_address_format': [
            '{{ line_1 }} {{ line_2 }}',
            '{{ locality }}{% if locality %}, {% endif %}{{ state }} {{ zip }}',
        ],
    },
    # my - Malaysia
    'my': {
        'name': 'Malaysia',
        'latitude': 4.140634,
        'longitude': 109.6181485,
        'zoom': 5,
        'address_format': 'number_first',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_FIRST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_FIRST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }} {{ line_2 }}',
            '{{ locality }}{% if locality %}, {% endif %}{{ state }} {{ zip }}',
        ],
    },
    # sg - Singapore
    'sg': {
        'name': 'Singapore',
        'latitude': 1.3146632,
        'longitude': 103.8454093,
        'zoom': 10,
        'address_format': 'number_first',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_FIRST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_FIRST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }} {{ line_2 }}',
            '{{ locality }}{% if locality %}, {% endif %}{{ state }} {{ zip }}',
        ],
    },
    # hk - Hong Kong
    'hk': {
        'name': 'Hong Kong',
        'latitude': 22.3700556,
        'longitude': 114.1223784,
        'zoom': 10,
        'address_format': 'number_first',
        'no_zipcodes': True,  # ACHTUNG
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_FIRST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_FIRST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }} {{ line_2 }}',
            '{{ locality }}{% if locality %}, {% endif %}{{ state }} {{ zip }}',
        ],
    },
    # in - India
    'in': {
        'name': 'India',
        'latitude': 21.1289956,
        'longitude': 82.7792201,
        'zoom': 4,
        'address_format': 'number_first',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_FIRST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_FIRST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }} {{ line_2 }}',
            '{{ locality }}{% if locality %}, {% endif %}{{ state }} {{ zip }}',
        ],
    },
    # mt - Malta
    'mt': {
        'name': 'Malta',
        'latitude': 35.9440174,
        'longitude': 14.3795243,
        'zoom': 10,
        'address_format': 'number_first',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_FIRST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_FIRST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }} {{ line_2 }}',
            '{{ locality }}{% if locality %}, {% endif %}{{ state }} {{ zip }}',
        ],
    },
    # jm - Jamaica
    'jm': {
        'name': 'Jamaica',
        'latitude': 18.1155174,
        'longitude': -77.2760026,
        'zoom': 9,
        'address_format': 'number_first',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_FIRST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_FIRST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }} {{ line_2 }}',
            '{{ locality }}{% if locality %}, {% endif %}{{ state }} {{ zip }}',
        ],
    },
    # za - Southern Africa
    'za': {
        'name': 'Southern Africa',
        'latitude': -25.9652428,
        'longitude': 22.2583,
        'zoom': 4,
        'address_format': 'number_first',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_FIRST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_FIRST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }} {{ line_2 }}',
            '{{ locality }}{% if locality %}, {% endif %}{{ state }} {{ zip }}',
        ],
    },
    # bz - Belize
    'bz': {
        'name': 'Belice',
        'latitude': 17.1907805,
        'longitude': -88.3596575,
        'zoom': 7,
        'address_format': 'number_first',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_FIRST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_FIRST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }} {{ line_2 }}',
            '{{ locality }}{% if locality %}, {% endif %}{{ state }} {{ zip }}',
        ],
    },
    # LATINO & PENINSULARES
    # es - Spain
    'es': {
        'name': 'España',
        'latitude': 40.2085,
        'longitude': -3.713,
        'zoom': 5,
        'address_format': 'number_after_comma',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_AFTER_COMMA_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_AFTER_COMMA_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }}, {{ line_2 }}',
            '{{ zip }} {{ locality }}',
        ],
    },
    # mx - Mexico
    'mx': {
        'name': 'México',
        'latitude': 23.6260333,
        'longitude': -102.5375006,
        'zoom': 4,
        'address_format': 'number_after_No',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_AFTER_NO_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_AFTER_NO_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }}, {{ line_2 }}',
            '{{ zip }} {{ locality }}',
        ],
    },
    # gt - Guatemala
    'gt': {
        'name': 'Guatemala',
        'latitude': 15.7778664,
        'longitude': -90.2299097,
        'zoom': 7,
        'address_format': 'number_last',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_LAST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_LAST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }}, {{ line_2 }}',
            '{{ zip }} {{ locality }}',
        ],
    },
    # sv - El Salvador
    'sv': {
        'name': 'El Salvador',
        'latitude': 13.802994,
        'longitude': -88.9053364,
        'zoom': 8,
        'address_format': 'number_after_hash',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_AFTER_HASH_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_AFTER_HASH_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }}, {{ line_2 }}',
            '{{ zip }} {{ locality }}',
        ],
    },
    # hn - Honduras
    'hn': {
        'name': 'Honduras',
        'latitude': 14.7503821,
        'longitude': -86.241341,
        'zoom': 7,
        'address_format': 'number_first',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_FIRST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_FIRST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }} {{ line_2 }}',
            '{{ locality }}{% if locality %}, {% endif %}{{ state }} {{ zip }}',
        ],
    },
    # ni - Nicaragua
    'ni': {
        'name': 'Nicaragua',
        'latitude': 12.8691653,
        'longitude': -85.1411896,
        'zoom': 7,
        'address_format': 'number_last',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_LAST_FORMAT,  # it's complicated
        'line_1_regexp': LINE_1_NUMBER_LAST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }}, {{ line_2 }}',
            '{{ zip }} {{ locality }}',
        ],
    },
    # cr - Costa Rica
    'cr': {
        'name': 'Costa Rica',
        'latitude': 9.6301892,
        'longitude': -84.2541843,
        'zoom': 7,
        'address_format': 'number_after_hash',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_AFTER_HASH_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_AFTER_HASH_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }}, {{ line_2 }}',
            '{{ zip }} {{ locality }}',
        ],
    },
    # pa - Panama
    'pa': {
        'name': 'Panamá',
        'latitude': 8.4255193,
        'longitude': -80.1053645,
        'zoom': 7,
        'address_format': 'number_last',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_LAST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_LAST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }}, {{ line_2 }}',
            '{{ zip }} {{ locality }}',
        ],
    },
    # cu - Cuba
    'cu': {
        'name': 'Cuba',
        'latitude': 21.5513258,
        'longitude': -79.6017351,
        'zoom': 6,
        'address_format': 'number_after_hash',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_AFTER_HASH_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_AFTER_HASH_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }}, {{ line_2 }}',
            '{{ zip }} {{ locality }}',
        ],
    },
    # co - Colombia
    'co': {
        'name': 'Colombia',
        'latitude': 4.1156735,
        'longitude': -72.9301367,
        'zoom': 5,
        'address_format': 'number_last',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_LAST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_LAST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }}, {{ line_2 }}',
            '{{ zip }} {{ locality }}',
        ],
    },
    # ve - Venezuela
    've': {
        'name': 'Venezuela',
        'latitude': 5.1632955,
        'longitude': -69.4146705,
        'zoom': 4,
        'address_format': 'number_last',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_LAST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_LAST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }}, {{ line_2 }}',
            '{{ zip }} {{ locality }}',
        ],
    },
    # ec - Ecuador
    'ec': {
        'name': 'Ecuador',
        'latitude': -1.7929665,
        'longitude': -78.1368874,
        'zoom': 6,
        'address_format': 'number_last',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_LAST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_LAST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }}, {{ line_2 }}',
            '{{ zip }} {{ locality }}',
        ],
    },
    # pe - Peru
    'pe': {
        'name': 'Perú',
        'latitude': -9.1951786,
        'longitude': -74.9904165,
        'zoom': 5,
        'address_format': 'number_last',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_LAST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_LAST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }}, {{ line_2 }}',
            '{{ zip }} {{ locality }}',
        ],
    },
    # bo - Bolivia
    'bo': {
        'name': 'Bolivia',
        'latitude': -16.2837065,
        'longitude': -63.5493965,
        'zoom': 5,
        'address_format': 'number_after_comma',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_AFTER_COMMA_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_AFTER_COMMA_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }}, {{ line_2 }}',
            '{{ zip }} {{ locality }}',
        ],
    },
    # py - Paraguay
    'py': {
        'name': 'Paraguay',
        'latitude': -23.4380203,
        'longitude': -58.4483065,
        'zoom': 6,
        'address_format': 'number_last',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_LAST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_LAST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }}, {{ line_2 }}',
            '{{ zip }} {{ locality }}',
        ],
    },
    # uy - Uruguay
    'uy': {
        'name': 'Uruguay',
        'latitude': -32.5583168,
        'longitude': -55.8117212,
        'zoom': 6,
        'address_format': 'number_last',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_LAST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_LAST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }}, {{ line_2 }}',
            '{{ zip }} {{ locality }}',
        ],
    },
    # ar - Argentina
    'ar': {
        'name': 'Argentina',
        'latitude': -38.4192641,
        'longitude': -63.5989206,
        'zoom': 3,
        'address_format': 'number_last',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_LAST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_LAST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }}, {{ line_2 }}',
            '{{ zip }} {{ locality }}',
        ],
    },
    # cl - Chile
    'cl': {
        'name': 'Chile',
        'latitude': -36.739055,
        'longitude': -71.0574942,
        'zoom': 3,
        'address_format': 'number_after_No',
        'no_zipcodes': False,
        'zipcode_regexp': r'^[\d]{7}$',
        'line_1_format': LINE_1_NUMBER_AFTER_NO_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_AFTER_NO_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }}, {{ line_2 }}',
            '{{ zip }} {{ locality }}',
        ],
    },
    # Other
    'br': {
        'name': 'Brasil',
        'latitude': -14.2392976,
        'longitude': -53.1805017,
        'zoom': 4,
        'address_format': 'number_after_comma',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_AFTER_COMMA_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_AFTER_COMMA_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }}, {{ line_2 }}',
            '{{ zip }} {{ locality }}',
        ],
    },
    'de': {
        'name': 'Deutschland',
        'latitude': 51.1719674,
        'longitude': 10.4541194,
        'zoom': 4,
        'address_format': 'number_last',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_LAST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_LAST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }}, {{ line_2 }}',
            '{{ zip }} {{ locality }}',
        ],
    },
    'fr': {
        'name': 'France',
        'latitude': 46.2157467,
        'longitude': 2.2088258,
        'zoom': 5,
        'address_format': 'number_first',
        'no_zipcodes': False,
        'zipcode_regexp': r'^\d{5}$',
        'line_1_format': LINE_1_NUMBER_FIRST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_FIRST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }} {{ line_2 }}',
            '{{ locality }}{% if locality %}, {% endif %}{{ state }} {{ zip }}',
        ],
    },
    'nl': {
        'name': 'Nederland',
        'latitude': 52.2129919,
        'longitude': 5.2793703,
        'zoom': 7,
        'address_format': 'number_last',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_LAST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_LAST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }}, {{ line_2 }}',
            '{{ zip }} {{ locality }}',
        ],
    },
    'it': {
        'name': 'Italia',
        'latitude': 41.2924601,
        'longitude': 12.5736108,
        'zoom': 5,
        'address_format': 'number_last',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_LAST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_LAST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }}, {{ line_2 }}',
            '{{ zip }} {{ locality }}',
        ],
    },
    'pt': {
        'name': 'Portugal',
        'latitude': 39.557191,
        'longitude': -7.8536599,
        'zoom': 6,
        'address_format': 'number_last',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_LAST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_LAST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }}, {{ line_2 }}',
            '{{ zip }} {{ locality }}',
        ],
    },
    'ru': {
        'name': 'Россия',
        'latitude': 55.0,
        'longitude': 103.0,
        'zoom': 3,
        'address_format': 'number_after_comma',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_AFTER_COMMA_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_AFTER_COMMA_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }}, {{ line_2 }}',
            '{{ zip }} {{ locality }}',
        ],
    },
    'jp': {
        'name': 'Japan',
        'latitude': 34.7857324,
        'longitude': 134.3756902,
        'zoom': 4,
        'address_format': 'number_last',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_LAST_FORMAT,  # it's complicated
        'line_1_regexp': LINE_1_NUMBER_LAST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }}, {{ line_2 }}',
            '{{ locality }}, {{ metropolis }} {{ zip }}',
        ],
    },
    'ph': {
        'name': 'Philippines',
        'latitude': 11.6736183,
        'longitude': 118.1265216,
        'zoom': 5,
        'address_format': 'number_first',
        'no_zipcodes': False,
        'zipcode_regexp': None,
        'line_1_format': LINE_1_NUMBER_FIRST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_FIRST_REGEXP,
        'multiline_address_format': [
            '{{ line_1 }} {{ line_2 }}',
            '{{ locality }}{% if locality %}, {% endif %}{{ state }} {{ zip }}',
        ],
    },
    'pr': {
        'name': 'Puerto Rico',
        'latitude': 18.236200430083585,
        'longitude': -66.49128340670978,
        'zoom': 7,
        'address_format': 'number_first',
        'no_zipcodes': False,
        'zipcode_regexp': r'^[0-9]{5}$',
        'line_1_format': LINE_1_NUMBER_FIRST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_FIRST_REGEXP_EN,
        'multiline_address_format': [
            '{{ line_1 }} {{ line_2 }}',
            '{{ locality }}{% if locality %}, {% endif %}{{ zip }}',
            '{% if country_code != reference_country_code %}{{ country }}{% endif %}',
        ],
    },
    'vi': {
        'name': 'Virgin Islands',
        'latitude': 17.739063616535677,
        'longitude': -64.75398243396896,
        'zoom': 8,
        'address_format': 'number_first',
        'no_zipcodes': False,
        'zipcode_regexp': r'^[0-9]{5}$',
        'line_1_format': LINE_1_NUMBER_FIRST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_FIRST_REGEXP_EN,
        'multiline_address_format': [
            '{{ line_1 }} {{ line_2 }}',
            '{{ locality }}{% if locality %}, {% endif %}{{ zip }}',
            '{% if country_code != reference_country_code %}{{ country }}{% endif %}',
        ],
    },
    'im': {
        'name': 'Isle of Man',
        'latitude': 54.22815946187888,
        'longitude': -4.532349904882312,
        'zoom': 6,
        'address_format': 'number_first',
        'no_zipcodes': False,
        'zipcode_regexp': r'^[a-z0-9A-Z ]{5,8}$',
        'line_1_format': LINE_1_NUMBER_FIRST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_FIRST_REGEXP_EN,
        'multiline_address_format': [
            '{{ line_1 }} {{ line_2 }}',
            '{{ locality }}{% if locality %}, {% endif %}{{ zip }}',
            '{% if country_code != reference_country_code %}{{ country }}{% endif %}',
        ],
    },
    'je': {
        'name': 'Jersey',
        'latitude': 49.2188168723891,
        'longitude': -2.1267727083481773,
        'zoom': 5,
        'address_format': 'number_first',
        'no_zipcodes': False,
        'zipcode_regexp': r'^[a-z0-9A-Z ]{5,8}$',
        'line_1_format': LINE_1_NUMBER_FIRST_FORMAT,
        'line_1_regexp': LINE_1_NUMBER_FIRST_REGEXP_EN,
        'multiline_address_format': [
            '{{ line_1 }} {{ line_2 }}',
            '{{ locality }}{% if locality %}, {% endif %}{{ zip }}',
            '{% if country_code != reference_country_code %}{{ country }}{% endif %}',
        ],
    },
}

LANGUAGE_CODES = {
    'us': 'en-us',  # USA
    'pl': 'pl-pl',  # Poland
    # SCANDINAVIA
    'dk': 'da-dk',  # Denmark
    'fi': 'fi-fi',  # Finland
    'no': 'nb-no',  # Norway (Bokmål)
    'se': 'en-us',  # Sweden
    # COMMONWEALTH
    'au': 'en-au',  # Australia
    'bm': 'en-bm',  # Bermuda
    'bz': 'en-bz',  # Belize
    'ca': 'en-ca',  # Canada
    'gb': 'en-gb',  # Great Britain
    'gg': 'en-gg',  # Guernsey
    'gi': 'en-gi',  # Gibraltar
    'gu': 'en-gu',  # Guam
    'hk': 'en-hk',  # Hong Kong
    'ie': 'en-ie',  # Ireland
    'im': 'en-im',  # Isle of Man
    'in': 'en-in',  # India
    'je': 'en-je',  # Jersey
    'jm': 'en-jm',  # Jamaica
    'mp': 'en-mp',  # Northern Mariana Islands
    'mt': 'en-mt',  # Malta
    'my': 'en-my',  # Malaysia
    'ng': 'en-ng',  # Nigeria
    'nz': 'en-nz',  # New Zealand
    'ph': 'en-us',  # Philippines
    'pr': 'en-pr',  # Puerto Rico
    'sg': 'en-sg',  # Singapore
    'vi': 'en-vi',  # Virgin Islands
    'ws': 'en-ws',  # Samoa
    'za': 'en-za',  # Southern Africa
    # LATINO & PENINSULARES
    'ar': 'es-ar',  # Argentina
    'bo': 'es-bo',  # Bolivia
    'cl': 'es-cl',  # Chile
    'co': 'es-co',  # Colombia
    'cr': 'es-cr',  # Costa Rica
    'cu': 'es-cu',  # Cuba
    'es': 'es-es',  # Spain
    'ec': 'es-ec',  # Ecuador
    'gt': 'es-gt',  # Guatemala
    'hn': 'es-hn',  # Honduras
    'mx': 'es-mx',  # Mexico
    'ni': 'es-ni',  # Nicaragua
    'pa': 'es-pa',  # Panama
    'pe': 'es-pe',  # Peru
    'py': 'es-py',  # Paraguay
    'sv': 'es-sv',  # El Salvador
    'uy': 'es-uy',  # Uruguay
    've': 'es-ve',  # Venezuela
    # Temporary English until translated
    'nl': 'en-us',  # Netherlands
    'it': 'en-us',  # Italy
    'jp': 'en-us',  # Japan
    'be': 'en-us',  # Belgium
    # Russian language in use
    'ru': 'ru-ru',  # Russia
    'by': 'ru-by',  # Belarus
    'kz': 'ru-kz',  # Kazakhstan
    'kg': 'ru-kg',  # Kyrgyzstan
    # Ukrainian
    'ua': 'uk-ua',  # Ukraine
    # French
    'fr': 'fr-fr',  # France
    'gf': 'fr-gf',  # French Guiana
    'gp': 'fr-gp',  # Guadeloupe
    'mq': 'fr-mq',  # Martinique
    'yt': 'fr-yt',  # Mayotte
    're': 'fr-re',  # Réunion
    # Portuguese
    'br': 'pt-br',  # Brazil
    'pt': 'pt-pt',  # Portugal
    # German
    'de': 'en-us',  # Germany
    'at': 'de-at',  # Austria
    'ch': 'de-ch',  # Switzerland
    'lu': 'de-lu',  # Luxembourg
    # Chinese Simplified
    'zh': 'zh-cn',  # China
}
CURRENCY_LOCALES = {
    'us': 'en_US',  # USA
    'pl': 'pl_PL',  # Poland
    # SCANDINAVIA
    'dk': 'da_DK',  # Denmark
    'fi': 'fi_FI',  # Finland
    'no': 'nb_NO',  # Norway (Bokmål)
    'se': 'sv_SE',  # Sweden
    # COMMONWEALTH
    'au': 'en_AU',  # Australia
    'bz': 'en_BZ',  # Belize
    'ca': 'en_CA',  # Canada
    'gb': 'en_GB',  # Great Britain
    'hk': 'en_HK',  # Hong Kong
    'ie': 'en_IE',  # Ireland
    'in': 'en_IN',  # India
    'jm': 'en_JM',  # Jamaica
    'mt': 'en_MT',  # Malta
    'my': 'ms_MY',  # Malaysia
    'ng': 'en_NG',  # Nigeria
    'nz': 'en_NZ',  # New Zealand
    'sg': 'en_SG',  # Singapore
    'za': 'en_ZA',  # Southern Africa
    'pr': 'en_US',  # Puerto Rico
    'vi': 'en_US',  # Virgin Islands
    'gu': 'en_US',  # Guam
    'mp': 'en_US',  # Northern Mariana Islands
    'ws': 'en_WS',  # Samoa
    'im': 'en_IM',  # Isle of Man
    'bm': 'en_BM',  # Bermuda
    'gg': 'en_GB',  # Guernsey
    'je': 'en_JE',  # Jersey
    'gi': 'en_GI',  # Gibraltar
    # LATINO & PENINSULARES
    'es': 'es_ES',  # Spain
    'ar': 'es_AR',  # Argentina
    'bo': 'es_BO',  # Bolivia
    'cl': 'es_CL',  # Chile
    'co': 'es_CO',  # Colombia
    'cu': 'es_CU',  # Cuba
    'ec': 'es_EC',  # Ecuador
    'gt': 'es_GT',  # Guatemala
    'hn': 'es_HN',  # Honduras
    'mx': 'es_MX',  # Mexico
    'ni': 'es_NI',  # Nicaragua
    'pa': 'es_PA',  # Panama
    'py': 'es_PY',  # Paraguay
    'pe': 'es_PE',  # Peru
    'sv': 'es_SV',  # El Salvador
    'uy': 'es_UY',  # Uruguay
    've': 'es_VE',  # Venezuela
    'cr': 'es_CR',  # Costa Rica
    # French
    'fr': 'fr_FR',  # France
    'gf': 'fr_FR',  # French Guiana
    'gp': 'fr_FR',  # Guadeloupe
    'mq': 'fr_FR',  # Martinique
    'yt': 'fr_FR',  # Mayotte
    're': 'fr_FR',  # Réunion
    # Other
    'br': 'pt_BR',  # Brazil
    'de': 'de_DE',  # Germany
    'nl': 'nl_NL',  # Netherlands
    'it': 'it_IT',  # Italy
    'pt': 'pt_PT',  # Portugal
    'ru': 'ru_RU',  # Russia
    'jp': 'ja_JP',  # Japan
    'ph': 'en_PH',  # Philippines
}
# locale data loaded from json generated using:
#   import locale
#   CURRENCY_DATA = {}
#   for cc, locale_code in CURRENCY_LOCALES.items():
#       locale.setlocale(locale.LC_ALL, (locale_code, 'UTF-8'))
#       CURRENCY_DATA[cc] = locale.localeconv()
#   with open('data/currencies.json', 'w', encoding='utf-8') as fd:
#       json.dump(CURRENCY_DATA, fd, sort_keys=True, indent=4, separators=(', ', ': '))
with open(
    os.path.join(
        os.path.dirname(__file__),
        '..',
        'data',
        'currencies.json',
    ),
    'r',
    encoding='utf-8',
) as fd:
    CURRENCY_DATA = json.load(fd)
# HACKS
CURRENCY_DATA['ar']['frac_digits'] = 0
CURRENCY_DATA.update(
    {
        'ws': {
            'currency_symbol': 'SAT$',
            'decimal_point': '.',
            'frac_digits': 2,
            'grouping': [3, 3, 0],
            'int_curr_symbol': 'WST ',
            'int_frac_digits': 2,
            'mon_decimal_point': '.',
            'mon_grouping': [3, 3, 0],
            'mon_thousands_sep': ',',
            'n_cs_precedes': 1,
            'n_sep_by_space': 0,
            'n_sign_posn': 1,
            'negative_sign': '-',
            'p_cs_precedes': 1,
            'p_sep_by_space': 0,
            'p_sign_posn': 1,
            'positive_sign': '',
            'thousands_sep': ',',
        },
        'im': {
            'currency_symbol': '\u00a3',
            'decimal_point': '.',
            'frac_digits': 2,
            'grouping': [3, 3, 0],
            'int_curr_symbol': 'IMP ',
            'int_frac_digits': 2,
            'mon_decimal_point': '.',
            'mon_grouping': [3, 3, 0],
            'mon_thousands_sep': ',',
            'n_cs_precedes': 1,
            'n_sep_by_space': 0,
            'n_sign_posn': 1,
            'negative_sign': '-',
            'p_cs_precedes': 1,
            'p_sep_by_space': 0,
            'p_sign_posn': 1,
            'positive_sign': '',
            'thousands_sep': ',',
        },
        'bm': {
            'currency_symbol': '\u00a3',
            'decimal_point': '.',
            'frac_digits': 2,
            'grouping': [3, 3, 0],
            'int_curr_symbol': 'BMD ',
            'int_frac_digits': 2,
            'mon_decimal_point': '.',
            'mon_grouping': [3, 3, 0],
            'mon_thousands_sep': ',',
            'n_cs_precedes': 1,
            'n_sep_by_space': 0,
            'n_sign_posn': 1,
            'negative_sign': '-',
            'p_cs_precedes': 1,
            'p_sep_by_space': 0,
            'p_sign_posn': 1,
            'positive_sign': '',
            'thousands_sep': ',',
        },
        'je': {
            'currency_symbol': '\u00a3',
            'decimal_point': '.',
            'frac_digits': 2,
            'grouping': [3, 3, 0],
            'int_curr_symbol': 'JEP ',
            'int_frac_digits': 2,
            'mon_decimal_point': '.',
            'mon_grouping': [3, 3, 0],
            'mon_thousands_sep': ',',
            'n_cs_precedes': 1,
            'n_sep_by_space': 0,
            'n_sign_posn': 1,
            'negative_sign': '-',
            'p_cs_precedes': 1,
            'p_sep_by_space': 0,
            'p_sign_posn': 1,
            'positive_sign': '',
            'thousands_sep': ',',
        },
        'gi': {
            'currency_symbol': '\u00a3',
            'decimal_point': '.',
            'frac_digits': 2,
            'grouping': [3, 3, 0],
            'int_curr_symbol': 'GIP ',
            'int_frac_digits': 2,
            'mon_decimal_point': '.',
            'mon_grouping': [3, 3, 0],
            'mon_thousands_sep': ',',
            'n_cs_precedes': 1,
            'n_sep_by_space': 0,
            'n_sign_posn': 1,
            'negative_sign': '-',
            'p_cs_precedes': 1,
            'p_sep_by_space': 0,
            'p_sign_posn': 1,
            'positive_sign': '',
            'thousands_sep': ',',
        },
        'bz': {
            'currency_symbol': 'BZ$',
            'decimal_point': '.',
            'frac_digits': 2,
            'grouping': [3, 3, 0],
            'int_curr_symbol': 'BZD ',
            'int_frac_digits': 2,
            'mon_decimal_point': '.',
            'mon_grouping': [3, 3, 0],
            'mon_thousands_sep': ',',
            'n_cs_precedes': 1,
            'n_sep_by_space': 0,
            'n_sign_posn': 1,
            'negative_sign': '-',
            'p_cs_precedes': 1,
            'p_sep_by_space': 0,
            'p_sign_posn': 1,
            'positive_sign': '',
            'thousands_sep': ',',
        },
        'cu': {
            'currency_symbol': '$',
            'decimal_point': '.',
            'frac_digits': 2,
            'grouping': [3, 3, 0],
            'int_curr_symbol': 'CUP ',
            'int_frac_digits': 2,
            'mon_decimal_point': '.',
            'mon_grouping': [3, 3, 0],
            'mon_thousands_sep': ',',
            'n_cs_precedes': 1,
            'n_sep_by_space': 0,
            'n_sign_posn': 1,
            'negative_sign': '-',
            'p_cs_precedes': 1,
            'p_sep_by_space': 0,
            'p_sign_posn': 1,
            'positive_sign': '',
            'thousands_sep': ',',
        },
        'jm': {
            'currency_symbol': '$',
            'decimal_point': '.',
            'frac_digits': 2,
            'grouping': [3, 3, 0],
            'int_curr_symbol': 'JMD ',
            'int_frac_digits': 2,
            'mon_decimal_point': '.',
            'mon_grouping': [3, 3, 0],
            'mon_thousands_sep': ',',
            'n_cs_precedes': 1,
            'n_sep_by_space': 0,
            'n_sign_posn': 1,
            'negative_sign': '-',
            'p_cs_precedes': 1,
            'p_sep_by_space': 0,
            'p_sign_posn': 1,
            'positive_sign': '',
            'thousands_sep': ',',
        },
    }
)
# ENDOFHACKS
CURRENCY_CODES = {
    country: data['int_curr_symbol'].strip() for country, data in CURRENCY_DATA.items()
}
DEFAULT_TIME_ZONES = {
    'us': 'America/Los_Angeles',
    'pl': 'Europe/Warsaw',  # Poland
    # SCANDINAVIA
    'dk': 'Europe/Copenhagen',  # Denmark
    'fi': 'Europe/Helsinki',  # Finland
    'no': 'Europe/Oslo',  # Norway (Bokmål)
    'se': 'Europe/Stockholm',  # Sweden
    # COMMONWEALTH
    'au': 'Australia/Perth',  # Australia
    'bz': 'America/Belize',  # Belize
    'ca': 'America/Vancouver',  # Canada
    'gb': 'Europe/London',  # Great Britain
    'hk': 'Asia/Hong_Kong',  # Hong Kong
    'ie': 'Europe/Dublin',  # Ireland
    'in': 'Asia/Kolkata',  # India
    'jm': 'America/Jamaica',  # Jamaica
    'mt': 'Europe/Malta',  # Malta
    'my': 'Asia/Kuala_Lumpur',  # Malaysia
    'nz': 'Pacific/Auckland',  # New Zealand
    'ng': 'Africa/Lagos',  # Nigeria
    'sg': 'Asia/Singapore',  # Singapore
    'za': 'Africa/Johannesburg',  # Southern Africa
    # LATINO & PENINSULARES
    'es': 'Europe/Madrid',  # Spain
    'ar': 'America/Argentina/Buenos_Aires',  # Argentina
    'bo': 'America/La_Paz',  # Bolivia
    'cl': 'America/Santiago',  # Chile
    'co': 'America/Bogota',  # Colombia
    'cu': 'America/Havana',  # Cuba
    'ec': 'America/Guayaquil',  # Ecuador
    'gt': 'America/Guatemala',  # Guatemala
    'hn': 'America/Tegucigalpa',  # Honduras
    'mx': 'America/Mexico_City',  # Mexico
    'ni': 'America/Managua',  # Nicaragua
    'pa': 'America/Panama',  # Panama
    'py': 'America/Asuncion',  # Paraguay
    'pe': 'America/Lima',  # Peru
    'uy': 'America/Montevideo',  # Uruguay
    've': 'America/Caracas',  # Venezuela
    # Other
    'br': 'America/Rio_Branco',  # Brazil
    'de': 'Europe/Berlin',  # Germany
    'fr': 'Europe/Paris',  # France
    'nl': 'Europe/Amsterdam',  # Netherlands
    'it': 'Europe/Rome',  # Italy
    'pt': 'Europe/Lisbon',  # Portugal
    'ru': 'Europe/Kaliningrad',  # Russia
    'jp': 'Asia/Tokyo',  # Japan
    'ph': 'Asia/Manila',  # Philippines
    # Extended countries
    'pr': 'America/Puerto_Rico',  # Puerto Rico
    'vi': 'America/St_Thomas',  # Virgin Islands
    'gu': 'Pacific/Guam',  # Guam
    'mp': 'Pacific/Saipan',  # Northern Mariana Islands
    'ws': 'Pacific/Apia',  # Samoa
    'im': 'Europe/Isle_of_Man',  # Isle of Man
    'bm': 'Atlantic/Bermuda',  # Bermuda
    'gg': 'Europe/Guernsey',  # Guernsey
    'je': 'Europe/Jersey',  # Jersey
    'gi': 'Europe/Gibraltar',  # Gibraltar
    'gf': 'America/Cayenne',  # French Guiana
    'gp': 'America/Guadeloupe',  # Guadeloupe
    'mq': 'America/Martinique',  # Martinique
    'yt': 'Indian/Mayotte',  # Mayotte
    're': 'Indian/Reunion',  # Réunion
}
FIRST_DAY_OF_WEEK = {
    'ar': 1,  # Argentina
    'au': 1,  # Australia
    'bm': 1,  # Bermuda
    'bo': 1,  # Bolivia
    'br': 1,  # Brazil
    'bz': 0,  # Belize
    'ca': 0,  # Canada
    'cl': 0,  # Chile
    'co': 1,  # Colombia
    'cr': 0,  # Costa Rica
    'cu': 0,  # Cuba
    'de': 1,  # Germany
    'dk': 1,  # Denmark
    'ec': 1,  # Ecuador
    'es': 1,  # Spain
    'fi': 1,  # Finland
    'fr': 1,  # France
    'gb': 1,  # Great Britain
    'gf': 1,  # French Guiana
    'gg': 1,  # Guernsey
    'gi': 1,  # Gibraltar
    'gp': 1,  # Guadeloupe
    'gt': 1,  # Guatemala
    'gu': 0,  # Guam
    'hk': 1,  # Hong Kong
    'hn': 1,  # Honduras
    'ie': 1,  # Ireland
    'im': 1,  # Isle of Man
    'in': 1,  # India
    'it': 1,  # Italy
    'je': 1,  # Jersey
    'jm': 0,  # Jamaica
    'jp': 1,  # Japan
    'mp': 0,  # Northern Mariana Islands
    'mq': 1,  # Martinique
    'mt': 1,  # Malta
    'mx': 1,  # Mexico
    'my': 1,  # Malaysia
    'ng': 1,  # Nigeria
    'ni': 1,  # Nicaragua
    'nl': 1,  # Netherlands
    'no': 1,  # Norway
    'nz': 1,  # New Zealand
    'pa': 1,  # Panama
    'pe': 1,  # Peru
    'ph': 0,  # Philippines
    'pl': 1,  # Poland
    'pr': 0,  # Puerto Rico
    'pt': 1,  # Portugal
    'py': 1,  # Paraguay
    're': 1,  # Réunion
    'ru': 1,  # Russia
    'se': 1,  # Sweden
    'sg': 0,  # Singapore
    'sv': 0,  # El Salvador
    'us': 0,  # USA
    'uy': 1,  # Uruguay
    've': 1,  # Venezuela
    'vi': 0,  # Virgin Islands
    'ws': 0,  # Samoa
    'yt': 1,  # Mayotte
    'za': 1,  # Southern Africa
    'zh': 1,  # China
}

#######################################
## CUSTOMER SUPPORT CONTACT DETAILS
#######################################


CS_PHONES = {
    'us': '******-735-3553',  # default
    'pl': '+48 570 027 321',
    'gb': '+44 1323 700057',
    'ie': '+353 76 680 1661',
    'br': '+55 11 2769-6566',
    'in': '+91 97663 15188',
    'ph': '+63 2 706 6226',
    'za': '+27 73 455 2068',
    'es': '+34 858 16 00 49',
    'cl': '+34 858 16 00 49',  # for now
    'mx': '+52 55 852 66563',
    'fr': '+33 1 76 39 10 11',
    'pr': '******-735-3553',
    'vi': '******-735-3553',
    'gu': '******-735-3553',
    'mp': '******-735-3553',
    'ws': '******-735-3553',
    'im': '+44 1323 700057',
    'bm': '+44 1323 700057',
    'gg': '+44 1323 700057',
    'je': '+44 1323 700057',
    'gi': '+44 1323 700057',
    'gf': '+33 1 76 39 10 11',
    'gp': '+33 1 76 39 10 11',
    'mq': '+33 1 76 39 10 11',
    'yt': '+33 1 76 39 10 11',
    're': '+33 1 76 39 10 11',
}
CS_WORKING_HOURS = {
    'us': ('9', '17'),  # default
    'pl': ('9', '17'),
    'sg': ('9am', '6pm'),
}

CS_EMAILS = {
    'default': '<EMAIL>',
    'br': '<EMAIL>',
    'es': '<EMAIL>',
    'gb': '<EMAIL>',
    'ie': '<EMAIL>',
    'mx': '<EMAIL>',
    'pl': '<EMAIL>',
    'us': '<EMAIL>',
    'za': '<EMAIL>',
    'pr': '<EMAIL>',
    'vi': '<EMAIL>',
    'gu': '<EMAIL>',
    'mp': '<EMAIL>',
    'ws': '<EMAIL>',
    'im': '<EMAIL>',
    'bm': '<EMAIL>',
    'gg': '<EMAIL>',
    'je': '<EMAIL>',
    'gi': '<EMAIL>',
}

CS_EMAILS_BIZ = {
    'default': '<EMAIL>',
    'br': '<EMAIL>',
    'es': '<EMAIL>',
    'gb': '<EMAIL>',
    'ie': '<EMAIL>',
    'mx': '<EMAIL>',
    'pl': '<EMAIL>',
    'us': '<EMAIL>',
    'za': '<EMAIL>',
    'pr': '<EMAIL>',
    'vi': '<EMAIL>',
    'gu': '<EMAIL>',
    'mp': '<EMAIL>',
    'ws': '<EMAIL>',
    'im': '<EMAIL>',
    'bm': '<EMAIL>',
    'gg': '<EMAIL>',
    'je': '<EMAIL>',
    'gi': '<EMAIL>',
}

REPORT_EMAILS = CS_EMAILS.copy()
REPORT_EMAILS['pl'] = '<EMAIL>'
REPORT_EMAILS['default'] = '<EMAIL>'  # default
REPORT_EMAILS['us'] = '<EMAIL>'
