{"./cliapps/business/business_categories/tests/test_change_tmp_slugs.py": 0.393, "./cliapps/business/business_categories/tests/test_synchronize_treatments_assigned_to_businesses.py": 0.529, "./cliapps/business/business_categories/tests/test_update_business_categories__ca.py": 0.7090000*********, "./cliapps/business/business_categories/tests/test_update_business_categories__es.py": 0.352, "./cliapps/business/business_categories/tests/test_update_business_categories__pl.py": 0.486, "./cliapps/business/business_categories/tests/test_update_business_categories__us.py": 0.09199999999999998, "./cliapps/business/business_categories/tests/test_utils.py": 0.521, "./cliapps/business/tests/test_merge_treatments__gb.py": 0.32399999999999995, "./cliapps/business/tests/test_update_payment_source.py": 0.6990000*********, "./cliapps/business/tests/test_update_previous_slugs__ca.py": 0.253, "./cliapps/business/tests/test_update_treatments__ca.py": 0.012, "./cliapps/business/tests/test_update_treatments__gb.py": 0.379, "./cliapps/business/tests/test_validate_new_business_categories_slugs.py": 0.307, "./cliapps/celery_tools/tests/test_cli_remove_tasks.py": 0.03, "./cliapps/es/tests/test_indices.py": 0.035, "./cliapps/es/tests/test_parse_document_types.py": 0.058, "./cliapps/user/tests/test_cancel_account_deletion_requests.py": 3.716, "./cliapps/user/tests/test_delete_requested_user_data.py": 0.574, "./cliapps/user/tests/test_generate_customer_user_ids_for_deletion.py": 0.254, "./cliapps/user/tests/test_remove_staffers_ric_923.py": 0.744, "./cliapps/user/tests/test_script_cleans_objects_from_account_deletion_phase_2.py": 2.858, "./cliapps/user/tests/test_send_emails_for_old_account_deletion_requests.py": 7.684000*********, "./country_config/tests/test_country_config.py": 3.117, "./debug_tools/tests/test_helper.py": 0.696, "./debug_tools/tests/test_tools/test_billing_tools.py": 6.***************, "./debug_tools/tests/test_utils.py": 0.033, "./drf_api/docs/tests/test_views.py": 0.369, "./drf_api/exceptions/tests/test_error_handler.py": 45.349, "./drf_api/service/auth/views/tests/test_logout.py": 0.241, "./drf_api/service/booksy_storage/tests/test_data.py": 0.113, "./drf_api/service/business/account/tests/test_session_status.py": 0.086, "./drf_api/service/business/calendar/tests/test_next_staffer.py": 0.925, "./drf_api/service/business/validators/tests/test_business_view_validator.py": 2.****************, "./drf_api/service/business/views/tests/test_business_utils.py": 0.477, "./drf_api/service/business/views/tests/test_business_visibility.py": 5.***************, "./drf_api/service/customer/tests/test_serializers.py": 0.*****************, "./drf_api/service/customer/views/tests/test_customer_business_views.py": 1.4080000*********, "./drf_api/service/customer/views/tests/test_customer_history.py": 0.116, "./drf_api/service/customer/views/tests/test_people_also_booked.py": 4.112, "./drf_api/service/family_and_friends/tests/test_basics_of_members.py": 5.674, "./drf_api/service/family_and_friends/tests/test_invitation.py": 4.94, "./drf_api/service/family_and_friends/tests/test_match_invited_user.py": 0.131, "./drf_api/service/family_and_friends/tests/test_member_photo.py": 0.9770000*********, "./drf_api/service/family_and_friends/tests/test_unlink.py": 5.094999999999999, "./drf_api/service/family_and_friends/tests/test_update_delete_profile_after_user_changes.py": 16.451999999999998, "./drf_api/service/help_center/tests/test_article_details.py": 0.8650000000000002, "./drf_api/service/help_center/tests/test_category_details.py": 1.288, "./drf_api/service/help_center/tests/test_category_list.py": 3.083999999999999, "./drf_api/service/migrator/tests/test_migrator.py": 0.152, "./drf_api/service/my_booksy/tests/test_my_booksy_pop_up_notification.py": 0.308, "./drf_api/service/navision/tests/documents/test_document_description.py": 11.779, "./drf_api/service/navision/tests/documents/test_document_list.py": 4.276, "./drf_api/service/notification/tests/test_webhooks.py": 0.116, "./drf_api/service/other/content_reports/tests/test_report_inappropriate_content.py": 0.158, "./drf_api/service/other/debug/tests/test_debug_stats.py": 0.07, "./drf_api/service/other/debug/tests/test_health_check.py": 0.026000000000000002, "./drf_api/service/other/debug/tests/test_redis_priority_monitor.py": 0.059, "./drf_api/service/other/routing/tests/test_force_update.py": 0.*****************, "./drf_api/service/other/routing/tests/test_views.py": 0.****************, "./drf_api/service/partner_app/tests/test_catalog.py": 2.119, "./drf_api/service/partner_app/tests/test_category.py": 0.159, "./drf_api/service/partner_app/tests/test_partner_app.py": 4.023, "./drf_api/service/splash/views/tests/test_post_checkout_splash.py": 3.****************, "./drf_api/service/stripe_integration/tests/test_fast_payout_account_details.py": 0.279, "./drf_api/service/stripe_integration/tests/test_fast_payout_available_balance.py": 0.412, "./drf_api/service/stripe_integration/tests/test_fast_payout_create.py": 2.****************, "./drf_api/service/stripe_integration/tests/test_fast_payout_limit_api_view.py": 1.255, "./drf_api/service/stripe_integration/tests/test_fast_payouts_merchant_enabled.py": 0.272, "./drf_api/service/stripe_integration/tests/test_payout_detail_list.py": 1.2570000*********, "./drf_api/service/stripe_sdi/tests/test_sdi_cancel_reader_action.py": 1.279, "./drf_api/service/stripe_sdi/tests/test_sdi_change_integration_mode.py": 0.113, "./drf_api/service/stripe_sdi/tests/test_sdi_payment_intent_status.py": 0.17, "./drf_api/service/stripe_sdi/tests/test_sdi_process_payment_intent.py": 0.162, "./drf_api/service/unsubscribe/tests/test_unsubscribe.py": 0.****************, "./drf_api/service/user/tests/test_delete_account.py": 1.****************, "./drf_api/service/utils/tests/test_check_password.py": 2.179, "./drf_api/service/versum_migration/tests/test_set_initial_versum_migration_password.py": 0.5820000*********, "./drf_api/service/whats_new/tests/test_view.py": 1.****************, "./drf_api/service/zowie_integration/tests/test_generate_jwt_for_zowie.py": 3.****************, "./drf_api/tests/test_booking_source_mixin.py": 0.*****************, "./drf_api/tests/test_booksy_view_session_mixin.py": 0.045, "./drf_api/tests/test_business_view_validator_mixin.py": 0.*****************, "./drf_api/tests/test_cors.py": 1.****************, "./drf_api/tests/test_es_json_encoder.py": 0.336, "./drf_api/tests/test_language.py": 0.4750000*********, "./drf_api/tests/test_profile.py": 0.*****************, "./drf_api/tests/test_request_logging.py": 30.10800*********5, "./drf_api/tests/test_sessions.py": 1.0070000*********, "./drf_api/tests/test_throttling.py": 0.135, "./drf_api/tests/test_url_point_to_proper_view.py": 0.026999999999999996, "./drf_api/tests/test_view_property.py": 0.06799999999999999, "./drf_api/tests/test_view_resolve_name.py": 0.096, "./drf_api/validators/tests/test_base_validation.py": 0.074, "./drf_api/validators/tests/test_validator.py": 0.045, "./env_secrets/tests/test_dev_secrets_provider.py": 2.813, "./env_secrets/tests/test_django_secrets_provider.py": 0.020999999999999998, "./env_secrets/tests/test_production_secrets_provider.py": 0.01, "./env_secrets/tests/test_secrets_manager.py": 0.042, "./lib/apple/tests/test_apns_client.py": 3.496, "./lib/booksy_sms/tests/test_sms_cost.py": 0.032, "./lib/booksy_sms/tests/test_vonage.py": 31.499000000000027, "./lib/celery_utils/metrics/tests/test_metrics.py": 1.102, "./lib/datadog/tests/test_celery_tasks_resources.py": 0.042, "./lib/datadog/tests/test_drf_resources.py": 0.1, "./lib/datadog/tests/test_tornado_resources.py": 0.16300000000000003, "./lib/deeplink/tests/test_deeplink_class.py": 0.025, "./lib/deeplink/tests/test_grpc.py": 0.10799999999999998, "./lib/deeplink/tests/test_pubsub.py": 0.1, "./lib/dispatch_context/tests/test_drf_context.py": 0.096, "./lib/dispatch_context/tests/test_tornado_context.py": 0.135, "./lib/elasticsearch/tests/test_analyzers.py": 33.10099999999998, "./lib/elasticsearch/tests/test_document_serializer.py": 0.033, "./lib/elasticsearch/tests/test_ilm_index.py": 1.5, "./lib/elasticsearch/tests/test_mapping_base.py": 0.01, "./lib/elasticsearch/tests/test_phone_completion.py": 0.31, "./lib/elasticsearch/tests/test_reindex_task_limit.py": 2.6, "./lib/elasticsearch/tests/test_tools.py": 0.031, "./lib/email/tests/test_bad_recipient_address.py": 0.05, "./lib/email/tests/test_iterable_client.py": 0.073, "./lib/email/tests/test_prepare_and_send_mail.py": 1.3569999999999995, "./lib/es_history/tests/test_diff.py": 0.07700000*********, "./lib/es_history/tests/test_document.py": 0.019999999999999997, "./lib/es_history/tests/test_manager.py": 3.322, "./lib/es_history/tests/test_models.py": 0.795, "./lib/es_history/tests/test_queue.py": 0.026000000000000002, "./lib/es_history/tests/test_utils.py": 0.031, "./lib/facebook/tests/test_utils.py": 0.066, "./lib/feature_flag/tests/test_class_based_flags.py": 0.14400000000000002, "./lib/feature_flag/tests/test_feature_flag_facade.py": 0.047, "./lib/feature_flag/tests/test_kill_switch_adapter.py": 0.039, "./lib/feature_flag/tests/test_launch_darkly_adapter.py": 0.017, "./lib/feature_flag/tests/test_launch_darkly_user_data_object.py": 0.045, "./lib/feature_flag/tests/test_override_decorator.py": 0.03, "./lib/fields/tests/test_deprecate_field.py": 0.026000000000000002, "./lib/fields/tests/test_password_fields.py": 2.762999999999999, "./lib/fields/tests/test_state_field.py": 0.076, "./lib/fields/tests/test_zipcode_field.py": 0.21300000000000002, "./lib/firebase/tests/test_firebase_api.py": 0.044000000000000004, "./lib/gcs_dataset/tests/test_conversion.py": 0.081, "./lib/gcs_dataset/tests/test_treatment.py": 0.14500000000000002, "./lib/gcs_dataset/tests/test_user_interest.py": 0.30600000000000005, "./lib/geocoding/tests/test__built_in.py": 0.01, "./lib/geocoding/tests/test_here_maps.py": 0.4240000000000002, "./lib/geocoding/tests/test_primitives.py": 0.159, "./lib/geocoding/tests/test_utils.py": 30.029, "./lib/geocoding_v2/tests/test_address.py": 0.21100000000000005, "./lib/lifecycle_utils/tests/test_drain.py": 0.011, "./lib/lifecycle_utils/tests/test_liveness.py": 0.41100000000000003, "./lib/lifecycle_utils/tests/test_readiness.py": 0.064, "./lib/lifecycle_utils/tests/test_uwsgi_stats.py": 0.025, "./lib/monkeypatching/tests/test_custom_m2m_model.py": 0.018000000000000002, "./lib/monkeypatching/tests/test_custom_refresh_from_db.py": 0.01, "./lib/monkeypatching/tests/test_jsonify.py": 2.409, "./lib/pubsub_depr/tests/test_tools.py": 0.028, "./lib/rate_limiter/tests/test_redis_rate_limiter.py": 0.08199999999999999, "./lib/searchables/tests/test_searchables.py": 0.049, "./lib/searchables/tests/test_serializer_in_searchable.py": 0.011, "./lib/segment_analytics/tests/test_context.py": 0.078, "./lib/swagger/tests/test_tornado_inspector.py": 7.159, "./lib/tagmanager/tests/test_client.py": 0.278, "./lib/tasks/tests/test_new_eta_tasks.py": 1.192, "./lib/tests/test_anonimyze.py": 2.689, "./lib/tests/test_api_client.py": 3.672, "./lib/tests/test_booksy_sms.py": 1.699, "./lib/tests/test_booksy_sms_counter.py": 0.3080000*********6, "./lib/tests/test_booksy_sms_invite_optimization.py": 0.051000000000000004, "./lib/tests/test_booksy_sms_whitelist.py": 0.6070000*********, "./lib/tests/test_booksy_weekdays.py": 0.075, "./lib/tests/test_brazil_dial_code_completion.py": 1.2209999999999999, "./lib/tests/test_cache.py": 0.165, "./lib/tests/test_capping.py": 0.22800000000000006, "./lib/tests/test_datetime_util.py": 0.032, "./lib/tests/test_dynamic_fields_serializer.py": 0.051000000000000004, "./lib/tests/test_employer_identification_number_validator.py": 2.329, "./lib/tests/test_enums.py": 3.031, "./lib/tests/test_events.py": 0.094, "./lib/tests/test_fields_from_docs.py": 1.319, "./lib/tests/test_flatten_errors.py": 2.55, "./lib/tests/test_format_currency.py": 0.11199999999999999, "./lib/tests/test_gdpr_lib.py": 0.3800000*********, "./lib/tests/test_history_model.py": 0.4570000*********, "./lib/tests/test_invite.py": 2.2209999999999996, "./lib/tests/test_invite_customer.py": 0.8069999999999999, "./lib/tests/test_ios_certificate.py": 0.10600000*********, "./lib/tests/test_langauge_helper.py": 2.024, "./lib/tests/test_lib_celery.py": 0.043, "./lib/tests/test_lib_db.py": 0.11000000*********, "./lib/tests/test_lib_jinja_renderer.py": 0.09, "./lib/tests/test_lib_tools.py": 0.23100000000000004, "./lib/tests/test_locks.py": 1.116, "./lib/tests/test_lokalise_client.py": 0.076, "./lib/tests/test_merge_helper.py": 28.87300000000003, "./lib/tests/test_models_use_auto_update_queryset.py": 0.011, "./lib/tests/test_none_empty_str_serializer_mixin.py": 0.033, "./lib/tests/test_pdf_renderer.py": 0.173, "./lib/tests/test_preprocess_messages.py": 0.052000000000000005, "./lib/tests/test_pretty_json_widget.py": 0.069, "./lib/tests/test_ranges.py": 0.016, "./lib/tests/test_rivers.py": 0.088, "./lib/tests/test_safe_json.py": 0.07700000*********, "./lib/tests/test_segment_analytics/test_aha_habit_moment.py": 0.636, "./lib/tests/test_segment_analytics/test_appointment_booked.py": 0.175, "./lib/tests/test_segment_analytics/test_base_track_props.py": 2.068, "./lib/tests/test_segment_analytics/test_client_added.py": 0.013, "./lib/tests/test_segment_analytics/test_clients_imported.py": 0.123, "./lib/tests/test_segment_analytics/test_clients_screen_shown.py": 0.122, "./lib/tests/test_segment_analytics/test_customer_invitation_sent.py": 0.054, "./lib/tests/test_segment_analytics/test_customer_invitation_sms_succeeded.py": 0.484, "./lib/tests/test_segment_analytics/test_experiment_variant.py": 0.056999999999999995, "./lib/tests/test_segment_analytics/test_identify_merchant_metropolis.py": 0.14900000000000002, "./lib/tests/test_segment_analytics/test_identify_wam.py": 0.10300000*********, "./lib/tests/test_segment_analytics/test_make_call.py": 2.969, "./lib/tests/test_segment_analytics/test_merchant_status.py": 0.2880000*********4, "./lib/tests/test_segment_analytics/test_registration_completed.py": 2.264, "./lib/tests/test_segment_analytics/test_segment_business_wrapper.py": 0.048, "./lib/tests/test_segment_analytics/test_specific_sum_of_appointments.py": 0.031, "./lib/tests/test_segment_analytics/test_subscription_purchased.py": 0.28, "./lib/tests/test_segment_analytics/test_subscription_updated.py": 0.518, "./lib/tests/test_segment_analytics/test_trial_countdown_started.py": 2.619, "./lib/tests/test_segment_analytics/test_utils_flatten.py": 0.026000000000000002, "./lib/tests/test_segment_analytics/test_utils_get_booking_count.py": 0.396, "./lib/tests/test_send_sms.py": 0.8130000000000002, "./lib/tests/test_serializers.py": 0.17600000000000005, "./lib/tests/test_sget_sgetv2_safe_get.py": 0.046, "./lib/tests/test_singleton_metaclass.py": 0.018, "./lib/tests/test_sms_booksy.py": 0.15400000000000003, "./lib/tests/test_sms_unicode_utils.py": 32.29599999999999, "./lib/tests/test_sort_bookings.py": 0.122, "./lib/tests/test_spreadsheet.py": 0.037000000000000005, "./lib/tests/test_telnyx_sms.py": 34.04300*********, "./lib/tests/test_text_transformation.py": 0.16400000000000003, "./lib/tests/test_things.py": 0.12100000*********, "./lib/tests/test_timezone.py": 3.123, "./lib/tests/test_timezone_hours.py": 0.683, "./lib/tests/test_tools.py": 0.11100000000000002, "./lib/tests/test_truncate_logs.py": 0.027999999999999997, "./lib/tests/test_twilio_sms.py": 2.186, "./lib/tests/test_unicode_utils.py": 0.064, "./lib/tests/test_update_returning_queryset.py": 0.115, "./lib/tests/test_utils.py": 0.447, "./lib/tests/test_validate_invite_hacks.py": 0.077, "./lib/tests/test_validate_tax_number.py": 31.346999999999984, "./lib/tests/test_validators.py": 31.662999999999972, "./lib/tests/test_zh_locale_formats.py": 0.04, "./merger_grpc/tests/test_serializers.py": 0.041999999999999996, "./service/b2b_referral/tests/test_handlers.py": 3.766, "./service/b2b_referral/tests/test_serializers.py": 0.043, "./service/best_of_booksy/tests/test_generate_certificate.py": 0.398, "./service/billing/tests/stripe/test_payment_method.py": 10.195, "./service/billing/tests/test_discount_code.py": 8.571, "./service/billing/tests/test_invoices.py": 22.042, "./service/billing/tests/test_payment.py": 21.372, "./service/billing/tests/test_settings.py": 33.12399999999999, "./service/billing/tests/test_subscription.py": 25.962, "./service/billing/tests/test_terms.py": 3.825, "./service/billing/tests/test_utils.py": 31.162000000000027, "./service/booking/merger/tests/test_customer_appointment.py": 7.54800009727478, "./service/booking/merger/tests/test_timeslots_forward.py": 2.529, "./service/booking/tests/create_customer_appointment_tests/test_create_customer_appointment.py": 412.0620*********, "./service/booking/tests/create_customer_appointment_tests/test_create_customer_appointment_with_partner_forward.py": 6.778, "./service/booking/tests/publishers/test_loyalty_program.py": 0.249, "./service/booking/tests/test_appointment_listing.py": 2.092, "./service/booking/tests/test_appointment_match_user_bci.py": 2.044, "./service/booking/tests/test_appointment_notify_ready.py": 1.797, "./service/booking/tests/test_appointment_reviews.py": 2.545, "./service/booking/tests/test_appointment_with_travelling_family_and_friends.py": 11.693, "./service/booking/tests/test_archive.py": 1.922, "./service/booking/tests/test_book_again.py": 4.369, "./service/booking/tests/test_booking_action.py": 28.381000000000004, "./service/booking/tests/test_booking_details.py": 2.738, "./service/booking/tests/test_business_appointment_family_and_friends.py": 15.719000*********, "./service/booking/tests/test_business_book_again.py": 18.333, "./service/booking/tests/test_calendar_drag.py": 21.401, "./service/booking/tests/test_calendar_drag_subbooking.py": 6.786999999999999, "./service/booking/tests/test_change_customer_prepaid_appointment.py": 7.52, "./service/booking/tests/test_check_tokenized_payments.py": 0.47700000000000004, "./service/booking/tests/test_create_appointment/test_payment_links_flow.py": 39.241, "./service/booking/tests/test_create_appointment/test_standard.py": 46.212999999999994, "./service/booking/tests/test_create_appointment/test_with_prepayment.py": 47.91600*********, "./service/booking/tests/test_create_appointment_anyresource.py": 3.967, "./service/booking/tests/test_create_appointment_with_online_service.py": 6.837000*********, "./service/booking/tests/test_create_appointment_with_traveling_service.py": 14.410999999999998, "./service/booking/tests/test_create_multibooking_appointment.py": 5.189, "./service/booking/tests/test_create_repeating_appointment.py": 4.252, "./service/booking/tests/test_customer_appointment_action.py": 27.662999999999997, "./service/booking/tests/test_customer_appointment_claim.py": 18.846, "./service/booking/tests/test_customer_appointment_family_and_friends.py": 64.96199999999999, "./service/booking/tests/test_customer_appointment_handler.py": 16.622, "./service/booking/tests/test_customer_booking_business_confirm.py": 21.113, "./service/booking/tests/test_customer_booking_resources.py": 3.628, "./service/booking/tests/test_customer_bookings.py": 0.683, "./service/booking/tests/test_edit_appointment.py": 41.67400*********4, "./service/booking/tests/test_edit_multibooking_appointment.py": 14.453999999999997, "./service/booking/tests/test_edit_repeating_appointment.py": 50.67800*********, "./service/booking/tests/test_family_and_friends_appointment_details.py": 0.01, "./service/booking/tests/test_family_and_friends_appointments.py": 15.654, "./service/booking/tests/test_fiscal_receipt.py": 9.***************, "./service/booking/tests/test_force_incomplete_multibooking.py": 6.448, "./service/booking/tests/test_get_appointment_details.py": 2.121, "./service/booking/tests/test_move_appointment.py": 60.058, "./service/booking/tests/test_move_customer_appointment.py": 19.527, "./service/booking/tests/test_move_multibooking_appointment.py": 6.009, "./service/booking/tests/test_move_repeating_appointment.py": 34.281000000000006, "./service/booking/tests/test_reservations.py": 7.133000*********, "./service/booking/tests/test_time_slots.py": 24.391999999999996, "./service/booksy_auth/tests/test_booksy_auth_update_webhook.py": 1.7349999999999999, "./service/braintree_app/tests/test_payment_methods.py": 16.027000000000005, "./service/business/feature_status/tests/boost_status/test_boost_status.py": 2.33, "./service/business/feature_status/tests/boost_status/test_not_eligible.py": 2.3930000000000002, "./service/business/feature_status/tests/test_feature_status_handlers.py": 7.237, "./service/business/feature_status/tests/test_invite_customers_status.py": 3.9299999999999997, "./service/business/feature_status/tests/test_message_blast_status.py": 0.423, "./service/business/feature_status/tests/test_serializers.py": 0.02, "./service/business/feature_status/tests/test_service_promotions_status.py": 1.739, "./service/business/feature_status/tests/test_social_media_status.py": 1.578, "./service/business/serializers/tests/test_invite_again.py": 1.177, "./service/business/tests/bci_relations/test_link_existing_bcis.py": 5.642, "./service/business/tests/bci_relations/test_new_member.py": 23.497, "./service/business/tests/bci_relations/test_unlink_bci_relations.py": 17.756, "./service/business/tests/bci_relations/test_update_member_bci.py": 25.453, "./service/business/tests/test_account.py": 1.486, "./service/business/tests/test_account_deletion.py": 2.584, "./service/business/tests/test_agenda.py": 0.017, "./service/business/tests/test_ask_switch_to_pro_handler.py": 0.73, "./service/business/tests/test_auth_token.py": 4.767, "./service/business/tests/test_business_access_level.py": 10.664, "./service/business/tests/test_business_account_create.py": 17.***************, "./service/business/tests/test_business_account_handler.py": 8.216, "./service/business/tests/test_business_activate.py": 11.626000*********, "./service/business/tests/test_business_android_push_receiver.py": 6.659, "./service/business/tests/test_business_basic_details_handler.py": 0.964, "./service/business/tests/test_business_change_extra_attrs.py": 0.119, "./service/business/tests/test_business_change_package.py": 5.271, "./service/business/tests/test_business_create_from_b_listing.py": 6.525, "./service/business/tests/test_business_create_handler.py": 131.**************, "./service/business/tests/test_business_customer_attached_files.py": 5.911000*********, "./service/business/tests/test_business_customer_exists.py": 16.511, "./service/business/tests/test_business_customer_groups.py": 1.849, "./service/business/tests/test_business_customer_import_handler.py": 13.***************, "./service/business/tests/test_business_customer_import_handler_json.py": 11.915, "./service/business/tests/test_business_customer_info_bookings_fizjo_handler.py": 2.99, "./service/business/tests/test_business_customer_info_bookings_handler.py": 2.182, "./service/business/tests/test_business_customer_info_different_types.py": 10.672, "./service/business/tests/test_business_customer_info_handler.py": 26.174999999999997, "./service/business/tests/test_business_customer_info_list_handler.py": 15.48, "./service/business/tests/test_business_customer_info_list_handler_from_letter.py": 8.897, "./service/business/tests/test_business_customer_info_manual_merge_handler.py": 48.36600*********, "./service/business/tests/test_business_customer_info_merge.py": 10.557, "./service/business/tests/test_business_customer_info_more_photos_reordering.py": 1.61, "./service/business/tests/test_business_customer_info_products.py": 1.326, "./service/business/tests/test_business_customer_info_tags_handler.py": 0.746, "./service/business/tests/test_business_customers_groups_listing.py": 0.01, "./service/business/tests/test_business_details_filtering.py": 9.979, "./service/business/tests/test_business_email_receiver.py": 2.351, "./service/business/tests/test_business_email_receiver_check_handler.py": 0.876, "./service/business/tests/test_business_gdpr_annex_pdf_preview.py": 2.354, "./service/business/tests/test_business_ios_push_receiver.py": 3.7800000000000002, "./service/business/tests/test_business_login.py": 23.653, "./service/business/tests/test_business_publish_photos_to_portfolio.py": 1.702, "./service/business/tests/test_business_sms_management.py": 4.446, "./service/business/tests/test_business_user_session_exists.py": 5.087, "./service/business/tests/test_calendars.py": 33.08999999999999, "./service/business/tests/test_calendars_hours.py": 0.183, "./service/business/tests/test_category.py": 2.578, "./service/business/tests/test_create_business.py": 7.645, "./service/business/tests/test_create_by_percent.py": 0.01, "./service/business/tests/test_customer_info.py": 5.601000*********, "./service/business/tests/test_customer_info_thin.py": 5.372, "./service/business/tests/test_deeplinks.py": 0.756, "./service/business/tests/test_details.py": 47.402999999999984, "./service/business/tests/test_gdpr_agreements.py": 9.892, "./service/business/tests/test_invite_again.py": 69.697, "./service/business/tests/test_locale.py": 0.726, "./service/business/tests/test_login_status.py": 3.789, "./service/business/tests/test_monthly_bookings.py": 18.076, "./service/business/tests/test_mybusiness_handler.py": 53.71099999999999, "./service/business/tests/test_mybusinesses_handler.py": 4.666, "./service/business/tests/test_notification_list_access.py": 2.375, "./service/business/tests/test_online_booking_integrations.py": 2.45, "./service/business/tests/test_push_receiver.py": 3.123, "./service/business/tests/test_quick_invite.py": 10.677000*********, "./service/business/tests/test_recently_invited.py": 0.032, "./service/business/tests/test_renting_venue.py": 1.156, "./service/business/tests/test_renting_venue_enpoints.py": 4.686, "./service/business/tests/test_reviews.py": 14.920000000000002, "./service/business/tests/test_service_addons.py": 5.3119999999999985, "./service/business/tests/test_service_categories.py": 13.993, "./service/business/tests/test_service_handler.py": 19.869, "./service/business/tests/test_service_hints.py": 8.942, "./service/business/tests/test_service_promotions.py": 10.472, "./service/business/tests/test_service_suggestion.py": 2.054, "./service/business/tests/test_service_usage_info.py": 1.6179999999999999, "./service/business/tests/test_services_handler.py": 21.891, "./service/business/tests/test_services_reordering.py": 2.451, "./service/business/tests/test_services_wordclouds.py": 0.9359999999999999, "./service/business/tests/test_subdomain.py": 0.039, "./service/business/tests/test_utils.py": 4.04, "./service/business_related/tests/test_amenities.py": 1.487, "./service/business_related/tests/test_amenities_access.py": 0.663, "./service/business_related/tests/test_concierge_email.py": 1.5470000000000002, "./service/business_related/tests/test_safety_rules.py": 5.733, "./service/c2b_referral/tests/test_c2b_data.py": 0.79, "./service/consents/tests/test_consent_forms.py": 6.112, "./service/consents/tests/test_consents.py": 31.***************, "./service/customer/tests/test_book_again.py": 30.***************, "./service/customer/tests/test_business_facebook_login.py": 17.781, "./service/customer/tests/test_businesses_gallery.py": 9.584, "./service/customer/tests/test_businesses_with_promotions.py": 1.019, "./service/customer/tests/test_customer_account_handler.py": 12.359, "./service/customer/tests/test_customer_android_push_receiver.py": 5.776, "./service/customer/tests/test_customer_bookmark.py": 1.647, "./service/customer/tests/test_customer_create_account.py": 26.***************, "./service/customer/tests/test_customer_facebook_login.py": 17.***************, "./service/customer/tests/test_customer_ios_push_receiver.py": 3.837, "./service/customer/tests/test_customer_login.py": 6.469, "./service/customer/tests/test_customer_phone_serializer.py": 0.076, "./service/customer/tests/test_customer_photo.py": 0.805, "./service/customer/tests/test_customer_reviews.py": 5.884, "./service/customer/tests/test_customer_reviews_awaiting.py": 0.647, "./service/customer/tests/test_galleries_generator.py": 0.069, "./service/customer/tests/test_hcaptcha.py": 0.*****************, "./service/customer/tests/test_ios_token_cleanup.py": 0.01, "./service/customer/tests/test_lavito_customers_auth.py": 0.01, "./service/customer/tests/test_my_booksy.py": 23.***************, "./service/customer/tests/test_my_booksy_available_today.py": 3.03, "./service/customer/tests/test_my_booksy_favorites_visited.py": 4.640000*********, "./service/customer/tests/test_my_booksy_galleries.py": 15.110999999999999, "./service/customer/tests/test_my_booksy_important_bookings.py": 2.779, "./service/customer/tests/test_my_booksy_last_booking_location.py": 0.661, "./service/customer/tests/test_my_booksy_near_me.py": 1.8530000000000002, "./service/customer/tests/test_my_booksy_new_on_booksy.py": 26.815, "./service/customer/tests/test_my_booksy_notifications_family_and_friends.py": 4.4750000000000005, "./service/customer/tests/test_my_booksy_reactivate_booking.py": 3.3, "./service/customer/tests/test_my_booksy_recommended.py": 0.752, "./service/customer/tests/test_recaptcha.py": 0.05, "./service/customer/tests/test_reviews.py": 12.51, "./service/customer/tests/test_sms_code.py": 8.482, "./service/customer/tests/test_timeslots_booksy_slots.py": 0.05, "./service/customer/tests/test_timeslots_scopes.py": 0.7719999999999999, "./service/customer/tests/test_user_agreement_messge.py": 0.028, "./service/customer/tests/test_user_agrrements.py": 5.366999999999999, "./service/df_creator/tests/test_dynamic_texts.py": 0.41900000000000004, "./service/df_creator/tests/test_flyer_data.py": 2.2489999999999997, "./service/df_creator/tests/test_hashtags.py": 0.901, "./service/df_creator/tests/test_render.py": 0.788, "./service/df_creator/tests/test_serializers.py": 1.504, "./service/df_creator/tests/test_shared.py": 0.99, "./service/donation/tests/test_donations.py": 2.835, "./service/donation/tests/test_donations_modal.py": 1.521, "./service/experiment_v3/tests/test_business_experiment.py": 24.897, "./service/experiment_v3/tests/test_named_experiment.py": 9.472999999999999, "./service/images/tests/test_cover_image.py": 6.567, "./service/images/tests/test_image_comment.py": 0.891, "./service/images/tests/test_image_delete_from_es.py": 4.9479999999999995, "./service/images/tests/test_image_reorder.py": 3.568, "./service/images/tests/test_image_search_handler.py": 3.406, "./service/images/tests/test_image_upload.py": 19.993, "./service/images/tests/test_serializers.py": 0.018, "./service/images/v2/tests/test_cover_image_access.py": 0.785, "./service/images/v2/tests/test_handlers.py": 14.712, "./service/images/v2/tests/test_serializers.py": 0.7160000000000002, "./service/intro_screen/tests/test_intro_screen_endpoints.py": 3.096, "./service/intro_screen/tests/test_serializers.py": 0.36, "./service/invoicing/tests/test_bci_buyers.py": 1.78, "./service/invoicing/tests/test_buyers.py": 5.2989999999999995, "./service/invoicing/tests/test_cash_register_document.py": 7.634, "./service/invoicing/tests/test_fc_invoices.py": 37.054, "./service/invoicing/tests/test_fc_sellers.py": 4.781, "./service/invoicing/tests/test_invoices.py": 18.622, "./service/invoicing/tests/test_sellers.py": 4.9079999999999995, "./service/management/commands/tests/test_fix_stripe_customer_data.py": 0.06, "./service/market_pay/tests/test_marketpay_services.py": 0.6829999999999999, "./service/market_pay/tests/test_next_payout.py": 0.778, "./service/market_pay/tests/test_payout_details.py": 0.839, "./service/market_pay/tests/test_payouts_list.py": 0.82, "./service/market_pay/tests/test_pba.py": 1.859, "./service/marketing/tests/test_dynamic_listing.py": 18.386, "./service/marketing/tests/test_trigger_event.py": 5.196, "./service/marketing/tests/test_unsubscribe.py": 2.2969999999999997, "./service/marketplace/tests/cms/test_seo_cms.py": 5.5200000000000005, "./service/message_blast/tests/test_image.py": 0.829, "./service/message_blast/tests/test_images.py": 136.374, "./service/message_blast/tests/test_message_blast.py": 97.58000000000004, "./service/metrics/tests/test_business_applications.py": 0.752, "./service/notification/tests/test_evox_webhook.py": 2.165, "./service/notification/tests/test_iterable_webhook.py": 3.393, "./service/notification/tests/test_telnyx_webhook.py": 7.****************, "./service/notification/tests/test_twilio_serializer.py": 0.059000000000000004, "./service/notification/tests/test_twillo_webhook.py": 0.025, "./service/other/contact_us/tests/test_contact_us_endpoint.py": 1.771, "./service/other/tests/test_account_exists.py": 0.595, "./service/other/tests/test_celery.py": 1.387, "./service/other/tests/test_cloudfront_proxy.py": 2.254, "./service/other/tests/test_geocoding.py": 3.252, "./service/other/tests/test_get_business_network.py": 2.284, "./service/other/tests/test_phone_validation.py": 1.59, "./service/other/tests/test_physical_therapy.py": 0.061, "./service/other/tests/test_visit_redirect.py": 5.345, "./service/other/tests/test_wait_list.py": 2.292, "./service/other/tests/test_warehouse.py": 0.949, "./service/partners/facebook/v3/tests/test_dialog_oauth_handler.py": 7.256, "./service/partners/facebook/v3/tests/test_extras_handler.py": 3.****************, "./service/partners/facebook/v3/tests/test_token_handler.py": 7.753, "./service/partners/facebook/v3/tests/test_unit.py": 3.142, "./service/partners/facebook/v3/tests/test_webhook_handler.py": 7.****************, "./service/partners/groupon/v1/tests/test_availability.py": 7.623000*********, "./service/partners/groupon/v1/tests/test_booking.py": 14.222000*********, "./service/partners/groupon/v1/tests/test_heartbeat_check.py": 1.4449999999999998, "./service/partners/groupon/v1/tests/test_serializers.py": 5.897000*********, "./service/pos/tests/test_avs_zipcode.py": 2.16, "./service/pos/tests/test_booking_charges.py": 5.178, "./service/pos/tests/test_business_action.py": 17.403, "./service/pos/tests/test_business_bsx.py": 4.548, "./service/pos/tests/test_business_cash_flow.py": 1.6800000000000002, "./service/pos/tests/test_business_last_receipt.py": 1.591, "./service/pos/tests/test_business_pos_handler.py": 10.238999999999999, "./service/pos/tests/test_business_pos_plan_info_handler.py": 11.15, "./service/pos/tests/test_business_simple_transaction.py": 155.68399999999994, "./service/pos/tests/test_business_transaction.py": 235.935, "./service/pos/tests/test_business_transaction_details.py": 25.754000000000005, "./service/pos/tests/test_business_transaction_family_and_friends.py": 5.627, "./service/pos/tests/test_charge_call4payment_inactive_variant.py": 2.631, "./service/pos/tests/test_commissions.py": 17.061999999999998, "./service/pos/tests/test_customer_info_serializer.py": 0.245, "./service/pos/tests/test_customer_transaction.py": 23.310999999999996, "./service/pos/tests/test_customer_transaction_actions.py": 22.693, "./service/pos/tests/test_mass_commision_rate_details_handler.py": 9.703, "./service/pos/tests/test_mass_resource_commision_rate_details_handler.py": 10.435, "./service/pos/tests/test_payment_methods.py": 4.83, "./service/pos/tests/test_registers.py": 1.612, "./service/pos/tests/test_search_items.py": 11.864, "./service/pos/tests/test_transaction_commission_staffer_id.py": 16.758000000000003, "./service/pos/tests/test_transaction_details_for_customer.py": 0.296, "./service/printer_api/tests/test_login.py": 11.222000*********, "./service/printer_api/tests/test_printer_api.py": 9.757000*********, "./service/printer_api/tests/test_printer_config.py": 2.269, "./service/printer_api/tests/test_printout.py": 17.233999999999998, "./service/profile_completeness/tests/test_serializer.py": 0.33399999999999996, "./service/profile_completeness/tests/test_step_message_blast.py": 1.035, "./service/profile_completeness/tests/test_steps_invite_customer.py": 6.851999999999999, "./service/profile_completeness/tests/test_steps_photo.py": 1.911, "./service/purchase/tests/test_apple.py": 2.162, "./service/purchase/tests/test_braintree.py": 0.01, "./service/purchase/tests/test_google.py": 2.2270000000000003, "./service/purchase/tests/test_sms_cost.py": 3.032, "./service/purchase/tests/test_subscription.py": 19.342999999999996, "./service/r_and_d/tests/test_pattern_check.py": 3.755, "./service/renting_venue/tests/test_change_renting_venue_details_request.py": 0.11699999999999999, "./service/schedule/tests/test_working_hours.py": 38.710999965667725, "./service/search/tests/test_business.py": 0.23399999999999999, "./service/search/tests/test_businesses.py": 27.159, "./service/search/tests/test_businesses_filtering.py": 11.841999999999999, "./service/search/tests/test_faceting.py": 8.81, "./service/search/tests/test_region.py": 2.3289999999999997, "./service/search/tests/test_resolve_geocoder.py": 0.09, "./service/search/tests/test_search_engine.py": 2.594, "./service/search/tests/test_search_experiment.py": 12.082999999999998, "./service/search/tests/test_search_serializers.py": 0.064, "./service/search/tests/test_street_hints_handler.py": 14.007000*********, "./service/search/tests/test_venue.py": 2.492, "./service/search/tests/test_venue_resolver_handler.py": 2.007, "./service/segment/tests/test_business_counters.py": 0.76, "./service/sequencing_number/tests/test_business_document_handler.py": 1.6480000*********, "./service/sequencing_number/tests/test_business_document_settings_handler.py": 4.****************, "./service/sequencing_number/tests/test_business_documents_list_handler.py": 1.518, "./service/siwa/tests/test_sign_in_with_apple.py": 70.**************, "./service/tests/test_account.py": 41.**************, "./service/tests/test_analytics_tokens_mixin.py": 0.044, "./service/tests/test_appsflyer_mixin.py": 0.449, "./service/tests/test_business_ip_whitelisting.py": 4.203, "./service/tests/test_customer_resource_rich_handler.py": 0.812, "./service/tests/test_customer_update.py": 2.965, "./service/tests/test_dry_run_mixin.py": 40.92400*********, "./service/tests/test_facebook_connect.py": 14.072, "./service/tests/test_get_language_from_header.py": 0.2590000*********, "./service/tests/test_homepage_handler.py": 0.783, "./service/tests/test_ip_address_split.py": 0.018000000000000002, "./service/tests/test_password_reset.py": 5.241, "./service/tests/test_requests_rate_limit.py": 5.849, "./service/tests/test_resources.py": 90.885, "./service/tests/test_resources_photos.py": 3.078, "./service/tests/test_resources_reordering.py": 0.759, "./service/tests/test_service_photo.py": 4.084, "./service/tests/test_service_type_marketing_mixin.py": 1.****************, "./service/tests/test_set_admin_permissions.py": 0.7619999999999999, "./service/tests/test_tools.py": 9.286999999999997, "./service/tests/test_unsubscribe.py": 0.863, "./service/voucher/tests/test_business_voucher_additional_info.py": 0.444, "./service/voucher/tests/test_customer_voucher_additional_info.py": 0.109, "./service/voucher/tests/test_orders_business.py": 4.279999999999999, "./service/voucher/tests/test_orders_customer.py": 7.249, "./service/voucher/tests/test_void_voucher.py": 0.328, "./service/voucher/tests/test_voucher.py": 12.201, "./service/voucher/tests/test_voucher_customer.py": 5.008, "./service/voucher/tests/test_voucher_order_detail.py": 0.518, "./service/voucher/tests/test_voucher_orders_finalize.py": 2.121, "./service/voucher/tests/test_voucher_simple_redeem.py": 8.052999999999999, "./service/voucher/tests/test_voucher_template.py": 29.343999999999998, "./service/warehouse/tests/test_barcode_details.py": 3.6959999999999997, "./service/warehouse/tests/test_barcode_generator.py": 8.742999999999999, "./service/warehouse/tests/test_barcodes_list.py": 1.475, "./service/warehouse/tests/test_brand_details.py": 4.784, "./service/warehouse/tests/test_brand_list.py": 2.412, "./service/warehouse/tests/test_categories_tree.py": 11.901000000000002, "./service/warehouse/tests/test_category_details.py": 5.929, "./service/warehouse/tests/test_category_list.py": 6.582, "./service/warehouse/tests/test_commodities_archive.py": 3.35, "./service/warehouse/tests/test_commodities_delete.py": 8.066, "./service/warehouse/tests/test_commodity_consumption.py": 5.087, "./service/warehouse/tests/test_commodity_details.py": 15.708999999999998, "./service/warehouse/tests/test_commodity_history.py": 5.154999999999999, "./service/warehouse/tests/test_commodity_list.py": 21.759999999999998, "./service/warehouse/tests/test_commodity_order.py": 0.811, "./service/warehouse/tests/test_commodity_search.py": 5.292999999999999, "./service/warehouse/tests/test_document.py": 29.61, "./service/warehouse/tests/test_formula.py": 8.572000*********, "./service/warehouse/tests/test_internal_expenditure_reasons.py": 0.764, "./service/warehouse/tests/test_quick_order.py": 3.5780000000000003, "./service/warehouse/tests/test_stock_level_history.py": 2.321, "./service/warehouse/tests/test_supplier.py": 4.868, "./service/warehouse/tests/test_utils.py": 2.6550000000000002, "./service/warehouse/tests/test_volume_measure_list.py": 2.339, "./service/warehouse/tests/test_warehouse_comodities.py": 2.517, "./service/warehouse/tests/test_warehouse_details.py": 6.823, "./service/warehouse/tests/test_warehouse_list.py": 2.469, "./settings/tests/test_performance_test_flag.py": 0.016, "./settings/tests/test_sms.py": 0.229, "./settings/tests/test_yaml_admin.py": 0.019, "./tests/test_wsgi_proxy.py": 0.041, "./tests/tests_rwg/test_fields.py": 0.020999999999999998, "./tests/tests_scripts/test__fix_adyen_ever_passed_kyc.py": 3.607, "./tests/tests_scripts/test_add_survey_account_deletion__all.py": 0.032, "./tests/tests_scripts/test_add_survey_choice_reason_duplicate_accounts__all.py": 0.042, "./tests/tests_scripts/test_add_tax_regions_tax_rates__fr.py": 0.151, "./tests/tests_scripts/test_auth_initial_migration_of_sessions.py": 0.245, "./tests/tests_scripts/test_delete_bats_duplicates__us.py": 0.981, "./tests/tests_scripts/test_delete_merchants__fr.py": 0.024, "./tests/tests_scripts/test_fill_basket_customer_card_id__all.py": 0.077, "./tests/tests_scripts/test_fix_tax_additional_data__us.py": 3.085, "./tests/tests_scripts/test_obfuscate_pg.py": 10.197, "./tests/tests_scripts/test_populate_invoice_business_id_field__all.py": 0.093, "./tests/tests_scripts/test_resync_merchants__fr.py": 0.024, "./tests/tests_scripts/test_script_add_billing_config_from_defaults__all.py": 0.015, "./tests/tests_scripts/test_script_add_registration_polls__all.py": 0.8650000*********, "./tests/tests_scripts/test_script_add_subscription_editors.py": 0.07300000*********, "./tests/tests_scripts/test_script_change_boost_offline_invoicing_target__pl.py": 0.9110000*********, "./tests/tests_scripts/test_script_clear_merchant_sync_fields__us.py": 0.028, "./tests/tests_scripts/test_script_create_boost_settings__all.py": 2.132, "./tests/tests_scripts/test_script_create_businesses__us.py": 1.967, "./tests/tests_scripts/test_script_delete_empty_image_objects.py": 0.076, "./tests/tests_scripts/test_script_delete_staffer_service_resources__all.py": 0.249, "./tests/tests_scripts/test_script_fill_gift_card_generic_images__all.py": 0.031, "./tests/tests_scripts/test_script_fill_gross_prices_billing_subscriptions__all.py": 0.31999999999999995, "./tests/tests_scripts/test_script_mark_old_boost_clients_as_recurring__br_gb_pl_us_za.py": 0.69, "./tests/tests_scripts/test_script_mark_omitted_clients_as_recurring.py": 2.568, "./tests/tests_scripts/test_script_message_blast_template_null_automated_status__all.py": 0.205, "./tests/tests_scripts/test_script_pos_clear_footer_line_2__all.py": 0.195, "./tests/tests_scripts/test_script_regenerate_deeplinks__all.py": 0.27, "./tests/tests_scripts/test_script_reindex_bci_by_blocked_phone__all.py": 0.****************, "./tests/tests_scripts/test_script_remove_duplicate_invoices__pl.py": 0.132, "./tests/tests_scripts/test_script_remove_online_invoices__fr.py": 2.827, "./tests/tests_scripts/test_script_remove_personal_data_of_bnp_users__pl.py": 0.948, "./tests/tests_scripts/test_script_remove_pl_prefix_from_tax_ids__pl.py": 0.05, "./tests/tests_scripts/test_script_remove_saas_online_invoices__us.py": 0.057, "./tests/tests_scripts/test_script_remove_unused_square_payment_type__us_gb.py": 0.385, "./tests/tests_scripts/test_script_set_visible_from_in_business__all.py": 0.455, "./tests/tests_scripts/test_script_structure_add_chiswick_region__gb.py": 0.02, "./tests/tests_scripts/test_update_treatment_braids__gb_fr.py": 0.083, "./webapps/admin_extra/enterprise_data/tests/test_common.py": 0.01, "./webapps/admin_extra/enterprise_data/tests/test_parser_resource_update.py": 0.2880000*********, "./webapps/admin_extra/enterprise_data/tests/test_parser_service_mapping_update.py": 0.094, "./webapps/admin_extra/enterprise_data/tests/test_parser_service_update.py": 0.13299999999999998, "./webapps/admin_extra/enterprise_data/tests/test_updater_business_logos.py": 0.616, "./webapps/admin_extra/enterprise_data/tests/test_updater_business_visibility.py": 4.793, "./webapps/admin_extra/enterprise_data/tests/test_updater_calendar_visibility.py": 3.6520000000000006, "./webapps/admin_extra/enterprise_data/tests/test_updater_opening_hours.py": 0.47, "./webapps/admin_extra/enterprise_data/tests/test_updater_resources.py": 5.069999999999996, "./webapps/admin_extra/enterprise_data/tests/test_updater_security_settings.py": 0.607, "./webapps/admin_extra/enterprise_data/tests/test_updater_services.py": 2.47, "./webapps/admin_extra/enterprise_data/tests/test_updater_utils.py": 0.019999999999999997, "./webapps/admin_extra/enterprise_data/tests/test_uploader_image_bundles.py": 0.841, "./webapps/admin_extra/management/commands/test_db_export.py": 0.01, "./webapps/admin_extra/tasks/tests/test_change_booking.py": 0.5329999999999999, "./webapps/admin_extra/tasks/tests/test_dry_offline_invoicing.py": 0.846, "./webapps/admin_extra/tasks/tests/test_images_import.py": 0.136, "./webapps/admin_extra/tasks/tests/test_mail_pdf_sender.py": 0.064, "./webapps/admin_extra/tasks/tests/test_mass_billing_business_discounts.py": 2.477, "./webapps/admin_extra/tasks/tests/test_mass_billing_business_offer.py": 2.6260000000000003, "./webapps/admin_extra/tasks/tests/test_mass_billing_merchants_switcher.py": 1.1**********00003, "./webapps/admin_extra/tasks/tests/test_mass_billing_offer_purchase.py": 2.1189999999999998, "./webapps/admin_extra/tasks/tests/test_mass_billing_offers_changer.py": 2.367, "./webapps/admin_extra/tasks/tests/test_mass_billing_purchase_flow_changer.py": 1.2029999999999996, "./webapps/admin_extra/tasks/tests/test_mass_business_retrial_switch.py": 2.076, "./webapps/admin_extra/tasks/tests/test_mass_offline_to_billing_switch.py": 9.559999999999999, "./webapps/admin_extra/tasks/tests/test_mass_revert_business_from_invalid.py": 1.2550000*********, "./webapps/admin_extra/tasks/tests/test_mass_sms_price_and_limit_changer.py": 0.8700000*********, "./webapps/admin_extra/tasks/tests/test_mass_switch_merchants_payment_processor_task.py": 0.41900000000000004, "./webapps/admin_extra/tasks/tests/test_old_subscription_products.py": 0.471, "./webapps/admin_extra/tasks/tests/test_report_inappropiate_content.py": 0.246, "./webapps/admin_extra/tasks/tests/test_subscription_buyer_import.py": 1.309, "./webapps/admin_extra/tasks/tests/test_subscription_buyer_update_task.py": 3.4869999999999988, "./webapps/admin_extra/tasks/tests/test_switch_merchants_to_billing.py": 6.258000000000002, "./webapps/admin_extra/tasks/tests/test_update_padding_time_task.py": 0.187, "./webapps/admin_extra/tasks/tests/test_versum_import.py": 7.8149999999999995, "./webapps/admin_extra/tasks/tests/test_zip_codes_to_areas_task.py": 0.021, "./webapps/admin_extra/tests/payment_providers/test_stripe_transfer_funds.py": 0.545, "./webapps/admin_extra/tests/test_admin_settings.py": 1.182, "./webapps/admin_extra/tests/test_admin_tools_view.py": 0.101, "./webapps/admin_extra/tests/test_adyen_report.py": 3.8969999999999994, "./webapps/admin_extra/tests/test_adyen_views.py": 0.078, "./webapps/admin_extra/tests/test_b_listing_import_view.py": 1.872, "./webapps/admin_extra/tests/test_blisting.py": 0.897, "./webapps/admin_extra/tests/test_booking_remove.py": 0.377, "./webapps/admin_extra/tests/test_boost_mass_tools.py": 12.505, "./webapps/admin_extra/tests/test_business.py": 2.02, "./webapps/admin_extra/tests/test_business_status_changer.py": 1.832, "./webapps/admin_extra/tests/test_businesses_work_schedule.py": 2.8970000000000002, "./webapps/admin_extra/tests/test_buyer_mx_tax_id_change_view.py": 2.2***************, "./webapps/admin_extra/tests/test_celery_task_status.py": 0.521, "./webapps/admin_extra/tests/test_check_constraints_command.py": 10.477, "./webapps/admin_extra/tests/test_claim_pending_report.py": 0.018000000000000002, "./webapps/admin_extra/tests/test_custom_push_send_view.py": 16.504, "./webapps/admin_extra/tests/test_customer_import.py": 0.349, "./webapps/admin_extra/tests/test_delete_imported_reviews.py": 2.8300000000000005, "./webapps/admin_extra/tests/test_feature_flags_view.py": 0.089, "./webapps/admin_extra/tests/test_fiscal_archive_signature_verification_tool.py": 0.7210000000000002, "./webapps/admin_extra/tests/test_forms.py": 3.549999999999999, "./webapps/admin_extra/tests/test_frontdesk_access.py": 4.066, "./webapps/admin_extra/tests/test_generate_dl.py": 2.****************, "./webapps/admin_extra/tests/test_generate_staffers.py": 1.81, "./webapps/admin_extra/tests/test_google_calendar_importer.py": 0.842, "./webapps/admin_extra/tests/test_group_permission_mixin.py": 0.232, "./webapps/admin_extra/tests/test_import_file.py": 2.****************, "./webapps/admin_extra/tests/test_import_tools.py": 0.033, "./webapps/admin_extra/tests/test_login.py": 1.133, "./webapps/admin_extra/tests/test_new_bank_importer.py": 38.73800*********4, "./webapps/admin_extra/tests/test_offline_invoicing_for_migrations.py": 0.****************, "./webapps/admin_extra/tests/test_offline_invoicing_view.py": 0.317, "./webapps/admin_extra/tests/test_public_partners_businesspartnerdata_tool.py": 2.444, "./webapps/admin_extra/tests/test_push_sender.py": 0.5700000*********, "./webapps/admin_extra/tests/test_quick_reports.py": 2.287, "./webapps/admin_extra/tests/test_requests_rate_limit.py": 0.01, "./webapps/admin_extra/tests/test_retention_report.py": 3.****************, "./webapps/admin_extra/tests/test_review_import_view.py": 0.436, "./webapps/admin_extra/tests/test_styleseat_import.py": 4.574, "./webapps/admin_extra/tests/test_subdomains.py": 3.344, "./webapps/admin_extra/tests/test_subscription_batch_edit.py": 42.***************, "./webapps/admin_extra/tests/test_subscription_import.py": 4.***************, "./webapps/admin_extra/tests/test_time_span.py": 2.437, "./webapps/admin_extra/tests/test_trial_end_changer.py": 0.8940000*********, "./webapps/admin_extra/tests/test_trial_end_report.py": 1.565, "./webapps/admin_extra/tests/test_urls.py": 2.946, "./webapps/admin_extra/tests/test_user.py": 1.658, "./webapps/admin_extra/tests/test_vagaro_import.py": 0.259, "./webapps/admin_extra/tests/test_versum_loader.py": 0.27, "./webapps/admin_extra/tests/test_warehouse_commodities_import.py": 3.298, "./webapps/admin_extra/tests/test_wholesaler_commodities_import.py": 0.953, "./webapps/adyen/tests/test_adyen_requests.py": 0.6459999999999999, "./webapps/adyen/tests/test_auth.py": 0.504, "./webapps/adyen/tests/test_capture.py": 0.211, "./webapps/adyen/tests/test_details.py": 0.1****************, "./webapps/adyen/tests/test_disable.py": 0.129, "./webapps/adyen/tests/test_first_auth_ee.py": 0.149, "./webapps/adyen/tests/test_float_amount_to_cents.py": 0.056999999999999995, "./webapps/adyen/tests/test_get_reference.py": 2.939, "./webapps/adyen/tests/test_logging.py": 0.019, "./webapps/adyen/tests/test_make_request.py": 0.14200000000000002, "./webapps/adyen/tests/test_models.py": 0.07899999999999999, "./webapps/adyen/tests/test_notifications.py": 0.401, "./webapps/adyen/tests/test_online.py": 0.01, "./webapps/adyen/tests/test_oper_result.py": 0.016, "./webapps/adyen/tests/test_refund.py": 0.2240000*********, "./webapps/adyen/tests/test_refund_conn_err_oper.py": 0.03, "./webapps/adyen/tests/test_reports.py": 0.123, "./webapps/adyen/tests/test_service.py": 0.044, "./webapps/adyen/tests/test_tasks.py": 0.107, "./webapps/adyen/tests/test_three_d_secure_1_flows.py": 1.155, "./webapps/allauth_google/tests/test_custom_account_adapter.py": 0.061, "./webapps/b2b_referral/tests/test_b2b_referral_details.py": 0.064, "./webapps/b2b_referral/tests/test_b2breferral.py": 1.****************, "./webapps/b2b_referral/tests/test_b2breferralcode.py": 0.052, "./webapps/b2b_referral/tests/test_b2breferraldeeplink.py": 3.421, "./webapps/b2b_referral/tests/test_b2breferralreward.py": 10.155, "./webapps/b2b_referral/tests/test_b2breferralsetting.py": 0.362, "./webapps/b2b_referral/tests/test_notifications.py": 3.521, "./webapps/best_of_booksy/tests/test_notifications.py": 0.108, "./webapps/best_of_booksy/tests/test_tasks.py": 0.51, "./webapps/billing/tests/admin/test_base.py": 0.****************, "./webapps/billing/tests/admin/test_business.py": 47.**************, "./webapps/billing/tests/admin/test_config.py": 0.082, "./webapps/billing/tests/admin/test_cycle.py": 1.862, "./webapps/billing/tests/admin/test_discount.py": 45.**************, "./webapps/billing/tests/admin/test_integration.py": 1.221, "./webapps/billing/tests/admin/test_offer.py": 5.748, "./webapps/billing/tests/admin/test_offline_migration.py": 9.634, "./webapps/billing/tests/admin/test_one_off_charge.py": 0.694, "./webapps/billing/tests/admin/test_payment.py": 7.035000*********, "./webapps/billing/tests/admin/test_product.py": 4.048, "./webapps/billing/tests/admin/test_refund.py": 7.255, "./webapps/billing/tests/admin/test_subscription.py": 15.474000000000007, "./webapps/billing/tests/apis/test_boost.py": 0.015, "./webapps/billing/tests/interfaces/test_stripe.py": 0.298, "./webapps/billing/tests/messages/test_subscription.py": 0.527, "./webapps/billing/tests/messages/test_transactions.py": 1.104, "./webapps/billing/tests/models/test_billing_history.py": 0.251, "./webapps/billing/tests/models/test_boost.py": 1.0170000*********, "./webapps/billing/tests/models/test_business_settings.py": 0.2710000*********, "./webapps/billing/tests/models/test_discount_codes.py": 41.09999999999996, "./webapps/billing/tests/models/test_integration.py": 0.304, "./webapps/billing/tests/models/test_one_off_charge.py": 0.21899999999999997, "./webapps/billing/tests/models/test_product_offer.py": 1.327, "./webapps/billing/tests/models/test_refund.py": 0.178, "./webapps/billing/tests/models/test_subscribed_product.py": 3.08, "./webapps/billing/tests/models/test_subscription.py": 7.206999999999999, "./webapps/billing/tests/serializers/boost/test_retry_charge.py": 0.706, "./webapps/billing/tests/services/test_boost.py": 3.4810000000000003, "./webapps/billing/tests/services/test_charge.py": 2.6969999999999996, "./webapps/billing/tests/services/test_dispute.py": 0.192, "./webapps/billing/tests/services/test_external_customer.py": 0.27699999999999997, "./webapps/billing/tests/services/test_migrated_subscription.py": 0.257, "./webapps/billing/tests/services/test_payment_method.py": 1.2700000000000005, "./webapps/billing/tests/services/test_refund.py": 0.37299999999999994, "./webapps/billing/tests/tasks/test_auto_switch_billing_payment_processor_for_inactive_businesses.py": 0.902, "./webapps/billing/tests/templatetags/test_billing_admin_tags.py": 1.099, "./webapps/billing/tests/test_actions.py": 1.1160000000000003, "./webapps/billing/tests/test_billing_cycle_switch.py": 31.868000000000006, "./webapps/billing/tests/test_boost_tasks.py": 1.3940000*********, "./webapps/billing/tests/test_business_discounts.py": 6.5699999999999985, "./webapps/billing/tests/test_business_status.py": 5.296, "./webapps/billing/tests/test_cache.py": 0.249, "./webapps/billing/tests/test_churn.py": 4.119, "./webapps/billing/tests/test_close_subscription.py": 2.3829999999999996, "./webapps/billing/tests/test_discount_codes.py": 1.958, "./webapps/billing/tests/test_long_subscription.py": 1.927, "./webapps/billing/tests/test_notifications.py": 0.978, "./webapps/billing/tests/test_one_off_charge.py": 4.187, "./webapps/billing/tests/test_payment_processor.py": 35.63700*********, "./webapps/billing/tests/test_products_management.py": 1.526, "./webapps/billing/tests/test_reports.py": 0.281, "./webapps/billing/tests/test_retry_charge.py": 2.8129999999999997, "./webapps/billing/tests/test_serializers.py": 57.75199999999999, "./webapps/billing/tests/test_stripe_tasks.py": 4.441, "./webapps/billing/tests/test_subscription_creator.py": 40.948, "./webapps/billing/tests/test_subscription_overdue.py": 1.032, "./webapps/billing/tests/test_tasks.py": 1.0670000000000002, "./webapps/billing/tests/test_utils.py": 0.3320000*********, "./webapps/billing/tests/views/test_boost_next_payment.py": 0.5589999999999999, "./webapps/billing/tests/views/test_boost_overdue.py": 2.101, "./webapps/billing/tests/views/test_churn.py": 1.1949999999999998, "./webapps/billing/tests/views/test_config.py": 0.17900000000000002, "./webapps/billing/tests/views/test_next_payment.py": 1.96, "./webapps/billing/tests/views/test_offer.py": 4.774, "./webapps/billing/tests/views/test_offline_migration.py": 0.261, "./webapps/billing/tests/views/test_payment_method.py": 2.7879999999999994, "./webapps/billing/tests/views/test_permissions.py": 0.088, "./webapps/billing/tests/views/test_subscription_details.py": 2.4259999999999997, "./webapps/billing/tests/views/test_subscription_purchase.py": 3.685, "./webapps/billing/tests/views/test_subscription_retry_charge.py": 2.3759999999999994, "./webapps/billing/tests/views/test_task_status.py": 0.859, "./webapps/billing/tests/views/test_total_overdue.py": 1.****************, "./webapps/booking/actions/tests/test_appointment_modified.py": 0.022, "./webapps/booking/calendar_importer/tests/test_import_appointment.py": 3.1200000000000006, "./webapps/booking/calendar_importer/tests/test_notifications.py": 1.188, "./webapps/booking/calendar_importer/tests/test_pubsub.py": 0.11499999999999998, "./webapps/booking/calendar_importer/tests/test_subscribers.py": 1.98, "./webapps/booking/elasticsearch/tests/test_appointment.py": 1.18, "./webapps/booking/factory/tests/test_subbookings.py": 1.2389999999999999, "./webapps/booking/notifications/tests/test_base.py": 0.109, "./webapps/booking/notifications/tests/test_contexts.py": 0.117, "./webapps/booking/notifications/tests/test_first_customer_appointment_finished_notification.py": 0.27699999999999997, "./webapps/booking/notifications/tests/test_notifications_receivers.py": 0.229, "./webapps/booking/tests/book_again_v2/test_book_again_v2.py": 0.562, "./webapps/booking/tests/book_again_v2/test_data_retriever.py": 6.036, "./webapps/booking/tests/messages/test_appointment.py": 0.578, "./webapps/booking/tests/messages/test_appointment_history.py": 0.171, "./webapps/booking/tests/test_adapters.py": 0.085, "./webapps/booking/tests/test_add_coordinates_to_traveling_appointments.py": 0.028999999999999998, "./webapps/booking/tests/test_admin_appointment.py": 6.0680000000000005, "./webapps/booking/tests/test_appointment_action.py": 9.173, "./webapps/booking/tests/test_appointment_base.py": 0.01, "./webapps/booking/tests/test_appointment_by_business.py": 54.42199999999998, "./webapps/booking/tests/test_appointment_by_customer.py": 25.137000000000004, "./webapps/booking/tests/test_appointment_checkout.py": 0.18200000000000008, "./webapps/booking/tests/test_appointment_checkout_factory.py": 1.014, "./webapps/booking/tests/test_appointment_consent_forms.py": 16.581000000000003, "./webapps/booking/tests/test_appointment_family_and_friends.py": 8.805000*********, "./webapps/booking/tests/test_appointment_matcher.py": 0.322, "./webapps/booking/tests/test_appointment_queryset.py": 0.6140000*********, "./webapps/booking/tests/test_appointment_timezone.py": 0.31, "./webapps/booking/tests/test_appointment_total_discount.py": 1.182, "./webapps/booking/tests/test_appointment_wrapper.py": 4.008, "./webapps/booking/tests/test_autoassign.py": 1.65, "./webapps/booking/tests/test_book_again.py": 2.587, "./webapps/booking/tests/test_book_again_serializer.py": 4.161, "./webapps/booking/tests/test_booking_box.py": 6.065, "./webapps/booking/tests/test_booking_is_first_cb_for_business.py": 0.505, "./webapps/booking/tests/test_booking_is_first_crossing_and_first_crossing.py": 40.28700*********, "./webapps/booking/tests/test_bookingchange.py": 0.7799999999999999, "./webapps/booking/tests/test_bulk_finish_bookings.py": 0.296, "./webapps/booking/tests/test_business_in_customer_appointment_context_serializer.py": 0.086, "./webapps/booking/tests/test_business_in_customer_booking_serializer.py": 0.137, "./webapps/booking/tests/test_business_multibooking_appointment.py": 0.581, "./webapps/booking/tests/test_client_discount_promotion.py": 6.809, "./webapps/booking/tests/test_customer_for_calendar.py": 3.0460000000000007, "./webapps/booking/tests/test_customer_multibooking_appointment.py": 3.7600000000000002, "./webapps/booking/tests/test_dashboard_order_bug.py": 1.756, "./webapps/booking/tests/test_drag_booking_serializer.py": 3.9219999999999993, "./webapps/booking/tests/test_empty_booked_for.py": 1.056, "./webapps/booking/tests/test_exceeded_payment_deadline_task.py": 0.895, "./webapps/booking/tests/test_localtime_filter.py": 0.201, "./webapps/booking/tests/test_match_subbookings.py": 0.442, "./webapps/booking/tests/test_notifications.py": 2.1130000000000004, "./webapps/booking/tests/test_repeating_appointment.py": 50.445, "./webapps/booking/tests/test_repeating_booking_series.py": 18.002, "./webapps/booking/tests/test_repeating_tools.py": 0.351, "./webapps/booking/tests/test_schedule_bulk_update_appointments.py": 10.847, "./webapps/booking/tests/test_service_addons.py": 2.129, "./webapps/booking/tests/test_service_data.py": 1.2380000000000002, "./webapps/booking/tests/test_service_questions.py": 2.8849999999999993, "./webapps/booking/tests/test_service_variant_loader.py": 0.142, "./webapps/booking/tests/test_slots_lookups.py": 2.094, "./webapps/booking/tests/test_subbooking_queryset.py": 0.254, "./webapps/booking/tests/test_time_slots.py": 4.475999999999999, "./webapps/booking/tests/test_time_slots_for_current_day.py": 2.261, "./webapps/booking/tests/test_traveling.py": 1.103, "./webapps/booking/tests/test_update_default_promotion.py": 2.161, "./webapps/booking/tests/test_update_first_bookings.py": 0.358, "./webapps/booking/tests/test_update_repeating_booking.py": 65.136, "./webapps/booking/tests/test_update_txn_charge_date.py": 0.482, "./webapps/booking/tests/views/test_book_again.py": 0.862, "./webapps/booking/timeslots/v1/tests/test_core.py": 0.*****************, "./webapps/booking/timeslots/v1/tests/test_data_sources.py": 0.30699999999999994, "./webapps/booking/timeslots/v1/tests/test_drawer.py": 35.070999999999984, "./webapps/booking/timeslots/v1/tests/test_functions.py": 0.037000000000000005, "./webapps/booking/timeslots/v1/tests/test_generate_slots.py": 38.35699999999999, "./webapps/booking/timeslots/v1/tests/test_matcher.py": 2.136, "./webapps/booking/timeslots/v1/tests/test_matcher_availability.py": 0.513, "./webapps/booking/timeslots/v1/tests/test_promotions.py": 2.004, "./webapps/booking/timeslots/v1/tests/test_scope_factory.py": 0.096, "./webapps/booking/timeslots/v1/tests/test_serializers.py": 0.32500000000000007, "./webapps/booking/tools/tests/test_partner_forward_slots.py": 0.128, "./webapps/boost/tests/test_admin_actions.py": 6.428000*********, "./webapps/boost/tests/test_api_for_billing.py": 8.158999999999999, "./webapps/boost/tests/test_appointment_queryset.py": 3.533, "./webapps/boost/tests/test_baker_recipes.py": 0.23, "./webapps/boost/tests/test_boost_api.py": 9.749000000000004, "./webapps/boost/tests/test_boost_appointment.py": 5.975, "./webapps/boost/tests/test_boost_appointment_queryset.py": 11.758000000000003, "./webapps/boost/tests/test_boost_service.py": 0.863, "./webapps/boost/tests/test_boosted_business.py": 3.949, "./webapps/boost/tests/test_braintree.py": 0.805, "./webapps/boost/tests/test_combos_with_boost.py": 2.5140000000000002, "./webapps/boost/tests/test_events.py": 5.723999999999999, "./webapps/boost/tests/test_gross_value.py": 0.927, "./webapps/boost/tests/test_no_show_revenue_leak_fix.py": 14.572, "./webapps/boost/tests/test_payment.py": 1.9369999999999998, "./webapps/boost/tests/test_rows.py": 3.7279999999999998, "./webapps/boost/tests/test_serializers.py": 0.647, "./webapps/boost/tests/test_storage.py": 0.012, "./webapps/boost/tests/test_stripe.py": 3.4679999999999995, "./webapps/boost/tests/test_test_utils.py": 4.352, "./webapps/boost/tests/test_update_amount.py": 1.1440000*********, "./webapps/boost/tests/test_waiver_of_withdrawal.py": 2.125, "./webapps/braintree_app/tests/test_admin.py": 2.586, "./webapps/braintree_app/tests/test_models.py": 0.213, "./webapps/braintree_app/tests/test_payment_processor.py": 0.6440000*********, "./webapps/braintree_app/tests/test_serializers.py": 2.7119999999999997, "./webapps/business/business_categories/tests/test_assign_treatment_to_service.py": 0.399, "./webapps/business/business_categories/tests/test_business_category.py": 1.0170000000000003, "./webapps/business/business_categories/tests/test_business_category_importer_for_test_environments.py": 0.7929999999999999, "./webapps/business/business_categories/tests/test_business_category_manager.py": 0.*****************, "./webapps/business/business_categories/tests/test_cache.py": 0.11199999999999999, "./webapps/business/business_categories/tests/test_helpers.py": 3.478, "./webapps/business/business_categories/tests/test_images.py": 0.065, "./webapps/business/business_categories/tests/test_merger.py": 0.593, "./webapps/business/business_categories/tests/test_modifier.py": 3.174, "./webapps/business/business_categories/tests/test_receivers.py": 0.195, "./webapps/business/business_categories/tests/test_service_suggestion.py": 0.385, "./webapps/business/business_categories/tests/test_service_suggestion_util.py": 0.19***************, "./webapps/business/business_categories/tests/test_slugs.py": 1.****************, "./webapps/business/business_categories/tests/test_treatment_assignment.py": 0.822, "./webapps/business/business_categories/tests/test_treatments.py": 2.****************, "./webapps/business/business_categories/tests/test_universal_treatment_tree.py": 0.894, "./webapps/business/business_visibility/tests/test_business_visibility_validations.py": 0.*****************, "./webapps/business/elasticsearch/tests/test_account.py": 1.411, "./webapps/business/elasticsearch/tests/test_bci_reindex.py": 2.33, "./webapps/business/elasticsearch/tests/test_business.py": 0.303, "./webapps/business/elasticsearch/tests/test_business_account_document.py": 0.121, "./webapps/business/elasticsearch/tests/test_business_availability_per_category.py": 1.****************, "./webapps/business/elasticsearch/tests/test_business_customer_document.py": 1.038, "./webapps/business/elasticsearch/tests/test_business_customer_family_and_friends.py": 3.****************, "./webapps/business/elasticsearch/tests/test_business_history_reindex.py": 0.167, "./webapps/business/elasticsearch/tests/test_business_partner.py": 0.177, "./webapps/business/elasticsearch/tests/test_business_reindex.py": 1.6580000*********, "./webapps/business/elasticsearch/tests/test_resources.py": 0.442, "./webapps/business/elasticsearch/tests/test_service_categories.py": 0.406, "./webapps/business/management/commands/tests/test_fill_m2m_created_field.py": 0.315, "./webapps/business/notifications/tests/test_after_registration_notifications.py": 0.84, "./webapps/business/notifications/tests/test_invitation_reminder.py": 1.****************, "./webapps/business/notifications/tests/test_iterable_webhook_notifications.py": 7.665, "./webapps/business/notifications/tests/test_marketing_consent_notification.py": 0.6800000000000002, "./webapps/business/notifications/tests/test_prompted_reviews.py": 0.7859999999999999, "./webapps/business/notifications/tests/test_services_notifications.py": 1.3039999999999998, "./webapps/business/notifications/tests/test_visibility_notifications.py": 0.585, "./webapps/business/searchables/serializers/tests/test_business_low_availability.py": 3.777, "./webapps/business/searchables/serializers/tests/test_family_and_friends.py": 1.252, "./webapps/business/searchables/serializers/tests/test_get_business_categories.py": 0.*****************, "./webapps/business/searchables/test_business_se_tuning.py": 2.****************, "./webapps/business/searchables/tests/test_business_completion_searchable.py": 1.***************, "./webapps/business/searchables/tests/test_es_business_account.py": 0.28, "./webapps/business/searchables/tests/test_es_business_availability_all.py": 5.172, "./webapps/business/searchables/tests/test_es_business_availability_implicit.py": 6.191, "./webapps/business/searchables/tests/test_es_business_category.py": 30.308, "./webapps/business/searchables/tests/test_es_business_category_weighted_suggester.py": 0.****************, "./webapps/business/searchables/tests/test_es_business_customer_bookings_staffers_searchable.py": 0.*****************, "./webapps/business/searchables/tests/test_es_business_customer_exists_searchable.py": 0.*****************, "./webapps/business/searchables/tests/test_es_business_customer_family_and_friends_searchable.py": 2.157, "./webapps/business/searchables/tests/test_es_business_customer_gdpr_searchable.py": 0.315, "./webapps/business/searchables/tests/test_es_business_customer_groups.py": 4.709, "./webapps/business/searchables/tests/test_es_business_customer_locked_fields.py": 2.543, "./webapps/business/searchables/tests/test_es_business_customer_search_highlight.py": 1.9050000000000002, "./webapps/business/searchables/tests/test_es_business_customer_searchable.py": 1.426, "./webapps/business/searchables/tests/test_es_business_customer_services_searchable.py": 0.139, "./webapps/business/searchables/tests/test_es_business_customer_tags_searchable.py": 0.371, "./webapps/business/searchables/tests/test_es_business_faceting.py": 2.594000*********, "./webapps/business/searchables/tests/test_es_business_inner_hits_searchable.py": 2.446, "./webapps/business/searchables/tests/test_es_business_location_and_category.py": 5.247999999999999, "./webapps/business/searchables/tests/test_es_business_location_saturation.py": 0.509, "./webapps/business/searchables/tests/test_es_business_location_traveling.py": 2.98, "./webapps/business/searchables/tests/test_es_business_location_v3.py": 3.634000*********, "./webapps/business/searchables/tests/test_es_business_manual_boost_score.py": 0.804, "./webapps/business/searchables/tests/test_es_business_mixed_match.py": 5.133000*********, "./webapps/business/searchables/tests/test_es_business_promotion_boost.py": 0.636, "./webapps/business/searchables/tests/test_es_business_query_partial_name_match_1.py": 5.177, "./webapps/business/searchables/tests/test_es_business_query_partial_name_match_2.py": 2.815, "./webapps/business/searchables/tests/test_es_business_region_location.py": 3.58, "./webapps/business/searchables/tests/test_es_business_reviews.py": 0.943, "./webapps/business/searchables/tests/test_es_business_searchable.py": 4.139999999999999, "./webapps/business/searchables/tests/test_es_business_searchable_location_widen.py": 7.5089999999999995, "./webapps/business/searchables/tests/test_es_business_secondary_category_searchable.py": 1.006, "./webapps/business/searchables/tests/test_es_business_service_searchable.py": 0.12, "./webapps/business/searchables/tests/test_es_business_treatment_searchable.py": 0.391, "./webapps/business/searchables/tests/test_es_business_widen_searchable.py": 3.3130000000000006, "./webapps/business/searchables/tests/test_es_service_type_searchable.py": 0.4310000*********, "./webapps/business/servicers/tests/test_business_details.py": 0.01, "./webapps/business/tests/business_customer/test_active_gift_cards_not_cleaned_on_update.py": 0.33, "./webapps/business/tests/business_customer/test_bci_history.py": 0.5160000*********, "./webapps/business/tests/business_customer/test_bci_import_with_tags.py": 0.436, "./webapps/business/tests/business_customer/test_bci_serializer.py": 2.3920000000000003, "./webapps/business/tests/business_customer/test_bci_update_preserve_tags.py": 0.27, "./webapps/business/tests/business_customer/test_business_invite_delay.py": 1.091, "./webapps/business/tests/business_customer/test_customer_data.py": 0.29800000000000004, "./webapps/business/tests/business_customer/test_customer_data_with_relation.py": 0.607, "./webapps/business/tests/business_customer/test_customer_reviews.py": 1.444, "./webapps/business/tests/business_customer/test_type_data.py": 0.3540000000000002, "./webapps/business/tests/grpc/test_loyalty_program_business_details.py": 0.01, "./webapps/business/tests/messages/test_business_customer_info_publisher.py": 1.8389999999999997, "./webapps/business/tests/messages/test_business_publisher.py": 0.652, "./webapps/business/tests/resources/test_resource_invite.py": 1.315, "./webapps/business/tests/serializers/test_automation.py": 1.167, "./webapps/business/tests/serializers/test_retrial.py": 0.4439999999999999, "./webapps/business/tests/test_assign_treatment_to_services.py": 1.113, "./webapps/business/tests/test_assign_treatment_to_services_serializer.py": 0.57, "./webapps/business/tests/test_attendance_list.py": 0.01, "./webapps/business/tests/test_b_listings.py": 14.849000000000002, "./webapps/business/tests/test_bci_from_contact.py": 3.176, "./webapps/business/tests/test_bci_get_or_create_for_user.py": 1.987, "./webapps/business/tests/test_bci_get_or_create_from_contact.py": 0.238, "./webapps/business/tests/test_bci_latest_bookings.py": 0.154, "./webapps/business/tests/test_bci_reindex_on_biz_activity_change.py": 0.874, "./webapps/business/tests/test_bci_zip_code.py": 0.16699999999999998, "./webapps/business/tests/test_business.py": 6.879999999999998, "./webapps/business/tests/test_business_admin.py": 41.11699999999999, "./webapps/business/tests/test_business_annotate_renting_venue_invitation.py": 0.489, "./webapps/business/tests/test_business_blocked_overdue_task.py": 2.231, "./webapps/business/tests/test_business_boost_switch.py": 0.513, "./webapps/business/tests/test_business_category_admin.py": 4.035, "./webapps/business/tests/test_business_change_recording.py": 0.352, "./webapps/business/tests/test_business_custom_data_widget.py": 0.045, "./webapps/business/tests/test_business_customer.py": 3.275, "./webapps/business/tests/test_business_elasticsearch_base.py": 0.01, "./webapps/business/tests/test_business_enums.py": 2.868, "./webapps/business/tests/test_business_fizjo_check_access.py": 0.424, "./webapps/business/tests/test_business_forms_table_create.py": 0.111, "./webapps/business/tests/test_business_get_can_use_frontdesk.py": 1.655, "./webapps/business/tests/test_business_get_mp_deeplink.py": 0.194, "./webapps/business/tests/test_business_go_live.py": 0.297, "./webapps/business/tests/test_business_onboarding_walkthrough_banner.py": 1.6320000*********, "./webapps/business/tests/test_business_physiotherapy.py": 0.28, "./webapps/business/tests/test_business_ports.py": 0.043, "./webapps/business/tests/test_business_promotion.py": 2.13, "./webapps/business/tests/test_business_salon_network.py": 0.841, "./webapps/business/tests/test_business_searchables.py": 13.824999999999996, "./webapps/business/tests/test_business_serializer.py": 69.23399999999998, "./webapps/business/tests/test_business_tools.py": 0.809, "./webapps/business/tests/test_business_top_services.py": 4.068, "./webapps/business/tests/test_business_trial_end_start.py": 0.387, "./webapps/business/tests/test_business_visible_delay.py": 1.0310000*********, "./webapps/business/tests/test_business_visible_serializer.py": 0.020999999999999998, "./webapps/business/tests/test_can_send_invite.py": 0.374, "./webapps/business/tests/test_claim_pending_report.py": 10.084999999999999, "./webapps/business/tests/test_coordinate_serializer.py": 0.019, "./webapps/business/tests/test_customer_invite.py": 1.456, "./webapps/business/tests/test_enable_sms_booking_confirmations_for_new_business.py": 0.116, "./webapps/business/tests/test_exclude_business_from_analysis.py": 0.179, "./webapps/business/tests/test_feature_statuses.py": 3.1799999999999997, "./webapps/business/tests/test_focus_on_query_view.py": 10.703999999999999, "./webapps/business/tests/test_history_retriever.py": 0.314, "./webapps/business/tests/test_location_serializer.py": 0.059, "./webapps/business/tests/test_messages_business.py": 10.258, "./webapps/business/tests/test_modern_query_hints_view.py": 17.283999999999995, "./webapps/business/tests/test_notification_target.py": 0.105, "./webapps/business/tests/test_omnibus_price.py": 2.0469999999999993, "./webapps/business/tests/test_online_booking_integrations.py": 0.111, "./webapps/business/tests/test_renting_venue.py": 10.975999999999999, "./webapps/business/tests/test_resource_admin.py": 0.5630000*********, "./webapps/business/tests/test_resource_unique_together.py": 0.191, "./webapps/business/tests/test_retrial.py": 0.7679999999999999, "./webapps/business/tests/test_segment_signals.py": 0.18, "./webapps/business/tests/test_service_category.py": 0.134, "./webapps/business/tests/test_service_post_save.py": 2.008, "./webapps/business/tests/test_service_price.py": 29.970000000000002, "./webapps/business/tests/test_service_promotion_slots.py": 0.18400000000000002, "./webapps/business/tests/test_service_promotions_CD_FS_HH_LM.py": 78.05599999999998, "./webapps/business/tests/test_service_promotions_listing.py": 0.037, "./webapps/business/tests/test_service_queryset.py": 1.013, "./webapps/business/tests/test_service_resources.py": 4.571, "./webapps/business/tests/test_service_serializer.py": 12.751, "./webapps/business/tests/test_service_type_marketing.py": 0.208, "./webapps/business/tests/test_service_type_marketing_seen_view.py": 0.304, "./webapps/business/tests/test_service_types_view.py": 1.067, "./webapps/business/tests/test_service_usage.py": 1.036, "./webapps/business/tests/test_service_variant.py": 0.6399999999999999, "./webapps/business/tests/test_service_variant_payments.py": 0.6880000*********, "./webapps/business/tests/test_service_variant_queryset.py": 0.7090000*********, "./webapps/business/tests/test_single_review_feedback_status.py": 0.953, "./webapps/business/tests/test_suggest_service_types_view.py": 0.694, "./webapps/business/tests/test_tags.py": 0.519, "./webapps/business/tests/test_task_match_users.py": 2.5879999999999996, "./webapps/business/tests/test_tasks.py": 0.99, "./webapps/business/tests/test_test_business_marking.py": 0.4760000*********, "./webapps/business/tests/test_trusted_clients.py": 2.6090000000000004, "./webapps/business/tests/test_update_save.py": 0.373, "./webapps/business/tests/test_utils.py": 1.9529999999999998, "./webapps/business/tests/test_validators.py": 0.034, "./webapps/business/tests/views/test_automation.py": 0.8240000*********, "./webapps/business/tests/views/test_booking_available.py": 1.479, "./webapps/business/tests/views/test_customer_service_details.py": 2.481, "./webapps/business/tests/views/test_retrial.py": 0.659, "./webapps/business_calendar/serializers/tests/test_calendar_change.py": 0.42700000000000005, "./webapps/business_calendar/utils/tests/test_changed_dates.py": 0.32499999999999996, "./webapps/business_calendar/utils/tests/test_store.py": 0.382, "./webapps/business_calendar/views/tests/test_calendar_change.py": 0.528, "./webapps/business_calendar/views/tests/test_changed_dates.py": 0.589, "./webapps/business_consents/tests/test_actions.py": 0.021, "./webapps/business_consents/tests/test_services.py": 0.*****************, "./webapps/business_consents/tests/test_views.py": 0.19999999999999998, "./webapps/business_customer_info/tests/test_business_customer_info_views.py": 0.304, "./webapps/business_related/tests/test_amenities.py": 0.376, "./webapps/business_related/tests/test_amenities_serializers.py": 0.*****************, "./webapps/business_related/tests/test_business_related_validators.py": 0.10899999999999999, "./webapps/business_related/tests/test_security_settings.py": 0.22, "./webapps/c2b_referral/tests/test_tasks.py": 0.37, "./webapps/c2b_referral/tests/test_utils.py": 0.020999999999999998, "./webapps/commission/tests/test_scripts.py": 0.036000000000000004, "./webapps/consents/tests/test_fields.py": 0.056, "./webapps/consents/tests/test_models.py": 2.319, "./webapps/consents/tests/test_serializers.py": 1.095, "./webapps/consents/tests/test_tasks.py": 4.279, "./webapps/contact_us/tests/test_recipients.py": 0.10899999999999999, "./webapps/contact_us/tests/test_send_email.py": 3.199, "./webapps/df_creator/admin/tests/test_forms.py": 0.7110000000000002, "./webapps/df_creator/tests/test_helpers.py": 0.018000000000000002, "./webapps/df_creator/tests/test_models_df_category.py": 0.08799999999999998, "./webapps/donation/tests/test_donation_transaction.py": 7.08, "./webapps/elasticsearch/tests/test_availability_task.py": 2.848, "./webapps/elasticsearch/tests/test_create_index_with_alias.py": 5.364999999999999, "./webapps/elasticsearch/tests/test_get_all_indexes.py": 2.974, "./webapps/elasticsearch/tests/test_index_create_alias.py": 0.764, "./webapps/experiment_v3/management/commands/tests/test_mark_duplicats_deleted.py": 3.37, "./webapps/experiment_v3/tests/exp/test_bounding_box_experiment.py": 0.07400000*********, "./webapps/experiment_v3/tests/exp/test_new_my_booksy_layout.py": 0.18900000000000003, "./webapps/experiment_v3/tests/exp/test_new_on_booksy_gallery.py": 0.333, "./webapps/experiment_v3/tests/exp/test_return_rate_experiment.py": 0.066, "./webapps/experiment_v3/tests/exp/test_service_details_experiment.py": 0.079, "./webapps/experiment_v3/tests/exp/test_user_gender.py": 34.751, "./webapps/experiment_v3/tests/filters/test_api_country.py": 0.028, "./webapps/experiment_v3/tests/filters/test_base_filter.py": 0.027, "./webapps/experiment_v3/tests/filters/test_business_filters.py": 0.209, "./webapps/experiment_v3/tests/plugins/test_segment.py": 0.07999999999999999, "./webapps/experiment_v3/tests/tasks/test_experimental_user_gender_import_task.py": 2.3580000000000005, "./webapps/experiment_v3/tests/tasks/test_recommendation_group_import_tasks.py": 0.01, "./webapps/experiment_v3/tests/test_alternative_switch_to_anyone_experiment.py": 0.099, "./webapps/experiment_v3/tests/test_base_exp_with_user_pseudo_id.py": 0.127, "./webapps/experiment_v3/tests/test_base_experiment.py": 0.2940000*********, "./webapps/experiment_v3/tests/test_base_experiment_with_universal_control_group.py": 0.19, "./webapps/experiment_v3/tests/test_base_plugin.py": 0.017, "./webapps/experiment_v3/tests/test_change_the_anyone_copy_experiment.py": 0.104, "./webapps/experiment_v3/tests/test_cleaned_up_view_of_services_experiment.py": 0.102, "./webapps/experiment_v3/tests/test_collapsed_expanded_view_experiment.py": 0.101, "./webapps/experiment_v3/tests/test_django_db_proxy.py": 0.066, "./webapps/experiment_v3/tests/test_easier_access_to_book_again_on_web_experiment.py": 0.098, "./webapps/experiment_v3/tests/test_easier_access_to_popular_calendar_filters_experiment.py": 1.585, "./webapps/experiment_v3/tests/test_easier_access_to_staffer_dropdown_menu_experiment.py": 3.8089999999999997, "./webapps/experiment_v3/tests/test_enhanced_calendar_differentiation.py": 0.16699999999999998, "./webapps/experiment_v3/tests/test_experiment_cache.py": 0.079, "./webapps/experiment_v3/tests/test_experiment_parameters.py": 0.146, "./webapps/experiment_v3/tests/test_experiment_variant_for_user_or_fingerprint_view.py": 0.667, "./webapps/experiment_v3/tests/test_experiment_variant_for_user_view.py": 0.696, "./webapps/experiment_v3/tests/test_messages.py": 0.34299999999999997, "./webapps/experiment_v3/tests/test_models_experiment.py": 0.10600000*********, "./webapps/experiment_v3/tests/test_models_experiment_slot.py": 0.09, "./webapps/experiment_v3/tests/test_models_experiment_variant.py": 0.025, "./webapps/experiment_v3/tests/test_rollout.py": 0.243, "./webapps/experiment_v3/tests/test_router.py": 0.2879999999999999, "./webapps/experiment_v3/tests/test_staffer_selection_experiment.py": 0.105, "./webapps/experiment_v3/tests/test_time_slot_markers_experiment.py": 0.102, "./webapps/experiment_v3/tests/test_watcher.py": 0.431, "./webapps/family_and_friends/tests/serializers/test_family_and_friends_bookings_serializer.py": 1.951, "./webapps/family_and_friends/tests/serializers/test_invitation_serializer.py": 0.458, "./webapps/family_and_friends/tests/serializers/test_match_invited_user_serializer.py": 0.9239999999999999, "./webapps/family_and_friends/tests/serializers/test_member_appointment_serializer.py": 2.5909999999999997, "./webapps/family_and_friends/tests/serializers/test_member_create_update_serializer.py": 2.201, "./webapps/family_and_friends/tests/serializers/test_member_profile_serializer.py": 0.039, "./webapps/family_and_friends/tests/serializers/test_member_with_relation_response_serializer.py": 0.223, "./webapps/family_and_friends/tests/serializers/test_unlink_serializer.py": 0.375, "./webapps/family_and_friends/tests/test_appointment_notifications.py": 1.664, "./webapps/family_and_friends/tests/test_booking_notifications.py": 1.541, "./webapps/family_and_friends/tests/test_consent_notification.py": 2.7610000000000006, "./webapps/family_and_friends/tests/test_delete_user.py": 15.721000000000002, "./webapps/family_and_friends/tests/test_factory.py": 6.122999999999998, "./webapps/family_and_friends/tests/test_find_booksy_user_candidate.py": 0.268, "./webapps/family_and_friends/tests/test_helpers.py": 3.256, "./webapps/family_and_friends/tests/test_helpers_invitation.py": 0.43, "./webapps/family_and_friends/tests/test_invitation.py": 1.1289999999999998, "./webapps/family_and_friends/tests/test_models.py": 1.332, "./webapps/family_and_friends/tests/test_notifications.py": 2.785, "./webapps/family_and_friends/tests/test_pos_helpers.py": 1.506, "./webapps/family_and_friends/tests/test_reversed_relations.py": 0.03, "./webapps/family_and_friends/tests/test_review_helpers.py": 0.9179999999999999, "./webapps/family_and_friends/tests/test_settings.py": 0.029, "./webapps/family_and_friends/tests/test_tools.py": 1.476, "./webapps/family_and_friends/tests/test_update_member_bcis_after_user_update.py": 5.138, "./webapps/feeds/facebook/tests/test_tasks.py": 0.6459999999999999, "./webapps/feeds/facebook/tests/test_tools.py": 0.44, "./webapps/feeds/google/tests/test_google_create_booking.py": 20.972, "./webapps/feeds/google/tests/test_google_feeds_valid_items.py": 2.194, "./webapps/feeds/google/tests/test_google_itemizer.py": 4.906000*********, "./webapps/feeds/google/tests/test_google_update_booking.py": 4.734, "./webapps/feeds/groupon/tests/test_tasks.py": 1.1019999999999999, "./webapps/feeds/groupon/tests/test_utils.py": 0.08499999999999998, "./webapps/feeds/tests/test_google.py": 0.197, "./webapps/french_certification/tests/integration_tests/test_adapters.py": 0.26599999999999996, "./webapps/french_certification/tests/subscribers/test_loyalty_program.py": 5.015, "./webapps/french_certification/tests/test_actions.py": 1.7930000000000004, "./webapps/french_certification/tests/test_admin.py": 4.380999999999999, "./webapps/french_certification/tests/test_appointment.py": 9.985000*********, "./webapps/french_certification/tests/test_business_transaction_fc.py": 6.355, "./webapps/french_certification/tests/test_fiscal_archive.py": 7.196, "./webapps/french_certification/tests/test_fiscal_receipt.py": 4.175, "./webapps/french_certification/tests/test_fiscal_receipt_payments.py": 4.9399999999999995, "./webapps/french_certification/tests/test_grand_totals.py": 5.3759999999999994, "./webapps/french_certification/tests/test_integrations/test_utils.py": 0.16999999999999998, "./webapps/french_certification/tests/test_jet.py": 0.9830000*********, "./webapps/french_certification/tests/test_models.py": 0.747, "./webapps/french_certification/tests/test_serializers.py": 25.158999999999995, "./webapps/french_certification/tests/test_services.py": 68.38399999999999, "./webapps/french_certification/tests/test_tasks.py": 33.727000000000004, "./webapps/french_certification/tests/test_utils.py": 0.192, "./webapps/french_certification/tests/test_validators.py": 0.033, "./webapps/french_certification/tests/test_views.py": 21.094000000000005, "./webapps/google_sign_in/tests/test_views.py": 2.371, "./webapps/help_center/tests/test_models.py": 0.055, "./webapps/hints/tests/test_forms.py": 0.306, "./webapps/hints/tests/test_models.py": 0.6250000*********, "./webapps/hints/tests/test_serializers.py": 0.09, "./webapps/hints/tests/test_views.py": 3.9290000000000007, "./webapps/images/tests/test_actions.py": 0.223, "./webapps/images/tests/test_auto_reindex_image_model.py": 8.018, "./webapps/images/tests/test_booksy_image_upload_field.py": 0.017, "./webapps/images/tests/test_business_image_placeholder.py": 0.045, "./webapps/images/tests/test_image_comment.py": 0.20600000000000002, "./webapps/images/tests/test_image_interface.py": 0.****************, "./webapps/images/tests/test_image_reorder.py": 0.20099999999999998, "./webapps/images/tests/test_image_searchables.py": 0.905, "./webapps/images/tests/test_image_v2_serializer.py": 0.042, "./webapps/images/tests/test_messages.py": 0.251, "./webapps/images/tests/test_notifications.py": 0.29400000000000004, "./webapps/images/tests/test_publish_photos_to_portfolio.py": 0.647, "./webapps/instagram_integration/tests/test_handlers.py": 2.003, "./webapps/instagram_integration/tests/test_helpers.py": 0.052, "./webapps/intro_screen/tests/test_data.py": 0.05, "./webapps/intro_screen/tests/test_serializers.py": 0.07400000*********, "./webapps/invoice/tests/test_serializers.py": 0.5509999999999999, "./webapps/invoice/tests/test_storage.py": 0.7270000*********, "./webapps/invoicing/searchables/tests/test_business_customer_buyers.py": 0.159, "./webapps/invoicing/serializers/tests/test_cash_register_document_item_serializer.py": 0.20600000000000002, "./webapps/invoicing/serializers/tests/test_cash_register_document_serializers.py": 1.4819999999999998, "./webapps/invoicing/serializers/tests/test_common_serializers.py": 0.311, "./webapps/invoicing/serializers/tests/test_fc_invoice_item_serializer.py": 0.7, "./webapps/invoicing/serializers/tests/test_fc_invoice_serializers.py": 8.705000000000002, "./webapps/invoicing/serializers/tests/test_fc_seller_serializer.py": 4.659999999999999, "./webapps/invoicing/serializers/tests/test_invoice_item_serializer.py": 0.236, "./webapps/invoicing/serializers/tests/test_invoice_serializers.py": 5.862000*********, "./webapps/invoicing/serializers/tests/test_seller_serializer.py": 0.155, "./webapps/invoicing/tests/test_adapters.py": 0.17, "./webapps/invoicing/tests/test_cash_register_document.py": 1.****************, "./webapps/invoicing/tests/test_customer_invoice.py": 6.955, "./webapps/invoicing/tests/test_invoice_item.py": 0.121, "./webapps/invoicing/tests/test_ports.py": 0.494, "./webapps/invoicing/tests/test_scripts.py": 0.126, "./webapps/invoicing/tests/test_serializer.py": 0.034, "./webapps/kill_switch/test/test_models.py": 0.*****************, "./webapps/market_pay/tests/test_account_holder_photo_serializer.py": 0.203, "./webapps/market_pay/tests/test_admin.py": 0.164, "./webapps/market_pay/tests/test_contact_serializer.py": 0.054, "./webapps/market_pay/tests/test_helpers.py": 0.042, "./webapps/market_pay/tests/test_manual_payout.py": 0.477, "./webapps/market_pay/tests/test_marketpay_serializers.py": 35.**************, "./webapps/market_pay/tests/test_notification_planner.py": 0.698, "./webapps/market_pay/tests/test_notifications.py": 1.713, "./webapps/market_pay/tests/test_payout_flow.py": 0.*****************, "./webapps/market_pay/tests/test_report.py": 1.825, "./webapps/market_pay/tests/test_serializer_phone_field.py": 0.033999999999999996, "./webapps/market_pay/tests/test_signatory_provider.py": 0.*****************, "./webapps/market_pay/tests/test_transfer_flow.py": 1.****************, "./webapps/market_pay/tests/test_verification_serializer.py": 0.052000000000000005, "./webapps/market_pay/views/tests/test_adyen_to_stripe_consent_view.py": 6.**********00003, "./webapps/market_pay/views/tests/test_adyen_to_stripe_post_checkout_consent.py": 5.523000*********, "./webapps/market_pay/views/tests/test_notifications_handler.py": 1.714, "./webapps/marketing/tests/test_utils.py": 0.24400000000000005, "./webapps/marketplace/cms/tests/test_best_reviews.py": 0.749, "./webapps/marketplace/cms/tests/test_category_regions_listing.py": 1.608, "./webapps/marketplace/cms/tests/test_feature_flags.py": 0.14500000000000002, "./webapps/marketplace/cms/tests/test_regions_homepage.py": 0.524, "./webapps/marketplace/cms/tests/test_seo_cms_content.py": 1.3219999999999998, "./webapps/marketplace/cms/tests/test_seo_content_searchable.py": 0.796, "./webapps/marketplace/tests/test_actions.py": 0.38300000000000006, "./webapps/marketplace/tests/test_admin_actions.py": 2.231, "./webapps/marketplace/tests/test_boost_api.py": 1.551, "./webapps/marketplace/tests/test_boost_payment_source_pl.py": 0.112, "./webapps/marketplace/tests/test_boost_reactivation.py": 4.811000*********, "./webapps/marketplace/tests/test_boost_vs_reviews.py": 0.849, "./webapps/marketplace/tests/test_business_network.py": 0.107, "./webapps/marketplace/tests/test_chargable_payable.py": 12.213000*********, "./webapps/marketplace/tests/test_chargeable_row.py": 1.953, "./webapps/marketplace/tests/test_claim.py": 14.5**********0004, "./webapps/marketplace/tests/test_cleanup_cms_conent.py": 3.689, "./webapps/marketplace/tests/test_cms_content_search.py": 1.0030000*********, "./webapps/marketplace/tests/test_commissions.py": 10.552, "./webapps/marketplace/tests/test_cost.py": 2.7910000000000004, "./webapps/marketplace/tests/test_count_commission.py": 1.547, "./webapps/marketplace/tests/test_future_bookings.py": 0.748, "./webapps/marketplace/tests/test_import_b_listings.py": 0.824, "./webapps/marketplace/tests/test_initialize_seo_region_category_listings.py": 0.078, "./webapps/marketplace/tests/test_invoice_summary_report.py": 1.107, "./webapps/marketplace/tests/test_make_sure_merchant_can_pay.py": 0.634, "./webapps/marketplace/tests/test_marketplace_bookings.py": 4.537, "./webapps/marketplace/tests/test_marketplace_business.py": 0.21599999999999997, "./webapps/marketplace/tests/test_marketplace_business_history.py": 0.116, "./webapps/marketplace/tests/test_marketplace_transaction.py": 2.864, "./webapps/marketplace/tests/test_marketplace_transaction_row.py": 1.106, "./webapps/marketplace/tests/test_marketplace_transaction_status.py": 0.099, "./webapps/marketplace/tests/test_match_treatment.py": 1.342, "./webapps/marketplace/tests/test_notifications.py": 1.6609999999999996, "./webapps/marketplace/tests/test_overdues_cutoff_date.py": 0.703, "./webapps/marketplace/tests/test_pay.py": 1.778, "./webapps/marketplace/tests/test_promotions.py": 1.105, "./webapps/marketplace/tests/test_push.py": 1.914, "./webapps/marketplace/tests/test_refund.py": 11.121999999999996, "./webapps/marketplace/tests/test_retry_payments.py": 0.88, "./webapps/marketplace/tests/test_seo_metadata.py": 1.4**********00003, "./webapps/marketplace/tests/test_set_has_braintree_task.py": 0.129, "./webapps/marketplace/tests/test_tools.py": 0.053, "./webapps/marketplace/tests/test_utils.py": 4.013, "./webapps/message_blast/tests/test_admin.py": 1.154, "./webapps/message_blast/tests/test_blocked_phone_number.py": 0.10099999999999999, "./webapps/message_blast/tests/test_helpers.py": 48.045, "./webapps/message_blast/tests/test_models.py": 6.053, "./webapps/message_blast/tests/test_notifications.py": 3.8260000000000005, "./webapps/message_blast/tests/test_schedules.py": 0.575, "./webapps/message_blast/tests/test_serializers.py": 1.2569999999999997, "./webapps/message_blast/tests/test_task.py": 3.51, "./webapps/message_blast/tests/test_views.py": 0.328, "./webapps/metrics/tests/test_serializers.py": 0.053, "./webapps/navision/grpc/tests/test_client.py": 0.028, "./webapps/navision/grpc/tests/test_ports.py": 3.282, "./webapps/navision/grpc/tests/test_serializers.py": 2.402, "./webapps/navision/tests/admin/test_invoicing_error.py": 2.551, "./webapps/navision/tests/clients/v1/test_invoice_client.py": 0.281, "./webapps/navision/tests/clients/v1/test_merchant_client.py": 0.30100000000000005, "./webapps/navision/tests/clients/v2/test_invoice_client.py": 0.241, "./webapps/navision/tests/clients/v2/test_merchant_client.py": 0.036, "./webapps/navision/tests/clients/v2/test_update_client.py": 0.037, "./webapps/navision/tests/decorators/test_decorators.py": 1.718, "./webapps/navision/tests/decorators/test_prevent_double_error_processing.py": 0.656, "./webapps/navision/tests/invoicing/test_booksy_billing_refactor_flag_off.py": 14.992000000000003, "./webapps/navision/tests/invoicing/test_booksy_billing_refactor_flag_on.py": 45.72500*********, "./webapps/navision/tests/invoicing/test_boost_mixins.py": 7.751999999999998, "./webapps/navision/tests/invoicing/test_boost_offline.py": 8.809, "./webapps/navision/tests/invoicing/test_boost_offline_invoices_for_migration.py": 13.187000*********, "./webapps/navision/tests/invoicing/test_boost_offline_offline_online_migration_date_flag.py": 2.466, "./webapps/navision/tests/invoicing/test_boost_online_invoicing_refactor_flag_off.py": 35.205, "./webapps/navision/tests/invoicing/test_boost_online_invoicing_refactor_flag_on.py": 37.513000000000005, "./webapps/navision/tests/invoicing/test_catch_errors.py": 11.61, "./webapps/navision/tests/invoicing/test_saas_offline.py": 22.866999999999997, "./webapps/navision/tests/invoicing/test_saas_offline_sync_based_on_approved_date_flag_off.py": 2.0140000000000002, "./webapps/navision/tests/invoicing/test_sms_offline.py": 0.8989999999999999, "./webapps/navision/tests/tasks/test_api_sync_invoices_with_errors.py": 0.9010000000000002, "./webapps/navision/tests/tasks/test_api_tasks.py": 32.992000000000004, "./webapps/navision/tests/tasks/test_navision_saas.py": 0.37399999999999994, "./webapps/navision/tests/tasks/test_process_error_task.py": 4.3389999999999995, "./webapps/navision/tests/tasks/test_update_tax_rates.py": 1.2819999999999998, "./webapps/navision/tests/tasks/test_verify_new_billing_merchants.py": 1.359, "./webapps/navision/tests/test_actions.py": 0.33799999999999997, "./webapps/navision/tests/test_admin.py": 23.672999999999995, "./webapps/navision/tests/test_get_proper_serializer.py": 0.07100000*********, "./webapps/navision/tests/test_helpers.py": 0.127, "./webapps/navision/tests/test_invoice_details.py": 0.668, "./webapps/navision/tests/test_invoice_details_forms.py": 9.983999999999998, "./webapps/navision/tests/test_invoice_details_forms_serializers.py": 36.42100*********4, "./webapps/navision/tests/test_models/test_invoice.py": 2.58, "./webapps/navision/tests/test_models/test_invoicingerror_history_model.py": 0.14100000*********, "./webapps/navision/tests/test_models/test_merchant.py": 0.*****************, "./webapps/navision/tests/test_models/test_tax_rate.py": 0.054, "./webapps/navision/tests/test_nav_settings_view.py": 1.537, "./webapps/navision/tests/test_navision_settings.py": 0.093, "./webapps/navision/tests/test_ports.py": 2.38, "./webapps/navision/tests/test_subscription_buyer_update.py": 1.539, "./webapps/navision/tests/test_tax_groups.py": 0.07200000*********, "./webapps/navision/tests/test_tax_rate_admin.py": 0.49600000000000005, "./webapps/navision/tests/test_tax_rates.py": 1.8760000*********, "./webapps/notification/management/tests/test_clean_duplicated_recievers.py": 0.16499999999999998, "./webapps/notification/tasks/tests/test_delete_expired_push_tokens.py": 0.196, "./webapps/notification/tasks/tests/test_reminder_hour_change_old_value_cleanup.py": 0.244, "./webapps/notification/tasks/tests/test_remove_unused_tokens.py": 0.076, "./webapps/notification/tasks/tests/test_retrieve_stucked_received.py": 0.05499999999999999, "./webapps/notification/tasks/tests/test_retrieve_stucked_started.py": 0.776, "./webapps/notification/tasks/tests/test_single_notification_schedule_task.py": 0.106, "./webapps/notification/tasks/tests/test_sms_history.py": 0.264, "./webapps/notification/tests/test_base_notification.py": 0.918, "./webapps/notification/tests/test_booking_finished_reviews.py": 0.7119999999999999, "./webapps/notification/tests/test_booking_finished_scenario.py": 37.961999999999996, "./webapps/notification/tests/test_channel_sms.py": 0.065, "./webapps/notification/tests/test_commence_send_sms.py": 3.056, "./webapps/notification/tests/test_document_as_model.py": 0.01, "./webapps/notification/tests/test_email_channel.py": 0.095, "./webapps/notification/tests/test_family_and_friends_notifications.py": 12.511, "./webapps/notification/tests/test_generic_booking_communication.py": 2.2039999999999997, "./webapps/notification/tests/test_get_recipients.py": 0.294, "./webapps/notification/tests/test_legacy_sms_stats_and_summary.py": 0.624, "./webapps/notification/tests/test_load_push_in_bulk.py": 0.101, "./webapps/notification/tests/test_loggers.py": 0.11699999999999999, "./webapps/notification/tests/test_models.py": 2.351, "./webapps/notification/tests/test_no_blast_suggestion.py": 2.184, "./webapps/notification/tests/test_notification_flow.py": 1.3820000*********, "./webapps/notification/tests/test_notification_planner.py": 2.511, "./webapps/notification/tests/test_notification_recipient_filter.py": 0.175, "./webapps/notification/tests/test_notification_schedule.py": 0.21800000000000003, "./webapps/notification/tests/test_notification_status.py": 0.192, "./webapps/notification/tests/test_ports.py": 0.052, "./webapps/notification/tests/test_push_rebuild.py": 33.28800000000002, "./webapps/notification/tests/test_receivers.py": 0.056, "./webapps/notification/tests/test_render_scenarios.py": 1.4449999999999996, "./webapps/notification/tests/test_retrial.py": 2.119, "./webapps/notification/tests/test_scenario.py": 3.477, "./webapps/notification/tests/test_send_sms_registration_code_task.py": 0.207, "./webapps/notification/tests/test_senders.py": 0.111, "./webapps/notification/tests/test_set_notification_receivers.py": 0.053, "./webapps/notification/tests/test_simple_scenario.py": 0.032, "./webapps/notification/tests/test_sms_stats_and_summary.py": 6.831999999999999, "./webapps/notification/tests/test_split_meta_dict.py": 0.02, "./webapps/notification/tests/test_staffer_locked_access_scenario.py": 0.439, "./webapps/notification/tests/test_tools.py": 1.416, "./webapps/payment_gateway/tests/messages/test_balance_transaction.py": 0.209, "./webapps/payment_gateway/tests/services/test_balance_transaction.py": 0.22200000000000003, "./webapps/payment_gateway/tests/services/test_dispute.py": 0.103, "./webapps/payment_gateway/tests/services/test_fee.py": 0.162, "./webapps/payment_gateway/tests/services/test_payment.py": 0.5630000000000002, "./webapps/payment_gateway/tests/services/test_payout.py": 0.212, "./webapps/payment_gateway/tests/services/test_refund.py": 0.382, "./webapps/payment_gateway/tests/services/test_transfer_fund.py": 0.08, "./webapps/payment_gateway/tests/services/test_wallet.py": 0.101, "./webapps/payment_gateway/tests/test_actions.py": 1.05, "./webapps/payment_gateway/tests/test_entities.py": 0.08199999999999999, "./webapps/payment_gateway/tests/test_ports.py": 0.029, "./webapps/payment_gateway/tests/test_scripts.py": 1.****************, "./webapps/payment_providers/management/commands/tests/test_migrate_adyen_to_stripe.py": 8.294, "./webapps/payment_providers/management/commands/tests/test_validate_cards_using_payment_history.py": 0.07600000*********, "./webapps/payment_providers/tests/adyen/provider/test_adyen_provider_utils.py": 0.091, "./webapps/payment_providers/tests/adyen/provider/test_auth_capture.py": 0.5790000000000002, "./webapps/payment_providers/tests/adyen/provider/test_payment_method.py": 0.13999999999999999, "./webapps/payment_providers/tests/adyen/provider/test_refund_cancel.py": 0.183, "./webapps/payment_providers/tests/adyen/provider/test_transfer_fund.py": 0.07, "./webapps/payment_providers/tests/adyen/test_adyen_payment_services.py": 0.087, "./webapps/payment_providers/tests/adyen/test_adyen_payments.py": 0.*****************, "./webapps/payment_providers/tests/adyen/test_adyen_scripts.py": 0.328, "./webapps/payment_providers/tests/adyen/test_adyen_services.py": 0.245, "./webapps/payment_providers/tests/adyen/test_adyen_services_initialization.py": 0.01, "./webapps/payment_providers/tests/adyen/test_synchronization.py": 0.271, "./webapps/payment_providers/tests/adyen/test_webhooks.py": 0.367, "./webapps/payment_providers/tests/stripe/test_migrate_account_holder_pappers_fr.py": 0.068, "./webapps/payment_providers/tests/stripe/test_stripe_account_holder.py": 0.*****************, "./webapps/payment_providers/tests/stripe/test_stripe_customer.py": 0.044, "./webapps/payment_providers/tests/stripe/test_stripe_notifications.py": 0.****************, "./webapps/payment_providers/tests/stripe/test_stripe_payment_method_service.py": 0.034, "./webapps/payment_providers/tests/stripe/test_stripe_payments.py": 0.3360000*********, "./webapps/payment_providers/tests/stripe/test_stripe_payouts.py": 9.113, "./webapps/payment_providers/tests/stripe/test_stripe_scripts.py": 0.097, "./webapps/payment_providers/tests/stripe/test_stripe_services_initialization.py": 3.108, "./webapps/payment_providers/tests/stripe/test_stripe_terminal.py": 0.049, "./webapps/payment_providers/tests/stripe/test_stripe_tokenized_pm.py": 0.156, "./webapps/payment_providers/tests/stripe/test_stripe_transfer_funds.py": 0.029, "./webapps/payment_providers/tests/stripe/test_synchronize_stripe_objects.py": 0.717, "./webapps/payment_providers/tests/test_adyen_to_strpie_card_migration.py": 0.344, "./webapps/payment_providers/tests/test_common_account_holder.py": 0.062, "./webapps/payment_providers/tests/test_common_customer.py": 0.055, "./webapps/payment_providers/tests/test_common_payments.py": 0.*****************, "./webapps/payment_providers/tests/test_common_scripts.py": 0.546, "./webapps/payment_providers/tests/test_common_tokenized_pm.py": 0.818, "./webapps/payment_providers/tests/test_tasks.py": 0.358, "./webapps/payment_providers/tests/test_tokenized_payment_method_warning_circles.py": 0.327, "./webapps/payments/tests/end_to_end/test_make_payment.py": 4.***************, "./webapps/payments/tests/test_account_holder_views.py": 0.6000000*********, "./webapps/payments/tests/test_account_management_views.py": 0.875, "./webapps/payments/tests/test_notifications.py": 0.8930000*********, "./webapps/payments/tests/test_payment_views.py": 0.****************, "./webapps/payments/tests/test_tap_to_pay_views.py": 0.112, "./webapps/payments/tests/test_tokenized_pm_views.py": 0.909, "./webapps/point_of_sale/tests/services/test_basket_item_services.py": 0.7390000*********, "./webapps/point_of_sale/tests/services/test_basket_payment_analytics_service.py": 0.056999999999999995, "./webapps/point_of_sale/tests/services/test_basket_payment_services.py": 0.****************, "./webapps/point_of_sale/tests/services/test_basket_service.py": 0.*****************, "./webapps/point_of_sale/tests/services/test_basket_tip_services.py": 0.042, "./webapps/point_of_sale/tests/services/test_cancellation_fee_auth_analytics.py": 0.039, "./webapps/point_of_sale/tests/services/test_cancellation_fee_auth_services.py": 0.*****************, "./webapps/point_of_sale/tests/services/test_common_services.py": 0.025, "./webapps/point_of_sale/tests/services/test_migration_cancellation_fee_auth_service.py": 0.023, "./webapps/point_of_sale/tests/services/test_pos_services.py": 0.123, "./webapps/point_of_sale/tests/test_ports.py": 0.12, "./webapps/point_of_sale/tests/test_scripts.py": 0.46299999999999997, "./webapps/pop_up_notification/tests/test_consents_notification_popup_serializer.py": 0.134, "./webapps/pop_up_notification/tests/test_create_whats_new_family_and_friends_popup_celery_task.py": 0.134, "./webapps/pop_up_notification/tests/test_digital_flyer_serializer.py": 0.043, "./webapps/pop_up_notification/tests/test_family_and_friends_notification.py": 0.291, "./webapps/pop_up_notification/tests/test_meet_me_again_serializer.py": 2.3069999999999995, "./webapps/pop_up_notification/tests/test_what_is_new_family_and_friends_notification.py": 2.164, "./webapps/pos/management/commands/tests/test_disable_pay_by_app.py": 0.58, "./webapps/pos/tests/messages/test_transaction.py": 0.388, "./webapps/pos/tests/pos_refactor/bcr/test_stripe_stage_2.py": 82.32799999999996, "./webapps/pos/tests/pos_refactor/cf/test_adyen_stage_2.py": 20.033, "./webapps/pos/tests/pos_refactor/cf/test_stripe_stage_2.py": 58.53999999999999, "./webapps/pos/tests/pos_refactor/data_migration/test_bcr.py": 6.675999999999999, "./webapps/pos/tests/pos_refactor/data_migration/test_cf.py": 4.24, "./webapps/pos/tests/pos_refactor/data_migration/test_migration_cash.py": 1.706, "./webapps/pos/tests/pos_refactor/data_migration/test_migration_pba.py": 6.8500000000000005, "./webapps/pos/tests/pos_refactor/data_migration/test_migration_prepayment.py": 15.900000000000004, "./webapps/pos/tests/pos_refactor/data_migration/test_migration_split.py": 3.5620000000000003, "./webapps/pos/tests/pos_refactor/data_migration/test_migration_tips.py": 2.759, "./webapps/pos/tests/pos_refactor/data_migration/test_scripts.py": 4.9959999999999996, "./webapps/pos/tests/pos_refactor/pba/test_adyen_stage_2.py": 65.47900*********, "./webapps/pos/tests/pos_refactor/pba/test_stripe_stage_2.py": 50.543000000000006, "./webapps/pos/tests/pos_refactor/prepayment/test_adyen_stage_2.py": 96.04700*********, "./webapps/pos/tests/pos_refactor/prepayment/test_stripe_stage_2.py": 126.11900*********, "./webapps/pos/tests/pos_refactor/split/test_adyen_stage_2.py": 12.123999999999999, "./webapps/pos/tests/pos_refactor/split/test_stripe_stage_2.py": 10.331, "./webapps/pos/tests/pos_refactor/test_cash.py": 1.9609999999999999, "./webapps/pos/tests/pos_refactor/test_commissions.py": 2.1750000000000003, "./webapps/pos/tests/pos_refactor/test_paying_offline.py": 8.361, "./webapps/pos/tests/pos_refactor/test_ports.py": 0.079, "./webapps/pos/tests/pos_refactor/test_proxy_provider.py": 0.633, "./webapps/pos/tests/pos_refactor/test_selling_things.py": 6.161, "./webapps/pos/tests/pos_refactor/test_services.py": 32.961, "./webapps/pos/tests/pos_refactor/test_square.py": 1.8940000*********, "./webapps/pos/tests/pos_refactor/test_tips.py": 14.786999999999999, "./webapps/pos/tests/pos_refactor/test_various.py": 4.9479999999999995, "./webapps/pos/tests/test_actions.py": 0.038, "./webapps/pos/tests/test_adapters.py": 0.689, "./webapps/pos/tests/test_auto_closing_prepayments.py": 16.352, "./webapps/pos/tests/test_batch_change_pos_plans.py": 0.263, "./webapps/pos/tests/test_business_address_change.py": 1.522, "./webapps/pos/tests/test_business_item_searchable.py": 2.238, "./webapps/pos/tests/test_calculations.py": 0.365, "./webapps/pos/tests/test_calculations_from_gross_amount.py": 0.*****************, "./webapps/pos/tests/test_chargeback_transitions.py": 2.662, "./webapps/pos/tests/test_commissions.py": 1.5050000*********, "./webapps/pos/tests/test_deposit.py": 5.466, "./webapps/pos/tests/test_diff_display.py": 0.29, "./webapps/pos/tests/test_disable_deprecated_payment_types.py": 0.374, "./webapps/pos/tests/test_marketpay_flow.py": 0.010000000000000002, "./webapps/pos/tests/test_missing_notifications.py": 6.238, "./webapps/pos/tests/test_model_bank_account.py": 0.026000000000000002, "./webapps/pos/tests/test_model_tax_rate.py": 3.****************, "./webapps/pos/tests/test_model_transaction.py": 3.****************, "./webapps/pos/tests/test_notifications.py": 6.123, "./webapps/pos/tests/test_payment_method.py": 0.066, "./webapps/pos/tests/test_payment_method_credit_card_serializer.py": 0.097, "./webapps/pos/tests/test_payment_rows.py": 26.137, "./webapps/pos/tests/test_payment_type.py": 0.616, "./webapps/pos/tests/test_payout_flow.py": 1.044, "./webapps/pos/tests/test_pba_status_change.py": 0.223, "./webapps/pos/tests/test_populate_transactionrow_service_data.py": 0.166, "./webapps/pos/tests/test_pos_payment_serializer.py": 34.***************, "./webapps/pos/tests/test_pos_plan.py": 0.018, "./webapps/pos/tests/test_provider_adyen_ee.py": 3.***************, "./webapps/pos/tests/test_provider_base.py": 1.1660000*********, "./webapps/pos/tests/test_recalculate_pos_plans_for_payment_type.py": 11.***************, "./webapps/pos/tests/test_receipt_footer.py": 0.802, "./webapps/pos/tests/test_refund_transactions.py": 3.3609999999999998, "./webapps/pos/tests/test_scripts.py": 0.517, "./webapps/pos/tests/test_serializers.py": 4.049, "./webapps/pos/tests/test_service_variants_serializers.py": 0.376, "./webapps/pos/tests/test_simple_checkout_with_voucher.py": 5.795999999999999, "./webapps/pos/tests/test_simple_transaction_serializer.py": 10.48, "./webapps/pos/tests/test_simple_transaction_shared_registers.py": 0.487, "./webapps/pos/tests/test_tasks.py": 13.271, "./webapps/pos/tests/test_tip.py": 3.789, "./webapps/pos/tests/test_tip_rows_calculations.py": 0.16200000000000006, "./webapps/pos/tests/test_total_format_for_cancellation_fee.py": 0.237, "./webapps/pos/tests/test_transaction_cash_info.py": 2.895, "./webapps/pos/tests/test_transaction_default_travel_fee_price.py": 0.199, "./webapps/pos/tests/test_transaction_serializer.py": 30.358000000000004, "./webapps/pos/tests/test_transaction_serializer_family_and_friends.py": 4.510000*********, "./webapps/pos/tests/test_transaction_serializer_legacy.py": 14.065000*********, "./webapps/pos/tests/test_transaction_tip_rows.py": 5.463, "./webapps/pos/tests/test_update_no_show_serializer.py": 24.286999999999992, "./webapps/pos/tests/test_update_transaction_with_booking.py": 11.256, "./webapps/pos/tests/test_validate_booking_and_deposit.py": 0.115, "./webapps/printer_api/tests/test_printer_config.py": 0.157, "./webapps/printer_api/tests/test_serializer.py": 0.***************98, "./webapps/profile_completeness/tests/test_admin.py": 2.578, "./webapps/profile_completeness/tests/test_finished_appointments.py": 0.476, "./webapps/profile_completeness/tests/test_helper_methods.py": 0.805, "./webapps/profile_completeness/tests/test_models.py": 1.002, "./webapps/profile_completeness/tests/test_notifications.py": 1.7969999999999997, "./webapps/profile_completeness/tests/test_serializer.py": 3.1009999999999995, "./webapps/profile_completeness/tests/test_steps.py": 2.9890000000000008, "./webapps/profile_completeness/tests/test_steps_boost.py": 0.158, "./webapps/profile_completeness/tests/test_steps_customer_booking.py": 0.833, "./webapps/profile_completeness/tests/test_steps_customer_invite.py": 0.541, "./webapps/profile_completeness/tests/test_steps_launch_promotions.py": 27.079000000000004, "./webapps/profile_completeness/tests/test_steps_link_profile.py": 0.252, "./webapps/profile_completeness/tests/test_steps_mobile_payments.py": 0.127, "./webapps/profile_completeness/tests/test_steps_no_show_protection.py": 0.7140000*********, "./webapps/profile_completeness/tests/test_steps_offline.py": 0.304, "./webapps/profile_completeness/tests/test_steps_refer_business.py": 0.262, "./webapps/profile_completeness/tests/test_steps_reviews.py": 4.888, "./webapps/profile_completeness/tests/test_steps_sell_giftcard.py": 0.136, "./webapps/profile_completeness/tests/test_steps_use_social_post_creator.py": 0.128, "./webapps/profile_completeness/tests/test_tiers.py": 1.9769999999999999, "./webapps/profile_completeness/tests/test_views.py": 2.9990000000000006, "./webapps/public_partners/signals/tests/test_appointment.py": 0.01, "./webapps/public_partners/signals/tests/test_business.py": 0.01, "./webapps/public_partners/signals/tests/test_business_customer.py": 0.01, "./webapps/public_partners/signals/tests/test_consent.py": 0.01, "./webapps/public_partners/signals/tests/test_consent_form.py": 0.01, "./webapps/public_partners/signals/tests/test_image.py": 0.01, "./webapps/public_partners/signals/tests/test_oauth2_installation.py": 0.01, "./webapps/public_partners/signals/tests/test_resource.py": 0.01, "./webapps/public_partners/signals/tests/test_service.py": 0.01, "./webapps/public_partners/tasks/tests/test_appointment_webhook.py": 0.01, "./webapps/public_partners/tasks/tests/test_notify_client_task.py": 0.01, "./webapps/public_partners/test_utils.py": 0.01, "./webapps/public_partners/tests/appointment/test_appointment_bulk_import.py": 0.01, "./webapps/public_partners/tests/appointment/test_appointment_cancel.py": 0.01, "./webapps/public_partners/tests/appointment/test_appointment_cancel_from_v03.py": 0.01, "./webapps/public_partners/tests/appointment/test_appointment_confirm.py": 0.01, "./webapps/public_partners/tests/appointment/test_appointment_confirm_from_v03.py": 0.01, "./webapps/public_partners/tests/appointment/test_appointment_create.py": 0.01, "./webapps/public_partners/tests/appointment/test_appointment_create_from_v03.py": 0.01, "./webapps/public_partners/tests/appointment/test_appointment_decline_from_v03.py": 0.01, "./webapps/public_partners/tests/appointment/test_appointment_finish_from_v03.py": 0.01, "./webapps/public_partners/tests/appointment/test_appointment_listing.py": 0.01, "./webapps/public_partners/tests/appointment/test_appointment_mapping.py": 0.01, "./webapps/public_partners/tests/appointment/test_appointment_no_show_from_v03.py": 0.01, "./webapps/public_partners/tests/appointment/test_appointment_retrieve.py": 0.01, "./webapps/public_partners/tests/appointment/test_appointment_update.py": 0.01, "./webapps/public_partners/tests/appointment/test_appointment_update_from_v03.py": 0.01, "./webapps/public_partners/tests/billing/test_subscription.py": 0.01, "./webapps/public_partners/tests/business/test_business.py": 0.01, "./webapps/public_partners/tests/business/test_business_activation.py": 0.01, "./webapps/public_partners/tests/business/test_business_deactivation.py": 0.01, "./webapps/public_partners/tests/business/test_business_mapping.py": 0.01, "./webapps/public_partners/tests/business/test_business_statistic.py": 0.01, "./webapps/public_partners/tests/business/test_business_v05.py": 0.01, "./webapps/public_partners/tests/business_customer/test_business_customer.py": 0.01, "./webapps/public_partners/tests/business_customer/test_business_customer_bulk_import.py": 0.01, "./webapps/public_partners/tests/business_customer/test_business_customer_count.py": 0.01, "./webapps/public_partners/tests/business_customer/test_business_customer_v04.py": 0.01, "./webapps/public_partners/tests/oauth2/test_oauth2_application.py": 0.01, "./webapps/public_partners/tests/oauth2/test_oauth2_authorization_code_openid_pkce.py": 0.01, "./webapps/public_partners/tests/oauth2/test_oauth2_authorization_code_pkce.py": 0.01, "./webapps/public_partners/tests/oauth2/test_oauth2_installation.py": 0.01, "./webapps/public_partners/tests/oauth2/test_oauth2_jwt_assertion_grant_type.py": 0.01, "./webapps/public_partners/tests/oauth2/test_oauth2_resource_owner_grant_type.py": 0.01, "./webapps/public_partners/tests/resource/test_resource.py": 0.01, "./webapps/public_partners/tests/resource/test_resource_bulk_import.py": 0.01, "./webapps/public_partners/tests/resource/test_resource_photo.py": 0.01, "./webapps/public_partners/tests/resource/test_resource_v04.py": 0.01, "./webapps/public_partners/tests/review/test_appointment_review.py": 0.01, "./webapps/public_partners/tests/review/test_appointment_review_v04.py": 0.01, "./webapps/public_partners/tests/review/test_review_bulk_import.py": 0.01, "./webapps/public_partners/tests/review/test_review_import.py": 0.01, "./webapps/public_partners/tests/review/test_review_statistics.py": 0.01, "./webapps/public_partners/tests/review/test_review_v04.py": 0.01, "./webapps/public_partners/tests/schedule/test_schedule.py": 0.01, "./webapps/public_partners/tests/schedule/test_schedule_bulk_import.py": 0.01, "./webapps/public_partners/tests/service/test_service.py": 0.01, "./webapps/public_partners/tests/service/test_service_bulk_import.py": 0.01, "./webapps/public_partners/tests/service/test_service_v04.py": 0.01, "./webapps/public_partners/tests/service_category/test_service_category.py": 0.01, "./webapps/public_partners/tests/service_category/test_service_category_bulk_import.py": 0.01, "./webapps/public_partners/tests/service_variant/test_service_variant.py": 0.01, "./webapps/public_partners/tests/service_variant/test_service_variant_bulk_import.py": 0.01, "./webapps/public_partners/tests/service_variant/test_service_variant_v04.py": 0.01, "./webapps/public_partners/tests/test_amenities.py": 0.01, "./webapps/public_partners/tests/test_business_category.py": 0.01, "./webapps/public_partners/tests/test_business_subdomain.py": 0.01, "./webapps/public_partners/tests/test_consent.py": 0.01, "./webapps/public_partners/tests/test_consent_form.py": 0.01, "./webapps/public_partners/tests/test_errors.py": 0.01, "./webapps/public_partners/tests/test_firewall_ip.py": 0.01, "./webapps/public_partners/tests/test_geocoding.py": 0.01, "./webapps/public_partners/tests/test_image.py": 0.01, "./webapps/public_partners/tests/test_me.py": 0.01, "./webapps/public_partners/tests/test_notifications_receiver.py": 0.01, "./webapps/public_partners/tests/test_service_photo.py": 0.01, "./webapps/public_partners/tests/test_service_suggestion.py": 0.01, "./webapps/public_partners/tests/test_subdomain_candidate.py": 0.01, "./webapps/public_partners/tests/test_throttling.py": 0.01, "./webapps/public_partners/tests/test_timezone_header.py": 0.01, "./webapps/public_partners/tests/test_token.py": 0.01, "./webapps/public_partners/tests/test_traveling_to_customer.py": 0.01, "./webapps/public_partners/tests/test_user.py": 0.01, "./webapps/public_partners/tests/test_versioning.py": 0.01, "./webapps/public_partners/tests/test_webhook.py": 0.01, "./webapps/public_partners/tests/test_webhook_log.py": 0.01, "./webapps/public_partners/tests/test_wrong_version.py": 0.01, "./webapps/public_partners/tests/time_off/test_time_off.py": 0.01, "./webapps/public_partners/tests/time_off/test_time_off_bulk_import.py": 0.01, "./webapps/public_partners/tests/time_off/test_time_off_group.py": 0.01, "./webapps/pubsub/tests/test_pubsub_message.py": 0.05299999999999999, "./webapps/pubsub/tests/test_pubsub_subscribing.py": 0.068, "./webapps/purchase/tasks/tests/test_apple.py": 2.841, "./webapps/purchase/tasks/tests/test_braintree.py": 2.6159999999999997, "./webapps/purchase/tasks/tests/test_compute_business_status_task.py": 9.706999999999999, "./webapps/purchase/tasks/tests/test_google.py": 0.20400000*********, "./webapps/purchase/tasks/tests/test_offline.py": 1.3190000000000002, "./webapps/purchase/tasks/tests/test_renew_business.py": 2.451, "./webapps/purchase/tasks/tests/test_webhook.py": 0.08199999999999999, "./webapps/purchase/tests/test_admin.py": 52.7**********002, "./webapps/purchase/tests/test_braintree_apply_free_days.py": 0.264, "./webapps/purchase/tests/test_braintree_apply_free_month.py": 0.6699999999999999, "./webapps/purchase/tests/test_braintree_utils.py": 1.2579999999999998, "./webapps/purchase/tests/test_buyer_model.py": 0.161, "./webapps/purchase/tests/test_churn.py": 2.086, "./webapps/purchase/tests/test_fill_subscriptionbuyer_data.py": 10.204, "./webapps/purchase/tests/test_google_play.py": 0.01, "./webapps/purchase/tests/test_google_play_v4.py": 20.034999999999997, "./webapps/purchase/tests/test_import_offline_transactions.py": 1.058, "./webapps/purchase/tests/test_invoice_selected_buyer.py": 1.139, "./webapps/purchase/tests/test_mrr.py": 0.6669999999999999, "./webapps/purchase/tests/test_navision_integration.py": 1.191, "./webapps/purchase/tests/test_offline_apply_free_month.py": 0.159, "./webapps/purchase/tests/test_purchase_utils.py": 5.384999999999998, "./webapps/purchase/tests/test_renew_one_business.py": 1.0679999999999998, "./webapps/purchase/tests/test_serializers.py": 1.3849999999999991, "./webapps/purchase/tests/test_sms_charges.py": 1.898, "./webapps/purchase/tests/test_staff_charges.py": 8.843, "./webapps/purchase/tests/test_subscription.py": 11.807000000000002, "./webapps/purchase/tests/test_suspension_flow.py": 1.6059999999999999, "./webapps/purchase/tests/test_validators.py": 0.075, "./webapps/qr_code/tests/test_factory.py": 0.078, "./webapps/qr_code_origami/tests/test_generator.py": 0.266, "./webapps/qr_code_origami/tests/test_views.py": 1.509, "./webapps/r_and_d/management/commands/tests/test_run_patterns_for_old_appointments.py": 1.068, "./webapps/r_and_d/test_utils.py": 0.01, "./webapps/r_and_d/tests/test_check_booking_reactivation.py": 3.325, "./webapps/r_and_d/tests/test_datetime_utils.py": 0.03, "./webapps/r_and_d/tests/test_daytime.py": 0.02, "./webapps/r_and_d/tests/test_pattern.py": 22.913, "./webapps/r_and_d/tests/test_tasks.py": 1.578, "./webapps/register/tests/test_ports.py": 0.28200000000000003, "./webapps/register/tests/test_register_serializers.py": 4.122, "./webapps/register/tests/test_register_transactions.py": 1.274, "./webapps/reviews/tests/test_actions.py": 0.33299999999999996, "./webapps/reviews/tests/test_auto_bookmarked.py": 9.636, "./webapps/reviews/tests/test_import_reviews.py": 1.041, "./webapps/reviews/tests/test_mark_review_as_fake.py": 1.026, "./webapps/reviews/tests/test_model_methods.py": 0.272, "./webapps/reviews/tests/test_notifications.py": 0.927, "./webapps/reviews/tests/test_review_serializer.py": 0.23, "./webapps/reviews/tests/test_reviews.py": 5.297, "./webapps/reviews/tests/test_simple_review_serializer.py": 0.539, "./webapps/schedule/tests/test_appt_conflict.py": 0.572, "./webapps/schedule/tests/test_copy_schedule.py": 0.212, "./webapps/schedule/tests/test_freeze_working_hours.py": 0.7209999999999999, "./webapps/schedule/tests/test_legacy_hours_serializer.py": 0.086, "./webapps/schedule/tests/test_notifications.py": 0.284, "./webapps/schedule/tests/test_schedule_ports.py": 0.5429999999999999, "./webapps/schedule/tests/test_schedule_serializers.py": 0.165, "./webapps/schedule/tests/test_schedule_tools.py": 0.025, "./webapps/schedule/tests/test_slots_expressions.py": 0.241, "./webapps/schedule/tests/test_working_hours.py": 1.892, "./webapps/script_runner/performance_scripts/tests/test_script_create_consent_consents.py": 2.919, "./webapps/script_runner/performance_scripts/tests/test_script_create_consent_forms.py": 0.072, "./webapps/script_runner/performance_scripts/tests/test_script_create_customer_users__us.py": 1.912, "./webapps/script_runner/performance_scripts/tests/test_script_create_reviews__all.py": 3.871, "./webapps/script_runner/tests/test_delete_scripts.py": 0.077, "./webapps/script_runner/tests/test_dependencies.py": 0.028, "./webapps/script_runner/tests/test_execute_scripts.py": 0.16099999999999998, "./webapps/script_runner/tests/test_execute_scripts_dependencies.py": 0.048, "./webapps/script_runner/tests/test_set_billing_permissions.py": 0.08299999999999999, "./webapps/script_runner/tests_scripts/test_add_neighborhood.py": 0.033, "./webapps/script_runner/tests_scripts/test_mark_deleted_stripe_customers.py": 0.141, "./webapps/script_runner/tests_scripts/test_script___remove_invoicing_errors_no_source__all.py": 0.036, "./webapps/script_runner/tests_scripts/test_script__assign_dummy_buyer_to_test_businesses__pl.py": 0.496, "./webapps/script_runner/tests_scripts/test_script_add_tax_group_for_guyane__fr.py": 0.033, "./webapps/script_runner/tests_scripts/test_script_add_tax_group_for_monaco__fr.py": 0.033, "./webapps/script_runner/tests_scripts/test_script_change_null_tax_rate_to_default_for_services__fr.py": 0.199, "./webapps/script_runner/tests_scripts/test_script_deleted_category_clean_services__all.py": 0.213, "./webapps/script_runner/tests_scripts/test_script_disable_pos_options__fr.py": 0.449, "./webapps/script_runner/tests_scripts/test_script_disable_square_integration.py": 0.132, "./webapps/script_runner/tests_scripts/test_script_enable_physiotheraphy_for_existing_businesses.py": 0.526, "./webapps/script_runner/tests_scripts/test_script_fill_basket_info_in_fiscal_receipt__fr.py": 0.223, "./webapps/script_runner/tests_scripts/test_script_fix_cancellation_fee_synchro.py": 0.07300000*********, "./webapps/script_runner/tests_scripts/test_script_fix_variants_without_series.py": 0.219, "./webapps/script_runner/tests_scripts/test_script_reassign_missing_paymentrow_intents.py": 0.077, "./webapps/script_runner/tests_scripts/test_script_upload_category_icons_v2__all.py": 0.068, "./webapps/script_runner/tests_scripts/test_script_zero_out_voucher_template_tax_rate.py": 4.75, "./webapps/search_engine_tuning/tests/business/test_boost_initial_value.py": 0.066, "./webapps/search_engine_tuning/tests/business/test_conversion.py": 0.163, "./webapps/search_engine_tuning/tests/business/test_return_rate.py": 1.5299999999999998, "./webapps/search_engine_tuning/tests/business/test_reviews_rating.py": 5.686, "./webapps/search_engine_tuning/tests/business/test_secondary_category.py": 1.627, "./webapps/search_engine_tuning/tests/business/test_service_promotions_profitability.py": 1.007, "./webapps/search_engine_tuning/tests/business/test_utt_score.py": 2.202, "./webapps/search_engine_tuning/tests/business_categories/test_last_x_bookings.py": 1.6749999999999998, "./webapps/search_engine_tuning/tests/test_addon_tuning.py": 0.428, "./webapps/search_engine_tuning/tests/test_business_customer_tuning.py": 0.252, "./webapps/search_engine_tuning/tests/test_commodity_tuning.py": 1.389, "./webapps/search_engine_tuning/tests/test_conversion_analysis_report.py": 8.205, "./webapps/search_engine_tuning/tests/test_service_tuning.py": 1.953, "./webapps/search_engine_tuning/tests/test_service_variant_tuning.py": 1.4, "./webapps/search_engine_tuning/tests/test_tuning_for_family_and_friends.py": 0.644, "./webapps/search_engine_tuning/tests/test_user_interest_calculator.py": 0.40800000000000003, "./webapps/search_engine_tuning/tests/test_user_tuning.py": 3.266, "./webapps/segment/tests/test_actions.py": 0.133, "./webapps/segment/tests/test_bump_counters.py": 0.153, "./webapps/segment/tests/test_green_merchant_task.py": 0.839, "./webapps/segment/tests/test_location_entered_gtm_task.py": 0.097, "./webapps/segment/tests/test_models.py": 0.08099999999999999, "./webapps/segment/tests/test_run_analytics_task.py": 0.428, "./webapps/segment/tests/test_save_used_app_version_task.py": 0.194, "./webapps/segment/tests/test_serializers.py": 0.063, "./webapps/segment/tests/test_set_timezone_user_field_iterable.py": 0.01, "./webapps/segment/tests/test_tasks.py": 2.1390000000000002, "./webapps/segment/tests/test_utils.py": 1.2449999999999994, "./webapps/segment/tests/test_wam_status_task.py": 0.016, "./webapps/sequencing_number/tests/test_sequencing_record.py": 0.5359999999999999, "./webapps/session/servicers/tests/test_get_business_user_info.py": 0.023, "./webapps/session/servicers/tests/test_session_check.py": 0.01, "./webapps/session/tests/test_client.py": 0.185, "./webapps/session/tests/test_session.py": 4.449, "./webapps/statistics/checks/tests/test_most_reviewed.py": 0.01, "./webapps/statistics/spreadsheets/warehouse/tests/test_warehouse_reports.py": 0.*****************, "./webapps/statistics/tests/test_aggregate.py": 39.472, "./webapps/statistics/tests/test_booking_duration.py": 0.197, "./webapps/statistics/tests/test_bookings_notifications.py": 0.541, "./webapps/statistics/tests/test_happy_hours_notification_target.py": 0.108, "./webapps/statistics/tests/test_min_date.py": 0.255, "./webapps/statistics/tests/test_notifications.py": 0.716, "./webapps/statistics/tests/test_today_performant_revenues.py": 2.594, "./webapps/stats_and_reports/reports/appointment_reports/tests/test_appointments_by_days.py": 1.197, "./webapps/stats_and_reports/reports/appointment_reports/tests/test_appointments_cancellations.py": 0.685, "./webapps/stats_and_reports/reports/appointment_reports/tests/test_appointments_chart.py": 2.0660000000000003, "./webapps/stats_and_reports/reports/appointment_reports/tests/test_appointments_list.py": 1.8479999999999999, "./webapps/stats_and_reports/reports/appointment_reports/tests/test_appointments_summary.py": 2.264, "./webapps/stats_and_reports/reports/appointment_reports/tests/test_categories_services_summary.py": 6.268000000000002, "./webapps/stats_and_reports/reports/cash_flow/tests/test_bcr_transaction_summary.py": 1.064, "./webapps/stats_and_reports/reports/cash_flow/tests/test_cash_register_summary.py": 0.838, "./webapps/stats_and_reports/reports/cash_flow/tests/test_cash_registers_transactions.py": 0.999, "./webapps/stats_and_reports/reports/cash_flow/tests/test_mobile_payments_transactions_summary.py": 1.806, "./webapps/stats_and_reports/reports/cash_flow/tests/test_payment_types_summary.py": 0.939, "./webapps/stats_and_reports/reports/cash_flow/tests/test_square_payments.py": 0.198, "./webapps/stats_and_reports/reports/cash_flow/tests/test_stripe_payout_reports.py": 3.65, "./webapps/stats_and_reports/reports/client_reports/tests/test_client_by_group.py": 0.966, "./webapps/stats_and_reports/reports/client_reports/tests/test_clients_by_hashtag.py": 1.346, "./webapps/stats_and_reports/reports/client_reports/tests/test_clients_by_type.py": 1.09, "./webapps/stats_and_reports/reports/client_reports/tests/test_clients_charts.py": 2.772, "./webapps/stats_and_reports/reports/client_reports/tests/test_clients_list.py": 1.081, "./webapps/stats_and_reports/reports/client_reports/tests/test_clients_no_shows.py": 1.1720000000000002, "./webapps/stats_and_reports/reports/client_reports/tests/test_new_clients.py": 0.883, "./webapps/stats_and_reports/reports/client_reports/tests/test_potential_clients_list.py": 1.634, "./webapps/stats_and_reports/reports/client_reports/tests/test_returning_clients.py": 0.973, "./webapps/stats_and_reports/reports/client_reports/tests/test_sleeping_away_clients.py": 0.887, "./webapps/stats_and_reports/reports/client_reports/tests/test_top_clients.py": 0.348, "./webapps/stats_and_reports/reports/inventory/tests/test_high_rotating_products.py": 0.3980000*********, "./webapps/stats_and_reports/reports/inventory/tests/test_inventory_dashboard_box.py": 2.1, "./webapps/stats_and_reports/reports/inventory/tests/test_low_stock.py": 0.*****************, "./webapps/stats_and_reports/reports/inventory/tests/test_meter.py": 0.34600000000000003, "./webapps/stats_and_reports/reports/inventory/tests/test_stock_consumption_by_booking.py": 0.43600000000000005, "./webapps/stats_and_reports/reports/inventory/tests/test_stock_consumption_summary.py": 0.47200000000000003, "./webapps/stats_and_reports/reports/inventory/tests/test_stock_movement_summary.py": 0.6160000*********, "./webapps/stats_and_reports/reports/inventory/tests/test_stock_on_hand.py": 0.275, "./webapps/stats_and_reports/reports/inventory/tests/test_stock_rotation.py": 1.402, "./webapps/stats_and_reports/reports/inventory/tests/test_stock_value.py": 0.318, "./webapps/stats_and_reports/reports/promotion/tests/test_boost_charts.py": 0.835, "./webapps/stats_and_reports/reports/promotion/tests/test_boost_claims.py": 6.548, "./webapps/stats_and_reports/reports/promotion/tests/test_dashboard_box.py": 0.825, "./webapps/stats_and_reports/reports/promotion/tests/test_dashboard_tables.py": 1.881, "./webapps/stats_and_reports/reports/promotion/tests/test_new_clients_from_boost.py": 1.613, "./webapps/stats_and_reports/reports/promotion/tests/test_special_offers_summary.py": 0.5920000*********, "./webapps/stats_and_reports/reports/revenue/tests/test_appointments_profitability.py": 1.13, "./webapps/stats_and_reports/reports/revenue/tests/test_dashboard_box.py": 0.189, "./webapps/stats_and_reports/reports/revenue/tests/test_discount.py": 0.944, "./webapps/stats_and_reports/reports/revenue/tests/test_membership_summary.py": 2.01, "./webapps/stats_and_reports/reports/revenue/tests/test_memberships_redemptions.py": 0.937, "./webapps/stats_and_reports/reports/revenue/tests/test_outstanding_invoices.py": 0.999, "./webapps/stats_and_reports/reports/revenue/tests/test_packages_redemptions.py": 1.126, "./webapps/stats_and_reports/reports/revenue/tests/test_packages_summary.py": 1.914, "./webapps/stats_and_reports/reports/revenue/tests/test_revenue_charts.py": 1.268, "./webapps/stats_and_reports/reports/revenue/tests/test_revenue_forecast.py": 0.955, "./webapps/stats_and_reports/reports/revenue/tests/test_revenue_stats_commons.py": 0.222, "./webapps/stats_and_reports/reports/revenue/tests/test_staff_revenue_forecast.py": 1.012, "./webapps/stats_and_reports/reports/revenue/tests/test_total_revenue.py": 0.886, "./webapps/stats_and_reports/reports/sales_reports/tests/test_sales_by_giftcards.py": 5.672000*********, "./webapps/stats_and_reports/reports/sales_reports/tests/test_sales_by_memberships.py": 3.6340000000000003, "./webapps/stats_and_reports/reports/sales_reports/tests/test_sales_by_packages.py": 3.675, "./webapps/stats_and_reports/reports/sales_reports/tests/test_sales_by_products.py": 2.847, "./webapps/stats_and_reports/reports/sales_reports/tests/test_sales_by_services.py": 3.****************, "./webapps/stats_and_reports/reports/sales_reports/tests/test_sales_log.py": 2.2569999999999997, "./webapps/stats_and_reports/reports/sales_reports/tests/test_sales_summary.py": 0.939, "./webapps/stats_and_reports/reports/sales_reports/tests/test_sales_trends.py": 2.855, "./webapps/stats_and_reports/reports/sales_reports/tests/test_taxes_summary.py": 2.9**********00003, "./webapps/stats_and_reports/reports/staff/staff_performance_per_staffer/tests/test_appointments_log.py": 3.9290000000000003, "./webapps/stats_and_reports/reports/staff/staff_performance_per_staffer/tests/test_commissions_n_sales_by_type.py": 3.7109999999999994, "./webapps/stats_and_reports/reports/staff/staff_performance_per_staffer/tests/test_detailed_reviews.py": 2.076, "./webapps/stats_and_reports/reports/staff/staff_performance_per_staffer/tests/test_general.py": 5.2299999999999995, "./webapps/stats_and_reports/reports/staff/staff_performance_per_staffer/tests/test_queries.py": 1.2560000000000002, "./webapps/stats_and_reports/reports/staff/staff_performance_per_staffer/tests/test_report.py": 0.769, "./webapps/stats_and_reports/reports/staff/staff_performance_per_staffer/tests/test_reviews_overview.py": 1.792, "./webapps/stats_and_reports/reports/staff/staff_performance_per_staffer/tests/test_sales_n_commissions_by_product.py": 0.982, "./webapps/stats_and_reports/reports/staff/staff_performance_per_staffer/tests/test_staffer_appointments_overview.py": 5.768999999999999, "./webapps/stats_and_reports/reports/staff/staff_performance_per_staffer/tests/test_staffer_time_distribution.py": 0.273, "./webapps/stats_and_reports/reports/staff/staff_performance_per_staffer/tests/test_staffer_timeoffs.py": 1.0699999999999998, "./webapps/stats_and_reports/reports/staff/tests/test_dashboard_sections.py": 2.269, "./webapps/stats_and_reports/reports/staff/tests/test_staff_commission_details.py": 4.019, "./webapps/stats_and_reports/reports/staff/tests/test_staff_gratuity.py": 0.952, "./webapps/stats_and_reports/reports/staff/tests/test_staff_revenue_payment_methods.py": 1.926, "./webapps/stats_and_reports/reports/staff/tests/test_staff_time_offs.py": 0.377, "./webapps/stats_and_reports/reports/staff/tests/test_staff_working_hours.py": 0.627, "./webapps/stats_and_reports/reports/staff/tests/test_staff_working_hours_dataframes.py": 0.243, "./webapps/stats_and_reports/reports/staff/tests/test_staffer_commission_summary.py": 0.677, "./webapps/stats_and_reports/reports/staff/tests/test_staffer_perfomance_summarry.py": 1.633, "./webapps/stats_and_reports/reports/tests/test_common_stats.py": 0.633, "./webapps/stats_and_reports/reports/tests/test_dashboards.py": 0.061, "./webapps/stats_and_reports/reports/tests/test_fields.py": 0.*****************, "./webapps/stats_and_reports/reports/tests/test_get_spreadsheet.py": 34.779999999999994, "./webapps/stats_and_reports/reports/tests/test_main_dashboard.py": 0.7680000*********, "./webapps/stats_and_reports/reports/tests/test_simple_stats.py": 3.007, "./webapps/stats_and_reports/reports/tests/test_time_functions.py": 0.28400000000000003, "./webapps/stats_and_reports/reports/tests/test_utils.py": 0.5250000*********, "./webapps/stats_and_reports/tests/test_commission_stats.py": 36.81599999999998, "./webapps/stats_and_reports/tests/test_data_scope.py": 0.036, "./webapps/stats_and_reports/tests/test_handlers.py": 13.489000000000003, "./webapps/stats_and_reports/tests/test_helper_mixin_queryset.py": 0.131, "./webapps/stats_and_reports/tests/test_querysets.py": 5.452999999999999, "./webapps/stats_and_reports/tests/test_report_metadata_api.py": 0.706, "./webapps/stats_and_reports/tests/test_reports.py": 34.543999999999976, "./webapps/stats_and_reports/tests/test_serializers.py": 33.53499999999999, "./webapps/stripe_app/tests/apis/test_charge.py": 0.047, "./webapps/stripe_app/tests/apis/test_customer.py": 0.7070000*********, "./webapps/stripe_app/tests/apis/test_payment_method.py": 0.094, "./webapps/stripe_app/tests/interfaces/test_charge.py": 0.091, "./webapps/stripe_app/tests/interfaces/test_customer.py": 0.052000000000000005, "./webapps/stripe_app/tests/interfaces/test_payment_method.py": 0.11800000*********, "./webapps/stripe_app/tests/interfaces/test_refund.py": 0.057999999999999996, "./webapps/stripe_app/tests/services/test_charge.py": 0.8650000000000004, "./webapps/stripe_app/tests/services/test_customer.py": 0.164, "./webapps/stripe_app/tests/services/test_events.py": 0.081, "./webapps/stripe_app/tests/services/test_payment_method.py": 0.****************, "./webapps/stripe_app/tests/services/test_refund.py": 0.4430000*********7, "./webapps/stripe_app/tests/test_admin.py": 1.407, "./webapps/stripe_app/tests/test_events.py": 0.038000000000000006, "./webapps/stripe_app/tests/test_models.py": 0.124, "./webapps/stripe_app/tests/test_tasks.py": 0.034, "./webapps/stripe_app/tests/test_utils.py": 0.*****************, "./webapps/stripe_app/tests/views/test_webhooks.py": 0.067, "./webapps/stripe_integration/tests/test_account.py": 18.032, "./webapps/stripe_integration/tests/test_account_webhooks.py": 0.7790000*********, "./webapps/stripe_integration/tests/test_checkout.py": 4.221, "./webapps/stripe_integration/tests/test_connect_webhooks.py": 5.***************, "./webapps/stripe_integration/tests/test_models.py": 0.369, "./webapps/stripe_integration/tests/test_ports.py": 0.013, "./webapps/stripe_integration/tests/test_provider.py": 0.****************, "./webapps/stripe_integration/tests/test_reader.py": 1.573, "./webapps/stripe_integration/tests/test_serializer.py": 2.****************, "./webapps/stripe_integration/tests/test_synchronize_stripe_customer.py": 2.396, "./webapps/stripe_integration/tests/test_tools.py": 0.676, "./webapps/stripe_terminal/tests/test_models.py": 0.*****************, "./webapps/stripe_terminal/tests/test_serializers.py": 2.****************, "./webapps/stripe_terminal/tests/test_tasks.py": 0.521, "./webapps/stripe_terminal/tests/views/test_hardware.py": 1.****************, "./webapps/stripe_terminal/tests/views/test_order.py": 2.7030000000000003, "./webapps/stripe_terminal/tests/views/test_order_payment.py": 3.2309999999999994, "./webapps/structure/tests/test_edit_region_admin.py": 0.443, "./webapps/structure/tests/test_es_region_british_city_part_one.py": 1.35, "./webapps/structure/tests/test_es_region_british_city_part_two.py": 1.25, "./webapps/structure/tests/test_es_region_multi_type.py": 0.7090000000000002, "./webapps/structure/tests/test_es_region_polish_distinct_city_community.py": 1.057, "./webapps/structure/tests/test_es_region_polish_district_and_city.py": 1.737, "./webapps/structure/tests/test_es_region_polish_state.py": 3.146, "./webapps/structure/tests/test_es_region_state.py": 1.4060000*********, "./webapps/structure/tests/test_models.py": 0.097, "./webapps/structure/tests/test_zipcode_admin.py": 1.963, "./webapps/subdomain_grpc/tests/test_client.py": 0.013, "./webapps/survey/tests/test_customer_poll.py": 0.233, "./webapps/survey/tests/test_poll_details_view.py": 0.421, "./webapps/survey/tests/test_polls_list_view.py": 0.985, "./webapps/survey/tests/test_polls_views.py": 0.8699999999999999, "./webapps/turntracker/tests/test_data.py": 0.01, "./webapps/turntracker/tests/test_finish_turn.py": 0.66, "./webapps/turntracker/tests/test_grid.py": 1.8069999999999997, "./webapps/turntracker/tests/test_grid_revenue_mode.py": 0.30700000000000005, "./webapps/turntracker/tests/test_grid_service_mode.py": 0.*****************, "./webapps/turntracker/tests/test_grid_source.py": 16.087, "./webapps/turntracker/tests/test_grid_staff_mode.py": 1.5150000*********, "./webapps/turntracker/tests/test_models.py": 0.852, "./webapps/turntracker/tests/test_pentiles.py": 1.677, "./webapps/turntracker/tests/test_settings.py": 2.****************, "./webapps/turntracker/tests/test_staff_management.py": 4.585, "./webapps/turntracker/tests/test_turntracker_feature_flag.py": 0.*****************, "./webapps/user/elasticsearch/tests/test_user.py": 3.199, "./webapps/user/servicers/tests/test_user_details.py": 0.01, "./webapps/user/tests/test_account_deletion.py": 3.****************, "./webapps/user/tests/test_apple_logged_user_authorize.py": 0.*****************, "./webapps/user/tests/test_booking_score.py": 1.665, "./webapps/user/tests/test_form.py": 0.044, "./webapps/user/tests/test_gdpr_data_export.py": 0.279, "./webapps/user/tests/test_notifiactions_account_deletion_completed.py": 0.544, "./webapps/user/tests/test_notifications.py": 8.139000*********, "./webapps/user/tests/test_profile_tasks.py": 0.038, "./webapps/user/tests/test_serializers.py": 1.23, "./webapps/user/tests/test_tasks.py": 3.****************, "./webapps/user/tests/test_test_user_marking.py": 0.*****************, "./webapps/user/tests/test_user_endpoints.py": 0.117, "./webapps/user/tests/test_user_model.py": 9.605, "./webapps/user/tests/test_user_search_model.py": 0.*****************, "./webapps/user/tests/test_utils.py": 39.**************, "./webapps/utt/tests/test_assign_secondary_categories.py": 3.861, "./webapps/utt/tests/test_predictions_confirmation.py": 0.809, "./webapps/utt/tests/test_predictor_run.py": 0.391, "./webapps/utt/tests/test_predictors_update.py": 1.****************, "./webapps/versum_migration/importer/tests/test_importer.py": 0.038, "./webapps/versum_migration/management/tests/test_regenerate_versum_pos_settings.py": 0.665, "./webapps/versum_migration/subscription/tests/test_information_filler.py": 1.875, "./webapps/versum_migration/user_connection/tests/test_customer_registration_completed_task.py": 0.194, "./webapps/versum_migration/user_connection/tests/test_existing_users_connector.py": 0.114, "./webapps/versum_migration/user_connection/tests/test_missing_versum_branches_connector.py": 0.352, "./webapps/versum_migration/user_connection/tests/test_schedule_appointments_sync_task.py": 0.014, "./webapps/versum_migration/user_connection/tests/test_user_connector.py": 0.184, "./webapps/versum_migration/user_connection/tests/test_user_synchronizer.py": 0.203, "./webapps/voucher/management/commands/tests/test_business_voucher_orders.py": 0.465, "./webapps/voucher/management/commands/tests/test_fill_sold_vouchers_before_migration_threshold.py": 0.9839999999999999, "./webapps/voucher/management/commands/tests/test_fix_invalid_voucher_template_services.py": 0.288, "./webapps/voucher/management/commands/tests/test_migrate_voucher_templates_to_same_value_and_price.py": 0.5349999999999999, "./webapps/voucher/management/commands/tests/test_modify_voucher_template_available_valid_till_values.py": 0.625, "./webapps/voucher/management/commands/tests/test_modify_voucher_template_valid_till_to_never.py": 0.321, "./webapps/voucher/management/commands/tests/test_set_all_services_voucher_templates.py": 0.4, "./webapps/voucher/tests/test_enums.py": 0.01, "./webapps/voucher/tests/test_helpers.py": 1.066, "./webapps/voucher/tests/test_migrate_vouchers_templates_to_same_value_and_price.py": 0.39, "./webapps/voucher/tests/test_notifications.py": 0.187, "./webapps/voucher/tests/test_paying_with_voucher.py": 37.895999999999994, "./webapps/voucher/tests/test_selling_voucher.py": 8.851, "./webapps/voucher/tests/test_serializers.py": 6.188, "./webapps/voucher/tests/test_tasks.py": 2.899, "./webapps/voucher/tests/test_views.py": 28.062999999999988, "./webapps/voucher/tests/test_voucher_migration_config.py": 0.038000000000000006, "./webapps/voucher/tests/test_voucher_model.py": 52.17000000000002, "./webapps/voucher/tests/test_voucher_order.py": 0.893, "./webapps/voucher/tests/test_voucher_templates.py": 10.055000*********, "./webapps/wait_list/tests/test_assign_waitlist_to_bookings.py": 0.652, "./webapps/wait_list/tests/test_waitlist.py": 0.9380000*********, "./webapps/wait_list/tests/test_waitlist_tools.py": 32.54600000000002, "./webapps/warehouse/tests/test_commodity.py": 1.981, "./webapps/warehouse/tests/test_commodity_serializers.py": 1.107, "./webapps/warehouse/tests/test_documents.py": 1.5150000000000003, "./webapps/warehouse/tests/test_quick_order_factory_tests.py": 1.065, "./webapps/warehouse/tests/test_serializers.py": 10.060000000000004, "./webapps/warehouse/tests/test_supply.py": 0.7939999999999999, "./webapps/warehouse/tests/test_warehouse.py": 0.114, "./webapps/warehouse/tests/test_wholesaler_commodities_loader.py": 0.318, "./webapps/zoom/tests/test_models.py": 3.0210000000000004, "./webapps/zoom/tests/test_tasks.py": 0.235}