// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

// BOOKSY CHANGE
package booksy.pubsub.contrib;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
// BOOKSY CHANGE
import "booksy/pubsub/contrib/schema.proto";

option cc_enable_arenas = true;
option csharp_namespace = "Google.Cloud.PubSub.V1";
option go_package = "google.golang.org/genproto/googleapis/pubsub/v1;pubsub";
option java_multiple_files = true;
option java_outer_classname = "PubsubProto";
option java_package = "com.google.pubsub.v1";
option php_namespace = "Google\\Cloud\\PubSub\\V1";
option ruby_package = "Google::Cloud::PubSub::V1";

// The service that an application uses to manipulate topics, and to send
// messages to a topic.
service Publisher {
  option (google.api.default_host) = "pubsub.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/pubsub";

  // Creates the given topic with the given name. See the [resource name rules]
  // (https://cloud.google.com/pubsub/docs/admin#resource_names).
  rpc CreateTopic(Topic) returns (Topic) {
    option (google.api.http) = {
      put: "/v1/{name=projects/*/topics/*}"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates an existing topic. Note that certain properties of a
  // topic are not modifiable.
  rpc UpdateTopic(UpdateTopicRequest) returns (Topic) {
    option (google.api.http) = {
      patch: "/v1/{topic.name=projects/*/topics/*}"
      body: "*"
    };
  }

  // Adds one or more messages to the topic. Returns `NOT_FOUND` if the topic
  // does not exist.
  rpc Publish(PublishRequest) returns (PublishResponse) {
    option (google.api.http) = {
      post: "/v1/{topic=projects/*/topics/*}:publish"
      body: "*"
    };
    option (google.api.method_signature) = "topic,messages";
  }

  // Gets the configuration of a topic.
  rpc GetTopic(GetTopicRequest) returns (Topic) {
    option (google.api.http) = {
      get: "/v1/{topic=projects/*/topics/*}"
    };
    option (google.api.method_signature) = "topic";
  }

  // Lists matching topics.
  rpc ListTopics(ListTopicsRequest) returns (ListTopicsResponse) {
    option (google.api.http) = {
      get: "/v1/{project=projects/*}/topics"
    };
    option (google.api.method_signature) = "project";
  }

  // Lists the names of the attached subscriptions on this topic.
  rpc ListTopicSubscriptions(ListTopicSubscriptionsRequest)
      returns (ListTopicSubscriptionsResponse) {
    option (google.api.http) = {
      get: "/v1/{topic=projects/*/topics/*}/subscriptions"
    };
    option (google.api.method_signature) = "topic";
  }

  // Lists the names of the snapshots on this topic. Snapshots are used in
  // [Seek](https://cloud.google.com/pubsub/docs/replay-overview) operations,
  // which allow you to manage message acknowledgments in bulk. That is, you can
  // set the acknowledgment state of messages in an existing subscription to the
  // state captured by a snapshot.
  rpc ListTopicSnapshots(ListTopicSnapshotsRequest)
      returns (ListTopicSnapshotsResponse) {
    option (google.api.http) = {
      get: "/v1/{topic=projects/*/topics/*}/snapshots"
    };
    option (google.api.method_signature) = "topic";
  }

  // Deletes the topic with the given name. Returns `NOT_FOUND` if the topic
  // does not exist. After a topic is deleted, a new topic may be created with
  // the same name; this is an entirely new topic with none of the old
  // configuration or subscriptions. Existing subscriptions to this topic are
  // not deleted, but their `topic` field is set to `_deleted-topic_`.
  rpc DeleteTopic(DeleteTopicRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{topic=projects/*/topics/*}"
    };
    option (google.api.method_signature) = "topic";
  }

  // Detaches a subscription from this topic. All messages retained in the
  // subscription are dropped. Subsequent `Pull` and `StreamingPull` requests
  // will return FAILED_PRECONDITION. If the subscription is a push
  // subscription, pushes to the endpoint will stop.
  rpc DetachSubscription(DetachSubscriptionRequest)
      returns (DetachSubscriptionResponse) {
    option (google.api.http) = {
      post: "/v1/{subscription=projects/*/subscriptions/*}:detach"
    };
  }
}

// A policy constraining the storage of messages published to the topic.
message MessageStoragePolicy {
  // A list of IDs of GCP regions where messages that are published to the topic
  // may be persisted in storage. Messages published by publishers running in
  // non-allowed GCP regions (or running outside of GCP altogether) will be
  // routed for storage in one of the allowed regions. An empty list means that
  // no regions are allowed, and is not a valid configuration.
  repeated string allowed_persistence_regions = 1;
}

// Settings for validating messages published against a schema.
message SchemaSettings {
  // Required. The name of the schema that messages published should be
  // validated against. Format is `projects/{project}/schemas/{schema}`. The
  // value of this field will be `_deleted-schema_` if the schema has been
  // deleted.
  string schema = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "pubsub.googleapis.com/Schema" }
  ];

  // The encoding of messages validated against `schema`.
  Encoding encoding = 2;
}

// A topic resource.
message Topic {
  option (google.api.resource) = {
    type: "pubsub.googleapis.com/Topic"
    pattern: "projects/{project}/topics/{topic}"
    pattern: "_deleted-topic_"
  };

  // Required. The name of the topic. It must have the format
  // `"projects/{project}/topics/{topic}"`. `{topic}` must start with a letter,
  // and contain only letters (`[A-Za-z]`), numbers (`[0-9]`), dashes (`-`),
  // underscores (`_`), periods (`.`), tildes (`~`), plus (`+`) or percent
  // signs (`%`). It must be between 3 and 255 characters in length, and it
  // must not start with `"goog"`.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // See [Creating and managing labels]
  // (https://cloud.google.com/pubsub/docs/labels).
  map<string, string> labels = 2;

  // Policy constraining the set of Google Cloud Platform regions where messages
  // published to the topic may be stored. If not present, then no constraints
  // are in effect.
  MessageStoragePolicy message_storage_policy = 3;

  // The resource name of the Cloud KMS CryptoKey to be used to protect access
  // to messages published on this topic.
  //
  // The expected format is `projects/*/locations/*/keyRings/*/cryptoKeys/*`.
  string kms_key_name = 5;

  // Settings for validating messages published against a schema.
  SchemaSettings schema_settings = 6;

  // Reserved for future use. This field is set only in responses from the
  // server; it is ignored if it is set in any requests.
  bool satisfies_pzs = 7;

  // Indicates the minimum duration to retain a message after it is published to
  // the topic. If this field is set, messages published to the topic in the
  // last `message_retention_duration` are always available to subscribers. For
  // instance, it allows any attached subscription to [seek to a
  // timestamp](https://cloud.google.com/pubsub/docs/replay-overview#seek_to_a_time)
  // that is up to `message_retention_duration` in the past. If this field is
  // not set, message retention is controlled by settings on individual
  // subscriptions. Cannot be more than 7 days or less than 10 minutes.
  google.protobuf.Duration message_retention_duration = 8;
}

// A message that is published by publishers and consumed by subscribers. The
// message must contain either a non-empty data field or at least one attribute.
// Note that client libraries represent this object differently
// depending on the language. See the corresponding [client library
// documentation](https://cloud.google.com/pubsub/docs/reference/libraries) for
// more information. See [quotas and limits]
// (https://cloud.google.com/pubsub/quotas) for more information about message
// limits.
message PubsubMessage {
  // The message data field. If this field is empty, the message must contain
  // at least one attribute.
  bytes data = 1;

  // Attributes for this message. If this field is empty, the message must
  // contain non-empty data. This can be used to filter messages on the
  // subscription.
  map<string, string> attributes = 2;

  // ID of this message, assigned by the server when the message is published.
  // Guaranteed to be unique within the topic. This value may be read by a
  // subscriber that receives a `PubsubMessage` via a `Pull` call or a push
  // delivery. It must not be populated by the publisher in a `Publish` call.
  string message_id = 3;

  // The time at which the message was published, populated by the server when
  // it receives the `Publish` call. It must not be populated by the
  // publisher in a `Publish` call.
  google.protobuf.Timestamp publish_time = 4;

  // If non-empty, identifies related messages for which publish order should be
  // respected. If a `Subscription` has `enable_message_ordering` set to `true`,
  // messages published with the same non-empty `ordering_key` value will be
  // delivered to subscribers in the order in which they are received by the
  // Pub/Sub system. All `PubsubMessage`s published in a given `PublishRequest`
  // must specify the same `ordering_key` value.
  string ordering_key = 5;
}

// Request for the GetTopic method.
message GetTopicRequest {
  // Required. The name of the topic to get.
  // Format is `projects/{project}/topics/{topic}`.
  string topic = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "pubsub.googleapis.com/Topic" }
  ];
}

// Request for the UpdateTopic method.
message UpdateTopicRequest {
  // Required. The updated topic object.
  Topic topic = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Indicates which fields in the provided topic to update. Must be
  // specified and non-empty. Note that if `update_mask` contains
  // "message_storage_policy" but the `message_storage_policy` is not set in
  // the `topic` provided above, then the updated value is determined by the
  // policy configured at the project or organization level.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request for the Publish method.
message PublishRequest {
  // Required. The messages in the request will be published on this topic.
  // Format is `projects/{project}/topics/{topic}`.
  string topic = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "pubsub.googleapis.com/Topic" }
  ];

  // Required. The messages to publish.
  repeated PubsubMessage messages = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response for the `Publish` method.
message PublishResponse {
  // The server-assigned ID of each published message, in the same order as
  // the messages in the request. IDs are guaranteed to be unique within
  // the topic.
  repeated string message_ids = 1;
}

// Request for the `ListTopics` method.
message ListTopicsRequest {
  // Required. The name of the project in which to list topics.
  // Format is `projects/{project-id}`.
  string project = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Maximum number of topics to return.
  int32 page_size = 2;

  // The value returned by the last `ListTopicsResponse`; indicates that this is
  // a continuation of a prior `ListTopics` call, and that the system should
  // return the next page of data.
  string page_token = 3;
}

// Response for the `ListTopics` method.
message ListTopicsResponse {
  // The resulting topics.
  repeated Topic topics = 1;

  // If not empty, indicates that there may be more topics that match the
  // request; this value should be passed in a new `ListTopicsRequest`.
  string next_page_token = 2;
}

// Request for the `ListTopicSubscriptions` method.
message ListTopicSubscriptionsRequest {
  // Required. The name of the topic that subscriptions are attached to.
  // Format is `projects/{project}/topics/{topic}`.
  string topic = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "pubsub.googleapis.com/Topic" }
  ];

  // Maximum number of subscription names to return.
  int32 page_size = 2;

  // The value returned by the last `ListTopicSubscriptionsResponse`; indicates
  // that this is a continuation of a prior `ListTopicSubscriptions` call, and
  // that the system should return the next page of data.
  string page_token = 3;
}

// Response for the `ListTopicSubscriptions` method.
message ListTopicSubscriptionsResponse {
  // The names of subscriptions attached to the topic specified in the request.
  repeated string subscriptions = 1 [(google.api.resource_reference) = {
    type: "pubsub.googleapis.com/Subscription"
  }];

  // If not empty, indicates that there may be more subscriptions that match
  // the request; this value should be passed in a new
  // `ListTopicSubscriptionsRequest` to get more subscriptions.
  string next_page_token = 2;
}

// Request for the `ListTopicSnapshots` method.
message ListTopicSnapshotsRequest {
  // Required. The name of the topic that snapshots are attached to.
  // Format is `projects/{project}/topics/{topic}`.
  string topic = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "pubsub.googleapis.com/Topic" }
  ];

  // Maximum number of snapshot names to return.
  int32 page_size = 2;

  // The value returned by the last `ListTopicSnapshotsResponse`; indicates
  // that this is a continuation of a prior `ListTopicSnapshots` call, and
  // that the system should return the next page of data.
  string page_token = 3;
}

// Response for the `ListTopicSnapshots` method.
message ListTopicSnapshotsResponse {
  // The names of the snapshots that match the request.
  repeated string snapshots = 1;

  // If not empty, indicates that there may be more snapshots that match
  // the request; this value should be passed in a new
  // `ListTopicSnapshotsRequest` to get more snapshots.
  string next_page_token = 2;
}

// Request for the `DeleteTopic` method.
message DeleteTopicRequest {
  // Required. Name of the topic to delete.
  // Format is `projects/{project}/topics/{topic}`.
  string topic = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "pubsub.googleapis.com/Topic" }
  ];
}

// Request for the DetachSubscription method.
message DetachSubscriptionRequest {
  // Required. The subscription to detach.
  // Format is `projects/{project}/subscriptions/{subscription}`.
  string subscription = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "pubsub.googleapis.com/Subscription"
    }
  ];
}

// Response for the DetachSubscription method.
// Reserved for future use.
message DetachSubscriptionResponse {}

// The service that an application uses to manipulate subscriptions and to
// consume messages from a subscription via the `Pull` method or by
// establishing a bi-directional stream using the `StreamingPull` method.
service Subscriber {
  option (google.api.default_host) = "pubsub.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/pubsub";

  // Creates a subscription to a given topic. See the [resource name rules]
  // (https://cloud.google.com/pubsub/docs/admin#resource_names).
  // If the subscription already exists, returns `ALREADY_EXISTS`.
  // If the corresponding topic doesn't exist, returns `NOT_FOUND`.
  //
  // If the name is not provided in the request, the server will assign a random
  // name for this subscription on the same project as the topic, conforming
  // to the [resource name format]
  // (https://cloud.google.com/pubsub/docs/admin#resource_names). The generated
  // name is populated in the returned Subscription object. Note that for REST
  // API requests, you must specify a name in the request.
  rpc CreateSubscription(Subscription) returns (Subscription) {
    option (google.api.http) = {
      put: "/v1/{name=projects/*/subscriptions/*}"
      body: "*"
    };
    option (google.api.method_signature) =
        "name,topic,push_config,ack_deadline_seconds";
  }

  // Gets the configuration details of a subscription.
  rpc GetSubscription(GetSubscriptionRequest) returns (Subscription) {
    option (google.api.http) = {
      get: "/v1/{subscription=projects/*/subscriptions/*}"
    };
    option (google.api.method_signature) = "subscription";
  }

  // Updates an existing subscription. Note that certain properties of a
  // subscription, such as its topic, are not modifiable.
  rpc UpdateSubscription(UpdateSubscriptionRequest) returns (Subscription) {
    option (google.api.http) = {
      patch: "/v1/{subscription.name=projects/*/subscriptions/*}"
      body: "*"
    };
  }

  // Lists matching subscriptions.
  rpc ListSubscriptions(ListSubscriptionsRequest)
      returns (ListSubscriptionsResponse) {
    option (google.api.http) = {
      get: "/v1/{project=projects/*}/subscriptions"
    };
    option (google.api.method_signature) = "project";
  }

  // Deletes an existing subscription. All messages retained in the subscription
  // are immediately dropped. Calls to `Pull` after deletion will return
  // `NOT_FOUND`. After a subscription is deleted, a new one may be created with
  // the same name, but the new one has no association with the old
  // subscription or its topic unless the same topic is specified.
  rpc DeleteSubscription(DeleteSubscriptionRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{subscription=projects/*/subscriptions/*}"
    };
    option (google.api.method_signature) = "subscription";
  }

  // Modifies the ack deadline for a specific message. This method is useful
  // to indicate that more time is needed to process a message by the
  // subscriber, or to make the message available for redelivery if the
  // processing was interrupted. Note that this does not modify the
  // subscription-level `ackDeadlineSeconds` used for subsequent messages.
  rpc ModifyAckDeadline(ModifyAckDeadlineRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{subscription=projects/*/subscriptions/*}:modifyAckDeadline"
      body: "*"
    };
    option (google.api.method_signature) =
        "subscription,ack_ids,ack_deadline_seconds";
  }

  // Acknowledges the messages associated with the `ack_ids` in the
  // `AcknowledgeRequest`. The Pub/Sub system can remove the relevant messages
  // from the subscription.
  //
  // Acknowledging a message whose ack deadline has expired may succeed,
  // but such a message may be redelivered later. Acknowledging a message more
  // than once will not result in an error.
  rpc Acknowledge(AcknowledgeRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{subscription=projects/*/subscriptions/*}:acknowledge"
      body: "*"
    };
    option (google.api.method_signature) = "subscription,ack_ids";
  }

  // Pulls messages from the server. The server may return `UNAVAILABLE` if
  // there are too many concurrent pull requests pending for the given
  // subscription.
  rpc Pull(PullRequest) returns (PullResponse) {
    option (google.api.http) = {
      post: "/v1/{subscription=projects/*/subscriptions/*}:pull"
      body: "*"
    };
    option (google.api.method_signature) =
        "subscription,return_immediately,max_messages";
    option (google.api.method_signature) = "subscription,max_messages";
  }

  // Establishes a stream with the server, which sends messages down to the
  // client. The client streams acknowledgements and ack deadline modifications
  // back to the server. The server will close the stream and return the status
  // on any error. The server may close the stream with status `UNAVAILABLE` to
  // reassign server-side resources, in which case, the client should
  // re-establish the stream. Flow control can be achieved by configuring the
  // underlying RPC channel.
  rpc StreamingPull(stream StreamingPullRequest)
      returns (stream StreamingPullResponse) {}

  // Modifies the `PushConfig` for a specified subscription.
  //
  // This may be used to change a push subscription to a pull one (signified by
  // an empty `PushConfig`) or vice versa, or change the endpoint URL and other
  // attributes of a push subscription. Messages will accumulate for delivery
  // continuously through the call regardless of changes to the `PushConfig`.
  rpc ModifyPushConfig(ModifyPushConfigRequest)
      returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/v1/{subscription=projects/*/subscriptions/*}:modifyPushConfig"
      body: "*"
    };
    option (google.api.method_signature) = "subscription,push_config";
  }

  // Gets the configuration details of a snapshot. Snapshots are used in
  // <a href="https://cloud.google.com/pubsub/docs/replay-overview">Seek</a>
  // operations, which allow you to manage message acknowledgments in bulk. That
  // is, you can set the acknowledgment state of messages in an existing
  // subscription to the state captured by a snapshot.
  rpc GetSnapshot(GetSnapshotRequest) returns (Snapshot) {
    option (google.api.http) = {
      get: "/v1/{snapshot=projects/*/snapshots/*}"
    };
    option (google.api.method_signature) = "snapshot";
  }

  // Lists the existing snapshots. Snapshots are used in [Seek](
  // https://cloud.google.com/pubsub/docs/replay-overview) operations, which
  // allow you to manage message acknowledgments in bulk. That is, you can set
  // the acknowledgment state of messages in an existing subscription to the
  // state captured by a snapshot.
  rpc ListSnapshots(ListSnapshotsRequest) returns (ListSnapshotsResponse) {
    option (google.api.http) = {
      get: "/v1/{project=projects/*}/snapshots"
    };
    option (google.api.method_signature) = "project";
  }

  // Creates a snapshot from the requested subscription. Snapshots are used in
  // [Seek](https://cloud.google.com/pubsub/docs/replay-overview) operations,
  // which allow you to manage message acknowledgments in bulk. That is, you can
  // set the acknowledgment state of messages in an existing subscription to the
  // state captured by a snapshot.
  // If the snapshot already exists, returns `ALREADY_EXISTS`.
  // If the requested subscription doesn't exist, returns `NOT_FOUND`.
  // If the backlog in the subscription is too old -- and the resulting snapshot
  // would expire in less than 1 hour -- then `FAILED_PRECONDITION` is returned.
  // See also the `Snapshot.expire_time` field. If the name is not provided in
  // the request, the server will assign a random
  // name for this snapshot on the same project as the subscription, conforming
  // to the [resource name format]
  // (https://cloud.google.com/pubsub/docs/admin#resource_names). The
  // generated name is populated in the returned Snapshot object. Note that for
  // REST API requests, you must specify a name in the request.
  rpc CreateSnapshot(CreateSnapshotRequest) returns (Snapshot) {
    option (google.api.http) = {
      put: "/v1/{name=projects/*/snapshots/*}"
      body: "*"
    };
    option (google.api.method_signature) = "name,subscription";
  }

  // Updates an existing snapshot. Snapshots are used in
  // <a href="https://cloud.google.com/pubsub/docs/replay-overview">Seek</a>
  // operations, which allow
  // you to manage message acknowledgments in bulk. That is, you can set the
  // acknowledgment state of messages in an existing subscription to the state
  // captured by a snapshot.
  rpc UpdateSnapshot(UpdateSnapshotRequest) returns (Snapshot) {
    option (google.api.http) = {
      patch: "/v1/{snapshot.name=projects/*/snapshots/*}"
      body: "*"
    };
  }

  // Removes an existing snapshot. Snapshots are used in [Seek]
  // (https://cloud.google.com/pubsub/docs/replay-overview) operations, which
  // allow you to manage message acknowledgments in bulk. That is, you can set
  // the acknowledgment state of messages in an existing subscription to the
  // state captured by a snapshot.
  // When the snapshot is deleted, all messages retained in the snapshot
  // are immediately dropped. After a snapshot is deleted, a new one may be
  // created with the same name, but the new one has no association with the old
  // snapshot or its subscription, unless the same subscription is specified.
  rpc DeleteSnapshot(DeleteSnapshotRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/v1/{snapshot=projects/*/snapshots/*}"
    };
    option (google.api.method_signature) = "snapshot";
  }

  // Seeks an existing subscription to a point in time or to a given snapshot,
  // whichever is provided in the request. Snapshots are used in [Seek]
  // (https://cloud.google.com/pubsub/docs/replay-overview) operations, which
  // allow you to manage message acknowledgments in bulk. That is, you can set
  // the acknowledgment state of messages in an existing subscription to the
  // state captured by a snapshot. Note that both the subscription and the
  // snapshot must be on the same topic.
  rpc Seek(SeekRequest) returns (SeekResponse) {
    option (google.api.http) = {
      post: "/v1/{subscription=projects/*/subscriptions/*}:seek"
      body: "*"
    };
  }
}

// A subscription resource.
message Subscription {
  option (google.api.resource) = {
    type: "pubsub.googleapis.com/Subscription"
    pattern: "projects/{project}/subscriptions/{subscription}"
  };

  // Possible states for a subscription.
  enum State {
    // Default value. This value is unused.
    STATE_UNSPECIFIED = 0;

    // The subscription can actively receive messages
    ACTIVE = 1;

    // The subscription cannot receive messages because of an error with the
    // resource to which it pushes messages. See the more detailed error state
    // in the corresponding configuration.
    RESOURCE_ERROR = 2;
  }

  // Required. The name of the subscription. It must have the format
  // `"projects/{project}/subscriptions/{subscription}"`. `{subscription}` must
  // start with a letter, and contain only letters (`[A-Za-z]`), numbers
  // (`[0-9]`), dashes (`-`), underscores (`_`), periods (`.`), tildes (`~`),
  // plus (`+`) or percent signs (`%`). It must be between 3 and 255 characters
  // in length, and it must not start with `"goog"`.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The name of the topic from which this subscription is receiving
  // messages. Format is `projects/{project}/topics/{topic}`. The value of this
  // field will be `_deleted-topic_` if the topic has been deleted.
  string topic = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "pubsub.googleapis.com/Topic" }
  ];

  // If push delivery is used with this subscription, this field is
  // used to configure it. Either `pushConfig` or `bigQueryConfig` can be set,
  // but not both. If both are empty, then the subscriber will pull and ack
  // messages using API methods.
  PushConfig push_config = 4;

  // If delivery to BigQuery is used with this subscription, this field is
  // used to configure it. Either `pushConfig` or `bigQueryConfig` can be set,
  // but not both. If both are empty, then the subscriber will pull and ack
  // messages using API methods.
  BigQueryConfig bigquery_config = 18;

  // The approximate amount of time (on a best-effort basis) Pub/Sub waits for
  // the subscriber to acknowledge receipt before resending the message. In the
  // interval after the message is delivered and before it is acknowledged, it
  // is considered to be <i>outstanding</i>. During that time period, the
  // message will not be redelivered (on a best-effort basis).
  //
  // For pull subscriptions, this value is used as the initial value for the ack
  // deadline. To override this value for a given message, call
  // `ModifyAckDeadline` with the corresponding `ack_id` if using
  // non-streaming pull or send the `ack_id` in a
  // `StreamingModifyAckDeadlineRequest` if using streaming pull.
  // The minimum custom deadline you can specify is 10 seconds.
  // The maximum custom deadline you can specify is 600 seconds (10 minutes).
  // If this parameter is 0, a default value of 10 seconds is used.
  //
  // For push delivery, this value is also used to set the request timeout for
  // the call to the push endpoint.
  //
  // If the subscriber never acknowledges the message, the Pub/Sub
  // system will eventually redeliver the message.
  int32 ack_deadline_seconds = 5;

  // Indicates whether to retain acknowledged messages. If true, then
  // messages are not expunged from the subscription's backlog, even if they are
  // acknowledged, until they fall out of the `message_retention_duration`
  // window. This must be true if you would like to [`Seek` to a timestamp]
  // (https://cloud.google.com/pubsub/docs/replay-overview#seek_to_a_time) in
  // the past to replay previously-acknowledged messages.
  bool retain_acked_messages = 7;

  // How long to retain unacknowledged messages in the subscription's backlog,
  // from the moment a message is published.
  // If `retain_acked_messages` is true, then this also configures the retention
  // of acknowledged messages, and thus configures how far back in time a `Seek`
  // can be done. Defaults to 7 days. Cannot be more than 7 days or less than 10
  // minutes.
  google.protobuf.Duration message_retention_duration = 8;

  // See <a href="https://cloud.google.com/pubsub/docs/labels"> Creating and
  // managing labels</a>.
  map<string, string> labels = 9;

  // If true, messages published with the same `ordering_key` in `PubsubMessage`
  // will be delivered to the subscribers in the order in which they
  // are received by the Pub/Sub system. Otherwise, they may be delivered in
  // any order.
  bool enable_message_ordering = 10;

  // A policy that specifies the conditions for this subscription's expiration.
  // A subscription is considered active as long as any connected subscriber is
  // successfully consuming messages from the subscription or is issuing
  // operations on the subscription. If `expiration_policy` is not set, a
  // *default policy* with `ttl` of 31 days will be used. The minimum allowed
  // value for `expiration_policy.ttl` is 1 day.
  ExpirationPolicy expiration_policy = 11;

  // An expression written in the Pub/Sub [filter
  // language](https://cloud.google.com/pubsub/docs/filtering). If non-empty,
  // then only `PubsubMessage`s whose `attributes` field matches the filter are
  // delivered on this subscription. If empty, then no messages are filtered
  // out.
  string filter = 12;

  // A policy that specifies the conditions for dead lettering messages in
  // this subscription. If dead_letter_policy is not set, dead lettering
  // is disabled.
  //
  // The Cloud Pub/Sub service account associated with this subscriptions's
  // parent project (i.e.,
  // service-{project_number}@gcp-sa-pubsub.iam.gserviceaccount.com) must have
  // permission to Acknowledge() messages on this subscription.
  DeadLetterPolicy dead_letter_policy = 13;

  // A policy that specifies how Pub/Sub retries message delivery for this
  // subscription.
  //
  // If not set, the default retry policy is applied. This generally implies
  // that messages will be retried as soon as possible for healthy subscribers.
  // RetryPolicy will be triggered on NACKs or acknowledgement deadline
  // exceeded events for a given message.
  RetryPolicy retry_policy = 14;

  // Indicates whether the subscription is detached from its topic. Detached
  // subscriptions don't receive messages from their topic and don't retain any
  // backlog. `Pull` and `StreamingPull` requests will return
  // FAILED_PRECONDITION. If the subscription is a push subscription, pushes to
  // the endpoint will not be made.
  bool detached = 15;

  // If true, Pub/Sub provides the following guarantees for the delivery of
  // a message with a given value of `message_id` on this subscription:
  //
  // * The message sent to a subscriber is guaranteed not to be resent
  // before the message's acknowledgement deadline expires.
  // * An acknowledged message will not be resent to a subscriber.
  //
  // Note that subscribers may still receive multiple copies of a message
  // when `enable_exactly_once_delivery` is true if the message was published
  // multiple times by a publisher client. These copies are  considered distinct
  // by Pub/Sub and have distinct `message_id` values.
  bool enable_exactly_once_delivery = 16;

  // Output only. Indicates the minimum duration for which a message is retained
  // after it is published to the subscription's topic. If this field is set,
  // messages published to the subscription's topic in the last
  // `topic_message_retention_duration` are always available to subscribers. See
  // the `message_retention_duration` field in `Topic`. This field is set only
  // in responses from the server; it is ignored if it is set in any requests.
  google.protobuf.Duration topic_message_retention_duration = 17
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. An output-only field indicating whether or not the subscription can receive
  // messages.
  State state = 19 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A policy that specifies how Cloud Pub/Sub retries message delivery.
//
// Retry delay will be exponential based on provided minimum and maximum
// backoffs. https://en.wikipedia.org/wiki/Exponential_backoff.
//
// RetryPolicy will be triggered on NACKs or acknowledgement deadline exceeded
// events for a given message.
//
// Retry Policy is implemented on a best effort basis. At times, the delay
// between consecutive deliveries may not match the configuration. That is,
// delay can be more or less than configured backoff.
message RetryPolicy {
  // The minimum delay between consecutive deliveries of a given message.
  // Value should be between 0 and 600 seconds. Defaults to 10 seconds.
  google.protobuf.Duration minimum_backoff = 1;

  // The maximum delay between consecutive deliveries of a given message.
  // Value should be between 0 and 600 seconds. Defaults to 600 seconds.
  google.protobuf.Duration maximum_backoff = 2;
}

// Dead lettering is done on a best effort basis. The same message might be
// dead lettered multiple times.
//
// If validation on any of the fields fails at subscription creation/updation,
// the create/update subscription request will fail.
message DeadLetterPolicy {
  // The name of the topic to which dead letter messages should be published.
  // Format is `projects/{project}/topics/{topic}`.The Cloud Pub/Sub service
  // account associated with the enclosing subscription's parent project (i.e.,
  // service-{project_number}@gcp-sa-pubsub.iam.gserviceaccount.com) must have
  // permission to Publish() to this topic.
  //
  // The operation will fail if the topic does not exist.
  // Users should ensure that there is a subscription attached to this topic
  // since messages published to a topic with no subscriptions are lost.
  string dead_letter_topic = 1;

  // The maximum number of delivery attempts for any message. The value must be
  // between 5 and 100.
  //
  // The number of delivery attempts is defined as 1 + (the sum of number of
  // NACKs and number of times the acknowledgement deadline has been exceeded
  // for the message).
  //
  // A NACK is any call to ModifyAckDeadline with a 0 deadline. Note that
  // client libraries may automatically extend ack_deadlines.
  //
  // This field will be honored on a best effort basis.
  //
  // If this parameter is 0, a default value of 5 is used.
  int32 max_delivery_attempts = 2;
}

// A policy that specifies the conditions for resource expiration (i.e.,
// automatic resource deletion).
message ExpirationPolicy {
  // Specifies the "time-to-live" duration for an associated resource. The
  // resource expires if it is not active for a period of `ttl`. The definition
  // of "activity" depends on the type of the associated resource. The minimum
  // and maximum allowed values for `ttl` depend on the type of the associated
  // resource, as well. If `ttl` is not set, the associated resource never
  // expires.
  google.protobuf.Duration ttl = 1;
}

// Configuration for a push delivery endpoint.
message PushConfig {
  // Contains information needed for generating an
  // [OpenID Connect
  // token](https://developers.google.com/identity/protocols/OpenIDConnect).
  message OidcToken {
    // [Service account
    // email](https://cloud.google.com/iam/docs/service-accounts)
    // to be used for generating the OIDC token. The caller (for
    // CreateSubscription, UpdateSubscription, and ModifyPushConfig RPCs) must
    // have the iam.serviceAccounts.actAs permission for the service account.
    string service_account_email = 1;

    // Audience to be used when generating OIDC token. The audience claim
    // identifies the recipients that the JWT is intended for. The audience
    // value is a single case-sensitive string. Having multiple values (array)
    // for the audience field is not supported. More info about the OIDC JWT
    // token audience here: https://tools.ietf.org/html/rfc7519#section-4.1.3
    // Note: if not specified, the Push endpoint URL will be used.
    string audience = 2;
  }

  // A URL locating the endpoint to which messages should be pushed.
  // For example, a Webhook endpoint might use `https://example.com/push`.
  string push_endpoint = 1;

  // Endpoint configuration attributes that can be used to control different
  // aspects of the message delivery.
  //
  // The only currently supported attribute is `x-goog-version`, which you can
  // use to change the format of the pushed message. This attribute
  // indicates the version of the data expected by the endpoint. This
  // controls the shape of the pushed message (i.e., its fields and metadata).
  //
  // If not present during the `CreateSubscription` call, it will default to
  // the version of the Pub/Sub API used to make such call. If not present in a
  // `ModifyPushConfig` call, its value will not be changed. `GetSubscription`
  // calls will always return a valid version, even if the subscription was
  // created without this attribute.
  //
  // The only supported values for the `x-goog-version` attribute are:
  //
  // * `v1beta1`: uses the push format defined in the v1beta1 Pub/Sub API.
  // * `v1` or `v1beta2`: uses the push format defined in the v1 Pub/Sub API.
  //
  // For example:
  // <pre><code>attributes { "x-goog-version": "v1" } </code></pre>
  map<string, string> attributes = 2;

  // An authentication method used by push endpoints to verify the source of
  // push requests. This can be used with push endpoints that are private by
  // default to allow requests only from the Cloud Pub/Sub system, for example.
  // This field is optional and should be set only by users interested in
  // authenticated push.
  oneof authentication_method {
    // If specified, Pub/Sub will generate and attach an OIDC JWT token as an
    // `Authorization` header in the HTTP request for every pushed message.
    OidcToken oidc_token = 3;
  }
}

// Configuration for a BigQuery subscription.
message BigQueryConfig {
  // Possible states for a BigQuery subscription.
  enum State {
    // Default value. This value is unused.
    STATE_UNSPECIFIED = 0;

    // The subscription can actively send messages to BigQuery
    ACTIVE = 1;

    // Cannot write to the BigQuery table because of permission denied errors.
    PERMISSION_DENIED = 2;

    // Cannot write to the BigQuery table because it does not exist.
    NOT_FOUND = 3;

    // Cannot write to the BigQuery table due to a schema mismatch.
    SCHEMA_MISMATCH = 4;
  }

  // The name of the table to which to write data, of the form
  // {projectId}:{datasetId}.{tableId}
  string table = 1;

  // When true, use the topic's schema as the columns to write to in BigQuery,
  // if it exists.
  bool use_topic_schema = 2;

  // When true, write the subscription name, message_id, publish_time,
  // attributes, and ordering_key to additional columns in the table. The
  // subscription name, message_id, and publish_time fields are put in their own
  // columns while all other message properties (other than data) are written to
  // a JSON object in the attributes column.
  bool write_metadata = 3;

  // When true and use_topic_schema is true, any fields that are a part of the
  // topic schema that are not part of the BigQuery table schema are dropped
  // when writing to BigQuery. Otherwise, the schemas must be kept in sync and
  // any messages with extra fields are not written and remain in the
  // subscription's backlog.
  bool drop_unknown_fields = 4;

  // Output only. An output-only field that indicates whether or not the subscription can
  // receive messages.
  State state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// A message and its corresponding acknowledgment ID.
message ReceivedMessage {
  // This ID can be used to acknowledge the received message.
  string ack_id = 1;

  // The message.
  PubsubMessage message = 2;

  // The approximate number of times that Cloud Pub/Sub has attempted to deliver
  // the associated message to a subscriber.
  //
  // More precisely, this is 1 + (number of NACKs) +
  // (number of ack_deadline exceeds) for this message.
  //
  // A NACK is any call to ModifyAckDeadline with a 0 deadline. An ack_deadline
  // exceeds event is whenever a message is not acknowledged within
  // ack_deadline. Note that ack_deadline is initially
  // Subscription.ackDeadlineSeconds, but may get extended automatically by
  // the client library.
  //
  // Upon the first delivery of a given message, `delivery_attempt` will have a
  // value of 1. The value is calculated at best effort and is approximate.
  //
  // If a DeadLetterPolicy is not set on the subscription, this will be 0.
  int32 delivery_attempt = 3;
}

// Request for the GetSubscription method.
message GetSubscriptionRequest {
  // Required. The name of the subscription to get.
  // Format is `projects/{project}/subscriptions/{sub}`.
  string subscription = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "pubsub.googleapis.com/Subscription"
    }
  ];
}

// Request for the UpdateSubscription method.
message UpdateSubscriptionRequest {
  // Required. The updated subscription object.
  Subscription subscription = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Indicates which fields in the provided subscription to update.
  // Must be specified and non-empty.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// Request for the `ListSubscriptions` method.
message ListSubscriptionsRequest {
  // Required. The name of the project in which to list subscriptions.
  // Format is `projects/{project-id}`.
  string project = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Maximum number of subscriptions to return.
  int32 page_size = 2;

  // The value returned by the last `ListSubscriptionsResponse`; indicates that
  // this is a continuation of a prior `ListSubscriptions` call, and that the
  // system should return the next page of data.
  string page_token = 3;
}

// Response for the `ListSubscriptions` method.
message ListSubscriptionsResponse {
  // The subscriptions that match the request.
  repeated Subscription subscriptions = 1;

  // If not empty, indicates that there may be more subscriptions that match
  // the request; this value should be passed in a new
  // `ListSubscriptionsRequest` to get more subscriptions.
  string next_page_token = 2;
}

// Request for the DeleteSubscription method.
message DeleteSubscriptionRequest {
  // Required. The subscription to delete.
  // Format is `projects/{project}/subscriptions/{sub}`.
  string subscription = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "pubsub.googleapis.com/Subscription"
    }
  ];
}

// Request for the ModifyPushConfig method.
message ModifyPushConfigRequest {
  // Required. The name of the subscription.
  // Format is `projects/{project}/subscriptions/{sub}`.
  string subscription = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "pubsub.googleapis.com/Subscription"
    }
  ];

  // Required. The push configuration for future deliveries.
  //
  // An empty `pushConfig` indicates that the Pub/Sub system should
  // stop pushing messages from the given subscription and allow
  // messages to be pulled and acknowledged - effectively pausing
  // the subscription if `Pull` or `StreamingPull` is not called.
  PushConfig push_config = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for the `Pull` method.
message PullRequest {
  // Required. The subscription from which messages should be pulled.
  // Format is `projects/{project}/subscriptions/{sub}`.
  string subscription = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "pubsub.googleapis.com/Subscription"
    }
  ];

  // Optional. If this field set to true, the system will respond immediately
  // even if it there are no messages available to return in the `Pull`
  // response. Otherwise, the system may wait (for a bounded amount of time)
  // until at least one message is available, rather than returning no messages.
  // Warning: setting this field to `true` is discouraged because it adversely
  // impacts the performance of `Pull` operations. We recommend that users do
  // not set this field.
  bool return_immediately = 2
      [deprecated = true, (google.api.field_behavior) = OPTIONAL];

  // Required. The maximum number of messages to return for this request. Must
  // be a positive integer. The Pub/Sub system may return fewer than the number
  // specified.
  int32 max_messages = 3 [(google.api.field_behavior) = REQUIRED];
}

// Response for the `Pull` method.
message PullResponse {
  // Received Pub/Sub messages. The list will be empty if there are no more
  // messages available in the backlog. For JSON, the response can be entirely
  // empty. The Pub/Sub system may return fewer than the `maxMessages` requested
  // even if there are more messages available in the backlog.
  repeated ReceivedMessage received_messages = 1;
}

// Request for the ModifyAckDeadline method.
message ModifyAckDeadlineRequest {
  // Required. The name of the subscription.
  // Format is `projects/{project}/subscriptions/{sub}`.
  string subscription = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "pubsub.googleapis.com/Subscription"
    }
  ];

  // Required. List of acknowledgment IDs.
  repeated string ack_ids = 4 [(google.api.field_behavior) = REQUIRED];

  // Required. The new ack deadline with respect to the time this request was
  // sent to the Pub/Sub system. For example, if the value is 10, the new ack
  // deadline will expire 10 seconds after the `ModifyAckDeadline` call was
  // made. Specifying zero might immediately make the message available for
  // delivery to another subscriber client. This typically results in an
  // increase in the rate of message redeliveries (that is, duplicates).
  // The minimum deadline you can specify is 0 seconds.
  // The maximum deadline you can specify is 600 seconds (10 minutes).
  int32 ack_deadline_seconds = 3 [(google.api.field_behavior) = REQUIRED];
}

// Request for the Acknowledge method.
message AcknowledgeRequest {
  // Required. The subscription whose message is being acknowledged.
  // Format is `projects/{project}/subscriptions/{sub}`.
  string subscription = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "pubsub.googleapis.com/Subscription"
    }
  ];

  // Required. The acknowledgment ID for the messages being acknowledged that
  // was returned by the Pub/Sub system in the `Pull` response. Must not be
  // empty.
  repeated string ack_ids = 2 [(google.api.field_behavior) = REQUIRED];
}

// Request for the `StreamingPull` streaming RPC method. This request is used to
// establish the initial stream as well as to stream acknowledgements and ack
// deadline modifications from the client to the server.
message StreamingPullRequest {
  // Required. The subscription for which to initialize the new stream. This
  // must be provided in the first request on the stream, and must not be set in
  // subsequent requests from client to server.
  // Format is `projects/{project}/subscriptions/{sub}`.
  string subscription = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "pubsub.googleapis.com/Subscription"
    }
  ];

  // List of acknowledgement IDs for acknowledging previously received messages
  // (received on this stream or a different stream). If an ack ID has expired,
  // the corresponding message may be redelivered later. Acknowledging a message
  // more than once will not result in an error. If the acknowledgement ID is
  // malformed, the stream will be aborted with status `INVALID_ARGUMENT`.
  repeated string ack_ids = 2;

  // The list of new ack deadlines for the IDs listed in
  // `modify_deadline_ack_ids`. The size of this list must be the same as the
  // size of `modify_deadline_ack_ids`. If it differs the stream will be aborted
  // with `INVALID_ARGUMENT`. Each element in this list is applied to the
  // element in the same position in `modify_deadline_ack_ids`. The new ack
  // deadline is with respect to the time this request was sent to the Pub/Sub
  // system. Must be >= 0. For example, if the value is 10, the new ack deadline
  // will expire 10 seconds after this request is received. If the value is 0,
  // the message is immediately made available for another streaming or
  // non-streaming pull request. If the value is < 0 (an error), the stream will
  // be aborted with status `INVALID_ARGUMENT`.
  repeated int32 modify_deadline_seconds = 3;

  // List of acknowledgement IDs whose deadline will be modified based on the
  // corresponding element in `modify_deadline_seconds`. This field can be used
  // to indicate that more time is needed to process a message by the
  // subscriber, or to make the message available for redelivery if the
  // processing was interrupted.
  repeated string modify_deadline_ack_ids = 4;

  // Required. The ack deadline to use for the stream. This must be provided in
  // the first request on the stream, but it can also be updated on subsequent
  // requests from client to server. The minimum deadline you can specify is 10
  // seconds. The maximum deadline you can specify is 600 seconds (10 minutes).
  int32 stream_ack_deadline_seconds = 5
      [(google.api.field_behavior) = REQUIRED];

  // A unique identifier that is used to distinguish client instances from each
  // other. Only needs to be provided on the initial request. When a stream
  // disconnects and reconnects for the same stream, the client_id should be set
  // to the same value so that state associated with the old stream can be
  // transferred to the new stream. The same client_id should not be used for
  // different client instances.
  string client_id = 6;

  // Flow control settings for the maximum number of outstanding messages. When
  // there are `max_outstanding_messages` or more currently sent to the
  // streaming pull client that have not yet been acked or nacked, the server
  // stops sending more messages. The sending of messages resumes once the
  // number of outstanding messages is less than this value. If the value is
  // <= 0, there is no limit to the number of outstanding messages. This
  // property can only be set on the initial StreamingPullRequest. If it is set
  // on a subsequent request, the stream will be aborted with status
  // `INVALID_ARGUMENT`.
  int64 max_outstanding_messages = 7;

  // Flow control settings for the maximum number of outstanding bytes. When
  // there are `max_outstanding_bytes` or more worth of messages currently sent
  // to the streaming pull client that have not yet been acked or nacked, the
  // server will stop sending more messages. The sending of messages resumes
  // once the number of outstanding bytes is less than this value. If the value
  // is <= 0, there is no limit to the number of outstanding bytes. This
  // property can only be set on the initial StreamingPullRequest. If it is set
  // on a subsequent request, the stream will be aborted with status
  // `INVALID_ARGUMENT`.
  int64 max_outstanding_bytes = 8;
}

// Response for the `StreamingPull` method. This response is used to stream
// messages from the server to the client.
message StreamingPullResponse {
  // Acknowledgement IDs sent in one or more previous requests to acknowledge a
  // previously received message.
  message AcknowledgeConfirmation {
    // Successfully processed acknowledgement IDs.
    repeated string ack_ids = 1 [ctype = CORD];

    // List of acknowledgement IDs that were malformed or whose acknowledgement
    // deadline has expired.
    repeated string invalid_ack_ids = 2 [ctype = CORD];

    // List of acknowledgement IDs that were out of order.
    repeated string unordered_ack_ids = 3 [ctype = CORD];
  }

  // Acknowledgement IDs sent in one or more previous requests to modify the
  // deadline for a specific message.
  message ModifyAckDeadlineConfirmation {
    // Successfully processed acknowledgement IDs.
    repeated string ack_ids = 1 [ctype = CORD];

    // List of acknowledgement IDs that were malformed or whose acknowledgement
    // deadline has expired.
    repeated string invalid_ack_ids = 2 [ctype = CORD];
  }

  // Subscription properties sent as part of the response.
  message SubscriptionProperties {
    // True iff exactly once delivery is enabled for this subscription.
    bool exactly_once_delivery_enabled = 1;
    // True iff message ordering is enabled for this subscription.
    bool message_ordering_enabled = 2;
  }

  // Received Pub/Sub messages. This will not be empty.
  repeated ReceivedMessage received_messages = 1;

  reserved 2;

  // This field will only be set if `enable_exactly_once_delivery` is set to
  // `true`.
  AcknowledgeConfirmation acknowledge_confirmation = 5;

  // This field will only be set if `enable_exactly_once_delivery` is set to
  // `true`.
  ModifyAckDeadlineConfirmation modify_ack_deadline_confirmation = 3;

  // Properties associated with this subscription.
  SubscriptionProperties subscription_properties = 4;
}

// Request for the `CreateSnapshot` method.
message CreateSnapshotRequest {
  // Required. User-provided name for this snapshot. If the name is not provided
  // in the request, the server will assign a random name for this snapshot on
  // the same project as the subscription. Note that for REST API requests, you
  // must specify a name.  See the <a
  // href="https://cloud.google.com/pubsub/docs/admin#resource_names"> resource
  // name rules</a>. Format is `projects/{project}/snapshots/{snap}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "pubsub.googleapis.com/Snapshot" }
  ];

  // Required. The subscription whose backlog the snapshot retains.
  // Specifically, the created snapshot is guaranteed to retain:
  //  (a) The existing backlog on the subscription. More precisely, this is
  //      defined as the messages in the subscription's backlog that are
  //      unacknowledged upon the successful completion of the
  //      `CreateSnapshot` request; as well as:
  //  (b) Any messages published to the subscription's topic following the
  //      successful completion of the CreateSnapshot request.
  // Format is `projects/{project}/subscriptions/{sub}`.
  string subscription = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "pubsub.googleapis.com/Subscription"
    }
  ];

  // See <a href="https://cloud.google.com/pubsub/docs/labels"> Creating and
  // managing labels</a>.
  map<string, string> labels = 3;
}

// Request for the UpdateSnapshot method.
message UpdateSnapshotRequest {
  // Required. The updated snapshot object.
  Snapshot snapshot = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Indicates which fields in the provided snapshot to update.
  // Must be specified and non-empty.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// A snapshot resource. Snapshots are used in
// [Seek](https://cloud.google.com/pubsub/docs/replay-overview)
// operations, which allow you to manage message acknowledgments in bulk. That
// is, you can set the acknowledgment state of messages in an existing
// subscription to the state captured by a snapshot.
message Snapshot {
  option (google.api.resource) = {
    type: "pubsub.googleapis.com/Snapshot"
    pattern: "projects/{project}/snapshots/{snapshot}"
  };

  // The name of the snapshot.
  string name = 1;

  // The name of the topic from which this snapshot is retaining messages.
  string topic = 2 [
    (google.api.resource_reference) = { type: "pubsub.googleapis.com/Topic" }
  ];

  // The snapshot is guaranteed to exist up until this time.
  // A newly-created snapshot expires no later than 7 days from the time of its
  // creation. Its exact lifetime is determined at creation by the existing
  // backlog in the source subscription. Specifically, the lifetime of the
  // snapshot is `7 days - (age of oldest unacked message in the subscription)`.
  // For example, consider a subscription whose oldest unacked message is 3 days
  // old. If a snapshot is created from this subscription, the snapshot -- which
  // will always capture this 3-day-old backlog as long as the snapshot
  // exists -- will expire in 4 days. The service will refuse to create a
  // snapshot that would expire in less than 1 hour after creation.
  google.protobuf.Timestamp expire_time = 3;

  // See [Creating and managing labels]
  // (https://cloud.google.com/pubsub/docs/labels).
  map<string, string> labels = 4;
}

// Request for the GetSnapshot method.
message GetSnapshotRequest {
  // Required. The name of the snapshot to get.
  // Format is `projects/{project}/snapshots/{snap}`.
  string snapshot = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "pubsub.googleapis.com/Snapshot" }
  ];
}

// Request for the `ListSnapshots` method.
message ListSnapshotsRequest {
  // Required. The name of the project in which to list snapshots.
  // Format is `projects/{project-id}`.
  string project = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Project"
    }
  ];

  // Maximum number of snapshots to return.
  int32 page_size = 2;

  // The value returned by the last `ListSnapshotsResponse`; indicates that this
  // is a continuation of a prior `ListSnapshots` call, and that the system
  // should return the next page of data.
  string page_token = 3;
}

// Response for the `ListSnapshots` method.
message ListSnapshotsResponse {
  // The resulting snapshots.
  repeated Snapshot snapshots = 1;

  // If not empty, indicates that there may be more snapshot that match the
  // request; this value should be passed in a new `ListSnapshotsRequest`.
  string next_page_token = 2;
}

// Request for the `DeleteSnapshot` method.
message DeleteSnapshotRequest {
  // Required. The name of the snapshot to delete.
  // Format is `projects/{project}/snapshots/{snap}`.
  string snapshot = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "pubsub.googleapis.com/Snapshot" }
  ];
}

// Request for the `Seek` method.
message SeekRequest {
  // Required. The subscription to affect.
  string subscription = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "pubsub.googleapis.com/Subscription"
    }
  ];

  oneof target {
    // The time to seek to.
    // Messages retained in the subscription that were published before this
    // time are marked as acknowledged, and messages retained in the
    // subscription that were published after this time are marked as
    // unacknowledged. Note that this operation affects only those messages
    // retained in the subscription (configured by the combination of
    // `message_retention_duration` and `retain_acked_messages`). For example,
    // if `time` corresponds to a point before the message retention
    // window (or to a point before the system's notion of the subscription
    // creation time), only retained messages will be marked as unacknowledged,
    // and already-expunged messages will not be restored.
    google.protobuf.Timestamp time = 2;

    // The snapshot to seek to. The snapshot's topic must be the same as that of
    // the provided subscription.
    // Format is `projects/{project}/snapshots/{snap}`.
    string snapshot = 3 [(google.api.resource_reference) = {
      type: "pubsub.googleapis.com/Snapshot"
    }];
  }
}

// Response for the `Seek` method (this response is empty).
message SeekResponse {}
