from dataclasses import dataclass
from datetime import datetime

from webapps.onboarding_space.domain.enums import (
    ProfileSetupStepsEnum,
    CompletionColorsEnum,
    BusinessCategoryEnum,
    CountryCode,
)


@dataclass(frozen=True)
class BusinessContext:
    business_category: BusinessCategoryEnum
    country_code: CountryCode


@dataclass(frozen=True)
class Step:
    type: ProfileSetupStepsEnum
    value: bool | int


@dataclass(frozen=True)
class ColorCodedTextDTO:
    field_text: str
    color: CompletionColorsEnum


@dataclass(frozen=True)
class OnboardingSpaceParamsDTO:
    active_from: datetime
    steps: list[Step]
    import_and_invite_count: int


class OnboardingSpaceProgress:
    """Domain model of an onboarding space progress.

    It is not created on domain layer. Instead, Django model is created by OnboardingSpaceRepository
    and domain model is used for field updates.
    """

    steps_defaults = {
        ProfileSetupStepsEnum.ADD_COVER_PHOTO: False,
        ProfileSetupStepsEnum.ADD_PORTFOLIO: 0,
        ProfileSetupStepsEnum.ADD_SERVICES: 0,
        ProfileSetupStepsEnum.IMPORT_AND_INVITE: 0,
        ProfileSetupStepsEnum.SHARE_PROFILE: False,
        ProfileSetupStepsEnum.CREATE_POST: 0,
    }

    def __init__(
        self,
        params: OnboardingSpaceParamsDTO,
        _id: int | None = None,
    ):
        self.id = _id
        self.active_from = params.active_from
        self.steps = params.steps
        self.import_and_invite_count = params.import_and_invite_count

    def __repr__(self):
        return f'OnboardingSpaceProgress(business_id={self.id})'

    @classmethod
    def create(cls, active_from: datetime, business_id: int | None = None):
        return cls(
            params=OnboardingSpaceParamsDTO(
                active_from=active_from, steps=cls._get_default_steps(), import_and_invite_count=0
            ),
            _id=business_id,
        )

    @classmethod
    def _get_default_steps(cls) -> list[Step]:
        return [
            Step(type=step, value=default_value)
            for step, default_value in cls.steps_defaults.items()
        ]
