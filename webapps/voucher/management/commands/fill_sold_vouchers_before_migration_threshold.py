from django.core.management import BaseCommand

from lib.tools import chunker
from webapps.voucher.enums import VoucherType
from webapps.voucher.models import VoucherTemplate
from webapps.voucher.tasks import fill_sold_vouchers_before_migration_threshold_task


class Command(BaseCommand):
    def add_arguments(self, parser):
        parser.add_argument('--async', action='store_true')
        parser.add_argument('--dry-run', action='store_true')

    def handle(self, *args, **opts):
        pos_ids = (
            VoucherTemplate.all_objects.filter(
                type=VoucherType.EGIFT_CARD,
            )
            .values_list('pos_id', flat=True)
            .distinct()
        )

        for pos_ids_chunk in chunker(pos_ids, 100):
            if opts.get('async'):
                fill_sold_vouchers_before_migration_threshold_task.delay(
                    pos_ids=pos_ids_chunk,
                    dry_run=opts.get('dry_run'),
                )
            else:
                fill_sold_vouchers_before_migration_threshold_task.run(
                    pos_ids=pos_ids_chunk,
                    dry_run=opts.get('dry_run'),
                )
