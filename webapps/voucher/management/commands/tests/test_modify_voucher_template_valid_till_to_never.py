from itertools import cycle

from dateutil.relativedelta import relativedelta
from django.test import TestCase
from model_bakery import baker
from parameterized import parameterized

from lib.tools import tznow
from webapps.business.baker_recipes import (
    business_recipe,
)
from webapps.pos.baker_recipes import pos_recipe
from webapps.voucher.baker_recipes import voucher_template_recipe, egift_card_template_recipe
from webapps.voucher.enums import OrderingVoucherTemplateValidTill
from webapps.voucher.management.commands.modify_voucher_template_valid_till_to_never import (
    Command,
)
from webapps.voucher.models import (
    VoucherOrder,
    Voucher,
    VoucherTemplate,
)


class TestChangeValidTillVoucherTemplateCommand(TestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        business = business_recipe.make()
        cls.pos = pos_recipe.make(business=business)

    def test_set_valid_till_to_never(self):
        valid_till_old_periods = (
            OrderingVoucherTemplateValidTill.DAYS_30.value,
            OrderingVoucherTemplateValidTill.DAYS_90.value,
            OrderingVoucherTemplateValidTill.END_OF_MONTH.value,
            OrderingVoucherTemplateValidTill.END_OF_YEAR.value,
            OrderingVoucherTemplateValidTill.YEAR_1.value,
            OrderingVoucherTemplateValidTill.MONTHS_24.value,
            OrderingVoucherTemplateValidTill.MONTHS_6.value,
            OrderingVoucherTemplateValidTill.MONTHS_9.value,
            OrderingVoucherTemplateValidTill.YEAR_1.value,
            OrderingVoucherTemplateValidTill.MONTHS_24.value,
        )
        old_voucher_templates = egift_card_template_recipe.make(
            pos=self.pos,
            valid_till=cycle(valid_till_old_periods),
            active=True,
            _quantity=len(valid_till_old_periods),
        )
        Command().handle()

        new_valid_till_list = VoucherTemplate.objects.filter(active=True).values_list(
            'valid_till',
            flat=True,
        )
        assert len(new_valid_till_list) == len(valid_till_old_periods)
        assert set(new_valid_till_list) == {OrderingVoucherTemplateValidTill.UNLIMITED}

        for template in old_voucher_templates:
            template.refresh_from_db()
            assert not template.active

    def test_dont_modify_valid_till(self):
        old_voucher_template = egift_card_template_recipe.make(
            pos=self.pos, valid_till=OrderingVoucherTemplateValidTill.UNLIMITED
        )
        Command().handle()

        old_voucher_template.refresh_from_db()
        assert old_voucher_template.active
        assert old_voucher_template.valid_till == OrderingVoucherTemplateValidTill.UNLIMITED
        assert VoucherTemplate.objects.count() == 1

    def test_change_templates_valid_till_value_and_skip_updating_related_orders_and_vouchers(self):
        voucher_template = egift_card_template_recipe.make(
            pos=self.pos,
            type=Voucher.VOUCHER_TYPE__EGIFT_CARD,
            valid_till=OrderingVoucherTemplateValidTill.DAYS_30.value,
            active=True,
        )
        voucher_order = baker.make(VoucherOrder, voucher_template=voucher_template)
        voucher = baker.make(
            Voucher,
            pos=self.pos,
            voucher_template=voucher_template,
            valid_till=tznow().date() + relativedelta(days=30),
        )
        Command().handle()
        voucher_order.refresh_from_db()
        voucher_template.refresh_from_db()
        voucher.refresh_from_db()

        assert not voucher_template.active
        assert VoucherTemplate.objects.count() == 2
        assert (
            VoucherTemplate.objects.filter(active=True).first().valid_till
            == OrderingVoucherTemplateValidTill.UNLIMITED
        )
        assert voucher_order.voucher_template == voucher_template
        assert voucher.voucher_template == voucher_template
        assert (
            voucher_order.voucher_template.valid_till
            == OrderingVoucherTemplateValidTill.DAYS_30.value
        )
        assert voucher.voucher_template.valid_till == OrderingVoucherTemplateValidTill.DAYS_30.value

    @parameterized.expand(
        (
            (Voucher.VOUCHER_TYPE__PACKAGE,),
            (Voucher.VOUCHER_TYPE__MEMBERSHIP,),
        )
    )
    def test_dont_modify_valid_till_not_gift_card(self, voucher_type):
        voucher = voucher_template_recipe.make(
            pos=self.pos,
            type=voucher_type,
            valid_till=OrderingVoucherTemplateValidTill.DAYS_30.value,
            active=True,
        )
        Command().handle()

        voucher.refresh_from_db()
        assert voucher.active
        assert VoucherTemplate.objects.count() == 1
        assert (
            VoucherTemplate.objects.first().valid_till
            == OrderingVoucherTemplateValidTill.DAYS_30.value
        )
