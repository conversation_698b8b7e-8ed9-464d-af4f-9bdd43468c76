import unittest

import pytest
from dateutil.relativedelta import relativedelta
from django.test import override_settings
from model_bakery import baker

from country_config import Country
from lib.tools import tznow
from webapps.business.enums import PriceType
from webapps.business.models import Business, ServiceVariant
from webapps.pos.models import POS
from webapps.pos.tests import TestCaseWithSetUp
from webapps.voucher.enums import VoucherTemplateStatus
from webapps.voucher.models import (
    Voucher,
    VoucherAdditionalInfo,
    VoucherOrder,
    VoucherTemplate,
    VoucherTemplateServiceVariant,
)
from webapps.voucher.order_serializers import VoucherTemplateForOrderSerializer
from webapps.voucher.serializers import VoucherTemplateDetailSerializer


@pytest.mark.django_db
@pytest.mark.usefixtures('default_voucher_background')
class TestEgiftCertificateTemplateSerializer(TestCaseWithSetUp):
    def setUp(self):
        super().setUp()
        self.services = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
            _quantity=2,
        )

    @staticmethod
    def _get_data(update_data=None):
        data = {
            'type': Voucher.VOUCHER_TYPE__EGIFT_CARD,
            'status': VoucherTemplateStatus.ACTIVE,
            'name': 'Ekstra Karta',
            'description': None,
            'valid_till': VoucherTemplate.DAYS_30,
            'item_price': 100,
            'tax_rate': 12,
            'value': 120,
            'services': [],
            'online_purchase': True,
        }
        if update_data:
            data.update(update_data)
        return data

    def get_serializer(self, data, instance=None):
        serializer = VoucherTemplateDetailSerializer(
            data=data,
            instance=instance,
            context={
                'pos': self.pos,
            },
        )

        serializer.is_valid()
        print(serializer.errors)

        return serializer

    @override_settings(API_COUNTRY=Country.PL)
    def test_valid_till(self):
        """This test checks if egift template will be saved with valid_till."""

        # Try DAYS_30
        data = self._get_data({'valid_till': VoucherTemplate.DAYS_30})
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        # Try DAYS_90
        data = self._get_data({'valid_till': VoucherTemplate.DAYS_90})
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        # Try MONTHS_6
        data = self._get_data({'valid_till': VoucherTemplate.MONTHS_6})
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        # Try YEAR_1
        data = self._get_data({'valid_till': VoucherTemplate.YEAR_1})
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        # Try with end of...
        data = self._get_data({'valid_till': VoucherTemplate.END_OF_MONTH})
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        # Try with end of...
        data = self._get_data({'valid_till': VoucherTemplate.END_OF_MONTH})
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        # Try with unlimited
        data = self._get_data({'valid_till': VoucherTemplate.UNLIMITED})

        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

    @override_settings(API_COUNTRY=Country.PL)
    def test_value(self):
        """Test serializer validation with different 'value' values"""

        # Value is None
        data = self._get_data({'value': None})
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is False
        assert serializer.errors['value'][0].code == 'value_0'

        # Value is 0
        data = self._get_data({'value': 0})
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is False
        assert serializer.errors['value'][0].code == 'value_0'

        # Value is real number
        data = self._get_data({'value': 100})
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        gc_template = serializer.save()
        assert gc_template.value == 100

    @override_settings(API_COUNTRY=Country.PL)
    def test_services(self):
        sv = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )

        sv2 = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )

        # Empty services
        data = self._get_data({'services': []})
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        # Single services
        data = self._get_data(
            {
                'services': [
                    {
                        'service_variant_id': sv.id,
                    }
                ]
            }
        )
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        gc_template = serializer.save()

        assert gc_template.services.count() == 1
        assert gc_template.services.get().service_variant == sv
        assert gc_template.services.get().amount == -1

        # Single services with amount
        data = self._get_data(
            {
                'services': [
                    {
                        'service_variant_id': sv.id,
                        'amount': 122,
                        'item_price': 23,
                    }
                ]
            }
        )
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        gc_template = serializer.save()

        assert gc_template.services.count() == 1
        assert gc_template.services.get().service_variant == sv
        assert gc_template.services.get().amount == -1
        assert gc_template.services.get().item_price is None

        # Multiple services
        data = self._get_data(
            {
                'services': [
                    {
                        'service_variant_id': sv.id,
                    },
                    {
                        'service_variant_id': sv2.id,
                    },
                ]
            }
        )
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        gc_template = serializer.save()
        assert gc_template.services.count() == 2

    @override_settings(API_COUNTRY=Country.FR)
    def test_services_validate_price_and_value(self):
        data = self._get_data(
            {
                'valid_till': VoucherTemplate.YEAR_1,
                'services': [],
            }
        )
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is False

        errors = serializer.errors['item_price']
        assert len(errors) == 1
        error = errors[0]
        assert error.code == 'item_price_same_as_value'
        assert str(error) == 'Item price need to be the same as value'

    @override_settings(API_COUNTRY=Country.US)
    def test_create_selecting_services_disabled_flag_on(self):
        data = self._get_data({'services': [{'service_variant_id': sv.id} for sv in self.services]})
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is False
        assert serializer.errors['services'][0].code == 'selecting_services_not_supported'

    @override_settings(API_COUNTRY=Country.GB)
    def test_create_all_services_selecting_services_disabled_flag_on(self):
        data = self._get_data(
            {
                'valid_till': VoucherTemplate.MONTHS_6,
            }
        )
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True
        gc_template = serializer.save()
        assert gc_template.services.count() == 0

    @override_settings(API_COUNTRY=Country.US)
    def test_edit_services_selecting_services_disabled_flag_on(self):
        gc_template = baker.make(
            VoucherTemplate,
            pos=self.pos,
            type=Voucher.VOUCHER_TYPE__EGIFT_CARD,
            status=VoucherTemplateStatus.ACTIVE,
            online_purchase=True,
        )
        baker.make(
            VoucherTemplateServiceVariant,
            voucher_template=gc_template,
            service_variant=self.services[0],
        )
        data = self._get_data({'services': [{'service_variant_id': sv.id} for sv in self.services]})
        serializer = self.get_serializer(data, gc_template)
        assert serializer.is_valid() is False
        assert serializer.errors['services'][0].code == 'editing_services_not_supported'

    @override_settings(API_COUNTRY=Country.GB)
    def test_edit_old_services_selecting_services_disabled_flag_on(self):
        gc_template = baker.make(
            VoucherTemplate,
            pos=self.pos,
            type=Voucher.VOUCHER_TYPE__EGIFT_CARD,
            status=VoucherTemplateStatus.ACTIVE,
            online_purchase=True,
        )
        baker.make(
            VoucherTemplateServiceVariant,
            voucher_template=gc_template,
            service_variant=self.services[0],
        )
        data = self._get_data(
            {
                'services': [{'service_variant_id': self.services[0].id}],
                'valid_till': VoucherTemplate.MONTHS_6,
            }
        )
        serializer = self.get_serializer(data, gc_template)
        assert serializer.is_valid() is True
        serializer.save()
        assert gc_template.item_price == data['item_price']

    @override_settings(API_COUNTRY=Country.US)
    def test_edit_all_services_selecting_services_disabled_flag_on(self):
        gc_template = baker.make(
            VoucherTemplate,
            pos=self.pos,
            type=Voucher.VOUCHER_TYPE__EGIFT_CARD,
            status=VoucherTemplateStatus.ACTIVE,
            online_purchase=True,
        )
        baker.make(
            VoucherTemplateServiceVariant,
            voucher_template=gc_template,
            service_variant=self.services[0],
        )
        data = self._get_data({'valid_till': VoucherTemplate.MONTHS_6})
        serializer = self.get_serializer(data, gc_template)
        assert serializer.is_valid() is True
        serializer.save()
        assert gc_template.services.count() == 0
        assert gc_template.item_price == data['item_price']

    @override_settings(API_COUNTRY=Country.PL)
    def test_update_sold_vouchers_before_migration_threshold_flag_on(self):
        assert not VoucherAdditionalInfo.objects.filter(pos=self.pos).exists()

        data = self._get_data({})
        serializer = self.get_serializer(data)
        serializer.save()
        assert VoucherAdditionalInfo.objects.get(
            pos=self.pos
        ).sold_vouchers_before_migration_threshold


@pytest.mark.django_db
@pytest.mark.usefixtures('default_voucher_background')
class TestPackageTemplateSerializer(TestCaseWithSetUp):
    def setUp(self):
        super().setUp()

        self.sv = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )

    def get_validity_data(self, valid_till):
        return {
            'type': Voucher.VOUCHER_TYPE__PACKAGE,
            'status': VoucherTemplateStatus.ACTIVE,
            'name': 'Nazwa',
            'description': None,
            'valid_till': valid_till,
            'item_price': 123.43,
            'tax_rate': 12,
            'value': 31,
            'services': [
                {
                    'service_variant_id': self.sv.id,
                    'amount': 12,
                    'item_price': 12,
                }
            ],
            'online_purchase': False,
        }

    def get_serializer(self, data):
        serializer = VoucherTemplateDetailSerializer(
            data=data,
            context={
                'pos': self.pos,
            },
        )

        serializer.is_valid()
        print(serializer.errors)

        return serializer

    def test_valid_till(self):
        """This test checks if package template will be saved with valid_till"""

        # Try DAYS_30
        data = self.get_validity_data(VoucherTemplate.DAYS_30)
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        # Try DAYS_90
        data = self.get_validity_data(VoucherTemplate.DAYS_90)
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        # Try MONTHS_6
        data = self.get_validity_data(VoucherTemplate.MONTHS_6)
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        # Try YEAR_1
        data = self.get_validity_data(VoucherTemplate.YEAR_1)
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        # Try with end of...
        data = self.get_validity_data(VoucherTemplate.END_OF_MONTH)
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        # Try with end of...
        data = self.get_validity_data(VoucherTemplate.END_OF_MONTH)

        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        # Try with unlimited
        data = self.get_validity_data(VoucherTemplate.UNLIMITED)

        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

    def test_value(self):
        """Test serializer validation with different 'value' values"""

        def get_data(value):
            return {
                'type': Voucher.VOUCHER_TYPE__PACKAGE,
                'status': VoucherTemplateStatus.ACTIVE,
                'name': 'Nazwa',
                'description': None,
                'valid_till': VoucherTemplate.DAYS_30,
                'item_price': 123.43,
                'tax_rate': 12,
                'value': value,
                'services': [
                    {
                        'service_variant_id': self.sv.id,
                        'amount': 12,
                        'item_price': 12,
                    }
                ],
                'online_purchase': False,
            }

        # Value is None
        data = get_data(None)
        serializer = self.get_serializer(data)
        assert serializer.is_valid(), serializer.errors

        # Value is 0
        data = get_data(0)
        serializer = self.get_serializer(data)
        assert serializer.is_valid(), serializer.errors

        # Value is real number
        data = get_data(100)
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True
        pg_template = serializer.save()
        assert pg_template.value is None  # value for package is ignored

    def test_services(self):
        def get_data(services):
            return {
                'type': Voucher.VOUCHER_TYPE__PACKAGE,
                'status': VoucherTemplateStatus.ACTIVE,
                'name': 'Nazwa',
                'description': None,
                'valid_till': VoucherTemplate.DAYS_30,
                'item_price': 123.43,
                'tax_rate': 12,
                'value': 120,
                'services': services,
                'online_purchase': False,
            }

        sv = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )

        sv2 = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )

        # Empty services
        data = get_data([])
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is False
        assert serializer.errors['services'][0].code == 'at_least_one_service'

        # Single services
        data = get_data(
            [
                {
                    'service_variant_id': sv.id,
                }
            ]
        )
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is False
        assert serializer.errors['services'][0]['amount'][0].code == 'required'
        assert serializer.errors['services'][0]['item_price'][0].code == 'required'

        # Single services with amount
        data = get_data(
            [
                {
                    'service_variant_id': sv.id,
                    'amount': 122,
                    'item_price': 23,
                }
            ]
        )
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        pg_template = serializer.save()

        assert pg_template.services.count() == 1
        assert pg_template.services.get().service_variant == sv
        assert pg_template.services.get().amount == 122
        assert pg_template.services.get().item_price == 23

        # Single services with amount
        data = get_data(
            [
                {
                    'service_variant_id': sv.id,
                    'amount': -1,
                    'item_price': 23,
                }
            ]
        )
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is False
        assert serializer.errors['services'][0]['amount'][0].code == 'amount_greater_0'

        # Multiple services
        data = get_data(
            [
                {
                    'service_variant_id': sv.id,
                    'amount': 122,
                    'item_price': 23,
                },
                {
                    'service_variant_id': sv2.id,
                    'amount': 2,
                    'item_price': 33,
                },
            ]
        )
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        pg_template = serializer.save()
        assert pg_template.services.count() == 2
        assert pg_template.services.first().service_variant == sv
        assert pg_template.services.first().amount == 122
        assert pg_template.services.first().item_price == 23
        assert pg_template.services.last().service_variant == sv2
        assert pg_template.services.last().amount == 2
        assert pg_template.services.last().item_price == 33

    def test_update_sold_vouchers_before_migration_threshold(self):
        assert not VoucherAdditionalInfo.objects.filter(pos=self.pos).exists()

        data = self.get_validity_data(VoucherTemplate.DAYS_30)
        serializer = self.get_serializer(data)
        serializer.save()

        assert not VoucherAdditionalInfo.objects.filter(pos=self.pos).exists()


@pytest.mark.django_db
@pytest.mark.usefixtures('default_voucher_background')
class TestMembershipTemplateSerializer(TestCaseWithSetUp):
    def setUp(self):
        super().setUp()

        self.sv = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )

    def get_validity_data(self, valid_till):
        return {
            'type': Voucher.VOUCHER_TYPE__MEMBERSHIP,
            'status': VoucherTemplateStatus.ACTIVE,
            'name': 'Nazwa',
            'description': None,
            'valid_till': valid_till,
            'item_price': 123.43,
            'tax_rate': 12,
            'value': 31,
            'services': [
                {
                    'service_variant_id': self.sv.id,
                    'amount': 12,
                    'item_price': 12,
                }
            ],
            'online_purchase': False,
        }

    def get_serializer(self, data):
        serializer = VoucherTemplateDetailSerializer(
            data=data,
            context={
                'pos': self.pos,
            },
        )

        serializer.is_valid()
        print(serializer.errors)

        return serializer

    def test_valid_till(self):
        """This test checks if package template will be saved with valid_till"""

        # Try DAYS_30
        data = self.get_validity_data(VoucherTemplate.DAYS_30)
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        # Try DAYS_90
        data = self.get_validity_data(VoucherTemplate.DAYS_90)
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        # Try MONTHS_6
        data = self.get_validity_data(VoucherTemplate.MONTHS_6)
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        # Try YEAR_1
        data = self.get_validity_data(VoucherTemplate.YEAR_1)
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        # Try with end of...
        data = self.get_validity_data(VoucherTemplate.END_OF_MONTH)
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        # Try with end of...
        data = self.get_validity_data(VoucherTemplate.END_OF_MONTH)
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        # Try with unlimited
        data = self.get_validity_data(VoucherTemplate.UNLIMITED)

        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

    def test_value(self):
        """Test serializer validation with different 'value' values"""

        def get_data(value):
            return {
                'type': Voucher.VOUCHER_TYPE__MEMBERSHIP,
                'status': VoucherTemplateStatus.ACTIVE,
                'name': 'Nazwa',
                'description': None,
                'valid_till': VoucherTemplate.DAYS_30,
                'item_price': 123.43,
                'tax_rate': 12,
                'value': value,
                'services': [
                    {
                        'service_variant_id': self.sv.id,
                    }
                ],
                'online_purchase': False,
            }

        # Value is None
        data = get_data(None)
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        mr_template = serializer.save()
        assert mr_template.value is None

        # Value is 0
        data = get_data(0)
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        mr_template = serializer.save()
        assert mr_template.value is None

        # Value is real number
        data = get_data(100)
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        mr_template = serializer.save()
        assert mr_template.value is None

    def test_services(self):
        def get_data(services):
            return {
                'type': Voucher.VOUCHER_TYPE__MEMBERSHIP,
                'status': VoucherTemplateStatus.ACTIVE,
                'name': 'Nazwa',
                'description': None,
                'valid_till': VoucherTemplate.DAYS_30,
                'item_price': 123.43,
                'tax_rate': 12,
                'value': 120,
                'services': services,
                'online_purchase': False,
            }

        sv = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )

        sv2 = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )

        # Empty services
        data = get_data([])
        serializer = self.get_serializer(data)
        assert serializer.is_valid(), serializer.errors

        # Single services
        data = get_data(
            [
                {
                    'service_variant_id': sv.id,
                }
            ]
        )
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        mr_template = serializer.save()

        assert mr_template.services.count() == 1
        assert mr_template.services.get().service_variant == sv
        assert mr_template.services.get().amount == -1
        assert mr_template.services.get().item_price is None

        # Single services with amount
        data = get_data(
            [
                {
                    'service_variant_id': sv.id,
                    'amount': 122,
                    'item_price': 23,
                }
            ]
        )
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        mr_template = serializer.save()

        assert mr_template.services.count() == 1
        assert mr_template.services.get().service_variant == sv
        assert mr_template.services.get().amount == -1
        assert mr_template.services.get().item_price is None

        # Multiple services
        data = get_data(
            [
                {
                    'service_variant_id': sv.id,
                    'amount': 122,
                    'item_price': 23,
                },
                {
                    'service_variant_id': sv2.id,
                    'amount': 2,
                    'item_price': 33,
                },
            ]
        )
        serializer = self.get_serializer(data)
        assert serializer.is_valid() is True

        mr_template = serializer.save()
        assert mr_template.services.count() == 2
        assert mr_template.services.first().service_variant == sv
        assert mr_template.services.first().amount == -1
        assert mr_template.services.first().item_price is None
        assert mr_template.services.last().service_variant == sv2
        assert mr_template.services.last().amount == -1
        assert mr_template.services.last().item_price is None

    def test_update_sold_vouchers_before_migration_threshold(self):
        assert not VoucherAdditionalInfo.objects.filter(pos=self.pos).exists()

        data = self.get_validity_data(VoucherTemplate.DAYS_30)
        serializer = self.get_serializer(data)
        serializer.save()

        assert not VoucherAdditionalInfo.objects.filter(pos=self.pos).exists()


@pytest.mark.django_db
@pytest.mark.usefixtures('default_voucher_background')
class TestMoveVoucherOrders(unittest.TestCase):
    def setUp(self):
        business = baker.make(Business)
        baker.make(POS, business=business)
        self.voucher_template = baker.make(VoucherTemplate, deleted=tznow(), series=1)

        self.voucher_order = baker.make(VoucherOrder, voucher_template=self.voucher_template)

    def test_create_new(self):
        self.voucher_template2 = baker.make(VoucherTemplate, series=self.voucher_template.series)

        self.voucher_order.refresh_from_db()
        assert self.voucher_order.voucher_template == self.voucher_template2


@pytest.mark.django_db
@pytest.mark.usefixtures('default_voucher_background')
class TestVoucherTemplateForOrderSerializer:
    @pytest.fixture
    def voucher_template(self, default_voucher_background) -> 'VoucherTemplate':
        """Create VoucherTemplate with some background image."""
        image = default_voucher_background
        image.image_url = 'http://image.url/image.jpg'
        return baker.make(
            VoucherTemplate,
            deleted=tznow(),
            series=1,
            background_image=image,
        )

    def test_background_image_url_field(self, voucher_template):
        serializer = VoucherTemplateForOrderSerializer(instance=voucher_template)
        assert serializer.data.get('background_image_url') == 'http://image.url/image.jpg'
