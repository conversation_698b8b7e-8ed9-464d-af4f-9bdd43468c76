# Generated by Django 2.2.10 on 2020-06-01 08:32

import django.db.models.deletion
import django_countries.fields
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ('register', '0010_auto_20190124_0717'),
        ('pos', '0196_auto_20200608_0657'),
        ('business', '0281_add_boost_on_trial_fields'),
    ]

    operations = [
        migrations.CreateModel(
            name='Buyer',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('name', models.CharField(max_length=200)),
                ('address', models.CharField(max_length=200)),
                ('zip_code', models.CharField(max_length=25)),
                ('city', models.CharField(max_length=100)),
                ('country', django_countries.fields.CountryField(max_length=2)),
                ('tax_id_number', models.CharField(blank=True, max_length=50)),
                ('email', models.EmailField(blank=True, max_length=254)),
                (
                    'customer',
                    models.ForeignKey(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name='invoice_buyer_details_set',
                        to='business.BusinessCustomerInfo',
                    ),
                ),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='CashRegisterDocument',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('seller_name', models.CharField(max_length=200)),
                ('seller_tax_id_number', models.CharField(max_length=50)),
                ('seller_address', models.CharField(max_length=200)),
                ('seller_zip_code', models.CharField(max_length=25)),
                ('seller_city', models.CharField(max_length=100)),
                ('seller_country', django_countries.fields.CountryField(max_length=2)),
                ('seller_bank_name', models.CharField(max_length=100)),
                ('seller_account_number', models.CharField(max_length=50)),
                ('buyer_name', models.CharField(max_length=200)),
                ('buyer_tax_id_number', models.CharField(blank=True, max_length=50)),
                ('buyer_address', models.CharField(max_length=200)),
                ('buyer_zip_code', models.CharField(max_length=25)),
                ('buyer_city', models.CharField(max_length=100)),
                ('buyer_country', django_countries.fields.CountryField(max_length=2)),
                ('issue_date', models.DateField(verbose_name='Date of issue')),
                ('number', models.CharField(blank=True, max_length=50, null=True)),
                ('number_in_month', models.IntegerField(blank=True, null=True)),
                (
                    'type',
                    models.CharField(
                        choices=[
                            ('KW', 'Cash register disbursement'),
                            ('KP', 'Cash register receipt'),
                        ],
                        max_length=2,
                    ),
                ),
                ('place_of_issue', models.CharField(blank=True, max_length=50)),
                ('issuing_staffer_name', models.CharField(max_length=50)),
                (
                    'business',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='cash_register_documents',
                        to='business.Business',
                    ),
                ),
                (
                    'buyer',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to='invoicing.Buyer',
                        verbose_name='Deposit from / payout to',
                    ),
                ),
                (
                    'issuing_staffer',
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name='cash_register_documents',
                        to='business.Resource',
                    ),
                ),
                (
                    'register_operation',
                    models.OneToOneField(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name='cash_register_document',
                        to='register.RegisterOperation',
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='CustomerInvoice',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('seller_name', models.CharField(max_length=200)),
                ('seller_tax_id_number', models.CharField(max_length=50)),
                ('seller_address', models.CharField(max_length=200)),
                ('seller_zip_code', models.CharField(max_length=25)),
                ('seller_city', models.CharField(max_length=100)),
                ('seller_country', django_countries.fields.CountryField(max_length=2)),
                ('seller_bank_name', models.CharField(max_length=100)),
                ('seller_account_number', models.CharField(max_length=50)),
                ('buyer_name', models.CharField(max_length=200)),
                ('buyer_tax_id_number', models.CharField(blank=True, max_length=50)),
                ('buyer_address', models.CharField(max_length=200)),
                ('buyer_zip_code', models.CharField(max_length=25)),
                ('buyer_city', models.CharField(max_length=100)),
                ('buyer_country', django_countries.fields.CountryField(max_length=2)),
                ('issue_date', models.DateField(verbose_name='Date of issue')),
                ('number', models.CharField(blank=True, max_length=50, null=True)),
                ('number_in_month', models.IntegerField(blank=True, null=True)),
                ('issuing_staffer_name', models.CharField(max_length=50)),
                ('internal_description', models.CharField(blank=True, max_length=100)),
                ('correction_reason', models.CharField(blank=True, max_length=200)),
                (
                    'sale_date',
                    models.DateField(
                        help_text='Date of making or completing supply of goods or ' 'services',
                        verbose_name='Date of sale',
                    ),
                ),
                ('payment_term', models.DateField(verbose_name='Term of payment')),
                (
                    'payment_method',
                    models.CharField(
                        choices=[
                            ('transfer', 'Bank transfer'),
                            ('cash', 'Cash'),
                            ('credit_card', 'Credit card'),
                        ],
                        max_length=15,
                    ),
                ),
                ('discount_amount', models.IntegerField(blank=True, default=0)),
                ('is_paid', models.BooleanField(default=False)),
                (
                    'business',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='customer_invoices',
                        to='business.Business',
                    ),
                ),
                (
                    'buyer',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to='invoicing.Buyer',
                    ),
                ),
                (
                    'cash_register_document',
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to='invoicing.CashRegisterDocument',
                    ),
                ),
                (
                    'corrected_invoice',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='correcting_invoices_set',
                        to='invoicing.CustomerInvoice',
                    ),
                ),
                (
                    'issuing_staffer',
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name='customer_invoices',
                        to='business.Resource',
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='Seller',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('name', models.CharField(max_length=200)),
                ('address', models.CharField(max_length=200)),
                ('zip_code', models.CharField(max_length=25)),
                ('city', models.CharField(max_length=100)),
                ('country', django_countries.fields.CountryField(max_length=2)),
                ('tax_id_number', models.CharField(max_length=50)),
                ('bank_name', models.CharField(max_length=100)),
                ('account_number', models.CharField(max_length=50)),
                ('invoice_prefix', models.CharField(max_length=20)),
                ('correction_prefix', models.CharField(max_length=20)),
                ('cash_register_receipt_prefix', models.CharField(max_length=20)),
                ('cash_register_disbursement_prefix', models.CharField(max_length=20)),
                (
                    'business',
                    models.OneToOneField(
                        db_constraint=False,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name='invoice_seller_details',
                        to='business.Business',
                    ),
                ),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='InvoiceItem',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('from_corrected', models.BooleanField(default=False)),
                ('name', models.CharField(max_length=250)),
                ('quantity', models.IntegerField(default=1)),
                ('unit_symbol', models.CharField(max_length=10)),
                ('net_unit_price', models.IntegerField()),
                (
                    'tax_rate',
                    models.DecimalField(blank=True, decimal_places=2, max_digits=4, null=True),
                ),
                ('total_net_value', models.IntegerField()),
                ('tax_amount', models.IntegerField()),
                ('total_gross_value', models.IntegerField()),
                (
                    'invoice',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='items',
                        to='invoicing.CustomerInvoice',
                    ),
                ),
                (
                    'transaction_row',
                    models.ForeignKey(
                        blank=True,
                        db_constraint=False,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name='customer_invoice_items',
                        to='pos.TransactionRow',
                    ),
                ),
            ],
            options={
                'ordering': ('id',),
            },
        ),
        migrations.AddField(
            model_name='customerinvoice',
            name='seller',
            field=models.ForeignKey(
                null=True, on_delete=django.db.models.deletion.SET_NULL, to='invoicing.Seller'
            ),
        ),
        migrations.AddField(
            model_name='customerinvoice',
            name='transaction',
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name='customer_invoices',
                to='pos.Transaction',
            ),
        ),
        migrations.CreateModel(
            name='CashRegisterDocumentItem',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('name', models.CharField(max_length=250)),
                ('amount', models.IntegerField()),
                ('account', models.CharField(blank=True, max_length=50)),
                (
                    'cash_register_document',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='items',
                        to='invoicing.CashRegisterDocument',
                    ),
                ),
            ],
            options={
                'ordering': ('id',),
            },
        ),
        migrations.AddField(
            model_name='cashregisterdocument',
            name='seller',
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to='invoicing.Seller',
                verbose_name='Issuer',
            ),
        ),
        migrations.AlterUniqueTogether(
            name='customerinvoice',
            unique_together={('business', 'number')},
        ),
        migrations.AlterUniqueTogether(
            name='cashregisterdocument',
            unique_together={('business', 'number')},
        ),
    ]
