import unittest

import pytest
from model_bakery import baker

from lib.test_utils import get_business_notifications
from webapps.business.baker_recipes import business_recipe
from webapps.images.actions import portfolio_photo_added_handler
from webapps.images.enums import ImageTypeEnum
from webapps.images.models import Image
from webapps.images.notifications import Portfolio5thPhotoAddedNotification
from webapps.notification.enums import NotificationIcon


@pytest.mark.django_db
class PortfolioPhotoAddedHandlerTest(unittest.TestCase):
    def setUp(self) -> None:
        super().setUp()
        self.business = business_recipe.make()

    def test_portfolio_photo_added_handler(self):
        baker.make(Image, business=self.business, category=ImageTypeEnum.INSPIRATION, _quantity=3)
        image_no4 = baker.make(Image, business=self.business, category=ImageTypeEnum.INSPIRATION)
        image_no5 = baker.make(Image, business=self.business, category=ImageTypeEnum.INSPIRATION)

        # test handler for 4th photo when there is already 5 photos
        portfolio_photo_added_handler(image_no4)

        notification = Portfolio5thPhotoAddedNotification(image_no4)
        self.assertFalse(notification.schedule_record.record.id)

        portfolio_photo_added_handler(image_no5)

        notification = Portfolio5thPhotoAddedNotification(image_no5)
        notification.schedule_record.assert_success()

        notifications = get_business_notifications(self.business.id)
        self.assertEqual(1, notifications.hits.total.value)
        self.assertTrue(notifications.hits[0])
        self.assertEqual(NotificationIcon.RECOGNITION, notifications.hits[0].icon)
        self.assertFalse(notifications.hits[0].crucial)
        self.assertEqual(3, notifications.hits[0].relevance)
