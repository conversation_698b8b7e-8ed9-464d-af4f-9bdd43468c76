import elasticsearch
from django.test import TestCase
from elasticsearch.exceptions import Transport<PERSON>rror
from mock import patch
from model_bakery import baker
from model_bakery.recipe import Recipe

from lib.elasticsearch.consts import ESDocType
from lib.elasticsearch.tools import index_document, delete_document
from webapps.business.baker_recipes import business_recipe
from webapps.images.baker_recipes import fake_image_url, image_recipe
from webapps.images.elasticsearch import ImageSearchHitSerializer
from webapps.images.enums import ImageTypeEnum
from webapps.images.models import Image, ImageComment
from webapps.images.searchables import ImageSearchable
from webapps.user.models import UserProfile


class ImageUpdateToEsTests(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.business = baker.make_recipe('webapps.business.business_recipe')
        cls.images = image_recipe.make(
            description='Old description',
            _quantity=5,
            business=cls.business,
        )
        cls.comment_recipe = Recipe(ImageComment, image=cls.images[0])
        cls.business_comments: ImageComment = cls.comment_recipe.make(
            profile_type=UserProfile.Type.BUSINESS,
            _quantity=3,
        )
        cls.images_ids = [image.id for image in cls.images]
        cls.comments_ids = [comment.id for comment in cls.business_comments]
        cls.commented_image_id = cls.images_ids[0]

    def _synchronize_es_with_teardowned_images(self):
        index_document(Image.es_doc_type, self.images_ids, refresh_index=True)

    def _synchronize_es_with_teardowned_comments(self):
        index_document(ImageComment.es_doc_type, self.comments_ids, refresh_index=True)

    @staticmethod
    def _get_image_searchable(image_ids):
        res = ImageSearchable(
            ESDocType.IMAGE,
            serializer=ImageSearchHitSerializer,
        ).execute({'image_id': image_ids})
        return res

    @staticmethod
    def _image_exist_in_es(image_id):
        res = ImageUpdateToEsTests._get_image_searchable([image_id])
        assert res.hits.total.value == 1
        assert res.hits[0].image_id == image_id

    @staticmethod
    def _image_dont_exist_in_es(image_id):
        res = ImageUpdateToEsTests._get_image_searchable([image_id])
        assert res.hits.total.value == 0

    def _check_images_description(self, ids, description):
        pass

    @patch('lib.elasticsearch.tools.bump_document')
    def test_update_to_es_in_create(self, bump_document_patch):
        some_business = business_recipe.make()
        _ = Image.objects.create(
            category=ImageTypeEnum.BIZ_PHOTO,
            image_url=fake_image_url(),
            width=450,
            height=300,
        )
        some_image = Image.objects.create(
            business=some_business,
            category=ImageTypeEnum.BIZ_PHOTO,
            image_url=fake_image_url(),
            width=450,
            height=300,
        )
        some_image.reindex(refresh_index=True)
        self._image_exist_in_es(some_image.id)
        bump_document_patch.assert_not_called()

    @patch('lib.elasticsearch.tools.bump_document')
    def test_update_to_es_in_save(self, bump_document_patch):
        some_business = business_recipe.make()
        _ = Image.objects.create(
            category=ImageTypeEnum.BIZ_PHOTO,
            image_url=fake_image_url(),
            width=450,
            height=300,
        )
        some_image = Image(
            business=some_business,
            category=ImageTypeEnum.BIZ_PHOTO,
            image_url=fake_image_url(),
            width=450,
            height=300,
        )
        some_image.save()
        some_image.reindex(refresh_index=True)
        self._image_exist_in_es(some_image.id)
        bump_document_patch.assert_not_called()

    @patch('lib.elasticsearch.tools.bump_document')
    def test_update_to_es_can_be_skipped_in_create(self, bump_document_patch):
        some_business = business_recipe.make()
        _ = Image.objects.create(
            category=ImageTypeEnum.BIZ_PHOTO,
            image_url=fake_image_url(),
            width=450,
            height=300,
        )
        some_image = Image.objects.create(
            business=some_business,
            category=ImageTypeEnum.BIZ_PHOTO,
            image_url=fake_image_url(),
            width=450,
            height=300,
            bump_instances_to_es=False,
        )
        self._image_dont_exist_in_es(some_image.id)
        bump_document_patch.assert_not_called()

    @patch('lib.elasticsearch.tools.bump_document')
    def test_update_to_es_can_be_skipped_in_save(self, bump_document_patch):
        some_business = business_recipe.make()
        _ = Image.objects.create(
            category=ImageTypeEnum.BIZ_PHOTO,
            image_url=fake_image_url(),
            width=450,
            height=300,
        )
        some_image = Image(
            business=some_business,
            category=ImageTypeEnum.BIZ_PHOTO,
            image_url=fake_image_url(),
            width=450,
            height=300,
        )
        some_image.save(bump_instances_to_es=False)
        self._image_dont_exist_in_es(some_image.id)
        bump_document_patch.assert_not_called()

    @patch('lib.elasticsearch.tools.bump_document')
    def test_update_to_es_by_bump_river_in_save(self, bump_document_patch):
        some_business = business_recipe.make()
        _ = Image.objects.create(
            category=ImageTypeEnum.BIZ_PHOTO,
            image_url=fake_image_url(),
            width=450,
            height=300,
        )
        some_image = Image(
            business=some_business,
            category=ImageTypeEnum.BIZ_PHOTO,
            image_url=fake_image_url(),
            width=450,
            height=300,
        )
        some_image.save(bump_later=True)
        self._image_dont_exist_in_es(some_image.id)
        bump_document_patch.assert_called_once_with(
            Image.river,
            [some_image.id],
        )

    @patch('lib.elasticsearch.tools.bump_document')
    def test_update_to_es_by_bump_river_in_create(self, bump_document_patch):
        some_business = business_recipe.make()
        _ = Image.objects.create(
            category=ImageTypeEnum.BIZ_PHOTO,
            image_url=fake_image_url(),
            width=450,
            height=300,
        )
        some_image = Image.objects.create(
            business=some_business,
            category=ImageTypeEnum.BIZ_PHOTO,
            image_url=fake_image_url(),
            width=450,
            height=300,
            bump_later=True,
        )
        self._image_dont_exist_in_es(some_image.id)
        bump_document_patch.assert_called_once_with(
            Image.river,
            [some_image.id],
        )

    @patch('lib.elasticsearch.tools.bump_document')
    def test_update_to_es_in_update_for_all_images(self, bump_document_patch):
        self._synchronize_es_with_teardowned_images()
        new_description = 'New description'
        expected_old_image = [(i, 'Old description') for i in self.images_ids]
        expected_new_image = [(i, new_description) for i in self.images_ids]
        es_images = self._get_image_searchable(self.images_ids)
        es_old_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_old_image) == sorted(expected_old_image)

        Image.objects.all().update(description=new_description)
        self._synchronize_es_with_teardowned_images()
        es_images = self._get_image_searchable(self.images_ids)
        es_new_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_new_image) == sorted(expected_new_image)
        bump_document_patch.assert_not_called()

    @patch('lib.elasticsearch.tools.bump_document')
    def test_update_to_es_in_update_for_filtered_images(
        self,
        bump_document_patch,
    ):
        self._synchronize_es_with_teardowned_images()
        images_to_update = self.images_ids[::2]
        new_description = 'New description'
        expected_old_image = [(i, 'Old description') for i in self.images_ids]
        expected_new_image = [
            (i, new_description if i in images_to_update else 'Old description')
            for i in self.images_ids
        ]
        es_images = self._get_image_searchable(self.images_ids)
        es_old_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_old_image) == sorted(expected_old_image)

        Image.objects.filter(id__in=images_to_update).update(
            description=new_description,
        )
        self._synchronize_es_with_teardowned_images()

        es_images = self._get_image_searchable(self.images_ids)
        es_new_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_new_image) == sorted(expected_new_image)
        bump_document_patch.assert_not_called()

    @patch('lib.elasticsearch.tools.bump_document')
    def test_update_to_es_in_update_for_excluded_images(
        self,
        bump_document_patch,
    ):
        self._synchronize_es_with_teardowned_images()
        excluded_images = self.images_ids[::2]
        new_description = 'New description'
        expected_old_image = [(i, 'Old description') for i in self.images_ids]
        expected_new_image = [
            (i, 'Old description' if i in excluded_images else new_description)
            for i in self.images_ids
        ]
        es_images = self._get_image_searchable(self.images_ids)
        es_old_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_old_image) == sorted(expected_old_image)

        Image.objects.exclude(id__in=excluded_images).update(
            description=new_description,
        )
        self._synchronize_es_with_teardowned_images()

        es_images = self._get_image_searchable(self.images_ids)
        es_new_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_new_image) == sorted(expected_new_image)
        bump_document_patch.assert_not_called()

    @patch('lib.elasticsearch.tools.bump_document')
    def test_update_to_es_in_update_skipped(
        self,
        bump_document_patch,
    ):
        self._synchronize_es_with_teardowned_images()
        new_description = 'New description'
        expected_old_image = [(i, 'Old description') for i in self.images_ids]
        es_images = self._get_image_searchable(self.images_ids)
        es_old_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_old_image) == sorted(expected_old_image)

        Image.objects.all().update(
            description=new_description,
            bump_instances_to_es=False,
        )

        es_images = self._get_image_searchable(self.images_ids)
        es_new_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_new_image) == sorted(expected_old_image)
        bump_document_patch.assert_not_called()

    @patch('lib.elasticsearch.tools.bump_document')
    def test_update_to_es_in_update_bump_later(
        self,
        bump_document_patch,
    ):
        self._synchronize_es_with_teardowned_images()
        new_description = 'New description'
        expected_old_image = [(i, 'Old description') for i in self.images_ids]
        es_images = self._get_image_searchable(self.images_ids)
        es_old_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_old_image) == sorted(expected_old_image)

        Image.objects.all().update(
            description=new_description,
            bump_later=True,
        )

        es_images = self._get_image_searchable(self.images_ids)
        es_new_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_new_image) == sorted(expected_old_image)

        bump_document_patch.assert_called_once()
        actual_river, actual_images_ids = bump_document_patch.call_args[0]
        assert actual_river == Image.river
        self.assertCountEqual(actual_images_ids, self.images_ids)

    @patch('lib.elasticsearch.tools.bump_document')
    def test_update_to_es_in_update_empty_qs(
        self,
        bump_document_patch,
    ):
        self._synchronize_es_with_teardowned_images()
        new_description = 'New description'
        expected_old_image = [(i, 'Old description') for i in self.images_ids]
        es_images = self._get_image_searchable(self.images_ids)
        es_old_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_old_image) == sorted(expected_old_image)

        Image.objects.none().update(
            description=new_description,
        )

        es_images = self._get_image_searchable(self.images_ids)
        es_new_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_new_image) == sorted(expected_old_image)
        bump_document_patch.assert_not_called()

    @patch('lib.elasticsearch.tools.index_document')
    @patch('lib.elasticsearch.tools.bump_document')
    def test_update_to_es_in_update_failed(
        self,
        bump_document_patch,
        index_document_patch,
    ):
        self._synchronize_es_with_teardowned_images()
        index_document_patch.side_effect = elasticsearch.ConnectionTimeout()
        new_description = 'New description'
        expected_old_image = [(i, 'Old description') for i in self.images_ids]
        es_images = self._get_image_searchable(self.images_ids)
        es_old_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_old_image) == sorted(expected_old_image)

        Image.objects.all().update(
            description=new_description,
        )

        es_images = self._get_image_searchable(self.images_ids)
        es_new_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_new_image) == sorted(expected_old_image)

        bump_document_patch.assert_called_once()
        actual_river, actual_images_ids = bump_document_patch.call_args[0]
        assert actual_river == Image.river
        self.assertCountEqual(actual_images_ids, self.images_ids)

    def test_delete_from_es_in_delete(self):
        self._synchronize_es_with_teardowned_images()
        expected_old_image = [(i, 'Old description') for i in self.images_ids]
        expected_new_image = []
        es_images = self._get_image_searchable(self.images_ids)
        es_old_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_old_image) == sorted(expected_old_image)

        Image.objects.all().delete()

        es_images = self._get_image_searchable(self.images_ids)
        es_new_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_new_image) == sorted(expected_new_image)

    def test_delete_from_es_in_soft_delete(self):
        self._synchronize_es_with_teardowned_images()
        expected_old_image = [(i, 'Old description') for i in self.images_ids]
        expected_new_image = []
        es_images = self._get_image_searchable(self.images_ids)
        es_old_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_old_image) == sorted(expected_old_image)

        Image.objects.all().soft_delete()

        es_images = self._get_image_searchable(self.images_ids)
        es_new_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_new_image) == sorted(expected_new_image)

    def _get_side_effect_iterator(self):
        side_effect_iterator = iter(
            [
                TransportError('Unable to sniff hosts.'),
                delete_document,
            ]
        )

        def _side_effect_iterator(doc_type, images_ids):
            elem = next(side_effect_iterator)
            if isinstance(elem, TransportError):
                raise elem
            return elem(doc_type, images_ids)

        return _side_effect_iterator

    @patch('lib.elasticsearch.tools.delete_document')
    def test_delete_from_es_in_delete_es_down_at_beginning(
        self,
        mocked_delete_document,
    ):
        self._synchronize_es_with_teardowned_images()
        expected_old_image = [(i, 'Old description') for i in self.images_ids]
        expected_new_image = []
        es_images = self._get_image_searchable(self.images_ids)
        es_old_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_old_image) == sorted(expected_old_image)

        mocked_delete_document.side_effect = self._get_side_effect_iterator()
        Image.objects.all().delete()

        es_images = self._get_image_searchable(self.images_ids)
        es_new_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_new_image) == sorted(expected_new_image)
        assert mocked_delete_document.call_count == 2

    @patch('lib.elasticsearch.tools.delete_document')
    def test_delete_from_es_in_soft_delete_es_down_at_beginning(
        self,
        mocked_delete_document,
    ):
        self._synchronize_es_with_teardowned_images()
        expected_old_image = [(i, 'Old description') for i in self.images_ids]
        expected_new_image = []
        es_images = self._get_image_searchable(self.images_ids)
        es_old_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_old_image) == sorted(expected_old_image)

        mocked_delete_document.side_effect = self._get_side_effect_iterator()
        Image.objects.all().soft_delete()

        es_images = self._get_image_searchable(self.images_ids)
        es_new_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_new_image) == sorted(expected_new_image)
        assert mocked_delete_document.call_count == 2

    def test_delete_from_es_in_delete_with_exclude(self):
        self._synchronize_es_with_teardowned_images()
        excluded_images = self.images_ids[::2]
        expected_old_image = [(i, 'Old description') for i in self.images_ids]
        expected_new_image = [(i, 'Old description') for i in excluded_images]
        es_images = self._get_image_searchable(self.images_ids)
        es_old_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_old_image) == sorted(expected_old_image)

        Image.objects.exclude(id__in=excluded_images).delete()

        es_images = self._get_image_searchable(self.images_ids)
        es_new_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_new_image) == sorted(expected_new_image)

    def test_delete_from_es_in_soft_delete_with_exclude(self):
        self._synchronize_es_with_teardowned_images()
        excluded_images = self.images_ids[::2]
        expected_old_image = [(i, 'Old description') for i in self.images_ids]
        expected_new_image = [(i, 'Old description') for i in excluded_images]
        es_images = self._get_image_searchable(self.images_ids)
        es_old_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_old_image) == sorted(expected_old_image)

        Image.objects.exclude(id__in=excluded_images).soft_delete()

        es_images = self._get_image_searchable(self.images_ids)
        es_new_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_new_image) == sorted(expected_new_image)

    @patch('lib.elasticsearch.tools.delete_document')
    def test_delete_from_es_in_delete_with_exclude_es_down_at_beginning(
        self,
        mocked_delete_document,
    ):
        self._synchronize_es_with_teardowned_images()
        excluded_images = self.images_ids[::2]
        expected_old_image = [(i, 'Old description') for i in self.images_ids]
        expected_new_image = [(i, 'Old description') for i in excluded_images]
        es_images = self._get_image_searchable(self.images_ids)
        es_old_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_old_image) == sorted(expected_old_image)

        mocked_delete_document.side_effect = self._get_side_effect_iterator()
        Image.objects.exclude(id__in=excluded_images).delete()

        es_images = self._get_image_searchable(self.images_ids)
        es_new_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_new_image) == sorted(expected_new_image)
        assert mocked_delete_document.call_count == 2

    @patch('lib.elasticsearch.tools.delete_document')
    def test_delete_from_es_in_soft_delete_with_exclude_es_down_at_beginning(
        self,
        mocked_delete_document,
    ):
        self._synchronize_es_with_teardowned_images()
        excluded_images = self.images_ids[::2]
        expected_old_image = [(i, 'Old description') for i in self.images_ids]
        expected_new_image = [(i, 'Old description') for i in excluded_images]
        es_images = self._get_image_searchable(self.images_ids)
        es_old_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_old_image) == sorted(expected_old_image)

        mocked_delete_document.side_effect = self._get_side_effect_iterator()
        Image.objects.exclude(id__in=excluded_images).soft_delete()

        es_images = self._get_image_searchable(self.images_ids)
        es_new_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_new_image) == sorted(expected_new_image)
        assert mocked_delete_document.call_count == 2

    def test_delete_from_es_in_delete_from_instance(self):
        self._synchronize_es_with_teardowned_images()
        image_to_delete = self.images_ids[0]
        expected_old_image = [(i, 'Old description') for i in self.images_ids]
        expected_new_image = [(i, 'Old description') for i in self.images_ids[1:]]
        es_images = self._get_image_searchable(self.images_ids)
        es_old_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_old_image) == sorted(expected_old_image)

        Image.objects.get(id=image_to_delete).delete()

        es_images = self._get_image_searchable(self.images_ids)
        es_new_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_new_image) == sorted(expected_new_image)

    def test_delete_from_es_in_soft_delete_from_instance(self):
        self._synchronize_es_with_teardowned_images()
        self._synchronize_es_with_teardowned_comments()
        image_to_delete = self.images_ids[0]
        expected_old_image = [(i, 'Old description') for i in self.images_ids]
        expected_new_image = [(i, 'Old description') for i in self.images_ids[1:]]
        es_images = self._get_image_searchable(self.images_ids)
        es_old_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_old_image) == sorted(expected_old_image)

        Image.objects.get(id=image_to_delete).soft_delete()

        es_images = self._get_image_searchable(self.images_ids)
        es_new_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_new_image) == sorted(expected_new_image)

    @patch('lib.elasticsearch.tools.delete_document')
    def test_delete_from_es_in_delete_from_instance_es_down_at_beginning(
        self,
        mocked_delete_document,
    ):
        self._synchronize_es_with_teardowned_images()
        image_to_delete = self.images_ids[0]
        expected_old_image = [(i, 'Old description') for i in self.images_ids]
        expected_new_image = [(i, 'Old description') for i in self.images_ids[1:]]
        es_images = self._get_image_searchable(self.images_ids)
        es_old_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_old_image) == sorted(expected_old_image)

        mocked_delete_document.side_effect = self._get_side_effect_iterator()
        Image.objects.get(id=image_to_delete).delete()

        es_images = self._get_image_searchable(self.images_ids)
        es_new_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_new_image) == sorted(expected_new_image)
        assert mocked_delete_document.call_count == 2

    @patch('lib.elasticsearch.tools.delete_document')
    def test_delete_from_es_in_soft_delete_from_instance_es_down_at_beginning(
        self,
        mocked_delete_document,
    ):
        self._synchronize_es_with_teardowned_images()
        image_to_delete = self.images_ids[0]
        expected_old_image = [(i, 'Old description') for i in self.images_ids]
        expected_new_image = [(i, 'Old description') for i in self.images_ids[1:]]
        es_images = self._get_image_searchable(self.images_ids)
        es_old_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_old_image) == sorted(expected_old_image)

        mocked_delete_document.side_effect = self._get_side_effect_iterator()
        Image.objects.get(id=image_to_delete).soft_delete()

        es_images = self._get_image_searchable(self.images_ids)
        es_new_image = [(i.image_id, i.description) for i in es_images]
        assert sorted(es_new_image) == sorted(expected_new_image)
        assert mocked_delete_document.call_count == 2

    def test_delete_comment_from_es_in_soft_delete_from_instance(self):
        self._synchronize_es_with_teardowned_images()
        self._synchronize_es_with_teardowned_comments()
        comment_to_delete = self.comments_ids[0]
        expected_old_comments = self.comments_ids
        expected_new_comments = self.comments_ids[1:]
        es_images = self._get_image_searchable([self.commented_image_id])
        es_comments = es_images[0].comments
        es_old_comments = [comment.comment_id for comment in es_comments]
        assert sorted(es_old_comments) == sorted(expected_old_comments)

        ImageComment.objects.get(id=comment_to_delete).soft_delete()

        es_images = self._get_image_searchable([self.commented_image_id])
        es_comments = es_images[0].comments
        es_new_comments = [comment.comment_id for comment in es_comments]
        assert sorted(es_new_comments) == sorted(expected_new_comments)

    def test_update_to_es_in_update_for_all_image_comments(self):
        self._synchronize_es_with_teardowned_images()
        self._synchronize_es_with_teardowned_comments()
        new_content = 'New content'
        expected_old_comments = [(i, 'Old content') for i in self.comments_ids]
        expected_new_comments = [(i, new_content) for i in self.comments_ids]
        es_images = self._get_image_searchable([self.commented_image_id])
        es_comments = es_images[0].comments
        es_old_comments = [(comment.comment_id, 'Old content') for comment in es_comments]
        assert sorted(es_old_comments) == sorted(expected_old_comments)

        ImageComment.objects.all().update(content=new_content)
        self._synchronize_es_with_teardowned_images()
        self._synchronize_es_with_teardowned_comments()
        es_images = self._get_image_searchable([self.commented_image_id])
        es_comments = es_images[0].comments
        es_new_comments = [(comment.comment_id, comment.content) for comment in es_comments]
        assert sorted(es_new_comments) == sorted(expected_new_comments)
