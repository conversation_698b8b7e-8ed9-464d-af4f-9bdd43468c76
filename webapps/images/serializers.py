from django.utils.translation import gettext as _
from rest_framework import serializers

from service.images.serializers import ImageCreateSerializer
from webapps.images.enums import ImageTypeEnum

from webapps.images.models import Image


class ImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = Image
        fields = ['id', 'image_url']

    def to_internal_value(self, data):
        image = Image.objects.filter(id=data.get('id')).last()

        if image is None:
            raise serializers.ValidationError(
                _('Selected image does not exist.'), code='does_not_exist'
            )

        return image

    def to_representation(self, instance):
        if getattr(instance, 'deleted'):
            return None
        return super().to_representation(instance)


class VenueCoverImageCreateSerializer(ImageCreateSerializer):
    class Meta(ImageCreateSerializer.Meta):
        model = Image
        fields = ('image', 'category', 'is_cover_photo')

    def to_internal_value(self, data):
        data.update(
            {
                'is_cover_photo': True,
                'category': ImageTypeEnum.BIZ_PHOTO,
            }
        )
        return super().to_internal_value(data)

    def validate(self, attrs):
        attrs = super().validate(attrs)
        attrs.pop('override_in_single_photo_category', None)
        return attrs
