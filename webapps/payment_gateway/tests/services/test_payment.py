# pylint: disable=protected-access
import uuid
from decimal import Decimal

import pytest
from django.utils import timezone
from freezegun import freeze_time
from mock import patch
from model_bakery import baker
from parameterized import parameterized

from lib.payment_gateway.entities import (
    DisputeSplitsEntity,
    PaymentSplitsEntity,
    RefundSplitsEntity,
)
from lib.payment_gateway.enums import (
    BalanceTransactionStatus,
    BalanceTransactionType,
    PaymentMethodType,
    PaymentStatus,
)
from lib.payment_providers.entities import PaymentEntity
from lib.payments.enums import BGCReportingCategoryEnum, PaymentError, PaymentProviderCode
from webapps.payment_gateway.exceptions import (
    InvalidAmount,
    InvalidOperation,
    InvalidSenderReceiver,
    OperationNotAllowed,
)
from webapps.payment_gateway.models import (
    BalanceTransaction,
    BalanceTransactionHistory,
    Payment,
)
from webapps.payment_gateway.services.payment import PaymentService
from webapps.payment_gateway.tests.services.base import BaseServiceTestCase

# pylint: disable=line-too-long


@pytest.mark.django_db
class PaymentServiceTests(BaseServiceTestCase):
    @patch('webapps.payment_gateway.services.payment.PaymentProvidersAdapter.initialize_payment')
    @patch(
        'webapps.payment_gateway.services.payment.payment_gateway_balance_transaction_created_event.send'
    )
    def test_initialize_payment__success(self, event_bt_created_mock, payment_adapter_mock):
        payment_adapter_mock.return_value = PaymentEntity(
            id=uuid.UUID("4d5f3316-ce49-489d-9eb8-58323b28106c"),
            amount=1,
            fee_amount=1,
            status="",
            auto_capture=True,
        )

        bt = PaymentService.initialize_payment(
            amount=101,
            payment_splits=PaymentSplitsEntity(percentage_fee=Decimal(0), fixed_fee=64),
            refund_splits=RefundSplitsEntity(percentage_fee=Decimal(50), fixed_fee=13),
            dispute_splits=DisputeSplitsEntity(percentage_fee=Decimal(11), fixed_fee=14),
            sender=self.cus_wallet,
            receiver=self.biz_wallet,
            payment_method=PaymentMethodType.CARD,
            payment_provider_code=PaymentProviderCode.STRIPE,
            capture_automatically=True,
            payment_token=None,
        )
        self.assertEqual(event_bt_created_mock.call_count, 1)
        self.assertEqual(BalanceTransaction.objects.count(), 1)
        self.assertEqual(BalanceTransaction.objects.last().pk, bt.pk)
        self.assertEqual(bt.status, BalanceTransactionStatus.PROCESSING)
        self.assertEqual(bt.external_id, uuid.UUID("4d5f3316-ce49-489d-9eb8-58323b28106c"))
        self.assertEqual(bt.amount, 101)
        self.assertEqual(bt.fee_amount, 64)
        self.assertEqual(bt.transaction_type, BalanceTransactionType.PAYMENT)
        self.assertEqual(bt.payment_method, PaymentMethodType.CARD)
        self.assertEqual(bt.payment_provider_code, PaymentProviderCode.STRIPE)
        self.assertEqual(payment_adapter_mock.call_count, 1)
        self.assertEqual(BalanceTransactionHistory.objects.count(), 1)
        self.assertEqual(BalanceTransactionHistory.objects.last().balance_transaction_id, bt.id)
        self.assertEqual(Payment.objects.count(), 1)
        payment = Payment.objects.last()
        self.assertEqual(payment.balance_transaction, bt)
        self.assertDictEqual(payment.refund_splits, {"percentage_fee": '50.00', "fixed_fee": 13})
        self.assertDictEqual(payment.dispute_splits, {"percentage_fee": '11.00', "fixed_fee": 14})

    @patch('webapps.payment_gateway.services.payment.PaymentProvidersAdapter.initialize_payment')
    @patch(
        'webapps.payment_gateway.services.payment.payment_gateway_balance_transaction_created_event.send'
    )
    def test_initialize_payment_for_booksy__success(
        self, event_bt_created_mock, payment_adapter_mock
    ):
        payment_adapter_mock.return_value = PaymentEntity(
            id=uuid.UUID("4d5f3316-ce49-489d-9eb8-58323b28106c"),
            amount=1,
            fee_amount=1,
            status="",
            auto_capture=True,
        )

        bt = PaymentService.initialize_payment(
            amount=1000,
            payment_splits=PaymentSplitsEntity(percentage_fee=Decimal(0), fixed_fee=0),
            refund_splits=RefundSplitsEntity(percentage_fee=Decimal(0), fixed_fee=0),
            dispute_splits=DisputeSplitsEntity(percentage_fee=Decimal(0), fixed_fee=0),
            sender=self.cus_wallet,
            receiver=self.booksy_wallet,
            payment_method=PaymentMethodType.CARD,
            payment_provider_code=PaymentProviderCode.STRIPE,
            capture_automatically=True,
            payment_token=None,
            metadata={
                'booksy_gift_card_id': '6c1e1068-c83d-42f1-89b9-a75741c297d3',
                'booksy_gift_card_external_id': 'ba896bef-e9c0-4b81-a316-ded067080585',
                'reporting_category': BGCReportingCategoryEnum.CHARGE_BGC,
            },
        )
        self.assertEqual(event_bt_created_mock.call_count, 1)
        self.assertEqual(BalanceTransaction.objects.count(), 1)
        self.assertEqual(BalanceTransaction.objects.last().pk, bt.pk)
        self.assertEqual(bt.status, BalanceTransactionStatus.PROCESSING)
        self.assertEqual(bt.external_id, uuid.UUID("4d5f3316-ce49-489d-9eb8-58323b28106c"))
        self.assertEqual(bt.amount, 1000)
        self.assertEqual(bt.fee_amount, 0)
        self.assertEqual(bt.receiver, self.booksy_wallet)
        self.assertEqual(bt.sender, self.cus_wallet)
        self.assertEqual(
            bt.metadata,
            {
                'booksy_gift_card_id': '6c1e1068-c83d-42f1-89b9-a75741c297d3',
                'booksy_gift_card_external_id': 'ba896bef-e9c0-4b81-a316-ded067080585',
                'reporting_category': BGCReportingCategoryEnum.CHARGE_BGC,
            },
        )
        self.assertEqual(payment_adapter_mock.call_count, 1)
        self.assertEqual(BalanceTransactionHistory.objects.count(), 1)
        self.assertEqual(BalanceTransactionHistory.objects.last().balance_transaction_id, bt.id)
        self.assertEqual(Payment.objects.count(), 1)
        payment = Payment.objects.last()
        self.assertEqual(payment.balance_transaction, bt)
        self.assertDictEqual(payment.refund_splits, {"percentage_fee": '0.00', "fixed_fee": 0})
        self.assertDictEqual(payment.dispute_splits, {"percentage_fee": '0.00', "fixed_fee": 0})
        self.assertEqual(
            payment.metadata,
            {
                'booksy_gift_card_id': '6c1e1068-c83d-42f1-89b9-a75741c297d3',
                'booksy_gift_card_external_id': 'ba896bef-e9c0-4b81-a316-ded067080585',
                'reporting_category': BGCReportingCategoryEnum.CHARGE_BGC,
            },
        )

    @patch('webapps.payment_gateway.services.payment.PaymentProvidersAdapter.initialize_payment')
    @patch(
        'webapps.payment_gateway.services.payment.payment_gateway_balance_transaction_created_event.send'
    )
    def test_initialize_payment__invalid_sender_receiver(
        self,
        event_bt_created_mock,
        payment_adapter_mock,
    ):
        for sender_wallet, receiver_wallet in [
            (self.booksy_wallet, self.cus_wallet),
            (self.booksy_wallet, self.biz_wallet),
            (self.booksy_wallet, self.booksy_wallet),
            (self.booksy_wallet, self.anon_wallet),
            (self.biz_wallet, self.cus_wallet),
            (self.biz_wallet, self.biz_wallet),
            (self.biz_wallet, self.booksy_wallet),
            (self.biz_wallet, self.anon_wallet),
            (self.anon_wallet, self.anon_wallet),
            (self.anon_wallet, self.cus_wallet),
            (self.anon_wallet, self.booksy_wallet),
            (self.cus_wallet, self.anon_wallet),
            (self.cus_wallet, self.cus_wallet),
        ]:
            with self.assertRaises(InvalidSenderReceiver):
                PaymentService.initialize_payment(
                    amount=101,
                    payment_splits=PaymentSplitsEntity(percentage_fee=Decimal(0), fixed_fee=13),
                    refund_splits=RefundSplitsEntity(percentage_fee=Decimal(10), fixed_fee=13),
                    dispute_splits=DisputeSplitsEntity(percentage_fee=Decimal(11), fixed_fee=13),
                    sender=sender_wallet,
                    receiver=receiver_wallet,
                    payment_method=PaymentMethodType.CARD,
                    payment_provider_code=PaymentProviderCode.STRIPE,
                    capture_automatically=True,
                )
            self.assertEqual(BalanceTransaction.objects.count(), 0)
            self.assertEqual(event_bt_created_mock.call_count, 0)
            self.assertEqual(payment_adapter_mock.call_count, 0)

    @parameterized.expand(
        [
            (-1, 12),
            (100, -13),
            (-100, -13),
        ]
    )
    @patch('webapps.payment_gateway.services.payment.PaymentProvidersAdapter.initialize_payment')
    @patch(
        'webapps.payment_gateway.services.payment.payment_gateway_balance_transaction_created_event.send'
    )
    def test_initialize_payment__invalid_amount(
        self,
        amount,
        payment_fee_amount,
        event_bt_created_mock,
        payment_adapter_mock,
    ):
        with self.assertRaises(InvalidAmount):
            PaymentService.initialize_payment(
                amount=amount,
                payment_splits=PaymentSplitsEntity(
                    percentage_fee=Decimal(0),
                    fixed_fee=payment_fee_amount,
                ),
                refund_splits=RefundSplitsEntity(percentage_fee=Decimal(11), fixed_fee=13),
                dispute_splits=DisputeSplitsEntity(percentage_fee=Decimal(11), fixed_fee=13),
                sender=self.anon_wallet,
                receiver=self.biz_wallet,
                payment_method=PaymentMethodType.CARD,
                payment_provider_code=PaymentProviderCode.STRIPE,
                capture_automatically=True,
            )
            self.assertEqual(BalanceTransaction.objects.count(), 0)
            self.assertEqual(event_bt_created_mock.call_count, 0)
            self.assertEqual(payment_adapter_mock.call_count, 0)

    @patch(
        'webapps.payment_gateway.services.payment.payment_gateway_balance_transaction_updated_event.send'
    )
    @patch('webapps.payment_gateway.services.payment.PaymentProvidersAdapter.modify_payment')
    def test_update_payment(self, modify_payment_mock, bt_updated_mock):
        # check if payment isn't updated unnecessarily if nothing changes
        bt = baker.make(
            BalanceTransaction,
            transaction_type=BalanceTransactionType.PAYMENT,
            amount=100,
            fee_amount=40,  # 100 * 10% + 30  (from payment_splits below)
        )
        payment = baker.make(
            Payment,
            balance_transaction=bt,
            status=PaymentStatus.NEW,
            error_code=PaymentError.GENERIC_PAYMENT_ERROR,
            action_required_details={"x": "d"},
            metadata={"key": "value"},
            payment_splits=PaymentSplitsEntity(percentage_fee=Decimal(10), fixed_fee=30).as_dict(),
            refund_splits={},
            dispute_splits={},
        )

        PaymentService.update_payment(
            payment=payment,
            new_status=PaymentStatus.NEW,
        )
        self.assertEqual(bt_updated_mock.call_count, 0)
        self.assertEqual(modify_payment_mock.call_count, 0)

        PaymentService.update_payment(
            payment=payment,
            new_status=PaymentStatus.NEW,
            new_amount=100,
        )
        self.assertEqual(bt_updated_mock.call_count, 0)
        self.assertEqual(modify_payment_mock.call_count, 0)

        PaymentService.update_payment(
            payment=payment,
            new_status=PaymentStatus.NEW,
            new_amount=100,
            error_code=PaymentError.GENERIC_PAYMENT_ERROR,
        )
        self.assertEqual(bt_updated_mock.call_count, 0)
        self.assertEqual(modify_payment_mock.call_count, 0)

        PaymentService.update_payment(
            payment=payment,
            new_status=PaymentStatus.NEW,
            new_amount=100,
            error_code=PaymentError.GENERIC_PAYMENT_ERROR,
            action_required_details={"x": "d"},
        )
        self.assertEqual(bt_updated_mock.call_count, 0)
        self.assertEqual(modify_payment_mock.call_count, 0)

        PaymentService.update_payment(
            payment=payment,
            new_status=PaymentStatus.NEW,
            new_amount=100,
            error_code=PaymentError.GENERIC_PAYMENT_ERROR,
            action_required_details={"x": "d"},
            metadata={"key": "value"},
        )
        self.assertEqual(bt_updated_mock.call_count, 0)
        self.assertEqual(modify_payment_mock.call_count, 0)

        # test the case when the amount actually changes
        PaymentService.update_payment(
            payment=payment,
            new_status=PaymentStatus.NEW,
            new_amount=150,
            error_code=PaymentError.GENERIC_PAYMENT_ERROR,
            action_required_details={"x": "d"},
            metadata={"key": "value"},
        )
        self.assertEqual(bt_updated_mock.call_count, 2)  # update payment + update fee
        self.assertEqual(modify_payment_mock.call_count, 1)
        bt.refresh_from_db()
        self.assertEqual(bt.amount, 150)
        self.assertEqual(bt.fee_amount, 45)  # 150 * 10% + 30

    @patch(
        'webapps.payment_gateway.services.payment.payment_gateway_balance_transaction_updated_event.send'
    )
    def test_update_payment__capture_date(self, bt_updated_mock):
        # check if payment isn't updated unnecessarily if nothing changes
        bt = baker.make(
            BalanceTransaction,
            transaction_type=BalanceTransactionType.PAYMENT,
            amount=100,
        )
        payment = baker.make(
            Payment,
            balance_transaction=bt,
            status=PaymentStatus.AUTHORIZED,
            capture_date=None,
            payment_splits={},
            refund_splits={},
            dispute_splits={},
        )

        # test capture_date
        date = timezone.now()
        with freeze_time(date):
            PaymentService.update_payment(
                payment=payment,
                new_status=PaymentStatus.CAPTURED,
            )
            self.assertEqual(bt_updated_mock.call_count, 1)  # update payment
            payment.refresh_from_db()
            self.assertEqual(payment.status, PaymentStatus.CAPTURED)
            self.assertEqual(payment.capture_date, date)

    @patch(
        'webapps.payment_gateway.services.payment.payment_gateway_balance_transaction_updated_event.send'
    )
    @patch('webapps.payment_gateway.services.payment.PaymentProvidersAdapter.modify_payment')
    def test_update_fee_amount(self, modify_payment_mock, bt_updated_mock):
        bt = self._create_payment_bt(amount=100)

        PaymentService.update_fee_amount(
            balance_transaction=bt,
            payment_fee_amount=0,
        )
        self.assertEqual(modify_payment_mock.call_count, 0)
        self.assertEqual(bt_updated_mock.call_count, 0)

        PaymentService.update_fee_amount(
            balance_transaction=bt,
            payment_fee_amount=10,
        )
        self.assertEqual(modify_payment_mock.call_count, 1)
        self.assertEqual(bt_updated_mock.call_count, 1)

        PaymentService.update_fee_amount(
            balance_transaction=bt,
            payment_fee_amount=10,
        )
        self.assertEqual(modify_payment_mock.call_count, 1)
        self.assertEqual(bt_updated_mock.call_count, 1)

    @patch('webapps.payment_gateway.services.fee.PaymentProvidersAdapter.mark_payment_as_failed')
    def test_mark_payment_as_failed(self, mark_payment_as_failed_mock):
        bt = self._create_payment_bt(
            payment_method=PaymentMethodType.CARD,  # invalid method
            status=BalanceTransactionStatus.PROCESSING,
        )

        with self.assertRaises(InvalidOperation):
            PaymentService.mark_payment_as_failed(balance_transaction=bt)

        bt.payment_method = PaymentMethodType.TERMINAL  # correct method

        PaymentService.mark_payment_as_failed(balance_transaction=bt, error_code="racksnotfound")

        mark_payment_as_failed_mock.assert_called_once_with(
            payment_id=bt.external_id,
            error_code="racksnotfound",
        )

    @parameterized.expand(
        [
            (BalanceTransactionType.PAYOUT, False),
            (BalanceTransactionType.REFUND, False),
            (BalanceTransactionType.DISPUTE, False),
            (BalanceTransactionType.TRANSFER_FUND, False),
            (BalanceTransactionType.FEE, False),
            (BalanceTransactionType.PAYMENT, True),
        ]
    )
    def test_validate_balance_transaction_type(self, bt_type, validity):
        bt = baker.make(BalanceTransaction, transaction_type=bt_type)

        if validity:
            self.assertEqual(None, PaymentService._validate_balance_transaction_type(bt))
        else:
            with pytest.raises(OperationNotAllowed):
                PaymentService._validate_balance_transaction_type(bt)

    @parameterized.expand(
        [
            (PaymentMethodType.TERMINAL, False),
            (PaymentMethodType.CARD, True),
            (PaymentMethodType.GOOGLE_PAY, True),
            (PaymentMethodType.APPLE_PAY, True),
            (PaymentMethodType.TAP_TO_PAY, False),
            (PaymentMethodType.KEYED_IN_PAYMENT, True),
        ]
    )
    def test_validate_balance_transaction_payment_method(self, bt_payment_method, validity):
        bt = baker.make(BalanceTransaction, payment_method=bt_payment_method)

        if validity:
            self.assertEqual(None, PaymentService._validate_server_payment_method(bt))
        else:
            with pytest.raises(InvalidOperation):
                PaymentService._validate_server_payment_method(bt)

    @parameterized.expand(
        [
            (None, False),
            (uuid.uuid4(), True),
        ]
    )
    @patch('webapps.payment_gateway.services.payment.PaymentService.update_payment')
    @patch('webapps.payment_gateway.services.payment.PaymentProvidersAdapter.capture_payment')
    def test_capture_payment(
        self,
        external_id,
        validity,
        capture_payment_mock,
        update_payment_mock,
    ):
        bt = baker.make(
            BalanceTransaction,
            transaction_type=BalanceTransactionType.PAYMENT,
            payment_method=PaymentMethodType.CARD,
            external_id=external_id,
        )

        baker.make(
            Payment,
            balance_transaction=bt,
            payment_splits={},
            refund_splits={},
            dispute_splits={},
        )

        PaymentService.capture_payment(bt)

        if validity:
            self.assertEqual(update_payment_mock.call_count, 0)
            self.assertEqual(capture_payment_mock.call_count, 1)
        else:
            self.assertEqual(update_payment_mock.call_count, 1)
            self.assertEqual(capture_payment_mock.call_count, 0)
