# Generated by Django 4.0.4 on 2022-08-08 12:01

import uuid

import django.core.serializers.json
import django.db.models.deletion
from django.db import migrations, models

import lib.models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name='BalanceTransaction',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'id',
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                ('amount', models.IntegerField()),
                ('fee_amount', models.IntegerField(default=0)),
                (
                    'status',
                    models.CharField(
                        choices=[
                            ('processing', 'Processing'),
                            ('success', 'Success'),
                            ('failed', 'Failed'),
                            ('canceled', 'Canceled'),
                        ],
                        default='processing',
                        max_length=20,
                    ),
                ),
                (
                    'payment_method',
                    models.CharField(
                        choices=[('terminal', 'Terminal'), ('card', 'Card (Online)')],
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    'payment_provider_code',
                    models.CharField(
                        choices=[('adyen', 'Adyen'), ('stripe', 'Stripe')], max_length=20
                    ),
                ),
                (
                    'transaction_type',
                    models.CharField(
                        choices=[
                            ('payment', 'Payment'),
                            ('refund', 'Refund'),
                            ('dispute', 'Dispute'),
                            ('payout', 'Payout'),
                            ('fee', 'Fee'),
                        ],
                        max_length=64,
                    ),
                ),
                ('external_id', models.UUIDField(blank=True, null=True)),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.CreateModel(
            name='BalanceTransactionHistory',
            fields=[
                (
                    'id',
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                ('data', models.JSONField(encoder=django.core.serializers.json.DjangoJSONEncoder)),
                ('traceback', models.TextField()),
            ],
            options={
                'verbose_name_plural': 'Balance transaction history',
            },
        ),
        migrations.CreateModel(
            name='Dispute',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'id',
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                (
                    'type',
                    models.CharField(
                        choices=[
                            ('chargeback', 'Chargeback'),
                            ('second_chargeback', 'Second chargeback'),
                            ('reversed_chargeback', 'Reversed chargeback'),
                        ],
                        max_length=40,
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.CreateModel(
            name='Fee',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'id',
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                (
                    'fee_type',
                    models.CharField(
                        choices=[('refund', 'Refund Fee'), ('dispute', 'Dispute Fee')],
                        max_length=20,
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'id',
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                (
                    'status',
                    models.CharField(
                        choices=[
                            ('new', 'New'),
                            ('sent_for_authorization', 'Sent for authorization'),
                            ('action_required', 'Action required'),
                            ('authorized', 'Authorized'),
                            ('authorization_failed', 'Authorization failed'),
                            ('sent_for_capture', 'Sent for capture'),
                            ('captured', 'Captured'),
                            ('capture_failed', 'Capture failed'),
                            ('canceled', 'Canceled'),
                        ],
                        default='new',
                        max_length=40,
                    ),
                ),
                (
                    'error_code',
                    models.CharField(
                        blank=True,
                        choices=[
                            (
                                'generic_error',
                                'The payment has been declined for an unknown reason.',
                            ),
                            ('bank_refusal', 'The issuing bank did not approve this action'),
                            (
                                'authentication_required',
                                'The card was declined as the transaction requires authentication.',
                            ),
                            (
                                'card_not_supported',
                                'The card does not support this type of purchase.',
                            ),
                            (
                                'currency_not_supported',
                                'The card does not support the specified currency.',
                            ),
                            ('expired_card', 'The card has expired.'),
                            (
                                'fraudulent',
                                'The payment has been declined as we suspect it is fraudulent.',
                            ),
                            ('incorrect_cvc', 'The CVC number is incorrect.'),
                            ('incorrect_number', 'The card number is incorrect.'),
                            ('incorrect_pin', 'The PIN entered is incorrect.'),
                            ('incorrect_zip', 'The ZIP/postal code is incorrect.'),
                            (
                                'insufficient_funds',
                                'The card has insufficient funds to complete the purchase.',
                            ),
                            (
                                'invalid_amount',
                                'The payment amount is invalid, or exceeds the amount that is allowed.',
                            ),
                            (
                                'issuer_not_available',
                                'The card issuer could not be reached, so the payment could not be authorized.',
                            ),
                            (
                                'offline_pin_required',
                                'The card has been declined as it requires a PIN.',
                            ),
                            (
                                'pin_try_exceeded',
                                'The allowable number of PIN tries has been exceeded.',
                            ),
                            (
                                'restricted_card',
                                'The card cannot be used to make this payment (it is possible it has been reported lost or stolen).',
                            ),
                        ],
                        default=None,
                        max_length=100,
                        null=True,
                    ),
                ),
                ('action_required_details', models.JSONField(null=True)),
                ('refund_splits', models.JSONField()),
                ('dispute_splits', models.JSONField()),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.CreateModel(
            name='Payout',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'id',
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                (
                    'payout_type',
                    models.CharField(
                        choices=[('fast', 'Fast (also known as instant)'), ('regular', 'Regular')],
                        default='regular',
                        max_length=20,
                    ),
                ),
                (
                    'status',
                    models.CharField(
                        choices=[
                            ('in_payment_processor', 'Waiting to be sent by payment processor'),
                            ('in_transit', 'In transit to destination bank account'),
                            ('arrived', 'Payout should be visible on destination bank account'),
                            ('canceled', 'Canceled'),
                            ('failed', 'Failed'),
                        ],
                        default='in_payment_processor',
                        max_length=20,
                    ),
                ),
                (
                    'error_code',
                    models.CharField(
                        blank=True,
                        choices=[
                            ('account_closed', 'The bank account has been closed.'),
                            ('account_frozen', 'The bank account has been frozen.'),
                            (
                                'bank_account_restricted',
                                'The bank account has restrictions on either the type, or the number, of payouts allowed. This normally indicates that the bank account is a savings or other non-checking account.',
                            ),
                            (
                                'bank_ownership_changed',
                                'The destination bank account is no longer valid because its branch has changed ownership.',
                            ),
                            ('could_not_process', 'The bank could not process this payout.'),
                            (
                                'debit_not_authorized',
                                'Debit transactions are not approved on the bank account.',
                            ),
                            (
                                'declined',
                                'The bank has declined this transfer. Please contact the bank before retrying.',
                            ),
                            (
                                'insufficient_funds',
                                'Your provider account has insufficient funds to cover the payout.',
                            ),
                            (
                                'invalid_account_number',
                                'The routing number seems correct, but the account number is invalid.',
                            ),
                            (
                                'incorrect_account_holder_name',
                                'Your bank notified us that the bank account holder name on file is incorrect.',
                            ),
                            (
                                'incorrect_account_holder_address',
                                'Your bank notified us that the bank account holder address on file is incorrect.',
                            ),
                            (
                                'incorrect_account_holder_tax_id',
                                'Your bank notified us that the bank account holder tax ID on file is incorrect.',
                            ),
                            (
                                'invalid_currency',
                                'The bank was unable to process this payout because of its currency. This is probably because the bank account cannot accept payments in that currency.',
                            ),
                            (
                                'no_account',
                                'The bank account details on file are probably incorrect. No bank account could be located with those details.',
                            ),
                            (
                                'unsupported_card',
                                'The bank no longer supports payouts to this card.',
                            ),
                            ('generic_error', 'Payout failed. Please contact support.'),
                        ],
                        default=None,
                        max_length=100,
                        null=True,
                    ),
                ),
                ('splits', models.JSONField()),
                ('expected_arrival_date', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.CreateModel(
            name='Refund',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'id',
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                ('reason', models.CharField(max_length=255)),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.CreateModel(
            name='Wallet',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'id',
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                ('statement_name', models.CharField(max_length=255)),
                (
                    'owner_type',
                    models.CharField(
                        choices=[
                            ('booksy', 'Booksy'),
                            ('customer', 'Customer'),
                            ('business', 'Business'),
                            ('anonymous', 'Anonymous (i.e. walk-in customer)'),
                        ],
                        max_length=20,
                    ),
                ),
                ('business_id', models.IntegerField(blank=True, null=True, unique=True)),
                ('user_id', models.IntegerField(blank=True, null=True, unique=True)),
                ('customer_id', models.UUIDField(blank=True, null=True, unique=True)),
                ('account_holder_id', models.UUIDField(blank=True, null=True, unique=True)),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.CreateModel(
            name='WalletFeeSettings',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'id',
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                (
                    'payment_provider_code',
                    models.CharField(
                        choices=[('adyen', 'Adyen'), ('stripe', 'Stripe')], max_length=20
                    ),
                ),
                ('default', models.BooleanField(default=False)),
                ('fast_payout_provision_percentage', models.FloatField()),
                ('fast_payout_provision_fee', models.IntegerField()),
                ('regular_payout_provision_percentage', models.FloatField()),
                ('regular_payout_provision_fee', models.IntegerField()),
            ],
            options={
                'verbose_name_plural': 'Wallet fee settings',
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.AddConstraint(
            model_name='walletfeesettings',
            constraint=models.UniqueConstraint(
                condition=models.Q(('default', True)),
                fields=('payment_provider_code',),
                name='wallet_fee_settings_unique_default',
            ),
        ),
        migrations.AddField(
            model_name='wallet',
            name='custom_fee_settings',
            field=models.ManyToManyField(
                related_name='wallets', to='payment_gateway.walletfeesettings'
            ),
        ),
        migrations.AddField(
            model_name='refund',
            name='balance_transaction',
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='refund',
                to='payment_gateway.balancetransaction',
            ),
        ),
        migrations.AddField(
            model_name='payout',
            name='balance_transaction',
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='payout',
                to='payment_gateway.balancetransaction',
            ),
        ),
        migrations.AddField(
            model_name='payment',
            name='balance_transaction',
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='payment',
                to='payment_gateway.balancetransaction',
            ),
        ),
        migrations.AddField(
            model_name='fee',
            name='balance_transaction',
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='fee',
                to='payment_gateway.balancetransaction',
            ),
        ),
        migrations.AddField(
            model_name='dispute',
            name='balance_transaction',
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='dispute',
                to='payment_gateway.balancetransaction',
            ),
        ),
        migrations.AddField(
            model_name='balancetransactionhistory',
            name='balance_transaction',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='history_objects',
                to='payment_gateway.balancetransaction',
            ),
        ),
        migrations.AddField(
            model_name='balancetransaction',
            name='paid_out_in_payout',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name='paid_out_balance_transactions',
                to='payment_gateway.payout',
            ),
        ),
        migrations.AddField(
            model_name='balancetransaction',
            name='parent_balance_transaction',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name='child_balance_transactions',
                to='payment_gateway.balancetransaction',
            ),
        ),
        migrations.AddField(
            model_name='balancetransaction',
            name='receiver',
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name='balance_transactions_received',
                to='payment_gateway.wallet',
            ),
        ),
        migrations.AddField(
            model_name='balancetransaction',
            name='sender',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='balance_transactions_sent',
                to='payment_gateway.wallet',
            ),
        ),
    ]
