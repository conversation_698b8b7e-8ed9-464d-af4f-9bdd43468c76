# Generated by Django 4.1.10 on 2023-11-24 09:26

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("payment_gateway", "0009_balancetransaction_metadata"),
    ]

    operations = [
        migrations.AlterField(
            model_name="balancetransaction",
            name="external_id",
            field=models.UUIDField(blank=True, db_index=True, null=True),
        ),
        migrations.AlterField(
            model_name="wallet",
            name="account_holder_id",
            field=models.UUIDField(blank=True, db_index=True, null=True, unique=True),
        ),
        migrations.AlterField(
            model_name="wallet",
            name="customer_id",
            field=models.UUIDField(blank=True, db_index=True, null=True, unique=True),
        ),
    ]
