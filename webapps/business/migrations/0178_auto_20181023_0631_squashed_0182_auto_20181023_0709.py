# Generated by Django 1.11.11 on 2018-10-23 07:15
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    replaces = [
        ('business', '0178_auto_20181023_0631'),
        ('business', '0179_auto_20181023_0655'),
        ('business', '0180_auto_20181023_0658'),
        ('business', '0181_auto_20181023_0701'),
        ('business', '0182_auto_20181023_0709'),
    ]

    dependencies = [
        ('business', '0177_merge_20181015_1158'),
    ]

    operations = [
        migrations.CreateModel(
            name='BCIPatientFile',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('file_number', models.CharField(max_length=30)),
                (
                    'gender',
                    models.CharField(choices=[('F', 'Female'), ('M', 'Male')], max_length=1),
                ),
                ('document_type', models.CharField(max_length=30)),
                ('document_number', models.CharField(max_length=30)),
                (
                    'branch',
                    models.CharField(
                        choices=[
                            [
                                '01',
                                'Dolno\u015bl\u0105ski Oddzia\u0142 Narodowego Funduszu Zdrowia we Wroc\u0142awiu',
                            ],
                            [
                                '02',
                                'Kujawsko-Pomorski Oddzia\u0142 Narodowego Funduszu Zdrowia w Bydgoszczy',
                            ],
                            ['03', 'Lubelski Oddzia\u0142 Narodowego Funduszu Zdrowia w Lublinie'],
                            [
                                '04',
                                'Lubuski Oddzia\u0142 Narodowego Funduszu Zdrowia w Zielonej G\xf3rze',
                            ],
                            [
                                '05',
                                '\u0141\xf3dzki Oddzia\u0142 Narodowego Funduszu Zdrowia w \u0141odzi',
                            ],
                            [
                                '06',
                                'Ma\u0142opolski Oddzia\u0142 Narodowego Funduszu Zdrowia w Krakowie',
                            ],
                            [
                                '07',
                                'Mazowiecki Oddzia\u0142 Narodowego Funduszu Zdrowia w Warszawie',
                            ],
                            ['08', 'Opolski Oddzia\u0142 Narodowego Funduszu Zdrowia w Opolu'],
                            [
                                '09',
                                'Podkarpacki Oddzia\u0142 Narodowego Funduszu Zdrowia w Rzeszowie',
                            ],
                            [
                                '10',
                                'Podlaski Oddzia\u0142 Narodowego Funduszu Zdrowiaw Bia\u0142ymstoku',
                            ],
                            [
                                '11',
                                'Pomorski Oddzia\u0142 Narodowego Funduszu Zdrowia w Gda\u0144sku',
                            ],
                            [
                                '12',
                                '\u015al\u0105ski Oddzia\u0142 Narodowego Funduszu Zdrowia w Katowicach',
                            ],
                            [
                                '13',
                                '\u015awi\u0119tokrzyski Oddzia\u0142 Narodowego Funduszu Zdrowia w Kielcach',
                            ],
                            [
                                '14',
                                'Warmi\u0144sko-Mazurski Oddzia\u0142 Narodowego Funduszu Zdrowia w Olsztynie',
                            ],
                            [
                                '15',
                                'Wielkopolski Oddzia\u0142 Narodowego Funduszu Zdrowia w Poznaniu',
                            ],
                            [
                                '16',
                                'Zachodniopomorski Oddzia\u0142 Narodowego Funduszu Zdrowia w Szczecinie',
                            ],
                        ],
                        max_length=30,
                    ),
                ),
                (
                    'special_permissions_code',
                    models.CharField(
                        blank=True,
                        choices=[
                            ('', ''),
                            ('AZ', 'AZ'),
                            ('IB', 'IB'),
                            ('IW', 'IW'),
                            ('PO', 'PO'),
                            ('IN', 'IN'),
                            ('WP', 'WP'),
                            ('ZK', 'ZK'),
                            ('S', 'S'),
                        ],
                        max_length=2,
                        null=True,
                    ),
                ),
                (
                    'bci',
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='patient_file',
                        to='business.BusinessCustomerInfo',
                    ),
                ),
            ],
            options={
                'abstract': False,
                'get_latest_by': 'updated',
            },
        ),
    ]
