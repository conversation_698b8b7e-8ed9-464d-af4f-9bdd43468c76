# Generated by Django 1.11.7 on 2018-02-27 14:22
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0122_merge_20180214_1445'),
    ]

    operations = [
        migrations.CreateModel(
            name='CancellationReason',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                (
                    'cancellation_date',
                    models.DateTimeField(
                        blank=True, null=True, verbose_name='Cancellation Date (UTC)'
                    ),
                ),
                (
                    'cancellation_reason',
                    models.CharField(
                        choices=[
                            ('P', 'Price is to high'),
                            ('F', 'Lack of main functionality'),
                            ('CP', 'Competitor have better price'),
                            ('CF', 'Competitor have better functionality'),
                            ('S', 'Tech issues (stability, bugs)'),
                            ('M', 'Problems with migration old data'),
                            ('BS', 'Bad sale/ target'),
                            ('MP', 'Client expected marketplace'),
                            ('BC', 'Business change'),
                            ('U', 'No info'),
                            ('O', 'Other'),
                        ],
                        max_length=2,
                        verbose_name='Reason of cancel',
                    ),
                ),
                (
                    'cancellation_info',
                    models.TextField(blank=True, null=True, verbose_name='Cancellation comment'),
                ),
                (
                    'business',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='cancellation_reason',
                        to='business.Business',
                    ),
                ),
            ],
            options={
                'ordering': ['cancellation_date'],
                'verbose_name': 'Business Cancellation  Reason',
                'verbose_name_plural': 'Business Cancellation  Reasons',
            },
        ),
        migrations.AlterField(
            model_name='businesscustomerinfo',
            name='client_type',
            field=models.CharField(
                blank=True,
                choices=[
                    ('UN', 'Unknown'),
                    ('BD', 'Added by business directly'),
                    ('BP', 'Added by business via cell phone'),
                    ('BI', 'Added by business by import'),
                    ('BV', 'Added by business via invite'),
                    ('BS', 'Added by business via subdomain/deeplink'),
                    ('BF', 'Added by facebook'),
                    ('BW', 'Added by widget'),
                    ('CR', 'Recurring customer'),
                    ('CN', 'New customer'),
                    ('GP', 'Google Partner'),
                    ('YP', 'Yelp Partner'),
                    ('LO', 'Legacy'),
                ],
                default='UN',
                max_length=2,
                null=True,
            ),
        ),
    ]
