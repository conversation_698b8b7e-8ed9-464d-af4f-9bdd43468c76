# -*- coding: utf-8 -*-
# Generated by Django 1.11.17 on 2019-05-22 14:54
from django.db import migrations, models


def forward(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    Business = apps.get_model('business', 'Business')
    Business.objects.using(db_alias).filter(payment_source='B').update(has_braintree=True)


def noop(apps, schema_editor):
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0226_auto_20190517_1340'),
    ]

    operations = [
        migrations.AddField(
            model_name='business',
            name='has_braintree',
            field=models.BooleanField(blank=True, default=False),
        ),
        migrations.RunPython(forward, reverse_code=noop),
    ]
