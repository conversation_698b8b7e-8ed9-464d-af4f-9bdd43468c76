from django.db import models, migrations
import django.contrib.postgres.fields
import django.contrib.postgres.fields.jsonb
import datetime
import lib.fields.date_time_infinity_field
import lib.fields.time_24_hour_field
import dateutil.relativedelta
import lib.interval.fields
import lib.tools
import lib.fields.phone_number
import lib.fields.tstzrange
import django.core.validators


class Migration(migrations.Migration):

    dependencies = [
        ('photo', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Business',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True, db_index=True)),
                ('deleted', models.DateTimeField(null=True, blank=True)),
                (
                    'id',
                    models.AutoField(serialize=False, primary_key=True, db_column='business_id'),
                ),
                ('name', models.CharField(max_length=250)),
                ('name_short', models.CharField(max_length=250, null=True, blank=True)),
                ('address', models.CharField(max_length=100, null=True, blank=True)),
                ('address2', models.CharField(max_length=100, null=True, blank=True)),
                ('city', models.CharField(max_length=100, blank=True)),
                (
                    'booking_mode',
                    models.CharField(
                        default='',
                        max_length=1,
                        choices=[
                            ('A', 'Automatic (no overbooking, automatic confirmation)'),
                            ('M', 'Manual (overbooking, full time slots available)'),
                            ('S', 'Semi-automatic'),
                        ],
                    ),
                ),
                (
                    'phone',
                    lib.fields.phone_number.BooksyPhoneNumberField(
                        max_length=50, null=True, blank=True
                    ),
                ),
                ('longitude', models.FloatField(null=True, blank=True)),
                ('latitude', models.FloatField(null=True, blank=True)),
                ('reviews_rank', models.FloatField(null=True, blank=True)),
                ('reviews_count', models.IntegerField(null=True, blank=True)),
                ('website', models.TextField(null=True, blank=True)),
                ('description', models.TextField(null=True, blank=True)),
                ('credit_cards', models.TextField(null=True, blank=True)),
                ('parking', models.CharField(max_length=50, null=True, blank=True)),
                ('wheelchair_access', models.CharField(max_length=50, null=True, blank=True)),
                (
                    'pricing_level',
                    models.PositiveSmallIntegerField(
                        blank=True,
                        null=True,
                        choices=[
                            (1, 'Low pricing level ($)'),
                            (2, 'Normal pricing level ($$)'),
                            (3, 'High pricing level ($$$)'),
                        ],
                    ),
                ),
                ('yelp_id', models.CharField(max_length=500, null=True, blank=True)),
                ('email_signature', models.TextField(max_length=500, null=True, blank=True)),
                ('promoted', models.NullBooleanField(default=False)),
                (
                    'booking_max_lead_time',
                    lib.interval.fields.IntervalField(
                        default=dateutil.relativedelta.relativedelta(
                            hour=None,
                            seconds=0,
                            months=3,
                            year=None,
                            days=0,
                            years=0,
                            hours=0,
                            second=None,
                            microsecond=None,
                            month=None,
                            microseconds=0,
                            leapdays=0,
                            minutes=0,
                            day=None,
                            minute=None,
                            weekday=None,
                        )
                    ),
                ),
                (
                    'booking_min_lead_time',
                    lib.interval.fields.IntervalField(default=datetime.timedelta(0, 3600)),
                ),
                (
                    'booking_max_modification_time',
                    lib.interval.fields.IntervalField(default=datetime.timedelta(0, 43200)),
                ),
                (
                    'booking_auto_reject_timeout',
                    lib.interval.fields.IntervalField(
                        default=datetime.timedelta(1), null=True, blank=True
                    ),
                ),
                ('active', models.BooleanField(default=False)),
                ('active_from', models.DateTimeField(null=True, blank=True)),
                ('active_till', models.DateTimeField(null=True, blank=True)),
                ('paid_from', models.DateTimeField(null=True, blank=True)),
                ('paid_till', models.DateTimeField(null=True, blank=True)),
                (
                    'status',
                    models.CharField(
                        default='S',
                        max_length=1,
                        choices=[
                            ('S', 'Setup'),
                            ('T', 'Trial'),
                            ('E', 'Trial End'),
                            ('P', 'Paid'),
                            ('O', 'Payment Overdue'),
                            ('B', 'Blocked'),
                        ],
                    ),
                ),
                ('trial_till', models.DateTimeField(null=True, blank=True)),
                ('show_tour_button', models.BooleanField(default=True)),
                (
                    'sms_priority',
                    models.CharField(
                        default='O', max_length=1, choices=[('O', 'Off'), ('P', 'Eco'), ('S', 'On')]
                    ),
                ),
                ('sms_limit', models.IntegerField(default=0)),
                ('sms_limit_history', django.contrib.postgres.fields.jsonb.JSONField(default=dict)),
                ('sms_limit_alerts', django.contrib.postgres.fields.jsonb.JSONField(default=dict)),
                ('livespace_company_id', models.CharField(max_length=40, blank=True)),
                ('livespace_contact_id', models.CharField(max_length=40, blank=True)),
                ('locked_limit_hourly', models.IntegerField(null=True, blank=True)),
            ],
            options={
                'verbose_name': 'Business',
                'verbose_name_plural': 'Businesses',
            },
        ),
        migrations.CreateModel(
            name='BusinessCalendar',
            fields=[
                (
                    'id',
                    models.AutoField(
                        serialize=False, primary_key=True, db_column='business_calendar_id'
                    ),
                ),
                (
                    'type',
                    models.CharField(
                        max_length=1,
                        db_column='calendar_type',
                        choices=[
                            ('B', 'Blockout Hours'),
                            ('O', 'Open Hours'),
                            ('D', 'Blocked Days'),
                        ],
                    ),
                ),
                ('valid_from', models.DateTimeField(default=lib.tools.tznow)),
                ('valid_till', models.DateTimeField(null=True, blank=True)),
            ],
            options={
                'verbose_name': 'Business Calendar',
                'verbose_name_plural': 'Business Calendars',
            },
        ),
        migrations.CreateModel(
            name='BusinessCalendarEntry',
            fields=[
                (
                    'id',
                    models.AutoField(
                        serialize=False, primary_key=True, db_column='business_calendar_entry_id'
                    ),
                ),
                (
                    'day_of_the_week',
                    models.PositiveSmallIntegerField(
                        blank=True,
                        null=True,
                        choices=[
                            (1, 'Monday'),
                            (2, 'Tuesday'),
                            (3, 'Wednesday'),
                            (4, 'Thursday'),
                            (5, 'Friday'),
                            (6, 'Saturday'),
                            (0, 'Sunday'),
                        ],
                    ),
                ),
                ('day_from', models.DateField(null=True, blank=True)),
                ('day_to', models.DateField(null=True, blank=True)),
                ('time_from', lib.fields.time_24_hour_field.Time24HourField(null=True, blank=True)),
                ('time_to', lib.fields.time_24_hour_field.Time24HourField(null=True, blank=True)),
            ],
            options={
                'verbose_name': 'Business Calendar Entry',
                'verbose_name_plural': 'Business Calendar Entries',
            },
        ),
        migrations.CreateModel(
            name='BusinessCategory',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True, db_index=True)),
                ('deleted', models.DateTimeField(null=True, blank=True)),
                (
                    'id',
                    models.AutoField(serialize=False, primary_key=True, db_column='category_id'),
                ),
                ('name', models.CharField(max_length=50)),
                (
                    'keywords',
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.CharField(max_length=100), null=True, size=None
                    ),
                ),
                ('order', models.IntegerField(null=True, blank=True)),
                ('description', models.TextField(null=True, blank=True)),
                ('title', models.CharField(max_length=150, null=True, blank=True)),
                ('marketplace_url', models.CharField(max_length=150, null=True, blank=True)),
                ('footer', models.TextField(null=True, blank=True)),
                ('livespace_group_id', models.CharField(max_length=100, null=True, blank=True)),
            ],
            options={
                'ordering': ['order', 'name'],
                'verbose_name': 'Business Category',
                'verbose_name_plural': 'Business Categories',
            },
        ),
        migrations.CreateModel(
            name='BusinessCustomerInfo',
            fields=[
                (
                    'id',
                    models.AutoField(
                        serialize=False, primary_key=True, db_column='business_customer_info_id'
                    ),
                ),
                ('recurring', models.NullBooleanField(default=None)),
                (
                    'bookmarked',
                    models.BooleanField(
                        default=False, help_text='Added to the favourite businesses'
                    ),
                ),
                ('bookmarked_date', models.DateTimeField(null=True, blank=True)),
                (
                    'business_secret_note',
                    models.CharField(
                        help_text='Secret note made by business about the customer.',
                        max_length=300,
                        verbose_name='business secret note',
                        blank=True,
                    ),
                ),
                (
                    'blacklisted',
                    models.BooleanField(
                        default=False,
                        help_text='If checked, no more bookings by this customer will be allowed in this business',
                        verbose_name='blacklisted',
                    ),
                ),
                (
                    'visible_in_business',
                    models.BooleanField(
                        default=True,
                        help_text="If checked, customer will appear in business' customers list",
                        verbose_name='visible in business',
                    ),
                ),
                (
                    'first_name',
                    models.CharField(max_length=30, verbose_name='first name', blank=True),
                ),
                (
                    'last_name',
                    models.CharField(max_length=30, verbose_name='last name', blank=True),
                ),
                (
                    'email',
                    models.EmailField(
                        blank=True,
                        max_length=75,
                        verbose_name='e-mail address',
                        validators=[django.core.validators.EmailValidator()],
                    ),
                ),
                (
                    'cell_phone',
                    lib.fields.phone_number.BooksyPhoneNumberField(max_length=50, blank=True),
                ),
                (
                    'address_line_1',
                    models.CharField(max_length=100, verbose_name='address line 1', blank=True),
                ),
                (
                    'address_line_2',
                    models.CharField(max_length=100, verbose_name='address line 2', blank=True),
                ),
            ],
            options={
                'verbose_name': 'Business Customer Card',
                'verbose_name_plural': 'Business Customer Cards',
            },
        ),
        migrations.CreateModel(
            name='BusinessFacebookPage',
            fields=[
                (
                    'id',
                    models.CharField(
                        max_length=20,
                        serialize=False,
                        verbose_name='Facebook Page ID',
                        primary_key=True,
                        db_column='fb_page_id',
                    ),
                ),
            ],
            options={
                'verbose_name': 'Business Facebook Page',
                'verbose_name_plural': 'Business Facebook Pages',
            },
        ),
        migrations.CreateModel(
            name='BusinessHours',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True, db_index=True)),
                ('deleted', models.DateTimeField(null=True, blank=True)),
                (
                    'id',
                    models.AutoField(
                        serialize=False, primary_key=True, db_column='business_hours_id'
                    ),
                ),
                (
                    'day_of_the_week',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, 'Monday'),
                            (2, 'Tuesday'),
                            (3, 'Wednesday'),
                            (4, 'Thursday'),
                            (5, 'Friday'),
                            (6, 'Saturday'),
                            (0, 'Sunday'),
                        ]
                    ),
                ),
                ('hour_from', lib.fields.time_24_hour_field.Time24HourField()),
                ('hour_till', lib.fields.time_24_hour_field.Time24HourField()),
                (
                    'type',
                    models.CharField(
                        max_length=1,
                        db_column='hour_type',
                        choices=[('B', 'Blockout'), ('O', 'Open')],
                    ),
                ),
            ],
            options={
                'verbose_name': 'Business Hour',
                'verbose_name_plural': 'Business Hours',
            },
        ),
        migrations.CreateModel(
            name='BusinessPhoto',
            fields=[
                (
                    'id',
                    models.AutoField(
                        serialize=False, primary_key=True, db_column='business_photo_id'
                    ),
                ),
                ('description', models.TextField(blank=True)),
                ('order', models.PositiveSmallIntegerField()),
                (
                    'type',
                    models.CharField(
                        default='D',
                        max_length=1,
                        choices=[
                            ('T', 'Thumbnail'),
                            ('D', 'Detail'),
                            ('C', 'Cover'),
                            ('O', 'Original'),
                        ],
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='DelayedBookmarkEvent',
            fields=[
                (
                    'id',
                    models.AutoField(
                        verbose_name='ID', serialize=False, auto_created=True, primary_key=True
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True, db_index=True)),
                ('deleted', models.DateTimeField(null=True, blank=True)),
                (
                    'customer_email',
                    models.EmailField(
                        blank=True,
                        max_length=75,
                        verbose_name='e-mail address',
                        validators=[django.core.validators.EmailValidator()],
                    ),
                ),
                (
                    'customer_cell_phone',
                    lib.fields.phone_number.BooksyPhoneNumberField(max_length=50, blank=True),
                ),
                ('valid_till', models.DateTimeField(null=True, blank=True)),
                ('completed', models.BooleanField(default=False)),
            ],
            options={
                'abstract': False,
                'get_latest_by': 'updated',
            },
        ),
        migrations.CreateModel(
            name='ExternalAPISync',
            fields=[
                (
                    'api_name',
                    models.CharField(
                        max_length=40,
                        serialize=False,
                        primary_key=True,
                        choices=[
                            ('livespace', 'Livespace'),
                            ('livespace_deal_bookings', 'Livespace deal (bookings)'),
                            ('livespace_deal_old_businesses', 'Livespace deal (dead businesses)'),
                        ],
                    ),
                ),
                ('last_fetch_range', lib.fields.tstzrange.TsTzRangeField(null=True, blank=True)),
            ],
        ),
        migrations.CreateModel(
            name='Resource',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True, db_index=True)),
                ('deleted', models.DateTimeField(null=True, blank=True)),
                (
                    'id',
                    models.AutoField(serialize=False, primary_key=True, db_column='resource_id'),
                ),
                ('name', models.CharField(max_length=50)),
                (
                    'type',
                    models.CharField(max_length=1, choices=[('S', 'Staff'), ('R', 'Resource')]),
                ),
                ('visible', models.BooleanField(default=True)),
                ('description', models.TextField(null=True, blank=True)),
                ('active', models.BooleanField(default=True)),
                (
                    'staff_email',
                    models.EmailField(
                        blank=True,
                        max_length=75,
                        verbose_name='e-mail address',
                        validators=[django.core.validators.EmailValidator()],
                    ),
                ),
                (
                    'staff_cell_phone',
                    lib.fields.phone_number.BooksyPhoneNumberField(
                        max_length=50, null=True, blank=True
                    ),
                ),
                (
                    'staff_access_level',
                    models.CharField(
                        blank=True,
                        max_length=10,
                        null=True,
                        choices=[
                            ('owner', 'Owner'),
                            ('manager', 'Manager'),
                            ('reception', 'Reception'),
                            ('staff', 'Staff'),
                        ],
                    ),
                ),
                (
                    'business',
                    models.ForeignKey(
                        on_delete=models.deletion.CASCADE,
                        related_name='resources',
                        to='business.Business',
                    ),
                ),
                (
                    'photo',
                    models.ForeignKey(
                        on_delete=models.deletion.CASCADE, blank=True, to='photo.Photo', null=True
                    ),
                ),
            ],
            options={
                'verbose_name': 'Resource',
                'verbose_name_plural': 'Resources',
            },
        ),
        migrations.CreateModel(
            name='ResourceCalendar',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True, db_index=True)),
                ('deleted', models.DateTimeField(null=True, blank=True)),
                (
                    'id',
                    models.AutoField(
                        serialize=False, primary_key=True, db_column='resource_calendar_id'
                    ),
                ),
                ('valid_from', models.DateTimeField(auto_now_add=True, null=True)),
                (
                    'valid_till',
                    lib.fields.date_time_infinity_field.DateTimeInfinityField(
                        null=True, blank=True
                    ),
                ),
                (
                    'resource',
                    models.ForeignKey(
                        on_delete=models.deletion.CASCADE,
                        related_name='resource_calendars',
                        to='business.Resource',
                    ),
                ),
            ],
            options={
                'verbose_name': 'Resource Calendar',
                'verbose_name_plural': 'Resource Calendars',
            },
        ),
        migrations.CreateModel(
            name='ResourceTimeSlot',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True, db_index=True)),
                ('deleted', models.DateTimeField(null=True, blank=True)),
                (
                    'id',
                    models.AutoField(
                        serialize=False, primary_key=True, db_column='resource_time_slot_id'
                    ),
                ),
                (
                    'day_of_the_week',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, 'Monday'),
                            (2, 'Tuesday'),
                            (3, 'Wednesday'),
                            (4, 'Thursday'),
                            (5, 'Friday'),
                            (6, 'Saturday'),
                            (0, 'Sunday'),
                        ]
                    ),
                ),
                ('available_from', lib.fields.time_24_hour_field.Time24HourField()),
                ('available_to', lib.fields.time_24_hour_field.Time24HourField()),
                (
                    'resource_calendar',
                    models.ForeignKey(
                        on_delete=models.deletion.CASCADE,
                        related_name='resource_time_slots',
                        blank=True,
                        to='business.ResourceCalendar',
                        null=True,
                    ),
                ),
            ],
            options={
                'verbose_name': 'Resource Time Slot',
                'verbose_name_plural': 'Resource Time Slots',
            },
        ),
        migrations.CreateModel(
            name='Service',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True, db_index=True)),
                ('deleted', models.DateTimeField(null=True, blank=True)),
                ('id', models.AutoField(serialize=False, primary_key=True, db_column='service_id')),
                ('name', models.CharField(max_length=50)),
                ('order', models.IntegerField(default=0)),
                ('active', models.BooleanField(default=True)),
                (
                    'padding_type',
                    models.CharField(
                        blank=True,
                        max_length=1,
                        null=True,
                        choices=[('A', 'After'), ('B', 'Before'), ('C', 'Both before & after')],
                    ),
                ),
                ('padding_time', lib.interval.fields.IntervalField(null=True, blank=True)),
                ('description', models.CharField(max_length=5000, null=True, blank=True)),
                ('note', models.CharField(max_length=1500, null=True, blank=True)),
                (
                    'business',
                    models.ForeignKey(
                        on_delete=models.deletion.CASCADE,
                        related_name='services',
                        to='business.Business',
                    ),
                ),
            ],
            options={
                'verbose_name': 'Service',
                'verbose_name_plural': 'Services',
            },
        ),
        migrations.CreateModel(
            name='ServiceCategory',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True, db_index=True)),
                ('deleted', models.DateTimeField(null=True, blank=True)),
                (
                    'id',
                    models.AutoField(
                        serialize=False, primary_key=True, db_column='service_category_id'
                    ),
                ),
                ('name', models.CharField(max_length=50)),
                ('order', models.SmallIntegerField()),
                (
                    'business',
                    models.ForeignKey(
                        on_delete=models.deletion.CASCADE,
                        related_name='service_categories',
                        to='business.Business',
                    ),
                ),
            ],
            options={
                'verbose_name': 'Service Category',
                'verbose_name_plural': 'Service Categories',
            },
        ),
        migrations.CreateModel(
            name='ServiceVariant',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True)),
                ('updated', models.DateTimeField(auto_now=True, db_index=True)),
                ('deleted', models.DateTimeField(null=True, blank=True)),
                (
                    'id',
                    models.AutoField(
                        serialize=False, primary_key=True, db_column='service_variant_id'
                    ),
                ),
                ('valid_from', models.DateTimeField()),
                ('valid_till', models.DateTimeField(null=True, blank=True)),
                ('active', models.BooleanField(default=True)),
                ('duration', lib.interval.fields.IntervalField()),
                (
                    'type',
                    models.CharField(
                        blank=True,
                        max_length=1,
                        null=True,
                        choices=[
                            ('X', 'Fixed price'),
                            ('V', 'Varies'),
                            ('D', "Don't show"),
                            ('F', 'Free'),
                            ('S', 'Price starts at'),
                        ],
                    ),
                ),
                (
                    'price',
                    models.DecimalField(null=True, max_digits=10, decimal_places=2, blank=True),
                ),
                ('time_slot_interval', lib.interval.fields.IntervalField(null=True, blank=True)),
                (
                    'service',
                    models.ForeignKey(
                        on_delete=models.deletion.CASCADE,
                        related_name='service_variants',
                        to='business.Service',
                    ),
                ),
            ],
            options={
                'verbose_name': 'Service Variant',
                'verbose_name_plural': 'Service Variants',
            },
        ),
        migrations.CreateModel(
            name='StafferLockedAccess',
            fields=[
                (
                    'id',
                    models.AutoField(
                        verbose_name='ID', serialize=False, auto_created=True, primary_key=True
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True)),
                (
                    'field_name',
                    models.CharField(
                        max_length=10,
                        choices=[
                            ('email', 'Access to email'),
                            ('phone', 'Access to email'),
                            ('alert', 'Alert to Buisness owner has been send'),
                        ],
                    ),
                ),
                (
                    'business',
                    models.ForeignKey(
                        on_delete=models.deletion.CASCADE, related_name='+', to='business.Business'
                    ),
                ),
                (
                    'business_customer_info',
                    models.ForeignKey(
                        on_delete=models.deletion.CASCADE,
                        related_name='+',
                        to='business.BusinessCustomerInfo',
                    ),
                ),
                (
                    'staffer',
                    models.ForeignKey(
                        on_delete=models.deletion.CASCADE,
                        related_name='+',
                        blank=True,
                        to='business.Resource',
                        null=True,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='TempImage',
            fields=[
                (
                    'id',
                    models.AutoField(
                        verbose_name='ID', serialize=False, auto_created=True, primary_key=True
                    ),
                ),
                ('image', models.ImageField(upload_to='.')),
                ('img_ratio', models.CharField(max_length=255, default='img_ratio')),
                (
                    'img_ratio_rectangle',
                    models.CharField(max_length=255, default='img_ratio_rectangle'),
                ),
                (
                    'business',
                    models.ForeignKey(
                        on_delete=models.deletion.CASCADE,
                        blank=True,
                        to='business.Business',
                        null=True,
                    ),
                ),
                (
                    'original_bizphoto',
                    models.ForeignKey(
                        on_delete=models.deletion.CASCADE,
                        blank=True,
                        to='business.BusinessPhoto',
                        null=True,
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name='service',
            name='service_category',
            field=models.ForeignKey(
                on_delete=models.deletion.CASCADE,
                related_name='services',
                blank=True,
                to='business.ServiceCategory',
                null=True,
            ),
        ),
        migrations.AddField(
            model_name='resource',
            name='services',
            field=models.ManyToManyField(related_name='resources', to='business.Service'),
        ),
    ]
