# Generated by Django 1.11.7 on 2018-02-28 15:47
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0126_merge_20180228_1136'),
    ]

    operations = [
        migrations.AlterField(
            model_name='business',
            name='renting_venue',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name='contractors',
                to='business.Business',
                verbose_name='Umbrella Venue',
            ),
        ),
        migrations.AlterField(
            model_name='business',
            name='venue_type',
            field=models.CharField(
                choices=[('OW', 'Common Business'), ('SS', 'Salon Suite'), ('SA', 'Salon Place')],
                db_index=True,
                default='OW',
                max_length=2,
            ),
        ),
        migrations.AlterField(
            model_name='businesscustomerinfo',
            name='client_type',
            field=models.CharField(
                blank=True,
                choices=[
                    ('UN', 'Unknown'),
                    ('BD', 'Added by business directly'),
                    ('BP', 'Added by business via cell phone'),
                    ('BI', 'Added by business by import'),
                    ('BV', 'Added by business via invite'),
                    ('BS', 'Added by business via subdomain/deeplink'),
                    ('BF', 'Added by facebook'),
                    ('BW', 'Added by widget'),
                    ('CR', 'Recurring customer'),
                    ('CN', 'New customer'),
                    ('GP', 'Google Partner'),
                    ('YP', 'Yelp Partner'),
                    ('LO', 'Legacy'),
                ],
                default='UN',
                max_length=2,
                null=True,
            ),
        ),
    ]
