from bo_obs.datadog.enums import BooksyTeams
from django.http.response import Http404
from rest_framework import status
from rest_framework.response import Response

from drf_api.base_views import BaseBooksySessionGenericAPIView
from lib.elasticsearch.consts import ESDocType
from lib.searchables.searchables import MultiSearch
from lib_drf.docs.schema import BooksyAutoSchema
from webapps.business.searchables.business.services import BusinessServiceSearchable
from webapps.business.searchables.serializers.business import BusinessServiceHitSerializer
from webapps.business.serializers.service import ServiceDetailsResponseSerializer
from webapps.pos.elasticsearch.addons import ServiceAddOnDocumentHitSerializer
from webapps.pos.searchables.addon_searchables import ServiceAddOnSearchable


class CustomerServiceDetailsViewSchema(BooksyAutoSchema):
    def get_responses(self, path, method):
        serializer = self.get_response_serializer(path, method)
        return {
            status.HTTP_200_OK: {
                'content': {
                    'application/json': {
                        'schema': self._get_reference(serializer),
                    },
                },
                'description': 'Service details',
            },
        }


class CustomerServiceDetailsView(BaseBooksySessionGenericAPIView):
    ADDONS_LIMIT = 1000

    booksy_teams = (BooksyTeams.CUSTOMER_SEARCH,)
    schema = CustomerServiceDetailsViewSchema()
    serializer_class = ServiceDetailsResponseSerializer

    def _prepare_service_search(self, business_id, service_id):
        return (
            BusinessServiceSearchable(
                ESDocType.BUSINESS,
                serializer=BusinessServiceHitSerializer,
            )
            .search(
                {
                    'business_id': business_id,
                    'service_id': service_id,
                }
            )
            .params(
                size=1,
            )
        )

    def _prepare_addons_search(self, business_id, service_id):
        return (
            ServiceAddOnSearchable(
                ESDocType.ADDON,
                serializer=ServiceAddOnDocumentHitSerializer,
            )
            .search(
                {
                    'business_id': business_id,
                    'service_id': service_id,
                }
            )
            .params(
                size=self.ADDONS_LIMIT,
            )
            .sort('order', 'created')
        )

    # TODO: staffers should be filtered at search, but currently there is no index
    @staticmethod
    def _filter_staffers(staffers, service):
        staffer_ids = set(service.staffer_id or [])

        return [staffer for staffer in staffers if staffer.id in staffer_ids]

    @staticmethod
    def _filter_addons(addons):
        return [addon for addon in addons if addon.data.is_available_for_customer_booking]

    def get(self, request, business_pk: int, service_pk: int):
        search = (
            MultiSearch()
            .add(self._prepare_service_search(business_pk, service_pk))
            .add(self._prepare_addons_search(business_pk, service_pk))
        )
        response = search.execute()

        if len(response[0].hits.hits) == 0:
            raise Http404()

        business = response[0].hits.hits[0]
        service_category = business.inner_hits.service_category.hits.hits[0]
        service = service_category.inner_hits.service.hits.hits[0]
        staff = self._filter_staffers(
            [doc._source for doc in business.inner_hits.staff.hits.hits], service._source
        )
        addons = self._filter_addons([doc._source for doc in response[1].hits.hits])

        return Response(
            data=self.get_serializer(
                instance={
                    'business': business._source,
                    'service_category': service_category._source,
                    'service': service._source.to_dict(),
                    'staff': staff,
                    'addons': [addon.to_dict() for addon in addons],
                }
            ).data,
            status=status.HTTP_200_OK,
        )
