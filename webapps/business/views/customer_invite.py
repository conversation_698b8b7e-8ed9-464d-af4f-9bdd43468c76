from bo_obs.datadog.enums import BooksyTeams
from django.shortcuts import get_object_or_404
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from drf_api.base_views import BaseBooksySessionGenericAPIView
from drf_api.mixins import BusinessViewValidatorMixin, ResponseSerializerMixin
from drf_api.service.user.serializer import EmptySerializer
from lib.invite import CustomerInvitationResult
from webapps.business.models import Resource
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business_customer_info.serializers.customer_invite import (
    BusinessCustomerInviteResponse,
)


class BusinessCustomerInviteView(
    BusinessViewValidatorMixin,
    ResponseSerializerMixin,
    BaseBooksySessionGenericAPIView,
):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING, BooksyTeams.PROVIDER_ONBOARDING)
    permission_classes = (IsAuthenticated,)
    required_minimum_access_level = Resource.STAFF_ACCESS_LEVEL_ADVANCED

    response_serializer_class = BusinessCustomerInviteResponse
    serializer_class = EmptySerializer

    def post(self, request, business_pk, customer_pk):
        fetcher = self.get_fetcher(business_pk)

        bci = get_object_or_404(
            BusinessCustomerInfo,
            id=customer_pk,
            business_id=business_pk,
        )

        invitation_result: CustomerInvitationResult = bci.invite(
            staffer_invite=fetcher.access_level in Resource.STAFF_ACCESS_LEVELS_NOT_OWNER
        )

        was_invited_successfully = invitation_result in [
            CustomerInvitationResult.SUCCESS_SMS,
            CustomerInvitationResult.SUCCESS_PUSH,
            CustomerInvitationResult.SUCCESS_EMAIL,
        ]

        return Response(
            self.get_response_serializer(
                {
                    'was_invited_successfully': was_invited_successfully,
                    'invitation_result': invitation_result.value,
                    'result_description': invitation_result.label,
                }
            ).data
        )
