import elasticsearch_dsl as dsl
from django.conf import settings

from lib.elasticsearch.consts import ESDocType
from lib.elasticsearch.document import ILMDocument
from lib.elasticsearch.index import LifecycleManagementIndex
from lib.es_history.document import HistoryChangesMeta, HistoryDocumentMixin, NoRecurseField
from webapps.business.serializers.history import BusinessHistorySerializer


def category_properties():
    return {
        'id': dsl.Keyword(index=False),
        'name': dsl.Text(index=False),
        'internal_name': dsl.Text(index=False),
        'full_name': dsl.Text(index=False),
    }


class BusinessHistoryChangesDoc(dsl.InnerDoc, metaclass=HistoryChangesMeta):
    visible = dsl.Boolean(index=False)
    status = dsl.Keyword(index=False)
    payment_source = dsl.Keyword(index=False)
    paid_till = dsl.Date(index=False)
    trial_till = dsl.Date(index=False)
    name = dsl.Text(index=False)
    official_name = dsl.Text(index=False)
    name_short = dsl.Text(index=False)
    website = dsl.Text(index=False)
    facebook_link = dsl.Text(index=False)
    instagram_link = dsl.Text(index=False)
    ecommerce_link = dsl.Text(index=False)
    phone = dsl.Text(index=False)
    alert_phone = dsl.Text(index=False)
    description = dsl.Text(index=False)
    boost_status = dsl.Keyword(index=False)
    has_braintree = dsl.Boolean(index=False)

    location = dsl.Object(
        properties={
            'zipcode': dsl.Keyword(index=False),
            'address': dsl.Text(index=False),
            'address2': dsl.Text(index=False),
            'city': dsl.Text(index=False),
            'latitude': dsl.Float(index=False),
            'longitude': dsl.Float(index=False),
            'time_zone_name': dsl.Keyword(index=False),
        }
    )

    owner = dsl.Object(
        properties={
            'id': dsl.Keyword(index=False),
            'first_name': dsl.Text(index=False),
            'last_name': dsl.Text(index=False),
            'email': dsl.Keyword(index=False),
        },
    )

    primary_category = dsl.Object(
        properties=category_properties(),
    )
    categories = NoRecurseField(
        dsl.Object(
            properties=category_properties(),
        )
    )

    traveling = dsl.Object(
        properties={
            'price': dsl.Keyword(index=False),
            'price_type': dsl.Keyword(index=False),
            'distance': dsl.Integer(index=False),
            'distance_unit': dsl.Keyword(index=False),
            'hide_address': dsl.Boolean(index=False),
            'traveling_only': dsl.Boolean(index=False),
            'policy': dsl.Text(index=False),
        },
    )


class BusinessHistoryDocument(HistoryDocumentMixin, ILMDocument):
    class Meta:
        serializer = BusinessHistorySerializer
        routing = 'id'

    changes = dsl.Object(BusinessHistoryChangesDoc)


class BusinessHistoryIndex(LifecycleManagementIndex):
    name = f'business_history_index_{settings.OLD_ES_SUFFIX}'
    index_settings = {
        'max_ngram_diff': 20,
    }
    documents = {
        ESDocType.BUSINESS_HISTORY: BusinessHistoryDocument,
    }
    cluster_settings = {}
    policy = dict(
        phases=dict(
            hot=dict(
                actions=dict(
                    rollover=dict(max_size='1GB', max_age='1d'),
                ),
            ),
            warm=dict(
                min_age='5d',
                actions=dict(
                    shrink=dict(number_of_shards=1),
                ),
            ),
            cold=dict(
                min_age='30d',
                actions=dict(
                    freeze={},
                ),
            ),
            delete=dict(
                min_age='90d',
                actions=dict(
                    delete={},
                ),
            ),
        ),
    )
