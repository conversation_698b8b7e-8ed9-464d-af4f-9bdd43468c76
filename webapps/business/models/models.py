# pylint: disable=too-many-lines
# pylint: disable=C0209
from __future__ import annotations

import calendar
import collections
import copy
import datetime
import json
import logging
import typing as t
from decimal import Decimal
from difflib import Differ
from itertools import islice
from urllib.parse import urlencode, urljoin

import jinja2
from dateutil import tz as dateutil_tz
from dateutil.relativedelta import relativedelta
from dirtyfields import DirtyFieldsMixin
from django.conf import settings
from django.core.cache import cache
from django.contrib.postgres.aggregates import ArrayAgg
from django.core.exceptions import (
    NON_FIELD_ERRORS,
    ValidationError,
)
from django.core.validators import (
    MaxValueValidator,
    MinValueValidator,
    RegexValidator,
    validate_email,
)
from django.db import models, transaction
from django.db.models import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Case,
    Count,
    DO_NOTHING,
    F,
    J<PERSON><PERSON>ield,
    Q,
    Value,
    When,
)
from django.db.models.expressions import Exists, OuterRef, Subquery
from django.db.models.functions import Concat
from django.db.models.signals import (
    m2m_changed,
    post_delete,
    post_save,
)
from django.db.utils import IntegrityError
from django.template.defaultfilters import truncatechars
from django.utils.encoding import (
    force_str,
)
from django.utils.functional import cached_property, classproperty
from django.utils.html import escape, format_html
from django.utils.translation import gettext, gettext_lazy as _

import lib.tools

from country_config import Country, CountryConfig
from lib import deeplink  # workaround for mocking
from lib.admin_helpers import admin_link
from lib.db import READ_ONLY_DB, get_cursor, using_db_for_reads
from lib.deeplink import BranchIOAppTypes, DeepLinkCache
from lib.elasticsearch.consts import ESDocType
from lib.elasticsearch.tools import ESDocMixin
from lib.enums import SMSPaidEnum, StrChoicesEnum, StrEnum
from lib.es_history.models import HistoryMixin
from lib.feature_flag.adapter import UserData
from lib.feature_flag.enums import ExperimentVariants
from lib.feature_flag.experiment.invites import EffortlessInvitesExperiment
from lib.feature_flag.feature import (
    LoyaltyProgramFlag,
    SetSMSLimitForActivePeriodFlag,
)
from lib.feature_flag.feature.boost import BoostDontDisableServicePromotionsWhenEndingBoost
from lib.feature_flag.feature.business import (
    CacheBusinessInviteDeeplink,
    CheckUpdateBusinessDeeplinkFlag,
    PreventResetCurrentStafferServicesFlag,
)
from lib.feature_flag.killswitch import HintsAndWalkThroughEventsPublishingFlag
from lib.feature_flag.feature.payment import AutomaticKYCConsentAssignmentFlag
from lib.fields import PositiveBigIntegerField
from lib.fields.phone_number import BooksyPhoneNumberField, get_prep_value_form
from lib.frontdesk import is_frontdesk_enabled_for_country
from lib.interval import fields as interval_fields
from lib.models import (
    ArchiveManager,
    ArchiveModel,
    ArchiveOneToOneField,
    AutoUpdateManager,
    AutoUpdateQuerySet,
    ChangeArchivedModel,
)
from lib.rivers import River, bump_document
from lib.serializers import safe_get
from lib.tools import (
    build_or_update_url,
    mp_deeplink,
    tznow,
    webbiz_deeplink,
)
from lib.unicode_utils import (
    slugify,
    text_slug,
    unicode_smart_truncate,
)
from lib.validators import (
    validate_latitude,
    validate_longitude,
)
from service.business.publishers.loyalty_program import LoyaltyProgramBusinessChangedPublisher
from webapps import consts
from webapps.booking.enums import BookingMode as BM
from webapps.boost.enums import BoostAppointmentStatus
from webapps.business.cache import get_cached_invite_deeplink
from webapps.business.consts import (
    CATEGORIES_WITH_PATIENT_FILES,
    LAST_BOOKINGS_PERIOD,
    TOP_SERVICES_MINIMAL_BOOKINGS_COUNT,
    TOP_SERVICES_NUMBER,
    SEO_DEEPLINK_TYPES,
    LINK_CODE,
    INTEGRATIONS_DEEPLINK_KEY,
    MEDICAL_CATEGORIES,
    MOBILE_DEEPLINK_KEY,
    DESKTOP_URL_KEY,
    DL_LINK,
    FALLBACK_URL_KEY,
    EXTEND_TRIAL_EXPERIMENT_CATEGORIES,
)
from webapps.business.enums import (
    AutoCancellationReasonType,
    BoostPaymentSource,
    BusinessIntegration,
    CancellationReasonType,
    CancellationType,
    CustomData,
    FacebookFBEConnect,
    PriceType,
    ResourceType,
    StaffAccessLevels,
    SeoUrl,
    BusinessCategoryEnum,
)
from webapps.business.events import (
    business_activated_event,
    business_ended_promotion_event,
    business_primary_category_saved_event,
)
from webapps.business.exceptions import BusinessHasNoPos
from webapps.business.messages.business import (
    BusinessCardChangedMessage,
    BusinessPrimaryDataChangedMessage,
)
from webapps.business.mixins.t_business import TestBusiness
from webapps.business.models.service import (
    Service,
    ServicePromotion,
    ServiceVariant,
)
from webapps.business.service_price import ServicePrice
from webapps.business_consents.enums import ConsentCode
from webapps.business_consents.ports import BusinessConsentsPort
from webapps.business_related.models import Amenities
from webapps.ecommerce.adapters import EcommercePermissionAdapter, EcommerceBusinessAdapter
from webapps.images.enums import ImageTypeEnum
from webapps.images.mixins import PhotoModelMixin
from webapps.kill_switch.models import KillSwitch
from webapps.market_pay.enums import AccountHolderStatus
from webapps.notification.enums import DeeplinkFeature
from webapps.notification.scenarios.base import ScenarioSkipped
from webapps.notification.scenarios.scenarios_access_rights import (
    StafferInvitationScenario,
    StafferLockedAccessScenario,
)
from webapps.pos.enums import POSPlanPaymentTypeEnum, PaymentTypeEnum
from webapps.profile_completeness.events import (
    step_attract_using_boost,
)
from webapps.public_partners.models import OAuth2Installation
from webapps.public_partners.query_sets import PartnerAppDataQuerySet
from webapps.schedule.ports import get_resource_default_hours
from webapps.segment.tasks import (
    analytics_boost_on_branchio_task,
    analytics_boost_off_branchio_task,
    analytics_boost_on_off_gtm_task,
    analytics_boost_on_off_task,
    analytics_business_contact_preferences_updated_task,
    analytics_business_status_updated_task,
)
from webapps.structure.constants import (
    TZ_NAMES_CHOICES,
)
from webapps.subdomain_grpc.client import SubdomainGRPC, SubdomainGRPCError
from webapps.subdomain_grpc.subdomain_cache import SubdomainsCache
from webapps.turntracker.ports import get_turntracker_enabled
from webapps.user.const import FORCED_LAST_NAME, Gender


log = logging.getLogger('booksy.images')
logger_deeplink_ms = logging.getLogger('booksy.deeplink_ms')

MAX_NUMBER_SLOTS = 7
MAX_NUMBER_CH_DESCRIPTION_MP = 1200
MAX_NUMBER_CH_TITLE_MP = 120
MAX_NUMBER_CH_BUTTON_MP = 120

TRIAL_TIME_PROMO = 'TT'
TRIAL_USER_PROMO = 'TU'
TRIAL_ENDED_PROMO = 'TE'
TRIAL_CANCELED_PROMO = 'TC'
PAID_PROMO = 'P'

BUSINESS_PROMOTION_TYPES = (
    (TRIAL_TIME_PROMO, _('Trial Time')),
    (TRIAL_USER_PROMO, _('Trial User')),
    (TRIAL_ENDED_PROMO, _('Trial Ended')),
    (TRIAL_CANCELED_PROMO, _('Trial Canceled')),
    (PAID_PROMO, _('Paid')),
)

TRIAL_STATUS_ACTIVE = 'trial_active'
TRIAL_STATUS_NOT_ACTIVE = 'trial_not_active'
TRIAL_STATUS_END = 'trial_end'
TRIAL_STATUS_END_CONFIRMED = 'trial_end_confirmed'


class BusinessVersion(models.Model):
    id = models.AutoField(primary_key=True, db_column='businessversion_id')
    business = models.OneToOneField('Business', on_delete=models.CASCADE)
    version = models.DateTimeField(auto_now=True)
    booking_lock = models.BooleanField(null=False, blank=False, default=False)

    @classmethod
    def create_booking_lock(cls, business_id):
        if KillSwitch.killed(KillSwitch.System.BUSINESS_VERSION_LOCKS):
            return tznow()
        lock, _created = cls.objects.get_or_create(business_id=business_id)
        if lock.has_booking_lock:
            raise IntegrityError('Another booking in progress')
        lock.booking_lock = True
        lock.save()
        return lock.version

    @classmethod
    def release_booking_lock(cls, business_id, version):
        if KillSwitch.killed(KillSwitch.System.BUSINESS_VERSION_LOCKS):
            return tznow()
        lock, _created = cls.objects.get_or_create(business_id=business_id)
        if not (lock.booking_lock and lock.version == version):
            raise IntegrityError('Booking lock verification failed')
        lock.booking_lock = False
        lock.save()
        return lock.version

    @property
    def has_booking_lock(self):
        if KillSwitch.killed(KillSwitch.System.BUSINESS_VERSION_LOCKS):
            return False
        return self.booking_lock and (
            (lib.tools.tznow() - self.version).total_seconds()
            < settings.BUSINESS_BOOKING_LOCK_MAX_TIME
        )

    @classmethod
    def update(cls, business_id):
        if KillSwitch.killed(KillSwitch.System.BUSINESS_VERSION_LOCKS):
            return None
        lock, _created = cls.objects.get_or_create(business_id=business_id)
        if lock.has_booking_lock:
            # don't update when booking in progress
            return None
        cls.objects.update_or_create(business_id=business_id, defaults={'booking_lock': False})

    @property
    def timestamp(self):
        return lib.tools.datetime_to_timestamp(self.version)


# unfortunately can'y moved to separate file because of
# recursive import it is not even my function
def get_default_sms_notification_status():
    if settings.API_COUNTRY in settings.COUNTRY__BIZ__SMS_NOTIFICATION_ENABLED:
        return Business.SMSStatus.ENABLED

    return Business.SMSStatus.DISABLED


class HoursUpdateMixin:
    def update_hours(self, field_name, data):
        if data is None:
            data = []
        hours = getattr(self, field_name)

        old_hours = {h.day_of_the_week: h.hours for h in hours.all()}
        new_hours = [d['day_of_the_week'] for d in data]
        to_delete = {old_hour for old_hour in set(old_hours.keys()) if old_hour not in new_hours}
        if to_delete:
            hours.filter(day_of_the_week__in=to_delete).delete()

        for day in data:
            if old_hours.get(day['day_of_the_week']) != day.get('hours'):
                hours.update_or_create(
                    day_of_the_week=day['day_of_the_week'],
                    defaults={'hours': day['hours']},
                )

    def update_hours_from_model(self, field_name, related_manager):
        data = [
            {'day_of_the_week': h.day_of_the_week, 'hours': h.hours} for h in related_manager.all()
        ]
        self.update_hours(field_name, data)


def calculate_trial_from(business):
    if settings.STATUS_FLOW__TRIAL_COUNT_FROM_DATE_CREATED:
        return business.created

    return tznow()


def calculate_trial_till(business):
    trial_duration = datetime.timedelta(days=settings.STATUS_FLOW__TRIAL_DURATION)
    trial_from = calculate_trial_from(business)

    if business.trial_till is not None:
        return business.trial_till  # don't change if set

    if trial_from is None:
        return None

    return trial_from + trial_duration


def get_default_trial_till():
    return None


def get_default_api_country():
    return settings.API_COUNTRY


def get_default_boost_payment_source():
    if settings.API_COUNTRY in settings.BRAINTREE_SKIP_COUNTRIES:
        return BoostPaymentSource.OFFLINE

    return BoostPaymentSource.ONLINE


class BusinessQuerySet(AutoUpdateQuerySet):
    def normal_businesses(self):
        return self.exclude(status=Business.Status.B_LISTING)

    def annotate_visible_in_marketplace(self):
        return self.annotate(
            db_active_and_visible=Case(
                When(
                    active=True,
                    visible=True,
                    then=True,
                ),
                default=False,
                output_field=BooleanField(),
            ),
            has_services=Exists(
                ServiceVariant.objects.filter(
                    service__business=OuterRef('pk'),
                    service__active=True,
                    resources__active=True,
                    resources__visible=True,
                    resources__deleted__isnull=True,
                    service__is_available_for_customer_booking=True,
                    active=True,
                )
            ),
            has_resources=Exists(
                Resource.objects.filter(
                    business=OuterRef('pk'),
                    active=True,
                    deleted__isnull=True,
                    visible=True,
                )
            ),
        ).annotate(
            visible_in_marketplace=Case(
                When(
                    db_active_and_visible=True,
                    has_services=True,
                    has_resources=True,
                    then=True,
                ),
                default=False,
                output_field=BooleanField(),
            ),
        )

    def indexed_in_elasticsearch(self):
        return (
            self.filter(
                active=True,
                visible=True,
            )
            .annotate_visible_in_marketplace()
            .filter(
                Q(status=Business.Status.VENUE)
                | Q(status=Business.Status.B_LISTING)
                | Q(
                    Q(has_services=True) & Q(has_resources=True),
                )
            )
        )

    def annotate_invitation_status(self, referrer_id: int):
        from webapps.b2b_referral.models import B2BReferral

        invitation_status_subquery = Subquery(
            B2BReferral.objects.filter(referrer_id=referrer_id, invited_id=OuterRef('pk')).values(
                'status'
            )[:1]
        )

        return self.annotate(
            renting_venue_invitation_status=invitation_status_subquery,
        )


class BusinessManager(AutoUpdateManager.from_queryset(BusinessQuerySet)):
    pass


# pylint: disable=too-many-instance-attributes, too-many-public-methods
class Business(
    HistoryMixin,
    ArchiveModel,
    DirtyFieldsMixin,
    ESDocMixin,
    TestBusiness,
):
    es_doc_type = ESDocType.BUSINESS
    extra_es_doc_types = [
        ESDocType.OPEN_HOURS,
    ]
    river = River.BUSINESS

    @classproperty
    def history_document_class(cls):  # pylint: disable=no-self-argument
        # Circular import
        from webapps.business.elasticsearch.history import BusinessHistoryDocument

        return BusinessHistoryDocument

    class Meta:
        verbose_name = _('Business')
        verbose_name_plural = _('Businesses')

    # ENUMS #

    class Verification(StrChoicesEnum):
        NOT_VERIFIED = '?', _('Not Verified')
        VERIFIED = 'V', _('Verified')
        NEGATIVE = 'N', _('Negative')
        WRONG_ADDRESS = 'W', _('Wrong Address')

    class Moderation(StrChoicesEnum):
        NOT_MODERATED = 'N', 'Not moderated'
        LOW = 'L', 'Low'
        MEDIUM = 'M', 'Medium'
        HIGH = 'H', 'High'

    class BookingMode(StrChoicesEnum):
        AUTO = BM.AUTO, _("Automatic (no overbooking, automatic confirmation)")
        MANUAL = BM.MANUAL, _("Manual (overbooking, full time slots available)")
        SEMIAUTO = BM.SEMIAUTO, _("Semi-automatic")

    class SMSPriority(StrChoicesEnum):
        ONLY_PUSH = 'O', _("Off")
        PREFER_PUSH = 'P', _("Eco")
        PREFER_SMS = 'S', _("On")

    class SMSStatus(StrChoicesEnum):
        DISABLED = 'D', _("Disabled")
        PENDING = 'P', _("Pending")
        ENABLED = 'E', _("Enabled")

    class PaymentSource(StrChoicesEnum):
        UNKNOWN = 'U', 'Unknown'  # customer hasn't chosen yet
        OFFLINE = 'O', 'Offline'
        BRAINTREE = 'B', 'Braintree'
        BRAINTREE_BILLING = 'R', 'Booksy Billing'
        ITUNES = 'I', 'Apple iTunes'
        PLAY = 'P', 'Google Play'

        @classmethod
        def online_sources(cls):
            return {
                cls.BRAINTREE,
                cls.PLAY,
                cls.ITUNES,
                cls.BRAINTREE_BILLING,
            }

        @classmethod
        def mobile_sources(cls):
            return {
                cls.PLAY,
                cls.ITUNES,
            }

    class Status(StrChoicesEnum):
        SETUP = 'S', _("Setup")
        TRIAL = 'T', _("Trial")
        TRIAL_BLOCKED = 'F', _("Trial End Blocked")
        PAID = 'P', _("Paid Active")
        OVERDUE = 'O', _("Payment Overdue Active")
        BLOCKED_OVERDUE = 'L', _("Payment Overdue Blocked")
        SUSPENDED = 'H', _("Suspended")
        CHURNED = 'R', _("Churned")
        BLOCKED = 'B', _("Business Invalid")
        # special statuses
        VENUE = 'V', _('Renting Venue')
        B_LISTING = 'C', _('B Listing')
        DEMO = 'D', _('Demo')
        DEMO_TEMPLATE = 'M', _('Demo Template')

        @classmethod
        def activity(cls):
            """Business.Status to Business.active mapping.

            Business.active field value is set based on Business.status
            in most cases (see Business.save()).
            If it's not automatic, this mapping returns None.

            """
            return {
                # active statuses
                cls.TRIAL: True,
                cls.PAID: True,
                cls.OVERDUE: True,
                cls.VENUE: True,
                # inactive statuses
                cls.SETUP: False,
                cls.BLOCKED: False,
                # special statuses
                cls.DEMO: True,
                cls.DEMO_TEMPLATE: True,
                cls.TRIAL_BLOCKED: False,
                cls.BLOCKED_OVERDUE: False,
                # b-listing activity is at moderators discretion
                cls.B_LISTING: None,  # ACHTUNG!
                cls.CHURNED: False,
                cls.SUSPENDED: False,
            }

        @classmethod
        def paid_statuses(cls):
            """A list of statuses indicating a merchant has paid at least once
            for Booksy subscription.

            Used for sms limits.

            """
            return {
                cls.PAID,
                cls.OVERDUE,
                cls.BLOCKED_OVERDUE,
            }

        @classmethod
        def never_paid_statuses(cls):
            """A list of statuses when we can assume a merchant has never
            paid anything (ie. doesn't have a subscription).

            Used for sms limits.

            As for Business.Status.BLOCKED, we don't really know, whether
            the Business has ever paid without checking its subscriptions.
            But as while being blocked it can't do anything
            it shouldn't matter so much.
            """
            return set(cls.values()) - cls.paid_statuses()

        @classmethod
        def analytics_never_paid_statuses(cls):
            """
            A list of statuses used by analytics to determine if we achieved
            status PAID for the first time.
            """
            return {
                cls.TRIAL,
                cls.TRIAL_BLOCKED,
            }

        @classmethod
        def incomplete(cls):
            """A list of statuses when Business profile is incomplete yet."""
            return {
                cls.SETUP,
                cls.VENUE,
                cls.DEMO,
                cls.DEMO_TEMPLATE,
                cls.B_LISTING,
            }

        @classmethod
        def active_statuses(cls):
            return {
                cls.TRIAL,
                cls.PAID,
                cls.OVERDUE,
                cls.VENUE,
            }

    class BoostStatus(StrChoicesEnum):
        ENABLED = 'E', _('Enabled')
        DISABLED = 'D', _('Disabled')
        ACTIVATION_PENDING = 'A', _('Activation Pending')
        SUSPENSION_PENDING = 'S', _('Deactivation Pending')

        @classmethod
        def active_statuses(cls):
            return {
                cls.ENABLED,
                cls.SUSPENSION_PENDING,
            }

    class Package(StrChoicesEnum):
        LITE = 'L', 'Lite'
        PRO = 'P', 'Pro'
        UNKNOWN = 'U', _('Unknown')

    ELIGIBLE_PACKAGE = (Package.LITE, Package.PRO)

    class ProfileType(StrEnum):
        NORMAL = 'normal'
        B_CARD = 'b_card'

    DEFAULT_PROFILE_TYPE = ProfileType.NORMAL

    NAME_MAX_LENGTH = 120

    # FIELDS #

    id = models.AutoField(primary_key=True, db_column='business_id')
    owner_id: int
    owner = models.ForeignKey(
        'user.User',
        null=False,
        blank=False,
        related_name='businesses',
        on_delete=models.PROTECT,
    )
    region = models.ForeignKey(
        'structure.Region',
        null=True,
        blank=True,
        on_delete=models.PROTECT,
        related_name='businesses',
    )
    # TODO: Remove when UTT2 will be fully operational
    categories = models.ManyToManyField(
        'business.BusinessCategory',
        related_name='businesses',
        blank=False,
    )
    # TODO: Remove '_utt' from name when UTT2 will be fully operational
    categories_utt = models.ManyToManyField(
        'utt.Category',
        related_name='businesses',
        blank=False,
        db_table='business_business_categories_utt_',
    )
    # TODO: Update related model when UTT2 will be fully operational
    primary_category = models.ForeignKey(
        'business.BusinessCategory',
        related_name='primary_businesses',
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )
    seo_region = models.ForeignKey(
        'structure.Region',
        related_name='seo_businesses',
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
        help_text='Optional region used in merchant profile link.',
    )
    # TODO: Remove when UTT2 will be fully operational
    treatments = models.ManyToManyField(
        'business.BusinessCategory',
        related_name='treatment_businesses',
        blank=True,
    )
    # TODO: Remove '_utt' from name when UTT2 will be fully operational
    treatments_utt = models.ManyToManyField(
        'utt.Treatment',
        related_name='businesses',
        blank=True,
    )

    # 41357 Umbrella venues
    # Business with status venue can have other business
    renting_venue = models.ForeignKey(  # pylint: disable=no-member
        'self',
        # tenants == contractors
        related_name='contractors',
        blank=True,
        null=True,
        verbose_name="Umbrella Venue",
        on_delete=models.SET_NULL,
    )

    # 50023 BListings
    b_listing = models.OneToOneField(  # pylint: disable=no-member
        'self',
        related_name='source',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
    )
    source_email = models.EmailField(max_length=254, blank=True)

    name = models.CharField(max_length=250)
    name_short = models.CharField(max_length=250, null=True, blank=True)
    official_name = models.CharField(max_length=250, null=True, blank=True)
    umbrella_brand_name = models.CharField(max_length=250, null=True, blank=True)
    address = models.CharField(max_length=100, null=True, blank=True)
    address2 = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name='apartment_number',
        help_text='Apartment or suite number. Max 100 characters',
    )
    city = models.CharField(max_length=100, null=False, blank=True)
    zipcode = models.CharField(max_length=20, null=True, blank=True)
    booking_mode = models.CharField(
        max_length=1,
        choices=BookingMode.choices(),
        default=BookingMode.AUTO,
    )
    phone = BooksyPhoneNumberField(null=True)
    alert_phone = BooksyPhoneNumberField(null=True)
    longitude = models.FloatField(null=True, blank=True, validators=[validate_longitude])
    latitude = models.FloatField(null=True, blank=True, validators=[validate_latitude])
    reviews_rank_avg = models.FloatField(null=True, blank=True)
    reviews_count = models.IntegerField(null=True, blank=True)
    reviews_rank_score = models.FloatField(null=True, blank=True)
    website = models.TextField(null=True, blank=True)
    facebook_link = models.TextField(null=True, blank=True)
    instagram_link = models.TextField(null=True, blank=True)
    description = models.TextField(null=True, blank=True)
    contractor_description = models.TextField(null=True, blank=True)
    ecommerce_link = models.TextField(
        verbose_name='Link to E-Commerce',
        blank=True,
        null=True,
    )
    public_email = models.EmailField(max_length=75, null=True, blank=True)

    # START DEPRECATED
    credit_cards = models.TextField(null=True, blank=True)
    parking = models.CharField(max_length=50, null=True, blank=True)
    wheelchair_access = models.CharField(max_length=50, null=True, blank=True)
    pricing_level = models.PositiveSmallIntegerField(
        null=True,
        blank=True,
        choices=(
            (1, _("Low pricing level ($)")),
            (2, _("Normal pricing level ($$)")),
            (3, _("High pricing level ($$$)")),
        ),
    )
    # END DEPRECATED

    boost_status = models.CharField(
        max_length=1,
        choices=BoostStatus.choices(),
        default=BoostStatus.DISABLED,
    )
    receive_promotion_notifications = models.BooleanField(default=True)
    # Lead Time & Cancellation
    # NOTE: psycopg2 is based on timedelta, which does not support
    #       months & years. Since we do want to display months
    #       in some of the views, we ASSUME that (just like
    #       PostgreSQL does) one month is 30 days (but note
    #       that 30 days is not always 1 month...)
    booking_max_lead_time = interval_fields.IntervalField(
        null=False,
        blank=False,
        default=relativedelta(months=3),
    )
    booking_min_lead_time = interval_fields.IntervalField(
        null=False,
        blank=False,
        default=relativedelta(minutes=30),
    )
    booking_max_modification_time = interval_fields.IntervalField(
        null=False,
        blank=False,
        default=relativedelta(hours=1),
    )
    active = models.BooleanField(null=False, blank=False, default=False, db_index=True)
    active_from = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='Active from (UTC)',
    )
    active_till = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='Active till (UTC)',
    )
    paid_from = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='Paid from (UTC)',
    )
    paid_till = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='Paid till (UTC)',
    )
    status = models.CharField(
        max_length=1,
        choices=Status.choices(),
        default=Status.SETUP,
        db_index=True,
    )
    package = models.CharField(
        max_length=1,
        choices=Package.choices(),
        default=Package.UNKNOWN,
        db_index=False,
    )
    payment_source = models.CharField(
        max_length=1,
        choices=PaymentSource.choices(),
        default=PaymentSource.UNKNOWN,
        verbose_name='Payment with',
    )
    boost_payment_source = models.CharField(
        max_length=1,
        choices=BoostPaymentSource.choices(),
        default=get_default_boost_payment_source,
        verbose_name='Boost payment with',
    )
    has_braintree = models.BooleanField(default=False, blank=True)
    trial_till = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='Trial till (UTC)',
        default=get_default_trial_till,
    )
    overdue_till = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='Overdue till (UTC)',
    )
    registration_code = models.ForeignKey(
        "registrationcode.RegistrationCode",
        related_name='businesses',
        verbose_name=_('Registration code'),
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
    )
    visible = models.BooleanField(
        default=True,
        verbose_name='Visibility in marketplace',
    )
    visible_from = models.DateTimeField(
        verbose_name='Visible from in marketplace (UTC)',
        null=True,
        blank=True,
    )
    visible_delay_till = models.DateTimeField(
        # if None then no visibility delay is set
        verbose_name='Visible delay till (UTC)',
        null=True,
        blank=True,
    )
    # sms settings
    sms_notification_status = models.CharField(
        max_length=1,
        choices=SMSStatus.choices(),
        default=get_default_sms_notification_status,
    )
    sms_priority = models.CharField(
        max_length=1,
        choices=SMSPriority.choices(),
        default=SMSPriority.PREFER_PUSH,
    )
    sms_limit = models.IntegerField(
        default=0,
        verbose_name='Sms limit (postpaid)',
    )
    # this has content like {'2014-11': 100, '2015-02': 250}
    sms_limit_history = JSONField(default=dict, blank=True)
    sms_limit_alerts = JSONField(default=dict, blank=True)

    locked_limit_hourly = models.IntegerField(null=True, blank=True)

    opening_hours_note = models.CharField(max_length=500, blank=True, null=True)

    # for Third Party API Integrations
    # like storing branch.io deeplinks
    integrations = JSONField(default=dict, blank=True)
    # integrations manager
    integrations_dict = lib.tools.PartUpdateDict('integrations')

    registration_source = models.ForeignKey(
        'booking.BookingSources',
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )

    custom_data = JSONField(default=dict, blank=True)
    buyer = models.ForeignKey(
        'purchase.SubscriptionBuyer',
        related_name='businesses',
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    invoice_address = models.CharField(
        max_length=1024,
        null=True,
        blank=True,
    )
    invoice_email = models.EmailField(blank=True, null=True, default=None)
    manual_boost_score = PositiveBigIntegerField(
        default=0,
    )

    verification = models.CharField(
        max_length=1,
        choices=Verification.choices(),
        default=Verification.NOT_VERIFIED,
    )
    moderation_status = models.CharField(
        max_length=1,
        choices=Moderation.choices(),
        default=Moderation.NOT_MODERATED,
        blank=True,
    )

    time_zone_name = models.CharField(
        max_length=200,
        choices=TZ_NAMES_CHOICES,
        null=True,
    )
    country_code = models.CharField(
        max_length=4,
        default=get_default_api_country,
    )
    wholesalers = models.ManyToManyField(
        'warehouse.Wholesaler',
        related_name='businesses',
        blank=True,
    )
    include_in_analysis = models.BooleanField(default=True, null=True)

    boost_contact_now = models.BooleanField(default=False, blank=True)
    boost_remind_later = models.BooleanField(default=False, blank=True)
    boost_remind_sended = models.BooleanField(default=False, blank=True)

    # Ticket: #65550
    consent_form_sms_request = models.BooleanField(default=True)

    has_new_billing = models.BooleanField(
        editable=False,
        null=True,
        default=False,
    )

    # Business profile completeness weights
    # MAKE SURE THESE ADD UP TO 1
    PROFILE_COMPLETENESS_WEIGHTS = {
        'address': Decimal('0.15'),
        'latitude|longitude': Decimal('0.1'),
        'phone': Decimal('0.15'),
        'cover_photo': Decimal('0.1'),
        'photos': Decimal('0.1'),
        'description': Decimal('0.1'),
        'website': Decimal('0.05'),
        'parking': Decimal('0.05'),
        'credit_cards': Decimal('0.05'),
        'wheelchair_access': Decimal('0.05'),
        'business_hours': Decimal('0.1'),
    }

    objects = BusinessManager()
    all_objects = AutoUpdateManager()

    @property
    def country_config(self) -> CountryConfig:
        return CountryConfig(Country(self.country_code))  # pylint: disable=no-value-for-parameter

    @property
    def profile_type(self):
        return self.integrations.get('profile_type', self.DEFAULT_PROFILE_TYPE)

    @profile_type.setter
    def profile_type(self, value: ProfileType):
        if value not in self.ProfileType.values():
            raise ValueError(f'value should be one of {self.ProfileType.values()}!')

        self.integrations['profile_type'] = value

    @property
    def owner_lang(self):
        return self.owner.user.profiles.filter(profile_type='B').first().language

    @property
    def admin_id_link(self):
        return format_html('<a href="{}">{}</a>', admin_link(self), truncatechars(self, 100))

    def _validate_renting_venue(self):
        if self.is_venue():
            # validate umbrella venue business
            if self.renting_venue_id is not None:
                raise ValidationError(
                    {
                        'renting_venue': _(
                            "Umbrella Venue {} can't be "
                            "added to other Umbrella Venue business {}"
                        ).format(
                            self.id,
                            self.renting_venue_id,
                        )
                    }
                )
        else:
            if self.id is None:
                # do not validate business not saved instances
                return

            if self.renting_venue_id is None:
                return

            if not self.renting_venue.active:
                raise ValidationError(
                    {'renting_venue': _("Business can't be assigned to not active umbrella venue")}
                )

    def clean_fields(self, exclude=None):
        # renting_venue will be validate in _validate_renting_venue
        # phone is validate in BusinessHandlers
        if exclude is None:
            exclude = ['renting_venue', 'phone']
        else:
            set(exclude).update(('renting_venue', 'phone'))

        super().clean_fields(exclude)
        self._validate_renting_venue()

    @property
    def is_in_setup_state(self):
        return self.status in Business.Status.incomplete()

    @property
    def is_traveling(self):
        traveling = getattr(self, 'traveling', False)
        return traveling and traveling.deleted is None

    @property
    def is_boost_payment_source_offline(self):
        return self.boost_payment_source == BoostPaymentSource.OFFLINE

    def has_frontdesk_enabled(self) -> bool:
        return self.custom_data.get(CustomData.CAN_USE_FRONTDESK, False)

    @property
    def customer_notifications_disabled(self) -> bool:
        return self.custom_data.get(CustomData.DISABLE_NOTIFICATIONS_TO_CLIENTS, False)

    @property
    def business_notifications_disabled(self) -> bool:
        return self.custom_data.get(CustomData.DISABLE_NOTIFICATIONS_TO_BUSINESS, False)

    def raise_if_customer_notifications_are_disabled(self) -> None:
        if self.customer_notifications_disabled:
            raise ScenarioSkipped({f'business.{CustomData.DISABLE_NOTIFICATIONS_TO_CLIENTS}': True})

    def raise_if_business_notifications_are_disabled(self) -> None:
        if self.business_notifications_disabled:
            raise ScenarioSkipped(
                {f'business.{CustomData.DISABLE_NOTIFICATIONS_TO_BUSINESS}': True}
            )

    @property
    def can_use_frontdesk(self):
        if self.has_frontdesk_enabled():
            return True
        return is_frontdesk_enabled_for_country(tz=self.get_timezone())

    @property
    def is_traveling_only(self):
        return self.is_traveling and self.traveling.traveling_only

    @property
    def is_migrated_demo_account(self):
        return 'demo_id' in self.integrations

    @cached_property
    def turntracker_enabled(self):
        return get_turntracker_enabled(self.id)

    @cached_property
    def events(self):
        """On-demand created BusinessEvent instance"""
        return BusinessEvent.objects.get_or_create(business=self)[0]

    def clean(self):
        super().clean()
        if not self.is_in_setup_state:
            errors = self.validate()
            if len(errors) > 0:
                _errors = collections.defaultdict(list)
                for error in errors:
                    _errors[error['field']].append(
                        ValidationError(
                            error.pop('description'),
                            params=error,
                        )
                    )
                raise ValidationError(_errors)

    def validate(self):
        """
        Checks if business contain valid data.

        NOTE: This function might alter business.latitude/longitude.

        :return: List of invalid fields errors dict.
        """
        errors = [
            self.check_business_region(self.region),
            self.check_business_primary_category(
                primary_category=self.primary_category,
                categories=self.categories,
            ),
            self.check_business_services(self.services),
            self.check_business_resources(self.resources),
        ]

        if self.region and (self.latitude is None or self.longitude is None):
            reg = self.region
            self.latitude = reg.latitude
            self.longitude = reg.longitude

        errors = [_f for _f in errors if _f]

        return errors

    @staticmethod
    def check_activeable_relation_validity(queryset, excluded_ids=None):
        validity_queryset = queryset.filter(active=True)
        if excluded_ids is not None:
            validity_queryset = validity_queryset.exclude(id__in=excluded_ids)
        return queryset.exists() and validity_queryset.exists()

    @classmethod
    def check_business_services(cls, services, excluded_ids=None):
        if not cls.check_activeable_relation_validity(services, excluded_ids):
            return cls.get_validation_error_dict(
                field=NON_FIELD_ERRORS,
                description='Active business require at least one service.',
            )

    @classmethod
    def check_business_resources(cls, resources, excluded_ids=None):
        if not cls.check_activeable_relation_validity(resources, excluded_ids):
            return cls.get_validation_error_dict(
                field=NON_FIELD_ERRORS,
                description='Active business require at least one staffer or resource.',
            )

    @classmethod
    def check_business_categories(cls, categories):
        if not categories.exists():
            return cls.get_validation_error_dict(
                field='categories',
                description='Active business require at least one category.',
            )

    @classmethod
    def check_business_primary_category(cls, primary_category, categories):
        error = cls.check_business_categories(categories)
        if error is None:
            if primary_category is None:
                error = cls.get_validation_error_dict(
                    field='primary_category',
                    description='Active business require primary category.',
                )
            elif not categories.filter(id=primary_category.id).exists():
                error = cls.get_validation_error_dict(
                    field='primary_category',
                    description='Primary category must be one of business categories.',
                )
        return error

    @classmethod
    def check_business_region(cls, region):
        if not region:
            return cls.get_validation_error_dict(
                field='region',
                description='Active business require region.',
            )

    @staticmethod
    def get_validation_error_dict(field, description):
        return {
            'code': 'invalid',
            'field': field,
            'type': 'validation',
            'description': gettext(description),
        }

    def _validate_status_transfer(self):
        original_status = self.get_dirty_fields().get('status')
        if (
            # firstly business will be with setup status
            # do not validate business without id
            self.id
            and original_status
            and original_status != Business.Status.VENUE
            and self.status == Business.Status.VENUE
        ):
            raise ValidationError(
                {'status': _("Forbidden transfer common business can't be umbrella venue")}
            )

    def save(
        self, force_insert=False, force_update=False, using=None, update_fields=None
    ):  # pylint: disable=arguments-differ, too-many-branches, too-many-statements
        self._validate_status_transfer()
        self._validate_renting_venue()
        # force active field value based on status
        active = Business.Status.activity().get(self.status, False)
        if active is not None and self.active != active:
            self.active = active
            if update_fields and 'active' not in update_fields:
                update_fields.append('active')

        # we get dirty after setting active
        dirty_fields = self.get_dirty_fields(check_relationship=True)
        if dirty_fields.get('active') is False and self.active:
            b_listing = self.b_listing
            if b_listing and b_listing.active:
                b_listing.active = False
                b_listing.save(update_fields=['active'])

            self.update_all_subdomains_deeplinks()

        if dirty_fields.get('name'):
            self.integrations['slug'] = self.slug

        self._validate_test_business_status(dirty_fields)

        old_status = dirty_fields.get('status')
        if (
            old_status
            and self.status == Business.Status.PAID
            and old_status
            in (
                Business.Status.TRIAL,
                Business.Status.TRIAL_BLOCKED,
            )
        ):
            from webapps.marketplace.models import MarketplacePromotionStatus

            MarketplacePromotionStatus.objects.create(
                status=MarketplacePromotionStatus.PROMOTION_STATUS__AFTER_SAAS_TRIAL,
                business_id=self.id,
            )

        if (
            'region' in dirty_fields.keys()
            and dirty_fields['region']
            and self.boost_status in Business.BoostStatus.active_statuses()
            and ('boost_status' not in dirty_fields or not dirty_fields['boost_status'])
        ):
            promotion = BusinessPromotion.objects.filter(business=self).last()
            promotion.restart_boost(business=self)

        if settings.BOOST.AUTO_CHURN and self.status in {
            Business.Status.CHURNED,
            Business.Status.SUSPENDED,
            Business.Status.BLOCKED_OVERDUE,
            Business.Status.BLOCKED,
        }:
            self.boost_status = Business.BoostStatus.DISABLED
            BusinessPromotion.cache_promotion_updater(self.id, BusinessPromotion.SAAS)
            if update_fields and 'boost_status' not in update_fields:
                update_fields.append('boost_status')
        if (
            settings.API_COUNTRY != Country.PL
            and self.status == Business.Status.PAID
            and old_status
            in {
                Business.Status.CHURNED,
                Business.Status.SUSPENDED,
                Business.Status.BLOCKED_OVERDUE,
                Business.Status.BLOCKED,
            }
        ):
            last_promotion = self.businesspromotion_set.order_by('promotion_start').last()
            if last_promotion and last_promotion.type == PAID_PROMO:
                from webapps.marketplace.notifications import BoostYouAreBackNotification

                BoostYouAreBackNotification(self).send()

        if (
            settings.TOTAL_M_SMS_LIMITS_FOR_TRIAL_ON
            and old_status
            and old_status in Business.Status.never_paid_statuses()
            and self.has_subscription
        ):
            # Reset LimtedSMSBunch counters
            self.limited_smses.filter(sms_count__gt=0).update(sms_count=0)
        if self.region and not self.time_zone_name:
            self.time_zone_name = self.region.time_zone_name
            if update_fields and 'time_zone_name' not in update_fields:
                update_fields.append('time_zone_name')

        if self.trial_till is None and self.status == Business.Status.TRIAL:
            self.trial_till = calculate_trial_till(self)
            if update_fields and 'trial_till' not in update_fields:
                update_fields.append('trial_till')

        created = self._state.adding
        updated = not created

        if (
            created
            and CustomData.SEND_BOOKING_CONFIRMATION_SMS not in self.custom_data
            and KillSwitch.alive(KillSwitch.System.ENABLE_SMS_BOOKING_CONFIRMATIONS_BY_DEFAULT)
        ):
            self.custom_data[CustomData.SEND_BOOKING_CONFIRMATION_SMS] = True

        if self.package in self.ELIGIBLE_PACKAGE:
            self.custom_data[CustomData.CAN_USE_FRONTDESK] = True

        if created and settings.API_COUNTRY == Country.PL:
            self.custom_data[CustomData.PARTNER_APPS_ENABLED] = True

        if self.visible and self.visible_delay_till:
            self.visible_delay_till = None
            if update_fields and 'visible_delay_till' not in update_fields:
                update_fields.append('visible_delay_till')
            business_activated_event.send(self, was_delay_set=True)

        if self.visible ^ bool(self.visible_from):
            self.visible_from = tznow() if self.visible else None
            if update_fields and 'visible_from' not in update_fields:
                update_fields.append('visible_from')

        super().save(
            force_insert=force_insert,
            force_update=force_update,
            using=using,
            update_fields=update_fields,
        )

        zipcode_changed = created or 'zipcode' in dirty_fields or 'region' in dirty_fields
        if (
            settings.NAVISION_CHECK_TAX_RATE_API_ON_NEW_MERCHANT
            and zipcode_changed
            and self.zip is not None
        ):
            from webapps.navision.tasks.tax_rates import fetch_tax_rate_for_zipcode_task

            fetch_tax_rate_for_zipcode_task.delay(self.zip)

        if self.pk and 'buyer' in dirty_fields:
            from webapps.navision.ports.tax_rates import TaxMatrix

            TaxMatrix.cache_clear([self.pk])

        if updated and old_status and old_status != self.status:
            business_id = self.id
            analytics_business_status_updated_task.delay(
                business_id=business_id,
                context={
                    'business_id': business_id,
                },
            )

        if 'primary_category' in dirty_fields.keys():
            business_primary_category_saved_event.send(self.id, created=created)

        EcommerceBusinessAdapter.update_business_data(self, dirty_fields)
        return updated

    @transaction.atomic
    def delete(self, using=None, keep_parents=False):
        from webapps.booking.models import SubBooking

        SubBooking.objects.filter(
            appointment__business_id=self.id,
        ).all().delete()
        self.appointments.all().delete()
        for service in self.services.all():
            service.service_variants.all().delete()
            service.resources.all().delete()
        self.services.all().delete()
        self.resources.all().delete()
        self.reciever_set.all().delete()
        return super().delete(using=using, keep_parents=keep_parents)

    @property
    def external_api_id(self):
        """An unambiguous ID used in external APIs

        A proper ID has the following format:
        <deployment_level>-<country_code>-<business_id>

        On live deployment, the deployment_level part is omitted.

        """
        return lib.tools.id_to_external_api(self.id)

    @property
    def slug(self):
        if self.name is None:
            return None
        return slugify(self.name)

    @property
    def reviews_stars(self):
        from webapps.reviews.tools import rank_to_stars

        return rank_to_stars(self.reviews_rank_avg)

    @property
    def promotion_status(self):
        from webapps.marketplace.models import MarketplacePromotionStatus

        return MarketplacePromotionStatus.get_status_for_business(self)

    @cached_property
    def pos(self):
        return self._get_pos()

    @property
    def is_visible_in_marketplace(self):  # should be improved with annotate_visible_in_marketplace
        return (
            self.active
            and self.visible
            and self.is_visible_in_marketplace_service_variant
            and self.is_visible_in_marketplace_resources
        )

    @property
    def is_visible_in_marketplace_service_variant(self):
        return ServiceVariant.objects.filter(
            service__business=self,
            service__active=True,
            resources__active=True,
            resources__visible=True,
            resources__deleted__isnull=True,
            service__is_available_for_customer_booking=True,
            active=True,
        ).exists()

    @property
    def is_visible_in_marketplace_resources(self):
        return Resource.objects.filter(
            business=self,
            active=True,
            deleted__isnull=True,
            visible=True,
        ).exists()

    def _get_pos(self):  # pylint: disable=too-many-return-statements
        """Cached shortcut to self.pos_set.filter(active=True).first().

        It's able to use prefetched POS list.

        """
        if not settings.POS or self.id is None:
            return None
        if 'pos_set' in getattr(self, '_prefetched_objects_cache', {}):
            pos_list = self._prefetched_objects_cache['pos_set']
            if pos_list and pos_list[0].active:
                return pos_list[0]
            return None
        if (
            # check cached value, but avoid triggering property
            'pos_enabled' in self.__dict__
            and not self.pos_enabled
        ):
            return None
        return self.pos_set.filter(active=True).first()

    @cached_property
    def pos_enabled(self):  # pylint: disable=method-hidden
        """Cached shortcut to self.pos_set.filter(active=True).exists().

        It's able to use prefetched POS list (via self.pos property).

        """
        if not settings.POS:
            return False
        if self.id is None:
            return False
        if (
            'pos_set' in getattr(self, '_prefetched_objects_cache', {})
            or 'pos_set' in self.__dict__  # check, but avoid triggering property
        ):
            return bool(self.pos)
        return self.pos_set.filter(active=True).exists()

    @cached_property
    def pos_market_pay_enabled(self):
        return self.pos and self.pos.marketpay_enabled

    @cached_property
    def pos_pay_by_app_enabled(self):
        """Cached check if business has a pay_by_app PaymentType enabled.

        It's able to use prefetched POS list (via self.pos property).

        """
        if not settings.POS:
            return False
        if self.id is None:
            return False

        from webapps.pos.models import PaymentType, POS

        if (
            'pos' in getattr(self, '_prefetched_objects_cache', {})
            or 'pos' in self.__dict__  # check, but avoid triggering property
        ):
            return (
                self.pos
                and self.pos.pay_by_app_status == POS.PAY_BY_APP_ENABLED
                and self.pos.payment_types.filter(
                    code=PaymentTypeEnum.PAY_BY_APP,
                ).exists()
            )

        pay_by_app_enabled = self.pos_set.filter(
            active=True,  # pos needs to be active as well
            pay_by_app_status=POS.PAY_BY_APP_ENABLED,
            payment_types__in=PaymentType.objects.filter(
                code=PaymentTypeEnum.PAY_BY_APP,
            ),
        ).exists()
        if pay_by_app_enabled:
            # cache pos_enabled as well, because we know it's True
            self.pos_enabled = True
        return pay_by_app_enabled

    @cached_property
    def booksy_pay_eligible(self) -> bool:
        return (
            settings.POS__BOOKSY_PAY
            and self.pos_enabled
            and self.pos.pos_refactor_stage2_enabled
            and self.pos.stripe_kyc_completed
        )

    @cached_property
    def booksy_pay_available(self) -> bool:
        return self.pos is not None and self.pos.booksy_pay_enabled and self.booksy_pay_eligible

    @cached_property
    def completed_kyc(self) -> bool:
        if not self.pos:
            return False

        account_holder = self.pos.account_holder

        if not account_holder:
            return False

        return (
            account_holder.ever_passed_kyc
            and account_holder.status == AccountHolderStatus.ACTIVE.value
            and account_holder.payout_allowed
        )

    @cached_property
    def staffers_by_user_id(self):
        """Performance fix for ResourceFromUserField."""
        return {
            staffer.staff_user_id: staffer
            for staffer in self.resources.filter(
                type=Resource.STAFF,
                staff_user__isnull=False,
            ).order_by('active', 'created')
        }

    @property
    def has_tuning(self):
        return hasattr(self, 'search_tuning')

    @property
    def hidden_on_web(self):
        return self.has_tuning and self.search_tuning.hidden_on_web

    def get_version_timestamp(self):
        """Get BusinessVersion timestamp handling DoesNotExist exception."""
        try:
            timestamp = self.businessversion.timestamp
        except BusinessVersion.DoesNotExist:
            # OneToOne not created yet
            timestamp = 0.0
        return timestamp

    @property
    def short_name(self) -> str:
        if (self.name_short or '').strip():
            return self.name_short

        return unicode_smart_truncate(self.name, **settings.SCENARIOS_TRUNCATE_BUSINESS_NAME)

    def get_effective_locked_limit_hourly(self):
        if self.locked_limit_hourly is None:
            return settings.STAFFER_LOCKED_LIMIT_HOURLY
        if self.locked_limit_hourly < 1:
            return 1

        return self.locked_limit_hourly

    def get_timezone(self):
        # pylint: disable=protected-access
        """
        Return Business's time zone as tzinfo object.
        Resolve queue
        1. return cached object
        2. return timezone based on time_zone_name column
        3. get timezone from region
        4. return UTC
        """
        tz = getattr(self, '_timezone', None)
        if tz:
            return tz

        if self.time_zone_name:
            tz = dateutil_tz.gettz(self.time_zone_name)
            if tz:
                tz._long_name = self.time_zone_name
                return tz

        region = self.region
        if region:
            tz = region.gettz()
            self._timezone = tz
        if not tz:
            tz = dateutil_tz.gettz('UTC')
            tz._long_name = 'UTC'
        return tz

    def set_timezone(self, timezone, shift_bookings=False):
        """
        Sets the Business timezone to `timezone` (a tzinfo instance with `_long_name`).
        Optionally enqueues a Celery task to shift future bookings from the old timezone
        to the new one, and provides a list of appointment IDs to be shifted.
        """
        # pylint: disable=protected-access
        from webapps.booking.models import Appointment
        from webapps.business.tasks import shift_bookings_to_timezone_task

        if not isinstance(timezone, datetime.tzinfo):
            raise TypeError('Provide valid timezone info')
        assert hasattr(timezone, '_long_name')

        if shift_bookings:
            old_tz = self.get_timezone()
            old_tz_name = old_tz._long_name
            new_tz_name = timezone._long_name

            now = datetime.datetime.now(tz=old_tz)
            tz_change_date = now

            appointment_ids = list(
                Appointment.objects.filter(
                    business_id=self.id,
                    booked_from__gt=now,
                    created__lte=tz_change_date,
                ).values_list('id', flat=True)
            )

            shift_bookings_to_timezone_task.delay(
                business_id=self.id,
                old_timezone_name=old_tz_name,
                new_timezone_name=new_tz_name,
                tz_change_date=now,
                appointment_ids=appointment_ids,
            )

        self.time_zone_name = timezone._long_name
        self._timezone = timezone
        # pylint: enable=protected-access

    @property
    def city_or_region_city(self) -> t.Optional[str]:  # pylint: disable=duplicate-code
        """Returns business city.

        It returns user entered city field
        or fallback to region of this business, if applicable.

        """
        if self.city:
            return self.city
        if self.region is None:
            return None
        city_instance = self.region.get_parent_by_type(settings.ES_CITY_LVL)
        return city_instance and city_instance.name

    @property
    def zip(self) -> t.Optional[str]:
        from webapps.structure.models import Region

        if self.zipcode:
            return self.zipcode
        if self.region and self.region.type == Region.Type.ZIP:
            return self.region.name
        return None

    @cached_property
    def state(self) -> t.Optional[str]:
        from webapps.structure.models import Region

        if self.region is not None:
            state = self.region.get_parent_by_type(Region.Type.STATE)

            if state:
                return state.name

        if self.zipcode is not None:
            zip_code = Region.objects.filter(name=self.zipcode, type=Region.Type.ZIP).first()

            if zip_code:
                state = zip_code.get_parent_by_type(Region.Type.STATE)

                if state:
                    return state.name

    @staticmethod
    def check_booking_min_max_lead_times(booking_min_lead_time, booking_max_lead_time):
        """Python checker for DB constraint."""
        # direct relativedelta comparison doesn't work very well
        # so we use datetimecomparison instead
        now = tznow()
        return now + booking_max_lead_time > now + booking_min_lead_time

    @staticmethod
    def check_booking_max_lead_modification_times(
        booking_max_lead_time, booking_max_modification_time
    ):
        """Python checker for DB constraint."""
        now = tznow()
        return now + booking_max_lead_time >= now + booking_max_modification_time

    def check_booking_create_time(self, booked_from):
        """Make sure that booked_from date conforms to business' booking
        policy times (create new booking)."""
        if booked_from is None:
            return False

        tz = self.get_timezone()
        now = tznow(tz=tz)

        if (
            now + self.booking_min_lead_time > booked_from
            or now + self.booking_max_lead_time < booked_from
        ):
            return False

        return True

    def check_booking_update_time(self, booked_from):
        """Make sure that booked_from date conforms to business' booking
        policy times (modify existing booking)."""
        if booked_from is None:
            return False

        tz = self.get_timezone()
        now = tznow(tz=tz)

        if (
            now + self.booking_min_lead_time > booked_from
            or now + self.booking_max_lead_time < booked_from
            or now + self.booking_max_modification_time > booked_from
        ):
            return False

        return True

    @property
    def subdomain_data(self):
        if not hasattr(self, '_subdomain_dict'):
            cached_subdomains = SubdomainsCache.get_from_cache(self.id)
            if cached_subdomains:
                self._subdomain_dict = json.loads(cached_subdomains)
                return self._subdomain_dict

            try:
                subdomains = SubdomainGRPC.search(data={'business_id': self.id})
                self._subdomain_dict = subdomains[0] if subdomains else {}

            except SubdomainGRPCError as e:
                log.error('Subdomain grpc error: %s', e.errors)
                return {}

        SubdomainsCache.push_to_cache(self.id, json.dumps(self._subdomain_dict).encode('utf-8'))

        return self._subdomain_dict

    @property
    def subdomain(self):
        return self.subdomain_data.get('subdomain')

    def set_subdomain_cache(self, subdomain_dict: dict):
        self._subdomain_dict = subdomain_dict

    def clear_subdomain_cache(self):
        key = '_subdomain_dict'
        if hasattr(self, key):
            delattr(self, key)

    def create_seo_subdomain(self, subdomain):
        """
        Copy of get_seo_subdomain subdomains for any string
        :param subdomain: string
        :return: string.url
        """
        return self._get_seo_subdomain(
            subdomain,
            protocol=True,
            channel=None,
            is_blast=False,
            staffer_invite=False,
            marketplace=False,
        )

    def get_seo_subdomain(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        self,
        protocol: bool,
        channel: str = None,
        is_blast: bool = False,
        staffer_invite: bool = False,
        marketplace: bool = False,
    ):
        return self._get_seo_subdomain(
            self.subdomain or '', protocol, channel, is_blast, staffer_invite, marketplace
        )

    @staticmethod
    def _get_seo_subdomain(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        subdomain: str,
        protocol: bool,
        channel: str,
        is_blast: bool,
        staffer_invite: bool,
        marketplace: bool,
    ) -> str:
        from webapps.notification.base import Channel

        path = ''
        if channel == Channel.Type.SMS:
            protocol = False

            if is_blast:
                path = f'/{SEO_DEEPLINK_TYPES[SeoUrl.BLAST]}/'
            elif staffer_invite:
                path = f'/{SEO_DEEPLINK_TYPES[SeoUrl.STAFFER_SMS]}/'
            elif marketplace:
                path = f'/{SEO_DEEPLINK_TYPES[SeoUrl.MARKETPLACE]}/'
            else:
                path = f'/{SEO_DEEPLINK_TYPES[SeoUrl.SMS]}/'

        protocol_prefix = 'https://' if protocol else ''
        return f'{protocol_prefix}{subdomain}.{settings.BOOKSY_DOMAIN}{path}'

    def get_marketplace_sitemap_url(self):
        from webapps.structure.models import (
            Region,
            get_operating_country,
        )

        if self.seo_region:
            main_region = self.seo_region
        else:
            zip_code = self.region
            if zip_code:
                regions = zip_code.get_parents()
                main_regions = [region for region in regions if region.type == Region.Type.CITY]
                if not main_regions:
                    main_regions = [
                        region for region in regions if region.type == Region.Type.VILLAGE
                    ]
                if not main_regions and len(regions) > 0:
                    main_region = regions[0]
                elif main_regions:
                    main_region = main_regions[0]
                else:
                    main_region = get_operating_country()
            else:
                main_region = get_operating_country()
        biz_id = self.id
        biz_slug = self.slug
        cat_slug = self.primary_category.slug if self.primary_category else 'None'
        region_id = main_region.id
        region_slug = main_region.slug if main_region else ''
        url = f'{biz_id}_{biz_slug}_{cat_slug}_{region_id}_{region_slug}'
        return url

    def get_marketplace_url(self, staffer_id: t.Optional[int] = None):
        url = f'/{settings.MARKETPLACE_LANG_COUNTRY}/{self.get_marketplace_sitemap_url()}'
        if staffer_id:
            url = f'{url}/staffer/{staffer_id}'

        return urljoin(settings.MARKETPLACE_URL, url)

    def get_marketplace_url_for_desktop_deeplink(
        self, staffer_id: t.Optional[int] = None, extra_params: t.Optional[dict] = None
    ) -> str:
        url = self.get_marketplace_url(staffer_id=staffer_id)
        params = {
            'do': 'invite',
        }
        if extra_params:
            params |= extra_params

        return build_or_update_url(url, params)

    def get_invite_marketplace_url(self, ignore_activity):
        from webapps.notification.base import Channel

        app_deeplink = self.get_mp_deeplink(
            feature=DeeplinkFeature.INVITE_NOT_FORCED_APP_REDIRECT,
            channel=Channel.Type.SMS,
            ignore_activity=ignore_activity,
        )
        return self.get_marketplace_url_for_desktop_deeplink(
            extra_params={
                'app_deeplink': app_deeplink,
                'utm_medium': DeeplinkFeature.INVITE_NOT_FORCED_APP_DOWNLOAD,
                'utm_source': Channel.Type.SMS,
            }
        )

    def get_seo_url(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        self,
        dummy=False,
        protocol=True,
        check_subdomain=True,
        channel=None,
        is_blast=False,
        staffer_invite=False,
    ):
        """Returns canonical, SEO-friendly URL to Business in Marketplace.

        If Business has claimed a subdomain, use it.
        If not, construct a link to Booksy Marketplace.

        If dummy=True then simplest working URL is returned.
        """
        if dummy:
            return self.get_invite_mp_deeplink(
                channel=channel,
            )

        if check_subdomain and self.subdomain:
            return self.get_seo_subdomain(
                protocol=protocol,
                channel=channel,
                is_blast=is_blast,
                staffer_invite=staffer_invite,
            )

        category = self.primary_category

        region = self.region
        if region is not None:
            city = region.get_parent_by_type(settings.ES_CITY_LVL)
            region_id = city.id if city else 0
        else:
            region_id = 0

        parts = [
            str(self.id),
            self.name,
            category.name if category else '-',
            str(region_id),
            self.city_or_region_city,
        ]
        parts = [text_slug(part).replace('_', '-') or '-' for part in parts]

        return urljoin(
            settings.MARKETPLACE_URL,
            f"/{settings.MARKETPLACE_LANG_COUNTRY}/{'_'.join(parts)}",
        )

    def get_mp_deeplink(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        self,
        link_code=LINK_CODE,
        channel=None,
        campaign=None,
        feature=None,
        stage=None,
        tags=None,
        force=False,
        ignore_activity=False,
    ):
        # TODO::local fix me if i won't work
        # if settings.LOCAL_DEPLOYMENT:
        #     return f'{mp_deeplink(link_code)}/{self.id}'
        """Deeplink to Marketplace page."""
        allowed_link_codes = {
            LINK_CODE: INTEGRATIONS_DEEPLINK_KEY,
        }
        is_business_activity_ok = ignore_activity or self.active
        if allowed_link_codes.get(link_code) and is_business_activity_ok:
            if not isinstance(self.integrations, dict):
                self.integrations = {}
            key = allowed_link_codes.get(link_code)
            analytics_data = {
                k: v
                for k, v in {
                    'channel': channel,
                    'campaign': campaign,
                    'feature': feature,
                    'stage': stage,
                    'tags': tags,
                }.items()
                if v
            }
            if not force and not analytics_data and key in self.integrations:
                # looks like it never happens
                branchio_url = self.integrations[key]
            else:
                marketplace_url = self.get_marketplace_url_for_desktop_deeplink()
                generate_data = self._branchio_generate_data(
                    link_code=link_code,
                    marketplace_url=marketplace_url,
                )

                branchio_url = deeplink.generate_deeplink(
                    app_type='C', data=generate_data, **analytics_data
                )
                update_fields = ['integrations']
                if branchio_url is not None and not analytics_data:
                    # looks like it never happens
                    self.integrations[key] = branchio_url
                    self.save(update_fields=update_fields)

            if branchio_url is not None:
                return branchio_url

        return f'{mp_deeplink(link_code)}/{self.id}'

    def get_invite_mp_deeplink(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        self,
        channel=None,
        campaign=None,
        stage=None,
        tags=None,
        force=False,
        ignore_activity=False,
    ):
        return self.get_mp_deeplink(
            feature=DeeplinkFeature.MERCHANT_CUSTOMER_INVITE,
            channel=channel,
            campaign=campaign,
            stage=stage,
            tags=tags,
            force=force,
            ignore_activity=ignore_activity,
        )

    def _get_staffer_invite_mp_deeplink(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        self,
        channel=None,
        campaign=None,
        stage=None,
        tags=None,
        force=False,
        ignore_activity=False,
    ):
        return self.get_mp_deeplink(
            feature=DeeplinkFeature.STAFFER_CUSTOMER_INVITE,
            channel=channel,
            campaign=campaign,
            stage=stage,
            tags=tags,
            force=force,
            ignore_activity=ignore_activity,
        )

    def get_email_invite_mp_deeplink(self, staffer_invite=False):
        from webapps.notification.base import Channel

        if CacheBusinessInviteDeeplink():
            return get_cached_invite_deeplink(
                business_id=self.id,
                channel=Channel.Type.EMAIL,
                staffer_invite=staffer_invite,
            )
        if staffer_invite:
            return self._get_staffer_invite_mp_deeplink(
                channel=Channel.Type.EMAIL,
            )
        return self.get_invite_mp_deeplink(
            channel=Channel.Type.EMAIL,
        )

    def get_push_invite_mp_deeplink(self, staffer_invite=False):
        from webapps.notification.base import Channel

        if CacheBusinessInviteDeeplink():
            return get_cached_invite_deeplink(
                business_id=self.id,
                channel=Channel.Type.PUSH,
                staffer_invite=staffer_invite,
            )

        if staffer_invite:
            return self._get_staffer_invite_mp_deeplink(channel=Channel.Type.PUSH)
        return self.get_invite_mp_deeplink(channel=Channel.Type.PUSH)

    def get_sms_invite_mp_deeplink(
        self,
        dummy: bool = False,
        protocol: bool = True,
        staffer_invite: bool = False,
    ):
        from webapps.notification.base import Channel

        return self.get_seo_url(
            dummy=dummy,
            protocol=protocol,
            check_subdomain=True,
            channel=Channel.Type.SMS,
            staffer_invite=staffer_invite,
        )

    def update_mp_deeplink_data(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        self,
        deeplink_url,
        link_code=LINK_CODE,
        channel=None,
        campaign=None,
        feature=None,
        stage=None,
        tags=None,
    ):
        """Update Deeplink to Marketplace page."""
        # DEPRECATED. use update_all_subdomains_deeplinks
        allowed_link_codes = {
            LINK_CODE: INTEGRATIONS_DEEPLINK_KEY,
        }
        if allowed_link_codes.get(link_code):
            business = self
            if not isinstance(business.integrations, dict):
                business.integrations = {}
            key = allowed_link_codes.get(link_code)
            analytics_data = {
                k: v
                for k, v in {
                    'channel': channel,
                    'campaign': campaign,
                    'feature': feature,
                    'stage': stage,
                    'tags': tags,
                }.items()
                if v
            }

            marketplace_url = self.get_marketplace_url_for_desktop_deeplink()
            generate_data = self._branchio_generate_data(
                link_code=link_code,
                marketplace_url=marketplace_url,
            )
            update_fields = ['integrations']

            branchio_url_change = deeplink.update_deeplink_data(
                url=deeplink_url,
                app_type=BranchIOAppTypes.CUSTOMER,
                data=generate_data,
                **analytics_data,
            )
            if branchio_url_change is not None and not analytics_data:
                business.integrations[key] = deeplink_url
                business.save(update_fields=update_fields)
        return deeplink_url

    def generate_new_deeplinks(self, ignore_activity=True):
        from webapps.notification.base import Channel

        return {
            'general': self.get_invite_mp_deeplink(ignore_activity=ignore_activity),
            'sms': self.get_invite_mp_deeplink(
                channel=Channel.Type.SMS,
                ignore_activity=ignore_activity,
            ),
            'blast': self._get_blast_invite_mp_deeplink(ignore_activity=ignore_activity),
            'staffer_sms': self._get_staffer_invite_mp_deeplink(
                channel=Channel.Type.SMS,
                ignore_activity=ignore_activity,
            ),
            'marketplace_url': self.get_invite_marketplace_url(ignore_activity=ignore_activity),
        }

    def _get_blast_invite_mp_deeplink(self, ignore_activity=False):
        from webapps.notification.base import Channel

        return self.get_mp_deeplink(
            feature=DeeplinkFeature.BLAST,
            channel=Channel.Type.SMS,
            ignore_activity=ignore_activity,
        )

    def update_subdomain_deeplinks(self, only_empty: bool = True, ignore_activity: bool = False):
        from webapps.notification.base import Channel

        if data := dict(self.subdomain_data):
            deeplinks = data.setdefault('deeplinks', {})
            if not only_empty:
                deeplinks.clear()

            to_update = not all(deeplinks.get(type_) for type_ in SEO_DEEPLINK_TYPES)
            if not deeplinks.get(SeoUrl.GENERAL):
                deeplinks[SeoUrl.GENERAL] = self.get_invite_mp_deeplink(
                    ignore_activity=ignore_activity,
                )
            if not deeplinks.get(SeoUrl.SMS):
                deeplinks[SeoUrl.SMS] = self.get_invite_mp_deeplink(
                    channel=Channel.Type.SMS,
                    ignore_activity=ignore_activity,
                )
            if not deeplinks.get(SeoUrl.BLAST):
                deeplinks[SeoUrl.BLAST] = self.get_mp_deeplink(
                    feature=DeeplinkFeature.BLAST,
                    channel=Channel.Type.SMS,
                    ignore_activity=ignore_activity,
                )
            if not deeplinks.get(SeoUrl.STAFFER_SMS):
                deeplinks[SeoUrl.STAFFER_SMS] = self._get_staffer_invite_mp_deeplink(
                    channel=Channel.Type.SMS,
                    ignore_activity=ignore_activity,
                )
            if not deeplinks.get(SeoUrl.MARKETPLACE):
                deeplinks[SeoUrl.MARKETPLACE] = self.get_invite_marketplace_url(
                    ignore_activity=ignore_activity
                )

            if to_update:
                self._update_subdomain_deeplinks(data)

    def _update_subdomain_deeplinks(self, data):
        data.pop('uuid', None)
        data.pop('updated', None)
        data.pop('created', None)
        try:
            resposne = SubdomainGRPC.claim(data=data)
        except SubdomainGRPCError as e:
            log.error(
                '[ERROR] Subdomain _update_subdomain_deeplinks error: %s',
                e.errors,
            )
        else:
            self.set_subdomain_cache(resposne)

    def get_webbiz_url(self, as_deeplink=True):
        """Return URL to WebBiz panel with optional customized domain."""
        if as_deeplink:
            return self.get_webbiz_deeplink('base')

        return urljoin(
            settings.BIZ_WEB_2_APP_URL,
            f'{settings.LANGUAGE_CODE[:2]}-{self.country_code}/',
        )

    def get_webbiz_deeplink(self, link_code):
        """Deeplink to WebBiz panel."""
        if link_code not in ['get_business_app', 'get_customer_app']:
            branchio_deeplink = DeepLinkCache.get(link_code)
            if branchio_deeplink:
                return branchio_deeplink

        return f"{webbiz_deeplink(link_code)}?{urlencode({'business_id': self.id})}"

    def _branchio_generate_data(
        self,
        link_code,
        marketplace_url,
    ):
        mobile_link = f'show_business/{self.id}'
        # experiment for beacon analytics
        if link_code != LINK_CODE:
            mobile_link = f'show_business/{self.id}/beacon'
        generate_data = {
            MOBILE_DEEPLINK_KEY: mobile_link,
            DESKTOP_URL_KEY: marketplace_url,
        }
        return generate_data

    def get_marketplace_deeplink(self):
        general_deeplink = ''
        if self.active:
            if hasattr(self, '_subdomain_dict'):
                general_deeplink = self._subdomain_dict.get('deeplinks', {}).get(SeoUrl.GENERAL, '')

            if not general_deeplink:
                general_deeplink = self.subdomain_data.get('deeplinks', {}).get(SeoUrl.GENERAL, '')

            if self.subdomain:
                self.subdomains_deeplinks_self_check_update()

        return general_deeplink or f'{mp_deeplink(LINK_CODE)}/{self.id}'

    def subdomains_deeplinks_self_check_update(self):
        # check and update deeplinks for business
        deeplinks = self.subdomain_data.get('deeplinks', {})
        link_to_check = f'/{DL_LINK}/{LINK_CODE}/'
        if (
            not deeplinks
            or not all(deeplinks.values())
            or any(link_to_check in v for v in deeplinks.values())
        ) and CheckUpdateBusinessDeeplinkFlag(UserData(subject_key=self.id)):
            self.update_all_subdomains_deeplinks()

    def _instead_of_subdomain_deeplinks(self) -> dict:
        from webapps.notification.base import Channel
        from webapps.subdomain_grpc.consts import SUBDOMAINS_DEEPLINK_DATA

        # like `https://booksy.com/en-us/dl/show-business/386034`
        instead_of_deeplink = f'{mp_deeplink(LINK_CODE)}/{self.id}'
        deeplinks = {}
        for key in SUBDOMAINS_DEEPLINK_DATA:
            if key == SeoUrl.MARKETPLACE:
                deeplinks[key] = self.get_marketplace_url_for_desktop_deeplink(
                    extra_params={
                        'app_deeplink': instead_of_deeplink,
                        'utm_medium': DeeplinkFeature.INVITE_NOT_FORCED_APP_DOWNLOAD,
                        'utm_source': Channel.Type.SMS,
                    }
                )
            else:
                deeplinks[key] = instead_of_deeplink

        return deeplinks

    def update_all_subdomains_deeplinks(self, ignore_activity: bool = False):
        # due to using "batch_generate_deeplink" we can update all deeplinks at once
        # and it doesn't affect BranchIO limits, moreover if there is non-working deeplink
        # such approach will fix it, otherwise BranchIO will return old working deeplink

        data = dict(self.subdomain_data)
        if not data:
            return None

        deeplinks = self.generate_subdomain_deeplinks() if ignore_activity or self.active else {}

        data['deeplinks'] = deeplinks
        self._update_subdomain_deeplinks(data)

    def generate_subdomain_deeplinks(self) -> dict:
        from webapps.subdomain_grpc.consts import (
            SUBDOMAINS_DEEPLINK_DATA,
            DEEPLINK_GENERATE_ATTEMPTS,
        )

        batch_generate_deeplink_data = self._prepare_subdomain_deeplinks_data()

        # https://booksy.atlassian.net/browse/PXMAR-1419
        deeplinks = None
        for _ in range(DEEPLINK_GENERATE_ATTEMPTS):
            deeplinks = deeplink.batch_generate_deeplink(batch_generate_deeplink_data)
            if len(deeplinks) == len(SUBDOMAINS_DEEPLINK_DATA) and all(
                row['deeplink'] for row in deeplinks
            ):
                logger_deeplink_ms.info(
                    '[INFO] generate_subdomain_deeplinks batch_generate_deeplink, '
                    'business_id: %s, deeplinks: %s',
                    self.id,
                    deeplinks,
                )
                break

        if not deeplinks or not all(row['deeplink'] for row in deeplinks):
            logger_deeplink_ms.error(
                '[ERROR] generate_subdomain_deeplinks batch_generate_deeplink, '
                'business_id: %s, deeplinks: %s, '
                'error: not all deeplinks generated (or at all)',
                self.id,
                deeplinks,
            )

            return self._instead_of_subdomain_deeplinks()

        return self._handled_subdomain_deeplinks_result(deeplinks)

    def _prepare_subdomain_deeplinks_data(self) -> list[dict]:
        from webapps.subdomain_grpc.consts import SUBDOMAINS_DEEPLINK_DATA

        batch_generate_deeplink_data = []
        deeplinks_data = copy.deepcopy(SUBDOMAINS_DEEPLINK_DATA)
        marketplace_url = self.get_marketplace_url_for_desktop_deeplink()
        generate_data = self._branchio_generate_data(
            link_code=LINK_CODE,
            marketplace_url=marketplace_url,
        )

        effortless_invites_variant = EffortlessInvitesExperiment(
            UserData(subject_key=self.id, is_experiment=True)
        )

        for deeplink_data in deeplinks_data.values():
            deeplink_data.update(
                {
                    'business_id': self.id,
                    'country_code': settings.API_COUNTRY,
                }
            )

            if not deeplink_data['fields'].get('data', {}).get(MOBILE_DEEPLINK_KEY):
                deeplink_data['fields'].setdefault('data', {})[MOBILE_DEEPLINK_KEY] = generate_data[
                    MOBILE_DEEPLINK_KEY
                ]
            if not deeplink_data['fields'].get('data', {}).get(DESKTOP_URL_KEY):
                deeplink_data['fields'].setdefault('data', {})[DESKTOP_URL_KEY] = generate_data[
                    DESKTOP_URL_KEY
                ]
            if (
                effortless_invites_variant == ExperimentVariants.VARIANT_A
                and deeplink_data['fields']['feature']
                in EffortlessInvitesExperiment.experimental_features
            ):
                deeplink_data['fields'].setdefault('data', {})[FALLBACK_URL_KEY] = generate_data[
                    DESKTOP_URL_KEY
                ]

            batch_generate_deeplink_data.append(deeplink_data)

        return batch_generate_deeplink_data

    def _handled_subdomain_deeplinks_result(self, deeplinks: list) -> dict:
        from webapps.subdomain_grpc.consts import SUBDOMAINS_DEEPLINK_DATA
        from webapps.notification.base import Channel

        result = {}
        for index, key in enumerate(SUBDOMAINS_DEEPLINK_DATA):
            if key == SeoUrl.MARKETPLACE:
                result[key] = self.get_marketplace_url_for_desktop_deeplink(
                    extra_params={
                        'app_deeplink': deeplinks[index]['deeplink'],
                        'utm_medium': DeeplinkFeature.INVITE_NOT_FORCED_APP_DOWNLOAD,
                        'utm_source': Channel.Type.SMS,
                    }
                )
            else:
                result[key] = deeplinks[index]['deeplink']

        return result

    # pylint: disable=unsubscriptable-object
    def get_photo(self, photo_type):
        qs = self.images.filter(category=photo_type).only('image_url')
        if photo_type == ImageTypeEnum.LOGO:
            # check prefetched first
            image = getattr(self, 'prefetched_logo_qs', None)
            if image is None:
                image = qs.last()
            elif image:
                image = image[-1]
        else:
            image = qs.last()

        return image.full_url if image else None

    # pylint: enable=unsubscriptable-object

    def get_cover_photo_url(self):
        image = self.images.cover_photos().only('image_url').first()
        return image.full_url if image else None

    def get_location_name(self, with_city=False):
        """Returns "<address>, <zipcode>"."""
        region = self.region
        address = self.address
        if self.address2:
            address = f'{force_str(address)},{force_str(self.address2)}'

        return ', '.join(
            filter(
                None,
                [
                    address,
                    region and region.name,
                    self.city_or_region_city if with_city else None,
                ],
            )
        )

    def bump_document(self):
        bump_document(River.BUSINESS, [self.id])

    def __repr__(self):
        return f'<Business: {self.id}>'

    def __str__(self):
        return f'Business [{self.external_api_id}] "{self.name}" ({self.owner.email})'

    @transaction.atomic
    def create_resource_from_owner(self):
        """Creates resource based on business owner data.

        This only works for new businesses. See #17051

        """
        from webapps.schedule.models import copy_business_hours_to_resource

        if self.active or self.resources.exists():
            return

        resource = Resource.objects.create(
            business=self,
            name=f'{self.owner.first_name} {self.owner.last_name}'[:50],
            type='S',
            visible=True,
            active=True,
            staff_email=self.owner.email,
            staff_user=self.owner,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_OWNER,
        )
        EcommercePermissionAdapter.update_store_permission(resource, has_store_available=True)
        copy_business_hours_to_resource(
            business_id=self.id,
            resource_id=resource.id,
            tz=self.get_timezone(),
        )
        return resource

    def set_sms_limit(self, sms_limit):
        now = self.tznow
        now_key = now.strftime('%Y-%m')
        self.sms_limit = sms_limit
        self.sms_limit_history[now_key] = sms_limit

        active_period = self.active_billing_period
        user_data = UserData(custom={'business_id': self.id})
        if SetSMSLimitForActivePeriodFlag(user_data) and active_period:
            self.sms_limit_history[active_period] = sms_limit

    @property
    def active_billing_period(self) -> str | None:
        from webapps.purchase.utils import collect_billing_cycles_for_business

        if billing_cycles_data := collect_billing_cycles_for_business(self, 1):
            billing_cycle = billing_cycles_data[0]['billing_cycles'][0]
            start_date = billing_cycle[0]
            return start_date.strftime('%Y-%m')
        return None

    @property
    def reviews_rank_avg_round(self):
        if not self.reviews_rank_avg:
            return self.reviews_rank_avg
        return round(self.reviews_rank_avg, 1)

    def get_num_reviews_per_rank(self, exclude_review_from_users: t.Iterable[int] = None) -> t.Dict:
        """Return number of reviews per rank example:
        {1:3, 2:3, 3:0, 4:1, 5:1}
        """
        if exclude_review_from_users:
            qs = self.reviews.exclude(user_id__in=exclude_review_from_users)
        else:
            qs = self.reviews
        values = qs.values('rank').annotate(reviews_count=Count('rank'))
        num_reviews_per_rank = {rank: 0 for rank in range(1, 6)}
        for review_info in values:
            num_reviews_per_rank[review_info['rank']] += review_info['reviews_count']
        return num_reviews_per_rank

    @staticmethod
    def post_save_handler(
        sender, instance: "Business", created, **_
    ):  # pylint: disable=unused-argument, too-many-branches
        from webapps.business.enums import APPSFLYER_MEDIA_SOURCE

        dirty = instance.get_dirty_fields(check_relationship=True)

        marketplace_url_fields = {'name', 'primary_category', 'region', 'seo_region'}
        should_update_deeplinks = bool(marketplace_url_fields & dirty.keys())
        if should_update_deeplinks:
            # When any of `marketplace_url_fields` fields are changed the marketplace url
            # is also changed and deeplinks must be updated to point to the new url.
            # See self.get_marketplace_sitemap_url()
            # pylint: disable=unnecessary-lambda
            transaction.on_commit(lambda: instance.update_all_subdomains_deeplinks())

        business_primary_data_fields = [
            'time_zone_name',
            'primary_category',
            'active_from',
            'name',
            'status',
            'address',
            'zipcode',
            'city',
        ]
        if any(val in dirty for val in business_primary_data_fields):
            if HintsAndWalkThroughEventsPublishingFlag():
                transaction.on_commit(BusinessPrimaryDataChangedMessage(instance).publish)

        if 'booking_max_lead_time' in dirty:
            from webapps.business.tasks import business_replan_repeating_bookings

            business_replan_repeating_bookings.delay(instance.id)
        if 'boost_status' in dirty:
            BusinessPromotion.update_boost_status(
                instance,
                instance.boost_status,
                dirty['boost_status'],
            )
        if (
            'status' in dirty
            and instance.country_code in APPSFLYER_MEDIA_SOURCE
            and instance.status == Business.Status.PAID
        ):
            from webapps.admin_extra.tasks import appsflyer_shortcut_link_task
            from webapps.business.tools import prepare_appslyer_link

            business_id = str(instance.id)
            short_link = instance.external_api_id
            link = prepare_appslyer_link(instance)
            if short_link and link:
                list_with_data = [{'biz_id': business_id, 'url': link, 'key': short_link}]
                appsflyer_shortcut_link_task.delay(list_with_data)

        user_data = UserData(key=f'business_{instance.id}')
        if any(
            value in dirty
            for value in ['name', 'region', 'address', 'address2', 'city', 'time_zone_name']
        ) and LoyaltyProgramFlag(user_data):
            transaction.on_commit(BusinessCardChangedMessage(instance).publish)
            # DEPRECATED Should be removed after loyalty program is updated to the new topics
            transaction.on_commit(LoyaltyProgramBusinessChangedPublisher(business=instance).publish)

        if created:
            Amenities.objects.get_or_create(business=instance)
            if settings.API_COUNTRY == Country.US and AutomaticKYCConsentAssignmentFlag():
                # create consent for new business to simplify KYC process
                BusinessConsentsPort.get_or_create_consent(
                    consent_code=ConsentCode.US_USER_INPUT_KYC,
                    business_id=instance.id,
                    visible=False,  # make sure it's not visible on calendar
                )

        if not created and any(key in dirty for key in ['reviews_rank_avg', 'reviews_count']):
            instance.maybe_check_boost_visibility()

    def get_payment_rows_to_settle(self):
        """
        Get payment_rows ready to settle.
        PaymentRows not settled yet, with Completed PBA payment(
            PAYMENT_SUCCESS, DEPOSIT_CHARGE_SUCCESS
        ) or Completed Pepayment (PREPAYMENT_SUCCESS)

        Raises:
            Exception: If business has no POS relation

        Returns:
            django.db.models.query.QuerySet: PaymentRow queryset.
        """
        if self.pos is None:
            raise BusinessHasNoPos('Business has no POS')

        from webapps.pos.models import PaymentRow

        return PaymentRow.objects.get_dwolla_record(add_settled_param=True).filter(
            Q(receipt__transaction__pos=self.pos),
        )

    def get_pos_plan(self, plan_type: POSPlanPaymentTypeEnum):
        """
        Get POS plan of a given type
        """
        from webapps.pos.models import POSPlan

        if not self.pos:
            # return lowest tier plan
            return POSPlan.objects.filter(individual=False, plan_type=plan_type).last()

        return self.pos.get_pos_plan(plan_type)

    @cached_property
    @using_db_for_reads(READ_ONLY_DB)
    def is_single_category(self):
        """Checks if Business has all services in a single category."""
        return (
            self.services.filter(
                active=True,
            )
            .values_list('service_category_id', flat=True)
            .distinct()
            .count()
            == 1
        )

    @cached_property
    def has_addons(self):
        from webapps.business.models.service import ServiceAddOn

        return ServiceAddOn.objects.filter(business=self).exists()

    @property
    def number_of_staffers(self):
        """Return number of active staffers in current business"""
        return len(self.staffer_ids)

    @property
    def number_of_appliances(self):
        """Return number of active appliances in current business"""
        return len(self.appliance_ids)

    @cached_property
    def resource_ids(self):
        """Active Resources in current business"""
        return dict(
            self.resources.filter(active=True)
            .values_list('type')
            .annotate(ids=ArrayAgg('id', ordering=('order', 'id'), default=None))
        )

    @property
    def staffer_ids(self):
        return self.resource_ids.get(Resource.STAFF, [])

    @property
    def appliance_ids(self):
        return self.resource_ids.get(Resource.APPLIANCE, [])

    def trial_blocked_available(self):
        """
        Return true if business status can be changed to
            Business.Status.TRIAL_BLOCKED
        :return: bool
        """
        return self.status == Business.Status.TRIAL

    def churn_available(self):
        """
        Return true if business status
        can be changed to Business.Status.BLOCKED_OVERDUE
        :return: bool
        """
        if self.active:
            return self.status in (
                Business.Status.PAID,
                Business.Status.OVERDUE,
                Business.Status.BLOCKED_OVERDUE,
            )
        # if business active for sometime
        # but have self.status == Business.Status.BLOCKED_OVERDUE
        # we want be able to create CancellationReason for it
        return self.status == Business.Status.BLOCKED_OVERDUE

    @property
    def hidden_in_search(self) -> bool:
        return not self.visible or self.custom_data.get(CustomData.HIDDEN_IN_SEARCH, False)

    @hidden_in_search.setter
    def hidden_in_search(self, value: bool):
        if value:
            self.custom_data[CustomData.HIDDEN_IN_SEARCH] = True
            return

        try:
            del self.custom_data[CustomData.HIDDEN_IN_SEARCH]
        except KeyError:
            pass

    @property
    def not_published(self) -> bool:
        if hasattr(self, 'profile_setup_progress'):
            return (
                self.hidden_in_search or self.status == Business.Status.SETUP
            ) and not self.profile_setup_progress.completed
        return False

    @property
    def was_published_after_profile_setup(self) -> bool:
        if hasattr(self, 'profile_setup_progress'):
            return self.profile_setup_progress.completed
        return False

    def set_go_back_to_search_date(self):
        self.custom_data[CustomData.HIDDEN_IN_SEARCH_TURN_OFF_UTCTIMESTAMP] = calendar.timegm(
            (tznow() + datetime.timedelta(days=7)).utctimetuple()
        )

    def delete_go_back_to_search_date(self):
        try:
            del self.custom_data[CustomData.HIDDEN_IN_SEARCH_TURN_OFF_UTCTIMESTAMP]
        except KeyError:
            pass

    @cached_property
    def suggest_services_context(self):
        if not self.visible or self.hidden_in_search:
            return {}

        context = {
            'business_categories': [str(c.id) for c in self.categories.all()] + ['*'],
            'is_b_listing': self.is_b_listing(),
        }
        if self.latitude and self.longitude:
            context['location'] = ','.join(map(str, [self.latitude, self.longitude]))
        return context

    @property
    def versum_official_name(self) -> str | None:
        return self.custom_data.get(CustomData.VERSUM_OFFICIAL_NAME)

    def is_venue(self):
        """Return True if business is have status Umbrella Venue"""
        return self.status == Business.Status.VENUE

    def is_b_listing(self):
        return self.status == Business.Status.B_LISTING

    def get_source(self):
        if not self.is_b_listing():
            return None
        try:
            return self.source
        except Business.DoesNotExist:
            return None

    def remove_categories(self, category_ids):
        from webapps.business.models.category import BusinessCategory

        if category_ids:
            categories_delete = BusinessCategory.objects.filter(id__in=category_ids)
            self.categories.remove(*list(categories_delete))

    def add_categories(self, category_ids):
        from webapps.business.models.category import BusinessCategory

        if category_ids:
            categories_add = BusinessCategory.objects.filter(id__in=category_ids)
            self.categories.add(*list(categories_add))

    def get_categories_internal_names(self):
        return list(self.categories.all().values_list('internal_name', flat=True))

    def get_counter_categories(self, without_ids=None):
        """
        Create counter of categories of all venue contractors
        :param without_ids: optional iterable.
               Will exclude all contractors categories in without_ids
        :return: Counter() from collections
        """
        category_counter = collections.Counter()
        if self.contractors.exists():
            # if some contractors were added
            # exclude them to get categories
            # before them
            contractors_qs = self.contractors
            if without_ids is not None:
                contractors_qs = contractors_qs.exclude(id__in=without_ids)

            contractor_ids = contractors_qs.values_list('id', flat=True).distinct()
            # don't use distinct
            # the same category might be added
            # from more then one contractor
            current_categories_ids = Business.objects.filter(id__in=contractor_ids).values_list(
                'categories', flat=True
            )

            # form counter of categories
            for category_id in current_categories_ids:
                category_counter[category_id] += 1
        return category_counter

    def remove_contractors_categories(self, deleted_contractor_ids):
        """
        Remove category of given contractors
        if none of other contractors do not contribute to that category
        :param deleted_contractor_ids: iterable. each element int
        :return: None
        """

        if not self.is_venue():
            return
        if not deleted_contractor_ids:
            return
        to_delete = set()

        updated_categories_ids = (
            Business.objects.filter(id__in=deleted_contractor_ids)
            .values_list('categories', flat=True)
            .distinct()
        )
        category_counter = self.get_counter_categories()

        for category_id in updated_categories_ids:
            if category_counter[category_id] == 0:
                to_delete.add(category_id)
            else:
                category_counter[category_id] -= 1

        self.remove_categories(to_delete)
        # reindex umbrella
        # and all removed contractors
        self.save(update_fields=['updated'])

    def add_contractors_categories(self, added_contractor_ids):
        """
        Remove category of given contractors
        if none of other contractors do not contribute to that category
        :param added_contractor_ids:
        :return:
        """
        if not self.is_venue():
            return
        if not added_contractor_ids:
            return

        to_add = set()

        updated_categories_ids = (
            Business.objects.filter(id__in=added_contractor_ids)
            .values_list('categories', flat=True)
            .distinct()
        )
        category_counter = self.get_counter_categories(without_ids=added_contractor_ids)

        for category_id in updated_categories_ids:
            if category_id not in category_counter:
                to_add.add(category_id)

        self.add_categories(to_add)
        # reindex umbrella
        # and all added contractors
        self.save(update_fields=['updated'])

    def recalculate_umbrella_rank(self, create_change=False):
        """
        Recalculate reviews_rank_avg and reviews_count
        for individual Umbrella Venue.
        :param create_change: optional flag if True
                              will create business cahnge object
        :return: None
        """
        from webapps.business.models.business_change import BusinessChange

        if not self.is_venue():
            return
        from webapps.user.tools import get_system_user
        from webapps.reviews.tasks import wilson_confidence

        updated_by = get_system_user()
        reviews_rank = 0
        reviews_count = 0
        before = None
        if create_change:
            before = BusinessChange.extract_vars(self)
        for contractor in self.contractors.filter(active=True):
            # contractor.reviews_count can be None
            # this mean that contractor has zero reviews
            if contractor.reviews_count is not None:
                reviews_count += contractor.reviews_count
                rank = contractor.reviews_rank_avg if contractor.reviews_rank_avg else 0
                # calculate weighted sum
                reviews_rank += rank * contractor.reviews_count

        if reviews_count:
            reviews_rank = reviews_rank / reviews_count
        reviews_rank_score = wilson_confidence(reviews_rank, reviews_count)
        if reviews_rank == 0:
            # see rank_to_stars func
            # 0.0 will lead to 1 star - this is inconsistent
            self.reviews_rank_avg = None
        else:
            self.reviews_rank_avg = reviews_rank
        self.reviews_count = reviews_count
        self.reviews_rank_score = reviews_rank_score
        self.updated = tznow()
        self.save(
            update_fields=[
                'reviews_rank_avg',
                'reviews_count',
                'reviews_rank_score',
                'updated',
            ],
        )
        if create_change:
            # BusinessChange will be created in save_related BusinessForm
            BusinessChange.add(
                self,
                self,
                before,
                operator=updated_by,
                metadata={
                    'endpoint': 'Business.recalculate_umbrella_rank',
                },
            )

    def get_formatted_address(self):
        """
        Return address of venue if business is contractor
        Otherwise address of business
        :return: dict with keys address, city, coordinate: {latitude, longitude}
        """
        ret = {}
        if self.renting_venue_id:
            venue = self.renting_venue
            ret = {
                'address': venue.get_location_name(with_city=True),
                'city': venue.city_or_region_city,
                'coordinate': {
                    'latitude': venue.latitude,
                    'longitude': venue.longitude,
                },
            }
        else:
            ret = {
                'address': self.get_location_name(with_city=True),
                'city': self.city_or_region_city,
                'coordinate': {
                    'latitude': self.latitude,
                    'longitude': self.longitude,
                },
            }
        return ret

    @property
    def agreement_exist(self):
        return hasattr(self, 'agreement')

    def get_promotion(self, date=None):
        return self._get_promotion_qset(date).order_by('id').last()

    def get_commission(self):
        from webapps.marketplace.models import MarketplaceCommission

        return MarketplaceCommission.get_commission(self)

    def has_promotion(self, date=None):
        return self._get_promotion_qset(date).exists()

    def _get_promotion_qset(self, date):
        if date is None:
            date = tznow()
        return BusinessPromotion.objects.filter(
            business=self,
            promotion_start__lte=date,
        ).filter(Q(promotion_end__isnull=True) | Q(promotion_end__gte=date))

    @property
    def promotion_allowed(self):
        has_mindjungle = (
            self.has_braintree or self.boost_payment_source == BoostPaymentSource.OFFLINE
        )
        # TODO @orzech remove all flags except has_mindbush after
        #  migrate all apps to 3.0
        return {
            'has_mindbush': has_mindjungle,
            'has_mindjungle': has_mindjungle,
            'has_braintree': has_mindjungle,
            'status': True,
            'reviews': True,
            'rank_avg': True,
            'bookings': True,
            'moderation': True,
            'description': True,
        }

    @property
    def boost_visibility_allowed(self):
        return {
            'reviews': (self.reviews_count or 0) >= 5
            and (self.reviews_rank_avg_round or 0.0) >= 4.5,
            'inspirations': self.images.filter(
                category=ImageTypeEnum.INSPIRATION,
            ).count()
            >= 3,
            'cover_photo': self.images.filter(
                is_cover_photo=True,
            ).exists(),
        }

    def claim_ratio(self, days=None):
        """
        Counts ratio of claimed boost appointments to all valid boost appointments.
        It's crucial to detect claim abuse

        :param days: number of  days to consider. If null take all
        :return: boost claim ratio
        """
        from webapps.boost.models import BoostAppointment

        all_common_condition = ~Q(
            status=BoostAppointmentStatus.NOSHOW
        ) & ~Q(  # no-shows are excluded from calculations
            deleted__isnull=False,
            status__in=[
                *BoostAppointmentStatus.payable_statuses(),
                BoostAppointmentStatus.OFFLINE,
            ],
        )  # exclude invalid BAs, that have been previously deleted due to e.g. order change

        query_kwargs = {'boost_promotion__business': self}
        if days:
            query_kwargs.update(
                {'appointment__booked_till__gte': tznow() - datetime.timedelta(days=days)}
            )

        claimed_count_expr = Count(
            'id',
            filter=Q(status__in=BoostAppointmentStatus.claimed_by_merchant_statuses()),
        )
        all_count_expr = Count('id')

        data = (
            BoostAppointment.objects.filter(all_common_condition, **query_kwargs)
            .values('boost_promotion__business')
            .annotate(
                claimed_count=claimed_count_expr,
                all_count=all_count_expr,
            )
        )

        if not data:
            return 0.0

        business_data = data[0]
        if not business_data or not business_data['all_count']:
            return 0.0

        return round(business_data['claimed_count'] * 1.0 / business_data['all_count'], 4)

    @property
    def claim_allowed(self):
        claim_ratio = self.claim_ratio()
        if not claim_ratio:
            return claim_ratio < settings.BOOST.CLAIM_ALLOWED_THRESHOLD

        return max(self.claim_ratio(days=30), claim_ratio) < settings.BOOST.CLAIM_ALLOWED_THRESHOLD

    @property
    def is_claim_ratio_over_account_notice_limit(self):
        return (
            self.claim_ratio(days=30) >= settings.BOOST.CLAIM_LIMIT_BEFORE_ACCOUNT_NOTICE
            or self.claim_ratio() >= settings.BOOST.CLAIM_LIMIT_BEFORE_ACCOUNT_NOTICE
        )

    @property
    def promoted_before(self):
        return BusinessPromotion.objects.filter(business=self).exists()

    def has_categories_internal_name_match(self, matching_categories: set[BusinessCategoryEnum]):
        categories_internal_name = []
        if self.primary_category:
            categories_internal_name.append(self.primary_category.internal_name)

        prefetched_objects_cache = getattr(self, '_prefetched_objects_cache', {})
        if 'categories' in prefetched_objects_cache:
            categories_internal_name.extend(
                category.internal_name for category in prefetched_objects_cache['categories']
            )
        else:
            categories_internal_name.extend(self.categories.values_list('internal_name', flat=True))
        return any(
            internal_name in matching_categories for internal_name in categories_internal_name
        )

    @cached_property
    def patient_file_enabled(self):
        if settings.API_COUNTRY not in settings.PHYSIOTHERAPY_COUNTRIES:
            return False

        if not self.custom_data.get(CustomData.PHYSIOTHERAPY_ENABLED, False):
            return False

        return self.has_categories_internal_name_match(
            matching_categories=CATEGORIES_WITH_PATIENT_FILES
        )

    @cached_property
    def show_medical_consents(self):
        if (
            settings.SHOW_MEDICAL_CONSENTS
            and self.primary_category
            and self.primary_category.internal_name in MEDICAL_CATEGORIES
        ):
            return True
        return False

    @cached_property
    def has_proper_category_for_extend_trial(self):
        return self.has_categories_internal_name_match(
            matching_categories=EXTEND_TRIAL_EXPERIMENT_CATEGORIES
        )

    def can_transform_into_b_listing(self):
        from cliapps.business.b_listing import check_blisting_email

        if (
            self.custom_data.get(
                CustomData.CAN_TRANSFORM_INTO_B_LISTING,
            )
            is False
        ):
            return False
        if not (self.address and self.region):
            return False
        try:
            if not check_blisting_email(email=self.owner.email):
                return False
        except Business.owner.RelatedObjectDoesNotExist:
            pass
        return (
            (self.status == Business.Status.BLOCKED)
            or (self.status == Business.Status.CHURNED and not self.active)
            or (self.status == Business.Status.TRIAL_BLOCKED and not self.active)
        )

    @property
    def sms_limits_paid_status(self):
        """Returns enum which we can use ex. to check
        country sms limits in settings."""
        if self.has_subscription:
            return SMSPaidEnum.NON_TRIAL
        return SMSPaidEnum.TRIAL

    @cached_property
    def phone_with_prefix(self):
        """Returns the phone number with the country prefix."""
        return get_prep_value_form(self.phone)

    @cached_property
    @using_db_for_reads(
        database_name=READ_ONLY_DB,
    )
    def top_services_ranking(self):
        """
        Returns list of services ordered by booking popularity.

        :return: List of ordered bookings count dicts:
            - service_id
            - booking_count
            - gender
        """
        from webapps.booking.models import Appointment
        from webapps.booking.models import SubBooking

        try:
            first_applicable_id = (
                SubBooking.objects.filter(
                    created__gte=(self.tznow - datetime.timedelta(days=LAST_BOOKINGS_PERIOD))
                )
                .order_by('created')
                .values_list('id', flat=True)[0]
            )
        except IndexError:
            return []

        service_ranking = (
            SubBooking.objects.filter(
                service_variant__service__business_id=self.id,
                id__gte=first_applicable_id,
                appointment__type=Appointment.TYPE.CUSTOMER,
                appointment__business_id=self.id,
                service_variant__service__is_available_for_customer_booking=True,
                service_variant__service__active=True,
                combo_parent__isnull=True,
            )
            .values(
                service_id=F('service_variant__service__id'),
                gender=F('appointment__booked_for__user__gender'),
            )
            .exclude(
                service_id__isnull=True,
            )
            .annotate(bookings_count=Count('service_variant__service__id'))
            .order_by('-bookings_count')
        )

        bookings_count = sum(elem['bookings_count'] for elem in service_ranking)
        return [] if bookings_count < TOP_SERVICES_MINIMAL_BOOKINGS_COUNT else service_ranking

    @cached_property
    @using_db_for_reads(
        database_name=READ_ONLY_DB,
    )
    def top_online_services_ranking(self):
        from webapps.booking.models import Appointment
        from webapps.booking.models import SubBooking

        online_service_ranking = (
            SubBooking.objects.filter(
                service_variant__service__business_id=self.id,
                created__gte=(self.tznow - datetime.timedelta(days=LAST_BOOKINGS_PERIOD)),
                appointment__type=Appointment.TYPE.CUSTOMER,
                service_variant__isnull=False,
                service_variant__service__is_available_for_customer_booking=True,
                service_variant__service__active=True,
                service_variant__service__is_online_service=True,
            )
            .values(
                service_id=F('service_variant__service__id'),
                gender=F('appointment__booked_for__user__gender'),
            )
            .annotate(
                bookings_count=Count('service_variant__service__id'),
            )
            .order_by(
                '-bookings_count',
            )
        )
        return online_service_ranking

    def bookable_online_services(self):
        return self.services.filter(
            is_online_service=True,
            is_available_for_customer_booking=True,
            active=True,
        )

    @cached_property
    def bookable_online_service_ids(self):
        return self.bookable_online_services().values_list('id', flat=True)

    @cached_property
    def has_bookable_online_services(self) -> bool:
        return self.bookable_online_services().exists()

    def fill_top_online_service_ids(self, top_service_ids):
        too_few_count = TOP_SERVICES_NUMBER - len(top_service_ids)
        all_online_service_ids = self.bookable_online_service_ids
        other_online_ids = list(set(all_online_service_ids) - set(top_service_ids))
        return top_service_ids + other_online_ids[:too_few_count]

    @staticmethod
    def get_top_service_ids_by_gender(services_ranking, gender):
        return [
            elem['service_id']
            for elem in islice(
                (x for x in services_ranking if x['gender'] == gender),
                TOP_SERVICES_NUMBER,
            )
        ]

    @staticmethod
    def get_top_service_ids_both_genders(service_ranking):
        services_bookings = collections.defaultdict(int)
        for elem in service_ranking:
            services_bookings[elem['service_id']] += elem['bookings_count']
        top_services_bookings = sorted(
            (
                (service_id, booking_count)
                for service_id, booking_count in list(services_bookings.items())
            ),
            key=lambda x: -x[1],
        )
        return list(
            islice(
                (elem[0] for elem in top_services_bookings),
                TOP_SERVICES_NUMBER,
            )
        )

    @property
    def top_female_services_ids(self):
        if self.has_bookable_online_services:
            top_service_ids = self.get_top_service_ids_by_gender(
                self.top_online_services_ranking, Gender.Female
            )
            if len(top_service_ids) < TOP_SERVICES_NUMBER:
                top_service_ids = self.fill_top_online_service_ids(top_service_ids)
            return top_service_ids

        return self.get_top_service_ids_by_gender(self.top_services_ranking, Gender.Female)

    @property
    def top_male_services_ids(self):
        if self.has_bookable_online_services:
            top_service_ids = self.get_top_service_ids_by_gender(
                self.top_online_services_ranking, Gender.Male
            )
            if len(top_service_ids) < TOP_SERVICES_NUMBER:
                top_service_ids = self.fill_top_online_service_ids(top_service_ids)
            return top_service_ids

        return self.get_top_service_ids_by_gender(self.top_services_ranking, Gender.Male)

    @property
    def top_business_services_ids(self):
        if self.has_bookable_online_services:
            service_ids = self.get_top_service_ids_both_genders(self.top_online_services_ranking)
            if len(service_ids) < TOP_SERVICES_NUMBER:
                service_ids = self.fill_top_online_service_ids(service_ids)
            return service_ids

        return self.get_top_service_ids_both_genders(self.top_services_ranking)

    @property
    def best_of_booksy_business_award_names(self):
        return self.best_of_booksy_business_awards.annotate(
            award_name=Concat(F('award__name'), Value('_'), F('award__award_period__period_name'))
        ).values_list('award_name', flat=True)

    def bump_boost_visibility(self):
        """
        the job is delegated to `update_selected_businesses_tuning_params_task` which
        updates tunings for all businesses gathered at River.BUSINESS_SEARCH_DATA every 10 min
        """
        bump_document(River.BUSINESS_SEARCH_DATA, [self.id])

    def maybe_check_boost_visibility(self):
        if self.boost_status not in self.BoostStatus.active_statuses() or not self.has_tuning:
            return

        self.bump_boost_visibility()

    def _get_time_slots_optimization(self):
        return self.custom_data.get(
            CustomData.TIME_SLOTS_OPTIMIZATION_ENABLED,
            False,
        )

    def _set_time_slots_optimization(self, value):
        if value is None:
            value = False
        self.custom_data[CustomData.TIME_SLOTS_OPTIMIZATION_ENABLED] = value

    time_slots_optimization = property(
        _get_time_slots_optimization,
        _set_time_slots_optimization,
    )

    @property
    def has_active_booksy_med(self):
        return self.partner_apps.filter(
            status__in=OAuth2Installation.ACTIVE_STATUSES,
            application__client_id=settings.FIZJOREJESTRACJA_APPLICATION_CLIENT_ID,
            deleted__isnull=True,
        ).exists()

    def has_customer_booking(self, source=None):
        from webapps.booking.models import Appointment

        if source and source.name == consts.LANDING_PAGE:
            return True
        return Appointment.objects.filter(
            business=self,
            type=Appointment.TYPE.CUSTOMER,
            status=Appointment.STATUS.FINISHED,
        ).exists()

    def successful_appointments_count(self):
        from webapps.booking.models import Appointment

        return Appointment.objects.filter(
            business=self,
            status=Appointment.STATUS.FINISHED,
        ).count()

    def get_current_subscription(self, date=None):
        if not date:
            date = tznow()
        return (
            self.subscriptions.filter(Q(expiry__gte=date) | Q(expiry__isnull=True))
            .filter(start__lte=date)
            .order_by('-start')
            .first()
        )

    @property
    def donations_enabled(self):
        # deprecated
        return False

    @property
    def has_safety_rules(self) -> bool:
        return bool(self.safety_rules.exists() or self.safety_note_text)

    @property
    def safety_note_text(self):
        return self.note.text if hasattr(self, 'note') else None

    def disable_boost_availability(self):
        self._switch_boost_availability(False)

    def enable_boost_availability(self):
        self._switch_boost_availability(True)

    def _switch_boost_availability(self, enable):
        from webapps.marketplace.models import MarketplaceCommission

        commission = MarketplaceCommission.get_commission(self)
        commission_data = {}
        if commission:
            if commission.business:
                commission.marketplace = enable
                commission.save()
                return

            commission_data = {
                'commission': commission.commission,
                'bottom_cap': commission.bottom_cap,
                'max': commission.max,
                'minimum_commission': commission.minimum_commission,
                'flat_fee': commission.flat_fee,
            }
        # TODO delete after commision.max refactor
        else:
            from webapps.marketplace.models import get_default_min_commission

            commission_data = {
                'max': 250,
                'bottom_cap': get_default_min_commission(),
            }
        MarketplaceCommission(business=self, marketplace=enable, **commission_data).save()

    def get_pdf_annex(self):
        from lib.pdf_render import PDFRenderer

        template_fnc = 'gdpr_annex_{}'.format
        default_nip = '_' * 13

        business_name = safe_get(self, ['agreement', 'business_name']) or self.name
        address = safe_get(self, ['agreement', 'business_address']) or self.address
        city = self.city
        zip_code = self.region.name
        business_address = ', '.join((address, city, zip_code))
        nip = safe_get(self, ['agreement', 'NIP']) or default_nip

        template_kwargs = {
            'owner': self.owner,
            'business_name': business_name,
            'business_address': business_address,
            'NIP': nip,
        }

        try:
            return PDFRenderer.render_pdf(
                template_name=template_fnc(settings.API_COUNTRY),
                **template_kwargs,
            )
        except jinja2.exceptions.TemplateNotFound:
            return PDFRenderer.render_pdf(
                template_name=template_fnc('default'),
                **template_kwargs,
            )

    @property
    def boosted(self):
        return self.boost_status in Business.BoostStatus.active_statuses()

    def is_user_from_recommending_salon(self, user_id):
        """
        Have the user previously became a client of any other salon that directs
        to `self` via 'You can also book with us here'?
        """
        from webapps.business.models.bci import BusinessCustomerInfo

        return BusinessCustomerInfo.objects.filter(
            business__salon_network__members=self,
            user_id=user_id,
        ).exists()

    def get_delayed_activation_day(self):
        return self.visible_delay_till

    def set_delayed_activation_day(self, delayed_activation_datetime):
        if delayed_activation_datetime <= tznow():
            return
        self.visible = False
        self.visible_delay_till = delayed_activation_datetime
        self.save(update_fields=['visible_delay_till', 'visible'])

    def get_excluded_delayed_invitations_bci_ids(self, ignore_visibility=False):
        from webapps.business.models.bci import BusinessCustomerInfo

        if not ignore_visibility and (
            not self.visible_delay_till or self.visible_delay_till < tznow()
        ):
            return []
        return list(
            BusinessCustomerInfo.objects.filter(
                business_id=self.id,
                excluded_from_delayed_invitation=True,
            ).values_list('id', flat=True)
        )

    def add_bci_to_delayed_invitation_exclusion(self, bci_id):
        from webapps.business.models.bci import BusinessCustomerInfo

        BusinessCustomerInfo.objects.filter(
            id=bci_id,
            business_id=self.id,
            excluded_from_delayed_invitation=False,
        ).update(excluded_from_delayed_invitation=True)

    def remove_bcis_from_delayed_invitation_exclusion(self, bci_ids):
        from webapps.business.models.bci import BusinessCustomerInfo

        BusinessCustomerInfo.objects.filter(
            id__in=bci_ids,
            business_id=self.id,
            excluded_from_delayed_invitation=True,
        ).update(excluded_from_delayed_invitation=False)

    def is_fbe_connected(self, staffer_id: t.Optional[int] = 0) -> bool:
        return self.get_facebook_fbe_connection_data(staffer_id).enabled

    def get_facebook_fbe_connection_data(
        self,
        staffer_id: t.Optional[int] = 0,
    ) -> FacebookFBEConnect:
        staffer_id = self._staffer_id_to_key(staffer_id)
        return FacebookFBEConnect(
            **self.integrations.get(BusinessIntegration.FACEBOOK, {}).get(staffer_id, {})
        )

    def set_facebook_fbe_connection_data(
        self,
        data: FacebookFBEConnect,
        staffer_id: t.Optional[int] = 0,
    ) -> None:
        staffer_id = self._staffer_id_to_key(staffer_id)
        if not self.integrations.get(BusinessIntegration.FACEBOOK):
            self.integrations[BusinessIntegration.FACEBOOK] = {}

        if not self.integrations[BusinessIntegration.FACEBOOK].get(staffer_id):
            self.integrations[BusinessIntegration.FACEBOOK][staffer_id] = {}
        self.integrations[BusinessIntegration.FACEBOOK][staffer_id].update(data.__dict__)
        self.save(update_fields=['integrations'])

    def remove_facebook_fbe_connection(self, staffer_id: t.Optional[int] = 0) -> None:
        self.set_facebook_fbe_connection_data(FacebookFBEConnect(), staffer_id)

    @staticmethod
    def _staffer_id_to_key(staffer_id: t.Optional[int]) -> str:
        # integer keys would be casted to str in database anyway
        # staffer_id = 0/None - means it's the main connection, done by owner or manager
        return str(staffer_id) if staffer_id else 'owner'

    def get_global_claim_percent(self):
        return round(self.claim_ratio() * 100, 2)

    def get_monthly_claim_percent(self):
        return round(self.claim_ratio(days=30) * 100, 2)

    def go_offline(self, commit=True):
        """Turn off online booking and hide business for search."""
        from webapps.business.notifications.search_visibility import (
            BusinessNotVisibleNotification,
        )

        self.visible = False
        self.hidden_in_search = True
        self.delete_go_back_to_search_date()

        if commit:
            self.save(update_fields=['visible', 'custom_data'])
            BusinessNotVisibleNotification(
                event_sender=None,
                business_id=self.id,
            ).schedule(days_ahead=3)

    def go_online(self, commit=True):
        """Turn on online booking and show business in search results."""
        self.visible = True
        self.hidden_in_search = False
        self.delete_go_back_to_search_date()

        if commit:
            self.save(update_fields=['visible', 'custom_data'])

    def go_invisible_in_search_results(self, commit=True):
        self.hidden_in_search = True
        self.set_go_back_to_search_date()

        if commit:
            self.save(update_fields=['custom_data'])

    def go_visible_in_search_results(self, commit=True):
        self.hidden_in_search = False
        self.delete_go_back_to_search_date()

        if commit:
            self.save(update_fields=['custom_data'])

    def buyer_at(self, date_time):
        return (
            self.historical_buyers.filter(
                owned_from__lte=date_time,
            )
            .order_by('-owned_from')
            .first()
            or self.buyer
        )

    @cached_property
    def current_buyer(self):
        return self.buyer_at(tznow())

    @property
    def tznow(self):
        return tznow(tz=self.get_timezone())

    @property
    def has_subscription(self) -> bool:
        return self.status in Business.Status.paid_statuses()


# pylint: enable=too-many-instance-attributes, too-many-public-methods


post_save.connect(Business.post_save_handler, Business)


class BusinessEvent(ArchiveModel):
    business = models.OneToOneField(
        'business.Business',
        on_delete=models.CASCADE,
    )
    first_no_show = models.BooleanField(default=False)
    late_cancellation_count = models.IntegerField(default=0)
    ask_for_message_blast_activation = models.BooleanField(default=True)

    objects = AutoUpdateManager()
    all_objects = models.Manager()

    def __str__(self):
        return f'Events of {self.business}'


class PromotionAwaitingBusiness(Business):
    class Meta:
        proxy = True


class RentingVenueManager(models.Manager.from_queryset(AutoUpdateQuerySet)):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(
                active=True,
                status=Business.Status.VENUE,
            )
        )


class RentingVenue(Business):
    objects = RentingVenueManager()

    class Meta:
        proxy = True
        ordering = ["created"]
        verbose_name = _('Umbrella Venue')
        verbose_name_plural = _('Umbrella Venues')


class BListingQueryManager(models.Manager.from_queryset(AutoUpdateQuerySet)):
    def get_queryset(self):
        return (
            super()
            .get_queryset()
            .filter(
                status=Business.Status.B_LISTING,
            )
        )


# pylint: disable=too-many-instance-attributes
class BListing(Business):
    """
    Proxy model to Business (with status = Business.Status.B_LISTING).
    Used as business visible on list but without a possibility
    to book an appointment.
    """

    objects = BListingQueryManager()

    class Meta:
        proxy = True
        ordering = ['created']
        verbose_name = _('B Listing')
        verbose_name_plural = _('B Listing')

    def save(self, *args, **kwargs):
        if not self.id:
            self.status = Business.Status.B_LISTING
            self.include_in_analysis = False
        dirty_fields = self.get_dirty_fields()
        if dirty_fields.get('active') and not self.active:
            self.b_listing_acquisitions.filter(deleted__isnull=True).update(
                deleted=tznow(),
            )
            self.active_till = tznow()
        return super().save(*args, **kwargs)


class ResourceQuerySet(PartnerAppDataQuerySet, AutoUpdateQuerySet):
    pass


class ResourceManager(AutoUpdateManager.from_queryset(ResourceQuerySet)):
    pass


# pylint: enable=too-many-instance-attributes
class Resource(
    PhotoModelMixin,
    ArchiveModel,
    ESDocMixin,
    DirtyFieldsMixin,
):
    photo_folder = ImageTypeEnum.RESOURCE
    es_doc_type = ESDocType.RESOURCE

    class Meta:
        verbose_name = _('Resource')
        verbose_name_plural = _('Resources')
        ordering = ['order', 'id']

    STAFF = ResourceType.STAFF
    APPLIANCE = ResourceType.APPLIANCE
    RESOURCE_TYPES = ResourceType.choices()

    id = models.AutoField(primary_key=True, db_column='resource_id')
    business = models.ForeignKey(
        'business.Business',
        related_name='resources',
        on_delete=models.CASCADE,
    )
    services = models.ManyToManyField(
        Service,
        related_name='resources',
        blank=True,
    )
    service_variants = models.ManyToManyField(
        ServiceVariant,
        related_name='resources',
        blank=True,
    )
    name = models.CharField(
        null=False,
        blank=False,
        max_length=(consts.FIRST_NAME_LEN + consts.LAST_NAME_LEN + 1),
    )
    type = models.CharField(max_length=1, choices=RESOURCE_TYPES, null=False, blank=False)
    visible = models.BooleanField(default=True)  # visible on customer cal.
    description = models.TextField(null=True, blank=True)
    order = models.IntegerField(default=0)
    active = models.BooleanField(default=True, db_index=True)

    # will be used both with type=STAFF and type=APPLIANCE
    photo = models.ForeignKey(
        'photo.Photo',
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )

    # will be used only with type=STAFF
    staff_email = models.EmailField(
        _('e-mail address'), max_length=75, blank=True, validators=[validate_email]
    )
    staff_cell_phone = BooksyPhoneNumberField(null=True)
    staff_user = models.ForeignKey(
        'user.User',
        related_name='staffers',
        null=True,
        blank=True,
        default=None,
        on_delete=models.SET_NULL,
    )
    reviews_rank_score = models.FloatField(null=True, blank=True)
    reviews_rank_avg = models.FloatField(null=True, blank=True)
    import_uid = models.CharField(max_length=64, null=True, blank=True)
    position = models.CharField(max_length=64, blank=True, default='')
    invited = models.DateTimeField(blank=True, default=None, null=True)
    # use to hide resource on business calendar, if the resource has no bookings (!)
    visible_on_calendar = models.BooleanField(default=True, null=True)

    STAFF_ACCESS_LEVEL_OWNER = StaffAccessLevels.OWNER.value
    STAFF_ACCESS_LEVEL_MANAGER = StaffAccessLevels.MANAGER.value
    STAFF_ACCESS_LEVEL_RECEPTION = StaffAccessLevels.RECEPTION.value
    STAFF_ACCESS_LEVEL_ADVANCED = StaffAccessLevels.ADVANCED.value
    STAFF_ACCESS_LEVEL_STAFF = StaffAccessLevels.STAFF.value
    STAFF_ACCESS_LEVELS_ALL = (
        STAFF_ACCESS_LEVEL_OWNER,
        STAFF_ACCESS_LEVEL_MANAGER,
        STAFF_ACCESS_LEVEL_RECEPTION,
        STAFF_ACCESS_LEVEL_ADVANCED,
        STAFF_ACCESS_LEVEL_STAFF,
    )
    STAFF_ACCESS_LEVELS_NOT_OWNER = (
        STAFF_ACCESS_LEVEL_MANAGER,
        STAFF_ACCESS_LEVEL_RECEPTION,
        STAFF_ACCESS_LEVEL_ADVANCED,
        STAFF_ACCESS_LEVEL_STAFF,
    )
    STAFF_ACCESS_LEVELS_LIMIT_CALENDAR = [
        STAFF_ACCESS_LEVEL_STAFF,  # rm after full release of Booksy 2.0
    ]
    STAFF_ACCESS_LEVELS_LOCKED = (
        STAFF_ACCESS_LEVEL_RECEPTION,
        STAFF_ACCESS_LEVEL_ADVANCED,
        STAFF_ACCESS_LEVEL_STAFF,
    )
    staff_access_level = models.CharField(
        max_length=10,
        choices=StaffAccessLevels.choices(),
        null=True,
        blank=True,
    )
    version = models.PositiveIntegerField(null=True, default=0)

    objects = ResourceManager()
    all_objects = models.Manager()

    def clean(self):
        if not self.business.is_in_setup_state and self.active is False:
            error = self.business.check_business_resources(
                resources=self.business.resources,
                excluded_ids=[self.id],
            )
            if error is not None:
                raise ValidationError(error.pop('description'), params=error)

    def validate_unique(self, exclude=None):
        super().validate_unique(exclude)
        resource_qs = Resource.objects.filter(
            business=self.business,
            type=self.type,
            name=self.name,
            active=True,
        )
        if self.id:
            resource_qs = resource_qs.exclude(id=self.id)
        if self.active and resource_qs.exists():
            raise ValidationError(
                {
                    'name': [
                        {
                            'message': (
                                _("%s with this name already exists for this business")
                                % (self.get_type_display() or _('Resource'))
                            )
                        }
                    ]
                }
            )

    def update(self, **kwargs) -> None:
        for field, value in kwargs.items():
            setattr(self, field, value)
        self.save()

    @property
    def services_query(self):
        handled_services_ids = self.active_services_ids
        services_query = self.business.services.filter(pk__in=handled_services_ids)
        return services_query

    @cached_property
    def staff_cell_phone_with_prefix(self):
        """Returns the cell phone number with the country prefix."""
        return get_prep_value_form(self.staff_cell_phone)

    @property
    def unhandled_services(self):
        return self.business.services.exclude(pk__in=self.services_query)

    def __repr__(self):
        return (
            f"<{self._meta.object_name}: "
            f"{hasattr(self, 'attributes') and self.attributes or self.id} type={self.type}>"
        )

    def __str__(self):
        return f'{self.get_type_display()} {self.name} [{self.id}]'

    @property
    def active_service_variants(self):
        return self.service_variants.filter(active=True)

    @property
    def active_services(self):
        if self.is_staffer:
            services = list(
                Service.objects.filter(
                    active=True,
                    service_variants__in=self.service_variants.filter(
                        active=True, service__combo_type__isnull=True
                    ),
                ).distinct()
            )
            return services

        return list(
            self.services.annotate_is_combo().filter(
                active=True,
                is_combo=False,
            )
        )

    @property
    def active_services_ids(self):
        return [service.id for service in self.active_services]

    @property
    def admin_id_link(self):
        return format_html('<a href="{}">{}</a>', admin_link(self), escape(self))

    @property
    def current_working_hours(self):
        return get_resource_default_hours(business_id=self.business_id, resource_id=self.id)

    @property
    def is_staffer(self):
        return self.type == self.STAFF

    @property
    def is_appliance(self):
        return self.type == self.APPLIANCE

    @staticmethod
    def bump_version(resource_ids):
        Resource.objects.filter(
            id__in=list(resource_ids),
            active=True,
            deleted__isnull=True,
        ).update(
            version=F('version') + 1,
            updated=tznow(),
        )

    def staff_user_create(self, name=None, email=None, cell_phone=None):
        from webapps.user.models import User
        from webapps.business.models.bci import BusinessCustomerInfo

        staff_email = (email or self.staff_email or '').lower()
        staff_cell_phone = cell_phone or self.staff_cell_phone
        first_name, last_name = BusinessCustomerInfo.split_full_name(name or self.name)
        # ensure User.last_name is not empty
        last_name = last_name or FORCED_LAST_NAME

        lib.tools.quick_assert(
            not User.objects.filter(email__iexact=staff_email).exists(),
            ('invalid', 'validation', 'staff_email'),
            _('User with provided email already exists.'),
        )

        user = User.objects.create(
            username=User.generate_username(staff_email),
            email=staff_email,
            first_name=first_name,
            last_name=last_name,
            cell_phone=staff_cell_phone,
            is_active=True,
        )

        self.staff_user = user
        self.save()

    def staff_user_invite(self):
        if self.business.status == Business.Status.SETUP:
            # #30299 - send invitation after onboarding
            return
        if self.business.owner.email == self.staff_user.email:
            return
        user_password = lib.tools.generate_password()
        self.staff_user.set_password(user_password)
        self.staff_user.password_change_required = True
        self.staff_user.save()

        StafferInvitationScenario.send_mail(self, user_password)

        self.invited = self.business.tznow
        self.save(update_fields=['invited'])

    def add_services(self, services: t.Sequence[int] | t.Sequence['Service']):
        if services and isinstance(services[0], int):
            services = Service.objects.annotate_is_combo().filter(
                id__in=services, active=True, is_combo=False
            )
        for service in services:
            service.add_appliances([self])
            service.add_staffers([self])

    def remove_services(self, services: t.Sequence[int] | t.Sequence['Service']):
        if services and isinstance(services[0], int):
            services = (
                Service.objects.annotate_is_combo()
                .filter(id__in=services, active=True, is_combo=False)
                .prefetch_related('service_variants')
            )
        for service in services:
            service.remove_appliances([self])
            service.remove_staffers([self])

    def remove_all_services(self):
        # under assumption assigned inactive services won't ever be active again
        self.remove_services(self.active_services)

    def set_services(
        self, services: t.Sequence[int] | t.Sequence['Service'], reset_current_services=True
    ):
        if services and isinstance(services[0], int):
            received_service_ids = set(services)
            received_services = Service.objects.annotate_is_combo().filter(
                id__in=received_service_ids, active=True, is_combo=False
            )
        else:
            received_service_ids = {service.id for service in services}
            received_services = services

        current_services = self.active_services
        current_service_ids = {service.id for service in current_services}

        removed_service_ids: set = current_service_ids - received_service_ids
        added_service_ids: set = received_service_ids - current_service_ids

        removed_services = [s for s in current_services if s.id in removed_service_ids]
        added_services = [s for s in received_services if s.id in added_service_ids]

        self.remove_services(removed_services)
        self.add_services(added_services)

        if self.is_staffer and (
            reset_current_services or PreventResetCurrentStafferServicesFlag() is False
        ):
            kept_service_ids: set = received_service_ids & current_service_ids
            kept_services = [s for s in current_services if s.id in kept_service_ids]
            for service in kept_services:
                service.reset_staffers([self])

    def add_service_variants(self, service_variants: list['ServiceVariant']):
        self._validate_service_variants_assignability(raise_exception=True)

        for variant in service_variants:
            variant.add_staffers([self])

    def remove_service_variants(self, service_variants: list['ServiceVariant']):
        self._validate_service_variants_assignability(raise_exception=True)

        for variant in service_variants:
            variant.remove_staffers([self])

    def set_service_variants(self, service_variants: list['ServiceVariant']):
        self._validate_service_variants_assignability(raise_exception=True)

        received_service_variants = set(service_variants)
        current_service_variants = set(self.active_service_variants)

        removed_service_variants = list(current_service_variants - received_service_variants)
        added_service_variants = list(received_service_variants - current_service_variants)

        self.remove_service_variants(removed_service_variants)
        self.add_service_variants(added_service_variants)

    def _validate_service_variants_assignability(self, raise_exception=False):
        if self.is_staffer:
            return True

        if raise_exception:
            raise ValueError(
                "Resource has to be a staffer to be able to get service's variants assigned"
            )

        return False

    def save(self, **kwargs):  # pylint: disable=arguments-differ
        if self.type == Resource.STAFF and not self.staff_access_level:
            self.staff_access_level = Resource.STAFF_ACCESS_LEVEL_STAFF
        dirty_fields = self.get_dirty_fields()
        if self.id and (dirty_fields.get('active') or dirty_fields.get('visible')):
            user_ids = [bci.user_id for bci in self.bookmarking_customers.filter().only('user_id')]
            bump_document(River.USER_FAST_UPDATE_SEARCH_DATA, user_ids)
        self.version = self.version + 1 if self.version is not None else 1

        return super().save(**kwargs)


class ResourceHistory(ArchiveModel, ESDocMixin):
    es_doc_type = ESDocType.RESOURCE_HISTORY
    resource = models.ForeignKey(
        Resource,
        related_name='changelogs',
        on_delete=models.CASCADE,
    )
    requested_by = models.ForeignKey(
        'user.User',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
    )
    created = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Created (UTC)',
    )
    data = models.TextField(null=True, blank=True)
    diff = models.TextField(null=True, blank=True)

    class Meta:
        verbose_name = 'Resource History'
        verbose_name_plural = verbose_name

    @classmethod
    def create(cls, resource, requested_by, save=True, **kwargs):
        from webapps.business.elasticsearch.resources import ResourceHistoryDocument

        now = tznow()

        cls.resource = resource
        cls.created = now
        cls.data = cls.get_data(resource)
        cls.diff = cls.calculate_diff(cls)

        kwargs['created'] = cls.created
        kwargs['data'] = json.dumps(cls.data, indent=True, sort_keys=True)
        kwargs['resource_id'] = resource.id
        kwargs['requested_by_id'] = requested_by.id if requested_by else None
        kwargs['business_id'] = resource.business.id
        kwargs['diff'] = cls.diff

        entry = ResourceHistoryDocument(**kwargs)
        if save:
            entry.save()

        return entry

    @staticmethod
    def get_data(resource):
        from webapps.business.serializers import FullResourceSerializer

        representation = FullResourceSerializer(instance=resource).data

        variants = [
            'service variant ' + str(variant) for variant in representation.get('service_variants')
        ]
        if variants:
            representation['service_variants'] = variants
        services = ['service ' + str(service) for service in representation.get('services')]
        if services:
            representation['services'] = services

        return representation

    def calculate_diff(self):
        from webapps.business.elasticsearch.resources import ResourceHistoryDocument

        docs = (
            ResourceHistoryDocument.search()
            .query('term', resource_id=self.resource.id)
            .sort('-created')
            .execute()
        )
        previous_entry = docs[0].data if docs else ''
        previous_data = previous_entry
        current_data = json.dumps(self.data, indent=True, sort_keys=True)

        diff = Differ().compare(previous_data.split('\n'), current_data.split('\n'))
        return '\n'.join(line for line in diff if line[0] in '+-')


class ResourceInternalData(ChangeArchivedModel):
    resource = models.OneToOneField(
        Resource,
        primary_key=True,
        on_delete=models.CASCADE,
        related_name='internal_data',
    )
    last_seen_service_type_alert_generation = models.IntegerField(
        default=0
    )  # deprecated in favor of BusinessUserInternalData


class BusinessUserInternalData(ChangeArchivedModel):
    class Meta:
        verbose_name = 'Business User Internal Data'
        verbose_name_plural = verbose_name

        constraints = [
            models.UniqueConstraint(
                fields=['business', 'user'],
                name='business_user_unique_constraint',
            ),
        ]

    business = models.ForeignKey(
        Business,
        related_name='business_user_internal_data',
        on_delete=models.CASCADE,
    )
    user = models.ForeignKey(
        'user.User',
        related_name='business_user_internal_data',
        on_delete=models.CASCADE,
    )
    last_seen_service_type_alert_generation = models.IntegerField(default=0)


class StafferLockedAccess(models.Model):
    class Meta:
        verbose_name = _('Staffer Locked Access')
        verbose_name_plural = _('Staffer Locked Accesses')
        index_together = [
            ('business', 'staffer', 'created'),
        ]

    FIELDS_NAMES = (
        ('email', "Access to email"),
        ('phone', "Access to phone"),
        ('alert', "Alert to Business owner has been sent"),
    )

    created = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Created (UTC)',
    )
    business = models.ForeignKey(
        'business.Business',
        related_name='+',
        on_delete=models.CASCADE,
    )
    staffer = models.ForeignKey(
        Resource,
        null=True,
        blank=True,
        related_name='+',
        on_delete=models.CASCADE,
    )
    business_customer_info = models.ForeignKey(
        'business.BusinessCustomerInfo',
        related_name='+',
        on_delete=models.CASCADE,
    )
    field_name = models.CharField(
        null=False,
        blank=False,
        max_length=10,
        choices=FIELDS_NAMES,
    )

    @classmethod
    def log_access(cls, business_customer_info, staffer, field_name):
        bci = business_customer_info

        StafferLockedAccess(
            business=bci.business,
            staffer=staffer,
            business_customer_info=bci,
            field_name=field_name,
        ).save()

        grouped = (
            StafferLockedAccess.objects.filter(
                business=bci.business,
                staffer=staffer,
                created__gte=(tznow() - datetime.timedelta(hours=1)),
            )
            .values('field_name')
            .annotate(Count('field_name'))
        )

        counts = {i['field_name']: i['field_name__count'] for i in grouped}

        # alert has been already sent in last hour
        if counts.get('alert', 0) > 0:
            return counts

        count_hourly = sum(counts.get(i, 0) for i in ['email', 'phone'])
        locked_limit_hourly = bci.business.get_effective_locked_limit_hourly()
        if count_hourly >= locked_limit_hourly:
            StafferLockedAccessScenario.send_alert(
                business=bci.business,
                staffer=staffer,
                access_count=count_hourly,
            )
            StafferLockedAccess(
                business=bci.business,
                staffer=staffer,
                business_customer_info=bci,
                field_name='alert',
            ).save()

        return counts

    @classmethod
    def summary(cls, business_id):
        summary_periods = ['hour', 'day', 'week', 'month']
        summary_sql_part = '''
            SELECT
                '%s' AS period, staffer_id, count(*)
            FROM
                business_stafferlockedaccess
            WHERE
                business_id = %%s AND
                created >= (CURRENT_TIMESTAMP - INTERVAL '1 %s')
            GROUP BY staffer_id
        '''
        summary_sql = '\n\t\tUNION ALL\n'.join(
            summary_sql_part % (period, period) for period in summary_periods
        )

        cursor = get_cursor(READ_ONLY_DB)
        cursor.execute(summary_sql, [business_id] * len(summary_periods))
        records = cursor.fetchall()

        result = {}
        for period, staffer_id, count in records:
            if staffer_id not in result:
                result[staffer_id] = {key: 0 for key in summary_periods}
            result[staffer_id][period] = int(count)

        return result


class AttributeExtractError(BaseException):
    pass


class BusinessPromotion(ArchiveModel):
    class Meta:
        verbose_name = _('Business Promotion (Boost)')
        verbose_name_plural = _('Business Promotions (Boost)')
        constraints = [
            models.UniqueConstraint(
                name='unique_open_business_promotion',
                fields=['business'],
                condition=Q(promotion_end__isnull=True),
            )
        ]

    BY_BUSINESS = 'B'
    BY_ADMIN = 'A'
    BY_SYSTEM = 'P'
    BY_UNKNOWN = 'U'
    SAAS = 'S'
    CARD = 'C'

    PROMOTION_UPDATERS = (
        (BY_BUSINESS, _('Business')),
        (BY_ADMIN, _('Admin')),
        (SAAS, _('SaaS')),
        (CARD, _('Card')),
        (BY_SYSTEM, _('System')),
        (BY_UNKNOWN, _('Unknown')),
    )

    business = models.ForeignKey('business.Business', on_delete=models.CASCADE)
    promotion_start = models.DateTimeField()
    promotion_end = models.DateTimeField(null=True, default=None)
    enabled_by = models.CharField(choices=PROMOTION_UPDATERS, max_length=1, default=BY_UNKNOWN)
    disabled_by = models.CharField(
        choices=PROMOTION_UPDATERS,
        max_length=1,
        null=True,
    )
    type = models.CharField(choices=BUSINESS_PROMOTION_TYPES, max_length=2, default='P')
    max_users = models.IntegerField(default=0)
    end_confirmed = models.BooleanField(default=False)

    commission = models.ForeignKey(
        'marketplace.MarketplaceCommission',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name='business_promotions',
    )
    new_customers_notification_sended = models.BooleanField(default=False)

    def __repr__(self):
        return f'<{self.business.name}: {self.promotion_start} - {self.promotion_end}>'

    @classmethod
    def start_boost(cls, business, dry_run=False):  # pylint: disable=too-many-return-statements
        from webapps.business.models.business_change import BusinessChange
        from webapps.marketplace.models import MarketplaceCommission
        from webapps.marketplace.tasks import make_sure_merchant_can_pay_for_boost_task
        from webapps.user.tools import get_system_user

        commission = MarketplaceCommission.get_commission(business)
        if not commission or not commission.marketplace:
            business.boost_status = Business.BoostStatus.DISABLED
            business.save(update_fields=['boost_status'])
            return False

        last_promo = business.businesspromotion_set.order_by('promotion_end').last()
        enabled_by = BusinessPromotion.cache_promotion_getter(business.id)
        if (
            last_promo
            and enabled_by in (BusinessPromotion.SAAS, BusinessPromotion.CARD)
            and last_promo.disabled_by in (BusinessPromotion.SAAS, BusinessPromotion.CARD)
            and last_promo.disabled_by != enabled_by
        ):
            return False

        if business.status in Business.Status.analytics_never_paid_statuses():
            return False
        if dry_run:
            return True

        if (
            settings.API_COUNTRY == 'pl'
            and business.payment_source == Business.PaymentSource.BRAINTREE_BILLING
            and business.boost_payment_source != BoostPaymentSource.ONLINE
        ):
            metadata = {'function': 'BusinessPromotion.start_boost()'}
            with BusinessChange.recording_with_saving(
                business, metadata=metadata, operator=get_system_user()
            ):
                business.boost_payment_source = BoostPaymentSource.ONLINE

        _promotion, created = BusinessPromotion.objects.get_or_create(
            business=business,
            promotion_end=None,
            defaults={
                'promotion_start': tznow(),
                'commission': commission,
                'enabled_by': enabled_by,
                'type': PAID_PROMO,
            },
        )
        if not created:
            return True

        from webapps.marketplace.models import MarketplacePromotionStatus

        MarketplacePromotionStatus.objects.create(
            status=None,
            business=business,
        )
        business.bump_boost_visibility()

        make_sure_merchant_can_pay_for_boost_task.apply_async(
            args=(business.id,),
            eta=tznow() + datetime.timedelta(seconds=30),
        )

        # profile completeness
        step_attract_using_boost.send(business)

        return True

    def end_boost(self, business, restart=False):
        self.promotion_end = tznow()

        disabled_by = BusinessPromotion.cache_promotion_getter(business.id)
        self.disabled_by = disabled_by
        self.save()

        if restart:
            return

        if not BoostDontDisableServicePromotionsWhenEndingBoost():
            ServicePromotion.disable_business_promotions(business=business)
        business_ended_promotion_event.send(business)

    def restart_boost(self, business):
        self.end_boost(business, restart=True)
        BusinessPromotion.start_boost(business)

    @classmethod
    def update_boost_status(cls, business, boost_now, boost_before):
        from webapps.boost.helpers import cache_boost_change_getter

        active_now = boost_now in Business.BoostStatus.active_statuses()
        active_before = boost_before in Business.BoostStatus.active_statuses()

        if active_now == active_before:
            return
        last_promotion = business.get_promotion()
        if last_promotion and active_before and not active_now:
            last_promotion.end_boost(business)
        elif not active_before and active_now:
            cls.start_boost(business)
        if active_now != active_before:
            analytics_boost_on_off_task.delay(
                context={'business_id': business.id},
            )
            analytics_boost_on_off_gtm_task.delay(
                context={
                    'business_id': business.id,
                    'source_id': cache_boost_change_getter(business.id),
                },
            )
            analytics_task = (
                analytics_boost_on_branchio_task
                if active_now
                else analytics_boost_off_branchio_task
            )
            analytics_task.delay(context={'business_id': business.id})

    @classmethod
    def get_trial_status(cls, business, promotion=None):  # pylint: disable=unused-argument
        return TRIAL_STATUS_NOT_ACTIVE

    @staticmethod
    def cache_promotion_key(business_id):
        return f'businesspromotion::business_{business_id}'

    @classmethod
    def cache_promotion_updater(cls, business_id, changed_by):
        cache.set(
            cls.cache_promotion_key(business_id),
            changed_by,
            timeout=20,
        )

    @classmethod
    def cache_promotion_getter(cls, business_id):
        return cache.get(
            cls.cache_promotion_key(business_id),
            default=BusinessPromotion.BY_UNKNOWN,
        )


class CancellationReason(ArchiveModel):
    class Meta:
        verbose_name = _('Business Cancellation  Reason')
        verbose_name_plural = _('Business Cancellation  Reasons')
        ordering = [
            'cancellation_date',
        ]

    business = models.ForeignKey(
        'business.Business',
        related_name='cancellation_reason',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )

    cancellation_date = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='Cancellation Date (UTC)',
    )

    cancellation_reason = models.CharField(
        choices=sorted(CancellationReasonType.choices(), key=lambda x: x[1]),
        max_length=2,
        verbose_name='Reason of cancel',
        null=True,
        blank=True,
    )
    business_cancellation_reason = models.CharField(
        choices=AutoCancellationReasonType.choices(),
        max_length=2,
        verbose_name='Business reason of cancel',
        null=True,
        blank=True,
    )
    reason_additional_info = models.TextField(
        null=True,
        blank=True,
        max_length=500,
        help_text='Additional reason info provide by business',
    )

    cancellation_info = models.TextField(
        null=True,
        blank=True,
        verbose_name='Cancellation comment',
        help_text='Additional information typed by CS',
    )

    competitor_name = models.CharField(
        max_length=150,
        null=True,
        blank=True,
    )
    operator = models.ForeignKey(
        'user.User',
        on_delete=DO_NOTHING,
        null=True,
        blank=True,
        db_constraint=False,
        help_text="User who requested Churn",
    )

    # Used for delayed churns
    churn_done = models.BooleanField(
        null=True,
    )
    churn_type = models.CharField(
        choices=CancellationType.choices(),
        max_length=1,
        default=CancellationType.AUTOMATIC,
        editable=False,
    )

    payment_source_at_churn = models.CharField(
        choices=Business.PaymentSource.choices(),
        max_length=1,
        null=True,
        editable=False,
    )

    objects = ArchiveManager()

    def __str__(self):
        return f'CancellationReason id={self.id}'

    @property
    def left_for_competitor(self):
        return self.competitor_name is not None


# Only for settings.GDPR_ANNEX_COUNTRIES (PL so far)
def get_default_annex_signed():
    if not settings.GDPR_ANNEX:
        return None

    return settings.GDPR_ANNEX_SIGNED_BY_DEFAULT


class BusinessPolicyAgreement(ArchiveModel, DirtyFieldsMixin):
    # foreign key
    business = models.OneToOneField(
        'business.Business',
        on_delete=models.CASCADE,
        primary_key=True,
        related_name='agreement',
    )
    business_id: int
    # GDPR AGREEMENTS
    privacy_policy_agreement = models.BooleanField(
        default=False,
    )
    marketing_agreement = models.BooleanField(
        default=False,
    )
    partner_marketing_agreement = models.BooleanField(default=False)

    # field is needed only for US, now
    receiving_messages_consent = models.BooleanField(null=True)

    # Additional documents, if needed
    # Only for settings.GDPR_ANNEX_COUNTRIES (PL so far)
    annex_signed = models.BooleanField(
        default=get_default_annex_signed,
        verbose_name=_('GDPR Annex'),
        null=True,
    )
    # Temporary HACK for PL (#52359), to remove another hack... (#45465)
    # For PL this value will be used to decide whether gdpr is enabled
    # or not (instead of country setting) in message blasts
    gdpr_enabled = models.BooleanField(
        default=True,
        editable=False,
        null=True,
    )

    new_terms_flow = models.BooleanField(default=True)
    inspector_first_name = models.CharField(
        max_length=consts.FIRST_NAME_LEN,
        blank=True,
    )
    inspector_last_name = models.CharField(
        max_length=consts.LAST_NAME_LEN,
        blank=True,
    )
    inspector_email = models.EmailField(
        max_length=75,
        blank=True,
    )
    business_name = models.CharField(max_length=250, null=True, blank=True)
    business_address = models.CharField(max_length=100, null=True, blank=True)
    NIP = models.CharField(
        max_length=10,
        blank=True,
        validators=[
            RegexValidator(
                regex='^[0-9]*$',
                message='NIP must consist of only numbers',
            ),
            RegexValidator(
                regex='^.{10}$',
                message='NIP length has to be exactly 10 characters',
            ),
        ],
    )
    # marks business agreed second time after migration from offline
    reagreed_at = models.DateTimeField(verbose_name='Reagreed date (UTC)', null=True, blank=True)
    boost_policy_reagreement = models.DateTimeField(
        verbose_name='Boost terms consent',
        null=True,
        blank=True,
    )

    def __str__(self):
        return f'{self.__class__.__name__} id={self.business_id}'

    def save(self, *args, **kwargs):
        dirty_fields = self.get_dirty_fields()
        created = self._state.adding
        super().save(
            *args,
            **kwargs,
        )

        marketing_agreement = dirty_fields.get('marketing_agreement')
        receiving_messages_consent = dirty_fields.get('receiving_messages_consent')
        if (
            created
            or (marketing_agreement is not None and self.marketing_agreement != marketing_agreement)
            or (
                receiving_messages_consent is not None
                and self.receiving_messages_consent != receiving_messages_consent
            )
        ):
            business_id = self.business.id
            analytics_business_contact_preferences_updated_task.delay(
                business_id=business_id,
                context={
                    'business_id': business_id,
                },
            )
        return bool(created)


class SalonNetwork(ArchiveModel):
    """Model for 'You can also book with us here' functionality."""

    business = models.OneToOneField(
        'business.Business',
        on_delete=models.CASCADE,
        related_name='salon_network',
    )
    members = models.ManyToManyField(
        'business.Business',
        blank=True,
        symmetrical=False,
    )

    @staticmethod
    def post_change_handler(sender, instance, **kwargs):  # pylint: disable=unused-argument
        bump_document(River.BUSINESS, [instance.business_id])

    def __str__(self):
        return f'{self.business}'


post_delete.connect(SalonNetwork.post_change_handler, SalonNetwork)
post_save.connect(SalonNetwork.post_change_handler, SalonNetwork)
m2m_changed.connect(SalonNetwork.post_change_handler, SalonNetwork)


def default_distance():
    return 15 if settings.COUNTRY_DISTANCE_UNIT == 'mi' else 20


class TravelingToClients(ArchiveModel):
    """Business model extension for TtC"""

    business = ArchiveOneToOneField(
        'business.Business',
        on_delete=models.CASCADE,
        related_name='traveling',
    )
    price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    price_type = models.CharField(
        max_length=1,
        null=True,
        blank=True,
        choices=PriceType.choices(),
        default=PriceType.FREE,
    )
    distance = models.PositiveSmallIntegerField(
        default=default_distance,
        validators=[MinValueValidator(limit_value=1)],
    )
    distance_unit = models.CharField(max_length=2, default=settings.COUNTRY_DISTANCE_UNIT)
    policy = models.TextField(blank=True, default='')
    hide_address = models.BooleanField(default=False)
    traveling_only = models.BooleanField(default=False)

    objects = ArchiveManager()
    all_objects = models.Manager()

    def __str__(self):
        return f'business_id={self.business_id}'

    @property
    def traveling_price(self) -> ServicePrice:
        return ServicePrice(value=self.price, price_type=self.price_type)

    def save(
        self, force_insert=False, force_update=False, using=None, update_fields=None
    ):  # pylint: disable=arguments-differ
        if not self.traveling_only and self.hide_address:
            self.hide_address = False
            if update_fields is not None:
                update_fields = set(update_fields)
                if 'hide_address' not in update_fields:
                    update_fields.add('hide_address')
        super().save(
            force_insert=force_insert,
            force_update=force_update,
            using=using,
            update_fields=update_fields,
        )

    @staticmethod
    def post_save_handler(sender, instance, *args, **kwargs):  # pylint: disable=unused-argument
        if instance.deleted is None:
            if instance.traveling_only:
                instance.business.services.filter(
                    Q(is_traveling_service=False) | Q(is_online_service=True)
                ).update(
                    is_traveling_service=True,
                    is_online_service=False,
                )
        else:
            instance.business.services.filter(
                is_traveling_service=True,
            ).update(is_traveling_service=False)


post_save.connect(TravelingToClients.post_save_handler, TravelingToClients)


class ReTrialAttempt(ArchiveModel):
    """Stores information about a new Trial period for Business on TEB/Churned status"""

    business = models.ForeignKey(
        'business.Business',
        on_delete=models.CASCADE,
        related_name='retrial_attempts',
    )
    used = models.DateTimeField(blank=True, null=True, db_index=True)
    operator = models.ForeignKey(
        'user.User',
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
        db_constraint=False,
    )
    switched_from_status = models.CharField(
        max_length=1,
        choices=Business.Status.choices(),
        blank=True,
        null=True,
    )
    historical_sms_data = models.TextField(blank=True, null=True)
    duration = models.IntegerField(
        null=True,
        blank=True,
        validators=[MinValueValidator(1), MaxValueValidator(settings.MAX_TRIAL_EXTENSION_DURATION)],
    )

    objects = ArchiveManager()

    def __str__(self):
        return f'Re-Trial for business_id={self.business_id}'


class VersumToBooksyAgreements(ArchiveModel):
    business = models.OneToOneField(
        'business.Business',
        on_delete=models.CASCADE,
        primary_key=True,
        related_name='versum_agreement',
    )
    transfer_date = models.DateTimeField(null=True, blank=True)
    transfer_agreement = models.BooleanField(null=True)
    user_email = models.EmailField(blank=True)
    demo_agreement = models.BooleanField(null=True)
