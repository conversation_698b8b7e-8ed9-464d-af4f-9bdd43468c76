import pytest
from django.db import connection
from django.test.utils import CaptureQueriesContext
from django.urls import reverse
from model_bakery import baker
from rest_framework import status

from drf_api.lib.base_drf_test_case import BaseBusinessApiTestCase
from lib.feature_flag.feature import ServiceTypeServicesAndCombosBanner
from lib.tests.utils import override_feature_flag
from service.tests import dict_assert
from webapps.business.baker_recipes import (
    appliance_recipe,
    business_recipe,
    service_recipe,
    service_variant_recipe,
    staffer_recipe,
)
from webapps.business.enums import (
    PriceType,
    RateType,
    StaffAccessLevels,
)
from webapps.business.models import (
    Resource,
    Service,
    ServiceCategory,
    ServiceVariant,
    ServiceVariantPayment,
)
from webapps.business.serializers import ServiceSerializerWriter
from webapps.user.baker_recipes import user_recipe
from webapps.warehouse.models import (
    Commodity,
    Warehouse,
    WarehouseFormula,
    WarehouseFormulaRow,
)


# pylint: disable=too-many-instance-attributes
@pytest.mark.django_db
class ServiceCategoriesViewTestCase(BaseBusinessApiTestCase):

    def setUp(self):
        self.user = user_recipe.make()
        self.business = business_recipe.make(owner=self.user)
        super().setUp()
        self.url = reverse('service_categories', args=(self.business.id,))
        self.service_0 = baker.make(Service, business=self.business, order=0)

    def test_get_for_inactive_variant_with_payment(self):
        self.service_category = baker.make(
            ServiceCategory,
            business=self.business,
            name='AAA',
            order=1,
        )

        self.service_1 = baker.make(
            Service,
            business=self.business,
            order=1,
            service_category=self.service_category,
        )

        self.active_service_variant_without_payment = baker.make(
            ServiceVariant,
            service=self.service_1,
            duration='0100',
            active=True,
            price=20,
        )

        self.inactive_service_variant_with_payment = baker.make(
            ServiceVariant,
            service=self.service_1,
            duration='0100',
            active=False,
            price=20,
        )

        baker.make(
            ServiceVariantPayment,
            service_variant=self.inactive_service_variant_with_payment,
            payment_amount=10,
        )

        response = self.client.get(self.url)
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        categories = list(
            filter(
                lambda service_category: service_category.get('id') == self.service_category.id,
                response.json()['service_categories'],
            )
        )
        self.assertEqual(1, len(categories))
        category = categories[0]
        self.assertEqual(1, len(category['services']))

        service = category['services'][0]
        self.assertEqual(self.service_1.id, service['id'])
        self.assertFalse(service['no_show_protection'])

        self.assertEqual(1, len(service['variants']))
        self.assertEqual(
            self.active_service_variant_without_payment.id, service['variants'][0]['id']
        )
        self.assertFalse(service['variants'][0]['no_show_protection'])

    def test_get_staffers_in_resources(self):
        staffer_0 = staffer_recipe.make(business=self.business)
        service_variant_recipe.make(service=self.service_0)
        staffer_0.add_services([self.service_0])

        response = self.client.get(self.url)

        self.assertEqual(status.HTTP_200_OK, response.status_code)

        service = response.json()['service_categories'][0]['services'][0]
        self.assertEqual(1, len(service['resources']))
        self.assertEqual([staffer_0.id], service['resources'])

    def test_unique_staffers_in_resources(self):
        staffer_0 = staffer_recipe.make(business=self.business)
        service_variant_recipe.make(service=self.service_0, _quantity=2)
        staffer_0.add_services([self.service_0])

        response = self.client.get(self.url)

        self.assertEqual(status.HTTP_200_OK, response.status_code)

        service = response.json()['service_categories'][0]['services'][0]
        # two service variants, same staffer
        self.assertEqual(1, len(service['resources']))
        self.assertEqual([staffer_0.id], service['resources'])

    def test_get_appliances_in_resources(self):
        staffer_0 = staffer_recipe.make(business=self.business)
        appliance_0 = appliance_recipe.make(business=self.business)
        service_variant_recipe.make(service=self.service_0)
        staffer_0.add_services([self.service_0])
        appliance_0.add_services([self.service_0])

        response = self.client.get(self.url)

        self.assertEqual(status.HTTP_200_OK, response.status_code)

        service = response.json()['service_categories'][0]['services'][0]
        self.assertEqual(2, len(service['resources']))
        self.assertIn(staffer_0.id, service['resources'])
        self.assertIn(appliance_0.id, service['resources'])

    def test_get(self):
        response = self.client.get(self.url)

        self.assertEqual(status.HTTP_200_OK, response.status_code)
        service_categories = response.json()['service_categories']
        self.assertEqual(1, len(service_categories))
        self.assertEqual('Not categorized', service_categories[0]['name'])
        services = service_categories[0]['services']
        self.assertEqual(1, len(services))
        dict_assert(
            services[0],
            {
                'id': self.service_0.id,
                'name': self.service_0.name,
                'order': self.service_0.order,
                'description': self.service_0.description,
                'padding_type': self.service_0.padding_type,
                'padding_time': 0.0,
                'gap_time': 0.0,
                'resources': [],
                'tax_rate': self.service_0.tax_rate,
                'parallel_clients': self.service_0.parallel_clients,
                'wordcloud': self.service_0.wordcloud,
                'variants': [],
                'questions': [],
                'default_questions': [],
                'is_available_for_customer_booking': (
                    self.service_0.is_available_for_customer_booking
                ),
                'is_online_service': self.service_0.is_online_service,
                'is_traveling_service': self.service_0.is_traveling_service,
                'photos': [],
                'no_show_protection': None,
                'treatment': None,
                'is_treatment_selected_by_user': False,
            },
        )

    def test_get_combos(self):
        self.service_1 = baker.make(Service, name='service_1', business=self.business, order=1)
        self.service_2 = baker.make(Service, name='service_2', business=self.business, order=2)
        service_variant_2 = baker.make(
            ServiceVariant,
            service=self.service_2,
            duration='0100',
            active=True,
            price=20,
            type=PriceType.FIXED,
        )
        service_variant_3 = baker.make(
            ServiceVariant,
            service=self.service_2,
            duration='0100',
            active=True,
            price=30,
            type=PriceType.FIXED,
        )
        baker.make(
            ServiceVariantPayment,
            service_variant=service_variant_2,
            payment_amount=10,
        )

        self.staffer_1 = baker.make(
            Resource,
            type=Resource.STAFF,
            active=True,
            business=self.business,
        )
        self.staffer_2 = baker.make(
            Resource,
            type=Resource.STAFF,
            active=True,
            business=self.business,
        )
        self.staffer_1.add_services([self.service_1.id, self.service_2.id])
        self.staffer_2.add_services([self.service_1.id, self.service_2.id])

        with CaptureQueriesContext(connection) as capture:
            response = self.client.get(self.url)
            self.assertEqual(status.HTTP_200_OK, response.status_code)
            expected_queries_count = len(capture.captured_queries)

        body = {
            'name': 'new combo',
            'description': None,
            'is_available_for_customer_booking': False,
            'variants': [
                {
                    'combo_pricing': 'S',
                    'type': 'X',
                    'price': 35.00,
                    'combo_children': [
                        {
                            'gap_time': 0,
                            'price': '25.00',
                            'type': 'S',
                            'is_traveling_service': False,
                            'service_variant': {
                                'id': service_variant_2.id,
                                'service_name': 'Shave',
                                'service_color': 3,
                                'duration': service_variant_2.duration,
                                'price': service_variant_2.price,
                                'type': 'X',
                            },
                        },
                        {
                            'gap_time': 0,
                            'price': '15.00',
                            'type': 'X',
                            'is_traveling_service': False,
                            'service_variant': {
                                'id': service_variant_3.id,
                                'service_name': 'Beard maintenance',
                                'service_color': 5,
                                'duration': service_variant_3.duration,
                                'price': service_variant_3.price,
                                'type': 'X',
                            },
                        },
                    ],
                }
            ],
            'service_category_id': None,
            'combo_type': 'P',
            'photos': [],
            'service_category': None,
        }
        service_serializer = ServiceSerializerWriter(
            data=body,
            context={
                'user': self.user,
                'superuser': None,
                'pos': self.business.pos,
                'pos_pay_by_app_enabled': self.business.pos_pay_by_app_enabled,
                'business': self.business,
                'use_service_type': False,
            },
        )
        service_serializer.is_valid()
        service_serializer.save()

        with CaptureQueriesContext(connection) as capture:
            response = self.client.get(self.url + "?with_combos=1")
            self.assertEqual(status.HTTP_200_OK, response.status_code)
            combo_parents_count = {
                service_data['id']: service_data['combo_parents']
                for service_category_data in response.json()['service_categories']
                for service_data in service_category_data['services']
            }
            self.assertEqual(0, combo_parents_count[self.service_1.id])
            self.assertEqual(1, combo_parents_count[self.service_2.id])
            self.assertTrue(len(capture.captured_queries) <= expected_queries_count)

    def test_get_multiple(self):
        self.service_1 = baker.make(Service, business=self.business, order=1)
        self.service_2 = baker.make(Service, business=self.business, order=2)
        service_variant_2 = baker.make(
            ServiceVariant,
            service=self.service_2,
            duration='0100',
            active=True,
            price=20,
            type=PriceType.FIXED,
        )
        baker.make(
            ServiceVariantPayment,
            service_variant=service_variant_2,
            saving_type=RateType.AMOUNT,
            payment_amount=10,
        )

        response = self.client.get(self.url)

        self.assertEqual(status.HTTP_200_OK, response.status_code)
        service_categories = response.json()['service_categories']
        self.assertEqual(1, len(service_categories))
        self.assertEqual('Not categorized', service_categories[0]['name'])
        services = service_categories[0]['services']
        self.assertEqual(3, len(services))
        dict_assert(
            services[0],
            {
                'id': self.service_0.id,
                'name': self.service_0.name,
                'order': self.service_0.order,
                'description': self.service_0.description,
                'padding_type': self.service_0.padding_type,
                'padding_time': 0.0,
                'gap_time': 0.0,
                'note_to_customer': None,
                'resources': [],
                'tax_rate': self.service_0.tax_rate,
                'parallel_clients': self.service_0.parallel_clients,
                'wordcloud': self.service_0.wordcloud,
                'variants': [],
                'questions': [],
                'default_questions': [],
                'is_available_for_customer_booking': (
                    self.service_0.is_available_for_customer_booking
                ),
                'is_online_service': self.service_0.is_online_service,
                'is_traveling_service': self.service_0.is_traveling_service,
                'photos': [],
                'no_show_protection': None,
            },
        )
        dict_assert(
            services[2],
            {
                'id': self.service_2.id,
                'name': self.service_2.name,
                'order': self.service_2.order,
                'description': self.service_2.description,
                'padding_type': self.service_2.padding_type,
                'padding_time': 0.0,
                'gap_time': 0.0,
                'note_to_customer': None,
                'resources': [],
                'tax_rate': self.service_2.tax_rate,
                'parallel_clients': self.service_2.parallel_clients,
                'wordcloud': self.service_2.wordcloud,
                'variants': [
                    {
                        'id': service_variant_2.id,
                        'type': service_variant_2.type,
                        'price': '20.00',
                        'duration': 1.0,
                        'time_slot_interval': 15.0,
                        'gap_hole_start_after': 0.0,
                        'gap_hole_duration': 0.0,
                        'no_show_protection': {
                            'type': service_variant_2.payment.payment_type,
                            'payment_amount': 10.0,
                        },
                        'formula': [],
                        'label': None,
                    }
                ],
                'questions': [],
                'default_questions': [],
                'is_available_for_customer_booking': (
                    self.service_2.is_available_for_customer_booking
                ),
                'is_online_service': self.service_2.is_online_service,
                'is_traveling_service': self.service_2.is_traveling_service,
                'photos': [],
                'no_show_protection': {
                    'type': service_variant_2.payment.payment_type,
                    'payment_amount': 10.0,
                },
            },
        )

    # pylint: disable=too-many-statements
    @pytest.mark.random_failure  # https://booksy.atlassian.net/browse/PY-1561
    def test_get_multiple_with_categories(self):
        service_variant_no_category_0 = baker.make(
            ServiceVariant,
            service=self.service_0,
            duration='0100',
            active=True,
            price=20,
        )
        self._create_formula_for_service_variant(service_variant_no_category_0)
        service_variant_no_category_1 = baker.make(
            ServiceVariant,
            service=self.service_0,
            duration='0200',
            active=True,
            price=30,
        )
        service_variant_no_category_2 = baker.make(
            ServiceVariant,
            service=self.service_0,
            duration='0200',
            active=True,
            price=30,
        )
        no_category_formula = self._create_formula_for_service_variant(
            service_variant_no_category_1,
        )
        no_category_formula.service_variants.add(service_variant_no_category_2)

        self.category_0 = baker.make(
            ServiceCategory,
            business=self.business,
            name='AAA',
            order=1,
        )
        self.category_1 = baker.make(
            ServiceCategory,
            business=self.business,
            name='BBB',
            order=2,
        )
        self.service_1 = baker.make(
            Service,
            business=self.business,
            order=1,
            service_category=self.category_0,
        )
        self.service_2 = baker.make(
            Service,
            business=self.business,
            order=2,
            service_category=self.category_0,
        )
        self.service_3 = baker.make(
            Service,
            business=self.business,
            order=2,
            service_category=self.category_1,
        )
        service_variant_2_a = baker.make(
            ServiceVariant,
            service=self.service_2,
            duration='0100',
            active=True,
            price=20,
        )
        formula = self._create_formula_for_service_variant(service_variant_2_a)
        baker.make(
            ServiceVariantPayment,
            service_variant=service_variant_2_a,
            payment_amount=10,
        )
        service_variant_2_b = baker.make(
            ServiceVariant,
            service=self.service_2,
            duration='0200',
            active=True,
            price=40,
        )
        formula.service_variants.add(service_variant_2_b)
        baker.make(
            ServiceVariantPayment,
            service_variant=service_variant_2_b,
            payment_amount=10,
        )
        baker.make(  # no payment
            ServiceVariant,
            service=self.service_2,
            duration='0500',
            active=True,
            price=0,
        )
        service_variant_3_a = baker.make(
            ServiceVariant,
            service=self.service_3,
            duration='0100',
            active=True,
            price=20,
        )
        self._create_formula_for_service_variant(service_variant_3_a)
        baker.make(
            ServiceVariantPayment,
            service_variant=service_variant_3_a,
            payment_amount=10,
        )
        service_variant_3_b = baker.make(
            ServiceVariant,
            service=self.service_3,
            duration='0200',
            active=True,
            price=40,
        )
        baker.make(
            ServiceVariantPayment,
            service_variant=service_variant_3_b,
            payment_amount=10,
        )

        response = self.client.get(self.url)

        self.assertEqual(status.HTTP_200_OK, response.status_code)
        service_categories = response.json()['service_categories']
        self.assertEqual(3, len(service_categories))
        self.assertEqual('Not categorized', service_categories[0]['name'])
        services_not_categorized = service_categories[0]['services']
        self.assertEqual(1, len(services_not_categorized))
        self.assertEqual(
            services_not_categorized[0],
            {
                'id': self.service_0.id,
                'name': self.service_0.name,
                'order': self.service_0.order,
                'description': self.service_0.description,
                'padding_type': self.service_0.padding_type,
                'padding_time': 0.0,
                'gap_time': 0.0,
                'note_to_customer': None,
                'resources': [],
                'tax_rate': self.service_0.tax_rate,
                'parallel_clients': self.service_0.parallel_clients,
                'wordcloud': self.service_0.wordcloud,
                'questions': [],
                'default_questions': [],
                'is_available_for_customer_booking': (
                    self.service_0.is_available_for_customer_booking
                ),
                'is_online_service': self.service_0.is_online_service,
                'is_traveling_service': self.service_0.is_traveling_service,
                'photos': [],
                'no_show_protection': None,
            },
        )
        variants_not_categorized = services_not_categorized[0]['variants']
        self.assertEqual(3, len(variants_not_categorized))
        formula_0 = variants_not_categorized[0]['formula']
        self.assertEqual(2, len(formula_0))  # number of rows
        rows_keys = {
            'id',
            'commodity',
            'count',
            'warehouse',
        }
        self.assertTrue(set(formula_0[0].keys()).issuperset(rows_keys))
        self.assertTrue(set(formula_0[1].keys()).issuperset(rows_keys))
        formula_1 = variants_not_categorized[1]['formula']
        formula_2 = variants_not_categorized[2]['formula']
        self.assertNotEqual(formula_0, formula_1)
        self.assertEqual(formula_1, formula_2)
        category_0 = service_categories[1]
        self.assertEqual(category_0['name'], self.category_0.name)
        category_services_0 = category_0['services']
        self.assertEqual(2, len(category_services_0))
        self.assertFalse(category_services_0[0]['no_show_protection'])
        self.assertTrue(category_services_0[1]['no_show_protection'])
        self.assertEqual(2, len(category_services_0[1]['variants'][0]['formula']))
        self.assertEqual(2, len(category_services_0[1]['variants'][1]['formula']))

        category_1 = service_categories[2]
        self.assertEqual(self.category_1.name, category_1['name'])
        category_services_1 = category_1['services']
        self.assertEqual(1, len(category_services_1))

    def _create_formula_for_service_variant(self, service_variant):
        if not getattr(self, 'warehouse', None):
            self.warehouse = baker.make(Warehouse, business=self.business)
        formula = baker.make(
            WarehouseFormula,
            service_variants=[service_variant],
            rows=[
                baker.make(
                    WarehouseFormulaRow,
                    warehouse=self.warehouse,
                    commodity=baker.make(
                        Commodity,
                        business=self.business,
                        total_pack_capacity=30,
                    ),
                    count=3,
                ),
                baker.make(
                    WarehouseFormulaRow,
                    warehouse=self.warehouse,
                    commodity=baker.make(Commodity),
                    count=8,
                ),
            ],
        )
        return formula

    def test_get_services_for_basic_staffer(self):
        another_business = business_recipe.make()
        staffer = staffer_recipe.make(
            business=another_business,
            staff_access_level=StaffAccessLevels.STAFF,
            staff_user=self.user,
        )
        url = reverse('service_categories', args=(another_business.id,))
        service_1 = baker.make(Service, business=another_business)
        service_2 = baker.make(Service, business=another_business)
        service_variant_recipe.make(service=service_1)
        service_variant_recipe.make(service=service_2)
        baker.make(Service, business=another_business)
        staffer.add_services([service_1.id])

        response = self.client.get(url)

        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertEqual(1, len(response.json()['service_categories'][0]['services']))

    @override_feature_flag({ServiceTypeServicesAndCombosBanner.flag_name: True})
    def test_service_type_marketing_data(self):
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()['service_type_marketing']
        self.assertDictEqual(
            data,
            {
                'show_banner': True,
                'assigned_services_count': 0,
                'total_services_count': 1,
            },
        )

    @override_feature_flag({ServiceTypeServicesAndCombosBanner.flag_name: True})
    def test_service_type_marketing_data_owner_is_also_a_staffer_in_another_business(self):
        another_business = business_recipe.make()
        service_recipe.make(business=another_business, is_treatment_selected_by_user=False)
        staffer_recipe.make(
            business=another_business,
            staff_access_level=StaffAccessLevels.STAFF,
            staff_user=self.user,
        )
        url = reverse('service_categories', args=(another_business.id,))
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        data = response.json()['service_type_marketing']
        self.assertDictEqual(
            data,
            {
                'show_banner': False,
                'assigned_services_count': 0,
                'total_services_count': 1,
            },
        )
