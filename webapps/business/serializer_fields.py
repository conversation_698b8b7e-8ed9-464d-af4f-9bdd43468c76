from decimal import Decimal, InvalidOperation

from django.conf import settings
from django.utils.translation import gettext as _
from rest_framework import serializers
from rest_framework.fields import empty

from webapps.business.models import ServiceVariantPayment
from webapps.business.enums import (
    NoShowProtectionType,
    PriceType,
    RateType,
)

from lib.tools import format_currency


def convert_to_percent(price, payment_amount):
    percent = None
    payment_amount = payment_amount if payment_amount else 0
    if price:
        percent = payment_amount / price
    # convert to float to make it serializable
    return int(round(float(percent * 100), 2)) if percent else None


def validate_and_set_no_show_percent(no_show_protection, price, name=None, validate_only=False):
    """
    Validate no show protection fee of service_variant
    :param no_show_protection: dict. with keys 'payment_type' and 'percentage'
    :param price: Decimal. price of ServiceVariant.
    :param name: name of service. optional
    :param validate_only: use for validation only,
            without no_show_protection modification
    :return: None. will add  key payment_amount to no_show_protection dict
            if all data is valid
    """
    from webapps.pos.calculations import round_currency

    # validate no_show_protection
    hundred_percent = Decimal(100)
    if validate_only:
        percentage = no_show_protection.get('percentage')
    else:
        # delete percentage
        percentage = no_show_protection.pop('percentage', None)

    if price is None and name is None:
        raise serializers.ValidationError(
            _('Protection Fee can\'t be set,' 'because service do not have price')
        )

    if price is None:
        raise serializers.ValidationError(_('Service \'{}\' don\'t have a price').format(name))

    amount = round_currency(price * (percentage / hundred_percent))
    # check amount is equal or higher that minimal payment
    validate_no_show_amount(amount, price)

    if validate_only:
        return
    # save amount for future usage
    no_show_protection['payment_amount'] = amount


def validate_no_show_amount(amount, price=None):
    """
    Validate no show protection fee of service_variant amount.

    Will raise ValidationError if
        1) amount is less then minimal_payment
        for country (see settings.MINIMAL_POS_PAY_BY_APP_PAYMENT)
        2) If price is supplied. amount greater then price

    :param amount: Decimal. Amount of protection fee
    :param price: Decimal. Optional argument for service_variant with price
    :return: None
    """
    from webapps.pos.calculations import round_currency

    if amount is None:
        raise serializers.ValidationError(
            _("Can't set no show protection for variants without price"),
            code='user_decision',
        )
    minimal_payment_country = settings.MINIMAL_POS_PAY_BY_APP_PAYMENT
    minimal_payment = minimal_payment_country.get(
        settings.API_COUNTRY, minimal_payment_country.get('default')
    )
    hundred_percent = Decimal(100)

    if round_currency(amount) != amount:
        raise serializers.ValidationError(_('Payment does not meet the rounding mode'))

    if minimal_payment and amount < Decimal(minimal_payment):
        msg = _('Minimal payment amount can\'t be less than {}').format(
            format_currency(minimal_payment),
        )
        raise serializers.ValidationError(msg, 'minimal_payment_amount')

    percentage = (amount / price) * hundred_percent if price else None
    if percentage and percentage > hundred_percent:
        raise serializers.ValidationError(
            _('Percentage of no show fee can\'t be greater than 100%.')
        )


def validate_and_convert_percent_to_amount(no_show_protection, price, validate_only=False):
    if no_show_protection is None:
        return
    if validate_only and no_show_protection.get('skip_variant_if_no_price') and not price:
        return
    # additional validation to convert percentage to amount
    if price is not None:
        # service with price
        percentage = no_show_protection.get('percentage')
        payment_amount = no_show_protection.get('payment_amount')
        if percentage:
            validate_and_set_no_show_percent(
                no_show_protection,
                price,
                validate_only=validate_only,
            )
        elif payment_amount:
            validate_no_show_amount(payment_amount)
        # case when two percentage and payment_amount
        # validated in NoShowProtectionServiceVariantField
    else:
        # service without price validate amount
        # is not less of minimal payment
        payment_amount = no_show_protection.get('payment_amount')
        validate_no_show_amount(payment_amount)


class NoShowProtectionBaseField(serializers.Field):
    payment_type = serializers.ChoiceField(
        choices=NoShowProtectionType.values(),
        source='type',
    )

    def to_representation(self, instance):  # pylint: disable=arguments-renamed
        if instance.deleted is not None:
            return
        return {
            'type': instance.payment_type,
        }

    def to_internal_value(self, data):
        payment_type = data.pop('type', None) or data.pop('payment_type')
        if payment_type is None:
            raise serializers.ValidationError(_('Type of cancellation fee field is required'))
        if payment_type not in NoShowProtectionType.values():
            raise serializers.ValidationError(
                _('Invalid no show protection type {}').format(payment_type)
            )

        if 'percentage' in data:
            data['saving_type'] = RateType.PERCENTAGE.value
        else:
            data['saving_type'] = RateType.AMOUNT.value
        # convert to keys compatible with ServiceVariantPayment
        data['payment_type'] = payment_type
        return data


class NoShowProtectionServiceVariantField(NoShowProtectionBaseField):
    """
    Two fields are required to to_internal_value
        1) type
        2) percentage in case of service with price
        or
        3) payment_amount in case of service without price
    The same for to_internal_value:
    """

    def to_representation(self, instance):
        data = super().to_representation(instance)
        if data:
            if (
                instance.saving_type == RateType.PERCENTAGE.value
                and instance.service_variant.type in PriceType.has_price()
            ):
                price = None
                prices_per_sv = self.context.get('service_prices')
                if prices_per_sv:
                    price, _name = prices_per_sv.get(instance.service_variant_id)
                if price is None:
                    price = instance.service_variant.price
                data['percentage'] = convert_to_percent(price, instance.payment_amount)
            else:
                data['payment_amount'] = float(instance.payment_amount)

        return data

    def to_internal_value(self, attrs):  # pylint: disable=arguments-renamed
        data = super().to_internal_value(attrs)
        percentage = data.get('percentage')
        payment_amount = data.get('payment_amount')
        if payment_amount and percentage:
            raise serializers.ValidationError(
                _('Only one field should be used Percentage/ Payment_amount')
            )
        if percentage is not None:
            try:
                percentage_value = int(percentage)
                if not 1 <= percentage_value <= 100:
                    raise ValueError
            except ValueError as value_error:
                raise serializers.ValidationError(
                    _('Percentage must be integer of value in range 1-100.')
                ) from value_error

            try:
                data['percentage'] = Decimal(str(percentage))
            except InvalidOperation as invalid_operation:
                raise serializers.ValidationError(
                    _('Invalid payment percentage {}').format(percentage)
                ) from invalid_operation
        elif payment_amount is not None:
            try:
                data['payment_amount'] = Decimal(str(payment_amount))
            except InvalidOperation as invalid_operation:
                raise serializers.ValidationError(
                    _('Invalid payment amount {}').format(payment_amount)
                ) from invalid_operation
        else:
            raise serializers.ValidationError(
                _('Percentage field or payment amount is required'),
                'payment_amount_or_percentage_required',
            )
        return data


class NoShowProtectionServiceField(NoShowProtectionServiceVariantField):

    def to_representation(self, instance):
        payment = self._get_service_payment_obj(instance)
        if not payment:
            return None

        return super().to_representation(payment)

    def validate_empty_values(self, no_show_protection):  # pylint: disable=arguments-renamed
        from webapps.business.serializers import (
            NoShowProtectionMixin,
        )  # pylint: disable=cyclic-import

        service_instance = self.parent.instance
        if not service_instance or service_instance.is_combo:
            return super().validate_empty_values(no_show_protection)

        request_variants = self.parent.initial_data['variants']
        if not no_show_protection or no_show_protection is empty:
            no_show_protection, _ = NoShowProtectionMixin.get_service_no_show_protection(
                request_variants,
                service_instance.service_variants.filter(
                    deleted__isnull=True,
                    active=True,
                ),
            )
        lowest_price = self.get_lowest_service_variant_price(request_variants, service_instance)
        if lowest_price is not None and no_show_protection:
            # for validation purposes
            validate_and_convert_percent_to_amount(
                dict(no_show_protection), lowest_price, validate_only=True
            )
        return super().validate_empty_values(no_show_protection)

    @staticmethod
    def get_lowest_service_variant_price(request_variants, service):
        if service.is_combo:
            request_variants = (
                child['service_variant'] for child in request_variants[0].get('combo_children', [])
            )

        price = None
        for variant in request_variants:
            if variant.get('type') not in PriceType.has_price():
                price = None
                continue
            try:
                new_price = Decimal(variant.get('price'))
            except (ValueError, InvalidOperation, TypeError):
                continue
            if not price or price > new_price:
                price = new_price
        return price

    def _get_service_payment_obj(self, service_instance):
        if self.context.get('category_payments_objs_cache'):
            payment = self.context['category_payments_objs_cache'].get(service_instance.id)
            return payment
        if 'prefetched_service_payments' in self.context:
            payment = self.context['prefetched_service_payments'].get(service_instance.id)
            return payment
        # fallback if we are not nested field of ServiceCategorySerializer
        try:
            service_no_show_protection_id = getattr(
                service_instance, 'service_no_show_protection_id'
            )
        except AttributeError:
            payment = (
                ServiceVariantPayment.objects.filter(
                    service_variant__deleted__isnull=True,
                    service_variant__active=True,
                    service_variant__price__isnull=False,
                    service_variant__service=service_instance,
                )
                .select_related('service_variant')
                .first()
            )
        else:
            if not service_no_show_protection_id:
                return None
            payment = (
                ServiceVariantPayment.objects.filter(
                    service_variant_id=service_no_show_protection_id,
                )
                .select_related('service_variant')
                .first()
            )
        return payment
