import pytest
from django.urls import reverse
from model_bakery import baker
from rest_framework import status

from drf_api.lib.base_drf_test_case import BusinessOwnerAPITestCase
from lib.baker_utils import get_or_create_booking_source
from lib.feature_flag.feature import ServiceTypeAlertGenerationNumberFlag
from lib.tests.utils import override_feature_flag
from webapps.booking.models import BookingSources
from webapps.business.baker_recipes import business_recipe
from webapps.business.enums import ServiceTypeMarketingKind
from webapps.business.models import BusinessUserInternalData
from webapps.user.models import UserInternalData


@pytest.mark.django_db
class ServiceTypeMarketingTestCase(BusinessOwnerAPITestCase):
    @classmethod
    def setUpTestData(cls):
        cls.business = business_recipe.make()
        cls.user = cls.business.owner
        cls.biz_booking_src = get_or_create_booking_source(
            app_type=BookingSources.BUSINESS_APP,
            api_key='biz_key',
        )

        super().setUpTestData()

    def test_post_intro(self):
        user_internal_data = baker.make(
            UserInternalData,
            user=self.user,
        )
        url = reverse(
            'service_type_marketing_seen',
            kwargs={
                'business_pk': self.business.id,
                'kind': ServiceTypeMarketingKind.INTRO,
            },
        )

        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        user_internal_data.refresh_from_db()
        self.assertFalse(user_internal_data.show_service_type_intro_screen)

    def test_post_intro_user_internal_data_dont_exist(self):
        url = reverse(
            'service_type_marketing_seen',
            kwargs={
                'business_pk': self.business.id,
                'kind': ServiceTypeMarketingKind.INTRO,
            },
        )

        self.assertFalse(UserInternalData.objects.exists())

        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        user_internal_data = UserInternalData.objects.all().first()
        self.assertIsNotNone(user_internal_data)
        self.assertEqual(user_internal_data.user, self.user)
        self.assertFalse(user_internal_data.show_service_type_intro_screen)

    @override_feature_flag({ServiceTypeAlertGenerationNumberFlag.flag_name: 1})
    def test_post_alert(self):
        business_user_internal_data = baker.make(
            BusinessUserInternalData,
            user=self.user,
            business=self.business,
        )
        url = reverse(
            'service_type_marketing_seen',
            kwargs={
                'business_pk': self.business.id,
                'kind': ServiceTypeMarketingKind.ALERT,
            },
        )

        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        business_user_internal_data.refresh_from_db()
        self.assertEqual(
            business_user_internal_data.last_seen_service_type_alert_generation,
            ServiceTypeAlertGenerationNumberFlag(),
        )

    @override_feature_flag({ServiceTypeAlertGenerationNumberFlag.flag_name: 1})
    def test_post_alert_business_user_internal_data_dont_exist(self):
        url = reverse(
            'service_type_marketing_seen',
            kwargs={
                'business_pk': self.business.id,
                'kind': ServiceTypeMarketingKind.ALERT,
            },
        )

        self.assertFalse(BusinessUserInternalData.objects.exists())

        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        business_user_internal_data = BusinessUserInternalData.objects.all().first()
        self.assertIsNotNone(business_user_internal_data)
        self.assertEqual(business_user_internal_data.user, self.user)
        self.assertEqual(business_user_internal_data.business, self.business)
        self.assertEqual(
            business_user_internal_data.last_seen_service_type_alert_generation,
            ServiceTypeAlertGenerationNumberFlag(),
        )

    def test_invalid_kind_parameter(self):
        url = reverse(
            'service_type_marketing_seen',
            kwargs={
                'business_pk': self.business.id,
                'kind': 'Invalid',
            },
        )
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
