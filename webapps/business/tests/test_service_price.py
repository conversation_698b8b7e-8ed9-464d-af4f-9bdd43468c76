from decimal import Decimal

import pytest

from webapps.business.enums import PriceType
from webapps.business.service_price import ServicePrice


@pytest.mark.parametrize(
    'service_price, expected_value',
    [
        (ServicePrice(None, PriceType.FIXED), Decimal(0)),
        (ServicePrice(None, PriceType.STARTS_AT), Decimal(0)),
        (ServicePrice(None, PriceType.FREE), None),
        (ServicePrice(None, PriceType.DONT_SHOW), None),
        (ServicePrice(None, PriceType.VARIES), None),
        (ServicePrice(0, PriceType.FIXED), Decimal(0)),
        (ServicePrice(0, PriceType.STARTS_AT), Decimal(0)),
        (ServicePrice(0, PriceType.FREE), None),
        (ServicePrice(0, PriceType.DONT_SHOW), None),
        (ServicePrice(0, PriceType.VARIES), None),
    ],
)
def test_service_price_value(service_price, expected_value):
    assert service_price.value == expected_value


@pytest.mark.parametrize(
    'first, second, expected',
    [
        (
            ServicePrice(10, PriceType.FIXED),
            ServicePrice(10, PriceType.FIXED),
            ServicePrice(20, PriceType.FIXED),
        ),
        (
            ServicePrice(price_type=PriceType.FREE),
            ServicePrice(price_type=PriceType.FREE),
            ServicePrice(price_type=PriceType.FREE),
        ),
        (
            ServicePrice(price_type=PriceType.VARIES),
            ServicePrice(price_type=PriceType.VARIES),
            ServicePrice(price_type=PriceType.VARIES),
        ),
        (
            ServicePrice(price_type=PriceType.DONT_SHOW),
            ServicePrice(price_type=PriceType.DONT_SHOW),
            ServicePrice(price_type=PriceType.DONT_SHOW),
        ),
        (
            ServicePrice(10, PriceType.FIXED),
            ServicePrice(20, PriceType.FIXED),
            ServicePrice(30, PriceType.FIXED),
        ),
        (
            ServicePrice(10, PriceType.STARTS_AT),
            ServicePrice(20, PriceType.STARTS_AT),
            ServicePrice(30, PriceType.STARTS_AT),
        ),
        (
            ServicePrice(10, PriceType.FIXED),
            ServicePrice(20, PriceType.STARTS_AT),
            ServicePrice(30, PriceType.STARTS_AT),
        ),
        (
            ServicePrice(10, PriceType.FIXED),
            ServicePrice(price_type=PriceType.FREE),
            ServicePrice(10, PriceType.FIXED),
        ),
        (
            ServicePrice(10, PriceType.STARTS_AT),
            ServicePrice(price_type=PriceType.FREE),
            ServicePrice(10, PriceType.STARTS_AT),
        ),
        (
            ServicePrice(10, PriceType.FIXED),
            ServicePrice(price_type=PriceType.VARIES),
            ServicePrice(price_type=PriceType.VARIES),
        ),
        (
            ServicePrice(10, PriceType.FIXED),
            ServicePrice(price_type=PriceType.DONT_SHOW),
            ServicePrice(price_type=PriceType.VARIES),
        ),
        (
            ServicePrice(10, PriceType.STARTS_AT),
            ServicePrice(price_type=PriceType.DONT_SHOW),
            ServicePrice(price_type=PriceType.VARIES),
        ),
    ],
)
def test_add_service_prices(first, second, expected):
    """
    Check addition of prices.
    """
    result = first + second
    assert result == expected

    result = second + first
    assert result == expected


@pytest.mark.parametrize(
    'first, second, expected',
    [
        (
            ServicePrice(10, PriceType.FIXED),
            ServicePrice(10, PriceType.FIXED),
            ServicePrice(10, PriceType.FIXED),
        ),
        (
            ServicePrice(10, PriceType.STARTS_AT),
            ServicePrice(10, PriceType.STARTS_AT),
            ServicePrice(10, PriceType.STARTS_AT),
        ),
        (
            ServicePrice(price_type=PriceType.FREE),
            ServicePrice(price_type=PriceType.FREE),
            ServicePrice(price_type=PriceType.FREE),
        ),
        (
            ServicePrice(price_type=PriceType.VARIES),
            ServicePrice(price_type=PriceType.VARIES),
            ServicePrice(price_type=PriceType.VARIES),
        ),
        (
            ServicePrice(price_type=PriceType.DONT_SHOW),
            ServicePrice(price_type=PriceType.DONT_SHOW),
            ServicePrice(price_type=PriceType.DONT_SHOW),
        ),
        (
            ServicePrice(10, PriceType.FIXED),
            ServicePrice(20, PriceType.FIXED),
            ServicePrice(10, PriceType.STARTS_AT),
        ),
        (
            ServicePrice(10, PriceType.STARTS_AT),
            ServicePrice(20, PriceType.STARTS_AT),
            ServicePrice(10, PriceType.STARTS_AT),
        ),
        (
            ServicePrice(10, PriceType.FIXED),
            ServicePrice(20, PriceType.STARTS_AT),
            ServicePrice(10, PriceType.STARTS_AT),
        ),
        (
            ServicePrice(10, PriceType.FIXED),
            ServicePrice(price_type=PriceType.FREE),
            ServicePrice(0, PriceType.STARTS_AT),
        ),
        (
            ServicePrice(10, PriceType.STARTS_AT),
            ServicePrice(price_type=PriceType.FREE),
            ServicePrice(0, PriceType.STARTS_AT),
        ),
        (
            ServicePrice(10, PriceType.FIXED),
            ServicePrice(price_type=PriceType.VARIES),
            ServicePrice(price_type=PriceType.VARIES),
        ),
        (
            ServicePrice(10, PriceType.FIXED),
            ServicePrice(price_type=PriceType.DONT_SHOW),
            ServicePrice(price_type=PriceType.VARIES),
        ),
    ],
)
def test_or_service_prices(first, second, expected):
    """
    Check merging of prices. By merging we mean finding a common price that approximates
    both inputs.
    """
    result = first | second
    assert result == expected

    result = second | first
    assert result == expected


@pytest.mark.parametrize(
    'service_price, expected',
    [
        (ServicePrice(10, PriceType.FIXED), '$10.00'),
        (ServicePrice(10, PriceType.STARTS_AT), '$10.00+'),
        (ServicePrice(price_type=PriceType.FREE), 'Free'),
        (ServicePrice(price_type=PriceType.VARIES), 'Varies'),
        (ServicePrice(price_type=PriceType.DONT_SHOW), '-'),
    ],
)
def test_service_price_format_for_customer(service_price, expected):
    assert service_price.format_for_customer() == expected
