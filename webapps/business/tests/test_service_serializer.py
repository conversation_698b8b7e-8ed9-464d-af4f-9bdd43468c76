import copy
import unittest
from decimal import Decimal
from unittest.mock import patch

import pytest
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.utils.translation import gettext as _
from model_bakery import baker
from rest_framework.exceptions import ErrorDetail

from lib.feature_flag.feature.voucher import BlockPPForServicesWithActivePackageFlag
from lib.tests.utils import override_feature_flag
from lib.tools import tznow
from webapps.business.baker_recipes import (
    business_recipe,
    service_category_recipe,
    service_recipe,
    service_variant_recipe,
    staffer_recipe,
    traveling_to_client_recipe,
    treatment_recipe,
    appliance_recipe,
)
from webapps.business.enums import (
    ComboPricing,
    ComboType,
    NoShowProtectionType,
    PriceType,
    RateType,
)
from webapps.business.models import (
    Business,
    Resource,
    Service,
    ServiceVariant,
    ServiceVariantChangelog,
)
from webapps.business.models.category import BusinessCategory
from webapps.business.models.service import ComboMembership
from webapps.business.models.service import ServiceVariantPayment
from webapps.business.serializers import (
    ServiceSerializer,
    ServiceSerializerWriter,
    ServiceVariantSerializerWriter,
)
from webapps.business.service_price import ServicePrice
from webapps.experiment_v3.management.commands.migrate import (
    initialize_experiments,
)
from webapps.pos.baker_recipes import pos_recipe
from webapps.pos.models import (
    CommissionDefaults,
    CommissionRate,
    POS,
    TaxRate,
)
from webapps.user.models import User
from webapps.voucher.models import VoucherTemplate, Voucher, VoucherTemplateServiceVariant


@pytest.mark.django_db
class TestRegisterSerializers(unittest.TestCase):
    def setUp(self):
        """Set up an business ready for appointments."""

        initialize_experiments()

        self.user = baker.make(User)
        self.access_level = Resource.STAFF_ACCESS_LEVEL_OWNER

        self.business = baker.make(
            Business,
            owner=self.user,
        )
        self.staffer = baker.make(
            Resource,
            business=self.business,
            staff_user=self.business.owner,
            active=True,
            visible=True,
            type=Resource.STAFF,
        )

        self.service_variant_data = {
            "duration": 30,
            "type": "X",
            "price": 99,
            "time_slot_interval": 15,
            "gap_hole_duration": 10,
            "gap_hole_start_after": 10,
            "label": "simple label",
        }

        self.service_data = {
            "name": "test kolor 18",
            "color": 18,
            "gap_time": 0,
            "parallel_clients": 1,
            "note_to_customer": "Test Note",
            "variants": [self.service_variant_data],
            "resources": [self.staffer.id],
            "padding_time": 30,
            "padding_type": 'C',
        }

        self.service_serializer_required_fields = ['name', 'gap_time', 'resources', 'variants']

        self.service_serializer_no_required_fields = [
            'color',
            'parallel_clients',
            'note_to_customer',
            'padding_time',
        ]

        self.service_variant_serializer_required_fields = [
            'duration',
            'type',
        ]

        self.service_variant_serializer_no_required_fields = [
            'time_slot_interval',
            'gap_hole_duration',
            'gap_hole_start_after',
            'deposit',
            'label',
        ]

    def make_service_serializer(self, data, instance=None):
        serializer = ServiceSerializerWriter(
            data=data,
            instance=instance,
            context={
                'user': self.business.owner,
                'pos': self.business.pos,
                'pos_pay_by_app_enabled': self.business.pos_pay_by_app_enabled,
                'business': self.business,
            },
        )
        return serializer

    def make_sv_serializer(self, data, instance=None):
        serializer = ServiceVariantSerializerWriter(
            data=data,
            instance=instance,
            context={
                'pos': self.business.pos,
                'pos_pay_by_app_enabled': self.business.pos_pay_by_app_enabled,
                'business': self.business,
            },
        )
        return serializer

    def test_service_serializer(self):
        data = copy.deepcopy(self.service_data)
        serializer = self.make_service_serializer(data)
        serializer.is_valid()
        assert not serializer.errors
        assert serializer.validated_data

        instance = serializer.save()

        assert instance.name == data['name']
        assert instance.color == data['color']
        assert instance.padding_time.minutes == data['padding_time']
        assert instance.padding_type == data['padding_type']
        assert len(instance.active_resources) == len(self.service_data['resources'])
        assert len(instance.service_variants.all()) == len(self.service_data['variants'])
        data['color'] = 100
        serializer = self.make_service_serializer(data)
        serializer.is_valid()
        assert serializer.errors.get('color', None) is not None
        assert serializer.validated_data == {}

        data = copy.deepcopy(self.service_data)
        instance.gap_time = relativedelta(minutes=10)
        instance.padding_time = relativedelta(minutes=5)
        instance.padding_type = 'A'
        instance.save()

        del data['gap_time']
        del data['padding_type']
        del data['padding_time']

        serializer = self.make_service_serializer(data, instance)
        serializer.is_valid()

        assert serializer.validated_data['gap_time'].minutes == 10
        assert serializer.validated_data['padding_type'] == 'A'
        assert serializer.validated_data['padding_time'].minutes == 5

    def test_create_with_default_tax_rate(self):
        pos = pos_recipe.make(business=self.business)
        default_tax_rate = baker.make(
            TaxRate,
            pos=pos,
            default_for_service=True,
            rate=Decimal(99),
        )

        data = copy.deepcopy(self.service_data)
        serializer = self.make_service_serializer(data)
        self.assertTrue(serializer.is_valid())
        self.assertEqual(serializer.validated_data['tax_rate'], default_tax_rate.rate)

        data = copy.deepcopy(self.service_data)
        data['tax_rate'] = None
        serializer = self.make_service_serializer(data)
        self.assertTrue(serializer.is_valid())
        self.assertIsNone(serializer.validated_data['tax_rate'])

        data = copy.deepcopy(self.service_data)
        data['tax_rate'] = '0.00'
        serializer = self.make_service_serializer(data)
        self.assertTrue(serializer.is_valid())
        self.assertEqual(serializer.validated_data['tax_rate'], Decimal(0))

    def test_update_tax_rate(self):
        pos = pos_recipe.make(business=self.business)
        baker.make(
            TaxRate,
            pos=pos,
            default_for_service=True,
            rate=Decimal(99),
        )
        service = service_recipe.make(business=self.business, tax_rate=Decimal(5))

        data = copy.deepcopy(self.service_data)
        data['tax_rate'] = '7.00'
        serializer = self.make_service_serializer(data, instance=service)
        self.assertTrue(serializer.is_valid())
        self.assertEqual(serializer.validated_data['tax_rate'], Decimal(7))

        data = copy.deepcopy(self.service_data)
        data['tax_rate'] = None
        serializer = self.make_service_serializer(data, instance=service)
        self.assertTrue(serializer.is_valid())
        self.assertEqual(serializer.validated_data['tax_rate'], None)

        data = copy.deepcopy(self.service_data)
        data['tax_rate'] = '0.00'
        serializer = self.make_service_serializer(data, instance=service)
        self.assertTrue(serializer.is_valid())
        self.assertEqual(serializer.validated_data['tax_rate'], Decimal(0))

        data = copy.deepcopy(self.service_data)
        serializer = self.make_service_serializer(data, instance=service)
        self.assertTrue(serializer.is_valid())
        self.assertEqual(serializer.validated_data['tax_rate'], Decimal(5))

    def test_service_variant_serializer(self):
        data = copy.deepcopy(self.service_variant_data)
        del data['time_slot_interval']
        serializer = self.make_sv_serializer(data, None)
        serializer.is_valid()
        assert not serializer.errors
        assert serializer.validated_data
        assert serializer.validated_data['time_slot_interval'] == relativedelta(
            minutes=settings.SERVICE_VARIANT_DEFAULT_INTERVAL
        )

        del data['gap_hole_duration']
        serializer = self.make_sv_serializer(data, None)
        serializer.is_valid()
        assert serializer.errors.get('gap_hole_start_after', None) is not None

        data = copy.deepcopy(self.service_variant_data)
        del data['gap_hole_start_after']
        serializer = self.make_sv_serializer(data, None)
        serializer.is_valid()
        assert serializer.errors.get('gap_hole_duration', None) is not None

        data = copy.deepcopy(self.service_variant_data)
        del data['price']
        serializer = self.make_sv_serializer(data, None)
        serializer.is_valid()
        assert serializer.errors.get('price', None) is not None

        data = copy.deepcopy(self.service_variant_data)
        data['type'] = '@'
        serializer = self.make_sv_serializer(data, None)
        serializer.is_valid()
        assert serializer.errors.get('type', None) is not None

    def test_create_service(self):
        data = copy.deepcopy(self.service_data)
        serializer = self.make_service_serializer(data)
        serializer.is_valid()
        assert not serializer.errors
        assert serializer.validated_data

        instance = serializer.save()
        assert instance.service_variants.filter(active=True).count() == len(data['variants'])

        # Add new variant
        new_variant = {
            "duration": 45,
            "type": "X",
            "price": 100,
            "time_slot_interval": 15,
        }

        created_service_variant = instance.service_variants.last()
        data['variants'][0]['id'] = created_service_variant.id
        data['variants'].append(new_variant)

        serializer = self.make_service_serializer(data, instance)
        serializer.is_valid()
        instance = serializer.save()

        created_service_variant = instance.service_variants.last()
        assert created_service_variant.type == new_variant['type']
        assert created_service_variant.price == new_variant['price']
        assert (
            created_service_variant.time_slot_interval.minutes == new_variant['time_slot_interval']
        )
        assert created_service_variant.duration.minutes == new_variant['duration']
        assert instance.service_variants.filter(active=True).count() == len(data['variants'])

        # Delete Variant
        data['variants'].pop()
        serializer = self.make_service_serializer(data, instance)
        serializer.is_valid()
        instance = serializer.save()
        service = Service.objects.get(id=instance.id)
        assert service.service_variants.filter(active=True).count() == len(data['variants'])
        assert service.service_variants(manager='all_objects').count() == len(data['variants']) + 1


@pytest.mark.django_db
class TestServiceSerializerWriter(unittest.TestCase):  # pylint: disable=too-many-public-methods
    def setUp(self):
        self.user = baker.make(User)
        self.access_level = Resource.STAFF_ACCESS_LEVEL_OWNER

        self.business = baker.make(
            Business,
            name="Biznes",
            owner=self.user,
        )

        primary_category = baker.make(BusinessCategory)
        self.business.primary_category = primary_category
        self.business.save()

        self.pos = baker.make(POS, business=self.business)

        self.staffer = staffer_recipe.make(
            business=self.business,
        )
        self.appliance = appliance_recipe.make(
            business=self.business,
        )
        self.service = baker.make(
            Service,
            name="Nazwa podstawowa",
            business=self.business,
            gap_time=relativedelta(minutes=0),
            padding_time=relativedelta(minutes=30),
        )

        self.service_variant_1 = ServiceVariant.objects.create(
            service=self.service,
            duration=relativedelta(minutes=30),
            price=100,
            label='',
            valid_from=tznow(),
            type=PriceType.FIXED,
            time_slot_interval=relativedelta(minutes=30),
        )

        self.service_variant_2 = ServiceVariant.objects.create(
            service=self.service,
            duration=relativedelta(minutes=50),
            price=150,
            label='',
            valid_from=tznow(),
            type=PriceType.FIXED,
            time_slot_interval=relativedelta(minutes=30),
        )

        self.service_variant_3 = ServiceVariant.objects.create(
            service=self.service,
            duration=relativedelta(minutes=40),
            price=200,
            label='',
            valid_from=tznow(),
            type=PriceType.FIXED,
            time_slot_interval=relativedelta(minutes=30),
        )
        self.service.add_staffers([self.staffer])
        ServiceVariantChangelog.create_entry(self.user, self.service_variant_1)
        ServiceVariantChangelog.create_entry(self.user, self.service_variant_2)
        ServiceVariantChangelog.create_entry(self.user, self.service_variant_3)

    def make_serializer(self, data, instance=None, extra_context=None):
        context = {
            'user': self.business.owner,
            'pos': self.pos,
            'pos_pay_by_app_enabled': self.business.pos_pay_by_app_enabled,
            'business': self.business,
        }

        if extra_context:
            context.update(extra_context)

        serializer = ServiceSerializerWriter(data=data, instance=instance, context=context)
        return serializer

    @staticmethod
    def test_pricing_option_label_length():
        serializer = ServiceVariantSerializerWriter(
            data={
                'duration': 15,
                'type': PriceType.FIXED,
                'label': 'a' * 128 + 'a',
            }
        )
        assert not serializer.is_valid()
        assert serializer.errors['label'][0].code == 'max_length'

    def test_creating_deletable_service_variant(self):
        # Creating data for Service with 2 ServiceVariants
        serializer = ServiceSerializer(self.service)
        data = serializer.data
        data['resources'] = [self.staffer.id]
        old_service_variant_ids = set(
            ServiceVariant.objects.filter(active=True).values_list('id', flat=True)
        )

        # Changing service name
        data['name'] = 'Zmieniona nazwa'
        serializer = self.make_serializer(data, self.service)
        serializer.is_valid()
        print(serializer.errors)
        instance = serializer.save()
        new_service_variant_ids = set(
            ServiceVariant.objects.filter(active=True).values_list('id', flat=True)
        )

        assert instance.name == 'Zmieniona nazwa'
        assert len(instance.active_variants) == len(data['variants'])
        assert new_service_variant_ids == old_service_variant_ids

    def test_creating_service_variant(self):
        # Creating data for Service with 2 ServiceVariants
        serializer = ServiceSerializer(self.service)
        data = serializer.data
        data['resources'] = [self.staffer.id]

        # Create CommissionRate for each service variant
        for sv in ServiceVariant.objects.all():
            CommissionRate.objects.create(
                service_variant=sv,
                type=CommissionDefaults.COMMISSION_TYPE__AMOUNT,
                rate=10,
                pos=self.pos,
                product_id=None,
            )

        for sv in self.service.service_variants.all():
            assert sv.default_commission_rate is not None

        # Changing service name
        data['name'] = 'Zmieniona nazwa'
        serializer = self.make_serializer(data, self.service)
        serializer.is_valid()
        print(serializer.errors)
        instance = serializer.save()

        assert instance.name == 'Zmieniona nazwa'
        assert len(instance.active_variants) == len(data['variants'])

    @patch.object(ServiceVariant, 'can_be_safely_deleted')
    def test_remove_service_variant_not_safe(self, can_be_safely_deleted_mock):
        can_be_safely_deleted_mock.return_value = False

        # Creating data for Service with 2 ServiceVariants
        serializer = ServiceSerializer(self.service)
        data = serializer.data
        data['resources'] = [self.staffer.id]

        del data['variants'][1]
        serializer = self.make_serializer(data, self.service)
        serializer.is_valid()
        print(serializer.errors)
        instance = serializer.save()

        self.assertListEqual(
            list(instance.active_variants.values_list('price', flat=True)),
            [Decimal(100), Decimal(150)],
        )

    @patch.object(ServiceVariant, 'can_be_safely_deleted')
    def test_remove_service_variant_safe(self, can_be_safely_deleted_mock):
        can_be_safely_deleted_mock.return_value = True

        # Creating data for Service with 2 ServiceVariants
        serializer = ServiceSerializer(self.service)
        data = serializer.data
        data['resources'] = [self.staffer.id]

        del data['variants'][1]
        serializer = self.make_serializer(data, self.service)
        assert serializer.is_valid(), serializer.errors
        instance = serializer.save()

        self.assertListEqual(
            list(instance.active_variants.values_list('price', flat=True)),
            [Decimal(100), Decimal(150)],
        )

    @patch.object(ServiceVariant, 'can_be_safely_deleted')
    def test_add_remove_service_variant_safe(self, can_be_safely_deleted_mock):
        can_be_safely_deleted_mock.return_value = True

        # Creating data for Service with 2 ServiceVariants
        serializer = ServiceSerializer(self.service)
        data = serializer.data
        data['resources'] = [self.staffer.id]

        # It means that I delete this variant and make new one with
        # another price
        del data['variants'][1]['id']
        data['variants'][1]['price'] = 99

        serializer = self.make_serializer(data, self.service)
        serializer.is_valid()
        print(serializer.errors)
        instance = serializer.save()

        # ServiceVariant.can_be_safely_deleted (see
        # test_add_remove_service_variant_safe_when_cant_be_safely_deleted)
        # does not affect this results. It sets active flag to False in both
        # cases.
        self.assertListEqual(
            list(instance.active_variants.values_list('price', flat=True)),
            [Decimal(100), Decimal(99), Decimal(150)],
        )

    @patch.object(ServiceVariant, 'can_be_safely_deleted')
    def test_add_remove_service_variant_safe_when_cant_be_safely_deleted(
        self, can_be_safely_deleted_mock
    ):
        can_be_safely_deleted_mock.return_value = False

        # Creating data for Service with 2 ServiceVariants
        serializer = ServiceSerializer(self.service)
        data = serializer.data
        data['resources'] = [self.staffer.id]

        # It means that I delete this variant and make new one with
        # another price
        del data['variants'][1]['id']
        data['variants'][1]['price'] = 99

        serializer = self.make_serializer(data, self.service)
        serializer.is_valid()
        print(serializer.errors)
        instance = serializer.save()

        self.assertListEqual(
            list(instance.active_variants.values_list('price', flat=True)),
            [Decimal(100), Decimal(99), Decimal(150)],
        )

    def test_create_sv_changelog_with_appliances(self):
        self.service.add_appliances([self.appliance])
        service_variant = ServiceVariant.objects.create(
            service=self.service,
            duration=relativedelta(minutes=45),
            price=50,
            label='',
            valid_from=tznow(),
            type=PriceType.FIXED,
            time_slot_interval=relativedelta(minutes=45),
        )

        ServiceVariantChangelog.create_entry(self.user, service_variant)
        sv_changelog = ServiceVariantChangelog.objects.filter(
            service_variant_id=service_variant
        ).first()
        self.assertTrue(self.appliance.id in sv_changelog.data['service_resources'])

    def test_update_sv_changelog_add_appliance(self):
        self.service.add_appliances([self.appliance])
        ServiceVariantChangelog.create_entry(self.user, self.service_variant_1)
        sv_changelog = ServiceVariantChangelog.objects.filter(
            service_variant_id=self.service_variant_1.id
        ).all()
        self.assertTrue(self.appliance.id in sv_changelog[0].data['service_resources'])

    def test_update_sv_changelog_delete_appliance(self):
        self.service.add_appliances([self.appliance])
        ServiceVariantChangelog.create_entry(self.user, self.service_variant_1)
        self.service.set_appliances([])
        sv_changelog = ServiceVariantChangelog.objects.filter(
            service_variant_id=self.service_variant_1.id
        ).last()
        self.assertTrue(self.appliance.id not in sv_changelog.data['service_resources'])

    def test_create_traveling_service(self):
        serializer = ServiceSerializer(self.service)
        data = serializer.data
        data['resources'] = [self.staffer.id]
        data['is_traveling_service'] = True

        serializer = self.make_serializer(data, self.service)
        assert serializer.is_valid() is False
        assert serializer.errors['is_traveling_service'][0].code == 'traveling_disabled'

        baker.make(
            'business.TravelingToClients',
            business=self.business,
            traveling_only=True,
        )
        serializer = self.make_serializer(data, self.service)
        assert serializer.is_valid() is True, serializer.errors

        data['is_traveling_service'] = False
        serializer = self.make_serializer(data, self.service)
        assert serializer.is_valid() is False
        assert serializer.errors['is_traveling_service'][0].code == 'traveling_only'

    def test_updating_service_with_duplicated_new_variants(self):
        invalid_data = {
            "name": "Test",
            "gap_time": 0,
            "parallel_clients": 1,
            "color": None,
            "variants": [
                {
                    "duration": 30,
                    "type": "X",
                    "price": 99,
                    "label": "A",
                    "time_slot_interval": 15,
                    "formula": [],
                },
                {
                    "duration": 30,
                    "type": "X",
                    "price": 99,
                    "label": "A",
                    "time_slot_interval": 15,
                    "formula": [],
                },
            ],
            "resources": [self.staffer.id],
            "is_available_for_customer_booking": True,
            "is_online_service": False,
            "tax_rate": "23.00",
            "gap_hole": {},
        }

        valid_data = copy.deepcopy(invalid_data)
        valid_data["variants"][1]["label"] = "B"

        serializer = self.make_serializer(invalid_data, self.service)
        assert not serializer.is_valid()
        assert serializer.errors['variants'][0].code == 'duplicated'

        serializer = self.make_serializer(valid_data, self.service)
        assert serializer.is_valid()
        serializer.save()

    def test_updating_service_with_duplicated_with_existing_variant(self):
        invalid_data = {
            "name": "Test",
            "gap_time": 0,
            "parallel_clients": 1,
            "color": None,
            "variants": [
                {
                    "duration": 30,
                    "type": "X",
                    "price": 100,
                    "label": "",
                    "time_slot_interval": 30,
                    "formula": [],
                },
                {
                    "duration": 30,
                    "type": "X",
                    "price": 99,
                    "label": "",
                    "time_slot_interval": 15,
                    "formula": [],
                },
            ],
            "resources": [self.staffer.id],
            "is_available_for_customer_booking": True,
            "is_online_service": False,
            "tax_rate": "23.00",
            "gap_hole": {},
        }

        valid_data = copy.deepcopy(invalid_data)
        valid_data["variants"][1]["label"] = "B"

        serializer = self.make_serializer(invalid_data, self.service)
        assert not serializer.is_valid()
        assert serializer.errors['variants'][0].code == 'duplicated'

        serializer = self.make_serializer(valid_data, self.service)
        assert serializer.is_valid()
        serializer.save()

    def test_updating_service_swap_variant_ids(self):  # RM-256
        initial_data = {
            "name": "Test",
            "gap_time": 0,
            "parallel_clients": 1,
            "color": None,
            "variants": [
                {
                    "duration": 15,
                    "type": "X",
                    "price": 50,
                    "label": "Label",
                    "time_slot_interval": 15,
                    "formula": [],
                },
                {
                    "duration": 30,
                    "type": "X",
                    "price": 100,
                    "label": "Label 2",
                    "time_slot_interval": 30,
                    "formula": [],
                },
            ],
            "resources": [self.staffer.id],
            "is_available_for_customer_booking": True,
            "is_online_service": False,
            "tax_rate": "23.00",
            "gap_hole": {},
        }
        serializer = self.make_serializer(data=initial_data)
        self.assertTrue(serializer.is_valid())
        service = serializer.save()

        variant_1 = service.active_variants.get(price=50)
        variant_2 = service.active_variants.get(price=100)

        modified_data = copy.deepcopy(initial_data)
        modified_data['variants'][0]['id'] = variant_2.id
        modified_data['variants'][1]['id'] = variant_1.id

        serializer = self.make_serializer(instance=service, data=modified_data)
        self.assertTrue(serializer.is_valid())
        serializer.save()

    def test_updating_service_ignore_variant_id_of_other_service(self):
        other_service_variant = service_variant_recipe.make(
            service=service_recipe.make(business=self.business),
        )
        serializer = self.make_serializer(
            instance=self.service,
            data={
                'id': self.service.id,
                'name': 'Service',
                'resources': [self.staffer.id],
                'variants': [
                    {
                        'id': other_service_variant.id,
                        'duration': 60,
                        'label': '',
                        'price': '25.00',
                        'type': 'X',
                    },
                ],
            },
        )
        self.assertTrue(serializer.is_valid())
        serializer.save()

        self.assertNotEqual(
            self.service.service_variants.filter(active=True).get(), other_service_variant
        )

    def test_create_with_treatment(self):
        treatment = baker.make(
            BusinessCategory,
            type=BusinessCategory.TREATMENT,
            parent=self.business.primary_category,
        )
        data = {
            "name": "Test",
            "variants": [
                {
                    "duration": 30,
                    "type": "X",
                    "price": 99,
                    "label": "B",
                    "time_slot_interval": 15,
                    "formula": [],
                }
            ],
            "resources": [self.staffer.id],
            "tax_rate": "23.00",
            'treatment': treatment.id + 1,
        }

        # expect invalid treatment ids to be silently ignored
        serializer = self.make_serializer(copy.deepcopy(data), self.service)
        self.assertIs(serializer.is_valid(), True)

        data['treatment'] = treatment.id
        serializer = self.make_serializer(
            data,
            self.service,
        )
        self.assertIs(serializer.is_valid(), True)
        self.assertIn('treatment_id', serializer.validated_data)
        service = serializer.save()

        service.refresh_from_db()
        self.assertEqual(service.treatment_id, treatment.id)

        business_treatment_ids = set(self.business.treatments.values_list('id', flat=True))
        self.assertSetEqual(business_treatment_ids, {treatment.id})

    def test_create_combo__errors(self):
        data = self._get_combo_data()
        del data['variants'][0]['combo_pricing']
        serializer = self.make_serializer(data)
        self.assertFalse(serializer.is_valid())
        self.assertDictEqual(
            serializer.errors,
            {
                'non_field_errors': [
                    ErrorDetail(_('Service Combo must have specified pricing'), code='invalid'),
                ],
            },
        )

        data = self._get_combo_data()
        del data['variants'][0]['combo_children']
        serializer = self.make_serializer(data)
        self.assertFalse(serializer.is_valid())
        self.assertDictEqual(
            serializer.errors,
            {
                'non_field_errors': [
                    ErrorDetail(
                        _('Service Combo must have at least one child service'), code='invalid'
                    ),
                ],
            },
        )

        data = self._get_combo_data()
        data['variants'][0]['combo_children'].pop()
        serializer = self.make_serializer(data)
        self.assertFalse(serializer.is_valid())
        self.assertDictEqual(
            serializer.errors['variants'][0],
            {
                'combo_children': [
                    ErrorDetail(_('Add at least two services'), code='invalid'),
                ],
            },
        )

    def test_create_combo__services_price(self):
        """
        Ensure that prices of combo children are inherited from original variants.
        """
        data = self._get_combo_data(combo_pricing=ComboPricing.SERVICES)
        serializer = self.make_serializer(data)
        self.assertTrue(serializer.is_valid(), serializer.errors)

        service = serializer.save()
        service_variant = service.service_variants.first()
        self.assertEqual(service_variant.combo_children_through.count(), 2)
        self.assertIsNone(service_variant.price)
        self.assertIsNone(service_variant.type)
        self.assertEqual(service_variant.duration, relativedelta())

        serializer = ServiceSerializer(instance=service, context=dict(valid_currency=True))
        variant = serializer.data['variants'][0]
        self.assertEqual(variant['combo_pricing'], ComboPricing.SERVICES)
        self.assertEqual(variant['service_price'], '$250.00')

        for child in variant['combo_children']:
            self.assertIn('service_color', child['service_variant'])
            self.assertIn('service_name', child['service_variant'])
        self.assertEqual(variant['combo_children'][0]['type'], PriceType.FIXED)
        self.assertEqual(variant['combo_children'][0]['price'], '100.00')
        self.assertEqual(variant['combo_children'][0]['service_price'], '$100.00')
        self.assertEqual(variant['combo_children'][1]['type'], PriceType.FIXED)
        self.assertEqual(variant['combo_children'][1]['price'], '150.00')
        self.assertEqual(variant['combo_children'][1]['service_price'], '$150.00')

        # check edit price type
        data['variants'][0]['combo_children'][0]['type'] = PriceType.STARTS_AT
        data['variants'][0]['combo_children'][0]['price'] = 999
        serializer = self.make_serializer(instance=service, data=data)
        self.assertTrue(serializer.is_valid(), serializer.errors)

        # ensure we didn't change the price of original variant
        service = serializer.save()
        serializer = ServiceSerializer(instance=service)
        variant = serializer.data['variants'][0]
        self.assertEqual(variant['combo_children'][0]['service_price'], '$100.00')
        self.assertEqual(variant['combo_children'][0]['service_variant']['type'], PriceType.FIXED)
        self.assertEqual(variant['combo_children'][0]['service_variant']['price'], '100.00')

    def test_create_combo__custom_price(self):
        """
        Check overriding of service variant prices
        """
        # pylint: disable=too-many-statements
        data = self._get_combo_data(combo_pricing=ComboPricing.CUSTOM)
        serializer = self.make_serializer(data)
        self.assertTrue(serializer.is_valid(), serializer.errors)

        service = serializer.save()
        service_variant = service.service_variants.first()
        self.assertEqual(service_variant.combo_children_through.count(), 2)
        self.assertIsNone(service_variant.price)
        self.assertIsNone(service_variant.type)
        self.assertEqual(service_variant.duration, relativedelta())
        self.assertEqual(ComboMembership.objects.filter(combo__service=service).count(), 2)

        serializer = ServiceSerializer(instance=service, context=dict(valid_currency=True))
        variant = serializer.data['variants'][0]
        self.assertEqual(variant['combo_pricing'], ComboPricing.CUSTOM)
        self.assertEqual(variant['price'], '15.00')
        self.assertEqual(variant['type'], PriceType.STARTS_AT)
        self.assertEqual(variant['service_price'], '$15.00+')

        for child in variant['combo_children']:
            self.assertIn('service_color', child['service_variant'])
            self.assertIn('service_name', child['service_variant'])
        self.assertEqual(variant['combo_children'][0]['price'], '10.00')
        self.assertEqual(variant['combo_children'][0]['type'], PriceType.FIXED)
        self.assertEqual(variant['combo_children'][0]['service_price'], '$10.00')
        self.assertEqual(variant['combo_children'][1]['price'], '5.00')
        self.assertEqual(variant['combo_children'][1]['type'], PriceType.STARTS_AT)
        self.assertEqual(variant['combo_children'][1]['service_price'], '$5.00+')

        # check edit price type
        data['variants'][0]['id'] = variant['id']
        data['variants'][0]['combo_children'][0]['type'] = PriceType.STARTS_AT
        data['variants'][0]['combo_children'][0]['price'] = 999
        serializer = self.make_serializer(instance=service, data=data)
        self.assertTrue(serializer.is_valid(), serializer.errors)

        # ensure we didn't change the price of original variant
        service = serializer.save()
        self.assertEqual(ComboMembership.objects.filter(combo__service=service).count(), 2)
        serializer = ServiceSerializer(instance=service)
        variant = serializer.data['variants'][0]
        self.assertEqual(variant['combo_children'][0]['service_price'], '$999.00+')
        self.assertEqual(variant['combo_children'][0]['service_variant']['type'], PriceType.FIXED)
        self.assertEqual(variant['combo_children'][0]['service_variant']['price'], '100.00')

        # check default prices
        data = self._get_combo_data(combo_pricing=ComboPricing.CUSTOM)
        del data['variants'][0]['price']
        del data['variants'][0]['type']
        for child_data in data['variants'][0]['combo_children']:
            del child_data['price']
            del child_data['type']
        serializer = self.make_serializer(data)
        self.assertTrue(serializer.is_valid(), serializer.errors)
        service = serializer.save()
        service_variant = service.service_variants.first()
        combo_memberships = service_variant.combo_children_through.all()
        self.assertEqual(
            combo_memberships[0].service_price, ServicePrice(None, PriceType.DONT_SHOW)
        )
        self.assertEqual(
            combo_memberships[1].service_price, ServicePrice(None, PriceType.DONT_SHOW)
        )
        self.assertEqual(service_variant.service_price, ServicePrice(0, PriceType.DONT_SHOW))

        serializer = ServiceSerializer(instance=service)
        variant = serializer.data['variants'][0]
        self.assertEqual(variant['combo_children'][0]['type'], PriceType.DONT_SHOW)
        self.assertEqual(variant['combo_children'][0]['price'], None)
        self.assertEqual(variant['type'], PriceType.DONT_SHOW)
        self.assertIsNone(variant['price'])

        serializer = ServiceSerializer(instance=self.service_variant_1.service)
        self.assertEqual(serializer.data['combo_parents'], 2)

        data = self._get_combo_data()
        data['name'] = 'Test service 2'
        serializer = self.make_serializer(data)
        self.assertTrue(serializer.is_valid(), serializer.errors)
        serializer.save()

        serializer = ServiceSerializer(instance=self.service_variant_1.service)
        self.assertEqual(serializer.data['combo_parents'], 3)

    def test_update_combo_child_to_traveling_service(self):
        data = self._get_combo_data()
        serializer = self.make_serializer(data)
        self.assertTrue(serializer.is_valid(), serializer.errors)
        serializer.save()

        serializer = ServiceSerializer(instance=self.service)
        traveling_to_client_recipe.make(business=self.business)
        serializer = self.make_serializer(
            instance=self.service,
            data={
                'name': serializer.data['name'],
                'variants': serializer.data['variants'],
                'resources': serializer.data['resources'],
                'is_traveling_service': True,
            },
        )
        self.assertFalse(serializer.is_valid())
        self.assertDictEqual(
            serializer.errors,
            {
                'is_traveling_service': [
                    ErrorDetail(
                        _(
                            "Unable to change the service type to mobile. "
                            "It’s assigned to at least one combo service."
                        ),
                        code='traveling_combo_disabled',
                    ),
                ],
            },
        )

    def test_should_update_combo_child_to_traveling_service_with_deleted_combo(self):
        data = self._get_combo_data()
        serializer = self.make_serializer(data)
        self.assertTrue(serializer.is_valid(), serializer.errors)
        combo_service = serializer.save()

        combo_service.soft_delete()

        serializer = ServiceSerializer(instance=self.service)
        traveling_to_client_recipe.make(business=self.business)
        serializer = self.make_serializer(
            instance=self.service,
            data={
                'name': serializer.data['name'],
                'variants': serializer.data['variants'],
                'resources': serializer.data['resources'],
                'is_traveling_service': True,
            },
        )
        self.assertTrue(serializer.is_valid())

    def test_update_combo(self):
        data = self._get_combo_data(combo_pricing=ComboPricing.CUSTOM)
        serializer = self.make_serializer(data)
        self.assertTrue(serializer.is_valid(), serializer.errors)
        instance = serializer.save()

        data['variants'][0]['combo_children'][1].update(
            {
                'type': PriceType.FIXED,
                'price': 7,
            }
        )
        serializer = self.make_serializer(instance=instance, data=data)
        self.assertTrue(serializer.is_valid(), serializer.errors)

        instance = serializer.save()
        instance = Service.objects.get(id=instance.id)
        serializer = ServiceSerializer(instance=instance)
        variant = serializer.data['variants'][0]
        self.assertEqual(variant['service_price'], '$17.00')

        combo_variant = serializer.data['variants'][0]['combo_children'][1]
        self.assertEqual(combo_variant['type'], PriceType.FIXED)
        self.assertEqual(combo_variant['price'], '7.00')

    def test_update_combo_if_child_has_deleted_payment(self):
        data = self._get_combo_data(combo_pricing=ComboPricing.CUSTOM)
        serializer = self.make_serializer(data)
        self.assertTrue(serializer.is_valid(), serializer.errors)
        instance = serializer.save()

        child = instance.active_variants[0].combo_children_list[0]
        payment = baker.make(ServiceVariantPayment, service_variant=child)
        payment.delete()

        serializer = self.make_serializer(instance=instance, data=data)
        self.assertTrue(serializer.is_valid(), serializer.errors)

    def _get_combo_data(self, combo_type=ComboType.SEQUENCE, combo_pricing=ComboPricing.SERVICES):
        return dict(
            name='Test service',
            resources=[self.staffer.id],
            combo_type=combo_type,
            variants=[
                dict(
                    combo_pricing=combo_pricing,
                    type=None,
                    price=None,
                    duration=30,
                    combo_children=[
                        dict(
                            service_variant=dict(id=self.service_variant_1.id),
                            gap_time=15,
                            type=PriceType.FIXED,
                            price=10,
                        ),
                        dict(
                            service_variant=dict(id=self.service_variant_2.id),
                            type=PriceType.STARTS_AT,
                            price=5,
                        ),
                    ],
                )
            ],
        )

    def test_create_combo_with_no_resources(self):
        """
        Expect combo to inherit resources from its children
        """
        expected_resources = [self.staffer]

        self.service.add_staffers(expected_resources)

        data = self._get_combo_data()
        data['resources'] = []

        serializer = self.make_serializer(data)
        self.assertTrue(serializer.is_valid(), serializer.errors)
        instance = serializer.save()

        serializer = ServiceSerializer(instance=instance)
        self.assertSetEqual(
            set(serializer.data['resources']),
            {resource.id for resource in expected_resources},
        )

    def test_validate_combo_read_only_fields(self):
        """
        Expect price, duration and payment of the combo variant to be read only,
        and that validation is skipped.
        """
        data = self._get_combo_data(combo_pricing=ComboPricing.CUSTOM)
        data['variants'][0]['price'] = 'invalid value'
        data['variants'][0]['type'] = 'invalid value'
        data['variants'][0]['duration'] = 'invalid value'
        data['variants'][0]['payment'] = 'invalid value'
        serializer = self.make_serializer(data)
        self.assertTrue(serializer.is_valid(), serializer.errors)
        service = serializer.save()

        data['id'] = service.id
        serializer = self.make_serializer(instance=service, data=data)
        self.assertTrue(serializer.is_valid(), serializer.errors)

    def test_update_no_show_protection(self):
        service_variant = self.service_variant_1
        data = {
            'id': service_variant.service.id,
            'name': service_variant.service.name,
            'resources': [self.staffer.id],
            'variants': [
                {
                    'id': service_variant.id,
                    'type': PriceType.FIXED,
                    'price': '15.00',
                    'duration': 20,
                }
            ],
            'no_show_protection': {
                'type': NoShowProtectionType.PREPAYMENT,
                'percentage': 50,
            },
        }
        serializer = ServiceSerializerWriter(
            data=data,
            instance=service_variant.service,
            context={
                'user': self.user,
                'pos': self.business.pos,
                'business': self.business,
            },
        )
        self.assertTrue(serializer.is_valid())
        serializer.save()
        payment = ServiceVariantPayment.objects.get(service_variant=service_variant)
        self.assertEqual(payment.payment_type, NoShowProtectionType.PREPAYMENT)
        self.assertEqual(payment.saving_type, RateType.PERCENTAGE)
        self.assertEqual(payment.payment_amount, Decimal('7.50'))

        data['no_show_protection'] = None
        serializer = ServiceSerializerWriter(
            data=data,
            instance=service_variant.service,
            context={
                'user': self.user,
                'pos': self.business.pos,
                'business': self.business,
            },
        )
        self.assertTrue(serializer.is_valid())
        serializer.save()
        self.assertFalse(
            ServiceVariantPayment.objects.filter(service_variant=service_variant).exists()
        )

    def test_validate_minimal_payment_amount(self):
        service_variant = self.service_variant_1
        data = {
            'id': service_variant.service.id,
            'name': service_variant.service.name,
            'resources': [self.staffer.id],
            'variants': [
                {
                    'id': service_variant.id,
                    'type': PriceType.FIXED,
                    'price': '10.00',
                    'duration': 20,
                }
            ],
            'no_show_protection': {
                'type': NoShowProtectionType.PREPAYMENT,
                'percentage': 49,
            },
        }
        serializer = ServiceSerializerWriter(
            data=data,
            instance=service_variant.service,
            context={
                'user': self.user,
                'pos': self.business.pos,
                'business': self.business,
            },
        )
        self.assertFalse(serializer.is_valid())
        self.assertSetEqual(
            {error.code for error in serializer.errors['no_show_protection']},
            {'minimal_payment_amount'},
        )

        data['no_show_protection']['percentage'] = 50  # expect prepayment to be $5
        serializer = ServiceSerializerWriter(
            data=data,
            instance=service_variant.service,
            context={
                'user': self.user,
                'pos': self.business.pos,
                'business': self.business,
            },
        )
        self.assertTrue(serializer.is_valid())

    def test_validate_minimal_payment_amount__combo(self):
        service_variant_child_1 = service_variant_recipe.make(
            service=service_recipe.make(
                business=self.business,
                name='Child 1',
            ),
            price=Decimal('100.00'),
            type=PriceType.FIXED,
            payment__payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment__payment_amount=40,
            payment__saving_type=ServiceVariantPayment.PERCENTAGE,
        )
        service_variant_child_2 = service_variant_recipe.make(
            service=service_recipe.make(
                business=self.business,
                name='Child 2',
            ),
        )
        service_variant = service_variant_recipe.make(
            service=service_recipe.make(
                business=self.business,
                name='Combo',
                combo_type=ComboType.SEQUENCE,
            ),
            combo_pricing=ComboPricing.SERVICES,
            type=None,
            price=None,
        )
        ComboMembership.objects.bulk_create(
            [
                ComboMembership(
                    combo=service_variant,
                    child=service_variant_child_1,
                    order=1,
                ),
                ComboMembership(
                    combo=service_variant,
                    child=service_variant_child_2,
                    order=2,
                ),
            ]
        )

        data = {
            'id': service_variant.id,
            'combo_pricing': ComboPricing.CUSTOM,
            'combo_children': [
                {
                    'service_variant': {
                        'id': service_variant_child_1.id,
                    },
                    'price': 10,
                    'type': PriceType.FIXED,
                },
                {
                    'service_variant': {
                        'id': service_variant_child_2.id,
                    },
                    'price': 20,
                    'type': PriceType.FIXED,
                },
            ],
        }
        serializer = ServiceVariantSerializerWriter(
            instance=service_variant,
            data=data,
        )
        self.assertFalse(serializer.is_valid())

        # NOTE: error does not refer to "no_show_protection" as in regular services
        self.assertSetEqual(
            {error.code for error in serializer.errors['non_field_errors']},
            {'minimal_payment_amount'},
        )

        data['combo_children'][0]['price'] = 12.5  # expect prepayment to be $5
        serializer = ServiceVariantSerializerWriter(
            instance=service_variant,
            data=data,
        )
        self.assertTrue(serializer.is_valid())

    def test_validate_create(self):
        serializer = ServiceSerializer(self.service)
        data = serializer.data
        data['is_treatment_selected_by_user'] = True
        extra_context = {'use_service_type': True}
        serializer = self.make_serializer(data, extra_context=extra_context)
        self.assertTrue(serializer.is_valid(), serializer.errors)

    def test_validate_treatment_is_none(self):
        serializer = ServiceSerializer(self.service)
        data = serializer.data

        data['treatment'] = None

        serializer = self.make_serializer(data, self.service)
        self.assertTrue(serializer.is_valid(), serializer.errors)
        self.assertNotIn('treatment_id', serializer.validated_data)

    def test_validate_treatment_not_in_data(self):
        serializer = ServiceSerializer(self.service)
        data = serializer.data

        del data['treatment']

        serializer = self.make_serializer(data, self.service)
        self.assertTrue(serializer.is_valid(), serializer.errors)
        self.assertNotIn('treatment_id', serializer.validated_data)

    def test_use_service_type_validate_treatment_ok(self):
        serializer = ServiceSerializer(self.service)
        data = serializer.data

        new_treatment = treatment_recipe.make()
        data['treatment'] = new_treatment.id
        data['is_treatment_selected_by_user'] = True

        extra_context = {'use_service_type': True}
        serializer = self.make_serializer(data, self.service, extra_context)
        self.assertTrue(serializer.is_valid(), serializer.errors)
        self.assertIn('treatment_id', serializer.validated_data)
        self.assertIn('is_treatment_selected_by_user', serializer.validated_data)

    def test_use_service_type_validate_treatment_not_existing(self):
        serializer = ServiceSerializer(self.service)
        data = serializer.data

        data['treatment'] = 999
        data['is_treatment_selected_by_user'] = True

        extra_context = {'use_service_type': True}
        serializer = self.make_serializer(data, self.service, extra_context)
        self.assertFalse(serializer.is_valid(), serializer.errors)
        self.assertEqual(
            ['Treatment ID does not exist'],
            serializer.errors['treatment'],
        )

    def test_do_not_use_service_type_validate_treatment_not_existing(self):
        # backward compatibility
        serializer = ServiceSerializer(self.service)
        data = serializer.data

        data['treatment'] = 999
        data['is_treatment_selected_by_user'] = True

        serializer = self.make_serializer(data, self.service)
        self.assertTrue(serializer.is_valid(), serializer.errors)
        self.assertNotIn('treatment_id', serializer.validated_data)
        self.assertNotIn('is_treatment_selected_by_user', serializer.validated_data)

    def test_use_service_type_validate_treatment_is_none_ok(self):
        serializer = ServiceSerializer(self.service)
        data = serializer.data

        data['treatment'] = None
        data['is_treatment_selected_by_user'] = True

        extra_context = {'use_service_type': True}
        serializer = self.make_serializer(data, self.service, extra_context)
        self.assertTrue(serializer.is_valid(), serializer.errors)
        self.assertIn('treatment_id', serializer.validated_data)
        self.assertIn('is_treatment_selected_by_user', serializer.validated_data)

    def test_use_service_type_validate_treatment_is_none_invalid_data(self):
        self.service.is_treatment_selected_by_user = True
        self.service.save()

        serializer = ServiceSerializer(self.service)
        data = serializer.data

        data['treatment'] = None
        data['is_treatment_selected_by_user'] = False

        extra_context = {'use_service_type': True}
        serializer = self.make_serializer(data, self.service, extra_context)
        self.assertFalse(serializer.is_valid(), serializer.errors)
        self.assertEqual(
            ['Service type is required'],
            serializer.errors['treatment'],
        )

    def test_use_service_type_validate_treatment_is_not_none_invalid_data(self):
        self.service.is_treatment_selected_by_user = True
        self.service.save()

        serializer = ServiceSerializer(self.service)
        data = serializer.data

        new_treatment = treatment_recipe.make()
        data['treatment'] = new_treatment.id
        data['is_treatment_selected_by_user'] = False

        extra_context = {'use_service_type': True}
        serializer = self.make_serializer(data, self.service, extra_context)
        self.assertFalse(serializer.is_valid(), serializer.errors)
        self.assertEqual(
            ['Service type is required'],
            serializer.errors['treatment'],
        )

    def test_use_service_type_validate_treatment_not_in_data(self):
        serializer = ServiceSerializer(self.service)
        data = serializer.data

        del data['treatment']

        extra_context = {'use_service_type': True}
        serializer = self.make_serializer(data, self.service, extra_context)
        self.assertFalse(serializer.is_valid(), serializer.errors)
        self.assertEqual(
            ['Service type is required'],
            serializer.errors['treatment'],
        )

    def test_use_service_type_validate_is_treatment_selected_by_user_not_in_data(self):
        serializer = ServiceSerializer(self.service)
        data = serializer.data

        del data['is_treatment_selected_by_user']

        extra_context = {'use_service_type': True}
        serializer = self.make_serializer(data, self.service, extra_context)
        self.assertFalse(serializer.is_valid(), serializer.errors)
        self.assertEqual(
            ['Service type is required'],
            serializer.errors['treatment'],
        )

    def test_create_combo_with_superuser_in_context(self):
        superuser = baker.make(User, superuser=True)
        service_variant = self.service_variant_1
        data = {
            'id': service_variant.service.id,
            'name': service_variant.service.name,
            'resources': [self.staffer.id],
            'variants': [
                {
                    'id': service_variant.id,
                    'type': PriceType.FIXED,
                    'price': '15.00',
                    'duration': 20,
                }
            ],
        }
        self.assertFalse(ServiceVariantChangelog.objects.filter(requested_by=superuser).exists())
        serializer = ServiceSerializerWriter(
            data=data,
            instance=service_variant.service,
            context={
                'user': self.user,
                'superuser': superuser,
                'business': self.business,
            },
        )
        self.assertTrue(serializer.is_valid())
        serializer.save()
        self.assertTrue(ServiceVariantChangelog.objects.filter(requested_by=superuser).exists())

    def test_validate_parallel_combo(self):
        """
        Check extra validation for parallel combo
        """
        data = self._get_combo_data(combo_type=ComboType.PARALLEL)
        serializer = self.make_serializer(data)
        self.assertFalse(serializer.is_valid())
        self.assertEqual(serializer.errors['non_field_errors'][0].code, 'not_enough_staffers')

        staffer_recipe.make(
            business=self.business,
        )
        del self.business.resource_ids
        serializer = self.make_serializer(data)
        self.assertTrue(serializer.is_valid(), serializer.errors)

    @override_feature_flag({BlockPPForServicesWithActivePackageFlag: True})
    def test_block_pp_when_service_has_active_package(self):
        serializer = self._prepare_serializer_with_nsp_service_and_voucher(
            voucher_type=Voucher.VOUCHER_TYPE__PACKAGE,
            is_voucher_active=True,
            nsp_type=NoShowProtectionType.PREPAYMENT,
        )
        self.assertFalse(serializer.is_valid())
        self.assertEqual(
            serializer.errors['non_field_errors'][0],
            (
                "Deposit can't be set, because {service_name} "
                + "is a part of an existing package."
            ).format(service_name=self.service.name),
        )
        self.assertEqual(serializer.errors['non_field_errors'][0].code, 'package_with_pp')

    @override_feature_flag({BlockPPForServicesWithActivePackageFlag: True})
    def test_allow_pp_when_service_has_inactive_package(self):
        serializer = self._prepare_serializer_with_nsp_service_and_voucher(
            voucher_type=Voucher.VOUCHER_TYPE__PACKAGE,
            is_voucher_active=False,
            nsp_type=NoShowProtectionType.PREPAYMENT,
        )
        self.assertTrue(serializer.is_valid())

    @override_feature_flag({BlockPPForServicesWithActivePackageFlag: True})
    def test_allow_pp_when_service_has_active_gift_card(self):
        serializer = self._prepare_serializer_with_nsp_service_and_voucher(
            voucher_type=Voucher.VOUCHER_TYPE__EGIFT_CARD,
            is_voucher_active=True,
            nsp_type=NoShowProtectionType.PREPAYMENT,
        )
        self.assertTrue(serializer.is_valid())

    @override_feature_flag({BlockPPForServicesWithActivePackageFlag: True})
    def test_allow_cf_when_service_has_active_package(self):
        serializer = self._prepare_serializer_with_nsp_service_and_voucher(
            voucher_type=Voucher.VOUCHER_TYPE__PACKAGE,
            is_voucher_active=True,
            nsp_type=NoShowProtectionType.CANCELLATION_FEE,
        )
        self.assertTrue(serializer.is_valid())

    def _prepare_serializer_with_nsp_service_and_voucher(
        self, voucher_type, is_voucher_active, nsp_type
    ):
        service_variant = self.service_variant_1
        pg_template = baker.make(
            VoucherTemplate,
            type=voucher_type,
            valid_till=VoucherTemplate.DAYS_30,
            pos=self.pos,
            active=is_voucher_active,
        )
        baker.make(
            VoucherTemplateServiceVariant,
            voucher_template=pg_template,
            service_variant=service_variant,
            amount=2,
            item_price=19.28,
        )
        data = {
            'id': service_variant.service.id,
            'name': service_variant.service.name,
            'resources': [self.staffer.id],
            'variants': [
                {
                    'id': service_variant.id,
                    'type': PriceType.FIXED,
                    'price': '15.00',
                    'duration': 20,
                }
            ],
            'no_show_protection': {
                'type': nsp_type,
                'percentage': 50,
            },
        }
        return ServiceSerializerWriter(
            data=data,
            instance=service_variant.service,
            context={
                'user': self.user,
                'pos': self.business.pos,
                'business': self.business,
            },
        )


@pytest.mark.django_db
class TestServiceServiceCategory(unittest.TestCase):
    def setUp(self) -> None:
        self.business = business_recipe.make()
        service_category = service_category_recipe.make(business=self.business)

        self.uncategorized_service = service_recipe.make(
            business=self.business,
        )
        self.categorized_service = service_recipe.make(
            business=self.business,
            service_category=service_category,
        )
        service_variant_recipe.make(service=self.categorized_service)
        service_variant_recipe.make(service=self.uncategorized_service)

    def test_categorized_service_has_category_id(self):
        serializer = ServiceSerializer(instance=self.categorized_service)
        assert serializer.data['service_category_id'] >= 1

    def test_uncategorized_service_has_category_id(self):
        serializer = ServiceSerializer(instance=self.uncategorized_service)
        assert serializer.data['service_category_id'] is None

    def test_resources__combo(self):
        staffer = staffer_recipe.make(business=self.business)
        staffer.add_services(
            [
                self.categorized_service,
                self.uncategorized_service,
            ]
        )
        combo_service = service_recipe.make(
            business=self.business,
            combo_type=ComboType.SEQUENCE,
        )
        baker.make(
            ComboMembership,
            combo=service_variant_recipe.make(service=combo_service),
            child=self.categorized_service.active_variants[0],
        )

        serializer = ServiceSerializer(instance=combo_service)
        self.assertListEqual(serializer.data['resources'], [staffer.id])


@pytest.mark.django_db
class TestServiceSerializer(unittest.TestCase):
    def setUp(self) -> None:
        self.business = business_recipe.make()
        self.treatment = treatment_recipe.make()
        self.service = service_recipe.make(
            business=self.business,
            treatment=self.treatment,
        )

    def test_service_treatment_not_selected_by_user(self):
        serializer = ServiceSerializer(instance=self.service)
        data = serializer.data

        self.assertFalse(data['is_treatment_selected_by_user'])
        self.assertIsNone(data['treatment'])

    def test_service_treatment_selected_by_user(self):
        self.service.is_treatment_selected_by_user = True
        self.service.save()
        serializer = ServiceSerializer(instance=self.service)
        data = serializer.data

        self.assertTrue(data['is_treatment_selected_by_user'])
        self.assertEqual(data['treatment'], self.treatment.id)
