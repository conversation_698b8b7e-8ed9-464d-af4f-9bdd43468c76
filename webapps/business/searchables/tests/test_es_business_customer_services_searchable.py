import pytest
from django.test import override_settings

from lib.elasticsearch.consts import ESDocType
from webapps.business.elasticsearch.business_customer import (
    BusinessCustomerDocument,
)
from webapps.business.searchables.business_customer import (
    BusinessCustomerSearchable,
)


@pytest.fixture(scope='module')
def bci_dateinfo_dates_and_services(create_business_customer_index_module):
    index = create_business_customer_index_module
    BusinessCustomerDocument(
        merged_data=dict(
            email='<EMAIL>',
            visible_in_business=True,
            cell_phone='+***********',
            full_name='',
        ),
        booking_dateinfo=[
            {
                'date': '2020-06-23',
                'service_id': [1],
                'status': 'F',
            },
        ],
    ).save(refresh=True)
    BusinessCustomerDocument(
        merged_data=dict(
            email='<EMAIL>',
            visible_in_business=True,
            cell_phone='+***********',
            full_name='',
        ),
        booking_dateinfo=[
            {
                'date': '2020-06-21',
                'service_id': [2],
                'status': 'F',
            },
        ],
    ).save(refresh=True)
    index.refresh()


@override_settings(API_COUNTRY='pl')
@pytest.mark.usefixtures('bci_dateinfo_dates_and_services')
def test_customer_with_booking_searchable():
    search = BusinessCustomerSearchable(
        ESDocType.BUSINESS_CUSTOMER,
    ).search(
        dict(
            bookings_start='2020-06-01',
            bookings_end='2020-06-30',
            service_id=[1],
            booking_status=None,
        )
    )

    resp = search.execute()

    assert resp.hits.total.value == 1
