import pytest
from django.conf import settings
from django.test import override_settings
from elasticsearch_dsl import AttrDict

from lib.elasticsearch.consts import ESDocType, ESIndex
from lib.geocoding.primitives import BoundingBox, Circle, Point, circle_to_geojson
from webapps.business.searchables.business import BusinessSearchable
from webapps.business.searchables.serializers import BusinessSearchHitSerializer
from webapps.business.searchables.tests.utils import create_business_with_params
from webapps.structure.enums import RegionType


def _get_traveling_data(
    latitude: float,
    longitude: float,
    radius: float = 10000,  # in m
) -> dict:
    return dict(
        location=dict(lat=latitude, lon=longitude),
        area=circle_to_geojson(Circle(latitude, longitude, radius)),
    )


# Tampa is 27.994948, -82.5033115235167
# Orlando is 28.51931938,-81.35133392, 127 km from Tampa
BUSINESS_DATA = [
    {
        'id': 1,
        'name': '<PERSON>',
        'categories': [1, 2],
        'primary_category': 2,
        'treatments': [12],
        'location': (28.0787, -82.4600),  # 10.23 km from Tampa
        'region': 4,
    },
    {
        'id': 2,
        'name': '<PERSON>a <PERSON>quori',
        'categories': [1, 2],
        'primary_category': 1,
        'treatments': [12],
        'location': (27.9846, -82.4888),  # 1.83 km from Tampa
        'region': 4,
    },
    {
        'id': 3,
        'name': 'Barber Masterpiece',
        'categories': [3],
        'primary_category': 3,
        'treatments': [10],
        'location': (27.9755, -82.4662),  # 4.23 km from Tampa
        'region': 4,
    },
    {
        'id': 4,
        'name': 'Nail Art',
        'categories': [3, 1],
        'primary_category': 3,
        'treatments': [10],
        'location': (28.5153, -81.3637),  # 1.29 from Orlando
        'region': 5,
    },
    {
        'id': 5,
        'name': 'Christina Stylist',
        'categories': [4],
        'primary_category': 4,
        'treatments': [11],
        'location': (28.5104, -81.3757),  # 2.62 km from Orlando
        'region': 5,
    },
    {
        'id': 6,
        'name': 'Ace King',
        'categories': [5, 6],
        'primary_category': 6,
        'treatments': [11],
        'location': (28.5778, -81.2911),  # 8.76 km from Orlando
        'region': 5,
    },
    {
        'id': 7,
        'name': 'House of Beauty',
        'categories': [7, 8],
        'primary_category': 8,
        'treatments': [12],
        'location': (28.5779, -81.2911),  # 8.77 km from Orlando
        'region': 5,
    },
    {
        'id': 8,
        'name': 'Nyno Florist',
        'categories': [8],
        'primary_category': 8,
        'treatments': [13],
        'location': (28.5781, -81.2911),  # 8.79 km from Orlando
        'region': 5,
    },
    {
        'id': 9,
        'name': 'Ace King',
        'categories': [9],
        'primary_category': 9,
        'treatments': [19],
        'location': (28.5778, -81.2911),  # 8.76 km from Orlando
        'region': 5,
        'traveling': _get_traveling_data(28.5778, -81.2911),
    },
    {
        'id': 10,
        'name': 'Ace King',
        'categories': [9],
        'primary_category': 9,
        'treatments': [19],
        'location': (39.820223, -98.6673045),
        'region': 6,
    },
]


@pytest.fixture(scope='module')
def business_data(clean_index_module_fixture):
    index = clean_index_module_fixture(ESIndex.BUSINESS)

    for data in BUSINESS_DATA:
        es_data = dict(
            id=data['id'],
            name=data['name'],
            business_categories=[dict(id=i) for i in data['categories']],
            business_primary_category=dict(id=data['primary_category']),
            treatments=[dict(id=i) for i in data['treatments']],
            business_location=dict(
                coordinate=dict(
                    lat=data['location'][0],
                    lon=data['location'][1],
                ),
            ),
            regions=[dict(id=data['region'])],
            traveling=data.get('traveling', None),
        )
        create_business_with_params(es_data)

    index.refresh()


@pytest.mark.usefixtures('business_data')
@pytest.mark.parametrize(
    'data, results',
    [
        (dict(query='nyno'), [8]),
        (dict(category=[1]), [2, 1, 4]),
        (dict(category=[6, 8]), [6, 7, 8]),
        (dict(category=[1], treatment=[12]), [1, 2]),
        (dict(_location_geo_obj=Point(27.9949, -82.5033)), [2]),  # Tampa
        (dict(_location_geo_obj=Point(28.5193, -81.3513)), [4, 5, 9]),  # Orlando
    ],
)
def test_business_filter(data, results):
    # disable availability logic
    data['disable_implicit_availability'] = True
    data['distance_radius'] = settings.ES_DEFAULT_RADIUS  # 3000: from conftest
    searchable = BusinessSearchable(
        ESDocType.BUSINESS,
        serializer=BusinessSearchHitSerializer,
    )
    resp = searchable.execute(data)

    assert {r.id for r in resp} == set(results), f'Data: {data}'


@pytest.mark.usefixtures('business_data')
@pytest.mark.parametrize(
    'data, rings',
    [
        # Center ~27.9949, ~-82.5033
        (dict(_area_obj=BoundingBox(28.09001, -82.674031, 27.896869, -82.32883)), [1, 1, 0]),
        (
            dict(
                _region_obj=AttrDict(
                    dict(latitude=27.9949, longitude=-82.5033, type=RegionType.CITY)
                )
            ),
            [1, 1, 0],
        ),
        (
            dict(
                _location_geo_obj=Point(27.9949, -82.5033),
                _region_obj=AttrDict(dict(latitude=0.0, longitude=0.0, type=RegionType.CITY)),
            ),
            [1, 1, 0],
        ),
        (
            dict(
                _location_geo_obj=Point(27.9949, -82.5033),
                _area_obj=BoundingBox(0.0, 0.0, 0.0, 0.0),
            ),
            [1, 1, 0],
        ),
        (
            dict(
                _location_geo_obj=Point(27.9949, -82.5033),
                _region_obj=AttrDict(dict(latitude=0.0, longitude=0.0, type=RegionType.CITY)),
                treatment=[12],
            ),
            [1, 0, 0],
        ),
        # Center ~39.828127, ~-98.579404
        (
            dict(
                _region_obj=AttrDict(
                    dict(latitude=39.828127, longitude=-98.579404, type=RegionType.CITY)
                )
            ),
            [0, 1, 0],
        ),
        (
            dict(
                _location_geo_obj=Point(39.828127, -98.579404),
                _region_obj=AttrDict(dict(latitude=0.0, longitude=0.0, type=RegionType.CITY)),
            ),
            [0, 1, 0],
        ),
    ],
)
@override_settings(ES_DISTANCE_RADIUS_CHOICES=(10000, 8000, 3000))
def test_geo_distance_rings(data, rings):  # pylint: disable=redefined-outer-name
    # disable availability logic
    data['disable_implicit_availability'] = True

    searchable = BusinessSearchable(
        ESDocType.BUSINESS,
        serializer=BusinessSearchHitSerializer,
    )
    resp = searchable.execute(data)

    ring_buckets = resp.aggregations.rings.geo_distance.buckets
    assert [r.doc_count for r in ring_buckets] == rings, f'Data: {data}'


@pytest.mark.parametrize(
    'data, sort',
    [
        (
            dict(sort_order='name'),
            [
                {'name.sort': 'asc'},
                {'_score': 'desc'},
            ],
        ),
        (
            dict(sort_order='price'),
            [
                {
                    'service_categories.services.variants.price': {
                        'order': 'asc',
                        'mode': 'min',
                        'nested': {
                            'path': 'service_categories',
                            'nested': {
                                'path': 'service_categories.services',
                                'nested': {'path': 'service_categories.services.variants'},
                            },
                        },
                    }
                },
                {'_score': 'desc'},
                {'name.sort': 'asc'},
            ],
        ),
        (
            dict(sort_order='distance', location_geo='location_geo'),
            [
                {'_geo_distance': {'business_location.coordinate': 'location_geo', 'order': 'asc'}},
                {'_score': 'desc'},
                {'name.sort': 'asc'},
            ],
        ),
        (
            dict(location_viewport='viewport', location_viewport_point='point'),
            [
                '_score',
                dict(
                    _geo_distance={
                        'business_location.coordinate': 'point',
                        'order': 'asc',
                    }
                ),
            ],
        ),
    ],
)
def test_business_sort_searchable(data, sort):
    searchable = BusinessSearchable(
        ESDocType.BUSINESS,
        serializer=BusinessSearchHitSerializer,
    )
    # print(searchable.to_dict(data)['sort'])
    assert searchable.to_dict(data)['sort'] == sort
