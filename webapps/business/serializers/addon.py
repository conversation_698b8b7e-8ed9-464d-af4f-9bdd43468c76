# pylint: disable=duplicate-code
from django.db import transaction
from django.db.models import Prefetch
from django.utils.translation import gettext as _
from rest_framework import serializers

from lib.serializers import DurationField, PaginatorSerializer
from webapps.business.enums import PriceType
from webapps.business.models import (
    ServiceAddOn,
    Service,
    ServiceVariant,
)
from webapps.business.serializers import ServiceRelatedField
from webapps.business.serializers.fields import ServicePriceField
from webapps.photo.models import Photo
from webapps.pos.models import TaxRate

# Tr.Row.quantity has PositiveSmallIntegerField, so max is 32767
MAX_ALLOWED_QUANTITY = 32767


class GetRequestSerializer(PaginatorSerializer):
    query = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    combos_with_addons = serializers.BooleanField(required=False, default=False)


class ServiceAddOnSerializer(serializers.ModelSerializer):
    duration = DurationField()
    business_id = serializers.IntegerField(write_only=True, required=False)
    max_allowed_quantity = serializers.IntegerField(
        default=MAX_ALLOWED_QUANTITY,
        max_value=MAX_ALLOWED_QUANTITY,
    )
    services = ServiceRelatedField(queryset=Service.objects.all())

    class Meta:
        model = ServiceAddOn
        fields = (
            'id',
            'business_id',
            'name',
            'price',
            'price_type',
            'price_description',
            'tax_rate',
            'duration',
            'photo',
            'services',
            'order',
            'is_available_for_customer_booking',
            'max_allowed_quantity',
        )

    def validate_tax_rate(self, value):
        if value:
            tax_rate_object = TaxRate.objects.filter(
                rate=value,
                pos=self.context['pos'],
            ).first()
            if not tax_rate_object:
                raise serializers.ValidationError(_("POS doesn't have passed tax rate for addon"))
            return tax_rate_object.rate
        return value

    def validate(self, attrs):
        data = super().validate(attrs)
        if 'tax_rate' not in data:
            tax_rate_object = TaxRate.objects.filter(
                pos=self.context['pos'],
                default_for_service=True,
            ).first()
            if not tax_rate_object:
                raise serializers.ValidationError(_("POS doesn't have default tax rate for addon"))
            data['tax_rate'] = tax_rate_object.rate

        price_type = data.get('price_type', None)
        price = data.get('price', None)
        data['price'] = self._get_price_validated_with_type(price, price_type)

        return data

    @staticmethod
    def _get_price_validated_with_type(price, price_type):
        empty_values = (None, '')

        if price_type in PriceType.has_price():
            if price in empty_values:
                raise serializers.ValidationError({'price': _('Price is required')})
            if price <= 0:
                raise serializers.ValidationError({'price': _('Invalid price')})
        else:
            if price not in empty_values:
                raise serializers.ValidationError({'price': _('Price is not allowed')})
            price = None

        return price


class ServiceAddOnPhotoSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField()
    url = serializers.CharField(source='full_url')

    class Meta:
        model = Photo
        fields = (
            'id',
            'url',
        )


class ServiceAddOnRepresentationSerializer(ServiceAddOnSerializer):
    duration = DurationField()
    business_id = serializers.IntegerField(write_only=True, required=False)
    photo = ServiceAddOnPhotoSerializer(required=False)
    max_allowed_quantity = serializers.IntegerField(
        default=MAX_ALLOWED_QUANTITY,
        max_value=MAX_ALLOWED_QUANTITY,
    )
    service_price = ServicePriceField()
    service_variants = serializers.SerializerMethodField()

    class Meta:
        model = ServiceAddOn
        fields = (
            'id',
            'business_id',
            'name',
            'price',
            'price_type',
            'price_description',
            'tax_rate',
            'duration',
            'photo',
            'services',
            'is_available_for_customer_booking',
            'max_allowed_quantity',
            'service_price',
            'service_variants',
        )

    @classmethod
    def get_prefetches(cls):
        return [
            Prefetch(
                'services',
                queryset=Service.objects.filter(
                    active=True,
                    combo_type__isnull=True,  # ensure we won't list combos
                )
                .only(
                    'id',
                )
                .prefetch_related(
                    Prefetch(
                        'service_variants',
                        queryset=ServiceVariant.objects.filter(
                            active=True,
                        ).only('id', 'service_id'),
                        to_attr='active_variants',  # overwrite cached_property
                    ),
                ),
                to_attr='_prefetched_active_services',
            ),
        ]

    def update(self, instance, validated_data):
        photo = validated_data.pop('photo', None)
        if photo and (photo['id'] != instance.photo_id):
            instance.photo_id = photo['id']

        self.update_services(instance, validated_data)

        return super().update(instance, validated_data)

    @transaction.atomic
    def update_services(self, instance, validated_data):
        received_services = validated_data.pop('services', [])
        current_services = set(instance.active_services_ids)

        removed_services = current_services - set(received_services)
        added_services = set(received_services) - current_services

        instance.services.add(*added_services)
        instance.services.remove(*removed_services)

    @staticmethod
    def get_service_variants(instance) -> list[int]:
        return list(
            ServiceVariant.objects.select_related('service')
            .only('id')
            .filter(service__in=instance.active_services)
            .values_list('id', flat=True)
        )
