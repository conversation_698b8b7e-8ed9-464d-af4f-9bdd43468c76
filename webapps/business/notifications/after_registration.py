from collections import defaultdict
from datetime import date, timedelta
from typing import List

from django.conf import settings
from django.db.models import Count, Q
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _

from lib.db import READ_ONLY_DB
from lib.sensi.sensidb import fetch_db_all
from lib.tools import tznow
from webapps.booking.models import Appointment
from webapps.business.models import Business
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business.notifications.common import TrialMessagesPushChannelMixin
from webapps.business.notifications.planner import AheadSchedulePlanner
from webapps.images.enums import ImageTypeEnum
from webapps.message_blast.enums import (
    MessageBlastInternalNames,
    MessageBlastTemplateStatus,
)
from webapps.message_blast.helpers import (
    resolve_template_for_business,
)
from webapps.message_blast.models import (
    MessageBlastTemplate,
)
from webapps.notification.base import (
    BaseNotification,
    PopupTemplate,
    PushTarget,
)
from webapps.notification.channels import (
    Popup<PERSON>hannel,
    push_from_popup,
)
from webapps.notification.enums import (
    NotificationCategory,
    NotificationGroup,
    NotificationIcon,
    NotificationSize,
    NotificationTarget,
)
from webapps.notification.recipients import Managers, SystemSender
from webapps.voucher.enums import VoucherType
from webapps.voucher.models import VoucherTemplate


__all__ = [
    'After3DaysPhotoSuggestion',
    'After10BookingsMessagingSuggestion',
    'After2WeeksMessagingSuggestion',
    'After3WeeksMessagingSuggestion',
    'Every4WeeksGiftCardsSuggestion',
    'Next7DaysBirthdaysSuggestion',
    'NoBookings30DaysMessagingSuggestion',
    'NoBookings90DaysMessagingSuggestion',
    'NoPortfolioSuggestion',
    'SecondDayOnTrialSuggestion',
    'SeventhDayOnTrialSuggestion',
]


class BusinessSuggestion(BaseNotification):
    category = NotificationCategory.RECOGNITION
    sender = SystemSender
    recipients = (Managers,)

    def __init__(self, instance):
        super().__init__(instance)
        self.business = instance

    @property
    def identity(self):
        return f'{self.notif_type},{self.business.id}'


class After3DaysPhotoSuggestion(BusinessSuggestion):
    channels = (PopupChannel,)
    schedule_planner = AheadSchedulePlanner
    popup_template = PopupTemplate(
        group=NotificationGroup.PROFILE,
        size=NotificationSize.NORMAL,
        icon=NotificationIcon.SUGGESTION,
        crucial=False,
        relevance=3,
        messages=[
            _('Did you know...'),
            _('Photos of your business can help you get discovered'),
            _('Start snapping! 📸'),
        ],
    )

    IMAGE_CATEGORIES = [
        ImageTypeEnum.BIZ_PHOTO,
    ]

    def get_target(self):
        return PushTarget(type='profile_photos')

    def should_skip_with_plea(self):
        skip, reason = super().should_skip_with_plea()
        if skip:
            return skip, reason

        if self.business.images.filter(category__in=self.IMAGE_CATEGORIES).exists():
            return True, 'Some images exists'
        return False, ''


class NoPortfolioSuggestion(BaseNotification):
    category = NotificationCategory.RECOGNITION
    sender = SystemSender
    recipients = (Managers,)
    channels = (PopupChannel,)
    popup_template = PopupTemplate(
        group=NotificationGroup.PROFILE,
        size=NotificationSize.NORMAL,
        icon=NotificationIcon.SUGGESTION,
        crucial=False,
        relevance=3,
        messages=[
            _('Did you know...'),
            _('Photos of your work can help you drive more client bookings!'),
            _('Upload photos and keep your portfolio fresh 📸'),
        ],
    )

    def __init__(self, instance, business_id=None):
        super().__init__(instance, business_id=business_id)
        self.business_id = business_id

    @cached_property
    def business(self):
        return Business.objects.get(pk=self.business_id)

    @property
    def identity(self):
        return f'{self.notif_type},{self.business_id},{date.today()}'

    def get_target(self):
        return PushTarget(type='portfolio')


def businesses_7_days_without_portfolio():
    return (
        Business.objects.filter(
            active=True,
            visible=True,
        )
        .exclude(
            images__category=ImageTypeEnum.INSPIRATION,
            images__created__gte=tznow() - timedelta(days=7),
        )
        .values_list('id', flat=True)
        .iterator()
    )


class After10BookingsMessagingSuggestion(BusinessSuggestion):
    channels = (PopupChannel,)
    popup_template = PopupTemplate(
        group=NotificationGroup.MESSAGE_BLAST,
        size=NotificationSize.NORMAL,
        icon=NotificationIcon.SUGGESTION,
        crucial=False,
        relevance=4,
        messages=[
            _("You've had 10 clients so far 👏"),
            _('Want to turn them into repeat customers?'),
            _('Turn on automated messages to remind them to book'),
        ],
    )

    def get_target(self):
        template = resolve_template_for_business(
            business_id=self.business.id,
            internal_name=MessageBlastInternalNames.WELCOME_NEW_CLIENT,
        )

        return PushTarget(
            type='blast_template',
            id=template.id,
        )

    def should_skip_with_plea(self):
        return skip_if_automated_messaging(self.business)


class After2WeeksMessagingSuggestion(BusinessSuggestion):
    channels = (PopupChannel,)
    schedule_planner = AheadSchedulePlanner
    popup_template = PopupTemplate(
        group=NotificationGroup.MESSAGE_BLAST,
        size=NotificationSize.BIG,
        icon=NotificationIcon.SUGGESTION,
        crucial=False,
        relevance=3,
        messages=[
            _("We're thinking of you. Are your clients? 🤔"),
            _('Be sure you stay top of mind'),
            _('Turn on automated message options!'),
        ],
    )

    def get_target(self):
        return PushTarget(type='blast_dashboard')

    def should_skip_with_plea(self):
        skip, reason = super().should_skip_with_plea()
        if skip:
            return skip, reason

        bci_count = self.business.business_customer_infos.count()
        if bci_count < 11:
            return True, f'{bci_count} clients: is not more than 10'

        return skip_if_automated_messaging(self.business)


class After3WeeksMessagingSuggestion(BusinessSuggestion):
    channels = (PopupChannel,)
    schedule_planner = AheadSchedulePlanner
    popup_template = PopupTemplate(
        group=NotificationGroup.MESSAGE_BLAST,
        size=NotificationSize.NORMAL,
        icon=NotificationIcon.SUGGESTION,
        crucial=False,
        relevance=4,
        messages=[
            _("Superb! {count} clients booked you so far 🙌"),
            _('But do they know everything you offer?'),
            _('Send a message to let them know!'),
        ],
    )

    def get_context(self):
        clients = (
            self.business.appointments.filter(
                booked_for__isnull=False,
            )
            .values('booked_for')
            .distinct()
            .count()
        )

        return {'count': clients}

    def get_target(self):
        template = resolve_template_for_business(
            business_id=self.business.id,
            internal_name=MessageBlastInternalNames.INFORM_ABOUT_OTHER_SERVICES,
        )

        return PushTarget(
            type='blast_template',
            id=template.id,
        )

    def should_skip_with_plea(self):
        skip, reason = super().should_skip_with_plea()
        if skip:
            return skip, reason

        count = self.context['count']
        if count < 11:
            return True, f'{count} clients: is not more than 10'
        return skip_if_automated_messaging(self.business)


class Every4WeeksGiftCardsSuggestion(BusinessSuggestion):
    channels = (PopupChannel,)
    schedule_planner = AheadSchedulePlanner
    popup_template = PopupTemplate(
        group=NotificationGroup.GIFT_CARDS,
        size=NotificationSize.NORMAL,
        icon=NotificationIcon.SUGGESTION,
        crucial=False,
        relevance=4,
        messages=[
            _("Gift Cards are a great way to generate new revenue."),
            _(
                "Set up your first Gift Card, and let your clients "
                "know they're available for purchase."
            ),
        ],
    )

    def get_target(self):
        return PushTarget(
            type=NotificationTarget.GIFT_CARDS.value,
        )

    def should_skip_with_plea(self):
        skip, reason = super().should_skip_with_plea()
        if skip:
            return skip, reason

        if self.already_enabled_gift_cards:
            return True, 'Business enabled gift cards during last 4 weeks'

        # Repeat notification periodically until condition is fulfilled
        self.__class__(self.business).schedule(days_ahead=28)
        return skip_if_automated_messaging(self.business)

    @property
    def already_enabled_gift_cards(self) -> bool:
        if not self.business.pos:
            return False

        return VoucherTemplate.objects.filter(
            pos=self.business.pos,
            type=VoucherType.EGIFT_CARD,
        ).exists()


class BusinesClientSuggestion(BaseNotification):
    category = NotificationCategory.RECOGNITION
    sender = SystemSender
    recipients = (Managers,)

    def __init__(self, instance, business_id=None, count=None):
        super().__init__(instance, business_id=business_id, count=count)
        self.business = Business.objects.get(pk=business_id)
        self.count = count

    def get_context(self):
        return {'count': self.count}

    @property
    def identity(self):
        today = self.business.tznow.date()
        return f'{self.notif_type},{self.business.id},{today}'


class NoBookings30DaysMessagingSuggestion(BusinesClientSuggestion):
    channels = (PopupChannel,)
    popup_template = PopupTemplate(
        group=NotificationGroup.MESSAGE_BLAST,
        size=NotificationSize.NORMAL,
        icon=NotificationIcon.SUGGESTION,
        crucial=False,
        relevance=4,
        messages=[
            _("{count} clients didn't book last month 😮"),
            _("Don't let them ghost you!"),
            _('Use automated messages to remind them to book'),
        ],
    )

    def get_target(self):
        template = resolve_template_for_business(
            business_id=self.business.id,
            internal_name=MessageBlastInternalNames.INVITE_FOR_VISIT,
        )

        return PushTarget(
            type='blast_template',
            id=template.id,
        )

    def should_skip_with_plea(self):
        return skip_if_automated_messaging(self.business)


class NoBookings90DaysMessagingSuggestion(BusinesClientSuggestion):
    channels = (PopupChannel,)
    popup_template = PopupTemplate(
        group=NotificationGroup.MESSAGE_BLAST,
        size=NotificationSize.NORMAL,
        icon=NotificationIcon.SUGGESTION,
        crucial=False,
        relevance=4,
        messages=[
            _("{count} clients haven't booked in 3 months 😮"),
            _("Don't let them slip away!"),
            _('Use automated messages to remind them to book'),
        ],
    )

    def get_target(self):
        template = resolve_template_for_business(
            business_id=self.business.id,
            internal_name=MessageBlastInternalNames.REINVITE_DISCOUNT,
        )

        return PushTarget(
            type='blast_template',
            id=template.id,
        )

    def should_skip_with_plea(self):
        return skip_if_automated_messaging(self.business)


class Next7DaysBirthdaysSuggestion(BusinesClientSuggestion):
    channels = (PopupChannel,)
    popup_template = PopupTemplate(
        group=NotificationGroup.MESSAGE_BLAST,
        size=NotificationSize.NORMAL,
        icon=NotificationIcon.SUGGESTION,
        crucial=False,
        relevance=4,
        messages=[
            _('Celebrate your clients! 🎉'),
            _('{count} of your clients have a birthday this week'),
            _('Wish them a Happy Birthday!'),
        ],
    )

    def get_target(self):
        template = resolve_template_for_business(
            business_id=self.business.id,
            internal_name=MessageBlastInternalNames.HAPPY_BIRTHDAY,
        )

        return PushTarget(
            type='blast_template',
            id=template.id,
        )

    def should_skip_with_plea(self):
        return skip_if_automated_messaging(self.business)


class TrialBusinessClientBaseSuggestion(TrialMessagesPushChannelMixin, BusinesClientSuggestion):
    target = NotificationTarget.SUBSCRIPTIONS

    def should_skip_with_plea(self):
        if self.business.status != Business.Status.TRIAL:
            return True, 'Business in not on Trial!'
        return skip_if_automated_messaging(self.business)


class SecondDayOnTrialSuggestion(TrialBusinessClientBaseSuggestion):
    schedule_planner = AheadSchedulePlanner
    popup_template = PopupTemplate(
        group=NotificationGroup.MESSAGE_BLAST,
        size=NotificationSize.NORMAL,
        icon=NotificationIcon.SUGGESTION,
        crucial=False,
        messages=[
            _('Important notice'),
            _('Welcome back! You’re still in trial mode'),
            _('Subscribe for more great features'),
        ],
    )
    push_template = push_from_popup(popup_template)


class SeventhDayOnTrialSuggestion(TrialBusinessClientBaseSuggestion):
    schedule_planner = AheadSchedulePlanner
    popup_template = PopupTemplate(
        group=NotificationGroup.MESSAGE_BLAST,
        size=NotificationSize.NORMAL,
        icon=NotificationIcon.SUGGESTION,
        crucial=False,
        messages=[
            _('Important notice'),
            _('You’re halfway through your trial'),
            _('Activate your account and let’s shift into 2nd gear!'),
        ],
    )
    push_template = push_from_popup(popup_template)


LAZY_CLIENTS = '''
WITH latest_booking AS (
    SELECT business_id, booked_for_id, max(booked_from) AS booked_from
        FROM booking_appointment
        -- limit bookings to latest half a year
        -- this excludes clients not active last half a year
        WHERE booked_from > now() - interval '180 days' AND
              status = %(finished_status)s AND
              booked_for_id IS NOT NULL
        GROUP BY business_id, booked_for_id
),
selected_businesses AS (
    SELECT business_id, count(*)
        FROM latest_booking
        WHERE booked_from < %(latest_booked)s
        GROUP BY business_id
)
SELECT * FROM selected_businesses
    WHERE count > %(count)s
;'''


def clients_without_bookings(days: int, clients: int) -> List:
    """Businesses with more than `clients` without booking last `days`

    :returns: List[(<business_id>, <clients_count>)]
    """
    params = {
        'finished_status': Appointment.STATUS.FINISHED,
        'latest_booked': tznow().date() - timedelta(days=days),
        'count': clients,
    }
    return fetch_db_all(LAZY_CLIENTS, params, db=READ_ONLY_DB)


def clients_with_birthday(days: int, clients: int) -> List:
    """Businesses with more than `clients` with birthday in next `days`
    :returns: List[(<business_id>, <clients_count>)]
    """
    today = tznow(settings.COUNTRY_CONFIG.default_time_zone).date()
    filters = _get_birthday_filter(today, days)

    return list(
        BusinessCustomerInfo.objects.filter(
            blacklisted=False,
            visible_in_business=True,
        )
        .filter(filters)
        .values_list('business')
        .annotate(count=Count('*'))
        .filter(count__gt=clients)
    )


def _get_birthday_filter(start_date: date, next_days: int):
    data = defaultdict(list)
    for i in range(0, next_days):
        day = start_date + timedelta(days=i)
        data[day.month].append(day.day)

    filters = Q()
    for month, days in data.items():
        filters.add(
            (Q(birthday__month=month) & Q(birthday__day__in=days))
            | (Q(user__birthday__month=month) & Q(user__birthday__day__in=days)),
            Q.OR,
        )
    return filters


def skip_if_automated_messaging(business):
    if MessageBlastTemplate.objects.filter(
        business=business, automated_status=MessageBlastTemplateStatus.ACTIVE
    ).exists():
        return True, 'Automated Messages already enabled'
    return False, ''
