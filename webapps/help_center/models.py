import typing
from django.conf import settings
from django.contrib.postgres.fields import <PERSON><PERSON><PERSON><PERSON><PERSON>
from django.db import models
from django.db.models import Q, F

from lib.models import ArchiveModel, ArchiveManager


class Category(ArchiveModel):
    class Meta(ArchiveModel.Meta):
        verbose_name_plural = 'Categories'

    parent = models.ForeignKey(
        'self',
        null=True,
        blank=True,
        on_delete=models.PROTECT,
        related_name='children',
    )
    path = ArrayField(
        models.IntegerField(),
        default=list,
        help_text='Field which contains path to current nested category.',
    )
    codename = models.CharField(
        max_length=50, help_text='Codename for better distinguish categories connections.'
    )
    order = models.IntegerField(null=False, blank=False, default=100)

    objects = ArchiveManager()

    def __str__(self):
        return f'ID: {self.id}, Codename: {self.codename}'

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

        new_path = []
        if self.parent:
            new_path = self.parent.path + [self.parent.id]

        if self.path != new_path:
            self.path = new_path
            super().save(update_fields=['path'])
            self._update_children_path()

    def _update_children_path(self):
        if children := self.children.all():
            for child in children:
                child.save()

    @classmethod
    def root_category_ids_with_localized_articles(cls, language: str) -> typing.Set[int]:
        root_category_ids_with_articles = (
            cls.objects.filter(
                contents__language=language,
                contents__deleted__isnull=True,
                articles__contents__language=language,
                articles__contents__deleted__isnull=True,
            )
            .annotate(root_category=F('path__0'))
            .values('root_category', 'id')
        )

        return set(c['root_category'] or c['id'] for c in root_category_ids_with_articles)


class CategoryContent(ArchiveModel):
    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['category', 'language'],
                condition=Q(deleted__isnull=True),
                name='unique_language_category_content',
            ),
        ]

    objects = ArchiveManager()

    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='contents')
    name = models.CharField(max_length=86)
    language = models.CharField(
        max_length=5,
        choices=settings.LANGUAGES,
        default=settings.LANGUAGE_CODE[:2],
    )

    def __str__(self):
        return f'ID: {self.id}, Name: {self.name}, Lang: {self.language}'


class Article(ArchiveModel):
    category = models.ForeignKey(Category, on_delete=models.PROTECT, related_name='articles')
    order = models.IntegerField(null=False, blank=False, default=100)
    related_articles = models.ManyToManyField('self', blank=True)

    objects = ArchiveManager()


class ArticleContent(ArchiveModel):
    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['article', 'language'],
                condition=Q(deleted__isnull=True),
                name='unique_language_article_content',
            ),
        ]

    objects = ArchiveManager()

    article = models.ForeignKey(Article, on_delete=models.CASCADE, related_name='contents')
    title = models.CharField(max_length=200)
    content = models.TextField()
    language = models.CharField(
        max_length=5,
        choices=settings.LANGUAGES,
        default=settings.LANGUAGE_CODE[:2],
    )

    def __str__(self):
        return f'ID: {self.id}, Title: {self.title}, Lang: {self.language}'
