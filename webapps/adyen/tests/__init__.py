import json
import logging

import responses
from django.conf import settings

from webapps.adyen.consts import error_type


logging.disable(logging.CRITICAL)


auth_success_data = {
    "pspReference": "7914643409856901",
    "resultCode": "Authorised",
    "authCode": "65496",
}

auth_three_d_secure_required_data = {
    "pspReference": "7914643409856901",
    "resultCode": "RedirectShopper",
    "authCode": "65466",
}

three_d_1_auth_success = {
    "additionalData": {"recurringProcessingModel": "CardOnFile"},
    "authCode": "064451",
    "pspReference": "****************",
    "resultCode": "Authorised",
}

three_d_1_auth_failure = {
    "additionalData": {"recurringProcessingModel": "CardOnFile"},
    "authCode": "064451",
    "pspReference": "****************",
    "resultCode": "Failed",
}


def mock_403(url):
    data = {
        "errorType": error_type.SECURITY,
        "errorCode": "901",
        "message": "Invalid Merchant Account",
        "status": 403,
    }
    responses.add(responses.POST, url, json.dumps(data), status=403)


def mock_auth_success():
    responses.add(
        responses.POST,
        settings.ADYEN_AUTH_URL,
        json=auth_success_data,
    )


def mock_auth_3ds_required():
    responses.add(
        responses.POST,
        settings.ADYEN_AUTH_URL,
        json=auth_three_d_secure_required_data,
    )


def mock_3ds1_response_success():
    responses.add(
        responses.POST,
        settings.ADYEN_3DS1_URL,
        json=three_d_1_auth_success,
    )


def mock_3ds1_response_failed():
    responses.add(
        responses.POST,
        settings.ADYEN_3DS1_URL,
        json=three_d_1_auth_failure,
        status=422,
    )


def mock_auth_fail():
    data = {
        "pspReference": "****************",
        "refusalReason": "CVC Declined",
        "resultCode": "Refused",
    }

    responses.add(responses.POST, settings.ADYEN_AUTH_URL, json=data)


def mock_capture_success():
    data = {"pspReference": "8614661758938972", "response": "[capture-received]"}
    responses.add(responses.POST, settings.ADYEN_CAPTURE_URL, json=data)


def mock_capture_fail():
    data = {
        "status": 422,
        "errorCode": "100",
        "message": "No amount specified",
        "errorType": "validation",
    }
    responses.add(
        responses.POST,
        settings.ADYEN_CAPTURE_URL,
        json=data,
        status=422,
    )


def mock_refund_success():
    data = {
        "pspReference": "7914664186207142",
        "response": "[cancelOrRefund-received]",
    }
    responses.add(
        responses.POST,
        settings.ADYEN_CANCEL_OR_REFUND_URL,
        json=data,
    )


def mock_refund_fail():
    data = {
        "status": 422,
        "errorCode": "167",
        "message": "Original pspReference required for this operation",
        "errorType": "validation",
    }
    responses.add(
        responses.POST,
        settings.ADYEN_CANCEL_OR_REFUND_URL,
        json=data,
        status=422,
    )


def mock_details_success():
    data = {
        'shopperReference': 'qwertyuioplkjhgfdswertyu',
        'creationDate': '2016-07-15T16:32:01+02:00',
        'details': [
            {
                'RecurringDetail': {
                    'additionalData': {'cardBin': '510008'},
                    'firstPspReference': '****************',
                    'recurringDetailReference': '****************',
                    'variant': 'bijcard',
                    'acquirer': 'TestPmmAcquirer',
                    'acquirerAccount': 'TestPmmAcquirerAccount',
                    'alias': 'C189971601011356',
                    'aliasType': 'Default',
                    'paymentMethodVariant': 'bijcard',
                    'creationDate': '2016-07-15T16:32:01+02:00',
                    'contractTypes': ['RECURRING'],
                    'card': {
                        'expiryYear': '2018',
                        'holderName': 'Adam Malysz',
                        'expiryMonth': '8',
                        'number': '3332',
                    },
                }
            },
            {
                'RecurringDetail': {
                    'additionalData': {'cardBin': '510029'},
                    'firstPspReference': '****************',
                    'recurringDetailReference': '****************',
                    'variant': 'mc',
                    'acquirer': 'TestPmmAcquirer',
                    'acquirerAccount': 'TestPmmAcquirerAccount',
                    'alias': 'H175512426350687',
                    'aliasType': 'Default',
                    'paymentMethodVariant': 'mc',
                    'creationDate': '2016-07-15T16:42:49+02:00',
                    'contractTypes': ['RECURRING'],
                    'card': {
                        'expiryYear': '2018',
                        'holderName': 'John Doe',
                        'expiryMonth': '8',
                        'number': '2909',
                    },
                }
            },
        ],
        'lastKnownShopperEmail': '<EMAIL>',
    }
    responses.add(
        responses.POST,
        settings.ADYEN_DETAILS_URL,
        json=data,
    )


def mock_details_empty():
    data = {
        'shopperReference': '3cc618ba389d471995f0168c849c6243',
        'creationDate': '2016-07-19T22:56:10+02:00',
        'lastKnownShopperEmail': '<EMAIL>',
    }
    responses.add(responses.POST, settings.ADYEN_DETAILS_URL, json=data)


def mock_details_fail():
    responses.add(responses.POST, settings.ADYEN_DETAILS_URL, body='')


def mock_disable_success():
    data = {
        'details': [
            {
                'RecurringDetail': {
                    'additionalData': {'cardBin': '510008'},
                    'firstPspReference': '****************',
                    'recurringDetailReference': '****************',
                    'variant': 'bijcard',
                    'acquirer': 'TestPmmAcquirer',
                    'acquirerAccount': 'TestPmmAcquirerAccount',
                    'alias': 'C189971601011356',
                    'aliasType': 'Default',
                    'paymentMethodVariant': 'bijcard',
                    'creationDate': '2016-07-19T17:05:30+02:00',
                    'contractTypes': ['RECURRING'],
                    'card': {
                        'expiryYear': '2018',
                        'holderName': 'Adam Malysz',
                        'expiryMonth': '8',
                        'number': '3332',
                    },
                }
            }
        ],
        'response': '[detail-successfully-disabled]',
    }
    responses.add(
        responses.POST,
        settings.ADYEN_DISABLE_URL,
        json=data,
    )


def mock_disable_fail():
    data = {
        'errorCode': '803',
        'errorType': 'validation',
        'message': 'PaymentDetail not found',
        'status': 422,
    }
    responses.add(
        responses.POST,
        settings.ADYEN_DISABLE_URL,
        json=data,
        status=422,
    )
