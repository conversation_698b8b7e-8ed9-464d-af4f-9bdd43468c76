# Generated by Django 3.1.8 on 2021-06-15 10:05

from django.db import migrations, models


CHOICES = [
    ('business_availability', 'Business Availability'),
    ('c2b_referral', 'C2B Referral'),
    ('double_subscriptions_report', 'Double subscriptions report'),
    ('google_reserve_live_updates', 'Google reserve live updates'),
    ('yelp_reserve_live_updates', 'Yelp reserve live updates'),
    ('groupon_reserve_live_updates', 'Groupon reserve live updates'),
    ('pipedrive_celery', 'Pipedrive celery updates'),
    ('slots_from_slave', 'Use Slave DB for time slots handler'),
    ('slave_booking_listing', 'Use Slave DB for customer bookings list handler'),
    ('booksy_auth', 'Booksy Auth layer is enabled'),
    ('slave_booking_details', 'Use Slave DB for booking details'),
    ('slave_calendar', 'Use Slave DB for business calendar'),
    ('slave_pos_trans_details', 'Use Slave DB for POS Transaction details '),
    ('replica_pos_transactions', 'Use Replica DB for POS Transactions'),
    ('replica_booking_notifications', 'Use Replica DB for Booking Notifications'),
    ('patterns', 'Use patterns experiment in book again'),
    ('patterns_push', 'Send patterns push'),
    ('patterns_popup', 'Send patterns popup'),
    ('near_availability', 'Add inner hits to real availability (LA badge)'),
    ('available_today', "Generates 'Available Today' gallery (MyBooksy)"),
    ('booking_reactivation', 'Adds booking reactivation actions (gallery, sms)'),
    ('rate_limit', 'Disable Rate limit check on every request'),
    ('aws_synchronous_upload', 'S3 sync upload'),
    ('sms_whitelist_enabled', 'Blocking SMSes to non-whitelisted countries'),
    ('donations_promo', 'Donations Promo'),
    ('utt2_backend', 'Enable operation included in UTT2 upgrade.'),
    ('utt2_experiment', 'Enable search using UTT2 based fields.'),
    ('booking_user_search_data', 'Synchronous calculate search data for new booking'),
    ('discount_after_pob', '1 free month of subscription after Payment Overdue Blocked period.'),
    ('replace_banned_twilio_number', 'Replace banned Twilio number'),
    ('iterable_email_service', 'Enable sending messages via Iterable.'),
    ('replica_feed_generator', 'Enable using replica db for RwG feed generator.'),
    ('replica_live_update_rwg_task', 'Enable using replica db for RwG RTUs.'),
    ('replica_yelp_feed_generation', 'Enable using replica db for Yelp feed generator.'),
    ('use_alternative_us_sms_service', 'Enable sending sms in US via evox without extra headers'),
    ('use_sms_with_deeplinks_enabled', "Enables customer booking sms's with deeplinks"),
    ('gtm_for_admin', 'Enable Google Tag Manager (GTM) for admin'),
    (
        'reports_logical_replication',
        'Stats and reports will be calculated using logical replica db. When killed queries are routed to physical replica.',
    ),
    (
        'claim_appointments_merge_bci',
        'Turn On/Off feature that let our customers claim/merge appointments/bci by deeplink.',
    ),
    ('business_registration_started', 'business_registration_started'),
    ('business_registration_completed', 'business_registration_completed'),
    ('customer_registration_completed', 'customer_registration_completed'),
    ('business_info_updated', 'business_info_updated'),
    ('business_categories_updated', 'business_categories_updated'),
    ('cb_created_for_business', 'cb_created_for_business'),
    ('cb_finished_for_business', 'cb_finished_for_business'),
    ('cb_finished_for_customer', 'cb_finished_for_customer'),
    ('cb_customer_info_updated', 'cb_customer_info_updated'),
    ('staffer_created', 'staffer_created'),
    ('user_language_set', 'user_language_set'),
    ('paid_status_achieved', 'paid_status_achieved'),
    ('review_completed', 'review_completed'),
    ('cb_created_for_customer', 'cb_created_for_customer'),
    ('boost_on_off', 'boost_on_off'),
    ('business_pos_updated', 'business_pos_updated'),
    ('business_subscription_updated', 'business_subscription_updated'),
    ('business_app_opened', 'business_app_opened'),
    ('business_status_updated', 'business_status_updated'),
    ('old_martech_analytics', 'Turn on/off old MarTech analytics'),
    ('martech_analytics', 'Turn on/off martech analytics'),
    ('3d secure enabled', '3D Secure enabled'),
    ('navision_integration', 'Turn on/off Navision integration'),
    ('navision_invoicing', 'Turn on/off Navision invoicing integration'),
    ('match_users_by_phone_number', 'Match users by phone number'),
    ('old_mail_setup_complete', 'Turn on/off sending setup complete email'),
    ('contact_preferences_updated', 'Customer contact preferences updated'),
    ('old_mail_account_added', 'Turn on/off sending account added email'),
    ('business_contact_pref_upd', 'Business contact preferences updated'),
    ('business_pba_enabled', 'Pay By App enabled'),
    ('billing_suspend_enabled', 'Billing Suspend Enabled'),
    ('old_mail_inactivity_login', 'Turn on/off sending inactivity login email'),
    ('iterable_user_email_sync', "Sync user's email in iterable when changed"),
    ('business_delay_set', 'Business Delay Set'),
    ('onboarding_business_go_live', 'Onboarding Business Go Live'),
    (
        'billing_migration_via_api',
        'Auto-migration merchants into new Billing through the API (frontdesk/mobile)',
    ),
]


def forward(apps, schema_editor):
    from webapps.kill_switch.utils import ensure_all_exist_with_type

    db_alias = schema_editor.connection.alias
    KillSwitch = apps.get_model("kill_switch", "KillSwitch")
    ensure_all_exist_with_type(
        ["billing_migration_via_api"],
        model=KillSwitch,
        names=[x[0] for x in CHOICES],
        using=db_alias,
    )


def revert(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    KillSwitch_model = apps.get_model("kill_switch", "KillSwitch")
    KillSwitch_model.objects.using(db_alias).filter(
        name__in=["billing_migration_via_api"],
    ).delete()


class Migration(migrations.Migration):

    dependencies = [
        ('kill_switch', '0063_auto_20210513_1113'),
    ]

    operations = [
        migrations.AlterField(
            model_name='killswitch',
            name='name',
            field=models.CharField(
                choices=CHOICES, max_length=32, primary_key=True, serialize=False
            ),
        ),
        migrations.RunPython(
            code=forward,
            reverse_code=revert,
        ),
    ]
