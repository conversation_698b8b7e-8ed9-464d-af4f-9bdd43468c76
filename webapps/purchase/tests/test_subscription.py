import datetime
import unittest
from decimal import Decimal

import pytest
import requests
from django.core import mail
from django.db.utils import IntegrityError
from django.test import (
    TestCase as DjangoTestCase,
    override_settings,
)
from freezegun import freeze_time
from mock import MagicMock, patch
from model_bakery import baker
from parameterized import parameterized
from pytz import UTC

from lib.baker_utils import get_or_create_booking_source
from lib.deeplink.deeplinks_ms.ports import generate_deeplink_ms
from lib.test_utils import spy_mock
from lib.tools import (
    relativedelta,
    tznow,
)
from webapps.booking.models import BookingSources
from webapps.business.models import Business
from webapps.business.utils import (
    business_freeze,
    business_unfreeze,
)
from webapps.consts import ANDROID_SOLO, FRONTDESK_IOS, IPHONE_SOLO
from webapps.purchase.admin import SubscriptionForm
from webapps.purchase.enums import PlanTypeEnum
from webapps.purchase.models import (
    AddOn,
    Promotion,
    SMSPackage,
    Subscription,
    SubscriptionBuyer,
    SubscriptionListing,
    SubscriptionOffer,
    SubscriptionSMSPackage,
    SubscriptionTransaction,
)
from webapps.purchase.serializers import (
    SubscriptionListingQuerySerializer,
    SubscriptionListingSerializer,
)
from webapps.purchase.tasks import InvoiceCreate
from webapps.purchase.tasks.segment import (
    SegmentStatusChange,
    get_kwargs_to_segment_status_task,
)
from webapps.purchase.tests.utils_test import (
    create_subscription_product,
    create_subscriptions,
)
from webapps.user.models import User


def raise_connection_error(*args, **kwargs):
    raise requests.exceptions.ConnectionError


@pytest.mark.freeze_time(datetime.datetime(2020, 3, 30))
@pytest.mark.django_db
class TestTransactionSerializer(unittest.TestCase):
    def setUp(self):
        self.business = baker.make(Business)

    def test_subscription_product_price(self):
        product = create_subscription_product({'price': 25})
        assert product.actual_price == 25
        assert product.discount_price == 25

    def test_subscription_product_discount_price(self):
        product = create_subscription_product(
            {
                'price': 25,
                'discount_amount': 10,
            }
        )
        assert product.actual_price == 25
        assert product.discount_price == 15

    def test_subscription_product_payment_time_in_months_price(self):
        product = create_subscription_product({'price': 120, 'payment_time_in_months': 12})
        assert product.actual_price == 10
        assert product.discount_price == 10

    def test_subscription_product_price_all(self):
        product = create_subscription_product(
            {'price': 120, 'discount_amount': 5, 'payment_time_in_months': 12}
        )
        assert product.actual_price == 10
        assert product.discount_price == 5

    def test_subscription_actual_price(self):
        product = create_subscription_product(
            {
                'price': 120,
                'discount_amount': 5,
                'payment_time_in_months': 12,
                'discount_duration': 1,
            }
        )
        now = tznow()
        subscription = create_subscriptions(
            self.business,
            product,
            {
                'start': now - relativedelta.relativedelta(months=1),
                'end': now + relativedelta.relativedelta(months=1),
            },
        )
        assert subscription.price_in_date(now) == 10
        assert subscription.price_in_date(now - relativedelta.relativedelta(days=2)) == 5


@patch('webapps.purchase.tasks.segment.get_segment_api')
@pytest.mark.django_db
def test_segment_status_change_active(get_segment_api_mock):
    business = baker.make(Business)
    product = baker.make(SubscriptionListing)

    segment_api = MagicMock()
    get_segment_api_mock.return_value = segment_api

    subscription_itunes = baker.make(
        Subscription,
        business=business,
        expiry=tznow() + datetime.timedelta(days=100),
        start=tznow() - datetime.timedelta(days=100),
        source=Business.PaymentSource.ITUNES,
        product=product,
    )

    SegmentStatusChange(**get_kwargs_to_segment_status_task(subscription_itunes))

    subscription_google_play = baker.make(
        Subscription,
        business=business,
        expiry=tznow() + datetime.timedelta(days=100),
        start=tznow() - datetime.timedelta(days=100),
        source=Business.PaymentSource.PLAY,
        product=product,
    )

    SegmentStatusChange(**get_kwargs_to_segment_status_task(subscription_google_play))

    subscription_instance_itunes = segment_api.subscription_purchased.call_args_list[0][0][0]
    subscription_instance_google_play = segment_api.subscription_purchased.call_args_list[1][0][0]

    assert get_segment_api_mock.call_count == 2
    assert subscription_instance_itunes.source == "I"
    assert subscription_instance_google_play.source == "P"


@patch('webapps.purchase.tasks.segment.get_segment_api')
@pytest.mark.django_db
def test_segment_status_change_expired(get_segment_api_mock):
    business = baker.make(Business)
    product = baker.make(SubscriptionListing)

    segment_api = MagicMock()
    get_segment_api_mock.return_value = segment_api

    subscription_itunes = baker.make(
        Subscription,
        business=business,
        expiry=tznow() - datetime.timedelta(days=10),
        start=tznow() - datetime.timedelta(days=100),
        source=Business.PaymentSource.ITUNES,
        product=product,
    )

    SegmentStatusChange(**get_kwargs_to_segment_status_task(subscription_itunes))

    subscription_google_play = baker.make(
        Subscription,
        business=business,
        expiry=tznow() - datetime.timedelta(days=5),
        start=tznow() - datetime.timedelta(days=100),
        source=Business.PaymentSource.PLAY,
        product=product,
    )

    SegmentStatusChange(**get_kwargs_to_segment_status_task(subscription_google_play))

    assert get_segment_api_mock.call_count == 0


@pytest.mark.django_db
@pytest.mark.parametrize(
    'states, result',
    [
        ((('C',), ('F', 'X', 'F')), False),
        ((('C',), ('F', 'X', 'X')), False),
        ((('F', 'X', 'F'), ('C',)), True),
        ((('F', 'X', 'X'), ('C',)), True),
        ((('C',),), True),
        ((('F',),), False),
        ((('X',),), False),
        ((('F', 'X', 'C'),), True),
        ((('C', 'X', 'F'),), False),
        ((('C', 'C', 'F'),), False),
        ((('F', 'X', 'F'),), False),
        ((('F', 'X', 'C'), ('C',)), False),
        ((('C', 'X', 'F'), ('C',)), False),
        ((('C', 'C', 'F'), ('C',)), False),
        ((('F', 'X', 'F'), ('C',)), True),
    ],
)
@patch('webapps.purchase.tasks.segment.get_segment_api', MagicMock())
def test_is_first_paid_cycle(states, result):
    business = baker.make(Business)
    product = baker.make(SubscriptionListing)
    sub = None

    for txn_states in states:
        sub = baker.make(
            Subscription,
            business=business,
            expiry=tznow() - datetime.timedelta(days=10),
            start=tznow() - datetime.timedelta(days=100),
            source=Business.PaymentSource.BRAINTREE,
            product=product,
        )

        SegmentStatusChange(**get_kwargs_to_segment_status_task(sub))

        for state in txn_states:
            SubscriptionTransaction.objects.create(
                subscription=sub,
                business=business,
                charged_on=tznow(),
                state=state,
            )

    assert sub.is_first_paid_cycle is result


@pytest.mark.django_db
@pytest.mark.parametrize(
    'sub_start_date_shift',
    [
        # already paying business
        -datetime.timedelta(weeks=1),
        -datetime.timedelta(days=1),
        -datetime.timedelta(seconds=1),
    ],
)
@patch('webapps.purchase.models.base.AppleGoogleSubscriptionEventMessage', MagicMock())
def test_has_business_ever_had_subscription__paid(sub_start_date_shift):
    business = baker.make('business.Business')
    sub_start_date = business.created + sub_start_date_shift
    with patch('webapps.purchase.tasks.segment.SegmentStatusChange.delay'):
        baker.make('purchase.Subscription', business=business, start=sub_start_date)
    assert Subscription.has_business_ever_had_subscription(business.id)


@patch('webapps.purchase.models.base.AppleGoogleSubscriptionEventMessage', MagicMock())
@patch('webapps.purchase.tasks.segment.SegmentStatusChange.delay')
@pytest.mark.django_db
def test_has_business_ever_had_subscription__trial(_segment_status_change_mock):
    # first case, already paying business
    business = baker.make('business.Business')
    sub_start_date = business.created - datetime.timedelta(weeks=1)
    baker.make('purchase.Subscription', business=business, start=sub_start_date)
    assert Subscription.has_business_ever_had_subscription(business.id)

    # second case, business with subscription start date in the future
    business = baker.make('business.Business')
    sub_start_date = business.created + datetime.timedelta(weeks=1)
    baker.make('purchase.Subscription', business=business, start=sub_start_date)
    assert not Subscription.has_business_ever_had_subscription(business.id)

    # third case, business without a subscription
    business = baker.make('business.Business')
    assert not Subscription.has_business_ever_had_subscription(business.id)


@patch('webapps.purchase.tasks.segment.get_segment_api', MagicMock())
@pytest.mark.django_db
class TestSubscription(unittest.TestCase):
    def setUp(self):
        super().setUp()

        self.now = tznow()
        self.business = baker.make(
            Business,
            active=True,
            owner=baker.make(User),
            payment_source=Business.PaymentSource.UNKNOWN,
        )
        self.product = baker.make(SubscriptionListing)
        self.subscription = baker.make(
            Subscription,
            business=self.business,
            source=Business.PaymentSource.OFFLINE,
            product=self.product,
            start=self.now,
            expiry=self.now + relativedelta.relativedelta(months=1),
        )

    def _create_transaction(self, state=SubscriptionTransaction.State.CHARGED):
        baker.make(
            SubscriptionTransaction,
            business=self.business,
            subscription=self.subscription,
            state=state,
            # charged_on it may lay be before `subscription.start` and
            # after `subscription.expiry`
            charged_on=tznow(),
        )
        if state == SubscriptionTransaction.State.CHARGED:
            self.subscription.expiry += datetime.timedelta(days=30)
            self.subscription.save()

    def test_subscription_can_be_saved_twice(self):
        self.subscription.save()
        self.subscription.save()

    def test_has_started(self):
        with freeze_time(self.subscription.start) as frozen_datetime:
            frozen_datetime.tick(relativedelta.relativedelta(seconds=-1))
            self.assertFalse(self.subscription.has_started())

            frozen_datetime.move_to(self.subscription.start)
            self.assertTrue(self.subscription.has_started())

            frozen_datetime.move_to(self.subscription.expiry)
            self.assertTrue(self.subscription.has_started())

    def test_has_expired(self):
        with freeze_time(self.subscription.start) as frozen_datetime:
            self.assertFalse(self.subscription.has_expired())

            frozen_datetime.move_to(self.subscription.expiry)
            self.assertTrue(self.subscription.has_expired())

            frozen_datetime.tick(relativedelta.relativedelta(seconds=1))
            self.assertTrue(self.subscription.has_expired())

        # check rare case when expiry is null,
        # seems there was such option but is not used anymore
        self.subscription.expiry = None

        with freeze_time(self.subscription.start) as frozen_datetime:
            self.assertFalse(self.subscription.has_expired())

            frozen_datetime.tick(relativedelta.relativedelta(days=90))
            self.assertFalse(self.subscription.has_expired())

    def test_active(self):
        subscription = self.subscription

        with freeze_time(subscription.start - datetime.timedelta(seconds=3)):
            self.assertFalse(subscription.active)

        with freeze_time(subscription.start):
            self.assertTrue(subscription.active)

        with freeze_time(subscription.expiry + datetime.timedelta(seconds=3)):
            self.assertFalse(subscription.active)

        subscription.start = None
        subscription.expiry = None
        self.assertFalse(subscription.active)

    def test_braintree(self):
        import braintree

        self.subscription.source = Business.PaymentSource.BRAINTREE

        self.subscription.receipt = {'id': '123', 'status': braintree.Subscription.Status.Pending}
        self.assertFalse(self.subscription.has_started())
        self.assertFalse(self.subscription.has_expired())
        self.assertFalse(self.subscription.is_paid())

        self.subscription.receipt = {'id': '123', 'status': braintree.Subscription.Status.Active}
        self.assertTrue(self.subscription.has_started())
        self.assertFalse(self.subscription.has_expired())
        self.assertTrue(self.subscription.is_paid())

        self.subscription.receipt = {'id': '123', 'status': braintree.Subscription.Status.PastDue}
        self.assertTrue(self.subscription.has_started())
        self.assertTrue(self.subscription.has_expired())
        self.assertFalse(self.subscription.is_paid())

        self.subscription.receipt = {'id': '123', 'status': braintree.Subscription.Status.Canceled}
        self.assertTrue(self.subscription.has_started())
        self.assertTrue(self.subscription.is_paid())
        self.assertFalse(
            self.subscription.has_expired(self.subscription.expiry - datetime.timedelta(hours=1))
        )
        self.assertTrue(
            self.subscription.has_expired(self.subscription.expiry + datetime.timedelta(hours=1))
        )

        self.subscription.receipt = {'id': '123', 'status': braintree.Subscription.Status.Expired}
        self.assertTrue(self.subscription.has_started())
        self.assertTrue(self.subscription.is_paid())
        self.assertFalse(
            self.subscription.has_expired(self.subscription.expiry - datetime.timedelta(hours=1))
        )
        self.assertTrue(
            self.subscription.has_expired(self.subscription.expiry + datetime.timedelta(hours=1))
        )

        self.subscription.receipt = {'id': '123', 'status': braintree.Subscription.Status.Canceled}
        self.subscription.expiry = None
        self.subscription.renewing = False
        self.assertFalse(
            self.subscription.has_started(self.subscription.start + datetime.timedelta(hours=1))
        )
        self.assertFalse(
            self.subscription.has_expired(self.subscription.start + datetime.timedelta(hours=1))
        )
        self.assertFalse(self.subscription.is_paid())

    def test_is_paid_offline(self):
        self.subscription.source = Business.PaymentSource.OFFLINE
        self.subscription.save()

        self.assertTrue(self.subscription.is_paid())

        with freeze_time(self.subscription.start) as frozen_datetime:

            def _add_transaction(state):
                self._create_transaction(state)

                self.subscription = Subscription.objects.get(id=self.subscription.id)
                self.assertTrue(self.subscription.is_paid())

                frozen_datetime.tick(relativedelta.relativedelta(seconds=1))

            _add_transaction(SubscriptionTransaction.State.FAILED)
            _add_transaction(SubscriptionTransaction.State.CANCELLED)
            _add_transaction(SubscriptionTransaction.State.CHARGED)

    def test_is_paid_online(self):
        self.subscription.source = Business.PaymentSource.ITUNES
        self.subscription.save()

        self.assertFalse(self.subscription.is_paid())

        with freeze_time(self.subscription.start) as frozen_datetime:

            def _add_transaction(state, expected_is_paid):
                self._create_transaction(state)

                self.subscription = Subscription.objects.get(id=self.subscription.id)
                self.assertIs(self.subscription.is_paid(), expected_is_paid)

                frozen_datetime.tick(relativedelta.relativedelta(seconds=1))

            _add_transaction(SubscriptionTransaction.State.FAILED, False)

            # CANCELLED state means user was charged at one point
            _add_transaction(SubscriptionTransaction.State.CANCELLED, True)

            _add_transaction(SubscriptionTransaction.State.CHARGED, True)
            _add_transaction(SubscriptionTransaction.State.CANCELLED, True)
            _add_transaction(SubscriptionTransaction.State.FAILED, False)

    def test_change_business_payment_source_offline(self):
        self.business.payment_source = Business.PaymentSource.UNKNOWN
        self.business.save()

        subscription = Subscription(
            business=self.business,
            start=self.now,
            expiry=self.now + datetime.timedelta(days=30),
            source=Business.PaymentSource.OFFLINE,
        )
        subscription.save()

        self.business.refresh_from_db(fields=['payment_source'])
        self.assertEqual(self.business.payment_source, Business.PaymentSource.OFFLINE)

    def test_change_business_payment_source_online(self):
        self.business.payment_source = Business.PaymentSource.BRAINTREE
        self.business.save()

        subscription = Subscription(
            business=self.business,
            start=self.now,
            expiry=self.now + datetime.timedelta(days=30),
        )
        subscription.save()

        self.business.refresh_from_db(fields=['payment_source'])
        self.assertEqual(self.business.payment_source, Business.PaymentSource.BRAINTREE)

    def test_sms_package_assigned(self):
        add_on_type = baker.make(
            AddOn,
            renewing=True,
            external_id='recurring_add_on',
            add_on_type=AddOn.AddOnType.SMS,
            source=Business.PaymentSource.BRAINTREE,
            price_amount=10,
            currency_code='GBP',
        )
        sms_package = baker.make(
            SMSPackage,
            add_on=add_on_type,
            amount_per_billing_cycle=49,
            chargeable=True,
        )
        self.product.sms_package = sms_package
        self.product.save()
        subscription = baker.make(
            Subscription,
            business_id=self.business.id,
            product_id=self.product.id,
        )
        self.assertEqual(SubscriptionSMSPackage.objects.count(), 1)
        business_sms_package = SubscriptionSMSPackage.objects.filter(
            subscription_id=subscription.id,
            package_id=sms_package.id,
        ).first()
        self.assertEqual(business_sms_package.date_start, subscription.start)
        self.assertIsNone(business_sms_package.date_end)
        self.assertEqual(business_sms_package.amount_per_billing_cycle, 49)

        # SMS package is assigned only at subscription creation
        subscription.save()
        self.assertEqual(SubscriptionSMSPackage.objects.count(), 1)


@pytest.mark.django_db
class TestInvoice(DjangoTestCase):
    def setUp(self):
        self.business = baker.make(Business)
        self.product = baker.make(SubscriptionListing)
        self.subscription = baker.make(
            Subscription,
            business=self.business,
            product=self.product,
            source=Business.PaymentSource.BRAINTREE,
        )

    @patch.object(InvoiceCreate, 'apply_async')
    def test_send(self, apply_async_mock):
        with override_settings(LIVE_DEPLOYMENT=True):  # to send mock email
            transaction = SubscriptionTransaction(
                subscription=self.subscription,
                business=self.business,
                charged_on=tznow(),
                state=SubscriptionTransaction.State.CHARGED,
            )
            transaction.save()

            self.assertEqual(apply_async_mock.call_count, 0)

            with patch('braintree.Customer'):
                InvoiceCreate.run(transaction.id)

            expected_subject = (
                f'INVOICE for your Booksy account number: '
                f'#{self.business.country_code}-{self.business.id}'
            )
            self.assertEqual(len(mail.outbox), 1)
            self.assertEqual(mail.outbox[0].subject, expected_subject)

    @patch.object(InvoiceCreate, 'apply_async')
    @patch('webapps.purchase.models.Invoice.objects.create')
    def test_get_or_create_with_error(self, create_mock, apply_async_mock):
        create_mock.side_effect = IntegrityError()

        with override_settings(LIVE_DEPLOYMENT=True):
            transaction = SubscriptionTransaction(
                subscription=self.subscription,
                business=self.business,
                charged_on=tznow(),
                state=SubscriptionTransaction.State.CHARGED,
            )
            transaction.save()

            with patch('braintree.Customer'):
                InvoiceCreate.run(transaction.id)

            self.assertEqual(apply_async_mock.call_count, 1)
            self.assertEqual(len(mail.outbox), 0)


@pytest.mark.django_db
class TestSubscriptionOffer(DjangoTestCase):
    # fixtures = ['deeplink_request_mock']

    def setUp(self):
        self.now = tznow()
        self.product_1 = baker.make(SubscriptionListing, name='product-1', active=True)
        self.product_2 = baker.make(SubscriptionListing, name='product-2', active=True)
        self.product_3 = baker.make(SubscriptionListing, name='product-3', active=False)
        self.product_4 = baker.make(SubscriptionListing, name='product-4', active=True)
        self.business = baker.make(Business)

        self.offer = SubscriptionOffer.objects.create(
            business=self.business,
            valid_from=self.now,
            valid_to=self.now + datetime.timedelta(days=3),
            # hardcode deeplink so it's not generated in every test
            deeplink='https://dl.example.com',
        )
        self.offer.products.add(self.product_1, self.product_2, self.product_3)

    def test_deeplink(self):
        spy__generate_deeplink = spy_mock(generate_deeplink_ms)
        with patch('lib.deeplink.adapters.generate_deeplink_ms', spy__generate_deeplink):
            offer = SubscriptionOffer(
                business=self.business,
                valid_from=self.now,
                valid_to=self.now + datetime.timedelta(days=3),
            )
            offer.save()

        self.assertIsNotNone(offer.deeplink)
        self.assertEqual(1, spy__generate_deeplink.mock.call_count)

        # expect new deeplink on business change
        with patch('lib.deeplink.adapters.generate_deeplink_ms', spy__generate_deeplink):
            offer.business = baker.make(Business)
            offer.save()
        self.assertIsNotNone(offer.deeplink)
        self.assertEqual(2, spy__generate_deeplink.mock.call_count)

    def _test_display_offer_success(self, offer):
        serializer = SubscriptionListingQuerySerializer(
            data={
                'offer_id': offer.id,
                'available': True,
                'for_staffer_num': False,
            },
            context={
                'business_id': self.business.id,
            },
        )
        qs = SubscriptionListing.objects.all()
        qs, warnings = serializer.filter_queryset(qs, self.business.id)

        self.assertNotIn('offer_id', warnings)
        self.assertQuerysetEqual(
            qs,
            ['product-1', 'product-2', 'product-3'],  # all from offer
            transform=lambda obj: obj.name,
            ordered=False,
        )

    def _test_display_offer_failure(self, offer):
        serializer = SubscriptionListingQuerySerializer(
            data={
                'offer_id': offer.id,
                'available': True,
                'for_staffer_num': False,
            },
            context={
                'business_id': self.business.id,
            },
        )
        qs = SubscriptionListing.objects.all()
        qs, warnings = serializer.filter_queryset(qs, self.business.id)

        self.assertIn('offer_id', warnings)
        self.assertQuerysetEqual(
            qs,
            ['product-1', 'product-2', 'product-4'],  # all active
            transform=lambda obj: obj.name,
            ordered=False,
        )

    def test_display(self):
        self._test_display_offer_success(self.offer)

    def test_display_pending(self):
        """
        Check whether offer can't be bought before it becomes valid
        """
        with freeze_time(self.offer.valid_from - datetime.timedelta(seconds=1)):
            self._test_display_offer_failure(self.offer)

    def test_display_expired(self):
        """
        Check whether offer can't be bought after it expires
        """
        with freeze_time(self.offer.valid_to + datetime.timedelta(seconds=1)):
            self._test_display_offer_failure(self.offer)

    def test_display_archived(self):
        self.offer.delete()

        self._test_display_offer_failure(self.offer)

    def test_filter_ios_tablet(self):
        self.product_1.plan_type = PlanTypeEnum.TABLET
        self.product_1.save()

        self.product_2.plan_type = PlanTypeEnum.MOBILE
        self.product_2.save()

        ios_frontdesk_source = get_or_create_booking_source(
            app_type=BookingSources.BUSINESS_APP,
            name=FRONTDESK_IOS,
        )

        serializer = SubscriptionListingQuerySerializer(
            data={},
            context={
                'booking_source': ios_frontdesk_source,
                'business_id': self.business.id,
            },
        )
        qs = SubscriptionListing.objects.all()
        qs, _warnings = serializer.filter_queryset(qs, self.business.id)
        self.assertQuerysetEqual(qs, ['product-1'], transform=lambda obj: obj.name, ordered=False)

    def test_filter_ios_mobile(self):
        self.product_1.plan_type = PlanTypeEnum.TABLET
        self.product_1.save()

        self.product_2.plan_type = PlanTypeEnum.MOBILE
        self.product_2.save()

        ios_mobile_source = get_or_create_booking_source(
            app_type=BookingSources.BUSINESS_APP,
            name=IPHONE_SOLO,
        )

        serializer = SubscriptionListingQuerySerializer(
            data={},
            context={
                'booking_source': ios_mobile_source,
                'business_id': self.business.id,
            },
        )
        qs = SubscriptionListing.objects.all()
        qs, _warnings = serializer.filter_queryset(qs, self.business.id)
        self.assertQuerysetEqual(qs, ['product-2'], transform=lambda obj: obj.name, ordered=False)

    def test_filter_android(self):
        self.product_1.plan_type = PlanTypeEnum.TABLET
        self.product_1.save()

        self.product_2.plan_type = PlanTypeEnum.MOBILE
        self.product_2.save()

        android_source = get_or_create_booking_source(
            app_type=BookingSources.BUSINESS_APP,
            name=ANDROID_SOLO,
        )
        serializer = SubscriptionListingQuerySerializer(
            data={},
            context={
                'booking_source': android_source,
                'business_id': self.business.id,
            },
        )
        qs = SubscriptionListing.objects.all()
        qs, _warnings = serializer.filter_queryset(qs, self.business.id)
        self.assertQuerysetEqual(
            qs,
            ['product-1', 'product-2', 'product-3', 'product-4'],
            transform=lambda obj: obj.name,
            ordered=False,
        )

    def test_cross_device_validation(self):
        self.product_1.plan_type = PlanTypeEnum.TABLET
        self.product_1.save()

        ios_mobile_source = get_or_create_booking_source(
            app_type=BookingSources.BUSINESS_APP,
            name=IPHONE_SOLO,
        )

        baker.make(
            Subscription,
            business=self.business,
            product=self.product_1,
        )

        serializer = SubscriptionListingQuerySerializer(
            data={},
            context={
                'booking_source': ios_mobile_source,
                'business_id': self.business.id,
            },
        )

        assert serializer.is_valid() is False
        assert serializer.errors['non_field_errors'][0].code == 'cross_device'


@pytest.mark.django_db
class TestPromotion(unittest.TestCase):
    def setUp(self):
        self.now = tznow()

    def test_deeplink(self):
        spy__generate_deeplink = spy_mock(generate_deeplink_ms)
        with patch('lib.deeplink.adapters.generate_deeplink_ms', spy__generate_deeplink):
            promotion = Promotion(
                valid_from=self.now,
                valid_to=self.now + datetime.timedelta(days=3),
            )
            promotion.save()

        self.assertIsNotNone(promotion.deeplink)
        self.assertEqual(1, spy__generate_deeplink.mock.call_count)


@pytest.mark.django_db
@pytest.mark.freeze_time(datetime.datetime(2019, 5, 15, 12, tzinfo=UTC))
class TestOfflineSubscriptionsValidation(unittest.TestCase):
    def setUp(self):
        super().setUp()
        self.product = baker.make(SubscriptionListing)
        self.business = baker.make(
            Business, payment_source=Business.PaymentSource.OFFLINE, status=Business.Status.PAID
        )
        self.buyer = baker.make(SubscriptionBuyer)
        self.default_form_data = dict(
            business=self.business.id,
            buyer=self.buyer.id,
            product=self.product.id,
            receipt=dict(id=123),
            navision_invoicing_allowed=True,
        )

    def test_overlapping_ok_no_expiry(self):
        baker.make(
            Subscription,
            business=self.business,
            product=self.product,
            source=Business.PaymentSource.OFFLINE,
            start=datetime.datetime(2019, 1, 1, tzinfo=UTC),
        )
        form_data = dict(
            start=datetime.datetime(2018, 12, 1, tzinfo=UTC),
            expiry=datetime.datetime(2018, 12, 31, tzinfo=UTC),
        )
        form_data.update(self.default_form_data)
        form = SubscriptionForm(data=form_data)
        self.assertTrue(form.is_valid(), form.errors)

    def test_overlapping_ok_older(self):
        baker.make(
            Subscription,
            business=self.business,
            product=self.product,
            source=Business.PaymentSource.OFFLINE,
            start=datetime.datetime(2019, 1, 1, tzinfo=UTC),
            expiry=datetime.datetime(2019, 1, 31, tzinfo=UTC),
        )
        form_data = dict(
            start=datetime.datetime(2018, 12, 1, tzinfo=UTC),
            expiry=datetime.datetime(2018, 12, 31, tzinfo=UTC),
        )
        form_data.update(self.default_form_data)
        form = SubscriptionForm(data=form_data)
        self.assertTrue(form.is_valid(), form.errors)

    def test_overlapping_ok_newer(self):
        baker.make(
            Subscription,
            business=self.business,
            product=self.product,
            source=Business.PaymentSource.OFFLINE,
            start=datetime.datetime(2019, 1, 1, tzinfo=UTC),
            expiry=datetime.datetime(2019, 1, 31, tzinfo=UTC),
        )
        form_data = dict(
            start=datetime.datetime(2019, 12, 1, tzinfo=UTC),
            expiry=datetime.datetime(2019, 12, 31, tzinfo=UTC),
        )
        form_data.update(self.default_form_data)
        form = SubscriptionForm(data=form_data)
        self.assertTrue(form.is_valid(), form.errors)

    def test_overlapping_not_ok_large_interval(self):
        baker.make(
            Subscription,
            business=self.business,
            product=self.product,
            source=Business.PaymentSource.OFFLINE,
            start=datetime.datetime(2019, 1, 1, tzinfo=UTC),
            expiry=datetime.datetime(2019, 12, 31, tzinfo=UTC),
        )
        form_data = dict(
            start=datetime.datetime(2019, 2, 1, tzinfo=UTC),
            expiry=datetime.datetime(2019, 2, 28, tzinfo=UTC),
        )
        form_data.update(self.default_form_data)
        form = SubscriptionForm(data=form_data)
        self.assertFalse(form.is_valid())

    def test_overlapping_not_ok_left(self):
        baker.make(
            Subscription,
            business=self.business,
            product=self.product,
            source=Business.PaymentSource.OFFLINE,
            start=datetime.datetime(2019, 1, 1, tzinfo=UTC),
            expiry=datetime.datetime(2019, 12, 31, tzinfo=UTC),
        )
        form_data = dict(
            start=datetime.datetime(2018, 12, 1, tzinfo=UTC),
            expiry=datetime.datetime(2019, 2, 28, tzinfo=UTC),
        )
        form_data.update(self.default_form_data)
        form = SubscriptionForm(data=form_data)
        self.assertFalse(form.is_valid())

    def test_overlapping_not_ok_right(self):
        baker.make(
            Subscription,
            business=self.business,
            product=self.product,
            source=Business.PaymentSource.OFFLINE,
            start=datetime.datetime(2019, 1, 1, tzinfo=UTC),
            expiry=datetime.datetime(2019, 12, 31, tzinfo=UTC),
        )
        subscription = baker.make(
            Subscription,
            business=self.business,
            product=self.product,
            source=Business.PaymentSource.OFFLINE,
            start=datetime.datetime(2020, 12, 1, tzinfo=UTC),
            expiry=datetime.datetime(2020, 12, 31, tzinfo=UTC),
        )
        form_data = dict(
            start=datetime.datetime(2019, 12, 1, tzinfo=UTC),
            expiry=datetime.datetime(2019, 12, 31, tzinfo=UTC),
        )
        form_data.update(self.default_form_data)
        form = SubscriptionForm(instance=subscription, data=form_data)

        self.assertFalse(form.is_valid())
        self.assertIn(
            'New subscription date conflicts with the another one. '
            'Please change to date that does not collide.',
            form.errors['__all__'],
        )

    def test_overlapping_not_ok_middle(self):
        baker.make(
            Subscription,
            business=self.business,
            product=self.product,
            source=Business.PaymentSource.OFFLINE,
            start=datetime.datetime(2019, 1, 1, tzinfo=UTC),
            expiry=datetime.datetime(2019, 12, 31, tzinfo=UTC),
        )
        form_data = dict(
            start=datetime.datetime(2019, 2, 1, tzinfo=UTC),
            expiry=datetime.datetime(2019, 2, 28, tzinfo=UTC),
        )
        form_data.update(self.default_form_data)
        form = SubscriptionForm(data=form_data)
        self.assertFalse(form.is_valid())

    def test_business_has_new_billing(self):
        self.business.has_new_billing = True
        self.business.save()

        baker.make(
            Subscription,
            business=self.business,
            product=self.product,
            source=Business.PaymentSource.OFFLINE,
            start=datetime.datetime(2019, 1, 1, tzinfo=UTC),
        )
        form_data = dict(
            start=datetime.datetime(2018, 12, 1, tzinfo=UTC),
            expiry=datetime.datetime(2018, 12, 31, tzinfo=UTC),
        )
        form_data.update(self.default_form_data)

        form = SubscriptionForm(data=form_data)
        form.is_valid()
        self.assertFalse(form.is_valid())
        self.assertIn('uses new Billing', str(form.errors))

    def test_recurrence_have_to_be_larger_than_zero_if_any(self):
        baker.make(
            Subscription,
            business=self.business,
            product=self.product,
            source=Business.PaymentSource.OFFLINE,
            start=datetime.datetime(2019, 1, 1, tzinfo=UTC),
            expiry=datetime.datetime(2019, 1, 31, tzinfo=UTC),
        )
        form_data = dict(
            start=datetime.datetime(2018, 12, 1, tzinfo=UTC),
            expiry=datetime.datetime(2018, 12, 31, tzinfo=UTC),
            agreement_months=-1,
        )
        form_data.update(self.default_form_data)
        form = SubscriptionForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('agreement_months', form.errors)

    def test_recurrence_have_to_be_smaller_than_year_if_any(self):
        baker.make(
            Subscription,
            business=self.business,
            product=self.product,
            source=Business.PaymentSource.OFFLINE,
            start=datetime.datetime(2019, 1, 1, tzinfo=UTC),
            expiry=datetime.datetime(2019, 1, 31, tzinfo=UTC),
        )
        form_data = dict(
            start=datetime.datetime(2018, 12, 1, tzinfo=UTC),
            expiry=datetime.datetime(2018, 12, 31, tzinfo=UTC),
            agreement_months=13,
        )
        form_data.update(self.default_form_data)
        form = SubscriptionForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('agreement_months', form.errors)

    @patch('webapps.purchase.tasks.segment.SegmentStatusChange.delay')
    def test_invoicing_exclusion_is_mandatory_if_subscription_is_invoicing_excluded(
        self,
        _segment_status_change_mock,
    ):
        subscription = baker.make(
            Subscription,
            source=Business.PaymentSource.OFFLINE,
            navision_invoicing_allowed=True,
        )

        form_data = self.default_form_data

        form_data.update(
            navision_invoicing_allowed=False,
        )

        form = SubscriptionForm(instance=subscription, data=form_data)

        self.assertFalse(form.is_valid())
        self.assertEqual(
            form.errors['navision_invoicing_exclusion_reason'],
            ['Please provide exclusion reason'],
        )

    def test_invoicing_exclusion_is_never_mandatory_for_online(self):
        subscription = baker.make(
            Subscription,
            source=Business.PaymentSource.BRAINTREE,
            navision_invoicing_allowed=False,
        )

        form_data = self.default_form_data
        form_data.pop('navision_invoicing_allowed')
        form_data.update(
            start=datetime.datetime(2018, 12, 1, tzinfo=UTC),
            expiry=datetime.datetime(2018, 12, 31, tzinfo=UTC),
        )

        form = SubscriptionForm(instance=subscription, data=form_data)

        self.assertTrue(form.is_valid(), form.errors)
        self.assertEqual(form.errors, {})


@pytest.mark.django_db
@pytest.mark.freeze_time(datetime.datetime(2019, 5, 15, 12, tzinfo=UTC))
class TestFreeze(unittest.TestCase):
    def setUp(self):
        super().setUp()
        self.product = baker.make(SubscriptionListing)

    @parameterized.expand(
        (
            (True, False),
            (False, True),
        )
    )
    def test_freeze(self, has_new_billing, result):
        business = baker.make(
            Business,
            payment_source=Business.PaymentSource.OFFLINE,
            status=Business.Status.PAID,
            has_new_billing=has_new_billing,
        )
        subscription = baker.make(
            Subscription,
            business=business,
            product=self.product,
            source=Business.PaymentSource.OFFLINE,
            start=datetime.datetime(2019, 1, 1, tzinfo=UTC),
            expiry=datetime.datetime(2019, 12, 31, tzinfo=UTC),
        )
        success = business_freeze(business=business, operator=None, endpoint='test')
        self.assertEqual(success, result)
        if result:
            business = Business.objects.get(id=business.id)
            subscription = Subscription.objects.get(id=subscription.id)
            self.assertEqual(business.status, Business.Status.BLOCKED_OVERDUE)
            self.assertEqual(subscription.expiry, tznow())

    @parameterized.expand(
        (
            (True, False),
            (False, True),
        )
    )
    def test_unfreeze(self, has_new_billing, result):
        business = baker.make(
            Business,
            payment_source=Business.PaymentSource.OFFLINE,
            status=Business.Status.BLOCKED_OVERDUE,
            has_new_billing=has_new_billing,
        )
        subscription = baker.make(
            Subscription,
            business=business,
            product=self.product,
            source=Business.PaymentSource.OFFLINE,
            start=datetime.datetime(2019, 1, 1, tzinfo=UTC),
            expiry=datetime.datetime(2019, 2, 28, tzinfo=UTC),
        )

        success = business_unfreeze(business=business, operator=None, endpoint='test')
        self.assertEqual(success, result)
        if result:
            business = Business.objects.get(id=business.id)
            subscription = Subscription.objects.get(id=subscription.id)
            self.assertEqual(subscription.expiry, tznow() + relativedelta.relativedelta(years=20))
            self.assertEqual(business.status, Business.Status.PAID)
            self.assertEqual(business.paid_till, subscription.expiry)


class TestSubscriptionListingSerializer(unittest.TestCase):
    @patch('webapps.purchase.serializers.BSCT.prepare_staff_add_ons_for_sub_create')
    def test_price_to_pay(self, prepare_add_ons_mock):
        prepare_add_ons_mock.return_value = dict(
            new_recurring_add_on_amount=Decimal('20.00'),
            new_recurring_add_on_quantity=1,
        )
        product = SubscriptionListing(
            price_amount=Decimal('99.00'),
            charge_for_staffers=True,
        )
        serializer = SubscriptionListingSerializer(
            product,
            discount=Decimal('0.50'),
            discount_cycles=3,
            business_id=12345,
        )
        price_to_pay = serializer.data['price_to_pay']
        self.assertEqual(price_to_pay, Decimal('59.50'))
