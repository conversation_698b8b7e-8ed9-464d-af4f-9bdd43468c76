import dataclasses

from lib.events import EventSignal
from lib.payment_providers.enums import EntityType


@dataclasses.dataclass
class SubscriptionBuyerEntityTypeUpdatedEventBody:
    business_id: int
    entity_type: EntityType


subscription_buyer_entity_type_updated_event = EventSignal(
    # sends SubscriptionBuyerEntityTypeUpdatedEventBody
    event_type='subscription_buyer_entity_type_updated',
)
