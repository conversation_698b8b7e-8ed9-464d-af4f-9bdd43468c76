import collections
import csv
import datetime
import re
from copy import deepcopy
from decimal import Decimal, ROUND_HALF_UP
from io import BytesIO

import braintree
from dal import autocomplete
from dateutil.relativedelta import relativedelta
from django import forms
from django.conf import settings
from django.contrib import admin, messages
from django.contrib.admin import widgets
from django.core.exceptions import ObjectDoesNotExist
from django.core.validators import MinValueValidator
from django.db import transaction, models
from django.db.models import (
    Case,
    CharField,
    F,
    Q,
    Value,
    When,
    Count,
    BooleanField,
)
from django.forms.utils import ValidationError
from django.http import HttpResponse
from django.shortcuts import redirect, get_object_or_404
from django.template import loader
from django.templatetags.static import static
from django.urls import re_path as url
from django.urls.base import reverse
from django.urls.base import reverse as url_reverse
from django.utils.decorators import method_decorator
from django.utils.html import format_html_join, format_html
from django.utils.translation import gettext as _
from django.views.decorators.http import require_GET
from pytz import UTC

from country_config import Country
from lib.admin_helpers import (
    NoAddDelMixin,
    BaseModelAdmin,
    NoDelMixin,
    ReadOnlyFieldsMixin,
    admin_link,
    get_link_html,
    ReadOnlyTabular,
    LazyLoadInlineMixin,
    LazyLoadModelFormset,
)
from lib.booksy_sms import SMSCosts
from lib.deeplink import DeepLinkCache, SUBSCRIPTIONS
from lib.enums import StrChoicesEnum, SMSPaidEnum, SMSTypeEnum
from lib.feature_flag.feature.admin import ShowInvoiceAddressInBuyerAdminFlag
from lib.feature_flag.feature.navision import NavisionFixMerchantSyncIEFlag
from lib.tools import tznow, CacheOrEstimatePaginator
from webapps.admin_extra.consts import (
    ENTERPRISE_IMPORTER_ID,
)
from webapps.admin_extra.tools import export_as_csv_action
from webapps.admin_extra.widgets import AdminSplitDateTimeWithChoices
from webapps.billing.enums import TransactionStatus
from webapps.business.models import Business
from webapps.business.models.business_change import BusinessChange
from webapps.navision.utils import check_navision_operator_permission
from webapps.notification.models import (
    NotificationSMSStatistics,
)
from webapps.purchase.braintree.sms_charges import BraintreeSMSChargeTool
from webapps.purchase.forms import (
    PromotionAdminForm,
    SubscriptionOfferAdminForm,
)
from webapps.purchase.models import (
    AddOn,
    InvoiceAddress,
    is_currency,
    PAYMENT_TIME_IN_MONTHS_CHOICES,
    Promotion,
    SMSPackage,
    Subscription,
    SubscriptionAddOn,
    SubscriptionBuyer,
    SubscriptionDiscount,
    SubscriptionExternalInfo,
    SubscriptionListing,
    SubscriptionListingSwitch,
    SubscriptionOffer,
    SubscriptionSMSPackage,
    SubscriptionTransaction,
    Coaching,
    CoachingHistory,
)
from webapps.purchase.query_sets import (
    get_queryset_with_subscription_count_annotation,
)
from webapps.purchase.tasks import ComputeBusinessStatusTask
from webapps.purchase.tasks.apple import apple_receipt_decode_task
from webapps.purchase.utils import (
    adjust_billing_date,
)
from webapps.purchase.validators import AVAILABLE_PAYMENT_DUE_DAYS_LIST
from webapps.structure.enums import RegionType
from webapps.structure.models import Region
from webapps.user.groups import GroupName
from webapps.user.tools import get_user_from_django_request


def dt_fmt(dt):
    if dt is None:
        return ''

    return dt.astimezone(UTC).strftime('%B %d, %Y, %H:%M UTC')


class SubscriptionTransactionAdmin(BaseModelAdmin):
    list_display = (
        'id',
        'date',
        'state',
        'price',
        'product',
        'business_link',
        'external_id',
        'invoice_id',
    )
    list_filter = (
        'charged_on',
        'subscription__start',
        'subscription__expiry',
        'subscription__source',
        'subscription__product__name',
        'state',
    )
    preserve_filters = True

    ordering = ('-id',)

    search_fields = (
        '=id',
        '=subscription__id',
        '=subscription__business__id',
        'subscription__business__name',
        'subscription__business__owner__email',
        'external_id',
    )
    raw_id_fields = (
        'subscription',
        'business',
    )
    actions = ['export']
    date_hierarchy = 'charged_on'

    fields = (
        'subscription',
        'charged_on',
        'state',
        'price_with_discount',
        'external_id',
        'payment_method',
        'reference_month',
    )

    def has_change_permission(self, request, obj=None):
        OFFLINE = Business.PaymentSource.OFFLINE
        if obj is not None and obj.subscription.source == OFFLINE:
            return True

        return False

    def has_delete_permission(self, request, obj=None):
        OFFLINE = Business.PaymentSource.OFFLINE
        if obj is not None and obj.subscription.source == OFFLINE:
            return True

        return False

    def get_queryset(self, request):
        qs = super(SubscriptionTransactionAdmin, self).get_queryset(request)

        qs = qs.prefetch_related(
            'invoice',
            'subscription',
            'subscription__product',
        )
        qs = qs.filter(subscription__isnull=False)

        return qs

    def save_model(self, request, obj, form, change):
        if obj.subscription.source == Business.PaymentSource.OFFLINE:
            obj.save()

    def price(self, obj):
        return '%s %s' % (obj.price_with_discount, obj.get_currency_code())

    price.admin_order_field = 'price_with_discount'

    def date(self, obj):
        return dt_fmt(obj.charged_on)

    date.admin_order_field = 'charged_on'

    def invoice_id(self, obj):
        try:
            invoice_id = f'{settings.API_COUNTRY}-{obj.invoice.invoice_id}'
        except ObjectDoesNotExist:
            invoice_id = None
        return invoice_id

    invoice_id.short_description = 'Invoice ID'

    def product(self, obj):
        product = obj.subscription.product
        return format_html('<a href="{}">{}</a>', admin_link(product), product.name)

    product.short_description = 'Product'
    product.admin_order_field = 'subscription__product_id'

    def business_link(self, obj):
        biz = obj.subscription.business
        return biz.admin_id_link

    business_link.short_description = 'Business'
    business_link.admin_order_field = 'subscription__business_id'

    def export(self, request, queryset):
        fieldnames = collections.OrderedDict(
            [
                ('Transaction ID', 'id'),
                ('Business ID', 'subscription__business_id'),
                ('Subscription ID', 'subscription__id'),
                ('Date', 'charged_on'),
                ('State', 'state'),
                ('Price', 'price_with_discount'),
                ('Currency code', 'subscription__product__currency_code'),
                ('Product name', 'subscription__product__name'),
                ('Product ID', 'subscription__product__id'),
                ('Product Google ID', 'subscription__product__google_id'),
                ('Product Apple ID', 'subscription__product__apple_id'),
                ('Product Braintree ID', 'subscription__product__braintree_id'),
                ('External ID', 'external_id'),
            ]
        )
        # https://code.djangoproject.com/ticket/16735#comment:21
        queryset = (
            queryset.annotate(**{k: F(v) for k, v in list(fieldnames.items())})
            .annotate(
                State=Case(
                    output_field=CharField(),
                    *(
                        When(state=short, then=Value(display))
                        for short, display in SubscriptionTransaction.State.choices()
                    ),
                ),
            )
            .values(*list(fieldnames.keys()))
        )

        _buffer = BytesIO()
        writer = csv.DictWriter(_buffer, list(fieldnames.keys()))
        writer.writeheader()
        writer.writerows(queryset.iterator())

        response = HttpResponse(_buffer.getvalue(), content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="export.csv"'
        return response

    export.short_description = 'Export CSV'


class SubscriptionTransactionInline(admin.StackedInline):
    model = SubscriptionTransaction
    extra = 0
    can_delete = True
    show_change_link = True
    fields = (
        'charged_on',
        'state',
        'price_with_discount',
        'external_id',
        'transaction_type',
        'payment_method',
        'reference_month',
    )

    def has_delete_permission(self, request, obj=None):
        if obj is not None and obj.source == Business.PaymentSource.OFFLINE:
            return True

        return False


class SubscriptionTransactionReadOnlyInline(SubscriptionTransactionInline):
    fields = (
        'charged_on',
        'state',
        'price_with_discount',
        'external_id',
        'transaction_type',
    )
    readonly_fields = fields


class SubscriptionStatusFilter(admin.SimpleListFilter):
    title = 'Status'
    parameter_name = 'status'

    def lookups(self, request, model_admin):
        return (
            ('Inactive',) * 2,
            ('Active',) * 2,
            (braintree.Subscription.Status.Canceled,) * 2,
            (braintree.Subscription.Status.Expired,) * 2,
            (braintree.Subscription.Status.PastDue,) * 2,
            (braintree.Subscription.Status.Pending,) * 2,
        )

    def queryset(self, request, queryset):
        now = tznow()
        braintree_active = Q(
            source=Business.PaymentSource.BRAINTREE,
            receipt__status=braintree.Subscription.Status.Active,
        )
        other_active = ~Q(source=Business.PaymentSource.BRAINTREE) & Q(
            start__lte=now, expiry__gte=now
        )
        active = braintree_active | other_active

        if self.value() == 'Inactive':
            return queryset.filter(~active)
        elif self.value() == 'Active':
            return queryset.filter(active)
        elif self.value() is not None:
            return queryset.filter(
                source=Business.PaymentSource.BRAINTREE,
                receipt__status=self.value(),
            )


class SubscriptionCountFilter(admin.SimpleListFilter):
    SUBSCRIPTION_COUNT_ONE = 'one'
    SUBSCRIPTION_COUNT_MORE_THAN_ONE = 'more_than_one'

    title = 'Subscription Count'
    parameter_name = 'subscription_count'

    def lookups(self, request, model_admin):
        return (
            (self.__class__.SUBSCRIPTION_COUNT_ONE, 'One'),
            (self.__class__.SUBSCRIPTION_COUNT_MORE_THAN_ONE, 'More than one'),
        )

    def queryset(self, request, queryset):
        if self.value() == self.__class__.SUBSCRIPTION_COUNT_ONE:
            return queryset.filter(subscription_count=1)
        elif self.value() == self.__class__.SUBSCRIPTION_COUNT_MORE_THAN_ONE:
            return queryset.filter(subscription_count__gt=1)


class SubscriptionMethodFilter(admin.SimpleListFilter):
    title = 'Method'
    parameter_name = 'method'

    def lookups(self, request, model_admin):
        return (
            (None, _('Any')),
            ('braintree', 'Braintree'),
            ('apple', 'Apple Pay'),
            ('google', 'Google Pay'),
        )

    def queryset(self, request, queryset):
        if self.value() in ('braintree', 'apple', 'google'):
            queryset = queryset.exclude(**{'%s_id__exact' % self.value(): ''})
        return queryset


class SubscriptionForm(forms.ModelForm):
    class Meta:
        model = Subscription
        fields = '__all__'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['source'].initial = Business.PaymentSource.OFFLINE
        self.fields['source'].disabled = True
        self.fields['product'].label_from_instance = lambda x: x.name
        self.fields['product'].label = 'Product name'
        self.fields['renewing'].initial = True
        self.fields['buyer'].required = self.is_buyer_required

        self.fields['instalments'].initial = 1

        if 'start' in self.fields:
            self.fields['start'].initial = tznow()

        if 'business' in self.fields:
            queryset = Business.objects.normal_businesses()
            self.fields['business'].queryset = queryset

    def is_overlapping_with_any_offline(self) -> bool:
        start = self.cleaned_data.get('start') or self.instance.start
        expiry = self.cleaned_data.get('expiry') or self.instance.expiry
        business = self.cleaned_data.get('business') or self.instance.business

        if not start or not business:
            # Required fields will be validated later
            return

        if expiry:
            dates_query = Q(
                Q(start__lte=expiry, expiry__isnull=True) | Q(start__lte=expiry, expiry__gte=start)
            )
        else:
            dates_query = Q(expiry__isnull=True) | Q(expiry__gte=start)

        return (
            Subscription.objects.filter(
                dates_query, business=business, source=Business.PaymentSource.OFFLINE
            )
            .exclude(
                id=self.instance.id,
            )
            .exists()
        )

    @property
    def is_buyer_required(self):
        if self.instance.id is not None and self.instance.created is not None:
            after_update = self.instance.created > datetime.datetime(
                2021, 4, 26, tzinfo=datetime.timezone.utc
            )
        else:
            #  All new subs are **after_update**
            after_update = True

        enforced_in_country = settings.API_COUNTRY == Country.PL

        return after_update and enforced_in_country

    def clean(self):
        exclusion_reason = self.cleaned_data['navision_invoicing_exclusion_reason']

        is_offline = self.cleaned_data.get('source') == Business.PaymentSource.OFFLINE
        navision_invoicing_allowed = self.cleaned_data.get('navision_invoicing_allowed', True)
        reason_is_null_or_blank = not exclusion_reason or exclusion_reason.isspace()

        if is_offline and (not navision_invoicing_allowed) and reason_is_null_or_blank:
            raise ValidationError(
                {
                    'navision_invoicing_exclusion_reason': 'Please provide exclusion reason',
                }
            )

        cleaned_data = super(SubscriptionForm, self).clean()
        business = cleaned_data.get('business')
        if business:
            if business.status == Business.Status.SETUP:
                raise ValidationError(
                    'Business \'{}\' has setup status. '
                    'No subscription can be added on this stage.'.format(business.name)
                )
            if business.has_new_billing:
                raise ValidationError("Business '{}' uses new Billing.".format(business.name))
        start = self.cleaned_data.get('start')
        expiry = self.cleaned_data.get('expiry')
        if start and expiry and expiry <= start:
            raise forms.ValidationError('Subscription expiry date must be greater than start date')

        if self.is_overlapping_with_any_offline():
            raise forms.ValidationError(
                'New subscription date conflicts with the another one. '
                'Please change to date that does not collide.'
            )

        # Add defaults
        if cleaned_data.get('max_staffer_num') is None and 'product' in cleaned_data:
            cleaned_data['max_staffer_num'] = cleaned_data['product'].max_staffer_num

        if cleaned_data.get('price_amount') is None and 'product' in cleaned_data:
            cleaned_data['price_amount'] = cleaned_data['product'].price_amount

        return cleaned_data


class SubscriptionDiscountOfflineForm(forms.ModelForm):
    class Meta:
        model = SubscriptionDiscount
        fields = ('discount_percentage', 'date_start', 'date_end', 'discount_type')

    discount_percentage = forms.DecimalField(
        label='Discount %',
        decimal_places=1,
        # tmp...
        min_value=-100,
        max_value=100,
    )

    def __init__(self, *args, **kwargs):
        instance = kwargs.get('instance')
        initial = kwargs.get('initial', {})
        if instance and instance.amount is not None and instance.subscription.product is not None:
            base_price = (
                instance.subscription.price_amount or instance.subscription.product.price_amount
            )
            if base_price:
                discount_percentage = (instance.amount * Decimal(100) / base_price).quantize(
                    Decimal('1.0'), rounding=ROUND_HALF_UP
                )
            else:
                discount_percentage = Decimal(0)
            initial['discount_percentage'] = discount_percentage
            kwargs['initial'] = initial
        super(SubscriptionDiscountOfflineForm, self).__init__(*args, **kwargs)
        self.fields['discount_type'].required = True
        self.fields['date_start'].required = True
        self.fields['date_end'].required = True

    def clean_discount_percentage(self):
        discount_percentage = self.cleaned_data['discount_percentage']
        if (
            discount_percentage
            and 'discount_percentage' in self.changed_data
            and discount_percentage % 1 != 0
        ):
            raise forms.ValidationError('Please enter whole number, ex. 10.0 is valid, 10.5 is not')
        return discount_percentage

    def clean(self):
        cleaned_data = super(SubscriptionDiscountOfflineForm, self).clean()
        subscription = cleaned_data['subscription']
        if not subscription.product:
            raise forms.ValidationError('Subscription has no product assigned')
        return cleaned_data

    def save(self, commit=True):
        subscription = self.instance.subscription
        percentage = self.cleaned_data['discount_percentage']
        base_price = subscription.price_amount or subscription.product.price_amount
        # TODO: Number of billing cycles
        self.instance.number_of_billing_cycles = None
        if base_price is not None:
            self.instance.amount = (Decimal(percentage) * base_price / Decimal(100)).quantize(
                Decimal('0.01'), rounding=ROUND_HALF_UP
            )
        super(SubscriptionDiscountOfflineForm, self).save(commit=True)
        return self.instance


class SubscriptionDiscountOfflineInline(admin.StackedInline):
    model = SubscriptionDiscount
    form = SubscriptionDiscountOfflineForm
    classes = ['collapse']
    extra = 0
    min_num = 0


class SubscriptionDiscountOnlineForm(forms.ModelForm):
    class Meta:
        model = SubscriptionDiscount
        fields = (
            'amount',
            'inherited_from_id',
            'success',
            'quantity',
            'number_of_billing_cycles',
            'discount_type',
            'date_start',
            'date_end',
            'discount_percentage',
        )

    discount_percentage = forms.DecimalField(
        label='Discount %',
        decimal_places=1,
        # tmp...
        min_value=-100,
        max_value=100,
    )

    def __init__(self, *args, **kwargs):
        instance = kwargs.get('instance')
        initial = kwargs.get('initial', {})
        if instance and instance.amount is not None and instance.subscription.product is not None:
            base_price = (
                instance.subscription.price_amount or instance.subscription.product.price_amount
            )
            if base_price:
                discount_percentage = (instance.amount * Decimal(100) / base_price).quantize(
                    Decimal('1.0'), rounding=ROUND_HALF_UP
                )
            else:
                discount_percentage = Decimal(0)
            initial['discount_percentage'] = discount_percentage
            kwargs['initial'] = initial
        super(SubscriptionDiscountOnlineForm, self).__init__(*args, **kwargs)
        for field_name in self.fields:
            self.fields[field_name].disabled = True
            self.fields[field_name].required = False


class SubscriptionDiscountOnlineInline(admin.StackedInline):
    model = SubscriptionDiscount
    form = SubscriptionDiscountOnlineForm
    classes = ['collapse']
    extra = 0
    min_num = 0
    can_delete = False

    def has_add_permission(self, request, obj=None):
        return False


class CoachingInline(admin.TabularInline):
    model = Coaching
    min_num = 0
    extra = 0
    raw_id_fields = ['subscription', 'business']


class SplitDateTimeField(forms.SplitDateTimeField):
    def clean(self, value):
        # For existing instances forms.SplitDateTimeField throws
        # "Enter a list of values" error
        if isinstance(value, datetime.datetime):
            return value
        return super(SplitDateTimeField, self).clean(value)


class SubscriptionSMSPackageOfflineForm(forms.ModelForm):
    class Meta:
        model = SubscriptionSMSPackage
        fields = (
            'subscription',
            'package',
            'renewing',
            'date_start',
            'date_end',
        )

    renewing = forms.BooleanField(required=False)
    date_start = SplitDateTimeField(required=False, widget=widgets.AdminSplitDateTime())
    date_end = SplitDateTimeField(required=False, widget=widgets.AdminSplitDateTime())
    package = forms.ModelChoiceField(
        queryset=SMSPackage.objects.filter(
            Q(add_on__source=Business.PaymentSource.OFFLINE) | Q(chargeable=False)
        )
    )

    def __init__(self, *args, **kwargs):
        instance = kwargs.get('instance')
        initial = kwargs.get('initial', {})
        if instance:
            if instance.date_end is None:
                initial['renewing'] = True
            else:
                initial['renewing'] = False

            kwargs['initial'] = initial
        super(SubscriptionSMSPackageOfflineForm, self).__init__(*args, **kwargs)

        if (instance and instance.date_start <= tznow()) or (
            instance and instance.package and instance.package.chargeable
        ):
            self.fields['package'].disabled = True
            self.fields['date_start'].disabled = True
        else:
            self.fields['date_start'].required = True

        if instance and instance.date_end is not None:
            self.fields['renewing'].disabled = True
            self.fields['date_end'].disabled = True

    def clean(self):
        cleaned_data = super(SubscriptionSMSPackageOfflineForm, self).clean()
        renewing = self.cleaned_data.get('renewing')
        package = self.cleaned_data.get('package')
        # Additional package can't be given for free
        if (
            package
            and not package.chargeable
            and (not self.instance.id or 'package' in self.changed_data)
        ):
            raise forms.ValidationError('Only paid packages can be added here')
        # Can change renewing param only true -> false way, ex. because of
        # Braintree payments. If there's a need to renew package, new one can be
        # added.
        if (
            self.instance.id
            and self.instance.date_end
            and 'renewing' in self.changed_data
            and renewing
        ):
            raise forms.ValidationError(
                'Can switch renewing package to non-renewing only, not the '
                'opposite. If you need renewing package please create new one.'
            )
        date_end = self.cleaned_data.get('date_end')
        if renewing and date_end:
            raise forms.ValidationError('Please remove date end or mark package as non-renewing')
        if not renewing and not date_end:
            raise forms.ValidationError('Please provide date end or mark package as renewing')
        return cleaned_data

    def save(self, commit=True):
        super(SubscriptionSMSPackageOfflineForm, self).save(commit=False)
        self.instance.amount_per_billing_cycle = self.instance.package.amount_per_billing_cycle
        with transaction.atomic():
            self.instance.save()
            self.save_m2m()
        return self.instance


class SubscriptionSMSPackageOnlineForm(forms.ModelForm):
    class Meta:
        model = SubscriptionSMSPackage
        fields = (
            'subscription',
            'package',
            'renewing',
            'date_start',
            'date_end',
        )

    renewing = forms.BooleanField(required=False)
    date_start = SplitDateTimeField(
        required=False, disabled=True, widget=widgets.AdminSplitDateTime()
    )
    date_end = SplitDateTimeField(
        required=False, disabled=True, widget=widgets.AdminSplitDateTime()
    )
    package = forms.ModelChoiceField(
        queryset=SMSPackage.objects.filter(
            Q(
                add_on__source__in=[
                    Business.PaymentSource.BRAINTREE,
                    Business.PaymentSource.ITUNES,
                    Business.PaymentSource.PLAY,
                ]
            )
            | Q(chargeable=False)
        )
    )

    def __init__(self, *args, **kwargs):
        instance = kwargs.get('instance')
        initial = kwargs.get('initial', {})
        if instance:
            if instance.date_end is None:
                initial['renewing'] = True
            else:
                initial['renewing'] = False

            kwargs['initial'] = initial
        super(SubscriptionSMSPackageOnlineForm, self).__init__(*args, **kwargs)

        if (instance and instance.date_start <= tznow()) or (
            instance and instance.package and instance.package.chargeable
        ):
            self.fields['package'].disabled = True

        if instance and instance.date_end is not None:
            self.fields['renewing'].disabled = True

    def clean(self):
        cleaned_data = super(SubscriptionSMSPackageOnlineForm, self).clean()
        package = self.cleaned_data.get('package')
        subscription = self.cleaned_data.get('subscription')
        if package and package.chargeable and package.add_on.source != subscription.source:
            raise forms.ValidationError(
                'Please select SMS package with payment source matching subscription'
            )
        # Additional package can't be given for free
        if (
            package
            and not package.chargeable
            and (not self.instance.id or 'package' in self.changed_data)
        ):
            raise forms.ValidationError('Only paid packages can be added here')
        renewing = self.cleaned_data.get('renewing')
        # Can change renewing param only true -> false way, ex. because of
        # Braintree payments. If there's a need to renew package, new one can be
        # added.
        if (
            self.instance.id
            and self.instance.date_end
            and 'renewing' in self.changed_data
            and renewing
        ):
            raise forms.ValidationError(
                'Can switch renewing package to non-renewing only, not the '
                'opposite. If you need renewing package please create new one.'
            )
        if not renewing and not subscription.expiry:
            raise forms.ValidationError(
                'You can\'t create non-renewing packages for pending subscriptions'
            )
        # tmp
        if not subscription.expiry:
            raise forms.ValidationError('Can\'t add SMS for pending subscriptions')
        return cleaned_data

    def apply_charges(self, subscription, sms_package):
        """Purchase SMS packages or remove them."""
        success = None
        errors = None
        single_success = None
        if (
            self.instance.id is None
            and sms_package.add_on.source == Business.PaymentSource.BRAINTREE
        ):
            if self.cleaned_data.get('renewing'):
                # single_success param tells us if first, initial transaction
                # went through. If single transaction was successful, but
                # later we couldn't add renewing add-on, we have to at least
                # create non-renewing sms package for user and display info
                # about it.
                (
                    success,
                    errors,
                    single_success,
                ) = BraintreeSMSChargeTool.purchase_renewing_sms_package(
                    subscription=subscription, sms_package=sms_package
                )
            else:
                success, errors = BraintreeSMSChargeTool.purchase_non_renewing_sms_package(
                    subscription=subscription, sms_package=sms_package
                )
        elif (
            'renewing' in self.changed_data
            and not self.cleaned_data.get('renewing')
            and sms_package.add_on.source == Business.PaymentSource.BRAINTREE
        ):
            success, errors = BraintreeSMSChargeTool.end_renewing_sms_package(
                subscription=subscription, sms_package=sms_package
            )

        return success, errors, single_success

    def save(self, commit=True):
        super(SubscriptionSMSPackageOnlineForm, self).save(commit=False)
        self.instance.amount_per_billing_cycle = self.instance.package.amount_per_billing_cycle
        subscription = self.instance.subscription
        sms_package = self.instance.package
        if not self.cleaned_data.get('renewing') and not self.instance.date_end:
            self.instance.date_end = subscription.expiry

        self.instance.date_start = (
            subscription.current_billing_cycle_start
            or
            # Old Braintree entries & mobile
            adjust_billing_date(
                subscription.expiry - relativedelta(months=1), subscription.start.day
            )
        )
        if sms_package and sms_package.chargeable:
            success, errors, single_success = self.apply_charges(subscription, sms_package)
            if not success and single_success:
                # Single transaction went through, create non-renewing package
                # at least
                self.instance.date_end = subscription.expiry
                self.instance.warning = (
                    'Single transaction went through but coudn\'t add recurring'
                    ' transactions - non-renewing SMS package was created'
                    ' instead'
                )
            elif success is False:
                self.instance.error = 'Error in payments'
                # Do not save anything
                return self.instance

        with transaction.atomic():
            self.instance.save()
            self.save_m2m()
        return self.instance


class SubscriptionSMSPackageOfflineInline(admin.StackedInline):
    model = SubscriptionSMSPackage
    extra = 0
    form = SubscriptionSMSPackageOfflineForm


class SubscriptionSMSPackageOnlineInline(admin.StackedInline):
    model = SubscriptionSMSPackage
    extra = 0
    form = SubscriptionSMSPackageOnlineForm


class SubscriptionExternalInlineForm(forms.ModelForm):
    class Meta:
        model = SubscriptionExternalInfo
        fields = (
            'subscription',
            'sp',
            'entity_name',
            'tax_id',
            'invoice_email',
            'agreement_type',
            'instalments',
            'agreement_signed_date',
            'recover',
        )

    recover = forms.ChoiceField(
        choices=Meta.model.VindicationFormChoices.choices_with_empty(),
        required=False,
    )


class BusinessFormSet(ReadOnlyFieldsMixin, LazyLoadModelFormset):
    default_load_limit = 20

    def get_queryset(self):
        return self.queryset


class BusinessInline(ReadOnlyTabular, LazyLoadInlineMixin, admin.TabularInline):
    model = Business
    fields = (
        'business_id',
        'invoice_email',
        'name',
    )
    formset = BusinessFormSet
    readonly_fields = fields
    verbose_name_plural = 'Businesses (top 20)'

    def business_id(self, obj):
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:business_business_change', args=(obj.pk,)),
            obj.pk,
        )

    business_id.short_description = 'Business ID'


class SubscriptionInlineForm(forms.ModelForm):
    class Meta:
        model = Subscription
        fields = (
            'navision_invoicing_allowed',
            'navision_invoicing_exclusion_reason',
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        if self.instance.pk is not None and self.instance.source == Business.PaymentSource.OFFLINE:
            self.fields['navision_invoicing_allowed'].disabled = False
            self.fields['navision_invoicing_exclusion_reason'].disabled = False
        else:
            self.fields['navision_invoicing_allowed'].disabled = True
            self.fields['navision_invoicing_exclusion_reason'].disabled = True

    def clean(self):
        exclusion_reason = self.cleaned_data['navision_invoicing_exclusion_reason']

        is_offline = self.instance.source == Business.PaymentSource.OFFLINE
        navision_invoicing_allowed = self.cleaned_data.get('navision_invoicing_allowed', True)
        reason_is_null_or_blank = not exclusion_reason or exclusion_reason.isspace()

        if is_offline and (not navision_invoicing_allowed) and reason_is_null_or_blank:
            raise ValidationError(
                {
                    'navision_invoicing_exclusion_reason': 'Please provide exclusion reason',
                }
            )

        return super().clean()


class SubscriptionInline(NoAddDelMixin, admin.TabularInline):
    model = Subscription
    form = SubscriptionInlineForm
    formfield_overrides = {models.TextField: {'widget': forms.TextInput}}
    fields = (
        'get_id',
        'business',
        'product',
        'navision_invoicing_allowed',
        'navision_invoicing_exclusion_reason',
    )
    readonly_fields = (
        'get_id',
        'business',
        'product',
    )

    def get_id(self, obj):
        return format_html(
            '<a href="{}">{}</a>',
            reverse('admin:purchase_subscription_change', args=(obj.pk,)),
            obj.pk,
        )

    get_id.short_description = 'Subscription'


class InvoiceAddressForm(forms.ModelForm):
    class Meta:
        model = InvoiceAddress
        fields = '__all__'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        if settings.CHECK_ZIPCODE_IN_REGION_TABLE:
            self.fields['zipcode'].required = True
        else:
            self.fields['zipcode_textual'].required = True
            self.fields['zipcode_textual'].label = 'Zipcode'

    def clean_zipcode_textual(self):
        zipcode_textual = self.cleaned_data.get('zipcode_textual')

        if zipcode_textual is None:
            return

        if not re.match(settings.COUNTRY_CONFIG.zipcode_regexp, zipcode_textual, re.IGNORECASE):
            raise ValidationError('Invalid zipcode format')

        return zipcode_textual


class InvoiceAddressAdmin(NoDelMixin, BaseModelAdmin):
    class Media:
        css = {'all': ('css/autocomplete.css',)}

    form = InvoiceAddressForm

    list_display = (
        'id',
        'city',
        'address_details1',
    )
    fields = (
        'address_details1',
        'city',
        'zipcode',
        'zipcode_textual',
        'state',
    )
    autocomplete_fields = (
        'zipcode',
        'state',
    )

    def get_fields(self, request, obj=None):
        fields = super(InvoiceAddressAdmin, self).get_fields(request, obj)

        if settings.CHECK_ZIPCODE_IN_REGION_TABLE:
            fields = tuple(field for field in fields if field != 'zipcode_textual')
        else:
            fields = tuple(field for field in fields if field != 'zipcode')

        return fields


class SubscriptionBuyerNavisionExcludedFilter(admin.SimpleListFilter):
    title = 'Navision excluded'
    parameter_name = 'navision_excluded'

    def lookups(self, request, model_admin):
        return (
            ('yes', 'yes'),
            ('no', 'no'),
        )

    def queryset(self, request, queryset):
        value = self.value()

        if value == 'yes':
            queryset = queryset.filter(
                Q(businesses_cnt=0) | Q(businesses_cnt=F('businesses_excluded_cnt'))
            )
        elif value == 'no':
            queryset = queryset.filter(
                Q(businesses_cnt__gt=0) & Q(businesses_cnt__gt=F('businesses_excluded_cnt'))
            )
        return queryset


class SubscriptionBuyerInvoiceAllowedFilter(admin.SimpleListFilter):
    title = 'Navision Invoicing allowed'
    parameter_name = 'navision_invoicing_allowed'

    def lookups(self, request, model_admin):
        return (
            ('yes', 'yes'),
            ('no', 'no'),
        )

    def queryset(self, request, queryset):
        value = self.value()

        if value == 'yes':
            queryset = queryset.filter(
                navision_invoicing_allowed=True,
            )
        elif value == 'no':
            queryset = queryset.filter(
                navision_invoicing_allowed=False,
            )
        return queryset


class SubscriptionBuyerPaymentMethodFilter(admin.SimpleListFilter):
    title = "Payment Source"
    parameter_name = "payment_source"

    def lookups(self, request, model_admin):
        return Business.PaymentSource.choices()

    def queryset(self, request, queryset):
        value = self.value()

        if value is None:
            return queryset

        return queryset.filter(subscriptions__source=value)


class SubscriptionBuyerOwnedBusinessesFilter(admin.SimpleListFilter):
    title = "Owned Businesses"
    parameter_name = "owned_businesses"

    def lookups(self, request, model_admin):
        return (
            ('zero', 'Zero'),
            ('one', 'One'),
            ('multiple', 'Multiple'),
        )

    def queryset(self, request, queryset):
        value = self.value()

        if value is None:
            return queryset
        elif value == 'zero':
            return queryset.annotate(business_count=Count('businesses')).filter(business_count=0)
        elif value == 'one':
            return queryset.annotate(business_count=Count('businesses')).filter(business_count=1)
        elif value == 'multiple':
            return queryset.annotate(business_count=Count('businesses')).filter(
                business_count__gt=1
            )


class InvoiceAddressInBuyerForm(InvoiceAddressForm):
    state = forms.ModelChoiceField(
        queryset=Region.objects.filter(type=RegionType.STATE),
        widget=autocomplete.ModelSelect2(url='admin:state_autocomplete'),
        required=False,
    )
    zipcode = forms.ModelChoiceField(
        queryset=Region.objects.filter(type=RegionType.ZIP),
        widget=autocomplete.ModelSelect2(url='admin:zip_autocomplete'),
        required=False,
    )


class SubscriptionBuyerAdminChangeForm(forms.ModelForm):
    NOT_SUPPORTED_FIELD_HELP_TEXT = (
        'This field is no longer supported and is not used in invoices. Use {} instead.'
    ).format

    move_changes_to_navision = forms.BooleanField(
        required=False,
        initial=False,
        label='Synchronize data with navision immediately',
    )
    invoicing_exclusion_reason = forms.CharField(
        required=False,
        label='Stop invoicing reason',
        help_text='Fill in ONLY if you don\'t want Buyer '
        'to be invoiced automatically through Navision',
    )
    payment_due_days = forms.ChoiceField(
        choices=tuple((str(element), element) for element in AVAILABLE_PAYMENT_DUE_DAYS_LIST),
        initial=7,
    )
    invoice_email = forms.EmailField(required=True)
    address_details1 = forms.CharField(required=False)
    city = forms.CharField(required=False)
    state = forms.CharField(required=False)
    zipcode = forms.CharField(required=False)

    class Meta:
        model = SubscriptionBuyer
        fields = [
            'entity_name',
            'tax_id',
            'entity_type',
            'invoice_email',
            'extra_invoice_emails',
            'invoice_address',
            'tax_group',
            'batch_invoices',
            'payment_due_days',
            'is_verified',
            'vat_registered',
            'active',
            'move_changes_to_navision',
            'invoicing_allowed',
            'invoicing_exclusion_reason',
            'invoice_details_editable',
        ]
        help_texts = {
            'is_verified': _(
                'Once buyer is verified and ACTIVE, it will be integrated with Navision'
            ),
        }

    def __init__(self, *args, **kwargs):
        self.merchant_data = None

        super().__init__(*args, **kwargs)
        if ShowInvoiceAddressInBuyerAdminFlag() and settings.CHECK_ZIPCODE_IN_REGION_TABLE:
            invoice_address_form = InvoiceAddressInBuyerForm(instance=self.instance.invoice_address)
            for field in invoice_address_form.fields:
                self.fields[field] = invoice_address_form.fields[field]
                self.initial[field] = invoice_address_form.initial.get(field)
                self.fields[field].required = False

        if not settings.NAVISION_USE_TAX_GROUPS:
            if tax_group_field := self.fields.get('tax_group'):
                tax_group_field.required = False
                tax_group_field.disabled = True

        if not settings.CHECK_ZIPCODE_IN_REGION_TABLE:
            if invoice_address_field := self.fields.get('invoice_address'):
                invoice_address_field.required = True

        tax_id_field = self.fields.get('tax_id')
        vat_registered_field = self.fields.get('vat_registered')
        entity_type_field = self.fields.get('entity_type')
        if not self.instance.admin_can_edit_tax_related_fields:
            if tax_id_field:
                tax_id_field.disabled = True
                vat_registered_field.disabled = True
            if entity_type_field:
                entity_type_field.disabled = True

        if self.instance.is_verified:
            if is_verified_field := self.fields.get('is_verified'):
                is_verified_field.disabled = True

    def clean(self):
        cleaned_data = super().clean()
        is_verified = cleaned_data.get('is_verified')

        if not cleaned_data.get('entity_name'):
            self.add_error('entity_name', 'This field is required.')

        if not cleaned_data.get('invoicing_allowed') and not cleaned_data.get(
            'invoicing_exclusion_reason'
        ):
            self.add_error(
                'invoicing_exclusion_reason',
                'Reason is required if Invoicing Allowed is set to False.',
            )

        if is_verified and not settings.NAVISION_INTEGRATION_ENABLED:
            self.add_error(None, 'Navision integration is disabled')

        if ShowInvoiceAddressInBuyerAdminFlag() and settings.CHECK_ZIPCODE_IN_REGION_TABLE:
            invoice_address_form = self._get_invoice_address_form(cleaned_data)
            if not invoice_address_form.is_valid() and 'invoice_address' not in self.changed_data:
                for field, error in invoice_address_form.errors.items():
                    self.add_error(field, error)
            if 'invoice_address' in self.changed_data and any(
                invoice_address_field in self.changed_data
                for invoice_address_field in ('address_details1', 'city', 'state', 'zipcode')
            ):
                self.add_error(
                    None,
                    'Cannot edit the reference to an invoice address and its details at the same time',
                )

        return cleaned_data

    def save(self, commit=True):
        is_verified = 'is_verified' in self.changed_data and self.cleaned_data.get('is_verified')
        if is_verified and not self.instance.verified_at:
            self.instance.verified_by = self.request_user.user
            self.instance.verified_at = tznow()

        self._update_vat_registered_field_based_on_tax_id()

        if ShowInvoiceAddressInBuyerAdminFlag() and settings.CHECK_ZIPCODE_IN_REGION_TABLE:
            invoice_address_form = self._get_invoice_address_form(self.cleaned_data)
            if 'invoice_address' not in self.changed_data:
                invoice_address = invoice_address_form.save()  # validated in clean
                self.instance.invoice_address = invoice_address

        instance = super().save(commit=commit)
        if instance.pk:
            instance.sync_with_merchant(
                immediately=self.cleaned_data.get('move_changes_to_navision', False),
            )

        return instance

    def _update_vat_registered_field_based_on_tax_id(self):
        if NavisionFixMerchantSyncIEFlag() and settings.API_COUNTRY == Country.IE:
            return

        vat_registered = False
        if (tax_id := self.cleaned_data.get('tax_id')) and tax_id and tax_id.strip():
            vat_registered = True
        self.changed_data.append('vat_registered')
        self.cleaned_data['vat_registered'] = vat_registered
        self.instance.vat_registered = vat_registered

    def _get_invoice_address_form(self, data):
        address_data = {
            'address_details1': data.get('address_details1'),
            'city': data.get('city'),
            'state': data.get('state'),
            'zipcode': data.get('zipcode'),
        }
        invoice_address = self.instance.invoice_address
        form = InvoiceAddressInBuyerForm(instance=invoice_address, data=address_data)
        return form


@admin.action(
    description='Invoice boost online (last finished billing cycle)',
    permissions=['navision'],
)
def invoice_boost_online(_model_admin, request, queryset):
    from webapps.navision.enums import InvoicePaymentSourceType
    from webapps.navision.invoicing.boost_online import BoostOnlineInvoiceFactory
    from webapps.navision.models import Invoice, InvoicingSummary
    from webapps.navision.tasks.boost_online import create_boost_online_invoice_task
    from webapps.user.models import User
    from webapps.user.tools import get_system_user

    days_for_claim = 7

    for buyer in queryset:
        if not buyer.businesses.exists():
            messages.info(request, f'buyer ID: {buyer.id} has no businesses\n')
            continue

        for business in buyer.businesses.all():
            last_ended_cycle = (
                business.billing_cycles.filter(
                    date_end__lt=tznow() - relativedelta(days=days_for_claim)
                )
                .order_by('-date_end')
                .first()
            )

            if not last_ended_cycle:
                messages.info(
                    request,
                    f'business ID: {business.id} (buyer ID: {buyer.id}) '
                    f'has no billing cycle with ended claim period\n',
                )
                continue

            user_id = request.user.id
            operator = User.objects.filter(id=user_id).first() or get_system_user()
            invoicing_summary, _ = InvoicingSummary.objects.get_or_create(
                invoicing_target=last_ended_cycle.date_end.replace(
                    hour=0, minute=0, second=0, microsecond=0
                ),
                operator=operator,
                service=Invoice.Service.BOOST,
                source=InvoicePaymentSourceType.ONLINE,
                invoicing_date=tznow().replace(hour=0, minute=0, second=0, microsecond=0),
            )
            factory = BoostOnlineInvoiceFactory(invoicing_summary=invoicing_summary)

            is_business_already_invoiced = (
                factory.already_invoiced_qs().filter(business_id=business.id).exists()
            )

            if is_business_already_invoiced:
                messages.info(
                    request,
                    f'business ID: {business.id} (buyer ID: {buyer.id}) is already invoiced\n',
                )
                continue

            create_boost_online_invoice_task.delay(business.id, factory.invoicing_summary.id)
            messages.success(
                request,
                f'business ID: {business.id} (buyer ID: {buyer.id}) invoiced successfully\n',
            )


@admin.action(
    description='Invoice boost offline',
    permissions=['navision'],
)
def invoice_boost_offline(_model_admin, _request, queryset):
    from webapps.navision.invoicing.offline import BoostOfflineInvoiceFactory
    from webapps.navision.tasks.offline import (
        create_offline_boost_invoice_task,
        create_offline_batch_boost_invoice_task,
    )

    factory = BoostOfflineInvoiceFactory()

    for buyer in queryset:
        businesses_ids = (
            buyer.businesses.values('id')
            .difference(factory.already_invoiced_qs())
            .values_list('id', flat=True)
        )

        if buyer.batch_invoices:
            create_offline_batch_boost_invoice_task.delay(
                list(businesses_ids),
                factory.invoicing_summary.id,
            )
        else:
            for business_id in businesses_ids:
                create_offline_boost_invoice_task.delay(business_id, factory.invoicing_summary.id)


@admin.action(
    description='Invoice SaaS Offline',
    permissions=['navision'],
)
def invoice_saas_offline(_modeladmin, _request, queryset):
    from webapps.navision.tasks.offline import (
        create_offline_saas_invoice_task,
        create_offline_batch_saas_invoice_task,
    )
    from webapps.navision.invoicing.offline import (
        SaaSOfflineInvoiceFactory,
    )

    saas_factory = SaaSOfflineInvoiceFactory()

    for buyer in queryset:
        subscriptions_to_invoice = set()

        for business in buyer.businesses.all():
            for subscription in business.subscriptions.filter(
                source=Business.PaymentSource.OFFLINE,
            ):
                subscriptions_to_invoice.add(subscription.id)

        subscriptions_to_invoice_qs = (
            Subscription.objects.filter(id__in=subscriptions_to_invoice)
            .intersection(
                saas_factory.subscriptions_to_invoice_qs(),
            )
            .difference(saas_factory.already_invoiced_qs())
        )

        if buyer.batch_invoices:
            create_offline_batch_saas_invoice_task.delay(
                saas_factory.invoicing_summary.id,
                [subscription.id for subscription in subscriptions_to_invoice_qs],
            )
        else:
            for subscription in subscriptions_to_invoice_qs:
                create_offline_saas_invoice_task.delay(
                    saas_factory.invoicing_summary.id,
                    subscription.id,
                )


@admin.action(
    description='Invoice SaaS online (last charged transaction)',
    permissions=['navision'],
)
def invoice_saas_online(_model_admin, request, queryset):
    from webapps.navision.enums import InvoicePaymentSourceType
    from webapps.navision.invoicing.booksy_billing import BooksyBillingSaaSInvoiceFactory
    from webapps.navision.models import Invoice, InvoicingSummary
    from webapps.navision.tasks.booksy_billing import (
        create_new_billing_saas_invoice_task,
    )
    from webapps.user.models import User
    from webapps.user.tools import get_system_user

    for buyer in queryset:
        if not buyer.businesses.exists():
            messages.info(request, f'buyer ID: {buyer.id} has no businesses\n')
            continue

        for business in buyer.businesses.all():
            last_billing_cycle = (
                business.billing_cycles.filter(date_start__lt=tznow())
                .order_by('-date_start')
                .first()
            )
            if not last_billing_cycle:
                messages.info(
                    request,
                    f'business ID: {business.id} (buyer ID: {buyer.id}) '
                    f'has no eligible billing cycle\n',
                )
                continue
            last_charged_transaction = (
                last_billing_cycle.transactions.filter(status=TransactionStatus.CHARGED).order_by(
                    '-created'
                )
            ).first()

            if not last_charged_transaction:
                messages.info(
                    request,
                    f'business ID: {business.id} (buyer ID: {buyer.id}) '
                    f'has no eligible transaction to invoice\n',
                )
                continue

            if last_charged_transaction.amount <= Decimal(0):
                messages.info(
                    request,
                    f'business ID: {business.id} (buyer ID: {buyer.id}) '
                    f'transaction amount equals 0 or less and will not be invoiced\n',
                )
                continue

            user_id = request.user.id
            operator = User.objects.filter(id=user_id).first() or get_system_user()
            invoicing_summary, _ = InvoicingSummary.objects.get_or_create(
                invoicing_target=last_charged_transaction.created.replace(
                    hour=0, minute=0, second=0, microsecond=0
                ),
                operator=operator,
                service=Invoice.Service.SAAS,
                source=InvoicePaymentSourceType.ONLINE,
                invoicing_date=tznow().replace(hour=0, minute=0, second=0, microsecond=0),
            )
            factory = BooksyBillingSaaSInvoiceFactory(invoicing_summary=invoicing_summary)

            is_business_already_invoiced = (
                factory.already_invoiced_qs().filter(business_id=business.id).exists()
            )

            if is_business_already_invoiced:
                messages.info(
                    request,
                    f'business ID: {business.id} (buyer ID: {buyer.id}) is already invoiced\n',
                )
                continue

            create_new_billing_saas_invoice_task.delay(factory.invoicing_summary.id, business.id)
            messages.success(
                request,
                f'business ID: {business.id} (buyer ID: {buyer.id}) invoiced successfully\n',
            )


class SubscriptionBuyerAdmin(NoDelMixin, BaseModelAdmin):
    class Media:
        css = {'all': ('css/autocomplete.css',)}

    paginator = CacheOrEstimatePaginator
    form = SubscriptionBuyerAdminChangeForm
    inlines = [
        BusinessInline,
        SubscriptionInline,
    ]
    list_display = (
        'id',
        'tax_id',
        'vat_registered',
        'entity_type',
        'entity_name',
        'owned_businesses',
        'invoice_email',
        'is_verified',
        'active',
        'verified_at',
        'navision_invoicing_allowed',
        'merchant_info',
    )
    fieldsets = (
        (
            'Buyer info',
            {
                'fields': (
                    'entity_name',
                    'tax_id',
                    'vat_registered',
                    'entity_type',
                    'invoice_email',
                    'extra_invoice_emails',
                    'tax_group',
                    'invoice_details_editable',
                    'invoice_address',
                )
            },
        ),
        (
            'Buyer - Navision integration',
            {
                'fields': (
                    'batch_invoices',
                    'payment_due_days',
                    'is_verified',
                    'active',
                    'move_changes_to_navision',
                ),
            },
        ),
        (
            'Navision invoicing',
            {
                'fields': (
                    'invoicing_allowed',
                    'invoicing_exclusion_reason',
                    'navision_invoicing_allowed',
                    'navision_integration_exclusion_reasons',
                ),
            },
        ),
        (
            'Others (read-only)',
            {
                'fields': (
                    'enova_uid',
                    'verified_at',
                    'verified_by',
                    'subscriptions_count',
                    'merchant_info',
                )
            },
        ),
    )
    readonly_fields = [
        'enova_uid',
        'verified_at',
        'verified_by',
        'subscriptions_count',
        'navision_invoicing_allowed',
        'navision_integration_exclusion_reasons',
        'merchant_info',
    ]
    raw_id_fields = (
        'invoice_address',
        'tax_group',
    )
    search_fields = (
        '=id',
        'businesses__id',
        '=tax_id',
        'entity_name',
        'invoice_email',
    )
    query_fields = ('id', 'businesses__id', 'tax_id', 'entity_name', 'invoice_email')
    query_fields_placeholders = {'businesses__id': 'business_id'}
    list_filter = (
        'is_verified',
        'entity_type',
        'vat_registered',
        SubscriptionBuyerInvoiceAllowedFilter,
        SubscriptionBuyerPaymentMethodFilter,
        SubscriptionBuyerOwnedBusinessesFilter,
    )
    actions = [
        invoice_boost_online,
        invoice_boost_offline,
        invoice_saas_offline,
        invoice_saas_online,
    ]

    def get_form(self, request, obj=None, change=False, **kwargs):
        help_texts = {
            'subscriptions_count': 'This field shows the number of offline subscriptions only.'
        }
        kwargs.update({'help_texts': help_texts})
        form = super().get_form(request, obj, change, **kwargs)
        form.request_user = request.user
        return form

    def get_fieldsets(self, request, obj=None):
        fieldsets = super().get_fieldsets(request, obj)
        if ShowInvoiceAddressInBuyerAdminFlag() and settings.CHECK_ZIPCODE_IN_REGION_TABLE:
            if not 'city' in fieldsets[0][1]['fields']:
                fieldsets[0][1]['fields'] += (
                    'address_details1',
                    'city',
                    'state',
                    'zipcode',
                )
        return fieldsets

    def get_autocomplete_fields(self, request):
        if ShowInvoiceAddressInBuyerAdminFlag() and settings.CHECK_ZIPCODE_IN_REGION_TABLE:
            return (
                'zipcode',
                'state',
            )
        return super().get_autocomplete_fields(request)

    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .select_related(
                'verified_by',
                'merchant',
                'invoice_address',
            )
            .prefetch_related('businesses')
            .annotate(
                businesses_cnt=Count("businesses"),
                enterprises_cnt=Count(
                    'businesses',
                    filter=Q(
                        businesses__integrations__importer=ENTERPRISE_IMPORTER_ID,
                    ),
                ),
            )
            .annotate(
                navision_invoicing_allowed=Case(
                    When(invoicing_allowed=False, then=False),
                    When(active=False, then=False),
                    When(enterprises_cnt__gt=0, then=False),
                    When(businesses_cnt=0, then=False),
                    When(merchant_id__isnull=True, then=False),
                    default=True,
                    output_field=BooleanField(),
                )
            )
        )

    def merchant_info(self, obj):
        merchant = obj.merchant
        if merchant:
            return format_html(
                'ID: <a href="{}">{}</a><br>Navision synced at: {}',
                reverse('admin:navision_merchant_change', args=(merchant.pk,)),
                merchant.merchant_id,
                merchant.synced_at.isoformat() if merchant.synced_at else "-",
            )
        return "-"

    merchant_info.short_description = 'Merchant'

    def navision_integration_exclusion_reasons(self, obj):
        return '\n'.join(obj.navision_invoicing_exclusion_reasons)

    def owned_businesses(self, subscription_buyer: SubscriptionBuyer):
        def ids():
            business_ids = subscription_buyer.businesses.values_list('id', flat=True)[:20]
            for business_id in business_ids:
                yield reverse('admin:business_business_change', args=(business_id,)), business_id

        return format_html_join(', ', '<a href="{0}">{1}</a>', ids())

    owned_businesses.short_description = 'Owned Businesses (top 20)'

    def has_navision_permission(self, request, obj=None):
        return check_navision_operator_permission(request)


class SubscriptionAdmin(NoDelMixin, BaseModelAdmin):
    list_max_show_all = 20000
    list_per_page = 100
    list_display = (
        'id',
        'business_id_number',
        'business_link',
        'subscription_count',
        'product_name',
        'product_price',
        'source',
        'duration',
        'renewing',
        'status',
        'balance',
        'trans',
        'external_id',
        'cancel',
        'discunts',
    )
    list_filter = (
        'source',
        'product',
        'start',
        'expiry',
        'renewing',
        SubscriptionStatusFilter,
        SubscriptionCountFilter,
    )
    preserve_filters = True
    list_select_related = (
        'business',
        'product',
    )
    form = SubscriptionForm
    ordering = ('-id',)

    search_fields = (
        '=id',
        '=business__id',
        'business__name',
        'business__owner__email',
        '=transactions__external_id',
    )

    fields = (
        'id',
        'business',
        'product',
        'buyer',
        'source',
        'start',
        'expiry',
        'next_billing_date',
        'current_billing_cycle_start',
        'current_billing_cycle_end',
        'paid_through_date',
        'renewing',
        'navision_invoicing_allowed',
        'navision_invoicing_exclusion_reason',
        'agreement_signed_date',
        'custom_payment_time_in_months',
        'instalments',
        'note',
        'max_staffer_num',
        'price_amount',
        'manage_subscription_deeplink',
        'custom_data',
        'receipt_details',
    )
    readonly_fields = (
        'id',
        'current_billing_cycle_start',
        'current_billing_cycle_end',
        'paid_through_date',
        'manage_subscription_deeplink',
        'custom_data',
        'receipt_details',
    )
    # As of 27.06.2019 we don't want to change online form
    offline_fields = (
        'id',
        'business',
        'product',
        'buyer',
        'source',
        # Product preview
        'product_price',
        'product_min_staff',
        'product_max_staff',
        'product_postpaid_sms_limit',
        # sms
        'sms_summary',
        'start',
        'expiry',
        'next_billing_date',
        'current_billing_cycle_end',
        'renewing',
        'navision_invoicing_allowed',
        'navision_invoicing_exclusion_reason',
        'agreement_signed_date',
        'custom_payment_time_in_months',
        'instalments',
        'note',
        'max_staffer_num',
        'price_amount',
        'monthly_base_price',
    )
    raw_id_fields = (
        'business',
        'buyer',
    )

    inlines = (
        # mess in tickets - different release dates for #51920 parts
        CoachingInline,
    )

    date_hierarchy = 'start'

    actions = [
        export_as_csv_action(
            fields=list_display,
            header=True,
            force_fields=True,
        ),
    ]

    change_form_template = 'admin/change_forms/change_form__subscription.html'

    class Media:
        js = (static('admin/js/admin_extra.js'),)

    def get_queryset(self, request):
        qs = super(SubscriptionAdmin, self).get_queryset(request)
        return get_queryset_with_subscription_count_annotation(qs)

    def change_view(self, request, object_id, form_url='', extra_context=None):
        return super(SubscriptionAdmin, self).change_view(
            request, object_id, form_url, extra_context
        )

    def get_inline_instances(self, request, obj=None):
        inline_instances = super(SubscriptionAdmin, self).get_inline_instances(request, obj)
        if (
            not obj
            or obj
            and obj.source == Business.PaymentSource.OFFLINE
            or SubscriptionListingAdmin.is_eligible_user(request)
        ):
            transaction_inline_class = SubscriptionTransactionInline
        else:
            transaction_inline_class = SubscriptionTransactionReadOnlyInline
        if obj is not None and obj.source != Business.PaymentSource.OFFLINE:
            discount_inline_class = SubscriptionDiscountOnlineInline
            if settings.SMS_LIMITS_IN_SUBSCRIPTION:
                sms_package_inline = SubscriptionSMSPackageOnlineInline(self.model, self.admin_site)
            else:
                sms_package_inline = None
        else:
            discount_inline_class = SubscriptionDiscountOfflineInline
            sms_package_inline = SubscriptionSMSPackageOfflineInline(self.model, self.admin_site)
        transaction_inline = transaction_inline_class(self.model, self.admin_site)
        discount_inline = discount_inline_class(self.model, self.admin_site)
        inline_instances.extend([transaction_inline, discount_inline])
        if sms_package_inline:
            inline_instances.append(sms_package_inline)
        return inline_instances

    @method_decorator(require_GET)
    def decode_apple_receipt(self, request, pk):
        subscription = get_object_or_404(Subscription, id=pk)
        ret = redirect(url_reverse('admin:purchase_subscription_change', args=[pk]))
        if subscription.source != Business.PaymentSource.ITUNES:
            self.message_user(
                request,
                'Only Apple iTunes Subscription can be decoded.',
                messages.ERROR,
            )
            return ret

        task = apple_receipt_decode_task.async_run(receipt=subscription.receipt)
        task_url = url_reverse('admin:notification_notificationschedule_change', args=[task.id])
        message = format_html('The result of the task in <a href="{}">{}</a>.', task_url, task_url)

        self.message_user(
            request,
            message,
            messages.SUCCESS,
        )
        return ret

    def get_form(self, request, obj=None, **kwargs):
        form = super(SubscriptionAdmin, self).get_form(request, obj, **kwargs)

        form.base_fields['source'].initial = Business.PaymentSource.OFFLINE
        form.base_fields['source'].disabled = True

        form.base_fields['product'].label_from_instance = lambda x: x.name

        product_qs = form.base_fields['product'].queryset
        offline_qs = product_qs.filter(active=True, braintree_id='', apple_id='', google_id='')

        form.base_fields['price_amount'].label = 'Final price amount'

        form.base_fields['renewing'].initial = True

        if obj:
            online = obj.source != Business.PaymentSource.OFFLINE
        else:
            # We can only add offline subscriptions in admin
            online = False
        if online:
            for field in list(form.base_fields.values()):
                field.disabled = True
        else:
            form.base_fields['product'].queryset = offline_qs
            # TODO: Base fields shoudn't be changed
            form.base_fields['start'].initial = tznow()

        return form

    def get_urls(self):
        urls = super().get_urls()
        additional_urls = [
            url(
                r'(?P<pk>\d+)/decode_apple_receipt/$',
                self.admin_site.admin_view(self.decode_apple_receipt),
                name='decode_apple_receipt',
            ),
        ]
        return additional_urls + urls

    def get_fields(self, request, obj=None):
        """
        Hook for specifying fields.
        """
        # As of 27.06.2019 we don't want to change online form
        if obj is not None and obj.source == Business.PaymentSource.OFFLINE:
            return self.offline_fields
        return super(SubscriptionAdmin, self).get_fields(request, obj)

    def get_readonly_fields(self, request, obj=None):
        readonly_fields = super(SubscriptionAdmin, self).get_readonly_fields(request, obj)
        if obj and obj.source == Business.PaymentSource.OFFLINE:
            # Business + product preview
            add_readonly = (
                'business',
                'product_price',
                'product_min_staff',
                'product_max_staff',
                'product_postpaid_sms_limit',
                'sms_summary',
            )
            return add_readonly + readonly_fields
        elif obj:
            return ('business', 'start', 'expiry') + readonly_fields
        else:
            return readonly_fields

    def get_search_results(self, request, queryset, search_term):
        queryset, use_distinct = super(SubscriptionAdmin, self).get_search_results(
            request, queryset, search_term
        )
        # search by external id of receipt
        queryset |= self.model.objects.filter(
            Q(receipt__contains={'id': search_term}) | Q(receipt__contains={'orderId': search_term})
        )
        return queryset, use_distinct

    def response_change(self, request, obj):
        # US restrict access https://redmine.booksy.pm/issues/68729
        if not request.user.groups.filter(name=GroupName.SUBSCRIPTION_EDITOR).exists():
            messages.error(request, 'You are not in group subscription_editor')
            return redirect(request.path)
        return super(SubscriptionAdmin, self).response_change(request, obj)

    def save_model(self, request, obj: Subscription, form, change):
        # US restrict access https://redmine.booksy.pm/issues/68729
        if not request.user.groups.filter(name=GroupName.SUBSCRIPTION_EDITOR).exists():
            return

        # Online subscriptions can't be modified here, except SMS packages
        if obj.source != Business.PaymentSource.OFFLINE:
            return
        super(SubscriptionAdmin, self).save_model(
            request=request,
            obj=obj,
            form=form,
            change=change,
        )
        user = get_user_from_django_request(request)
        metadata = {
            'user_email': user.email if user else '',
            'user_id': user.id if user else '',
            'admin_view': 'SubscriptionAdmin',
        }
        business = obj.business
        old_buyer = business.buyer
        new_buyer = obj.buyer
        business_update_fields = []

        if new_buyer is not None:
            # Add business to SubscriptionBuyer **businesses**
            if business.buyer_id != new_buyer.id:
                old_obj_vars = {'buyer_id': business.buyer_id}

                business.buyer_id = new_buyer.id
                business_update_fields.append('buyer')

                new_obj_vars = {'buyer_id': new_buyer.id}

                BusinessChange.add(business, new_obj_vars, old_obj_vars, operator=user)

        if business.payment_source == Business.PaymentSource.UNKNOWN:
            business.payment_source = obj.source
            business_update_fields.append('payment_source')
        # We do not support copying sms limit for subscriptions active from
        # different date than today
        if obj.start.date() == tznow().date() and obj.product.postpaid_sms_limit is not None:
            business.sms_limit = obj.product.postpaid_sms_limit
            business_update_fields.append('sms_limit')
        if business_update_fields:
            business.save(update_fields=business_update_fields)

            if old_buyer is not None and old_buyer != new_buyer:
                old_buyer.sync_with_merchant(immediately=False)
                self.message_user(
                    request,
                    f'Previous SubscriptionBuyer (ID: {old_buyer.id}) - '
                    f'{old_buyer.approx_sync_schedule}',
                )
            if new_buyer is not None:
                new_buyer.sync_with_merchant(immediately=False)
                self.message_user(
                    request,
                    f'Current SubscriptionBuyer (ID: {new_buyer.id}) - '
                    f'{new_buyer.approx_sync_schedule}',
                )

        ComputeBusinessStatusTask.run(
            business_id=obj.business_id,
            metadata=metadata,
        )

    def save_formset(self, request, form, formset, change):
        if formset.model == SubscriptionSMSPackage:
            instances = formset.save()
            for instance in instances:
                if hasattr(instance, 'error'):
                    messages.error(request, instance.error)
                if hasattr(instance, 'warning'):
                    messages.warning(request, instance.warning)
        else:
            return super(SubscriptionAdmin, self).save_formset(request, form, formset, change)

    def sms_summary(self, obj):
        if not obj:
            return None
        # Entries ae ordered from newest to oldest
        stats = NotificationSMSStatistics.sms_summary(obj.business, periods_amount=2)

        if len(stats) >= 2:
            prev_cycle = stats[1]
            return format_html(
                '<p>Count postpaid: {}</p><p>Price per part: {}</p><p>Total cost: {}</p>',
                prev_cycle["count_paid"],
                SMSCosts.sms_cost_format(1),
                prev_cycle["payable_cost_formatted"],
            )

    sms_summary.short_description = 'Sms summary for previous billing cycle'

    def product_name(self, obj):
        return getattr(obj.product, 'name', None)

    product_name.short_description = 'Product'
    product_name.admin_order_field = 'product__name'

    def product_min_staff(self, obj):
        return getattr(obj.product, 'min_staff', '') or ''

    def product_max_staff(self, obj):
        return getattr(obj.product, 'max_staff', '') or ''

    def product_postpaid_sms_limit(self, obj):
        return getattr(obj.product, 'postpaid_sms_limit', '') or ''

    def product_price(self, obj):
        return getattr(obj.product, 'price', None)

    product_price.admin_order_field = 'product__price_amount'

    def duration(self, obj):
        return '%s - %s' % (dt_fmt(obj.start), dt_fmt(obj.expiry))

    duration.admin_order_field = 'start'

    def manage_subscription_deeplink(self, obj):
        deeplink = DeepLinkCache.get(SUBSCRIPTIONS)
        to_copy = format_html(
            '''<span onclick="Utils.copyToClipboard(\'{}\');"
            style="cursor: pointer" title="Click to copy deeplink">{}&ensp;
            <i class="icon-share icon-alpha75"></i>
            </span>''',
            deeplink,
            deeplink,
        )
        return to_copy

    manage_subscription_deeplink.short_description = 'Manage subscription deeplink'

    def business_link(self, obj):
        biz = obj.business
        return biz.admin_id_link

    business_link.short_description = 'Business'
    business_link.admin_order_field = 'business_id'

    def business_id_number(self, obj):
        return obj.business_id

    business_id_number.short_description = 'Business ID'
    business_id_number.admin_order_field = 'business_id'

    def subscription_count(self, obj):
        if hasattr(obj, 'subscription_count'):
            return obj.subscription_count
        return obj.business.subscriptions.count()

    subscription_count.admin_order_field = 'subscription_count'

    def status(self, obj):
        if obj.source == Business.PaymentSource.BRAINTREE:
            return obj.receipt.get('status')

        return 'Active' if obj.active else 'Inactive'

    def balance(self, obj):
        if obj.source == Business.PaymentSource.BRAINTREE:
            balance = obj.receipt.get('balance')
            if balance is not None:
                # BrainTree balance is always in USD
                return '%s %s' % (balance, 'USD')

    @staticmethod
    def receipt_details(obj):
        if obj.source != Business.PaymentSource.ITUNES:
            return 'Option not available'
        link = reverse('admin:decode_apple_receipt', kwargs={'pk': obj.id})
        return format_html(
            """<a href="{}">
                Decode receipt
            </a>""",
            link,
        )

    receipt_details.short_description = 'Receipt details'

    def trans(self, obj):
        t = loader.get_template('admin/fields/field__transactions.html')
        transactions = [
            {
                'charged_on': dt_fmt(x.charged_on),
                'state': x.get_state_display(),
                'price': '%s %s'
                % (
                    x.price_with_discount,
                    x.get_currency_code(),
                ),
                'url': admin_link(x),
            }
            for x in obj.transactions.order_by('charged_on')
        ]
        return t.render({'id': obj.id, 'transactions': transactions})

    trans.short_description = 'Transactions'

    def discunts(self, obj):
        t = loader.get_template('admin/fields/field__discounts.html')
        discounts = [
            {
                'created': dt_fmt(x.created),
                'amount': x.amount,
                'number_of_billing_cycles': x.number_of_billing_cycles,
                'url': admin_link(x),
                'success': x.success,
            }
            for x in obj.discounts.order_by('created')
        ]
        return t.render({'id': obj.id, 'discounts': discounts})

    discunts.short_description = 'Discounts'

    def cancel(self, obj):
        return format_html(
            """<a href="%s" onclick="return confirm('Are you sure?')">
                Cancel
            </a>""",
            reverse('admin:cancel_subscription', kwargs={'sub_id': obj.id}),
        )

    def external_id(self, obj):
        if obj.source == Business.PaymentSource.PLAY:
            return obj.receipt.get('orderId')
        elif obj.source == Business.PaymentSource.BRAINTREE:
            return obj.receipt.get('id')


class SubscriptionListingForm(forms.ModelForm):
    class Meta:
        model = SubscriptionListing
        exclude = ['extra_data']

    class SMSConfigEnum(StrChoicesEnum):
        FIRST_N_FREE = 'N', 'First N (any value) per phone number for free'
        PAID = 'P', 'All paid'
        UNLIMITED = 'U', 'All unlimited'

    INVITES_CHOICES = (
        (SMSConfigEnum.FIRST_N_FREE.value, SMSConfigEnum.FIRST_N_FREE.label),
        (SMSConfigEnum.PAID.value, SMSConfigEnum.PAID.label),
        (SMSConfigEnum.UNLIMITED.value, SMSConfigEnum.UNLIMITED.label),
    )
    MARKETING_CHOICES = (
        (SMSConfigEnum.PAID.value, SMSConfigEnum.PAID.label),
        (SMSConfigEnum.UNLIMITED.value, SMSConfigEnum.UNLIMITED.label),
    )
    SYSTEM_CHOICES = MARKETING_CHOICES

    payment_time_in_months = forms.ChoiceField(
        choices=PAYMENT_TIME_IN_MONTHS_CHOICES, initial=1, label='Payment period (in months)'
    )
    staff_add_ons = forms.ModelMultipleChoiceField(
        queryset=AddOn.objects.filter(add_on_type=AddOn.AddOnType.STAFFER),
        label='Staff add-ons',
        required=False,
    )

    # See: https://gl2.booksy.net:8443/booksy/core/-/wikis/SMS-flow-03.01.2020
    sms_config_invites = forms.ChoiceField(choices=INVITES_CHOICES, label='Invite SMS config')
    free_sms_invites_count = forms.IntegerField(
        validators=[MinValueValidator(0)],
        label='Free SMS invites amount',
        required=False,
    )
    sms_config_marketing = forms.ChoiceField(
        choices=MARKETING_CHOICES, label='Marketing SMS config'
    )
    sms_config_system = forms.ChoiceField(choices=SYSTEM_CHOICES, label='System SMS config')

    def __init__(self, *args, **kwargs):
        instance = kwargs.get('instance')
        initial = kwargs.get('initial', {})
        sms_config = settings.FIRST_N_SMS_LIMITS.get(SMSPaidEnum.NON_TRIAL, {})
        if instance and settings.SMS_LIMITS_IN_SUBSCRIPTION:
            sms_config_json = instance.sms_config.get('first_n_sms_limits', {})
            if sms_config_json:
                sms_config = {}
                for key, value in sms_config_json.items():
                    sms_config[SMSTypeEnum(key)] = value
        if sms_config:
            # Map model values to form values
            model_to_form = {
                None: self.SMSConfigEnum.UNLIMITED,
                0: self.SMSConfigEnum.PAID,
            }
            invites = sms_config[SMSTypeEnum.INVITATION]
            if invites is not None and invites > 0:
                initial['sms_config_invites'] = self.SMSConfigEnum.FIRST_N_FREE
                initial['free_sms_invites_count'] = invites
            else:
                initial['sms_config_invites'] = model_to_form[invites]
            marketing = sms_config[SMSTypeEnum.MARKETING]
            system = sms_config[SMSTypeEnum.SYSTEM]
            initial['sms_config_marketing'] = model_to_form[marketing]
            initial['sms_config_system'] = model_to_form[system]

        kwargs['initial'] = initial

        super(SubscriptionListingForm, self).__init__(*args, **kwargs)
        if settings.SMS_LIMITS_IN_SUBSCRIPTION and 'sms_package' in self.fields:
            self.fields['sms_package'].queryset = SMSPackage.objects.filter(chargeable=False)
        else:
            disabled_fields = [
                'sms_config_invites',
                'sms_config_marketing',
                'sms_config_system',
                'free_sms_invites_count',
            ]
            if 'sms_package' in self.fields:
                disabled_fields.append('sms_package')
            for field_name in disabled_fields:
                self.fields[field_name].disabled = True
                self.fields[field_name].required = False

    def _clean_staff_add_ons(self, product_key=None, payment_source=None):
        add_ons = self.cleaned_data['staff_add_ons']
        charge_for_staffers = self.cleaned_data['charge_for_staffers']
        if charge_for_staffers and not add_ons:
            raise forms.ValidationError('Please assign add-ons if charge for staffers is enabled')
        if not add_ons:
            return
        if not payment_source:
            product_key_to_payment_source = {
                'braintree_id': Business.PaymentSource.BRAINTREE,
                'apple_id': Business.PaymentSource.ITUNES,
                'google_id': Business.PaymentSource.PLAY,
            }
            payment_source = product_key_to_payment_source[product_key]
        add_on_payment_sources = set(a.source for a in add_ons)
        if len(add_on_payment_sources) != 1 or add_on_payment_sources.pop() != payment_source:
            raise forms.ValidationError(
                'Please select add-ons with the same payment source as subscription plan'
            )

        renewing_add_ons = [x for x in add_ons if x.renewing]
        if len(renewing_add_ons) != 1:
            raise forms.ValidationError('Please choose 1 recurring staff add-on')

    def _clean_sms_config(self):
        if not settings.SMS_LIMITS_IN_SUBSCRIPTION:
            return
        system_sms_config = self.cleaned_data.get('sms_config_system')
        if (
            not self.cleaned_data.get('sms_package')
            and system_sms_config != self.SMSConfigEnum.UNLIMITED
        ):
            raise forms.ValidationError(
                'Please assign prepaid SMS package if system SMS are payable'
            )
        invite_sms_config = self.cleaned_data.get('sms_config_invites')
        free_sms_invites_count = self.cleaned_data.get('free_sms_invites_count')
        if invite_sms_config != self.SMSConfigEnum.FIRST_N_FREE and (
            free_sms_invites_count is not None and free_sms_invites_count > 0
        ):
            raise forms.ValidationError(
                'You can set free SMS invites amount only when proper option '
                'in invite SMS config is selected '
            )
        if (
            invite_sms_config == self.SMSConfigEnum.FIRST_N_FREE
            and free_sms_invites_count is None
            or (free_sms_invites_count is not None and free_sms_invites_count <= 0)
        ):
            raise forms.ValidationError(
                'Please set free SMS invites amount to value greater than 0 or '
                'change option in invite SMS config'
            )

    def clean(self):
        cleaned_data = super(SubscriptionListingForm, self).clean()
        self._clean_sms_config()
        product_ids = (
            (cleaned_data.get('apple_id'), 'apple_id'),
            (cleaned_data.get('braintree_id'), 'braintree_id'),
            (cleaned_data.get('google_id'), 'google_id'),
        )
        truth_products = [x for x in product_ids if bool(x[0])]
        if not truth_products:
            self._clean_staff_add_ons(payment_source=Business.PaymentSource.OFFLINE)
            return cleaned_data
        # check that only one product id provided
        if len(truth_products) > 1:
            raise forms.ValidationError(
                _(
                    'More than one product provider chosen. '
                    'Please chose only one, '
                    'and provide apple_id, braintree_id or google_id'
                ),
                params={
                    'code': 'invalid',
                    'field': 'apple_id, braintree_id, google_id',
                    'type': 'validation',
                },
            )
        product_id, product_key = truth_products[0]
        # check product with given product id doesn't exist
        filter_by_product_id = {
            product_key: product_id,
        }
        qs = SubscriptionListing.objects.filter(**filter_by_product_id)
        if self.instance.id:
            qs = qs.exclude(id=self.instance.id)
        if qs.exists():
            raise forms.ValidationError(
                _('Product with product_id {} already exists: Product_Id {}').format(
                    product_id, qs.first().id
                ),
                params={
                    'code': 'invalid',
                    'field': product_key,
                    'type': 'validation',
                },
            )

        self._clean_staff_add_ons(product_key=product_key)
        max_staffer_num = cleaned_data.get('max_staffer_num')
        min_staffer_num = cleaned_data.get('min_staffer_num')
        if (
            self.instance.id
            and (
                hasattr(self.instance, 'subscriptionlistingswitch')
                or self.instance.downgrades.exists()
                or self.instance.upgrades.exists()
            )
            and (
                min_staffer_num != self.instance.min_staffer_num
                or max_staffer_num != self.instance.max_staffer_num
            )
        ):
            raise forms.ValidationError(
                'Can\'t change min_staffer_num/max_staffer_num.'
                ' Modify or remove Subscription product switches first.'
            )

        return cleaned_data

    def set_sms_limits(self):
        """Save sms limits to be consumable by
        webapps.notification.tools.bulk_apply_sms_limits() function."""
        sms_config = self.instance.sms_config.get('first_n_sms_limits', {})
        # Map form values to model values
        form_to_model = {
            self.SMSConfigEnum.UNLIMITED: None,
            self.SMSConfigEnum.PAID: 0,
        }
        invite_sms_config = self.cleaned_data['sms_config_invites']
        system_sms_config = self.cleaned_data['sms_config_system']
        marketing_sms_config = self.cleaned_data['sms_config_marketing']

        if invite_sms_config == self.SMSConfigEnum.FIRST_N_FREE:
            sms_count = self.cleaned_data['free_sms_invites_count']
            sms_config[SMSTypeEnum.INVITATION.value] = sms_count
        else:
            sms_config[SMSTypeEnum.INVITATION.value] = form_to_model[invite_sms_config]

        sms_config[SMSTypeEnum.SYSTEM.value] = form_to_model[system_sms_config]
        sms_config[SMSTypeEnum.MARKETING.value] = form_to_model[marketing_sms_config]

        self.instance.sms_config['first_n_sms_limits'] = sms_config

    def save(self, commit=True):
        super(SubscriptionListingForm, self).save(commit=False)
        if settings.SMS_LIMITS_IN_SUBSCRIPTION:
            self.set_sms_limits()
        self.instance.save()
        self.save_m2m()
        return self.instance


class SubscriptionListingToolForm(SubscriptionListingForm):
    class Meta:
        model = SubscriptionListing
        exclude = [
            'extra_data',
            'recurrence',
            'discount_duration',
        ]


class SubscriptionListingAdmin(NoDelMixin, ReadOnlyFieldsMixin, BaseModelAdmin):
    list_display = (
        'id',
        'name',
        'active',
        'price',
        'recurrence_nice',
        'apple_id',
        'google_id',
        'braintree_id',
        'plan_type',
        'order',
    )
    exclude = (
        'extra_data',
        'recurrence',
    )
    list_filter = (
        'active',
        'plan_type',
        SubscriptionMethodFilter,
    )
    search_fields = [
        'name',
        '=apple_id',
        '=google_id',
        '=braintree_id',
        '=order',
    ]
    readonly_fields = ('created', 'updated', 'deleted')
    form = SubscriptionListingForm
    change_form_template = 'admin/change_forms/change_form__subscriptionlisting.html'

    @staticmethod
    def is_eligible_user(request):
        user = get_user_from_django_request(request)
        if user is None:
            return False
        return (
            user.is_superuser
            or user.groups.filter(name=GroupName.ADD_SUBSCRIPTION_LISTING).exists()
        )

    def has_add_permission(self, request, obj=None):
        if not settings.LIVE_DEPLOYMENT:
            return True
        else:
            return self.is_eligible_user(request)

    def get_form(self, request, obj=None, **kwargs):
        form = super(SubscriptionListingAdmin, self).get_form(request, obj, **kwargs)
        # Non-model fields can't be included in get_readonly_fields() in admin
        # TODO: Editing form's base_fields changes them forever, find other way
        # TODO: wrong field order for non eligible user
        if not self.is_eligible_user(request):
            readonly_fields = form.base_fields
        elif obj and obj.purchases.exists():
            readonly_fields = {
                'charge_for_staffers',
                'max_payable_staffer',
                'staff_add_ons',
                'postpaid_sms_limit',
                'sms_package',
                'sms_config_invites',
                'free_sms_invites_count',
                'sms_config_marketing',
                'sms_config_system',
            }
        else:
            readonly_fields = set()
        for field_name in form.base_fields:
            if field_name in readonly_fields:
                form.base_fields[field_name].disabled = True
            else:
                form.base_fields[field_name].disabled = False
        return form

    def get_readonly_fields(self, request, obj=None):
        if not self.is_eligible_user(request):
            return ReadOnlyFieldsMixin.get_readonly_fields(self, request, obj=obj)
        else:
            return self.readonly_fields

    def recurrence_nice(self, obj):
        units = ('years', 'months', 'days', 'hours', 'minutes', 'seconds', 'microseconds')
        return ', '.join(
            '%s %s' % (getattr(obj.recurrence, u), u) for u in units if getattr(obj.recurrence, u)
        )

    recurrence_nice.short_description = 'Recurrence'
    recurrence_nice.admin_order_field = 'recurrence'


class SubscriptionDiscountAdmin(NoDelMixin, ReadOnlyFieldsMixin, BaseModelAdmin):
    list_display = (
        'id',
        'subscription',
        'amount',
        'inherited_from_id',
        'number_of_billing_cycles',
        'success',
    )
    raw_id_fields = (
        'parent',
        'subscription',
    )
    search_fields = (
        '=id',
        '=subscription__id',
        '=subscription__business__id',
        '=inherited_from_id',
    )
    list_filter = ('success',)

    def has_add_permission(self, request, obj=None):
        return False


class SubscriptionOfferAdmin(BaseModelAdmin):
    list_display = (
        'id',
        'business',
        'products_data',
        'valid_from',
        'valid_to',
    )
    preserve_filters = True

    ordering = ('-id',)

    search_fields = (
        '=id',
        '=business__id',
        '=deeplink',
    )
    raw_id_fields = (
        'business',
        'products',
    )
    # ACHTUNG: This doesn't contain all fields. Look at get_fields().
    fields = (
        'business',
        'valid_from',
        'valid_to',
        'date_start',
        'url',
        'discount',
        'discount_billing_cycles',
    )
    readonly_fields = (
        'url',
        'products_data',
    )
    form = SubscriptionOfferAdminForm
    # 'Disable' going to add view without business id
    change_list_template = 'admin/change_lists/change_list__subscriptionoffer.html'
    change_form_template = 'admin/change_forms/change_form__subscriptionoffer.html'

    class Media:
        js = (static('admin/js/admin_extra.js'),)

    def products_data(self, obj):
        html_links = (get_link_html(p) for p in obj.products.all())
        return format_html(', '.join(html_links))

    products_data.short_description = 'Products'

    def url(self, obj):
        if obj.deeplink:
            return format_html(
                '''<span onclick="Utils.copyToClipboard(\'{}\');"
style="cursor: pointer" title="Click to copy deeplink">{}&ensp;
<i class="icon-share icon-alpha75"></i>
</span>''',
                obj.deeplink,
                obj.deeplink,
            )
        else:
            return 'Url will be visible after you save the offer'

    def get_queryset(self, request):
        qs = super(SubscriptionOfferAdmin, self).get_queryset(request)
        qs = qs.prefetch_related('products')
        return qs

    def response_add(self, request, obj, post_url_continue=None):
        return redirect(reverse('admin:purchase_subscriptionoffer_change', args=(obj.id,)))

    def formfield_for_dbfield(self, db_field, request, **kwargs):
        if db_field.name == 'valid_to':
            return db_field.formfield(
                form_class=forms.fields.SplitDateTimeField,
                widget=AdminSplitDateTimeWithChoices(
                    attrs={
                        'from_field': 'valid_from',
                    }
                ),
            )

        return super(SubscriptionOfferAdmin, self).formfield_for_dbfield(
            db_field, request, **kwargs
        )

    def get_form(self, request, obj=None, **kwargs):
        form = super(SubscriptionOfferAdmin, self).get_form(request, obj, **kwargs)
        if 'business' in form.base_fields:
            form.base_fields['business'].initial = request.GET.get('business_id')
        return form

    def get_fields(self, request, obj=None):
        """Do not allow to add new products once deeplink is
        generated."""
        if obj:
            return ('products_data',) + self.fields
        else:
            return ('products',) + self.fields

    def get_readonly_fields(self, request, obj=None):
        readonly_fields = super(SubscriptionOfferAdmin, self).get_readonly_fields(request, obj)
        if obj:
            return ('business',) + readonly_fields
        else:
            return readonly_fields


class PromotionAdmin(BaseModelAdmin):
    list_display = (
        'id',
        'name',
        'products_data',
        'valid_from',
        'valid_to',
    )
    preserve_filters = True

    ordering = ('-id',)

    search_fields = (
        '=id',
        '=name',
        '=products__name',
        '=deeplink',
        '=campaign',
    )
    raw_id_fields = ('products',)
    readonly_fields = (
        'url',
        'products_data',
    )

    fieldsets = (
        (
            None,
            {
                'fields': (
                    'products',
                    'name',
                    'description',
                    'valid_from',
                    'valid_to',
                    'date_start',
                    'discount',
                    'discount_billing_cycles',
                    'url',
                )
            },
        ),
        ('Analytics info', {'fields': ('campaign',)}),
    )

    form = PromotionAdminForm
    change_form_template = 'admin/change_forms/change_form__promotion.html'

    class Media:
        js = (static('admin/js/admin_extra.js'),)

    def url(self, obj):
        if obj.deeplink:
            return format_html(
                '''<span onclick="Utils.copyToClipboard(\'{}\');"
style="cursor: pointer" title="Click to copy deeplink">{}&ensp;
<i class="icon-share icon-alpha75"></i>
</span>''',
                obj.deeplink,
                obj.deeplink,
            )
        else:
            return 'Url will be visible after you save the offer'

    def get_queryset(self, request):
        qs = super(PromotionAdmin, self).get_queryset(request)
        qs = qs.prefetch_related('products')
        return qs

    def products_data(self, obj):
        html_links = (get_link_html(p) for p in obj.products.all())
        return format_html(', '.join(html_links))

    products_data.short_description = 'Products'

    def formfield_for_dbfield(self, db_field, request, **kwargs):
        if db_field.name == 'valid_to':
            return db_field.formfield(
                form_class=forms.fields.SplitDateTimeField,
                widget=AdminSplitDateTimeWithChoices(
                    attrs={
                        'from_field': 'valid_from',
                    }
                ),
            )

        return super(PromotionAdmin, self).formfield_for_dbfield(db_field, request, **kwargs)

    def get_fieldsets(self, request, obj=None):
        """Do not allow to add new products once deeplink is
        generated."""
        if self.fieldsets:
            fieldsets = deepcopy(self.fieldsets)
        else:
            fieldsets = [(None, {'fields': self.get_fields(request, obj)})]

        if obj is not None:
            for name, options in fieldsets:
                fields = list(options['fields'])
                if 'products' not in fields:
                    continue

                index = fields.index('products')
                if index >= 0:
                    fields[index] = 'products_data'
                    options['fields'] = fields

        return fieldsets


class CoachingForm(forms.ModelForm):
    class Meta:
        model = Coaching
        fields = ('subscription', 'price', 'paid_to', 'currency_code')

    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop('request', None)
        super().__init__(*args, **kwargs)

    def save(self, *args, **kwargs):
        obj = super().save(*args, **kwargs)
        if self.request:
            obj.operator = self.request.user
        obj.save()
        return obj


class CoachingAdmin(BaseModelAdmin):
    model = Coaching

    list_display = (
        'call_id',
        'call_business_id',
        'subscription',
        'price',
        'paid_to',
        'currency_code',
    )

    fields = list_display

    raw_id_fields = [
        'subscription',
        'business',
    ]

    readonly_fields = (
        'call_id',
        'call_business_id',
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._coaching_before_change = None

    def call_id(self, obj):
        return obj.id

    call_id.short_description = 'Coaching ID'

    def call_business_id(self, obj):
        return obj.business.id

    call_business_id.short_description = 'Business ID'

    def save_model(self, request, obj, form, change):
        super().save_model(request, obj, form, change)
        self._coaching_before_change = CoachingHistory.add(
            form.instance,
            CoachingHistory.extract_vars(form.instance),
            self._coaching_before_change or {},
            operator=get_user_from_django_request(request),
        )

    def get_form(self, request, obj=None, **kwargs):
        if obj is not None:
            self._coaching_before_change = CoachingHistory.extract_vars(obj)
        return super().get_form(request, obj, **kwargs)

    def render_change_form(self, request, context, **kwargs):
        context['adminform'].form.fields['subscription'].queryset = Subscription.objects.filter(
            source=Business.PaymentSource.OFFLINE
        )
        return super().render_change_form(request, context, **kwargs)


"""
class SubscriptionBusinessDiscountForm(forms.ModelForm):
    class Meta:
        model = SubscriptionBusinessDiscount
        exclude = []

    discount_percentage = forms.DecimalField(
        max_digits=3,
        decimal_places=0,
        required=True,
        label='Discount %',
        min_value=-100,
        max_value=100,
    )


class SubscriptionBusinessDiscountAdmin(NoDelMixin, BaseModelAdmin):
    # TODO: Remove! This view should be in subscription admin as inline
    form = SubscriptionBusinessDiscountForm

    list_display = (
        'id',
        'subscription',
        'discount_percentage',
        #'discount_duration',
        'discount_type',
    )
    raw_id_fields = (
        'subscription',
    )
    search_fields = (
        '=id',
        '=subscription__id',
        '=subscription__business__id',
    )"""


class AddOnAdmin(NoDelMixin, ReadOnlyFieldsMixin, BaseModelAdmin):
    list_display = (
        'id',
        'name',
        'add_on_type',
        'external_id',
        'price',
    )

    ordering = ('-id',)

    search_fields = (
        '=id',
        '=name',
        '=add_on_type',
        '=external_id',
    )

    fields = (
        'name',
        'source',
        'add_on_type',
        'external_id',
        'price_amount',
        'currency_code',
        'renewing',
    )

    readonly_fields = ('price',)

    exclude = ('deleted',)

    def price(self, obj):
        return '{} {}'.format(obj.price_amount, obj.currency_code)

    @staticmethod
    def is_eligible_user(request):
        user = get_user_from_django_request(request)
        if user is None:
            return False
        return (
            user.is_superuser
            or user.groups.filter(name=GroupName.ADD_SUBSCRIPTION_LISTING).exists()
        )

    def has_add_permission(self, request, obj=None):
        if not settings.LIVE_DEPLOYMENT:
            return True
        else:
            return self.is_eligible_user(request)

    def get_readonly_fields(self, request, obj=None):
        if (
            not self.is_eligible_user(request)
            or
            # Not by related_name as there are 2 relations to the same table
            (obj and SubscriptionAddOn.objects.filter(add_on=obj).exists())
        ):
            return ReadOnlyFieldsMixin.get_readonly_fields(self, request, obj=obj)
        else:
            return self.readonly_fields


class SubscriptionAddOnAdmin(NoDelMixin, ReadOnlyFieldsMixin, BaseModelAdmin):
    list_display = (
        'id',
        'business_id',
        'subscription_id',
        'date_start',
        'date_end',
        'effective_date',
        'quantity',
    )

    ordering = ('-id',)

    search_fields = (
        '=id',
        '=subscription__id',
        '=subscription__business__id',
        '=date_start',
        '=date_end',
        '=effective_date',
    )

    fields = (
        'id',
        'subscription',
        'source_subscription',
        'add_on',
        'date_start',
        'date_end',
        'effective_date',
        'quantity',
    )

    readonly_fields = fields

    def has_add_permission(self, request, obj=None):
        return False

    def business_id(self, obj):
        if obj:
            return obj.subscription.business_id


class SubscriptionListingSwitchForm(forms.ModelForm):
    class Meta:
        model = SubscriptionListingSwitch
        fields = ('product', 'downgrade_product', 'upgrade_product')

    def clean(self):
        cleaned_data = super(SubscriptionListingSwitchForm, self).clean()
        product = cleaned_data['product']
        downgrade_product = cleaned_data.get('downgrade_product')
        upgrade_product = cleaned_data.get('upgrade_product')
        if downgrade_product and (
            downgrade_product.max_staffer_num is None
            or product.min_staffer_num is None
            or downgrade_product.max_staffer_num != product.min_staffer_num - 1
        ):
            raise forms.ValidationError(
                'Max staffer num for downgrade product has to be 1 less than'
                ' (current) product min staffer num'
            )
        if upgrade_product and (
            upgrade_product.min_staffer_num is None
            or product.max_staffer_num is None
            or upgrade_product.min_staffer_num != product.max_staffer_num + 1
        ):
            raise forms.ValidationError(
                'Min staffer num for upgrade product has to be 1 more than'
                ' (current) product max staffer num'
            )

        return cleaned_data


class SubscriptionListingSwitchAdmin(ReadOnlyFieldsMixin, BaseModelAdmin):
    list_display = (
        'id',
        'product_name',
        'downgrade_product_name',
        'upgrade_product_name',
    )
    search_fields = [
        '=product__id',
        '=downgrade_product__id',
        '=upgrade_product__id',
    ]
    form = SubscriptionListingSwitchForm

    @staticmethod
    def is_eligible_user(request):
        user = get_user_from_django_request(request)
        if user is None:
            return False
        return (
            user.is_superuser
            or user.groups.filter(name=GroupName.ADD_SUBSCRIPTION_LISTING).exists()
        )

    def has_add_permission(self, request, obj=None):
        if not settings.LIVE_DEPLOYMENT:
            return True
        else:
            return self.is_eligible_user(request)

    def has_delete_permission(self, request, obj=None):
        if not settings.LIVE_DEPLOYMENT:
            return True
        else:
            return self.is_eligible_user(request)

    def get_readonly_fields(self, request, obj=None):
        if not self.is_eligible_user(request):
            return ReadOnlyFieldsMixin.get_readonly_fields(self, request, obj=obj)
        else:
            return self.readonly_fields

    def product_name(self, obj):
        return obj.product.name

    def downgrade_product_name(self, obj):
        return getattr(obj.downgrade_product, 'name', '')

    def upgrade_product_name(self, obj):
        return getattr(obj.upgrade_product, 'name', '')


class SubscriptionSMSPackageAdmin(NoDelMixin, ReadOnlyFieldsMixin, BaseModelAdmin):
    list_display = (
        'id',
        'subscription_id',
        'business_id',
        'package_id',
        'date_start',
        'date_end',
        'amount_per_billing_cycle',
    )

    ordering = ('-id',)

    search_fields = (
        '=id',
        '=subscription__id',
        '=subscription__business__id',
        '=date_start',
        '=date_end',
        '=package__id',
    )

    fields = (
        'id',
        'subscription',
        'business_id',
        'package',
        'date_start',
        'date_end',
        'amount_per_billing_cycle',
    )

    readonly_fields = fields

    def has_add_permission(self, request, obj=None):
        return False

    def business_id(self, obj):
        if obj:
            return obj.subscription.business_id


class SMSPackageForm(forms.ModelForm):
    """Allows to create/edit SMSPackage along with AddOn."""

    FREE = 'F'
    PAID = 'P'

    CHARGEABLE_CHOICES = (
        (FREE, 'Free'),
        (PAID, 'Paid'),
    )

    chargeable = forms.TypedChoiceField(
        choices=CHARGEABLE_CHOICES, coerce=lambda x: x == 'P', label='Type'
    )
    source = forms.ChoiceField(
        choices=Business.PaymentSource.choices_with_empty(),
        label='Payment source',
        required=False,
    )
    price_amount = forms.DecimalField(decimal_places=2, max_digits=6, required=False)
    currency_code = forms.CharField(
        required=False,
        validators=[is_currency],
    )
    external_id = forms.CharField(required=False, label='Add-on external ID (online only)')

    add_on_fields = (
        'source',
        'external_id',
        'price_amount',
        'currency_code',
    )

    class Meta:
        model = SMSPackage
        fields = (
            'name',
            'amount_per_billing_cycle',
            'chargeable',
            'source',
            'external_id',
            'price_amount',
            'currency_code',
        )

    def __init__(self, *args, **kwargs):
        instance = kwargs.get('instance')
        initial = kwargs.get('initial', {})
        if instance:
            if instance.chargeable:
                chargeable = self.PAID
            else:
                chargeable = self.FREE
            initial['chargeable'] = chargeable
            if instance.add_on:
                for field_name in self.add_on_fields:
                    initial[field_name] = getattr(instance.add_on, field_name)
            kwargs['initial'] = initial
        super(SMSPackageForm, self).__init__(*args, **kwargs)

    def clean(self):
        super(SMSPackageForm, self).clean()
        chargeable = self.cleaned_data.get('chargeable')
        price = self.cleaned_data.get('price_amount')
        currency = self.cleaned_data.get('currency_code')
        payment_source = self.cleaned_data.get('source')
        external_id = self.cleaned_data.get('external_id')
        if chargeable and price is not None and price <= 0:
            raise forms.ValidationError('Please provide correct price (greater than 0)')
        if chargeable and not all([price, currency, payment_source]):
            raise forms.ValidationError('Please fill in price, currency and payment source')
        if chargeable and payment_source == Business.PaymentSource.BRAINTREE and not external_id:
            raise forms.ValidationError('Please fill in add-on external ID')
        if not chargeable and any([price, currency, external_id, payment_source]):
            raise forms.ValidationError(
                'You can\'t set price, currency, add-on ID and payment source '
                'for FREE sms package'
            )
        return self.cleaned_data

    def save(self, commit=True):
        super(SMSPackageForm, self).save(commit=False)
        chargeable = self.cleaned_data['chargeable']
        with transaction.atomic():
            if not chargeable and self.instance.add_on:
                self.instance.add_on.soft_delete()
                self.instance.add_on = None
            elif self.instance.add_on:
                AddOn.objects.filter(id=self.instance.add_on_id).update(
                    source=self.cleaned_data['source'],
                    external_id=self.cleaned_data['external_id'],
                    price_amount=self.cleaned_data['price_amount'],
                    currency_code=self.cleaned_data['currency_code'],
                )
            elif chargeable:
                add_on = AddOn.objects.create(
                    source=self.cleaned_data['source'],
                    add_on_type=AddOn.AddOnType.SMS,
                    external_id=self.cleaned_data['external_id'],
                    name='{} add-on'.format(self.instance.name[:-7]),
                    price_amount=self.cleaned_data['price_amount'],
                    currency_code=self.cleaned_data['currency_code'],
                    renewing=True,
                )
                self.instance.add_on = add_on
            self.instance.save()
            self.save_m2m()
        return self.instance


class SMSPackageAdmin(NoDelMixin, BaseModelAdmin):
    list_display = (
        'id',
        'name',
        'payment_source',
        'price',
        'amount_per_billing_cycle',
    )

    ordering = ('-id',)

    search_fields = (
        '=id',
        '=add_on__source',
    )

    fields = (
        'id',
        'name',
        'amount_per_billing_cycle',
        'chargeable',
    ) + SMSPackageForm.add_on_fields

    readonly_fields = ('id',)

    form = SMSPackageForm

    def has_add_permission(self, request, obj=None):
        if not settings.LIVE_DEPLOYMENT:
            return True
        else:
            return SubscriptionListingAdmin.is_eligible_user(request)

    def get_form(self, request, obj=None, **kwargs):
        form = super(SMSPackageAdmin, self).get_form(request, obj, **kwargs)
        # Non-model fields can't be included in get_readonly_fields() in admin
        if not SubscriptionListingAdmin.is_eligible_user(request):
            readonly_fields = self.fields
        else:
            readonly_fields = set()
        for field_name in form.base_fields:
            if field_name in readonly_fields:
                form.base_fields[field_name].disabled = True
            else:
                form.base_fields[field_name].disabled = False
        return form

    def has_delete_permission(self, request, obj=None):
        if not settings.LIVE_DEPLOYMENT:
            return True
        else:
            return SubscriptionListingAdmin.is_eligible_user(request)

    def payment_source(self, obj):
        if obj and obj.add_on:
            return obj.add_on.get_source_display()

    def price(self, obj):
        if obj and not obj.add_on and not obj.chargeable:
            return 'Free'
        if obj and obj.add_on:
            return '{} {}'.format(obj.add_on.price_amount, obj.add_on.currency_code)


admin.site.register(SMSPackage, SMSPackageAdmin)
admin.site.register(Subscription, SubscriptionAdmin)
admin.site.register(SubscriptionSMSPackage, SubscriptionSMSPackageAdmin)
admin.site.register(SubscriptionTransaction, SubscriptionTransactionAdmin)
admin.site.register(SubscriptionListing, SubscriptionListingAdmin)
admin.site.register(SubscriptionDiscount, SubscriptionDiscountAdmin)
admin.site.register(SubscriptionOffer, SubscriptionOfferAdmin)
admin.site.register(Promotion, PromotionAdmin)
admin.site.register(Coaching, CoachingAdmin)
admin.site.register(AddOn, AddOnAdmin)
admin.site.register(SubscriptionAddOn, SubscriptionAddOnAdmin)
admin.site.register(SubscriptionListingSwitch, SubscriptionListingSwitchAdmin)
admin.site.register(SubscriptionBuyer, SubscriptionBuyerAdmin)
admin.site.register(InvoiceAddress, InvoiceAddressAdmin)
