import collections
import datetime
import logging
import io
import traceback

import pandas as pd

from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.db import IntegrityError
from django.db.models import Q
from pytz import UTC
from requests import request, exceptions

from lib.apple.exceptions import AppleDisabledException
from lib.celery_tools import celery_task
from lib.db import using_db_for_reads, PRIMARY_DB
from lib.email import send_email
from lib.tools import tznow
from lib.time_24_hour import format_datetime
from webapps.admin_extra.consts import XLS_MIME
from webapps.business.models import Business
from webapps.notification.enums import ScheduleState
from webapps.notification.models import NotificationSchedule
from webapps.notification.scenarios import simple_scenario
from webapps.purchase.models import (
    Subscription,
    SubscriptionListing,
    SubscriptionTransaction,
)
from webapps.purchase.serializers import milli_to_datetime


logger = logging.getLogger('booksy.purchase')


class AppleReceiptError(Exception):
    def __init__(self, business_id, receipt=None):
        self.business_id = business_id
        self.receipt = receipt

    def __repr__(self):
        return '%s (business_id=%s)' % (self.__class__.__name__, self.business_id)


class BusinessMismatchError(AppleReceiptError):
    def __init__(self, business_id_set, **kwargs):
        self.business_id_set = business_id_set
        super(BusinessMismatchError, self).__init__(**kwargs)

    def __repr__(self):
        return '%s (business_id=%s, business_id_set=%s)' % (
            self.__class__.__name__,
            self.business_id,
            self.business_id_set,
        )


class InvalidReceiptError(AppleReceiptError):
    pass


class InvalidReceiptMessageError(InvalidReceiptError):
    def __init__(self, business_id, receipt=None, msg=None):
        super(InvalidReceiptMessageError, self).__init__(business_id, receipt)
        self.msg = msg

    def __repr__(self):
        return '{} {!r}(business_id={})'.format(
            self.__class__.__name__,
            self.msg,
            self.business_id,
        )


def dt_to_timestamp(dt):
    return int((dt - datetime.datetime(1970, 1, 1, tzinfo=UTC)).total_seconds())


status_message_mapping = {
    21000: 'The App Store could not read the JSON object you provided.',
    21002: 'The data in the receipt-data property was malformed or missing.',
    21003: 'The receipt could not be authenticated.',
    21004: 'The shared secret you provided '
    'does not match the shared secret on file for your account.',
    21005: 'The receipt server is not currently available.',
    21006: 'This receipt is valid but the subscription has expired. '
    'When this status code is returned to your server, '
    'the receipt data is also decoded and '
    'returned as part of the response. '
    'Only returned for '
    'iOS 6 style transaction receipts for auto-renewable subscriptions.',
    21007: 'This receipt is from the test environment, '
    'but it was sent to the production environment '
    'for verification. Send it to the test environment instead.',
    21008: 'This receipt is from the production environment, '
    'but it was sent to the test environment for verification. '
    'Send it to the production environment instead.',
    21010: 'This receipt could not be authorized. '
    'Treat this the same as if a purchase was never made.',
}


def fetch_receipt_data(receipt_data, raw_response=False):
    if settings.APPLE_DISABLED:
        raise AppleDisabledException

    payload = {
        'receipt-data': receipt_data,
        'password': settings.APPLE_RECEIPT_VALIDATION_PASSWORD,
    }

    is_sandbox = False
    resp = request(
        method='post',
        url=settings.APPLE_RECEIPT_URL['production'],
        json=payload,
        timeout=10.0,
    ).json()

    if resp['status'] == 21007:
        is_sandbox = True
        resp = request(
            method='post',
            url=settings.APPLE_RECEIPT_URL['sandbox'],
            json=payload,
            timeout=10.0,
        ).json()

    if resp['status'] != 0:
        msg = status_message_mapping.get(resp['status'])
        if msg is None and 21199 > resp['status'] > 21100:
            msg = 'Internal data access error.'
        else:
            msg = 'Unknown error. Error Code {}'.format(resp['status'])
        error = '[APPLE]. {}'.format(msg)
        raise ValueError(error)

    if raw_response:
        return resp, is_sandbox

    try:
        return resp['latest_receipt_info'], is_sandbox
    except KeyError:
        return None, None


def split_iap_into_subs(iaps):
    iaps_original = collections.defaultdict(list)

    for iap in iaps:
        iaps_original[f"{iap['original_transaction_id']}_{iap['product_id']}"].append(iap)

    return {
        sub_period[0]['web_order_line_item_id']: sub_period
        for sub_period in list(iaps_original.values())
    }


def get_viable_transaction_info(iap):
    data = dict()
    data['product_id'] = iap.get('product_id')
    default_language = settings.LANGUAGE_CODE[:2].lower()
    if iap.get('cancellation_date'):
        state = SubscriptionTransaction.State.CANCELLED
        reason = iap.get('cancellation_reason')
        cancel_datetime = milli_to_datetime(iap['cancellation_date_ms'])
        data['cancellation_date'] = format_datetime(
            cancel_datetime,
            'long_date_ymd',
            default_language,
        )
        data['cancellation_time'] = format_datetime(
            cancel_datetime,
            'time_hm',
            default_language,
        )
        if reason:
            data['cancellation_reason'] = reason
    else:
        state = None

    if iap.get('expiration_intent'):
        state = SubscriptionTransaction.State.FAILED
        data['expiration_intent'] = iap['expiration_intent']
    if iap.get('is_in_billing_retry_period'):
        data['is_in_billing_retry_period'] = iap['is_in_billing_retry_period']

    return state, data


def update_transactions(sub, iaps):
    try:
        last = sub.transactions.latest('charged_on')
    except SubscriptionTransaction.DoesNotExist:
        start = int(iaps[0]['purchase_date_ms']) // 1000
    else:
        start = dt_to_timestamp(last.charged_on)

    missing = [x for x in iaps if int(x['purchase_date_ms']) // 1000 >= start]

    for iap in missing:
        state, data = get_viable_transaction_info(iap)
        SubscriptionTransaction.new(
            subscription=sub,
            charged_on=milli_to_datetime(iap['purchase_date_ms']),
            external_id=iap['web_order_line_item_id'],
            external_transaction_id=iap['transaction_id'],
            state=state,
            data=data,
        )

    expiry_date = milli_to_datetime(iaps[-1]['expires_date_ms'])
    if expiry_date < tznow():
        data = get_viable_transaction_info(iaps[-1])
        if iaps[-1].get('cancellation_date'):
            state = SubscriptionTransaction.State.CANCELLED
        else:
            state = SubscriptionTransaction.State.FAILED
        SubscriptionTransaction.new(
            subscription=sub,
            charged_on=expiry_date,
            state=state,
            external_transaction_id=iaps[-1]['transaction_id'],
            data=data,
        )


def update_sub(sub, iaps, business_id, receipt):
    """ACHTUNG: sub is a (sub, created) tuple"""
    if sub is None:
        sub = Subscription(source=Business.PaymentSource.ITUNES)
        created = True
    else:
        # ACHTUNG - tuple is unpacked
        sub = sub[0]
        created = False

    product_id = iaps[0]['product_id']
    try:
        sub.product = SubscriptionListing.objects.get(apple_id=product_id)
    except SubscriptionListing.DoesNotExist:
        logger.error(
            '[APPLE] unknown product (product %s, business %s)',
            product_id,
            business_id,
        )
        return None, False

    sub.business_id = business_id
    sub.start = milli_to_datetime(iaps[0]['purchase_date_ms'])
    sub.expiry = milli_to_datetime(iaps[-1]['expires_date_ms'])
    sub.renewing = sub.expiry > tznow()
    if (
        not sub.renewing
        and Business.objects.filter(
            id=business_id, payment_source=Business.PaymentSource.ITUNES
        ).exists()
        and sub.expiry > tznow() - datetime.timedelta(days=30)
        and not Subscription.objects.filter(
            source=Business.PaymentSource.ITUNES, renewing=True, business_id=business_id
        )
        .exclude(id=sub.id)
        .exists()
    ):
        sub.renewing = True

    sub.receipt = dict(
        receipt_data=sub.receipt.get('receipt_data', receipt),
        web_order_line_item_id=iaps[0]['web_order_line_item_id'],
        # TODO: Duplicates?
        original_transaction_id=iaps[0]['original_transaction_id'],
    )

    try:
        sub.save()
    except IntegrityError:
        return None, False

    update_transactions(sub=sub, iaps=iaps)

    return sub, created


def schedule_renew(
    subs,
    iaps,
    business_id,
    is_sandbox=not settings.LIVE_DEPLOYMENT,
):
    # we will only cancel tasks with transactions from this receipt
    # which is equivalent to single Apple ID account
    id_query = Q()
    for web_order_line_item_id in iaps:
        id_query |= Q(parameters__web_order_line_item_id=f'"{web_order_line_item_id}"')

    # business may have been called as str or int
    biz_query = Q(parameters__business_id=f'"{business_id}"') | Q(
        parameters__business_id=business_id
    )

    # above queries would be much simpler if parameters were JSONField!

    task_name = AppleSubscriptionStatusTask.SCENARIO_NAME
    NotificationSchedule.objects.filter(
        id_query,
        biz_query,
        task_id__startswith=task_name,
        scheduled__gt=tznow(),
        state=ScheduleState.PENDING,
    ).update(
        state=ScheduleState.CANCELED,
    )

    subs.sort(key=lambda x: x.expiry, reverse=True)

    try:
        earliest_expiry = subs[0].expiry
        web_order_line_item_id = subs[0].receipt['web_order_line_item_id']
    except IndexError:
        return

    for sub in subs:
        if sub is None:
            continue
        if sub.expiry < tznow():
            break
        if sub.expiry < earliest_expiry:
            earliest_expiry = sub.expiry
            web_order_line_item_id = sub.receipt['web_order_line_item_id']

    if earliest_expiry > tznow():
        if not is_sandbox:
            eta = earliest_expiry + datetime.timedelta(days=1)
        else:
            eta = earliest_expiry + datetime.timedelta(minutes=1)
        AppleSubscriptionStatusTask.apply_async(
            kwargs=dict(
                business_id=business_id,
                web_order_line_item_id=web_order_line_item_id,
            ),
            eta=eta,
        )


@simple_scenario
@using_db_for_reads(PRIMARY_DB)
def AppleSubscriptionStatusTask(
    business_id,
    receipt=None,
    web_order_line_item_id=None,
    save_receipt=True,
    update_business_status=True,
    number_of_retry=0,
    origin=None,
):
    """Updates all subscriptions taken from iOS 7-style receipt,
    a.k.a. "Grand Unified Receipt".

    If this receipt was never acknowledged, you have to supply it as
    an argument, otherwise web_order_line_item_id is sufficient --
    even to renew subscription not associated with given ID -- as long as
    this transaction ID was on receipt of interest.

    By specifying save_receipt=False, only validation checks for business and
    given receipt (thus Apple ID account) will be done, but the Subscriptions
    themselves won't be neither created nor modified.
    """
    MAX_RETRY_NUMBER = 3

    def wrapper(
        business_id,
        receipt=None,
        web_order_line_item_id=None,
        save_receipt=True,
        update_business_status=True,
        number_of_retry=0,
    ):
        if receipt is None and web_order_line_item_id is not None:
            try:
                receipt = (
                    Subscription.objects.filter(
                        source=Business.PaymentSource.ITUNES,
                        receipt__web_order_line_item_id=web_order_line_item_id,
                    )
                    .first()
                    .receipt.get('receipt_data')
                )
            except Subscription.DoesNotExist:
                pass
        if receipt is None:
            receipt = (
                Subscription.objects.filter(
                    business=business_id,
                    source=Business.PaymentSource.ITUNES,
                )
                .latest('expiry')
                .receipt.get('receipt_data')
            )
        if receipt is None:
            return []

        try:
            receipt_info, is_sandbox = fetch_receipt_data(receipt)
            # no subscription were bought
            # receipt do not have latest_receipt_info key
            if receipt_info is None:
                return []
            receipt_info = sorted(
                receipt_info, key=lambda iap: milli_to_datetime(iap['purchase_date_ms'])
            )
        except ValueError as error:
            logger.error('[APPLE] {!r}'.format(error))
            raise InvalidReceiptError(
                business_id=business_id,
                receipt=receipt,
            )
        except TypeError as error:
            logger.error('[APPLE] {!r}'.format(error))
            raise InvalidReceiptMessageError(
                business_id=business_id,
                receipt=receipt,
                msg='{!r}'.format(error),
            )
        except KeyError as error:
            logger.error('[APPLE] KeyError {!r}'.format(error))
            raise InvalidReceiptError(
                business_id=business_id,
                receipt=receipt,
            )
        except (IOError, exceptions.Timeout) as error:
            if number_of_retry >= MAX_RETRY_NUMBER:
                raise
            logger.error(
                'Retry AppleSubscriptionStatusTask {!r}, '
                'business_id: {}, number_of_retry:{}, receipt:{}'.format(
                    error,
                    business_id,
                    number_of_retry,
                    receipt,
                )
            )
            AppleSubscriptionStatusTask.apply_async(
                kwargs=dict(
                    business_id=business_id,
                    receipt=receipt,
                    web_order_line_item_id=web_order_line_item_id,
                    save_receipt=save_receipt,
                    update_business_status=update_business_status,
                    number_of_retry=(number_of_retry + 1),
                ),
                eta=tznow() + datetime.timedelta(minutes=1),
            )
            raise

        iaps = split_iap_into_subs(receipt_info)

        if not iaps:
            raise InvalidReceiptError(
                business_id=business_id,
                receipt=receipt,
            )

        subs_qs = Subscription.objects.filter(
            source=Business.PaymentSource.ITUNES,
        )
        id_query = Q()
        for web_order_line_item_id in iaps:
            id_query |= Q(receipt__web_order_line_item_id=web_order_line_item_id)
        subs_qs = subs_qs.filter(id_query)

        subs = {sub.receipt['web_order_line_item_id']: (sub, False) for sub in subs_qs.iterator()}

        subs_biz = {sub[0].business_id for sub in list(subs.values())}
        if any((str(biz_id) != str(business_id) for biz_id in subs_biz)):
            raise BusinessMismatchError(
                business_id=business_id,
                receipt=receipt,
                business_id_set=set(subs_biz),
            )

        if save_receipt:
            for web_order_line_item_id in iaps:
                subs[web_order_line_item_id] = update_sub(
                    sub=subs.get(web_order_line_item_id),
                    iaps=iaps[web_order_line_item_id],
                    business_id=business_id,
                    receipt=receipt,
                )

            subscriptions = [x[0] for x in list(subs.values()) if x[0] is not None]

            if update_business_status:
                from webapps.purchase.tasks import ComputeBusinessStatusTask

                metadata = {
                    'task': 'AppleSubscriptionStatusTask',
                }
                ComputeBusinessStatusTask.run(
                    business_id=business_id,
                    metadata=metadata,
                )

            Business.objects.filter(id=business_id, has_new_billing=False).update(
                payment_source=Business.PaymentSource.ITUNES,
                updated=tznow(),
            )

            schedule_renew(
                subs=subscriptions,
                iaps=iaps,
                business_id=business_id,
                is_sandbox=is_sandbox,
            )
        return iaps

    try:
        ret = wrapper(
            business_id=business_id,
            receipt=receipt,
            web_order_line_item_id=web_order_line_item_id,
            save_receipt=save_receipt,
            update_business_status=update_business_status,
            number_of_retry=number_of_retry,
        )
    except (BusinessMismatchError, InvalidReceiptError) as e:
        return {
            'state': ScheduleState.FAILURE,
            'exception': repr(e),
            'stack': traceback.format_exc().splitlines(),
        }
    else:
        return {
            'state': ScheduleState.SUCCESS,
            'result': ret,
        }


@celery_task(time_limit=16 * 60, soft_time_limit=15 * 60)
def apple_subscriptions_check_task(months: int, email: str):
    email_subject = f'Apple subscription check ({settings.API_COUNTRY}) results'
    data = pd.DataFrame()
    data['Business ID'] = pd.Series()
    data['Business status'] = pd.Series()
    data['Subscription ID'] = pd.Series()
    data['Subscription expiry'] = pd.Series()
    data['Processing status'] = pd.Series()
    data['Additional info'] = pd.Series()

    err_status = 'ERROR'
    ok_status = 'OK'
    now_ = tznow()
    expiry_min = now_ - relativedelta(months=months)
    paid_expiry_max = now_ - datetime.timedelta(days=1)
    overdue_statuses = {
        Business.Status.OVERDUE,
        Business.Status.BLOCKED_OVERDUE,
    }
    # Get IDs of Businesses to consider
    business_ids = (
        Subscription.objects.filter(
            Q(
                Q(
                    business__status__in=overdue_statuses,
                )
                | Q(
                    business__status=Business.Status.PAID,
                    expiry__lt=paid_expiry_max,
                )
            ),
            source=Business.PaymentSource.ITUNES,
            business__payment_source=Business.PaymentSource.ITUNES,
            expiry__gte=expiry_min,
        )
        .order_by(
            'business_id',
        )
        .distinct('business_id')
        .values_list('business_id', flat=True)
    )

    for idx, biz_id in enumerate(business_ids.iterator()):
        # Get latest apple subscription
        sub = (
            Subscription.objects.filter(
                business_id=biz_id,
                business__payment_source=Business.PaymentSource.ITUNES,
                source=Business.PaymentSource.ITUNES,
            )
            .select_related(
                'business',
            )
            .only(
                'id',
                'business_id',
                'business__status',
                'expiry',
                'receipt',
            )
            .order_by('-expiry')
            .first()
        )
        if not sub:
            continue
        # Same conditions again, but for latest subscription
        if sub.expiry >= expiry_min and (
            (sub.business.status == Business.Status.PAID and sub.expiry < paid_expiry_max)
            or sub.business.status in overdue_statuses
        ):
            try:
                sync_res = AppleSubscriptionStatusTask.run(
                    business_id=biz_id,
                    receipt=sub.receipt['receipt_data'],
                    save_receipt=True,
                    # Important
                    update_business_status=False,
                )
            # Unable to obtain test data
            except Exception as e:
                data.loc[idx, 'Business ID'] = biz_id
                data.loc[idx, 'Subscription ID'] = sub.id
                data.loc[idx, 'Processing status'] = err_status
                data.loc[idx, 'Additional info'] = e
                continue

            if sync_res['state'] != ScheduleState.SUCCESS:
                data.loc[idx, 'Business ID'] = biz_id
                data.loc[idx, 'Subscription ID'] = sub.id
                data.loc[idx, 'Processing status'] = err_status
                data.loc[idx, 'Additional info'] = sync_res.get('exception')
                continue

            receipt_json = sync_res['result'].get(sub.receipt['web_order_line_item_id'])
            if not receipt_json:
                data.loc[idx, 'Business ID'] = biz_id
                data.loc[idx, 'Subscription ID'] = sub.id
                data.loc[idx, 'Processing status'] = err_status
                data.loc[idx, 'Additional info'] = 'Missing receipt'
                continue

            sub_expiry = milli_to_datetime(receipt_json[-1]['expires_date_ms'])
            if sub_expiry > now_ and sub.business.status in overdue_statuses:
                from webapps.purchase.tasks import ComputeBusinessStatusTask

                metadata = {
                    'task': 'apple_subscriptions_check_task',
                }
                ComputeBusinessStatusTask.run(
                    business_id=biz_id,
                    metadata=metadata,
                )
            elif sub_expiry <= now_:
                data.loc[idx, 'Business ID'] = biz_id
                data.loc[idx, 'Subscription ID'] = sub.id
                data.loc[idx, 'Business status'] = sub.business.status
                data.loc[idx, 'Subscription expiry'] = sub_expiry.strftime('%Y-%m-%d %H:%M:%S')
                data.loc[idx, 'Processing status'] = ok_status

    timestamp = datetime.datetime.now().strftime('%d_%m_%Y_%H_%M_%S')
    sio = io.BytesIO()
    with pd.ExcelWriter(sio, engine='openpyxl') as pandas_writer:
        data.to_excel(pandas_writer, index=False)
    sio.seek(0)

    send_email(
        email,
        'Report in the attachment',
        subject=email_subject,
        attachments=[
            (
                f'apple_subscriptions_check_{timestamp}.xlsx',
                sio.getvalue(),
                XLS_MIME,
            )
        ],
        omit_celery=False,
    )


@simple_scenario
def apple_receipt_decode_task(receipt):
    if 'receipt_data' not in receipt:
        raise KeyError('Receipt missing key "receipt_data".')

    apple_response, is_sandbox = fetch_receipt_data(receipt['receipt_data'], raw_response=True)

    receipt_info = apple_response.get('latest_receipt_info', [])
    receipt_info = sorted(
        receipt_info,
        key=lambda iap: milli_to_datetime(iap['purchase_date_ms']),
        reverse=True,
    )
    iaps = split_iap_into_subs(receipt_info)

    return {
        '_booksy_response': iaps,
        '_apple_response': apple_response,
    }
