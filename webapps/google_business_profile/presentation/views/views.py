# pylint: disable=line-too-long
from bo_obs.datadog.enums import BooksyTeams
from lagom import magic_bind_to_container
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import Response

from drf_api.base_views import BaseBooksySessionGenericAPIView
from drf_api.mixins import BusinessViewValidatorMixin
from webapps.business.models import Resource
from webapps.google_business_profile.application.dtos.auth_dto import AuthContext
from webapps.google_business_profile.application.services.accounts_service import AccountsService
from webapps.google_business_profile.application.services.bp_service import (
    BooksyProfileSaveService,
    BooksyProfileGetService,
)
from webapps.google_business_profile.application.services.category_service import CategoriesService
from webapps.google_business_profile.application.services.check_address_service import (
    AddressCheckService,
)
from webapps.google_business_profile.application.services.locations_service import LocationsService
from webapps.google_business_profile.application.services.locations_status_service import (
    LocationStatusService,
)
from webapps.google_business_profile.containers import container
from webapps.google_business_profile.domain.analytics.dto import AnalyticsBusinessIdentifier
from webapps.google_business_profile.infrastructure.factories.gbp_gateway_factory import (
    GoogleBusinessProfileGatewayFactory,
)
from webapps.google_business_profile.presentation.serializers.accounts import (
    AccountsSerializer,
)
from webapps.google_business_profile.presentation.serializers.booksy_profile import (
    BooksyProfileRequestSerializer,
    BooksyLocationIdResponseSerializer,
)
from webapps.google_business_profile.presentation.serializers.address_validation import (
    AddressCheckResponseSerializer,
)
from webapps.google_business_profile.presentation.serializers.categories import (
    CategoriesRequestSerializer,
    CategoriesResponseSerializer,
)
from webapps.google_business_profile.presentation.serializers.common import (
    AccountTokenSerializer,
    LocationTokenSerializer,
    TokenRequestSerializer,
)
from webapps.google_business_profile.presentation.serializers.locations import (
    LocationsSerializer,
    ParticularLocationSerializer,
    LocationStatusSerializer,
    SimpleLocationSerializer,
    SimpleCreateLocationSerializer,
    AllLocationsPresentation,
    CreateLocationResponseSerializer,
)
from webapps.google_business_profile.presentation.views.dto_converter import DtoMapper
from webapps.google_business_profile.presentation.views.exception_handlers import (
    handle_gbp_exceptions,
)
from webapps.google_business_profile.shared import BusinessId, UserId


class AccountsView(BusinessViewValidatorMixin, BaseBooksySessionGenericAPIView):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)
    required_minimum_access_level = Resource.STAFF_ACCESS_LEVEL_OWNER
    permission_classes = (IsAuthenticated,)
    serializer_class = AccountsSerializer

    @magic_bind_to_container(container)
    @handle_gbp_exceptions
    def post(
        self,
        request,
        business_pk: BusinessId,
        gbp_gateway_factory: GoogleBusinessProfileGatewayFactory,
        accounts_service: AccountsService,
        *args,
        **kwargs,
    ):
        self.get_business(business_pk)

        serializer_request = TokenRequestSerializer(data=request.data)
        serializer_request.is_valid(raise_exception=True)
        data = serializer_request.validated_data

        auth_context = AuthContext(oauth_token=data.get('oauth_token'))
        gbp_gateway = gbp_gateway_factory.create_gateway(auth_context=auth_context)
        accounts_service.gateway = gbp_gateway

        accounts_list = accounts_service.get_accounts()
        result = {'accounts': accounts_list}
        serializer = self.get_serializer(result)
        return Response(status=status.HTTP_200_OK, data=serializer.data)


class LocationsView(BusinessViewValidatorMixin, BaseBooksySessionGenericAPIView):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)
    required_minimum_access_level = Resource.STAFF_ACCESS_LEVEL_OWNER
    permission_classes = (IsAuthenticated,)
    serializer_class = LocationsSerializer

    @magic_bind_to_container(container)
    @handle_gbp_exceptions
    def post(
        self,
        request,
        business_pk: BusinessId,
        gbp_gateway_factory: GoogleBusinessProfileGatewayFactory,
        locations_service: LocationsService,
        *args,
        **kwargs,
    ):
        """
        get list of locations. Post is used to hide oauth_token which is passed by ui
        """
        business = self.get_business(business_pk)
        analytics_business_identifier = AnalyticsBusinessIdentifier(
            business_id=BusinessId(business.id), user_id=UserId(business.owner.id)
        )

        serializer_request = AccountTokenSerializer(data=request.data)
        serializer_request.is_valid(raise_exception=True)
        data = serializer_request.validated_data

        auth_context = AuthContext(
            oauth_token=data.get('oauth_token'), account_id=data.get('account_id')
        )
        gbp_gateway = gbp_gateway_factory.create_gateway(auth_context=auth_context)
        locations_service.google_gateway = gbp_gateway

        locations_list = locations_service.get_locations(
            analytics_business_identifier=analytics_business_identifier
        )
        result = {'locations': locations_list}
        serializer = self.get_serializer(result)
        return Response(status=status.HTTP_200_OK, data=serializer.data)

    @magic_bind_to_container(container)
    @handle_gbp_exceptions
    def put(
        self,
        request,
        business_pk: BusinessId,
        gbp_gateway_factory: GoogleBusinessProfileGatewayFactory,
        locations_service: LocationsService,
        *args,
        **kwargs,
    ):
        """
        create location
        """
        self.get_business(business_pk)
        serializer_request = SimpleCreateLocationSerializer(data=request.data)
        serializer_request.is_valid(raise_exception=True)
        data = serializer_request.validated_data

        auth_context = AuthContext(
            oauth_token=data.get('oauth_token'), account_id=data.get('account_id')
        )
        gbp_gateway = gbp_gateway_factory.create_gateway(auth_context=auth_context)
        locations_service.google_gateway = gbp_gateway
        locations_service.auth_context = auth_context
        accept_language = request.headers.get('Accept-Language', '')

        res = locations_service.create_location_business_profile(
            accept_language=accept_language,
            business_id=business_pk,
        )
        serializer = CreateLocationResponseSerializer(instance=res)
        return Response(status=status.HTTP_200_OK, data=serializer.data)


class LocationView(BusinessViewValidatorMixin, BaseBooksySessionGenericAPIView):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)
    required_minimum_access_level = Resource.STAFF_ACCESS_LEVEL_OWNER
    permission_classes = (IsAuthenticated,)
    serializer_class = ParticularLocationSerializer

    @magic_bind_to_container(container)
    @handle_gbp_exceptions
    def post(
        self,
        request,
        business_pk: BusinessId,
        gbp_gateway_factory: GoogleBusinessProfileGatewayFactory,
        location_service: LocationsService,
        *args,
        **kwargs,
    ):
        """
        get particular location + what we have in our db (our BusinessProfile, Business)
        """
        self.get_business(business_pk)
        serializer_request = LocationTokenSerializer(data=request.data)
        serializer_request.is_valid(raise_exception=True)
        data = serializer_request.validated_data

        auth_context = AuthContext(
            oauth_token=data.get('oauth_token'),
            location_id=data.get('location_id'),
        )
        gbp_gateway = gbp_gateway_factory.create_gateway(auth_context=auth_context)
        location_service.google_gateway = gbp_gateway

        result = location_service.compare_business_data(
            business_id=BusinessId(business_pk),
        )
        serializer = AllLocationsPresentation(result)
        return Response(status=status.HTTP_200_OK, data=serializer.data)

    @magic_bind_to_container(container)
    @handle_gbp_exceptions
    def put(
        self,
        request,
        business_pk: BusinessId,
        gbp_gateway_factory: GoogleBusinessProfileGatewayFactory,
        location_service: LocationsService,
        *args,
        **kwargs,
    ):
        """
        update particular location (+ our Business)
        """
        self.get_business(business_pk)

        serializer_request = SimpleLocationSerializer(data=request.data)
        serializer_request.is_valid(raise_exception=True)
        data = serializer_request.validated_data

        auth_context = AuthContext(
            oauth_token=data.get('oauth_token'),
            location_id=data.get('location_id'),
        )
        gbp_gateway = gbp_gateway_factory.create_gateway(auth_context=auth_context)
        location_service.google_gateway = gbp_gateway

        location_service.update_location(business_id=business_pk)
        return Response(status=status.HTTP_200_OK, data={})


class LocationStatusView(BusinessViewValidatorMixin, BaseBooksySessionGenericAPIView):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)
    required_minimum_access_level = Resource.STAFF_ACCESS_LEVEL_OWNER
    permission_classes = (IsAuthenticated,)
    serializer_class = ParticularLocationSerializer

    @magic_bind_to_container(container)
    @handle_gbp_exceptions
    def post(
        self,
        request,
        business_pk: BusinessId,
        gbp_gateway_factory: GoogleBusinessProfileGatewayFactory,
        location_status_service: LocationStatusService,
        *args,
        **kwargs,
    ):
        """
        get status of particular location
        https://developers.google.com/my-business/content/manage-verification#get-current-state
        """
        business = self.get_business(business_pk)
        analytics_business_identifier = AnalyticsBusinessIdentifier(
            business_id=BusinessId(business.id), user_id=UserId(business.owner.id)
        )

        serializer_request = LocationTokenSerializer(data=request.data)
        serializer_request.is_valid(raise_exception=True)
        data = serializer_request.validated_data

        auth_context = AuthContext(
            oauth_token=data.get('oauth_token'),
            location_id=data.get('location_id'),
        )
        gbp_gateway = gbp_gateway_factory.create_gateway(auth_context=auth_context)
        location_status_service.gateway = gbp_gateway

        result = location_status_service.get_location_status(
            analytics_business_identifier=analytics_business_identifier,
            location_id=data.get('location_id'),
        )
        serializer = LocationStatusSerializer(instance=result)
        return Response(status=status.HTTP_200_OK, data=serializer.data)


class CategoriesView(BusinessViewValidatorMixin, BaseBooksySessionGenericAPIView):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)
    required_minimum_access_level = Resource.STAFF_ACCESS_LEVEL_OWNER
    permission_classes = (IsAuthenticated,)
    serializer_class = CategoriesResponseSerializer

    @magic_bind_to_container(container)
    @handle_gbp_exceptions
    def post(  # pylint: disable=too-many-positional-arguments, too-many-arguments
        self,
        request,
        business_pk: BusinessId,
        gbp_gateway_factory: GoogleBusinessProfileGatewayFactory,
        categories_service: CategoriesService,
        dto_mapper: DtoMapper,
        *args,
        **kwargs,
    ):
        """
        get categories list
        supported categories:
        https://developers.google.com/actions-center/verticals/appointments/redirect/policies/platform-policies#supported_merchant_types
        """
        self.get_business(business_pk)
        serializer_request = CategoriesRequestSerializer(data=request.data)
        serializer_request.is_valid(raise_exception=True)
        data = serializer_request.validated_data

        query_params = dto_mapper.to_category_query_params(serializer_request.validated_data)
        auth_context = AuthContext(oauth_token=data.get('oauth_token'))
        gbp_gateway = gbp_gateway_factory.create_gateway(auth_context=auth_context)
        categories_service.gateway = gbp_gateway

        categories_list = categories_service.get_categories(category_data=query_params)
        result = {'categories': categories_list}
        serializer = self.get_serializer(result)
        return Response(status=status.HTTP_200_OK, data=serializer.data)


class BooksyProfileView(BusinessViewValidatorMixin, BaseBooksySessionGenericAPIView):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)
    required_minimum_access_level = Resource.STAFF_ACCESS_LEVEL_OWNER
    permission_classes = (IsAuthenticated,)
    serializer_class = BooksyProfileRequestSerializer

    @magic_bind_to_container(container)
    @handle_gbp_exceptions
    def post(
        self,
        request,
        business_pk: BusinessId,
        gbp_gateway_factory: GoogleBusinessProfileGatewayFactory,
        booksy_profile_service: BooksyProfileSaveService,
        *args,
        **kwargs,
    ):
        """
        save GBP profile to Booksy
        """
        self.get_business(business_pk)

        serializer_request = self.serializer_class(data=request.data)
        serializer_request.is_valid(raise_exception=True)
        data = serializer_request.validated_data

        auth_context = AuthContext(
            oauth_token=data.get('oauth_token'),
            location_id=data.get('location_id'),
            account_id=data.get('account_id'),
        )
        gbp_gateway = gbp_gateway_factory.create_gateway(
            auth_context=auth_context,
        )
        booksy_profile_service.inject_dynamic_args(gbp_gateway)

        booksy_profile_service.save_booksy_profile(
            business_id=BusinessId(business_pk),
            auth_context=auth_context,
        )

        return Response(status=status.HTTP_200_OK, data={})

    @magic_bind_to_container(container)
    def get(
        self,
        request,
        business_pk: BusinessId,
        booksy_profile_service: BooksyProfileGetService,
        *args,
        **kwargs,
    ):
        """
        get GBP profile from Booksy
        """
        self.get_business(business_pk)

        res = booksy_profile_service.get_location_id(
            business_id=BusinessId(business_pk),
        )

        res = {"location_id": res}
        serializer = BooksyLocationIdResponseSerializer(instance=res)
        return Response(status=status.HTTP_200_OK, data=serializer.data)


class CheckAddressView(BusinessViewValidatorMixin, BaseBooksySessionGenericAPIView):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)
    required_minimum_access_level = Resource.STAFF_ACCESS_LEVEL_OWNER
    permission_classes = (IsAuthenticated,)
    serializer_class = AddressCheckResponseSerializer

    @magic_bind_to_container(container)
    @handle_gbp_exceptions
    def get(
        self,
        request,
        business_pk: BusinessId,
        address_check_service: AddressCheckService,
        *args,
        **kwargs,
    ):
        self.get_business(business_pk)

        result = address_check_service.check_address(business_id=BusinessId(business_pk))
        serializer = self.get_serializer_class()(instance=result)

        return Response(status=status.HTTP_200_OK, data=serializer.data)
