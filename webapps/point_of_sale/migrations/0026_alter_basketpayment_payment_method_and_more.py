# Generated by Django 4.2.13 on 2024-05-20 16:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("point_of_sale", "0025_add_booksy_pay_basketpayment_source"),
    ]

    operations = [
        migrations.AlterField(
            model_name="basketpayment",
            name="payment_method",
            field=models.CharField(
                choices=[
                    ("terminal", "Terminal"),
                    ("card", "Card (Mobile Payment)"),
                    ("square", "Square"),
                    ("google_pay", "Google Pay"),
                    ("apple_pay", "Apple Pay"),
                    ("tap_to_pay", "Tap To Pay"),
                    ("blik", "Blik"),
                    ("cash", "Cash"),
                    ("check", "Check"),
                    ("credit_card", "Credit Card"),
                    ("subscription", "Subscription Card"),
                    ("store_credit", "Store Credit"),
                    ("bank_transfer", "Bank Transfer"),
                    ("american_express", "American Express"),
                    ("paypal", "PayPal"),
                    ("booksy_gift_card", "Booksy Gift Card"),
                    ("egift_card", "Gift Card"),
                    ("membership", "Membership"),
                    ("package", "Package"),
                    ("direct_payment", "Direct Payment"),
                    ("pba_donations", "Donations"),
                    ("giftcard", "Own GiftCard"),
                    ("voucher", "Voucher"),
                ],
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name="paymentmethodvariant",
            name="payment_method_type",
            field=models.CharField(
                choices=[
                    ("terminal", "Terminal"),
                    ("card", "Card (Mobile Payment)"),
                    ("square", "Square"),
                    ("google_pay", "Google Pay"),
                    ("apple_pay", "Apple Pay"),
                    ("tap_to_pay", "Tap To Pay"),
                    ("blik", "Blik"),
                    ("cash", "Cash"),
                    ("check", "Check"),
                    ("credit_card", "Credit Card"),
                    ("subscription", "Subscription Card"),
                    ("store_credit", "Store Credit"),
                    ("bank_transfer", "Bank Transfer"),
                    ("american_express", "American Express"),
                    ("paypal", "PayPal"),
                    ("booksy_gift_card", "Booksy Gift Card"),
                    ("egift_card", "Gift Card"),
                    ("membership", "Membership"),
                    ("package", "Package"),
                    ("direct_payment", "Direct Payment"),
                    ("pba_donations", "Donations"),
                    ("giftcard", "Own GiftCard"),
                    ("voucher", "Voucher"),
                ],
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name="posplan",
            name="payment_method_type",
            field=models.CharField(
                choices=[
                    ("terminal", "Terminal"),
                    ("card", "Card (Mobile Payment)"),
                    ("square", "Square"),
                    ("google_pay", "Google Pay"),
                    ("apple_pay", "Apple Pay"),
                    ("tap_to_pay", "Tap To Pay"),
                    ("blik", "Blik"),
                    ("cash", "Cash"),
                    ("check", "Check"),
                    ("credit_card", "Credit Card"),
                    ("subscription", "Subscription Card"),
                    ("store_credit", "Store Credit"),
                    ("bank_transfer", "Bank Transfer"),
                    ("american_express", "American Express"),
                    ("paypal", "PayPal"),
                    ("booksy_gift_card", "Booksy Gift Card"),
                    ("egift_card", "Gift Card"),
                    ("membership", "Membership"),
                    ("package", "Package"),
                    ("direct_payment", "Direct Payment"),
                    ("pba_donations", "Donations"),
                    ("giftcard", "Own GiftCard"),
                    ("voucher", "Voucher"),
                ],
                max_length=20,
            ),
        ),
    ]
