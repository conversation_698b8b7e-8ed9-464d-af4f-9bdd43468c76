import datetime
import uuid
from decimal import Decimal
from unittest.mock import MagicMock

import pytest
from django.test import TestCase
from django.utils import timezone
from freezegun import freeze_time
from mock import patch
from model_bakery import baker
from parameterized import parameterized

from lib.feature_flag.feature.payment import TapToPayDiscountFlag, BlikDiscountFlag
from lib.payment_gateway.entities import (
    BalanceTransactionEntity,
    DisputeEntity,
    DisputeSplitsEntity,
    RefundSplitsEntity,
    PaymentSplitsEntity,
    TransferFundEntity,
)
from lib.payment_gateway.enums import (
    BalanceTransactionStatus,
    BalanceTransactionType,
    DisputeType,
    TransferFundOrigin,
)
from lib.payment_providers.entities import AuthorizePaymentMethodDataEntity
from lib.payments.enums import PaymentError, PaymentProviderCode
from lib.point_of_sale.enums import (
    BasketCustomerPaymentAction,
    BasketPaymentSource,
    BasketPaymentStatus,
    BasketPaymentType,
    PaymentMethodType,
)
from lib.tests.utils import override_feature_flag
from lib.tools import minor_unit
from webapps.point_of_sale.exceptions import (
    OperationNotAllowed,
    BasketPaymentNotFound,
    InvalidBasketPayment,
)
from webapps.point_of_sale.models import Basket, BasketPayment, BasketTip
from webapps.point_of_sale.services.basket_payment import BasketPaymentService


class BasketPaymentServiceTests(TestCase):
    @parameterized.expand(
        [
            (BasketPaymentStatus.PENDING, BasketPaymentStatus.ACTION_REQUIRED),
            (BasketPaymentStatus.PENDING, BasketPaymentStatus.SUCCESS),
            (BasketPaymentStatus.PENDING, BasketPaymentStatus.FAILED),
            (BasketPaymentStatus.PENDING, BasketPaymentStatus.CANCELED),
            (BasketPaymentStatus.ACTION_REQUIRED, BasketPaymentStatus.SUCCESS),
            (BasketPaymentStatus.ACTION_REQUIRED, BasketPaymentStatus.FAILED),
            (BasketPaymentStatus.ACTION_REQUIRED, BasketPaymentStatus.CANCELED),
        ]
    )
    def test_change_basket_payment_status_correct(self, initial_status, new_status):
        basket_payment = baker.make(BasketPayment, status=initial_status)
        returned_basket_payment = (
            BasketPaymentService._change_basket_payment_status(  # pylint: disable=protected-access
                basket_payment=basket_payment,
                new_status=new_status,
            )
        )
        self.assertEqual(returned_basket_payment.status, new_status)

    @parameterized.expand(
        [
            (BasketPaymentStatus.SUCCESS, BasketPaymentStatus.PENDING),
            (BasketPaymentStatus.SUCCESS, BasketPaymentStatus.ACTION_REQUIRED),
            (BasketPaymentStatus.SUCCESS, BasketPaymentStatus.SUCCESS),
            (BasketPaymentStatus.SUCCESS, BasketPaymentStatus.FAILED),
            (BasketPaymentStatus.SUCCESS, BasketPaymentStatus.CANCELED),
            (BasketPaymentStatus.FAILED, BasketPaymentStatus.PENDING),
            (BasketPaymentStatus.FAILED, BasketPaymentStatus.ACTION_REQUIRED),
            (BasketPaymentStatus.FAILED, BasketPaymentStatus.SUCCESS),
            (BasketPaymentStatus.FAILED, BasketPaymentStatus.FAILED),
            (BasketPaymentStatus.FAILED, BasketPaymentStatus.CANCELED),
            (BasketPaymentStatus.CANCELED, BasketPaymentStatus.PENDING),
            (BasketPaymentStatus.SUCCESS, BasketPaymentStatus.ACTION_REQUIRED),
            (BasketPaymentStatus.SUCCESS, BasketPaymentStatus.SUCCESS),
            (BasketPaymentStatus.SUCCESS, BasketPaymentStatus.FAILED),
            (BasketPaymentStatus.SUCCESS, BasketPaymentStatus.CANCELED),
        ]
    )
    def test_change_basket_payment_status_incorrect(self, initial_status, new_status):
        basket_payment = baker.make(BasketPayment, status=initial_status)
        with self.assertRaises(OperationNotAllowed):
            BasketPaymentService._change_basket_payment_status(  # pylint: disable=protected-access
                basket_payment=basket_payment,
                new_status=new_status,
            )

    # pylint: disable=protected-access
    @freeze_time(datetime.datetime(2024, 4, 2, 10, 0, 0, tzinfo=datetime.UTC))
    @patch('webapps.point_of_sale.services.basket_payment.PaymentGatewayPort.get_business_wallet')
    @patch(
        'webapps.payment_providers.ports.account_holder_ports.'
        'PaymentProvidersAccountHolderPort.get_account_holder_settings'
    )
    def test_get_splits_after_discounts__ttp(self, get_ah_settings_mock, get_wallet_mock):
        get_wallet_mock.return_value.account_holder_id = 123
        get_ah_settings_mock.return_value.entity.stripe.tap_to_pay_fees_accepted_at = (
            timezone.now() - datetime.timedelta(hours=5)
        )

        basket = baker.make(Basket)
        basket_payment = baker.make(
            BasketPayment,
            basket=basket,
            type=BasketPaymentType.PAYMENT,
            amount=1000,
            payment_method=PaymentMethodType.TAP_TO_PAY,
            status=BasketPaymentStatus.PENDING,
            source=BasketPaymentSource.PAYMENT,
            auto_capture=False,
        )

        payment_splits = PaymentSplitsEntity(percentage_fee=Decimal('10.00'), fixed_fee=10)
        refund_splits = RefundSplitsEntity(percentage_fee=Decimal('20.00'), fixed_fee=20)
        dispute_splits = DisputeSplitsEntity(percentage_fee=Decimal('30.00'), fixed_fee=30)

        zero_payment_splits = PaymentSplitsEntity(percentage_fee=Decimal('0.00'), fixed_fee=0)
        zero_refund_splits = RefundSplitsEntity(percentage_fee=Decimal('0.00'), fixed_fee=0)

        # test happy path
        with override_feature_flag(
            {
                TapToPayDiscountFlag.flag_name: {
                    "max_payment_amount": 10000,
                    "payments_count_limit": 3,
                    "max_time_to_use": 10 * 60,  # 10 hours
                }
            }
        ):
            ps, rs, ds = BasketPaymentService._get_splits_after_discounts(
                basket_payment=basket_payment,
                payment_splits=payment_splits,
                refund_splits=refund_splits,
                dispute_splits=dispute_splits,
            )
            self.assertEqual(ps, zero_payment_splits)  # discounted to 0
            self.assertEqual(rs, zero_refund_splits)  # discounted to 0
            self.assertEqual(ds, dispute_splits)  # disputes left intact

        # test max time to use expired scenario
        with override_feature_flag(
            {
                TapToPayDiscountFlag.flag_name: {
                    "max_payment_amount": 10000,
                    "payments_count_limit": 3,
                    "max_time_to_use": 3 * 60,  # 3 hours (fees accepted 5h ago)
                }
            }
        ):
            ps, rs, ds = BasketPaymentService._get_splits_after_discounts(
                basket_payment=basket_payment,
                payment_splits=payment_splits,
                refund_splits=refund_splits,
                dispute_splits=dispute_splits,
            )
            self.assertEqual(ps, payment_splits)  # not discounted
            self.assertEqual(rs, refund_splits)  # not discounted
            self.assertEqual(ds, dispute_splits)  # not discounted

        # test scenario with no fees accepted
        with override_feature_flag(
            {
                TapToPayDiscountFlag.flag_name: {
                    "max_payment_amount": 10000,
                    "payments_count_limit": 3,
                    "max_time_to_use": 30 * 60,  # 30 hours
                }
            }
        ):
            get_ah_settings_mock.return_value.entity.stripe.tap_to_pay_fees_accepted_at = None
            ps, rs, ds = BasketPaymentService._get_splits_after_discounts(
                basket_payment=basket_payment,
                payment_splits=payment_splits,
                refund_splits=refund_splits,
                dispute_splits=dispute_splits,
            )
            self.assertEqual(ps, payment_splits)  # not discounted
            self.assertEqual(rs, refund_splits)  # not discounted
            self.assertEqual(ds, dispute_splits)  # not discounted
            get_ah_settings_mock.return_value.entity.stripe.tap_to_pay_fees_accepted_at = (
                timezone.now() - datetime.timedelta(hours=5)
            )

        # test scenario with amount exceeding
        with override_feature_flag(
            {
                TapToPayDiscountFlag.flag_name: {
                    "max_payment_amount": 100,
                    "payments_count_limit": 3,
                    "max_time_to_use": 30 * 60,  # 30 hours
                }
            }
        ):
            ps, rs, ds = BasketPaymentService._get_splits_after_discounts(
                basket_payment=basket_payment,
                payment_splits=payment_splits,
                refund_splits=refund_splits,
                dispute_splits=dispute_splits,
            )
            self.assertEqual(ps, payment_splits)  # not discounted
            self.assertEqual(rs, refund_splits)  # not discounted
            self.assertEqual(ds, dispute_splits)  # not discounted

        # test scenario with another payment method
        with override_feature_flag(
            {
                TapToPayDiscountFlag.flag_name: {
                    "max_payment_amount": 10000,
                    "payments_count_limit": 3,
                    "max_time_to_use": 30 * 60,  # 30 hours
                }
            }
        ):
            basket_payment.payment_method = PaymentMethodType.TERMINAL
            ps, rs, ds = BasketPaymentService._get_splits_after_discounts(
                basket_payment=basket_payment,
                payment_splits=payment_splits,
                refund_splits=refund_splits,
                dispute_splits=dispute_splits,
            )
            self.assertEqual(ps, payment_splits)  # not discounted
            self.assertEqual(rs, refund_splits)  # not discounted
            self.assertEqual(ds, dispute_splits)  # not discounted
            basket_payment.payment_method = PaymentMethodType.TAP_TO_PAY

        # test scenario without feature flag turned on (no discount expected)
        ps, rs, ds = BasketPaymentService._get_splits_after_discounts(
            basket_payment=basket_payment,
            payment_splits=payment_splits,
            refund_splits=refund_splits,
            dispute_splits=dispute_splits,
        )
        self.assertEqual(ps, payment_splits)  # not discounted
        self.assertEqual(rs, refund_splits)  # not discounted
        self.assertEqual(ds, dispute_splits)  # not discounted

    # pylint: disable=protected-access,too-many-statements
    @freeze_time(datetime.datetime(2024, 7, 5, 13, 0, 0, tzinfo=datetime.UTC))
    @patch('webapps.pos.ports.POSPort.blik_in_prepayment_promo_enabled')
    def test_get_splits_after_discounts__blik(self, pos_port_mock):
        pos_port_mock.return_value = True
        basket = baker.make(Basket)
        basket_payment = baker.make(
            BasketPayment,
            basket=basket,
            type=BasketPaymentType.PAYMENT,
            amount=1000,
            payment_method=PaymentMethodType.BLIK,
            status=BasketPaymentStatus.PENDING,
            source=BasketPaymentSource.PAYMENT,
            auto_capture=False,
        )

        payment_splits = PaymentSplitsEntity(percentage_fee=Decimal('10.00'), fixed_fee=10)
        refund_splits = RefundSplitsEntity(percentage_fee=Decimal('20.00'), fixed_fee=20)
        dispute_splits = DisputeSplitsEntity(percentage_fee=Decimal('30.00'), fixed_fee=30)

        zero_payment_splits = PaymentSplitsEntity(percentage_fee=Decimal('0.00'), fixed_fee=0)
        zero_refund_splits = RefundSplitsEntity(percentage_fee=Decimal('0.00'), fixed_fee=0)

        # test happy path for checkout
        with override_feature_flag(
            {
                BlikDiscountFlag.flag_name: {
                    "prepayment_promo_start": 1,  # disabled
                    "prepayment_promo_end": 2,  # disabled
                    "checkout_promo_start": 1720094400,  # 04.07.24
                    "checkout_promo_end": 1720267200,  # 06.07.24
                }
            }
        ):
            ps, rs, ds = BasketPaymentService._get_splits_after_discounts(
                basket_payment=basket_payment,
                payment_splits=payment_splits,
                refund_splits=refund_splits,
                dispute_splits=dispute_splits,
            )
            self.assertEqual(ps, zero_payment_splits)  # discounted to 0
            self.assertEqual(rs, zero_refund_splits)  # discounted to 0
            self.assertEqual(ds, dispute_splits)  # disputes left intact

        # test not started yet (checkout)
        with override_feature_flag(
            {
                BlikDiscountFlag.flag_name: {
                    "prepayment_promo_start": 1,  # disabled
                    "prepayment_promo_end": 2,  # disabled
                    "checkout_promo_start": 1720440000,  # 08.07.24
                    "checkout_promo_end": 1720612800,  # 10.07.24
                }
            }
        ):
            ps, rs, ds = BasketPaymentService._get_splits_after_discounts(
                basket_payment=basket_payment,
                payment_splits=payment_splits,
                refund_splits=refund_splits,
                dispute_splits=dispute_splits,
            )
            self.assertEqual(ps, payment_splits)  # not discounted
            self.assertEqual(rs, refund_splits)  # not discounted
            self.assertEqual(ds, dispute_splits)  # not discounted

        # test already finished (checkout)
        with override_feature_flag(
            {
                BlikDiscountFlag.flag_name: {
                    "prepayment_promo_start": 1,  # disabled
                    "prepayment_promo_end": 2,  # disabled
                    "checkout_promo_start": 1719921600,  # 02.07.24
                    "checkout_promo_end": 1720094400,  # 04.07.24
                }
            }
        ):
            ps, rs, ds = BasketPaymentService._get_splits_after_discounts(
                basket_payment=basket_payment,
                payment_splits=payment_splits,
                refund_splits=refund_splits,
                dispute_splits=dispute_splits,
            )
            self.assertEqual(ps, payment_splits)  # not discounted
            self.assertEqual(rs, refund_splits)  # not discounted
            self.assertEqual(ds, dispute_splits)  # not discounted

        # test happy path for prepayment
        basket_payment.source = BasketPaymentSource.PREPAYMENT
        basket_payment.save()
        with override_feature_flag(
            {
                BlikDiscountFlag.flag_name: {
                    "prepayment_promo_start": 1720094400,  # 04.07.24
                    "prepayment_promo_end": 1720267200,  # 06.07.24
                    "checkout_promo_start": 1,  # disabled
                    "checkout_promo_end": 2,  # disabled
                }
            }
        ):
            ps, rs, ds = BasketPaymentService._get_splits_after_discounts(
                basket_payment=basket_payment,
                payment_splits=payment_splits,
                refund_splits=refund_splits,
                dispute_splits=dispute_splits,
            )
            self.assertEqual(ps, zero_payment_splits)  # discounted to 0
            self.assertEqual(rs, zero_refund_splits)  # discounted to 0
            self.assertEqual(ds, dispute_splits)  # disputes left intact

        # test not started yet (prepayment)
        with override_feature_flag(
            {
                BlikDiscountFlag.flag_name: {
                    "prepayment_promo_start": 1720440000,  # 08.07.24
                    "prepayment_promo_end": 1720612800,  # 10.07.24
                    "checkout_promo_start": 1,  # disabled
                    "checkout_promo_end": 2,  # disabled
                }
            }
        ):
            ps, rs, ds = BasketPaymentService._get_splits_after_discounts(
                basket_payment=basket_payment,
                payment_splits=payment_splits,
                refund_splits=refund_splits,
                dispute_splits=dispute_splits,
            )
            self.assertEqual(ps, payment_splits)  # not discounted
            self.assertEqual(rs, refund_splits)  # not discounted
            self.assertEqual(ds, dispute_splits)  # not discounted

        # test already finished (prepayment)
        with override_feature_flag(
            {
                BlikDiscountFlag.flag_name: {
                    "prepayment_promo_start": 1719921600,  # 02.07.24
                    "prepayment_promo_end": 1720094400,  # 04.07.24
                    "checkout_promo_start": 1,  # disabled
                    "checkout_promo_end": 2,  # disabled
                }
            }
        ):
            ps, rs, ds = BasketPaymentService._get_splits_after_discounts(
                basket_payment=basket_payment,
                payment_splits=payment_splits,
                refund_splits=refund_splits,
                dispute_splits=dispute_splits,
            )
            self.assertEqual(ps, payment_splits)  # not discounted
            self.assertEqual(rs, refund_splits)  # not discounted
            self.assertEqual(ds, dispute_splits)  # not discounted

        # test blacklisted business (prepayment)
        pos_port_mock.return_value = False
        with override_feature_flag(
            {
                BlikDiscountFlag.flag_name: {
                    "prepayment_promo_start": 1720094400,  # 04.07.24
                    "prepayment_promo_end": 1720267200,  # 06.07.24
                    "checkout_promo_start": 1,  # disabled
                    "checkout_promo_end": 2,  # disabled
                }
            }
        ):
            ps, rs, ds = BasketPaymentService._get_splits_after_discounts(
                basket_payment=basket_payment,
                payment_splits=payment_splits,
                refund_splits=refund_splits,
                dispute_splits=dispute_splits,
            )
            self.assertEqual(ps, payment_splits)  # not discounted
            self.assertEqual(rs, refund_splits)  # not discounted
            self.assertEqual(ds, dispute_splits)  # not discounted
        pos_port_mock.return_value = True

        # test scenario without feature flag turned on (no discount expected)
        ps, rs, ds = BasketPaymentService._get_splits_after_discounts(
            basket_payment=basket_payment,
            payment_splits=payment_splits,
            refund_splits=refund_splits,
            dispute_splits=dispute_splits,
        )
        self.assertEqual(ps, payment_splits)  # not discounted
        self.assertEqual(rs, refund_splits)  # not discounted
        self.assertEqual(ds, dispute_splits)  # not discounted

    def test_initialize_basket_payment_offline(self):
        basket = baker.make(Basket)
        basket_payment = BasketPaymentService.initialize_basket_payment(
            basket=basket,
            basket_payment_type=BasketPaymentType.PAYMENT,
            amount=minor_unit(123),
            payment_method_type=PaymentMethodType.CASH,
            payment_initialization=True,
            status=BasketPaymentStatus.SUCCESS,
            source=BasketPaymentSource.PAYMENT,
            payment_splits=PaymentSplitsEntity(percentage_fee=Decimal('0.00'), fixed_fee=0),
            refund_splits=RefundSplitsEntity(percentage_fee=Decimal('0.00'), fixed_fee=0),
            dispute_splits=DisputeSplitsEntity(percentage_fee=Decimal('0.00'), fixed_fee=0),
            auto_capture=False,
        )
        self.assertNotEqual(basket_payment.id, None)

    @patch('webapps.point_of_sale.services.basket_payment.get_customer_wallet_id_adapter')
    @patch('webapps.point_of_sale.services.basket_payment.initialize_payment_adapter')
    def test_initialize_basket_payment_online(self, payment_adapter_mock, get_wallet_mock):
        payment_adapter_mock.return_value = uuid.uuid4()
        get_wallet_mock.return_value = uuid.uuid4()

        basket = baker.make(Basket)
        basket_payment = BasketPaymentService.initialize_basket_payment(
            basket=basket,
            basket_payment_type=BasketPaymentType.PAYMENT,
            amount=minor_unit(123),
            payment_method_type=PaymentMethodType.CARD,
            payment_initialization=True,
            status=BasketPaymentStatus.PENDING,
            source=BasketPaymentSource.PREPAYMENT,
            payment_splits=PaymentSplitsEntity(percentage_fee=Decimal('0.00'), fixed_fee=0),
            refund_splits=RefundSplitsEntity(percentage_fee=Decimal('0.00'), fixed_fee=0),
            dispute_splits=DisputeSplitsEntity(percentage_fee=Decimal('0.00'), fixed_fee=0),
            payment_provider_code=PaymentProviderCode.ADYEN,
            user_id=1,
            auto_capture=False,
            payment_token=None,
        )
        self.assertNotEqual(basket_payment.id, None)
        self.assertEqual(payment_adapter_mock.call_count, 1)

    @patch('webapps.point_of_sale.services.basket_payment.get_anonymous_wallet_id_adapter')
    @patch('webapps.point_of_sale.services.basket_payment.initialize_payment_adapter')
    def test_initialize_payment_balance_transaction(self, payment_adapter_mock, get_wallet_mock):
        payment_adapter_mock.return_value = uuid.uuid4()
        get_wallet_mock.return_value = uuid.uuid4()

        basket_payment = BasketPayment(
            type=BasketPaymentType.PAYMENT,
            payment_provider_code=PaymentProviderCode.ADYEN,
            basket=baker.make(Basket, business_id=1),
            amount=minor_unit(123),
            payment_method=PaymentMethodType.CARD,
        )

        BasketPaymentService.initialize_payment_balance_transaction(
            basket_payment=basket_payment,
            payment_splits=PaymentSplitsEntity(fixed_fee=0, percentage_fee=Decimal('0.00')),
            refund_splits=RefundSplitsEntity(fixed_fee=0, percentage_fee=Decimal('0.00')),
            dispute_splits=DisputeSplitsEntity(fixed_fee=0, percentage_fee=Decimal('0.00')),
            payment_token=None,
        )

        self.assertNotEqual(basket_payment.id, None)
        self.assertNotEqual(basket_payment.balance_transaction_id, None)
        self.assertEqual(payment_adapter_mock.call_count, 1)

    @patch('webapps.point_of_sale.services.basket_payment.get_anonymous_wallet_id_adapter')
    @patch('webapps.point_of_sale.services.basket_payment.initialize_refund_adapter')
    def test_initialize_refund_balance_transaction(self, refund_adapter_mock, get_wallet_mock):
        refund_adapter_mock.return_value = uuid.uuid4()
        get_wallet_mock.return_value = uuid.uuid4()

        parent_basket_payment = baker.make(
            BasketPayment,
            balance_transaction_id=uuid.uuid4(),
        )

        basket_payment = BasketPayment(
            type=BasketPaymentType.REFUND,
            payment_provider_code=PaymentProviderCode.ADYEN,
            basket=baker.make(Basket, business_id=1),
            amount=minor_unit(123),
            payment_method=PaymentMethodType.CARD,
            parent_basket_payment=parent_basket_payment,
        )

        BasketPaymentService._initialize_refund_balance_transaction(  # pylint: disable=protected-access
            basket_payment=basket_payment,
        )

        self.assertNotEqual(basket_payment.id, None)
        self.assertNotEqual(basket_payment.balance_transaction_id, None)
        self.assertEqual(refund_adapter_mock.call_count, 1)

    def test_get_basket_payment_by_basket_payment_id(self):
        with self.assertRaises(BasketPaymentNotFound):
            BasketPaymentService.get_basket_payment(basket_payment_id=uuid.uuid4())

        basket_payment = baker.make(BasketPayment)
        returned_bp = BasketPaymentService.get_basket_payment(basket_payment_id=basket_payment.id)
        self.assertEqual(returned_bp, basket_payment)

    def test_get_basket_payment_by_balance_transaction_id(self):
        with self.assertRaises(BasketPaymentNotFound):
            BasketPaymentService.get_basket_payment(balance_transaction_id=uuid.uuid4())

        basket_payment = baker.make(BasketPayment, balance_transaction_id=uuid.uuid4())
        returned_bp = BasketPaymentService.get_basket_payment(
            balance_transaction_id=basket_payment.balance_transaction_id
        )
        self.assertEqual(returned_bp, basket_payment)

    def test_update_basket_payment_details(self):
        basket_payment = baker.make(BasketPayment, status=BasketPaymentStatus.PENDING)

        returned_bp = BasketPaymentService.update_basket_payment_details(
            basket_payment=basket_payment,
            new_status=BasketPaymentStatus.ACTION_REQUIRED,
            error_code=PaymentError.GENERIC_ERROR,
            action_required_details={'test': 123},
        )
        self.assertEqual(basket_payment, returned_bp)
        basket_payment.refresh_from_db()

        self.assertEqual(basket_payment.status, BasketPaymentStatus.ACTION_REQUIRED)
        self.assertEqual(basket_payment.error_code, PaymentError.GENERIC_ERROR)
        self.assertEqual(basket_payment.action_required_details, {'test': 123})

        returned_bp2 = BasketPaymentService.update_basket_payment_details(
            basket_payment=basket_payment,
            new_status=BasketPaymentStatus.SUCCESS,
        )
        self.assertEqual(basket_payment, returned_bp2)
        basket_payment.refresh_from_db()

        self.assertEqual(basket_payment.status, BasketPaymentStatus.SUCCESS)
        self.assertEqual(basket_payment.error_code, None)  # error cleaned
        self.assertEqual(basket_payment.action_required_details, None)  # details cleaned

    def test_update_basket_payment_amount(self):
        basket_payment = baker.make(BasketPayment, amount=minor_unit(123), user_id=1)

        returned_bp = BasketPaymentService.update_basket_payment_amount(
            basket_payment=basket_payment,
            new_amount=minor_unit(1234),
        )
        self.assertEqual(basket_payment, returned_bp)
        basket_payment.refresh_from_db()
        self.assertEqual(basket_payment.amount, minor_unit(1234))
        self.assertEqual(BasketTip.objects.count(), 0)

    def test_update_basket_payment_amount_bcr_flow(self):
        basket_payment = baker.make(BasketPayment, amount=minor_unit(123), user_id=1)

        returned_bp = BasketPaymentService.update_basket_payment_amount_bcr_flow(
            basket_payment=basket_payment,
            new_amount=minor_unit(1234),
            metadata={'tip_amount': '234'},
        )
        self.assertEqual(basket_payment, returned_bp)
        basket_payment.refresh_from_db()
        self.assertEqual(basket_payment.amount, minor_unit(1234))

        basket_tip = BasketTip.objects.get()
        self.assertEqual(basket_tip.amount, minor_unit(2.34))

    @patch(
        'webapps.point_of_sale.services.basket_payment.'
        'BasketPaymentService._initialize_refund_balance_transaction'
    )
    def test_initialize_refund_basket_payment(self, initialize_mock):
        original_basket_payment = baker.make(
            BasketPayment,
            balance_transaction_id=uuid.uuid4(),
            type=BasketPaymentType.PAYMENT,
            payment_provider_code=PaymentProviderCode.ADYEN,
        )

        refund_basket_payment = BasketPaymentService.initialize_refund_basket_payment(
            original_basket_payment=original_basket_payment,
        )

        self.assertEqual(initialize_mock.call_count, 1)
        self.assertNotEqual(refund_basket_payment, None)

    def test_create_dispute_basket_payment(self):
        basket = baker.make(Basket)
        parent_bt_id = uuid.uuid4()

        basket_payment = baker.make(
            BasketPayment,
            basket=basket,
            user_id=1,
            balance_transaction_id=parent_bt_id,
            payment_method=PaymentMethodType.CARD,
            payment_provider_code=PaymentProviderCode.ADYEN,
        )

        bt_id = uuid.uuid4()
        balance_transaction = BalanceTransactionEntity(
            id=bt_id,
            created=None,
            updated=None,
            external_id=None,
            parent_balance_transaction_id=parent_bt_id,
            dispute=DisputeEntity(
                id=uuid.uuid4(),
                balance_transaction_id=uuid.uuid4(),
                type=DisputeType.CHARGEBACK,
            ),
            payment_provider_code=PaymentProviderCode.ADYEN,
            payment_method=PaymentMethodType.CARD,
            status=BalanceTransactionStatus.SUCCESS,
            receiver_id=uuid.uuid4(),
            sender_id=uuid.uuid4(),
            amount=basket_payment.amount,
            fee_amount=0,
            transaction_type=BalanceTransactionType.DISPUTE,
            paid_out_in_payout_id=None,
            payment=None,
            refund=None,
            payout=None,
            fee=None,
            transfer_fund=TransferFundEntity(
                balance_transaction_id=bt_id,
                id=uuid.uuid4(),
                origin=TransferFundOrigin.INTERNAL,
            ),
            metadata={},
        )

        dispute_basket_payment = BasketPaymentService.create_dispute_basket_payment(
            balance_transaction=balance_transaction,
        )

        self.assertEqual(dispute_basket_payment.basket, basket)
        self.assertEqual(dispute_basket_payment.type, BasketPaymentType.CHARGEBACK)
        self.assertEqual(dispute_basket_payment.amount, basket_payment.amount)
        self.assertEqual(dispute_basket_payment.payment_method, basket_payment.payment_method)
        self.assertEqual(
            dispute_basket_payment.payment_provider_code, basket_payment.payment_provider_code
        )
        self.assertEqual(dispute_basket_payment.user_id, basket_payment.user_id)
        self.assertEqual(dispute_basket_payment.status, BasketPaymentStatus.SUCCESS)
        self.assertEqual(dispute_basket_payment.parent_basket_payment, basket_payment)
        self.assertEqual(dispute_basket_payment.balance_transaction_id, balance_transaction.id)

    @patch('webapps.point_of_sale.services.basket_payment.authorize_payment_adapter')
    def test_authorize_payment(self, adapter_mock):
        basket_payment = baker.make(BasketPayment)

        BasketPaymentService.authorize_payment(
            basket_payment=basket_payment,
            wallet_id=uuid.uuid4(),
            payment_method_data=AuthorizePaymentMethodDataEntity(),
            additional_data=None,
        )
        self.assertEqual(adapter_mock.call_count, 1)

    def test_is_refund_possible_for_booksy_gift_card_is_false(self):
        basket_payment = baker.make(
            BasketPayment,
            balance_transaction_id=2,
            payment_method='booksy_gift_card',
            type=BasketPaymentType.PAYMENT,
        )

        with pytest.raises(InvalidBasketPayment) as e:
            BasketPaymentService.is_refund_possible(basket_payment)
        assert "Booksy Gift Cards payment method is not refundable" == str(e.value)

    @patch('webapps.point_of_sale.services.basket_payment.capture_payment_adapter')
    def test_capture_payment(self, adapter_mock):
        basket_payment = baker.make(BasketPayment)

        BasketPaymentService.capture_payment(
            basket_payment=basket_payment,
            wallet_id=uuid.uuid4(),
        )
        self.assertEqual(adapter_mock.call_count, 1)

    @patch(
        'webapps.point_of_sale.services.basket_payment.'
        'BasketPaymentService.update_basket_payment_details'
    )
    @patch('webapps.point_of_sale.services.basket_payment.cancel_balance_transaction_adapter')
    def test_cancel_payment_offline(self, adapter_mock, update_mock):
        basket_payment = baker.make(BasketPayment, balance_transaction_id=None)

        BasketPaymentService.cancel_payment(
            basket_payment=basket_payment,
        )
        self.assertEqual(adapter_mock.call_count, 0)
        self.assertEqual(update_mock.call_count, 1)

    @patch('webapps.payment_gateway.ports.PaymentGatewayPort.get_customer_wallet', MagicMock())
    @patch(
        'webapps.point_of_sale.services.basket_payment.'
        'BasketPaymentService.update_basket_payment_details'
    )
    @patch('webapps.point_of_sale.services.basket_payment.cancel_balance_transaction_adapter')
    def test_cancel_payment_online(self, adapter_mock, update_mock):
        with self.assertRaises(OperationNotAllowed):
            basket_payment = baker.make(
                BasketPayment,
                balance_transaction_id=uuid.uuid4(),
                status=BasketPaymentStatus.SUCCESS,
            )

            BasketPaymentService.cancel_payment(
                basket_payment=basket_payment,
            )

        basket_payment = baker.make(BasketPayment, balance_transaction_id=uuid.uuid4())

        BasketPaymentService.cancel_payment(
            basket_payment=basket_payment,
        )
        self.assertEqual(adapter_mock.call_count, 1)
        self.assertEqual(adapter_mock.call_count, 1)

    @parameterized.expand(
        [
            (
                BasketCustomerPaymentAction.MAKE_PAYMENT,
                BasketPaymentType.PAYMENT,
                BasketPaymentStatus.PENDING,
            ),
            (
                BasketCustomerPaymentAction.CANCEL_PAYMENT,
                BasketPaymentType.PAYMENT,
                BasketPaymentStatus.PENDING,
            ),
        ]
    )
    def test_is_customer_action_allowed(
        self,
        action,
        allowed_basket_payment_type,
        allowed_basket_payment_status,
    ):
        for basket_payment_type in BasketPaymentType.values():
            for basket_payment_status in BasketPaymentStatus.values():
                expected_result = (
                    basket_payment_type == allowed_basket_payment_type
                    and basket_payment_status == allowed_basket_payment_status
                )
                result = BasketPaymentService.is_customer_action_allowed(
                    basket_payment=baker.make(
                        BasketPayment,
                        type=basket_payment_type,
                        status=basket_payment_status,
                    ),
                    action=action,
                )
                self.assertEqual(
                    result,
                    expected_result,
                    f'basket_payment_type:{basket_payment_type},basket_payment_status: {basket_payment_status}',  # pylint: disable=line-too-long
                )
