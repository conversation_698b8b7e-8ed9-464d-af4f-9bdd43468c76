from dataclasses import dataclass
from datetime import datetime


@dataclass
class ProviderFeatureChecklist:  # pylint: disable=too-many-instance-attributes,duplicate-code
    provider_id: int
    forwarded: bool
    physio: bool
    network: bool
    umbrella_venue: bool
    has_combo_services: bool
    has_mobile_services: bool
    has_online_services: bool
    has_addons: bool
    has_payments: bool
    has_questions: bool
    has_custom_forms: bool
    created_at: datetime
