from uuid import UUID

from rest_framework import status
from rest_framework.exceptions import ValidationError as SerializerValidationError
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from webapps.booking.v2.application.http_api.request.claim_draft import (
    ClaimDraftRequest,
)
from webapps.booking.v2.application.http_api.response.draft import DraftResponse
from webapps.booking.v2.application.http_api.view.base import BaseView
from webapps.booking.v2.application.http_api.view.utils import with_error_handling
from webapps.booking.v2.commons.errors import ValidationError


# pylint: disable=duplicate-code
class ClaimDraftView(BaseView):
    serializer_class = ClaimDraftRequest
    response_serializer_class = DraftResponse
    permission_classes = (IsAuthenticated,)

    @with_error_handling
    def post(self, _: Request, draft_id: str) -> Response:
        user_id = self.request.user.id if self.request and self.request.user else None
        request_serializer = self.get_serializer(data=self.request.data)

        try:
            draft_id = UUID(draft_id)
        except ValueError as e:
            raise ValidationError from e

        try:
            request_serializer.is_valid(raise_exception=True)
        except SerializerValidationError as e:
            raise ValidationError from e

        version = request_serializer.validated_data['version']
        response_serializer = self._application_service.claim_draft(
            user_id=user_id,
            draft_id=draft_id,
            version=version,
        )

        return Response(
            status=status.HTTP_200_OK,
            data=response_serializer.data,
        )
