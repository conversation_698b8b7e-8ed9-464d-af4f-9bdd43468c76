# pylint: disable=duplicate-code
import builtins
from collections import Counter
from dataclasses import dataclass, field
from datetime import date, datetime
from itertools import chain, groupby
from operator import itemgetter
from random import shuffle
from typing import Iterable, Optional, Type, Union

import pandas as pd
from bo_obs.datadog.enums import DatadogOperationNames
from ddtrace import tracer
from django.utils.functional import cached_property
from pandas import DataFrame, Timestamp

from lib.enums import StrEnum
from webapps.booking.exceptions import (
    BookingConflict,
    DrawForOverbookingException,
    MissingParallelResourcesException,
    MissingResourcesException,
)
from webapps.booking.models import SubBooking
from webapps.booking.timeslots.v1.time_scope import TimeRangesScope  # sic! absolute
from webapps.booking.tools.tools import iter_leaf_services
from webapps.turntracker.grid.grid_main import calculate_next_staffers_for_business
from .consts import (
    AVAILABLE,
    COMMON_ORDER,
    DATE,
    DRAFT,
    ORDER,
    RESOURCE_ID,
    RESOURCE_TYPE,
    SLOT,
)
from .core import (
    APPLIANCE_TYPE,
    MergedSlots,
    ResourceSlots,
    STAFFER_TYPE,
    ServiceDraft,
    ValidationMatrix,
)
from .data_sources import (
    BookedRanges,
    ResourceTimeOffs,
    WorkingHours,
)
from .service_scope import Service, ServiceScope
from .simple_turntracker import get_next_turn

IdsGroup = tuple[int, ...]  # noqa
IdsGroupList = Iterable[IdsGroup]
DateSlot = tuple[date, int]


class ResourceType(StrEnum):
    STAFFER = 'staffer'
    APPLIANCE = 'appliance'

    @property
    def is_staffer(self):
        return self == self.STAFFER

    # duplicate native method with explicite signature to avoid false type warnings
    def __iter__(self) -> Iterable['ResourceType']:
        yield ResourceType.STAFFER
        yield ResourceType.APPLIANCE


@dataclass
class MatchedService(Service):
    staffer_id: Optional[int] = None
    appliance_id: Optional[int] = None
    subbooking: Optional[SubBooking] = None

    def __post_init__(self):
        self.validate_resources()

    def validate_resources(self):
        # for now there are too many tests supposing a possibility of creating booking
        # without resources !!
        pass

    @property
    def resource_ids(self):
        """Resource IDs considered in validation of the service.

        When the resource is already selected (staffer_id, appliance_id),
        we skip validating other resources performing given service(staffer_ids, appliance_ids):
        staffer_id and appliance_id replace staffer_ids and appliance_ids
        """
        if self.combo_children:
            return tuple(
                sorted(
                    set(
                        chain.from_iterable(
                            chain(
                                [child.staffer_id] if child.staffer_id else child.staffer_ids,
                                [child.appliance_id] if child.appliance_id else child.appliance_ids,
                            )
                            for child in self.combo_children
                        )
                    )
                )
            )

        return tuple(
            sorted(
                set(
                    chain(
                        [self.staffer_id] if self.staffer_id else self.staffer_ids,
                        [self.appliance_id] if self.appliance_id else self.appliance_ids,
                    )
                )
            )
        )


@dataclass
class DrawServiceScope(ServiceScope):
    services: tuple[MatchedService, ...]  # noqa
    time_slots_optimization: bool = False
    min_leading_time: Optional[datetime] = None
    max_leading_time: Optional[datetime] = None
    # ordered list of all business active staffers
    staffer_ids: tuple[int, ...] = field(default_factory=tuple)
    # ordered list of all business active appliances
    appliance_ids: tuple[int, ...] = field(default_factory=tuple)
    resource_required: bool = True

    def fill_services_shifts(self):
        # skip filling - all shifts are fixed and come from subbookings
        # fill only idx's
        for idx, service in enumerate(iter_leaf_services(self.services)):
            service.idx = idx

    @property
    def pointless(self):
        """When doesn't require resources and doesn't have resources, nothing to validate or draw"""
        return not (self.requires_staffer or self.requires_appliance or self.any_resource_set)

    @property
    def any_resource_set(self):
        for service in iter_leaf_services(self.services):
            if service.staffer_id or service.appliance_id:
                return True
        return False

    @cached_property
    def any_resource_not_set(self):
        return self.any_staffer_not_set or self.any_appliance_not_set

    @property
    def any_staffer_not_set(self):
        for service in iter_leaf_services(self.services):
            if service.staffer_id is None and service.staffer_ids:
                return True
        return False

    @property
    def any_appliance_not_set(self):
        for service in iter_leaf_services(self.services):
            if service.appliance_id is None and service.appliance_ids:
                return True
        return False

    @cached_property
    def requires_appliance(self):
        return any(filter(lambda x: x.appliance_ids, iter_leaf_services(self.services)))

    @cached_property
    def requires_staffer(self):
        return any(filter(lambda x: x.staffer_ids, iter_leaf_services(self.services)))

    @property
    def has_combo_parallel(self):
        return any(filter(lambda x: x.combo_parallel, self.services))

    @property
    def assigned_resources(self):
        return set(
            filter(
                None,
                chain.from_iterable(
                    (service.staffer_id, service.appliance_id)
                    for service in iter_leaf_services(self.services)
                ),
            )
        )

    def is_any_available_resource_missing(self):
        return any(
            filter(
                lambda x: x.available_staffer_ids == tuple()
                or x.available_appliance_ids == tuple(),
                iter_leaf_services(self.services),
            )
        )


@dataclass
class DrawScope:
    business_id: int
    service_scope: DrawServiceScope
    time_scope: TimeRangesScope
    # dates_slots - (date, start_slot) pairs to consider in drawing.
    # For simple appointment it will be:
    #     dates_slots=[(date_of_booking, 0)]
    #     service_scope.services[0].shift=time_of_booking
    # For repeating booking it will contain many dates:
    #     dates_slots=[(date1, 0), (date2, 0), (date3, 0)]
    # The separate start_slot for every date allows custom repeating booking
    # with every booking starting at different time:
    #     dates_slots=[(date1, 0), (date2, 60), (date3, 0)]
    #          - the appointment at date2 starts an hour later than at date1
    dates_slots: list[DateSlot]
    staffers_drawer_class: Optional[Type['DrawOrder']]
    appliances_drawer_class: Optional[Type['DrawOrder']]
    omit_appointment_ids: Optional[list[int]] = None  # noqa
    omit_repeating_ids: Optional[list[int]] = None


class DrawerMatrix(DataFrame):
    """
    Drawer data for both staffers and appliances availability, like this:

             date  resource_id  resource_type draft
    0  2021-08-01            1              0     0
    1  2021-08-01            2              1     0
    2  2021-08-01            3              0     0
    4  2021-08-01            1              0     1
    5  2021-08-01            2              1     1
    6  2021-08-01            3              0     1

    The first row means:
    at 2021-08-01 the staffer (STAFFER_TYPE==0) with id=1 is available
    for the first (leaf) subbooking
    `draft` column is the `service.idx` property of a ServiceDraft instance.
    """

    COLUMNS = [DATE, RESOURCE_ID, RESOURCE_TYPE, DRAFT]

    # pylint: disable=line-too-long
    @classmethod
    def create(cls, drafts_matrix):
        """
                Create DrawerMatrix from availability matrix, like this:

                                     ServiceDraft(idx=0 res=SA)  ServiceDraft(idx=1 res=SA)  resource_type  resource_id
        resource_id date       slot
        1           2021-08-01 120                         True                        True              0            1
        2           2021-08-01 120                         True                        True              1            2
        3           2021-08-01 120                         True                        True              0            3
        4           2021-08-01 120                        False                       False              1            4

                Both drafts/subbookings with appliance id=4 are not valid
        """
        # pylint: enable=line-too-long

        # Rename `draft` to `draft.service.idx`.
        # It would hurt performance to have object values in cells.
        new_columns = {
            draft: draft.service.idx
            for draft in drafts_matrix.columns
            if isinstance(draft, ServiceDraft)
        }

        draw_matrix = (
            drafts_matrix.rename(columns=new_columns)
            # make `date` available for melt
            .reset_index(DATE)
            # transform to separate row for each combination of (`date`, `resource`) and `draft`
            .melt(id_vars=[DATE, RESOURCE_ID, RESOURCE_TYPE], var_name=DRAFT, value_name=AVAILABLE)
        )
        return cls(draw_matrix)


@dataclass
class Drawer:
    """
    Draw resources for appointments

    i.e. assign staffer and/or appliance for every subbooking in the appointment.
    A separate Drawer instance is created for each resource_type (staffers vs appliances).

    The vectorized implementation of drawing could be considered an overkill for a simple
    appointment, but it shines for repeating bookings; and single implementation is better.
    """

    resource_type: int
    drawer_matrix: DrawerMatrix
    drafts: list[ServiceDraft]
    # ResourcesDrawer provides preferred order or resources to select
    # if None, the Drawer has nothing to do, because all resources are set by user
    draw_order: Optional['DrawOrder']
    # list of appointment dates, can be longer than dates in drawer_matrix
    # since the matrix contains only valid/available records
    dates: list[date]

    def __post_init__(self):
        self.resource_ids_attr = (
            'staffer_ids' if self.resource_type == STAFFER_TYPE else 'appliance_ids'
        )
        self.resource_id_attr = (
            'staffer_id' if self.resource_type == STAFFER_TYPE else 'appliance_id'
        )

    @cached_property
    def resource_matrix(self):
        """
        Resources availability, like DrawerMatrix, but for single resource_type:
        either staffers or appliances; with prefered order added from ResourceDrawer, like this:

                 date  resource_id draft  order
        0  2021-08-01            1     0      0
        2  2021-08-01            3     0      1
        4  2021-08-01            1     1      0
        6  2021-08-01            3     1      1
        8  2021-08-01            1     2      0
        10 2021-08-01            3     2      1

        """
        draw_matrix = self.drawer_matrix[
            self.drawer_matrix[RESOURCE_TYPE] == self.resource_type
        ].drop(columns=[RESOURCE_TYPE])

        if self.draw_order is not None:
            ordered_ids = self.draw_order.get_resource_ids()
            order_df = DataFrame(range(len(ordered_ids)), index=ordered_ids, columns=[ORDER])
            draw_matrix = draw_matrix.join(order_df, on=[RESOURCE_ID])
        return draw_matrix

    @cached_property
    def sequential_draft_idxs(self):
        """
        Indexes (service.idx) of sequential leaf drafts requiring current resource_type

        Leaf drafts of parallel combo resources are skipped.
        """
        seq_drafts = (
            draft
            for draft in self.drafts
            if not draft.service.combo_parallel
            or (
                self.resource_type == APPLIANCE_TYPE and not draft.service.combo_parallel_appliances
            )
        )
        return [
            draft.service.idx
            for draft in iter_leaf_services(seq_drafts)
            if getattr(draft.service, self.resource_ids_attr)
        ]

    def overbooked(self) -> list[date]:
        """
        Validate that all subbookings are possible.

        It is done in separate step, before actually drawing resources.
        If not valid, return list of dates with conflicts.
        """

        leaf_drafts_count = len(
            [
                draft
                for draft in iter_leaf_services(self.drafts)
                if getattr(draft.service, self.resource_ids_attr)
            ]
        )

        # number of valid drafts (whatever resources) at each date
        unique_drafts = (
            self.resource_matrix[self.resource_matrix[AVAILABLE]]
            .groupby(DATE)
            .agg({'draft': 'nunique'})
        )
        if unique_drafts.empty:
            return self.dates

        # select only valid rows: dates where the number of valid drafts equals
        # the number of leaf drafts.
        valid = unique_drafts[unique_drafts.draft == leaf_drafts_count]

        if valid.size < len(self.dates):
            # convert Timestamp to python date
            valid_dates = {Timestamp(ts).date() for ts in valid.index.values}
            dates = [date_ for date_ in self.dates if date_ not in valid_dates]
            return dates

        return []

    def draw_resources(self, raise_exception=True) -> dict[date, dict[int, list[int]]]:
        # nothing to do: skip calculations
        if self.draw_order is None:
            return {}

        draw_dfs = []
        seq_draw_df = None

        if self.sequential_draft_idxs:
            matrix = self.resource_matrix[
                self.resource_matrix.draft.isin(self.sequential_draft_idxs)
            ]
            # Build sort columns:
            # 1. `draft` always the first column - group sorting by draft
            sort_columns = [DRAFT, AVAILABLE]
            # sort AVAILABLE descending, to get available first
            sort_ascending = [True, False]

            # 2. In multibooking, prefer resources common to most drafts;
            # it has preference over the order provided by ResourcesDrawer
            if len(self.sequential_draft_idxs) > 1:
                common_order = 10000 - (
                    matrix.groupby(RESOURCE_ID)
                    .agg({AVAILABLE: 'sum'})
                    .rename(columns={AVAILABLE: COMMON_ORDER})
                )
                matrix = matrix.join(common_order, on=[RESOURCE_ID])
                sort_columns.append(COMMON_ORDER)
                sort_ascending.append(True)

            # 3. Add `order` provided by ResourcesDrawer
            sort_columns.append(ORDER)
            sort_ascending.append(True)

            # Do the sorting and select the first resource in each date/draft group.
            # The result is not checked.
            # If not force_overbooking, self.overbooked was called earlier.
            # If force_overbooking, we get next unavailable resource,
            # when available are not present.
            #
            # This selection is done as if all drafts are sequential.
            # For drafts in parallel it gives wrong result - they will be corrected later.
            seq_draw_df = (
                matrix.sort_values(by=sort_columns, ascending=sort_ascending)
                .groupby([DATE, DRAFT])
                .agg('first')
                .reset_index()
            )

        for parent in self.drafts:
            if not parent.service.combo_parallel:
                continue

            if (
                self.resource_type == APPLIANCE_TYPE
                and not parent.service.combo_parallel_appliances
            ):
                assert seq_draw_df is not None
                seq_draw_df = parent.update_sequential_draw_df(seq_draw_df)
                continue

            par_draw_df = parent.get_parallel_draw_df(
                self.resource_matrix,
                raise_exception=raise_exception,
            )
            draw_dfs.append(par_draw_df)

        # at the end: seq_draw_df could be modified in `parent.update_sequential_draw_df`
        if seq_draw_df is not None:
            draw_dfs.append(seq_draw_df)

        if not draw_dfs:
            return {}

        draw_df = pd.concat(draw_dfs)

        # Convert the result to the plain python format:
        # {<date>: {<draft service idx>: resource_id}}
        result_df = (
            draw_df.sort_values([DATE, DRAFT])
            .groupby(DATE)
            .agg({RESOURCE_ID: list, DRAFT: list})
            .reset_index()
        )
        result = {
            row.date.date(): dict(zip(row.draft, row.resource_id)) for row in result_df.itertuples()
        }
        return result


class DrawOrder:
    """Draw resources for appointment

    i.e. assign staffer and/or appliance for every subbooking in the appointment
    A subclass must define `get_preferred_resource_ids` method.
    """

    def __init__(
        self, *, ordered_resource_ids: tuple[int, ...], resource_type: ResourceType, **_kwargs
    ):  # noqa
        self.ordered_resource_ids = ordered_resource_ids
        self.resource_type = resource_type

    def get_resource_ids(self):
        raise NotImplementedError()


class OrderedDrawOrder(DrawOrder):
    def get_resource_ids(self):
        return self.ordered_resource_ids


class RandomizedDrawOrder(DrawOrder):
    def get_resource_ids(self):
        resource_ids = list(self.ordered_resource_ids)
        shuffle(resource_ids)
        return resource_ids


class GridTurntrackerDrawOrder(DrawOrder):
    """Turntracker mode

    Use this resource drawer for today bookings - can use grid, but fallback to simple TT
    """

    def __init__(self, *, ordered_resource_ids, resource_type, turn_date, business_id, **_kwargs):
        super().__init__(ordered_resource_ids=ordered_resource_ids, resource_type=resource_type)
        self.turn_date = turn_date
        self.business_id = business_id

    def get_resource_ids(self):
        resource_ids = calculate_next_staffers_for_business(self.business_id)
        return resource_ids or get_next_turn(
            business_id=self.business_id,
            turn_date=self.turn_date,
            ordered_staffer_ids=self.ordered_resource_ids,
        )


class SimpleTurntrackerDrawOrder(DrawOrder):
    """Turntracker mode

    Use this resources drawer for future bookings.
    """

    def __init__(self, *, ordered_resource_ids, resource_type, turn_date, business_id, **_kwargs):
        super().__init__(ordered_resource_ids=ordered_resource_ids, resource_type=resource_type)
        self.turn_date = turn_date
        self.business_id = business_id

    def get_resource_ids(self):
        return get_next_turn(
            business_id=self.business_id,
            turn_date=self.turn_date,
            ordered_staffer_ids=self.ordered_resource_ids,
        )


@dataclass
class DrawResult:
    # resource ids for services in the order iter_leaf_services(services), group by date
    staffer_ids: dict[date, dict[int, list[int]]]
    appliance_ids: dict[date, dict[int, list[int]]]
    errors: bool = False


@tracer.wrap(DatadogOperationNames.VALIDATE_BOOKED)
def validate_booked(*, draw_scope: DrawScope, booked_ranges: BookedRanges) -> bool:
    """Validate booking by business with fully selected resources, without overbooking

    The only validation needed is against existing bookings.
    """
    if booked_ranges.empty:
        return True

    service_scope = draw_scope.service_scope
    assigned_resources = service_scope.assigned_resources
    if not assigned_resources:
        raise builtins.RuntimeError('Resources must be selected')

    slots = ResourceSlots.create(
        [
            (resource_id, slot_date, slot)
            for resource_id in assigned_resources
            for slot_date, slot in draw_scope.dates_slots
        ]
    ).set_index(SLOT, append=True, drop=False)
    drafts = [ServiceDraft.create(service) for service in service_scope.services]

    matrix = ValidationMatrix.create_factors(
        merged_slots=slots,
        booked_ranges=booked_ranges,
        drafts=drafts,
    )
    valid = matrix.agg('all', axis='columns')
    if not valid.all():
        overbooking_dates = valid[
            ~valid  # pylint: disable=invalid-unary-operand-type
        ].index.get_level_values(DATE)
        raise BookingConflict(overbooking_dates=overbooking_dates)

    return True


@tracer.wrap(DatadogOperationNames.DRAW_RESOURCES)
def validate_and_draw_resources(
    *,
    draw_scope: DrawScope,
    working_hours: WorkingHours,
    booked_ranges: BookedRanges,
    staffers_draw_order: Optional[DrawOrder] = None,
    appliances_draw_order: Optional[DrawOrder] = None,
    part_day_timeoffs: Optional[ResourceTimeOffs] = None,
    force_overbooking: bool = False,
):
    service_scope = draw_scope.service_scope
    slots = MergedSlots.create(draw_scope.dates_slots)
    drafts = [ServiceDraft.create(service) for service in service_scope.services]
    drafts_matrix = ValidationMatrix.create_factors(
        merged_slots=slots,
        booked_ranges=booked_ranges,
        working_hours=working_hours,
        part_day_timeoffs=part_day_timeoffs,
        drafts=drafts,
    )
    drafts_matrix = drafts_matrix.merge_factors(drafts)

    draw_matrix = DrawerMatrix.create(drafts_matrix)
    if service_scope.requires_staffer:
        staffers_drawer = Drawer(
            resource_type=STAFFER_TYPE,
            drawer_matrix=draw_matrix,
            drafts=drafts,
            draw_order=staffers_draw_order if service_scope.any_staffer_not_set else None,
            dates=draw_scope.time_scope.dates,
        )
        if overbooked := staffers_drawer.overbooked():
            if not force_overbooking:
                raise MissingResourcesException(overbooking_dates=overbooked)
            if service_scope.any_staffer_not_set and draw_scope.service_scope.resource_required:
                raise DrawForOverbookingException(overbooking_dates=overbooked)

    if service_scope.requires_appliance:
        appliances_drawer = Drawer(
            resource_type=APPLIANCE_TYPE,
            drawer_matrix=draw_matrix,
            drafts=drafts,
            draw_order=appliances_draw_order if service_scope.any_appliance_not_set else None,
            dates=draw_scope.time_scope.dates,
        )
        if overbooked := appliances_drawer.overbooked():
            if not force_overbooking:
                raise MissingResourcesException(overbooking_dates=overbooked)
            if service_scope.any_appliance_not_set and draw_scope.service_scope.resource_required:
                raise DrawForOverbookingException(overbooking_dates=overbooked)

    try:
        staffer_ids = (
            staffers_drawer.draw_resources(
                raise_exception=(
                    draw_scope.service_scope.resource_required or not force_overbooking
                )
            )
            if service_scope.requires_staffer
            else {}
        )
        appliance_ids = (
            appliances_drawer.draw_resources(
                raise_exception=(
                    draw_scope.service_scope.resource_required or not force_overbooking
                )
            )
            if service_scope.requires_appliance
            else {}
        )
    except MissingParallelResourcesException as e:
        if force_overbooking:
            raise DrawForOverbookingException() from e
        raise MissingResourcesException() from e

    return DrawResult(staffer_ids=staffer_ids, appliance_ids=appliance_ids)


def get_common_ids(id_lists: Iterable[Union[Iterable[int], None]]) -> set:
    return set.intersection(*map(set, filter(None, id_lists)))


def group_most_common_ids(
    id_list: Iterable[Union[IdsGroup, None]],
) -> IdsGroupList:
    """Convert groups of ids to groups of common ids - sorted from most common to least common

    e.g [(1, 2), (1,)] -> [(1,), (2,)]
    """
    most_common = Counter(chain.from_iterable(filter(None, id_list))).most_common()
    return [
        tuple(map(itemgetter(0), grouped))
        for __key, grouped in groupby(most_common, key=itemgetter(1))
    ]
