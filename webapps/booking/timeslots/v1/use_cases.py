# pylint: disable=duplicate-code
from datetime import date
from logging import getLogger
from typing import Optional, TYPE_CHECKING, Union

from bo_obs.datadog.enums import DatadogOperationNames
from ddtrace import tracer

from lib.datadog.enums import DatadogCustomServices
from lib.tools import tznow
from webapps.booking.simple_turntracker import get_next_turn
from webapps.booking.timeslots.v1.planner import SlotsResult
from webapps.booking.timeslots.v1.time_scope import DateRangeScope
from webapps.turntracker.grid.grid_main import calculate_next_staffers_for_business
from .booking_loader import booked_ranges_queryset
from .consts import AVAILABLE
from .data_sources import (
    BookedRanges,
    BusinessServices,
    GapHoleServices,
    ResourceTimeOffs,
    WorkingHours,
)
from .drawer import (
    DrawResult,
    DrawScope,
    ResourceType,
    validate_and_draw_resources,
    validate_booked,
)
from .matcher import MatchScope, match_subbookings
from .planner import generate_slots
from .schedule_loader import (
    resource_timeoffs_queryset,
    working_hours_queryset,
)
from .service_scope import BusinessSettings, ServiceScope, SlotsScope

if TYPE_CHECKING:
    from webapps.business.models import Business

log = getLogger('booksy.timeslots')


def availability_slots_create(  # pylint: disable=too-many-arguments
    *,
    time_scope: DateRangeScope,
    business_settings: BusinessSettings,
    business_resources_data,
    business_services_data,
    booked_ranges_data,
    working_hours_data,
    resource_timeoffs_data,
):
    business_services = BusinessServices.create(
        business_services_data=business_services_data,
        business_resources_data=business_resources_data,
    )
    booked_ranges = BookedRanges.create(
        booked_ranges_data=booked_ranges_data,
    )
    resource_timeoffs = ResourceTimeOffs.create(
        resource_timeoffs_data=resource_timeoffs_data,
        time_scope=time_scope,
    )
    working_hours = WorkingHours.create(working_hours_data=working_hours_data)

    booked_ranges = booked_ranges.split_on_gap_hole(business_services)
    booked_ranges = resource_timeoffs.modify_booked_ranges(booked_ranges)
    working_hours = resource_timeoffs.modify_working_hours(working_hours)

    for service_variant_id, service_variant_data in business_services.iterrows():
        if not service_variant_data[AVAILABLE]:
            continue

        service_scope = ServiceScope.from_service_variant(
            service_variant_id=service_variant_id,
            business_services=business_services,
            business_settings=business_settings,
        )

        slots_result = generate_slots(
            service_scope=service_scope,
            working_hours=working_hours.for_resource_ids(service_scope.resource_ids),
            booked_ranges=booked_ranges.for_resource_ids(service_scope.resource_ids),
        )
        # do something with results
        print(slots_result.merged_slots_series())


@tracer.wrap(DatadogOperationNames.SLOTS, service=DatadogCustomServices.TIMESLOTS_ENGINE)
def customer_slots_create(
    *,
    slots_scope: SlotsScope,
    business_services: BusinessServices,
) -> SlotsResult:
    booked_ranges = BookedRanges.create(
        booked_ranges_data=booked_ranges_queryset(
            slots_scope.time_scope,
            business_id=slots_scope.business_id,
            resource_ids=slots_scope.service_scope.resource_ids,
            omit_appointment_ids=slots_scope.omit_appointment_ids,
        ),
    )

    working_hours = WorkingHours.create(
        working_hours_data=working_hours_queryset(
            slots_scope.time_scope,
            business_id=slots_scope.business_id,
            resource_ids=slots_scope.service_scope.resource_ids,
        ),
    )
    resource_timeoffs = ResourceTimeOffs.create(
        resource_timeoffs_data=resource_timeoffs_queryset(
            slots_scope.time_scope,
            business_id=slots_scope.business_id,
            resource_ids=slots_scope.service_scope.resource_ids,
        ),
        time_scope=slots_scope.time_scope,
    )

    booked_ranges = booked_ranges.split_on_gap_hole(business_services)
    booked_ranges = resource_timeoffs.modify_booked_ranges(booked_ranges)
    working_hours = resource_timeoffs.modify_working_hours(working_hours)

    slots_response = generate_slots(
        service_scope=slots_scope.service_scope,
        booked_ranges=booked_ranges,
        working_hours=working_hours,
    )

    return slots_response


@tracer.wrap(DatadogOperationNames.MATCHER, service=DatadogCustomServices.TIMESLOTS_ENGINE)
def appointment_subbookings_match(
    *,
    match_scope: MatchScope,
    business: 'Business',
):
    """
    Matching is the process of evaluation and population of an appointment during dry_run.

    It involves:
    - attaching resources availability info
    - filling missing booked_from/booked_till values according to service_variant properties
    - in the case of multibooking, finding non conflicting slots and resources to fullfill
    requested appointment (more below).

    Manually selected resources and AnyResource are preserved, with the exception in multibooking:
    - if a manually selected resource is propagated to following subbookings then the resource
    replaces AnyResource
    AnyResource is added, when needed.

    Possibly modified fields:
    - booked_form
    - booked_till
    - staffer
    - appliance
    Added field:
    - _availability

    Multibooking matching depends on service_scope.preserve_order and fixed_start of services.

    - with preserve_order=true and all services with fixed_start: no changes allowed
    - with preserve_order=true and some services without fixed_start:
        - services with fixed start must be at the beginning; service with fixed_start after
          a service without fixed start is an error
        - serivces without fixed_start can be delayed (added customer waiting_time) to avoid
          conflicts
    - with preserve_order=false:
        - fixed_start is forbidden in all services (but the first)
        - services can be reordered and delayed to avoid conflicts

    :param subbookings: list of subbookings data from dry_run
    :returns: modified subbookings
    """

    booked_ranges = BookedRanges.create(
        booked_ranges_data=booked_ranges_queryset(
            match_scope.time_scope,
            business_id=business.id,
            resource_ids=match_scope.service_scope.resource_ids,
            omit_appointment_ids=match_scope.omit_appointment_ids,
        ),
    )
    working_hours = WorkingHours.create(
        working_hours_data=working_hours_queryset(
            match_scope.time_scope,
            business_id=business.id,
            resource_ids=match_scope.service_scope.resource_ids,
        ),
    )
    resource_timeoffs = ResourceTimeOffs.create(
        resource_timeoffs_data=resource_timeoffs_queryset(
            match_scope.time_scope,
            business_id=business.id,
            resource_ids=match_scope.service_scope.resource_ids,
        ),
        time_scope=match_scope.time_scope,
    )

    if not booked_ranges.empty:
        gap_hole_services = GapHoleServices.create(business.id)
        booked_ranges = booked_ranges.split_on_gap_hole(gap_hole_services)

    working_hours = resource_timeoffs.modify_working_hours(working_hours)
    resource_timeoffs = resource_timeoffs.part_day_timeoffs()

    match_result = match_subbookings(
        match_scope=match_scope,
        working_hours=working_hours,
        part_day_timeoffs=resource_timeoffs,
        booked_ranges=booked_ranges,
    )

    return match_result


@tracer.wrap(DatadogOperationNames.DRAWER, service=DatadogCustomServices.TIMESLOTS_ENGINE)
def appointment_resources_draw(  # pylint: disable=too-many-return-statements
    *,
    draw_scope: DrawScope,
    business: 'Business',
    validate=True,
    force_overbooking=False,
) -> Optional[DrawResult]:  # noqa
    """Validate appointment and (if needed) draw resources.

    Drawing resources is assigning required staffer/appliance to every subbooking, where needed.

    :param business: Business instance
    :param subbookings: prepared appointment's subbookings before saving
    :param validate: stop processing on any conflict in availability - always for Customer Booking
    :param force_overbooking: ignore conflicts with other bookings - only Business can do it

    :return: errors, subbookings (with resources assigned)
    """
    business_id = draw_scope.business_id

    if draw_scope.service_scope.pointless:
        return None

    all_resources_set = not draw_scope.service_scope.any_resource_not_set

    # Processing is split into different scenarios. This makes the code simpler - don't mix
    # all scenarios in the single process; it allows also skip loading not needed data.

    # First, analyze data to select scenario:

    # no further processing
    no_action_needed = force_overbooking and all_resources_set
    # only validate against BookedRanges
    booked_validation_needed = not validate and not force_overbooking and all_resources_set

    # Select scenario, load required data and execute scenario:

    if no_action_needed:
        return None

    booked_ranges = BookedRanges.create(
        booked_ranges_data=booked_ranges_queryset(
            draw_scope.time_scope,
            business_id=business_id,
            resource_ids=draw_scope.service_scope.resource_ids,
            omit_appointment_ids=draw_scope.omit_appointment_ids,
            omit_repeating_ids=draw_scope.omit_repeating_ids,
        ),
    )

    if not booked_ranges.empty:
        gap_hole_services = GapHoleServices.create(business_id)
        booked_ranges = booked_ranges.split_on_gap_hole(gap_hole_services)

    if booked_validation_needed:
        validate_booked(draw_scope=draw_scope, booked_ranges=booked_ranges)
        return None

    working_hours = WorkingHours.create(
        working_hours_data=working_hours_queryset(
            draw_scope.time_scope,
            business_id=business_id,
            resource_ids=draw_scope.service_scope.resource_ids,
        ),
    )
    resource_timeoffs = ResourceTimeOffs.create(
        resource_timeoffs_data=resource_timeoffs_queryset(
            draw_scope.time_scope,
            business_id=business_id,
            resource_ids=draw_scope.service_scope.resource_ids,
        ),
        time_scope=draw_scope.time_scope,
    )

    working_hours = resource_timeoffs.modify_working_hours(working_hours)
    part_day_timeoffs = resource_timeoffs.part_day_timeoffs()

    staffers_drawer = (
        draw_scope.staffers_drawer_class(
            ordered_resource_ids=draw_scope.service_scope.staffer_ids,
            resource_type=ResourceType.STAFFER,
            turn_date=draw_scope.time_scope.dates[0],
            business_id=business.id,
        )
        if draw_scope.service_scope.any_staffer_not_set
        else None
    )
    appliances_drawer = (
        draw_scope.appliances_drawer_class(
            ordered_resource_ids=draw_scope.service_scope.appliance_ids,
            resource_type=ResourceType.APPLIANCE,
        )
        if draw_scope.service_scope.any_appliance_not_set
        else None
    )

    draw_result = validate_and_draw_resources(
        draw_scope=draw_scope,
        working_hours=working_hours,
        booked_ranges=booked_ranges,
        part_day_timeoffs=part_day_timeoffs,
        force_overbooking=force_overbooking,
        staffers_draw_order=staffers_drawer,
        appliances_draw_order=appliances_drawer,
    )

    return draw_result


def get_calendar_next_staffer_id(*, business_id: int, for_date: date) -> Union[int, None]:
    from webapps.business.models import Business

    business = Business.objects.get(id=business_id)

    staffer_id = None

    if for_date == tznow(business.get_timezone()).date():
        next_staffers = calculate_next_staffers_for_business(business)
        staffer_id = next_staffers[0] if next_staffers else None

    return staffer_id


def get_next_staffer_ids(business_id: int, for_date: 'date', working_resource_ids) -> list[int]:
    booked_ranges = BookedRanges.create(
        booked_ranges_data=booked_ranges_queryset(
            DateRangeScope(start_date=for_date, end_date=for_date),
            business_id=business_id,
            resource_ids=working_resource_ids,
        ),
    )

    return get_next_turn(booked_ranges, working_resource_ids)
