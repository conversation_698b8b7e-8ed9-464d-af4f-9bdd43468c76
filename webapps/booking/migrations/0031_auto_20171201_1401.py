# Generated by Django 1.10 on 2017-12-01 14:01
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('booking', '0030_merge_20171115_1639'),
    ]

    operations = [
        migrations.AddField(
            model_name='repeatingbooking',
            name='parent',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name='children',
                to='booking.RepeatingBooking',
            ),
        ),
        migrations.AlterField(
            model_name='booking',
            name='type',
            field=models.CharField(
                choices=[
                    ('B', 'Created by business'),
                    ('C', 'Created by customer'),
                    ('T', 'Resource time off (full days)'),
                    ('R', 'Resource time reservation (like booking)'),
                ],
                max_length=1,
            ),
        ),
        migrations.AlterField(
            model_name='bookingsources',
            name='name',
            field=models.CharField(
                choices=[
                    ('Web', 'Web'),
                    ('Widget', 'Widget'),
                    ('Android', 'Android'),
                    ('iPhone', 'iPhone'),
                    ('Internal', 'Internal'),
                    ('ImporterPARP', 'ImporterPARP'),
                    ('GoogleFeeds', 'GoogleFeeds'),
                    ('Facebook', 'Facebook'),
                    ('YelpFeeds', 'YelpFeeds'),
                ],
                max_length=20,
            ),
        ),
    ]
