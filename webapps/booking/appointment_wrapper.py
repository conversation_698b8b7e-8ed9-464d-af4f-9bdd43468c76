#!/usr/bin/env python
import builtins
from contextlib import contextmanager
from typing import ContextManager

from django.core.exceptions import ObjectDoesNotExist
from django.db.transaction import atomic
from django.utils.functional import cached_property

from lib.es_history.utils import partial_to_representation
from lib.feature_flag.feature.monetisation import EnablePeakHoursFlag
from lib.rivers import bump_document, River
from lib.strategy import StrategyMixin, process_strategy
from lib.tools import datetimeinfinity
from webapps.booking.appointment_checkout import AppointmentCheckoutFactory
from webapps.booking.enums import (
    AppointmentTypeSM,
    WhoMakesChange,
    RepeatType,
    AppointmentType,
)
from webapps.booking.exceptions import BookingConflict
from webapps.booking.models import Appointment, Empty, RepeatingBooking, SubBooking
from webapps.booking.no_show_protection.business_appointment import (
    create_business_appointment_deposit_validator_factory as deposit_validator,
)
from webapps.booking.serializers.history import AppointmentWrapperHistorySerializer
from webapps.booking.tasks import split_new_repeating_task, update_repeating_by_parent_booking
from webapps.business.models.bci import (
    BusinessCustomerInfo,
    BCIFromContact,
)
from webapps.business.omnibus_price import get_omnibus_prices
from webapps.consents.manager import ConsentManager
from webapps.public_partners.events import send_signal_to_partner
from webapps.public_partners.signals import send_appointment_webhooks_to_partner_apps_signal
from webapps.booking.appointment_checkout import AppointmentCheckout
from webapps.business.service_price import ServicePrice


class AppointmentWrapper(StrategyMixin):
    PROXY_TO_APPOINTMENT = {
        # 'booked_from',  # separate property
        'booked_for',
        'booked_for_id',
        'booked_by',
        'customer_name',
        'customer_email',
        'customer_phone',
        'business',
        'business_id',
        'family_and_friends',
        'first_booking_id',
        'is_family_and_friends',
        'can_customer_change',
        'status',
        'repeating',
        'repeating_id',
        'customer_note',
        'business_note',
        'business_secret_note',
        'external_source',
        'service_questions',
        'total',
        'prepayment_deadline',
        'is_prepaid',
        'is_booksy_pay_eligible',
        'is_having_deposit',
        'is_booksy_pay_available',
        'is_booksy_pay_payment_window_open',
        'is_paid_by_booksy_pay',
        # autofilled data
        'type',
        'source',
        'source_id',
        'updated_by',
        'updated_by_id',
        'join_meeting_url',
        'meeting_id',
        'traveling',
        'from_promo',
        '_version',
        'version',
        'import_uid',
        'partner_app_data',
        'created',
    }
    PROXY_TO_FIRST_SUBBOOKING = {
        # methods
        'get_payment_and_deposit',
        'clear_cache_payment_and_deposit',
    }
    PROXY_TO_LAST_SUBBOOKING = {
        'review',
        'review_id',
    }

    def __init__(self, subbookings, is_public_api=False):
        super().__init__()
        try:
            appt = subbookings[0].appointment
        except ObjectDoesNotExist:
            appt = Appointment()
            for subbooking in subbookings:
                subbooking.appointment = appt

        self.appointment = appt
        self._is_new_record = not bool(self.appointment.pk)
        self.subbookings = self._sort_subbookings(subbookings)
        self._first = subbookings[0]
        self._last = subbookings[-1]
        self._set_appointment_booked_dates()
        self._is_public_api = is_public_api
        self._booksy_pay_flow_enabled: bool = False

    @staticmethod
    def _sort_subbookings(subbookings):
        dt_infinity = datetimeinfinity()

        def sort_key(subbooking):
            subbooking_id = getattr(subbooking, 'id', None)
            if subbooking_id is None:
                subbooking_id = float('inf')

            return subbooking.booked_from or dt_infinity, subbooking_id

        return sorted(subbookings, key=sort_key)

    def _set_appointment_booked_dates(self):
        appt = self.appointment
        appt.booked_from = appt.calculate_booked_from(self.subbookings)
        appt.booked_till = appt.calculate_booked_till(self.subbookings)

    def is_multibooking(self):
        return len([s for s in self.subbookings if s.id]) > 1 or (
            self.appointment.is_multibooking()
        )

    def __getattr__(self, item):
        if item in self.PROXY_TO_APPOINTMENT:
            return getattr(self.appointment, item)
        if item in self.PROXY_TO_FIRST_SUBBOOKING:
            return getattr(self._first, item)
        if item in self.PROXY_TO_LAST_SUBBOOKING:
            return getattr(self._last, item)
        raise AttributeError(f'Appointment object has no attribute {item}')

    @atomic
    def save(
        # pylint: disable=too-many-arguments, too-many-branches, too-many-statements, too-many-positional-arguments
        self,
        to_delete,
        overbooking,
        who_makes_change,
        update_future,
        new_repeating=None,
        dry_run=False,
        recurring=None,
        validate_as: WhoMakesChange = None,
        prev_status=Empty,
        is_booksy_gift_card_appointment=None,
        deposit: dict | None = None,
    ):
        try:
            # clear checkout cached_property
            del self.checkout
        except AttributeError:
            pass

        self._set_appointment_booked_dates()

        if deposit is not None:
            create_business_appointment_deposit_validator = (
                deposit_validator.CreateBusinessAppointmentDepositValidatorFactory.make(
                    deposit_validator.CreateBusinessAppointmentDepositValidatorFactoryParams(
                        business_id=self.appointment.business_id,
                        appointment=self.appointment,
                        deposit_amount=deposit['deposit_amount'],
                        subbookings=self.subbookings,
                        appointment_total_value=self.checkout.total.value,
                    )
                )
            )
            if not create_business_appointment_deposit_validator.validate():
                raise create_business_appointment_deposit_validator.get_error()
        if dry_run:
            if new_repeating is not None:
                # set new_repeating attribute to send it back to user
                self.new_repeating = new_repeating
            self.appointment.subbookings = self.subbookings
            for subbooking in self.appointment.subbookings:
                subbooking._update_service_data()  # pylint: disable=protected-access
                if hasattr(subbooking, '_service_promotion'):
                    del subbooking._service_promotion  # pylint: disable=protected-access
            return

        appointment = self.appointment
        self._set_customer_data(appointment, who_makes_change, recurring)

        appointment.total = self.checkout.total

        # preserve selected resources, possible AnyResource before draw
        selected_resources = self.subbookings[0].staffer, self.subbookings[0].appliance
        resource_required = True
        if new_repeating and new_repeating['repeat'] == RepeatType.GROUP:
            resource_required = False

        try:
            appointment.make_appointment(
                subbookings=self.subbookings,
                subbooking_ids_to_delete=[b.id for b in to_delete],
                who_makes_change=validate_as or who_makes_change,
                overbooking=overbooking,
                updated_by=self.updated_by,
                prev_status=prev_status,
                resource_required=resource_required,
                is_booksy_gift_card_appointment=(
                    is_booksy_gift_card_appointment
                    if is_booksy_gift_card_appointment is not None
                    else appointment.is_booksy_gift_card_appointment
                ),
            )
        except BookingConflict as exc:
            if appointment.repeating and not new_repeating and update_future:
                # try to raise exception for all bookings conflicts
                self.repeating.split_repeating_by_booking(
                    appointment=self.appointment,
                    update_future=update_future,
                    overbooking=overbooking,
                    who_makes_change=who_makes_change,
                    updated_by=self.updated_by,
                    addons_to_update=self.subbookings[0].addons,
                    resources=selected_resources,
                )
            raise exc

        if appointment.repeating and not new_repeating:
            # skipped for RepeatType.GROUP
            self.appointment.repeating.split_repeating_by_booking(
                appointment=self.appointment,
                update_future=update_future,
                overbooking=overbooking,
                who_makes_change=who_makes_change,
                updated_by=self.updated_by,
                addons_to_update=self.subbookings[0].addons,
                resources=selected_resources,
            )

            if update_future and self.appointment.repeating.repeat == RepeatType.GROUP:
                self.appointment.repeating.update_group_booking(
                    prototype=self.appointment,
                    updated_by=self.updated_by,
                    who_makes_change=who_makes_change,
                    overbooking=overbooking,
                )

        Appointment.update_subbookings_addons(self.subbookings)
        self.used_addons_cache_clear()

        if EnablePeakHoursFlag():
            Appointment.update_subbookings_surcharges(self.subbookings)

        if new_repeating:
            # trim previously existing repeating
            existing_repeating = appointment.repeating
            to_delete_appts = []
            if existing_repeating:
                to_delete_appts = existing_repeating.trim_to_appointment_id(appointment.id)
            # create new repeating
            repeating = RepeatingBooking()  # data set in make_repeating_booking
            booking = self.subbookings[0]
            booking.appointment = appointment
            bookings_list = [
                booking,
            ] + [
                repeating.clone_appointment(
                    appointment,
                    booking,
                    draft,
                    repeat_type=new_repeating['repeat'],
                    status=SubBooking.compute_new_status(
                        booked_till=draft['booked_till'],
                        base_status=Appointment.STATUS.ACCEPTED,
                    ),
                )[1]
                for draft in new_repeating['booking_dates'][1:]
            ]
            repeating.make_repeating_booking(
                data=new_repeating,
                bookings_list=bookings_list,
                deleted_appointments=to_delete_appts,
                updated_by=appointment.updated_by,
                _who_makes_change=who_makes_change,
                overbooking=overbooking,
                business=appointment.business,
            )
            if existing_repeating is not None:
                # get child of existing repeating booking if exist
                # sorted by date of creation in desc order
                child = existing_repeating.children.order_by('-created').first()
                if child is not None:
                    # repeating parent is adopting happy little child
                    # from unfavorable existing_repeating parent
                    # Always based on the real story
                    with atomic():
                        child.parent = repeating
                        child.save(update_fields=['parent'])
                    # call task to update child repeating
                    if update_future:
                        update_repeating_by_parent_booking.delay(
                            child_id=child.id,
                            who_makes_change=who_makes_change,
                            updated_by_id=booking.appointment.updated_by_id,
                            resource_ids=[
                                booking.staffer.id if booking.staffer else None,
                                booking.appliance.id if booking.appliance else None,
                            ],
                        )

            split_new_repeating_task.delay(repeating_id=repeating.id)

            self.subbookings[0] = repeating.first_appointment.subbookings[0]

        # clear subbooking staffer and appliance caches
        for subbooking in self.subbookings:
            subbooking.clear_resource_caches()

        # update client data
        self._set_client_data()
        self.send_signal()

    @process_strategy('customer_data')
    def _set_customer_data(self, appointment, who_makes_change, recurring):
        # try to create customer object
        bci = None
        if who_makes_change == WhoMakesChange.CUSTOMER:
            bci = BusinessCustomerInfo.get_or_create_for_user(
                user=appointment.updated_by,
                business=appointment.business,
                recurring=recurring,
                source=appointment.source,
            )
        elif (
            who_makes_change == WhoMakesChange.BUSINESS
            and (appointment.customer_phone or appointment.customer_email)
            and appointment.booked_for_id is None
        ):
            bci, _c = BusinessCustomerInfo.get_or_create_from_contact(
                BCIFromContact(
                    business=appointment.business,
                    customer_name=appointment.customer_name,
                    customer_email=appointment.customer_email,
                    customer_phone=appointment.customer_phone,
                ),
            )  # auto create or merge

        if bci:
            appointment.booked_for = bci
            customer_data = bci.as_customer_data()
            appointment.customer_name = customer_data.full_name
            appointment.customer_phone = customer_data.cell_phone
            appointment.customer_email = customer_data.email
        else:
            bci = appointment.booked_for
        if bci and not bci.visible_in_business:
            bci.visible_in_business = True
            bci.save(update_fields=['visible_in_business'])
            bump_document(River.BUSINESS_CUSTOMER, bci.id)

    @process_strategy('client_data')
    def _set_client_data(self):
        if not self.booked_for:
            return
        update_fields = []
        if (
            self.booked_for
            and self.booked_for.cell_phone
            and BusinessCustomerInfo.objects.filter(
                business_id=self.business.id, cell_phone=self.booked_for.cell_phone
            )
            .exclude(id=self.booked_for_id)
            .exists()
        ):
            if self.booked_for.set_client_type(self.booked_for.CLIENT_TYPE__BUSINESS_PHONE):
                update_fields.append('client_type')
        else:
            if self.booked_for.set_client_type(BusinessCustomerInfo.CLIENT_TYPE__BUSINESS_DIRECTLY):
                update_fields.append('client_type')
        if self.booked_for.first_appointment_id is None:
            self.booked_for.first_appointment = self.appointment
            update_fields.append('first_appointment')
        if update_fields:
            self.booked_for.save(update_fields=update_fields)

    @property
    def appointment_id(self):
        # it's only backwards compatible, real appointment_id is appointment_uid
        return self.appointment.id if self.is_multibooking() else self._first.id

    @property
    def appointment_uid(self):
        return self.appointment.id

    @property
    def appointment_type(self):
        return AppointmentTypeSM.MULTI if self.is_multibooking() else (AppointmentTypeSM.SINGLE)

    @property
    def id(self):  # pylint: disable=invalid-name
        """Used in .get_actions()"""
        # TODO: REMOVE - only for test - to check usage
        return self.appointment_uid

    @property
    def booked_from(self):
        return self.appointment.calculate_booked_from(self.subbookings)

    @property
    def booked_till(self):
        return self.appointment.calculate_booked_till(self.subbookings)

    @property
    def secret(self):
        return self.appointment.secret

    def booked_for_data(self, access_level=None):
        booked_for = self.appointment.booked_for
        if not booked_for:
            return None
        from webapps.business.searchables.serializers.business_customer import (
            BCIWithLastAppointmentHitSerializer,
        )

        return BCIWithLastAppointmentHitSerializer(
            instance=booked_for,
            context={'access_level': access_level, 'refresh_document': True},
        ).data

    @property
    def total(self) -> ServicePrice:
        return self.checkout.total

    @cached_property
    def checkout(self) -> AppointmentCheckout:
        try:
            return AppointmentCheckoutFactory(
                business=self.business,
                subbookings=self.subbookings,
                traveling=self.traveling,
                pos=self.business.pos,
                booksy_pay_flow_enabled=self.booksy_pay_flow_enabled,
                is_paid_by_booksy_pay=self.is_paid_by_booksy_pay,
                appointment_type=AppointmentType(self.appointment.type),
            ).create()
        except AttributeError as e:
            # reraise as RuntimeError to make original exception visible,
            # otherwise it will be hidden behind Appointment's AttributeError
            raise builtins.RuntimeError from e

    @cached_property
    def omnibus_prices(self):
        try:
            return self._get_omnibus_prices()
        except AttributeError as e:
            # reraise as RuntimeError to make original exception visible,
            # otherwise it will be hidden behind Appointment's AttributeError
            raise builtins.RuntimeError from e

    def _get_omnibus_prices(self):
        service_variant_ids = [
            booking.service_variant.id for booking in self.subbookings if booking.service_variant
        ]
        if not service_variant_ids:
            return {}
        return get_omnibus_prices(service_variant_ids)

    def check_access(self, staffer):
        """Check whether appointment is editable by given staffer."""
        return any(subbooking.check_access(staffer) for subbooking in self.subbookings)

    @classmethod
    def get_appointment(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        cls,
        appointment_type,
        appointment_id,
        business_id=None,
        customer_user_id=None,
        prefetch_all=True,
    ):
        if appointment_type == AppointmentTypeSM.SINGLE:
            fnc = cls.get_by_subbooking_id
        elif appointment_type == AppointmentTypeSM.MULTI:
            fnc = cls.get_by_appointment_id
        else:
            raise NotImplementedError()

        appointment = fnc(
            appointment_id,
            business_id=business_id,
            customer_user_id=customer_user_id,
            prefetch_all=prefetch_all,
        )
        return appointment

    @classmethod
    def get_by_subbooking_id(
        cls, appointment_id, business_id=None, customer_user_id=None, prefetch_all=True
    ):
        return cls._get_by_id(
            'bookings',
            appointment_id,
            business_id,
            customer_user_id,
            prefetch_all=prefetch_all,
        )

    @classmethod
    def get_by_appointment_id(
        cls,
        appointment_id,
        business_id=None,
        customer_user_id=None,
        prefetch_all=True,
    ):
        return cls._get_by_id('id', appointment_id, business_id, customer_user_id, prefetch_all)

    @classmethod
    def _get_by_id(
        cls,
        field_name,
        _id,
        business_id,
        customer_user_id,
        prefetch_all,
    ):
        assert bool(business_id) ^ bool(customer_user_id)

        qset = Appointment.objects.filter(deleted__isnull=True, **{field_name: _id})
        if business_id:
            qset = qset.filter(business=business_id)
        elif customer_user_id:
            qset = qset.filter(booked_for__user_id=customer_user_id)

        appointment = qset.first()

        if not appointment and customer_user_id:
            appointment = (
                Appointment.objects.filter(deleted__isnull=True, **{field_name: _id})
                .filter(family_and_friends__booked_by__user_id=customer_user_id)
                .first()
            )

        if appointment is None:
            return None

        subbookings = (
            appointment.prefetch_all_subbookings if prefetch_all else appointment.subbookings
        )

        if not subbookings:
            return None

        return cls(subbookings=subbookings)

    @cached_property
    def consent_manager(self):
        return ConsentManager.from_appointment(self.appointment)

    def consent_manager_cache_clear(self):
        try:  # nosemgrep
            del self.consent_manager  # noqa
        except AttributeError:
            pass

    @cached_property
    def used_addons(self):
        return {
            usage.service_addon_id: usage
            for subbooking in self.appointment.subbookings
            for usage in subbooking.addons_set.all()
        }

    def used_addons_cache_clear(self):
        try:  # nosemgrep
            del self.used_addons  # noqa
        except AttributeError:
            pass

    def send_signal(self):
        if not self._is_public_api:
            send_appointment_webhooks_to_partner_apps_signal(
                sender=Appointment,
                instance=self.appointment,
                created=self._is_new_record,
            )
            send_signal_to_partner.send(
                self.appointment_uid,
                business_id=self.appointment.business_id,
            )

    def get_appointment_sms_deeplink(self, sms_source):
        return self.appointment.get_appointment_sms_deeplink(sms_source)

    def extract_state(self, fields=None):
        return partial_to_representation(AppointmentWrapperHistorySerializer, self, fields)

    @contextmanager
    def booksy_pay_flow(self) -> ContextManager[None]:
        """
        Enables Booksy Pay context for an appointment.

        Booksy Pay flow forces a 100% prepayment (see AppointmentCheckoutFactory).
        """
        self._booksy_pay_flow_enabled = True
        yield
        self._booksy_pay_flow_enabled = False

    @property
    def booksy_pay_flow_enabled(self) -> bool:
        return self._booksy_pay_flow_enabled

    @property
    def is_booksy_gift_card_appointment(self) -> bool:
        return self.appointment.is_booksy_gift_card_appointment or False
