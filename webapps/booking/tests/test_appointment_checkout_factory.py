from decimal import Decimal
from operator import attrgetter
from datetime import datetime, time

import pytest
from django.db import connection
from django.test.utils import CaptureQueriesContext
from model_bakery import baker

from webapps.booking.appointment_checkout import (
    AppointmentCheckoutFactory,
    BooksyPayPrice,
    BooksyPayRate,
    PrepayPrice,
    PrepayRate,
    _Row,
)
from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.booking.enums import AppointmentType
from webapps.booking.models import (
    Appointment,
    SubBooking,
)
from webapps.booking.tests.utils import create_appointment
from webapps.business.baker_recipes import (
    business_recipe,
    service_recipe,
    service_variant_recipe,
)
from webapps.business.enums import (
    ComboPricing,
    ComboType,
    PriceType,
)
from webapps.business.models.service import (
    ComboMembership,
    ServiceVariantPayment,
)
from webapps.business.service_price import ServicePrice
from webapps.pos.models import TransactionRow
from webapps.premium_services.public import DayOfWeek, PeakHour, PeakHourServiceFactory, Service


@pytest.mark.django_db
def test_appointment_checkout_factory__addons():
    appointment = create_appointment()
    business = appointment.business
    subbooking = appointment.subbookings[0]

    pos = baker.make('pos.POS', business=business)
    baker.make('pos.TaxRate', pos=pos, rate=Decimal(7), default_for_service=True)

    checkout = AppointmentCheckoutFactory(
        business=business,
        subbookings=sorted(appointment.subbookings, key=attrgetter('id')),
        traveling=appointment.traveling,
        pos=pos,
        appointment_type=AppointmentType(appointment.type),
    ).create()
    assert checkout.components[0].rows[0].item_price is None

    service_variant = service_variant_recipe.make(price=Decimal(10), type=PriceType.STARTS_AT)
    subbooking.service_variant = service_variant
    subbooking.save(override=True)

    baker.make(
        'business.ServiceAddOnUse',
        business=business,
        subbooking=subbooking,
        service_addon=baker.make('business.ServiceAddOn', name='addon first'),
        price=15.00,
        quantity=2,
        services_ids=[service_variant.service.id],
    )
    baker.make(
        'business.ServiceAddOnUse',
        business=business,
        subbooking=subbooking,
        service_addon=baker.make('business.ServiceAddOn', name='addon second'),
        price=20.00,
        quantity=2,
        services_ids=[service_variant.service.id],
    )

    checkout = AppointmentCheckoutFactory(
        business=business,
        subbookings=sorted(appointment.subbookings, key=attrgetter('id')),
        traveling=appointment.traveling,
        pos=pos,
        appointment_type=AppointmentType(appointment.type),
    ).create()
    assert checkout.components[0].rows[0].item_price.price_type == PriceType.STARTS_AT
    assert checkout.components[0].rows[0].item_price.value == Decimal(10)
    assert checkout.components[0].rows[1].item_price.value == Decimal(15)
    assert checkout.components[0].rows[2].item_price.value == Decimal(20)


@pytest.mark.django_db
def test_appointment_checkout_factory__no_show_protection():
    appointment = create_appointment()
    business = appointment.business
    subbooking = appointment.subbookings[0]

    pos = baker.make('pos.POS', business=business)
    baker.make('pos.TaxRate', pos=pos, rate=Decimal(7), default_for_service=True)

    service_variant = service_variant_recipe.make(price=Decimal(10), type=PriceType.STARTS_AT)
    baker.make(
        'business.ServiceVariantPayment',
        service_variant=service_variant,
        payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
        payment_amount=Decimal('1.5'),  # 15% of $10
        saving_type=ServiceVariantPayment.PERCENTAGE,
    )

    subbooking.service_variant = service_variant
    subbooking.save(override=True)

    checkout_factory = AppointmentCheckoutFactory(
        subbookings=sorted(appointment.subbookings, key=attrgetter('id')),
        traveling=appointment.traveling,
        business=business,
        pos=pos,
        appointment_type=AppointmentType(appointment.type),
    )
    checkout = checkout_factory.create()
    assert checkout.components[0].rows[0].item_price.price_type == PriceType.STARTS_AT
    assert checkout.components[0].rows[0].item_price.value == Decimal(10)

    service_variant.refresh_from_db()
    subbooking.refresh_from_db()

    checkout = checkout_factory.create()
    assert checkout.components[0].rows[0].for_prepay is True
    assert checkout.components[0].prepay_rate == PrepayRate(
        value=Decimal(15),  # as %
        is_amount=False,
        is_cancellation_fee=True,
    )


@pytest.mark.parametrize(
    'for_listing',
    [
        (True,),
        (False,),
    ],
)
@pytest.mark.django_db
def test_appointment_wrapper_checkout__db_queries(for_listing):
    """
    Ensure that AppointmentCheckoutFactory and calculation of appointment.total
    from wrapper does not cause extra db fetches.
    """
    appointment = create_appointment()
    business = appointment.business

    pos = baker.make('pos.POS', business=business)
    baker.make('pos.TaxRate', pos=pos, rate=Decimal(7), default_for_service=True)

    subbookings = list(
        SubBooking.objects.filter(appointment_id=appointment.id).prefetch_all(
            for_listing=for_listing,
        )
    )
    wrapper = AppointmentWrapper(subbookings)

    with CaptureQueriesContext(connection) as capture:
        checkout = wrapper.checkout
        assert capture.captured_queries == []

        total = checkout.total
        assert total is not None
        assert capture.captured_queries == []


@pytest.mark.django_db
def test_appointment_checkout_factory__combo():
    business = business_recipe.make()
    pos = baker.make('pos.POS', business=business)
    baker.make('pos.TaxRate', pos=pos, rate=Decimal(7), default_for_service=True)

    service_variant_child_1 = service_variant_recipe.make(
        service=service_recipe.make(business=business, name='Child 1', tax_rate=Decimal('23')),
    )
    service_variant_child_2 = service_variant_recipe.make(
        service=service_recipe.make(business=business, name='Child 2', tax_rate=None),
    )
    service_variant = service_variant_recipe.make(
        service=service_recipe.make(business=business, name='Combo', combo_type=ComboType.SEQUENCE),
        combo_pricing=ComboPricing.CUSTOM,
        type=None,
        price=None,
    )
    ComboMembership.objects.bulk_create(
        [
            ComboMembership(
                combo=service_variant,
                child=service_variant_child_1,
                order=1,
                type=PriceType.FIXED,
                price=Decimal('10'),
            ),
            ComboMembership(
                combo=service_variant,
                child=service_variant_child_2,
                order=2,
                type=PriceType.STARTS_AT,
                price=Decimal('15'),
            ),
        ]
    )

    appointment = create_appointment(
        [
            {
                'service_variant': service_variant,
                'combo_children': [
                    {'service_variant': service_variant_child_1},
                    {'service_variant': service_variant_child_2},
                ],
            },
        ],
        business=business,
    )
    checkout = AppointmentCheckoutFactory(
        subbookings=sorted(appointment.subbookings, key=attrgetter('id')),
        traveling=appointment.traveling,
        business=business,
        pos=pos,
        appointment_type=AppointmentType(appointment.type),
    ).create()

    assert len(checkout.components) == 2

    assert len(checkout.components[0].rows) == 1
    assert checkout.components[0].rows[0] == _Row(
        item_price=ServicePrice(Decimal('10'), PriceType.FIXED),
        for_discount=True,
        for_prepay=True,
        subbooking=appointment.subbookings[0].combo_children[0],
        service_variant=service_variant_child_1,
        service_variant_version=service_variant_child_1.version,
        service_variant_price=Decimal('10'),
        service_variant_type=PriceType.FIXED,
        service_variant_combo_parent=service_variant,
        type=TransactionRow.TRANSACTION_ROW_TYPE__SERVICE,
        tax_rate=Decimal('23'),
    )
    assert checkout.components[0].prepay_rate is None
    assert checkout.components[0].discount_rate is None

    assert len(checkout.components[1].rows) == 1
    assert checkout.components[1].rows[0] == _Row(
        item_price=ServicePrice(Decimal('15'), PriceType.STARTS_AT),
        for_discount=True,
        for_prepay=True,
        subbooking=appointment.subbookings[0].combo_children[1],
        service_variant=service_variant_child_2,
        service_variant_version=service_variant_child_2.version,
        service_variant_price=Decimal('15'),
        service_variant_type=PriceType.STARTS_AT,
        service_variant_combo_parent=service_variant,
        type=TransactionRow.TRANSACTION_ROW_TYPE__SERVICE,
        tax_rate=None,
    )
    assert checkout.components[1].prepay_rate is None
    assert checkout.components[1].discount_rate is None

    assert checkout.total == ServicePrice(
        value=Decimal('25'),
        price_type=PriceType.STARTS_AT,
        should_display_price_types=PriceType.should_display().union({PriceType.VARIES}),
    )


@pytest.mark.django_db
def test_appointment_checkout_factory__combo_with_prepayment():
    business = business_recipe.make()
    pos = baker.make('pos.POS', business=business)
    baker.make('pos.TaxRate', pos=pos, rate=Decimal(7), default_for_service=True)

    service_variant_child_1 = service_variant_recipe.make(
        service=service_recipe.make(business=business, tax_rate=None),
        price=Decimal('2000.00'),
        type=PriceType.FIXED,
        payment__payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
        payment__payment_amount=Decimal('100'),  # 5%
        payment__saving_type=ServiceVariantPayment.PERCENTAGE,
    )
    service_variant_child_2 = service_variant_recipe.make(
        service=service_recipe.make(business=business, tax_rate=None),
        price=Decimal('3000.00'),
        type=PriceType.FIXED,
        payment__payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
        payment__payment_amount=Decimal('55'),
        payment__saving_type=ServiceVariantPayment.AMOUNT,
    )
    service_variant = service_variant_recipe.make(
        service=service_recipe.make(business=business, combo_type=ComboType.SEQUENCE),
        combo_pricing=ComboPricing.CUSTOM,
    )
    ComboMembership.objects.bulk_create(
        [
            ComboMembership(
                combo=service_variant,
                child=service_variant_child_1,
                order=1,
                price=Decimal('200.00'),
                type=PriceType.FIXED,
            ),
            ComboMembership(
                combo=service_variant,
                child=service_variant_child_2,
                order=2,
                price=Decimal('300.00'),
                type=PriceType.FIXED,
            ),
        ]
    )

    appointment = create_appointment(
        [
            {
                'service_variant': service_variant,
                'combo_children': [
                    {'service_variant': service_variant_child_1},
                    {'service_variant': service_variant_child_2},
                ],
            },
        ],
        business=business,
        type=Appointment.TYPE.CUSTOMER,
    )
    checkout = AppointmentCheckoutFactory(
        subbookings=sorted(appointment.subbookings, key=attrgetter('id')),
        traveling=appointment.traveling,
        business=business,
        pos=pos,
        appointment_type=AppointmentType(appointment.type),
    ).create()

    assert len(checkout.components) == 2
    assert checkout.components[0].prepay_rate == PrepayRate(
        value=Decimal('5'),
        is_amount=False,
        is_cancellation_fee=False,
    )
    assert checkout.components[1].prepay_rate == PrepayRate(
        value=Decimal('55'),
        is_amount=True,
        is_cancellation_fee=False,
    )

    assert checkout.total == ServicePrice(
        value=Decimal('500.00'),
        price_type=PriceType.FIXED,
    )
    assert checkout.prepayment == PrepayPrice(
        value=Decimal('65'),  # 5% * $200 + $55
        excluded_tax=Decimal('0'),
    )


@pytest.mark.django_db
def test_appointment_checkout_factory__booksy_pay() -> None:
    business = business_recipe.make()
    pos = baker.make('pos.POS', business=business)
    baker.make('pos.TaxRate', pos=pos, rate=Decimal(7), default_for_service=True)
    service_variant = service_variant_recipe.make(
        service=service_recipe.make(business=business, tax_rate=None),
        price=Decimal('100.00'),
        type=PriceType.FIXED,
    )
    appointment = create_appointment(
        [
            {'service_variant': service_variant},
        ],
        business=business,
        type=Appointment.TYPE.CUSTOMER,
    )
    appointment_wrapper = AppointmentWrapper(list(appointment.subbookings))

    with (appointment_wrapper.booksy_pay_flow(),):
        checkout = AppointmentCheckoutFactory(
            subbookings=appointment_wrapper.subbookings,
            business=business,
            pos=pos,
            booksy_pay_flow_enabled=appointment_wrapper.booksy_pay_flow_enabled,
            is_paid_by_booksy_pay=appointment_wrapper.is_paid_by_booksy_pay,
            appointment_type=AppointmentType(appointment.type),
        ).create()

        # In Booksy Pay flow, PP attributes are expected to be None/False
        assert len(checkout.components) == 1
        assert checkout.components[0].prepay_rate is None
        assert checkout.components[0].rows[0].for_prepay is False
        assert checkout.total == ServicePrice(
            value=Decimal('100.00'),
            price_type=PriceType.FIXED,
        )

        assert checkout.components[0].rows[0].for_booksy_pay is True
        assert checkout.components[0].booksy_pay_rate == BooksyPayRate(value=Decimal(100))
        assert checkout.booksy_pay == BooksyPayPrice(
            value=Decimal('100'),
            excluded_tax=Decimal('0'),
        )


@pytest.mark.django_db
def test_appointment_checkout_factory__with_surcharge():
    business = business_recipe.make()
    pos = baker.make('pos.POS', business=business)
    baker.make('pos.TaxRate', pos=pos, rate=Decimal(7), default_for_service=True)
    service_variant = service_variant_recipe.make(
        service__business=business,
        service__name='Haircut',
        type=PriceType.FIXED,
        price=15,
    )
    peak_hour_service = PeakHourServiceFactory.get_service()
    peak_hour_service.enable(
        business.id,
        [
            PeakHour(
                business_id=business.id,
                day_of_week=DayOfWeek.SATURDAY,
                service_variants=[
                    Service(
                        elevation_rate=Decimal(20),
                        hour_from=time(10, 0),
                        hour_till=time(14, 0),
                        service_variant_id=service_variant.id,
                    ),
                ],
            ),
        ],
    )
    booked_from = datetime(2018, 1, 6, 12, 0, tzinfo=business.get_timezone())

    appointment = create_appointment(
        [
            {
                'booked_from': booked_from,
                'booked_till': booked_from + service_variant.duration,
                'service_variant': service_variant,
            },
        ],
        business=business,
    )
    checkout = AppointmentCheckoutFactory(
        subbookings=appointment.subbookings,
        traveling=appointment.traveling,
        business=business,
        pos=pos,
        appointment_type=AppointmentType(appointment.type),
    ).create()
    assert len(checkout.components) == 1
    assert len(checkout.components[0].rows) == 1
    assert checkout.components[0].rows[0] == _Row(
        item_price=ServicePrice(Decimal('18'), PriceType.FIXED),
        for_discount=False,
        for_prepay=True,
        subbooking=appointment.subbookings[0],
        service_variant=service_variant,
        service_variant_version=service_variant.version,
        service_variant_price=Decimal('15'),
        service_variant_type=PriceType.FIXED,
        service_variant_combo_parent=None,
        type=TransactionRow.TRANSACTION_ROW_TYPE__SERVICE,
        tax_rate=None,
    )
    assert checkout.components[0].prepay_rate is None
    assert checkout.components[0].discount_rate is None
    assert checkout.total == ServicePrice(
        value=Decimal('18'),
        price_type=PriceType.FIXED,
        should_display_price_types=PriceType.should_display().union({PriceType.VARIES}),
    )


@pytest.mark.django_db
def test_appointment_checkout_factory__combo_with_surcharge():
    business = business_recipe.make()
    pos = baker.make('pos.POS', business=business)
    baker.make('pos.TaxRate', pos=pos, rate=Decimal(7), default_for_service=True)

    service_variant_child_1 = service_variant_recipe.make(
        service=service_recipe.make(business=business, name='Child 1', tax_rate=Decimal('23')),
    )
    service_variant_child_2 = service_variant_recipe.make(
        service=service_recipe.make(business=business, name='Child 2', tax_rate=None),
    )
    service_variant = service_variant_recipe.make(
        service=service_recipe.make(business=business, name='Combo', combo_type=ComboType.SEQUENCE),
        combo_pricing=ComboPricing.CUSTOM,
        type=None,
        price=None,
    )
    ComboMembership.objects.bulk_create(
        [
            ComboMembership(
                combo=service_variant,
                child=service_variant_child_1,
                order=1,
                type=PriceType.FIXED,
                price=Decimal('10'),
            ),
            ComboMembership(
                combo=service_variant,
                child=service_variant_child_2,
                order=2,
                type=PriceType.STARTS_AT,
                price=Decimal('15'),
            ),
        ]
    )
    peak_hour_service = PeakHourServiceFactory.get_service()
    peak_hour_service.enable(
        business.id,
        [
            PeakHour(
                business_id=business.id,
                day_of_week=DayOfWeek.SATURDAY,
                service_variants=[
                    Service(
                        elevation_rate=Decimal(20),
                        hour_from=time(10, 0),
                        hour_till=time(14, 0),
                        service_variant_id=service_variant.id,
                    ),
                ],
            ),
        ],
    )
    booked_from = datetime(2018, 1, 6, 12, 0, tzinfo=business.get_timezone())

    appointment = create_appointment(
        [
            {
                'service_variant': service_variant,
                'combo_children': [
                    {
                        'booked_from': booked_from,
                        'booked_till': booked_from + service_variant_child_1.service_duration,
                        'service_variant': service_variant_child_1,
                    },
                    {
                        'service_variant': service_variant_child_2,
                    },
                ],
            },
        ],
        business=business,
    )
    checkout = AppointmentCheckoutFactory(
        subbookings=sorted(appointment.subbookings, key=attrgetter('id')),
        traveling=appointment.traveling,
        business=business,
        pos=pos,
        appointment_type=AppointmentType(appointment.type),
    ).create()

    assert len(checkout.components) == 2

    assert len(checkout.components[0].rows) == 1
    assert checkout.components[0].rows[0] == _Row(
        item_price=ServicePrice(Decimal('12'), PriceType.FIXED),
        for_discount=False,
        for_prepay=True,
        subbooking=appointment.subbookings[0].combo_children[0],
        service_variant=service_variant_child_1,
        service_variant_version=service_variant_child_1.version,
        service_variant_price=Decimal('10'),
        service_variant_type=PriceType.FIXED,
        service_variant_combo_parent=service_variant,
        type=TransactionRow.TRANSACTION_ROW_TYPE__SERVICE,
        tax_rate=Decimal('23'),
    )
    assert checkout.components[0].prepay_rate is None
    assert checkout.components[0].discount_rate is None

    assert len(checkout.components[1].rows) == 1
    assert checkout.components[1].rows[0] == _Row(
        item_price=ServicePrice(Decimal('18'), PriceType.STARTS_AT),
        for_discount=False,
        for_prepay=True,
        subbooking=appointment.subbookings[0].combo_children[1],
        service_variant=service_variant_child_2,
        service_variant_version=service_variant_child_2.version,
        service_variant_price=Decimal('15'),
        service_variant_type=PriceType.STARTS_AT,
        service_variant_combo_parent=service_variant,
        type=TransactionRow.TRANSACTION_ROW_TYPE__SERVICE,
        tax_rate=None,
    )
    assert checkout.components[1].prepay_rate is None
    assert checkout.components[1].discount_rate is None

    assert checkout.total == ServicePrice(
        value=Decimal('30'),
        price_type=PriceType.STARTS_AT,
        should_display_price_types=PriceType.should_display().union({PriceType.VARIES}),
    )
