from decimal import Decimal

from collections import Counter
from datetime import time, timed<PERSON><PERSON>
from itertools import product
from unittest.mock import patch, PropertyMock, Mock

import pytest
from django.conf import settings
from django.contrib.postgres.fields.ranges import DateRange
from model_bakery import baker

from lib.feature_flag.bug import NoRepeatingTextForGroupBookingFlag
from lib.feature_flag.feature.booking import DisableParallelSameComboDrawer
from lib.tests.utils import override_feature_flag
from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.booking.enums import (
    BookingRangesErrors,
    RepeatEndType,
    RepeatType,
    BookingAction,
)
from webapps.booking.exceptions import BookingConflict, MissingResourcesException
from webapps.booking.factory.service_questions import QuestionsList
from webapps.booking.models import Appointment, BookingSources, RepeatingBooking
from webapps.booking.serializers.appointment import (
    AppointmentSerializer,
    CustomerAppointmentSerializer,
)
from webapps.booking.tests.test_appointment_base import build_appointment_data
from webapps.booking.tests.utils import create_appointment, create_business_appointment_data
from webapps.business.baker_recipes import bci_recipe
from webapps.business.enums import CustomData, PriceType
from webapps.business.resources import AnyResource
from webapps.business.service_price import ServicePrice
from webapps.schedule.models import ResourceTimeOff, Schedule


# pylint: disable=too-many-arguments,redefined-outer-name,unused-argument
@pytest.fixture
def repeating_appt_wrapper(
    staffer,
    appliance,
    get_booked_time,
    service_variant,
    appointment_context,
):
    data = build_appointment_data(
        variant=service_variant,
        booked_from=get_booked_time(10),
        booked_till=get_booked_time(11),
        staffer=staffer,
        appliance=appliance,
    )
    data['new_repeating'] = {
        'repeat': RepeatType.EVERY_DAY,
        'end_type': RepeatEndType.AFTER_N_BOOKINGS,
        'repeat_number': 5,
    }
    data['dry_run'] = False

    serializer = AppointmentSerializer(
        instance=None,
        data=data,
        context=appointment_context,
    )
    serializer.is_valid(raise_exception=True)
    appointment = serializer.save()
    return appointment


def test_simple_repeating_appointment(repeating_appt_wrapper):
    repeating_booking = repeating_appt_wrapper.repeating
    assert repeating_booking
    assert Appointment.objects.filter(repeating=repeating_booking).count() == 5


@pytest.mark.parametrize('position, future', product(range(5), [True, False]))
def test_reschedule_appointment(  # pylint: disable=too-many-branches,too-many-positional-arguments
    repeating_appt_wrapper,
    appointment_context,
    staffer,
    make_staffer,
    position: int,
    future: bool,
):
    extra_staffer = make_staffer()
    existing_repeating = repeating_appt_wrapper.repeating
    existing_appointments = list(existing_repeating.extra_appointments)
    edited_appointment = existing_appointments[position]
    edited_wrapped_appt = AppointmentWrapper(edited_appointment.subbookings)

    data = AppointmentSerializer(
        instance=edited_wrapped_appt,
        context=appointment_context,
    ).data
    data['subbookings'][0]['staffer_id'] = extra_staffer.id
    data |= {
        'dry_run': False,
        'overbooking': False,
        '_notify_about_reschedule': False,
        '_update_future_bookings': future,
    }

    serializer = AppointmentSerializer(
        instance=edited_wrapped_appt,
        data=data,
        context=appointment_context,
    )
    assert serializer.is_valid(), serializer.errors
    saved_wrapped_appt = serializer.save()

    for appt in existing_appointments:
        appt.refresh_from_db()
        assert appt.deleted is None

    assert saved_wrapped_appt.appointment == edited_appointment

    # assert staffer changed in edited and following appointments
    assert edited_appointment.subbookings[0].staffer_id == extra_staffer.id
    if future and position < 4:
        assert len(edited_appointment.repeating.extra_appointments) == 5 - position
        for appt in edited_appointment.repeating.extra_appointments:
            assert appt.subbookings[0].staffer_id == extra_staffer.id

    # assert staffer changed in edited appointment only
    if not future:
        for appt in existing_appointments:
            if appt != edited_appointment:
                assert appt.subbookings[0].staffer_id == staffer.id

    # changed appointment splits repeating series into parts
    if not future:
        assert edited_appointment.repeating is None
        if position == 2:
            appt0, appt1 = existing_appointments[:2]
            assert appt0.repeating and appt0.repeating == appt1.repeating == existing_repeating
            appt3, appt4 = existing_appointments[3:]
            assert appt3.repeating and appt3.repeating == appt4.repeating != existing_repeating

        # single-element series don't need repeating
        if position == 1:
            assert existing_appointments[0].repeating is None
        if position == 3:
            assert existing_appointments[4].repeating is None

    # old repeating becomes repeating of the first part
    # the second part gets a new repeating
    if future:
        if position == 0:
            # is it paradoxical?
            assert existing_repeating.deleted
        if position > 1:
            assert existing_appointments[0].repeating == existing_repeating
        if new_repeating := saved_wrapped_appt.repeating:
            assert new_repeating != existing_repeating


@pytest.mark.parametrize('position, future', product(range(3), [True, False]))
def test_change_repeating(
    repeating_appt_wrapper,
    appointment_context,
    position: int,
    future: bool,
):
    existing_repeating = repeating_appt_wrapper.repeating
    existing_appointments = list(existing_repeating.extra_appointments)
    edited_appointment = existing_appointments[position]
    edited_wrapped_appt = AppointmentWrapper(edited_appointment.subbookings)

    data = AppointmentSerializer(
        instance=edited_wrapped_appt,
        context=appointment_context,
    ).data
    data |= {
        'dry_run': False,
        'overbooking': False,
        '_notify_about_reschedule': False,
        '_update_future_bookings': future,
        'new_repeating': {
            'repeat': RepeatType.EVERY_WEEK,
            'end_type': RepeatEndType.AFTER_N_BOOKINGS,
            'repeat_number': 5,
        },
    }

    serializer = AppointmentSerializer(
        instance=edited_wrapped_appt,
        data=data,
        context=appointment_context,
    )
    assert serializer.is_valid(), serializer.errors
    saved_wrapped_appt = serializer.save()
    new_repeating = saved_wrapped_appt.repeating

    for appt in existing_appointments:
        appt.refresh_from_db()

    assert new_repeating != existing_repeating
    assert len(new_repeating.extra_appointments) == 5
    assert (
        new_repeating.extra_appointments[0].booked_from + timedelta(days=7)
        == new_repeating.extra_appointments[1].booked_from
    )

    # appointments before the edited remain untouched
    if position == 2:
        for old_appt in existing_appointments[:position]:
            assert old_appt.deleted is None
            assert old_appt.repeating == existing_repeating

    # appointments after the edited are deleted; a new ones are created insteed
    for old_appt in existing_appointments[position + 1 :]:
        assert old_appt.deleted


def test_repeating_timeoffs_conflict(  # pylint: disable=too-many-positional-arguments
    business,
    staffer,
    appliance,
    booked_date,
    get_booked_time,
    service_variant,
    appointment_context,
):
    next_date = booked_date + timedelta(days=1)
    baker.make(
        ResourceTimeOff,
        business=business,
        resource=staffer,
        date_range=DateRange(next_date, next_date, bounds='[]'),
    )
    baker.make(
        Schedule,
        business=business,
        resource=staffer,
        date=booked_date,
        hours=[(time(12), time(19))],
    )

    data = build_appointment_data(
        variant=service_variant,
        booked_from=get_booked_time(10),
        booked_till=get_booked_time(11),
        staffer=staffer,
        appliance=appliance,
    )
    data['new_repeating'] = {
        'repeat': RepeatType.EVERY_DAY,
        'end_type': RepeatEndType.AFTER_N_BOOKINGS,
        'repeat_number': 3,
    }
    data['dry_run'] = False

    serializer = AppointmentSerializer(
        instance=None,
        data=data,
        context=appointment_context,
    )
    assert serializer.is_valid() is True, serializer.errors
    with pytest.raises(BookingConflict) as exc:
        serializer.save()

    assert exc.value.notices[f'{BookingRangesErrors.BOOKING_OVERBOOKING_ERROR}_dates'] == [
        booked_date.strftime(settings.DATE_FORMAT),
        next_date.strftime(settings.DATE_FORMAT),
    ]


def test_repeating_booking_conflict(  # pylint: disable=too-many-positional-arguments
    business,
    staffer,
    booked_date,
    get_booked_time,
    service_variant,
    appointment_context,
):
    next_date = booked_date + timedelta(days=1)
    create_appointment(
        [
            {
                'service_variant': service_variant,
                'booked_from': get_booked_time(10) + timedelta(days=1),
                'booked_till': get_booked_time(11) + timedelta(days=1),
                'staffer': staffer,
            }
        ],
        business=business,
    )

    data = create_business_appointment_data(
        [
            {
                'service_variant_id': service_variant.id,
                'booked_from': get_booked_time(10),
                'booked_till': get_booked_time(11),
                'staffer_id': staffer.id,
            }
        ],
        dry_run=False,
        new_repeating={
            'repeat': RepeatType.EVERY_DAY,
            'end_type': RepeatEndType.AFTER_N_BOOKINGS,
            'repeat_number': 3,
        },
    )
    serializer = AppointmentSerializer(
        instance=None,
        data=data,
        context=appointment_context,
    )
    assert serializer.is_valid() is True, serializer.errors
    with pytest.raises(BookingConflict) as exc:
        serializer.save()

    assert exc.value.notices[f'{BookingRangesErrors.BOOKING_OVERBOOKING_ERROR}_dates'] == [
        next_date.strftime(settings.DATE_FORMAT),
    ]


@override_feature_flag({NoRepeatingTextForGroupBookingFlag.flag_name: True})
def test_group_booking(
    make_staffer,
    make_appliance,
    get_booked_time,
    service_variant,
    appointment_context,
):
    staffer = make_staffer()
    service_variant.resources.add(staffer)
    # prepare already 3 appliances
    service_variant.service.resources.add(make_appliance())
    service_variant.service.resources.add(make_appliance())

    data = build_appointment_data(
        variant=service_variant,
        booked_from=get_booked_time(10),
        booked_till=get_booked_time(11),
        staffer=staffer,  # during dry_run response should contain AnyStaffer
    )
    data['new_repeating'] = {'repeat': RepeatType.GROUP, 'repeat_number': 3}
    data['dry_run'] = True

    # Test matcher
    # not enough staffers: should raise error?
    # TODO: howto show info 'there is not enough staffers' ?
    serializer = AppointmentSerializer(
        instance=None,
        data=data,
        context=appointment_context,
    )
    assert serializer.is_valid()
    serializer.save()
    assert serializer.data['subbookings'][0]['staffer_id'] == AnyResource.id
    assert serializer.data['subbookings'][0]['appliance_id'] == AnyResource.id
    assert serializer.data['subbookings'][0]['_availability']['staffers'][-1]['type'] == 'info'

    # enough staffers: should be valid
    data['new_repeating']['repeat_number'] = 2
    serializer = AppointmentSerializer(
        instance=None,
        data=data,
        context=appointment_context,
    )

    assert serializer.is_valid()

    # The same with the drawer
    data['new_repeating']['repeat_number'] = 3
    data['dry_run'] = False

    serializer = AppointmentSerializer(
        instance=None,
        data=data,
        context=appointment_context,
    )
    serializer.is_valid(raise_exception=True)

    # not enough staffers for 3 parallel bookings
    assert len(service_variant.active_staffers) == 2
    with pytest.raises(BookingConflict):
        serializer.save()

    # make the third staffer; it should be enough
    service_variant.resources.add(make_staffer())
    del service_variant.active_staffers

    assert len(service_variant.active_staffers) == 3
    serializer = AppointmentSerializer(
        instance=None,
        data=data,
        context=appointment_context,
    )
    serializer.is_valid(raise_exception=True)
    appointment = serializer.save()
    assert appointment

    appointments = appointment.repeating.extra_appointments
    assert len(appointments) == 3
    for appt1, appt2 in zip(appointments, appointments[1:]):
        assert appt1.subbookings[0].staffer != appt2.subbookings[0].staffer

    for appt1, appt2 in zip(appointments, appointments[1:]):
        assert appt1.subbookings[0].appliance != appt2.subbookings[0].appliance

    for appt in appointments:
        assert not appointment.repeating.get_repeat_text(appt.subbookings[0], appt.repeating)


# this feature does not work with disableed SameComboDraft!
@override_feature_flag({DisableParallelSameComboDrawer: False})
def test_group_booking_oversize(  # pylint: disable=too-many-positional-arguments
    business,
    staffer,
    make_staffer,
    make_appliance,
    get_booked_time,
    service_variant,
    appointment_context,
):
    """Force create group booking with more bookings than staffers"""

    # Let's have ordered staffers
    business.custom_data[CustomData.AUTOASSIGN_ORDERED_STAFFERS] = True
    business.save()

    # prepare 3 staffers/appliances
    service_variant.resources.add(make_staffer(order=1))
    service_variant.resources.add(make_staffer(order=2))
    service_variant.service.resources.add(make_appliance(order=1))
    service_variant.service.resources.add(make_appliance(order=2))

    data = build_appointment_data(
        variant=service_variant,
        booked_from=get_booked_time(10),
        booked_till=get_booked_time(11),
        staffer=AnyResource,
    )
    data['new_repeating'] = {'repeat': RepeatType.GROUP, 'repeat_number': 5}
    data['dry_run'] = False
    data['overbooking'] = True

    # Test drawer: overbooking oversized group booking
    serializer = AppointmentSerializer(
        instance=None,
        data=data,
        context=appointment_context,
    )
    assert serializer.is_valid(), serializer.errors
    appointment = serializer.save()
    appts = appointment.repeating.extra_appointments
    # 2 additional appts gets the first staffer
    counter = Counter(appt.subbookings[0].staffer_id for appt in appts)
    assert counter[staffer.id] == 3


def test_group_booking_no_service_variant(
    make_staffer,
    make_appliance,
    get_booked_time,
    service_variant,
    appointment_context,
):
    service_variant.resources.add(make_staffer())
    service_variant.resources.add(make_staffer())
    # prepare already 3 appliances
    service_variant.service.resources.add(make_appliance())
    service_variant.service.resources.add(make_appliance())

    data = build_appointment_data(
        variant='a',
        booked_from=get_booked_time(10),
        booked_till=get_booked_time(11),
        staffer=AnyResource,
    )
    data['new_repeating'] = {'repeat': RepeatType.GROUP, 'repeat_number': 3}
    data['overbooking'] = False
    data['dry_run'] = True

    serializer = AppointmentSerializer(
        instance=None,
        data=data,
        context=appointment_context,
    )

    assert serializer.is_valid(), serializer.errors
    appointment = serializer.save()
    assert appointment

    data['dry_run'] = False
    serializer = AppointmentSerializer(
        instance=None,
        data=data,
        context=appointment_context,
    )

    assert serializer.is_valid(), serializer.errors
    appointment = serializer.save()
    assert appointment


@patch(
    'webapps.business.models.Business.turntracker_enabled',
    new_callable=PropertyMock,
    return_value=True,
)
def test_group_booking_tt_dry_run(  # pylint: disable=too-many-positional-arguments
    business,
    make_staffer,
    make_appliance,
    get_booked_time,
    service_variant,
    appointment_context,
):
    service_variant.resources.add(make_staffer())
    service_variant.service.resources.add(make_appliance())

    data = build_appointment_data(
        variant=service_variant,
        booked_from=get_booked_time(10),
        booked_till=get_booked_time(11),
        staffer=AnyResource,
    )
    data['new_repeating'] = {'repeat': RepeatType.GROUP, 'repeat_number': 2}
    data['dry_run'] = True

    serializer = AppointmentSerializer(
        instance=None,
        data=data,
        context=appointment_context,
    )

    assert serializer.is_valid() is True, serializer.errors
    serializer.save()

    assert serializer.data['subbookings'][0]['autoassigned_staffer_id'] is None
    assert serializer.data['subbookings'][0]['staffer_id'] == AnyResource.id
    assert serializer.data['subbookings'][0]['autoassign'] is True


def test_customer_cant_edit_group_booking(
    business,
    service_variant,
    get_booked_time,
    appointment_context,
):
    bci = bci_recipe.make(business=business)
    data = build_appointment_data(
        variant=service_variant,
        booked_from=get_booked_time(10),
        booked_till=get_booked_time(11),
        staffer=AnyResource,
    )
    data.update(
        {
            'booked_for': bci,
            'dry_run': False,
            'overbooking': True,
            'new_repeating': {
                'repeat': RepeatType.GROUP,
                'repeat_number': 2,
            },
        }
    )

    serializer = AppointmentSerializer(
        instance=None,
        data=data,
        context=appointment_context,
    )
    serializer.is_valid(raise_exception=True)
    appointment_wrapper = serializer.save()

    serializer_context = {
        'booking_source': Mock(app_type=BookingSources.CUSTOMER_APP),
        'user': bci.user,
        'business': business,
    }
    serializer = CustomerAppointmentSerializer(
        instance=appointment_wrapper,
        context=serializer_context,
    )

    assert appointment_wrapper.appointment.can_customer_change() is False
    assert serializer.data['actions'][BookingAction.CHANGE] is False


def test_group_booking_business_has_appliance_staffer(
    get_booked_time,
    service_variant,
    appointment_context,
):
    assert service_variant.service.business.resources.all().count() == 2  # appliance + staffer

    service = service_variant.service
    service.questions = QuestionsList(['Question 1', 'Question 2'])
    service.save()

    data = build_appointment_data(
        variant=service_variant,
        booked_from=get_booked_time(10),
        booked_till=get_booked_time(11),
        staffer=AnyResource,
    )
    data['new_repeating'] = {'repeat': RepeatType.GROUP, 'repeat_number': 2}
    data['dry_run'] = True
    data['subbookings'][0]['is_highlighted'] = True

    serializer = AppointmentSerializer(
        instance=None,
        data=data,
        context=appointment_context,
    )
    assert serializer.is_valid()
    serializer.save()
    assert serializer.data['subbookings'][0]['staffer_id'] == service_variant.staffer_ids[0]
    assert serializer.data['subbookings'][0]['appliance_id'] == service_variant.appliance_ids[0]
    assert serializer.data['new_repeating']['total'] == str(
        ServicePrice(Decimal('30.00'), PriceType.FIXED)
    )

    data.update(
        {
            'dry_run': False,
            'overbooking': True,
        },
    )
    serializer = AppointmentSerializer(
        instance=None,
        data=data,
        context=appointment_context,
    )
    assert serializer.is_valid()
    serializer.save()

    assert serializer.data['repeating']['total'] == str(
        ServicePrice(Decimal('30.00'), PriceType.FIXED)
    )

    res = (
        RepeatingBooking.objects.get(id=serializer.data['repeating']['id'])
        .extra_appointments_unlimited.prefetch_related('bookings')
        .values('bookings__is_highlighted', 'service_questions')
    )

    assert all(i['bookings__is_highlighted'] for i in res)

    expected_result = {'Question 1': '', 'Question 2': ''}
    assert all(dict(i['service_questions'].items()) == expected_result for i in res)


def test_repeating_total_value(service_variant, get_booked_time, appointment_context):
    data = build_appointment_data(
        variant=service_variant,
        booked_from=get_booked_time(12),
        booked_till=get_booked_time(13),
    )
    data['new_repeating'] = {
        'repeat': RepeatType.EVERY_DAY,
        'end_type': RepeatEndType.AFTER_N_BOOKINGS,
        'repeat_number': 7,
    }
    data['dry_run'] = False

    serializer = AppointmentSerializer(
        instance=None,
        data=data,
        context=appointment_context,
    )
    serializer.is_valid(raise_exception=True)
    repeated_appointment = serializer.save()

    appointments = Appointment.objects.filter(repeating=repeated_appointment.repeating)
    for appointment in appointments:
        assert appointment.total_value == repeated_appointment.total.value
        assert (
            appointment.subbookings[0].service_data_internal.service_variant_price
            == repeated_appointment.total.value
        )


def test_reschedule_conflict(  # pylint: disable=too-many-branches,too-many-positional-arguments
    repeating_appt_wrapper,
    appointment_context,
    staffer,
    make_staffer,
    get_booked_time,
    business,
    service_variant,
):
    create_appointment(
        [
            {
                'staffer': staffer,
                'booked_from': get_booked_time(12),
                'booked_till': get_booked_time(13),
            }
        ],
        business=business,
    )

    existing_repeating = repeating_appt_wrapper.repeating
    existing_appointments = list(existing_repeating.extra_appointments)
    edited_appointment = existing_appointments[0]
    edited_wrapped_appt = AppointmentWrapper(edited_appointment.subbookings)

    data = AppointmentSerializer(
        instance=edited_wrapped_appt,
        context=appointment_context,
    ).data
    data['subbookings'][0]['booked_from'] = get_booked_time(12)
    data['subbookings'][0]['booked_till'] = get_booked_time(13)

    data |= {
        'dry_run': False,
        'overbooking': False,
        '_notify_about_reschedule': False,
        '_update_future_bookings': True,
    }

    serializer = AppointmentSerializer(
        instance=edited_wrapped_appt,
        data=data,
        context=appointment_context,
    )
    assert serializer.is_valid(), serializer.errors
    with pytest.raises(MissingResourcesException):
        serializer.save()
