from dateutil.relativedelta import relativedelta
from django.test import TestCase
from model_bakery import baker

from lib.tools import tznow
from webapps.booking.book_again_v2.data_retriever import BookAgainInfoDataRetriever
from webapps.booking.tests.utils import create_appointment
from webapps.business.baker_recipes import (
    business_recipe,
    bci_recipe,
    service_recipe,
    service_variant_recipe,
    staffer_recipe,
    service_addon_recipe,
)
from webapps.business.enums import ComboType
from webapps.business.models import ServiceAddOnUse, ComboMembership
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.family_and_friends.baker_recipes import (
    member_recipe,
    mbci_recipe,
    member_appointment_recipe,
    relation_recipe,
)
from webapps.user.baker_recipes import user_recipe
from webapps.user.models import UserProfile


class TestBookAgainDataReceiver(TestCase):
    def setUp(self):
        super().setUp()
        self.business = business_recipe.make()
        self.user = user_recipe.make()
        self.bci = bci_recipe.make(business=self.business, user=self.user)

    def generate_member_profile_with_bci(self):
        baker.make(UserProfile, user=self.user, profile_type=UserProfile.Type.CUSTOMER)
        member = member_recipe.make(user_profile=self.user.customer_profile)
        mbci_recipe.make(member=member, bci=self.bci)
        return member, self.bci

    def generate_family_and_friends_appointment(
        self, booked_for: BusinessCustomerInfo, booked_by: BusinessCustomerInfo
    ):
        service = service_recipe.make()
        service_variant = service_variant_recipe.make(service=service)
        appointment = create_appointment(
            [{'service_variant': service_variant}], business=self.business, booked_for=booked_for
        )
        member_appointment_recipe.make(
            appointment=appointment, booked_by=booked_by, booked_for=booked_for
        )
        return appointment

    def _prepare_combo_appointment_data(self):
        staffer = staffer_recipe.make(business=self.business)
        staffer2 = staffer_recipe.make(business=self.business)
        treatments = [baker.make('BusinessCategory') for _ in range(3)]
        services = [
            service_recipe.make(
                business=self.business, treatment=treatments[i], resources=[staffer, staffer2]
            )
            for i in range(3)
        ]
        combo_children = [
            service_variant_recipe.make(
                service=services[i],
                duration=relativedelta(minutes=10 * i),
            )
            for i in range(3)
        ]
        combo = service_variant_recipe.make(
            service=service_recipe.make(business=self.business, combo_type=ComboType.SEQUENCE)
        )
        for i, child in enumerate(combo_children):
            baker.make(ComboMembership, combo=combo, child=child, order=i, gap_time=relativedelta())
            child.service.add_staffers([staffer, staffer2])
        return combo, combo_children, staffer, staffer2, services

    def test_appointment_with_service_not_active(self):
        service = service_recipe.make(active=False)
        service_variant = service_variant_recipe.make(service=service)
        appointment = create_appointment(
            [{'service_variant': service_variant}],
            business=self.business,
            booked_for=self.bci,
        )
        retriever = BookAgainInfoDataRetriever(
            business_id=self.business.id, appointment_id=appointment.id, user_id=self.user.id
        )
        self.assertListEqual(
            retriever.get_subbookings_data(),
            [
                {
                    'id': appointment.subbookings[0].id,
                    'treatment_id': None,
                    'treatment_slug': None,
                    'treatment_internal_name': None,
                    'service_variant_id': service_variant.id,
                    'service_variant_active': True,
                    'service_active': False,
                    'service_is_available_for_customer_booking': True,
                    'combo_parent_id': None,
                    'staffer_id': None,
                    'appliance_id': None,
                    'is_staffer_still_active': False,
                }
            ],
        )

    def test_appointment_with_removed_staffer(self):
        staffer = staffer_recipe.make(business=self.business)
        service = service_recipe.make()
        service_variant = service_variant_recipe.make(service=service)
        appointment = create_appointment(
            [
                {
                    'service_variant': service_variant,
                    'staffer': staffer,
                },
            ],
            business=self.business,
            booked_for=self.bci,
        )
        retriever = BookAgainInfoDataRetriever(
            business_id=self.business.id, appointment_id=appointment.id, user_id=self.user.id
        )
        self.assertListEqual(
            retriever.get_subbookings_data(),
            [
                {
                    'id': appointment.subbookings[0].id,
                    'treatment_id': None,
                    'treatment_slug': None,
                    'treatment_internal_name': None,
                    'service_variant_id': service_variant.id,
                    'service_variant_active': True,
                    'service_active': True,
                    'service_is_available_for_customer_booking': True,
                    'combo_parent_id': None,
                    'staffer_id': staffer.id,
                    'appliance_id': None,
                    'is_staffer_still_active': False,
                }
            ],
        )

    def test_appointment_without_services(self):
        appointment = create_appointment(business=self.business, booked_for=self.bci)
        retriever = BookAgainInfoDataRetriever(
            business_id=self.business.id, appointment_id=appointment.id, user_id=self.user.id
        )
        self.assertListEqual(
            retriever.get_subbookings_data(),
            [
                {
                    'id': appointment.subbookings[0].id,
                    'treatment_id': None,
                    'treatment_slug': None,
                    'treatment_internal_name': None,
                    'service_variant_id': None,
                    'service_variant_active': None,
                    'service_active': None,
                    'service_is_available_for_customer_booking': None,
                    'combo_parent_id': None,
                    'staffer_id': None,
                    'appliance_id': None,
                    'is_staffer_still_active': False,
                }
            ],
        )

    def test_appointment_with_removed_addon(self):
        service = service_recipe.make()
        service_variant = service_variant_recipe.make(service=service)
        addon = service_addon_recipe.make(
            business=self.business, services=[service], deleted=tznow()
        )
        appointment = create_appointment(
            [
                {
                    'service_variant': service_variant,
                },
            ],
            business=self.business,
            booked_for=self.bci,
        )
        baker.make(
            ServiceAddOnUse,
            business=self.business,
            service_addon=addon,
            quantity=2,
            subbooking=appointment.bookings.first(),
            services_ids=[service.id],
        )
        retriever = BookAgainInfoDataRetriever(
            business_id=self.business.id, appointment_id=appointment.id, user_id=self.user.id
        )
        self.assertListEqual(
            retriever.get_subbookings_data(),
            [
                {
                    'id': appointment.subbookings[0].id,
                    'treatment_id': None,
                    'treatment_slug': None,
                    'treatment_internal_name': None,
                    'service_variant_id': service_variant.id,
                    'service_variant_active': True,
                    'service_active': True,
                    'service_is_available_for_customer_booking': True,
                    'combo_parent_id': None,
                    'staffer_id': None,
                    'appliance_id': None,
                    'is_staffer_still_active': False,
                }
            ],
        )
        self.assertListEqual(
            retriever.get_addons_data([appointment.subbookings[0].id]),
            [
                {
                    'subbooking': appointment.subbookings[0].id,
                    'service_addon': addon.id,
                    'quantity': 2,
                    'addon_deleted': addon.deleted,
                    'max_allowed_quantity': None,
                    'is_available_for_customer_booking': True,
                    'still_in_same_service': False,
                }
            ],
        )

    def test_deleted_business(self):
        self.business.visible = False
        self.business.save()
        self.business.soft_delete()
        appointment = create_appointment(business=self.business, booked_for=self.bci)
        retriever = BookAgainInfoDataRetriever(
            business_id=self.business.id, appointment_id=appointment.id, user_id=self.user.id
        )
        self.assertDictEqual(
            retriever.get_business_data(),
            {
                'id': self.business.id,
                'visible': False,
                'deleted': self.business.deleted,
                'active': True,
                'region_id': self.business.region.id,
                'region_name': self.business.region.name,
                'region_latitude': self.business.region.latitude,
                'region_longitude': self.business.region.longitude,
                'primary_category_id': self.business.primary_category.id,
                'primary_category_slug': self.business.primary_category.slug,
                'is_blacklisted_user': False,
            },
        )

    def test_blacklisted_user(self):
        appointment = create_appointment(business=self.business, booked_for=self.bci)
        appointment.booked_for.blacklisted = True
        appointment.booked_for.save()
        retriever = BookAgainInfoDataRetriever(
            business_id=self.business.id, appointment_id=appointment.id, user_id=self.user.id
        )
        self.assertDictEqual(
            retriever.get_business_data(),
            {
                'id': self.business.id,
                'visible': True,
                'deleted': self.business.deleted,
                'active': True,
                'region_id': self.business.region.id,
                'region_name': self.business.region.name,
                'region_latitude': self.business.region.latitude,
                'region_longitude': self.business.region.longitude,
                'primary_category_id': self.business.primary_category.id,
                'primary_category_slug': self.business.primary_category.slug,
                'is_blacklisted_user': True,
            },
        )

    def test_appointment_with_service_not_available_for_booking(self):
        service = service_recipe.make(is_available_for_customer_booking=False)
        service_variant = service_variant_recipe.make(service=service)
        appointment = create_appointment(
            [{'service_variant': service_variant}],
            business=self.business,
            booked_for=self.bci,
        )
        retriever = BookAgainInfoDataRetriever(
            business_id=self.business.id, appointment_id=appointment.id, user_id=self.user.id
        )
        self.assertListEqual(
            retriever.get_subbookings_data(),
            [
                {
                    'id': appointment.subbookings[0].id,
                    'treatment_id': None,
                    'treatment_slug': None,
                    'treatment_internal_name': None,
                    'service_variant_id': service_variant.id,
                    'service_variant_active': True,
                    'service_active': True,
                    'service_is_available_for_customer_booking': False,
                    'combo_parent_id': None,
                    'staffer_id': None,
                    'appliance_id': None,
                    'is_staffer_still_active': False,
                }
            ],
        )

    def test_appointment_with_service_variant_not_active(self):
        service = service_recipe.make()
        service_variant = service_variant_recipe.make(service=service, active=False)
        appointment = create_appointment(
            [{'service_variant': service_variant}],
            business=self.business,
            booked_for=self.bci,
        )
        retriever = BookAgainInfoDataRetriever(
            business_id=self.business.id, appointment_id=appointment.id, user_id=self.user.id
        )
        self.assertListEqual(
            retriever.get_subbookings_data(),
            [
                {
                    'id': appointment.subbookings[0].id,
                    'treatment_id': None,
                    'treatment_slug': None,
                    'treatment_internal_name': None,
                    'service_variant_id': service_variant.id,
                    'service_variant_active': False,
                    'service_active': True,
                    'service_is_available_for_customer_booking': True,
                    'combo_parent_id': None,
                    'staffer_id': None,
                    'appliance_id': None,
                    'is_staffer_still_active': False,
                }
            ],
        )

    def test_appointment_with_one_service_not_available_for_booking(self):
        service = service_recipe.make(is_available_for_customer_booking=False)
        service2 = service_recipe.make()
        service_variant = service_variant_recipe.make(service=service)
        service_variant2 = service_variant_recipe.make(service=service2)
        appointment = create_appointment(
            [{'service_variant': service_variant}, {'service_variant': service_variant2}],
            business=self.business,
            booked_for=self.bci,
        )
        retriever = BookAgainInfoDataRetriever(
            business_id=self.business.id, appointment_id=appointment.id, user_id=self.user.id
        )
        self.assertListEqual(
            retriever.get_subbookings_data(),
            [
                {
                    'id': appointment.subbookings[0].id,
                    'treatment_id': None,
                    'treatment_slug': None,
                    'treatment_internal_name': None,
                    'service_variant_id': service_variant.id,
                    'service_variant_active': True,
                    'service_active': True,
                    'service_is_available_for_customer_booking': False,
                    'combo_parent_id': None,
                    'staffer_id': None,
                    'appliance_id': None,
                    'is_staffer_still_active': False,
                },
                {
                    'id': appointment.subbookings[1].id,
                    'treatment_id': None,
                    'treatment_slug': None,
                    'treatment_internal_name': None,
                    'service_variant_id': service_variant2.id,
                    'service_variant_active': True,
                    'service_active': True,
                    'service_is_available_for_customer_booking': True,
                    'combo_parent_id': None,
                    'staffer_id': None,
                    'appliance_id': None,
                    'is_staffer_still_active': False,
                },
            ],
        )

    def test_appointment_with_one_service_not_active(self):
        service = service_recipe.make(active=False)
        service2 = service_recipe.make()
        service_variant = service_variant_recipe.make(service=service)
        service_variant2 = service_variant_recipe.make(service=service2)
        appointment = create_appointment(
            [{'service_variant': service_variant}, {'service_variant': service_variant2}],
            business=self.business,
            booked_for=self.bci,
        )
        retriever = BookAgainInfoDataRetriever(
            business_id=self.business.id, appointment_id=appointment.id, user_id=self.user.id
        )
        self.assertListEqual(
            retriever.get_subbookings_data(),
            [
                {
                    'id': appointment.subbookings[0].id,
                    'treatment_id': None,
                    'treatment_slug': None,
                    'treatment_internal_name': None,
                    'service_variant_id': service_variant.id,
                    'service_variant_active': True,
                    'service_active': False,
                    'service_is_available_for_customer_booking': True,
                    'combo_parent_id': None,
                    'staffer_id': None,
                    'appliance_id': None,
                    'is_staffer_still_active': False,
                },
                {
                    'id': appointment.subbookings[1].id,
                    'treatment_id': None,
                    'treatment_slug': None,
                    'treatment_internal_name': None,
                    'service_variant_id': service_variant2.id,
                    'service_variant_active': True,
                    'service_active': True,
                    'service_is_available_for_customer_booking': True,
                    'combo_parent_id': None,
                    'staffer_id': None,
                    'appliance_id': None,
                    'is_staffer_still_active': False,
                },
            ],
        )

    def test_appointment_with_one_service_variant_not_active(self):
        service = service_recipe.make()
        service2 = service_recipe.make()
        service_variant = service_variant_recipe.make(service=service, active=False)
        service_variant2 = service_variant_recipe.make(service=service2)
        appointment = create_appointment(
            [{'service_variant': service_variant}, {'service_variant': service_variant2}],
            business=self.business,
            booked_for=self.bci,
        )
        retriever = BookAgainInfoDataRetriever(
            business_id=self.business.id, appointment_id=appointment.id, user_id=self.user.id
        )

        self.assertListEqual(
            retriever.get_subbookings_data(),
            [
                {
                    'id': appointment.subbookings[0].id,
                    'treatment_id': None,
                    'treatment_slug': None,
                    'treatment_internal_name': None,
                    'service_variant_id': service_variant.id,
                    'service_variant_active': False,
                    'service_active': True,
                    'service_is_available_for_customer_booking': True,
                    'combo_parent_id': None,
                    'staffer_id': None,
                    'appliance_id': None,
                    'is_staffer_still_active': False,
                },
                {
                    'id': appointment.subbookings[1].id,
                    'treatment_id': None,
                    'treatment_slug': None,
                    'treatment_internal_name': None,
                    'service_variant_id': service_variant2.id,
                    'service_variant_active': True,
                    'service_active': True,
                    'service_is_available_for_customer_booking': True,
                    'combo_parent_id': None,
                    'staffer_id': None,
                    'appliance_id': None,
                    'is_staffer_still_active': False,
                },
            ],
        )

    def test_family_and_friends_appointment(self):
        member, bci = self.generate_member_profile_with_bci()
        friend = relation_recipe.make(parent=member).member
        friend_bci = bci_recipe.make(business=self.business)
        mbci_recipe.make(member=friend, bci=friend_bci)
        appointment = self.generate_family_and_friends_appointment(
            booked_for=friend_bci, booked_by=bci
        )
        retriever = BookAgainInfoDataRetriever(
            business_id=self.business.id, appointment_id=appointment.id, user_id=self.user.id
        )
        self.assertDictEqual(
            retriever.get_appointment_data(),
            {
                'id': appointment.id,
                'booked_for': friend_bci.id,
                'booked_for_user': friend_bci.user.id,
                'booked_by': bci.id,
                'booked_by_user': self.user.id,
                'member_exists': True,
                'member_id': friend.id,
            },
        )

    def test_family_and_friends_appointment_booked_for_me(self):
        member, bci = self.generate_member_profile_with_bci()
        parent = relation_recipe.make(member=member).parent
        parent_bci = bci_recipe.make(business=self.business)
        mbci_recipe.make(member=parent, bci=parent_bci)
        appointment = self.generate_family_and_friends_appointment(
            booked_for=bci, booked_by=parent_bci
        )
        retriever = BookAgainInfoDataRetriever(
            business_id=self.business.id, appointment_id=appointment.id, user_id=self.user.id
        )
        self.assertDictEqual(
            retriever.get_appointment_data(),
            {
                'id': appointment.id,
                'booked_for': bci.id,
                'booked_for_user': bci.user.id,
                'booked_by': parent_bci.id,
                'booked_by_user': parent_bci.user.id,
                'member_exists': False,
                'member_id': member.id,
            },
        )

    def test_family_and_friends_appointment_member_not_longer_in_family(self):
        _, bci = self.generate_member_profile_with_bci()
        old_friend_bci = bci_recipe.make(business=self.business)
        mbci = mbci_recipe.make(bci=old_friend_bci)
        appointment = self.generate_family_and_friends_appointment(
            booked_for=old_friend_bci, booked_by=bci
        )
        retriever = BookAgainInfoDataRetriever(
            business_id=self.business.id, appointment_id=appointment.id, user_id=self.user.id
        )
        self.assertDictEqual(
            retriever.get_appointment_data(),
            {
                'id': appointment.id,
                'booked_for': old_friend_bci.id,
                'booked_for_user': old_friend_bci.user.id,
                'booked_by': bci.id,
                'booked_by_user': self.user.id,
                'member_exists': False,
                'member_id': mbci.member.id,
            },
        )

    def test_existing_combo_appointment(self):
        combo, combo_children, staffer, staffer2, _ = self._prepare_combo_appointment_data()
        appointment = create_appointment(
            [
                {
                    'service_variant': combo,
                    'combo_children': [
                        {
                            'service_variant': combo_children[0],
                            'staffer': staffer,
                        },
                        {
                            'service_variant': combo_children[1],
                            'staffer': staffer2,
                        },
                        {
                            'service_variant': combo_children[2],
                            'staffer': staffer,
                        },
                    ],
                }
            ],
            booked_for=self.bci,
            business=self.business,
        )
        retriever = BookAgainInfoDataRetriever(
            business_id=self.business.id, appointment_id=appointment.id, user_id=self.user.id
        )
        self.assertListEqual(
            retriever.get_combo_membership_data([combo.id]),
            [{'combo': combo.id, 'child': child.id} for child in combo_children],
        )
        self.assertListEqual(
            retriever.get_subbookings_data(),
            [
                {
                    'id': appointment.subbookings[0].id,
                    'treatment_id': None,
                    'treatment_slug': None,
                    'treatment_internal_name': None,
                    'service_variant_id': combo.id,
                    'service_variant_active': True,
                    'service_active': True,
                    'service_is_available_for_customer_booking': True,
                    'combo_parent_id': None,
                    'staffer_id': None,
                    'appliance_id': None,
                    'is_staffer_still_active': False,
                },
                {
                    'id': appointment.subbookings[0].combo_children[0].id,
                    'treatment_id': combo_children[0].service.treatment.id,
                    'treatment_slug': combo_children[0].service.treatment.slug,
                    'treatment_internal_name': combo_children[0].service.treatment.internal_name,
                    'service_variant_id': combo_children[0].id,
                    'service_variant_active': True,
                    'service_active': True,
                    'service_is_available_for_customer_booking': True,
                    'combo_parent_id': appointment.subbookings[0].combo_children[0].combo_parent_id,
                    'staffer_id': staffer.id,
                    'appliance_id': None,
                    'is_staffer_still_active': True,
                },
                {
                    'id': appointment.subbookings[0].combo_children[1].id,
                    'treatment_id': combo_children[1].service.treatment.id,
                    'treatment_slug': combo_children[1].service.treatment.slug,
                    'treatment_internal_name': combo_children[1].service.treatment.internal_name,
                    'service_variant_id': combo_children[1].id,
                    'service_variant_active': True,
                    'service_active': True,
                    'service_is_available_for_customer_booking': True,
                    'combo_parent_id': appointment.subbookings[0].combo_children[1].combo_parent_id,
                    'staffer_id': staffer2.id,
                    'appliance_id': None,
                    'is_staffer_still_active': True,
                },
                {
                    'id': appointment.subbookings[0].combo_children[2].id,
                    'treatment_id': combo_children[2].service.treatment.id,
                    'treatment_slug': combo_children[2].service.treatment.slug,
                    'treatment_internal_name': combo_children[2].service.treatment.internal_name,
                    'service_variant_id': combo_children[2].id,
                    'service_variant_active': True,
                    'service_active': True,
                    'service_is_available_for_customer_booking': True,
                    'combo_parent_id': appointment.subbookings[0].combo_children[2].combo_parent_id,
                    'staffer_id': staffer.id,
                    'appliance_id': None,
                    'is_staffer_still_active': True,
                },
            ],
        )

    def test_existing_combo_appointment_with_missing_service_variant(self):
        combo, combo_children, staffer, staffer2, _ = self._prepare_combo_appointment_data()
        combo_children[1].active = False
        combo_children[1].save()
        appointment = create_appointment(
            [
                {
                    'service_variant': combo,
                    'combo_children': [
                        {
                            'service_variant': combo_children[0],
                            'staffer': staffer,
                        },
                        {
                            'service_variant': combo_children[1],
                            'staffer': staffer2,
                        },
                        {
                            'service_variant': combo_children[2],
                            'staffer': staffer,
                        },
                    ],
                }
            ],
            booked_for=self.bci,
            business=self.business,
        )
        retriever = BookAgainInfoDataRetriever(
            business_id=self.business.id, appointment_id=appointment.id, user_id=self.user.id
        )
        self.assertListEqual(
            retriever.get_combo_membership_data([combo.id]),
            [
                {'combo': combo.id, 'child': combo_children[0].id},
                {'combo': combo.id, 'child': combo_children[2].id},
            ],
        )
        self.assertListEqual(
            retriever.get_subbookings_data(),
            [
                {
                    'id': appointment.subbookings[0].id,
                    'treatment_id': None,
                    'treatment_slug': None,
                    'treatment_internal_name': None,
                    'service_variant_id': combo.id,
                    'service_variant_active': True,
                    'service_active': True,
                    'service_is_available_for_customer_booking': True,
                    'combo_parent_id': None,
                    'staffer_id': None,
                    'appliance_id': None,
                    'is_staffer_still_active': False,
                },
                {
                    'id': appointment.subbookings[0].combo_children[0].id,
                    'treatment_id': combo_children[0].service.treatment.id,
                    'treatment_slug': combo_children[0].service.treatment.slug,
                    'treatment_internal_name': combo_children[0].service.treatment.internal_name,
                    'service_variant_id': combo_children[0].id,
                    'service_variant_active': True,
                    'service_active': True,
                    'service_is_available_for_customer_booking': True,
                    'combo_parent_id': appointment.subbookings[0].combo_children[0].combo_parent_id,
                    'staffer_id': staffer.id,
                    'appliance_id': None,
                    'is_staffer_still_active': True,
                },
                {
                    'id': appointment.subbookings[0].combo_children[1].id,
                    'treatment_id': combo_children[1].service.treatment.id,
                    'treatment_slug': combo_children[1].service.treatment.slug,
                    'treatment_internal_name': combo_children[1].service.treatment.internal_name,
                    'service_variant_id': combo_children[1].id,
                    'service_variant_active': False,
                    'service_active': True,
                    'service_is_available_for_customer_booking': True,
                    'combo_parent_id': appointment.subbookings[0].combo_children[1].combo_parent_id,
                    'staffer_id': staffer2.id,
                    'appliance_id': None,
                    'is_staffer_still_active': True,
                },
                {
                    'id': appointment.subbookings[0].combo_children[2].id,
                    'treatment_id': combo_children[2].service.treatment.id,
                    'treatment_slug': combo_children[2].service.treatment.slug,
                    'treatment_internal_name': combo_children[2].service.treatment.internal_name,
                    'service_variant_id': combo_children[2].id,
                    'service_variant_active': True,
                    'service_active': True,
                    'service_is_available_for_customer_booking': True,
                    'combo_parent_id': appointment.subbookings[0].combo_children[2].combo_parent_id,
                    'staffer_id': staffer.id,
                    'appliance_id': None,
                    'is_staffer_still_active': True,
                },
            ],
        )

    def test_existing_combo_appointment_with_one_child_service_not_available_for_booking(self):
        combo, combo_children, staffer, staffer2, services = self._prepare_combo_appointment_data()
        services[1].is_available_for_customer_booking = False
        services[1].save()

        appointment = create_appointment(
            [
                {
                    'service_variant': combo,
                    'combo_children': [
                        {
                            'service_variant': combo_children[0],
                            'staffer': staffer,
                        },
                        {
                            'service_variant': combo_children[1],
                            'staffer': staffer2,
                        },
                        {
                            'service_variant': combo_children[2],
                            'staffer': staffer,
                        },
                    ],
                }
            ],
            booked_for=self.bci,
            business=self.business,
        )
        retriever = BookAgainInfoDataRetriever(
            business_id=self.business.id, appointment_id=appointment.id, user_id=self.user.id
        )
        self.assertListEqual(
            retriever.get_combo_membership_data([combo.id]),
            [{'combo': combo.id, 'child': child.id} for child in combo_children],
        )
        self.assertListEqual(
            retriever.get_subbookings_data(),
            [
                {
                    'id': appointment.subbookings[0].id,
                    'treatment_id': None,
                    'treatment_slug': None,
                    'treatment_internal_name': None,
                    'service_variant_id': combo.id,
                    'service_variant_active': True,
                    'service_active': True,
                    'service_is_available_for_customer_booking': True,
                    'combo_parent_id': None,
                    'staffer_id': None,
                    'appliance_id': None,
                    'is_staffer_still_active': False,
                },
                {
                    'id': appointment.subbookings[0].combo_children[0].id,
                    'treatment_id': combo_children[0].service.treatment.id,
                    'treatment_slug': combo_children[0].service.treatment.slug,
                    'treatment_internal_name': combo_children[0].service.treatment.internal_name,
                    'service_variant_id': combo_children[0].id,
                    'service_variant_active': True,
                    'service_active': True,
                    'service_is_available_for_customer_booking': True,
                    'combo_parent_id': appointment.subbookings[0].combo_children[0].combo_parent_id,
                    'staffer_id': staffer.id,
                    'appliance_id': None,
                    'is_staffer_still_active': True,
                },
                {
                    'id': appointment.subbookings[0].combo_children[1].id,
                    'treatment_id': combo_children[1].service.treatment.id,
                    'treatment_slug': combo_children[1].service.treatment.slug,
                    'treatment_internal_name': combo_children[1].service.treatment.internal_name,
                    'service_variant_id': combo_children[1].id,
                    'service_variant_active': True,
                    'service_active': True,
                    'service_is_available_for_customer_booking': False,
                    'combo_parent_id': appointment.subbookings[0].combo_children[1].combo_parent_id,
                    'staffer_id': staffer2.id,
                    'appliance_id': None,
                    'is_staffer_still_active': True,
                },
                {
                    'id': appointment.subbookings[0].combo_children[2].id,
                    'treatment_id': combo_children[2].service.treatment.id,
                    'treatment_slug': combo_children[2].service.treatment.slug,
                    'treatment_internal_name': combo_children[2].service.treatment.internal_name,
                    'service_variant_id': combo_children[2].id,
                    'service_variant_active': True,
                    'service_active': True,
                    'service_is_available_for_customer_booking': True,
                    'combo_parent_id': appointment.subbookings[0].combo_children[2].combo_parent_id,
                    'staffer_id': staffer.id,
                    'appliance_id': None,
                    'is_staffer_still_active': True,
                },
            ],
        )

    def test_existing_combo_appointment_with_new_service_variant(self):
        combo, combo_children, staffer, staffer2, _ = self._prepare_combo_appointment_data()
        appointment = create_appointment(
            [
                {
                    'service_variant': combo,
                    'combo_children': [
                        {
                            'service_variant': combo_children[1],
                            'staffer': staffer2,
                        },
                        {
                            'service_variant': combo_children[2],
                            'staffer': staffer,
                        },
                    ],
                }
            ],
            booked_for=self.bci,
            business=self.business,
        )
        retriever = BookAgainInfoDataRetriever(
            business_id=self.business.id, appointment_id=appointment.id, user_id=self.user.id
        )
        self.assertListEqual(
            retriever.get_combo_membership_data([combo.id]),
            [{'combo': combo.id, 'child': child.id} for child in combo_children],
        )
        self.assertListEqual(
            retriever.get_subbookings_data(),
            [
                {
                    'id': appointment.subbookings[0].id,
                    'treatment_id': None,
                    'treatment_slug': None,
                    'treatment_internal_name': None,
                    'service_variant_id': combo.id,
                    'service_variant_active': True,
                    'service_active': True,
                    'service_is_available_for_customer_booking': True,
                    'combo_parent_id': None,
                    'staffer_id': None,
                    'appliance_id': None,
                    'is_staffer_still_active': False,
                },
                {
                    'id': appointment.subbookings[0].combo_children[0].id,
                    'treatment_id': combo_children[1].service.treatment.id,
                    'treatment_slug': combo_children[1].service.treatment.slug,
                    'treatment_internal_name': combo_children[1].service.treatment.internal_name,
                    'service_variant_id': combo_children[1].id,
                    'service_variant_active': True,
                    'service_active': True,
                    'service_is_available_for_customer_booking': True,
                    'combo_parent_id': appointment.subbookings[0].combo_children[0].combo_parent_id,
                    'staffer_id': staffer2.id,
                    'appliance_id': None,
                    'is_staffer_still_active': True,
                },
                {
                    'id': appointment.subbookings[0].combo_children[1].id,
                    'treatment_id': combo_children[2].service.treatment.id,
                    'treatment_slug': combo_children[2].service.treatment.slug,
                    'treatment_internal_name': combo_children[2].service.treatment.internal_name,
                    'service_variant_id': combo_children[2].id,
                    'service_variant_active': True,
                    'service_active': True,
                    'service_is_available_for_customer_booking': True,
                    'combo_parent_id': appointment.subbookings[0].combo_children[1].combo_parent_id,
                    'staffer_id': staffer.id,
                    'appliance_id': None,
                    'is_staffer_still_active': True,
                },
            ],
        )

    def test_existing_combo_appointment_with_staffer_not_available(self):
        combo, combo_children, staffer, staffer2, _ = self._prepare_combo_appointment_data()
        staffer3 = staffer_recipe.make()
        appointment = create_appointment(
            [
                {
                    'service_variant': combo,
                    'combo_children': [
                        {
                            'service_variant': combo_children[0],
                            'staffer': staffer,
                        },
                        {
                            'service_variant': combo_children[1],
                            'staffer': staffer2,
                        },
                        {
                            'service_variant': combo_children[2],
                            'staffer': staffer3,
                        },
                    ],
                }
            ],
            booked_for=self.bci,
            business=self.business,
        )
        retriever = BookAgainInfoDataRetriever(
            business_id=self.business.id, appointment_id=appointment.id, user_id=self.user.id
        )
        self.assertListEqual(
            retriever.get_combo_membership_data([combo.id]),
            [{'combo': combo.id, 'child': child.id} for child in combo_children],
        )
        self.assertListEqual(
            retriever.get_subbookings_data(),
            [
                {
                    'id': appointment.subbookings[0].id,
                    'treatment_id': None,
                    'treatment_slug': None,
                    'treatment_internal_name': None,
                    'service_variant_id': combo.id,
                    'service_variant_active': True,
                    'service_active': True,
                    'service_is_available_for_customer_booking': True,
                    'combo_parent_id': None,
                    'staffer_id': None,
                    'appliance_id': None,
                    'is_staffer_still_active': False,
                },
                {
                    'id': appointment.subbookings[0].combo_children[0].id,
                    'treatment_id': combo_children[0].service.treatment.id,
                    'treatment_slug': combo_children[0].service.treatment.slug,
                    'treatment_internal_name': combo_children[0].service.treatment.internal_name,
                    'service_variant_id': combo_children[0].id,
                    'service_variant_active': True,
                    'service_active': True,
                    'service_is_available_for_customer_booking': True,
                    'combo_parent_id': appointment.subbookings[0].combo_children[0].combo_parent_id,
                    'staffer_id': staffer.id,
                    'appliance_id': None,
                    'is_staffer_still_active': True,
                },
                {
                    'id': appointment.subbookings[0].combo_children[1].id,
                    'treatment_id': combo_children[1].service.treatment.id,
                    'treatment_slug': combo_children[1].service.treatment.slug,
                    'treatment_internal_name': combo_children[1].service.treatment.internal_name,
                    'service_variant_id': combo_children[1].id,
                    'service_variant_active': True,
                    'service_active': True,
                    'service_is_available_for_customer_booking': True,
                    'combo_parent_id': appointment.subbookings[0].combo_children[1].combo_parent_id,
                    'staffer_id': staffer2.id,
                    'appliance_id': None,
                    'is_staffer_still_active': True,
                },
                {
                    'id': appointment.subbookings[0].combo_children[2].id,
                    'treatment_id': combo_children[2].service.treatment.id,
                    'treatment_slug': combo_children[2].service.treatment.slug,
                    'treatment_internal_name': combo_children[2].service.treatment.internal_name,
                    'service_variant_id': combo_children[2].id,
                    'service_variant_active': True,
                    'service_active': True,
                    'service_is_available_for_customer_booking': True,
                    'combo_parent_id': appointment.subbookings[0].combo_children[2].combo_parent_id,
                    'staffer_id': staffer3.id,
                    'appliance_id': None,
                    'is_staffer_still_active': False,
                },
            ],
        )
