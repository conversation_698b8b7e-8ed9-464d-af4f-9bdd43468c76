from django.conf import settings
from django.utils.encoding import force_str
from django.utils.translation import gettext
from django.utils.translation import gettext_lazy as _

from service.exceptions import ServiceError

BOOKING_CONFLICT_MESSAGE = _('This time slot is no longer available.')


class BookingConflict(ServiceError):
    DEFAULT_MESSAGE = BOOKING_CONFLICT_MESSAGE

    def __init__(self, message=None, notices=None, overbooking_dates=None):
        self.message = force_str(message or self.DEFAULT_MESSAGE)
        self.notices = notices or {}
        if overbooking_dates is not None:
            self.notices['overbooking_dates'] = [
                date_.strftime(settings.DATE_FORMAT) for date_ in overbooking_dates
            ]

        ServiceError.__init__(
            self,
            code=409,
            errors=[
                {
                    'code': 'conflict',
                    'type': 'validation',
                    'description': self.message,
                    'notices': self.notices,
                }
            ],
        )


class MatcherConflict(ServiceError):
    def __init__(self, message=None):
        self.message = message or gettext(
            'One or more services are not available on the selected time'
        )
        ServiceError.__init__(
            self,
            code=400,
            errors=[
                {
                    'code': 'matcher_conflict',
                    'type': 'validation',
                    'description': self.message,
                }
            ],
        )


class DrawForOverbookingException(BookingConflict):
    def __init__(self, message=None, notices=None, overbooking_dates=None):
        if message is None:
            message = gettext('Overbooking requires all resources to be manually selected')
        super().__init__(message, notices, overbooking_dates)


class MissingResourcesException(BookingConflict):
    pass


class MissingParallelResourcesException(BookingConflict):
    pass


class BusinessDoesNotExistException(ServiceError): ...


class UserIsBlacklistedException(ServiceError): ...


class TimeSlotsException(ServiceError): ...
