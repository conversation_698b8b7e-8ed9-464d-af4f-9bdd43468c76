import logging
from dataclasses import dataclass
from datetime import date, timed<PERSON>ta
from typing import Any

from lib.db import READ_ONLY_DB, using_db_for_reads
from service.customer.time_slots_forward import TimeSlotsForward
from service.partner_forward import partner_forward
from webapps.booking.exceptions import (
    BusinessDoesNotExistException,
    TimeSlotsException,
    UserIsBlacklistedException,
)
from webapps.booking.experiments.compare_new_slots.compare import (
    generate_and_compare_with_timeslot_service_slots_for_service_variants,
)
from webapps.booking.timeslots.v1.booksy_slots import BooksySlots
from webapps.booking.timeslots.v1.booksy_slots_generator import BooksySlotsGenerator
from webapps.booking.timeslots.v1.serializers import RequestData, Subbooking
from webapps.booking.timeslots.v1.slots_transformer import transform_booksy_slots_to_time_slots
from webapps.business.context import business_context
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business.models.models import Business
from webapps.kill_switch.models import KillS<PERSON>
from webapps.user.models import User

DEFAULT_END_DAYS_DELTA = 14

TimeSlots = dict[date, list[dict[str, Any]]]
ServiceVariantsTimeSlots = dict[int, TimeSlots]

logger = logging.getLogger("booksy.timeslots.get_business_service_variants_command")


@dataclass
class GetBusinessServiceVariantsTimeSlotsCommandInput:
    business_id: int
    start: date
    service_variant_ids: list[int]
    end: date | None = None
    user: User | None = None


@dataclass
class GetBusinessServiceVariantsTimeSlotsCommandOutput:
    slots: ServiceVariantsTimeSlots


class GetBusinessServiceVariantsTimeSlotsCommand:
    @using_db_for_reads(
        database_name=READ_ONLY_DB,
        condition=lambda: KillSwitch.alive(KillSwitch.System.REPLICA_TIME_SLOTS),
    )
    def execute(
        self,
        command_input: GetBusinessServiceVariantsTimeSlotsCommandInput,
    ) -> GetBusinessServiceVariantsTimeSlotsCommandOutput:
        business = self._business(command_input.business_id)

        if command_input.user:
            self._ensure_user_is_not_blacklisted(
                business_id=business.id,
                user=command_input.user,
            )

        end = command_input.end or command_input.start + timedelta(days=DEFAULT_END_DAYS_DELTA)

        try:
            slots = self._calculate_time_slots(
                business=business,
                start=command_input.start,
                end=end,
                service_variant_ids=command_input.service_variant_ids,
            )
            return GetBusinessServiceVariantsTimeSlotsCommandOutput(slots)
        except Exception as e:
            raise TimeSlotsException() from e

    def _business(self, business_id: int) -> Business:
        try:
            return Business.objects.get(id=business_id, active=True)
        except Business.DoesNotExist as e:
            raise BusinessDoesNotExistException() from e

    def _ensure_user_is_not_blacklisted(
        self,
        business_id: int,
        user: User,
    ) -> None:
        if BusinessCustomerInfo.objects.filter(
            business_id=business_id,
            user=user,
            blacklisted=True,
        ).exists():
            raise UserIsBlacklistedException()

    def _calculate_time_slots(
        self,
        business: Business,
        start: date,
        end: date,
        service_variant_ids: list[int],
    ) -> ServiceVariantsTimeSlots:
        with business_context(business):
            slots = self._calculate_time_slots_with_old_algorithm(
                business=business,
                start=start,
                end=end,
                service_variant_ids=service_variant_ids,
            )

            try:
                generate_and_compare_with_timeslot_service_slots_for_service_variants(
                    business_id=business.id,
                    start=start,
                    end=end,
                    service_variant_ids=service_variant_ids,
                    slots=slots,
                    should_forward=TimeSlotsForward().should_forward(),
                    is_slots_optimization_enabled=business.time_slots_optimization,
                )
            except Exception as exception:  # pylint: disable=broad-exception-caught
                # Prevent the command from crasing when the comparison fails as it's not critical
                logger.warning(
                    "Failed to generate and compare time slots for business service variants: %s",
                    exception,
                )

        return slots

    def _calculate_time_slots_with_old_algorithm(
        self,
        business: Business,
        start: date,
        end: date,
        service_variant_ids: list[int],
    ) -> ServiceVariantsTimeSlots:
        slots: ServiceVariantsTimeSlots = {}

        for service_variant_id in service_variant_ids:
            slots[service_variant_id] = transform_booksy_slots_to_time_slots(
                self._calculate_booksy_slots_with_partner_forward(
                    RequestData(
                        business_id=business.id,
                        start_date=start,
                        end_date=end,
                        subbookings=(
                            Subbooking(
                                service_variant_id=service_variant_id,
                            ),
                        ),
                    ),
                )
            )

        return slots

    @partner_forward(TimeSlotsForward)
    def _calculate_booksy_slots_with_partner_forward(
        self,
        request_data: RequestData,
    ) -> BooksySlots:
        return BooksySlotsGenerator.generate(request_data)
