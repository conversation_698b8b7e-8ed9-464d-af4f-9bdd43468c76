from datetime import date, time, timedelta

import pandas as pd
import pytest
from django.contrib.postgres.fields.ranges import DateRange


from webapps.business.tools import active_resource_ids
from webapps.schedule.models import BusinessHours, ResourceHours, Schedule
from webapps.schedule.working_hours import (
    ShiftUpdate,
    BusinessSchedule,
    ResourcesSchedule,
    _add_waiting_opening_hours,
    get_empty_hours_df,
    week_schedule,
)

start_date = date(2021, 2, 1)  # monday
middle_date = date(2021, 2, 2)
end_date = date(2021, 2, 8)


# pylint: disable=unused-argument


def test_business_schedule(
    business,
    business_opening_hours,
    schedule_recipe,
):
    schedule_recipe.make(date=middle_date, hours=[(time(8), time(12))])

    business_df = BusinessSchedule(
        business_id=business.id,
        start_date=start_date,
        end_date=end_date,
    ).data_frame()

    assert business_df.loc[start_date].hours == [[time(10), time(18)]]
    assert business_df.loc[middle_date].hours == [[time(8), time(12)]]
    assert business_df.loc[start_date].valid_from == BusinessHours.FIRST


def test_resource_schedule(  # pylint: disable=too-many-arguments
    business,
    staffer,
    staffer_recipe,
    resource_working_hours,
    schedule_recipe,
    working_hours_recipe,
):
    schedule_recipe.make(date=middle_date, resource=staffer, hours=[(time(8), time(12))])
    staffer2 = staffer_recipe.make()
    hours = [(time(11), time(18))]
    next_date = middle_date + timedelta(days=1)
    working_hours_recipe.make(
        valid_from=next_date,
        monday=hours,
        tuesday=hours,
        wednesday=hours,
        thursday=hours,
        friday=hours,
    )

    resources_df = ResourcesSchedule(
        business_id=business.id,
        resource_ids=[staffer.id, staffer2.id],
        start_date=start_date,
        end_date=end_date,
    ).data_frame()

    assert resources_df.loc[staffer.id, pd.to_datetime(start_date)].hours == [[time(10), time(18)]]

    assert resources_df.loc[staffer.id, pd.to_datetime(middle_date)].hours == [[time(8), time(12)]]
    assert (
        resources_df.loc[staffer.id, pd.to_datetime(start_date)].valid_from == ResourceHours.FIRST
    )

    schedule = week_schedule(
        business_id=business.id,
        week_start=start_date,
        week_end=end_date,
        resource_ids=[staffer.id, staffer2.id],
        tz=business.get_timezone(),
    )
    assert schedule


def test_time_off_schedule(
    business,
    staffer,
    resource_working_hours,
    time_off_recipe,
):
    time_off_recipe.make(
        resource=staffer,
        date_range=DateRange(start_date, start_date, bounds='[]'),
    )
    time_off_recipe.make(
        resource=staffer,
        date_range=DateRange(middle_date, middle_date, bounds='[]'),
        hour_from=time(12),
        hour_till=time(20),
    )

    resources_df = ResourcesSchedule(
        business_id=business.id,
        resource_ids=[staffer.id],
        start_date=start_date,
        end_date=end_date,
    ).data_frame(with_time_offs_merged=True)

    assert resources_df.loc[staffer.id, pd.to_datetime(start_date)].hours == []
    assert resources_df.loc[staffer.id, pd.to_datetime(middle_date)].hours == [(time(10), time(12))]


def test_time_off_normalize(
    business,
    staffer,
    resource_working_hours,
    time_off_recipe,
):
    time_off_recipe.make(
        resource=staffer,
        date_range=DateRange(start_date, start_date, bounds='[]'),
    )
    time_off_recipe.make(
        resource=staffer,
        date_range=DateRange(middle_date, middle_date, bounds='[]'),
        hour_from=time(12),
        hour_till=time(20),
    )

    resources_df = ResourcesSchedule(
        business_id=business.id,
        resource_ids=[staffer.id],
        start_date=start_date,
        end_date=end_date,
    ).data_frame()

    # normalize all day to working hours
    assert resources_df.loc[staffer.id, pd.to_datetime(start_date)].time_off_hours == [
        (time(10), time(18))
    ]
    # don't normalize part day #76024
    assert resources_df.loc[staffer.id, pd.to_datetime(middle_date)].time_off_hours == [
        (time(12), time(20))
    ]


def test_excessive_off_schedule(
    business,
    staffer,
    resource_working_hours,
    time_off_recipe,
):
    time_off_recipe.make(
        resource=staffer,
        date_range=DateRange(start_date, date(2050, 1, 1)),
    )

    time_offs_df = ResourcesSchedule(  # pylint: disable=protected-access
        business_id=business.id,
        resource_ids=[staffer.id],
        start_date=start_date,
        end_date=end_date,
    )._time_offs_frame()

    assert time_offs_df.shape[0] == 8, 'should create time_offs for requested dates only'


def test_no_week_hours_schedule(
    business,
    staffer,
    schedule_recipe,
):
    schedule_recipe.make(date=middle_date, resource=staffer, hours=[(time(8), time(12))])

    resources_df = ResourcesSchedule(
        business_id=business.id,
        resource_ids=[staffer.id],
        start_date=start_date,
        end_date=end_date,
    ).data_frame()

    assert resources_df.loc[staffer.id, pd.to_datetime(start_date)].hours == []

    assert resources_df.loc[staffer.id, pd.to_datetime(middle_date)].hours == [[time(8), time(12)]]


def test_empty_shift(
    business,
    staffer,
    business_opening_hours,
    resource_working_hours,
    schedule_recipe,
):
    schedule_recipe.make(date=middle_date, hours=[(time(6), time(22))])

    business_df = BusinessSchedule(
        business_id=business.id,
        start_date=start_date,
        end_date=end_date,
    ).data_frame()

    resources_df = ResourcesSchedule(
        business_id=business.id,
        resource_ids=[staffer.id],
        start_date=start_date,
        end_date=end_date,
    ).data_frame()

    empty_shift_df = get_empty_hours_df(resources_df, business_df)
    assert empty_shift_df.loc[middle_date].hours == [(time(6), time(10)), (time(18), time(22))]


def test_business_schedule_valid_from(
    business,
    business_opening_hours,
):
    business_opening_hours.valid_from = middle_date
    business_opening_hours.save()

    business_df = BusinessSchedule(
        business_id=business.id,
        start_date=start_date,
        end_date=end_date,
    ).data_frame()

    __, valid_from = _add_waiting_opening_hours(
        business_df.reset_index(), business.id, business.get_timezone()
    )

    assert valid_from is None


@pytest.mark.freeze_time('2022-03-16')
@pytest.mark.parametrize(
    'hours, expected_schedules',
    [
        pytest.param([(time(10), time(18))], 0, id="Shift fully overlap working hours"),
        pytest.param([(time(10), time(17))], 1, id="Shift partially overlap working hours"),
        pytest.param(None, 1, id="Shift is unavailable"),
    ],
)
def test_shift_updates_schedules(business, business_opening_hours, hours, expected_schedules):
    date.today()
    updater = ShiftUpdate(
        hours=hours,
        shift_date=date.today(),
        business_id=business.id,
        all_resource_ids=active_resource_ids(business.id),
        tz=business.get_timezone(),
    )

    assert Schedule.objects.filter(business_id=business.id).count() == 0

    updater.apply_update()

    assert Schedule.objects.filter(business_id=business.id).count() == expected_schedules


def test_shift_updates_full_day_500(business, business_opening_hours):
    date.today()
    resource_ids = active_resource_ids(business.id)
    updater = ShiftUpdate(
        hours=[(time(0), time(23, 59))],
        shift_date=date.today(),
        business_id=business.id,
        all_resource_ids=resource_ids,
        tz=business.get_timezone(),
        adjust_working_hours=True,
    )

    # assert does not raises Exception
    updater.is_valid_update()


def test_shift_updates_no_hours(business):
    date.today()
    resource_ids = active_resource_ids(business.id)
    updater = ShiftUpdate(
        hours=[(time(0), time(23, 59))],
        shift_date=date.today(),
        business_id=business.id,
        all_resource_ids=resource_ids,
        tz=business.get_timezone(),
        adjust_working_hours=True,
    )

    # assert does not raises Exception
    updater.apply_update()


def test_business_hours_insert_day_schedule(business_hours_recipe):
    hours = [(time(10), time(14))]
    business_hours = business_hours_recipe.make(
        monday=hours,
        tuesday=hours,
        wednesday=hours,
        thursday=hours,
        friday=hours,
    )

    business_hours.refresh_from_db()

    assert business_hours.monday == hours
    assert business_hours.tuesday == hours
    assert business_hours.wednesday == hours
    assert business_hours.thursday == hours
    assert business_hours.friday == hours
    assert business_hours.saturday == []
    assert business_hours.sunday == []

    assert business_hours.schedule_data == {
        'friday': [['10:00:00', '14:00:00']],
        'monday': [['10:00:00', '14:00:00']],
        'sunday': [],
        'tuesday': [['10:00:00', '14:00:00']],
        'saturday': [],
        'thursday': [['10:00:00', '14:00:00']],
        'wednesday': [['10:00:00', '14:00:00']],
    }


def test_business_hours_insert_schedule_data_is_ignored(business_hours_recipe):
    hours = [(time(10), time(14))]
    business_hours = business_hours_recipe.make(
        monday=hours,
        tuesday=hours,
        wednesday=hours,
        thursday=hours,
        friday=hours,
        schedule_data="Whatever",
    )

    business_hours.refresh_from_db()

    assert business_hours.schedule_data == {
        'friday': [['10:00:00', '14:00:00']],
        'monday': [['10:00:00', '14:00:00']],
        'sunday': [],
        'tuesday': [['10:00:00', '14:00:00']],
        'saturday': [],
        'thursday': [['10:00:00', '14:00:00']],
        'wednesday': [['10:00:00', '14:00:00']],
    }


def test_business_hours_update_day_schedule(business_hours_recipe):

    hours = [(time(10), time(14))]
    business_hours = business_hours_recipe.make(
        monday=hours,
        tuesday=hours,
        wednesday=hours,
        thursday=hours,
        friday=hours,
    )

    business_hours.refresh_from_db()

    business_hours.wednesday.append((time(12), time(15)))
    business_hours.sunday = [(time(12), time(16))]

    business_hours.save()
    business_hours.refresh_from_db()

    assert business_hours.schedule_data == {
        'friday': [['10:00:00', '14:00:00']],
        'monday': [['10:00:00', '14:00:00']],
        'sunday': [['12:00:00', '16:00:00']],
        'tuesday': [['10:00:00', '14:00:00']],
        'saturday': [],
        'thursday': [['10:00:00', '14:00:00']],
        'wednesday': [['10:00:00', '14:00:00'], ['12:00:00', '15:00:00']],
    }


def test_business_hours_update_schedule_data_is_ignored(business_hours_recipe):

    hours = [(time(10), time(14))]
    business_hours = business_hours_recipe.make(
        monday=hours,
        tuesday=hours,
        wednesday=hours,
        thursday=hours,
        friday=hours,
    )

    business_hours.refresh_from_db()

    business_hours.schedule_data = "Whatever"
    business_hours.sunday = [(time(12), time(16))]

    business_hours.save()
    business_hours.refresh_from_db()

    assert business_hours.schedule_data == {
        'friday': [['10:00:00', '14:00:00']],
        'monday': [['10:00:00', '14:00:00']],
        'sunday': [['12:00:00', '16:00:00']],
        'tuesday': [['10:00:00', '14:00:00']],
        'saturday': [],
        'thursday': [['10:00:00', '14:00:00']],
        'wednesday': [['10:00:00', '14:00:00']],
    }


def test_resource_hours_insert_day_schedule(working_hours_recipe):
    hours = [(time(10), time(14))]
    resource_hours = working_hours_recipe.make(
        monday=hours,
        tuesday=hours,
        wednesday=hours,
        thursday=hours,
        friday=hours,
    )

    resource_hours.refresh_from_db()

    assert resource_hours.monday == hours
    assert resource_hours.tuesday == hours
    assert resource_hours.wednesday == hours
    assert resource_hours.thursday == hours
    assert resource_hours.friday == hours
    assert resource_hours.saturday == []
    assert resource_hours.sunday == []

    assert resource_hours.schedule_data == {
        'friday': [['10:00:00', '14:00:00']],
        'monday': [['10:00:00', '14:00:00']],
        'sunday': [],
        'tuesday': [['10:00:00', '14:00:00']],
        'saturday': [],
        'thursday': [['10:00:00', '14:00:00']],
        'wednesday': [['10:00:00', '14:00:00']],
    }


def test_resource_hours_insert_schedule_data_is_ignored(working_hours_recipe):
    hours = [(time(10), time(14))]
    resource_hours = working_hours_recipe.make(
        monday=hours,
        tuesday=hours,
        wednesday=hours,
        thursday=hours,
        friday=hours,
        schedule_data="Whatever",
    )

    resource_hours.refresh_from_db()

    assert resource_hours.schedule_data == {
        'friday': [['10:00:00', '14:00:00']],
        'monday': [['10:00:00', '14:00:00']],
        'sunday': [],
        'tuesday': [['10:00:00', '14:00:00']],
        'saturday': [],
        'thursday': [['10:00:00', '14:00:00']],
        'wednesday': [['10:00:00', '14:00:00']],
    }


def test_resource_hours_update_day_schedule(working_hours_recipe):

    hours = [(time(10), time(14))]
    resource_hours = working_hours_recipe.make(
        monday=hours,
        tuesday=hours,
        wednesday=hours,
        thursday=hours,
        friday=hours,
    )

    resource_hours.refresh_from_db()

    resource_hours.wednesday.append((time(12), time(15)))
    resource_hours.sunday = [(time(12), time(16))]

    resource_hours.save()
    resource_hours.refresh_from_db()

    assert resource_hours.schedule_data == {
        'friday': [['10:00:00', '14:00:00']],
        'monday': [['10:00:00', '14:00:00']],
        'sunday': [['12:00:00', '16:00:00']],
        'tuesday': [['10:00:00', '14:00:00']],
        'saturday': [],
        'thursday': [['10:00:00', '14:00:00']],
        'wednesday': [['10:00:00', '14:00:00'], ['12:00:00', '15:00:00']],
    }


def test_resource_hours_update_schedule_data_is_ignored(working_hours_recipe):

    hours = [(time(10), time(14))]
    resource_hours = working_hours_recipe.make(
        monday=hours,
        tuesday=hours,
        wednesday=hours,
        thursday=hours,
        friday=hours,
    )

    resource_hours.refresh_from_db()

    resource_hours.schedule_data = "Whatever"
    resource_hours.sunday = [(time(12), time(16))]

    resource_hours.save()
    resource_hours.refresh_from_db()

    assert resource_hours.schedule_data == {
        'friday': [['10:00:00', '14:00:00']],
        'monday': [['10:00:00', '14:00:00']],
        'sunday': [['12:00:00', '16:00:00']],
        'tuesday': [['10:00:00', '14:00:00']],
        'saturday': [],
        'thursday': [['10:00:00', '14:00:00']],
        'wednesday': [['10:00:00', '14:00:00']],
    }
