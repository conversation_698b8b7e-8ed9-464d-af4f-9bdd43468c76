# pylint: disable=cyclic-import
import operator
from datetime import date, datetime, time, timedelta
from functools import reduce
from itertools import chain

from dateutil.tz import tzfile
from django.conf import settings
from django.contrib.postgres.aggregates import ArrayAgg
from django.contrib.postgres.fields.ranges import DateRange
from django.db.models import Q
from django.utils.encoding import force_str
from django.utils.timezone import override
from django.utils.translation import gettext_lazy as _
from numpy import vectorize
from pandas import (
    DataFrame,
    NaT,
    Series,
    Timestamp,
    date_range,
    isnull,
)

from lib.enums import StrEnum
from lib.ranges import (
    complement_ranges,
    merge_ranges,
    subtract_ranges,
)
from lib.sensi.sensidb import execute_on_db, fetch_db_all
from lib.tools import tznow
from webapps.consts import MOBILE_SOURCES, SALON_APPS
from webapps.schedule.enums import (
    DayOfWeek,
    APPLIANCE_TIME_OFF_REASONS,
    STAFF_TIME_OFF_REASONS,
)
from webapps.schedule.hours_tools import (
    coerce_object,
    dates_in_range,
    first_day_of_week,
    format_date_range,
    get_whole_weeks_month_range,
    month_range,
    total_minutes,
)
from webapps.schedule.models import (
    BusinessHours,
    ResourceHours,
    ResourceTimeOff,
    Schedule,
)
from webapps.schedule.signals import schedule_update
from webapps.schedule.typing import WeekHours


weekday = vectorize(lambda x: x.isoweekday() % 7, otypes=['object'])
vec_total_minutes = vectorize(total_minutes, otypes=['object'])
vec_merge_ranges = vectorize(merge_ranges, otypes=['object'])
vec_subtract_ranges = vectorize(subtract_ranges, otypes=['object'])
# pylint: disable=too-many-arguments


TIMESTAMP_DATE = date(1900, 1, 1)
DELTA1 = timedelta(days=1)


def _stamp(hour: time):
    return Timestamp(datetime.combine(TIMESTAMP_DATE, hour)) if hour else NaT


def _hstart(hours):
    return _stamp(hours[0][0]) if hours else NaT


def _hend(hours):
    return _stamp(hours[-1][-1]) if hours else NaT


class ErrorCode(StrEnum):
    APPOINTMENTS_CONFLICT = 'appointments_conflict'
    SHIFTS_APPOINTMENTS_CONFLICT = 'shifts_appointments_conflict'
    TIME_OFF_APPOINTMENTS_CONFLICT = 'time_off_appointments_conflict'
    EMPTY_SHIFTS = 'empty_shifts'
    ADJUST_WORKING_HOURS = 'adjust_working_hours'


class ErrorMessages:
    @classmethod
    def is_mobile(cls, app_source: str = None) -> bool:
        if app_source and app_source in list(MOBILE_SOURCES) + SALON_APPS:
            return True
        return False

    @classmethod
    def get_error(cls, app_source: str = None, error_code: ErrorCode = None):
        if cls.is_mobile(app_source):
            return cls.mobile_errors.get(error_code) or cls.errors.get(error_code)
        return cls.errors.get(error_code)

    errors = {
        ErrorCode.ADJUST_WORKING_HOURS: dict(
            code=ErrorCode.ADJUST_WORKING_HOURS,
            title=_('Adjust Working Hours?'),
            lines=[
                _(
                    'Would you like to update Working Hours for your Staff '
                    'and Resources to agree with your new Business Hours?'
                ),
                _(
                    'Warning: If you choose to update any customization '
                    'to future Shifts will be lost.'
                ),
            ],
        ),
        ErrorCode.EMPTY_SHIFTS: dict(
            code=ErrorCode.EMPTY_SHIFTS,
            title=_('Uncovered Shifts'),
            lines=[
                _(
                    "By extending your Business Hours there may be Shifts "
                    "that aren't covered. Please adjust Staff hours as appropriate."
                ),
            ],
        ),
        ErrorCode.TIME_OFF_APPOINTMENTS_CONFLICT: dict(
            code=ErrorCode.TIME_OFF_APPOINTMENTS_CONFLICT,
            title=_('Appointment Conflicts'),
            lines=[
                _(
                    'It looks like these changes conflict with existing appointments. '
                    'Are you sure you want to move forward?'
                )
            ],
        ),
        ErrorCode.SHIFTS_APPOINTMENTS_CONFLICT: dict(
            code=ErrorCode.SHIFTS_APPOINTMENTS_CONFLICT,
            title=_('Conflicts with existing appointments'),
            lines=[
                _(
                    'Applying changes to the current working hours will result '
                    'in conflicts with existing appointments. '
                    'Are you sure you want to apply these changes?'
                )
            ],
        ),
        ErrorCode.APPOINTMENTS_CONFLICT: dict(
            code=ErrorCode.APPOINTMENTS_CONFLICT,
            title=_('Conflicts with existing appointments'),
            lines=[
                _(
                    'Applying changes to the current working hours will result '
                    'in conflicts with existing appointments. '
                    'Are you sure you want to apply these changes?'
                )
            ],
        ),
    }

    mobile_errors = {
        ErrorCode.ADJUST_WORKING_HOURS: dict(
            code=ErrorCode.ADJUST_WORKING_HOURS,
            title=_('Adjust Working Hours?'),
            lines=[
                _(
                    'Would you like to update Working Hours for your Staff '
                    'and Resources to agree with your new Business Hours?'
                ),
                _(
                    'Warning: If you choose to update, all your Staff and Resources '
                    'will have the same Working Hours as updated Business Hours.'
                ),
            ],
        )
    }


class BusinessSchedule:
    """Business Opening Hours (default and customized) for given dates range"""

    def __init__(
        self,
        business_id: int,
        start_date: date = None,
        end_date: date = None,
        dates: list[date] = None,
    ):
        if start_date is not None:
            if end_date is None or end_date < start_date:
                raise RuntimeError('Wrong parameters')
        elif not dates:
            raise RuntimeError('Wrong parameters')

        self.business_id = business_id
        self.start_date = start_date
        self.end_date = end_date
        self.dates = dates if dates else dates_in_range(start_date, end_date)

    def data_frame(self):
        """Business Schedule DataFrame

        :return: DataFrame(
                    columns=[
                        'dow',  # day of week
                        'opening_hours',  # default opening hours
                        'schedule_hours', # date customized hours
                        'hours',    # merged opening_hours and
                                    # schedule_hours (schedule_hours
                                    # overwrite opening_hours)
                    ],
                    index='date',
                 )
        """
        business_df = DataFrame.from_records(
            self.hours_qs(),
            columns=['date', 'dow', 'opening_hours', 'schedule_hours', 'valid_from'],
        ).set_index('date')
        # pylint: disable=unsupported-assignment-operation
        business_df['hours'] = business_df.schedule_hours.where(
            business_df.schedule_hours.notna(),
            business_df.opening_hours,
        )
        business_df['hours'] = coerce_object(business_df.hours, list)
        # pylint: enable=unsupported-assignment-operation

        return business_df

    def hours_qs(self):
        sql = '''
        WITH 
        week_hours AS (
            SELECT business_id,
                   monday,
                   tuesday,
                   wednesday,
                   thursday,
                   friday,
                   saturday,
                   sunday,
                   valid_from,
                   COALESCE(
                       LEAD(valid_from, 1) 
                       OVER (PARTITION BY business_id ORDER BY valid_from
                   ), '9999-12-31') AS valid_till
            FROM schedule_businesshours
            WHERE business_id = %(business_id)s
        ),
        dates AS (
            SELECT dt as date, EXTRACT(dow from dt) as dow
            FROM unnest(%(dates)s) dt
        )
        SELECT
            d.date,
            d.dow,
            CASE
                WHEN d.dow = 0 THEN w.sunday
                WHEN d.dow = 1 THEN w.monday
                WHEN d.dow = 2 THEN w.tuesday
                WHEN d.dow = 3 THEN w.wednesday
                WHEN d.dow = 4 THEN w.thursday
                WHEN d.dow = 5 THEN w.friday
                WHEN d.dow = 6 THEN w.saturday
            END AS opening_hours,
            s.hours AS schedule_hours,
            w.valid_from
        FROM dates d
        LEFT JOIN week_hours w
            ON d.date >= w.valid_from AND d.date < w.valid_till
        LEFT JOIN schedule_schedule s
            ON s.business_id = w.business_id AND
               s.resource_id IS NULL AND
               s.date = d.date AND
               s.deleted IS NULL
        ORDER BY d.date  
        ;
        '''
        params = dict(
            business_id=self.business_id,
            dates=self.dates,  # must be a list not tuple
        )
        return fetch_db_all(sql, params)


class ResourcesSchedule:
    """Resources Working Hours (default and customized) for given dates range"""

    def __init__(
        self,
        business_id: int,
        resource_ids: list[int],
        start_date: date = None,
        end_date: date = None,
        dates: list[date] = None,
    ):
        """
        :param business_id: Business ID
        :param resource_ids: Selected or all active Resources IDs
        :param start_date: Date range start
        :param end_date: date range end (included)
        :param dates: (possibly non contingent) list of dates
        :param resource_ids: limit to resource IDs (optional)

        Use either start_date/end_date or dates
        """
        if start_date is not None:
            if end_date is None or end_date < start_date:
                raise RuntimeError('Wrong parameters')
        elif not dates:
            raise RuntimeError('Wrong parameters')

        self.business_id = business_id
        self.start_date = start_date
        self.end_date = end_date
        self.dates = dates or dates_in_range(start_date, end_date)
        if dates is None and start_date is not None:
            self.dates = dates_in_range(start_date, end_date)
        self.resource_ids = resource_ids

    def data_frame(self, with_durations=False, with_time_offs_merged=False):
        """Resources Schedule Data Frame

        :param with_durations: include duration columns
        :param with_time_offs_merged: merge time_offs into `hours`, so hours
                    provide resource's availability for booking
        :return: DataFrame(
                    columns=[
                        'dow',  # day of week
                        'working_hours',  # default working hours
                        'working_start',  # for convenience
                        'working_end',
                        'schedule_hours', # date customized working hours
                        'schedule_start', # for convenience
                        'schedule_end',
                        'hours',          # merged working_hours/schedule_hours
                        'hours_start',    # for convenience
                        'hours_end',
                        'time_offs',      # time offs full info (list[dict])
                        'time_off_hours', # merged time offs hours

                        # if with durations:
                        'work_duration',
                        'scheduled_duration',
                        'time_off_duration',
                    ],
                    index=['resource_id', 'date'],
                 )
        """
        result_df = self._working_hours_frame()

        # calculated fields
        #
        # 'hours' field = timeoff | schedule_hours | working_hours
        dow_records = result_df.schedule_hours.isna()
        result_df = result_df.assign(
            hours=result_df.working_hours.where(dow_records, result_df.schedule_hours),
            hours_start=result_df.working_start.where(
                dow_records,
                result_df.schedule_start,
            ),
            hours_end=result_df.working_end.where(
                dow_records,
                result_df.schedule_end,
            ),
        )

        result_df = result_df.assign(
            hours=coerce_object(result_df.hours, list),
            working_hours=coerce_object(result_df.working_hours, list),
        )
        time_offs_df = self._time_offs_frame(resources_df=result_df)
        if time_offs_df.size:
            time_offs_df['hours'] = time_offs_df.apply(
                lambda x: [x.hour_from.time(), x.hour_till.time()],
                axis=1,
            )
            time_offs_gb = time_offs_df.groupby(['resource_id', 'date'])
            offs_df = time_offs_gb.apply(lambda x: x.to_dict('records')).to_frame('time_offs')
            offs_df['time_off_hours'] = time_offs_gb.hours.apply(lambda x: merge_ranges(sorted(x)))
            result_df = result_df.join(offs_df)
            result_df['time_offs'] = coerce_object(result_df.time_offs, list)
            result_df['time_off_hours'] = coerce_object(
                result_df.time_off_hours,
                list,
            )
        else:
            result_df['time_offs'] = [[]] * result_df.shape[0]
            result_df['time_off_hours'] = [[]] * result_df.shape[0]

        if with_durations:
            result_df = self._with_durations(result_df)

        if with_time_offs_merged:
            result_df = self._with_time_offs_merged(result_df)
        return result_df

    def _working_hours_frame(self):
        result_df = create_typed_frame(
            [
                (
                    resource_id,
                    date,
                    dow,
                    w_hours,
                    _hstart(w_hours),
                    _hend(w_hours),
                    s_hours,
                    _hstart(s_hours),
                    _hend(s_hours),
                    valid_from,
                )
                for resource_id, date, dow, w_hours, s_hours, valid_from in self._working_hours_qs()
            ],
            columns=[
                'resource_id',
                'date',
                'dow',
                'working_hours',
                'working_start',
                'working_end',
                'schedule_hours',
                'schedule_start',
                'schedule_end',
                'valid_from',
            ],
            dtypes=[
                'int64',
                'datetime64[ns]',
                'int64',
                'object',
                'datetime64[ns]',
                'datetime64[ns]',
                'object',
                'datetime64[ns]',
                'datetime64[ns]',
                'object',
            ],
        ).set_index(['resource_id', 'date'])

        return result_df

    def _working_hours_qs(self):
        if not self.resource_ids:
            return []

        return working_hours_query(
            business_id=self.business_id,
            resource_ids=self.resource_ids,
            dates=self.dates,  # must be a list
        )

    def _time_offs_frame(self, resources_df=None):
        """

        :param resources_df: if provided, normalize time offs to be
                            within shift hours
        :return: DataFrame
        """
        columns = [
            'id',
            'resource_id',
            'date',
            'start_date',
            'end_date',
            'hour_from',
            'hour_till',
            'approved',
            'reason_code',
            'note',
        ]
        time_offs_df = (
            create_typed_frame(
                (
                    (
                        id_,
                        res_id,
                        date_,
                        d_range.lower,
                        d_range.upper - DELTA1,
                        _stamp(hfrom),
                        _stamp(htill),
                        apvd,
                        code,
                        note,
                    )
                    for id_, res_id, d_range, hfrom, htill, apvd, code, note in self._time_offs_qs(
                        [
                            'id',
                            'resource_id',
                            'date_range',
                            'hour_from',
                            'hour_till',
                            'approved',
                            'reason_code',
                            'note',
                        ]
                    )
                    # limit do self.dates to prevent explode rows count
                    # on large date_range value
                    for date_ in self.dates
                    if date_ in d_range
                ),
                columns=columns,
                dtypes=[
                    'int64',
                    'int64',
                    'datetime64[ns]',
                    'object',
                    'object',
                    'object',
                    'object',
                    'object',
                    'object',
                    'object',
                ],
            )
            .set_index(['resource_id', 'date'])
            .sort_index()
        )

        if time_offs_df.empty:
            return time_offs_df

        time_offs_df['all_day'] = time_offs_df.hour_from.isna().astype('int')
        if resources_df is not None:
            # Normalize time_offs hours
            # hour_from/till aligned to working hours start/end
            hours_start = resources_df.hours_start.reindex(time_offs_df.index)
            hours_end = resources_df.hours_end.reindex(time_offs_df.index)

            time_offs_df['hour_from'] = hours_start.where(
                time_offs_df.hour_from.isna(),
                time_offs_df.hour_from,
            )
            time_offs_df['hour_till'] = hours_end.where(
                time_offs_df.hour_till.isna(),
                time_offs_df.hour_till,
            )

            # make sure hour_from/till are time values, but with duration 0
            # (during no working hours)
            empty_start = time_offs_df.hour_from.isna()
            time_offs_df.loc[empty_start, 'hour_from'] = _stamp(time(0))
            time_offs_df.loc[empty_start, 'hour_till'] = _stamp(time(0))

        return time_offs_df

    @staticmethod
    def _with_durations(resources_df):
        resources_df['time_off_duration'] = vec_total_minutes(resources_df.time_off_hours)
        if resources_df.hours_start.notna().any():
            resources_df['scheduled_duration'] = (
                resources_df.hours_end - resources_df.hours_start
            ).dt.seconds / 60
            # normalize values
            # resources_df.fillna(0, inplace=True)
            resources_df['work_duration'] = (
                resources_df.scheduled_duration - resources_df.time_off_duration
            )
        else:
            resources_df['scheduled_duration'] = 0
            resources_df['work_duration'] = 0

        return resources_df

    @staticmethod
    def _with_time_offs_merged(resources_df):
        resources_df['hours'] = vec_subtract_ranges(
            resources_df.hours,
            resources_df.time_off_hours,
        )
        return resources_df

    def _time_offs_qs(self, values_list):
        if self.start_date:
            date_filter = Q(
                date_range__overlap=DateRange(
                    self.start_date,
                    self.end_date,
                    bounds='[]',
                )
            )
        else:
            date_filter = reduce(
                operator.ior, [Q(date_range__contains=date_) for date_ in self.dates]
            )

        return ResourceTimeOff.objects.filter(
            Q(business_id=self.business_id),
            Q(resource__in=self.resource_ids),
            date_filter,
        ).values_list(*values_list)


def week_schedule(  # pylint: disable=too-many-arguments
    business_id: int,
    week_start: date,
    week_end: date,
    resource_ids: list[int],
    tz: tzfile,
):
    """Week view for shiftworks editing

    :return: dict
    """
    # here we need most wide range of data: month with adjacent weeks
    # although we return small range (usually one week) of data,
    # we calculate month's durations and
    # we need to return empty_shifts etc. for the wide range

    df_start_date, df_end_date = get_whole_weeks_month_range(week_start, week_end)

    # used in np.query
    month_start, month_end = (  # pylint: disable=unused-variable
        month_range(week_start)
        if week_end.month > week_start.month and week_end.replace(day=1).isoweekday() > 3
        else month_range(week_end)
    )

    business_df = BusinessSchedule(
        business_id,
        df_start_date,
        df_end_date,
    ).data_frame()

    resources_df = ResourcesSchedule(
        business_id,
        resource_ids=resource_ids,
        start_date=df_start_date,
        end_date=df_end_date,
    ).data_frame(with_durations=True)

    empty_hours_df = get_empty_hours_df(resources_df, business_df)

    business_df['duration'] = vec_total_minutes(business_df.hours)
    business_week_df = business_df.query('date >= @week_start and date <= @week_end')

    business_week_df = business_week_df.join(
        empty_hours_df.rename(columns=dict(hours='empty_hours'))
    )
    business_week_df = business_week_df.assign(
        empty_hours=coerce_object(business_week_df.empty_hours, list)
    ).reset_index()

    # use waiting hours if exist
    business_week_df, biz_valid_from = _add_waiting_opening_hours(business_week_df, business_id, tz)

    business_week_duration = business_week_df.duration.sum()
    business_month_duration = business_df.query(
        'date >= @month_start and date <= @month_end'
    ).duration.sum()
    business_closed_dates = business_df.index[business_df.duration == 0]

    res_week_df = resources_df.query('date >= @week_start and date <= @week_end').reset_index(
        'date'
    )

    # use waiting hours if exist
    res_week_df, res_valid_from = _add_waiting_working_hours(
        res_week_df,
        business_id,
        resource_ids,
        tz,
    )

    res_week_gb = res_week_df.groupby('resource_id')
    res_week_scheduled = res_week_gb.scheduled_duration.sum()
    res_week_work = res_week_gb.work_duration.sum()

    res_month_gb = resources_df.query('date >= @month_start and date <= @month_end').groupby(
        'resource_id'
    )
    res_month_scheduled = res_month_gb.scheduled_duration.sum()
    res_month_work = res_month_gb.work_duration.sum()

    # final cleanup for serialization
    business_week_df = business_week_df.reset_index().sort_values('date')
    res_week_df = res_week_df.reset_index().sort_values('date')

    res_grouped = res_week_df.groupby('resource_id')
    res_schedule = {res_id: schedule.to_dict('records') for res_id, schedule in res_grouped}

    result = dict(
        business=dict(
            id=business_id,
            schedule=business_week_df.to_dict('records'),
            week_duration=business_week_duration,
            month_duration=business_month_duration,
            empty_shifts=empty_hours_df.reset_index().to_dict('records'),
            closed_dates=business_closed_dates,
            hours_apply_from=biz_valid_from,
        ),
        resources=[
            dict(
                resource_id=resource_id,
                schedule=res_schedule[resource_id],
                week_scheduled_duration=res_week_scheduled[resource_id],
                week_work_duration=res_week_work[resource_id],
                month_scheduled_duration=res_month_scheduled[resource_id],
                month_work_duration=res_month_work[resource_id],
                hours_apply_from=res_valid_from.get(resource_id),
            )
            for resource_id in resource_ids
        ],
        time_off_reasons=STAFF_TIME_OFF_REASONS,
        appliance_time_off_reasons=APPLIANCE_TIME_OFF_REASONS,
        # not used in serializer
        _empty_shifts_df=empty_hours_df,
    )
    return result


def _add_waiting_opening_hours(business_df, business_id, tz):
    # valid_from is null when start < valid_from
    valid_from = business_df.valid_from[business_df.valid_from.notna()].max()
    start = business_df.date.min()
    today = tznow(tz).date()

    opening_hours_obj = BusinessHours.get(business_id)
    if opening_hours_obj and opening_hours_obj.valid_from > start:
        # business_df contains old opening_hours, but new opening_hours
        # are waiting
        valid_from = opening_hours_obj.valid_from if opening_hours_obj.valid_from > today else None

        business_df = business_df.set_index('dow').drop(columns=['opening_hours'])
        opening_hours_df = DataFrame.from_records(
            map(tuple, opening_hours_obj.hours.items()),
            columns=['dow', 'opening_hours'],
        ).set_index('dow')
        business_df = business_df.join(opening_hours_df)
        business_df['opening_hours'] = coerce_object(business_df.opening_hours, list)
    else:
        # business_df contains already newest opening_hours.
        # If they are not waiting, clear valid_from
        if isnull(valid_from) or valid_from <= today:
            valid_from = None

    return business_df, valid_from


def _add_waiting_working_hours(resources_df, business_id, resource_ids, tz):
    start = resources_df.date.min().date()
    today = tznow(tz).date()
    valid_from_data = resources_df.valid_from
    # valid_from is null when start < valid_from
    valid_from_data = valid_from_data[valid_from_data.notna()]
    valid_from_series = valid_from_data.groupby('resource_id').max()
    res_valid_from = valid_from_series[valid_from_series > today].to_dict()

    res_working_objs = list(
        ResourceHours.get_latest_records(
            business_id,
            resource_ids,
        )
    )
    if any(obj.valid_from > start for obj in res_working_objs):
        # at least some resource have newer working_hours waiting
        # load newest working_hours for all resources
        resources_df = (
            resources_df.reset_index()
            .set_index(['resource_id', 'dow'])
            .drop(columns=['working_hours'])
        )
        res_valid_from = {
            obj.resource_id: obj.valid_from if obj.valid_from > today else None
            for obj in res_working_objs
        }
        # # in case some resource is not initialized
        # for rid in resource_ids:
        #     if rid not in res_valid_from:
        #         res_valid_from[rid] = None

        res_working_df = DataFrame.from_records(
            [
                (obj.resource_id, dow, hours)
                for obj in res_working_objs
                for dow, hours in obj.hours.items()
            ],
            columns=['resource_id', 'dow', 'working_hours'],
        ).set_index(['resource_id', 'dow'])
        resources_df = resources_df.join(res_working_df)
        resources_df['working_hours'] = coerce_object(resources_df.working_hours, list)

    return resources_df, res_valid_from


def simple_resource_schedule(
    business_id,
    resource_id,
    start_date,
    end_date,
):
    resources_df = ResourcesSchedule(
        business_id,
        resource_ids=[resource_id],
        start_date=start_date,
        end_date=end_date,
    ).data_frame(with_time_offs_merged=True)

    # preparing for serialization
    resources_df.reset_index('date', inplace=True)
    resources_df.sort_values('date', inplace=True)

    return resources_df[['date', 'hours']].xs(resource_id).to_dict('records')


class UpdateBase:
    """Base class for schedule updaters"""

    def __init__(
        self,
        business_id: int,
        all_resource_ids: list[int],
        tz: tzfile,
    ):
        """Shiftworks base class

        :param business_id: Business ID
        :param resource_ids: Selected or all active Resources ids
        :param tz: timezone of the Business
        """
        self.business_id = business_id
        self.all_resource_ids = all_resource_ids
        self.timezone = tz
        # for datetime filters in queryset we need time zone name
        # as argument for override(...) - override(timezone) is not working...
        self.tzname = tz._long_name  # pylint: disable=protected-access
        self.errors = []
        self.warnings = []

    @staticmethod
    def _format_conflict(conflict, **kwargs):
        return dict(
            title=force_str(conflict['title']),
            lines=[force_str(l).format(**kwargs) for l in conflict['lines']],
        )

    @staticmethod
    def _format_appointment_conflict(conflict, appt_conflicts, **kwargs):
        start = appt_conflicts[0]['booked_from__date']
        end = appt_conflicts[-1]['booked_from__date']
        if 'date' not in kwargs:
            kwargs['date'] = format_date_range(start, end)

        error = dict(
            title=force_str(conflict['title']),
            lines=[force_str(l).format(**kwargs) for l in conflict['lines']],
            conflict=dict(
                date=start.strftime(settings.DATE_FORMAT),
            ),
        )

        if 'resource_ids' in appt_conflicts[0]:
            error['conflict']['resources'] = list(
                chain.from_iterable(appt['resource_ids'] for appt in appt_conflicts)
            )

        return error

    def _is_single_staffer_business(self):
        if len(self.all_resource_ids) == 1:
            return True
        if len(self.all_resource_ids) == 2:
            from webapps.business.models import Resource

            return (
                Resource.objects.filter(
                    business_id=self.business_id, active=True, type=Resource.STAFF
                ).count()
                == 1
            )
        return False


class ShiftUpdate(UpdateBase):  # pylint: disable=too-many-instance-attributes
    def __init__(
        self,
        business_id: int,
        all_resource_ids: list[int],
        tz: tzfile,
        shift_date: date,
        resource_id: int = None,
        hours: list[tuple[time, time]] = None,
        adjust_working_hours: bool = None,
        week_start_date: date = None,
        booking_source: str = None,
    ):
        """Shift Update

        :param business_id: Business ID
        :param all_resource_ids: All active Resources ids
        :param tz: timezone of the Business
        :param shift_date: selected date
        :param resource_id: selected resource, if None update Business Shift
        :param hours: new hours
        :param adjust_working_hours: if True adjust reset resources hours
        """
        super().__init__(business_id, all_resource_ids, tz)

        self.shift_date = shift_date
        self.resource_id = resource_id
        self.hours = sorted(hours) if hours else []
        self.adjust_working_hours = adjust_working_hours
        self.week_start_date = week_start_date
        self.booking_source = booking_source

    def schedule(self):
        """Return schedule for the first target week"""
        week_start = self.week_start_date or first_day_of_week(self.shift_date)

        schedule = week_schedule(
            business_id=self.business_id,
            week_start=week_start,
            week_end=week_start + timedelta(days=6),
            resource_ids=self.all_resource_ids,
            tz=self.timezone,
        )
        empty_df = schedule['_empty_shifts_df']
        if empty_df.size:
            # select empty shifts from updated dates
            dates_index = date_range(self.shift_date, self.shift_date)
            if empty_df.index.intersection(dates_index).size:
                self.warnings.append(
                    ErrorMessages.get_error(self.booking_source, ErrorCode.EMPTY_SHIFTS)
                )

        return schedule

    def is_valid_update(self):
        if self.resource_id:
            self._validate_resource_update()
        else:
            self._validate_business_update()
        return not self.errors

    def apply_update(self):
        if self.resource_id:
            self._apply_resource_update()
        else:
            self._apply_business_update()

        schedule_update.send(Schedule, instance=dict(business_id=self.business_id))

    def _validate_resource_update(self):
        appt_conflicts = self.get_appointment_conflicts()
        if appt_conflicts:
            self.errors.append(
                ErrorMessages.get_error(self.booking_source, ErrorCode.SHIFTS_APPOINTMENTS_CONFLICT)
            )

    def _apply_resource_update(self):
        Schedule.objects.update_or_create(
            business_id=self.business_id,
            resource_id=self.resource_id,
            date=self.shift_date,
            defaults=dict(hours=self.hours),
        )
        if self._is_single_staffer_business():
            Schedule.objects.update_or_create(
                business_id=self.business_id,
                resource__isnull=True,
                date=self.shift_date,
                defaults=dict(hours=self.hours),
            )

    def _validate_business_update(self):
        if self.adjust_working_hours is None:
            self.errors.append(
                ErrorMessages.get_error(self.booking_source, ErrorCode.ADJUST_WORKING_HOURS)
            )
            return

        appt_conflicts = self.get_appointment_conflicts()
        if appt_conflicts:
            self.errors.append(
                ErrorMessages.get_error(self.booking_source, ErrorCode.SHIFTS_APPOINTMENTS_CONFLICT)
            )
            return

    def _is_business_working_hours_overlap(self):
        business_hours_obj = BusinessHours.objects.filter(
            business_id=self.business_id, valid_from__lte=self.shift_date
        ).last()
        if not business_hours_obj:
            return False

        day_of_week = int(self.shift_date.strftime("%w"))
        business_hours = business_hours_obj.hours.get(day_of_week, None)

        return business_hours and self.hours and self.hours == business_hours

    def _apply_business_update(self):
        if self.adjust_working_hours:
            for resource_id in self.all_resource_ids:
                Schedule.objects.update_or_create(
                    business_id=self.business_id,
                    resource_id=resource_id,
                    date=self.shift_date,
                    defaults=dict(hours=self.hours),
                )
            self.apply_single_staffer_business()

        # if schedule overlaps with default business working hours, delete it as it's unnecessary
        if self._is_business_working_hours_overlap():
            Schedule.objects.filter(
                business_id=self.business_id,
                resource__isnull=True,
                date=self.shift_date,
            ).delete()
        else:
            Schedule.objects.update_or_create(
                business_id=self.business_id,
                resource__isnull=True,
                date=self.shift_date,
                defaults=dict(hours=self.hours),
            )

    def apply_single_staffer_business(self):
        if not self._is_single_staffer_business() or not self.hours:
            return

        time_offs = list(
            ResourceTimeOff.objects.filter(
                business_id=self.business_id,
                date_range__contains=self.shift_date,
            )
        )
        if not time_offs:
            return

        for time_off in time_offs:
            lower = time_off.date_range.lower
            upper = time_off.date_range.upper - DELTA1
            # single day time_off
            if lower == upper and lower == self.shift_date:
                time_off.delete()
            elif lower == self.shift_date:
                time_off.date_range = DateRange(lower + DELTA1, upper, bounds='[]')
                time_off.save()
            elif upper == self.shift_date:
                time_off.date_range = DateRange(lower, upper - DELTA1, bounds='[]')
            # lower < shift_date < upper
            else:
                time_off.date_range = DateRange(lower, self.shift_date - DELTA1, bounds='[]')
                time_off.save()
                ResourceTimeOff.objects.create(
                    business_id=self.business_id,
                    resource=time_off.resource,
                    date_range=DateRange(self.shift_date + DELTA1, upper, bounds='[]'),
                    reason_code=time_off.reason_code,
                    note=time_off.note,
                    approved=time_off.approved,
                )

    def get_appointment_conflicts(self):
        dates = [self.shift_date]

        with override(self.tzname):
            booking_qs = ApptConflict.booking_qs(with_resource_ids=bool(self.resource_id)).filter(
                Q(appointment__business_id=self.business_id),
                ApptConflict.dates_filter(dates, self.timezone),
            )
            if time_filter := ApptConflict.complement_time_filters(self.hours):
                booking_qs = booking_qs.filter(time_filter)
            return list(booking_qs)


class ScheduleCopyPaste(UpdateBase):
    def __init__(
        self,
        business_id: int,
        resource_ids: list[int],
        all_resource_ids: list[int],
        tz: tzfile,
        copy_date: date = None,
        paste_dates: list[date] = None,
        is_week: bool = None,
        force: bool = None,
        booking_source: str = None,
    ):
        """

        :param business_id: Business ID
        :param resource_ids: Selected for update
        :param all_resource_ids: all active resources
        :param tz: timezone of the Business
        :param copy_date:
        :param paste_dates:
        :param is_week:
        :param force:
        """
        super().__init__(business_id, all_resource_ids, tz)

        self.resource_ids = resource_ids or all_resource_ids
        self.force = force
        self.copy_date = copy_date
        self.paste_dates = paste_dates
        self.is_week = is_week
        self.force = force

        self.source_start = self.copy_date
        self.source_end = (
            self.source_start + timedelta(days=6) if self.is_week else self.source_start
        )
        self.booking_source = booking_source

    def is_valid_action(self):
        appt_conflicts = self.get_appointment_conflicts()
        if appt_conflicts:
            self.errors.append(
                ErrorMessages.get_error(self.booking_source, ErrorCode.APPOINTMENTS_CONFLICT)
            )

        return not self.errors

    def schedule(self):
        """Return schedule for the first target week"""
        week_start = first_day_of_week(self.paste_dates[0])

        return week_schedule(
            business_id=self.business_id,
            week_start=week_start,
            week_end=week_start + timedelta(days=6),
            resource_ids=self.all_resource_ids,
            tz=self.timezone,
        )

    def apply_action(self):
        freeze_resources_working_hours(
            resource_ids=self.resource_ids,
            business_id=self.business_id,
            start_date=self.source_start,
            end_date=self.source_end,
        )
        with_business = set(self.resource_ids) == set(self.all_resource_ids)
        if with_business:
            freeze_business_opening_hours(
                business_id=self.business_id,
                start_date=self.source_start,
                end_date=self.source_end,
            )

        if self.is_week:
            target_dates = list(
                chain.from_iterable(
                    (date_ + timedelta(days=dow) for dow in DayOfWeek) for date_ in self.paste_dates
                )
            )
            copy_week_schedule(
                business_id=self.business_id,
                resource_ids=self.resource_ids,
                source_start=self.source_start,
                source_end=self.source_end,
                target_dates=target_dates,
                with_business=with_business,
            )
        else:
            copy_day_schedule(
                business_id=self.business_id,
                resource_ids=self.resource_ids,
                source_date=self.copy_date,
                target_dates=self.paste_dates,
                with_business=with_business,
            )

    def get_appointment_conflicts(self) -> list:
        resources_df = ResourcesSchedule(
            business_id=self.business_id,
            start_date=self.source_start,
            end_date=self.source_end,
            resource_ids=self.resource_ids,
        ).data_frame()

        if self.is_week:
            res_week_ranges = (
                (res_id, {row.dow: row.hours for row in hours_df.itertuples()})
                for res_id, hours_df in resources_df.groupby('resource_id')
            )
            time_filter = ApptConflict.ior(
                (
                    ApptConflict.iand(
                        (Q(resources=res_id), ApptConflict.week_time_filter(week_ranges))
                    )
                    for res_id, week_ranges in res_week_ranges
                )
            )
            step = 7
        else:
            time_filter = ApptConflict.ior(
                (
                    ApptConflict.iand(
                        (
                            Q(resources=row.resource_id),
                            ApptConflict.complement_time_filters(row.hours),
                        )
                    )
                    for row in resources_df.reset_index('resource_id').itertuples()
                )
            )
            step = 1

        with override(self.tzname):
            return list(
                ApptConflict.booking_qs(with_resource_ids=True).filter(
                    # Index Scan filters
                    Q(appointment__business_id=self.business_id),
                    ApptConflict.dates_filter(
                        self.paste_dates,
                        self.timezone,
                        step=step,
                    ),
                    # Seq Scan filters
                    time_filter,
                )
            )


class OpeningHoursUpdate(UpdateBase):
    def __init__(
        self,
        business_id: int,
        all_resource_ids: list[int],
        tz: tzfile,
        opening_hours: WeekHours = None,
        hours_apply_from: date = None,
        week_start_date: date = None,
        adjust_working_hours: bool = None,
        booking_source: str = None,
    ):
        """

        :param business_id: Business ID
        :param resource_ids: all active Resources ids
        :param tz: timezone of the Business
        :param booking_source: name of booking source
        :param business_opening_hours: complete list of open days in the week
        :param hours_apply_from: Date to apply new schedule from
        :param week_start_date: week to be return by `schedule` method
        :param adjust_working_hours: should adjust resources working hours
        """
        super().__init__(business_id, all_resource_ids, tz)

        self.opening_hours = opening_hours
        self.hours_apply_from = hours_apply_from
        self.week_start_date = week_start_date
        self.adjust_working_hours = adjust_working_hours
        self.booking_source = booking_source

        # will store list of updated days of week after `apply_action` call
        self.updated = None

    def is_valid_action(self):
        if self.adjust_working_hours is None:
            self.errors.append(
                ErrorMessages.get_error(self.booking_source, ErrorCode.ADJUST_WORKING_HOURS)
            )
            return

        appt_conflicts = self.get_appointment_conflicts()
        if appt_conflicts:
            self.errors.append(
                ErrorMessages.get_error(self.booking_source, ErrorCode.APPOINTMENTS_CONFLICT)
            )

        return not self.errors

    def schedule(self):
        """Return schedule for the first target week"""
        start_date = self.week_start_date or first_day_of_week(
            self.hours_apply_from or tznow(self.timezone).date()
        )

        schedule = week_schedule(
            business_id=self.business_id,
            week_start=start_date,
            week_end=start_date + timedelta(days=6),
            resource_ids=self.all_resource_ids,
            tz=self.timezone,
        )

        # empty shifts check
        # We check updated days of week within month calulated in week_schedule.
        # This is highly not optimal: we should compare:
        # 1. opening hours with working hours
        # 2. opening hours with all scheduled hours in the future
        # But practicaly it should work
        empty_df = schedule['_empty_shifts_df']
        if empty_df.size and self.updated:
            # we have date indexed, we need dow indexed
            empty_df = empty_df.reset_index()
            empty_df = empty_df.assign(
                dow=weekday(empty_df.date),
            ).set_index('dow')

            if empty_df.index.intersection(self.updated).size:
                self.warnings.append(
                    ErrorMessages.get_error(self.booking_source, ErrorCode.EMPTY_SHIFTS)
                )

        return schedule

    def apply_action(self):
        self.updated = BusinessHours.set_hours(
            business_id=self.business_id,
            hours=self.opening_hours,
            apply_from=self.hours_apply_from,
            tz=self.timezone,
        )
        if self.adjust_working_hours:
            ResourceHours.set_many_resources_hours(
                business_id=self.business_id,
                hours=self.opening_hours,
                apply_from=self.hours_apply_from,
                tz=self.timezone,
            )

            # clear all schedule: business and resources
            apply_from = self.hours_apply_from or tznow(self.timezone).date()
            Schedule.objects.filter(
                business_id=self.business_id,
                date__gte=apply_from,
            ).delete()

    def get_appointment_conflicts(self) -> list:
        tz = self.timezone
        start_from = self.hours_apply_from or tznow(tz).date()
        with override(self.tzname):
            return list(
                ApptConflict.booking_qs().filter(
                    # Index Scan filters for reasonable performance
                    Q(appointment__business_id=self.business_id),
                    ApptConflict.from_date_filter(start_from, tz),
                    # opening hours filter
                    ApptConflict.week_time_filter(self.opening_hours),
                )
            )


class WorkingHoursUpdate(UpdateBase):
    def __init__(
        self,
        business_id: int,
        all_resource_ids: list[int],
        tz: tzfile,
        resource_id: int = None,
        working_hours: WeekHours = None,
        hours_apply_from: date = None,
        week_start_date: date = None,
        booking_source: str = None,
    ):
        """

        :param business_id: Business ID
        :param all_resource_ids: all active Resources ids
        :param tz: timezone of the Business
        :param resource_id:
        :param working_hours:
        :param hours_apply_from:
        :param week_start_date:
        """
        super().__init__(business_id, all_resource_ids, tz)

        self.resource_id = resource_id
        self.working_hours = working_hours
        self.hours_apply_from = hours_apply_from
        self.week_start_date = week_start_date
        self.booking_source = booking_source

        # will store index of updated records after `apply_action` call
        self.updated_ind = None

    def is_valid_action(self):
        appt_conflicts = self.get_appointment_conflicts()
        if appt_conflicts:
            self.errors.append(
                ErrorMessages.get_error(self.booking_source, ErrorCode.APPOINTMENTS_CONFLICT)
            )

        return not self.errors

    def schedule(self):
        """Return schedule for the first target week"""
        start_date = self.week_start_date or first_day_of_week(
            self.hours_apply_from or tznow(self.timezone).date()
        )

        schedule = week_schedule(
            business_id=self.business_id,
            week_start=start_date,
            week_end=start_date + timedelta(days=6),
            resource_ids=self.all_resource_ids,
            tz=self.timezone,
        )

        # empty shifts check
        # We check updated days of week within month calulated in week_schedule.
        # This is highly not optimal: we should compare:
        # 1. opening hours with working hours
        # 2. opening hours with all scheduled hours in the future
        # But practicaly it should work
        empty_df = schedule['_empty_shifts_df']
        if empty_df.size and self.updated_ind is not None:
            # we have date indexed, we need dow indexed
            empty_df = empty_df.reset_index()
            empty_df = empty_df.assign(
                dow=weekday(empty_df.date),
            ).set_index('dow')

            updated = self.updated_ind.droplevel('resource_id').unique()

            if empty_df.index.intersection(updated).size:
                self.warnings.append(
                    ErrorMessages.get_error(self.booking_source, ErrorCode.EMPTY_SHIFTS)
                )

        return schedule

    def apply_action(self):
        self.updated = ResourceHours.set_hours(
            self.business_id,
            self.resource_id,
            self.working_hours,
            self.hours_apply_from,
            self.timezone,
        )
        # should we add `adjust_working_hours` params?
        # clear resources schedule
        apply_from = self.hours_apply_from or tznow(self.timezone).date()
        Schedule.objects.filter(
            business_id=self.business_id,
            resource_id=self.resource_id,
            date__gte=apply_from,
        ).delete()

    def get_appointment_conflicts(self):
        start_from = (
            datetime.combine(self.hours_apply_from, time(0)).replace(tzinfo=self.timezone)
            if self.hours_apply_from
            else tznow(self.timezone)
        )
        working_hours_filter = ApptConflict.week_time_filter(self.working_hours)
        with override(self.tzname):
            return list(
                ApptConflict.booking_qs(
                    with_resource_ids=True,
                ).filter(
                    working_hours_filter,
                    # Index Scan filters for reasonable performance
                    appointment__business_id=self.business_id,
                    resources=self.resource_id,
                    booked_from__gte=start_from,
                )
            )


class BulkTimeOffsCreate(UpdateBase):
    def __init__(
        self,
        business_id: int,
        tz: tzfile,
        resource_ids: list[int],
        all_resource_ids: list[int],
        start_date: date = None,
        end_date: date = None,
        hour_from: time = None,
        hour_till: time = None,
        reason_code: str = None,
        approved: bool = None,
        note: str = None,
        booking_source: str = None,
    ):
        """

        :param business_id: Business ID
        :param resource_ids: Selected to update
        :param all_resource_ids: all active resources in Business
        :param tz: timezone of the Business
        :param start_date:
        :param end_date:
        :param hour_from:
        :param hour_till:
        :param reason_code:
        :param approved:
        :param note:
        """
        super().__init__(business_id, all_resource_ids, tz)

        self.start_date = start_date
        self.end_date = end_date
        self.hour_from = hour_from
        self.hour_till = hour_till
        self.reason_code = reason_code
        self.approved = approved
        self.note = note
        self.resource_ids = resource_ids
        self.booking_source = booking_source

    def schedule(self):
        """Return schedule for the first target week"""
        week_start = first_day_of_week(self.start_date)

        schedule = week_schedule(
            business_id=self.business_id,
            week_start=week_start,
            week_end=week_start + timedelta(days=6),
            resource_ids=self.all_resource_ids,
            tz=self.timezone,
        )
        empty_df = schedule['_empty_shifts_df']
        if empty_df.size:
            # select empty shifts from updated dates
            dates_index = date_range(self.start_date, self.end_date)
            if empty_df.index.intersection(dates_index).size:
                self.warnings.append(
                    ErrorMessages.get_error(self.booking_source, ErrorCode.EMPTY_SHIFTS)
                )

        return schedule

    def is_valid_update(self):
        appt_conflicts = self.get_appointment_conflicts()
        if appt_conflicts:
            self.errors.append(
                ErrorMessages.get_error(self.booking_source, ErrorCode.APPOINTMENTS_CONFLICT)
            )

        return not self.errors

    def apply_update(self):
        ResourceTimeOff.objects.bulk_create(
            [
                ResourceTimeOff(
                    business_id=self.business_id,
                    date_range=DateRange(
                        self.start_date,
                        self.end_date,
                        bounds='[]',
                    ),
                    resource_id=resource_id,
                    hour_from=self.hour_from,
                    hour_till=self.hour_till,
                    approved=self.approved,
                    reason_code=self.reason_code,
                    note=self.note or '',
                )
                for resource_id in self.resource_ids
            ]
        )
        self.apply_single_staffer_business()

    def apply_single_staffer_business(self):
        # close business on single staffer all day time off
        if self._is_single_staffer_business() and self.hour_from is None:
            for date_ in dates_in_range(self.start_date, self.end_date):
                Schedule.objects.update_or_create(
                    business_id=self.business_id,
                    resource__isnull=True,
                    date=date_,
                    defaults=dict(hours=[]),
                )

    def get_appointment_conflicts(self):
        dates = dates_in_range(self.start_date, self.end_date)

        with override(self.tzname):
            booking_qs = ApptConflict.booking_qs().filter(
                Q(appointment__business_id=self.business_id),
                Q(resources__in=self.resource_ids),
                ApptConflict.dates_filter(dates, self.timezone),
            )
            if self.hour_from is not None:
                booking_qs = booking_qs.filter(
                    Q(booked_from__time__gt=self.hour_from)
                    & Q(booked_from__time__lte=self.hour_till)
                )
            return list(booking_qs)

    def is_holiday_added(self):
        """2 days or more time off added"""
        return len(dates_in_range(self.start_date, self.end_date)) >= 2


class TimeOffUpdate(UpdateBase):
    def __init__(
        self,
        business_id: int,
        all_resource_ids: list[int],
        tz: tzfile,
        time_off: ResourceTimeOff = None,
        resource_id: int = None,
        start_date: date = None,
        end_date: date = None,
        hour_from: time = None,
        hour_till: time = None,
        reason_code: str = None,
        approved: bool = None,
        note: str = None,
        booking_source: str = None,
    ):
        """

        :param business_id: Business ID
        :param all_resource_ids: all active Resources ids
        :param tz: timezone of the Business
        :param time_off:
        :param resource_id:
        :param start_date:
        :param end_date:
        :param hour_from:
        :param hour_till:
        :param reason_code:
        :param approved:
        :param note:
        """
        super().__init__(business_id, all_resource_ids, tz)

        self.time_off = time_off
        self.resource_id = resource_id
        self.date_from = start_date
        self.date_till = end_date
        self.hour_from = hour_from
        self.hour_till = hour_till
        self.reason_code = reason_code
        self.approved = approved
        self.note = note or ''
        self.booking_source = booking_source

    def schedule(self):
        """Return schedule for the first target week"""
        week_start = first_day_of_week(self.date_from)

        schedule = week_schedule(
            business_id=self.business_id,
            week_start=week_start,
            week_end=week_start + timedelta(days=6),
            resource_ids=self.all_resource_ids,
            tz=self.timezone,
        )
        empty_df = schedule['_empty_shifts_df']
        if empty_df.size:
            # select empty shifts from updated dates
            dates_index = date_range(self.date_from, self.date_till)
            if empty_df.index.intersection(dates_index).size:
                self.warnings.append(
                    ErrorMessages.get_error(self.booking_source, ErrorCode.EMPTY_SHIFTS)
                )

        return schedule

    def is_valid_update(self):
        appt_conflicts = self.get_appointment_conflicts()
        if appt_conflicts:
            self.errors.append(
                ErrorMessages.get_error(
                    self.booking_source,
                    ErrorCode.TIME_OFF_APPOINTMENTS_CONFLICT,
                )
            )

        return not self.errors

    def apply_update(self):
        # this must go first, it needs self.time_off before change
        self.apply_single_staffer_business()

        for attr in ['hour_from', 'hour_till', 'approved', 'reason_code', 'note']:
            setattr(self.time_off, attr, getattr(self, attr))
        self.time_off.date_range = DateRange(self.date_from, self.date_till + timedelta(days=1))
        self.time_off.save()

    def apply_single_staffer_business(self):
        if not self._is_single_staffer_business():
            return

        old_time_offs = (
            set(
                dates_in_range(
                    self.time_off.date_range.lower,
                    self.time_off.date_range.upper - DELTA1,
                )
            )
            if self.time_off.hour_from is None
            else set()
        )
        new_time_offs = (
            set(dates_in_range(self.date_from, self.date_till)) if self.hour_from is None else set()
        )

        to_delete = old_time_offs - new_time_offs
        to_create = new_time_offs - old_time_offs

        if to_delete:
            Schedule.objects.filter(
                business_id=self.business_id,
                resource__isnull=True,
                date__in=to_delete,
            ).delete()
        if to_create:
            for date_ in to_create:
                Schedule.objects.update_or_create(
                    business_id=self.business_id,
                    resource__isnull=True,
                    date=date_,
                    defaults=dict(hours=[]),
                )

    def get_appointment_conflicts(self):
        dates = dates_in_range(self.date_from, self.date_till)

        with override(self.tzname):
            booking_qs = ApptConflict.booking_qs().filter(
                Q(appointment__business_id=self.business_id),
                Q(resources=self.resource_id),
                ApptConflict.dates_filter(dates, self.timezone),
            )
            if self.hour_from is not None:
                booking_qs = booking_qs.filter(
                    Q(booked_from__time__gt=self.hour_from)
                    & Q(booked_from__time__lte=self.hour_till)
                )
            return list(booking_qs)


class ApptConflict:
    """Collection of function to query conflicting appointments"""

    @staticmethod
    def ior(filters):
        filters = list(filter(None, filters))
        return reduce(operator.ior, filters) if filters else None

    @staticmethod
    def iand(filters):
        filters = list(filter(None, filters))
        return reduce(operator.iand, filters) if filters else None

    @staticmethod
    def booking_qs(with_resource_ids=False):
        """SubBooking queryset of active time occupying bookings"""
        from webapps.booking.models import Appointment, SubBooking

        queryset = SubBooking.objects.filter(
            appointment__status__in=Appointment.STATUSES_OCCUPYING_TIME_SLOTS,
            deleted__isnull=True,
            appointment__type__in=Appointment.TYPES_BOOKABLE,
        ).order_by('booked_from')
        if with_resource_ids:
            return queryset.annotate(resource_ids=ArrayAgg('resources', default=None)).values(
                'booked_from__date', 'resource_ids'
            )

        return queryset.values('booked_from__date')

    @classmethod
    def dates_filter(cls, dates: list[date], timezone, step=1) -> Q:
        """Filter booked_from by whole days.
        ie. from date's 0:00 hours till date's 23:59.

        It is Index Scan Filter to limit query costs.

        :param dates: list of dates. If step=7 it is a list of week start dates
        :param timezone: tzinfo required to calculate hours.
        :param step: int
        """
        date_ranges = merge_ranges([(date_, date_ + timedelta(days=step)) for date_ in dates])

        return cls.ior(
            (
                cls._date_range_filter(start_date, end_date, timezone)
                for start_date, end_date in date_ranges
            )
        )

    @staticmethod
    def from_date_filter(date_: date, timezone) -> Q:
        """Filter booked_from by whole dates - start limit only

        It is Index Scan Filter to limit query costs.

        :param date_: start date
        :param timezone: tzinfo required to calculate hours.
        """
        booked_from = datetime.combine(date_, time(hour=0), tzinfo=timezone)
        return Q(booked_from__gte=booked_from)

    @classmethod
    def week_time_filter(cls, working_hours_dict: WeekHours) -> Q:
        """Time filter for non working hours by weekday

        It is Seq Scan query
        """

        # ensure all weekdays are filled
        if len(working_hours_dict) < 7:
            working_hours_dict = {dow: working_hours_dict.get(dow, []) for dow in DayOfWeek}

        return cls.ior(
            (
                cls.iand((Q(booked_from__week_day=dow + 1), cls.complement_time_filters(hours)))
                for dow, hours in working_hours_dict.items()
            )
        )

    @classmethod
    def complement_time_filters(cls, hours):
        if not hours:
            return None
        return cls.ior(
            (
                (
                    (Q(booked_from__time__gte=start) & Q(booked_from__time__lt=end))
                    | (Q(booked_till__time__gt=start) & Q(booked_till__time__lt=end))
                )
                for start, end in complement_ranges(hours)
            )
        )

    @staticmethod
    def _date_range_filter(start_date, end_date, timezone) -> Q:
        """Filter booking within date range (exclusive end)"""
        booked_from = datetime.combine(start_date, time(hour=0), tzinfo=timezone)
        booked_till = datetime.combine(end_date, time(hour=0), tzinfo=timezone)
        return Q(booked_from__gte=booked_from) & Q(booked_till__lt=booked_till)


def copy_week_schedule(
    business_id: int,
    resource_ids: list[int],
    source_start: date,
    source_end: date,
    target_dates: list[date],
    with_business: bool,
):
    """Copy schedules from source dates to target dates
    joining by day of week
    """

    if source_end < source_start:
        return

    sql = '''
    WITH source AS (
        SELECT business_id, resource_id, EXTRACT(dow from date) dow, hours
        FROM schedule_schedule
        WHERE business_id = %(business_id)s AND
            (
                resource_id in %(resource_ids)s OR
                (%(with_business)s and resource_id IS NULL)
            ) AND
            date >= %(source_start)s AND date <= %(source_end)s AND
            deleted IS NULL
    )
    INSERT INTO schedule_schedule
        (created, updated, business_id, resource_id, date, hours)
        SELECT now(), now(),
            source.business_id,
            source.resource_id,
            dt,
            source.hours
        FROM source 
        JOIN UNNEST(%(target_dates)s) dt 
            ON EXTRACT(dow from dt::date) = source.dow 
        ON CONFLICT (business_id, date, COALESCE(resource_id, 0))
            WHERE deleted IS NULL
            DO UPDATE SET hours = EXCLUDED.hours, updated=now()
    ; 
    '''
    execute_on_db(
        sql,
        dict(
            business_id=business_id,
            resource_ids=tuple(resource_ids),
            source_start=source_start,
            source_end=source_end,
            target_dates=target_dates,
            with_business=with_business,
        ),
    )


def copy_day_schedule(
    business_id: int,
    resource_ids: list[int],
    source_date: date,
    target_dates: list[date],
    with_business: bool,
):
    """Copy schedules from single date to target dates"""
    sql = '''
    WITH source AS (
        SELECT business_id, resource_id, hours
        FROM schedule_schedule
        WHERE business_id = %(business_id)s AND
            (
                resource_id in %(resource_ids)s OR
                (%(with_business)s and resource_id IS NULL)
            ) AND
            date = %(source_date)s AND
            deleted IS NULL
    )
    INSERT INTO schedule_schedule
        (created, updated, business_id, resource_id, date, hours)
        SELECT now(), now(),
            source.business_id,
            source.resource_id,
            dt,
            source.hours
        FROM source 
        JOIN UNNEST(%(target_dates)s) dt 
            ON true 
        ON CONFLICT (business_id, date, COALESCE(resource_id, 0))
            WHERE deleted IS NULL
            DO UPDATE SET hours = EXCLUDED.hours, updated=now()
    ; 
    '''
    execute_on_db(
        sql,
        dict(
            business_id=business_id,
            resource_ids=tuple(resource_ids),
            source_date=source_date,
            target_dates=target_dates,
            with_business=with_business,
        ),
    )


def get_empty_hours_df(
    resources_df: DataFrame,
    business_df: DataFrame,
    index: str = 'date',
) -> DataFrame:
    """Business hours not covered by at least one resource's hours

    :param resources_df: df with all resources hours
        DataFrame(columns=['hours'], index=[index, 'resource_id'])
    :param business_df: df with business hours
        DataFrame(columns=['hours'], index=index)
    :param index: column name to groupby and join on; 'date' or 'dow'
    :return DataFrame(columns=['hours'], index=index)
    """
    if index not in resources_df.index.names or index not in business_df.index.names:
        raise RuntimeError('missing index in dataframe')

    if index not in ['date', 'dow']:
        raise RuntimeError('wrong args')

    business_index = business_df.index.droplevel(business_df.index.names.difference([index]))
    resources_hours = resources_df.hours.groupby(index).apply(
        lambda x: merge_ranges(sorted(chain.from_iterable(x)))
    )
    empty_shifts_df = DataFrame(
        dict(
            hours=vec_subtract_ranges(business_df.hours, resources_hours),
        ),
        index=business_index,
    )
    empty_shifts_df = empty_shifts_df[empty_shifts_df.hours.str.len() > 0]

    return empty_shifts_df


def create_typed_frame(data, columns, dtypes):
    """Create DataFrame with typed columns

    Ensures to have proper types, for empty df as well.
    """
    result_df = DataFrame(data, columns=columns)
    if not result_df.size:
        result_df = DataFrame(
            {column: Series([], dtype=dtype) for column, dtype in zip(columns, dtypes)}
        )
    else:
        columns = result_df.columns
        for column_name, dtype in zip(columns, dtypes):
            column = result_df[column_name]
            if dtype not in [column.dtype, column.dtype.name]:
                result_df[column_name] = column.astype(dtype)

    return result_df


def freeze_business_opening_hours(
    business_id: int,
    start_date: date,
    end_date: date,
):
    """Make current opening hours frozen by creating schedule records

    Creates record for all dates between start_date
    and end_date (included).
    Existing schedule records are preserved.

    :param business_id: business_id

    :param start_date: freeze from start_date
    :param end_date: freeze till end_date (included)
    """
    if end_date < start_date:
        return

    sql = '''
    WITH 
    week_hours AS (
        SELECT business_id,
               monday,
               tuesday,
               wednesday,
               thursday,
               friday,
               saturday,
               sunday,
               valid_from,
               COALESCE(
                   LEAD(valid_from, 1) 
                   OVER (PARTITION BY business_id ORDER BY valid_from
               ), '9999-12-31') AS valid_till
        FROM schedule_businesshours
        WHERE business_id = %(business_id)s
    ),
    dates AS (
        SELECT dt as date, EXTRACT(dow from dt) as dow
        FROM generate_series(timestamp %(start)s, %(end)s, '1 day') dt
    )
    INSERT INTO schedule_schedule
        (created, updated, business_id, date, hours)
        SELECT now(), now(),
            wh.business_id,
            d.date,
            CASE
                WHEN d.dow = 0 THEN wh.sunday
                WHEN d.dow = 1 THEN wh.monday
                WHEN d.dow = 2 THEN wh.tuesday
                WHEN d.dow = 3 THEN wh.wednesday
                WHEN d.dow = 4 THEN wh.thursday
                WHEN d.dow = 5 THEN wh.friday
                WHEN d.dow = 6 THEN wh.saturday
            END as hours
        FROM dates d
         LEFT JOIN week_hours wh
            ON d.date >= wh.valid_from AND d.date < wh.valid_till
        ON CONFLICT (business_id, date, COALESCE(resource_id, 0))
            WHERE deleted IS NULL
            DO NOTHING
         ;
    '''
    execute_on_db(sql, dict(business_id=business_id, start=start_date, end=end_date))


def freeze_resources_working_hours(
    resource_ids: list[int],
    business_id: int,
    start_date: date,
    end_date: date,
):
    """Make current opening hours frozen by creating schedule records

    Creates record for all dates between start_date
    and end_date (included).
    Existing schedule records are preserved.

    :param business_id: business_id
    :param resource_ids: list of resource_id

    :param start_date: freeze from start_date
    :param end_date: freeze till end_date (included)
    """
    if end_date < start_date:
        return

    sql = '''
    WITH 
    week_hours AS (
        SELECT business_id,
               resource_id,
               monday,
               tuesday,
               wednesday,
               thursday,
               friday,
               saturday,
               sunday,
               valid_from,
               COALESCE(
                   LEAD(valid_from, 1) 
                   OVER (PARTITION BY business_id, resource_id ORDER BY valid_from
               ), '9999-12-31') AS valid_till
        FROM schedule_resourcehours
        WHERE business_id = %(business_id)s AND 
              resource_id in %(resource_ids)s
    ),
    dates AS (
        SELECT dt as date, EXTRACT(dow from dt) as dow, resource_id
        FROM generate_series(timestamp %(start)s, %(end)s, '1 day') dt
        CROSS JOIN UNNEST(%(resource_ids_arr)s) resource_id
    )
    INSERT INTO schedule_schedule
        (created, updated, business_id, resource_id, date, hours)
        SELECT now(), now(),
            wh.business_id,
            wh.resource_id,
            d.date,
            CASE
                WHEN d.dow = 0 THEN wh.sunday
                WHEN d.dow = 1 THEN wh.monday
                WHEN d.dow = 2 THEN wh.tuesday
                WHEN d.dow = 3 THEN wh.wednesday
                WHEN d.dow = 4 THEN wh.thursday
                WHEN d.dow = 5 THEN wh.friday
                WHEN d.dow = 6 THEN wh.saturday
            END as hours
        FROM dates d
         LEFT JOIN week_hours wh
            ON d.date >= wh.valid_from AND d.date < wh.valid_till AND 
            d.resource_id = wh.resource_id
        WHERE wh.business_id IS NOT NULL
        ON CONFLICT (business_id, date, COALESCE(resource_id, 0))
            WHERE deleted IS NULL
            DO NOTHING
    ; 
    '''
    execute_on_db(
        sql,
        dict(
            business_id=business_id,
            start=start_date,
            end=end_date,
            resource_ids=tuple(resource_ids),
            resource_ids_arr=list(resource_ids),
        ),
    )


def working_hours_query(
    *, business_id: int, resource_ids: list[int], dates: list[date], as_slots=False
):
    if not resource_ids:
        raise RuntimeError('resource_ids must not be empty')
    if not dates:
        raise RuntimeError('dates must not be empty')

    select = '''
        SELECT
        d.resource_id,
        d.date,
        d.dow,
        CASE
            WHEN d.dow = 0 THEN w.sunday
            WHEN d.dow = 1 THEN w.monday
            WHEN d.dow = 2 THEN w.tuesday
            WHEN d.dow = 3 THEN w.wednesday
            WHEN d.dow = 4 THEN w.thursday
            WHEN d.dow = 5 THEN w.friday
            WHEN d.dow = 6 THEN w.saturday
        END AS working_hours,
        s.hours AS schedule_hours,
        w.valid_from
    '''

    if as_slots:
        select = '''
            SELECT
            d.resource_id,
            d.date,
            CASE
                WHEN s.business_schedule_id IS NOT NULL THEN slots(s.hours) 
                WHEN d.dow = 0 THEN slots(w.sunday)
                WHEN d.dow = 1 THEN slots(w.monday)
                WHEN d.dow = 2 THEN slots(w.tuesday)
                WHEN d.dow = 3 THEN slots(w.wednesday)
                WHEN d.dow = 4 THEN slots(w.thursday)
                WHEN d.dow = 5 THEN slots(w.friday)
                WHEN d.dow = 6 THEN slots(w.saturday)
            END AS hours
        '''

    sql_template = '''
    WITH
    week_hours AS (
        SELECT business_id,
               resource_id,
               monday,
               tuesday,
               wednesday,
               thursday,
               friday,
               saturday,
               sunday,
               valid_from,
               COALESCE(
                   LEAD(valid_from, 1)
                   OVER (PARTITION BY business_id, resource_id
                         ORDER BY valid_from),
                   '9999-12-31'
               ) AS valid_till
        FROM schedule_resourcehours
        WHERE business_id = %(business_id)s AND
              resource_id in %(resource_ids)s
    ),
    dates AS (
        SELECT dt as date, EXTRACT(dow from dt) as dow, resource_id
        FROM unnest(%(dates)s) dt
        CROSS JOIN unnest(%(resource_ids_arr)s) resource_id
    )
    {select}
    FROM dates d
    LEFT JOIN week_hours w
        ON d.date >= w.valid_from AND
           d.date < w.valid_till AND
           d.resource_id = w.resource_id
    LEFT JOIN schedule_schedule s
        ON s.business_id = %(business_id)s AND
           s.resource_id = d.resource_id AND
           s.date = d.date AND
           s.deleted IS NULL
    ORDER BY d.date
    ;
    '''
    sql = sql_template.format(select=select)

    params = dict(
        business_id=business_id,
        resource_ids=tuple(resource_ids),
        resource_ids_arr=list(resource_ids),
        dates=list(dates),  # make sure it is a list
    )
    return fetch_db_all(sql, params)
