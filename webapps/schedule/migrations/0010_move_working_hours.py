# Generated by Django 2.2.13 on 2020-11-04 10:41

from django.db import migrations, models
import django.db.models.deletion
import lib.validators
import webapps.schedule.fields


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0301_move_working_hours'),
        ('schedule', '0009_schedule_info'),
    ]

    operations = [
        migrations.SeparateDatabaseAndState(
            state_operations=[
                migrations.CreateModel(
                    name='ResourceWorkingHours',
                    fields=[
                        (
                            'created',
                            models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)'),
                        ),
                        (
                            'updated',
                            models.DateTimeField(
                                auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                            ),
                        ),
                        (
                            'deleted',
                            models.DateTimeField(
                                blank=True, null=True, verbose_name='Deleted (UTC)'
                            ),
                        ),
                        (
                            'id',
                            models.AutoField(
                                db_column='resource_working_hours_id',
                                primary_key=True,
                                serialize=False,
                            ),
                        ),
                        (
                            'day_of_the_week',
                            models.PositiveSmallIntegerField(
                                choices=[
                                    (1, 'Monday'),
                                    (2, 'Tuesday'),
                                    (3, 'Wednesday'),
                                    (4, 'Thursday'),
                                    (5, 'Friday'),
                                    (6, 'Saturday'),
                                    (0, 'Sunday'),
                                ]
                            ),
                        ),
                        (
                            'hours',
                            webapps.schedule.fields.HoursField(
                                base_field=webapps.schedule.fields.TimeRangeField(
                                    base_field=models.TimeField(), default=list, size=2
                                ),
                                default=list,
                                size=None,
                            ),
                        ),
                        (
                            'resource',
                            models.ForeignKey(
                                on_delete=django.db.models.deletion.CASCADE,
                                related_name='working_hours',
                                to='business.Resource',
                            ),
                        ),
                    ],
                    options={
                        'verbose_name': 'Resource Working Hours',
                        'verbose_name_plural': 'Resource Working Hours',
                        'ordering': ['day_of_the_week'],
                        'unique_together': {('resource', 'day_of_the_week')},
                    },
                ),
                migrations.CreateModel(
                    name='BusinessOpeningHours',
                    fields=[
                        (
                            'created',
                            models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)'),
                        ),
                        (
                            'updated',
                            models.DateTimeField(
                                auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                            ),
                        ),
                        (
                            'deleted',
                            models.DateTimeField(
                                blank=True, null=True, verbose_name='Deleted (UTC)'
                            ),
                        ),
                        (
                            'id',
                            models.AutoField(
                                db_column='business_opening_hours_id',
                                primary_key=True,
                                serialize=False,
                            ),
                        ),
                        (
                            'day_of_the_week',
                            models.PositiveSmallIntegerField(
                                choices=[
                                    (1, 'Monday'),
                                    (2, 'Tuesday'),
                                    (3, 'Wednesday'),
                                    (4, 'Thursday'),
                                    (5, 'Friday'),
                                    (6, 'Saturday'),
                                    (0, 'Sunday'),
                                ],
                                validators=[lib.validators.validate_day_number],
                            ),
                        ),
                        (
                            'hours',
                            webapps.schedule.fields.HoursField(
                                base_field=webapps.schedule.fields.TimeRangeField(
                                    base_field=models.TimeField(), default=list, size=2
                                ),
                                default=list,
                                size=None,
                            ),
                        ),
                        (
                            'business',
                            models.ForeignKey(
                                on_delete=django.db.models.deletion.CASCADE,
                                related_name='business_opening_hours',
                                to='business.Business',
                            ),
                        ),
                    ],
                    options={
                        'verbose_name': 'Business Opening Hours',
                        'verbose_name_plural': 'Business Opening Hours',
                        'unique_together': {('business', 'day_of_the_week')},
                    },
                ),
            ],
            # tables renamed in busines 0301_move...
            database_operations=[],
        )
    ]
