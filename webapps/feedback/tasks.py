import logging

from django.conf import settings

from lib import jinja_renderer
from lib.celery_tools import celery_task
from lib.email import send_email

logger = logging.getLogger('booksy.feedback.services')


@celery_task
def SendFeedBackFormEmail(data, user_id=None):
    from webapps.booking.models import BookingSources
    from webapps import consts
    from webapps.user.models import User

    user = User.objects.get(id=user_id) if user_id else None
    full_name = user.full_name if user else u"Anonymous User"

    sjr = jinja_renderer.ScenariosJinjaRenderer()
    body = sjr.render(
        scenario_name='feedback',
        template_name='feedback',
        template_args={
            'full_name': full_name,
            'score_value': data.get('score_value', None),
            'feedback_text': data.get('feedback_text', None),
            'booksy_domain': settings.BOOKSY_DOMAIN,
            'country_code': settings.API_COUNTRY,
            'user_agent': data.get('user_agent', None),
            'x_version': data.get('x_version', None),
            'x_fingerprint': data.get('x_fingerprint', None),
            'ip': data.get('ip', None),
            'user': user,
        },
    )

    if settings.LIVE_DEPLOYMENT:
        to_addr = [
            '<EMAIL>',
        ]
    else:
        to_addr = [
            '<EMAIL>',
        ]

    source = BookingSources.objects.filter(id=data.get('source')).first()
    if source and source.name == consts.IPHONE:
        to_addr.extend(
            [
                '<EMAIL>',
                '<EMAIL>',
            ]
        )

    logger.info(body.encode('utf-8', 'ignore'))

    send_email(
        to_addr=to_addr, body=body.replace(u'\n', u'<br>'), subject=u"Feedback from: " + full_name
    )
