import decimal
import uuid

import stripe
from django.conf import settings
from django.core import validators
from django.db import models, transaction
from django.utils.translation import gettext_lazy as _

from lib.fields.phone_number import BooksyPhoneNumberField
from lib.models import (
    ArchiveModel,
    ChangeArchivedModel,
    UndeletableModel,
)
from webapps import consts
from webapps.stripe_terminal import STRIPE_HARDWARE_API_VERSION
from webapps.stripe_terminal.entities import StripeTerminalOrderEntity
from webapps.stripe_terminal.enums import (
    HARDWARE_ORDER_STRIPE_STATUS_TO_STATUS,
    PAYMENT_INTENT_STRIPE_STATUS_TO_STATUS,
    CheckoutType,
    DeprecatedStripeShippingMethodType,
    PaymentStatusType,
    ReaderType,
    StatusType,
    StripePaymentStatusType,
    StripePaymentType,
    StripeShippingMethodType,
    StripeStatusType,
)


class HardwareFeature(UndeletableModel, ArchiveModel):
    class Meta:
        verbose_name = 'Hardware feature'
        verbose_name_plural = 'Hardware features'

    id = models.AutoField(
        primary_key=True,
    )
    name = models.CharField(
        max_length=255,
        unique=True,
        null=False,
        blank=False,
    )
    description = models.TextField(
        max_length=1024,
        null=True,
        blank=True,
    )

    def __str__(self):
        return self.name


class Hardware(UndeletableModel, ArchiveModel):
    class Meta:
        verbose_name = 'Hardware'
        verbose_name_plural = 'Hardwares'
        constraints = [
            models.UniqueConstraint(
                fields=['product_type'],
                condition=models.Q(is_active=True),
                name="product_type_unique_is_active",
            ),
        ]

    id = models.AutoField(
        primary_key=True,
    )
    external_id = models.CharField(
        max_length=32,
        unique=True,
        null=True,
        blank=True,
    )
    product_type = models.CharField(
        max_length=255,
        null=True,
        blank=True,
    )
    name = models.CharField(
        max_length=255,
    )
    price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        default=None,
    )  # booksy price
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        default=None,
    )  # stripe price
    currency = models.CharField(max_length=8)
    max_per_order = models.IntegerField(blank=True, null=True)
    features = models.ManyToManyField(
        HardwareFeature,
        through='HardwareSpecification',
        through_fields=('hardware', 'feature'),
        blank=True,
    )
    image_url = models.URLField(
        blank=True,
        null=True,
        default=None,
    )
    is_active = models.BooleanField(default=False)

    @classmethod
    def refresh(cls):
        result = []
        hardware_sku_list = stripe.terminal.HardwareSku.list(
            country=str(settings.API_COUNTRY).upper(),
            stripe_version=STRIPE_HARDWARE_API_VERSION,
        ).get('data')
        hardware_product_list = stripe.terminal.HardwareProduct.list(
            stripe_version=STRIPE_HARDWARE_API_VERSION,
        ).get('data')
        with transaction.atomic():
            for hardware_sku in hardware_sku_list:
                hardware = Hardware.objects.filter(external_id=hardware_sku.get('id')).first()
                if not hardware:
                    hardware = Hardware(external_id=hardware_sku.get('id'))

                product_type = [
                    product
                    for product in hardware_product_list
                    if product['id'] == hardware_sku['product']
                ][0]['type']
                hardware.product_type = product_type
                hardware.price = hardware.price or hardware_sku.get('amount') / 100.0
                hardware.amount = hardware_sku.get('amount') / 100.0
                hardware.currency = hardware_sku.get('currency')
                hardware.max_per_order = hardware_sku.get('orderable')

                hardware.save()
                result.append(hardware)
        return result

    def __str__(self):
        return f'Hardware [{self.id}] {self.name} - {self.product_type}'


class HardwareSpecification(ChangeArchivedModel):
    class Meta:
        unique_together = [('hardware', 'feature')]
        verbose_name = 'Hardware specification'
        verbose_name_plural = 'Hardware specifications'

    id = models.AutoField(
        primary_key=True,
    )
    hardware = models.ForeignKey(
        Hardware,
        on_delete=models.CASCADE,
        null=False,
    )
    feature = models.ForeignKey(
        HardwareFeature,
        on_delete=models.CASCADE,
        null=False,
    )


class OrderItem(UndeletableModel, ArchiveModel):
    class Meta:
        verbose_name = 'Hardware order item'
        verbose_name_plural = 'Hardware order items'

    id = models.AutoField(
        primary_key=True,
    )
    order = models.ForeignKey(
        'Order',
        related_name='items',
        on_delete=models.CASCADE,
        null=False,
    )
    hardware = models.ForeignKey(
        'Hardware',
        on_delete=models.CASCADE,
        null=False,
    )
    quantity = models.IntegerField(
        null=False,
        blank=False,
    )
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        default=None,
    )


class OrderStatusHistory(ArchiveModel):
    class Meta:
        verbose_name = 'Order status history'
        verbose_name_plural = 'Order status history'

    id = models.AutoField(
        primary_key=True,
    )
    order = models.ForeignKey(
        'Order',
        related_name='statuses_history',
        on_delete=models.CASCADE,
        null=False,
    )
    status = models.CharField(
        choices=StatusType.choices(),
        max_length=20,
        null=False,
        blank=False,
        default=StatusType.PENDING,
    )
    stripe_status = models.CharField(
        choices=StripeStatusType.choices(),
        max_length=20,
        null=True,
        blank=True,
    )

    @classmethod
    def add_status_history(cls, order, stripe_status):
        order_status = HARDWARE_ORDER_STRIPE_STATUS_TO_STATUS.get(
            stripe_status,
            PaymentStatusType.PENDING,
        )
        OrderStatusHistory.objects.create(
            order=order,
            stripe_status=stripe_status,
            status=order_status,
        )


class OrderPayment(UndeletableModel, ArchiveModel):
    class Meta:
        verbose_name = 'Order payment'
        verbose_name_plural = 'Order payments'

    PaymentStatusType = PaymentStatusType
    StripePaymentStatusType = StripePaymentStatusType

    id = models.AutoField(
        primary_key=True,
    )
    external_id = models.CharField(
        max_length=32,
        unique=True,
        null=True,
        blank=True,
    )
    business_id = models.IntegerField()
    user_id = models.IntegerField(null=True)
    order = models.OneToOneField(
        'Order',
        on_delete=models.CASCADE,
        null=False,
        related_name='payment',
    )
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        default=None,
    )
    status = models.CharField(
        choices=PaymentStatusType.choices(),
        max_length=20,
        null=False,
        blank=False,
        default=PaymentStatusType.PENDING,
    )
    stripe_status = models.CharField(
        choices=StripePaymentStatusType.choices(),
        max_length=40,
        null=True,
        blank=True,
    )
    idempotency_key = models.UUIDField(
        unique=True,
        null=False,
        blank=False,
        default=uuid.uuid4,
        editable=False,
    )
    client_secret = models.CharField(
        max_length=255,
        null=True,
        blank=True,
    )

    def save(
        self,
        force_insert=False,
        force_update=False,
        using=None,
        update_fields=None,
        **kwargs,
    ):
        self.status = PAYMENT_INTENT_STRIPE_STATUS_TO_STATUS.get(
            self.stripe_status,
            PaymentStatusType.PENDING,
        )
        return super().save()

    def __str__(self):
        return f'Payment [{self.id}]'


class Order(UndeletableModel, ArchiveModel):
    class Meta:
        verbose_name = 'Order'
        verbose_name_plural = 'Orders'

    ReaderType = ReaderType
    CheckoutType = CheckoutType
    StatusType = StatusType
    DeprecatedStripeShippingMethodType = DeprecatedStripeShippingMethodType
    StripeShippingMethodType = StripeShippingMethodType
    StripePaymentType = StripePaymentType
    StripeStatusType = StripeStatusType

    id = models.AutoField(
        primary_key=True,
    )
    business_id = models.IntegerField()
    user_id = models.IntegerField(null=True)
    external_id = models.CharField(
        max_length=32,
        unique=True,
        null=True,
        blank=True,
    )
    reader_type = models.CharField(
        choices=ReaderType.choices(),
        max_length=20,
        null=True,
        blank=True,
    )
    checkout_type = models.CharField(
        choices=CheckoutType.choices(),
        max_length=20,
        null=True,
        blank=True,
    )
    shipping_company = models.CharField(
        _('company'),
        max_length=255,
        null=True,
        blank=True,
    )
    shipping_first_name = models.CharField(
        _('first name'),
        max_length=consts.FIRST_NAME_LEN,
        null=False,
        blank=False,
    )
    shipping_last_name = models.CharField(
        _('last name'),
        max_length=consts.LAST_NAME_LEN,
        null=False,
        blank=False,
    )
    billing_company = models.CharField(
        _('company'),
        max_length=255,
        null=True,
        blank=True,
    )
    billing_first_name = models.CharField(
        _('first name'),
        max_length=consts.FIRST_NAME_LEN,
        null=False,
        blank=False,
    )
    billing_last_name = models.CharField(
        _('last name'),
        max_length=consts.LAST_NAME_LEN,
        null=False,
        blank=False,
    )
    email = models.EmailField(
        _('e-mail address'),
        max_length=75,
        null=False,
        blank=False,
        validators=[validators.validate_email],
    )
    cell_phone = BooksyPhoneNumberField(
        null=False,
        blank=False,
    )
    shipping_method = models.CharField(
        choices=StripeShippingMethodType.choices() + DeprecatedStripeShippingMethodType.choices(),
        max_length=20,
        null=False,
        blank=False,
        default=StripeShippingMethodType.STANDARD,
    )
    shipping_address_line_1 = models.CharField(
        _('address line 1'),
        null=False,
        blank=False,
        max_length=consts.ADDRESS_LINE__MAX_LENGTH,
    )
    shipping_address_line_2 = models.CharField(
        _('address line 2'),
        null=True,
        blank=True,
        max_length=consts.ADDRESS_LINE__MAX_LENGTH,
    )
    shipping_city = models.CharField(
        max_length=100,
        null=False,
        blank=False,
    )
    shipping_zipcode = models.ForeignKey(
        'structure.Region',
        related_name='+',
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    shipping_state = models.ForeignKey(
        'structure.Region',
        related_name='order_shipping_state',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
    )
    copy_shipping_to_billing = models.BooleanField(
        null=False,
        blank=False,
        default=True,
    )
    billing_address_line_1 = models.CharField(
        _('address line 1'),
        null=False,
        blank=False,
        max_length=consts.ADDRESS_LINE__MAX_LENGTH,
    )
    billing_address_line_2 = models.CharField(
        _('address line 2'),
        null=True,
        blank=True,
        max_length=consts.ADDRESS_LINE__MAX_LENGTH,
    )
    billing_city = models.CharField(
        max_length=100,
        null=False,
        blank=False,
    )
    billing_zipcode = models.ForeignKey(
        'structure.Region',
        related_name='+',
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    billing_state = models.ForeignKey(
        'structure.Region',
        related_name='+',
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    billing_tax_uuid = models.CharField(
        max_length=40,
        null=True,
        blank=True,
    )
    payment_type = models.CharField(
        choices=StripePaymentType.choices(),
        max_length=20,
        null=True,
        blank=True,
        default=StripePaymentType.MONTHLY_INVOICE,
    )
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        default=None,
    )
    shipping_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        default=None,
    )
    shipment_tracking = models.JSONField(default=list)
    tax_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        default=None,
    )
    status = models.CharField(
        choices=StatusType.choices(),
        max_length=20,
        null=False,
        blank=False,
        default=StatusType.PENDING,
    )
    stripe_status = models.CharField(
        choices=StripeStatusType.choices(),
        max_length=20,
        null=True,
        blank=True,
    )
    idempotency_key = models.UUIDField(
        unique=True,
        null=False,
        blank=False,
        default=uuid.uuid4,
        editable=False,
    )

    @property
    def is_delivered(self) -> bool:
        return self.stripe_status == StripeStatusType.DELIVERED

    @property
    def name(self):
        return ' '.join([self.shipping_first_name, self.shipping_last_name])

    @property
    def total_amount(self):
        return (
            decimal.Decimal(self.amount or 0)
            + decimal.Decimal(self.shipping_amount or 0)
            + decimal.Decimal(self.tax_amount or 0)
        )

    def save(
        self, force_insert=False, force_update=False, using=None, update_fields=None, **kwargs
    ):
        self.status = HARDWARE_ORDER_STRIPE_STATUS_TO_STATUS.get(
            self.stripe_status,
            StatusType.PENDING,
        )
        if self.copy_shipping_to_billing:
            self.billing_address_line_1 = self.shipping_address_line_1
            self.billing_address_line_2 = self.shipping_address_line_2
            self.billing_city = self.shipping_city
            self.billing_zipcode = self.shipping_zipcode
            self.billing_state_id = self.shipping_state_id
        return super().save()

    @transaction.atomic
    def save_after_failed_validation(self):
        self.status = HARDWARE_ORDER_STRIPE_STATUS_TO_STATUS.get(
            self.stripe_status,
            StatusType.FAILED,
        )
        self.statuses_history.create(status=Order.StatusType.FAILED)
        return super().save()

    def __str__(self):
        return f'Order [{self.id}]'

    @property
    def entity(self):
        return StripeTerminalOrderEntity(
            id=self.id,
            business_id=int(self.business_id),
            user_id=int(self.user_id) if self.user_id else None,
            external_id=self.external_id,
            reader_type=ReaderType(self.reader_type) if self.reader_type else None,
            shipping_company=self.shipping_company,
            shipping_first_name=self.shipping_first_name,
            shipping_last_name=self.shipping_last_name,
            billing_company=self.billing_company,
            billing_first_name=self.billing_first_name,
            billing_last_name=self.billing_last_name,
            email=self.email,
            cell_phone=self.cell_phone,
            shipping_address_line_1=self.shipping_address_line_1,
            shipping_address_line_2=self.shipping_address_line_2,
            shipping_city=self.shipping_city,
            shipping_zipcode=str(self.shipping_zipcode) if self.shipping_zipcode else None,
            shipping_state=str(self.shipping_state) if self.shipping_state else None,
            billing_address_line_1=self.billing_address_line_1,
            billing_address_line_2=self.billing_address_line_2,
            billing_city=self.billing_city,
            billing_zipcode=str(self.billing_zipcode) if self.billing_zipcode else None,
            billing_state=str(self.billing_state) if self.shipping_state else None,
            amount=self.amount,
            status=self.status,
            is_delivered=self.is_delivered,
        )


class ShippingMethod(ArchiveModel):
    """
    1 instance of each method type is expected to be created (via admin panel)
    """

    method_type = models.CharField(
        max_length=30, choices=StripeShippingMethodType.choices(), unique=True
    )
    price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=False,
        blank=False,
        validators=[
            validators.MinValueValidator(0.01),
        ],
    )
