from bo_obs.datadog.enums import BooksyTeams
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from drf_api.base_views import BaseBooksySessionAPIView
from drf_api.service.business.validators.access import get_business_view_validator
from service.mixins.throttling import get_django_user_ip
from webapps.business.models import Resource
from webapps.business_consents.exceptions import (
    ConsentNotFoundException,
    FieldValidationException,
    InvalidActionException,
)
from webapps.business_consents.serializers import UpdateBusinessConsentSerializer
from webapps.business_consents.service import BusinessConsentService


class BaseAccountAPIView(BaseBooksySessionAPIView):
    permission_classes = (IsAuthenticated,)

    def validate_business(self):
        business_id = self.kwargs['business_pk']
        validator = get_business_view_validator(
            business=business_id,
            request=self.request,
            required_minimum_access_level=Resource.STAFF_ACCESS_LEVEL_OWNER,
            user=self.request.user,
        )
        validator.validate()


class ListUpdateBusinessConsentsAPIView(BaseAccountAPIView, APIView):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    def get(self, request, business_pk):
        """
        List all unfilled consents of a business
        """
        self.validate_business()

        consents = BusinessConsentService.get_business_consents(business_id=business_pk)
        generic_consents = []
        custom_consents = []
        for consent in consents:
            if consent['is_custom']:
                custom_consents.append(consent)
            else:
                generic_consents.append(consent)
        return Response(
            data={
                "consents": generic_consents,
                "custom_consents": custom_consents,
            },
            status=status.HTTP_200_OK,
        )

    def put(self, request, business_pk):
        """
        Update a consent of a business
        """
        self.validate_business()

        serializer = UpdateBusinessConsentSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        validated_data = serializer.validated_data
        consent_code = validated_data['consent_code']
        action = validated_data['action']
        payload = validated_data['payload']

        try:
            BusinessConsentService.update_business_consent(
                consent_code=consent_code,
                action=action,
                payload=payload,
                business_id=business_pk,
                user_id=request.user.id,
                ip_address=get_django_user_ip(request),
            )
            return Response(data={"code": "ok"}, status=status.HTTP_202_ACCEPTED)

        except ConsentNotFoundException:
            return Response(
                data={
                    "errors": [
                        {
                            "code": "consent_doesnt_exist",
                            "description": "Such consent does not exist!",
                        }
                    ]
                },
                status=status.HTTP_404_NOT_FOUND,
            )
        except InvalidActionException as e:
            return Response(
                data={"errors": [{"code": "invalid_action", "description": str(e)}]},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except FieldValidationException as e:
            return Response(data={"errors": e.field_errors}, status=status.HTTP_400_BAD_REQUEST)
