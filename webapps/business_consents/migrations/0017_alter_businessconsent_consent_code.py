# Generated by Django 4.2.15 on 2024-09-02 12:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("business_consents", "0016_alter_businessconsent_consent_code"),
    ]

    operations = [
        migrations.AlterField(
            model_name="businessconsent",
            name="consent_code",
            field=models.CharField(
                choices=[
                    ("FRANCE_MIGRATE_TO_STRIPE", "Migrate to Stripe on France"),
                    (
                        "FRANCE_MIGRATE_TO_STRIPE_RETRY",
                        "Migrate to Stripe on France retry",
                    ),
                    (
                        "POLAND_MIGRATE_TO_STRIPE",
                        "Migrate from Adyen to Stripe in Poland",
                    ),
                    (
                        "POLAND_MIGRATE_TO_STRIPE_V2",
                        "Migrate from Adyen to Stripe in Poland (version 2)",
                    ),
                    (
                        "POLAND_MIGRATE_TO_STRIPE_V3",
                        "Migrate from Adyen to Stripe in Poland (version 3 - 27.05)",
                    ),
                    (
                        "POLAND_MIGRATE_TO_STRIPE_V4",
                        "Migrate from Adyen to Stripe in Poland (version 4 - 03.06)",
                    ),
                    (
                        "POLAND_ONBOARD_TO_STRIPE_INDIVIDUAL",
                        "Onboard to Stripe - Individuals (peselowcy)",
                    ),
                    (
                        "POLAND_ONBOARD_TO_STRIPE_JDG",
                        "Onboard to Stripe - JDGs (from CEIDG)",
                    ),
                    (
                        "POLAND_ONBOARD_TO_STRIPE_KRS",
                        "Onboard to Stripe - Companies (from KRS)",
                    ),
                    (
                        "STRIPE_ACCOUNT_RESTRICTED_SOON",
                        "Stripe account will be restricted soon",
                    ),
                    ("US_USER_INPUT_KYC", "User Input (Effortless) KYC in US (Stripe)"),
                    (
                        "GB_MIGRATE_TO_STRIPE",
                        "Migrate from Adyen to Stripe in Great Britain",
                    ),
                    (
                        "TEST_POLAND_MIGRATE_TO_STRIPE",
                        "Migrate to Stripe in Poland (TEST)",
                    ),
                    ("SIMPLE_TEST_CONSENT", "Simple test consent"),
                    ("COMPLEX_TEST_CONSENT", "Complex test consent"),
                    ("CUSTOM_TEST_CONSENT", "Custom test consent (webview)"),
                ],
                max_length=64,
            ),
        ),
    ]
