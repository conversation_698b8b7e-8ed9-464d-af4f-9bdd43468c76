from dacite import from_dict
from django.db import transaction

from lib.events import event_receiver
from lib.french_certification.utils import french_certification_enabled
from lib.point_of_sale.entities import BasketPaymentEntity
from lib.point_of_sale.enums import (
    BasketPaymentType,
    BasketPaymentSource,
    BasketPaymentStatus,
)
from lib.point_of_sale.events import (
    basket_payment_details_updated_event,
    basket_payment_added_event,
)
from lib.pos.utils import txn_refactor_stage2_enabled
from webapps.french_certification.enums import TreasuryOperationType
from webapps.french_certification.exceptions import (
    PrepaymentForBasketExists,
    DifferentSumOfPrepaymentAmountsThanBasketPaymentAmount,
    PrepaymentExistsButNoFiscalReceipt,
    NoBasketPaymentException,
)
from webapps.french_certification.services.fiscal_receipt import FiscalReceiptService
from webapps.french_certification.services.loyalty_card import LoyaltyCardService
from webapps.french_certification.services.migration import MigrationService
from webapps.french_certification.services.treasury_operation import TreasuryOperationService
from webapps.french_certification.utils import (
    basket_fully_paid,
    basket_has_vouchers,
    get_first_basket_prepayment,
    get_basket_vouchers,
    get_basket_items,
    check_if_prepayment_for_basket_exists,
    get_basket_payments,
    get_basket_items_data_for_prepayment,
    get_data_from_basket_item,
    get_data_from_basket_payment,
)
from webapps.point_of_sale.models import Basket
from webapps.pos.models import Transaction


def create_fiscal_receipt(basket_payment):  # pylint:disable=too-many-branches,too-many-statements
    if not basket_payment:
        raise NoBasketPaymentException
    business_id = (
        Basket.objects.filter(id=basket_payment['basket_id'])
        .values_list('business_id', flat=True)
        .first()
    )
    if not french_certification_enabled(business_id):
        return

    basket_payment = from_dict(
        data_class=BasketPaymentEntity,
        data=basket_payment,
    )

    basket_id = basket_payment.basket_id

    txn = Transaction.objects.filter(
        basket_id=basket_id,
    ).last()

    if not txn_refactor_stage2_enabled(txn):
        return

    if (
        basket_payment.source == BasketPaymentSource.PREPAYMENT
        and basket_payment.status == BasketPaymentStatus.SUCCESS
    ):
        if check_if_prepayment_for_basket_exists(basket_id=basket_id):
            raise PrepaymentForBasketExists(
                f"Prepayment for bakset with the following basket ID already exists: '{basket_id}'."
            )

        basket_items_data = get_basket_items_data_for_prepayment(basket_id=basket_id)
        sum_of_prepayment_amounts = sum(
            value['prepayment_amount'] for value in basket_items_data.values()
        )
        if sum_of_prepayment_amounts != basket_payment.amount:
            raise DifferentSumOfPrepaymentAmountsThanBasketPaymentAmount(
                f"Sum Of Prepayments Amounts is different than Total Prepayment Amount."
                f"Basket ID: {basket_id}. "
                f"Sum Of Prepayments Amounts: {sum_of_prepayment_amounts}. "
                f"Total Prepayment Amount: {basket_payment.amount}."
            )
        basket_data = {
            'basket_items_data': basket_items_data,
            'basket_payments_data': {
                str(basket_payment.id): get_data_from_basket_payment(basket_payment=basket_payment)
            },
            'number_of_lines': len(basket_items_data),
            'prepayment_amount': basket_payment.amount,
        }
        FiscalReceiptService.create_fiscal_receipt(
            basket_id=basket_id,
            operator_id=txn.operator_id,
            basket_data=basket_data,
            loyalty_card_data={
                'bci_id': txn.customer_card_id,
            },
            is_prepayment=True,
        )
    elif (
        basket_payment.source == BasketPaymentSource.PAYMENT
        and basket_payment.type == BasketPaymentType.PAYMENT
        and basket_fully_paid(basket_id)
        and not basket_has_vouchers(basket_id)
    ):
        basket_payments = get_basket_payments(basket_id=basket_id, only_successful=True)
        prepayment_amount = 0
        if prepayment_basket_payment := get_first_basket_prepayment(basket_payments):
            if not MigrationService.prepayment_created_before_certification(
                prepayment_basket_payment
            ) and not check_if_prepayment_for_basket_exists(basket_id=basket_id):
                raise PrepaymentExistsButNoFiscalReceipt(
                    f"Can not create Fiscal Receipt. "
                    f"There is basket payment for Prepayment "
                    f"but no fiscal receipt for prepayment. "
                    f"Basket ID: {basket_id}. "
                )

            prepayment_amount = prepayment_basket_payment.amount
            basket_payments = [
                basket_payment
                for basket_payment in basket_payments
                if str(basket_payment.id) != str(prepayment_basket_payment.id)
            ]

        basket_items = get_basket_items(basket_id=basket_id)
        basket_data = {
            'basket_items_data': {
                str(basket_item.id): get_data_from_basket_item(
                    basket_item=basket_item,
                    prepayment_amount=0,
                )
                for basket_item in basket_items
            },
            'basket_payments_data': {
                str(db_basket_payment.id): get_data_from_basket_payment(
                    basket_payment=db_basket_payment
                )
                for db_basket_payment in basket_payments
            },
            'number_of_lines': len(basket_items),
            'prepayment_amount': prepayment_amount,
        }
        is_prepayment = False
        loyalty_card_data = LoyaltyCardService.get_loyalty_card_transaction_data(txn.id)
        if txn.customer_card_id is None or loyalty_card_data is not None:
            FiscalReceiptService.create_fiscal_receipt(
                basket_id=basket_id,
                operator_id=txn.operator_id,
                basket_data=basket_data,
                loyalty_card_data=loyalty_card_data or {},
                is_prepayment=is_prepayment,
            )
        else:
            LoyaltyCardService.cache_basket_data(
                txn.id,
                {
                    'basket_data': basket_data,
                    'basket_id': basket_id,
                    'is_prepayment': is_prepayment,
                },
            )
    elif (
        basket_payment.source == BasketPaymentSource.CANCELLATION_FEE
        and basket_payment.type == BasketPaymentType.PAYMENT
        and basket_payment.status == BasketPaymentStatus.SUCCESS
    ):
        basket_payments = get_basket_payments(basket_id=basket_id, only_successful=True)
        basket_items = get_basket_items(basket_id=basket_id).filter(item_price__gt=0)
        basket_data = {
            'basket_items_data': {
                str(basket_item.id): get_data_from_basket_item(
                    basket_item=basket_item, prepayment_amount=0
                )
                for basket_item in basket_items
            },
            'basket_payments_data': {
                str(db_basket_payment.id): get_data_from_basket_payment(
                    basket_payment=db_basket_payment
                )
                for db_basket_payment in basket_payments
            },
            'number_of_lines': len(basket_items),
            'prepayment_amount': 0,
        }
        FiscalReceiptService.create_fiscal_receipt(
            basket_id=basket_id,
            operator_id=txn.operator_id,
            basket_data=basket_data,
            loyalty_card_data={
                'bci_id': txn.customer_card_id,
            },
            is_prepayment=False,
        )


@event_receiver(basket_payment_added_event)
@event_receiver(basket_payment_details_updated_event)
def create_fiscal_receipt_receiver(basket_payment: dict, **kwargs):
    create_fiscal_receipt(basket_payment=basket_payment)


@transaction.atomic
def create_treasury_operation(basket_payment):
    business_id = (
        Basket.objects.filter(id=basket_payment['basket_id'])
        .values_list('business_id', flat=True)
        .first()
    )
    if not french_certification_enabled(business_id):
        return

    basket_payment = from_dict(
        data_class=BasketPaymentEntity,
        data=basket_payment,
    )

    basket_id = basket_payment.basket_id

    txn = Transaction.objects.filter(basket_id=basket_id).last()

    if not txn_refactor_stage2_enabled(txn):
        return

    if vouchers := get_basket_vouchers(basket_id):
        for voucher in vouchers:
            TreasuryOperationService.create_treasury_operation(
                basket_id=basket_id,
                amount=voucher.gross_total,
                operation_type=TreasuryOperationType.GIFT_CARD_PURCHASE,
                basket_payment_id=basket_payment.id,
            )


@event_receiver(basket_payment_added_event)
@event_receiver(basket_payment_details_updated_event)
def create_treasury_operation_receiver(basket_payment: dict, **kwargs):
    create_treasury_operation(basket_payment=basket_payment)
