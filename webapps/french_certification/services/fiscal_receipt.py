from django.db import transaction
from django.db.models import Q
from django.utils.translation import gettext as _

from lib.email import send_email
from lib.jinja_renderer import ScenariosJinjaRenderer
from lib.pdf_render import PDFRenderer
from webapps.french_certification.enums import (
    FiscalReceiptExportType,
    FiscalReceiptType,
)
from webapps.french_certification.exceptions import (
    InvalidFiscalReceiptOperation,
)
from webapps.french_certification.models import (
    FiscalReceipt,
    FiscalReceiptDuplicate,
    FiscalReceiptExport,
)
from webapps.french_certification.services.jet import JETService
from webapps.french_certification.services.software_version import SoftwareVersionService
from webapps.french_certification.utils import (
    get_datetime_formatter,
    get_business_id_from_basket_id,
    format_tax_rate_for_printout,
)
from webapps.point_of_sale.models import BasketItem
from webapps.pos.models import Transaction


class FiscalReceiptService:
    PRINTOUT_TEMPLATE = 'french_certification_fiscal_receipt'

    @staticmethod
    def _get_offered_basket_items_ids(basket_id):
        offered_item_discount_rate = 100
        offered_item_price = 0
        return BasketItem.objects.filter(
            Q(discount_rate=offered_item_discount_rate) | Q(item_price=offered_item_price),
            basket_id=basket_id,
        ).values_list('id', flat=True)

    @staticmethod
    def create_fiscal_receipt(
        basket_id, operator_id, basket_data, loyalty_card_data, is_prepayment=False
    ):
        offered_basket_items_ids = FiscalReceiptService._get_offered_basket_items_ids(basket_id)
        with transaction.atomic():
            business_id = get_business_id_from_basket_id(basket_id)
            fiscal_receipt = FiscalReceipt.objects.create(
                basket_id=basket_id,
                business_id=business_id,
                operator_id=operator_id,
                type=FiscalReceiptType.SALE,
                software_version=SoftwareVersionService.get_version(business_id),
                basket_data=basket_data,
                loyalty_card_data=loyalty_card_data,
                is_prepayment=is_prepayment,
            )
            for offered_basket_item_id in offered_basket_items_ids:
                JETService.offered_article_event(
                    business_id=business_id,
                    basket_item_id=offered_basket_item_id,
                    operator_id=operator_id,
                )
            return fiscal_receipt

    @staticmethod
    @transaction.atomic
    def print_fiscal_receipt(
        fiscal_receipt,
        operator_id,
        reason=None,
        generate_empty_response=False,
    ):
        FiscalReceiptService._validate_fiscal_receipt_is_not_cancelled(fiscal_receipt)
        FiscalReceiptService._handle_duplicate(fiscal_receipt, reason, operator_id)
        FiscalReceiptExport.objects.create(
            business_id=fiscal_receipt.business_id,
            fiscal_receipt=fiscal_receipt,
            type=FiscalReceiptExportType.PRINTOUT,
        )

        if generate_empty_response:
            return

        return FiscalReceiptService.get_fiscal_receipt_pdf(fiscal_receipt=fiscal_receipt)

    @staticmethod
    def get_fiscal_receipt_printout(fiscal_receipt):
        return FiscalReceiptService.get_fiscal_receipt_pdf(fiscal_receipt=fiscal_receipt)

    @staticmethod
    @transaction.atomic
    def send_fiscal_receipt_by_email(fiscal_receipt, operator_id, customer_email, reason=None):
        FiscalReceiptService._validate_fiscal_receipt_is_not_cancelled(fiscal_receipt)
        FiscalReceiptService._handle_duplicate(fiscal_receipt, reason, operator_id)
        FiscalReceiptExport.objects.create(
            business_id=fiscal_receipt.business_id,
            fiscal_receipt=fiscal_receipt,
            type=FiscalReceiptExportType.EMAIL,
        )
        send_email(
            to_addr=customer_email,
            body=FiscalReceiptService.get_fiscal_receipt_email_content(fiscal_receipt),
            attachments=FiscalReceiptService.get_fiscal_receipt_email_attachments(fiscal_receipt),
        )

    @staticmethod
    def _validate_fiscal_receipt_is_not_cancelled(fiscal_receipt):
        if fiscal_receipt.is_cancelled:
            raise InvalidFiscalReceiptOperation('Cannot export cancelled receipt')

    @staticmethod
    def _handle_duplicate(fiscal_receipt, reason, operator_id):
        if FiscalReceiptExport.objects.filter(fiscal_receipt=fiscal_receipt).exists():
            if not reason:
                raise InvalidFiscalReceiptOperation('Cannot create a duplicate without reason')
            reprint_number = (
                FiscalReceiptDuplicate.objects.filter(original_document=fiscal_receipt).count() + 1
            )
            FiscalReceiptDuplicate.objects.create(
                original_document=fiscal_receipt,
                reprint_number=reprint_number,
                business_id=fiscal_receipt.business_id,
                software_version=str(
                    SoftwareVersionService.get_version(fiscal_receipt.business_id)
                ),
                operator_id=operator_id,
                reason=reason,
            )

    @staticmethod
    def get_fiscal_receipt_email_content(fiscal_receipt):
        sjr = ScenariosJinjaRenderer()
        return sjr.render(
            scenario_name='french_certification',
            template_name='fiscal_receipt',
            template_args={'fiscal_receipt': fiscal_receipt},
        )

    @staticmethod
    def get_fiscal_receipt_email_attachments(fiscal_receipt):
        pdf_content = FiscalReceiptService.get_fiscal_receipt_pdf(fiscal_receipt)
        return [(f'{_("Receipt")} {fiscal_receipt.number}.pdf', pdf_content, 'application/pdf')]

    @staticmethod
    def get_fiscal_receipt_pdf(fiscal_receipt):
        from webapps.french_certification.serializers import FiscalReceiptPrintSerializer

        serializer = FiscalReceiptPrintSerializer(fiscal_receipt)
        return PDFRenderer.render_pdf(
            template_name=FiscalReceiptService.PRINTOUT_TEMPLATE,
            data=serializer.data,
            format_datetime=get_datetime_formatter(fiscal_receipt=fiscal_receipt),
            format_tax_rate=format_tax_rate_for_printout,
        )

    @staticmethod
    def cancel_fiscal_receipt(
        fiscal_receipt,
        reason,
        operator_id,
    ):
        if fiscal_receipt.type == FiscalReceiptType.CANCELLATION:
            raise InvalidFiscalReceiptOperation('Cannot cancel a cancellation')

        if fiscal_receipt.is_cancelled:
            raise InvalidFiscalReceiptOperation('Cancellation already exists')

        with transaction.atomic():
            cancellation = FiscalReceipt.objects.create(
                basket_id=fiscal_receipt.basket_id,
                business_id=fiscal_receipt.business_id,
                operator_id=operator_id,
                type=FiscalReceiptType.CANCELLATION,
                cancelled_sale=fiscal_receipt,
                cancellation_reason=reason,
                loyalty_card_data=fiscal_receipt.loyalty_card_data,
                software_version=str(
                    SoftwareVersionService.get_version(fiscal_receipt.business_id)
                ),
                basket_data=fiscal_receipt.basket_data,
                is_prepayment=fiscal_receipt.is_prepayment,
            )
            JETService.fiscal_receipt_cancellation_event(
                business_id=fiscal_receipt.business_id,
                fiscal_receipt=fiscal_receipt,
                reason=reason,
                operator_id=operator_id,
            )
            return cancellation

    @staticmethod
    def fiscal_receipt_sale_from_transaction_id(transaction_id):
        basket_id = Transaction.objects.values_list('basket_id', flat=True).get(id=transaction_id)
        if basket_id:
            fiscal_receipt = FiscalReceipt.objects.filter(
                basket_id=basket_id,
                type=FiscalReceiptType.SALE,
            ).first()
        else:
            fiscal_receipt = None
        return fiscal_receipt

    @staticmethod
    def business_has_fiscal_receipts(business_id):
        return FiscalReceipt.objects.filter(business_id=business_id).exists()
