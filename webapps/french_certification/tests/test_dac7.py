from datetime import date
from unittest.mock import patch

import pytest
from django.db import transaction
from freezegun import freeze_time
from model_bakery import baker

from webapps.french_certification.dac7 import (
    FiscalReceipt,
    report,
)
from webapps.french_certification.enums import FiscalReceiptType

receipts_per_business_per_day = (
    {
        "id": 1,
        "receipts": {
            "2024-01-01": [{}],
            "2024-01-02": 5 * [{}],
        },
    },
    {
        "id": 2,
        "receipts": {
            "2024-01-01": [
                {},
                {"value": 5000},
            ],
            "2024-01-02": 2 * [{}],
        },
    },
    {
        "id": 3,
        "receipts": {
            "2024-01-01": [],
            "2024-01-02": 3 * [{}],
            "2024-01-03": [
                {},
                {
                    "receipt_type": FiscalReceiptType.CANCELLATION,
                    "value": 2500,
                },
            ],
            "2024-01-04": 3 * [{"receipt_type": FiscalReceiptType.CANCELLATION}],
        },
    },
)


@pytest.fixture
@transaction.atomic
def fakedata() -> list[FiscalReceipt]:
    fiscal_receipts: list[FiscalReceipt] = []
    for bizdata in receipts_per_business_per_day:
        for receiptdate, receiptdata in bizdata.get("receipts", {}).items():
            with freeze_time(receiptdate, tick=True):
                for data in receiptdata:
                    fiscal_receipts.append(
                        baker.make(
                            FiscalReceipt,
                            business_id=bizdata["id"],
                            total_paid_value=data.get("value", 10000),
                            type=data.get("receipt_type", FiscalReceiptType.SALE),
                        )
                    )
    return fiscal_receipts


@pytest.mark.django_db
def test_report__gives_amount_and_volume_per_day_and_business(fakedata: list[FiscalReceipt]):
    with (
        patch("webapps.french_certification.dac7.get_customer_appointments_ids_adapter"),
        patch(
            "webapps.french_certification.dac7.get_basket_ids_for_appointments_adapter",
        ) as mock_baskets,
    ):
        mock_baskets.return_value = [fr.basket_id for fr in fakedata]
        assert list(report()) == [
            {"day": date(2024, 1, 1), "business_id": 1, "volume": 1, "amount": 10000},
            {"day": date(2024, 1, 1), "business_id": 2, "volume": 2, "amount": 15000},
            {"day": date(2024, 1, 2), "business_id": 1, "volume": 5, "amount": 50000},
            {"day": date(2024, 1, 2), "business_id": 2, "volume": 2, "amount": 20000},
            {"day": date(2024, 1, 2), "business_id": 3, "volume": 3, "amount": 30000},
            {"day": date(2024, 1, 3), "business_id": 3, "volume": 2, "amount": 7500},
            {"day": date(2024, 1, 4), "business_id": 3, "volume": 3, "amount": -30000},
        ]
