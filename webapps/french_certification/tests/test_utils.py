import datetime
from typing import cast

from django.test import TestCase
from model_bakery import baker
from webapps.business.models import Business
from webapps.french_certification.tests.common import (
    create_data_initialization_event,
    bulk_close_day,
)
from webapps.french_certification.enums import ClosePeriodType
from webapps.french_certification.utils import (
    get_grand_total_for_last_closed,
)


class TestUtils(TestCase):
    def test_get_next_day_to_close_change_time(self):
        """
        There was a bug that calculated next day for closing wrong if there was a DST time change.
        This test reproduces the issue.
        """
        business = baker.make(Business, time_zone_name='Europe/Paris')
        # please don't change these dates, 29.10.2023 3am is when Europe/Paris time changed
        start_closing_from = datetime.datetime(2023, 10, 28, tzinfo=business.get_timezone())
        first_closed_day = datetime.datetime(2023, 10, 29, tzinfo=business.get_timezone())
        create_data_initialization_event(business.id, start_closing_from)
        bulk_close_day(business, [start_closing_from, first_closed_day])

        next_day = cast(
            datetime,
            get_grand_total_for_last_closed(ClosePeriodType.DAY, business).period_end.astimezone(
                business.get_timezone()
            ),
        )

        self.assertEqual(datetime.datetime(2023, 10, 30, tzinfo=business.get_timezone()), next_day)
