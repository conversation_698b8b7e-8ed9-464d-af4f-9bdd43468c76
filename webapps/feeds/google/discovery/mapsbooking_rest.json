{"canonicalName": "Google Maps Booking API", "ownerName": "Google", "documentationLink": "https://developers.google.com", "icons": {"x32": "http://www.google.com/images/icons/product/search-32.gif", "x16": "http://www.google.com/images/icons/product/search-16.gif"}, "title": "Google Maps Booking API", "kind": "discovery#restDescription", "parameters": {"alt": {"description": "Data format for response.", "location": "query", "type": "string", "enum": ["json", "media", "proto"], "default": "json", "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"]}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "type": "string", "location": "query"}, "$.xgafv": {"enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "type": "string", "location": "query", "description": "V1 error format."}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "type": "string", "location": "query"}, "uploadType": {"type": "string", "location": "query", "description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\")."}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "type": "string", "location": "query"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "upload_protocol": {"type": "string", "location": "query", "description": "Upload protocol for media (e.g. \"raw\", \"multipart\")."}, "callback": {"location": "query", "type": "string", "description": "JSONP"}, "oauth_token": {"location": "query", "type": "string", "description": "OAuth 2.0 token for the current user."}, "prettyPrint": {"location": "query", "description": "Returns response with indentations and line breaks.", "type": "boolean", "default": "true"}}, "version_module": true, "ownerDomain": "google.com", "baseUrl": "https://mapsbooking.googleapis.com/", "batchPath": "batch", "fullyEncodeReservedExpansion": true, "mtlsRootUrl": "https://mapsbooking.mtls.googleapis.com/", "discoveryVersion": "v1", "version": "v1alpha", "protocol": "rest", "basePath": "", "revision": "20200909", "id": "mapsbooking:v1alpha", "rootUrl": "https://mapsbooking.googleapis.com/", "name": "mapsbooking", "schemas": {"Service": {"description": "Info about a service that is provided by the merchant, e.g. haircut.", "id": "Service", "properties": {"integrationType": {"enumDescriptions": ["Defaults to END_TO_END.", "Complete integration that allows end to end booking through Google.", "Booking server doesn’t need to support this service. Only merchants, services, and (optionally) availability data need to be sent."], "description": "Depth of integration we support for this service. (optional) Irrelevant for partners with the starter integration. End to end will always be disabled for these partners.", "enum": ["INTEGRATION_TYPE_UNSPECIFIED", "INTEGRATION_TYPE_END_TO_END", "INTEGRATION_TYPE_INVENTORY_ONLY"], "type": "string"}, "serviceAttributeValueId": {"items": {"$ref": "ServiceAttributeValueId"}, "description": "Service attribute values that apply to this service (optional). Each Service may have zero or more values for each service attribute defined in the corresponding Merchant. (optional)", "type": "array"}, "prepaymentTerms": {"$ref": "PrepaymentTerms", "description": "Terms around when the prepayment is completed."}, "form": {"items": {"$ref": "ServiceIntakeForm"}, "description": "Deprecated. Please use intake_form and per_ticket_intake_form.", "type": "array"}, "homeServiceData": {"description": "Additional information unique to home service vertical. (optional)", "$ref": "HomeServiceData"}, "deposit": {"$ref": "<PERSON><PERSON><PERSON><PERSON>", "description": "Defines how a deposit may be charged to the user. Overrides the service deposit if one was specified. Setting this to an empty Deposit message removes any service-level deposit. (optional)"}, "actionLink": {"description": "An action link related to this service. If action link exists, type (see below) must be set in the Service.", "items": {"$ref": "ActionLink"}, "type": "array"}, "name": {"type": "string", "description": "The service resource name, which has the format of `partners/{partner_id}/merchants/{merchant_id}/services/{service_id}`."}, "rating": {"description": "User rating for this service as an aggregate metric over all reviews.", "$ref": "Rating"}, "location": {"type": "array", "items": {"$ref": "Location"}, "description": "Locations related to this service. IMPORTANT NOTES: If there are multiple visited locations related to this service, or the START_LOCATION is different from the VISITED_LOCATION, the START_LOCATION must be specified. Example: - A guided biking tour visiting three venues, the start venue needs to be specified. - A bus tour meeting at a hotel lobby and then head to the visited venue. The meeting location needs to be specified."}, "intakeForm": {"$ref": "ServiceIntakeForm", "description": "A form requesting additional information from the user when they book this service. (optional)"}, "type": {"description": "The predefined type of this service. (optional)", "enum": ["SERVICE_TYPE_UNSPECIFIED", "SERVICE_TYPE_DINING_RESERVATION", "SERVICE_TYPE_FOOD_ORDERING", "SERVICE_TYPE_FOOD_DELIVERY", "SERVICE_TYPE_FOOD_TAKEOUT", "SERVICE_TYPE_EVENT_TICKET", "SERVICE_TYPE_TRIP_TOUR", "SERVICE_TYPE_APPOINTMENT", "SERVICE_TYPE_ONLINE_APPOINTMENT", "SERVICE_TYPE_SHOPPING"], "type": "string", "enumDescriptions": ["Unused.", "Dining reservation.", "Food ordering, could be either food delivery or takeout or both.", "Food delivery.", "Food takeout.", "Event ticket.", "Trip tour.", "Service that provides appointments or classes. Recommended for (1) health and fitness, (2) spa and beauty, and (3) financial consults and evaluations services. Please see the supported service types: https://developers.google.com/maps-booking/guides/end-to-end-integration/overview", "Service that provides online appointment for a class or session which will be fully virtual. Must be set if enabling virtual service bookings.", "Service that allows users to shop from the given merchant. It could either be delivery or pickup."]}, "prepaymentType": {"enum": ["PREPAYMENT_TYPE_UNSPECIFIED", "REQUIRED", "OPTIONAL", "NOT_SUPPORTED"], "type": "string", "enumDescriptions": ["By default we will assume that the prepayment is NOT_SUPPORTED.", "The user has to pay this service at the booking time.", "The user can choose to pre-pay this service at the booking time or later, but it is not required in order to book.", "The prepayment is not supported for this service."], "description": "Whether a prepayment is required, optional or not supported."}, "noShowFee": {"$ref": "NoShowFee", "description": "Defines a no show fee that may be charged to the user. Overrides the service no show fee if one was specified. Setting this to an empty NoShowFee message removes any service-level no show fee. (optional)"}, "description": {"type": "string", "description": "The user-visible description of the service. Deprecated, use localized_description instead."}, "relatedMedia": {"type": "array", "items": {"$ref": "RelatedMedia"}, "description": "Photos related to this service. Google will crawl and store the media to ensure that they are displayed to end-users in the most efficient way. (optional)"}, "localizedDescription": {"description": "The user-visible description of the service. This field supports both plain-text and HTML-like formatting. Unlike plain text sections, customized layouts can be created here using headings, paragraphs, lists and some phrase tags. Please read the following instructions and notes carefully to ensure you create the best user-experience. Supported HTML-like formatting tags: Heading tags: <h1>, <h2>, <h3>, <h4>, <h5>, <h6> Heading tags can be used to display titles and sub-titles. For example, <h1>Itinerary</h1> will display the inline text as the most important heading of the section. Note that any inner HTML tags, styles or attributes will be ignored. For example, <h1 style=\"..\"> will be treated the same as <h1>. Only pure text wil be preserved. Paragraph tag: <p> The paragraph tag can be used to highlight a detailed introduction or contents. Any inner tags, styles or attributes will be ignored, with a few exceptions: <br>, <strong> and <em>. Please see the phrase tag section below for more details. List tags: <ul>, <ol>, <li> The <ul> tag can be used with the <li> tag to display unordered lists, and the <ol> tag can be used with <li> to display ordered lists. This is a good way to display checklists, schedules, or any other lists that fit your use-cases. Example: To show a list of features of a cruise trip: <ol> <li>Wonderful ocean view and chances to play with wildlife.</li> <li>Carefully designed travel arrangements and services.</li> <li>Gauranteed lowest price.</li> </ol> Note that only <li> children under <ul> or <ol> tags will be converted. All other children will be dropped. Also, any inner tags, attributes and styles will be ignored; we only preserve pure text contents. Division tag: <div> All supported inner tags of the <div> tag will be parsed with the rules stated above, imply <div> tag itself does not mean any grouping or indenting here. Also, any inner attributes and styles will be ignored. Phrase tags: <br>, <strong>, <em>: Only the three tags mentioned above are supported. <br> can be used to break lines in paragraphs, and <strong>/<em> can be used to highlight important text. Any other phrase tags will be ignored. Unsupported tags: * <html>, <header>, and <body> tags are not allowed. * Any other tags not mentioned above are not supported (for example <table>, <td> ...). Any URLs, anchors, and links will be stripped, and will never be displayed to end-users. If you want to use photos to create a rich user experience, please use the \"related_media\" field below to send your photo URLs. Important notes: * Try not to use other tags except for the supported ones mentioned above, because the contents within unsupported tags will be stripped, and may lead to an undesirable user experience. * Try avoid deep nested structures like more than 3 different heading levels or nested lists. Keeping the structure flat, simple, and straightforward, helps to create a better user experience. * If the currently supported layouts are not sufficient for your use cases, please reach out to the Reserve with Google team. * The recommended maximum size is 32,000 characters.", "$ref": "Text"}, "perTicketIntakeForm": {"description": "A form requesting additional information from the user when they book this service. This form must be filled out once for each ticket the user is booking. (optional)", "$ref": "ServiceIntakeForm"}, "ticketType": {"type": "array", "items": {"$ref": "TicketType"}, "description": "Types of tickets that can be booked/purchased for this service, if tickets are supported. (optional)"}, "rules": {"$ref": "SchedulingRules", "description": "Rules to book/cancel an appointment."}, "virtualSession": {"$ref": "VirtualSession", "description": "Optional. Information about virtual session. It is required for enabling virtual services."}, "taxRate": {"description": "The service's tax rate. If present this field overrides any tax_rate set at the merchant level. An empty message (i.e. tax_rate { }) will reset the applied tax rate to zero.", "$ref": "TaxRate"}, "perOrderFee": {"description": "Order level fees for purchasing this service. (optional)", "$ref": "PerOrder<PERSON><PERSON>"}, "priceInterpretation": {"type": "string", "enumDescriptions": ["Price interpretation unspecified, defaults to EXACT_AMOUNT.", "When the price should be interpreted as a specific value. Examples: $20 for a yoga class; $15 for a child haircut", "When the price of a service is variable but a minimum price is known and displayed to consumers. Consumers may make choices which increase the price. Note that any service that uses this PriceInterpretation must use PrepaymentType NOT_SUPPORTED. Examples: $30 for dog grooming, but additional consumer choices may increase the price", "When the price of a service is variable and no price information is displayed to consumers ahead of time. Note that any service that uses this PriceInterpretation must use PrepaymentType NOT_SUPPORTED and Price must be empty. Examples: A consultation for a home service"], "enum": ["PRICE_INTERPRETATION_UNSPECIFIED", "EXACT_AMOUNT", "STARTS_AT", "NOT_DISPLAYED"], "description": "Describes how the price is interpreted and displayed to the user. Can be used by any vertical except Dining and Things To Do to configure display of the service price."}, "directMerchantPayment": {"description": "Optional. Additional information which needs to be added if the service requires the user to pay directly to the merchant. IMPORTANT NOTE: RwG would not be involved in this transaction. It is required if virtual_session is defined and the service is not free or prepayment_type is NOT set to REQUIRED.", "$ref": "DirectMerchantPayment"}, "paymentOptionId": {"type": "array", "items": {"type": "string"}, "description": "A list of ids referencing the payment options which can be used to pay for this service. The actual payment options are defined at the Merchant level, and can also be shared among multiple Merchants."}, "price": {"description": "The price of the service.", "$ref": "Price"}, "toursAndActivitiesContent": {"$ref": "ToursAndActivitiesContent", "description": "Content fields specific to Tours and Activities."}, "ticketingVerticalSpecificData": {"$ref": "TicketingVerticalSpecificData", "description": "Additional information unique to the event ticketing vertical. (optional)"}, "serviceName": {"description": "The name of the service, e.g. \"Men's haircut\". Deprecated, use localized_service_name instead.", "type": "string"}, "requireCreditCard": {"description": "Indicates whether the user must provide a credit card in order to book this service. This field can be overridden at the availability level. (optional)", "enum": ["REQUIRE_CREDIT_CARD_UNSPECIFIED", "REQUIRE_CREDIT_CARD_CONDITIONAL", "REQUIRE_CREDIT_CARD_ALWAYS"], "enumDescriptions": ["The credit card requirement is not explicitly specified and the behaviour is identical to the one specified for CONDITIONAL.", "Google will require a credit card for the booking if any of the following conditions are met: * the availability has a price and the prepayment_type is REQUIRED * the no_show_fee is set * the deposit field is set.", "A credit card is always required in order to book this availability regardless of other field values."], "type": "string"}, "waitlistRules": {"$ref": "WaitlistRules", "description": "Rules to joining the waitlist."}, "localizedServiceName": {"$ref": "Text", "description": "The name of the service, e.g. \"Men's haircut\". Possibly in several locales."}}, "type": "object"}, "FeedStatistics": {"description": "Statistics obtained while processing an uploaded feed.", "id": "FeedStatistics", "type": "object", "properties": {"deletedItems": {"type": "string", "format": "int64", "description": "Items no longer present in this feed, and that were removed as a result."}, "existingItems": {"type": "string", "format": "int64", "description": "Existing items, updated as needed from newer information from the feed."}, "newItems": {"format": "int64", "description": "Newly added items by this feed. Items can be merchants, services or availability slots, depending on the type of the feed.", "type": "string"}}}, "TimeRange": {"type": "object", "properties": {"startTime": {"format": "google-datetime", "type": "string", "description": "The lower bound of the time range."}, "endTime": {"type": "string", "description": "The upper bound of the time range.", "format": "google-datetime"}}, "description": "A closed-open time range, i.e. [start_time, end_time).", "id": "TimeRange"}, "ScheduleException": {"properties": {"timeRange": {"description": "The time range of the exception. Any slots described by the recurrence which overlap this closed-open time range will be considered unavailable. Example: If the recurrence specifies a duration of 20 min, a repeat_every of 30 min, a start_time of 9:00am, and a repeat_until of 11:00am, then a ScheduleException with a time_range of 9:45am-11:00am would make unavailable the slots at 9:30-9:50am, 10-10:20am, and 10:30-10:50am. Note that because the time range is closed-open, the slot beginning at 11am slot would not be impacted.", "$ref": "TimeRange"}}, "id": "ScheduleException", "description": "ScheduleException messages represent booked/unavailable time ranges within the workday, which are exceptions to the recurrence described above. As time slots are booked, the list of exceptions should be updated to reflect the newly unavailable time ranges. The recurrence itself shouldn't be modified.", "type": "object"}, "PublicIdentificationData": {"id": "PublicIdentificationData", "properties": {"musicbrainzId": {"type": "string", "description": "The 36-character musicbrainz identifier of the artist or other music entities, if applicable. See https://musicbrainz.org/doc/MusicBrainz_Identifier. (optional)"}, "relevantUrl": {"description": "Public URL of any webpage that is dedicated to only the topic. This could include official websites, discogs, social media platforms, wikipedia or imdb pages, e.g. https://www.discogs.com/artist/1124645-<PERSON>-<PERSON>, https://www.wikidata.org/wiki/Q19320959, https://twitter.com/acmilan. (optional)", "type": "array", "items": {"type": "string"}}}, "description": "Identifiers, webpages, or any other public sources that refernece an entity.", "type": "object"}, "RelatedMedia": {"description": "Photos related to this service. Google will crawl these media to ensure that they are displayed correctly to end-users. (optional)", "type": "object", "properties": {"url": {"description": "URL of this media source. Google will crawl the media hosted at this URL.", "type": "string"}, "attribution": {"$ref": "Attribution", "description": "Attribution information about the source of the media. Note that if the attribution is required to display with the media to give credit to photographer or agency, this field must be set. (optional)"}, "caption": {"description": "Deprecated, prefer to use localized_caption.", "type": "string"}, "type": {"type": "string", "enumDescriptions": ["Unused.", "Indicates the media provided by the url is a photo."], "description": "Type of this media source.", "enum": ["TYPE_UNSPECIFIED", "PHOTO"]}, "localizedCaption": {"$ref": "Text", "description": "Caption of the media that supports i18n, only plain text is supported. Any HTML components will be stripped. (optional)"}}, "id": "RelatedMedia"}, "PaymentOption": {"properties": {"name": {"type": "string", "description": "The name of the payment option. This can be user visible."}, "validDuration": {"description": "Duration of the payment option validity (e.g. 30 day membership).", "type": "string", "format": "google-duration"}, "sessionCount": {"format": "int64", "type": "string", "description": "How many sessions this payment option can be used for. Valid only for multi-session / packs, where the value should be > 1."}, "paymentOptionType": {"type": "string", "enum": ["PAYMENT_OPTION_TYPE_UNSPECIFIED", "PAYMENT_OPTION_SINGLE_USE", "PAYMENT_OPTION_MULTI_USE", "PAYMENT_OPTION_UNLIMITED"], "enumDescriptions": ["Unused.", "Payment option can only be used once.", "Payment option can be used if its session count > 0.", "Payment option can be used within its valid time range - session count is inapplicable."], "description": "The type of this payment option. Single-use for drop-ins, multi-use for packs, and unlimited for memberships."}, "paymentOptionId": {"type": "string", "description": "This ID is used to identify this payment option. This ID is global to the whole aggregator, and re-using a value across multiple merchants will allow a user to pay with the corresponding payment option across those merchants. When re-using an ID accoss multiple merchants, updating any value for a payment option under one merchant will also update any other payment option with the same ID, under a different merchant. As such, it's a best practice to have all payment options sharing the same ID, always be updated to identical values, to avoid any possibility of underministic behavior."}, "taxRate": {"description": "The tax rate for this payment option. If present this field overrides the tax_rate field present in the Merchant or Service. An empty message (i.e. tax_rate { }) will reset the applied tax rate to zero.", "$ref": "TaxRate"}, "userRestriction": {"description": "Restricts the users eligible to purchase this payment option. Can be used to restrict a promotional payment option to a subset of users. If not set, all users are eligible.", "$ref": "UserPurchaseRestriction"}, "activationType": {"description": "Defines how the validity start date is determined for this payment option.", "enumDescriptions": ["Unused.", "Validity starts at the time of purchase.", "Validity starts when the payment option is used for the first time."], "type": "string", "enum": ["ACTIVATION_TYPE_UNSPECIFIED", "ACTIVATION_ON_PURCHASE", "ACTIVATION_ON_FIRST_USE"]}, "description": {"description": "A description of the payment option. This can be user visible.", "type": "string"}, "purchaseInterval": {"$ref": "TimeRange", "description": "The payment option can be purchased within this interval."}, "price": {"$ref": "Price", "description": "The price of the payment option."}, "validInterval": {"$ref": "TimeRange", "description": "The payment option can be used within this interval (e.g. special price for January 2017). If present, this overrides valid_duration and activation_type."}}, "id": "PaymentOption", "description": "A payment option, which can be used to pay for services provided by a merchant. Payment options can be shared among multiple merchants (e.g. merchants belonging to the same chain).", "type": "object"}, "LocalizedString": {"id": "LocalizedString", "properties": {"value": {"description": "Message in the locale above (UTF-8).", "type": "string"}, "locale": {"description": "IETF BCP 47 language code, such as \"en\", \"mas\", \"zh-Hant\", \"de-CH-1901\". See http://www.w3.org/International/articles/language-tags/.", "type": "string"}}, "type": "object", "description": "Instance of a string in one locale."}, "Text": {"id": "Text", "description": "A possibly-localized text payload. Some Text fields may contain marked-up content.", "type": "object", "properties": {"localizedValue": {"type": "array", "items": {"$ref": "LocalizedString"}, "description": "Per-locale text values. Optional."}, "value": {"description": "Text value in an unknown locale. Required if and only if `localized_value` is empty. The locale for this value may depend on the partner or service provider -- it should not be assumed to be any specific language.", "type": "string"}}}, "CreditCardRestrictions": {"type": "object", "id": "CreditCardRestrictions", "properties": {"creditCardType": {"items": {"enumDescriptions": ["Unused.", "A Visa credit card.", "A Mastercard credit card.", "An American Express credit card.", "A Discover credit card.", "A JCB credit card."], "enum": ["CREDIT_CARD_TYPE_UNSPECIFIED", "VISA", "MASTERCARD", "AMERICAN_EXPRESS", "DISCOVER", "JCB"], "type": "string"}, "description": "A list of supported credit cards. No credit cards are supported if empty.", "type": "array"}}, "description": "Restrictions to the credit card types this merchant accepts."}, "SchedulingRules": {"type": "object", "description": "The scheduling rules for a service.", "properties": {"cancellationPolicy": {"$ref": "CancellationPolicy", "description": "Scheduling rules cancellation policy. (required for Things-to-do)"}, "minBookingBufferBeforeEndTime": {"format": "int64", "type": "string", "description": "The duration (in seconds) from when the last booking can be made to when the availability slot ends. If this field is set, the \"admission_policy\" field must be set to TIME_FLEXIBLE to indicate that users can use the purchased tickets after slots start."}, "lateCancellationFee": {"description": "The fee for canceling within the minimum advance notice period.", "$ref": "Price"}, "minAdvanceBooking": {"description": "The duration (in seconds) from when the last booking can be made to when the availability slot starts.", "type": "string", "format": "int64"}, "noshowFee": {"$ref": "Price", "description": "The fee for no-show without canceling."}, "admissionPolicy": {"enum": ["ADMISSION_POLICY_UNSPECIFIED", "TIME_STRICT", "TIME_FLEXIBLE", "TIMED_ENTRY_WITH_FLEXIBLE_DURATION"], "description": "The admission policy that applies to this service. If unset, defaults to TIME_STRICT. (optional)", "enumDescriptions": ["Unused.", "Customers are required to be present at the start time of the availability slot, and the service is expected to finish at the end time of the slot. Examples of TIME_STRICT use cases: * A tour that starts at 9am that requires all attendees to arrive at the start time, and returns at around 12pm. * A haircut reservation at 3pm on Saturday that will take approximately 30 minutes. * A fitness class from 6pm to 8pm.", "Customers can arrive at any time between the start and end time of the availability slot to use this booking. Examples of TIME_FLEXIBLE use cases: * A museum ticket that can be used during any time on the purchase date. * An afternoon admission to an amusement park that can be used from 12pm to 9pm.", "Customers need to arrive at the merchant at the start time of the availability slot but can leave any time they want. For example, in the museum admission scenario, a timed entry ticket for 10am requires the user to be at the museum at 10am. The start time of availability slots for this service represents the designated entry time. The end time, however, is used solely as a key to identify the availability slot for booking."], "type": "string"}, "minAdvanceOnlineCanceling": {"format": "int64", "type": "string", "description": "The minimum advance notice in seconds required to cancel a booked appointment online. (optional)"}}, "id": "SchedulingRules"}, "ActionLink": {"description": "An action URL with associated language, list of countries restricted to, and optional platform that indicates which platform this action should be performed on.", "id": "ActionLink", "type": "object", "properties": {"restrictedCountry": {"items": {"type": "string"}, "type": "array", "description": "ISO 3166-1 alpha-2 country code. Leave empty for unrestricted visibility."}, "language": {"description": "The BCP-47 language tag identifying the language in which the content from this URI is available.", "type": "string"}, "actionLinkType": {"type": "string", "enum": ["ACTION_LINK_TYPE_UNSPECIFIED", "ACTION_LINK_TYPE_BOOK_APPOINTMENT", "ACTION_LINK_TYPE_BOOK_ONLINE_APPOINTMENT", "ACTION_LINK_TYPE_ORDER_FOOD", "ACTION_LINK_TYPE_ORDER_FOOD_DELIVERY", "ACTION_LINK_TYPE_ORDER_FOOD_TAKEOUT", "ACTION_LINK_TYPE_MAKE_DINING_RESERVATION", "ACTION_LINK_TYPE_SHOP_ONLINE"], "description": "Predetermined type of action.", "enumDescriptions": ["The action link type is unspecified.", "The action link type is booking an appointment.", "The action link type is booking an online appointment.", "The action link type is ordering food for delivery or takeout or both.", "The action link type is ordering food for delivery.", "The action link type is ordering food for takeout.", "The action link type is making a dining reservation.", "The action link type is ordering for shopping, could be deliver or pickup or both."]}, "url": {"type": "string", "description": "The entry point URL for this action link."}, "platform": {"enum": ["ACTION_PLATFORM_UNSPECIFIED", "ACTION_PLATFORM_WEB_APPLICATION", "ACTION_PLATFORM_MOBILE_WEB", "ACTION_PLATFORM_ANDROID", "ACTION_PLATFORM_IOS"], "type": "string", "enumDescriptions": ["The platform is unspecified.", "The action platform is web in general.", "The action platform is web on mobile devices.", "The action platform is Android OS.", "The action platform is iOS."], "description": "The platform that this action should be performed on. If this field is unset, ACTION_PLATFORM_WEB_APPLICATION will be used as fallback."}}}, "ServiceAvailability": {"properties": {"resourcesRestrict": {"$ref": "Resources", "description": "Setting resources_restrict further restricts the scope of the update to just this set of resources. All id fields of the resources must match exactly."}, "endTimeRestrict": {"description": "Setting end_time_restrict while leaving start_time_restrict unset is interpreted to mean all time up to the end_time_restrict.", "format": "google-datetime", "type": "string"}, "availability": {"description": "The new list of availability.", "type": "array", "items": {"$ref": "Availability"}}, "name": {"type": "string", "description": "The resource name of the service to apply this to. In the format of `partners/{partner_id}/merchants/{merchant_id}/services/{service_id}`"}, "durationRestrict": {"type": "string", "format": "google-duration", "description": "Setting duration further restricts the scope of the update to just the availability with matching duration."}, "startTimeRestrict": {"format": "google-datetime", "description": "If provided, we will consider the Availability entities provided to be a complete snapshot from [start_time_restrict, end_time_restrict). That is, all existing availability will be deleted if the following condition holds true: ``` start_time_restrict <= availability.start_sec && availability.start_sec < end_time_restrict ``` If a duration message is set, the condition is further restricted: ``` availability.duration == duration_restrict ``` If a resources_restrict message is set, the condition is further restricted: ``` availability.resources.staff_id == resources_restrict.staff_id && availability.resources.room_id == resources_restrict.room_id ``` These fields are typically used to provide a complete update of availability in a given time range. Setting start_time_restrict while leaving end_time_restrict unset is interpreted to mean all time beginning at start_time_restrict.", "type": "string"}}, "type": "object", "description": "A list of availability and who/when they should be applied to.", "id": "ServiceAvailability"}, "PerTicketFee": {"type": "object", "id": "PerTicket<PERSON>ee", "properties": {"facilityFee": {"description": "A fee that goes to the venue/facility.", "$ref": "Price"}, "taxes": {"$ref": "Price", "description": "Per ticket taxes."}, "serviceCharge": {"$ref": "Price", "description": "An extra charge assessed for a service."}}, "description": "Fees that must be paid for each ticket the user purchases."}, "PaymentProcessorConfig": {"type": "object", "description": "A configuration for a payment processor, setup on a per Merchant basis.", "properties": {"version": {"description": "The API version number sent to the payment processor along with payment requests.", "type": "string"}, "processor": {"enum": ["PROCESSOR_UNSPECIFIED", "PROCESSOR_STRIPE", "PROCESSOR_BRAINTREE"], "type": "string", "enumDescriptions": ["Unused", "A configuration for payments with Stripe.", "A configuration for payments with Braintree."], "description": "Defines the payment processor partner this configuration applies to."}, "publicKey": {"description": "The key used to identify this merchant with the payment processor. For Stripe, refer to: https://stripe.com/docs/dashboard#api-keys For Braintree, refer to: https://articles.braintreepayments.com/control-panel/important-gateway-credentials", "type": "string"}}, "id": "PaymentProcessorConfig"}, "Deposit": {"id": "<PERSON><PERSON><PERSON><PERSON>", "type": "object", "description": "A deposit that the user may be charged or have a hold on their credit card for.", "properties": {"minAdvanceCancellationSec": {"type": "string", "description": "Minimum advance cancellation for the deposit.", "format": "int64"}, "deposit": {"$ref": "Price", "description": "Deposit amount."}, "depositType": {"description": "Defines how the deposit is determined from the availability.", "type": "string", "enumDescriptions": ["The price is for a fixed amount. This is the default value if the field is not set. Examples: $50 deposit to reserve a table; $20 no show fee for a yoga class", "The price specified is per person, and the total price is calculated according to the party size specified in Resources as price_micros * party_size. A PER_PERSON price must be accompanied by a party size in the availability resources. If it is not, a party size of one is used. Examples: $10 each for tickets to a museum"], "enum": ["FIXED_RATE_DEFAULT", "PER_PERSON"]}}}, "ServiceAttributeValueId": {"description": "Identifies a particular value of a service attribute to be applied to a Service.", "properties": {"attributeId": {"description": "ID of an attribute as defined in Merchant.service_attribute, e.g. \"service-type\".", "type": "string"}, "valueId": {"type": "string", "description": "ID of the value for this attribute, e.g. \"haircut\". Must match a value_id in the service attribute definition."}}, "id": "ServiceAttributeValueId", "type": "object"}, "Entity": {"description": "Represents an entity related to the event.", "id": "Entity", "properties": {"entityType": {"enum": ["ENTITY_TYPE_UNSPECIFIED", "ENTITY_TYPE_PERFORMER", "ENTITY_TYPE_PLAYER", "ENTITY_TYPE_CONCERT_TOUR", "ENTITY_TYPE_SPORTS_SERIES", "ENTITY_TYPE_PLAY"], "enumDescriptions": ["Not specified. Do not use.", "The entity represents the artist or group performing at a concert or a show. Only applicable when event category is CONCERT or THEATRE.", "The entity represents the sports team or player at the event. Only applicable when event category is SPORTS.", "The entity represents the tour that this event belongs to. Only applicable when event category is CONCERT.", "The entity represents a sports tournament that this event belongs to. Only applicable when event category is SPORTS.", "The entity represents the type of play (e.g., musical, comedy, ballet, etc.) performed at the event. Only applicable when event category is THEATRE."], "description": "The type of the entity. (optional)", "type": "string"}, "publicIdentificationData": {"$ref": "PublicIdentificationData", "description": "Public references of the entity. (optional)"}, "name": {"description": "Name of the entity. (required)", "type": "string"}, "url": {"description": "Url of the webpage that unambiguously describes the entity. This is the webpage on the partner's website for the entity if any; for other public URLs of the entity, use relevant_url in public_identification_data. (optional)", "type": "string"}, "entityRole": {"description": "The role of the entity in the event. (optional)", "enum": ["ENTITY_ROLE_UNSPECIFIED", "ENTITY_ROLE_HEADLINER", "ENTITY_ROLE_SUPPORTER", "ENTITY_ROLE_HOME_TEAM", "ENTITY_ROLE_AWAY_TEAM"], "enumDescriptions": ["Not specified.", "The entity represents a headliner or leading performer at the event.", "The entity represents a supporting performer at the event.", "The entity represents the home team at the (sports) event.", "The entity represents the away team at the (sports) event."], "type": "string"}, "id": {"description": "Unique identifier of the entity in the partner's database. (optional)", "type": "string"}}, "type": "object"}, "Location": {"type": "object", "id": "Location", "description": "Geographic information about a location.", "properties": {"locationId": {"type": "string", "description": "Unique reference of the location within the service. This id can be used to refer to this location in other service fields. E.g in the custom intake form, a set of location ids can be used to specify pick up location options. If set, this id should be unique within the same service. (optional)"}, "placeId": {"type": "string", "description": "The Place ID for a place in the Google Places database and on Google Maps. See https://developers.google.com/places/place-id for more about Place IDs. If this is provided, Google will match the location to this place."}, "url": {"type": "string", "description": "The url of the location's public website. (optional)"}, "locationType": {"type": "string", "enum": ["LOCATION_TYPE_UNSPECIFIED", "VISITED_LOCATION", "START_LOCATION", "END_LOCATION"], "description": "The type of the location, must be supplied if this location is provided for a Service.", "enumDescriptions": ["Location type unspecified.", "The location where this service visits.", "The location where this service starts, also serves as MEETING_LOCATION.", "The location where this service ends."]}, "geo": {"description": "The Geo info of the location, including latitude, longitude, and address. (optional)", "$ref": "GeoCoordinates"}, "telephone": {"description": "The public telephone number of the location including its country and area codes, e.g. +***********. (optional)", "type": "string"}, "name": {"description": "The location's name, telephone, url and geo are used to support matching the location with places already present on Google Maps. This field is optional, but may be required in some contexts. For example, a Service.location without a name will not be matched to a business entity, even if they are located at the same address. (optional)", "type": "string"}}}, "ReplaceServiceAvailabilityRequest": {"description": "The request to replace a Service's availability.", "properties": {"serviceAvailability": {"$ref": "ServiceAvailability", "description": "The service availability that is used to replace the existing ones."}}, "type": "object", "id": "ReplaceServiceAvailabilityRequest"}, "ClientInformation": {"id": "ClientInformation", "properties": {"address": {"description": "Address of the client", "$ref": "PostalAddress"}, "givenName": {"description": "Given name of the client", "type": "string"}, "familyName": {"type": "string", "description": "Family name of the client"}, "email": {"type": "string", "description": "Email address of the client"}, "telephone": {"type": "string", "description": "Phone number of the client"}}, "type": "object", "description": "Personal information about the person making a booking"}, "ServiceIntakeFormField": {"id": "ServiceIntakeFormField", "properties": {"choiceText": {"type": "array", "description": "Set if and only if the field type is MULTIPLE_CHOICE, CHECKBOXES, or DROPDOWN. Used to enumerate possible choices.", "items": {"$ref": "Text"}}, "isRequired": {"type": "boolean", "description": "Indicates whether an answer to this field is required by a user."}, "hint": {"$ref": "Text", "description": "The hint text for input, which shows up as a text placeholder. This is only applicable when the field type is SHORT_ANSWER or PARAGRAPH. (optional)"}, "value": {"description": "Set if and only if the field type is LOCATION_SEARCH. Please use the \"location_id\" in the \"location\" field to specify the location value.", "type": "array", "items": {"type": "string"}}, "allowCustomAnswer": {"type": "boolean", "description": "Indicates whether a custom value is allowed in additional to predefined answers. This is only applicable when the field type is LOCATION_SEARCH. (optional)"}, "type": {"type": "string", "description": "The type of this field.", "enumDescriptions": ["Fields of unspecified or unknown type will be ignored.", "A one-line input field for text.", "A multi-line input field for text.", "A set of radio buttons that requires one choice from many options.", "One or more enumerated items with checkboxes.", "A selection from a dropdown.", "A yes/no button.", "A search box that supports finding matched location given user input from provided location list."], "enum": ["FIELD_TYPE_UNSPECIFIED", "SHORT_ANSWER", "PARAGRAPH", "MULTIPLE_CHOICE", "CHECKBOXES", "DROPDOWN", "BOOLEAN", "LOCATION_SEARCH"]}, "id": {"description": "A string from an aggregator partner which uniquely identifies a form field. This id should be the same as the id in the corresponding form field answer and must be unique across both the service level and per ticket intake forms. (required)", "type": "string"}, "additionalOption": {"items": {"$ref": "Text"}, "description": "Additional options provided in addition to the provided values. Only applicable when the field type is LOCATION_SEARCH. E.g. in addition to the provided location list, another available option can be \"I will contact supplier later\". (optional)", "type": "array"}, "localizedLabel": {"$ref": "Text", "description": "The text shown to the user for this field. The field can be supplied in multiple locales. (required)"}, "ticketTypeRestrict": {"description": "If this question should only be shown when the user books certain ticket types, this field should be set as the set of applicable ticket type ids. Leave the field empty if the question is always applicable.", "type": "array", "items": {"type": "string"}}, "label": {"type": "string", "description": "The text shown to the user for this field. Deprecated, please use `localized_label` instead."}}, "description": "Defines a field that is included in a ServiceIntakeForm.", "type": "object"}, "BatchReplaceServiceAvailabilityResponse": {"properties": {"extendedServiceAvailability": {"items": {"$ref": "ExtendedServiceAvailability"}, "description": "The successfully updated extended service availability messages that were used to replace the existing availability slots.", "type": "array"}}, "description": "The batch response to replace multiple Service's availability slots. Only successfully updated slots will be included in this message", "id": "BatchReplaceServiceAvailabilityResponse", "type": "object"}, "Resources": {"properties": {"partySize": {"description": "Applicable only for Dining: The party size which can be accommodated during this time slot. A restaurant can be associated with multiple Slots for the same time, each specifying a different party_size, if for instance 2, 3, or 4 people can be seated with a reservation.", "type": "integer", "format": "int32"}, "staffName": {"type": "string", "description": "Optional name of a staff member providing the service. This field will be displayed to users making a booking, and should be human readable, as opposed to an opaque identifier. This field must be present if staff_id is present."}, "roomId": {"description": "An optional ID for the room the service is located in. This field identifies the room across all merchants, services, and availability records. It also needs to be stable over time to allow correlation with past bookings. This field must be present if room_name is present.", "type": "string"}, "roomName": {"description": "An optional name for the room the service is located in. This field will be displayed to users making a booking, and should be human readable, as opposed to an opaque identifier. (optional but required if room_id is present) In dining a room name should only be used for seating areas such as the bar or patio and should not be used for fixed price menus, special activities, or any other non-room value (such as reservation or dinner). It is strongly recommended that the default seating area not have a room associated with it.", "type": "string"}, "staffId": {"description": "Optional ID for a staff member providing the service. This field identifies the staff member across all merchants, services, and availability records. It also needs to be stable over time to allow correlation with past bookings. This field must be present if staff_name is present.", "type": "string"}}, "description": "A resource is used to disambiguate availability slots from one another when different staff members, or rooms are part of the service. Multiple slots for the same service and time interval can co-exist when they have different resources.", "id": "Resources", "type": "object"}, "ServiceAttribute": {"properties": {"attributeName": {"type": "string", "description": "A user-visible name for this attribute, e.g. \"Account Type\"."}, "attributeId": {"description": "An identifier that uniquely identifies this service attribute among others for the same merchant, e.g. \"account-type\".", "type": "string"}, "value": {"type": "array", "items": {"$ref": "Value"}, "description": "All possible values for this service attribute."}}, "type": "object", "id": "ServiceAttribute", "description": "Service attributes are partner-defined categories that describe the Services for a Merchant. For example, a bank may define an \"Account Type\" service attribute with possible values of \"Personal\" and \"Business\", while a hair salon may define a \"Service Type\" service attribute with possible values of \"Haircut\", \"Color\", and \"Style\"."}, "LineItem": {"id": "LineItem", "type": "object", "description": "A single item in an order--the booking of a single service in a single time slot.", "properties": {"tickets": {"type": "array", "description": "Number of tickets ordered by Ticket Type.", "items": {"$ref": "OrderedTickets"}}, "startSec": {"format": "int64", "description": "Start time of the appointment slot in seconds of UTC time since Unix epoch.", "type": "string"}, "status": {"description": "Status of the Line Item.", "enum": ["BOOKING_STATUS_UNSPECIFIED", "CONFIRMED", "PENDING_MERCHANT_CONFIRMATION", "PENDING_CLIENT_CONFIRMATION", "CANCELED", "NO_SHOW", "NO_SHOW_PENALIZED", "FAILED", "DECLINED_BY_MERCHANT"], "type": "string", "enumDescriptions": ["Not specified.", "Booking has been confirmed", "Booking is awaiting confirmation by the merchant before it can transition into CONFIRMED status (this is NOT currently supported)", "Booking is awaiting confirmation by the client before it can transition into CONFIRMED status (this is NOT currently supported)", "Booking has been canceled on behalf of the user.", "<PERSON><PERSON> did not show for the appointment", "Client did not show for the appointment in violation of the cancellation policy.", "Booking could not be completed due to a processing failure.", "Booking was asynchronously declined by the merchant."]}, "serviceId": {"type": "string", "description": "ID of the merchant Service."}, "price": {"description": "The total price (excluding taxes) of this Line Item.", "$ref": "Price"}, "durationSec": {"format": "int64", "description": "Duration of the appointment slot in seconds.", "type": "string"}}}, "ServiceIntakeForm": {"properties": {"firstTimeCustomers": {"type": "boolean", "description": "If true, this form will be shown to first time customers. Deprecated. This functionality is not supported for intake forms."}, "returningCustomers": {"description": "If true, this form will be shown to repeat customers. Deprecated. This functionality is not supported for intake forms.", "type": "boolean"}, "field": {"items": {"$ref": "ServiceIntakeFormField"}, "type": "array", "description": "Fields that will be displayed to the user."}}, "id": "ServiceIntakeForm", "description": "Defines an intake form that customizes the service provided by a merchant.", "type": "object"}, "Recurrence": {"properties": {"repeatEvery": {"format": "google-duration", "type": "string", "description": "Defines the time between successive availability slots. Example: An availability with a duration of 20 min, a repeat_every of 30 min, a start_time of 9:00am, and a repeat_until of 11:00am will yield slots at 9-9:20am, 9:30-9:50am, 10-10:20am, 10:30-10:50am, 11-11:20am. (required)"}, "repeatUntil": {"description": "The inclusive maximum UTC timestamp the availability repeats until.", "format": "google-datetime", "type": "string"}}, "id": "Recurrence", "description": "Recurrence messages are optional, but allow for a more compact representation of consistently repeating availability slots. They typically represent a day's working schedule. ScheduleException messages are then used to represent booked/unavailable time ranges within the work day. Requirements: 1. The expansion of availability slots or recurrences must NOT create identical slots. If the ids, start_time, duration, and resources match, slots are considered identical. 2. Do NOT mix the standard availability format and recurrence within the slots of a single service. Recurrence benefits merchants/services that offer appointments. The standard format is geared towards merchants/services with regularly scheduled classes. 3. Recurrences should not last for more than 24 hours.", "type": "object"}, "Booking": {"properties": {"serviceId": {"type": "string", "description": "ID of the merchant service"}, "startTime": {"description": "Start time of the appointment slot", "format": "google-datetime", "type": "string"}, "status": {"enumDescriptions": ["Not specified.", "Booking has been confirmed", "Booking is awaiting confirmation by the merchant before it can transition into CONFIRMED status (this is NOT currently supported)", "Booking is awaiting confirmation by the client before it can transition into CONFIRMED status (this is NOT currently supported)", "Booking has been canceled on behalf of the user.", "<PERSON><PERSON> did not show for the appointment", "Client did not show for the appointment in violation of the cancellation policy.", "Booking could not be completed due to a processing failure.", "Booking was asynchronously declined by the merchant."], "description": "Status of the booking", "type": "string", "enum": ["BOOKING_STATUS_UNSPECIFIED", "CONFIRMED", "PENDING_MERCHANT_CONFIRMATION", "PENDING_CLIENT_CONFIRMATION", "CANCELED", "NO_SHOW", "NO_SHOW_PENALIZED", "FAILED", "DECLINED_BY_MERCHANT"]}, "duration": {"type": "string", "format": "google-duration", "description": "Duration of the appointment slot"}, "clientInformation": {"$ref": "ClientInformation", "description": "Personal information of the client making the appointment"}, "paymentInformation": {"description": "Information about payment transactions that relate to the booking.", "$ref": "PaymentInformation"}, "partySize": {"format": "int64", "type": "string", "description": "Party size of the booking"}, "merchantId": {"type": "string", "description": "ID of the merchant for the slot"}, "name": {"description": "Resource name of the booking: `partners/{partner ID}/bookings/{booking ID}`", "type": "string"}}, "description": "A booking for an inventory slot", "type": "object", "id": "Booking"}, "WaitlistRules": {"properties": {"supportsAdditionalRequest": {"type": "boolean", "description": "If true, the user will be able to send a free-form additional text request when joining the waitlist for this service."}, "minPartySize": {"format": "int32", "type": "integer", "description": "Required. Must be a positive integer for services providing waitlist functionality. If the service or merchant does not provide waitlist functionality, this must not be populated."}, "maxPartySize": {"format": "int32", "description": "Required. Must be a positive integer for services providing waitlist functionality. If the service or merchant does not provide waitlist functionality, this must not be populated.", "type": "integer"}}, "id": "WaitlistRules", "description": "Rules related to joining the waitlist.", "type": "object"}, "TicketingVerticalSpecificData": {"properties": {"eventSourceUrl": {"items": {"type": "string"}, "type": "array", "description": "Required. URL of the pages where the event information or descriptions can be found."}, "eventUrl": {"description": "The URL of the event on the partner's website. (optional)", "type": "string"}, "eventOrganizerUrl": {"description": "Optional. URL of the organizer who hosts the event.", "type": "string"}, "eventOrganizer": {"description": "Optional. Organizer who hosts the event.", "$ref": "Text"}, "entity": {"type": "array", "items": {"$ref": "Entity"}, "description": "A list of entities related to the event. (optional)"}, "eventAttendanceMode": {"description": "Required. The type of the event attendance.", "enumDescriptions": ["Not specified.", "For virtual events.", "For physical events.", "For events that are both physical and virtual."], "type": "string", "enum": ["ATTENDANCE_MODE_UNSPECIFIED", "ONLINE", "PHYSICAL", "PHYSICAL_ONLINE_MIXED"]}, "eventCategory": {"enumDescriptions": ["Not specified. Do not use.", "Concerts.", "Sports events.", "Theatre events.", "Exhibits.", "Workshops and Classes."], "description": "The category of the event. Set only when event falls into one of the predefined categories. (optional)", "enum": ["EVENT_CATEGORY_UNSPECIFIED", "EVENT_CATEGORY_CONCERT", "EVENT_CATEGORY_SPORTS", "EVENT_CATEGORY_THEATRE", "EVENT_CATEGORY_EXHIBITS", "EVENT_CATEGORY_WORKSHOPS_AND_CLASSES"], "type": "string"}, "eventVirtualLocationUrl": {"type": "array", "items": {"type": "string"}, "description": "Optional. URL where the event can be watched."}, "eventOrganizerType": {"description": "Optional. The type of the organizer.", "type": "string", "enum": ["ORGANIZER_TYPE_UNSPECIFIED", "PERSON", "ORGANIZATION"], "enumDescriptions": ["Not specified.", "For organizer who is a person.", "For organizer who is an organization."]}, "eventState": {"enumDescriptions": ["Not specified.", "The event is scheduled.", "The event is rescheduled.", "The event is cancelled.", "The event is postponed."], "enum": ["EVENT_STATE_UNSPECIFIED", "SCHEDULED", "RESCHEDULED", "CANCELLED", "POSTPONED"], "description": "Optional. State of the event.", "type": "string"}}, "description": "Additional information unique to the event ticketing vertical.", "id": "TicketingVerticalSpecificData", "type": "object"}, "TaxRate": {"id": "TaxRate", "description": "A tax rate applied when charging the user for a service, and which can be set on either a per merchant, or per service basis.", "type": "object", "properties": {"microPercent": {"type": "integer", "description": "A tax rate in millionths of one percent, effectively giving 6 decimals of precision. For example, if the tax rate is 7.253%, this field should be set to 7253000. If this field is left unset or set to 0, the total price charged to a user for any service provided by this merchant is the exact price specified by Service.price. The service price is assumed to be exempt from or already inclusive of applicable taxes. Taxes will not be shown to the user as a separate line item. If this field is set to any nonzero value, the total price charged to a user for any service provided by this merchant will include the service price plus the tax assessed using the tax rate provided here. Fractions of the smallest currency unit (for example, fractions of one cent) will be rounded using nearest even rounding. Taxes will be shown to the user as a separate line item.", "format": "int32"}}}, "Terms": {"description": "A set of rules and guidelines that are displayed to the user in order to make a booking through Reserve with Google.", "id": "Terms", "properties": {"url": {"description": "Optionally, the URL to the Terms and Conditions.", "type": "string"}, "text": {"description": "The text to be displayed to the user. Use localized_text below for new integrations.", "type": "string"}, "localizedText": {"description": "The localized text to be displayed to the user.", "$ref": "Text"}}, "type": "object"}, "PaymentRestrictions": {"properties": {"creditCardRestrictions": {"$ref": "CreditCardRestrictions", "description": "Restrictions to the credit cards this merchant accepts. We assume all credit cards are accepted if this field is not set. Note that the list of cards supported by CreditCardType will grow over time, meaning that leaving this empty subjects a configuration to future changes."}}, "id": "PaymentRestrictions", "type": "object", "description": "Restrictions to the payment methods this merchant accepts."}, "FeedStatus": {"properties": {"statistics": {"$ref": "FeedStatistics", "description": "Statistics specific to this feed."}, "name": {"description": "The feed resource name, which has the format of - `partners/{partner_id}/feeds/merchants/{file_name}` - `partners/{partner_id}/feeds/services/{file_name}` - `partners/{partner_id}/feeds/availability/{file_name}`", "type": "string"}, "state": {"description": "The processing state of this feed.", "type": "string", "enum": ["STATE_UNSPECIFIED", "IN_PROGRESS", "SUCCESS", "FAILURE"], "enumDescriptions": ["Default value. Unused.", "The feed is still being processed.", "The feed has been successfully processed.", "We encountered an error while processing the feed."]}, "errorDetails": {"type": "string", "description": "Human readable string providing more details if we failed to process this feed."}}, "description": "Status of a feed uploaded by the aggregator's platform.", "type": "object", "id": "FeedStatus"}, "RefundCondition": {"properties": {"refundPercent": {"description": "The percent that can be refunded, as long as the service booking is cancelled at least `min_duration_before_start_time` before the service start time, in the range of [0, 100]. When set to 0 (default), the service is not refundable. When set to 100 this service is fully refundable.", "type": "integer", "format": "uint32"}, "minDurationBeforeStartTime": {"format": "google-duration", "type": "string", "description": "Duration before the start time, until when the customer can receive a refund for part of the service's cost specified in `refund_percent`. When set to 0 (default), the service can be cancelled at any time."}}, "id": "RefundCondition", "type": "object", "description": "Defines a single refund condition. Multiple refund conditions could be used together to describe \"refund steps\" as various durations before the service start time."}, "NoShowFee": {"description": "A fee that a user may be charged if they have made a booking but do not show up.", "properties": {"feeType": {"enum": ["FIXED_RATE_DEFAULT", "PER_PERSON"], "description": "Defines how the fee is determined from the availability.", "type": "string", "enumDescriptions": ["The price is for a fixed amount. This is the default value if the field is not set. Examples: $50 deposit to reserve a table; $20 no show fee for a yoga class", "The price specified is per person, and the total price is calculated according to the party size specified in Resources as price_micros * party_size. A PER_PERSON price must be accompanied by a party size in the availability resources. If it is not, a party size of one is used. Examples: $10 each for tickets to a museum"]}, "fee": {"$ref": "Price", "description": "The amount the user may be charged if they do not show up for their reservation."}}, "type": "object", "id": "NoShowFee"}, "MerchantMatchingHints": {"type": "object", "id": "MerchantMatchingHints", "description": "Hints used to help Google match a merchant to a place on Google Maps.", "properties": {"placeId": {"type": "string", "description": "The Place ID for a place in the Google Places database and on Google Maps. See https://developers.google.com/places/place-id for more about Place IDs."}}}, "ListStatusResponse": {"properties": {"nextPageToken": {"description": "Token to retrieve the next page of results. There are no more results in the list if empty.", "type": "string"}, "status": {"type": "array", "items": {"$ref": "FeedStatus"}, "description": "Reverse chronological list of statuses for uploaded feeds. The maximum number of items returned is based on the page_size field in the request."}}, "description": "The request to retrieve the Status for multiple feeds uploaded by the aggregator's platform.", "id": "ListStatusResponse", "type": "object"}, "Attribution": {"id": "Attribution", "type": "object", "properties": {"localizedText": {"$ref": "Text", "description": "The text to give credit to the photographer or agency supporting i18n. This text will be displayed together with the source media. Note that only plain text is supported for this field, any HTML components will be stripped (hyperlink based attribution is not supported)."}, "text": {"type": "string", "description": "Deprecated, prefer to use localized_text."}}, "description": "Attribution information for this media."}, "PaymentInformation": {"id": "PaymentInformation", "description": "Payment details that relate to a booking", "properties": {"prepaymentStatus": {"enum": ["PREPAYMENT_STATUS_UNSPECIFIED", "PREPAYMENT_PROVIDED", "PREPAYMENT_NOT_PROVIDED", "PREPAYMENT_REFUNDED"], "type": "string", "enumDescriptions": ["Not specified, defaults to PREPAYMENT_NOT_PROVIDED.", "The fee for the booking has been paid in advance.", "The fee for the booking has not been paid in advance.", "The fee was previously PREPAYMENT_PROVIDED but has now been refunded."], "description": "Prepayment status of the booking. If the prepayment_status is PREPAYMENT_PROVIDED or PREPAYMENT_REFUNDED, then payment_transaction_id contains the associated unique transaction id."}, "paymentTransactionId": {"type": "string", "description": "Unique identifier for a payment transaction associated with the booking. Empty if not applicable."}}, "type": "object"}, "PostalAddress": {"description": "Address of a customer or a business.", "id": "PostalAddress", "type": "object", "properties": {"postalCode": {"type": "string", "description": "The postal code, e.g. \"94043\". (required)"}, "streetAddress": {"type": "string", "description": "The street address, e.g. \"1600 Amphitheatre Pkwy\". (required)"}, "addressLocality": {"type": "string", "description": "The locality, e.g. \"Mountain View\". (required)"}, "addressCountry": {"type": "string", "description": "The country, specified using its ISO 3166-1 alpha-2 country code, e.g. \"US\" (required)"}, "addressRegion": {"description": "The region, e.g. \"CA\". This field is only required in countries where region is commonly a part of the address. (optional)", "type": "string"}}}, "PerOrderFee": {"description": "Fees that must be paid once per order, regardless of number of tickets.", "type": "object", "id": "PerOrder<PERSON><PERSON>", "properties": {"deliveryFee": {"$ref": "Price", "description": "A fee that can vary by delivery method."}, "processingFee": {"description": "A fee to process the user's payment method.", "$ref": "Price"}}}, "BatchReplaceServiceAvailabilityRequest": {"type": "object", "description": "The batch request to replace multiple Service's availability slots.", "properties": {"extendedServiceAvailability": {"type": "array", "description": "The extended service availability that is used to replace the existing availability slots.", "items": {"$ref": "ExtendedServiceAvailability"}}}, "id": "BatchReplaceServiceAvailabilityRequest"}, "HomeServiceData": {"type": "object", "properties": {"categoryType": {"description": "The high level category to which this home service belongs to. E.g. plumber, electrician etc.", "type": "string"}, "jobType": {"description": "The job type under the category to which the given home service belongs to. E.g. unclog_drain, install_faucet are the job types under plumber category.", "type": "string"}}, "id": "HomeServiceData", "description": "Additional information required to be provided for home service vertical."}, "CancellationPolicy": {"description": "Cancellation policy for a service.", "id": "CancellationPolicy", "properties": {"refundCondition": {"description": "Zero or more refund conditions applicable to the policy.", "items": {"$ref": "RefundCondition"}, "type": "array"}}, "type": "object"}, "PrepaymentTerms": {"properties": {"chargeTimeBeforeStartTimeSec": {"description": "Time in seconds before the service start time that the user is charged for payment. This field should only be set when ChargeTiming is CHARGE_LATER.", "format": "int64", "type": "string"}, "chargeTiming": {"type": "string", "enumDescriptions": ["Unused.", "Customer will be charged immediately.", "Customer will be charged later."], "description": "When the charge will occur relative to the purchase time.", "enum": ["CHARGE_TIMING_UNSPECIFIED", "CHARGE_NOW", "CHARGE_LATER"]}}, "id": "PrepaymentTerms", "description": "Specific information around when prepayment is completed.", "type": "object"}, "GeoCoordinates": {"properties": {"unstructuredAddress": {"description": "An unstructured address could also be provided as a fallback. E.g. \"1600 amphitheatre parkway mountain view, ca 94043\"", "type": "string"}, "address": {"description": "Postal address of the location, preferred.", "$ref": "PostalAddress"}, "longitude": {"format": "double", "type": "number", "description": "Longitude in degrees. (optional)"}, "latitude": {"format": "double", "type": "number", "description": "Latitude in degrees. (optional)"}}, "description": "The Geo data of a location, including latitude, longitude, and address.", "id": "GeoCoordinates", "type": "object"}, "SchedulingRuleOverrides": {"id": "SchedulingRuleOverrides", "properties": {"firstBookableSec": {"type": "string", "format": "int64", "description": "The first time (in seconds) that this slot is able to be booked. This timestamp must be before the start_sec of the slot, or last_bookable_sec if specified."}, "lastOnlineCancellableSec": {"format": "int64", "type": "string", "description": "If set, the last time (in seconds since the Unix epoch) that this specific appointment slot can be cancelled through Reserve with Google. This field will override any service-level cancellation rules. (optional)"}, "lastBookableSec": {"format": "int64", "type": "string", "description": "The last time (in seconds) that this slot is able to be booked. This timestamp must be before the start_sec of the slot to be respected (if users should be able to book after the start time, use service level SchedulingRules.min_booking_before_end_time). If present, will override anything specified in the min_booking_buffer of the corresponding Service's SchedulingRules."}}, "type": "object", "description": "Availability level scheduling rules."}, "Availability": {"type": "object", "id": "Availability", "description": "An availability slot of the merchant's service, indicating time and number of spots.", "properties": {"deposit": {"$ref": "<PERSON><PERSON><PERSON><PERSON>", "description": "Optional deposit for this availability. Overrides the service deposit if one was specified."}, "schedulingRuleOverrides": {"description": "Availability scheduling rules. If fields are populated, they will override any corresponding scheduling rules on the service-level SchedulingRules.", "$ref": "SchedulingRuleOverrides"}, "requireCreditCard": {"enumDescriptions": ["The credit card requirement is not explicitly specified and the behaviour is identical to the one specified for CONDITIONAL.", "Google will require a credit card for the booking if any of the following conditions are met: * the availability has a price and the prepayment_type is REQUIRED * the no_show_fee is set * the deposit field is set.", "A credit card is always required in order to book this availability regardless of other field values."], "enum": ["REQUIRE_CREDIT_CARD_UNSPECIFIED", "REQUIRE_CREDIT_CARD_CONDITIONAL", "REQUIRE_CREDIT_CARD_ALWAYS"], "type": "string", "description": "Indicates whether the user must provide a credit card in order to book this availability slot. If the value is not set, it is inherited from the service level if it's set there. (optional)"}, "confirmationMode": {"enum": ["CONFIRMATION_MODE_UNSPECIFIED", "CONFIRMATION_MODE_SYNCHRONOUS", "CONFIRMATION_MODE_ASYNCHRONOUS"], "type": "string", "enumDescriptions": ["The confirmation mode was not specified. Synchronous confirmation will be assumed.", "Bookings for this availability will be confirmed synchronously.", "Bookings for this availability will be confirmed asynchronously."], "description": "The confirmation mode that will be used when booking this availability. Attempts to create bookings for availabilities with a confirmation mode of CONFIRMATION_MODE_SYNCHRONOUS must be immediatlely confirmed or denied. Attempts to create bookings for availabilities with confirmation mode of CONFIRMATION_MODE_ASYNCHRONOUS must be either immediately denied or created with status PENDING."}, "ticketTypeId": {"items": {"type": "string"}, "description": "Indicates a list of supported ticket types for this availability slot. If unset, all ticket types in the parent service are available for this slot. Note that the values of this field must be defined in the parent service. Examples: * Service with four ticket types: TicketType {ticket_type_id: \"adult_1\" short_description: \"Adult weekdays\"} TicketType {ticket_type_id: \"adult_2\" short_description: \"Adult weekends\"} TicketType {ticket_type_id: \"youth_1\" short_description: \"Youth weekdays\"} TicketType {ticket_type_id: \"youth_2\" short_description: \"Youth weekends\"} To represent the inventory during the weekdays: `availability {ticket_type_id: \"adult_1\" ticket_type_id: \"youth_1\"...}`. To represent the inventory during the holidays: `availability {ticket_type_id: \"adult_2\" ticket_type_id: \"youth_2\"...}`. * Service with three ticket types: TicketType {ticket_type_id: \"adult\" short_description: \"Adult\"} TicketType {ticket_type_id: \"youth\" short_description: \"Youth\"} TicketType {ticket_type_id: \"senior\" short_description: \"Senior\"} To indicate that all three ticket types are available for this time slot, use either `availability {ticket_type_id: \"adult\" ticket_type_id: \"youth\" ticket_type_id: \"senior\" ...}` or `availability {...}' (do not set ticket_type_id in this slot). (optional)", "type": "array"}, "paymentOptionId": {"description": "A list of ids referencing the payment options which can be used to pay for this slot. The actual payment options are defined at the Merchant level, and can also be shared among multiple Merchants. This field overrides any payment_option_ids specified in the service message. Similarly payment_option_ids specified here do NOT have to be present in the service message, though must be defined at the Merchant level.", "items": {"type": "string"}, "type": "array"}, "recurrence": {"$ref": "Recurrence", "description": "The recurrence information for the availability, representing more than one start time. A recurrence should contain appointments for one working day."}, "noShowFee": {"description": "Optional no show fee for this availability. Overrides the service no show fee if one was specified.", "$ref": "NoShowFee"}, "resources": {"description": "Optional resources used to disambiguate this availability slot from others when different staff members, or rooms are part of the service. E.g. the same Yoga class with two 2 instructors: ``` availability { resources { staff_id: \"1\" staff_name: \"<PERSON>\" } spots_total: 10 spots_open: 7 } availability { resources { staff_id: \"2\" staff_name: \"<PERSON>\" } spots_total: 5 spots_open: 2 } ```", "$ref": "Resources"}, "duration": {"type": "string", "description": "Duration of the appointment slot", "format": "google-duration"}, "startTime": {"description": "Start time of the appointment slot.", "type": "string", "format": "google-datetime"}, "availabilityTag": {"description": "An optional opaque string to identify this availability slot. If set, it will be included in the requests that book/update/cancel appointments.", "type": "string"}, "spotsOpen": {"description": "Number of open spots.", "format": "int64", "type": "string"}, "scheduleException": {"type": "array", "items": {"$ref": "ScheduleException"}, "description": "Times when this service cannot be scheduled. To limit the number of schedule_exception messages, consider joining adjacent exceptions."}, "spotsTotal": {"description": "Number of total spots and open spots of this availability. Examples: * Yoga class of 10 spots with 3 booked: `availability {spots_total: 10, spots_open: 7 ...}` * Chair massage session which is already fully booked: `availability {spots_total: 1, spots_open: 0 ...}` Note: If sending requests using the availability compression format defined below, these two fields will be inferred. * A Recurrence implies `spots_total=1` and `spots_open=1`. * A ScheduleException implies `spots_total=1` and `spots_open=0`.", "type": "string", "format": "int64"}, "durationRequirement": {"description": "The requirement to show the slots duration and/or endtime. This field will be ignored if the slot is unavailable. Not used in the Things-To-Do vertical. (optional)", "enumDescriptions": ["The handling of the end time is not specified. This is the default.", "The end time is not shown to the user.", "The end time has to be shown to the user before an appointment can be made."], "enum": ["DURATION_REQUIREMENT_UNSPECIFIED", "DO_NOT_SHOW_DURATION", "MUST_SHOW_DURATION"], "type": "string"}}}, "OrderedTickets": {"properties": {"count": {"description": "Number of tickets ordered for this type.", "type": "integer", "format": "int32"}, "ticketId": {"type": "string", "description": "ID of a Ticket Type."}}, "type": "object", "id": "OrderedTickets", "description": "Number of ordered tickets by Ticket Type."}, "Rating": {"type": "object", "id": "Rating", "properties": {"numberOfRatings": {"format": "uint64", "description": "Number of ratings used in calculating the value (required).", "type": "string"}, "value": {"format": "double", "description": "Average rating value (required when number_of_ratings > 0). The value must be in the range of [1, 5] and can be omitted if and only if the number_of_ratings is zero.", "type": "number"}}, "description": "Defines Rating for an entity."}, "Value": {"properties": {"valueId": {"description": "An identifier that uniquely identifies this value among others for this service attribute, e.g. \"personal\".", "type": "string"}, "valueName": {"type": "string", "description": "A user-visible name for the value, e.g. \"Personal\"."}}, "type": "object", "description": "Represents a possible value for a particular service attribute.", "id": "Value"}, "ToursAndActivitiesContent": {"type": "object", "description": "Content fields specific to Tours and Activities. Each element in the repeated field should be independent to allow separate rendering (e.g. as a bullet point). Populating ToursAndActivitiesContent is strongly recommended for tours and activities, but not strictly required. All fields support both plain-text and HTML-like text for basic formatting. Supported HTML-like formatting tags: Phrase tags: , ", "properties": {"inclusions": {"items": {"$ref": "Text"}, "description": "The user-visible list of inclusions.", "type": "array"}, "exclusions": {"items": {"$ref": "Text"}, "type": "array", "description": "The user-visible list of exclusions."}, "highlights": {"description": "The user-visible list of highlights.", "type": "array", "items": {"$ref": "Text"}}, "mustKnow": {"items": {"$ref": "Text"}, "type": "array", "description": "The user-visible list of important notes, use for details such as age restrictions or other conditions that make this service unsuitable."}}, "id": "ToursAndActivitiesContent"}, "VirtualPlatformInfo": {"properties": {"otherPlatformName": {"description": "The name of the platform if the platform is set to OTHER. (Required if platform is set to OTHER)", "$ref": "Text"}, "platform": {"description": "Platform used for virtual session.", "enumDescriptions": ["Unused.", "The merchant is flexible in which video platform they use.", "Google Hangouts product.", "Google Meet product.", "Zoom Video Communications.", "Skype.", "Livestreaming in YouTube.", "Should be set if the video platform used is different from the ones mentioned here."], "enum": ["PLATFORM_UNSPECIFIED", "FLEXIBLE", "GOOGLE_HANGOUTS", "GOOGLE_MEET", "ZOOM", "SKYPE", "YOUTUBE", "OTHER"], "type": "string"}}, "id": "VirtualPlatformInfo", "type": "object", "description": "Information about platform which will be used for this virtual session."}, "VirtualSession": {"properties": {"sessionInstructions": {"description": "Instructions on how this virtual class is set up. If the partner does not include the video URL with the booking, then this text must include when the video URL will be shared with the user. Eg. “Zoom url will be mailed 30 minutes prior to the class”. (Recommended)", "$ref": "Text"}, "isSessionPrerecorded": {"type": "boolean", "description": "Required. Set this as true if the virtual session is not live and is pre-recorded."}, "sessionRequirements": {"description": "Requirements for the given virtual session. Eg. yoga mat, cooking utensils etc. (Recommended)", "$ref": "Text"}, "virtualPlatformInfo": {"$ref": "VirtualPlatformInfo", "description": "Information about the virtual platform used in this session. (Required to enable virtual services)"}}, "type": "object", "description": "Information about virtual/online session. E.g. Online yoga class, virtual cooking class etc.", "id": "VirtualSession"}, "ExtendedServiceAvailability": {"id": "ExtendedServiceAvailability", "type": "object", "description": "A list of availability and who/when they should be applied to.", "properties": {"serviceId": {"description": "This is a mandatory field required to specify which service the availability messages below belong to.", "type": "string"}, "durationRestrict": {"format": "google-duration", "description": "Setting duration further restricts the scope of the update to just the availability with matching duration.", "type": "string"}, "resourcesRestrict": {"description": "Setting resources_restrict further restricts the scope of the update to just this set of resources. All id fields of the resources must match exactly.", "$ref": "Resources"}, "merchantId": {"description": "This is a mandatory field required to specify which merchant the availability messages below belong to.", "type": "string"}, "startTimeRestrict": {"description": "If provided, we will consider the Availability entities provided to be a complete snapshot from [start_time_restrict, end_time_restrict). That is, all existing availability will be deleted if the following condition holds true: ``` start_time_restrict <= availability.start_sec && availability.start_sec < end_time_restrict ``` If a duration message is set, the condition is futher restricted: ``` availability.duration == duration_restrict ``` If a resources_restrict message is set, the condition is further restricted: ``` availability.resources.staff_id == resources_restrict.staff_id && availability.resources.room_id == resources_restrict.room_id ``` These fields are typically used to provide a complete update of availability in a given time range. Setting start_time_restrict while leaving end_time_restrict unset is interpreted to mean all time beginning at start_time_restrict.", "format": "google-datetime", "type": "string"}, "availability": {"type": "array", "description": "The new list of availability.", "items": {"$ref": "Availability"}}, "endTimeRestrict": {"format": "google-datetime", "description": "Setting end_time_restrict while leaving start_time_restrict unset is interpreted to mean all time up to the end_time_restrict.", "type": "string"}}}, "DirectMerchantPayment": {"type": "object", "description": "Information about how the user can pay directly to the merchant instead of pre-paying for the service via RwG.", "id": "DirectMerchantPayment", "properties": {"paymentMethods": {"type": "array", "description": "Users would be advised to pay only via the payment methods mentioned below.", "items": {"$ref": "Text"}}}}, "TokenizationConfig": {"description": "A configuration for payment-processor tokenization, set up on a per-Merchant basis.", "id": "TokenizationConfig", "properties": {"tokenizationParameter": {"additionalProperties": {"type": "string"}, "description": "A tokenization configuration will typically have one tokenization_parameter whose key is \"gateway\" and whose value is the name of the processor. The rest of the parameters are dependent on the processor. See Google Pay's documentation for further information. Braintree example: tokenization_parameter { key: \"gateway\" value: \"braintree\" } tokenization_parameter { key: \"braintree:apiVersion\" value: \"v1\" } tokenization_parameter { key: \"braintree:sdkVersion\" value: \"2.30.0\" } tokenization_parameter { key: \"braintree:merchantId\" value: \"abcdef\" } tokenization_parameter { key: \"braintree:clientKey\" value: \"production_xxx_yyy\" } Stripe example: tokenization_parameter { key: \"gateway\" value: \"stripe\" } tokenization_parameter { key: \"stripe:version\" value: \"2018-02-28\" } tokenization_parameter { key: \"stripe:publishableKey\" value: \"pk_1234\" } Adyen example: tokenization_parameter { key: \"gateway\" value: \"adyen\" } tokenization_parameter { key: \"gatewayMerchantId\" value: \"yourId\" }", "type": "object"}, "billingInformationFormat": {"enumDescriptions": ["Not specified. Defaults to MIN.", "name, country code, and postal code (Google Pay default setting).", "name, street address, locality, region, country code, and postal code."], "type": "string", "description": "Include in the payment token the user's billing information as entered into Google Pay with their FOP (see above). Leaving the field empty is equivalent to specifying MIN.", "enum": ["BILLING_INFORMATION_FORMAT_UNSPECIFIED", "MIN", "FULL"]}}, "type": "object"}, "Order": {"id": "Order", "properties": {"clientInformation": {"description": "Personal information of the client making the order.", "$ref": "ClientInformation"}, "merchantId": {"description": "ID of the merchant that all services in this order belong to.", "type": "string"}, "item": {"description": "Line items in this order.", "type": "array", "items": {"$ref": "LineItem"}}, "paymentInformation": {"$ref": "PaymentInformation", "description": "Information about payment transactions that relate to the order."}, "name": {"type": "string", "description": "Resource name of the order: `partners/{partner ID}/orders/{order ID}`"}}, "type": "object", "description": "An order for service appointments with a merchant."}, "Price": {"properties": {"priceMicros": {"format": "int64", "type": "string", "description": "The price in micro-units of the currency. Fractions of smallest currency unit will be rounded using nearest even rounding. (e.g. For USD 2.5 cents rounded to 2 cents, 3.5 cents rounded to 4 cents, 0.5 cents rounded to 0 cents, 2.51 cents rounded to 3 cents)."}, "pricingOptionTag": {"type": "string", "description": "An optional and opaque string that identifies the pricing option that is associated with the extended price."}, "currencyCode": {"type": "string", "description": "The currency of the price that is defined in ISO 4217."}}, "id": "Price", "type": "object", "description": "The price of a service or a fee."}, "Merchant": {"properties": {"numBookings30d": {"description": "This field is deprecated.", "format": "int64", "type": "string"}, "paymentRestrictions": {"$ref": "PaymentRestrictions", "description": "Restrictions to the payment methods this merchant accepts. We assume no restrictions exist if this field is not set."}, "paymentProcessorConfig": {"$ref": "PaymentProcessorConfig", "description": "Configuration for a tokenized payment processor, if the merchant has support for it."}, "merchantName": {"type": "string", "description": "The merchant_name, telephone, url and geo are used to support matching partner inventory with merchants already present on Google Maps. This information will not be displayed. The name of the merchant."}, "serviceAttribute": {"items": {"$ref": "ServiceAttribute"}, "type": "array", "description": "Definitions for any service attributes used to describe the Services for this Merchant. (optional)"}, "telephone": {"description": "The public telephone number of the merchant including its country and area codes, e.g. +***********.", "type": "string"}, "actionLink": {"description": "Optional. An action URL with associated language, list of countries restricted to, type, and optional platform that indicates which platform this action should be performed on,", "items": {"$ref": "ActionLink"}, "type": "array"}, "category": {"description": "The category of the business in aggregator's platform.", "type": "string"}, "matchingHints": {"$ref": "MerchantMatchingHints", "description": "Hints to help Google match a merchant to a place on Google Maps. Note: most partners do not need to set this field, as Google will match merchants to places on Google Maps using the information provided above. (optional)"}, "brandId": {"description": "An opaque string that identifies the consumer-facing brand to use when displaying partner attribution. This field allows partners with multiple consumer-facing brands to provide merchants for all brands within the same feed. A brand consists of consumer-facing properties like the name, logo, Terms of Service, and Privacy Policy. If there is only one consumer-facing partner brand, this field does not need to be set and can be ignored. If the partner... Does not have multiple consumer-facing brands? --> Ignore this field Has Multiple Brands that are configured? If this field is set --> Associated consumer-facing brand attribution is used If this field is unset or the empty string --> Default consumer-facing brand attribution is used Careful Note: most partners do not need to set this field. If a partner wishes to use this field, they must contact us first to configure separate brands, including the default brand.", "type": "string"}, "geo": {"description": "The Geo info of the merchant, including latitude, longitude, and address.", "$ref": "GeoCoordinates"}, "name": {"type": "string", "description": "The merchant resource name, which has the format of `partners/{partner_id}/merchants/{merchant_id}`."}, "url": {"type": "string", "description": "The url of the merchant's public website."}, "paymentOption": {"items": {"$ref": "PaymentOption"}, "type": "array", "description": "Payment options available for this merchant. Services under this merchant will be able to individually limit the payment options they allow."}, "tokenizationConfig": {"description": "Configuration for a tokenized payment processor, if the merchant has support for it.", "$ref": "TokenizationConfig"}, "terms": {"$ref": "Terms", "description": "The specific merchant's Terms and Conditions displayed to the user when a service is being booked through Reserve with Google. In addition to these the aggregator partner's Terms and Conditions are always displayed to the user and must not be provided here."}, "taxRate": {"$ref": "TaxRate", "description": "The merchant's tax rate. If present this field overrides the deprecated tax_rate_basis_points field. An empty message (i.e. tax_rate { }) will reset the applied tax rate to zero."}, "taxRateBasisPoints": {"format": "uint32", "description": "The merchant's tax rate in basis points (one hundredth of one percent). For example, if the tax rate is 7.5%, this field should be set to 750. If this field is left unset or set to 0, the total price charged to a user for any service provided by this merchant is the exact price specified by Service.price. The service price is assumed to be exempt from or already inclusive of applicable taxes. Taxes will not be shown to the user as a separate line item. If this field is set to any nonzero value, the total price charged to a user for any service provided by this merchant will include the service price plus the tax assessed using the tax rate provided here. Fractions of the smallest currency unit (for example, fractions of one cent) will be rounded using nearest even rounding. Taxes will be shown to the user as a separate line item. This field is deprecated, please use tax_rate instead.", "type": "integer"}}, "description": "Info about a merchant that is on the aggregator's platform.", "type": "object", "id": "Merchant"}, "UserPurchaseRestriction": {"id": "UserPurchaseRestriction", "type": "object", "properties": {"newToMerchant": {"description": "A payment option that can only be purchased by users who have never purchased from the same merchant before.", "type": "boolean"}, "newToPaymentOption": {"type": "boolean", "description": "A payment option that can only be purchased by users who have never purchased the same payment option before."}}, "description": "Restricts the users eligible to purchase a payment option."}, "TicketType": {"description": "TicketType is used to differentiate among tickets with different prices and/or availabilities due to different user types, different service attributes, or different options/add-ons. A ticket is the minimal bookable unit to a service, e.g. a spot on a rafting trip, an admission to a museum, a full day double kayak rental.", "id": "TicketType", "properties": {"price": {"description": "The price of a single ticket of this type, exclusive of any taxes. The tax rate of Service is applied to its tickets.", "$ref": "Price"}, "localizedShortDescription": {"description": "A short description to this TicketType with i18n support. This can be user visible, e.g., “adult”, \"child\", “veteran”, “Row J”, etc. Required, each ticket type should have a description to be user visible. Separate values could be supplied for each locale.", "$ref": "Text"}, "optionDescription": {"description": "Description of any additional option which this ticket type represents, if any. Deprecated, use localized_option_description instead.", "type": "string"}, "ticketTypeId": {"type": "string", "description": "The ticket id is used to differentiate among different ticket types of the same service, and is only expected to be unique within a service."}, "localizedOptionDescription": {"$ref": "Text", "description": "Description of any additional option which this ticket type represents, if any. Separate values could be supplied for each locale. Additional options are useful when the ticket type represents multiple dimensions. Example 1: an admission ticket with different types 'adult', 'child' and language as an additional option, the expected TicketType list would be: - { ticket_type_id: \"ticket_type_1\" localized_short_description { value: \"adult\" } localized_option_description { value: \"english\" } } - { ticket_type_id: \"ticket_type_2\" localized_short_description { value: \"adult\" } localized_option_description { value: \"spanish\" } } - { ticket_type_id: \"ticket_type_3\" localized_short_description { value: \"child\" } localized_option_description { value: \"english\" } } - { ticket_type_id: \"ticket_type_4\" localized_short_description { value: \"child\" } localized_option_description { value: \"spanish\" } } Example 2: an multi-hour kayak rental with optional dry bag add-on, the short_description could be \"3 hours\" and the option_description could be either \"with dry bag\" or \"without dry bag\": - { ticket_type_id: \"ticket_type_1\" localized_short_description { value: \"2 hours\" } localized_option_description { value: \"english\" } } - { ticket_type_id: \"ticket_type_2\" localized_short_description { value: \"2 hours\" } localized_option_description { value: \"spanish\" } } - { ticket_type_id: \"ticket_type_3\" localized_short_description { value: \"3 hours\" } localized_option_description { value: \"english\" } } - { ticket_type_id: \"ticket_type_4\" localized_short_description { value: \"3 hours\" } localized_option_description { value: \"spanish\" } } Optional, but if any ticket type within the service has this field set, we expect all other ticket types to have this field set as well (a default option_description could be used). E.g. [{ticket_type_1, adult, english}, {ticket_type_1, adult, ''}] is not a valid list. Only two HTML formatting tags are supported: "}, "perTicketFee": {"description": "Additional fees for purchasing this ticket. (optional)", "$ref": "PerTicket<PERSON>ee"}, "shortDescription": {"description": "A short description to this TicketType. This can be user visible, e.g., “adult”, \"child\", “veteran”, “Row J”, etc. Required, each ticket type should have a description to be user visible. Deprecated, use localized_short_description instead.", "type": "string"}}, "type": "object"}, "Empty": {"type": "object", "properties": {}, "description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); } The JSON representation for `Empty` is empty JSON object `{}`.", "id": "Empty"}}, "description": "Scheduling aggregators call this API to notify us of appointment updates, and update their inventories including merchants, services, and availability.", "resources": {"notification": {"resources": {"partners": {"resources": {"orders": {"methods": {"patch": {"description": "Updates an existing Order.", "request": {"$ref": "Order"}, "httpMethod": "PATCH", "parameters": {"updateMask": {"description": "Field mask of all order fields to be updated", "type": "string", "location": "query", "format": "google-fieldmask"}, "name": {"type": "string", "location": "path", "required": true, "pattern": "^partners/[^/]+/orders/[^/]+$", "description": "Resource name of the order: `partners/{partner ID}/orders/{order ID}`"}}, "response": {"$ref": "Order"}, "id": "mapsbooking.notification.partners.orders.patch", "flatPath": "v1alpha/notification/partners/{partnersId}/orders/{ordersId}", "parameterOrder": ["name"], "path": "v1alpha/notification/{+name}"}}}, "bookings": {"methods": {"patch": {"flatPath": "v1alpha/notification/partners/{partnersId}/bookings/{bookingsId}", "path": "v1alpha/notification/{+name}", "httpMethod": "PATCH", "parameterOrder": ["name"], "parameters": {"bookingFailure.paymentFailure.threeds1Parameters.acsUrl": {"description": "The URL from which to load a form to present to the User for authentication.", "type": "string", "location": "query"}, "bookingFailure.paymentFailure.threeds1Parameters.transactionId": {"type": "string", "description": "An identifier used by the ACS provider. To be posted to the ACSUrl form if supplied.", "location": "query"}, "bookingFailure.paymentFailure.threeds1Parameters.paReq": {"type": "string", "description": "A PaymentAuthentication Request. To be posted to the ACSUrl form if supplied.", "location": "query"}, "bookingFailure.cause": {"type": "string", "location": "query", "enumDescriptions": ["Default value: Don't use; amounts to an \"unknown error\"", "The referenced availability slot is not available any longer.", "The user has already booked an appointment for the referenced availability slot.", "The lease (if provided) has expired and cannot be used any longer to complete the requested booking.", "The requested cancellation cannot be performed at the current time due to time restrictions in the merchant's cancellation policy.", "An error was encountered while processing the payment because the provided credit card type was not accepted by the merchant. The credit card type must be supplied in rejected_card_type.", "An error was encountered while processing the payment because the provided credit card was declined.", "An error was encountered with the pack/membership used to pay for the booking. There could be no valid uses left, it could have expired, etc.", "An error was encountered while processing the payment for this booking. Use this value to indicate a general payment related error, only if the error does not match to a specific payment error above.", "User cannot use the given payment option (e.g. user trying to use a first time price for the second time).", "A booking that the user tried to cancel has already been cancelled.", "A booking that the user tried to cancel is not cancellable.", "User has an existing reservation too close to this time.", "Booking failed due to the user being over the aggregator's per-user bookings limit.", "Set when payment is rejected because you are requesting that the transaction be tried again, but this time after undergoing 3DS1 challenge/response. Note that the current transaction's failure state will stay failed. The retry will be completely separate. When this is the failure reason, payment_failure.3DS1_parameters MUST be set. If it is not, then the current cause will be treated as if it were PAYMENT_ERROR."], "enum": ["CAUSE_UNSPECIFIED", "SLOT_UNAVAILABLE", "SLOT_ALREADY_BOOKED_BY_USER", "LEASE_EXPIRED", "OUTSIDE_CANCELLATION_WINDOW", "PAYMENT_ERROR_CARD_TYPE_REJECTED", "PAYMENT_ERROR_CARD_DECLINED", "PAYMENT_OPTION_NOT_VALID", "PAYMENT_ERROR", "USER_CANNOT_USE_PAYMENT_OPTION", "BOOKING_ALREADY_CANCELLED", "BOOKING_NOT_CANCELLABLE", "OVERLAPPING_RESERVATION", "USER_OVER_BOOKING_LIMIT", "PAYMENT_REQUIRES_3DS1"], "description": "The reason why the booking failed. (required)"}, "bookingFailure.paymentFailure.threeds1Parameters.mdMerchantData": {"type": "string", "location": "query", "description": "Merchant data. To be posted to the ACSUrl form if supplied."}, "bookingFailure.rejectedCardType": {"location": "query", "description": "(required only if cause is PAYMENT_ERROR_CARD_TYPE_REJECTED)", "enumDescriptions": ["Unused.", "A Visa credit card.", "A Mastercard credit card.", "An American Express credit card.", "A Discover credit card.", "A JCB credit card."], "type": "string", "enum": ["CREDIT_CARD_TYPE_UNSPECIFIED", "VISA", "MASTERCARD", "AMERICAN_EXPRESS", "DISCOVER", "JCB"]}, "updateMask": {"format": "google-fieldmask", "location": "query", "description": "Field mask of all booking fields to be updated", "type": "string"}, "name": {"description": "Resource name of the booking: `partners/{partner ID}/bookings/{booking ID}`", "pattern": "^partners/[^/]+/bookings/[^/]+$", "location": "path", "type": "string", "required": true}, "bookingFailure.description": {"location": "query", "type": "string", "description": "This optional field is used for the partner to include additional information for debugging purpose only. (optional)"}}, "description": "Updates an existing Booking.", "request": {"$ref": "Booking"}, "id": "mapsbooking.notification.partners.bookings.patch", "response": {"$ref": "Booking"}}}}}}}}, "inventory": {"resources": {"partners": {"resources": {"feeds": {"methods": {"getStatus": {"path": "v1alpha/inventory/{+name}/status", "flatPath": "v1alpha/inventory/partners/{partnersId}/feeds/{feedsId}/{feedsId1}/status", "id": "mapsbooking.inventory.partners.feeds.getStatus", "httpMethod": "GET", "description": "Retrieves the Status of a previously uploaded feed by the specified aggregator, and returns it.", "response": {"$ref": "FeedStatus"}, "parameterOrder": ["name"], "parameters": {"name": {"type": "string", "location": "path", "required": true, "pattern": "^partners/[^/]+/feeds/[^/]+/[^/]+$", "description": "The feed resource name, which has the format of - `partners/{partner_id}/feeds/merchants/{file_name}` - `partners/{partner_id}/feeds/services/{file_name}` - `partners/{partner_id}/feeds/availability/{file_name}`"}}}}, "resources": {"status": {"methods": {"list": {"response": {"$ref": "ListStatusResponse"}, "parameters": {"pageToken": {"description": "The next_page_token value returned from a previous List request, if any.", "location": "query", "type": "string"}, "timeRangeRestrict.startTime": {"type": "string", "location": "query", "format": "google-datetime", "description": "The lower bound of the time range."}, "pageSize": {"location": "query", "type": "integer", "format": "int32", "description": "The maximum number of items to return."}, "stateRestrict": {"location": "query", "description": "Optional restrict to filter results by feed state.", "type": "string", "enum": ["STATE_UNSPECIFIED", "IN_PROGRESS", "SUCCESS", "FAILURE"], "enumDescriptions": ["Default value. Unused.", "The feed is still being processed.", "The feed has been successfully processed.", "We encountered an error while processing the feed."]}, "name": {"required": true, "location": "path", "type": "string", "pattern": "^partners/[^/]+/feeds/[^/]+$", "description": "The feed resource name, which has the format of - `partners/{partner_id}/feeds/merchants` - `partners/{partner_id}/feeds/services` - `partners/{partner_id}/feeds/availability`"}, "timeRangeRestrict.endTime": {"description": "The upper bound of the time range.", "type": "string", "format": "google-datetime", "location": "query"}}, "flatPath": "v1alpha/inventory/partners/{partnersId}/feeds/{feedsId}/status", "httpMethod": "GET", "id": "mapsbooking.inventory.partners.feeds.status.list", "path": "v1alpha/inventory/{+name}/status", "parameterOrder": ["name"], "description": "Retrieves the Status of multiple previously uploaded merchant, service, or availability feeds by the specified aggregator, and returns them."}}}}}, "merchants": {"methods": {"create": {"parameterOrder": ["parent"], "description": "Creates a new Merchant managed by the specified aggregator, and returns it.", "flatPath": "v1alpha/inventory/partners/{partnersId}/merchants", "path": "v1alpha/inventory/{+parent}/merchants", "request": {"$ref": "Merchant"}, "response": {"$ref": "Merchant"}, "httpMethod": "POST", "parameters": {"parent": {"type": "string", "required": true, "pattern": "^partners/[^/]+$", "description": "The parent resource name for the partner who owns this merchant, in the format of `partners/{partner_id}`.", "location": "path"}, "merchantId": {"description": "The merchant id to use for this merchant.", "location": "query", "type": "string"}}, "id": "mapsbooking.inventory.partners.merchants.create"}, "patch": {"response": {"$ref": "Merchant"}, "request": {"$ref": "Merchant"}, "id": "mapsbooking.inventory.partners.merchants.patch", "parameterOrder": ["name"], "description": "Updates an existing Merchant managed by the specified aggregator, and returns it.", "httpMethod": "PATCH", "path": "v1alpha/inventory/{+name}", "flatPath": "v1alpha/inventory/partners/{partnersId}/merchants/{merchantsId}", "parameters": {"updateMask": {"description": "The specific fields to update for the merchant.", "format": "google-fieldmask", "type": "string", "location": "query"}, "name": {"type": "string", "required": true, "description": "The merchant resource name, which has the format of `partners/{partner_id}/merchants/{merchant_id}`.", "location": "path", "pattern": "^partners/[^/]+/merchants/[^/]+$"}}}, "delete": {"response": {"$ref": "Empty"}, "description": "Deletes an existing Merchant managed by the specified aggregator. All the merchant's services and availability will be disabled, too. If you re-add the merchant later, the same set of services and availability slots will appear.", "id": "mapsbooking.inventory.partners.merchants.delete", "httpMethod": "DELETE", "flatPath": "v1alpha/inventory/partners/{partnersId}/merchants/{merchantsId}", "parameters": {"name": {"required": true, "pattern": "^partners/[^/]+/merchants/[^/]+$", "location": "path", "description": "The resource name of the merchant to delete. In the format of partners/{partner_id}/merchants/{merchant_id}", "type": "string"}}, "parameterOrder": ["name"], "path": "v1alpha/inventory/{+name}"}}, "resources": {"services": {"resources": {"availability": {"methods": {"replace": {"httpMethod": "POST", "flatPath": "v1alpha/inventory/partners/{partnersId}/merchants/{merchantsId}/services/{servicesId}/availability:replace", "response": {"$ref": "ServiceAvailability"}, "parameters": {"name": {"location": "path", "required": true, "description": "The resource name of the service to apply this to. In the format of `partners/{partner_id}/merchants/{merchant_id}/services/{service_id}`", "pattern": "^partners/[^/]+/merchants/[^/]+/services/[^/]+$", "type": "string"}}, "parameterOrder": ["name"], "description": "Replaces the Availability of an existing Service of a merchant managed by the specified aggregator, and returns it.", "request": {"$ref": "ReplaceServiceAvailabilityRequest"}, "id": "mapsbooking.inventory.partners.merchants.services.availability.replace", "path": "v1alpha/inventory/{+name}/availability:replace"}}}}, "methods": {"delete": {"id": "mapsbooking.inventory.partners.merchants.services.delete", "httpMethod": "DELETE", "flatPath": "v1alpha/inventory/partners/{partnersId}/merchants/{merchantsId}/services/{servicesId}", "response": {"$ref": "Empty"}, "parameterOrder": ["name"], "path": "v1alpha/inventory/{+name}", "description": "Deletes an existing Service of a merchant managed by the specified aggregator. All the service's availability will be deleted, too.", "parameters": {"name": {"type": "string", "location": "path", "description": "The resource name of the service to delete. In the format of `partners/{partner_id}/merchants/{merchant_id}/services/{service_id}`", "required": true, "pattern": "^partners/[^/]+/merchants/[^/]+/services/[^/]+$"}}}, "create": {"parameterOrder": ["parent"], "parameters": {"serviceId": {"location": "query", "description": "The service id to use for this service.", "type": "string"}, "parent": {"required": true, "description": "The parent resource name for the merchant who owns this service, in the format of `partners/{partner_id}/merchants/{merchant_id}`.", "location": "path", "pattern": "^partners/[^/]+/merchants/[^/]+$", "type": "string"}}, "request": {"$ref": "Service"}, "flatPath": "v1alpha/inventory/partners/{partnersId}/merchants/{merchantsId}/services", "id": "mapsbooking.inventory.partners.merchants.services.create", "path": "v1alpha/inventory/{+parent}/services", "response": {"$ref": "Service"}, "description": "Creates a new Service of a merchant managed by the specified aggregator, and returns it.", "httpMethod": "POST"}, "patch": {"httpMethod": "PATCH", "parameters": {"updateMask": {"description": "The specific fields to update for the service.", "type": "string", "location": "query", "format": "google-fieldmask"}, "name": {"location": "path", "required": true, "description": "The service resource name, which has the format of `partners/{partner_id}/merchants/{merchant_id}/services/{service_id}`.", "pattern": "^partners/[^/]+/merchants/[^/]+/services/[^/]+$", "type": "string"}}, "parameterOrder": ["name"], "path": "v1alpha/inventory/{+name}", "response": {"$ref": "Service"}, "flatPath": "v1alpha/inventory/partners/{partnersId}/merchants/{merchantsId}/services/{servicesId}", "description": "Updates an existing Service of a merchant managed by the specified aggregator, and returns it.", "request": {"$ref": "Service"}, "id": "mapsbooking.inventory.partners.merchants.services.patch"}}}}}, "availability": {"methods": {"replace": {"request": {"$ref": "BatchReplaceServiceAvailabilityRequest"}, "flatPath": "v1alpha/inventory/partners/{partnersId}/availability:replace", "response": {"$ref": "BatchReplaceServiceAvailabilityResponse"}, "path": "v1alpha/inventory/{+parent}/availability:replace", "parameters": {"parent": {"type": "string", "pattern": "^partners/[^/]+$", "required": true, "location": "path", "description": "Format of `partners/{partner_id}`."}}, "description": "Replaces the Availability of existing Services. This batch call does not guarantee atomicity. Only successfully updated availability slots will be returned.", "httpMethod": "POST", "parameterOrder": ["parent"], "id": "mapsbooking.inventory.partners.availability.replace"}}}}}}}}, "servicePath": ""}