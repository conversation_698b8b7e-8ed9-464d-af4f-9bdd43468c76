# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: definitions.proto

from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='definitions.proto',
  package='',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=b'\n\x11\x64\x65\x66initions.proto\"\x8f\x02\n\x0c\x46\x65\x65\x64Metadata\x12\x43\n\x16processing_instruction\x18\x01 \x01(\x0e\x32#.FeedMetadata.ProcessingInstruction\x12\x14\n\x0cshard_number\x18\x02 \x01(\x05\x12\x14\n\x0ctotal_shards\x18\x03 \x01(\x05\x12\r\n\x05nonce\x18\x05 \x01(\x04\x12\x1c\n\x14generation_timestamp\x18\x04 \x01(\x03\"a\n\x15ProcessingInstruction\x12\x13\n\x0fPROCESS_UNKNOWN\x10\x00\x12\x17\n\x13PROCESS_AS_COMPLETE\x10\x01\x12\x1a\n\x16PROCESS_AS_INCREMENTAL\x10\x02\"L\n\x0cMerchantFeed\x12\x1f\n\x08metadata\x18\x01 \x01(\x0b\x32\r.FeedMetadata\x12\x1b\n\x08merchant\x18\x02 \x03(\x0b\x32\t.Merchant\"\xf8\x01\n\x08Merchant\x12\x13\n\x0bmerchant_id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x11\n\ttelephone\x18\x03 \x01(\t\x12\x0b\n\x03url\x18\x04 \x01(\t\x12\x1c\n\x03geo\x18\x05 \x01(\x0b\x32\x0f.GeoCoordinates\x12\x10\n\x08\x63\x61tegory\x18\x06 \x01(\t\x12\x18\n\x10num_bookings_30d\x18\x07 \x01(\x03\x12!\n\x15tax_rate_basis_points\x18\x08 \x01(\rB\x02\x18\x01\x12\x1a\n\x08tax_rate\x18\t \x01(\x0b\x32\x08.TaxRate\x12 \n\x0b\x61\x63tion_link\x18\x14 \x03(\x0b\x32\x0b.ActionLink\"V\n\x0eGeoCoordinates\x12\x10\n\x08latitude\x18\x01 \x01(\x01\x12\x11\n\tlongitude\x18\x02 \x01(\x01\x12\x1f\n\x07\x61\x64\x64ress\x18\x03 \x01(\x0b\x32\x0e.PostalAddress\"o\n\rPostalAddress\x12\x0f\n\x07\x63ountry\x18\x01 \x01(\t\x12\x10\n\x08locality\x18\x02 \x01(\t\x12\x0e\n\x06region\x18\x03 \x01(\t\x12\x13\n\x0bpostal_code\x18\x04 \x01(\t\x12\x16\n\x0estreet_address\x18\x05 \x01(\t\"\x1f\n\x07TaxRate\x12\x14\n\x0cmicroPercent\x18\x01 \x01(\x03\"Q\n\tResources\x12\x0f\n\x07staffId\x18\x01 \x01(\t\x12\x11\n\tstaffName\x18\x02 \x01(\t\x12\x0e\n\x06roomId\x18\x03 \x01(\t\x12\x10\n\x08roomName\x18\x04 \x01(\t\"I\n\x0bServiceFeed\x12\x1f\n\x08metadata\x18\x01 \x01(\x0b\x32\r.FeedMetadata\x12\x19\n\x07service\x18\x02 \x03(\x0b\x32\x08.Service\"\xb8\x06\n\x07Service\x12\x13\n\x0bmerchant_id\x18\x01 \x01(\t\x12\x12\n\nservice_id\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12\x15\n\x05price\x18\x05 \x01(\x0b\x32\x06.Price\x12\x1f\n\x05rules\x18\x06 \x01(\x0b\x32\x10.SchedulingRules\x12 \n\x04\x66orm\x18\x07 \x03(\x0b\x32\x12.ServiceIntakeForm\x12\x30\n\x0fprepayment_type\x18\x08 \x01(\x0e\x32\x17.Service.PrepaymentType\x12\x1a\n\x08tax_rate\x18\t \x01(\x0b\x32\x08.TaxRate\x12(\n\x0fvirtual_session\x18# \x01(\x0b\x32\x0f.VirtualSession\x12 \n\x0b\x61\x63tion_link\x18\x0e \x03(\x0b\x32\x0b.ActionLink\x12\"\n\x04type\x18\x0f \x01(\x0e\x32\x14.Service.ServiceType\x12\x37\n\x17\x64irect_merchant_payment\x18$ \x01(\x0b\x32\x16.DirectMerchantPayment\"`\n\x0ePrepaymentType\x12\x1f\n\x1bPREPAYMENT_TYPE_UNSPECIFIED\x10\x00\x12\x0c\n\x08REQUIRED\x10\x01\x12\x0c\n\x08OPTIONAL\x10\x02\x12\x11\n\rNOT_SUPPORTED\x10\x03\"\xad\x02\n\x0bServiceType\x12\x1c\n\x18SERVICE_TYPE_UNSPECIFIED\x10\x00\x12#\n\x1fSERVICE_TYPE_DINING_RESERVATION\x10\x01\x12\x1e\n\x1aSERVICE_TYPE_FOOD_ORDERING\x10\x02\x12\x1e\n\x1aSERVICE_TYPE_FOOD_DELIVERY\x10\x06\x12\x1d\n\x19SERVICE_TYPE_FOOD_TAKEOUT\x10\x07\x12\x1d\n\x19SERVICE_TYPE_EVENT_TICKET\x10\x03\x12\x1a\n\x16SERVICE_TYPE_TRIP_TOUR\x10\x04\x12\x1c\n\x18SERVICE_TYPE_APPOINTMENT\x10\x05\x12#\n\x1fSERVICE_TYPE_ONLINE_APPOINTMENT\x10\x08\"P\n\x05Price\x12\x14\n\x0cprice_micros\x18\x01 \x01(\x03\x12\x15\n\rcurrency_code\x18\x02 \x01(\t\x12\x1a\n\x12pricing_option_tag\x18\x03 \x01(\t\"T\n\x0fSchedulingRules\x12\x1b\n\x13min_advance_booking\x18\x01 \x01(\x03\x12$\n\x1cmin_advance_online_canceling\x18\x02 \x01(\x03\"\x87\x02\n\x16ServiceIntakeFormField\x12/\n\x04type\x18\x01 \x01(\x0e\x32!.ServiceIntakeFormField.FieldType\x12\r\n\x05label\x18\x02 \x01(\t\x12\r\n\x05value\x18\x03 \x03(\t\x12\x13\n\x0bis_required\x18\x04 \x01(\x08\"\x88\x01\n\tFieldType\x12\x1a\n\x16\x46IELD_TYPE_UNSPECIFIED\x10\x00\x12\x10\n\x0cSHORT_ANSWER\x10\x01\x12\r\n\tPARAGRAPH\x10\x02\x12\x13\n\x0fMULTIPLE_CHOICE\x10\x03\x12\x0e\n\nCHECKBOXES\x10\x04\x12\x0c\n\x08\x44ROPDOWN\x10\x05\x12\x0b\n\x07\x42OOLEAN\x10\x06\"v\n\x11ServiceIntakeForm\x12&\n\x05\x66ield\x18\x01 \x03(\x0b\x32\x17.ServiceIntakeFormField\x12\x1c\n\x14\x66irst_time_customers\x18\x02 \x01(\x08\x12\x1b\n\x13returning_customers\x18\x03 \x01(\x08\"g\n\x10\x41vailabilityFeed\x12\x1f\n\x08metadata\x18\x01 \x01(\x0b\x32\r.FeedMetadata\x12\x32\n\x14service_availability\x18\x02 \x03(\x0b\x32\x14.ServiceAvailability\"\xdf\x01\n\x13ServiceAvailability\x12 \n\x18start_timestamp_restrict\x18\x01 \x01(\x03\x12\x1e\n\x16\x65nd_timestamp_restrict\x18\x02 \x01(\x03\x12\x1c\n\x14merchant_id_restrict\x18\x03 \x01(\t\x12\x1b\n\x13service_id_restrict\x18\x04 \x01(\t\x12&\n\x12resources_restrict\x18\x06 \x01(\x0b\x32\n.Resources\x12#\n\x0c\x61vailability\x18\x05 \x03(\x0b\x32\r.Availability\"\xbf\x03\n\x0c\x41vailability\x12\x13\n\x0bmerchant_id\x18\x01 \x01(\t\x12\x12\n\nservice_id\x18\x02 \x01(\t\x12\x11\n\tstart_sec\x18\x03 \x01(\x03\x12\x14\n\x0c\x64uration_sec\x18\x04 \x01(\x03\x12\x13\n\x0bspots_total\x18\x05 \x01(\x03\x12\x12\n\nspots_open\x18\x06 \x01(\x03\x12\x18\n\x10\x61vailability_tag\x18\x07 \x01(\t\x12\x1d\n\tresources\x18\x08 \x01(\x0b\x32\n.Resources\x12\x19\n\x11payment_option_id\x18\t \x03(\t\x12,\n\nrecurrence\x18\n \x01(\x0b\x32\x18.Availability.Recurrence\x12;\n\x12schedule_exception\x18\x0b \x03(\x0b\x32\x1f.Availability.ScheduleException\x1a@\n\nRecurrence\x12\x18\n\x10repeat_until_sec\x18\x01 \x01(\x03\x12\x18\n\x10repeat_every_sec\x18\x02 \x01(\x05\x1a\x33\n\x11ScheduleException\x12\x1e\n\ntime_range\x18\x01 \x01(\x0b\x32\n.TimeRange\"/\n\tTimeRange\x12\x11\n\tbegin_sec\x18\x01 \x01(\x03\x12\x0f\n\x07\x65nd_sec\x18\x02 \x01(\x03\"\xc2\x03\n\x0eVirtualSession\x12#\n\x14session_instructions\x18\x01 \x01(\x0b\x32\x05.Text\x12#\n\x14session_requirements\x18\x02 \x01(\x0b\x32\x05.Text\x12\x42\n\x15virtual_platform_info\x18\x03 \x01(\x0b\x32#.VirtualSession.VirtualPlatformInfo\x12\x1e\n\x16is_session_prerecorded\x18\x04 \x01(\x08\x1a\x81\x02\n\x13VirtualPlatformInfo\x12>\n\x08platform\x18\x01 \x01(\x0e\x32,.VirtualSession.VirtualPlatformInfo.Platform\x12\"\n\x13other_platform_name\x18\x02 \x01(\x0b\x32\x05.Text\"\x85\x01\n\x08Platform\x12\x18\n\x14PLATFORM_UNSPECIFIED\x10\x00\x12\x0c\n\x08\x46LEXIBLE\x10\x01\x12\x13\n\x0fGOOGLE_HANGOUTS\x10\x02\x12\x0f\n\x0bGOOGLE_MEET\x10\x03\x12\x08\n\x04ZOOM\x10\x04\x12\t\n\x05SKYPE\x10\x05\x12\x0b\n\x07YOUTUBE\x10\x06\x12\t\n\x05OTHER\x10\x07\"7\n\x15\x44irectMerchantPayment\x12\x1e\n\x0fpayment_methods\x18\x01 \x03(\x0b\x32\x05.Text\"@\n\x04Text\x12\r\n\x05value\x18\x01 \x01(\t\x12)\n\x0flocalized_value\x18\x02 \x03(\x0b\x32\x10.LocalizedString\"0\n\x0fLocalizedString\x12\x0e\n\x06locale\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"\xcc\x03\n\nActionLink\x12\x0b\n\x03url\x18\x01 \x01(\t\x12\x10\n\x08language\x18\x02 \x01(\t\x12\x1a\n\x12restricted_country\x18\x03 \x03(\t\x12!\n\x08platform\x18\x04 \x01(\x0e\x32\x0f.ActionPlatform\x12\x34\n\x10\x61\x63tion_link_type\x18\x05 \x01(\x0e\x32\x1a.ActionLink.ActionLinkType\"\xa9\x02\n\x0e\x41\x63tionLinkType\x12 \n\x1c\x41\x43TION_LINK_TYPE_UNSPECIFIED\x10\x00\x12%\n!ACTION_LINK_TYPE_BOOK_APPOINTMENT\x10\x01\x12,\n(ACTION_LINK_TYPE_BOOK_ONLINE_APPOINTMENT\x10\x02\x12\x1f\n\x1b\x41\x43TION_LINK_TYPE_ORDER_FOOD\x10\x03\x12(\n$ACTION_LINK_TYPE_ORDER_FOOD_DELIVERY\x10\x04\x12\'\n#ACTION_LINK_TYPE_ORDER_FOOD_TAKEOUT\x10\x05\x12,\n(ACTION_LINK_TYPE_MAKE_DINING_RESERVATION\x10\x06*\xac\x01\n\x0e\x41\x63tionPlatform\x12\x1f\n\x1b\x41\x43TION_PLATFORM_UNSPECIFIED\x10\x00\x12#\n\x1f\x41\x43TION_PLATFORM_WEB_APPLICATION\x10\x01\x12\x1e\n\x1a\x41\x43TION_PLATFORM_MOBILE_WEB\x10\x02\x12\x1b\n\x17\x41\x43TION_PLATFORM_ANDROID\x10\x03\x12\x17\n\x13\x41\x43TION_PLATFORM_IOS\x10\x04\x62\x06proto3'
)

_ACTIONPLATFORM = _descriptor.EnumDescriptor(
  name='ActionPlatform',
  full_name='ActionPlatform',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ACTION_PLATFORM_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ACTION_PLATFORM_WEB_APPLICATION', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ACTION_PLATFORM_MOBILE_WEB', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ACTION_PLATFORM_ANDROID', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ACTION_PLATFORM_IOS', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=4317,
  serialized_end=4489,
)
_sym_db.RegisterEnumDescriptor(_ACTIONPLATFORM)

ActionPlatform = enum_type_wrapper.EnumTypeWrapper(_ACTIONPLATFORM)
ACTION_PLATFORM_UNSPECIFIED = 0
ACTION_PLATFORM_WEB_APPLICATION = 1
ACTION_PLATFORM_MOBILE_WEB = 2
ACTION_PLATFORM_ANDROID = 3
ACTION_PLATFORM_IOS = 4


_FEEDMETADATA_PROCESSINGINSTRUCTION = _descriptor.EnumDescriptor(
  name='ProcessingInstruction',
  full_name='FeedMetadata.ProcessingInstruction',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='PROCESS_UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PROCESS_AS_COMPLETE', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PROCESS_AS_INCREMENTAL', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=196,
  serialized_end=293,
)
_sym_db.RegisterEnumDescriptor(_FEEDMETADATA_PROCESSINGINSTRUCTION)

_SERVICE_PREPAYMENTTYPE = _descriptor.EnumDescriptor(
  name='PrepaymentType',
  full_name='Service.PrepaymentType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='PREPAYMENT_TYPE_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='REQUIRED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OPTIONAL', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='NOT_SUPPORTED', index=3, number=3,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1441,
  serialized_end=1537,
)
_sym_db.RegisterEnumDescriptor(_SERVICE_PREPAYMENTTYPE)

_SERVICE_SERVICETYPE = _descriptor.EnumDescriptor(
  name='ServiceType',
  full_name='Service.ServiceType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='SERVICE_TYPE_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SERVICE_TYPE_DINING_RESERVATION', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SERVICE_TYPE_FOOD_ORDERING', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SERVICE_TYPE_FOOD_DELIVERY', index=3, number=6,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SERVICE_TYPE_FOOD_TAKEOUT', index=4, number=7,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SERVICE_TYPE_EVENT_TICKET', index=5, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SERVICE_TYPE_TRIP_TOUR', index=6, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SERVICE_TYPE_APPOINTMENT', index=7, number=5,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SERVICE_TYPE_ONLINE_APPOINTMENT', index=8, number=8,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1540,
  serialized_end=1841,
)
_sym_db.RegisterEnumDescriptor(_SERVICE_SERVICETYPE)

_SERVICEINTAKEFORMFIELD_FIELDTYPE = _descriptor.EnumDescriptor(
  name='FieldType',
  full_name='ServiceIntakeFormField.FieldType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='FIELD_TYPE_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SHORT_ANSWER', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PARAGRAPH', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='MULTIPLE_CHOICE', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CHECKBOXES', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DROPDOWN', index=5, number=5,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BOOLEAN', index=6, number=6,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=2139,
  serialized_end=2275,
)
_sym_db.RegisterEnumDescriptor(_SERVICEINTAKEFORMFIELD_FIELDTYPE)

_VIRTUALSESSION_VIRTUALPLATFORMINFO_PLATFORM = _descriptor.EnumDescriptor(
  name='Platform',
  full_name='VirtualSession.VirtualPlatformInfo.Platform',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='PLATFORM_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FLEXIBLE', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='GOOGLE_HANGOUTS', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='GOOGLE_MEET', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ZOOM', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SKYPE', index=5, number=5,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='YOUTUBE', index=6, number=6,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OTHER', index=7, number=7,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=3545,
  serialized_end=3678,
)
_sym_db.RegisterEnumDescriptor(_VIRTUALSESSION_VIRTUALPLATFORMINFO_PLATFORM)

_ACTIONLINK_ACTIONLINKTYPE = _descriptor.EnumDescriptor(
  name='ActionLinkType',
  full_name='ActionLink.ActionLinkType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ACTION_LINK_TYPE_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ACTION_LINK_TYPE_BOOK_APPOINTMENT', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ACTION_LINK_TYPE_BOOK_ONLINE_APPOINTMENT', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ACTION_LINK_TYPE_ORDER_FOOD', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ACTION_LINK_TYPE_ORDER_FOOD_DELIVERY', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ACTION_LINK_TYPE_ORDER_FOOD_TAKEOUT', index=5, number=5,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ACTION_LINK_TYPE_MAKE_DINING_RESERVATION', index=6, number=6,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=4017,
  serialized_end=4314,
)
_sym_db.RegisterEnumDescriptor(_ACTIONLINK_ACTIONLINKTYPE)


_FEEDMETADATA = _descriptor.Descriptor(
  name='FeedMetadata',
  full_name='FeedMetadata',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='processing_instruction', full_name='FeedMetadata.processing_instruction', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shard_number', full_name='FeedMetadata.shard_number', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_shards', full_name='FeedMetadata.total_shards', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='nonce', full_name='FeedMetadata.nonce', index=3,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='generation_timestamp', full_name='FeedMetadata.generation_timestamp', index=4,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _FEEDMETADATA_PROCESSINGINSTRUCTION,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=22,
  serialized_end=293,
)


_MERCHANTFEED = _descriptor.Descriptor(
  name='MerchantFeed',
  full_name='MerchantFeed',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='metadata', full_name='MerchantFeed.metadata', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='merchant', full_name='MerchantFeed.merchant', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=295,
  serialized_end=371,
)


_MERCHANT = _descriptor.Descriptor(
  name='Merchant',
  full_name='Merchant',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='merchant_id', full_name='Merchant.merchant_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='Merchant.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='telephone', full_name='Merchant.telephone', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='url', full_name='Merchant.url', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='geo', full_name='Merchant.geo', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category', full_name='Merchant.category', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='num_bookings_30d', full_name='Merchant.num_bookings_30d', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate_basis_points', full_name='Merchant.tax_rate_basis_points', index=7,
      number=8, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='Merchant.tax_rate', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action_link', full_name='Merchant.action_link', index=9,
      number=20, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=374,
  serialized_end=622,
)


_GEOCOORDINATES = _descriptor.Descriptor(
  name='GeoCoordinates',
  full_name='GeoCoordinates',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='latitude', full_name='GeoCoordinates.latitude', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='longitude', full_name='GeoCoordinates.longitude', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='address', full_name='GeoCoordinates.address', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=624,
  serialized_end=710,
)


_POSTALADDRESS = _descriptor.Descriptor(
  name='PostalAddress',
  full_name='PostalAddress',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='country', full_name='PostalAddress.country', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='locality', full_name='PostalAddress.locality', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region', full_name='PostalAddress.region', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='postal_code', full_name='PostalAddress.postal_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='street_address', full_name='PostalAddress.street_address', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=712,
  serialized_end=823,
)


_TAXRATE = _descriptor.Descriptor(
  name='TaxRate',
  full_name='TaxRate',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='microPercent', full_name='TaxRate.microPercent', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=825,
  serialized_end=856,
)


_RESOURCES = _descriptor.Descriptor(
  name='Resources',
  full_name='Resources',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='staffId', full_name='Resources.staffId', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='staffName', full_name='Resources.staffName', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='roomId', full_name='Resources.roomId', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='roomName', full_name='Resources.roomName', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=858,
  serialized_end=939,
)


_SERVICEFEED = _descriptor.Descriptor(
  name='ServiceFeed',
  full_name='ServiceFeed',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='metadata', full_name='ServiceFeed.metadata', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='service', full_name='ServiceFeed.service', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=941,
  serialized_end=1014,
)


_SERVICE = _descriptor.Descriptor(
  name='Service',
  full_name='Service',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='merchant_id', full_name='Service.merchant_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='service_id', full_name='Service.service_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='Service.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='Service.description', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='Service.price', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rules', full_name='Service.rules', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='form', full_name='Service.form', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='prepayment_type', full_name='Service.prepayment_type', index=7,
      number=8, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='Service.tax_rate', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='virtual_session', full_name='Service.virtual_session', index=9,
      number=35, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action_link', full_name='Service.action_link', index=10,
      number=14, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='Service.type', index=11,
      number=15, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direct_merchant_payment', full_name='Service.direct_merchant_payment', index=12,
      number=36, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _SERVICE_PREPAYMENTTYPE,
    _SERVICE_SERVICETYPE,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1017,
  serialized_end=1841,
)


_PRICE = _descriptor.Descriptor(
  name='Price',
  full_name='Price',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='price_micros', full_name='Price.price_micros', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='currency_code', full_name='Price.currency_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pricing_option_tag', full_name='Price.pricing_option_tag', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1843,
  serialized_end=1923,
)


_SCHEDULINGRULES = _descriptor.Descriptor(
  name='SchedulingRules',
  full_name='SchedulingRules',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='min_advance_booking', full_name='SchedulingRules.min_advance_booking', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_advance_online_canceling', full_name='SchedulingRules.min_advance_online_canceling', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1925,
  serialized_end=2009,
)


_SERVICEINTAKEFORMFIELD = _descriptor.Descriptor(
  name='ServiceIntakeFormField',
  full_name='ServiceIntakeFormField',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='ServiceIntakeFormField.type', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='label', full_name='ServiceIntakeFormField.label', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='ServiceIntakeFormField.value', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_required', full_name='ServiceIntakeFormField.is_required', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _SERVICEINTAKEFORMFIELD_FIELDTYPE,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2012,
  serialized_end=2275,
)


_SERVICEINTAKEFORM = _descriptor.Descriptor(
  name='ServiceIntakeForm',
  full_name='ServiceIntakeForm',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='field', full_name='ServiceIntakeForm.field', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='first_time_customers', full_name='ServiceIntakeForm.first_time_customers', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='returning_customers', full_name='ServiceIntakeForm.returning_customers', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2277,
  serialized_end=2395,
)


_AVAILABILITYFEED = _descriptor.Descriptor(
  name='AvailabilityFeed',
  full_name='AvailabilityFeed',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='metadata', full_name='AvailabilityFeed.metadata', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='service_availability', full_name='AvailabilityFeed.service_availability', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2397,
  serialized_end=2500,
)


_SERVICEAVAILABILITY = _descriptor.Descriptor(
  name='ServiceAvailability',
  full_name='ServiceAvailability',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_timestamp_restrict', full_name='ServiceAvailability.start_timestamp_restrict', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_timestamp_restrict', full_name='ServiceAvailability.end_timestamp_restrict', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='merchant_id_restrict', full_name='ServiceAvailability.merchant_id_restrict', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='service_id_restrict', full_name='ServiceAvailability.service_id_restrict', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='resources_restrict', full_name='ServiceAvailability.resources_restrict', index=4,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='availability', full_name='ServiceAvailability.availability', index=5,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2503,
  serialized_end=2726,
)


_AVAILABILITY_RECURRENCE = _descriptor.Descriptor(
  name='Recurrence',
  full_name='Availability.Recurrence',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='repeat_until_sec', full_name='Availability.Recurrence.repeat_until_sec', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='repeat_every_sec', full_name='Availability.Recurrence.repeat_every_sec', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3059,
  serialized_end=3123,
)

_AVAILABILITY_SCHEDULEEXCEPTION = _descriptor.Descriptor(
  name='ScheduleException',
  full_name='Availability.ScheduleException',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='time_range', full_name='Availability.ScheduleException.time_range', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3125,
  serialized_end=3176,
)

_AVAILABILITY = _descriptor.Descriptor(
  name='Availability',
  full_name='Availability',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='merchant_id', full_name='Availability.merchant_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='service_id', full_name='Availability.service_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_sec', full_name='Availability.start_sec', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='duration_sec', full_name='Availability.duration_sec', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spots_total', full_name='Availability.spots_total', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spots_open', full_name='Availability.spots_open', index=5,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='availability_tag', full_name='Availability.availability_tag', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='resources', full_name='Availability.resources', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_option_id', full_name='Availability.payment_option_id', index=8,
      number=9, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='recurrence', full_name='Availability.recurrence', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_exception', full_name='Availability.schedule_exception', index=10,
      number=11, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_AVAILABILITY_RECURRENCE, _AVAILABILITY_SCHEDULEEXCEPTION, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2729,
  serialized_end=3176,
)


_TIMERANGE = _descriptor.Descriptor(
  name='TimeRange',
  full_name='TimeRange',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='begin_sec', full_name='TimeRange.begin_sec', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_sec', full_name='TimeRange.end_sec', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3178,
  serialized_end=3225,
)


_VIRTUALSESSION_VIRTUALPLATFORMINFO = _descriptor.Descriptor(
  name='VirtualPlatformInfo',
  full_name='VirtualSession.VirtualPlatformInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='platform', full_name='VirtualSession.VirtualPlatformInfo.platform', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='other_platform_name', full_name='VirtualSession.VirtualPlatformInfo.other_platform_name', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _VIRTUALSESSION_VIRTUALPLATFORMINFO_PLATFORM,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3421,
  serialized_end=3678,
)

_VIRTUALSESSION = _descriptor.Descriptor(
  name='VirtualSession',
  full_name='VirtualSession',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='session_instructions', full_name='VirtualSession.session_instructions', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='session_requirements', full_name='VirtualSession.session_requirements', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='virtual_platform_info', full_name='VirtualSession.virtual_platform_info', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_session_prerecorded', full_name='VirtualSession.is_session_prerecorded', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_VIRTUALSESSION_VIRTUALPLATFORMINFO, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3228,
  serialized_end=3678,
)


_DIRECTMERCHANTPAYMENT = _descriptor.Descriptor(
  name='DirectMerchantPayment',
  full_name='DirectMerchantPayment',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payment_methods', full_name='DirectMerchantPayment.payment_methods', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3680,
  serialized_end=3735,
)


_TEXT = _descriptor.Descriptor(
  name='Text',
  full_name='Text',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='value', full_name='Text.value', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='localized_value', full_name='Text.localized_value', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3737,
  serialized_end=3801,
)


_LOCALIZEDSTRING = _descriptor.Descriptor(
  name='LocalizedString',
  full_name='LocalizedString',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='locale', full_name='LocalizedString.locale', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='LocalizedString.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3803,
  serialized_end=3851,
)


_ACTIONLINK = _descriptor.Descriptor(
  name='ActionLink',
  full_name='ActionLink',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='url', full_name='ActionLink.url', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='language', full_name='ActionLink.language', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='restricted_country', full_name='ActionLink.restricted_country', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='platform', full_name='ActionLink.platform', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action_link_type', full_name='ActionLink.action_link_type', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _ACTIONLINK_ACTIONLINKTYPE,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3854,
  serialized_end=4314,
)

_FEEDMETADATA.fields_by_name['processing_instruction'].enum_type = _FEEDMETADATA_PROCESSINGINSTRUCTION
_FEEDMETADATA_PROCESSINGINSTRUCTION.containing_type = _FEEDMETADATA
_MERCHANTFEED.fields_by_name['metadata'].message_type = _FEEDMETADATA
_MERCHANTFEED.fields_by_name['merchant'].message_type = _MERCHANT
_MERCHANT.fields_by_name['geo'].message_type = _GEOCOORDINATES
_MERCHANT.fields_by_name['tax_rate'].message_type = _TAXRATE
_MERCHANT.fields_by_name['action_link'].message_type = _ACTIONLINK
_GEOCOORDINATES.fields_by_name['address'].message_type = _POSTALADDRESS
_SERVICEFEED.fields_by_name['metadata'].message_type = _FEEDMETADATA
_SERVICEFEED.fields_by_name['service'].message_type = _SERVICE
_SERVICE.fields_by_name['price'].message_type = _PRICE
_SERVICE.fields_by_name['rules'].message_type = _SCHEDULINGRULES
_SERVICE.fields_by_name['form'].message_type = _SERVICEINTAKEFORM
_SERVICE.fields_by_name['prepayment_type'].enum_type = _SERVICE_PREPAYMENTTYPE
_SERVICE.fields_by_name['tax_rate'].message_type = _TAXRATE
_SERVICE.fields_by_name['virtual_session'].message_type = _VIRTUALSESSION
_SERVICE.fields_by_name['action_link'].message_type = _ACTIONLINK
_SERVICE.fields_by_name['type'].enum_type = _SERVICE_SERVICETYPE
_SERVICE.fields_by_name['direct_merchant_payment'].message_type = _DIRECTMERCHANTPAYMENT
_SERVICE_PREPAYMENTTYPE.containing_type = _SERVICE
_SERVICE_SERVICETYPE.containing_type = _SERVICE
_SERVICEINTAKEFORMFIELD.fields_by_name['type'].enum_type = _SERVICEINTAKEFORMFIELD_FIELDTYPE
_SERVICEINTAKEFORMFIELD_FIELDTYPE.containing_type = _SERVICEINTAKEFORMFIELD
_SERVICEINTAKEFORM.fields_by_name['field'].message_type = _SERVICEINTAKEFORMFIELD
_AVAILABILITYFEED.fields_by_name['metadata'].message_type = _FEEDMETADATA
_AVAILABILITYFEED.fields_by_name['service_availability'].message_type = _SERVICEAVAILABILITY
_SERVICEAVAILABILITY.fields_by_name['resources_restrict'].message_type = _RESOURCES
_SERVICEAVAILABILITY.fields_by_name['availability'].message_type = _AVAILABILITY
_AVAILABILITY_RECURRENCE.containing_type = _AVAILABILITY
_AVAILABILITY_SCHEDULEEXCEPTION.fields_by_name['time_range'].message_type = _TIMERANGE
_AVAILABILITY_SCHEDULEEXCEPTION.containing_type = _AVAILABILITY
_AVAILABILITY.fields_by_name['resources'].message_type = _RESOURCES
_AVAILABILITY.fields_by_name['recurrence'].message_type = _AVAILABILITY_RECURRENCE
_AVAILABILITY.fields_by_name['schedule_exception'].message_type = _AVAILABILITY_SCHEDULEEXCEPTION
_VIRTUALSESSION_VIRTUALPLATFORMINFO.fields_by_name['platform'].enum_type = _VIRTUALSESSION_VIRTUALPLATFORMINFO_PLATFORM
_VIRTUALSESSION_VIRTUALPLATFORMINFO.fields_by_name['other_platform_name'].message_type = _TEXT
_VIRTUALSESSION_VIRTUALPLATFORMINFO.containing_type = _VIRTUALSESSION
_VIRTUALSESSION_VIRTUALPLATFORMINFO_PLATFORM.containing_type = _VIRTUALSESSION_VIRTUALPLATFORMINFO
_VIRTUALSESSION.fields_by_name['session_instructions'].message_type = _TEXT
_VIRTUALSESSION.fields_by_name['session_requirements'].message_type = _TEXT
_VIRTUALSESSION.fields_by_name['virtual_platform_info'].message_type = _VIRTUALSESSION_VIRTUALPLATFORMINFO
_DIRECTMERCHANTPAYMENT.fields_by_name['payment_methods'].message_type = _TEXT
_TEXT.fields_by_name['localized_value'].message_type = _LOCALIZEDSTRING
_ACTIONLINK.fields_by_name['platform'].enum_type = _ACTIONPLATFORM
_ACTIONLINK.fields_by_name['action_link_type'].enum_type = _ACTIONLINK_ACTIONLINKTYPE
_ACTIONLINK_ACTIONLINKTYPE.containing_type = _ACTIONLINK
DESCRIPTOR.message_types_by_name['FeedMetadata'] = _FEEDMETADATA
DESCRIPTOR.message_types_by_name['MerchantFeed'] = _MERCHANTFEED
DESCRIPTOR.message_types_by_name['Merchant'] = _MERCHANT
DESCRIPTOR.message_types_by_name['GeoCoordinates'] = _GEOCOORDINATES
DESCRIPTOR.message_types_by_name['PostalAddress'] = _POSTALADDRESS
DESCRIPTOR.message_types_by_name['TaxRate'] = _TAXRATE
DESCRIPTOR.message_types_by_name['Resources'] = _RESOURCES
DESCRIPTOR.message_types_by_name['ServiceFeed'] = _SERVICEFEED
DESCRIPTOR.message_types_by_name['Service'] = _SERVICE
DESCRIPTOR.message_types_by_name['Price'] = _PRICE
DESCRIPTOR.message_types_by_name['SchedulingRules'] = _SCHEDULINGRULES
DESCRIPTOR.message_types_by_name['ServiceIntakeFormField'] = _SERVICEINTAKEFORMFIELD
DESCRIPTOR.message_types_by_name['ServiceIntakeForm'] = _SERVICEINTAKEFORM
DESCRIPTOR.message_types_by_name['AvailabilityFeed'] = _AVAILABILITYFEED
DESCRIPTOR.message_types_by_name['ServiceAvailability'] = _SERVICEAVAILABILITY
DESCRIPTOR.message_types_by_name['Availability'] = _AVAILABILITY
DESCRIPTOR.message_types_by_name['TimeRange'] = _TIMERANGE
DESCRIPTOR.message_types_by_name['VirtualSession'] = _VIRTUALSESSION
DESCRIPTOR.message_types_by_name['DirectMerchantPayment'] = _DIRECTMERCHANTPAYMENT
DESCRIPTOR.message_types_by_name['Text'] = _TEXT
DESCRIPTOR.message_types_by_name['LocalizedString'] = _LOCALIZEDSTRING
DESCRIPTOR.message_types_by_name['ActionLink'] = _ACTIONLINK
DESCRIPTOR.enum_types_by_name['ActionPlatform'] = _ACTIONPLATFORM
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

FeedMetadata = _reflection.GeneratedProtocolMessageType('FeedMetadata', (_message.Message,), {
  'DESCRIPTOR' : _FEEDMETADATA,
  '__module__' : 'definitions_pb2'
  # @@protoc_insertion_point(class_scope:FeedMetadata)
  })
_sym_db.RegisterMessage(FeedMetadata)

MerchantFeed = _reflection.GeneratedProtocolMessageType('MerchantFeed', (_message.Message,), {
  'DESCRIPTOR' : _MERCHANTFEED,
  '__module__' : 'definitions_pb2'
  # @@protoc_insertion_point(class_scope:MerchantFeed)
  })
_sym_db.RegisterMessage(MerchantFeed)

Merchant = _reflection.GeneratedProtocolMessageType('Merchant', (_message.Message,), {
  'DESCRIPTOR' : _MERCHANT,
  '__module__' : 'definitions_pb2'
  # @@protoc_insertion_point(class_scope:Merchant)
  })
_sym_db.RegisterMessage(Merchant)

GeoCoordinates = _reflection.GeneratedProtocolMessageType('GeoCoordinates', (_message.Message,), {
  'DESCRIPTOR' : _GEOCOORDINATES,
  '__module__' : 'definitions_pb2'
  # @@protoc_insertion_point(class_scope:GeoCoordinates)
  })
_sym_db.RegisterMessage(GeoCoordinates)

PostalAddress = _reflection.GeneratedProtocolMessageType('PostalAddress', (_message.Message,), {
  'DESCRIPTOR' : _POSTALADDRESS,
  '__module__' : 'definitions_pb2'
  # @@protoc_insertion_point(class_scope:PostalAddress)
  })
_sym_db.RegisterMessage(PostalAddress)

TaxRate = _reflection.GeneratedProtocolMessageType('TaxRate', (_message.Message,), {
  'DESCRIPTOR' : _TAXRATE,
  '__module__' : 'definitions_pb2'
  # @@protoc_insertion_point(class_scope:TaxRate)
  })
_sym_db.RegisterMessage(TaxRate)

Resources = _reflection.GeneratedProtocolMessageType('Resources', (_message.Message,), {
  'DESCRIPTOR' : _RESOURCES,
  '__module__' : 'definitions_pb2'
  # @@protoc_insertion_point(class_scope:Resources)
  })
_sym_db.RegisterMessage(Resources)

ServiceFeed = _reflection.GeneratedProtocolMessageType('ServiceFeed', (_message.Message,), {
  'DESCRIPTOR' : _SERVICEFEED,
  '__module__' : 'definitions_pb2'
  # @@protoc_insertion_point(class_scope:ServiceFeed)
  })
_sym_db.RegisterMessage(ServiceFeed)

Service = _reflection.GeneratedProtocolMessageType('Service', (_message.Message,), {
  'DESCRIPTOR' : _SERVICE,
  '__module__' : 'definitions_pb2'
  # @@protoc_insertion_point(class_scope:Service)
  })
_sym_db.RegisterMessage(Service)

Price = _reflection.GeneratedProtocolMessageType('Price', (_message.Message,), {
  'DESCRIPTOR' : _PRICE,
  '__module__' : 'definitions_pb2'
  # @@protoc_insertion_point(class_scope:Price)
  })
_sym_db.RegisterMessage(Price)

SchedulingRules = _reflection.GeneratedProtocolMessageType('SchedulingRules', (_message.Message,), {
  'DESCRIPTOR' : _SCHEDULINGRULES,
  '__module__' : 'definitions_pb2'
  # @@protoc_insertion_point(class_scope:SchedulingRules)
  })
_sym_db.RegisterMessage(SchedulingRules)

ServiceIntakeFormField = _reflection.GeneratedProtocolMessageType('ServiceIntakeFormField', (_message.Message,), {
  'DESCRIPTOR' : _SERVICEINTAKEFORMFIELD,
  '__module__' : 'definitions_pb2'
  # @@protoc_insertion_point(class_scope:ServiceIntakeFormField)
  })
_sym_db.RegisterMessage(ServiceIntakeFormField)

ServiceIntakeForm = _reflection.GeneratedProtocolMessageType('ServiceIntakeForm', (_message.Message,), {
  'DESCRIPTOR' : _SERVICEINTAKEFORM,
  '__module__' : 'definitions_pb2'
  # @@protoc_insertion_point(class_scope:ServiceIntakeForm)
  })
_sym_db.RegisterMessage(ServiceIntakeForm)

AvailabilityFeed = _reflection.GeneratedProtocolMessageType('AvailabilityFeed', (_message.Message,), {
  'DESCRIPTOR' : _AVAILABILITYFEED,
  '__module__' : 'definitions_pb2'
  # @@protoc_insertion_point(class_scope:AvailabilityFeed)
  })
_sym_db.RegisterMessage(AvailabilityFeed)

ServiceAvailability = _reflection.GeneratedProtocolMessageType('ServiceAvailability', (_message.Message,), {
  'DESCRIPTOR' : _SERVICEAVAILABILITY,
  '__module__' : 'definitions_pb2'
  # @@protoc_insertion_point(class_scope:ServiceAvailability)
  })
_sym_db.RegisterMessage(ServiceAvailability)

Availability = _reflection.GeneratedProtocolMessageType('Availability', (_message.Message,), {

  'Recurrence' : _reflection.GeneratedProtocolMessageType('Recurrence', (_message.Message,), {
    'DESCRIPTOR' : _AVAILABILITY_RECURRENCE,
    '__module__' : 'definitions_pb2'
    # @@protoc_insertion_point(class_scope:Availability.Recurrence)
    })
  ,

  'ScheduleException' : _reflection.GeneratedProtocolMessageType('ScheduleException', (_message.Message,), {
    'DESCRIPTOR' : _AVAILABILITY_SCHEDULEEXCEPTION,
    '__module__' : 'definitions_pb2'
    # @@protoc_insertion_point(class_scope:Availability.ScheduleException)
    })
  ,
  'DESCRIPTOR' : _AVAILABILITY,
  '__module__' : 'definitions_pb2'
  # @@protoc_insertion_point(class_scope:Availability)
  })
_sym_db.RegisterMessage(Availability)
_sym_db.RegisterMessage(Availability.Recurrence)
_sym_db.RegisterMessage(Availability.ScheduleException)

TimeRange = _reflection.GeneratedProtocolMessageType('TimeRange', (_message.Message,), {
  'DESCRIPTOR' : _TIMERANGE,
  '__module__' : 'definitions_pb2'
  # @@protoc_insertion_point(class_scope:TimeRange)
  })
_sym_db.RegisterMessage(TimeRange)

VirtualSession = _reflection.GeneratedProtocolMessageType('VirtualSession', (_message.Message,), {

  'VirtualPlatformInfo' : _reflection.GeneratedProtocolMessageType('VirtualPlatformInfo', (_message.Message,), {
    'DESCRIPTOR' : _VIRTUALSESSION_VIRTUALPLATFORMINFO,
    '__module__' : 'definitions_pb2'
    # @@protoc_insertion_point(class_scope:VirtualSession.VirtualPlatformInfo)
    })
  ,
  'DESCRIPTOR' : _VIRTUALSESSION,
  '__module__' : 'definitions_pb2'
  # @@protoc_insertion_point(class_scope:VirtualSession)
  })
_sym_db.RegisterMessage(VirtualSession)
_sym_db.RegisterMessage(VirtualSession.VirtualPlatformInfo)

DirectMerchantPayment = _reflection.GeneratedProtocolMessageType('DirectMerchantPayment', (_message.Message,), {
  'DESCRIPTOR' : _DIRECTMERCHANTPAYMENT,
  '__module__' : 'definitions_pb2'
  # @@protoc_insertion_point(class_scope:DirectMerchantPayment)
  })
_sym_db.RegisterMessage(DirectMerchantPayment)

Text = _reflection.GeneratedProtocolMessageType('Text', (_message.Message,), {
  'DESCRIPTOR' : _TEXT,
  '__module__' : 'definitions_pb2'
  # @@protoc_insertion_point(class_scope:Text)
  })
_sym_db.RegisterMessage(Text)

LocalizedString = _reflection.GeneratedProtocolMessageType('LocalizedString', (_message.Message,), {
  'DESCRIPTOR' : _LOCALIZEDSTRING,
  '__module__' : 'definitions_pb2'
  # @@protoc_insertion_point(class_scope:LocalizedString)
  })
_sym_db.RegisterMessage(LocalizedString)

ActionLink = _reflection.GeneratedProtocolMessageType('ActionLink', (_message.Message,), {
  'DESCRIPTOR' : _ACTIONLINK,
  '__module__' : 'definitions_pb2'
  # @@protoc_insertion_point(class_scope:ActionLink)
  })
_sym_db.RegisterMessage(ActionLink)


_MERCHANT.fields_by_name['tax_rate_basis_points']._options = None
# @@protoc_insertion_point(module_scope)
