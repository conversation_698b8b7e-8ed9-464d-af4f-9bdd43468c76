# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: ext_maps_booking_partner_v3.proto

from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='ext_maps_booking_partner_v3.proto',
  package='ext.maps.booking.partner.v3',
  syntax='proto3',
  serialized_options=b'Z\035.;ext_maps_booking_partner_v3',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n!ext_maps_booking_partner_v3.proto\x12\x1b\x65xt.maps.booking.partner.v3\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\"u\n\x1e\x42\x61tchAvailabilityLookupRequest\x12\x13\n\x0bmerchant_id\x18\x01 \x01(\t\x12\x38\n\tslot_time\x18\x03 \x03(\x0b\x32%.ext.maps.booking.partner.v3.SlotTimeJ\x04\x08\x02\x10\x03\"t\n\x1f\x42\x61tchAvailabilityLookupResponse\x12Q\n\x16slot_time_availability\x18\x01 \x03(\x0b\x32\x31.ext.maps.booking.partner.v3.SlotTimeAvailability\"\xeb\x01\n\x08SlotTime\x12\x12\n\nservice_id\x18\x05 \x01(\t\x12\x11\n\tstart_sec\x18\x01 \x01(\x03\x12\x14\n\x0c\x64uration_sec\x18\x02 \x01(\x03\x12\x18\n\x10\x61vailability_tag\x18\x03 \x01(\t\x12>\n\x0cresource_ids\x18\x04 \x01(\x0b\x32(.ext.maps.booking.partner.v3.ResourceIds\x12H\n\x11\x63onfirmation_mode\x18\x06 \x01(\x0e\x32-.ext.maps.booking.partner.v3.ConfirmationMode\"c\n\x14SlotTimeAvailability\x12\x38\n\tslot_time\x18\x01 \x01(\x0b\x32%.ext.maps.booking.partner.v3.SlotTime\x12\x11\n\tavailable\x18\x02 \x01(\x08\"K\n\x18\x43heckAvailabilityRequest\x12/\n\x04slot\x18\x01 \x01(\x0b\x32!.ext.maps.booking.partner.v3.Slot\"\xb9\x03\n\x19\x43heckAvailabilityResponse\x12/\n\x04slot\x18\x01 \x01(\x0b\x32!.ext.maps.booking.partner.v3.Slot\x12\x17\n\x0f\x63ount_available\x18\x02 \x01(\x05\x12\'\n\x1blast_online_cancellable_sec\x18\x05 \x01(\x03\x42\x02\x18\x01\x12l\n\x14\x64uration_requirement\x18\x03 \x01(\x0e\x32J.ext.maps.booking.partner.v3.CheckAvailabilityResponse.DurationRequirementB\x02\x18\x01\x12L\n\x13\x61vailability_update\x18\x04 \x01(\x0b\x32/.ext.maps.booking.partner.v3.AvailabilityUpdate\"m\n\x13\x44urationRequirement\x12$\n DURATION_REQUIREMENT_UNSPECIFIED\x10\x00\x12\x18\n\x14\x44O_NOT_SHOW_DURATION\x10\x01\x12\x16\n\x12MUST_SHOW_DURATION\x10\x02\"^\n\x12\x41vailabilityUpdate\x12H\n\x11slot_availability\x18\x01 \x03(\x0b\x32-.ext.maps.booking.partner.v3.SlotAvailability\"\\\n\x10SlotAvailability\x12/\n\x04slot\x18\x01 \x01(\x0b\x32!.ext.maps.booking.partner.v3.Slot\x12\x17\n\x0f\x63ount_available\x18\x02 \x01(\x05\"|\n\x1f\x43heckOrderFulfillabilityRequest\x12\x13\n\x0bmerchant_id\x18\x01 \x01(\t\x12\x33\n\x04item\x18\x02 \x03(\x0b\x32%.ext.maps.booking.partner.v3.LineItem\x12\x0f\n\x07\x63\x61rt_id\x18\x03 \x01(\t\"\xf6\x01\n CheckOrderFulfillabilityResponse\x12H\n\x0e\x66ulfillability\x18\x01 \x01(\x0b\x32\x30.ext.maps.booking.partner.v3.OrderFulfillability\x12:\n\x0e\x66\x65\x65s_and_taxes\x18\x02 \x01(\x0b\x32\".ext.maps.booking.partner.v3.Price\x12/\n\x04\x66\x65\x65s\x18\x03 \x01(\x0b\x32!.ext.maps.booking.partner.v3.Fees\x12\x1b\n\x13\x63\x61rt_expiration_sec\x18\x04 \x01(\x03\"\x9a\x01\n\x04\x46\x65\x65s\x12I\n\x0eper_ticket_fee\x18\x01 \x03(\x0b\x32\x31.ext.maps.booking.partner.v3.SpecificPerTicketFee\x12G\n\rper_order_fee\x18\x02 \x03(\x0b\x32\x30.ext.maps.booking.partner.v3.SpecificPerOrderFee\"\x87\x01\n\x14SpecificPerTicketFee\x12\x11\n\tticket_id\x18\x01 \x01(\t\x12\x12\n\nservice_id\x18\x04 \x01(\t\x12\x10\n\x08\x66\x65\x65_name\x18\x02 \x01(\t\x12\x36\n\nfee_amount\x18\x03 \x01(\x0b\x32\".ext.maps.booking.partner.v3.Price\"_\n\x13SpecificPerOrderFee\x12\x10\n\x08\x66\x65\x65_name\x18\x01 \x01(\t\x12\x36\n\nfee_amount\x18\x02 \x01(\x0b\x32\".ext.maps.booking.partner.v3.Price\"-\n\x17GetBookingStatusRequest\x12\x12\n\nbooking_id\x18\x01 \x01(\t\"\xbc\x01\n\x18GetBookingStatusResponse\x12\x12\n\nbooking_id\x18\x01 \x01(\t\x12\x42\n\x0e\x62ooking_status\x18\x02 \x01(\x0e\x32*.ext.maps.booking.partner.v3.BookingStatus\x12H\n\x11prepayment_status\x18\x03 \x01(\x0e\x32-.ext.maps.booking.partner.v3.PrepaymentStatus\"\xdc\x03\n\x14\x43reateBookingRequest\x12/\n\x04slot\x18\x01 \x01(\x0b\x32!.ext.maps.booking.partner.v3.Slot\x12>\n\tlease_ref\x18\x02 \x01(\x0b\x32+.ext.maps.booking.partner.v3.LeaseReference\x12\x46\n\x10user_information\x18\x03 \x01(\x0b\x32,.ext.maps.booking.partner.v3.UserInformation\x12L\n\x13payment_information\x18\x04 \x01(\x0b\x32/.ext.maps.booking.partner.v3.PaymentInformation\x12_\n\x1dpayment_processing_parameters\x18\x05 \x01(\x0b\x32\x38.ext.maps.booking.partner.v3.PaymentProcessingParameters\x12\x19\n\x11idempotency_token\x18\x06 \x01(\t\x12\x1a\n\x12\x61\x64\x64itional_request\x18\x07 \x01(\t\x12\x10\n\x08offer_id\x18\t \x01(\t\x12\x13\n\x07\x64\x65\x61l_id\x18\x08 \x01(\tB\x02\x18\x01\"\xe1\x01\n\x15\x43reateBookingResponse\x12\x35\n\x07\x62ooking\x18\x01 \x01(\x0b\x32$.ext.maps.booking.partner.v3.Booking\x12K\n\x13user_payment_option\x18\x02 \x01(\x0b\x32..ext.maps.booking.partner.v3.UserPaymentOption\x12\x44\n\x0f\x62ooking_failure\x18\x03 \x01(\x0b\x32+.ext.maps.booking.partner.v3.BookingFailure\"G\n\x12\x43reateLeaseRequest\x12\x31\n\x05lease\x18\x01 \x01(\x0b\x32\".ext.maps.booking.partner.v3.Lease\"\x8e\x01\n\x13\x43reateLeaseResponse\x12\x31\n\x05lease\x18\x01 \x01(\x0b\x32\".ext.maps.booking.partner.v3.Lease\x12\x44\n\x0f\x62ooking_failure\x18\x02 \x01(\x0b\x32+.ext.maps.booking.partner.v3.BookingFailure\"\x85\x01\n\x05Lease\x12\x10\n\x08lease_id\x18\x01 \x01(\t\x12/\n\x04slot\x18\x02 \x01(\x0b\x32!.ext.maps.booking.partner.v3.Slot\x12\x16\n\x0euser_reference\x18\x03 \x01(\t\x12!\n\x19lease_expiration_time_sec\x18\x04 \x01(\x03\"\"\n\x0eLeaseReference\x12\x10\n\x08lease_id\x18\x01 \x01(\t\"\xc3\x01\n\x12\x43reateOrderRequest\x12\x31\n\x05order\x18\x01 \x01(\x0b\x32\".ext.maps.booking.partner.v3.Order\x12_\n\x1dpayment_processing_parameters\x18\x02 \x01(\x0b\x32\x38.ext.maps.booking.partner.v3.PaymentProcessingParameters\x12\x19\n\x11idempotency_token\x18\x03 \x01(\t\"\x98\x01\n\x13\x43reateOrderResponse\x12\x33\n\x05order\x18\x01 \x01(\x0b\x32\".ext.maps.booking.partner.v3.OrderH\x00\x12\x42\n\rorder_failure\x18\x02 \x01(\x0b\x32).ext.maps.booking.partner.v3.OrderFailureH\x00\x42\x08\n\x06result\"&\n\x13ListBookingsRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\"N\n\x14ListBookingsResponse\x12\x36\n\x08\x62ookings\x18\x01 \x03(\x0b\x32$.ext.maps.booking.partner.v3.Booking\"\x99\x01\n\x11ListOrdersRequest\x12\x11\n\x07user_id\x18\x01 \x01(\tH\x00\x12L\n\torder_ids\x18\x02 \x01(\x0b\x32\x37.ext.maps.booking.partner.v3.ListOrdersRequest.OrderIdsH\x00\x1a\x1c\n\x08OrderIds\x12\x10\n\x08order_id\x18\x01 \x03(\tB\x05\n\x03ids\"G\n\x12ListOrdersResponse\x12\x31\n\x05order\x18\x01 \x03(\x0b\x32\".ext.maps.booking.partner.v3.Order\"M\n\x14UpdateBookingRequest\x12\x35\n\x07\x62ooking\x18\x01 \x01(\x0b\x32$.ext.maps.booking.partner.v3.Booking\"\xe1\x01\n\x15UpdateBookingResponse\x12\x35\n\x07\x62ooking\x18\x01 \x01(\x0b\x32$.ext.maps.booking.partner.v3.Booking\x12K\n\x13user_payment_option\x18\x02 \x01(\x0b\x32..ext.maps.booking.partner.v3.UserPaymentOption\x12\x44\n\x0f\x62ooking_failure\x18\x03 \x01(\x0b\x32+.ext.maps.booking.partner.v3.BookingFailure\"\xab\x03\n\x07\x42ooking\x12\x12\n\nbooking_id\x18\x01 \x01(\t\x12/\n\x04slot\x18\x02 \x01(\x0b\x32!.ext.maps.booking.partner.v3.Slot\x12\x46\n\x10user_information\x18\x03 \x01(\x0b\x32,.ext.maps.booking.partner.v3.UserInformation\x12:\n\x06status\x18\x04 \x01(\x0e\x32*.ext.maps.booking.partner.v3.BookingStatus\x12L\n\x13payment_information\x18\x05 \x01(\x0b\x32/.ext.maps.booking.partner.v3.PaymentInformation\x12M\n\x14virtual_session_info\x18\x06 \x01(\x0b\x32/.ext.maps.booking.partner.v3.VirtualSessionInfo\x12:\n\noffer_info\x18\x07 \x01(\x0b\x32&.ext.maps.booking.partner.v3.OfferInfo\"O\n\x12VirtualSessionInfo\x12\x13\n\x0bsession_url\x18\x01 \x01(\t\x12\x12\n\nmeeting_id\x18\x02 \x01(\t\x12\x10\n\x08password\x18\x03 \x01(\t\"\xf8\x07\n\x0e\x42ookingFailure\x12@\n\x05\x63\x61use\x18\x01 \x01(\x0e\x32\x31.ext.maps.booking.partner.v3.BookingFailure.Cause\x12G\n\x12rejected_card_type\x18\x02 \x01(\x0e\x32+.ext.maps.booking.partner.v3.CreditCardType\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12^\n\x0fpayment_failure\x18\x04 \x01(\x0b\x32\x45.ext.maps.booking.partner.v3.BookingFailure.PaymentFailureInformation\x1a\xfb\x01\n\x19PaymentFailureInformation\x12u\n\x13threeds1_parameters\x18\x05 \x01(\x0b\x32X.ext.maps.booking.partner.v3.BookingFailure.PaymentFailureInformation.ThreeDS1Parameters\x1ag\n\x12ThreeDS1Parameters\x12\x0f\n\x07\x61\x63s_url\x18\x01 \x01(\t\x12\x0e\n\x06pa_req\x18\x02 \x01(\t\x12\x16\n\x0etransaction_id\x18\x03 \x01(\t\x12\x18\n\x10md_merchant_data\x18\x04 \x01(\t\"\xe7\x03\n\x05\x43\x61use\x12\x15\n\x11\x43\x41USE_UNSPECIFIED\x10\x00\x12\x14\n\x10SLOT_UNAVAILABLE\x10\x01\x12\x1f\n\x1bSLOT_ALREADY_BOOKED_BY_USER\x10\x02\x12\x11\n\rLEASE_EXPIRED\x10\x03\x12\x1f\n\x1bOUTSIDE_CANCELLATION_WINDOW\x10\x04\x12$\n PAYMENT_ERROR_CARD_TYPE_REJECTED\x10\x05\x12\x1f\n\x1bPAYMENT_ERROR_CARD_DECLINED\x10\x06\x12\x1c\n\x18PAYMENT_OPTION_NOT_VALID\x10\x07\x12\x11\n\rPAYMENT_ERROR\x10\x08\x12\"\n\x1eUSER_CANNOT_USE_PAYMENT_OPTION\x10\t\x12\x1d\n\x19\x42OOKING_ALREADY_CANCELLED\x10\n\x12\x1b\n\x17\x42OOKING_NOT_CANCELLABLE\x10\x0b\x12\x1b\n\x17OVERLAPPING_RESERVATION\x10\x0c\x12\x1b\n\x17USER_OVER_BOOKING_LIMIT\x10\r\x12\x15\n\x11OFFER_UNAVAILABLE\x10\x10\x12\x18\n\x10\x44\x45\x41L_UNAVAILABLE\x10\x0e\x1a\x02\x08\x01\x12\x19\n\x15PAYMENT_REQUIRES_3DS1\x10\x0f\"\xec\x01\n\x19PaymentFailureInformation\x12\x66\n\x13threeds1_parameters\x18\x05 \x01(\x0b\x32I.ext.maps.booking.partner.v3.PaymentFailureInformation.ThreeDS1Parameters\x1ag\n\x12ThreeDS1Parameters\x12\x0f\n\x07\x61\x63s_url\x18\x01 \x01(\t\x12\x0e\n\x06pa_req\x18\x02 \x01(\t\x12\x16\n\x0etransaction_id\x18\x03 \x01(\t\x12\x18\n\x10md_merchant_data\x18\x04 \x01(\t\"g\n\x12ThreeDS1Parameters\x12\x0f\n\x07\x61\x63s_url\x18\x01 \x01(\t\x12\x0e\n\x06pa_req\x18\x02 \x01(\t\x12\x16\n\x0etransaction_id\x18\x03 \x01(\t\x12\x18\n\x10md_merchant_data\x18\x04 \x01(\t\"\xf9\x01\n\x05Order\x12\x10\n\x08order_id\x18\x01 \x01(\t\x12\x46\n\x10user_information\x18\x02 \x01(\x0b\x32,.ext.maps.booking.partner.v3.UserInformation\x12L\n\x13payment_information\x18\x03 \x01(\x0b\x32/.ext.maps.booking.partner.v3.PaymentInformation\x12\x13\n\x0bmerchant_id\x18\x04 \x01(\t\x12\x33\n\x04item\x18\x05 \x03(\x0b\x32%.ext.maps.booking.partner.v3.LineItem\"\xa4\x04\n\x08LineItem\x12\x12\n\nservice_id\x18\x01 \x01(\t\x12\x11\n\tstart_sec\x18\x02 \x01(\x03\x12\x14\n\x0c\x64uration_sec\x18\x03 \x01(\x03\x12\x45\n\x07tickets\x18\x04 \x03(\x0b\x32\x34.ext.maps.booking.partner.v3.LineItem.OrderedTickets\x12\x31\n\x05price\x18\x05 \x01(\x0b\x32\".ext.maps.booking.partner.v3.Price\x12:\n\x06status\x18\x06 \x01(\x0e\x32*.ext.maps.booking.partner.v3.BookingStatus\x12K\n\x13intake_form_answers\x18\x07 \x01(\x0b\x32..ext.maps.booking.partner.v3.IntakeFormAnswers\x12K\n\x0ewarning_reason\x18\x08 \x01(\x0e\x32\x33.ext.maps.booking.partner.v3.LineItem.WarningReason\x1a\x32\n\x0eOrderedTickets\x12\x11\n\tticket_id\x18\x01 \x01(\t\x12\r\n\x05\x63ount\x18\x02 \x01(\x05\"W\n\rWarningReason\x12\x1e\n\x1aUNSPECIFIED_WARNING_REASON\x10\x00\x12\x12\n\x0ePRICE_INCREASE\x10\x01\x12\x12\n\x0ePRICE_DECREASE\x10\x02\"W\n\x11IntakeFormAnswers\x12\x42\n\x06\x61nswer\x18\x01 \x03(\x0b\x32\x32.ext.maps.booking.partner.v3.IntakeFormFieldAnswer\"5\n\x15IntakeFormFieldAnswer\x12\n\n\x02id\x18\x01 \x01(\t\x12\x10\n\x08response\x18\x02 \x03(\t\"\x98\x06\n\x0cOrderFailure\x12>\n\x05\x63\x61use\x18\x01 \x01(\x0e\x32/.ext.maps.booking.partner.v3.OrderFailure.Cause\x12H\n\x0e\x66ulfillability\x18\x02 \x01(\x0b\x32\x30.ext.maps.booking.partner.v3.OrderFulfillability\x12G\n\x12rejected_card_type\x18\x03 \x01(\x0e\x32+.ext.maps.booking.partner.v3.CreditCardType\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12\\\n\x0fpayment_failure\x18\x05 \x01(\x0b\x32\x43.ext.maps.booking.partner.v3.OrderFailure.PaymentFailureInformation\x1a\xf9\x01\n\x19PaymentFailureInformation\x12s\n\x13threeds1_parameters\x18\x05 \x01(\x0b\x32V.ext.maps.booking.partner.v3.OrderFailure.PaymentFailureInformation.ThreeDS1Parameters\x1ag\n\x12ThreeDS1Parameters\x12\x0f\n\x07\x61\x63s_url\x18\x01 \x01(\t\x12\x0e\n\x06pa_req\x18\x02 \x01(\t\x12\x16\n\x0etransaction_id\x18\x03 \x01(\t\x12\x18\n\x10md_merchant_data\x18\x04 \x01(\t\"\xc5\x01\n\x05\x43\x61use\x12\x15\n\x11\x43\x41USE_UNSPECIFIED\x10\x00\x12\x17\n\x13ORDER_UNFULFILLABLE\x10\x01\x12$\n PAYMENT_ERROR_CARD_TYPE_REJECTED\x10\x02\x12\x1f\n\x1bPAYMENT_ERROR_CARD_DECLINED\x10\x03\x12\x11\n\rPAYMENT_ERROR\x10\x04\x12\x17\n\x13INCORRECT_FEE_TOTAL\x10\x05\x12\x19\n\x15PAYMENT_REQUIRES_3DS1\x10\x06\"\xa7\x03\n\x13OrderFulfillability\x12Z\n\x06result\x18\x01 \x01(\x0e\x32J.ext.maps.booking.partner.v3.OrderFulfillability.OrderFulfillabilityResult\x12P\n\x13item_fulfillability\x18\x02 \x03(\x0b\x32\x33.ext.maps.booking.partner.v3.LineItemFulfillability\x12\x1c\n\x14unfulfillable_reason\x18\x03 \x01(\t\"\xc3\x01\n\x19OrderFulfillabilityResult\x12+\n\'ORDER_FULFILLABILITY_RESULT_UNSPECIFIED\x10\x00\x12\x0f\n\x0b\x43\x41N_FULFILL\x10\x01\x12\x1b\n\x17UNFULFILLABLE_LINE_ITEM\x10\x02\x12%\n!UNFULFILLABLE_SERVICE_COMBINATION\x10\x03\x12$\n ORDER_UNFULFILLABLE_OTHER_REASON\x10\x04\"\x8a\x07\n\x16LineItemFulfillability\x12\x33\n\x04item\x18\x01 \x01(\x0b\x32%.ext.maps.booking.partner.v3.LineItem\x12\\\n\x06result\x18\x02 \x01(\x0e\x32L.ext.maps.booking.partner.v3.LineItemFulfillability.ItemFulfillabilityResult\x12\x1c\n\x14unfulfillable_reason\x18\x03 \x01(\t\x12]\n\x0c\x61vailability\x18\x04 \x01(\x0b\x32G.ext.maps.booking.partner.v3.LineItemFulfillability.UpdatedAvailability\x12<\n\x0bticket_type\x18\x05 \x03(\x0b\x32\'.ext.maps.booking.partner.v3.TicketType\x12p\n\x1aviolated_ticket_constraint\x18\x06 \x03(\x0b\x32L.ext.maps.booking.partner.v3.LineItemFulfillability.ViolatedTicketConstraint\x1a)\n\x13UpdatedAvailability\x12\x12\n\nspots_open\x18\x01 \x01(\x05\x1at\n\x18ViolatedTicketConstraint\x12\x1a\n\x10min_ticket_count\x18\x01 \x01(\x05H\x00\x12\x1a\n\x10max_ticket_count\x18\x02 \x01(\x05H\x00\x12\x11\n\tticket_id\x18\x04 \x01(\tB\x07\n\x05valueJ\x04\x08\x03\x10\x04\"\x8e\x02\n\x18ItemFulfillabilityResult\x12*\n&ITEM_FULFILLABILITY_RESULT_UNSPECIFIED\x10\x00\x12\x0f\n\x0b\x43\x41N_FULFILL\x10\x01\x12\x14\n\x10SLOT_UNAVAILABLE\x10\x02\x12\x1f\n\x1b\x43HILD_TICKETS_WITHOUT_ADULT\x10\x06\x12$\n UNFULFILLABLE_TICKET_COMBINATION\x10\x03\x12\x13\n\x0fINCORRECT_PRICE\x10\x04\x12\x1e\n\x1aTICKET_CONSTRAINT_VIOLATED\x10\x07\x12#\n\x1fITEM_UNFULFILLABLE_OTHER_REASON\x10\x05\"\xea\x02\n\nTicketType\x12\x16\n\x0eticket_type_id\x18\x01 \x01(\t\x12\x1d\n\x11short_description\x18\x02 \x01(\tB\x02\x18\x01\x12\x46\n\x1blocalized_short_description\x18\x06 \x01(\x0b\x32!.ext.maps.booking.partner.v3.Text\x12\x31\n\x05price\x18\x03 \x01(\x0b\x32\".ext.maps.booking.partner.v3.Price\x12\x41\n\x0eper_ticket_fee\x18\x05 \x01(\x0b\x32).ext.maps.booking.partner.v3.PerTicketFee\x12\x1e\n\x12option_description\x18\x04 \x01(\tB\x02\x18\x01\x12G\n\x1clocalized_option_description\x18\x07 \x01(\x0b\x32!.ext.maps.booking.partner.v3.Text\"\xb7\x01\n\x0cPerTicketFee\x12:\n\x0eservice_charge\x18\x01 \x01(\x0b\x32\".ext.maps.booking.partner.v3.Price\x12\x38\n\x0c\x66\x61\x63ility_fee\x18\x02 \x01(\x0b\x32\".ext.maps.booking.partner.v3.Price\x12\x31\n\x05taxes\x18\x03 \x01(\x0b\x32\".ext.maps.booking.partner.v3.Price\"\\\n\x04Text\x12\r\n\x05value\x18\x01 \x01(\t\x12\x45\n\x0flocalized_value\x18\x02 \x03(\x0b\x32,.ext.maps.booking.partner.v3.LocalizedString\"0\n\x0fLocalizedString\x12\x0e\n\x06locale\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\")\n\x15MerchantMatchingHints\x12\x10\n\x08place_id\x18\x01 \x01(\t\"\xb3\x01\n\x10ServiceAttribute\x12\x14\n\x0c\x61ttribute_id\x18\x01 \x01(\t\x12\x16\n\x0e\x61ttribute_name\x18\x02 \x01(\t\x12\x42\n\x05value\x18\x03 \x03(\x0b\x32\x33.ext.maps.booking.partner.v3.ServiceAttribute.Value\x1a-\n\x05Value\x12\x10\n\x08value_id\x18\x01 \x01(\t\x12\x12\n\nvalue_name\x18\x02 \x01(\t\"\xa6\x04\n\nActionLink\x12\x0b\n\x03url\x18\x01 \x01(\t\x12\x10\n\x08language\x18\x02 \x01(\t\x12\x1a\n\x12restricted_country\x18\x03 \x03(\t\x12=\n\x08platform\x18\x04 \x01(\x0e\x32+.ext.maps.booking.partner.v3.ActionPlatform\x12P\n\x10\x61\x63tion_link_type\x18\x05 \x01(\x0e\x32\x36.ext.maps.booking.partner.v3.ActionLink.ActionLinkType\"\xcb\x02\n\x0e\x41\x63tionLinkType\x12 \n\x1c\x41\x43TION_LINK_TYPE_UNSPECIFIED\x10\x00\x12%\n!ACTION_LINK_TYPE_BOOK_APPOINTMENT\x10\x01\x12,\n(ACTION_LINK_TYPE_BOOK_ONLINE_APPOINTMENT\x10\x02\x12\x1f\n\x1b\x41\x43TION_LINK_TYPE_ORDER_FOOD\x10\x03\x12(\n$ACTION_LINK_TYPE_ORDER_FOOD_DELIVERY\x10\x04\x12\'\n#ACTION_LINK_TYPE_ORDER_FOOD_TAKEOUT\x10\x05\x12,\n(ACTION_LINK_TYPE_MAKE_DINING_RESERVATION\x10\x06\x12 \n\x1c\x41\x43TION_LINK_TYPE_SHOP_ONLINE\x10\x07\"D\n\x0bResourceIds\x12\x10\n\x08staff_id\x18\x01 \x01(\t\x12\x0f\n\x07room_id\x18\x02 \x01(\t\x12\x12\n\nparty_size\x18\x03 \x01(\x05\"\xb0\x03\n\x1bPaymentProcessingParameters\x12`\n\tprocessor\x18\x01 \x01(\x0e\x32I.ext.maps.booking.partner.v3.PaymentProcessingParameters.PaymentProcessorB\x02\x18\x01\x12 \n\x14payment_method_token\x18\x02 \x01(\tB\x02\x18\x01\x12%\n\x1dunparsed_payment_method_token\x18\x05 \x01(\t\x12\x13\n\x07version\x18\x03 \x01(\tB\x02\x18\x01\x12\x1d\n\x11payment_processor\x18\x04 \x01(\tB\x02\x18\x01\x12L\n\x13tokenization_config\x18\x06 \x01(\x0b\x32/.ext.maps.booking.partner.v3.TokenizationConfig\"d\n\x10PaymentProcessor\x12!\n\x1dPAYMENT_PROCESSOR_UNSPECIFIED\x10\x00\x12\x14\n\x10PROCESSOR_STRIPE\x10\x01\x12\x17\n\x13PROCESSOR_BRAINTREE\x10\x02\"\xf5\x01\n\x11UserPaymentOption\x12\x1e\n\x16user_payment_option_id\x18\x01 \x01(\t\x12\x1c\n\x14valid_start_time_sec\x18\x02 \x01(\x03\x12\x1a\n\x12valid_end_time_sec\x18\x03 \x01(\x03\x12<\n\x04type\x18\x04 \x01(\x0e\x32..ext.maps.booking.partner.v3.PaymentOptionType\x12\x16\n\x0eoriginal_count\x18\x05 \x01(\x05\x12\x15\n\rcurrent_count\x18\x06 \x01(\x05\x12\x19\n\x11payment_option_id\x18\x07 \x01(\t\"\xaf\x06\n\x12PaymentInformation\x12H\n\x11prepayment_status\x18\x01 \x01(\x0e\x32-.ext.maps.booking.partner.v3.PrepaymentStatus\x12\x1e\n\x16payment_transaction_id\x18\x02 \x01(\t\x12\x31\n\x05price\x18\x03 \x01(\x0b\x32\".ext.maps.booking.partner.v3.Price\x12\x36\n\ntax_amount\x18\x04 \x01(\x0b\x32\".ext.maps.booking.partner.v3.Price\x12\x30\n\x04\x66\x65\x65s\x18\x0e \x01(\x0b\x32\".ext.maps.booking.partner.v3.Price\x12:\n\x0e\x66\x65\x65s_and_taxes\x18\n \x01(\x0b\x32\".ext.maps.booking.partner.v3.Price\x12\x35\n\x07\x64\x65posit\x18\x08 \x01(\x0b\x32$.ext.maps.booking.partner.v3.Deposit\x12;\n\x0bno_show_fee\x18\t \x01(\x0b\x32&.ext.maps.booking.partner.v3.NoShowFee\x12`\n\x14payment_processed_by\x18\x05 \x01(\x0e\x32\x42.ext.maps.booking.partner.v3.PaymentInformation.PaymentProcessedBy\x12\x1b\n\x11payment_option_id\x18\x06 \x01(\tH\x00\x12 \n\x16user_payment_option_id\x18\x07 \x01(\tH\x00\x12\x15\n\rfraud_signals\x18\x0b \x01(\t\x12\x13\n\x0bpa_response\x18\x0c \x01(\t\x12\x18\n\x10md_merchant_data\x18\r \x01(\t\"m\n\x12PaymentProcessedBy\x12$\n PAYMENT_PROCESSED_BY_UNSPECIFIED\x10\x00\x12\x17\n\x13PROCESSED_BY_GOOGLE\x10\x01\x12\x18\n\x14PROCESSED_BY_PARTNER\x10\x02\x42\x0c\n\npayment_id\"P\n\x05Price\x12\x14\n\x0cprice_micros\x18\x01 \x01(\x03\x12\x15\n\rcurrency_code\x18\x02 \x01(\t\x12\x1a\n\x12pricing_option_tag\x18\x03 \x01(\t\"v\n\tNoShowFee\x12/\n\x03\x66\x65\x65\x18\x01 \x01(\x0b\x32\".ext.maps.booking.partner.v3.Price\x12\x38\n\x08\x66\x65\x65_type\x18\x03 \x01(\x0e\x32&.ext.maps.booking.partner.v3.PriceType\"\xa2\x01\n\x07\x44\x65posit\x12\x33\n\x07\x64\x65posit\x18\x01 \x01(\x0b\x32\".ext.maps.booking.partner.v3.Price\x12$\n\x1cmin_advance_cancellation_sec\x18\x02 \x01(\x03\x12<\n\x0c\x64\x65posit_type\x18\x03 \x01(\x0e\x32&.ext.maps.booking.partner.v3.PriceType\"\xfd\x06\n\x12TokenizationConfig\x12j\n\x16tokenization_parameter\x18\x01 \x03(\x0b\x32J.ext.maps.booking.partner.v3.TokenizationConfig.TokenizationParameterEntry\x12l\n\x1a\x62illing_information_format\x18\x02 \x01(\x0e\x32H.ext.maps.booking.partner.v3.TokenizationConfig.BillingInformationFormat\x12\x1f\n\x17merchant_of_record_name\x18\x03 \x01(\t\x12\x1c\n\x14payment_country_code\x18\x04 \x01(\t\x12\x66\n\x17\x63\x61rd_network_parameters\x18\x05 \x03(\x0b\x32\x45.ext.maps.booking.partner.v3.TokenizationConfig.CardNetworkParameters\x12X\n\x14\x61llowed_auth_methods\x18\x06 \x03(\x0e\x32:.ext.maps.booking.partner.v3.TokenizationConfig.AuthMethod\x1a<\n\x1aTokenizationParameterEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\xa5\x01\n\x15\x43\x61rdNetworkParameters\x12X\n\x0c\x63\x61rd_network\x18\x01 \x01(\x0e\x32\x42.ext.maps.booking.partner.v3.CreditCardRestrictions.CreditCardType\x12\x14\n\x0c\x61\x63quirer_bin\x18\x02 \x01(\t\x12\x1c\n\x14\x61\x63quirer_merchant_id\x18\x03 \x01(\t\"Y\n\x18\x42illingInformationFormat\x12*\n&BILLING_INFORMATION_FORMAT_UNSPECIFIED\x10\x00\x12\x07\n\x03MIN\x10\x01\x12\x08\n\x04\x46ULL\x10\x02\"K\n\nAuthMethod\x12\x1b\n\x17\x41UTH_METHOD_UNSPECIFIED\x10\x00\x12\x0c\n\x08PAN_ONLY\x10\x01\x12\x12\n\x0e\x43RYPTOGRAM_3DS\x10\x02\"\xf1\x01\n\x16\x43reditCardRestrictions\x12\\\n\x10\x63redit_card_type\x18\x01 \x03(\x0e\x32\x42.ext.maps.booking.partner.v3.CreditCardRestrictions.CreditCardType\"y\n\x0e\x43reditCardType\x12 \n\x1c\x43REDIT_CARD_TYPE_UNSPECIFIED\x10\x00\x12\x08\n\x04VISA\x10\x01\x12\x0e\n\nMASTERCARD\x10\x02\x12\x14\n\x10\x41MERICAN_EXPRESS\x10\x03\x12\x0c\n\x08\x44ISCOVER\x10\x04\x12\x07\n\x03JCB\x10\x05\"\xf9\x01\n\x04Slot\x12\x13\n\x0bmerchant_id\x18\x01 \x01(\t\x12\x12\n\nservice_id\x18\x02 \x01(\t\x12\x11\n\tstart_sec\x18\x03 \x01(\x03\x12\x14\n\x0c\x64uration_sec\x18\x04 \x01(\x03\x12\x18\n\x10\x61vailability_tag\x18\x05 \x01(\t\x12;\n\tresources\x18\x06 \x01(\x0b\x32(.ext.maps.booking.partner.v3.ResourceIds\x12H\n\x11\x63onfirmation_mode\x18\x07 \x01(\x0e\x32-.ext.maps.booking.partner.v3.ConfirmationMode\"\xc7\x01\n\x0fUserInformation\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x12\n\ngiven_name\x18\x02 \x01(\t\x12\x13\n\x0b\x66\x61mily_name\x18\x03 \x01(\t\x12;\n\x07\x61\x64\x64ress\x18\x04 \x01(\x0b\x32*.ext.maps.booking.partner.v3.PostalAddress\x12\x11\n\ttelephone\x18\x05 \x01(\t\x12\r\n\x05\x65mail\x18\x06 \x01(\t\x12\x15\n\rlanguage_code\x18\x07 \x01(\tJ\x04\x08\x08\x10\t\"o\n\rPostalAddress\x12\x0f\n\x07\x63ountry\x18\x01 \x01(\t\x12\x10\n\x08locality\x18\x02 \x01(\t\x12\x0e\n\x06region\x18\x03 \x01(\t\x12\x13\n\x0bpostal_code\x18\x04 \x01(\t\x12\x16\n\x0estreet_address\x18\x05 \x01(\t\"\x1d\n\tOfferInfo\x12\x10\n\x08offer_id\x18\x01 \x01(\t*\xb9\x01\n\rBookingStatus\x12\x1e\n\x1a\x42OOKING_STATUS_UNSPECIFIED\x10\x00\x12\r\n\tCONFIRMED\x10\x01\x12!\n\x1dPENDING_MERCHANT_CONFIRMATION\x10\x02\x12\x0c\n\x08\x43\x41NCELED\x10\x03\x12\x0b\n\x07NO_SHOW\x10\x04\x12\x15\n\x11NO_SHOW_PENALIZED\x10\x05\x12\n\n\x06\x46\x41ILED\x10\x06\x12\x18\n\x14\x44\x45\x43LINED_BY_MERCHANT\x10\x07*y\n\x0e\x43reditCardType\x12 \n\x1c\x43REDIT_CARD_TYPE_UNSPECIFIED\x10\x00\x12\x08\n\x04VISA\x10\x01\x12\x0e\n\nMASTERCARD\x10\x02\x12\x14\n\x10\x41MERICAN_EXPRESS\x10\x03\x12\x0c\n\x08\x44ISCOVER\x10\x04\x12\x07\n\x03JCB\x10\x05*\xac\x01\n\x0e\x41\x63tionPlatform\x12\x1f\n\x1b\x41\x43TION_PLATFORM_UNSPECIFIED\x10\x00\x12#\n\x1f\x41\x43TION_PLATFORM_WEB_APPLICATION\x10\x01\x12\x1e\n\x1a\x41\x43TION_PLATFORM_MOBILE_WEB\x10\x02\x12\x1b\n\x17\x41\x43TION_PLATFORM_ANDROID\x10\x03\x12\x17\n\x13\x41\x43TION_PLATFORM_IOS\x10\x04*\x97\x01\n\x11PaymentOptionType\x12#\n\x1fPAYMENT_OPTION_TYPE_UNSPECIFIED\x10\x00\x12\x1d\n\x19PAYMENT_OPTION_SINGLE_USE\x10\x01\x12\x1c\n\x18PAYMENT_OPTION_MULTI_USE\x10\x02\x12 \n\x1cPAYMENT_OPTION_UNLIMITED_USE\x10\x03*\x9d\x01\n\x10PrepaymentStatus\x12!\n\x1dPREPAYMENT_STATUS_UNSPECIFIED\x10\x00\x12\x17\n\x13PREPAYMENT_PROVIDED\x10\x01\x12\x1b\n\x17PREPAYMENT_NOT_PROVIDED\x10\x02\x12\x17\n\x13PREPAYMENT_REFUNDED\x10\x03\x12\x17\n\x13PREPAYMENT_CREDITED\x10\x04*3\n\tPriceType\x12\x16\n\x12\x46IXED_RATE_DEFAULT\x10\x00\x12\x0e\n\nPER_PERSON\x10\x01*|\n\x10\x43onfirmationMode\x12!\n\x1d\x43ONFIRMATION_MODE_UNSPECIFIED\x10\x00\x12!\n\x1d\x43ONFIRMATION_MODE_SYNCHRONOUS\x10\x01\x12\"\n\x1e\x43ONFIRMATION_MODE_ASYNCHRONOUS\x10\x02\x32\xe0\x0c\n\x0e\x42ookingService\x12K\n\x07GetPing\x12\x16.google.protobuf.Empty\x1a\x16.google.protobuf.Empty\"\x10\x82\xd3\xe4\x93\x02\n\x12\x08/v3/Ping\x12Y\n\x0eGetHealthCheck\x12\x16.google.protobuf.Empty\x1a\x16.google.protobuf.Empty\"\x17\x82\xd3\xe4\x93\x02\x11\x12\x0f/v3/HealthCheck\x12\xc3\x01\n\x1ePerformBatchAvailabilityLookup\x12;.ext.maps.booking.partner.v3.BatchAvailabilityLookupRequest\x1a<.ext.maps.booking.partner.v3.BatchAvailabilityLookupResponse\"&\x82\xd3\xe4\x93\x02 \"\x1b/v3/BatchAvailabilityLookup:\x01*\x12\xa7\x01\n\x11\x43heckAvailability\x12\x35.ext.maps.booking.partner.v3.CheckAvailabilityRequest\x1a\x36.ext.maps.booking.partner.v3.CheckAvailabilityResponse\"#\x82\xd3\xe4\x93\x02\x1d\"\x15/v3/CheckAvailability:\x04slot\x12\x94\x01\n\rCreateBooking\x12\x31.ext.maps.booking.partner.v3.CreateBookingRequest\x1a\x32.ext.maps.booking.partner.v3.CreateBookingResponse\"\x1c\x82\xd3\xe4\x93\x02\x16\"\x11/v3/CreateBooking:\x01*\x12\x94\x01\n\rUpdateBooking\x12\x31.ext.maps.booking.partner.v3.UpdateBookingRequest\x1a\x32.ext.maps.booking.partner.v3.UpdateBookingResponse\"\x1c\x82\xd3\xe4\x93\x02\x16\"\x11/v3/UpdateBooking:\x01*\x12\xa0\x01\n\x10GetBookingStatus\x12\x34.ext.maps.booking.partner.v3.GetBookingStatusRequest\x1a\x35.ext.maps.booking.partner.v3.GetBookingStatusResponse\"\x1f\x82\xd3\xe4\x93\x02\x19\"\x14/v3/GetBookingStatus:\x01*\x12\x90\x01\n\x0cListBookings\x12\x30.ext.maps.booking.partner.v3.ListBookingsRequest\x1a\x31.ext.maps.booking.partner.v3.ListBookingsResponse\"\x1b\x82\xd3\xe4\x93\x02\x15\"\x10/v3/ListBookings:\x01*\x12\xbd\x01\n\x18\x43heckOrderFulfillability\x12<.ext.maps.booking.partner.v3.CheckOrderFulfillabilityRequest\x1a=.ext.maps.booking.partner.v3.CheckOrderFulfillabilityResponse\"$\x82\xd3\xe4\x93\x02\x1e\"\x1c/v3/CheckOrderFulfillability\x12\x89\x01\n\x0b\x43reateOrder\x12/.ext.maps.booking.partner.v3.CreateOrderRequest\x1a\x30.ext.maps.booking.partner.v3.CreateOrderResponse\"\x17\x82\xd3\xe4\x93\x02\x11\"\x0f/v3/CreateOrder\x12\x85\x01\n\nListOrders\x12..ext.maps.booking.partner.v3.ListOrdersRequest\x1a/.ext.maps.booking.partner.v3.ListOrdersResponse\"\x16\x82\xd3\xe4\x93\x02\x10\"\x0e/v3/ListOrdersB\x1fZ\x1d.;ext_maps_booking_partner_v3b\x06proto3'
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_empty__pb2.DESCRIPTOR,])

_BOOKINGSTATUS = _descriptor.EnumDescriptor(
  name='BookingStatus',
  full_name='ext.maps.booking.partner.v3.BookingStatus',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='BOOKING_STATUS_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CONFIRMED', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PENDING_MERCHANT_CONFIRMATION', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CANCELED', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='NO_SHOW', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='NO_SHOW_PENALIZED', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='FAILED', index=6, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='DECLINED_BY_MERCHANT', index=7, number=7,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=14624,
  serialized_end=14809,
)
_sym_db.RegisterEnumDescriptor(_BOOKINGSTATUS)

BookingStatus = enum_type_wrapper.EnumTypeWrapper(_BOOKINGSTATUS)
_CREDITCARDTYPE = _descriptor.EnumDescriptor(
  name='CreditCardType',
  full_name='ext.maps.booking.partner.v3.CreditCardType',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='CREDIT_CARD_TYPE_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='VISA', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='MASTERCARD', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='AMERICAN_EXPRESS', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='DISCOVER', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='JCB', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=13902,
  serialized_end=14023,
)
_sym_db.RegisterEnumDescriptor(_CREDITCARDTYPE)

CreditCardType = enum_type_wrapper.EnumTypeWrapper(_CREDITCARDTYPE)
_ACTIONPLATFORM = _descriptor.EnumDescriptor(
  name='ActionPlatform',
  full_name='ext.maps.booking.partner.v3.ActionPlatform',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ACTION_PLATFORM_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ACTION_PLATFORM_WEB_APPLICATION', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ACTION_PLATFORM_MOBILE_WEB', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ACTION_PLATFORM_ANDROID', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ACTION_PLATFORM_IOS', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=14935,
  serialized_end=15107,
)
_sym_db.RegisterEnumDescriptor(_ACTIONPLATFORM)

ActionPlatform = enum_type_wrapper.EnumTypeWrapper(_ACTIONPLATFORM)
_PAYMENTOPTIONTYPE = _descriptor.EnumDescriptor(
  name='PaymentOptionType',
  full_name='ext.maps.booking.partner.v3.PaymentOptionType',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='PAYMENT_OPTION_TYPE_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PAYMENT_OPTION_SINGLE_USE', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PAYMENT_OPTION_MULTI_USE', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PAYMENT_OPTION_UNLIMITED_USE', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=15110,
  serialized_end=15261,
)
_sym_db.RegisterEnumDescriptor(_PAYMENTOPTIONTYPE)

PaymentOptionType = enum_type_wrapper.EnumTypeWrapper(_PAYMENTOPTIONTYPE)
_PREPAYMENTSTATUS = _descriptor.EnumDescriptor(
  name='PrepaymentStatus',
  full_name='ext.maps.booking.partner.v3.PrepaymentStatus',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='PREPAYMENT_STATUS_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PREPAYMENT_PROVIDED', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PREPAYMENT_NOT_PROVIDED', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PREPAYMENT_REFUNDED', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PREPAYMENT_CREDITED', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=15264,
  serialized_end=15421,
)
_sym_db.RegisterEnumDescriptor(_PREPAYMENTSTATUS)

PrepaymentStatus = enum_type_wrapper.EnumTypeWrapper(_PREPAYMENTSTATUS)
_PRICETYPE = _descriptor.EnumDescriptor(
  name='PriceType',
  full_name='ext.maps.booking.partner.v3.PriceType',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='FIXED_RATE_DEFAULT', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PER_PERSON', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=15423,
  serialized_end=15474,
)
_sym_db.RegisterEnumDescriptor(_PRICETYPE)

PriceType = enum_type_wrapper.EnumTypeWrapper(_PRICETYPE)
_CONFIRMATIONMODE = _descriptor.EnumDescriptor(
  name='ConfirmationMode',
  full_name='ext.maps.booking.partner.v3.ConfirmationMode',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='CONFIRMATION_MODE_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CONFIRMATION_MODE_SYNCHRONOUS', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CONFIRMATION_MODE_ASYNCHRONOUS', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=15476,
  serialized_end=15600,
)
_sym_db.RegisterEnumDescriptor(_CONFIRMATIONMODE)

ConfirmationMode = enum_type_wrapper.EnumTypeWrapper(_CONFIRMATIONMODE)
BOOKING_STATUS_UNSPECIFIED = 0
CONFIRMED = 1
PENDING_MERCHANT_CONFIRMATION = 2
CANCELED = 3
NO_SHOW = 4
NO_SHOW_PENALIZED = 5
FAILED = 6
DECLINED_BY_MERCHANT = 7
CREDIT_CARD_TYPE_UNSPECIFIED = 0
VISA = 1
MASTERCARD = 2
AMERICAN_EXPRESS = 3
DISCOVER = 4
JCB = 5
ACTION_PLATFORM_UNSPECIFIED = 0
ACTION_PLATFORM_WEB_APPLICATION = 1
ACTION_PLATFORM_MOBILE_WEB = 2
ACTION_PLATFORM_ANDROID = 3
ACTION_PLATFORM_IOS = 4
PAYMENT_OPTION_TYPE_UNSPECIFIED = 0
PAYMENT_OPTION_SINGLE_USE = 1
PAYMENT_OPTION_MULTI_USE = 2
PAYMENT_OPTION_UNLIMITED_USE = 3
PREPAYMENT_STATUS_UNSPECIFIED = 0
PREPAYMENT_PROVIDED = 1
PREPAYMENT_NOT_PROVIDED = 2
PREPAYMENT_REFUNDED = 3
PREPAYMENT_CREDITED = 4
FIXED_RATE_DEFAULT = 0
PER_PERSON = 1
CONFIRMATION_MODE_UNSPECIFIED = 0
CONFIRMATION_MODE_SYNCHRONOUS = 1
CONFIRMATION_MODE_ASYNCHRONOUS = 2


_CHECKAVAILABILITYRESPONSE_DURATIONREQUIREMENT = _descriptor.EnumDescriptor(
  name='DurationRequirement',
  full_name='ext.maps.booking.partner.v3.CheckAvailabilityResponse.DurationRequirement',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='DURATION_REQUIREMENT_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='DO_NOT_SHOW_DURATION', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='MUST_SHOW_DURATION', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1111,
  serialized_end=1220,
)
_sym_db.RegisterEnumDescriptor(_CHECKAVAILABILITYRESPONSE_DURATIONREQUIREMENT)

_BOOKINGFAILURE_CAUSE = _descriptor.EnumDescriptor(
  name='Cause',
  full_name='ext.maps.booking.partner.v3.BookingFailure.Cause',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='CAUSE_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SLOT_UNAVAILABLE', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SLOT_ALREADY_BOOKED_BY_USER', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='LEASE_EXPIRED', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='OUTSIDE_CANCELLATION_WINDOW', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PAYMENT_ERROR_CARD_TYPE_REJECTED', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PAYMENT_ERROR_CARD_DECLINED', index=6, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PAYMENT_OPTION_NOT_VALID', index=7, number=7,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PAYMENT_ERROR', index=8, number=8,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='USER_CANNOT_USE_PAYMENT_OPTION', index=9, number=9,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='BOOKING_ALREADY_CANCELLED', index=10, number=10,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='BOOKING_NOT_CANCELLABLE', index=11, number=11,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='OVERLAPPING_RESERVATION', index=12, number=12,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='USER_OVER_BOOKING_LIMIT', index=13, number=13,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='OFFER_UNAVAILABLE', index=14, number=16,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='DEAL_UNAVAILABLE', index=15, number=14,
      serialized_options=b'\010\001',
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PAYMENT_REQUIRES_3DS1', index=16, number=15,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=5564,
  serialized_end=6051,
)
_sym_db.RegisterEnumDescriptor(_BOOKINGFAILURE_CAUSE)

_LINEITEM_WARNINGREASON = _descriptor.EnumDescriptor(
  name='WarningReason',
  full_name='ext.maps.booking.partner.v3.LineItem.WarningReason',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UNSPECIFIED_WARNING_REASON', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PRICE_INCREASE', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PRICE_DECREASE', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=7111,
  serialized_end=7198,
)
_sym_db.RegisterEnumDescriptor(_LINEITEM_WARNINGREASON)

_ORDERFAILURE_CAUSE = _descriptor.EnumDescriptor(
  name='Cause',
  full_name='ext.maps.booking.partner.v3.OrderFailure.Cause',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='CAUSE_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ORDER_UNFULFILLABLE', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PAYMENT_ERROR_CARD_TYPE_REJECTED', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PAYMENT_ERROR_CARD_DECLINED', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PAYMENT_ERROR', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='INCORRECT_FEE_TOTAL', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PAYMENT_REQUIRES_3DS1', index=6, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=7940,
  serialized_end=8137,
)
_sym_db.RegisterEnumDescriptor(_ORDERFAILURE_CAUSE)

_ORDERFULFILLABILITY_ORDERFULFILLABILITYRESULT = _descriptor.EnumDescriptor(
  name='OrderFulfillabilityResult',
  full_name='ext.maps.booking.partner.v3.OrderFulfillability.OrderFulfillabilityResult',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ORDER_FULFILLABILITY_RESULT_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CAN_FULFILL', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='UNFULFILLABLE_LINE_ITEM', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='UNFULFILLABLE_SERVICE_COMBINATION', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ORDER_UNFULFILLABLE_OTHER_REASON', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=8368,
  serialized_end=8563,
)
_sym_db.RegisterEnumDescriptor(_ORDERFULFILLABILITY_ORDERFULFILLABILITYRESULT)

_LINEITEMFULFILLABILITY_ITEMFULFILLABILITYRESULT = _descriptor.EnumDescriptor(
  name='ItemFulfillabilityResult',
  full_name='ext.maps.booking.partner.v3.LineItemFulfillability.ItemFulfillabilityResult',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ITEM_FULFILLABILITY_RESULT_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CAN_FULFILL', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SLOT_UNAVAILABLE', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CHILD_TICKETS_WITHOUT_ADULT', index=3, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='UNFULFILLABLE_TICKET_COMBINATION', index=4, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='INCORRECT_PRICE', index=5, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TICKET_CONSTRAINT_VIOLATED', index=6, number=7,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ITEM_UNFULFILLABLE_OTHER_REASON', index=7, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=9202,
  serialized_end=9472,
)
_sym_db.RegisterEnumDescriptor(_LINEITEMFULFILLABILITY_ITEMFULFILLABILITYRESULT)

_ACTIONLINK_ACTIONLINKTYPE = _descriptor.EnumDescriptor(
  name='ActionLinkType',
  full_name='ext.maps.booking.partner.v3.ActionLink.ActionLinkType',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ACTION_LINK_TYPE_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ACTION_LINK_TYPE_BOOK_APPOINTMENT', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ACTION_LINK_TYPE_BOOK_ONLINE_APPOINTMENT', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ACTION_LINK_TYPE_ORDER_FOOD', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ACTION_LINK_TYPE_ORDER_FOOD_DELIVERY', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ACTION_LINK_TYPE_ORDER_FOOD_TAKEOUT', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ACTION_LINK_TYPE_MAKE_DINING_RESERVATION', index=6, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ACTION_LINK_TYPE_SHOP_ONLINE', index=7, number=7,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=10614,
  serialized_end=10945,
)
_sym_db.RegisterEnumDescriptor(_ACTIONLINK_ACTIONLINKTYPE)

_PAYMENTPROCESSINGPARAMETERS_PAYMENTPROCESSOR = _descriptor.EnumDescriptor(
  name='PaymentProcessor',
  full_name='ext.maps.booking.partner.v3.PaymentProcessingParameters.PaymentProcessor',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='PAYMENT_PROCESSOR_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PROCESSOR_STRIPE', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PROCESSOR_BRAINTREE', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=11350,
  serialized_end=11450,
)
_sym_db.RegisterEnumDescriptor(_PAYMENTPROCESSINGPARAMETERS_PAYMENTPROCESSOR)

_PAYMENTINFORMATION_PAYMENTPROCESSEDBY = _descriptor.EnumDescriptor(
  name='PaymentProcessedBy',
  full_name='ext.maps.booking.partner.v3.PaymentInformation.PaymentProcessedBy',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='PAYMENT_PROCESSED_BY_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PROCESSED_BY_GOOGLE', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PROCESSED_BY_PARTNER', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=12393,
  serialized_end=12502,
)
_sym_db.RegisterEnumDescriptor(_PAYMENTINFORMATION_PAYMENTPROCESSEDBY)

_TOKENIZATIONCONFIG_BILLINGINFORMATIONFORMAT = _descriptor.EnumDescriptor(
  name='BillingInformationFormat',
  full_name='ext.maps.booking.partner.v3.TokenizationConfig.BillingInformationFormat',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='BILLING_INFORMATION_FORMAT_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='MIN', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='FULL', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=13613,
  serialized_end=13702,
)
_sym_db.RegisterEnumDescriptor(_TOKENIZATIONCONFIG_BILLINGINFORMATIONFORMAT)

_TOKENIZATIONCONFIG_AUTHMETHOD = _descriptor.EnumDescriptor(
  name='AuthMethod',
  full_name='ext.maps.booking.partner.v3.TokenizationConfig.AuthMethod',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='AUTH_METHOD_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PAN_ONLY', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CRYPTOGRAM_3DS', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=13704,
  serialized_end=13779,
)
_sym_db.RegisterEnumDescriptor(_TOKENIZATIONCONFIG_AUTHMETHOD)

_CREDITCARDRESTRICTIONS_CREDITCARDTYPE = _descriptor.EnumDescriptor(
  name='CreditCardType',
  full_name='ext.maps.booking.partner.v3.CreditCardRestrictions.CreditCardType',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='CREDIT_CARD_TYPE_UNSPECIFIED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='VISA', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='MASTERCARD', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='AMERICAN_EXPRESS', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='DISCOVER', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='JCB', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=13902,
  serialized_end=14023,
)
_sym_db.RegisterEnumDescriptor(_CREDITCARDRESTRICTIONS_CREDITCARDTYPE)


_BATCHAVAILABILITYLOOKUPREQUEST = _descriptor.Descriptor(
  name='BatchAvailabilityLookupRequest',
  full_name='ext.maps.booking.partner.v3.BatchAvailabilityLookupRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='merchant_id', full_name='ext.maps.booking.partner.v3.BatchAvailabilityLookupRequest.merchant_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='slot_time', full_name='ext.maps.booking.partner.v3.BatchAvailabilityLookupRequest.slot_time', index=1,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=125,
  serialized_end=242,
)


_BATCHAVAILABILITYLOOKUPRESPONSE = _descriptor.Descriptor(
  name='BatchAvailabilityLookupResponse',
  full_name='ext.maps.booking.partner.v3.BatchAvailabilityLookupResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='slot_time_availability', full_name='ext.maps.booking.partner.v3.BatchAvailabilityLookupResponse.slot_time_availability', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=244,
  serialized_end=360,
)


_SLOTTIME = _descriptor.Descriptor(
  name='SlotTime',
  full_name='ext.maps.booking.partner.v3.SlotTime',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='service_id', full_name='ext.maps.booking.partner.v3.SlotTime.service_id', index=0,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='start_sec', full_name='ext.maps.booking.partner.v3.SlotTime.start_sec', index=1,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='duration_sec', full_name='ext.maps.booking.partner.v3.SlotTime.duration_sec', index=2,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='availability_tag', full_name='ext.maps.booking.partner.v3.SlotTime.availability_tag', index=3,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='resource_ids', full_name='ext.maps.booking.partner.v3.SlotTime.resource_ids', index=4,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='confirmation_mode', full_name='ext.maps.booking.partner.v3.SlotTime.confirmation_mode', index=5,
      number=6, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=363,
  serialized_end=598,
)


_SLOTTIMEAVAILABILITY = _descriptor.Descriptor(
  name='SlotTimeAvailability',
  full_name='ext.maps.booking.partner.v3.SlotTimeAvailability',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='slot_time', full_name='ext.maps.booking.partner.v3.SlotTimeAvailability.slot_time', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='available', full_name='ext.maps.booking.partner.v3.SlotTimeAvailability.available', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=600,
  serialized_end=699,
)


_CHECKAVAILABILITYREQUEST = _descriptor.Descriptor(
  name='CheckAvailabilityRequest',
  full_name='ext.maps.booking.partner.v3.CheckAvailabilityRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='slot', full_name='ext.maps.booking.partner.v3.CheckAvailabilityRequest.slot', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=701,
  serialized_end=776,
)


_CHECKAVAILABILITYRESPONSE = _descriptor.Descriptor(
  name='CheckAvailabilityResponse',
  full_name='ext.maps.booking.partner.v3.CheckAvailabilityResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='slot', full_name='ext.maps.booking.partner.v3.CheckAvailabilityResponse.slot', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='count_available', full_name='ext.maps.booking.partner.v3.CheckAvailabilityResponse.count_available', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='last_online_cancellable_sec', full_name='ext.maps.booking.partner.v3.CheckAvailabilityResponse.last_online_cancellable_sec', index=2,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='duration_requirement', full_name='ext.maps.booking.partner.v3.CheckAvailabilityResponse.duration_requirement', index=3,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='availability_update', full_name='ext.maps.booking.partner.v3.CheckAvailabilityResponse.availability_update', index=4,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _CHECKAVAILABILITYRESPONSE_DURATIONREQUIREMENT,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=779,
  serialized_end=1220,
)


_AVAILABILITYUPDATE = _descriptor.Descriptor(
  name='AvailabilityUpdate',
  full_name='ext.maps.booking.partner.v3.AvailabilityUpdate',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='slot_availability', full_name='ext.maps.booking.partner.v3.AvailabilityUpdate.slot_availability', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1222,
  serialized_end=1316,
)


_SLOTAVAILABILITY = _descriptor.Descriptor(
  name='SlotAvailability',
  full_name='ext.maps.booking.partner.v3.SlotAvailability',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='slot', full_name='ext.maps.booking.partner.v3.SlotAvailability.slot', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='count_available', full_name='ext.maps.booking.partner.v3.SlotAvailability.count_available', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1318,
  serialized_end=1410,
)


_CHECKORDERFULFILLABILITYREQUEST = _descriptor.Descriptor(
  name='CheckOrderFulfillabilityRequest',
  full_name='ext.maps.booking.partner.v3.CheckOrderFulfillabilityRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='merchant_id', full_name='ext.maps.booking.partner.v3.CheckOrderFulfillabilityRequest.merchant_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='item', full_name='ext.maps.booking.partner.v3.CheckOrderFulfillabilityRequest.item', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='cart_id', full_name='ext.maps.booking.partner.v3.CheckOrderFulfillabilityRequest.cart_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1412,
  serialized_end=1536,
)


_CHECKORDERFULFILLABILITYRESPONSE = _descriptor.Descriptor(
  name='CheckOrderFulfillabilityResponse',
  full_name='ext.maps.booking.partner.v3.CheckOrderFulfillabilityResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='fulfillability', full_name='ext.maps.booking.partner.v3.CheckOrderFulfillabilityResponse.fulfillability', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fees_and_taxes', full_name='ext.maps.booking.partner.v3.CheckOrderFulfillabilityResponse.fees_and_taxes', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fees', full_name='ext.maps.booking.partner.v3.CheckOrderFulfillabilityResponse.fees', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='cart_expiration_sec', full_name='ext.maps.booking.partner.v3.CheckOrderFulfillabilityResponse.cart_expiration_sec', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1539,
  serialized_end=1785,
)


_FEES = _descriptor.Descriptor(
  name='Fees',
  full_name='ext.maps.booking.partner.v3.Fees',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='per_ticket_fee', full_name='ext.maps.booking.partner.v3.Fees.per_ticket_fee', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='per_order_fee', full_name='ext.maps.booking.partner.v3.Fees.per_order_fee', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1788,
  serialized_end=1942,
)


_SPECIFICPERTICKETFEE = _descriptor.Descriptor(
  name='SpecificPerTicketFee',
  full_name='ext.maps.booking.partner.v3.SpecificPerTicketFee',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ticket_id', full_name='ext.maps.booking.partner.v3.SpecificPerTicketFee.ticket_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='service_id', full_name='ext.maps.booking.partner.v3.SpecificPerTicketFee.service_id', index=1,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fee_name', full_name='ext.maps.booking.partner.v3.SpecificPerTicketFee.fee_name', index=2,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fee_amount', full_name='ext.maps.booking.partner.v3.SpecificPerTicketFee.fee_amount', index=3,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1945,
  serialized_end=2080,
)


_SPECIFICPERORDERFEE = _descriptor.Descriptor(
  name='SpecificPerOrderFee',
  full_name='ext.maps.booking.partner.v3.SpecificPerOrderFee',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='fee_name', full_name='ext.maps.booking.partner.v3.SpecificPerOrderFee.fee_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fee_amount', full_name='ext.maps.booking.partner.v3.SpecificPerOrderFee.fee_amount', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2082,
  serialized_end=2177,
)


_GETBOOKINGSTATUSREQUEST = _descriptor.Descriptor(
  name='GetBookingStatusRequest',
  full_name='ext.maps.booking.partner.v3.GetBookingStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='booking_id', full_name='ext.maps.booking.partner.v3.GetBookingStatusRequest.booking_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2179,
  serialized_end=2224,
)


_GETBOOKINGSTATUSRESPONSE = _descriptor.Descriptor(
  name='GetBookingStatusResponse',
  full_name='ext.maps.booking.partner.v3.GetBookingStatusResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='booking_id', full_name='ext.maps.booking.partner.v3.GetBookingStatusResponse.booking_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='booking_status', full_name='ext.maps.booking.partner.v3.GetBookingStatusResponse.booking_status', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='prepayment_status', full_name='ext.maps.booking.partner.v3.GetBookingStatusResponse.prepayment_status', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2227,
  serialized_end=2415,
)


_CREATEBOOKINGREQUEST = _descriptor.Descriptor(
  name='CreateBookingRequest',
  full_name='ext.maps.booking.partner.v3.CreateBookingRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='slot', full_name='ext.maps.booking.partner.v3.CreateBookingRequest.slot', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lease_ref', full_name='ext.maps.booking.partner.v3.CreateBookingRequest.lease_ref', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='user_information', full_name='ext.maps.booking.partner.v3.CreateBookingRequest.user_information', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='payment_information', full_name='ext.maps.booking.partner.v3.CreateBookingRequest.payment_information', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='payment_processing_parameters', full_name='ext.maps.booking.partner.v3.CreateBookingRequest.payment_processing_parameters', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='idempotency_token', full_name='ext.maps.booking.partner.v3.CreateBookingRequest.idempotency_token', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='additional_request', full_name='ext.maps.booking.partner.v3.CreateBookingRequest.additional_request', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='offer_id', full_name='ext.maps.booking.partner.v3.CreateBookingRequest.offer_id', index=7,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='deal_id', full_name='ext.maps.booking.partner.v3.CreateBookingRequest.deal_id', index=8,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2418,
  serialized_end=2894,
)


_CREATEBOOKINGRESPONSE = _descriptor.Descriptor(
  name='CreateBookingResponse',
  full_name='ext.maps.booking.partner.v3.CreateBookingResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='booking', full_name='ext.maps.booking.partner.v3.CreateBookingResponse.booking', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='user_payment_option', full_name='ext.maps.booking.partner.v3.CreateBookingResponse.user_payment_option', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='booking_failure', full_name='ext.maps.booking.partner.v3.CreateBookingResponse.booking_failure', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2897,
  serialized_end=3122,
)


_CREATELEASEREQUEST = _descriptor.Descriptor(
  name='CreateLeaseRequest',
  full_name='ext.maps.booking.partner.v3.CreateLeaseRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='lease', full_name='ext.maps.booking.partner.v3.CreateLeaseRequest.lease', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3124,
  serialized_end=3195,
)


_CREATELEASERESPONSE = _descriptor.Descriptor(
  name='CreateLeaseResponse',
  full_name='ext.maps.booking.partner.v3.CreateLeaseResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='lease', full_name='ext.maps.booking.partner.v3.CreateLeaseResponse.lease', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='booking_failure', full_name='ext.maps.booking.partner.v3.CreateLeaseResponse.booking_failure', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3198,
  serialized_end=3340,
)


_LEASE = _descriptor.Descriptor(
  name='Lease',
  full_name='ext.maps.booking.partner.v3.Lease',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='lease_id', full_name='ext.maps.booking.partner.v3.Lease.lease_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='slot', full_name='ext.maps.booking.partner.v3.Lease.slot', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='user_reference', full_name='ext.maps.booking.partner.v3.Lease.user_reference', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lease_expiration_time_sec', full_name='ext.maps.booking.partner.v3.Lease.lease_expiration_time_sec', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3343,
  serialized_end=3476,
)


_LEASEREFERENCE = _descriptor.Descriptor(
  name='LeaseReference',
  full_name='ext.maps.booking.partner.v3.LeaseReference',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='lease_id', full_name='ext.maps.booking.partner.v3.LeaseReference.lease_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3478,
  serialized_end=3512,
)


_CREATEORDERREQUEST = _descriptor.Descriptor(
  name='CreateOrderRequest',
  full_name='ext.maps.booking.partner.v3.CreateOrderRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='order', full_name='ext.maps.booking.partner.v3.CreateOrderRequest.order', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='payment_processing_parameters', full_name='ext.maps.booking.partner.v3.CreateOrderRequest.payment_processing_parameters', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='idempotency_token', full_name='ext.maps.booking.partner.v3.CreateOrderRequest.idempotency_token', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3515,
  serialized_end=3710,
)


_CREATEORDERRESPONSE = _descriptor.Descriptor(
  name='CreateOrderResponse',
  full_name='ext.maps.booking.partner.v3.CreateOrderResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='order', full_name='ext.maps.booking.partner.v3.CreateOrderResponse.order', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='order_failure', full_name='ext.maps.booking.partner.v3.CreateOrderResponse.order_failure', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='result', full_name='ext.maps.booking.partner.v3.CreateOrderResponse.result',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=3713,
  serialized_end=3865,
)


_LISTBOOKINGSREQUEST = _descriptor.Descriptor(
  name='ListBookingsRequest',
  full_name='ext.maps.booking.partner.v3.ListBookingsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='user_id', full_name='ext.maps.booking.partner.v3.ListBookingsRequest.user_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3867,
  serialized_end=3905,
)


_LISTBOOKINGSRESPONSE = _descriptor.Descriptor(
  name='ListBookingsResponse',
  full_name='ext.maps.booking.partner.v3.ListBookingsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='bookings', full_name='ext.maps.booking.partner.v3.ListBookingsResponse.bookings', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3907,
  serialized_end=3985,
)


_LISTORDERSREQUEST_ORDERIDS = _descriptor.Descriptor(
  name='OrderIds',
  full_name='ext.maps.booking.partner.v3.ListOrdersRequest.OrderIds',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_id', full_name='ext.maps.booking.partner.v3.ListOrdersRequest.OrderIds.order_id', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4106,
  serialized_end=4134,
)

_LISTORDERSREQUEST = _descriptor.Descriptor(
  name='ListOrdersRequest',
  full_name='ext.maps.booking.partner.v3.ListOrdersRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='user_id', full_name='ext.maps.booking.partner.v3.ListOrdersRequest.user_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='order_ids', full_name='ext.maps.booking.partner.v3.ListOrdersRequest.order_ids', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_LISTORDERSREQUEST_ORDERIDS, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='ids', full_name='ext.maps.booking.partner.v3.ListOrdersRequest.ids',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=3988,
  serialized_end=4141,
)


_LISTORDERSRESPONSE = _descriptor.Descriptor(
  name='ListOrdersResponse',
  full_name='ext.maps.booking.partner.v3.ListOrdersResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='order', full_name='ext.maps.booking.partner.v3.ListOrdersResponse.order', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4143,
  serialized_end=4214,
)


_UPDATEBOOKINGREQUEST = _descriptor.Descriptor(
  name='UpdateBookingRequest',
  full_name='ext.maps.booking.partner.v3.UpdateBookingRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='booking', full_name='ext.maps.booking.partner.v3.UpdateBookingRequest.booking', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4216,
  serialized_end=4293,
)


_UPDATEBOOKINGRESPONSE = _descriptor.Descriptor(
  name='UpdateBookingResponse',
  full_name='ext.maps.booking.partner.v3.UpdateBookingResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='booking', full_name='ext.maps.booking.partner.v3.UpdateBookingResponse.booking', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='user_payment_option', full_name='ext.maps.booking.partner.v3.UpdateBookingResponse.user_payment_option', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='booking_failure', full_name='ext.maps.booking.partner.v3.UpdateBookingResponse.booking_failure', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4296,
  serialized_end=4521,
)


_BOOKING = _descriptor.Descriptor(
  name='Booking',
  full_name='ext.maps.booking.partner.v3.Booking',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='booking_id', full_name='ext.maps.booking.partner.v3.Booking.booking_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='slot', full_name='ext.maps.booking.partner.v3.Booking.slot', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='user_information', full_name='ext.maps.booking.partner.v3.Booking.user_information', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='status', full_name='ext.maps.booking.partner.v3.Booking.status', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='payment_information', full_name='ext.maps.booking.partner.v3.Booking.payment_information', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='virtual_session_info', full_name='ext.maps.booking.partner.v3.Booking.virtual_session_info', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='offer_info', full_name='ext.maps.booking.partner.v3.Booking.offer_info', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4524,
  serialized_end=4951,
)


_VIRTUALSESSIONINFO = _descriptor.Descriptor(
  name='VirtualSessionInfo',
  full_name='ext.maps.booking.partner.v3.VirtualSessionInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='session_url', full_name='ext.maps.booking.partner.v3.VirtualSessionInfo.session_url', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='meeting_id', full_name='ext.maps.booking.partner.v3.VirtualSessionInfo.meeting_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='password', full_name='ext.maps.booking.partner.v3.VirtualSessionInfo.password', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4953,
  serialized_end=5032,
)


_BOOKINGFAILURE_PAYMENTFAILUREINFORMATION_THREEDS1PARAMETERS = _descriptor.Descriptor(
  name='ThreeDS1Parameters',
  full_name='ext.maps.booking.partner.v3.BookingFailure.PaymentFailureInformation.ThreeDS1Parameters',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='acs_url', full_name='ext.maps.booking.partner.v3.BookingFailure.PaymentFailureInformation.ThreeDS1Parameters.acs_url', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pa_req', full_name='ext.maps.booking.partner.v3.BookingFailure.PaymentFailureInformation.ThreeDS1Parameters.pa_req', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='transaction_id', full_name='ext.maps.booking.partner.v3.BookingFailure.PaymentFailureInformation.ThreeDS1Parameters.transaction_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='md_merchant_data', full_name='ext.maps.booking.partner.v3.BookingFailure.PaymentFailureInformation.ThreeDS1Parameters.md_merchant_data', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5458,
  serialized_end=5561,
)

_BOOKINGFAILURE_PAYMENTFAILUREINFORMATION = _descriptor.Descriptor(
  name='PaymentFailureInformation',
  full_name='ext.maps.booking.partner.v3.BookingFailure.PaymentFailureInformation',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='threeds1_parameters', full_name='ext.maps.booking.partner.v3.BookingFailure.PaymentFailureInformation.threeds1_parameters', index=0,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_BOOKINGFAILURE_PAYMENTFAILUREINFORMATION_THREEDS1PARAMETERS, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5310,
  serialized_end=5561,
)

_BOOKINGFAILURE = _descriptor.Descriptor(
  name='BookingFailure',
  full_name='ext.maps.booking.partner.v3.BookingFailure',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cause', full_name='ext.maps.booking.partner.v3.BookingFailure.cause', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rejected_card_type', full_name='ext.maps.booking.partner.v3.BookingFailure.rejected_card_type', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='description', full_name='ext.maps.booking.partner.v3.BookingFailure.description', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='payment_failure', full_name='ext.maps.booking.partner.v3.BookingFailure.payment_failure', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_BOOKINGFAILURE_PAYMENTFAILUREINFORMATION, ],
  enum_types=[
    _BOOKINGFAILURE_CAUSE,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5035,
  serialized_end=6051,
)


_PAYMENTFAILUREINFORMATION_THREEDS1PARAMETERS = _descriptor.Descriptor(
  name='ThreeDS1Parameters',
  full_name='ext.maps.booking.partner.v3.PaymentFailureInformation.ThreeDS1Parameters',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='acs_url', full_name='ext.maps.booking.partner.v3.PaymentFailureInformation.ThreeDS1Parameters.acs_url', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pa_req', full_name='ext.maps.booking.partner.v3.PaymentFailureInformation.ThreeDS1Parameters.pa_req', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='transaction_id', full_name='ext.maps.booking.partner.v3.PaymentFailureInformation.ThreeDS1Parameters.transaction_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='md_merchant_data', full_name='ext.maps.booking.partner.v3.PaymentFailureInformation.ThreeDS1Parameters.md_merchant_data', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5458,
  serialized_end=5561,
)

_PAYMENTFAILUREINFORMATION = _descriptor.Descriptor(
  name='PaymentFailureInformation',
  full_name='ext.maps.booking.partner.v3.PaymentFailureInformation',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='threeds1_parameters', full_name='ext.maps.booking.partner.v3.PaymentFailureInformation.threeds1_parameters', index=0,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_PAYMENTFAILUREINFORMATION_THREEDS1PARAMETERS, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6054,
  serialized_end=6290,
)


_THREEDS1PARAMETERS = _descriptor.Descriptor(
  name='ThreeDS1Parameters',
  full_name='ext.maps.booking.partner.v3.ThreeDS1Parameters',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='acs_url', full_name='ext.maps.booking.partner.v3.ThreeDS1Parameters.acs_url', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pa_req', full_name='ext.maps.booking.partner.v3.ThreeDS1Parameters.pa_req', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='transaction_id', full_name='ext.maps.booking.partner.v3.ThreeDS1Parameters.transaction_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='md_merchant_data', full_name='ext.maps.booking.partner.v3.ThreeDS1Parameters.md_merchant_data', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5458,
  serialized_end=5561,
)


_ORDER = _descriptor.Descriptor(
  name='Order',
  full_name='ext.maps.booking.partner.v3.Order',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_id', full_name='ext.maps.booking.partner.v3.Order.order_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='user_information', full_name='ext.maps.booking.partner.v3.Order.user_information', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='payment_information', full_name='ext.maps.booking.partner.v3.Order.payment_information', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='merchant_id', full_name='ext.maps.booking.partner.v3.Order.merchant_id', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='item', full_name='ext.maps.booking.partner.v3.Order.item', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6398,
  serialized_end=6647,
)


_LINEITEM_ORDEREDTICKETS = _descriptor.Descriptor(
  name='OrderedTickets',
  full_name='ext.maps.booking.partner.v3.LineItem.OrderedTickets',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ticket_id', full_name='ext.maps.booking.partner.v3.LineItem.OrderedTickets.ticket_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='count', full_name='ext.maps.booking.partner.v3.LineItem.OrderedTickets.count', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7059,
  serialized_end=7109,
)

_LINEITEM = _descriptor.Descriptor(
  name='LineItem',
  full_name='ext.maps.booking.partner.v3.LineItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='service_id', full_name='ext.maps.booking.partner.v3.LineItem.service_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='start_sec', full_name='ext.maps.booking.partner.v3.LineItem.start_sec', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='duration_sec', full_name='ext.maps.booking.partner.v3.LineItem.duration_sec', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tickets', full_name='ext.maps.booking.partner.v3.LineItem.tickets', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='price', full_name='ext.maps.booking.partner.v3.LineItem.price', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='status', full_name='ext.maps.booking.partner.v3.LineItem.status', index=5,
      number=6, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='intake_form_answers', full_name='ext.maps.booking.partner.v3.LineItem.intake_form_answers', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='warning_reason', full_name='ext.maps.booking.partner.v3.LineItem.warning_reason', index=7,
      number=8, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_LINEITEM_ORDEREDTICKETS, ],
  enum_types=[
    _LINEITEM_WARNINGREASON,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6650,
  serialized_end=7198,
)


_INTAKEFORMANSWERS = _descriptor.Descriptor(
  name='IntakeFormAnswers',
  full_name='ext.maps.booking.partner.v3.IntakeFormAnswers',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='answer', full_name='ext.maps.booking.partner.v3.IntakeFormAnswers.answer', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7200,
  serialized_end=7287,
)


_INTAKEFORMFIELDANSWER = _descriptor.Descriptor(
  name='IntakeFormFieldAnswer',
  full_name='ext.maps.booking.partner.v3.IntakeFormFieldAnswer',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='ext.maps.booking.partner.v3.IntakeFormFieldAnswer.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='response', full_name='ext.maps.booking.partner.v3.IntakeFormFieldAnswer.response', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7289,
  serialized_end=7342,
)


_ORDERFAILURE_PAYMENTFAILUREINFORMATION_THREEDS1PARAMETERS = _descriptor.Descriptor(
  name='ThreeDS1Parameters',
  full_name='ext.maps.booking.partner.v3.OrderFailure.PaymentFailureInformation.ThreeDS1Parameters',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='acs_url', full_name='ext.maps.booking.partner.v3.OrderFailure.PaymentFailureInformation.ThreeDS1Parameters.acs_url', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pa_req', full_name='ext.maps.booking.partner.v3.OrderFailure.PaymentFailureInformation.ThreeDS1Parameters.pa_req', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='transaction_id', full_name='ext.maps.booking.partner.v3.OrderFailure.PaymentFailureInformation.ThreeDS1Parameters.transaction_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='md_merchant_data', full_name='ext.maps.booking.partner.v3.OrderFailure.PaymentFailureInformation.ThreeDS1Parameters.md_merchant_data', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5458,
  serialized_end=5561,
)

_ORDERFAILURE_PAYMENTFAILUREINFORMATION = _descriptor.Descriptor(
  name='PaymentFailureInformation',
  full_name='ext.maps.booking.partner.v3.OrderFailure.PaymentFailureInformation',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='threeds1_parameters', full_name='ext.maps.booking.partner.v3.OrderFailure.PaymentFailureInformation.threeds1_parameters', index=0,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_ORDERFAILURE_PAYMENTFAILUREINFORMATION_THREEDS1PARAMETERS, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7688,
  serialized_end=7937,
)

_ORDERFAILURE = _descriptor.Descriptor(
  name='OrderFailure',
  full_name='ext.maps.booking.partner.v3.OrderFailure',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cause', full_name='ext.maps.booking.partner.v3.OrderFailure.cause', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fulfillability', full_name='ext.maps.booking.partner.v3.OrderFailure.fulfillability', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rejected_card_type', full_name='ext.maps.booking.partner.v3.OrderFailure.rejected_card_type', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='description', full_name='ext.maps.booking.partner.v3.OrderFailure.description', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='payment_failure', full_name='ext.maps.booking.partner.v3.OrderFailure.payment_failure', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_ORDERFAILURE_PAYMENTFAILUREINFORMATION, ],
  enum_types=[
    _ORDERFAILURE_CAUSE,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7345,
  serialized_end=8137,
)


_ORDERFULFILLABILITY = _descriptor.Descriptor(
  name='OrderFulfillability',
  full_name='ext.maps.booking.partner.v3.OrderFulfillability',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='ext.maps.booking.partner.v3.OrderFulfillability.result', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='item_fulfillability', full_name='ext.maps.booking.partner.v3.OrderFulfillability.item_fulfillability', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='unfulfillable_reason', full_name='ext.maps.booking.partner.v3.OrderFulfillability.unfulfillable_reason', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _ORDERFULFILLABILITY_ORDERFULFILLABILITYRESULT,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8140,
  serialized_end=8563,
)


_LINEITEMFULFILLABILITY_UPDATEDAVAILABILITY = _descriptor.Descriptor(
  name='UpdatedAvailability',
  full_name='ext.maps.booking.partner.v3.LineItemFulfillability.UpdatedAvailability',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='spots_open', full_name='ext.maps.booking.partner.v3.LineItemFulfillability.UpdatedAvailability.spots_open', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9040,
  serialized_end=9081,
)

_LINEITEMFULFILLABILITY_VIOLATEDTICKETCONSTRAINT = _descriptor.Descriptor(
  name='ViolatedTicketConstraint',
  full_name='ext.maps.booking.partner.v3.LineItemFulfillability.ViolatedTicketConstraint',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='min_ticket_count', full_name='ext.maps.booking.partner.v3.LineItemFulfillability.ViolatedTicketConstraint.min_ticket_count', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max_ticket_count', full_name='ext.maps.booking.partner.v3.LineItemFulfillability.ViolatedTicketConstraint.max_ticket_count', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ticket_id', full_name='ext.maps.booking.partner.v3.LineItemFulfillability.ViolatedTicketConstraint.ticket_id', index=2,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='value', full_name='ext.maps.booking.partner.v3.LineItemFulfillability.ViolatedTicketConstraint.value',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=9083,
  serialized_end=9199,
)

_LINEITEMFULFILLABILITY = _descriptor.Descriptor(
  name='LineItemFulfillability',
  full_name='ext.maps.booking.partner.v3.LineItemFulfillability',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='item', full_name='ext.maps.booking.partner.v3.LineItemFulfillability.item', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='result', full_name='ext.maps.booking.partner.v3.LineItemFulfillability.result', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='unfulfillable_reason', full_name='ext.maps.booking.partner.v3.LineItemFulfillability.unfulfillable_reason', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='availability', full_name='ext.maps.booking.partner.v3.LineItemFulfillability.availability', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ticket_type', full_name='ext.maps.booking.partner.v3.LineItemFulfillability.ticket_type', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='violated_ticket_constraint', full_name='ext.maps.booking.partner.v3.LineItemFulfillability.violated_ticket_constraint', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_LINEITEMFULFILLABILITY_UPDATEDAVAILABILITY, _LINEITEMFULFILLABILITY_VIOLATEDTICKETCONSTRAINT, ],
  enum_types=[
    _LINEITEMFULFILLABILITY_ITEMFULFILLABILITYRESULT,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8566,
  serialized_end=9472,
)


_TICKETTYPE = _descriptor.Descriptor(
  name='TicketType',
  full_name='ext.maps.booking.partner.v3.TicketType',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ticket_type_id', full_name='ext.maps.booking.partner.v3.TicketType.ticket_type_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='short_description', full_name='ext.maps.booking.partner.v3.TicketType.short_description', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='localized_short_description', full_name='ext.maps.booking.partner.v3.TicketType.localized_short_description', index=2,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='price', full_name='ext.maps.booking.partner.v3.TicketType.price', index=3,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='per_ticket_fee', full_name='ext.maps.booking.partner.v3.TicketType.per_ticket_fee', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='option_description', full_name='ext.maps.booking.partner.v3.TicketType.option_description', index=5,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='localized_option_description', full_name='ext.maps.booking.partner.v3.TicketType.localized_option_description', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9475,
  serialized_end=9837,
)


_PERTICKETFEE = _descriptor.Descriptor(
  name='PerTicketFee',
  full_name='ext.maps.booking.partner.v3.PerTicketFee',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='service_charge', full_name='ext.maps.booking.partner.v3.PerTicketFee.service_charge', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='facility_fee', full_name='ext.maps.booking.partner.v3.PerTicketFee.facility_fee', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='taxes', full_name='ext.maps.booking.partner.v3.PerTicketFee.taxes', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9840,
  serialized_end=10023,
)


_TEXT = _descriptor.Descriptor(
  name='Text',
  full_name='ext.maps.booking.partner.v3.Text',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='value', full_name='ext.maps.booking.partner.v3.Text.value', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='localized_value', full_name='ext.maps.booking.partner.v3.Text.localized_value', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10025,
  serialized_end=10117,
)


_LOCALIZEDSTRING = _descriptor.Descriptor(
  name='LocalizedString',
  full_name='ext.maps.booking.partner.v3.LocalizedString',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='locale', full_name='ext.maps.booking.partner.v3.LocalizedString.locale', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='ext.maps.booking.partner.v3.LocalizedString.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10119,
  serialized_end=10167,
)


_MERCHANTMATCHINGHINTS = _descriptor.Descriptor(
  name='MerchantMatchingHints',
  full_name='ext.maps.booking.partner.v3.MerchantMatchingHints',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='place_id', full_name='ext.maps.booking.partner.v3.MerchantMatchingHints.place_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10169,
  serialized_end=10210,
)


_SERVICEATTRIBUTE_VALUE = _descriptor.Descriptor(
  name='Value',
  full_name='ext.maps.booking.partner.v3.ServiceAttribute.Value',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='value_id', full_name='ext.maps.booking.partner.v3.ServiceAttribute.Value.value_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value_name', full_name='ext.maps.booking.partner.v3.ServiceAttribute.Value.value_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10347,
  serialized_end=10392,
)

_SERVICEATTRIBUTE = _descriptor.Descriptor(
  name='ServiceAttribute',
  full_name='ext.maps.booking.partner.v3.ServiceAttribute',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='attribute_id', full_name='ext.maps.booking.partner.v3.ServiceAttribute.attribute_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='attribute_name', full_name='ext.maps.booking.partner.v3.ServiceAttribute.attribute_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='ext.maps.booking.partner.v3.ServiceAttribute.value', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_SERVICEATTRIBUTE_VALUE, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10213,
  serialized_end=10392,
)


_ACTIONLINK = _descriptor.Descriptor(
  name='ActionLink',
  full_name='ext.maps.booking.partner.v3.ActionLink',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='url', full_name='ext.maps.booking.partner.v3.ActionLink.url', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='language', full_name='ext.maps.booking.partner.v3.ActionLink.language', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='restricted_country', full_name='ext.maps.booking.partner.v3.ActionLink.restricted_country', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='platform', full_name='ext.maps.booking.partner.v3.ActionLink.platform', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='action_link_type', full_name='ext.maps.booking.partner.v3.ActionLink.action_link_type', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _ACTIONLINK_ACTIONLINKTYPE,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10395,
  serialized_end=10945,
)


_RESOURCEIDS = _descriptor.Descriptor(
  name='ResourceIds',
  full_name='ext.maps.booking.partner.v3.ResourceIds',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='staff_id', full_name='ext.maps.booking.partner.v3.ResourceIds.staff_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='room_id', full_name='ext.maps.booking.partner.v3.ResourceIds.room_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='party_size', full_name='ext.maps.booking.partner.v3.ResourceIds.party_size', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10947,
  serialized_end=11015,
)


_PAYMENTPROCESSINGPARAMETERS = _descriptor.Descriptor(
  name='PaymentProcessingParameters',
  full_name='ext.maps.booking.partner.v3.PaymentProcessingParameters',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='processor', full_name='ext.maps.booking.partner.v3.PaymentProcessingParameters.processor', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='payment_method_token', full_name='ext.maps.booking.partner.v3.PaymentProcessingParameters.payment_method_token', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='unparsed_payment_method_token', full_name='ext.maps.booking.partner.v3.PaymentProcessingParameters.unparsed_payment_method_token', index=2,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='version', full_name='ext.maps.booking.partner.v3.PaymentProcessingParameters.version', index=3,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='payment_processor', full_name='ext.maps.booking.partner.v3.PaymentProcessingParameters.payment_processor', index=4,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tokenization_config', full_name='ext.maps.booking.partner.v3.PaymentProcessingParameters.tokenization_config', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _PAYMENTPROCESSINGPARAMETERS_PAYMENTPROCESSOR,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11018,
  serialized_end=11450,
)


_USERPAYMENTOPTION = _descriptor.Descriptor(
  name='UserPaymentOption',
  full_name='ext.maps.booking.partner.v3.UserPaymentOption',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='user_payment_option_id', full_name='ext.maps.booking.partner.v3.UserPaymentOption.user_payment_option_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='valid_start_time_sec', full_name='ext.maps.booking.partner.v3.UserPaymentOption.valid_start_time_sec', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='valid_end_time_sec', full_name='ext.maps.booking.partner.v3.UserPaymentOption.valid_end_time_sec', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='type', full_name='ext.maps.booking.partner.v3.UserPaymentOption.type', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='original_count', full_name='ext.maps.booking.partner.v3.UserPaymentOption.original_count', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='current_count', full_name='ext.maps.booking.partner.v3.UserPaymentOption.current_count', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='payment_option_id', full_name='ext.maps.booking.partner.v3.UserPaymentOption.payment_option_id', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11453,
  serialized_end=11698,
)


_PAYMENTINFORMATION = _descriptor.Descriptor(
  name='PaymentInformation',
  full_name='ext.maps.booking.partner.v3.PaymentInformation',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='prepayment_status', full_name='ext.maps.booking.partner.v3.PaymentInformation.prepayment_status', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='payment_transaction_id', full_name='ext.maps.booking.partner.v3.PaymentInformation.payment_transaction_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='price', full_name='ext.maps.booking.partner.v3.PaymentInformation.price', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tax_amount', full_name='ext.maps.booking.partner.v3.PaymentInformation.tax_amount', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fees', full_name='ext.maps.booking.partner.v3.PaymentInformation.fees', index=4,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fees_and_taxes', full_name='ext.maps.booking.partner.v3.PaymentInformation.fees_and_taxes', index=5,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='deposit', full_name='ext.maps.booking.partner.v3.PaymentInformation.deposit', index=6,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='no_show_fee', full_name='ext.maps.booking.partner.v3.PaymentInformation.no_show_fee', index=7,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='payment_processed_by', full_name='ext.maps.booking.partner.v3.PaymentInformation.payment_processed_by', index=8,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='payment_option_id', full_name='ext.maps.booking.partner.v3.PaymentInformation.payment_option_id', index=9,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='user_payment_option_id', full_name='ext.maps.booking.partner.v3.PaymentInformation.user_payment_option_id', index=10,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fraud_signals', full_name='ext.maps.booking.partner.v3.PaymentInformation.fraud_signals', index=11,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pa_response', full_name='ext.maps.booking.partner.v3.PaymentInformation.pa_response', index=12,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='md_merchant_data', full_name='ext.maps.booking.partner.v3.PaymentInformation.md_merchant_data', index=13,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _PAYMENTINFORMATION_PAYMENTPROCESSEDBY,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='payment_id', full_name='ext.maps.booking.partner.v3.PaymentInformation.payment_id',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=11701,
  serialized_end=12516,
)


_PRICE = _descriptor.Descriptor(
  name='Price',
  full_name='ext.maps.booking.partner.v3.Price',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='price_micros', full_name='ext.maps.booking.partner.v3.Price.price_micros', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='currency_code', full_name='ext.maps.booking.partner.v3.Price.currency_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pricing_option_tag', full_name='ext.maps.booking.partner.v3.Price.pricing_option_tag', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12518,
  serialized_end=12598,
)


_NOSHOWFEE = _descriptor.Descriptor(
  name='NoShowFee',
  full_name='ext.maps.booking.partner.v3.NoShowFee',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='fee', full_name='ext.maps.booking.partner.v3.NoShowFee.fee', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fee_type', full_name='ext.maps.booking.partner.v3.NoShowFee.fee_type', index=1,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12600,
  serialized_end=12718,
)


_DEPOSIT = _descriptor.Descriptor(
  name='Deposit',
  full_name='ext.maps.booking.partner.v3.Deposit',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='deposit', full_name='ext.maps.booking.partner.v3.Deposit.deposit', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='min_advance_cancellation_sec', full_name='ext.maps.booking.partner.v3.Deposit.min_advance_cancellation_sec', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='deposit_type', full_name='ext.maps.booking.partner.v3.Deposit.deposit_type', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12721,
  serialized_end=12883,
)


_TOKENIZATIONCONFIG_TOKENIZATIONPARAMETERENTRY = _descriptor.Descriptor(
  name='TokenizationParameterEntry',
  full_name='ext.maps.booking.partner.v3.TokenizationConfig.TokenizationParameterEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='ext.maps.booking.partner.v3.TokenizationConfig.TokenizationParameterEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='ext.maps.booking.partner.v3.TokenizationConfig.TokenizationParameterEntry.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13383,
  serialized_end=13443,
)

_TOKENIZATIONCONFIG_CARDNETWORKPARAMETERS = _descriptor.Descriptor(
  name='CardNetworkParameters',
  full_name='ext.maps.booking.partner.v3.TokenizationConfig.CardNetworkParameters',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='card_network', full_name='ext.maps.booking.partner.v3.TokenizationConfig.CardNetworkParameters.card_network', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='acquirer_bin', full_name='ext.maps.booking.partner.v3.TokenizationConfig.CardNetworkParameters.acquirer_bin', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='acquirer_merchant_id', full_name='ext.maps.booking.partner.v3.TokenizationConfig.CardNetworkParameters.acquirer_merchant_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13446,
  serialized_end=13611,
)

_TOKENIZATIONCONFIG = _descriptor.Descriptor(
  name='TokenizationConfig',
  full_name='ext.maps.booking.partner.v3.TokenizationConfig',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='tokenization_parameter', full_name='ext.maps.booking.partner.v3.TokenizationConfig.tokenization_parameter', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='billing_information_format', full_name='ext.maps.booking.partner.v3.TokenizationConfig.billing_information_format', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='merchant_of_record_name', full_name='ext.maps.booking.partner.v3.TokenizationConfig.merchant_of_record_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='payment_country_code', full_name='ext.maps.booking.partner.v3.TokenizationConfig.payment_country_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='card_network_parameters', full_name='ext.maps.booking.partner.v3.TokenizationConfig.card_network_parameters', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='allowed_auth_methods', full_name='ext.maps.booking.partner.v3.TokenizationConfig.allowed_auth_methods', index=5,
      number=6, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_TOKENIZATIONCONFIG_TOKENIZATIONPARAMETERENTRY, _TOKENIZATIONCONFIG_CARDNETWORKPARAMETERS, ],
  enum_types=[
    _TOKENIZATIONCONFIG_BILLINGINFORMATIONFORMAT,
    _TOKENIZATIONCONFIG_AUTHMETHOD,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12886,
  serialized_end=13779,
)


_CREDITCARDRESTRICTIONS = _descriptor.Descriptor(
  name='CreditCardRestrictions',
  full_name='ext.maps.booking.partner.v3.CreditCardRestrictions',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='credit_card_type', full_name='ext.maps.booking.partner.v3.CreditCardRestrictions.credit_card_type', index=0,
      number=1, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _CREDITCARDRESTRICTIONS_CREDITCARDTYPE,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13782,
  serialized_end=14023,
)


_SLOT = _descriptor.Descriptor(
  name='Slot',
  full_name='ext.maps.booking.partner.v3.Slot',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='merchant_id', full_name='ext.maps.booking.partner.v3.Slot.merchant_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='service_id', full_name='ext.maps.booking.partner.v3.Slot.service_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='start_sec', full_name='ext.maps.booking.partner.v3.Slot.start_sec', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='duration_sec', full_name='ext.maps.booking.partner.v3.Slot.duration_sec', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='availability_tag', full_name='ext.maps.booking.partner.v3.Slot.availability_tag', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='resources', full_name='ext.maps.booking.partner.v3.Slot.resources', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='confirmation_mode', full_name='ext.maps.booking.partner.v3.Slot.confirmation_mode', index=6,
      number=7, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=14026,
  serialized_end=14275,
)


_USERINFORMATION = _descriptor.Descriptor(
  name='UserInformation',
  full_name='ext.maps.booking.partner.v3.UserInformation',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='user_id', full_name='ext.maps.booking.partner.v3.UserInformation.user_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='given_name', full_name='ext.maps.booking.partner.v3.UserInformation.given_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='family_name', full_name='ext.maps.booking.partner.v3.UserInformation.family_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='address', full_name='ext.maps.booking.partner.v3.UserInformation.address', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='telephone', full_name='ext.maps.booking.partner.v3.UserInformation.telephone', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='email', full_name='ext.maps.booking.partner.v3.UserInformation.email', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='language_code', full_name='ext.maps.booking.partner.v3.UserInformation.language_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=14278,
  serialized_end=14477,
)


_POSTALADDRESS = _descriptor.Descriptor(
  name='PostalAddress',
  full_name='ext.maps.booking.partner.v3.PostalAddress',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='country', full_name='ext.maps.booking.partner.v3.PostalAddress.country', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='locality', full_name='ext.maps.booking.partner.v3.PostalAddress.locality', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='region', full_name='ext.maps.booking.partner.v3.PostalAddress.region', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='postal_code', full_name='ext.maps.booking.partner.v3.PostalAddress.postal_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='street_address', full_name='ext.maps.booking.partner.v3.PostalAddress.street_address', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=14479,
  serialized_end=14590,
)


_OFFERINFO = _descriptor.Descriptor(
  name='OfferInfo',
  full_name='ext.maps.booking.partner.v3.OfferInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='offer_id', full_name='ext.maps.booking.partner.v3.OfferInfo.offer_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=14592,
  serialized_end=14621,
)

_BATCHAVAILABILITYLOOKUPREQUEST.fields_by_name['slot_time'].message_type = _SLOTTIME
_BATCHAVAILABILITYLOOKUPRESPONSE.fields_by_name['slot_time_availability'].message_type = _SLOTTIMEAVAILABILITY
_SLOTTIME.fields_by_name['resource_ids'].message_type = _RESOURCEIDS
_SLOTTIME.fields_by_name['confirmation_mode'].enum_type = _CONFIRMATIONMODE
_SLOTTIMEAVAILABILITY.fields_by_name['slot_time'].message_type = _SLOTTIME
_CHECKAVAILABILITYREQUEST.fields_by_name['slot'].message_type = _SLOT
_CHECKAVAILABILITYRESPONSE.fields_by_name['slot'].message_type = _SLOT
_CHECKAVAILABILITYRESPONSE.fields_by_name['duration_requirement'].enum_type = _CHECKAVAILABILITYRESPONSE_DURATIONREQUIREMENT
_CHECKAVAILABILITYRESPONSE.fields_by_name['availability_update'].message_type = _AVAILABILITYUPDATE
_CHECKAVAILABILITYRESPONSE_DURATIONREQUIREMENT.containing_type = _CHECKAVAILABILITYRESPONSE
_AVAILABILITYUPDATE.fields_by_name['slot_availability'].message_type = _SLOTAVAILABILITY
_SLOTAVAILABILITY.fields_by_name['slot'].message_type = _SLOT
_CHECKORDERFULFILLABILITYREQUEST.fields_by_name['item'].message_type = _LINEITEM
_CHECKORDERFULFILLABILITYRESPONSE.fields_by_name['fulfillability'].message_type = _ORDERFULFILLABILITY
_CHECKORDERFULFILLABILITYRESPONSE.fields_by_name['fees_and_taxes'].message_type = _PRICE
_CHECKORDERFULFILLABILITYRESPONSE.fields_by_name['fees'].message_type = _FEES
_FEES.fields_by_name['per_ticket_fee'].message_type = _SPECIFICPERTICKETFEE
_FEES.fields_by_name['per_order_fee'].message_type = _SPECIFICPERORDERFEE
_SPECIFICPERTICKETFEE.fields_by_name['fee_amount'].message_type = _PRICE
_SPECIFICPERORDERFEE.fields_by_name['fee_amount'].message_type = _PRICE
_GETBOOKINGSTATUSRESPONSE.fields_by_name['booking_status'].enum_type = _BOOKINGSTATUS
_GETBOOKINGSTATUSRESPONSE.fields_by_name['prepayment_status'].enum_type = _PREPAYMENTSTATUS
_CREATEBOOKINGREQUEST.fields_by_name['slot'].message_type = _SLOT
_CREATEBOOKINGREQUEST.fields_by_name['lease_ref'].message_type = _LEASEREFERENCE
_CREATEBOOKINGREQUEST.fields_by_name['user_information'].message_type = _USERINFORMATION
_CREATEBOOKINGREQUEST.fields_by_name['payment_information'].message_type = _PAYMENTINFORMATION
_CREATEBOOKINGREQUEST.fields_by_name['payment_processing_parameters'].message_type = _PAYMENTPROCESSINGPARAMETERS
_CREATEBOOKINGRESPONSE.fields_by_name['booking'].message_type = _BOOKING
_CREATEBOOKINGRESPONSE.fields_by_name['user_payment_option'].message_type = _USERPAYMENTOPTION
_CREATEBOOKINGRESPONSE.fields_by_name['booking_failure'].message_type = _BOOKINGFAILURE
_CREATELEASEREQUEST.fields_by_name['lease'].message_type = _LEASE
_CREATELEASERESPONSE.fields_by_name['lease'].message_type = _LEASE
_CREATELEASERESPONSE.fields_by_name['booking_failure'].message_type = _BOOKINGFAILURE
_LEASE.fields_by_name['slot'].message_type = _SLOT
_CREATEORDERREQUEST.fields_by_name['order'].message_type = _ORDER
_CREATEORDERREQUEST.fields_by_name['payment_processing_parameters'].message_type = _PAYMENTPROCESSINGPARAMETERS
_CREATEORDERRESPONSE.fields_by_name['order'].message_type = _ORDER
_CREATEORDERRESPONSE.fields_by_name['order_failure'].message_type = _ORDERFAILURE
_CREATEORDERRESPONSE.oneofs_by_name['result'].fields.append(
  _CREATEORDERRESPONSE.fields_by_name['order'])
_CREATEORDERRESPONSE.fields_by_name['order'].containing_oneof = _CREATEORDERRESPONSE.oneofs_by_name['result']
_CREATEORDERRESPONSE.oneofs_by_name['result'].fields.append(
  _CREATEORDERRESPONSE.fields_by_name['order_failure'])
_CREATEORDERRESPONSE.fields_by_name['order_failure'].containing_oneof = _CREATEORDERRESPONSE.oneofs_by_name['result']
_LISTBOOKINGSRESPONSE.fields_by_name['bookings'].message_type = _BOOKING
_LISTORDERSREQUEST_ORDERIDS.containing_type = _LISTORDERSREQUEST
_LISTORDERSREQUEST.fields_by_name['order_ids'].message_type = _LISTORDERSREQUEST_ORDERIDS
_LISTORDERSREQUEST.oneofs_by_name['ids'].fields.append(
  _LISTORDERSREQUEST.fields_by_name['user_id'])
_LISTORDERSREQUEST.fields_by_name['user_id'].containing_oneof = _LISTORDERSREQUEST.oneofs_by_name['ids']
_LISTORDERSREQUEST.oneofs_by_name['ids'].fields.append(
  _LISTORDERSREQUEST.fields_by_name['order_ids'])
_LISTORDERSREQUEST.fields_by_name['order_ids'].containing_oneof = _LISTORDERSREQUEST.oneofs_by_name['ids']
_LISTORDERSRESPONSE.fields_by_name['order'].message_type = _ORDER
_UPDATEBOOKINGREQUEST.fields_by_name['booking'].message_type = _BOOKING
_UPDATEBOOKINGRESPONSE.fields_by_name['booking'].message_type = _BOOKING
_UPDATEBOOKINGRESPONSE.fields_by_name['user_payment_option'].message_type = _USERPAYMENTOPTION
_UPDATEBOOKINGRESPONSE.fields_by_name['booking_failure'].message_type = _BOOKINGFAILURE
_BOOKING.fields_by_name['slot'].message_type = _SLOT
_BOOKING.fields_by_name['user_information'].message_type = _USERINFORMATION
_BOOKING.fields_by_name['status'].enum_type = _BOOKINGSTATUS
_BOOKING.fields_by_name['payment_information'].message_type = _PAYMENTINFORMATION
_BOOKING.fields_by_name['virtual_session_info'].message_type = _VIRTUALSESSIONINFO
_BOOKING.fields_by_name['offer_info'].message_type = _OFFERINFO
_BOOKINGFAILURE_PAYMENTFAILUREINFORMATION_THREEDS1PARAMETERS.containing_type = _BOOKINGFAILURE_PAYMENTFAILUREINFORMATION
_BOOKINGFAILURE_PAYMENTFAILUREINFORMATION.fields_by_name['threeds1_parameters'].message_type = _BOOKINGFAILURE_PAYMENTFAILUREINFORMATION_THREEDS1PARAMETERS
_BOOKINGFAILURE_PAYMENTFAILUREINFORMATION.containing_type = _BOOKINGFAILURE
_BOOKINGFAILURE.fields_by_name['cause'].enum_type = _BOOKINGFAILURE_CAUSE
_BOOKINGFAILURE.fields_by_name['rejected_card_type'].enum_type = _CREDITCARDTYPE
_BOOKINGFAILURE.fields_by_name['payment_failure'].message_type = _BOOKINGFAILURE_PAYMENTFAILUREINFORMATION
_BOOKINGFAILURE_CAUSE.containing_type = _BOOKINGFAILURE
_PAYMENTFAILUREINFORMATION_THREEDS1PARAMETERS.containing_type = _PAYMENTFAILUREINFORMATION
_PAYMENTFAILUREINFORMATION.fields_by_name['threeds1_parameters'].message_type = _PAYMENTFAILUREINFORMATION_THREEDS1PARAMETERS
_ORDER.fields_by_name['user_information'].message_type = _USERINFORMATION
_ORDER.fields_by_name['payment_information'].message_type = _PAYMENTINFORMATION
_ORDER.fields_by_name['item'].message_type = _LINEITEM
_LINEITEM_ORDEREDTICKETS.containing_type = _LINEITEM
_LINEITEM.fields_by_name['tickets'].message_type = _LINEITEM_ORDEREDTICKETS
_LINEITEM.fields_by_name['price'].message_type = _PRICE
_LINEITEM.fields_by_name['status'].enum_type = _BOOKINGSTATUS
_LINEITEM.fields_by_name['intake_form_answers'].message_type = _INTAKEFORMANSWERS
_LINEITEM.fields_by_name['warning_reason'].enum_type = _LINEITEM_WARNINGREASON
_LINEITEM_WARNINGREASON.containing_type = _LINEITEM
_INTAKEFORMANSWERS.fields_by_name['answer'].message_type = _INTAKEFORMFIELDANSWER
_ORDERFAILURE_PAYMENTFAILUREINFORMATION_THREEDS1PARAMETERS.containing_type = _ORDERFAILURE_PAYMENTFAILUREINFORMATION
_ORDERFAILURE_PAYMENTFAILUREINFORMATION.fields_by_name['threeds1_parameters'].message_type = _ORDERFAILURE_PAYMENTFAILUREINFORMATION_THREEDS1PARAMETERS
_ORDERFAILURE_PAYMENTFAILUREINFORMATION.containing_type = _ORDERFAILURE
_ORDERFAILURE.fields_by_name['cause'].enum_type = _ORDERFAILURE_CAUSE
_ORDERFAILURE.fields_by_name['fulfillability'].message_type = _ORDERFULFILLABILITY
_ORDERFAILURE.fields_by_name['rejected_card_type'].enum_type = _CREDITCARDTYPE
_ORDERFAILURE.fields_by_name['payment_failure'].message_type = _ORDERFAILURE_PAYMENTFAILUREINFORMATION
_ORDERFAILURE_CAUSE.containing_type = _ORDERFAILURE
_ORDERFULFILLABILITY.fields_by_name['result'].enum_type = _ORDERFULFILLABILITY_ORDERFULFILLABILITYRESULT
_ORDERFULFILLABILITY.fields_by_name['item_fulfillability'].message_type = _LINEITEMFULFILLABILITY
_ORDERFULFILLABILITY_ORDERFULFILLABILITYRESULT.containing_type = _ORDERFULFILLABILITY
_LINEITEMFULFILLABILITY_UPDATEDAVAILABILITY.containing_type = _LINEITEMFULFILLABILITY
_LINEITEMFULFILLABILITY_VIOLATEDTICKETCONSTRAINT.containing_type = _LINEITEMFULFILLABILITY
_LINEITEMFULFILLABILITY_VIOLATEDTICKETCONSTRAINT.oneofs_by_name['value'].fields.append(
  _LINEITEMFULFILLABILITY_VIOLATEDTICKETCONSTRAINT.fields_by_name['min_ticket_count'])
_LINEITEMFULFILLABILITY_VIOLATEDTICKETCONSTRAINT.fields_by_name['min_ticket_count'].containing_oneof = _LINEITEMFULFILLABILITY_VIOLATEDTICKETCONSTRAINT.oneofs_by_name['value']
_LINEITEMFULFILLABILITY_VIOLATEDTICKETCONSTRAINT.oneofs_by_name['value'].fields.append(
  _LINEITEMFULFILLABILITY_VIOLATEDTICKETCONSTRAINT.fields_by_name['max_ticket_count'])
_LINEITEMFULFILLABILITY_VIOLATEDTICKETCONSTRAINT.fields_by_name['max_ticket_count'].containing_oneof = _LINEITEMFULFILLABILITY_VIOLATEDTICKETCONSTRAINT.oneofs_by_name['value']
_LINEITEMFULFILLABILITY.fields_by_name['item'].message_type = _LINEITEM
_LINEITEMFULFILLABILITY.fields_by_name['result'].enum_type = _LINEITEMFULFILLABILITY_ITEMFULFILLABILITYRESULT
_LINEITEMFULFILLABILITY.fields_by_name['availability'].message_type = _LINEITEMFULFILLABILITY_UPDATEDAVAILABILITY
_LINEITEMFULFILLABILITY.fields_by_name['ticket_type'].message_type = _TICKETTYPE
_LINEITEMFULFILLABILITY.fields_by_name['violated_ticket_constraint'].message_type = _LINEITEMFULFILLABILITY_VIOLATEDTICKETCONSTRAINT
_LINEITEMFULFILLABILITY_ITEMFULFILLABILITYRESULT.containing_type = _LINEITEMFULFILLABILITY
_TICKETTYPE.fields_by_name['localized_short_description'].message_type = _TEXT
_TICKETTYPE.fields_by_name['price'].message_type = _PRICE
_TICKETTYPE.fields_by_name['per_ticket_fee'].message_type = _PERTICKETFEE
_TICKETTYPE.fields_by_name['localized_option_description'].message_type = _TEXT
_PERTICKETFEE.fields_by_name['service_charge'].message_type = _PRICE
_PERTICKETFEE.fields_by_name['facility_fee'].message_type = _PRICE
_PERTICKETFEE.fields_by_name['taxes'].message_type = _PRICE
_TEXT.fields_by_name['localized_value'].message_type = _LOCALIZEDSTRING
_SERVICEATTRIBUTE_VALUE.containing_type = _SERVICEATTRIBUTE
_SERVICEATTRIBUTE.fields_by_name['value'].message_type = _SERVICEATTRIBUTE_VALUE
_ACTIONLINK.fields_by_name['platform'].enum_type = _ACTIONPLATFORM
_ACTIONLINK.fields_by_name['action_link_type'].enum_type = _ACTIONLINK_ACTIONLINKTYPE
_ACTIONLINK_ACTIONLINKTYPE.containing_type = _ACTIONLINK
_PAYMENTPROCESSINGPARAMETERS.fields_by_name['processor'].enum_type = _PAYMENTPROCESSINGPARAMETERS_PAYMENTPROCESSOR
_PAYMENTPROCESSINGPARAMETERS.fields_by_name['tokenization_config'].message_type = _TOKENIZATIONCONFIG
_PAYMENTPROCESSINGPARAMETERS_PAYMENTPROCESSOR.containing_type = _PAYMENTPROCESSINGPARAMETERS
_USERPAYMENTOPTION.fields_by_name['type'].enum_type = _PAYMENTOPTIONTYPE
_PAYMENTINFORMATION.fields_by_name['prepayment_status'].enum_type = _PREPAYMENTSTATUS
_PAYMENTINFORMATION.fields_by_name['price'].message_type = _PRICE
_PAYMENTINFORMATION.fields_by_name['tax_amount'].message_type = _PRICE
_PAYMENTINFORMATION.fields_by_name['fees'].message_type = _PRICE
_PAYMENTINFORMATION.fields_by_name['fees_and_taxes'].message_type = _PRICE
_PAYMENTINFORMATION.fields_by_name['deposit'].message_type = _DEPOSIT
_PAYMENTINFORMATION.fields_by_name['no_show_fee'].message_type = _NOSHOWFEE
_PAYMENTINFORMATION.fields_by_name['payment_processed_by'].enum_type = _PAYMENTINFORMATION_PAYMENTPROCESSEDBY
_PAYMENTINFORMATION_PAYMENTPROCESSEDBY.containing_type = _PAYMENTINFORMATION
_PAYMENTINFORMATION.oneofs_by_name['payment_id'].fields.append(
  _PAYMENTINFORMATION.fields_by_name['payment_option_id'])
_PAYMENTINFORMATION.fields_by_name['payment_option_id'].containing_oneof = _PAYMENTINFORMATION.oneofs_by_name['payment_id']
_PAYMENTINFORMATION.oneofs_by_name['payment_id'].fields.append(
  _PAYMENTINFORMATION.fields_by_name['user_payment_option_id'])
_PAYMENTINFORMATION.fields_by_name['user_payment_option_id'].containing_oneof = _PAYMENTINFORMATION.oneofs_by_name['payment_id']
_NOSHOWFEE.fields_by_name['fee'].message_type = _PRICE
_NOSHOWFEE.fields_by_name['fee_type'].enum_type = _PRICETYPE
_DEPOSIT.fields_by_name['deposit'].message_type = _PRICE
_DEPOSIT.fields_by_name['deposit_type'].enum_type = _PRICETYPE
_TOKENIZATIONCONFIG_TOKENIZATIONPARAMETERENTRY.containing_type = _TOKENIZATIONCONFIG
_TOKENIZATIONCONFIG_CARDNETWORKPARAMETERS.fields_by_name['card_network'].enum_type = _CREDITCARDRESTRICTIONS_CREDITCARDTYPE
_TOKENIZATIONCONFIG_CARDNETWORKPARAMETERS.containing_type = _TOKENIZATIONCONFIG
_TOKENIZATIONCONFIG.fields_by_name['tokenization_parameter'].message_type = _TOKENIZATIONCONFIG_TOKENIZATIONPARAMETERENTRY
_TOKENIZATIONCONFIG.fields_by_name['billing_information_format'].enum_type = _TOKENIZATIONCONFIG_BILLINGINFORMATIONFORMAT
_TOKENIZATIONCONFIG.fields_by_name['card_network_parameters'].message_type = _TOKENIZATIONCONFIG_CARDNETWORKPARAMETERS
_TOKENIZATIONCONFIG.fields_by_name['allowed_auth_methods'].enum_type = _TOKENIZATIONCONFIG_AUTHMETHOD
_TOKENIZATIONCONFIG_BILLINGINFORMATIONFORMAT.containing_type = _TOKENIZATIONCONFIG
_TOKENIZATIONCONFIG_AUTHMETHOD.containing_type = _TOKENIZATIONCONFIG
_CREDITCARDRESTRICTIONS.fields_by_name['credit_card_type'].enum_type = _CREDITCARDRESTRICTIONS_CREDITCARDTYPE
_CREDITCARDRESTRICTIONS_CREDITCARDTYPE.containing_type = _CREDITCARDRESTRICTIONS
_SLOT.fields_by_name['resources'].message_type = _RESOURCEIDS
_SLOT.fields_by_name['confirmation_mode'].enum_type = _CONFIRMATIONMODE
_USERINFORMATION.fields_by_name['address'].message_type = _POSTALADDRESS
DESCRIPTOR.message_types_by_name['BatchAvailabilityLookupRequest'] = _BATCHAVAILABILITYLOOKUPREQUEST
DESCRIPTOR.message_types_by_name['BatchAvailabilityLookupResponse'] = _BATCHAVAILABILITYLOOKUPRESPONSE
DESCRIPTOR.message_types_by_name['SlotTime'] = _SLOTTIME
DESCRIPTOR.message_types_by_name['SlotTimeAvailability'] = _SLOTTIMEAVAILABILITY
DESCRIPTOR.message_types_by_name['CheckAvailabilityRequest'] = _CHECKAVAILABILITYREQUEST
DESCRIPTOR.message_types_by_name['CheckAvailabilityResponse'] = _CHECKAVAILABILITYRESPONSE
DESCRIPTOR.message_types_by_name['AvailabilityUpdate'] = _AVAILABILITYUPDATE
DESCRIPTOR.message_types_by_name['SlotAvailability'] = _SLOTAVAILABILITY
DESCRIPTOR.message_types_by_name['CheckOrderFulfillabilityRequest'] = _CHECKORDERFULFILLABILITYREQUEST
DESCRIPTOR.message_types_by_name['CheckOrderFulfillabilityResponse'] = _CHECKORDERFULFILLABILITYRESPONSE
DESCRIPTOR.message_types_by_name['Fees'] = _FEES
DESCRIPTOR.message_types_by_name['SpecificPerTicketFee'] = _SPECIFICPERTICKETFEE
DESCRIPTOR.message_types_by_name['SpecificPerOrderFee'] = _SPECIFICPERORDERFEE
DESCRIPTOR.message_types_by_name['GetBookingStatusRequest'] = _GETBOOKINGSTATUSREQUEST
DESCRIPTOR.message_types_by_name['GetBookingStatusResponse'] = _GETBOOKINGSTATUSRESPONSE
DESCRIPTOR.message_types_by_name['CreateBookingRequest'] = _CREATEBOOKINGREQUEST
DESCRIPTOR.message_types_by_name['CreateBookingResponse'] = _CREATEBOOKINGRESPONSE
DESCRIPTOR.message_types_by_name['CreateLeaseRequest'] = _CREATELEASEREQUEST
DESCRIPTOR.message_types_by_name['CreateLeaseResponse'] = _CREATELEASERESPONSE
DESCRIPTOR.message_types_by_name['Lease'] = _LEASE
DESCRIPTOR.message_types_by_name['LeaseReference'] = _LEASEREFERENCE
DESCRIPTOR.message_types_by_name['CreateOrderRequest'] = _CREATEORDERREQUEST
DESCRIPTOR.message_types_by_name['CreateOrderResponse'] = _CREATEORDERRESPONSE
DESCRIPTOR.message_types_by_name['ListBookingsRequest'] = _LISTBOOKINGSREQUEST
DESCRIPTOR.message_types_by_name['ListBookingsResponse'] = _LISTBOOKINGSRESPONSE
DESCRIPTOR.message_types_by_name['ListOrdersRequest'] = _LISTORDERSREQUEST
DESCRIPTOR.message_types_by_name['ListOrdersResponse'] = _LISTORDERSRESPONSE
DESCRIPTOR.message_types_by_name['UpdateBookingRequest'] = _UPDATEBOOKINGREQUEST
DESCRIPTOR.message_types_by_name['UpdateBookingResponse'] = _UPDATEBOOKINGRESPONSE
DESCRIPTOR.message_types_by_name['Booking'] = _BOOKING
DESCRIPTOR.message_types_by_name['VirtualSessionInfo'] = _VIRTUALSESSIONINFO
DESCRIPTOR.message_types_by_name['BookingFailure'] = _BOOKINGFAILURE
DESCRIPTOR.message_types_by_name['PaymentFailureInformation'] = _PAYMENTFAILUREINFORMATION
DESCRIPTOR.message_types_by_name['ThreeDS1Parameters'] = _THREEDS1PARAMETERS
DESCRIPTOR.message_types_by_name['Order'] = _ORDER
DESCRIPTOR.message_types_by_name['LineItem'] = _LINEITEM
DESCRIPTOR.message_types_by_name['IntakeFormAnswers'] = _INTAKEFORMANSWERS
DESCRIPTOR.message_types_by_name['IntakeFormFieldAnswer'] = _INTAKEFORMFIELDANSWER
DESCRIPTOR.message_types_by_name['OrderFailure'] = _ORDERFAILURE
DESCRIPTOR.message_types_by_name['OrderFulfillability'] = _ORDERFULFILLABILITY
DESCRIPTOR.message_types_by_name['LineItemFulfillability'] = _LINEITEMFULFILLABILITY
DESCRIPTOR.message_types_by_name['TicketType'] = _TICKETTYPE
DESCRIPTOR.message_types_by_name['PerTicketFee'] = _PERTICKETFEE
DESCRIPTOR.message_types_by_name['Text'] = _TEXT
DESCRIPTOR.message_types_by_name['LocalizedString'] = _LOCALIZEDSTRING
DESCRIPTOR.message_types_by_name['MerchantMatchingHints'] = _MERCHANTMATCHINGHINTS
DESCRIPTOR.message_types_by_name['ServiceAttribute'] = _SERVICEATTRIBUTE
DESCRIPTOR.message_types_by_name['ActionLink'] = _ACTIONLINK
DESCRIPTOR.message_types_by_name['ResourceIds'] = _RESOURCEIDS
DESCRIPTOR.message_types_by_name['PaymentProcessingParameters'] = _PAYMENTPROCESSINGPARAMETERS
DESCRIPTOR.message_types_by_name['UserPaymentOption'] = _USERPAYMENTOPTION
DESCRIPTOR.message_types_by_name['PaymentInformation'] = _PAYMENTINFORMATION
DESCRIPTOR.message_types_by_name['Price'] = _PRICE
DESCRIPTOR.message_types_by_name['NoShowFee'] = _NOSHOWFEE
DESCRIPTOR.message_types_by_name['Deposit'] = _DEPOSIT
DESCRIPTOR.message_types_by_name['TokenizationConfig'] = _TOKENIZATIONCONFIG
DESCRIPTOR.message_types_by_name['CreditCardRestrictions'] = _CREDITCARDRESTRICTIONS
DESCRIPTOR.message_types_by_name['Slot'] = _SLOT
DESCRIPTOR.message_types_by_name['UserInformation'] = _USERINFORMATION
DESCRIPTOR.message_types_by_name['PostalAddress'] = _POSTALADDRESS
DESCRIPTOR.message_types_by_name['OfferInfo'] = _OFFERINFO
DESCRIPTOR.enum_types_by_name['BookingStatus'] = _BOOKINGSTATUS
DESCRIPTOR.enum_types_by_name['CreditCardType'] = _CREDITCARDTYPE
DESCRIPTOR.enum_types_by_name['ActionPlatform'] = _ACTIONPLATFORM
DESCRIPTOR.enum_types_by_name['PaymentOptionType'] = _PAYMENTOPTIONTYPE
DESCRIPTOR.enum_types_by_name['PrepaymentStatus'] = _PREPAYMENTSTATUS
DESCRIPTOR.enum_types_by_name['PriceType'] = _PRICETYPE
DESCRIPTOR.enum_types_by_name['ConfirmationMode'] = _CONFIRMATIONMODE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

BatchAvailabilityLookupRequest = _reflection.GeneratedProtocolMessageType('BatchAvailabilityLookupRequest', (_message.Message,), {
  'DESCRIPTOR' : _BATCHAVAILABILITYLOOKUPREQUEST,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.BatchAvailabilityLookupRequest)
  })
_sym_db.RegisterMessage(BatchAvailabilityLookupRequest)

BatchAvailabilityLookupResponse = _reflection.GeneratedProtocolMessageType('BatchAvailabilityLookupResponse', (_message.Message,), {
  'DESCRIPTOR' : _BATCHAVAILABILITYLOOKUPRESPONSE,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.BatchAvailabilityLookupResponse)
  })
_sym_db.RegisterMessage(BatchAvailabilityLookupResponse)

SlotTime = _reflection.GeneratedProtocolMessageType('SlotTime', (_message.Message,), {
  'DESCRIPTOR' : _SLOTTIME,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.SlotTime)
  })
_sym_db.RegisterMessage(SlotTime)

SlotTimeAvailability = _reflection.GeneratedProtocolMessageType('SlotTimeAvailability', (_message.Message,), {
  'DESCRIPTOR' : _SLOTTIMEAVAILABILITY,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.SlotTimeAvailability)
  })
_sym_db.RegisterMessage(SlotTimeAvailability)

CheckAvailabilityRequest = _reflection.GeneratedProtocolMessageType('CheckAvailabilityRequest', (_message.Message,), {
  'DESCRIPTOR' : _CHECKAVAILABILITYREQUEST,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.CheckAvailabilityRequest)
  })
_sym_db.RegisterMessage(CheckAvailabilityRequest)

CheckAvailabilityResponse = _reflection.GeneratedProtocolMessageType('CheckAvailabilityResponse', (_message.Message,), {
  'DESCRIPTOR' : _CHECKAVAILABILITYRESPONSE,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.CheckAvailabilityResponse)
  })
_sym_db.RegisterMessage(CheckAvailabilityResponse)

AvailabilityUpdate = _reflection.GeneratedProtocolMessageType('AvailabilityUpdate', (_message.Message,), {
  'DESCRIPTOR' : _AVAILABILITYUPDATE,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.AvailabilityUpdate)
  })
_sym_db.RegisterMessage(AvailabilityUpdate)

SlotAvailability = _reflection.GeneratedProtocolMessageType('SlotAvailability', (_message.Message,), {
  'DESCRIPTOR' : _SLOTAVAILABILITY,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.SlotAvailability)
  })
_sym_db.RegisterMessage(SlotAvailability)

CheckOrderFulfillabilityRequest = _reflection.GeneratedProtocolMessageType('CheckOrderFulfillabilityRequest', (_message.Message,), {
  'DESCRIPTOR' : _CHECKORDERFULFILLABILITYREQUEST,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.CheckOrderFulfillabilityRequest)
  })
_sym_db.RegisterMessage(CheckOrderFulfillabilityRequest)

CheckOrderFulfillabilityResponse = _reflection.GeneratedProtocolMessageType('CheckOrderFulfillabilityResponse', (_message.Message,), {
  'DESCRIPTOR' : _CHECKORDERFULFILLABILITYRESPONSE,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.CheckOrderFulfillabilityResponse)
  })
_sym_db.RegisterMessage(CheckOrderFulfillabilityResponse)

Fees = _reflection.GeneratedProtocolMessageType('Fees', (_message.Message,), {
  'DESCRIPTOR' : _FEES,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.Fees)
  })
_sym_db.RegisterMessage(Fees)

SpecificPerTicketFee = _reflection.GeneratedProtocolMessageType('SpecificPerTicketFee', (_message.Message,), {
  'DESCRIPTOR' : _SPECIFICPERTICKETFEE,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.SpecificPerTicketFee)
  })
_sym_db.RegisterMessage(SpecificPerTicketFee)

SpecificPerOrderFee = _reflection.GeneratedProtocolMessageType('SpecificPerOrderFee', (_message.Message,), {
  'DESCRIPTOR' : _SPECIFICPERORDERFEE,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.SpecificPerOrderFee)
  })
_sym_db.RegisterMessage(SpecificPerOrderFee)

GetBookingStatusRequest = _reflection.GeneratedProtocolMessageType('GetBookingStatusRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETBOOKINGSTATUSREQUEST,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.GetBookingStatusRequest)
  })
_sym_db.RegisterMessage(GetBookingStatusRequest)

GetBookingStatusResponse = _reflection.GeneratedProtocolMessageType('GetBookingStatusResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETBOOKINGSTATUSRESPONSE,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.GetBookingStatusResponse)
  })
_sym_db.RegisterMessage(GetBookingStatusResponse)

CreateBookingRequest = _reflection.GeneratedProtocolMessageType('CreateBookingRequest', (_message.Message,), {
  'DESCRIPTOR' : _CREATEBOOKINGREQUEST,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.CreateBookingRequest)
  })
_sym_db.RegisterMessage(CreateBookingRequest)

CreateBookingResponse = _reflection.GeneratedProtocolMessageType('CreateBookingResponse', (_message.Message,), {
  'DESCRIPTOR' : _CREATEBOOKINGRESPONSE,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.CreateBookingResponse)
  })
_sym_db.RegisterMessage(CreateBookingResponse)

CreateLeaseRequest = _reflection.GeneratedProtocolMessageType('CreateLeaseRequest', (_message.Message,), {
  'DESCRIPTOR' : _CREATELEASEREQUEST,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.CreateLeaseRequest)
  })
_sym_db.RegisterMessage(CreateLeaseRequest)

CreateLeaseResponse = _reflection.GeneratedProtocolMessageType('CreateLeaseResponse', (_message.Message,), {
  'DESCRIPTOR' : _CREATELEASERESPONSE,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.CreateLeaseResponse)
  })
_sym_db.RegisterMessage(CreateLeaseResponse)

Lease = _reflection.GeneratedProtocolMessageType('Lease', (_message.Message,), {
  'DESCRIPTOR' : _LEASE,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.Lease)
  })
_sym_db.RegisterMessage(Lease)

LeaseReference = _reflection.GeneratedProtocolMessageType('LeaseReference', (_message.Message,), {
  'DESCRIPTOR' : _LEASEREFERENCE,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.LeaseReference)
  })
_sym_db.RegisterMessage(LeaseReference)

CreateOrderRequest = _reflection.GeneratedProtocolMessageType('CreateOrderRequest', (_message.Message,), {
  'DESCRIPTOR' : _CREATEORDERREQUEST,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.CreateOrderRequest)
  })
_sym_db.RegisterMessage(CreateOrderRequest)

CreateOrderResponse = _reflection.GeneratedProtocolMessageType('CreateOrderResponse', (_message.Message,), {
  'DESCRIPTOR' : _CREATEORDERRESPONSE,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.CreateOrderResponse)
  })
_sym_db.RegisterMessage(CreateOrderResponse)

ListBookingsRequest = _reflection.GeneratedProtocolMessageType('ListBookingsRequest', (_message.Message,), {
  'DESCRIPTOR' : _LISTBOOKINGSREQUEST,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.ListBookingsRequest)
  })
_sym_db.RegisterMessage(ListBookingsRequest)

ListBookingsResponse = _reflection.GeneratedProtocolMessageType('ListBookingsResponse', (_message.Message,), {
  'DESCRIPTOR' : _LISTBOOKINGSRESPONSE,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.ListBookingsResponse)
  })
_sym_db.RegisterMessage(ListBookingsResponse)

ListOrdersRequest = _reflection.GeneratedProtocolMessageType('ListOrdersRequest', (_message.Message,), {

  'OrderIds' : _reflection.GeneratedProtocolMessageType('OrderIds', (_message.Message,), {
    'DESCRIPTOR' : _LISTORDERSREQUEST_ORDERIDS,
    '__module__' : 'ext_maps_booking_partner_v3_pb2'
    # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.ListOrdersRequest.OrderIds)
    })
  ,
  'DESCRIPTOR' : _LISTORDERSREQUEST,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.ListOrdersRequest)
  })
_sym_db.RegisterMessage(ListOrdersRequest)
_sym_db.RegisterMessage(ListOrdersRequest.OrderIds)

ListOrdersResponse = _reflection.GeneratedProtocolMessageType('ListOrdersResponse', (_message.Message,), {
  'DESCRIPTOR' : _LISTORDERSRESPONSE,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.ListOrdersResponse)
  })
_sym_db.RegisterMessage(ListOrdersResponse)

UpdateBookingRequest = _reflection.GeneratedProtocolMessageType('UpdateBookingRequest', (_message.Message,), {
  'DESCRIPTOR' : _UPDATEBOOKINGREQUEST,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.UpdateBookingRequest)
  })
_sym_db.RegisterMessage(UpdateBookingRequest)

UpdateBookingResponse = _reflection.GeneratedProtocolMessageType('UpdateBookingResponse', (_message.Message,), {
  'DESCRIPTOR' : _UPDATEBOOKINGRESPONSE,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.UpdateBookingResponse)
  })
_sym_db.RegisterMessage(UpdateBookingResponse)

Booking = _reflection.GeneratedProtocolMessageType('Booking', (_message.Message,), {
  'DESCRIPTOR' : _BOOKING,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.Booking)
  })
_sym_db.RegisterMessage(Booking)

VirtualSessionInfo = _reflection.GeneratedProtocolMessageType('VirtualSessionInfo', (_message.Message,), {
  'DESCRIPTOR' : _VIRTUALSESSIONINFO,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.VirtualSessionInfo)
  })
_sym_db.RegisterMessage(VirtualSessionInfo)

BookingFailure = _reflection.GeneratedProtocolMessageType('BookingFailure', (_message.Message,), {

  'PaymentFailureInformation' : _reflection.GeneratedProtocolMessageType('PaymentFailureInformation', (_message.Message,), {

    'ThreeDS1Parameters' : _reflection.GeneratedProtocolMessageType('ThreeDS1Parameters', (_message.Message,), {
      'DESCRIPTOR' : _BOOKINGFAILURE_PAYMENTFAILUREINFORMATION_THREEDS1PARAMETERS,
      '__module__' : 'ext_maps_booking_partner_v3_pb2'
      # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.BookingFailure.PaymentFailureInformation.ThreeDS1Parameters)
      })
    ,
    'DESCRIPTOR' : _BOOKINGFAILURE_PAYMENTFAILUREINFORMATION,
    '__module__' : 'ext_maps_booking_partner_v3_pb2'
    # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.BookingFailure.PaymentFailureInformation)
    })
  ,
  'DESCRIPTOR' : _BOOKINGFAILURE,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.BookingFailure)
  })
_sym_db.RegisterMessage(BookingFailure)
_sym_db.RegisterMessage(BookingFailure.PaymentFailureInformation)
_sym_db.RegisterMessage(BookingFailure.PaymentFailureInformation.ThreeDS1Parameters)

PaymentFailureInformation = _reflection.GeneratedProtocolMessageType('PaymentFailureInformation', (_message.Message,), {

  'ThreeDS1Parameters' : _reflection.GeneratedProtocolMessageType('ThreeDS1Parameters', (_message.Message,), {
    'DESCRIPTOR' : _PAYMENTFAILUREINFORMATION_THREEDS1PARAMETERS,
    '__module__' : 'ext_maps_booking_partner_v3_pb2'
    # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.PaymentFailureInformation.ThreeDS1Parameters)
    })
  ,
  'DESCRIPTOR' : _PAYMENTFAILUREINFORMATION,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.PaymentFailureInformation)
  })
_sym_db.RegisterMessage(PaymentFailureInformation)
_sym_db.RegisterMessage(PaymentFailureInformation.ThreeDS1Parameters)

ThreeDS1Parameters = _reflection.GeneratedProtocolMessageType('ThreeDS1Parameters', (_message.Message,), {
  'DESCRIPTOR' : _THREEDS1PARAMETERS,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.ThreeDS1Parameters)
  })
_sym_db.RegisterMessage(ThreeDS1Parameters)

Order = _reflection.GeneratedProtocolMessageType('Order', (_message.Message,), {
  'DESCRIPTOR' : _ORDER,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.Order)
  })
_sym_db.RegisterMessage(Order)

LineItem = _reflection.GeneratedProtocolMessageType('LineItem', (_message.Message,), {

  'OrderedTickets' : _reflection.GeneratedProtocolMessageType('OrderedTickets', (_message.Message,), {
    'DESCRIPTOR' : _LINEITEM_ORDEREDTICKETS,
    '__module__' : 'ext_maps_booking_partner_v3_pb2'
    # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.LineItem.OrderedTickets)
    })
  ,
  'DESCRIPTOR' : _LINEITEM,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.LineItem)
  })
_sym_db.RegisterMessage(LineItem)
_sym_db.RegisterMessage(LineItem.OrderedTickets)

IntakeFormAnswers = _reflection.GeneratedProtocolMessageType('IntakeFormAnswers', (_message.Message,), {
  'DESCRIPTOR' : _INTAKEFORMANSWERS,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.IntakeFormAnswers)
  })
_sym_db.RegisterMessage(IntakeFormAnswers)

IntakeFormFieldAnswer = _reflection.GeneratedProtocolMessageType('IntakeFormFieldAnswer', (_message.Message,), {
  'DESCRIPTOR' : _INTAKEFORMFIELDANSWER,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.IntakeFormFieldAnswer)
  })
_sym_db.RegisterMessage(IntakeFormFieldAnswer)

OrderFailure = _reflection.GeneratedProtocolMessageType('OrderFailure', (_message.Message,), {

  'PaymentFailureInformation' : _reflection.GeneratedProtocolMessageType('PaymentFailureInformation', (_message.Message,), {

    'ThreeDS1Parameters' : _reflection.GeneratedProtocolMessageType('ThreeDS1Parameters', (_message.Message,), {
      'DESCRIPTOR' : _ORDERFAILURE_PAYMENTFAILUREINFORMATION_THREEDS1PARAMETERS,
      '__module__' : 'ext_maps_booking_partner_v3_pb2'
      # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.OrderFailure.PaymentFailureInformation.ThreeDS1Parameters)
      })
    ,
    'DESCRIPTOR' : _ORDERFAILURE_PAYMENTFAILUREINFORMATION,
    '__module__' : 'ext_maps_booking_partner_v3_pb2'
    # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.OrderFailure.PaymentFailureInformation)
    })
  ,
  'DESCRIPTOR' : _ORDERFAILURE,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.OrderFailure)
  })
_sym_db.RegisterMessage(OrderFailure)
_sym_db.RegisterMessage(OrderFailure.PaymentFailureInformation)
_sym_db.RegisterMessage(OrderFailure.PaymentFailureInformation.ThreeDS1Parameters)

OrderFulfillability = _reflection.GeneratedProtocolMessageType('OrderFulfillability', (_message.Message,), {
  'DESCRIPTOR' : _ORDERFULFILLABILITY,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.OrderFulfillability)
  })
_sym_db.RegisterMessage(OrderFulfillability)

LineItemFulfillability = _reflection.GeneratedProtocolMessageType('LineItemFulfillability', (_message.Message,), {

  'UpdatedAvailability' : _reflection.GeneratedProtocolMessageType('UpdatedAvailability', (_message.Message,), {
    'DESCRIPTOR' : _LINEITEMFULFILLABILITY_UPDATEDAVAILABILITY,
    '__module__' : 'ext_maps_booking_partner_v3_pb2'
    # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.LineItemFulfillability.UpdatedAvailability)
    })
  ,

  'ViolatedTicketConstraint' : _reflection.GeneratedProtocolMessageType('ViolatedTicketConstraint', (_message.Message,), {
    'DESCRIPTOR' : _LINEITEMFULFILLABILITY_VIOLATEDTICKETCONSTRAINT,
    '__module__' : 'ext_maps_booking_partner_v3_pb2'
    # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.LineItemFulfillability.ViolatedTicketConstraint)
    })
  ,
  'DESCRIPTOR' : _LINEITEMFULFILLABILITY,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.LineItemFulfillability)
  })
_sym_db.RegisterMessage(LineItemFulfillability)
_sym_db.RegisterMessage(LineItemFulfillability.UpdatedAvailability)
_sym_db.RegisterMessage(LineItemFulfillability.ViolatedTicketConstraint)

TicketType = _reflection.GeneratedProtocolMessageType('TicketType', (_message.Message,), {
  'DESCRIPTOR' : _TICKETTYPE,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.TicketType)
  })
_sym_db.RegisterMessage(TicketType)

PerTicketFee = _reflection.GeneratedProtocolMessageType('PerTicketFee', (_message.Message,), {
  'DESCRIPTOR' : _PERTICKETFEE,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.PerTicketFee)
  })
_sym_db.RegisterMessage(PerTicketFee)

Text = _reflection.GeneratedProtocolMessageType('Text', (_message.Message,), {
  'DESCRIPTOR' : _TEXT,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.Text)
  })
_sym_db.RegisterMessage(Text)

LocalizedString = _reflection.GeneratedProtocolMessageType('LocalizedString', (_message.Message,), {
  'DESCRIPTOR' : _LOCALIZEDSTRING,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.LocalizedString)
  })
_sym_db.RegisterMessage(LocalizedString)

MerchantMatchingHints = _reflection.GeneratedProtocolMessageType('MerchantMatchingHints', (_message.Message,), {
  'DESCRIPTOR' : _MERCHANTMATCHINGHINTS,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.MerchantMatchingHints)
  })
_sym_db.RegisterMessage(MerchantMatchingHints)

ServiceAttribute = _reflection.GeneratedProtocolMessageType('ServiceAttribute', (_message.Message,), {

  'Value' : _reflection.GeneratedProtocolMessageType('Value', (_message.Message,), {
    'DESCRIPTOR' : _SERVICEATTRIBUTE_VALUE,
    '__module__' : 'ext_maps_booking_partner_v3_pb2'
    # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.ServiceAttribute.Value)
    })
  ,
  'DESCRIPTOR' : _SERVICEATTRIBUTE,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.ServiceAttribute)
  })
_sym_db.RegisterMessage(ServiceAttribute)
_sym_db.RegisterMessage(ServiceAttribute.Value)

ActionLink = _reflection.GeneratedProtocolMessageType('ActionLink', (_message.Message,), {
  'DESCRIPTOR' : _ACTIONLINK,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.ActionLink)
  })
_sym_db.RegisterMessage(ActionLink)

ResourceIds = _reflection.GeneratedProtocolMessageType('ResourceIds', (_message.Message,), {
  'DESCRIPTOR' : _RESOURCEIDS,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.ResourceIds)
  })
_sym_db.RegisterMessage(ResourceIds)

PaymentProcessingParameters = _reflection.GeneratedProtocolMessageType('PaymentProcessingParameters', (_message.Message,), {
  'DESCRIPTOR' : _PAYMENTPROCESSINGPARAMETERS,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.PaymentProcessingParameters)
  })
_sym_db.RegisterMessage(PaymentProcessingParameters)

UserPaymentOption = _reflection.GeneratedProtocolMessageType('UserPaymentOption', (_message.Message,), {
  'DESCRIPTOR' : _USERPAYMENTOPTION,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.UserPaymentOption)
  })
_sym_db.RegisterMessage(UserPaymentOption)

PaymentInformation = _reflection.GeneratedProtocolMessageType('PaymentInformation', (_message.Message,), {
  'DESCRIPTOR' : _PAYMENTINFORMATION,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.PaymentInformation)
  })
_sym_db.RegisterMessage(PaymentInformation)

Price = _reflection.GeneratedProtocolMessageType('Price', (_message.Message,), {
  'DESCRIPTOR' : _PRICE,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.Price)
  })
_sym_db.RegisterMessage(Price)

NoShowFee = _reflection.GeneratedProtocolMessageType('NoShowFee', (_message.Message,), {
  'DESCRIPTOR' : _NOSHOWFEE,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.NoShowFee)
  })
_sym_db.RegisterMessage(NoShowFee)

Deposit = _reflection.GeneratedProtocolMessageType('Deposit', (_message.Message,), {
  'DESCRIPTOR' : _DEPOSIT,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.Deposit)
  })
_sym_db.RegisterMessage(Deposit)

TokenizationConfig = _reflection.GeneratedProtocolMessageType('TokenizationConfig', (_message.Message,), {

  'TokenizationParameterEntry' : _reflection.GeneratedProtocolMessageType('TokenizationParameterEntry', (_message.Message,), {
    'DESCRIPTOR' : _TOKENIZATIONCONFIG_TOKENIZATIONPARAMETERENTRY,
    '__module__' : 'ext_maps_booking_partner_v3_pb2'
    # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.TokenizationConfig.TokenizationParameterEntry)
    })
  ,

  'CardNetworkParameters' : _reflection.GeneratedProtocolMessageType('CardNetworkParameters', (_message.Message,), {
    'DESCRIPTOR' : _TOKENIZATIONCONFIG_CARDNETWORKPARAMETERS,
    '__module__' : 'ext_maps_booking_partner_v3_pb2'
    # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.TokenizationConfig.CardNetworkParameters)
    })
  ,
  'DESCRIPTOR' : _TOKENIZATIONCONFIG,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.TokenizationConfig)
  })
_sym_db.RegisterMessage(TokenizationConfig)
_sym_db.RegisterMessage(TokenizationConfig.TokenizationParameterEntry)
_sym_db.RegisterMessage(TokenizationConfig.CardNetworkParameters)

CreditCardRestrictions = _reflection.GeneratedProtocolMessageType('CreditCardRestrictions', (_message.Message,), {
  'DESCRIPTOR' : _CREDITCARDRESTRICTIONS,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.CreditCardRestrictions)
  })
_sym_db.RegisterMessage(CreditCardRestrictions)

Slot = _reflection.GeneratedProtocolMessageType('Slot', (_message.Message,), {
  'DESCRIPTOR' : _SLOT,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.Slot)
  })
_sym_db.RegisterMessage(Slot)

UserInformation = _reflection.GeneratedProtocolMessageType('UserInformation', (_message.Message,), {
  'DESCRIPTOR' : _USERINFORMATION,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.UserInformation)
  })
_sym_db.RegisterMessage(UserInformation)

PostalAddress = _reflection.GeneratedProtocolMessageType('PostalAddress', (_message.Message,), {
  'DESCRIPTOR' : _POSTALADDRESS,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.PostalAddress)
  })
_sym_db.RegisterMessage(PostalAddress)

OfferInfo = _reflection.GeneratedProtocolMessageType('OfferInfo', (_message.Message,), {
  'DESCRIPTOR' : _OFFERINFO,
  '__module__' : 'ext_maps_booking_partner_v3_pb2'
  # @@protoc_insertion_point(class_scope:ext.maps.booking.partner.v3.OfferInfo)
  })
_sym_db.RegisterMessage(OfferInfo)


DESCRIPTOR._options = None
_CHECKAVAILABILITYRESPONSE.fields_by_name['last_online_cancellable_sec']._options = None
_CHECKAVAILABILITYRESPONSE.fields_by_name['duration_requirement']._options = None
_CREATEBOOKINGREQUEST.fields_by_name['deal_id']._options = None
_BOOKINGFAILURE_CAUSE.values_by_name["DEAL_UNAVAILABLE"]._options = None
_TICKETTYPE.fields_by_name['short_description']._options = None
_TICKETTYPE.fields_by_name['option_description']._options = None
_PAYMENTPROCESSINGPARAMETERS.fields_by_name['processor']._options = None
_PAYMENTPROCESSINGPARAMETERS.fields_by_name['payment_method_token']._options = None
_PAYMENTPROCESSINGPARAMETERS.fields_by_name['version']._options = None
_PAYMENTPROCESSINGPARAMETERS.fields_by_name['payment_processor']._options = None
_TOKENIZATIONCONFIG_TOKENIZATIONPARAMETERENTRY._options = None

_BOOKINGSERVICE = _descriptor.ServiceDescriptor(
  name='BookingService',
  full_name='ext.maps.booking.partner.v3.BookingService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=15603,
  serialized_end=17235,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetPing',
    full_name='ext.maps.booking.partner.v3.BookingService.GetPing',
    index=0,
    containing_service=None,
    input_type=google_dot_protobuf_dot_empty__pb2._EMPTY,
    output_type=google_dot_protobuf_dot_empty__pb2._EMPTY,
    serialized_options=b'\202\323\344\223\002\n\022\010/v3/Ping',
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetHealthCheck',
    full_name='ext.maps.booking.partner.v3.BookingService.GetHealthCheck',
    index=1,
    containing_service=None,
    input_type=google_dot_protobuf_dot_empty__pb2._EMPTY,
    output_type=google_dot_protobuf_dot_empty__pb2._EMPTY,
    serialized_options=b'\202\323\344\223\002\021\022\017/v3/HealthCheck',
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='PerformBatchAvailabilityLookup',
    full_name='ext.maps.booking.partner.v3.BookingService.PerformBatchAvailabilityLookup',
    index=2,
    containing_service=None,
    input_type=_BATCHAVAILABILITYLOOKUPREQUEST,
    output_type=_BATCHAVAILABILITYLOOKUPRESPONSE,
    serialized_options=b'\202\323\344\223\002 \"\033/v3/BatchAvailabilityLookup:\001*',
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='CheckAvailability',
    full_name='ext.maps.booking.partner.v3.BookingService.CheckAvailability',
    index=3,
    containing_service=None,
    input_type=_CHECKAVAILABILITYREQUEST,
    output_type=_CHECKAVAILABILITYRESPONSE,
    serialized_options=b'\202\323\344\223\002\035\"\025/v3/CheckAvailability:\004slot',
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='CreateBooking',
    full_name='ext.maps.booking.partner.v3.BookingService.CreateBooking',
    index=4,
    containing_service=None,
    input_type=_CREATEBOOKINGREQUEST,
    output_type=_CREATEBOOKINGRESPONSE,
    serialized_options=b'\202\323\344\223\002\026\"\021/v3/CreateBooking:\001*',
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='UpdateBooking',
    full_name='ext.maps.booking.partner.v3.BookingService.UpdateBooking',
    index=5,
    containing_service=None,
    input_type=_UPDATEBOOKINGREQUEST,
    output_type=_UPDATEBOOKINGRESPONSE,
    serialized_options=b'\202\323\344\223\002\026\"\021/v3/UpdateBooking:\001*',
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetBookingStatus',
    full_name='ext.maps.booking.partner.v3.BookingService.GetBookingStatus',
    index=6,
    containing_service=None,
    input_type=_GETBOOKINGSTATUSREQUEST,
    output_type=_GETBOOKINGSTATUSRESPONSE,
    serialized_options=b'\202\323\344\223\002\031\"\024/v3/GetBookingStatus:\001*',
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ListBookings',
    full_name='ext.maps.booking.partner.v3.BookingService.ListBookings',
    index=7,
    containing_service=None,
    input_type=_LISTBOOKINGSREQUEST,
    output_type=_LISTBOOKINGSRESPONSE,
    serialized_options=b'\202\323\344\223\002\025\"\020/v3/ListBookings:\001*',
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='CheckOrderFulfillability',
    full_name='ext.maps.booking.partner.v3.BookingService.CheckOrderFulfillability',
    index=8,
    containing_service=None,
    input_type=_CHECKORDERFULFILLABILITYREQUEST,
    output_type=_CHECKORDERFULFILLABILITYRESPONSE,
    serialized_options=b'\202\323\344\223\002\036\"\034/v3/CheckOrderFulfillability',
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='CreateOrder',
    full_name='ext.maps.booking.partner.v3.BookingService.CreateOrder',
    index=9,
    containing_service=None,
    input_type=_CREATEORDERREQUEST,
    output_type=_CREATEORDERRESPONSE,
    serialized_options=b'\202\323\344\223\002\021\"\017/v3/CreateOrder',
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ListOrders',
    full_name='ext.maps.booking.partner.v3.BookingService.ListOrders',
    index=10,
    containing_service=None,
    input_type=_LISTORDERSREQUEST,
    output_type=_LISTORDERSRESPONSE,
    serialized_options=b'\202\323\344\223\002\020\"\016/v3/ListOrders',
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_BOOKINGSERVICE)

DESCRIPTOR.services_by_name['BookingService'] = _BOOKINGSERVICE

# @@protoc_insertion_point(module_scope)
