# Generated by Django 4.0.8 on 2023-02-01 19:40

from django.db import migrations, models
import django.db.models.deletion
import lib.models
import lib.payment_providers.enums
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('payment_providers', '0008_adyenpayout_capture_external_ids_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='SetupIntent',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'id',
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                (
                    'provider_code',
                    models.CharField(
                        choices=[('adyen', 'Adyen'), ('stripe', 'Stripe')],
                        db_index=True,
                        max_length=20,
                    ),
                ),
                (
                    'method_type',
                    models.CharField(
                        choices=[
                            ('terminal', 'Terminal'),
                            ('card', 'Card (Mobile Payment)'),
                            ('google_pay', 'Google Pay'),
                            ('apple_pay', 'Apple Pay'),
                        ],
                        db_index=True,
                        max_length=20,
                    ),
                ),
                (
                    'status',
                    models.CharField(
                        choices=[('new', 'New'), ('success', 'Success'), ('failed', 'Failed')],
                        default=lib.payment_providers.enums.SetupIntentStatus['NEW'],
                        max_length=30,
                    ),
                ),
                ('client_secret', models.CharField(blank=True, max_length=68, null=True)),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.CreateModel(
            name='StripeSetupIntent',
            fields=[
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'id',
                    models.UUIDField(
                        default=uuid.uuid4, editable=False, primary_key=True, serialize=False
                    ),
                ),
                ('external_id', models.CharField(max_length=32, unique=True)),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.AlterModelOptions(
            name='adyenrefund',
            options={},
        ),
        migrations.AlterModelOptions(
            name='paymentoperation',
            options={'ordering': ['created']},
        ),
        migrations.AlterModelOptions(
            name='stripetokenizedpaymentmethod',
            options={'get_latest_by': 'updated'},
        ),
        migrations.AlterModelManagers(
            name='stripetokenizedpaymentmethod',
            managers=[
                ('objects', lib.models.ArchiveManager()),
            ],
        ),
        migrations.AlterModelManagers(
            name='tokenizedpaymentmethod',
            managers=[
                ('objects', lib.models.ArchiveManager()),
            ],
        ),
        migrations.RemoveConstraint(
            model_name='stripetokenizedpaymentmethod',
            name='only_one_default_pm',
        ),
        migrations.RemoveField(
            model_name='adyencustomer',
            name='external_id',
        ),
        migrations.RemoveField(
            model_name='adyencustomer',
            name='ip',
        ),
        migrations.RemoveField(
            model_name='adyencustomer',
            name='shopper_reference',
        ),
        migrations.RemoveField(
            model_name='adyenpayment',
            name='external_id',
        ),
        migrations.RemoveField(
            model_name='adyenpayment',
            name='psp_reference',
        ),
        migrations.RemoveField(
            model_name='adyentokenizedpaymentmethod',
            name='cardholder_id',
        ),
        migrations.RemoveField(
            model_name='stripetokenizedpaymentmethod',
            name='default',
        ),
        migrations.RemoveField(
            model_name='tokenizedpaymentmethod',
            name='external_id',
        ),
        migrations.AddField(
            model_name='adyenauth',
            name='adyen_tokenized_payment_method',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name='auths',
                to='payment_providers.adyentokenizedpaymentmethod',
            ),
        ),
        migrations.AddField(
            model_name='adyenauth',
            name='raw_error_code',
            field=models.CharField(blank=True, max_length=16, null=True),
        ),
        migrations.AddField(
            model_name='adyenauth',
            name='refusal_reason',
            field=models.CharField(blank=True, max_length=64, null=True),
        ),
        migrations.AddField(
            model_name='adyencapture',
            name='raw_error_code',
            field=models.CharField(blank=True, max_length=16, null=True),
        ),
        migrations.AddField(
            model_name='adyencustomer',
            name='user_v1_id',
            field=models.IntegerField(blank=True, db_index=True, null=True),
        ),
        migrations.AddField(
            model_name='adyenrefund',
            name='oper_result',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='adyenrefund',
            name='psp_reference',
            field=models.CharField(blank=True, db_index=True, max_length=64, null=True),
        ),
        migrations.AddField(
            model_name='adyenrefund',
            name='raw_error_code',
            field=models.CharField(blank=True, max_length=16, null=True),
        ),
        migrations.AddField(
            model_name='adyentokenizedpaymentmethod',
            name='cardholder_v1_id',
            field=models.IntegerField(blank=True, db_index=True, null=True),
        ),
        migrations.AddField(
            model_name='adyentokenizedpaymentmethod',
            name='email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AddField(
            model_name='adyentokenizedpaymentmethod',
            name='ip',
            field=models.GenericIPAddressField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='adyentokenizedpaymentmethod',
            name='name',
            field=models.CharField(blank=True, max_length=128, null=True),
        ),
        migrations.AddField(
            model_name='adyentokenizedpaymentmethod',
            name='recurr_detail_ref',
            field=models.CharField(blank=True, db_index=True, max_length=16, null=True),
        ),
        migrations.AddField(
            model_name='adyentokenizedpaymentmethod',
            name='shopper_reference',
            field=models.CharField(blank=True, db_index=True, max_length=80, null=True),
        ),
        migrations.AddField(
            model_name='adyentransferfund',
            name='transfer_code',
            field=models.CharField(
                choices=[
                    ('debit_refund_fee', 'debit_refund_fee'),
                    ('debit_chargeback_fee', 'debit_chargeback_fee'),
                    ('manual_fee', 'manual_fee'),
                    ('credit_b2breward', 'credit_b2breward'),
                ],
                max_length=80,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name='payment',
            name='currency',
            field=models.CharField(max_length=3, null=True),
        ),
        migrations.AddField(
            model_name='payment',
            name='metadata',
            field=models.JSONField(default=dict),
        ),
        migrations.AddField(
            model_name='stripepaymentintent',
            name='error_code',
            field=models.CharField(blank=True, default=None, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='transferfund',
            name='currency',
            field=models.CharField(max_length=3, null=True),
        ),
        migrations.AlterField(
            model_name='adyenaccountholder',
            name='external_id',
            field=models.CharField(max_length=64, unique=True),
        ),
        migrations.AlterField(
            model_name='adyenauth',
            name='external_id',
            field=models.CharField(max_length=64, unique=True),
        ),
        migrations.AlterField(
            model_name='adyencapture',
            name='external_id',
            field=models.CharField(max_length=64, unique=True),
        ),
        migrations.AlterField(
            model_name='adyentokenizedpaymentmethod',
            name='tokenized_payment_method',
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='adyen_tokenized_payment_method',
                to='payment_providers.tokenizedpaymentmethod',
            ),
        ),
        migrations.AlterField(
            model_name='payment',
            name='error_code',
            field=models.CharField(
                blank=True,
                choices=[
                    ('connection_error', 'Connection Error. Please try again later.'),
                    ('generic_error', 'Declined. Try again or try another payment method.'),
                    ('generic_payment_error', 'Declined. Try again or try another payment method.'),
                    ('generic_card_error', 'Declined. Try again or try another payment method.'),
                    ('not_permitted', 'Declined. Try another payment method.'),
                    ('fraudulent', 'Declined. Try again or try another payment method.'),
                    ('restricted_card', 'Declined. Try again or try another payment method.'),
                    ('merchant_blacklist', 'Declined. Try another payment method.'),
                    ('reenter_transaction', 'Declined. Try again or try another payment method.'),
                    ('bank_account_verification_failed', 'Declined. Try another payment method.'),
                    ('debit_not_authorized', 'Declined. Contact the customer.'),
                    ('refer_to_customer', 'Declined. Contact the customer.'),
                    ('routing_number_invalid', "Declined. The bank routing number isn't valid."),
                    ('issuer_not_available', 'Declined. Try again or try another payment method.'),
                    ('bank_account_invalid', 'Declined. Try another payment method.'),
                    ('processing_error_card', 'Declined. Try again or try another payment method.'),
                    ('authentication_required', 'Declined. Try another payment method.'),
                    (
                        'transfers_not_allowed',
                        "Transfer can't be completed. Contact Booksy Support Team.",
                    ),
                    (
                        'stripe_account_problem',
                        "There's an issue with your Stripe account. Contact Stripe support team.",
                    ),
                    (
                        'account_country_invalid_address',
                        "There's an issue with the address. The account and the business must be located in the same country.",
                    ),
                    (
                        'country_unsupported',
                        "Accounts can't be set up in the country you selected yet. Choose from the currently supported countries.",
                    ),
                    (
                        'card_not_supported',
                        "Declined. This type of card isn't supported. Try another payment method.",
                    ),
                    (
                        'currency_not_supported',
                        "Declined. This card doesn't support transactions in this currency.",
                    ),
                    ('invalid_account_card', 'Declined. Try another payment method.'),
                    ('account_invalid', ''),
                    ('account_not_verified_yet', "Your account hasn't been activated."),
                    ('expired_card', 'This card has expired. Try another payment method.'),
                    (
                        'incorrect_cvc',
                        'Declined. Verify the CVC/CVV code and try again, or use another payment method.',
                    ),
                    (
                        'incorrect_number',
                        'The card number you entered is incorrect. Verify the number and try again.',
                    ),
                    (
                        'tax_id_invalid',
                        'The tax ID number you entered is invalid.It must be at least 9 digits. Verify the ID and try again.',
                    ),
                    ('taxes_calculation_failed', ''),
                    (
                        'invalid_expiry_month',
                        'The expiration month is invalid. Verify and try again.',
                    ),
                    (
                        'invalid_expiry_year',
                        'The expiration year is invalid. Verify and try again.',
                    ),
                    ('invalid_characters', 'You entered invalid characters. Verify and try again.'),
                    ('incorrect_pin', 'The PIN you entered is incorrect. Verify and try again.'),
                    (
                        'incorrect_zip',
                        'The ZIP/Postal Code you entered is incorrect. Verify and try again.',
                    ),
                    ('pin_required', 'Declined. Try another payment method.'),
                    ('card_decline_rate_limit_exceeded', 'Declined. Try another payment method.'),
                    ('card_declined', 'Declined. Try again or try another payment method.'),
                    ('phone_number_required', ''),
                    ('email_invalid', 'The email address is invalid. Verify and try again.'),
                    (
                        'incorrect_address',
                        'The mailing address is incorrect. Verify and try again.',
                    ),
                    ('state_unsupported', 'Declined. Try another payment method.'),
                    ('pin_try_exceeded', 'Declined. Try another payment method.'),
                    ('terminal_location_country_unsupported', ''),
                    ('insufficient_funds', 'Declined. Try another payment method.'),
                    (
                        'amount_too_large',
                        'Declined. The total is higher than the maximum allowed.Enter a lower amount and try again, or use another payment method.',
                    ),
                    (
                        'amount_too_small',
                        'Declined. The total is lower than the minimum allowed.Enter a higher amount and try again, or use another payment method.',
                    ),
                    ('invalid_amount', 'Declined. Try another payment method.'),
                    ('charge_already_captured', 'This transaction has already been processed.'),
                    (
                        'charge_already_refunded',
                        "The transaction you're trying to refund has already been refunded.",
                    ),
                    (
                        'technical_problem',
                        "There's been an error and this operation couldn't be completed. Try again.",
                    ),
                    ('instant_payouts_unsupported', ''),
                    ('payouts_not_allowed', ''),
                    ('timeout', 'Your request has timed out. Try again.'),
                    (
                        'order_creation_failed',
                        "This order couldn't be created. Verify and try again. ",
                    ),
                    (
                        'order_required_settings',
                        'This order is missing information. Verify and try again.',
                    ),
                    ('order_status_invalid', ''),
                    (
                        'shipping_calculation_failed',
                        "The shipping calculation couldn't be completed. Verify and try again.",
                    ),
                    (
                        'out_of_inventory',
                        'Something on this order is out of stock. Verify and try again.',
                    ),
                    ('payment_expired', 'Declined. Try again or try another payment method.'),
                    ('payment_failed', 'Declined. Try again or try another payment method.'),
                    ('url_invalid', 'The URL entered is invalid. Verify and try again.'),
                    ('three_d_secure_problem', 'Declined. Try another payment method.'),
                ],
                default=None,
                max_length=100,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name='payment',
            name='payment_method',
            field=models.CharField(
                choices=[
                    ('terminal', 'Terminal'),
                    ('card', 'Card (Mobile Payment)'),
                    ('google_pay', 'Google Pay'),
                    ('apple_pay', 'Apple Pay'),
                ],
                db_index=True,
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name='paymentoperation',
            name='type',
            field=models.CharField(
                choices=[
                    ('cancel_or_refund', 'Cancel or Refund'),
                    ('refund', 'Refund'),
                    ('chargeback', 'Chargeback'),
                    ('second_chargeback', 'Second chargeback'),
                    ('reversed_chargeback', 'Reversed chargeback'),
                ],
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name='stripecustomer',
            name='customer',
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='_stripe_customer',
                to='payment_providers.customer',
            ),
        ),
        migrations.AlterField(
            model_name='tokenizedpaymentmethod',
            name='method_type',
            field=models.CharField(
                choices=[
                    ('terminal', 'Terminal'),
                    ('card', 'Card (Mobile Payment)'),
                    ('google_pay', 'Google Pay'),
                    ('apple_pay', 'Apple Pay'),
                ],
                db_index=True,
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name='stripesetupintent',
            name='setup_intent',
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='stripe_setup_intent',
                to='payment_providers.setupintent',
            ),
        ),
        migrations.AddField(
            model_name='setupintent',
            name='customer',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='setup_intents',
                to='payment_providers.customer',
            ),
        ),
    ]
