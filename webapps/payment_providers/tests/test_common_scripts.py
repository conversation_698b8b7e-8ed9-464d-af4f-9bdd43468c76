from unittest import TestCase

import mock
import pytest
from model_bakery import baker

from webapps.business.models import Business
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_providers.models import *  # pylint: disable=wildcard-import,unused-wildcard-import
from webapps.payment_providers.scripts import (
    clear_all_payment_providers_business_data,
    clear_all_payment_providers_user_data,
    clear_payment_providers_data_for_business_ids,
)
from webapps.pos.models import POS
from webapps.stripe_integration.models import StripeAccount


@pytest.mark.django_db
class TestCommonScripts(TestCase):
    def setUp(self):
        super().setUp()

        # business
        self.business = baker.make(Business)
        self.business2 = baker.make(Business)

        # pos
        self.pos = baker.make(POS, business=self.business)
        self.pos2 = baker.make(POS, business=self.business2)

        # stripe_account_holder
        self.stripe_account = baker.make(
            StripeAccount, pos=self.pos, external_id='stripe_account_external_id'
        )
        self.stripe_account2 = baker.make(
            StripeAccount, pos=self.pos2, external_id='stripe_account_external_id2'
        )

    def _create_all_data_for_business(self, business):
        # common
        account_holder = AccountHolder.objects.get(
            id=PaymentGatewayPort.get_business_wallet(
                business_id=business.id,
            ).account_holder_id
        )
        payment = baker.make(Payment, account_holder=account_holder)
        baker.make(PaymentHistory, payment=payment, data={})
        payment_operation = baker.make(PaymentOperation, payment=payment)
        baker.make(PaymentOperationHistory, payment_operation=payment_operation, data={})
        transfer_fund = baker.make(TransferFund, sender=account_holder, receiver=account_holder)
        payout = baker.make(Payout, account_holder=account_holder)

        # stripe
        stripe_account_holder = baker.make(
            StripeAccountHolder,
            account_holder=account_holder,
            external_id=business.pos.stripe_account.external_id,
        )
        baker.make(StripeAccountHolderHistory, stripe_account_holder=stripe_account_holder, data={})
        payment_intent = baker.make(StripePaymentIntent, payment=payment)
        baker.make(StripePaymentIntentHistory, payment_intent=payment_intent, data={})
        baker.make(StripeTransferFund, transfer_fund=transfer_fund)
        baker.make(StripeLocation, account=stripe_account_holder)
        baker.make(StripeRefund, payment_operation=payment_operation)
        baker.make(StripeDispute, payment_operation=payment_operation)
        baker.make(StripePayout, payout=payout)

        # adyen
        baker.make(AdyenAccountHolder, account_holder=account_holder)
        adyen_payment = baker.make(AdyenPayment, payment=payment)
        baker.make(AdyenTransferFund, transfer_fund=transfer_fund)
        baker.make(AdyenPayout, payout=payout)
        adyen_auth = baker.make(AdyenAuth, adyen_payment=adyen_payment)
        baker.make(AdyenCapture, auth=adyen_auth)
        baker.make(AdyenRefund, payment_operation=payment_operation)
        baker.make(AdyenDispute, payment_operation=payment_operation)

    def _create_all_customer_data(self):
        # common
        customer = baker.make(Customer)
        tokenized_pm = baker.make(TokenizedPaymentMethod, customer=customer)

        # stripe
        baker.make(StripeCustomer, customer=customer)
        baker.make(StripeTokenizedPaymentMethod, tokenized_payment_method=tokenized_pm)

        # adyen
        baker.make(AdyenCustomer, customer=customer)
        baker.make(AdyenTokenizedPaymentMethod, tokenized_payment_method=tokenized_pm)

    def _create_non_related_data(self):
        baker.make(Notification)
        baker.make(ProviderRequestLog)

    def _create_all_data(self):
        self._create_all_data_for_business(self.business)
        self._create_all_data_for_business(self.business2)
        self._create_all_customer_data()
        self._create_all_customer_data()
        self._create_non_related_data()
        self._create_non_related_data()

    @mock.patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    def test_clear_all_business_data(self, _):
        self._create_all_data()

        clear_all_payment_providers_business_data(confirm=True)

        models_to_assert_removed = [
            AccountHolder,
            Payment,
            PaymentHistory,
            PaymentOperation,
            PaymentOperationHistory,
            TransferFund,
            Payout,
            StripeAccountHolder,
            StripeAccountHolderHistory,
            StripePaymentIntent,
            StripePaymentIntentHistory,
            StripeTransferFund,
            StripeLocation,
            StripeRefund,
            StripeDispute,
            StripePayout,
            AdyenAccountHolder,
            AdyenPayment,
            AdyenTransferFund,
            AdyenPayout,
            AdyenAuth,
            AdyenCapture,
            AdyenRefund,
            AdyenDispute,
        ]
        models_to_assert_not_removed = [
            Customer,
            TokenizedPaymentMethod,
            StripeCustomer,
            StripeTokenizedPaymentMethod,
            ProviderRequestLog,
            Notification,
            AdyenCustomer,
            AdyenTokenizedPaymentMethod,
        ]
        for model in models_to_assert_removed:
            self.assertEqual(model.objects.count(), 0)

        for model in models_to_assert_not_removed:
            self.assertEqual(model.objects.count(), 2)

    @mock.patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    def test_clear_all_user_data(self, _):
        self._create_all_data()

        clear_all_payment_providers_user_data(confirm=True)

        models_to_assert_removed = [
            Customer,
            TokenizedPaymentMethod,
            StripeCustomer,
            StripeTokenizedPaymentMethod,
            AdyenCustomer,
            AdyenTokenizedPaymentMethod,
        ]
        models_to_assert_not_removed = [
            AccountHolder,
            Payment,
            PaymentHistory,
            PaymentOperation,
            PaymentOperationHistory,
            TransferFund,
            Payout,
            StripeAccountHolder,
            StripeAccountHolderHistory,
            StripePaymentIntent,
            StripePaymentIntentHistory,
            StripeTransferFund,
            StripeLocation,
            StripeRefund,
            StripeDispute,
            StripePayout,
            ProviderRequestLog,
            Notification,
            AdyenAccountHolder,
            AdyenPayment,
            AdyenTransferFund,
            AdyenPayout,
            AdyenAuth,
            AdyenCapture,
        ]

        for model in models_to_assert_removed:
            self.assertEqual(model.objects.count(), 0)

        for model in models_to_assert_not_removed:
            self.assertEqual(model.objects.count(), 2)

    @mock.patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    def test_clear_data_for_business(self, _):
        self._create_all_data()

        clear_payment_providers_data_for_business_ids(
            business_ids=[self.business2.id], confirm=True
        )

        models_to_assert_not_removed = [
            Customer,
            TokenizedPaymentMethod,
            StripeCustomer,
            StripeTokenizedPaymentMethod,
        ]
        for model in models_to_assert_not_removed:
            self.assertEqual(model.objects.count(), 2)

        self.assertEqual(
            AccountHolder.objects.filter(
                stripe_account_holder__external_id=self.stripe_account2.external_id,
            ).count(),
            0,
        )
        self.assertEqual(
            StripeAccountHolder.objects.filter(
                external_id=self.stripe_account2.external_id,
            ).count(),
            0,
        )

        models_to_assert_removed = [
            AccountHolder,
            Payment,
            PaymentHistory,
            PaymentOperation,
            PaymentOperationHistory,
            TransferFund,
            Payout,
            StripeAccountHolder,
            StripeAccountHolderHistory,
            StripePaymentIntent,
            StripePaymentIntentHistory,
            StripeTransferFund,
            StripeLocation,
            StripeRefund,
            StripeDispute,
            StripePayout,
            AdyenAccountHolder,
            AdyenPayment,
            AdyenTransferFund,
            AdyenPayout,
            AdyenAuth,
            AdyenCapture,
        ]
        for model in models_to_assert_removed:
            self.assertEqual(model.objects.count(), 1)
