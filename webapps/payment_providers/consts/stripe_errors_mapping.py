from lib.payments.enums import (
    PaymentError,
    PayoutError,
)
from webapps.payment_providers.consts.stripe import StripePayoutError
from webapps.payment_providers.consts.stripe_error_code import (
    ApiErrorCode,
    DeclineCode,
    TapToPayErrorCode,
)

COMMON_PAYMENT_ERROR_STRIPE_MAPPING = {
    PaymentError.CONNECTION_ERROR: [],
    PaymentError.GENERIC_ERROR: [
        ApiErrorCode.ACSS_DEBIT_SESSION_INCOMPLETE,
        ApiErrorCode.ALIPAY_UPGRADE_REQUIRED,
        ApiErrorCode.BITCOIN_UPGRADE_REQUIRED,
        ApiErrorCode.CHARGE_INVALID_PARAMETER,
        ApiErrorCode.INTENT_INVALID_STATE,
        ApiErrorCode.INTENT_VERIFICATION_METHOD_MISSING,
        ApiErrorCode.INVALID_SOURCE_USAGE,
        ApiErrorCode.SEPA_UNSUPPORTED_ACCOUNT,
        ApiErrorCode.PRODUCT_INACTIVE,
        ApiErrorCode.RESOURCE_ALREADY_EXISTS,
        ApiErrorCode.RESOURCE_MISSING,
        ApiErrorCode.SKU_INACTIVE,
        DeclineCode.GENERIC_DECLINE,
    ],
    PaymentError.GENERIC_PAYMENT_ERROR: [
        DeclineCode.DUPLICATE_TRANSACTION,
    ],
    PaymentError.GENERIC_CARD_ERROR: [
        DeclineCode.NO_ACTION_TAKEN,
        DeclineCode.CALL_ISSUER,
        DeclineCode.DO_NOT_HONOR,
        DeclineCode.DO_NOT_TRY_AGAIN,
        DeclineCode.REVOCATION_OF_ALL_AUTHORIZATIONS,
        DeclineCode.REVOCATION_OF_AUTHORIZATION,
        DeclineCode.SECURITY_VIOLATION,
        DeclineCode.SERVICE_NOT_ALLOWED,
        DeclineCode.STOP_PAYMENT_ORDER,
        DeclineCode.TESTMODE_DECLINE,
        DeclineCode.TRANSACTION_NOT_ALLOWED,
        DeclineCode.TRY_AGAIN_LATER,
    ],
    PaymentError.NOT_PERMITTED: [
        DeclineCode.NOT_PERMITTED,
        ApiErrorCode.PAYMENT_INTENT_UNEXPECTED_STATE,
    ],
    PaymentError.FRAUDULENT: [
        DeclineCode.FRAUDULENT,
    ],
    PaymentError.RESTRICTED_CARD: [
        DeclineCode.RESTRICTED_CARD,
        DeclineCode.LOST_CARD,
        DeclineCode.PICKUP_CARD,
        DeclineCode.STOLEN_CARD,
    ],
    PaymentError.MERCHANT_BLACKLIST: [
        DeclineCode.MERCHANT_BLACKLIST,
    ],
    PaymentError.REENTER_TRANSACTION: [
        DeclineCode.REENTER_TRANSACTION,
    ],
    PaymentError.BANK_ACCOUNT_VERIFICATION_FAILED: [
        ApiErrorCode.BANK_ACCOUNT_UNVERIFIED,
        ApiErrorCode.BANK_ACCOUNT_VERIFICATION_FAILED,
    ],
    PaymentError.DEBIT_NOT_AUTHORIZED: [
        ApiErrorCode.DEBIT_NOT_AUTHORIZED,
    ],
    PaymentError.REFER_TO_CUSTOMER: [
        ApiErrorCode.REFER_TO_CUSTOMER,
    ],
    PaymentError.ROUTING_NUMBER_INVALID: [
        ApiErrorCode.ROUTING_NUMBER_INVALID,
    ],
    PaymentError.ISSUER_NOT_AVAILABLE: [
        DeclineCode.APPROVE_WITH_ID,
        DeclineCode.ISSUER_NOT_AVAILABLE,
    ],
    PaymentError.BANK_ACCOUNT_INVALID: [
        ApiErrorCode.BANK_ACCOUNT_UNUSABLE,
        ApiErrorCode.BANK_ACCOUNT_DECLINED,
        ApiErrorCode.BANK_ACCOUNT_EXISTS,
    ],
    PaymentError.PROCESSING_ERROR_CARD: [
        DeclineCode.PROCESSING_ERROR,
        ApiErrorCode.PROCESSING_ERROR,
    ],
    PaymentError.AUTHENTICATION_REQUIRED: [
        DeclineCode.AUTHENTICATION_REQUIRED,
        ApiErrorCode.AUTHENTICATION_REQUIRED,
        ApiErrorCode.PAYMENT_INTENT_AUTHENTICATION_FAILURE,
    ],
    PaymentError.TRANSFERS_NOT_ALLOWED: [
        ApiErrorCode.TRANSFERS_NOT_ALLOWED,
    ],
    PaymentError.STRIPE_ACCOUNT_PROBLEM: [
        ApiErrorCode.ACCOUNT_ERROR_COUNTRY_CHANGE_REQUIRES_ADDITIONAL_STEPS,
        ApiErrorCode.BILLING_INVALID_MANDATE,
        ApiErrorCode.NO_ACCOUNT,
        ApiErrorCode.NOT_ALLOWED_ON_STANDARD_ACCOUNT,
        ApiErrorCode.PLATFORM_ACCOUNT_REQUIRED,
    ],
    PaymentError.ACCOUNT_COUNTRY_INVALID_ADDRESS: [
        ApiErrorCode.ACCOUNT_COUNTRY_INVALID_ADDRESS,
        ApiErrorCode.COUNTRY_CODE_INVALID,
    ],
    PaymentError.COUNTRY_UNSUPPORTED: [
        ApiErrorCode.COUNTRY_UNSUPPORTED,
    ],
    PaymentError.CARD_NOT_SUPPORTED: [
        DeclineCode.CARD_NOT_SUPPORTED,
    ],
    PaymentError.CURRENCY_NOT_SUPPORTED: [
        DeclineCode.CURRENCY_NOT_SUPPORTED,
    ],
    PaymentError.INVALID_ACCOUNT_CARD: [
        DeclineCode.NEW_ACCOUNT_INFORMATION_AVAILABLE,
        DeclineCode.INVALID_ACCOUNT,
    ],
    PaymentError.ACCOUNT_INVALID: [
        ApiErrorCode.ACCOUNT_INVALID,
    ],
    PaymentError.ACCOUNT_NOT_VERIFIED_YET: [
        ApiErrorCode.TESTMODE_CHARGES_ONLY,
    ],
    PaymentError.EXPIRED_CARD: [
        ApiErrorCode.EXPIRED_CARD,
        DeclineCode.EXPIRED_CARD,
    ],
    PaymentError.INCORRECT_CVC: [
        ApiErrorCode.INCORRECT_CVC,
        DeclineCode.INCORRECT_CVC,
        ApiErrorCode.INVALID_CVC,
        DeclineCode.INVALID_CVC,
    ],
    PaymentError.INCORRECT_NUMBER: [
        ApiErrorCode.INCORRECT_NUMBER,
        DeclineCode.INCORRECT_NUMBER,
        ApiErrorCode.ACCOUNT_NUMBER_INVALID,
        ApiErrorCode.INVALID_NUMBER,
        DeclineCode.INVALID_NUMBER,
    ],
    PaymentError.TAX_ID_INVALID: [
        ApiErrorCode.TAX_ID_INVALID,
    ],
    PaymentError.TAXES_CALCULATION_FAILED: [
        ApiErrorCode.TAXES_CALCULATION_FAILED,
    ],
    PaymentError.INVALID_EXPIRY_MONTH: [
        ApiErrorCode.INVALID_EXPIRY_MONTH,
        DeclineCode.INVALID_EXPIRY_MONTH,
    ],
    PaymentError.INVALID_EXPIRY_YEAR: [
        ApiErrorCode.INVALID_EXPIRY_YEAR,
        DeclineCode.INVALID_EXPIRY_YEAR,
    ],
    PaymentError.INVALID_CHARACTERS: [
        ApiErrorCode.INVALID_CHARACTERS,
    ],
    PaymentError.INCORRECT_PIN: [
        DeclineCode.INVALID_PIN,
        DeclineCode.INCORRECT_PIN,
    ],
    PaymentError.INCORRECT_ZIP: [
        DeclineCode.INCORRECT_ZIP,
        ApiErrorCode.INCORRECT_ZIP,
        ApiErrorCode.POSTAL_CODE_INVALID,
    ],
    PaymentError.PIN_REQUIRED: [
        DeclineCode.OFFLINE_PIN_REQUIRED,
        DeclineCode.ONLINE_OR_OFFLINE_PIN_REQUIRED,
    ],
    PaymentError.CARD_DECLINE_RATE_LIMIT_EXCEEDED: [
        ApiErrorCode.CARD_DECLINE_RATE_LIMIT_EXCEEDED,
    ],
    PaymentError.CARD_DECLINED: [
        ApiErrorCode.CARD_DECLINED,
    ],
    PaymentError.PHONE_NUMBER_REQUIRED: [
        ApiErrorCode.CARDHOLDER_PHONE_NUMBER_REQUIRED,
    ],
    PaymentError.EMAIL_INVALID: [
        ApiErrorCode.EMAIL_INVALID,
    ],
    PaymentError.INCORRECT_ADDRESS: [
        ApiErrorCode.INCORRECT_ADDRESS,
    ],
    PaymentError.STATE_UNSUPPORTED: [
        ApiErrorCode.STATE_UNSUPPORTED,
    ],
    PaymentError.PIN_TRY_EXCEEDED: [
        DeclineCode.PIN_TRY_EXCEEDED,
    ],
    PaymentError.TERMINAL_LOCATION_COUNTRY_UNSUPPORTED: [
        ApiErrorCode.TERMINAL_LOCATION_COUNTRY_UNSUPPORTED,
    ],
    PaymentError.INSUFFICIENT_FUNDS: [
        DeclineCode.INSUFFICIENT_FUNDS,
        ApiErrorCode.INSUFFICIENT_FUNDS,
        DeclineCode.WITHDRAWAL_COUNT_LIMIT_EXCEEDED,
        ApiErrorCode.BALANCE_INSUFFICIENT,
    ],
    PaymentError.AMOUNT_TOO_LARGE: [
        ApiErrorCode.AMOUNT_TOO_LARGE,
    ],
    PaymentError.AMOUNT_TOO_SMALL: [
        ApiErrorCode.AMOUNT_TOO_SMALL,
    ],
    PaymentError.INVALID_AMOUNT: [
        DeclineCode.INVALID_AMOUNT,
        DeclineCode.CARD_VELOCITY_EXCEEDED,
        ApiErrorCode.INVALID_CHARGE_AMOUNT,
    ],
    PaymentError.CHARGE_ALREADY_CAPTURED: [
        ApiErrorCode.CHARGE_ALREADY_CAPTURED,
    ],
    PaymentError.CHARGE_ALREADY_REFUNDED: [
        ApiErrorCode.CHARGE_ALREADY_REFUNDED,
        ApiErrorCode.CHARGE_DISPUTED,
    ],
    PaymentError.TECHNICAL_PROBLEM: [
        ApiErrorCode.IDEMPOTENCY_KEY_IN_USE,
        ApiErrorCode.API_KEY_EXPIRED,
        ApiErrorCode.CLEARING_CODE_UNSUPPORTED,
        ApiErrorCode.CUSTOMER_MAX_PAYMENT_METHODS,
        ApiErrorCode.LIVEMODE_MISMATCH,
        ApiErrorCode.LOCK_TIMEOUT,
        ApiErrorCode.MISSING,
        ApiErrorCode.PLATFORM_API_KEY_EXPIRED,
        ApiErrorCode.PARAMETER_INVALID_EMPTY,
        ApiErrorCode.PARAMETER_INVALID_INTEGER,
        ApiErrorCode.PARAMETER_INVALID_STRING_BLANK,
        ApiErrorCode.PARAMETER_INVALID_STRING_EMPTY,
        ApiErrorCode.PARAMETER_MISSING,
        ApiErrorCode.PARAMETER_UNKNOWN,
        ApiErrorCode.PARAMETERS_EXCLUSIVE,
        ApiErrorCode.PAYMENT_INTENT_ACTION_REQUIRED,
        ApiErrorCode.PAYMENT_INTENT_INCOMPATIBLE_PAYMENT_METHOD,
        ApiErrorCode.PAYMENT_INTENT_INVALID_PARAMETER,
        ApiErrorCode.PAYMENT_INTENT_MANDATE_INVALID,
        ApiErrorCode.TLS_VERSION_UNSUPPORTED,
        ApiErrorCode.RATE_LIMIT,
        ApiErrorCode.PAYMENT_METHOD_UNSUPPORTED_TYPE,
        ApiErrorCode.SECRET_KEY_REQUIRED,
    ],
    PaymentError.INSTANT_PAYOUTS_UNSUPPORTED: [
        ApiErrorCode.INSTANT_PAYOUTS_UNSUPPORTED,
        ApiErrorCode.INVALID_CARD_TYPE,
    ],
    PaymentError.PAYOUTS_NOT_ALLOWED: [
        ApiErrorCode.PAYOUTS_NOT_ALLOWED,
    ],
    PaymentError.TIMEOUT: [
        ApiErrorCode.ORDER_UPSTREAM_TIMEOUT,
        ApiErrorCode.LOCK_TIMEOUT,
        ApiErrorCode.SENSITIVE_DATA_ACCESS_EXPIRED,
    ],
    PaymentError.ORDER_CREATION_FAILED: [
        ApiErrorCode.ORDER_CREATION_FAILED,
        ApiErrorCode.UPSTREAM_ORDER_CREATION_FAILED,
    ],
    PaymentError.ORDER_REQUIRED_SETTINGS: [
        ApiErrorCode.ORDER_REQUIRED_SETTINGS,
    ],
    PaymentError.ORDER_STATUS_INVALID: [
        ApiErrorCode.ORDER_STATUS_INVALID,
    ],
    PaymentError.SHIPPING_CALCULATION_FAILED: [
        ApiErrorCode.SHIPPING_CALCULATION_FAILED,
    ],
    PaymentError.OUT_OF_INVENTORY: [
        ApiErrorCode.OUT_OF_INVENTORY,
    ],
    PaymentError.PAYMENT_EXPIRED: [
        ApiErrorCode.PAYMENT_INTENT_PAYMENT_ATTEMPT_EXPIRED,
    ],
    PaymentError.PAYMENT_FAILED: [
        ApiErrorCode.PAYMENT_INTENT_PAYMENT_ATTEMPT_FAILED,
    ],
    PaymentError.URL_INVALID: [
        ApiErrorCode.URL_INVALID,
    ],
    PaymentError.THREE_D_SECURE_PROBLEM: [],
    # TTP
    PaymentError.CARD_READ_TIMED_OUT: [
        TapToPayErrorCode.CARD_READ_TIMED_OUT,
    ],
    PaymentError.TAP_TO_PAY_DEVICE_TAMPERED: [
        TapToPayErrorCode.TAP_TO_PAY_DEVICE_TAMPERED,
    ],
    PaymentError.TAP_TO_PAY_NFC_DISABLED: [
        TapToPayErrorCode.TAP_TO_PAY_NFC_DISABLED,
    ],
    PaymentError.LOCATION_SERVICES_DISABLED: [
        TapToPayErrorCode.LOCATION_SERVICES_DISABLED,
    ],
    PaymentError.TAP_TO_PAY_INSECURE_ENVIRONMENT: [
        TapToPayErrorCode.TAP_TO_PAY_INSECURE_ENVIRONMENT,
    ],
}


STRIPE_PAYMENT_ERROR_MAPPING = {}
for payment_error, provider_error_list in COMMON_PAYMENT_ERROR_STRIPE_MAPPING.items():
    for provider_error in provider_error_list:
        STRIPE_PAYMENT_ERROR_MAPPING[provider_error] = payment_error


PAYOUT_ERROR_MAPPING = {
    StripePayoutError.ACCOUNT_CLOSED: PayoutError.ACCOUNT_CLOSED,
    StripePayoutError.ACCOUNT_FROZEN: PayoutError.ACCOUNT_FROZEN,
    StripePayoutError.BANK_ACCOUNT_RESTRICTED: PayoutError.BANK_ACCOUNT_RESTRICTED,
    StripePayoutError.BANK_OWNERSHIP_CHANGED: PayoutError.BANK_OWNERSHIP_CHANGED,
    StripePayoutError.COULD_NOT_PROCESS: PayoutError.COULD_NOT_PROCESS,
    StripePayoutError.DEBIT_NOT_AUTHORIZED: PayoutError.DEBIT_NOT_AUTHORIZED,
    StripePayoutError.DECLINED: PayoutError.DECLINED,
    StripePayoutError.INSUFFICIENT_FUNDS: PayoutError.INSUFFICIENT_FUNDS,
    StripePayoutError.INVALID_ACCOUNT_NUMBER: PayoutError.INVALID_ACCOUNT_NUMBER,
    StripePayoutError.INCORRECT_ACCOUNT_HOLDER_NAME: PayoutError.INCORRECT_ACCOUNT_HOLDER_NAME,
    StripePayoutError.INCORRECT_ACCOUNT_HOLDER_ADDRESS: PayoutError.INCORRECT_ACCOUNT_HOLDER_ADDRESS,  # pylint: disable=line-too-long
    StripePayoutError.INCORRECT_ACCOUNT_HOLDER_TAX_ID: PayoutError.INCORRECT_ACCOUNT_HOLDER_TAX_ID,
    StripePayoutError.INVALID_CURRENCY: PayoutError.INVALID_CURRENCY,
    StripePayoutError.NO_ACCOUNT: PayoutError.NO_ACCOUNT,
    StripePayoutError.UNSUPPORTED_CARD: PayoutError.UNSUPPORTED_CARD,
}
