from decimal import Decimal

from django.core.management.base import BaseCommand
from webapps.stripe_integration.models import StripeAccount
from webapps.payment_providers.models import StripeAccountHolder


class Command(BaseCommand):
    help = 'Script to update min limit fast payout'

    def add_arguments(self, parser):
        parser.add_argument(
            '--fast_payout_min_limit',
            type=float,
            default='50',
            help='',
        )

    def handle(self, *args, **opts):  # pylint: disable=unused-argument
        self.stdout.write("Beginning update of min limit fast payout for StripeAccount objects.")
        StripeAccount.objects.update(
            fast_payout_min_limit=Decimal(opts.get('fast_payout_min_limit'))
        )
        self.stdout.write(
            self.style.SUCCESS("Update of StripeAccount objects finished successfully")
        )

        self.stdout.write("Beginning update of min limit fast payout for StripeAccount objects.")
        StripeAccountHolder.objects.update(
            fast_payout_min_limit=Decimal(opts.get('fast_payout_min_limit'))
        )
        self.stdout.write(
            self.style.SUCCESS("Update of StripeAccountHolder objects finished successfully")
        )
