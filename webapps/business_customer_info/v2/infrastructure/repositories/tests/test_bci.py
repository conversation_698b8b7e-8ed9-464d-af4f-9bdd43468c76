from django.test import TestCase
from model_bakery import baker
import pytest

from lib.rivers import River, remove_document
from webapps.admin_extra.models import BusinessCustomerImportLog
from webapps.business.models import Business as BusinessDjango
from webapps.business.models.bci import BusinessCustomerInfo as BusinessCustomerInfoDjango
from webapps.business_customer_info.v2.domain.enums import ImportType
from webapps.business_customer_info.v2.domain.models.bci import (
    Customer,
    CustomerParams,
)
from webapps.business_customer_info.v2.infrastructure.repositories.bci import (
    BusinessCustomerInfoRepository,
    BusinessCustomerImportLogRepository,
)
from webapps.elasticsearch.elastic import ELASTIC


class TestBusinessCustomerInfoRepository(TestCase):

    def setUp(self):
        self.repository = BusinessCustomerInfoRepository()
        self.business = baker.make(BusinessDjango)

    def test_get__all_attributes(self):
        baker.make(
            BusinessCustomerInfoDjango,
            business=self.business,
            first_name='Adam',
            last_name='Tester',
            cell_phone='+***********',
            email='<EMAIL>',
        )

        bcis = self.repository.get_all(business_id=self.business.id)

        assert len(bcis) == 1
        bci = bcis[0]
        assert isinstance(bci, Customer)
        assert bci.id is not None
        assert bci.first_name == 'Adam'
        assert bci.last_name == 'Tester'
        assert bci.cell_phone == '(*************'
        assert bci.email == '<EMAIL>'

    def test_get_all__returns_all_bcis_for_business(self):
        baker.make(BusinessCustomerInfoDjango, business=self.business, first_name='Adam')
        baker.make(BusinessCustomerInfoDjango, business=self.business, first_name='Bary')
        business_other = baker.make(BusinessDjango)
        baker.make(BusinessCustomerInfoDjango, business=business_other, first_name='Cecil')

        bcis = self.repository.get_all(business_id=self.business.id)

        assert len(bcis) == 2
        assert bcis[0].first_name == 'Adam'
        assert bcis[1].first_name == 'Bary'

    def test_gets_id_by_import_uid(self):
        bcis = [
            baker.make(BusinessCustomerInfoDjango, business=self.business, import_uid='fake_uid'),
            baker.make(BusinessCustomerInfoDjango, business=self.business, import_uid='fake_uid'),
        ]
        expected_ids = [bci.id for bci in bcis]
        baker.make(BusinessCustomerInfoDjango, business=self.business, import_uid='another_uid')
        baker.make(BusinessCustomerInfoDjango, business=self.business, import_uid='')
        baker.make(BusinessCustomerInfoDjango, business=self.business, import_uid=None)

        ids = self.repository.gets_id_by_import_uid(
            business_id=self.business.id, import_uid='fake_uid'
        )

        assert ids == expected_ids

    def test_add_many__created_objects_attributes(self):
        bci = Customer.create(
            params=CustomerParams(
                first_name='Adam',
                full_name='Adam',
                last_name='',
                cell_phone='+***********',
                email='<EMAIL>',
            )
        )

        self.repository.add_or_update_many(
            business_id=self.business.id, bcis=[bci], import_uid='fake_uid'
        )

        assert BusinessCustomerInfoDjango.objects.count() == 1
        bci_django = BusinessCustomerInfoDjango.objects.get(business=self.business)
        assert bci_django.id is not None
        assert bci_django.first_name == 'Adam'
        assert bci_django.last_name == ''
        assert bci_django.cell_phone == '(*************'
        assert bci_django.email == '<EMAIL>'
        assert bci_django.visible_in_business is True
        assert bci_django.import_uid == 'fake_uid'
        assert bci_django.client_type == BusinessCustomerInfoDjango.CLIENT_TYPE__BUSINESS_IMPORT
        assert bci_django.excluded_from_delayed_invitation is True

    def test_add_many__add_many_new(self):
        baker.make(BusinessCustomerInfoDjango, business=self.business, first_name='Adam')
        assert BusinessCustomerInfoDjango.objects.count() == 1
        bcis = [
            Customer.create(
                params=CustomerParams(
                    first_name='Bary',
                    full_name='Bary Stary',
                    last_name='Stary',
                    cell_phone='+***********',
                    email='',
                )
            ),
            Customer.create(
                params=CustomerParams(
                    first_name='Cecil',
                    full_name='Cecil Stedman',
                    last_name='Stedman',
                    cell_phone='+***********',
                    email='<EMAIL>',
                )
            ),
        ]

        self.repository.add_or_update_many(
            business_id=self.business.id, bcis=bcis, import_uid='fake_uid'
        )

        assert BusinessCustomerInfoDjango.objects.count() == 3
        bci_django = BusinessCustomerInfoDjango.objects.filter(business=self.business)
        assert bci_django[0].first_name == 'Adam'
        assert bci_django[1].first_name == 'Bary'
        assert bci_django[2].first_name == 'Cecil'

    def test_add_many__updated_attributes(self):
        bci_before_update = baker.make(
            BusinessCustomerInfoDjango,
            business=self.business,
            first_name='Adam',
            cell_phone='+***********',
            import_uid='old_uid',
        )
        assert BusinessCustomerInfoDjango.objects.count() == 1
        bci = Customer(
            _id=bci_before_update.id,
            params=CustomerParams(
                first_name='Bary',
                full_name='Bary Stary',
                last_name='Stary',
                cell_phone='+***********',
                email='<EMAIL>',
            ),
        )

        self.repository.add_or_update_many(
            business_id=self.business.id, bcis=[bci], import_uid='fake_uid'
        )

        assert BusinessCustomerInfoDjango.objects.count() == 1
        bci_django = BusinessCustomerInfoDjango.objects.get(business=self.business)
        assert bci_django.id is not None
        assert bci_django.first_name == 'Bary'
        assert bci_django.last_name == 'Stary'
        assert bci_django.cell_phone == '(*************'
        assert bci_django.email == '<EMAIL>'
        assert bci_django.visible_in_business is True
        assert bci_django.import_uid == 'fake_uid'
        assert bci_django.client_type == BusinessCustomerInfoDjango.CLIENT_TYPE__BUSINESS_IMPORT

    def test_add_many__update_many_existing(self):
        bci_before_update = [
            baker.make(BusinessCustomerInfoDjango, business=self.business, first_name='Adam'),
            baker.make(BusinessCustomerInfoDjango, business=self.business, first_name='Bary'),
        ]
        bcis = [
            Customer(
                _id=bci_before_update[0].id,
                params=CustomerParams(
                    first_name='Adam',
                    last_name='Tester',
                    full_name='Adam Tester',
                    cell_phone='+***********',
                    email='',
                ),
            ),
            Customer(
                _id=bci_before_update[1].id,
                params=CustomerParams(
                    first_name='Bary',
                    last_name='Stary',
                    full_name='Bary Stary',
                    cell_phone='+***********',
                    email='',
                ),
            ),
        ]

        self.repository.add_or_update_many(
            business_id=self.business.id, bcis=bcis, import_uid='fake_uid'
        )

        assert BusinessCustomerInfoDjango.objects.count() == 2
        bci_django = BusinessCustomerInfoDjango.objects.filter(business=self.business)
        assert bci_django[0].full_name == 'Adam Tester'
        assert bci_django[1].full_name == 'Bary Stary'

    def test_add_many__documents_reindexed(self):
        old_bci = baker.make(
            BusinessCustomerInfoDjango,
            business=self.business,
            first_name='Adam',
            cell_phone='+***********',
        )
        bcis = [
            Customer(
                _id=old_bci.id,
                params=CustomerParams(
                    cell_phone='+***********',
                    email='<EMAIL>',
                    first_name='Adam',
                    full_name='Adam Tester',
                    last_name='Tester',
                ),
            ),
            Customer.create(
                CustomerParams(
                    cell_phone='+***********',
                    email='',
                    first_name='Bary',
                    full_name='Bary Stary',
                    last_name='Stary',
                )
            ),
        ]
        queryset = ELASTIC.documents[BusinessCustomerInfoDjango.es_doc_type].get_queryset()
        assert queryset.filter(business_id=self.business.id).count() == 1
        assert old_bci.get_document()['business_customer']['full_name'] == 'Adam'

        self.repository.add_or_update_many(
            business_id=self.business.id, bcis=bcis, import_uid='fake_uid'
        )

        assert queryset.filter(business_id=self.business.id).count() == 2
        bcis_django = BusinessCustomerInfoDjango.objects.filter(business=self.business).order_by(
            'first_name'
        )
        assert bcis_django[0].get_document()['business_customer']['full_name'] == 'Adam Tester'
        assert bcis_django[1].get_document()['business_customer']['full_name'] == 'Bary Stary'

    def test_add_many__added_to_river(self):
        old_bci = baker.make(
            BusinessCustomerInfoDjango,
            business=self.business,
            first_name='Adam',
            cell_phone='+***********',
        )
        bcis = [
            Customer(
                _id=old_bci.id,
                params=CustomerParams(
                    cell_phone='+***********',
                    email='<EMAIL>',
                    first_name='Adam',
                    full_name='Adam Tester',
                    last_name='Tester',
                ),
            ),
            Customer.create(
                CustomerParams(
                    cell_phone='+***********',
                    email='',
                    first_name='Bary',
                    full_name='Bary Stary',
                    last_name='Stary',
                )
            ),
        ]

        self.repository.add_or_update_many(
            business_id=self.business.id, bcis=bcis, import_uid='fake_uid'
        )

        ids = BusinessCustomerInfoDjango.objects.filter(business=self.business).values_list(
            'id', flat=True
        )
        assert remove_document(River.BUSINESS_CUSTOMER, ids) == 2


@pytest.mark.django_db
class TestBusinessCustomerImportLogRepository:

    def test_add_contacts_import(self):
        repository = BusinessCustomerImportLogRepository()
        business = baker.make(BusinessDjango)
        assert BusinessCustomerImportLog.objects.count() == 0

        repository.add_contacts_import(
            business_id=business.id, imported_count=5, import_uid='fake_uid', device='test_device'
        )

        assert BusinessCustomerImportLog.objects.count() == 1
        import_log = BusinessCustomerImportLog.objects.get(business=business)
        assert import_log.import_type == ImportType.CONTACTS_IMPORT.value
        assert import_log.imported_count == 5
        assert import_log.import_uid == 'fake_uid'
        assert import_log.device == 'test_device'
