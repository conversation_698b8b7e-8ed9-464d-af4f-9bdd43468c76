import datetime
import logging
import re

from django.conf import settings
from lib.gdpr_descriptions import BUSINESS_CUSTOMER_AGREEMENTS
from lib.rivers import bump_document, River
from lib.tools import switch_locale, tznow

from webapps.admin_extra.import_tools.enums import VersumFieldNames as versumField
from webapps.business_customer_info.enums import BCIGetPreferWay
from webapps.business.models.bci import (
    BCIFromContact,
    BusinessCustomerInfo,
    BusinessCustomerInfoTag,
    Tag,
    TAG_REGEX_PATTERN,
)
from webapps.business_customer_info.bci_reports import ImportClientReports
from webapps.structure.models import Region
from webapps.user.models import UnsubscribedEmail

_logger = logging.getLogger('booksy.es_tasks')


tags_split_regex = re.compile(f"[({TAG_REGEX_PATTERN})]+")


def _date_fromisoformat(dtstr):
    # It is assumed that this function will only be called with a
    # string of length exactly 10, and (though this is not used) ASCII-only
    year = int(dtstr[0:4])
    if dtstr[4] != '-':
        raise ValueError(f'Invalid date separator: {dtstr[4]}')

    month = int(dtstr[5:7])

    if dtstr[7] != '-':
        raise ValueError('Invalid date separator')

    day = int(dtstr[8:10])

    return datetime.date(year, month, day)


class BCIImporter:
    """See import_customers()"""

    def __init__(self, business):
        """See import_customers()."""
        self.business = business
        self.is_gdpr = settings.GDPR_COUNTRIES.get(
            settings.API_COUNTRY, settings.GDPR_COUNTRIES.get('default', False)
        )
        # mapping zip_code: Region.id
        self.region_mapping = {}
        prefix = 'biz30'
        self.import_uid = tznow().strftime(f'{prefix}-%Y-%m-%dT%H:%M:%S')

    def set_zip_code(self, row, bci):
        zip_code = row.get(versumField.ZIP_CODE)
        if zip_code:
            region_id = self.region_mapping.get(zip_code)
            # Case 1: Correct region id in mapping
            if region_id:
                bci.region_id = region_id
            # Case 2: No region id in mapping yet
            elif region_id is None:
                try:
                    region_id = (
                        Region.objects.only('id').get(type=Region.Type.ZIP, name=zip_code).id
                    )
                except Region.DoesNotExist:
                    # Add dummy value to region_mapping to avoid next
                    # excess query
                    self.region_mapping[zip_code] = False
                else:
                    bci.region_id = region_id
                    self.region_mapping[zip_code] = region_id

    @staticmethod
    def set_gdpr_agreements(row, bci):
        for agreement_key in BUSINESS_CUSTOMER_AGREEMENTS:
            gdpr_agreement_value = row.get(agreement_key)
            if gdpr_agreement_value is not None:
                setattr(bci, agreement_key, gdpr_agreement_value)

    @staticmethod
    def get_customer_name(row):
        # form full name of customer
        if row.get(versumField.CUS_FULL_NAME):
            customer_name = row.get(versumField.CUS_FULL_NAME, '')
        else:
            customer_name = ' '.join(
                (row.get(versumField.CUS_FIRST_NAME, ''), row.get(versumField.CUS_LAST_NAME, ''))
            ).strip()

        return customer_name

    @staticmethod
    def _save_bci(bci):
        if bci.id:
            # Do not save to the db if BCI hasn't changed at all
            # (import_uid doesn't count)
            dirty_fields = bci.get_dirty_fields(check_relationship=True)
            dirty_fields.pop('import_uid', None)
            update_fields = list(dirty_fields.keys())
            if not update_fields:
                return
            update_fields.append('import_uid')
            bci.save(update_fields=update_fields)
        else:
            bci.save()

    @staticmethod
    def maybe_set_unsubscribed_email(row):
        if row.get(versumField.UNSUBSCRIBE) and row.get(versumField.CUS_EMAIL):
            email = row.get(versumField.CUS_EMAIL)
            if not UnsubscribedEmail.objects.filter(email=email).exists():
                un_ob = UnsubscribedEmail(email=email)
                un_ob.save()

    # pylint: disable=too-many-branches
    @switch_locale
    def import_customers(
        self,
        customers: list[dict],
        report_recipient: str = None,
        return_mapping: bool = False,
        language: str = None,
    ):
        """Main method (moved from webapps.business.tasks.py).
        Will import bci(BusinessCustomerInfo) to business.

        Customers arg is list of dict. Each dict item has the following elements:
            'full_name', 'first_name', 'last_name','email'
            'cell_phone', 'error', 'warning' ...

        For full name fields look CustomerImportFields
        For full name fields for Versum import look VersumFieldNames

        Min required fields: 'full_name', 'email' or 'cell_phone'
            and absence of 'error' field

        :param customers: lis of dict
        :param report_recipient: str, email if argument passed
                    report email will be sent
        :param return_mapping: bool, if passed function will return dict
            where key is previous id of customer and
            value is created bci objects (used in versum import for match)
        :param language: str, language of report add xlsx file
        :return: None or dict
        """

        id_mapping = {}
        business_customer_infos = []
        for i, row in enumerate(customers, 1):
            if 'error' in row:
                continue
            try:
                tags = row.pop('tags', [])
                customer_name = self.get_customer_name(row)
                bci, created = BusinessCustomerInfo.get_or_create_from_contact(
                    BCIFromContact(
                        business=self.business,
                        customer_name=customer_name,
                        customer_email=row.get(versumField.CUS_EMAIL, ''),
                        customer_phone=row.get(versumField.CUS_CELL_PHONE, ''),
                    ),
                    dry_run=True,
                    prefetch_profiles=False,
                    bci_get_prefer_way=BCIGetPreferWay.PHONE,
                )
                for field in [
                    versumField.CUS_FIRST_NAME,
                    versumField.CUS_LAST_NAME,
                    versumField.CUS_DISCOUNT,
                    versumField.BUSINESS_SECRET_NOTE,
                ]:
                    if field in row:
                        setattr(bci, field, row[field])

                bci.visible_in_business = True
                bci.import_uid = self.import_uid
                # additional fields
                if self.is_gdpr:
                    self.set_gdpr_agreements(row, bci)
                bci.address_line_1 = row.get(versumField.CUS_ADDRESS_LINE, '')
                self.set_zip_code(row, bci)
                if row.get(versumField.CUS_BIRTHDAY):
                    bci.birthday = row[versumField.CUS_BIRTHDAY]
                    if isinstance(bci.birthday, str):
                        bci.birthday = _date_fromisoformat(bci.birthday)
                    elif isinstance(bci.birthday, datetime.datetime):
                        bci.birthday = bci.birthday.date()
                self.maybe_set_unsubscribed_email(row)

                bci.disable_post_save_indexing()
                bci.set_client_type(bci.CLIENT_TYPE__BUSINESS_IMPORT)
                if return_mapping and row.get(versumField.ID_CUS):
                    id_mapping[row.get(versumField.ID_CUS)] = bci
                self._save_bci(bci)

                self.handle_bci_tags(tags, bci)

                national_identity_number = row.get(
                    versumField.NATIONAL_IDENTITY_NUMBER,
                )
                if national_identity_number:
                    patient_file = bci.get_or_create_patient_file()
                    if patient_file:
                        patient_file.national_identity_number = national_identity_number
                        patient_file.save()

                bump_document(River.BUSINESS_CUSTOMER, bci.id)
                if created:
                    business_customer_infos.append(bci)

            except (KeyboardInterrupt, SystemExit) as e:
                raise e
            except Exception:  # pylint: disable=broad-except
                # pylint: disable=C0209
                _logger.exception(
                    'Import BCI for business ID: {}, row: {}'.format(self.business.id, i)
                )

        # TODO ticket 73029 should be fixed
        # match_users_task.run([bci.id for bci in business_customer_infos])

        if report_recipient:
            # send report
            ImportClientReports.prepare_send_report(
                report_recipient,
                self.business,
                customers,
                language=language,
            )
        if return_mapping:
            return id_mapping
        return business_customer_infos

    # pylint: enable=too-many-branches

    @staticmethod
    def handle_bci_tags(tags: str, bci: BusinessCustomerInfo):
        if not tags:
            return

        tags_to_create = (Tag(name=tag, business_id=bci.business_id) for tag in tags)
        Tag.objects.bulk_create(tags_to_create, ignore_conflicts=True)

        tags_qs = bci.business.tags.filter(name__in=tags)
        bci_tags_to_create = (
            BusinessCustomerInfoTag(customer_id=bci.id, tag_id=tag_id)
            for tag_id in tags_qs.values_list('id', flat=True)
        )
        BusinessCustomerInfoTag.objects.bulk_create(
            bci_tags_to_create,
            ignore_conflicts=True,
        )
