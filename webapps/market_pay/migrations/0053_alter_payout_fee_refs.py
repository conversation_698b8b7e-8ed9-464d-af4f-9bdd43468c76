# Generated by Django 4.0.2 on 2022-02-21 03:07

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('market_pay', '0052_softdeletemanager_as_allobjects_in_pos_and_market_pay'),
    ]

    operations = [
        migrations.AlterField(
            model_name='payout',
            name='fee_refs',
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.CharField(max_length=500), default=list, size=None
            ),
        ),
    ]
