import logging
import typing as t
from decimal import Decimal
from collections import defaultdict

from django.conf import settings
from django.utils.functional import cached_property
from django.utils.translation import get_language, gettext, gettext_lazy as _

import lib.tools
from country_config import Country
from lib.time_24_hour import format_datetime
from webapps.business.models import Business
from webapps.business.notifications.planner import WorkingHoursSchedulePlanner
from webapps.business.tools import get_business_label
from webapps.market_pay.models import Payout
from webapps.market_pay.typing import PayoutData
from webapps.notification.base import (
    BaseNotification,
    Context,
    PopupTemplate,
    PushTarget,
)
from webapps.notification.channels import (
    EmailChannel,
    PopupChannel,
    PushChannel,
    push_from_popup,
    SMSChannel,
)
from webapps.notification.enums import (
    NotificationCategory,
    NotificationGroup,
    NotificationIcon,
    NotificationSize,
    NotificationTarget,
)
from webapps.notification.models import NotificationHistory
from webapps.notification.recipients import (
    Managers,
    NotificationCustomer,
    NotificationCustomerUser,
    Reception,
    SystemSender,
)
from webapps.pos.enums import PaymentTypeEnum, receipt_status
from webapps.pos.models import TransactionRow
from webapps.pos.tools import get_receipt_data

if t.TYPE_CHECKING:
    # pylint: disable=ungrouped-imports, unused-import
    from webapps.pos.models import PaymentRow


logger = logging.getLogger('market_pay.notifications')


class KYCReceiptBaseNotification(BaseNotification):
    recipients = (NotificationCustomer,)
    sender = SystemSender
    channels = (EmailChannel,)

    def __init__(self, transaction, **parameters):
        super().__init__(transaction, **parameters)

        self.transaction = transaction
        self.business = transaction.pos.business
        self.customer = transaction.customer_card
        self.customer_user = transaction.customer

    @property
    def identity(self) -> str:
        return f'{self.notif_type},{self.transaction.id}'


class KYCBaseNotification(BaseNotification):
    recipients = (Managers,)
    sender = SystemSender

    def __init__(self, marketpay_notification, **parameters):
        super().__init__(marketpay_notification, **parameters)
        self.marketpay_notification = marketpay_notification
        self.business = marketpay_notification.account_holder.pos.business

    @property
    def identity(self) -> str:
        return f'{self.notif_type},{self.marketpay_notification.id}'


class PassedKYCNotification(KYCBaseNotification):
    category = NotificationCategory.MARKETPAY_PASSED_KYC
    channels = (PopupChannel, SMSChannel)
    target = NotificationTarget.PREPAYMENT_POPUP
    schedule_planner = WorkingHoursSchedulePlanner
    popup_template = PopupTemplate(
        group=NotificationGroup.PAYMENT,
        icon=NotificationIcon.MOBILE_PAYMENT,
        crucial=False,
        size=NotificationSize.NORMAL,
        relevance=3,
        messages=[
            _("Congrats! You're set up on Mobile Payments."),
            _('Now protect your bottom line.'),
            _('Set up deposits and cancellation fees.'),
        ],
    )
    sms_template = push_from_popup(popup_template)


class PassedKYCEmailNotification(KYCBaseNotification):
    category = NotificationCategory.MARKETPAY_PASSED_KYC
    channels = (EmailChannel,)
    email_template_name = 'KYC/KYC_passed'


class FirstFailedKYCNotification(KYCBaseNotification):
    category = NotificationCategory.MARKETPAY_FIRST_FAILED_KYC
    channels = (PopupChannel, SMSChannel)
    target = NotificationTarget.KYC_DASHBOARD
    schedule_planner = WorkingHoursSchedulePlanner
    popup_template = PopupTemplate(
        group=NotificationGroup.PAYMENT,
        icon=NotificationIcon.MOBILE_PAYMENT,
        crucial=False,
        size=NotificationSize.NORMAL,
        relevance=3,
        messages=[
            _("Your account wasn't verified."),
            _('Mobile Payments has not been activated.'),
            _('Visit your Payments Settings to confirm details.'),
        ],
    )
    sms_template = push_from_popup(popup_template)


class FirstFailedKYCEmailNotification(KYCBaseNotification):
    category = NotificationCategory.MARKETPAY_FIRST_FAILED_KYC
    channels = (EmailChannel,)
    email_template_name = defaultdict(
        lambda: 'KYC/KYC_first_failed',
        {
            Country.PL: 'KYC/KYC_first_failed_pl',
            Country.GB: 'KYC/KYC_first_failed_europe',
            Country.ES: 'KYC/KYC_first_failed_europe',
        },
    )[
        Country(settings.API_COUNTRY)  # pylint: disable=no-value-for-parameter
    ]


class SubsequentFailedKYCNotification(KYCBaseNotification):
    category = NotificationCategory.MARKETPAY_SUBSEQUENT_FAILED_KYC
    channels = (PopupChannel, SMSChannel)
    target = NotificationTarget.KYC_DASHBOARD
    schedule_planner = WorkingHoursSchedulePlanner
    popup_template = PopupTemplate(
        group=NotificationGroup.PAYMENT,
        icon=NotificationIcon.MOBILE_PAYMENT,
        crucial=False,
        size=NotificationSize.NORMAL,
        relevance=3,
        messages=[
            _('Your payouts and payments have been stopped.'),
            _('Please correct your bank account.'),
            _("If left unresolved we'll issue customer refunds."),
        ],
    )
    sms_template = push_from_popup(popup_template)


class SubsequentFailedKYCEmailNotification(KYCBaseNotification):
    category = NotificationCategory.MARKETPAY_SUBSEQUENT_FAILED_KYC
    channels = (EmailChannel,)
    email_template_name = defaultdict(
        lambda: 'KYC/KYC_subsequent_failed',
        {
            Country.PL: 'KYC/KYC_subsequent_failed_europe',
            Country.GB: 'KYC/KYC_subsequent_failed_europe',
            Country.ES: 'KYC/KYC_subsequent_failed_europe',
        },
    )[
        Country(settings.API_COUNTRY)  # pylint: disable=no-value-for-parameter
    ]


class PayoutReversedFailedNotification(KYCBaseNotification):
    category = NotificationCategory.MARKETPAY_PAYOUT_REVERSED
    channels = (PopupChannel, SMSChannel)
    target = NotificationTarget.KYC_DASHBOARD
    schedule_planner = WorkingHoursSchedulePlanner
    popup_template = PopupTemplate(
        group=NotificationGroup.PAYMENT,
        icon=NotificationIcon.MOBILE_PAYMENT,
        crucial=False,
        size=NotificationSize.NORMAL,
        relevance=3,
        messages=[
            _('Your recent payout has been reversed.'),
            _('Your Mobile Payments access is restricted.'),
            _('Please check the accuracy of your banking details.'),
        ],
    )
    sms_template = push_from_popup(popup_template)


class PayoutReversedFailedEmailNotification(KYCBaseNotification):
    category = NotificationCategory.MARKETPAY_PAYOUT_REVERSED
    channels = (EmailChannel,)
    email_template_name = defaultdict(
        lambda: 'KYC/KYC_payout_reversed',
        {
            Country.PL: 'KYC/KYC_payout_reversed_europe',
            Country.GB: 'KYC/KYC_payout_reversed_europe',
            Country.ES: 'KYC/KYC_payout_reversed_europe',
        },
    )[
        Country(settings.API_COUNTRY)  # pylint: disable=no-value-for-parameter
    ]

    def get_context(self):
        value = self.marketpay_notification.content['amount']['value']
        currency = self.marketpay_notification.content['amount']['currency']
        payout_amount = f'{value} {currency}'
        return {"payout_amount": payout_amount}


class MobilePaymentsRegainedNotification(KYCBaseNotification):
    category = NotificationCategory.MARKETPAY_MOBILE_PAYMENTS_REGAINED
    channels = (EmailChannel,)
    email_template_name = 'KYC/KYC_mobile_payments_regained'


class _BasePayoutNotification(BaseNotification):
    recipients = (Managers,)
    sender = SystemSender
    category = NotificationCategory.MARKETPAY_PAYOUT

    def __init__(self, business: Business, payout_data: dict | list):
        super().__init__(business, payout_data=payout_data)
        self.business = business
        self.payout_data = (
            PayoutData(**payout_data) if isinstance(payout_data, dict) else PayoutData(*payout_data)
        )
        self.psp = self.payout_data.psp_reference
        self.xlsx = None

    @property
    def identity(self) -> str:
        return f'{self.notif_type},psp:{self.psp}'


class PayoutReportEmailNotification(_BasePayoutNotification):
    channels = (EmailChannel,)
    email_template_name = 'KYC/KYC_payout'

    def get_context(self) -> dict:
        from webapps.statistics.spreadsheets.sales_payout_summary_report import (
            PayoutReport,
        )

        payout_report = PayoutReport(
            self.psp,
            self.business.id,
            get_business_label(self.business),
            get_language(),
            self.business.get_timezone(),
        )
        context = self._get_email_data(payout_report.payout_summary_data_rows)
        logger.debug('Payout email notification prepared data %s', context)
        self.xlsx = payout_report.run()
        return context

    def get_attachments(self):
        attachments = []
        if self.xlsx is not None:
            attachments = [
                (
                    gettext("payout_report.%(file_extension)s")
                    % {
                        'file_extension': 'xlsx',
                    },
                    self.xlsx,
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                ),
            ]
        return attachments

    def _get_email_data(self, payout_report_summary_data_rows):
        data_rows = {d['payment_type']: d for d in payout_report_summary_data_rows if d['total']}

        income_names = [
            _('Payments at checkout'),
            _('Cancellation Fee Charges'),
            _('Deposits'),
            _('Incoming Manual Fund Transfers'),
            _('Tips at checkout'),
            _('Tip deposits'),
        ]
        incomes = []
        costs = []
        for name in income_names:
            if name in data_rows:
                incomes.append(self._format_row(data_rows.pop(name)))
        for row in data_rows.values():
            costs.append(self._format_row(row))

        total = sum((d['total'] or 0) for d in incomes + costs)
        return {
            "incomes": incomes,
            "costs": costs,
            "total_amount_formatted": lib.tools.format_currency(total),
        }

    @staticmethod
    def _format_row(row):
        data = dict(row)
        data['total_formatted'] = lib.tools.format_currency(data['total'])
        return data


class PayoutMobileNotification(_BasePayoutNotification):
    channels = (PopupChannel, PushChannel)
    schedule_planner = WorkingHoursSchedulePlanner

    @property
    def popup_template(self) -> PopupTemplate:
        if self.first_payout:
            messages = [
                _("You've received your first payout!"),
                _("That means {payout_amount} is on its way."),
                _("It should be credited to your account within 2-5 business days."),
            ]
        else:
            messages = [
                _("A payout is on its way!"),
                _("{payout_amount} will be credited to your account soon."),
            ]

        return PopupTemplate(
            group=NotificationGroup.PAYMENT,
            icon=NotificationIcon.MOBILE_PAYMENT_PAYOUT,
            size=NotificationSize.BIG,
            messages=messages,
            crucial=False,
            relevance=1,
        )

    @property
    def push_template(self) -> str:
        return push_from_popup(self.popup_template)

    @cached_property
    def first_payout(self) -> bool:
        payouts_count = Payout.objects.filter(
            account_holder_id=self.payout_data.account_holder_id
        ).count()
        return payouts_count == 1

    def get_context(self) -> dict:
        amount = abs(Decimal(self.payout_data.amount or 0))
        return {"payout_amount": lib.tools.format_currency(amount)}

    def get_target(self):
        return PushTarget(type=NotificationTarget.PAYOUT_DASHBOARD.value)


class _BasePaymentBusinessNotification(BaseNotification):
    sender = SystemSender
    recipients = (Managers, Reception)

    def __init__(self, payment_row: 'PaymentRow', **parameters):
        super().__init__(payment_row, **parameters)
        self.payment_row = payment_row
        self.business = payment_row.receipt.transaction.pos.business
        self.transaction = self.payment_row.receipt.transaction

    @property
    def identity(self) -> str:
        return f'{self.notif_type},{self.payment_row.id}'

    def get_target(self):
        return PushTarget(
            type=NotificationTarget.TRANSACTION,
            id=self.payment_row.receipt.transaction_id,
        )


class PaymentContext(Context):
    def get_context(self):
        payment_row = self.notification.payment_row
        client = self.notification.transaction.customer
        created = payment_row.created.astimezone(self.notification.business.get_timezone())
        transaction_date = format_datetime(created, 'date_ymd', get_language())
        transaction_time = format_datetime(created, 'time_hm', get_language())
        service_name = self._get_service_name(payment_row.receipt.transaction_id)

        appointment = self.notification.transaction.appointment
        booked_from = appointment.booked_from
        booking_date = format_datetime(booked_from, 'date_ymd', get_language())
        booking_time = format_datetime(booked_from, 'time_hm', get_language())
        return {
            "amount": lib.tools.format_currency(payment_row.amount),
            "client_name": client.full_name,
            "transaction_type": payment_row.payment_type.label,
            "transaction_date": transaction_date,
            "transaction_time": transaction_time,
            "service_name": service_name,
            "booking_date": booking_date,
            "booking_time": booking_time,
            "booking_reference": appointment.id,
            "customer_email": client.email,
            "customer_phone": appointment.customer_phone,
        }

    @staticmethod
    def _get_service_name(transaction_id):
        names = (
            TransactionRow.objects.filter(
                transaction_id=transaction_id,
            )
            .values_list(
                'subbooking__service_variant__service__name',
                'subbooking__service_name',
                'service_variant__service__name',
                'product__name',
            )
            .last()
            or []
        )
        first_name = next(filter(None, names), None)
        return first_name


class BusinessRefundContext(Context):
    def get_context(self):
        def get_name(transaction_row):
            name = None
            if transaction_row.subbooking:
                name = (
                    transaction_row.subbooking.service_variant.service.name
                    if transaction_row.subbooking.service_variant
                    else transaction_row.subbooking.service_name
                )
            elif transaction_row.service_variant:
                name = transaction_row.service_variant.service.name
            elif transaction_row.product:
                name = transaction_row.product.name

            return name

        services = list(
            filter(
                None, map(get_name, self.notification.payment_row.receipt.transaction.rows.all())
            )
        )

        amount = self.notification.payment_row.amount
        operation_fee = self.notification.payment_row.operation_fees.first()
        fee_amount = lib.tools.major_unit(operation_fee.amount)
        total_amount = amount + fee_amount

        return {
            "business_owner_name": self.notification.business.owner.full_name,
            "services": services,
            "refund_fee": lib.tools.format_currency(fee_amount),
            "total_amount": lib.tools.format_currency(total_amount),
        }


class RefundBusinessEmailNotification(_BasePaymentBusinessNotification):
    category = NotificationCategory.MARKETPAY_BUSINESS_REFUND
    contexts = (BusinessRefundContext, PaymentContext)
    channels = (EmailChannel,)
    email_template_name = 'KYC/KYC_business_refund'
    recipients = (Managers,)
    sender = SystemSender


class RefundBusinessMobileNotification(_BasePaymentBusinessNotification):
    channels = (PopupChannel, PushChannel)
    contexts = (BusinessRefundContext, PaymentContext)
    schedule_planner = WorkingHoursSchedulePlanner
    category = NotificationCategory.MARKETPAY_BUSINESS_REFUND

    popup_template = PopupTemplate(
        group=NotificationGroup.PAYMENT,
        icon=NotificationIcon.MOBILE_PAYMENT_WARNING,
        size=NotificationSize.NORMAL,
        messages=[
            _("You've refunded {total_amount} charge"),
            '{transaction_type} • {transaction_date} • {transaction_time}',
            '{client_name} • {service_name}',
        ],
        crucial=False,
        relevance=1,
    )
    push_template = push_from_popup(popup_template)


class ChargebackReceivedBusinessMobileNotification(_BasePaymentBusinessNotification):
    channels = (PopupChannel, PushChannel)
    contexts = (PaymentContext,)
    category = NotificationCategory.MARKETPAY_CHARGEBACK

    popup_template = PopupTemplate(
        group=NotificationGroup.PAYMENT,
        icon=NotificationIcon.MOBILE_PAYMENT_WARNING,
        size=NotificationSize.NORMAL,
        messages=[
            _('A {amount} chargeback has been processed'),
            '{transaction_type} • {transaction_date} • {transaction_time}',
            '{client_name} • {service_name}',
        ],
        crucial=False,
        relevance=1,
    )
    push_template = push_from_popup(popup_template)


class ChargebackReversedBusinessNotification(_BasePaymentBusinessNotification):
    category = NotificationCategory.MARKETPAY_CHARGEBACK_REVERSED
    channels = (EmailChannel,)
    contexts = (PaymentContext,)
    email_template_name = 'KYC/KYC_chargeback_reversed'
    recipients = (Managers,)
    sender = SystemSender

    def __init__(self, payment_row, mp_notification):
        super().__init__(payment_row, mp_notification=mp_notification)
        self.mp_notification = mp_notification

    @property
    def identity(self) -> str:
        return f'{self.notif_type},{self.payment_row.id}'

    @staticmethod
    def _get_service_name(transaction_id):
        names = (
            TransactionRow.objects.filter(
                transaction_id=transaction_id,
            )
            .values_list(
                'subbooking__service_variant__service__name',
                'subbooking__service_name',
                'service_variant__service__name',
                'product__name',
            )
            .last()
            or []
        )
        first_name = next(filter(None, names), None)
        return first_name

    def get_context(self) -> dict:
        return {
            "reason_code": (self.mp_notification['additionalData']['chargebackReasonCode']),
            "card_scheme": self.mp_notification['paymentMethod'],
            "dispute_psp_reference": self.mp_notification['pspReference'],
        }


class ChargebackReversedBusinessMobileNotification(_BasePaymentBusinessNotification):
    channels = (PopupChannel, PushChannel)
    contexts = (PaymentContext,)
    category = NotificationCategory.MARKETPAY_CHARGEBACK_REVERSED

    popup_template = PopupTemplate(
        group=NotificationGroup.PAYMENT,
        icon=NotificationIcon.MOBILE_PAYMENT_WARNING,
        size=NotificationSize.NORMAL,
        messages=[
            _('A {amount} chargeback has been reversed'),
            '{transaction_type} • {transaction_date} • {transaction_time}',
            '{client_name} • {service_name}',
        ],
        crucial=False,
        relevance=1,
    )
    push_template = push_from_popup(popup_template)


class SecondChargebackBusinessNotification(
    ChargebackReversedBusinessNotification,
):
    category = NotificationCategory.MARKETPAY_SECOND_CHARGEBACK
    email_template_name = 'KYC/KYC_second_chargeback'


class SecondChargebackBusinessMobileNotification(_BasePaymentBusinessNotification):
    channels = (PopupChannel, PushChannel)
    contexts = (PaymentContext,)
    category = NotificationCategory.MARKETPAY_SECOND_CHARGEBACK

    popup_template = PopupTemplate(
        group=NotificationGroup.PAYMENT,
        icon=NotificationIcon.MOBILE_PAYMENT_WARNING,
        size=NotificationSize.NORMAL,
        messages=[
            _('A {amount} chargeback has been upheld'),
            '{transaction_type} • {transaction_date} • {transaction_time}',
            '{client_name} • {service_name}',
        ],
        crucial=False,
        relevance=1,
    )
    push_template = push_from_popup(popup_template)


class PaymentCompletedNotification(KYCReceiptBaseNotification):
    category = NotificationCategory.MARKETPAY_PAYMENT_COMPLETED
    email_template_name = 'KYC/KYC_payment_completed'

    def get_context(self) -> dict:
        template_args, _ = get_receipt_data(
            self.transaction.pos,
            self.transaction,
            NotificationHistory.SENDER_CUSTOMER,
            get_language(),
        )

        return template_args


class CancellationFeeChargeNotification(PaymentCompletedNotification):
    category = NotificationCategory.MARKETPAY_CANCELLATION_FEE_CHARGE
    email_template_name = 'KYC/KYC_cancellation_fee_charge'


class RefundCustomerNotification(BaseNotification):
    category = NotificationCategory.MARKETPAY_CUSTOMER_REFUND
    recipients = (NotificationCustomerUser,)
    sender = SystemSender
    channels = (EmailChannel, PushChannel)
    email_template_name = 'KYC/KYC_customer_refund'
    push_template = _('{business_name} issued a {amount} refund for your recent booking.')

    @property
    def identity(self) -> str:
        return f'{self.notif_type},{self.payment_row.id}'

    def get_context(self) -> dict:
        pos = self.payment_row.receipt.transaction.pos
        transaction = self.payment_row.receipt.transaction
        user = transaction.customer_card.user
        language = user.customer_profile.language
        sender = NotificationHistory.SENDER_CUSTOMER
        data, _ = get_receipt_data(pos, transaction, sender, language)
        business = pos.business
        data.update(
            {
                "business_id": business.id,
                "business_name": business.name,
                "user_id": user.id,
                "user_email": user.email,
                "user_language": language,
                "card_last_digits": self.payment_row.card_last_digits,
                "amount": lib.tools.format_currency(self.payment_row.amount),
            }
        )

        return data

    def __init__(self, payment_row, **parameters):
        self.payment_row = payment_row
        self.business = payment_row.receipt.transaction.pos.business
        self.customer = payment_row.receipt.transaction.customer
        self.customer_card = payment_row.receipt.transaction.customer_card
        super().__init__(payment_row, **parameters)

    def get_target(self):
        return PushTarget(
            type='booking', id=self.payment_row.receipt.transaction.appointment.subbookings[0].id
        )


class CustomerNotificationHandler:
    @classmethod
    def handle_notification(cls, payment_row: 'PaymentRow'):
        # (status, payment_type)
        handlers = {
            (
                receipt_status.PAYMENT_SUCCESS,
                PaymentTypeEnum.PAY_BY_APP,
            ): PaymentCompletedNotification,
            (
                receipt_status.PREPAYMENT_SUCCESS,
                PaymentTypeEnum.PREPAYMENT,
            ): PaymentCompletedNotification,
            (
                receipt_status.DEPOSIT_CHARGE_SUCCESS,
                PaymentTypeEnum.PAY_BY_APP,
            ): CancellationFeeChargeNotification,
            (
                receipt_status.BOOKSY_PAY_SUCCESS,
                PaymentTypeEnum.BOOKSY_PAY,
            ): PaymentCompletedNotification,
        }
        notification = handlers.get(
            (payment_row.status, payment_row.payment_type.code),
        )
        if notification:
            notification(payment_row.receipt.transaction).send()
