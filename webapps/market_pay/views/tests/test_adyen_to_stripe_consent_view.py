import datetime

from django.test import override_settings
from freezegun import freeze_time
from mock.mock import patch
from model_bakery import baker
from rest_framework import status
from rest_framework.reverse import reverse

from country_config.enums import Country
from drf_api.lib.base_drf_test_case import BusinessOwnerAPITestCase
from lib.feature_flag.feature import (
    AdyenToStripeMaxNumberOfConsentsStageI,
    AdyenToStripeMigrationConsentDeadlineFlag,
)
from lib.feature_flag.killswitch import (
    DisableAdyenToStripeMigrationConsent,
    DisableAdyenToStripeMigrationConsentStageII,
)
from lib.test_utils import BaseTestUtilMixin
from lib.tests.utils import override_feature_flag
from service.tests import dict_assert
from webapps.business.models import Business, Resource
from webapps.experiment_v3.exp import ConsentAdyenStripeCalendarTextExperiment
from webapps.market_pay.enums import AdyenToStripeMigrationConsentStage
from webapps.market_pay.models import AccountHolder, AdyenToStripeMigrationConsent, ConsentStatus
from webapps.pos.baker_recipes import pos_recipe
from webapps.user.models import User


class AdyenToStripeConsentViewBaseTestCase(
    BaseTestUtilMixin,
    BusinessOwnerAPITestCase,
):
    WITH_EXPERIMENT = False

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        with patch.dict(
            ConsentAdyenStripeCalendarTextExperiment.config,
            {'active': cls.WITH_EXPERIMENT},
        ):
            ConsentAdyenStripeCalendarTextExperiment.initialize()
        cls.business = baker.make(
            Business,
            time_zone_name=cls.get_time_zone_name(),
        )
        cls.user = baker.make(User)
        cls.business.pos = pos_recipe.make(
            business=cls.business,
        )

    def url(self, business_id=None):
        if business_id is None:
            business_id = self.business.id
        return reverse(
            'adyen_to_stripe_consent',
            kwargs={
                'business_pk': business_id,
            },
        )

    def _standard_get_check(self, json_data):
        assert set(json_data.keys()).issuperset(
            {
                'variant',
                'consent_status',
                'show_adyen_to_stripe_consent',
                'deadline',
            }
        )

    @classmethod
    def _create_owner_resource(cls):
        baker.make(
            Resource,
            staff_user=cls.user,
            business=cls.business,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_OWNER,
        )

    @classmethod
    def _create_adyen_account_with_kyc(cls):
        baker.make(
            AccountHolder,
            ever_passed_kyc=True,
            pos=cls.business.pos,
        )

    def _test_post_201_agreed(self, agreed: bool):
        response = self.client.post(
            self.url(),
            data={
                'agreed': agreed,
            },
        )
        assert response.status_code == status.HTTP_201_CREATED

        consent = AdyenToStripeMigrationConsent.objects.filter(
            business=self.business,
            agreed=agreed,
            created__isnull=False,
            ip_address__isnull=False,
            approver=self.user,
        ).first()
        assert consent.created.tzname() == 'UTC'
        assert consent.stage == AdyenToStripeMigrationConsentStage.STAGE_I

    def _standard_get(self) -> dict:
        response = self.client.get(self.url())
        assert response.status_code == status.HTTP_200_OK
        json_data = response.json()
        self._standard_get_check(json_data)
        return json_data


class AdyenToStripeConsentViewTestCase(AdyenToStripeConsentViewBaseTestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls._create_owner_resource()
        cls._create_adyen_account_with_kyc()

    def test_get_200(self):
        json_data = self._standard_get()
        assert json_data['variant'] is None
        assert json_data['show_adyen_to_stripe_consent'] is True
        assert json_data['consent_status'] == ConsentStatus.CONSENT_NEEDED
        assert json_data['deadline'] is None

    def test_get_200_max_3_times(self):
        json_data = self._standard_get()
        assert json_data['variant'] is None
        assert json_data['show_adyen_to_stripe_consent'] is True
        assert json_data['consent_status'] == ConsentStatus.CONSENT_NEEDED
        self._test_post_201_agreed(False)
        self._test_post_201_agreed(False)
        with freeze_time(self.business.tznow + datetime.timedelta(hours=12, minutes=1)):
            json_data = self._standard_get()
        assert json_data['show_adyen_to_stripe_consent'] is True
        assert json_data['consent_status'] == ConsentStatus.CONSENT_NEEDED
        self._test_post_201_agreed(False)
        with freeze_time(self.business.tznow + datetime.timedelta(hours=36)):  # just a lot later
            json_data = self._standard_get()
        assert json_data['show_adyen_to_stripe_consent'] is False
        assert json_data['consent_status'] == ConsentStatus.CONSENT_NEEDED

        with (
            override_feature_flag({AdyenToStripeMaxNumberOfConsentsStageI.flag_name: 5}),
            freeze_time(self.business.tznow + datetime.timedelta(hours=36)),
        ):
            json_data = self._standard_get()
        assert json_data['show_adyen_to_stripe_consent'] is True
        assert json_data['consent_status'] == ConsentStatus.CONSENT_NEEDED
        self._test_post_201_agreed(False)

    def test_get_200_show_adyen_to_stripe_consent_true_modified_limit(self):
        self._test_post_201_agreed(False)
        self._test_post_201_agreed(False)
        self._test_post_201_agreed(False)

        with freeze_time(self.business.tznow + datetime.timedelta(hours=36)):
            json_data = self._standard_get()
        assert json_data['show_adyen_to_stripe_consent'] is False
        assert json_data['consent_status'] == ConsentStatus.CONSENT_NEEDED

        with (
            override_feature_flag({AdyenToStripeMaxNumberOfConsentsStageI.flag_name: 5}),
            freeze_time(self.business.tznow + datetime.timedelta(hours=36)),
        ):
            json_data = self._standard_get()
        assert json_data['show_adyen_to_stripe_consent'] is True
        assert json_data['consent_status'] == ConsentStatus.CONSENT_NEEDED

        self._test_post_201_agreed(False)
        with (
            override_feature_flag({AdyenToStripeMaxNumberOfConsentsStageI.flag_name: 5}),
            freeze_time(self.business.tznow + datetime.timedelta(hours=36)),
        ):
            json_data = self._standard_get()
        assert json_data['show_adyen_to_stripe_consent'] is True
        assert json_data['consent_status'] == ConsentStatus.CONSENT_NEEDED

        self._test_post_201_agreed(False)
        with (
            override_feature_flag({AdyenToStripeMaxNumberOfConsentsStageI.flag_name: 5}),
            freeze_time(self.business.tznow + datetime.timedelta(hours=36)),
        ):
            json_data = self._standard_get()
        assert json_data['show_adyen_to_stripe_consent'] is False
        assert json_data['consent_status'] == ConsentStatus.CONSENT_NEEDED

    def test_post_201_agreed(self):
        self._test_post_201_agreed(True)

    def test_post_201_not_agreed(self):
        self._test_post_201_agreed(False)

    def test_post_400_bad_request(self):
        response = self.client.post(self.url(), data={})
        assert response.status_code == status.HTTP_400_BAD_REQUEST

        assert not AdyenToStripeMigrationConsent.objects.filter(
            business=self.business,
            approver=self.user,
        ).exists()

    def test_super_long_ip_v4_header(self):
        response = self.client.post(
            self.url(),
            data={
                'agreed': True,
            },
            HTTP_X_FORWARDED_FOR='***************, *************',  # value from test instance
        )
        assert response.status_code == status.HTTP_201_CREATED

        assert AdyenToStripeMigrationConsent.objects.filter(
            business=self.business,
            approver=self.user,
        ).exists()

    def test_super_long_ip_v6_header(self):
        response = self.client.post(
            self.url(),
            data={
                'agreed': True,
            },
            HTTP_X_FORWARDED_FOR='2001:db8:85a3:8d3:1319:8a2e:370:7348,'
            '*************,'
            '***************',
        )
        assert response.status_code == status.HTTP_201_CREATED

        assert AdyenToStripeMigrationConsent.objects.filter(
            business=self.business,
            approver=self.user,
        ).exists()


class AdyenToStripeConsentViewWithExperimentBaseTestMixin:
    def test_get_200(self):
        json_data = self._standard_get()
        assert json_data['variant'] in ConsentAdyenStripeCalendarTextExperiment.Variants.values()
        assert json_data['show_adyen_to_stripe_consent'] is True
        assert json_data['consent_status'] == ConsentStatus.CONSENT_NEEDED
        assert json_data['deadline'] is None

    @override_feature_flag({AdyenToStripeMigrationConsentDeadlineFlag.flag_name: 'hehehempgruxd'})
    def test_get_200_with_invalid_deadline(self):
        json_data = self._standard_get()
        assert json_data['variant'] in ConsentAdyenStripeCalendarTextExperiment.Variants.values()
        assert json_data['show_adyen_to_stripe_consent'] is True
        assert json_data['consent_status'] == ConsentStatus.CONSENT_NEEDED
        assert json_data['deadline'] is None

    @override_feature_flag({AdyenToStripeMigrationConsentDeadlineFlag.flag_name: '2022-12-31'})
    def test_get_200_with_valid_deadline(self):
        json_data = self._standard_get()
        assert json_data['variant'] in ConsentAdyenStripeCalendarTextExperiment.Variants.values()
        assert json_data['show_adyen_to_stripe_consent'] is True
        assert json_data['consent_status'] == ConsentStatus.CONSENT_NEEDED
        assert json_data['deadline'] == '2022-12-31'

    @override_feature_flag({DisableAdyenToStripeMigrationConsent.flag_name: True})
    def test_get_200_false_killswitch_true(self):
        json_data = self._standard_get()
        assert json_data['variant'] is None
        assert json_data['show_adyen_to_stripe_consent'] is False
        assert json_data['consent_status'] == ConsentStatus.CONSENT_NOT_NEEDED

    @override_feature_flag({DisableAdyenToStripeMigrationConsent.flag_name: False})
    def test_get_200_true_killswitch_false(self):
        json_data = self._standard_get()
        assert json_data['variant'] in ConsentAdyenStripeCalendarTextExperiment.Variants.values()
        assert json_data['show_adyen_to_stripe_consent'] is True
        assert json_data['consent_status'] == ConsentStatus.CONSENT_NEEDED

    @override_feature_flag(
        {
            DisableAdyenToStripeMigrationConsent.flag_name: False,
            DisableAdyenToStripeMigrationConsentStageII.flag_name: True,
        }
    )
    def test_get_200_true_killswitch_false_stageii_killed(self):
        json_data = self._standard_get()
        assert json_data['variant'] in ConsentAdyenStripeCalendarTextExperiment.Variants.values()
        assert json_data['show_adyen_to_stripe_consent'] is True
        assert json_data['consent_status'] == ConsentStatus.CONSENT_NEEDED

    @override_settings(API_COUNTRY=Country.PL)
    def test_get_200_another_country_than_united_states(self):
        json_data = self._standard_get()
        assert json_data['variant'] is None
        assert json_data['show_adyen_to_stripe_consent'] is False
        assert json_data['consent_status'] == ConsentStatus.CONSENT_NOT_NEEDED

    def test_get_200_with_previous_consent(self):
        baker.make(
            AdyenToStripeMigrationConsent,
            business=self.business,
            approver=self.user,
            agreed=True,
        )

        json_data = self._standard_get()
        assert json_data['variant'] in ConsentAdyenStripeCalendarTextExperiment.Variants.values()
        assert json_data['show_adyen_to_stripe_consent'] is False
        assert json_data['consent_status'] == ConsentStatus.ALREADY_AGREED

    def test_get_200_with_previous_consent_disagree(self):
        baker.make(
            AdyenToStripeMigrationConsent,
            business=self.business,
            approver=self.user,
            agreed=False,
        )

        json_data = self._standard_get()
        assert json_data['variant'] in ConsentAdyenStripeCalendarTextExperiment.Variants.values()
        assert json_data['show_adyen_to_stripe_consent'] is False
        assert json_data['consent_status'] == ConsentStatus.CONSENT_NEEDED

    def test_get_200_variant_a(self):
        self._test_get_with_variant(ConsentAdyenStripeCalendarTextExperiment.Variants.VARIANT_A)

    def test_get_200_variant_b(self):
        self._test_get_with_variant(ConsentAdyenStripeCalendarTextExperiment.Variants.VARIANT_B)

    def test_get_200_variant_c(self):
        self._test_get_with_variant(ConsentAdyenStripeCalendarTextExperiment.Variants.VARIANT_C)

    def _test_get_with_variant(self, variant: ConsentAdyenStripeCalendarTextExperiment.Variants):
        ConsentAdyenStripeCalendarTextExperiment(self.business.id).set_variant(variant)
        json_data = self._standard_get()
        assert json_data['variant'] == variant
        assert json_data['show_adyen_to_stripe_consent'] is True
        assert json_data['consent_status'] == ConsentStatus.CONSENT_NEEDED

    def test_post_201_agreed(self):
        self._test_post_201_agreed(True)

    def test_post_201_not_agreed(self):
        self._test_post_201_agreed(False)


class AdyenToStripeConsentViewWithExperimentResourceOwnerTestCase(
    AdyenToStripeConsentViewBaseTestCase,
    AdyenToStripeConsentViewWithExperimentBaseTestMixin,
):
    WITH_EXPERIMENT = True

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls._create_owner_resource()
        cls._create_adyen_account_with_kyc()


class AdyenToStripeConsentViewWithExperimentOwnerTestCase(
    AdyenToStripeConsentViewBaseTestCase,
    AdyenToStripeConsentViewWithExperimentBaseTestMixin,
):
    WITH_EXPERIMENT = True

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls._create_adyen_account_with_kyc()
        cls.business.owner = cls.user
        cls.business.save()


class AdyenToStripeConsentViewWithExperimentResourceOwnerAndOwnerTestCase(
    AdyenToStripeConsentViewBaseTestCase,
    AdyenToStripeConsentViewWithExperimentBaseTestMixin,
):
    WITH_EXPERIMENT = True

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls._create_owner_resource()
        cls._create_adyen_account_with_kyc()
        cls.business.owner = cls.user
        cls.business.save()


class AdyenToStripeConsentViewPermissionsTestCase(AdyenToStripeConsentViewBaseTestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls._create_adyen_account_with_kyc()

    def test_get_200(self):
        json_data = self._standard_get()
        assert json_data['variant'] is None
        assert json_data['show_adyen_to_stripe_consent'] is False
        assert json_data['consent_status'] == ConsentStatus.BAD_USER

    def test_post_403_bad_business(self):
        response = self.client.post(
            self.url(),
            data={
                'agreed': True,
            },
        )
        assert response.status_code == status.HTTP_403_FORBIDDEN
        dict_assert(
            response.json(),
            {
                'detail': 'You do not have permission to perform this action.',
            },
        )

    def test_get_200_non_existing_business(self):
        response = self.client.get(self.url(123123212332112442325))  # non existing business id
        assert response.status_code == status.HTTP_200_OK
        json_data = response.json()
        self._standard_get_check(json_data)
        assert json_data['variant'] is None
        assert json_data['show_adyen_to_stripe_consent'] is False
        assert json_data['consent_status'] == ConsentStatus.BAD_USER

    def test_post_non_existing_bad_business(self):
        response = self.client.post(
            self.url(123123212332112442325),  # non existing business id
            data={
                'agreed': True,
            },
        )
        assert response.status_code == status.HTTP_403_FORBIDDEN
        dict_assert(
            response.json(),
            {
                'detail': 'You do not have permission to perform this action.',
            },
        )


class AdyenToStripeConsentViewWithoutAdyenAccountTestCase(AdyenToStripeConsentViewBaseTestCase):
    WITH_EXPERIMENT = True

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls._create_owner_resource()

    def test_get_200(self):
        json_data = self._standard_get()
        assert json_data['variant'] in ConsentAdyenStripeCalendarTextExperiment.Variants.values()
        assert json_data['show_adyen_to_stripe_consent'] is False
        assert json_data['consent_status'] == ConsentStatus.CONSENT_NOT_NEEDED

    def test_post_201_agreed(self):  # it is still possible to agree whenever no one cares
        self._test_post_201_agreed(True)
