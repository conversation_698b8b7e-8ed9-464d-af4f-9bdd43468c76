import datetime

import pytest
from rest_framework.exceptions import ErrorDetail

from webapps.family_and_friends.enums import RelationshipType
from webapps.family_and_friends.serializers.member import (
    PersonSerializer,
    PetSerializer,
    VehicleSerializer,
)


@pytest.mark.django_db
def test_person_serializer():
    serializer = PersonSerializer(
        data=dict(
            first_name=None,
            last_name='<PERSON>',
            cell_phone=None,
            email=None,
            birthday='9990-12-12',
            is_booksy_user=True,
        )
    )
    assert not serializer.is_valid()
    assert serializer.errors == {
        'first_name': [ErrorDetail(string='This field is required.', code='required')]
    }


@pytest.mark.django_db
def test_pet_serializer():
    serializer = PetSerializer(
        data=dict(
            first_name=None,
            last_name=None,
            birthday='1990-12-12',
            is_booksy_user=True,
            weight='123.123',
            breed='',
            pet_type=None,
        )
    )
    assert not serializer.is_valid()
    assert serializer.errors == {
        'first_name': [ErrorDetail(string='This field is required.', code='required')]
    }


@pytest.mark.django_db
def test_vehicle_serializer():
    vehicle_data = dict(
        registration_number=None,
        vin_number='1234567891234567890',
        model='x5',
        relationship_type=RelationshipType.VEHICLE,
        year='9999',
    )
    serializer = VehicleSerializer(data=vehicle_data)
    assert not serializer.is_valid()
    assert serializer.errors == {
        'registration_number': ['This field is required.'],
        'vin_number': ['Ensure this field has no more than 17 characters.'],
        'year': [f'Ensure this value is less than or equal to {datetime.date.today().year}.'],
    }
