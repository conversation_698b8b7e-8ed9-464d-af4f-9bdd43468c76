import pytest

from webapps.notification.base import BaseNotification, NotificationCategory
from webapps.notification.channels import EmailChannel
from webapps.notification.recipients import SystemSender
from webapps.family_and_friends.appointment_notifications import FamilyAndFriendsRecipients
from webapps.family_and_friends.tests.utils import TestFamilyAndFriendsMixin
from webapps.public_partners.notifications import CmdAppointmentCancellationNotification


@pytest.mark.django_db
class TestFamilyAndFriendsRecipients(TestFamilyAndFriendsMixin):
    def setUp(self) -> None:
        # pylint: disable=protected-access
        self.prev_registry = BaseNotification._registry
        BaseNotification._registry = {}

        class TestNotification(BaseNotification):  # pylint: disable=unused-variable
            recipients = (FamilyAndFriendsRecipients,)
            channels = (EmailChannel,)
            sender = SystemSender
            category = NotificationCategory.BOOKING_CHANGED

            def __init__(self, appointment, **parameters):
                super().__init__(appointment, **parameters)
                self.appointment = appointment

        self.notification_class = TestNotification

    def tearDown(self) -> None:
        # pylint: disable=protected-access
        BaseNotification._registry = self.prev_registry

    def test_inactive_member(self):
        parent_bci, member_bci = self.create_inactive_member_bcis()
        appointment = self.create_appointment(parent_bci, member_bci)
        notification = self.notification_class(appointment)
        recipients_list = notification.resolved_recipients
        assert recipients_list[0].email == parent_bci.user.email
        assert len(recipients_list) == 1

    def test_active_member(self):
        parent_bci, member_bci = self.create_active_members_bcis()
        appointment = self.create_appointment(parent_bci, member_bci)
        notification = self.notification_class(appointment)
        recipients_list = notification.resolved_recipients
        assert recipients_list[0].email == parent_bci.user.email
        assert len(recipients_list) == 1

    def test_inactive_member_invisible_in_business(self):
        parent_bci, member_bci = self.create_inactive_member_bcis()
        appointment = self.create_appointment(parent_bci, member_bci)
        member_bci.visible_in_business = False
        notification = self.notification_class(appointment)
        recipients_list = notification.resolved_recipients
        self.assertEqual(recipients_list, [])


@pytest.mark.django_db
class TestCmdAppointmentCancellationNotification(TestFamilyAndFriendsMixin):
    def test_inactive_member_appointment(self):
        parent_bci, member_bci = self.create_inactive_member_bcis()
        appointment = self.create_appointment(parent_bci, member_bci)
        notification = CmdAppointmentCancellationNotification(appointment)
        assert len(notification.resolved_recipients) == 2
        assert notification.resolved_recipients[0].email == ''
        assert notification.resolved_recipients[1].email == parent_bci.user.email

    def test_active_member_appointment(self):
        parent_bci, member_bci = self.create_active_members_bcis()
        appointment = self.create_appointment(parent_bci, member_bci)
        notification = CmdAppointmentCancellationNotification(appointment)
        assert len(notification.resolved_recipients) == 2
        assert notification.resolved_recipients[0].email == member_bci.user.email
        assert notification.resolved_recipients[1].email == parent_bci.user.email
