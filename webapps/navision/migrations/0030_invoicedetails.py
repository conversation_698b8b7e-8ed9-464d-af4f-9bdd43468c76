# Generated by Django 4.0.4 on 2022-07-06 09:58

import django.contrib.postgres.fields
from django.db import migrations, models
import django.db.models.deletion
import webapps.navision.models
import webapps.navision.models.invoice_details


class Migration(migrations.Migration):

    dependencies = [
        ('purchase', '0118_subscriptionbuyer_vat_registered'),
        ('navision', '0029_navisionsettings_enable_invoice_details_editing'),
    ]

    operations = [
        migrations.CreateModel(
            name='InvoiceDetails',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'status',
                    models.CharField(
                        choices=[('W', 'Waiting for review'), ('A', 'Approved'), ('R', 'Rejected')],
                        default=webapps.navision.models.invoice_details.InvoiceDetails.Status[
                            'APPROVED'
                        ],
                        max_length=1,
                    ),
                ),
                ('active_from', models.DateTimeField(editable=False, null=True, unique=True)),
                ('tax_id', models.CharField(blank=True, editable=False, max_length=20, null=True)),
                ('entity_name', models.CharField(editable=False, max_length=100)),
                (
                    'invoice_emails',
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.EmailField(max_length=254),
                        blank=True,
                        editable=False,
                        null=True,
                        size=5,
                    ),
                ),
                ('address_details', models.CharField(editable=False, max_length=100)),
                ('zip_code', models.CharField(editable=False, max_length=20)),
                (
                    'state_abbrev',
                    models.CharField(blank=True, editable=False, max_length=30, null=True),
                ),
                ('city', models.CharField(editable=False, max_length=255)),
                (
                    'country_code',
                    models.CharField(
                        choices=[
                            ('br', 'Brasil'),
                            ('ca', 'Canada'),
                            ('es', 'Spain'),
                            ('fr', 'France'),
                            ('gb', 'Great Britain'),
                            ('ie', 'Ireland'),
                            ('mx', 'Mexico'),
                            ('pl', 'Poland'),
                            ('us', 'United States'),
                            ('za', 'South Africa'),
                            ('ar', 'Argentina'),
                            ('au', 'Australia'),
                            ('cl', 'Chile'),
                            ('co', 'Colombia'),
                            ('pt', 'Portugal'),
                            ('de', 'Germany'),
                            ('in', 'India'),
                            ('it', 'Italy'),
                            ('my', 'Malaysia'),
                            ('ng', 'Nigeria'),
                            ('nl', 'Netherlands'),
                            ('se', 'Sweden'),
                            ('sg', 'Singapore'),
                            ('ru', 'Russia'),
                        ],
                        editable=False,
                        max_length=2,
                    ),
                ),
                (
                    'buyer',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='invoice_details_history',
                        to='purchase.subscriptionbuyer',
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
            managers=[
                ('objects', webapps.navision.models.invoice_details.InvoiceDetailsManager()),
            ],
        ),
    ]
