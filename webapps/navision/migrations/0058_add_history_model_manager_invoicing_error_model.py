# Generated by Django 4.1.7 on 2023-06-23 11:38

from django.db import migrations, models
import django.db.models.deletion
import webapps.billing.models.managers


class Migration(migrations.Migration):
    dependencies = [
        ('user', '0062_userinternaldata_account_deletion_execution_method'),
        ('navision', '0057_invoicingsummary_service_invoicingsummary_source'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='invoicingerror',
            options={},
        ),
        migrations.AlterModelManagers(
            name='invoicingerror',
            managers=[
                ('objects', webapps.billing.models.managers.AutoUpdateAndAutoAddHistoryManager()),
            ],
        ),
        migrations.AddField(
            model_name='invoicingerror',
            name='is_fixed',
            field=models.BooleanField(default=False),
        ),
        migrations.CreateModel(
            name='InvoicingErrorHistory',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                ('data', models.TextField()),
                ('metadata', models.TextField()),
                (
                    'model',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name='history',
                        to='navision.invoicingerror',
                    ),
                ),
                (
                    'operator',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to='user.user',
                    ),
                ),
            ],
            options={
                'verbose_name': 'Invoicing error history',
                'verbose_name_plural': 'Invoicing error history',
            },
        ),
    ]
