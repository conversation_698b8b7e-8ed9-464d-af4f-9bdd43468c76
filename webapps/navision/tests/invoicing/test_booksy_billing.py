import uuid
from datetime import datetime, timedelta
from decimal import Decimal
from unittest.mock import Mock, patch, call

from django.db.models import Q
from django.test import TestCase, override_settings
from django.utils.timezone import make_aware
from freezegun import freeze_time
from model_bakery import baker
from parameterized import parameterized
from pytz import UTC

from lib.feature_flag.feature.navision import NavisionInvoiceBBSaaSPaidAfterMigrationFlag
from lib.tests.utils import override_eppo_feature_flag
from lib.tools import tznow
from service.billing.tests import gen_func
from webapps.billing.enums import (
    TransactionStatus,
    ProductType,
    PaymentProcessorType,
    TransactionSource,
)
from webapps.billing.models import (
    BillingTransaction,
    BillingCycleProductCharge,
    BillingSubscription,
    BillingCycle,
)
from webapps.business.models import Business
from webapps.navision.baker_recipes import navision_integration_enabled_recipe
from webapps.navision.enums import InvoicePaymentSource, InvoicePaymentSourceType, InvoiceService
from webapps.navision.invoicing.booksy_billing import BooksyBillingSaaSInvoiceFactory
from webapps.navision.models import (
    Invoice,
    InvoiceItem,
    InvoicingError,
    InvoicingSummary,
    Merchant,
    BusinessInvoiceSummary,
)
from webapps.navision.tasks.booksy_billing import create_new_billing_saas_invoices_task
from webapps.purchase.admin import invoice_saas_online
from webapps.purchase.models import SubscriptionBuyer, InvoiceAddress
from webapps.stripe_app.models import PaymentIntent
from webapps.structure.models import Region
from webapps.user.models import User

baker.generators.add('lib.interval.fields.IntervalField', gen_func)


@override_settings(
    NAVISION_SPLIT_INVOICES_BASED_ON_BILLING_CYCLE=False,
    SUPPORTED_INVOICE_PAYMENT_METHODS={
        Business.PaymentSource.BRAINTREE: {
            'SaaS',
            'Boost',
        },
        Business.PaymentSource.BRAINTREE_BILLING: {
            'SaaS',
            'Boost',
        },
    },
    NAVISION_BANK_CODES_PER_SOURCE={
        'O': 'Offline Bank',
        'B': 'Online Bank',
        'S': 'Online Bank',
    },
)
class TestBooksyBillingInvoicing(TestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        navision_integration_enabled_recipe.make(
            auto_run_saas_online_invoicing=True,
            charge_to_invoice_delay=0,
        )

    def setUp(self) -> None:
        self.merchant = baker.make(Merchant)
        self.buyer = baker.make(
            SubscriptionBuyer,
            merchant=self.merchant,
        )
        self.business = baker.make(
            Business,
            payment_source=Business.PaymentSource.BRAINTREE_BILLING,
            has_new_billing=True,
            status=Business.Status.PAID,
            buyer=self.buyer,
        )

    def check_errors(self):
        if InvoicingError.objects.exists():
            self.fail('\n'.join(error.error for error in InvoicingError.objects.all()))

    @staticmethod
    def create_charge(
        billing_cycle,
        product_type=ProductType.SAAS,
        unit_price=60.0,
        gross_unit_price=None,
        discounted_price=None,
        gross_discounted_price=None,
        sms_amount=0,
        usage_from=make_aware(datetime(2021, 8, 1), timezone=UTC),
        usage_to=make_aware(datetime(2021, 9, 1), timezone=UTC),
        quantity=1,
    ):
        if gross_unit_price is None:
            gross_unit_price = unit_price
        if discounted_price is None:
            discounted_price = unit_price
        if gross_discounted_price is None:
            gross_discounted_price = (
                discounted_price if gross_unit_price == unit_price else gross_unit_price
            )

        final_price = Decimal(discounted_price) * quantity
        gross_final_price = Decimal(gross_discounted_price) * quantity

        total_price = Decimal(unit_price) * quantity
        gross_total_price = Decimal(gross_unit_price) * quantity

        discount_granted = Decimal(total_price) - Decimal(final_price)
        gross_discount_granted = Decimal(gross_total_price) - Decimal(gross_final_price)

        charge = baker.make(
            BillingCycleProductCharge,
            product__product_type=product_type,
            product__sms_amount=sms_amount,
            usage_from=usage_from,
            usage_to=usage_to,
            billing_cycle=billing_cycle,
            unit_price=unit_price,
            gross_unit_price=gross_unit_price,
            discounted_price=discounted_price,
            gross_discounted_price=gross_discounted_price,
            discount_granted=discount_granted,
            gross_discount_granted=gross_discount_granted,
            final_price=final_price,
            gross_final_price=gross_final_price,
            total_price=total_price,
            gross_total_price=gross_total_price,
            quantity=quantity,
            currency='USD',
        )

        return charge

    def create_saas_transaction(self):
        external_id = str(uuid.uuid4())

        billing_cycle = baker.make(BillingCycle)

        transaction = baker.make(
            BillingTransaction,
            amount=60,
            payment_processor=PaymentProcessorType.STRIPE,
            business=self.business,
            status=TransactionStatus.CHARGED,
            external_id=external_id,
            billing_cycle=billing_cycle,
            currency='USD',
        )

        charge: BillingCycleProductCharge = self.create_charge(
            billing_cycle=billing_cycle,
            gross_unit_price=80,
        )

        return transaction, charge, billing_cycle

    @parameterized.expand(
        [
            ('𝐸𝑠𝑡𝑒𝑡𝑖𝑐 𝐿𝑖𝑝𝑠 𝐶𝑙𝑖𝑛𝑖𝑐 𝟡𝟡𝟳 💋', 'Estetic Lips Clinic 997'),
            ('𝓗𝓮𝓵𝓵𝓸 𝓾𝓼𝓮𝓻', 'Hello user'),
            ('   Łódka Masażysta 34   ', 'Łódka Masażysta 34'),
        ]
    )
    def test_invoice_header(self, business_name, clean_name):
        self.business.name = business_name
        self.business.save()

        _ = self.create_saas_transaction()

        create_new_billing_saas_invoices_task()

        self.assertEqual(1, Invoice.objects.count())
        invoice = Invoice.objects.first()
        self.assertEqual(clean_name, invoice.business_name)

    def test_saas_only_charge(self):
        transaction, charge, _ = self.create_saas_transaction()

        tax_additional_data = {'id': 1, 'area': 'zip', 'tax_area_code': '90210'}
        charge.tax_additional_data = tax_additional_data
        charge.save()

        create_new_billing_saas_invoices_task()

        self.assertEqual(InvoicingError.objects.count(), 0)
        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 1)

        invoice = Invoice.objects.first()
        item = invoice.items.first()
        summary = InvoicingSummary.objects.first()

        self.assertEqual(invoice.currency, 'USD')
        self.assertEqual(invoice.bank_code, 'Online Bank')
        self.assertEqual(invoice.source, InvoicePaymentSource.STRIPE)
        self.assertEqual(item.product, 'Booksy Subscription')
        self.assertEqual(item.service, Invoice.Service.SAAS)
        self.assertEqual(item.payment_source, InvoicePaymentSource.STRIPE)
        self.assertEqual(item.base_gross_value, charge.gross_unit_price)
        self.assertEqual(item.quantity, charge.quantity)
        self.assertEqual(item.charging_dt, transaction.created)
        self.assertEqual(item.billing_cycle_start, charge.usage_from)
        self.assertEqual(item.billing_cycle_end, charge.usage_to - timedelta(days=1))
        self.assertEqual(item.transaction_id, transaction.external_id)
        self.assertEqual(item.object_id, charge.id)
        self.assertTrue(item.charge_completed)
        self.assertDictEqual(item.tax_additional_data, tax_additional_data)
        self.assertEqual(summary.service, InvoiceService.SAAS)
        self.assertEqual(summary.source, InvoicePaymentSourceType.ONLINE)

    def test_exclude_business_if_source_is_not_billing_subscription(self):
        transaction, _, _ = self.create_saas_transaction()

        transaction.transaction_source = TransactionSource.BOOST_OVERDUE
        transaction.save()

        create_new_billing_saas_invoices_task()

        self.assertEqual(InvoicingError.objects.count(), 0)
        self.assertEqual(Invoice.objects.count(), 0)
        self.assertEqual(InvoiceItem.objects.count(), 0)

    def test_each_product_type_have_correct_caption(self):
        _, _, billing_cycle = self.create_saas_transaction()

        self.create_charge(
            billing_cycle=billing_cycle,
            product_type=ProductType.STAFFER_SAAS,
            unit_price=20,
        )

        self.create_charge(
            billing_cycle=billing_cycle,
            product_type=ProductType.POSTPAID_SMS,
            unit_price=0.1,
            quantity=200,
        )

        self.create_charge(
            billing_cycle=billing_cycle,
            product_type=ProductType.PREPAID_SMS,
            unit_price=10,
            sms_amount=300,
        )

        create_new_billing_saas_invoices_task()

        self.assertEqual(InvoicingError.objects.count(), 0)

        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 4)

        self.assertEqual(
            InvoiceItem.objects.filter(
                product='200 x SMS Delivery',
                base_gross_value=0.1 * 200,
            ).count(),
            1,
        )

        # from the create_saas_transaction method:
        self.assertEqual(
            InvoiceItem.objects.filter(
                product='Booksy Subscription',
                base_gross_value=80,
            ).count(),
            1,
        )

        self.assertEqual(
            InvoiceItem.objects.filter(
                product='Staffer',
                base_gross_value=20,
            ).count(),
            1,
        )

        self.assertEqual(
            InvoiceItem.objects.filter(
                product='SMS Package: 300',
                base_gross_value=10,
            ).count(),
            1,
        )

    def test_unsupported_stripe_accounts_are_excluded_from_invoicing(self):
        transaction, _, _ = self.create_saas_transaction()

        baker.make(
            PaymentIntent,
            account_id='some_id',
            stripe_id=transaction.external_id,
        )

        with override_settings(
            NAVISION_INVOICE_ONLY_SELECTED_ACCOUNTS=True,
            NAVISION_INVOICED_ACCOUNTS=set(),
        ):
            create_new_billing_saas_invoices_task()

            self.assertEqual(InvoicingError.objects.count(), 0)

            self.assertEqual(Invoice.objects.count(), 0)

        with override_settings(
            NAVISION_INVOICE_ONLY_SELECTED_ACCOUNTS=True,
            NAVISION_INVOICED_ACCOUNTS={
                'some_id',
            },
        ):
            create_new_billing_saas_invoices_task()

            self.assertEqual(InvoicingError.objects.count(), 0)

            self.assertEqual(Invoice.objects.count(), 1)

    def test_saas_sms_with_postpaid_charge(self):
        external_id = str(uuid.uuid4())
        billing_cycle = baker.make(BillingCycle)

        baker.make(
            BillingTransaction,
            amount=31,
            business=self.business,
            status=TransactionStatus.CHARGED,
            external_id=external_id,
            billing_cycle=billing_cycle,
            payment_processor=PaymentProcessorType.STRIPE,
            currency='USD',
        )

        self.create_charge(
            billing_cycle=billing_cycle,
            unit_price=30,
        )
        self.create_charge(
            billing_cycle=billing_cycle,
            product_type=ProductType.STAFFER_SAAS,
            unit_price=5,
            discounted_price=0,
            quantity=2,
        )

        sms_charge = self.create_charge(
            billing_cycle=billing_cycle,
            product_type=ProductType.POSTPAID_SMS,
            unit_price=0.005,
            quantity=200,
        )

        create_new_billing_saas_invoices_task()

        self.assertEqual(InvoicingError.objects.count(), 0)

        self.assertEqual(Invoice.objects.count(), 1)

        self.assertEqual(
            InvoiceItem.objects.count(),
            2,
            'Staffer row should be skipped, because this addon was free.'
            'If you send zero row to navision something weird is happening (would not recommend).',
        )
        self.assertEqual(InvoiceItem.objects.filter(service=Invoice.Service.SAAS).count(), 1)
        self.assertEqual(InvoiceItem.objects.filter(service=Invoice.Service.SMS).count(), 1)

        invoice = Invoice.objects.first()
        summary = InvoicingSummary.objects.first()

        self.assertEqual(invoice.source, InvoicePaymentSource.STRIPE)
        self.assertEqual(summary.service, InvoiceService.SAAS)
        self.assertEqual(summary.source, InvoicePaymentSourceType.ONLINE)

        saas_item = invoice.items.filter(service=Invoice.Service.SAAS).first()

        self.assertEqual(saas_item.base_gross_value, Decimal('30.0'))
        self.assertEqual(saas_item.payment_source, InvoicePaymentSource.STRIPE)
        self.assertEqual(saas_item.quantity, 1)
        self.assertEqual(saas_item.transaction_id, external_id)
        self.assertTrue(saas_item.charge_completed)

        sms_item = invoice.items.filter(service=Invoice.Service.SMS).first()

        self.assertEqual(sms_item.base_gross_value, Decimal('0.005') * 200)
        self.assertEqual(sms_item.payment_source, InvoicePaymentSource.STRIPE)
        self.assertEqual(sms_item.quantity, 1)
        self.assertEqual(sms_item.transaction_id, external_id)
        self.assertEqual(sms_item.billing_cycle_start, sms_charge.usage_from)
        self.assertEqual(sms_item.billing_cycle_end, sms_charge.usage_to - timedelta(days=1))
        self.assertTrue(saas_item.charge_completed)

    @override_settings(
        NAVISION_SPLIT_INVOICES_BASED_ON_BILLING_CYCLE=True,
    )
    def test_sms_and_saas_are_split(self):
        external_id = str(uuid.uuid4())
        billing_cycle = baker.make(BillingCycle)

        baker.make(
            BillingTransaction,
            amount=30,
            business=self.business,
            status=TransactionStatus.CHARGED,
            external_id=external_id,
            billing_cycle=billing_cycle,
            currency='USD',
        )
        self.create_charge(
            billing_cycle=billing_cycle,
            unit_price=25,
        )

        self.create_charge(
            billing_cycle=billing_cycle,
            product_type=ProductType.STAFFER_SAAS,
            unit_price=5,
            quantity=2,
        )

        self.create_charge(
            billing_cycle=billing_cycle,
            product_type=ProductType.POSTPAID_SMS,
            unit_price=0.006,
            usage_from=make_aware(datetime(2021, 7, 1), timezone=UTC),
            usage_to=make_aware(datetime(2021, 7, 1), timezone=UTC),
            quantity=200,
        )

        create_new_billing_saas_invoices_task()

        self.assertEqual(InvoicingError.objects.count(), 0)

        self.assertEqual(Invoice.objects.count(), 2)

        summary = InvoicingSummary.objects.first()
        operator = summary.operator
        self.assertEqual(summary.service, InvoiceService.SAAS)
        self.assertEqual(summary.source, InvoicePaymentSourceType.ONLINE)
        self.assertIsNotNone(operator)
        self.assertEqual(operator.username, 'admin')
        self.assertEqual(operator.email, '<EMAIL>')

        saas_item = InvoiceItem.objects.filter(
            service=InvoiceService.SAAS,
        ).first()

        staffer_item = InvoiceItem.objects.filter(
            service=InvoiceService.STAFFERS,
        ).first()

        self.assertEqual(saas_item.invoice_id, staffer_item.invoice_id)

        sms_item = InvoiceItem.objects.filter(
            service=InvoiceService.SMS,
        ).first()

        self.assertNotEqual(
            saas_item.invoice_id,
            sms_item.invoice_id,
        )

    def test_invoicing_with_offset_to_last_month(self):
        external_id = str(uuid.uuid4())

        previous_cycle = baker.make(BillingCycle)

        baker.make(
            BillingTransaction,
            amount=31,
            business=self.business,
            status=TransactionStatus.CHARGED,
            external_id=external_id,
            billing_cycle=previous_cycle,
            currency='USD',
        )

        self.create_charge(
            billing_cycle=previous_cycle,
            product_type=ProductType.POSTPAID_SMS,
            unit_price=0.005,
            quantity=200,
        )

        expected_external_id = str(uuid.uuid4())

        yet_previous_cycle = baker.make(BillingCycle)

        transaction = baker.make(
            BillingTransaction,
            amount=31,
            business=self.business,
            status=TransactionStatus.CHARGED,
            external_id=expected_external_id,
            billing_cycle=yet_previous_cycle,
            currency='USD',
        )

        BillingTransaction.objects.filter(id=transaction.id).update(
            created=tznow() - timedelta(days=30)
        )

        self.create_charge(
            billing_cycle=yet_previous_cycle,
            product_type=ProductType.POSTPAID_SMS,
            unit_price=0.005,
            quantity=200,
            usage_from=make_aware(datetime(2021, 7, 1), timezone=UTC),
            usage_to=make_aware(datetime(2021, 8, 1), timezone=UTC),
        )

        create_new_billing_saas_invoices_task(offset_days=30)

        self.assertEqual(InvoicingError.objects.count(), 0)

        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 1)
        summary = InvoicingSummary.objects.first()
        operator = summary.operator
        self.assertEqual(summary.service, InvoiceService.SAAS)
        self.assertEqual(summary.source, InvoicePaymentSourceType.ONLINE)
        self.assertIsNotNone(operator)
        self.assertEqual(operator.username, 'admin')
        self.assertEqual(operator.email, '<EMAIL>')

        item = InvoiceItem.objects.first()

        self.assertEqual(item.transaction_id, expected_external_id)
        self.assertEqual(item.billing_cycle_start, make_aware(datetime(2021, 7, 1), timezone=UTC))
        self.assertEqual(item.billing_cycle_end, make_aware(datetime(2021, 7, 31), timezone=UTC))

    def test_transaction_is_not_double_invoiced_if_hard_checks_are_enabled(self):
        external_id = str(uuid.uuid4())

        baker.make(InvoiceItem, transaction_id=external_id)

        cycle = baker.make(BillingCycle)

        create_new_billing_saas_invoices_task(run_hard_checks=True)

        baker.make(
            BillingTransaction,
            amount=31,
            business=self.business,
            status=TransactionStatus.CHARGED,
            external_id=external_id,
            billing_cycle=cycle,
            currency='USD',
        )

        self.create_charge(
            billing_cycle=cycle,
            product_type=ProductType.POSTPAID_SMS,
            unit_price=0.005,
            quantity=200,
        )

        self.assertEqual(InvoicingError.objects.count(), 0)
        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 1)

    def test_skipped_transactions_are_ignored(self):
        subscription = baker.make(BillingSubscription, business=self.business)
        billing_cycle = baker.make(BillingCycle, business=self.business, subscription=subscription)

        BillingTransaction.create_skipped(
            self.business.id,
            subscription.id,
            billing_cycle.id,
            amount=Decimal('20'),
            currency='USD',
        )

        create_new_billing_saas_invoices_task()

        self.assertEqual(InvoicingError.objects.count(), 0)
        self.assertEqual(Invoice.objects.count(), 0)

    def test_transaction_is_not_double_invoiced(self):
        self.create_saas_transaction()

        create_new_billing_saas_invoices_task()

        self.assertEqual(InvoicingError.objects.count(), 0)
        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 1)
        summary = InvoicingSummary.objects.first()
        operator = summary.operator
        self.assertIsNotNone(operator)
        self.assertEqual(operator.username, 'admin')
        self.assertEqual(operator.email, '<EMAIL>')
        self.assertEqual(summary.service, InvoiceService.SAAS)
        self.assertEqual(summary.source, InvoicePaymentSourceType.ONLINE)

        create_new_billing_saas_invoices_task()

        self.assertEqual(InvoicingError.objects.count(), 0)
        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 1)

    @freeze_time(datetime(2021, 9, 28))
    def test_overcharge_is_gathered_from_previous_charge(self):
        subscription = baker.make(BillingSubscription)
        billing_cycle = baker.make(
            BillingCycle,
            date_start=tznow() - timedelta(days=7),
            date_end=tznow(),
            subscription=subscription,
        )

        prev_cycle = baker.make(
            BillingCycle,
            date_start=tznow() - timedelta(days=14),
            date_end=tznow() - timedelta(days=7),
            subscription=subscription,
        )

        baker.make(
            BillingTransaction,
            amount=100,
            business=self.business,
            status=TransactionStatus.CHARGED,
            billing_cycle=billing_cycle,
            currency='USD',
        )

        self.create_charge(
            billing_cycle=billing_cycle,
            unit_price=30,
            quantity=2,
        )

        self.create_charge(
            billing_cycle=prev_cycle,
            unit_price=20,
            usage_from=make_aware(datetime(2021, 7, 1), timezone=UTC),
            usage_to=make_aware(datetime(2021, 8, 1), timezone=UTC),
        )

        self.create_charge(
            billing_cycle=prev_cycle,
            product_type=ProductType.POSTPAID_SMS,
            unit_price=0.01,
            quantity=2000,
            usage_from=make_aware(datetime(2021, 7, 1), timezone=UTC),
            usage_to=make_aware(datetime(2021, 8, 1), timezone=UTC),
        )

        create_new_billing_saas_invoices_task()
        self.check_errors()

        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 3)
        summary = InvoicingSummary.objects.first()
        operator = summary.operator
        self.assertIsNotNone(operator)
        self.assertEqual(operator.username, 'admin')
        self.assertEqual(operator.email, '<EMAIL>')
        self.assertEqual(summary.service, InvoiceService.SAAS)
        self.assertEqual(summary.source, InvoicePaymentSourceType.ONLINE)

        invoice = Invoice.objects.first()

        # base_gross_value = unit_price * quantity, quantity always=1
        self.assertTrue(invoice.items.filter(base_gross_value=60, quantity=1).exists())
        self.assertTrue(invoice.items.filter(base_gross_value=20, quantity=1).exists())
        self.assertTrue(
            invoice.items.filter(base_gross_value=Decimal('0.01') * 2000, quantity=1).exists()
        )

    @override_settings(
        NAVISION_SPLIT_INVOICES_BASED_ON_BILLING_CYCLE=True,
    )
    @freeze_time(datetime(2021, 9, 28))
    def test_overcharge_is_split_if_invoice_with_different_billing_cycles_is_not_allowed(self):
        subscription = baker.make(BillingSubscription)
        billing_cycle = baker.make(
            BillingCycle,
            date_start=tznow() - timedelta(days=7),
            date_end=tznow(),
            subscription=subscription,
        )

        prev_cycle = baker.make(
            BillingCycle,
            date_start=tznow() - timedelta(days=14),
            date_end=tznow() - timedelta(days=7),
            subscription=subscription,
        )

        baker.make(
            BillingTransaction,
            amount=100,
            business=self.business,
            status=TransactionStatus.CHARGED,
            billing_cycle=billing_cycle,
            currency='USD',
        )

        self.create_charge(
            billing_cycle=billing_cycle,
            unit_price=30,
            quantity=2,
        )

        self.create_charge(
            billing_cycle=prev_cycle,
            unit_price=20,
            usage_from=make_aware(datetime(2021, 7, 1), timezone=UTC),
            usage_to=make_aware(datetime(2021, 8, 1), timezone=UTC),
        )

        self.create_charge(
            billing_cycle=prev_cycle,
            product_type=ProductType.POSTPAID_SMS,
            unit_price=0.01,
            quantity=2000,
            usage_from=make_aware(datetime(2021, 7, 1), timezone=UTC),
            usage_to=make_aware(datetime(2021, 8, 1), timezone=UTC),
        )

        create_new_billing_saas_invoices_task()
        self.check_errors()

        self.assertEqual(Invoice.objects.count(), 2)
        self.assertEqual(InvoiceItem.objects.count(), 3)
        summary = InvoicingSummary.objects.first()
        operator = summary.operator
        self.assertIsNotNone(operator)
        self.assertEqual(operator.username, 'admin')
        self.assertEqual(operator.email, '<EMAIL>')
        self.assertEqual(summary.service, InvoiceService.SAAS)
        self.assertEqual(summary.source, InvoicePaymentSourceType.ONLINE)

        # base_gross_values = unit_price * quantity
        saas_item = InvoiceItem.objects.get(base_gross_value=30 * 2, service=InvoiceService.SAAS)
        overdue_saas_item = InvoiceItem.objects.get(
            base_gross_value=20,
            service=InvoiceService.SAAS,
        )
        sms_item = InvoiceItem.objects.get(
            service=InvoiceService.SMS,
        )

        self.assertEqual(overdue_saas_item.invoice_id, sms_item.invoice_id)
        self.assertNotEqual(overdue_saas_item.invoice_id, saas_item.invoice_id)

    @freeze_time(datetime(2021, 9, 28))
    def test_overcharge_is_gathered_from_previous_charges(self):
        subscription = baker.make(BillingSubscription)
        billing_cycle = baker.make(
            BillingCycle,
            date_start=tznow() - timedelta(days=7),
            date_end=tznow(),
            subscription=subscription,
        )

        self.create_charge(billing_cycle=billing_cycle, unit_price=10)

        prev_cycle = baker.make(
            BillingCycle,
            date_start=tznow() - timedelta(days=14),
            date_end=tznow() - timedelta(days=7),
            subscription=subscription,
        )

        self.create_charge(billing_cycle=prev_cycle, unit_price=20)

        prev_prev_cycle = baker.make(
            BillingCycle,
            date_start=tznow() - timedelta(days=21),
            date_end=tznow() - timedelta(days=14),
            subscription=subscription,
        )

        self.create_charge(billing_cycle=prev_prev_cycle, unit_price=30)

        ancient_cycle = baker.make(
            BillingCycle,
            date_start=tznow() - timedelta(days=28),
            date_end=tznow() - timedelta(days=21),
            subscription=subscription,
        )

        self.create_charge(billing_cycle=ancient_cycle, unit_price=5)

        baker.make(
            BillingTransaction,
            amount=60,
            business=self.business,
            status=TransactionStatus.CHARGED,
            billing_cycle=billing_cycle,
            currency='USD',
        )

        create_new_billing_saas_invoices_task()
        self.check_errors()

        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 3)
        summary = InvoicingSummary.objects.first()
        operator = summary.operator
        self.assertIsNotNone(operator)
        self.assertEqual(operator.username, 'admin')
        self.assertEqual(operator.email, '<EMAIL>')
        self.assertEqual(summary.service, InvoiceService.SAAS)
        self.assertEqual(summary.source, InvoicePaymentSourceType.ONLINE)

        invoice = Invoice.objects.first()

        self.assertFalse(invoice.items.filter(base_gross_value=5, quantity=1).exists())
        self.assertTrue(invoice.items.filter(base_gross_value=10, quantity=1).exists())
        self.assertTrue(invoice.items.filter(base_gross_value=20, quantity=1).exists())
        self.assertTrue(invoice.items.filter(base_gross_value=30, quantity=1).exists())

    def test_gross_price_with_discount(self):
        subscription = baker.make(BillingSubscription)
        billing_cycle = baker.make(
            BillingCycle,
            date_start=tznow() - timedelta(days=7),
            date_end=tznow(),
            subscription=subscription,
        )

        self.create_charge(billing_cycle=billing_cycle, unit_price=10, discounted_price=3)

        baker.make(
            BillingTransaction,
            amount=5,
            business=self.business,
            status=TransactionStatus.CHARGED,
            billing_cycle=billing_cycle,
            currency='USD',
        )

        create_new_billing_saas_invoices_task.delay()

        self.assertEqual(InvoicingError.objects.count(), 0)
        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 2)

        item = InvoiceItem.objects.filter(service=InvoiceService.SAAS).first()
        discount_item = InvoiceItem.objects.filter(service=InvoiceService.DISCOUNT).first()

        self.assertEqual(item.base_gross_value, Decimal('10.00'))
        self.assertEqual(item.discount_gross_value, Decimal('0.00'))

        self.assertEqual(discount_item.base_gross_value, Decimal('7.00'))  # 10.00 - 3.00
        self.assertEqual(discount_item.discount_gross_value, Decimal('0.00'))

    def test_discount_and_base_gross_value_is_rounded_on_our_side(self):
        subscription = baker.make(BillingSubscription)
        billing_cycle = baker.make(
            BillingCycle,
            date_start=tznow() - timedelta(days=7),
            date_end=tznow(),
            subscription=subscription,
        )

        self.create_charge(
            billing_cycle=billing_cycle,
            unit_price='350.00',
            gross_unit_price='437.50',
            discounted_price='262.50',
            gross_discounted_price='328.125',
        )
        self.create_charge(
            billing_cycle=billing_cycle,
            product_type=ProductType.POSTPAID_SMS,
            unit_price='0.005',
            gross_unit_price='0.0056',
            quantity=100,
        )

        baker.make(
            BillingTransaction,
            amount='328.57',
            business=self.business,
            status=TransactionStatus.CHARGED,
            billing_cycle=billing_cycle,
            currency='USD',
        )

        create_new_billing_saas_invoices_task.delay()

        self.assertEqual(InvoicingError.objects.count(), 0)
        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 3)

        saas_item = InvoiceItem.objects.filter(service=InvoiceService.SAAS).first()
        sms_item = InvoiceItem.objects.filter(service=InvoiceService.SMS).first()
        discount_saas_item = InvoiceItem.objects.filter(service=InvoiceService.DISCOUNT).first()

        self.assertEqual(saas_item.base_gross_value, Decimal('437.50'))
        self.assertEqual(saas_item.quantity, 1)
        self.assertEqual(saas_item.discount_gross_value, Decimal('0.00'))

        self.assertEqual(sms_item.base_gross_value, Decimal('0.0056') * 100)
        self.assertEqual(sms_item.quantity, 1)
        self.assertEqual(sms_item.discount_gross_value, Decimal('0.00'))

        self.assertEqual(discount_saas_item.base_gross_value, Decimal('109.38'))  # 437.50 - 328.125
        self.assertEqual(discount_saas_item.quantity, 1)
        self.assertEqual(discount_saas_item.discount_gross_value, Decimal('0.00'))

    def test_final_price_is_used_if_quantity_field_is_not_used(self):
        subscription = baker.make(BillingSubscription)
        billing_cycle = baker.make(
            BillingCycle,
            date_start=tznow() - timedelta(days=7),
            date_end=tznow(),
            subscription=subscription,
        )

        saas_charge = self.create_charge(
            billing_cycle=billing_cycle,
            unit_price='350.00',
            gross_unit_price='437.50',
            discounted_price='262.50',
            gross_discounted_price='328.125',
            quantity=2,
        )
        # bogus price to show that this field will be taken into account no matter what
        saas_charge.gross_total_price = '2137.0'
        saas_charge.save()

        sms_charge = self.create_charge(
            billing_cycle=billing_cycle,
            product_type=ProductType.POSTPAID_SMS,
            unit_price='0.005',
            gross_unit_price='0.0056',
            quantity=100,
        )

        sms_charge.gross_final_price = '21.37'  # bogus price again
        sms_charge.save()

        baker.make(
            BillingTransaction,
            amount='328.57',
            business=self.business,
            status=TransactionStatus.CHARGED,
            billing_cycle=billing_cycle,
            currency='USD',
        )

        create_new_billing_saas_invoices_task.delay()

        self.assertEqual(InvoicingError.objects.count(), 0)
        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 3)

        saas_item = InvoiceItem.objects.filter(service=InvoiceService.SAAS).first()
        sms_item = InvoiceItem.objects.filter(service=InvoiceService.SMS).first()
        discount_saas_item = InvoiceItem.objects.filter(service=InvoiceService.DISCOUNT).first()

        self.assertEqual(saas_item.product, '2 x Booksy Subscription')
        self.assertEqual(saas_item.base_gross_value, Decimal('2137.0'))
        self.assertEqual(saas_item.quantity, 1)
        self.assertEqual(saas_item.discount_gross_value, Decimal('0.00'))

        self.assertEqual(sms_item.product, '100 x SMS Delivery')
        self.assertEqual(sms_item.base_gross_value, Decimal('21.37'))
        self.assertEqual(sms_item.quantity, 1)
        self.assertEqual(sms_item.discount_gross_value, Decimal('0.00'))

        self.assertIsNotNone(discount_saas_item)

    @parameterized.expand(
        [
            (
                3,
                make_aware(datetime(2021, 3, 30), timezone=UTC),
                make_aware(datetime(2021, 3, 29), timezone=UTC),
            ),
            (
                6,
                make_aware(datetime(2021, 6, 29), timezone=UTC),
                make_aware(datetime(2021, 6, 28), timezone=UTC),
            ),
            (
                12,
                make_aware(datetime(2021, 12, 30), timezone=UTC),
                make_aware(datetime(2021, 12, 29), timezone=UTC),
            ),
        ]
    )
    def test_saas_invoicing_sub_longer_than_one_month(
        self, sub_duration, sub_date_end, exp_cycle_end
    ):
        sub = baker.make(
            BillingSubscription,
            subscription_duration=sub_duration,
            date_end=sub_date_end,
        )
        external_id = str(uuid.uuid4())
        cycle = baker.make(BillingCycle, subscription=sub)
        transaction = baker.make(
            BillingTransaction,
            amount=60,
            business=self.business,
            status=TransactionStatus.CHARGED,
            external_id=external_id,
            billing_cycle=cycle,
            currency='USD',
            subscription=sub,
        )

        charge = self.create_charge(cycle)

        tax_additional_data = {'id': 1, 'area': 'zip', 'tax_area_code': '90210'}
        charge.tax_additional_data = tax_additional_data
        charge.save()

        self.assertTrue(transaction.subscription.is_long_subscription)

        create_new_billing_saas_invoices_task()

        self.assertEqual(InvoicingError.objects.count(), 0)
        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 1)
        summary = InvoicingSummary.objects.first()
        operator = summary.operator
        self.assertIsNotNone(operator)
        self.assertEqual(operator.username, 'admin')
        self.assertEqual(operator.email, '<EMAIL>')
        self.assertEqual(summary.service, InvoiceService.SAAS)
        self.assertEqual(summary.source, InvoicePaymentSourceType.ONLINE)

        invoice = Invoice.objects.first()
        item = InvoiceItem.objects.first()

        self.assertEqual(invoice.currency, 'USD')
        self.assertEqual(invoice.bank_code, 'Online Bank')
        self.assertEqual(item.product, 'Booksy Subscription')
        self.assertEqual(item.service, Invoice.Service.SAAS)
        self.assertEqual(item.base_gross_value, charge.gross_unit_price)
        self.assertEqual(item.quantity, charge.quantity)
        self.assertEqual(item.charging_dt, transaction.created)
        self.assertEqual(item.billing_cycle_start, charge.usage_from)
        self.assertEqual(item.billing_cycle_end, exp_cycle_end)
        self.assertEqual(item.transaction_id, transaction.external_id)
        self.assertEqual(item.object_id, charge.id)
        self.assertTrue(item.charge_completed)
        self.assertDictEqual(item.tax_additional_data, tax_additional_data)

    def test_no_invoice_created_if_no_charge_sub_longer_than_one_month(self):
        sub = baker.make(
            BillingSubscription,
            subscription_duration=3,
            date_end=make_aware(datetime(2021, 4, 1), timezone=UTC),
        )
        external_id = str(uuid.uuid4())
        cycle = baker.make(BillingCycle, subscription=sub)
        baker.make(
            BillingTransaction,
            amount=60,
            business=self.business,
            status=TransactionStatus.CHARGED,
            external_id=external_id,
            billing_cycle=cycle,
            currency='USD',
        )

        create_new_billing_saas_invoices_task()

        self.assertEqual(InvoicingError.objects.count(), 0)
        self.assertEqual(Invoice.objects.count(), 0)

    def test_long_sub_invoiced_as_one_month_if_incorrect_duration(self):
        sub = baker.make(
            BillingSubscription,
            subscription_duration=5,
            date_end=make_aware(datetime(2021, 6, 1), timezone=UTC),
        )

        external_id = str(uuid.uuid4())
        cycle = baker.make(BillingCycle, subscription=sub)
        baker.make(
            BillingTransaction,
            amount=60,
            business=self.business,
            status=TransactionStatus.CHARGED,
            external_id=external_id,
            billing_cycle=cycle,
            currency='USD',
        )

        charge = self.create_charge(cycle)

        tax_additional_data = {'id': 1, 'area': 'zip', 'tax_area_code': '90210'}
        charge.tax_additional_data = tax_additional_data
        charge.save()

        create_new_billing_saas_invoices_task()

        self.assertEqual(InvoicingError.objects.count(), 0)
        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 1)

        item = InvoiceItem.objects.first()

        self.assertEqual(item.billing_cycle_end, charge.usage_to - timedelta(days=1))

    def test_discount_as_separate_item(self):
        subscription = baker.make(BillingSubscription)
        billing_cycle = baker.make(
            BillingCycle,
            date_start=tznow() - timedelta(days=7),
            date_end=tznow(),
            subscription=subscription,
            business=self.business,
        )
        charge = self.create_charge(
            billing_cycle=billing_cycle,
            unit_price=10,
            discounted_price=7,
            quantity=1,
        )
        charge.refresh_tax_related_data()

        charge = self.create_charge(
            billing_cycle=billing_cycle,
            unit_price=10,
            discounted_price=6,
            quantity=7,
        )
        charge.refresh_tax_related_data()

        charge = self.create_charge(
            billing_cycle=billing_cycle, unit_price=0.001, discounted_price=0.001, quantity=547
        )
        charge.refresh_tax_related_data()

        baker.make(
            BillingTransaction,
            amount=49.55,  # 49.547
            business=self.business,
            status=TransactionStatus.CHARGED,
            billing_cycle=billing_cycle,
            currency='USD',
        )

        create_new_billing_saas_invoices_task()

        self.assertEqual(InvoicingError.objects.count(), 0)

        self.assertEqual(1, Invoice.objects.count())
        self.assertEqual(5, InvoiceItem.objects.count())

        discount_items = InvoiceItem.objects.filter(
            service=InvoiceService.DISCOUNT,
            base_gross_value__in=[3, 28],
            discount_gross_value=Decimal('0.00'),
        )
        base_items = InvoiceItem.objects.filter(
            ~Q(service=InvoiceService.DISCOUNT),
            base_gross_value__in=[70, 10, 0.55],
            discount_gross_value=Decimal('0.00'),
        )

        self.assertEqual(2, discount_items.count())
        self.assertEqual(3, base_items.count())

    def test_invoicing_if_offline_invoice_exists(self):
        offline_invoice = baker.make(
            Invoice,
            service=InvoiceService.SAAS,
            source=InvoicePaymentSource.OFFLINE,
        )
        summary = baker.make(
            InvoicingSummary,
            invoicing_target=tznow(),
            service=InvoiceService.SAAS,
            source=InvoicePaymentSourceType.OFFLINE,
        )
        baker.make(
            BusinessInvoiceSummary,
            report=summary,
            invoice=offline_invoice,
            business=self.business,
        )

        self.create_saas_transaction()
        create_new_billing_saas_invoices_task()

        self.assertEqual(InvoicingError.objects.count(), 0)

        self.assertEqual(2, Invoice.objects.count())
        self.assertEqual(1, InvoiceItem.objects.count())
        self.assertEqual(1, Invoice.objects.filter(source=InvoicePaymentSource.OFFLINE).count())
        self.assertEqual(InvoicingSummary.objects.count(), 2)
        self.assertEqual(InvoicingSummary.objects.filter(service=InvoiceService.SAAS).count(), 2)
        self.assertEqual(
            InvoicingSummary.objects.filter(source=InvoicePaymentSourceType.ONLINE).count(), 1
        )
        self.assertEqual(
            InvoicingSummary.objects.filter(source=InvoicePaymentSourceType.OFFLINE).count(), 1
        )

    def test_same_transaction_id_in_two_items(self):
        transaction_id = 'P05DR@W1@M'

        item = baker.make(
            InvoiceItem,
            service=Invoice.Service.SAAS,
            payment_source=InvoicePaymentSource.STRIPE,
            transaction_id=transaction_id,
        )
        baker.make(
            Invoice,
            service=InvoiceService.SAAS,
            source=InvoicePaymentSource.STRIPE,
            items=[item],
        )
        transaction, *_ = self.create_saas_transaction()
        transaction.external_id = transaction_id
        transaction.save()

        create_new_billing_saas_invoices_task()
        self.assertEqual(2, InvoiceItem.objects.count())

    def test_same_product_identifier_in_two_items(self):
        transaction, charge, _ = self.create_saas_transaction()

        product_identifier = (
            f'BillingCycleProductCharge: {charge.id}, '
            f'Transaction identifier: {transaction.external_id}, '
            f'Provider: {transaction.payment_processor}'
        )

        item = baker.make(
            InvoiceItem,
            service=Invoice.Service.SAAS,
            payment_source=InvoicePaymentSource.STRIPE,
            product_identifier=product_identifier,
        )
        baker.make(
            Invoice,
            service=InvoiceService.SAAS,
            source=InvoicePaymentSource.STRIPE,
            items=[item],
        )

        create_new_billing_saas_invoices_task()
        self.assertEqual(1, InvoiceItem.objects.count())

    def test_operator(self):
        user = baker.make(User)
        create_new_billing_saas_invoices_task.delay(user_id=user.id)
        self.assertEqual(user.id, InvoicingSummary.objects.first().operator.id)

    def test_system_operator_if_no_user_id(self):
        create_new_billing_saas_invoices_task.delay(user_id=None)
        self.assertEqual(1, InvoicingSummary.objects.count())
        summary = InvoicingSummary.objects.first()
        operator = summary.operator
        self.assertIsNotNone(operator)
        self.assertEqual(operator.username, 'admin')
        self.assertEqual(operator.email, '<EMAIL>')

    def test_system_operator_if_invalid_user_id(self):
        user = baker.make(User)
        create_new_billing_saas_invoices_task.delay(user_id=user.id + 1)
        self.assertEqual(1, InvoicingSummary.objects.count())
        summary = InvoicingSummary.objects.first()
        operator = summary.operator
        self.assertIsNotNone(operator)
        self.assertEqual(operator.username, 'admin')
        self.assertEqual(operator.email, '<EMAIL>')

    def test_one_summary_if_multiple_triggers_same_operator_and_target_date(self):
        user = baker.make(User)
        create_new_billing_saas_invoices_task(user_id=user.id)
        self.assertEqual(1, InvoicingSummary.objects.count())
        create_new_billing_saas_invoices_task(user_id=user.id)
        self.assertEqual(1, InvoicingSummary.objects.count())
        self.assertEqual(user.id, InvoicingSummary.objects.first().operator.id)

    @patch('webapps.navision.tasks.booksy_billing.create_new_billing_saas_invoice_task.delay')
    def test_multiple_businesses(self, mocked_task):
        summaries_ids = []
        for _ in range(3):  # already invoiced businesses
            business = baker.make(Business, buyer=baker.make(SubscriptionBuyer))
            invoice = baker.make(
                Invoice,
                source=InvoicePaymentSourceType.ONLINE,
                service=Invoice.Service.SAAS,
            )
            summary = baker.make(
                InvoicingSummary,
                service=invoice.service,
                source=invoice.source,
                invoicing_target=BooksyBillingSaaSInvoiceFactory.default_invoicing_target(),
            )
            summaries_ids.append(summary.id)
            baker.make(BusinessInvoiceSummary, report=summary, invoice=invoice, business=business)
            billing_cycle = baker.make(BillingCycle, business=business)
            self.create_charge(billing_cycle=billing_cycle)
            baker.make(
                BillingTransaction,
                business=business,
                billing_cycle=billing_cycle,
                status=TransactionStatus.CHARGED,
            )

        businesses_ids_to_invoice = []
        for _ in range(4):  # businesses to be invoiced
            address = baker.make(InvoiceAddress, zipcode=baker.make(Region, name='112'))
            buyer = baker.make(
                SubscriptionBuyer,
                is_verified=True,
                invoice_address=address,
                entity_name='aaaaa',
                invoice_email='<EMAIL>',
            )
            business = baker.make(Business, buyer=buyer)
            businesses_ids_to_invoice.append(business.id)
            billing_cycle = baker.make(BillingCycle, business=business)
            self.create_charge(billing_cycle=billing_cycle)
            baker.make(
                BillingTransaction,
                business=business,
                billing_cycle=billing_cycle,
                status=TransactionStatus.CHARGED,
            )

        create_new_billing_saas_invoices_task()
        new_summary = InvoicingSummary.objects.exclude(id__in=summaries_ids).first()
        self.assertEqual(4, mocked_task.call_count)
        mocked_task.assert_has_calls(
            [call(new_summary.id, biz_id, True) for biz_id in businesses_ids_to_invoice],
            any_order=True,
        )


@override_eppo_feature_flag({NavisionInvoiceBBSaaSPaidAfterMigrationFlag.flag_name: True})
class TestBooksyBillingInvoicingFlagOn(TestBooksyBillingInvoicing):
    pass


@override_settings(
    NAVISION_SPLIT_INVOICES_BASED_ON_BILLING_CYCLE=False,
    SUPPORTED_INVOICE_PAYMENT_METHODS={
        Business.PaymentSource.BRAINTREE: {
            'SaaS',
            'Boost',
        },
        Business.PaymentSource.BRAINTREE_BILLING: {
            'SaaS',
            'Boost',
        },
    },
    NAVISION_BANK_CODES_PER_SOURCE={
        'O': 'Offline Bank',
        'B': 'Online Bank',
    },
)
@freeze_time(datetime(2021, 9, 28))
@patch('webapps.purchase.admin.messages')
class TestInvoiceSaaSOnlineAdminAction(TestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        navision_integration_enabled_recipe.make(
            auto_run_saas_online_invoicing=True,
            charge_to_invoice_delay=0,
        )

    def setUp(self) -> None:
        super().setUp()
        self.merchant = baker.make(Merchant)
        self.buyer = baker.make(
            SubscriptionBuyer,
            merchant=self.merchant,
        )
        self.business = baker.make(
            Business,
            payment_source=Business.PaymentSource.BRAINTREE_BILLING,
            has_new_billing=True,
            status=Business.Status.PAID,
            buyer=self.buyer,
        )

        self.mock_request = Mock()
        self.mock_request.user.id = None

    def check_errors(self):
        if InvoicingError.objects.exists():
            self.fail('\n'.join(error.error for error in InvoicingError.objects.all()))

    def tearDown(self) -> None:
        self.check_errors()

        return super().tearDown()

    def create_saas_transaction(self):
        external_id = str(uuid.uuid4())
        sub = baker.make(BillingSubscription)
        finished_cycle = baker.make(
            BillingCycle,
            business=self.business,
            date_start=tznow() - timedelta(days=7),
            date_end=tznow(),
            subscription=sub,
        )

        baker.make(
            BillingCycleProductCharge,
            product__product_type=ProductType.SAAS,
            usage_from=make_aware(datetime(2021, 8, 1), timezone=UTC),
            usage_to=make_aware(datetime(2021, 9, 1), timezone=UTC),
            billing_cycle=finished_cycle,
            unit_price=60,
            gross_unit_price=80,
            discounted_price=60,
            gross_discounted_price=80,
            final_price=80,
            gross_final_price=80,
            quantity=1,
            currency='USD',
        )

        baker.make(
            BillingTransaction,
            amount=60,
            business=self.business,
            status=TransactionStatus.CHARGED,
            external_id=external_id,
            billing_cycle=finished_cycle,
            currency='USD',
        )

    def create_billing_cycle(self):
        sub = baker.make(BillingSubscription)
        cycle = baker.make(
            BillingCycle,
            business=self.business,
            date_start=tznow() - timedelta(days=7),
            date_end=tznow(),
            subscription=sub,
        )
        return cycle

    def create_charge(self, billing_cycle):
        baker.make(
            BillingCycleProductCharge,
            product__product_type=ProductType.SAAS,
            usage_from=make_aware(datetime(2021, 8, 1), timezone=UTC),
            usage_to=make_aware(datetime(2021, 9, 1), timezone=UTC),
            billing_cycle=billing_cycle,
            unit_price=60,
            gross_unit_price=80,
            discounted_price=60,
            gross_discounted_price=80,
            final_price=80,
            gross_final_price=80,
            quantity=1,
            currency='USD',
        )

    @patch('webapps.navision.tasks.booksy_billing.create_new_billing_saas_invoice_task.delay')
    def test_invoice_saas_online_admin_action(self, task_mock, msg_mock):
        self.create_saas_transaction()

        invoice_saas_online(None, self.mock_request, SubscriptionBuyer.objects.all())

        summary = InvoicingSummary.objects.first()
        self.assertEqual(summary.service, InvoiceService.SAAS)
        self.assertEqual(summary.source, InvoicePaymentSourceType.ONLINE)

        self.assertEqual(task_mock.call_count, 1)
        self.assertEqual(
            task_mock.call_args_list[0], call(InvoicingSummary.objects.first().id, self.business.id)
        )
        msg_mock.success.assert_has_calls(
            [
                call(
                    self.mock_request,
                    f'business ID: {self.business.id} '
                    f'(buyer ID: {self.buyer.id}) invoiced successfully\n',
                )
            ]
        )

    @patch('webapps.navision.tasks.booksy_billing.create_new_billing_saas_invoice_task.delay')
    def test_more_businesses(self, task_mock, msg_mock):
        first_business = self.business
        second_business = baker.make(
            Business,
            payment_source=Business.PaymentSource.BRAINTREE_BILLING,
            has_new_billing=True,
            status=Business.Status.PAID,
            buyer=self.buyer,
        )

        first_business_sub = baker.make(BillingSubscription)
        second_business_sub = baker.make(BillingSubscription)

        first_business_cycle = baker.make(
            BillingCycle,
            business=first_business,
            date_start=tznow() - timedelta(days=7),
            date_end=tznow(),
            subscription=first_business_sub,
        )

        second_business_cycle = baker.make(
            BillingCycle,
            business=second_business,
            date_start=tznow() - timedelta(days=9),
            date_end=tznow(),
            subscription=second_business_sub,
        )

        self.create_charge(first_business_cycle)
        self.create_charge(second_business_cycle)

        first_external_id = str(uuid.uuid4())
        second_external_id = str(uuid.uuid4())

        today = tznow()
        yesterday = today - timedelta(days=1)

        baker.make(
            BillingTransaction,
            amount=60,
            business=self.business,
            status=TransactionStatus.CHARGED,
            external_id=first_external_id,
            billing_cycle=first_business_cycle,
            currency='USD',
        )
        with freeze_time(yesterday):
            baker.make(
                BillingTransaction,
                amount=60,
                business=second_business,
                status=TransactionStatus.CHARGED,
                external_id=second_external_id,
                billing_cycle=second_business_cycle,
                currency='USD',
            )

        invoice_saas_online(None, self.mock_request, SubscriptionBuyer.objects.all())

        self.assertEqual(task_mock.call_count, 2)
        self.assertEqual(2, InvoicingSummary.objects.count())
        inv_summary_1 = InvoicingSummary.objects.get(invoicing_target=today)
        inv_summary_2 = InvoicingSummary.objects.get(invoicing_target=yesterday)
        task_mock.assert_has_calls(
            [call(inv_summary_1.id, first_business.id), call(inv_summary_2.id, second_business.id)],
            any_order=True,
        )

        self.assertEqual(msg_mock.success.call_count, 2)
        msg_mock.success.assert_has_calls(
            [
                call(
                    self.mock_request,
                    f'business ID: {first_business.id} '
                    f'(buyer ID: {self.buyer.id}) invoiced successfully\n',
                ),
                call(
                    self.mock_request,
                    f'business ID: {second_business.id} '
                    f'(buyer ID: {self.buyer.id}) invoiced successfully\n',
                ),
            ],
            any_order=True,
        )

    @patch('webapps.navision.tasks.booksy_billing.create_new_billing_saas_invoice_task.delay')
    def test_no_businesses_no_invoice(self, task_mock, msg_mock):
        self.buyer.businesses.clear()
        buyer_id = self.buyer.id
        second_merchant = baker.make(Merchant)
        second_buyer = baker.make(SubscriptionBuyer, merchant=second_merchant)
        business = baker.make(
            Business,
            payment_source=Business.PaymentSource.BRAINTREE_BILLING,
            has_new_billing=True,
            status=Business.Status.PAID,
            buyer=second_buyer,
        )

        cycle = self.create_billing_cycle()
        cycle.business = business
        cycle.save()

        self.create_charge(cycle)

        external_id = str(uuid.uuid4())

        baker.make(
            BillingTransaction,
            amount=60,
            business=business,
            status=TransactionStatus.CHARGED,
            external_id=external_id,
            billing_cycle=cycle,
            currency='USD',
        )

        self.assertEqual(SubscriptionBuyer.objects.count(), 2)

        invoice_saas_online(None, self.mock_request, SubscriptionBuyer.objects.all())

        self.assertEqual(task_mock.call_count, 1)

        msg_mock.info.assert_called_once_with(
            self.mock_request, f'buyer ID: {buyer_id} has no businesses\n'
        )

        msg_mock.success.assert_has_calls(
            [
                call(
                    self.mock_request,
                    f'business ID: {business.id} '
                    f'(buyer ID: {second_buyer.id}) invoiced successfully\n',
                )
            ]
        )

    @patch('webapps.navision.tasks.booksy_billing.create_new_billing_saas_invoice_task.delay')
    def test_no_billing_cycle_no_invoice(self, task_mock, msg_mock):
        self.assertEqual(SubscriptionBuyer.objects.count(), 1)
        self.assertEqual(Business.objects.count(), 1)

        invoice_saas_online(None, self.mock_request, SubscriptionBuyer.objects.all())

        self.assertEqual(task_mock.call_count, 0)

        msg_mock.info.assert_called_once_with(
            self.mock_request,
            f'business ID: {self.business.id} (buyer ID: {self.buyer.id}) '
            f'has no eligible billing cycle\n',
        )

    @patch('webapps.navision.tasks.booksy_billing.create_new_billing_saas_invoice_task.delay')
    def test_no_transaction_no_invoice(self, task_mock, msg_mock):
        cycle = self.create_billing_cycle()

        self.create_charge(cycle)

        invoice_saas_online(None, self.mock_request, SubscriptionBuyer.objects.all())

        self.assertEqual(task_mock.call_count, 0)
        msg_mock.info.assert_called_once_with(
            self.mock_request,
            f'business ID: {self.business.id} (buyer ID: {self.buyer.id}) '
            f'has no eligible transaction to invoice\n',
        )

    @patch('webapps.navision.tasks.booksy_billing.create_new_billing_saas_invoice_task.delay')
    def test_transaction_amount_zero_no_invoice(self, task_mock, msg_mock):
        cycle = self.create_billing_cycle()

        baker.make(
            BillingCycleProductCharge,
            product__product_type=ProductType.SAAS,
            usage_from=make_aware(datetime(2021, 8, 1), timezone=UTC),
            usage_to=make_aware(datetime(2021, 9, 1), timezone=UTC),
            billing_cycle=cycle,
            unit_price=0,
            gross_unit_price=0,
            discounted_price=0,
            gross_discounted_price=0,
            final_price=0,
            gross_final_price=0,
            quantity=1,
            currency='USD',
        )

        external_id = str(uuid.uuid4())

        baker.make(
            BillingTransaction,
            amount=0,
            business=self.business,
            status=TransactionStatus.CHARGED,
            external_id=external_id,
            billing_cycle=cycle,
            currency='USD',
        )

        invoice_saas_online(None, self.mock_request, SubscriptionBuyer.objects.all())

        self.assertEqual(task_mock.call_count, 0)
        msg_mock.info.assert_called_once_with(
            self.mock_request,
            f'business ID: {self.business.id} (buyer ID: {self.buyer.id}) '
            f'transaction amount equals 0 or less and will not be invoiced\n',
        )

    def test_admin_action_not_invoicing_twice(self, msg_mock):

        self.create_saas_transaction()

        invoice_saas_online(None, self.mock_request, SubscriptionBuyer.objects.all())

        summary = InvoicingSummary.objects.first()
        self.assertEqual(summary.service, InvoiceService.SAAS)
        self.assertEqual(summary.source, InvoicePaymentSourceType.ONLINE)

        self.assertEqual(InvoicingError.objects.count(), 0)
        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 1)
        msg_mock.success.assert_has_calls(
            [
                call(
                    self.mock_request,
                    f'business ID: {self.business.id} '
                    f'(buyer ID: {self.buyer.id}) invoiced successfully\n',
                )
            ]
        )

        invoice_saas_online(None, self.mock_request, SubscriptionBuyer.objects.all())

        self.assertEqual(InvoicingError.objects.count(), 0)
        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 1)
        msg_mock.info.assert_has_calls(
            [
                call(
                    self.mock_request,
                    # pylint: disable=line-too-long
                    f'business ID: {self.business.id} (buyer ID: {self.buyer.id}) is already invoiced\n',
                )
            ]
        )
