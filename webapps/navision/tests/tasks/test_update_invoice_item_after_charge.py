import datetime
from datetime import timedelta

import freezegun
from django.test import TestCase, override_settings
from model_bakery import baker

from country_config import Country
from lib.tools import tznow
from webapps.billing.enums import PaymentProcessorType, ProductType
from webapps.billing.models import BillingCycleProductCharge
from webapps.navision.enums import InvoicePaymentSource
from webapps.navision.models import InvoiceItem, Invoice
from webapps.navision.tasks.saas_online_based_on_billing_cycle import (
    update_invoice_item_as_charged_task,
)
from webapps.structure.models import Region


@override_settings(API_COUNTRY=Country.ES)
class UpdateInvoiceItemAfterChargeTestCase(TestCase):
    def setUp(self):
        baker.make(
            Region,
            name='España',
            type=Region.Type.COUNTRY,
        )
        self.billing_cycle = baker.make('billing.BillingCycle')
        self.saas_product = baker.make(
            BillingCycleProductCharge,
            billing_cycle=self.billing_cycle,
            product__product_type=ProductType.SAAS,
        )
        self.staffer_product = baker.make(
            BillingCycleProductCharge,
            billing_cycle=self.billing_cycle,
            product__product_type=ProductType.STAFFER_SAAS,
        )
        self.invoice = baker.make(Invoice, service=Invoice.Service.SAAS)
        self.another_product = baker.make(BillingCycleProductCharge)
        self.another_invoice = baker.make(Invoice)

    def _create_item_with_no_transaction(self, invoice, product, service):
        return self._create_item_with_transaction_info(invoice, product, service, None, None, None)

    @staticmethod
    def _create_item_with_transaction_info(
        invoice, product, service, charging_dt, transaction_external_id, provider
    ):
        product_identifier = (
            f'BillingCycleProductCharge: {product.id}, '
            f'Transaction identifier: {transaction_external_id}, '
            f'Provider: {provider}'
        )
        if service == Invoice.Service.DISCOUNT:
            product_identifier += ' - Discount value'
        return baker.make(
            InvoiceItem,
            invoice=invoice,
            object_id=product.id,
            service=service,
            charge_completed=False,
            charging_dt=charging_dt,
            transaction_id=transaction_external_id,
            payment_source=InvoicePaymentSource.from_billing_payment_processor(provider),
            update_status=None,
            product_identifier=product_identifier,
        )

    def create_items_with_no_transaction(self):
        self._create_item_with_no_transaction(self.invoice, self.saas_product, Invoice.Service.SAAS)
        self._create_item_with_no_transaction(
            self.invoice, self.saas_product, Invoice.Service.DISCOUNT
        )
        self._create_item_with_no_transaction(
            self.invoice, self.staffer_product, Invoice.Service.STAFFERS
        )
        self._create_item_with_no_transaction(
            self.another_invoice, self.another_product, Invoice.Service.SAAS
        )

    def create_items_with_transaction_info(
        self, old_charging_dt, old_transaction_external_id, old_provider
    ):
        self._create_item_with_transaction_info(
            self.invoice,
            self.saas_product,
            Invoice.Service.DISCOUNT,
            old_charging_dt,
            old_transaction_external_id,
            old_provider,
        )
        self._create_item_with_transaction_info(
            self.invoice,
            self.saas_product,
            Invoice.Service.SAAS,
            old_charging_dt,
            old_transaction_external_id,
            old_provider,
        )
        self._create_item_with_transaction_info(
            self.invoice,
            self.staffer_product,
            Invoice.Service.STAFFERS,
            old_charging_dt,
            old_transaction_external_id,
            old_provider,
        )
        self._create_item_with_transaction_info(
            self.another_invoice,
            self.another_product,
            Invoice.Service.SAAS,
            tznow() - timedelta(days=5),
            '123aaa-234-234-xxx-234',
            old_provider,
        )

    def _control_test_another_item_no_transaction(self):
        another_item = InvoiceItem.objects.get(invoice__id=self.another_invoice.id)
        self.assertIsNone(another_item.update_status)
        self.assertFalse(another_item.charge_completed)
        self.assertIsNone(another_item.transaction_id)
        self.assertIsNone(another_item.charging_dt)
        self.assertIsNone(another_item.payment_source)
        self.assertEqual(
            (
                f'BillingCycleProductCharge: {another_item.object_id}, '
                f'Transaction identifier: {None}, '
                f'Provider: {None}'
            ),
            another_item.product_identifier,
        )

    def _control_test_another_item_with_transaction(self):
        another_item = InvoiceItem.objects.get(invoice__id=self.another_invoice.id)
        self.assertIsNone(another_item.update_status)
        self.assertFalse(another_item.charge_completed)
        self.assertEqual('123aaa-234-234-xxx-234', another_item.transaction_id)
        self.assertEqual(tznow() - timedelta(days=5), another_item.charging_dt)
        self.assertEqual(
            (
                f'BillingCycleProductCharge: {another_item.object_id}, '
                f'Transaction identifier: 123aaa-234-234-xxx-234, '
                f'Provider: {PaymentProcessorType.BRAINTREE}'
            ),
            another_item.product_identifier,
        )

    def test_no_transaction_before_and_charged_now(self):
        self.create_items_with_no_transaction()

        # transaction info after charge:
        transaction_external_id = 'aaa-bbb-123'
        charging_dt = tznow()
        provider = PaymentProcessorType.STRIPE

        update_invoice_item_as_charged_task(
            transaction_external_id,
            charging_dt,
            self.billing_cycle.id,
            provider,
        )

        # 3 updated items:
        items = InvoiceItem.objects.filter(invoice__id=self.invoice.id)
        self.assertEqual(3, items.count())
        for item in items:
            self.assertEqual(InvoiceItem.Status.INIT, item.update_status)
            self.assertTrue(item.charge_completed)
            self.assertEqual(transaction_external_id, item.transaction_id)
            self.assertEqual(charging_dt, item.charging_dt)
            product_identifier = (
                f'BillingCycleProductCharge: {item.object_id}, '
                f'Transaction identifier: {transaction_external_id}, '
                f'Provider: {provider}'
            )
            if item.service == Invoice.Service.DISCOUNT:
                self.assertEqual(product_identifier + ' - Discount value', item.product_identifier)
            else:
                self.assertEqual(product_identifier, item.product_identifier)

        # no changes in another item
        self._control_test_another_item_no_transaction()

    @freezegun.freeze_time(datetime.datetime(2024, 10, 11, 23, 20, 0))
    def test_failed_transaction_before_and_charged_now(self):
        old_transaction_external_id = '3440234-234-234234'
        old_charging_dt = tznow() - timedelta(days=4)
        old_provider = PaymentProcessorType.BRAINTREE
        self.create_items_with_transaction_info(
            old_charging_dt, old_transaction_external_id, old_provider
        )

        # transaction info after charge:
        new_transaction_external_id = 'aaa-bbb-123-new'
        new_charging_dt = tznow()
        new_provider = PaymentProcessorType.STRIPE

        update_invoice_item_as_charged_task(
            new_transaction_external_id,
            new_charging_dt,
            self.billing_cycle.id,
            new_provider,
        )

        # 3 updated items:
        items = InvoiceItem.objects.filter(invoice__id=self.invoice.id)
        self.assertEqual(3, items.count())
        for item in items:
            self.assertEqual(InvoiceItem.Status.INIT, item.update_status)
            self.assertTrue(item.charge_completed)
            self.assertEqual(new_transaction_external_id, item.transaction_id)
            self.assertEqual(new_charging_dt, item.charging_dt)
            product_identifier = (
                f'BillingCycleProductCharge: {item.object_id}, '
                f'Transaction identifier: {new_transaction_external_id}, '
                f'Provider: {new_provider}'
            )
            if item.service == Invoice.Service.DISCOUNT:
                self.assertEqual(product_identifier + ' - Discount value', item.product_identifier)
            else:
                self.assertEqual(product_identifier, item.product_identifier)

        # no changes in another item:
        self._control_test_another_item_with_transaction()
