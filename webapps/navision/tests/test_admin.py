from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal
from functools import partial
from typing import Optional
from unittest.mock import patch, call, MagicMock

import pytz
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.db.models import Q
from django.test import override_settings
from django.urls.base import reverse
from django.utils import timezone
from freezegun import freeze_time
from model_bakery import baker
from model_bakery.recipe import Recipe
from parameterized import parameterized
from rest_framework import status

from country_config import Country, CountryConfig
from lib.test_utils import get_settings
from lib.tools import tznow
from webapps.admin_extra.tests import DjangoTestCase
from webapps.navision.admin import NavisionSettingsForm
from webapps.navision.baker_recipes import navision_settings_recipe
from webapps.navision.enums import InvoicePaymentSource, InvoiceService
from webapps.navision.models import (
    Invoice,
    InvoiceErrorResponse,
    InvoiceItem,
    SubscriptionInvoiceSummary,
    TaxRate,
    BusinessInvoiceSummary,
    InvoicingSummary,
)
from webapps.navision.models.merchant import Merchant
from webapps.navision.models.settings import NavisionSettings
from webapps.navision.models.tax_rate import TaxGroup
from webapps.purchase.models import SubscriptionBuyer, InvoiceAddress
from webapps.structure.baker_recipes import usa_recipe
from webapps.user.baker_recipes import user_recipe
from webapps.user.groups import GroupName


class DummyInvoicedObject(models.Model):
    pass


class TestMerchantAdmin(DjangoTestCase):
    def setUp(self):
        super().setUp()
        usa_recipe.make()
        self.user = self.login_admin()

    def test_changelist_works(self):
        url = reverse("admin:navision_merchant_changelist")

        buyer = baker.make(
            SubscriptionBuyer,
            entity_name="Test Co.",
            tax_id="*********",
            invoice_email='<EMAIL>',
            deleted=None,
            payment_due_days=7,
            invoice_address=baker.make(
                InvoiceAddress,
                zipcode__name='43222',
            ),
        )

        buyer.sync_with_merchant()

        resp = self.client.get(url, follow=True)

        self.assertContains(resp, "*********")


@patch(
    'webapps.navision.models.merchant.create_or_update_merchant_in_navision.run',
)
@override_settings(LIVE_DEPLOYMENT=True)
class TestSyncSelectedMerchantsAdmin(DjangoTestCase):
    def setUp(self):
        super().setUp()
        navision_settings_recipe.make()
        user = user_recipe.make(
            groups=[baker.make(Group, name=GroupName.NAVISION_ADMIN)],
            is_superuser=False,
            is_staff=True,
        )
        self.user = self.login_admin(admin=user)
        _perms = [
            self.user.user_permissions.add(
                Permission.objects.get_by_natural_key(
                    f'{action}_merchant',
                    'navision',
                    'merchant',
                )
            )
            for action in ['change', 'add', 'view']
        ]
        self.url = reverse("admin:navision_merchant_changelist")
        with freeze_time(datetime(2021, 3, 15, 12)):
            self.good_merchant = baker.make(Merchant)

    @staticmethod
    def run_action(*merchants_ids):
        return {
            'action': 'sync_selected_merchants',
            '_selected_action': merchants_ids,
        }

    def test_merchant_selected_to_sync(self, mock_merchants):
        data = self.run_action(self.good_merchant.id)
        resp = self.client.post(self.url, data, follow=True)
        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertContains(
            resp,
            'Merchants have been scheduled for sync, check them in a while.',
        )
        self.assertNotContains(resp, 'Merchants not synced')
        self.assertEqual(1, mock_merchants.call_count)
        self.assertListEqual([call(self.good_merchant.id)], mock_merchants.call_args_list)

    def test_merchant_synced_already(self, mock_merchants):
        synced_merchant = baker.make(Merchant, synced_at=tznow())
        data = self.run_action(self.good_merchant.id, synced_merchant.id)
        resp = self.client.post(self.url, data, follow=True)
        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertContains(
            resp,
            'Merchants have been scheduled for sync, check them in a while.',
        )
        self.assertNotContains(resp, 'Merchants not synced')
        self.assertEqual(2, mock_merchants.call_count)

    def test_scheduling_for_sync(self, mock_merchants):
        queued_merchant = baker.make(Merchant)
        data = self.run_action(self.good_merchant.id, queued_merchant.id)
        resp = self.client.post(self.url, data, follow=True)
        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertContains(
            resp,
            'Merchants have been scheduled for sync, check them in a while.',
        )
        self.assertEqual(2, mock_merchants.call_count)
        expected_calls = [call(self.good_merchant.id), call(queued_merchant.id)]
        self.assertTrue(all(call in mock_merchants.call_args_list for call in expected_calls))

    def test_merchant_not_synced_but_queued_more_than_2h_ago(self, mock_merchants):
        with freeze_time(tznow() - timedelta(hours=5)):
            queued_merchant = baker.make(Merchant)
        data = self.run_action(self.good_merchant.id, queued_merchant.id)
        resp = self.client.post(self.url, data, follow=True)
        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertContains(resp, 'Merchants have been scheduled for sync, check them in a while.')
        self.assertNotContains(resp, 'Merchants not synced')
        self.assertEqual(2, mock_merchants.call_count)
        self.assertIn(
            call(self.good_merchant.id),
            mock_merchants.call_args_list,
        )
        self.assertIn(
            call(queued_merchant.id),
            mock_merchants.call_args_list,
        )

    def test_sync_merchants_without_permission(self, mock_merchants):
        self.user.groups.set([])
        data = self.run_action(self.good_merchant.id)
        resp = self.client.post(self.url, data, follow=True)
        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertEqual(0, mock_merchants.call_count)


class TestInvoiceAdmin(DjangoTestCase):
    def setUp(self):
        super().setUp()
        self.user = self.login_admin()

        invoiced_object = baker.make(DummyInvoicedObject)
        self.invoice_item_recipe = Recipe(
            InvoiceItem,
            content_type=ContentType.objects.get_for_model(DummyInvoicedObject),
            object_id=invoiced_object.pk,
            invoiced_object=invoiced_object,
        )

    @patch('webapps.purchase.tasks.segment.SegmentStatusChange.delay')
    @patch('webapps.purchase.models.base.AppleGoogleSubscriptionEventMessage', MagicMock())
    def test_changelist_works(self, _mock):
        url = reverse("admin:navision_invoice_changelist")

        invoice = baker.make(
            Invoice,
            deleted=None,
            business_name='Name like no other!',
            service=InvoiceService.SAAS,
            _fill_optional=True,
        )
        baker.make(
            SubscriptionInvoiceSummary,
            invoice=invoice,
            report=baker.make(InvoicingSummary, service=InvoiceService.SAAS_PER_BC),
            _fill_optional=True,
        )
        baker.make(
            BusinessInvoiceSummary,
            invoice=invoice,
            report=baker.make(InvoicingSummary, service=InvoiceService.SAAS_PER_BC),
            _fill_optional=True,
        )
        self.invoice_item_recipe.make(
            invoice=invoice,
            tax_additional_data=None,
            _fill_optional=True,
        )

        resp = self.client.get(url, follow=True)

        self.assertContains(resp, "Name like no other!")
        self.assertContains(resp, 'SaaS per BC')

    def test_change_view_works(self):
        invoice = baker.make(
            Invoice,
            deleted=None,
            business_name='Name like no other!',
            _fill_optional=True,
        )
        invoice_item = self.invoice_item_recipe.make(
            tax_additional_data={'id': 1, 'area': 'zip', 'tax_area_name': '90210'},
            invoice=invoice,
            _fill_optional=True,
        )
        baker.make(
            InvoiceErrorResponse,
            invoice=invoice_item.invoice,
            deleted=None,
            _fill_optional=True,
        )

        url = reverse("admin:navision_invoice_change", args=(invoice_item.invoice_id,))

        resp = self.client.get(url, follow=True)

        self.assertContains(resp, "Name like no other!")
        self.assertContains(resp, "tax_area_name")

    def test_change_view_works_tax_additional_data_none(self):
        invoice = baker.make(
            Invoice,
            deleted=None,
            business_name='Name like no other!',
            _fill_optional=True,
        )
        invoice_item = self.invoice_item_recipe.make(
            invoice=invoice,
            tax_additional_data=None,
            _fill_optional=True,
        )

        url = reverse("admin:navision_invoice_change", args=(invoice_item.invoice_id,))
        resp = self.client.get(url, follow=True)

        self.assertContains(resp, "Name like no other!")
        self.assertNotContains(resp, "tax_area_name")

    def test_invoice_items_info(self):
        invoice_item = self.invoice_item_recipe.make(
            billing_cycle_start=datetime(2023, 10, 10, 0, 0, 0, tzinfo=pytz.UTC),
            billing_cycle_end=datetime(2023, 11, 10, 0, 0, 0, tzinfo=pytz.UTC),
            service=InvoiceService.SAAS,
            base_gross_value=Decimal(345.21000),
        )
        self.invoice_item_recipe.make(
            invoice=invoice_item.invoice,
            billing_cycle_start=datetime(2023, 10, 10, 0, 0, 0, tzinfo=pytz.UTC),
            billing_cycle_end=datetime(2023, 11, 10, 0, 0, 0, tzinfo=pytz.UTC),
            service=InvoiceService.STAFFERS,
            base_gross_value=Decimal(21.18000),
        )
        self.invoice_item_recipe.make(
            invoice=invoice_item.invoice,
            billing_cycle_start=datetime(2023, 10, 10, 0, 0, 0, tzinfo=pytz.UTC),
            billing_cycle_end=datetime(2023, 11, 10, 0, 0, 0, tzinfo=pytz.UTC),
            service=InvoiceService.SMS,
            base_gross_value=Decimal(0.009),
        )
        self.invoice_item_recipe.make(
            invoice=invoice_item.invoice,
            billing_cycle_start=datetime(2023, 10, 10, 0, 0, 0, tzinfo=pytz.UTC),
            billing_cycle_end=datetime(2023, 11, 10, 0, 0, 0, tzinfo=pytz.UTC),
            service=InvoiceService.SMS,
            base_gross_value=Decimal(1),
            quantity=2,
        )
        self.invoice_item_recipe.make(
            invoice=invoice_item.invoice,
            billing_cycle_start=datetime(2023, 10, 10, 0, 0, 0, tzinfo=pytz.UTC),
            billing_cycle_end=datetime(2023, 11, 10, 0, 0, 0, tzinfo=pytz.UTC),
            service=InvoiceService.DISCOUNT,
            base_gross_value=Decimal(45.00000),
        )

        self.invoice_item_recipe.make(
            invoice=invoice_item.invoice,
            billing_cycle_start=datetime(2023, 10, 10, 0, 0, 0, tzinfo=pytz.UTC),
            billing_cycle_end=datetime(2023, 11, 10, 0, 0, 0, tzinfo=pytz.UTC),
            service=InvoiceService.SAAS,
            base_gross_value=Decimal(45.00000),
            discount_gross_value=Decimal(12),
        )

        url = reverse("admin:navision_invoice_changelist")
        resp = self.client.get(url, follow=True)

        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertContains(resp, Decimal('356.40'))  # 354.399
        self.assertContains(resp, 'number_of_items">5')
        self.assertContains(resp, 'cycle_start nowrap">Oct. 10, 2023, midnight')
        self.assertContains(resp, 'cycle_end nowrap">Nov. 10, 2023, midnight')

    def test_invoice_item_info_no_discount_item(self):
        self.invoice_item_recipe.make(
            billing_cycle_start=datetime(2023, 10, 10, 0, 0, 0, tzinfo=pytz.UTC),
            billing_cycle_end=datetime(2023, 11, 10, 0, 0, 0, tzinfo=pytz.UTC),
            service=InvoiceService.SAAS,
            base_gross_value=Decimal(345.20000),
        )

        url = reverse("admin:navision_invoice_changelist")
        resp = self.client.get(url, follow=True)

        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertContains(resp, Decimal('345.20'))
        self.assertContains(resp, 'number_of_items">1')
        self.assertContains(resp, 'cycle_start nowrap">Oct. 10, 2023, midnight')
        self.assertContains(resp, 'cycle_end nowrap">Nov. 10, 2023, midnight')


class TestInvoicingSummaryAdmin(DjangoTestCase):
    def setUp(self):
        super().setUp()
        self.user = self.login_admin()

    def create_summaries(self):
        invoicing_summary = baker.make(InvoicingSummary)
        baker.make(
            BusinessInvoiceSummary,
            report=invoicing_summary,
            invoice__source=InvoicePaymentSource.OFFLINE,
            _quantity=13,
        )
        baker.make(
            BusinessInvoiceSummary,
            report=invoicing_summary,
            invoice__source=InvoicePaymentSource.BRAINTREE,
            _quantity=29,
        )
        baker.make(
            BusinessInvoiceSummary,
            report=invoicing_summary,
            invoice__source=InvoicePaymentSource.STRIPE,
            _quantity=51,
        )
        return invoicing_summary

    def test_changelist_works(self):
        url = reverse("admin:navision_invoicingsummary_changelist")

        self.create_summaries()

        resp = self.client.get(url, follow=True)

        self.assertContains(resp, "93")  # 13 + 29 + 51

    def test_change_view_works(self):
        invoicing_summary = self.create_summaries()

        url = reverse("admin:navision_invoicingsummary_change", args=(invoicing_summary.id,))

        resp = self.client.get(url, follow=True)

        self.assertContains(resp, "Offline: 13")
        self.assertContains(resp, "Braintree: 29")
        self.assertContains(resp, "Stripe: 51")

    def create_objects(self):
        boost_items = baker.make(
            InvoiceItem,
            service=InvoiceService.BOOST,
            base_gross_value=15.00,
            base_tax_value=5.00,
            billing_cycle_start=timezone.make_aware(datetime(2023, 4, 12, 9, 0, 0)),
            billing_cycle_end=timezone.make_aware(datetime(2023, 5, 11, 9, 0, 0)),
            _quantity=2,
        )
        saas_items = baker.make(
            InvoiceItem,
            service=InvoiceService.SAAS,
            base_gross_value=10.00,
            base_tax_value=5.00,
            billing_cycle_start=timezone.make_aware(datetime(2023, 4, 12, 9, 0, 0)),
            billing_cycle_end=timezone.make_aware(datetime(2023, 5, 11, 9, 0, 0)),
            _quantity=3,
        )
        staffer_item = baker.make(
            InvoiceItem,
            service=InvoiceService.STAFFERS,
            base_gross_value=50.00,
            base_tax_value=10.00,
            billing_cycle_start=timezone.make_aware(datetime(2023, 4, 12, 9, 0, 0)),
            billing_cycle_end=timezone.make_aware(datetime(2023, 5, 11, 9, 0, 0)),
        )

        # discount item here to check if everything's working as intended:
        discount_item = baker.make(
            InvoiceItem,
            service=InvoiceService.DISCOUNT,
            base_gross_value=20.00,
            billing_cycle_start=timezone.make_aware(datetime(2023, 4, 12, 9, 0, 0)),
            billing_cycle_end=timezone.make_aware(datetime(2023, 5, 11, 9, 0, 0)),
        )

        sms_items = baker.make(
            InvoiceItem,
            service=InvoiceService.SMS,
            base_gross_value=25.00,
            base_tax_value=7.00,
            billing_cycle_start=timezone.make_aware(datetime(2023, 4, 12, 9, 0, 0)),
            billing_cycle_end=timezone.make_aware(datetime(2023, 5, 11, 9, 0, 0)),
            _quantity=2,
        )

        discounted_saas_item = baker.make(
            InvoiceItem,
            service=InvoiceService.SAAS,
            base_gross_value=20.00,
            base_tax_value=5.00,
            discount_gross_value=10.00,
            billing_cycle_start=timezone.make_aware(datetime(2023, 4, 12, 9, 0, 0)),
            billing_cycle_end=timezone.make_aware(datetime(2023, 5, 11, 9, 0, 0)),
        )

        boost_invoice = baker.make(Invoice, service=InvoiceService.BOOST, items=boost_items)
        saas_invoice = baker.make(Invoice, service=InvoiceService.SAAS, items=saas_items)
        staffer_invoice = baker.make(
            Invoice, service=InvoiceService.STAFFERS, items=[staffer_item, discount_item]
        )
        sms_invoice = baker.make(Invoice, service=InvoiceService.SMS, items=sms_items)
        discounted_saas_invoice = baker.make(
            Invoice, service=InvoiceService.SAAS, items=[discounted_saas_item]
        )

        summary = baker.make(InvoicingSummary)

        baker.make(BusinessInvoiceSummary, report=summary, invoice=boost_invoice)
        baker.make(BusinessInvoiceSummary, report=summary, invoice=saas_invoice)
        baker.make(BusinessInvoiceSummary, report=summary, invoice=staffer_invoice)
        baker.make(BusinessInvoiceSummary, report=summary, invoice=sms_invoice)
        baker.make(BusinessInvoiceSummary, report=summary, invoice=discounted_saas_invoice)

        return summary

    def test_admin_report_methods(self):
        summary = self.create_objects()

        url = reverse('admin:navision_invoicingsummary_change', args=(summary.id,))

        resp = self.client.get(url, follow=True)

        # expected Items per service field
        self.assertContains(resp, 'Boost: 2')
        self.assertContains(resp, 'SaaS: 4')
        self.assertContains(resp, 'Staffers: 1')
        self.assertContains(resp, 'SMS: 2')

        self.assertContains(resp, '54.00')  # Updated expected Items tax total field

        self.assertContains(resp, '150.00')  # Updated expected Items gross total field

        self.assertContains(resp, '96.00')  # Updated expected Items net total field

        # Billing cycle calculations:

        self.assertContains(resp, '2023-04-12 - 2023-05-11: 5')  # Invoice count per cycle
        self.assertContains(
            resp, '2023-04-12 - 2023-05-11: 9'
        )  # Updated invoice items count per cycle
        self.assertContains(resp, '2023-04-12 - 2023-05-11: 0')  # business count per cycle
        self.assertContains(
            resp, '2023-04-12 - 2023-05-11: 150.00'
        )  # Updated base_gross_total per cycle

    def test_admin_methods_tax_none(self):
        items = baker.make(
            InvoiceItem,
            service=InvoiceService.SAAS,
            base_gross_value=100.00,
            base_tax_value=None,
            _quantity=3,
        )
        invoice = baker.make(Invoice, service=InvoiceService.SAAS, items=items)
        summary = baker.make(InvoicingSummary)
        baker.make(BusinessInvoiceSummary, report=summary, invoice=invoice)
        url = reverse('admin:navision_invoicingsummary_change', args=(summary.id,))

        resp = self.client.get(url, follow=True)

        self.assertContains(resp, 'SaaS: 3')

        self.assertContains(resp, '300.00')
        self.assertContains(resp, '0')

    def test_billing_cycle_methods_with_discount_service(self):
        boost_item = baker.make(
            InvoiceItem,
            service=InvoiceService.BOOST,
            base_gross_value=15.00,
            base_tax_value=5.00,
            billing_cycle_start=timezone.make_aware(datetime(2023, 4, 12, 9, 0, 0)),
            billing_cycle_end=timezone.make_aware(datetime(2023, 5, 11, 9, 0, 0)),
        )
        saas_item = baker.make(
            InvoiceItem,
            service=InvoiceService.SAAS,
            base_gross_value=10.00,
            base_tax_value=5.00,
            discount_gross_value=5.00,
            billing_cycle_start=timezone.make_aware(datetime(2023, 4, 12, 9, 0, 0)),
            billing_cycle_end=timezone.make_aware(datetime(2023, 5, 11, 9, 0, 0)),
        )

        staffer_item = baker.make(
            InvoiceItem,
            service=InvoiceService.STAFFERS,
            base_gross_value=50.00,
            base_tax_value=10.00,
            billing_cycle_start=timezone.make_aware(datetime(2023, 4, 12, 9, 0, 0)),
            billing_cycle_end=timezone.make_aware(datetime(2023, 5, 11, 9, 0, 0)),
        )

        sms_item = baker.make(
            InvoiceItem,
            service=InvoiceService.SMS,
            base_gross_value=25.00,
            base_tax_value=7.00,
            billing_cycle_start=timezone.make_aware(datetime(2023, 4, 12, 9, 0, 0)),
            billing_cycle_end=timezone.make_aware(datetime(2023, 5, 11, 9, 0, 0)),
        )

        discount_item = baker.make(
            InvoiceItem,
            service=InvoiceService.DISCOUNT,
            base_gross_value=30.00,
            base_tax_value=0.00,
            billing_cycle_start=timezone.make_aware(datetime(2023, 4, 12, 9, 0, 0)),
            billing_cycle_end=timezone.make_aware(datetime(2023, 5, 11, 9, 0, 0)),
        )

        boost_invoice = baker.make(Invoice, service=InvoiceService.BOOST, items=[boost_item])
        saas_invoice = baker.make(Invoice, service=InvoiceService.SAAS, items=[saas_item])
        staffer_invoice = baker.make(
            Invoice, service=InvoiceService.STAFFERS, items=[staffer_item, discount_item]
        )
        sms_invoice = baker.make(Invoice, service=InvoiceService.SMS, items=[sms_item])

        summary = baker.make(InvoicingSummary)

        baker.make(BusinessInvoiceSummary, report=summary, invoice=boost_invoice)
        baker.make(BusinessInvoiceSummary, report=summary, invoice=saas_invoice)
        baker.make(BusinessInvoiceSummary, report=summary, invoice=staffer_invoice)
        baker.make(BusinessInvoiceSummary, report=summary, invoice=sms_invoice)

        url = reverse('admin:navision_invoicingsummary_change', args=(summary.id,))

        resp = self.client.get(url, follow=True)

        self.assertContains(resp, '2023-04-12 - 2023-05-11: 4')  # invoice count per cycle
        self.assertContains(
            resp, '2023-04-12 - 2023-05-11: 4'
        )  # invoice items count per cycle, no discount item
        self.assertContains(resp, '2023-04-12 - 2023-05-11: 0')  # business count per cycle
        self.assertContains(
            resp, '2023-04-12 - 2023-05-11: 65.00'
        )  # base_gross_total per cycle, both separate discount item and the discount from
        # the discounted SaaS deducted: 100-30-5=65

    def test_items_with_varying_quantities(self):
        boost_item = baker.make(
            InvoiceItem,
            service=InvoiceService.BOOST,
            base_gross_value=15.00,
            base_tax_value=5.00,
            billing_cycle_start=timezone.make_aware(datetime(2023, 4, 12, 9, 0, 0)),
            billing_cycle_end=timezone.make_aware(datetime(2023, 5, 11, 9, 0, 0)),
            quantity=2,
        )
        saas_item = baker.make(
            InvoiceItem,
            service=InvoiceService.SAAS,
            base_gross_value=10.00,
            base_tax_value=5.00,
            discount_gross_value=2.00,
            billing_cycle_start=timezone.make_aware(datetime(2023, 4, 12, 9, 0, 0)),
            billing_cycle_end=timezone.make_aware(datetime(2023, 5, 11, 9, 0, 0)),
            quantity=3,
        )
        discount_item = baker.make(
            InvoiceItem,
            service=InvoiceService.DISCOUNT,
            base_gross_value=10.00,
            base_tax_value=0.00,
            billing_cycle_start=timezone.make_aware(datetime(2023, 4, 12, 9, 0, 0)),
            billing_cycle_end=timezone.make_aware(datetime(2023, 5, 11, 9, 0, 0)),
            quantity=2,
        )

        boost_invoice = baker.make(
            Invoice, service=InvoiceService.BOOST, items=[boost_item, discount_item]
        )
        saas_invoice = baker.make(Invoice, service=InvoiceService.SAAS, items=[saas_item])

        summary = baker.make(InvoicingSummary)
        baker.make(BusinessInvoiceSummary, report=summary, invoice=boost_invoice)
        baker.make(BusinessInvoiceSummary, report=summary, invoice=saas_invoice)

        url = reverse('admin:navision_invoicingsummary_change', args=(summary.id,))

        resp = self.client.get(url, follow=True)

        # expected Items per service field
        self.assertContains(resp, 'Boost: 1')
        self.assertContains(resp, 'SaaS: 1')

        self.assertContains(resp, '25.00')  # expected Items tax total field

        self.assertContains(resp, '44.00')  # expected Items gross total field

        self.assertContains(resp, '19.00')  # expected Items net total field

        # Billing cycle calculations:

        self.assertContains(resp, '2023-04-12 - 2023-05-11: 2')  # invoice count per cycle
        self.assertContains(
            resp, '2023-04-12 - 2023-05-11: 2'
        )  # invoice items count per cycle, no discount item
        self.assertContains(resp, '2023-04-12 - 2023-05-11: 0')  # business count per cycle
        self.assertContains(resp, '2023-04-12 - 2023-05-11: 44.00')  # total amount per cycle


@override_settings(LIVE_DEPLOYMENT=True)
class TestInvoiceRemoveSelectedInvoicesActionAdmin(DjangoTestCase):
    def setUp(self):
        super().setUp()
        user = user_recipe.make(
            groups=[baker.make(Group, name=GroupName.NAVISION_ADMIN)],
            is_superuser=False,
            is_staff=True,
        )
        self.user = self.login_admin(admin=user)
        _ = [
            self.user.user_permissions.add(
                Permission.objects.get_by_natural_key(
                    f'{action}_invoice',
                    'navision',
                    'invoice',
                )
            )
            for action in ['change', 'add', 'view']
        ]
        self.url = reverse("admin:navision_invoice_changelist")

        self.invoice = baker.make(
            Invoice,
            source=InvoicePaymentSource.BRAINTREE,
            status=Invoice.Status.INIT,
        )
        self.sent_invoice = baker.make(
            Invoice,
            source=InvoicePaymentSource.OFFLINE,
            status=Invoice.Status.SENT,
        )

    @staticmethod
    def run_action(*invoices):
        return {
            'action': 'remove_selected_invoices',
            '_selected_action': invoices,
        }

    def test_proper_invoice_selected(self):
        data = self.run_action(self.invoice.id)
        resp = self.client.post(self.url, data, follow=True)
        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertFalse(Invoice.objects.filter(id=self.invoice.id).exists())
        self.assertContains(
            resp, f'These invoices have been successfully removed: {self.invoice.id}'
        )
        self.assertNotContains(resp, '- make sure that selected invoices are in INIT status')

    def test_improper_invoice_selected(self):
        data = self.run_action(self.sent_invoice.id)
        resp = self.client.post(self.url, data, follow=True)
        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertTrue(Invoice.objects.filter(id=self.invoice.id).exists())
        self.assertTrue(Invoice.objects.filter(id=self.sent_invoice.id).exists())
        self.assertContains(
            resp,
            f'These invoices cannot be removed: {self.sent_invoice.id} '
            f'- make sure that selected invoices are in INIT status',
        )
        self.assertNotContains(resp, 'These invoices have been successfully removed')

    def test_both_invoices_selected(self):
        data = self.run_action(self.invoice.id, self.sent_invoice.id)
        resp = self.client.post(self.url, data, follow=True)
        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertFalse(Invoice.objects.filter(id=self.invoice.id).exists())
        self.assertTrue(Invoice.objects.filter(id=self.sent_invoice.id).exists())
        self.assertContains(
            resp,
            f'These invoices have been successfully removed: {self.invoice.id} \n'
            f'These invoices cannot be removed: {self.sent_invoice.id} '
            f'- make sure that selected invoices are in INIT status',
        )

    def test_remove_invoices_no_permission(self):
        self.user.groups.set([])
        data = self.run_action(self.invoice.id)
        resp = self.client.post(self.url, data, follow=True)
        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertTrue(Invoice.objects.filter(id=self.invoice.id).exists())


class TestRemoveSelectedTestInvoicesActionAdmin(DjangoTestCase):
    def setUp(self):
        super().setUp()
        user = user_recipe.make(
            groups=[baker.make(Group, name=GroupName.NAVISION_ADMIN)],
            is_superuser=False,
            is_staff=True,
        )
        self.user = self.login_admin(admin=user)
        for action in ['change', 'add', 'view']:
            self.user.user_permissions.add(
                Permission.objects.get_by_natural_key(
                    f'{action}_invoice',
                    'navision',
                    'invoice',
                )
            )
        self.url = reverse("admin:navision_invoice_changelist")

        self.init_prod_invoice = baker.make(
            Invoice,
            status=Invoice.Status.INIT,
            is_production=True,
        )
        self.init_test_invoice = baker.make(
            Invoice,
            status=Invoice.Status.INIT,
            is_production=False,
        )
        self.sent_prod_invoice = baker.make(
            Invoice,
            status=Invoice.Status.SENT,
            is_production=True,
        )
        self.sent_test_invoice = baker.make(
            Invoice,
            status=Invoice.Status.SENT,
            is_production=False,
        )

    @staticmethod
    def run_action(*invoices):
        return {
            'action': 'remove_test_invoices',
            '_selected_action': invoices,
        }

    def test_all_invoices_selected(self):
        data = self.run_action(
            self.sent_test_invoice.id,
            self.sent_prod_invoice.id,
            self.init_test_invoice.id,
            self.init_prod_invoice.id,
        )
        resp = self.client.post(self.url, data, follow=True)
        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertFalse(Invoice.objects.filter(is_production=False).exists())
        self.assertEqual(2, Invoice.objects.filter(is_production=True).count())
        self.assertContains(
            resp,
            f'These invoices have been successfully removed: '
            f'{self.sent_test_invoice.id}, {self.init_test_invoice.id} \n'
            f'These invoices cannot be removed: '
            f'{self.sent_prod_invoice.id}, {self.init_prod_invoice.id} '
            f'- make sure the selected invoices are test invoices',
        )

    def test_remove_invoices_no_permission(self):
        self.user.groups.set([])
        data = self.run_action(self.sent_test_invoice.id)
        resp = self.client.post(self.url, data, follow=True)
        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertTrue(Invoice.objects.filter(id=self.sent_test_invoice.id).exists())


def update_synced_at_field(
    idx: int | list[int], model: Invoice | Merchant, exc=False
) -> Optional[Invoice | Merchant]:
    if exc:
        return
    if isinstance(idx, int):
        idx = [idx]
    return model.objects.filter(id__in=idx).update(synced_at=tznow())


@patch(
    'webapps.navision.tasks.api.create_or_update_merchant_in_navision.run',
    side_effect=partial(update_synced_at_field, model=Merchant),
)
@patch(
    'webapps.navision.tasks.api.send_not_synced_invoices_to_navision.delay',
    side_effect=partial(update_synced_at_field, model=Invoice),
)
@override_settings(LIVE_DEPLOYMENT=True)
class TestInvoiceSyncSelectedInvoicesAndMerchantsActionAdmin(DjangoTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.region = usa_recipe.make()
        baker.make(NavisionSettings, region=cls.region)
        cls.url = reverse("admin:navision_invoice_changelist")

    def setUp(self):
        super().setUp()
        user = user_recipe.make(
            groups=[baker.make('Group', name=GroupName.NAVISION_ADMIN)],
            is_staff=True,
        )
        self.user = self.login_admin(admin=user)
        _ = [
            self.user.user_permissions.add(
                Permission.objects.get_by_natural_key(
                    f'{action}_invoice',
                    'navision',
                    'invoice',
                )
            )
            for action in ['change', 'add', 'view']
        ]
        self.invoice_1 = baker.make(
            Invoice,
            status=Invoice.Status.INIT,
            synced_at=None,
            queued_at=None,
            approved=True,
        )
        self.merchant_1 = self.invoice_1.merchant

        self.invoice_2 = baker.make(
            Invoice,
            status=Invoice.Status.ERROR,
            synced_at=None,
            queued_at=tznow() - timedelta(hours=3),
            approved=True,
        )
        self.merchant_2 = self.invoice_2.merchant

        self.queued_invoice = baker.make(
            Invoice,
            status=Invoice.Status.INIT,
            synced_at=None,
            queued_at=tznow(),
            approved=True,
        )
        self.merchant_3 = self.queued_invoice.merchant

    @staticmethod
    def run_action(*invoices):
        return {
            'action': 'sync_selected_invoices',
            '_selected_action': invoices,
        }

    def test_sync_not_approved_invoice(self, mock_invoices, mock_merchants):
        self.invoice_1.approved = False
        self.invoice_1.save()
        self.invoice_2.approved = False
        self.invoice_2.save()

        data = self.run_action(self.invoice_1.id, self.invoice_2.id)
        resp = self.client.post(self.url, data, follow=True)

        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertEqual(0, Invoice.objects.filter(approved=False).count())

        self.assertEqual(2, mock_merchants.call_count)
        expected_merchants_calls = [call(self.merchant_1.id), call(self.merchant_2.id)]
        mock_merchants.assert_has_calls(expected_merchants_calls, any_order=True)
        mock_invoices.assert_called_once_with([self.invoice_2.id, self.invoice_1.id])

        self.assertContains(
            resp,
            f'These merchants have been successfully synced: '
            f'{self.merchant_1.id}, {self.merchant_2.id} \n'
            f'These invoices have been scheduled for sync, check them in a while: '
            f'{self.invoice_1.id}, {self.invoice_2.id} \n',
        )
        self.assertNotContains(resp, 'These invoices cannot be synced')

    def test_sync_invoices_and_merchants(self, mock_invoices, mock_merchants):
        data = self.run_action(self.invoice_1.id, self.invoice_2.id)
        resp = self.client.post(self.url, data, follow=True)
        self.assertEqual(status.HTTP_200_OK, resp.status_code)

        self.assertEqual(2, mock_merchants.call_count)
        expected_merchants_calls = [call(self.merchant_2.id), call(self.merchant_1.id)]
        mock_merchants.assert_has_calls(expected_merchants_calls, any_order=True)
        mock_invoices.assert_called_once_with([self.invoice_2.id, self.invoice_1.id])

        self.assertContains(
            resp,
            f'These merchants have been successfully synced: '
            f'{self.merchant_1.id}, {self.merchant_2.id} \n'
            f'These invoices have been scheduled for sync, check them in a while: '
            f'{self.invoice_1.id}, {self.invoice_2.id} \n',
        )
        self.assertNotContains(resp, 'Merchants not synced properly')
        self.assertNotContains(resp, 'These invoices cannot be synced')

    def test_sync_invoices_with_synced_merchant_already(self, mock_invoices, mock_merchants):
        Merchant.objects.update(synced_at=tznow())
        data = self.run_action(self.invoice_1.id, self.invoice_2.id)
        resp = self.client.post(self.url, data, follow=True)
        self.assertEqual(status.HTTP_200_OK, resp.status_code)

        self.assertEqual(0, mock_merchants.call_count)
        mock_invoices.assert_called_once_with([self.invoice_2.id, self.invoice_1.id])

        self.assertNotContains(resp, 'Merchants have been successfully synced')
        self.assertNotContains(resp, 'Merchants not synced properly')
        self.assertNotContains(resp, 'These invoices cannot be synced')
        self.assertContains(
            resp,
            f'These invoices have been scheduled for sync, check them in a while: '
            f'{self.invoice_1.id}, {self.invoice_2.id} ',
        )

    def test_many_invoices_with_one_merchant(self, mock_invoices, mock_merchants):
        self.invoice_2.merchant = self.merchant_1
        self.invoice_2.save()
        data = self.run_action(self.invoice_1.id, self.invoice_2.id, self.queued_invoice.id)
        resp = self.client.post(self.url, data, follow=True)
        self.assertEqual(status.HTTP_200_OK, resp.status_code)

        mock_merchants.assert_called_once_with(self.merchant_1.id)
        mock_invoices.assert_called_once_with([self.invoice_2.id, self.invoice_1.id])

        self.assertContains(
            resp,
            f'These merchants have been successfully synced: {self.merchant_1.id} \n'
            f'These invoices have been scheduled for sync, check them in a while: '
            f'{self.invoice_1.id}, {self.invoice_2.id} \n'
            f'These invoices cannot be synced: {self.queued_invoice.id}',
        )
        self.assertNotContains(resp, 'Merchants not synced properly')

    def test_error_while_sync_merchant(self, _mock_invoices, mock_merchants):
        mock_merchants.side_effect = partial(update_synced_at_field, model=Merchant, exc=True)

        data = self.run_action(self.invoice_1.id, self.invoice_2.id)
        resp = self.client.post(self.url, data, follow=True)

        self.assertEqual(status.HTTP_200_OK, resp.status_code)

        self.assertContains(
            resp, f'Merchants not synced properly: {self.merchant_1.id}, {self.merchant_2.id}. '
        )
        self.assertContains(
            resp, f'These invoices cannot be synced: {self.invoice_1.id}, {self.invoice_2.id} '
        )
        self.assertNotContains(resp, 'These merchants have been successfully synced')
        self.assertNotContains(resp, 'These invoices have been scheduled for sync')

    def test_synced_invoice_selected(self, mock_invoices, mock_merchants):
        Invoice.objects.update(synced_at=tznow(), status=Invoice.Status.SENT)
        data = self.run_action(self.invoice_1.id, self.invoice_2.id)
        resp = self.client.post(self.url, data, follow=True)
        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertEqual(0, mock_merchants.call_count)
        self.assertEqual(0, mock_invoices.call_count)
        self.assertNotContains(resp, 'Merchants have been successfully synced')
        self.assertNotContains(resp, 'Merchants not synced properly')
        self.assertNotContains(resp, 'Invoices have been successfully synced')
        self.assertContains(
            resp,
            f'These invoices cannot be synced: {self.invoice_1.id}, {self.invoice_2.id}',
        )

    def test_sync_hundred_invoices(self, mock_invoices, mock_merchants):
        mock_invoices.side_effect = None
        mock_merchants.side_effect = None

        invoices = baker.make(
            Invoice,
            _quantity=100,
            status=Invoice.Status.INIT,
            synced_at=None,
            queued_at=None,
        )

        data = self.run_action(*[invoice.id for invoice in invoices])
        resp = self.client.post(self.url, data, follow=True)
        self.assertEqual(status.HTTP_200_OK, resp.status_code)

    def test_sync_invoices_without_permission(self, mock_invoices, mock_merchants):
        self.user.groups.set([])
        data = self.run_action(self.invoice_1.id)
        resp = self.client.post(self.url, data, follow=True)
        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertEqual(0, mock_merchants.call_count)
        self.assertEqual(0, mock_invoices.call_count)


class TestApproveSelectedInvoices(DjangoTestCase):
    @property
    def url(self):
        return reverse("admin:navision_invoice_changelist")

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.region = usa_recipe.make()
        baker.make(NavisionSettings, region=cls.region)

    def setUp(self):
        super().setUp()
        user = user_recipe.make(
            groups=[baker.make('Group', name=GroupName.NAVISION_OPERATIONS)],
            is_staff=True,
        )
        self.user = self.login_admin(admin=user)
        _ = [
            self.user.user_permissions.add(
                Permission.objects.get_by_natural_key(
                    f'{action}_invoice',
                    'navision',
                    'invoice',
                )
            )
            for action in ['change', 'add', 'view']
        ]
        baker.make(Invoice, approved=False, _quantity=3)
        baker.make(Invoice, approved=True, _quantity=3)
        baker.make(Invoice, approved=None, _quantity=3)

    @staticmethod
    def run_action(invoices):
        return {
            'action': 'approve_selected_invoices',
            '_selected_action': invoices,
        }

    def test_approve_invoices(self):
        data = self.run_action(list(Invoice.objects.all().values_list('id', flat=True)))
        resp = self.client.post(self.url, data, follow=True)
        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertContains(resp, 'Invoices approved')
        self.assertEqual(9, Invoice.objects.filter(approved=True).count())
        self.assertEqual(
            0, Invoice.objects.filter(Q(approved=False) | Q(approved__isnull=True)).count()
        )

    def test_no_permission(self):
        self.user.groups.set([])

        data = self.run_action(list(Invoice.objects.all().values_list('id', flat=True)))
        resp = self.client.post(self.url, data, follow=True)
        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertNotContains(resp, 'Invoices approved')
        self.assertEqual(3, Invoice.objects.filter(approved=True).count())
        self.assertEqual(
            6, Invoice.objects.filter(Q(approved=False) | Q(approved__isnull=True)).count()
        )


class TestApproveInvoicesSelectedSummaries(DjangoTestCase):
    @property
    def url(self):
        return reverse("admin:navision_invoicingsummary_changelist")

    def setUp(self):
        super().setUp()
        user = user_recipe.make(
            groups=[baker.make('Group', name=GroupName.NAVISION_ADMIN)],
            is_superuser=False,
            is_staff=True,
        )
        self.user = self.login_admin(admin=user)

        _ = [
            self.user.user_permissions.add(
                Permission.objects.get_by_natural_key(
                    f'{action}_invoicingsummary',
                    'navision',
                    'invoicingsummary',
                )
            )
            for action in ['change', 'add', 'view']
        ]

    @staticmethod
    def create_summary(approved):
        summary = baker.make(InvoicingSummary)
        baker.make(BusinessInvoiceSummary, invoice__approved=approved, report=summary, _quantity=5)
        return summary

    @staticmethod
    def run_action(*invoicing_summaries):
        return {
            'action': 'approve_invoices',
            '_selected_action': invoicing_summaries,
        }

    @parameterized.expand(
        [
            (False, 0, 10),
            (True, 10, 10),
        ]
    )
    def test_approve_invoice_summaries(self, approved, n_invoices_before, n_invoices_after):
        summary_1 = self.create_summary(approved)
        summary_2 = self.create_summary(approved)
        self.create_summary(False)

        self.assertEqual(n_invoices_before, Invoice.objects.filter(approved=True).count())

        data = self.run_action(summary_1.id, summary_2.id)
        resp = self.client.post(self.url, data, follow=True)

        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertContains(resp, 'Invoices approved, check them in a while')
        self.assertEqual(n_invoices_after, Invoice.objects.filter(approved=True).count())

    def test_approve_invoice_summaries_with_some_approved(self):
        summary = baker.make(InvoicingSummary)
        baker.make(BusinessInvoiceSummary, invoice__approved=False, report=summary, _quantity=3)
        baker.make(BusinessInvoiceSummary, invoice__approved=True, report=summary, _quantity=2)

        self.assertEqual(2, Invoice.objects.filter(approved=True).count())
        self.assertEqual(3, Invoice.objects.filter(approved=False).count())

        data = self.run_action(summary.id)
        resp = self.client.post(self.url, data, follow=True)

        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertContains(resp, 'Invoices approved, check them in a while')
        self.assertEqual(5, Invoice.objects.filter(approved=True).count())

    def test_approve_invoices_no_permissions(self):
        self.user.groups.set([])

        summary_1 = self.create_summary(False)
        summary_2 = self.create_summary(False)

        self.assertEqual(0, Invoice.objects.filter(approved=True).count())

        data = self.run_action(summary_1.id, summary_2.id)
        resp = self.client.post(self.url, data, follow=True)

        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertEqual(0, Invoice.objects.filter(approved=True).count())


class TestRemoveUnapprovedInvoicesAction(DjangoTestCase):
    @property
    def url(self):
        return reverse("admin:navision_invoicingsummary_changelist")

    def setUp(self):
        super().setUp()
        user = user_recipe.make(
            groups=[baker.make('Group', name=GroupName.NAVISION_OPERATIONS)],
            is_superuser=False,
            is_staff=True,
        )
        self.user = self.login_admin(admin=user)

        _ = [
            self.user.user_permissions.add(
                Permission.objects.get_by_natural_key(
                    f'{action}_invoicingsummary',
                    'navision',
                    'invoicingsummary',
                )
            )
            for action in ['change', 'add', 'view']
        ]

        self.expected_msg = (
            'Unapproved invoices from selected summaries have been scheduled for deletion, '
            'check it in a while.'
        )

    @staticmethod
    def create_summary(approved):
        summary = baker.make(InvoicingSummary)
        baker.make(BusinessInvoiceSummary, invoice__approved=approved, report=summary, _quantity=5)
        return summary

    @staticmethod
    def run_action(*invoicing_summaries):
        return {
            'action': 'remove_unapproved_invoices',
            '_selected_action': invoicing_summaries,
        }

    @parameterized.expand(
        [
            (False, 15, 5),
            (True, 15, 15),
        ]
    )
    def test_remove_unapproved_invoices(self, approved, n_invoices_before, n_invoices_after):
        summary_1 = self.create_summary(approved)
        summary_2 = self.create_summary(approved)
        self.create_summary(False)

        self.assertEqual(n_invoices_before, Invoice.objects.count())

        data = self.run_action(summary_1.id, summary_2.id)
        resp = self.client.post(self.url, data, follow=True)

        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertContains(resp, self.expected_msg)
        self.assertEqual(n_invoices_after, Invoice.objects.count())

    def test_remove_from_invoice_summaries_with_some_approved(self):
        summary = baker.make(InvoicingSummary)
        baker.make(BusinessInvoiceSummary, invoice__approved=False, report=summary, _quantity=3)
        baker.make(BusinessInvoiceSummary, invoice__approved=True, report=summary, _quantity=2)

        self.assertEqual(2, Invoice.objects.filter(approved=True).count())
        self.assertEqual(3, Invoice.objects.filter(approved=False).count())

        data = self.run_action(summary.id)
        resp = self.client.post(self.url, data, follow=True)

        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertContains(resp, self.expected_msg)
        self.assertEqual(2, Invoice.objects.count())

    def test_remove_invoices_no_permissions(self):
        self.user.groups.set([])

        summary_1 = self.create_summary(False)
        summary_2 = self.create_summary(False)

        self.assertEqual(10, Invoice.objects.count())

        data = self.run_action(summary_1.id, summary_2.id)
        resp = self.client.post(self.url, data, follow=True)

        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertEqual(10, Invoice.objects.count())

    def test_invoice_items_are_removed_with_the_action(self):
        invoice = baker.make(Invoice, approved=False)
        baker.make(InvoiceItem, invoice=invoice, _quantity=3)
        summary = baker.make(InvoicingSummary)
        baker.make(BusinessInvoiceSummary, invoice=invoice, report=summary)

        self.assertEqual(Invoice.objects.count(), 1)
        self.assertEqual(InvoiceItem.objects.count(), 3)

        data = self.run_action(summary.id)
        resp = self.client.post(self.url, data, follow=True)

        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertContains(resp, self.expected_msg)

        self.assertEqual(Invoice.objects.count(), 0)
        self.assertEqual(InvoiceItem.objects.count(), 0)

    def test_sent_and_error_invoices_not_deleted(self):
        summary = baker.make(InvoicingSummary)
        baker.make(
            BusinessInvoiceSummary,
            invoice__approved=False,
            invoice__status=Invoice.Status.SENT,
            report=summary,
            _quantity=3,
        )
        baker.make(
            BusinessInvoiceSummary,
            invoice__approved=False,
            invoice__status=Invoice.Status.INIT,
            report=summary,
            _quantity=2,
        )
        baker.make(
            BusinessInvoiceSummary,
            invoice__approved=False,
            invoice__status=Invoice.Status.ERROR,
            report=summary,
            _quantity=3,
        )

        self.assertEqual(Invoice.objects.count(), 8)

        data = self.run_action(summary.id)
        resp = self.client.post(self.url, data, follow=True)

        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertContains(resp, self.expected_msg)

        self.assertEqual(Invoice.objects.count(), 6)
        self.assertEqual(Invoice.objects.filter(status=Invoice.Status.INIT).count(), 0)
        self.assertEqual(Invoice.objects.filter(status=Invoice.Status.ERROR).count(), 3)
        self.assertEqual(Invoice.objects.filter(status=Invoice.Status.SENT).count(), 3)


class TestRemoveTestInvoicesInSelectedSummaries(DjangoTestCase):
    def setUp(self):
        super().setUp()
        user = user_recipe.make(
            groups=[baker.make(Group, name=GroupName.NAVISION_ADMIN)],
            is_superuser=False,
            is_staff=True,
        )
        self.user = self.login_admin(admin=user)
        for action in ['change', 'add', 'view']:
            self.user.user_permissions.add(
                Permission.objects.get_by_natural_key(
                    f'{action}_invoicingsummary',
                    'navision',
                    'invoicingsummary',
                )
            )
        self.url = reverse("admin:navision_invoicingsummary_changelist")

        business = baker.make('business.Business')

        self.summary_1 = baker.make(InvoicingSummary, invoicing_target=tznow())
        baker.make(
            BusinessInvoiceSummary,
            report=self.summary_1,
            business=business,
            invoice__status=Invoice.Status.INIT,
            invoice__is_production=False,
        )
        baker.make(
            BusinessInvoiceSummary,
            report=self.summary_1,
            business=business,
            invoice__status=Invoice.Status.SENT,
            invoice__is_production=False,
        )
        baker.make(
            BusinessInvoiceSummary,
            report=self.summary_1,
            business=business,
            invoice__status=Invoice.Status.INIT,
            invoice__is_production=True,
        )

        self.summary_2 = baker.make(InvoicingSummary, invoicing_target=tznow())
        baker.make(
            BusinessInvoiceSummary,
            report=self.summary_1,
            business=business,
            invoice__status=Invoice.Status.INIT,
            invoice__is_production=None,
        )
        baker.make(
            BusinessInvoiceSummary,
            report=self.summary_2,
            business=business,
            invoice__status=Invoice.Status.SENT,
            invoice__is_production=True,
        )
        baker.make(
            BusinessInvoiceSummary,
            report=self.summary_2,
            business=business,
            invoice__status=Invoice.Status.SENT,
            invoice__is_production=False,
        )

        self.expected_msg = 'Test invoices have been scheduled for deletion, check it in a while.'

    @staticmethod
    def run_action(*summaries):
        return {
            'action': 'remove_test_invoices_in_selected_summaries',
            '_selected_action': summaries,
        }

    def test_remove_only_test_invoices_when_is_production_field_is_false(self):
        self.assertEqual(6, Invoice.objects.count())
        self.assertTrue(Invoice.objects.filter(is_production=False).exists())

        data = self.run_action(self.summary_1.id, self.summary_2.id)
        resp = self.client.post(self.url, data, follow=True)

        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertEqual(2, InvoicingSummary.objects.count())
        self.assertEqual(3, Invoice.objects.count())
        self.assertFalse(Invoice.objects.filter(is_production=False).exists())
        self.assertContains(resp, self.expected_msg)

    def test_remove_only_test_invoices_from_one_summary(self):
        self.assertEqual(6, Invoice.objects.count())
        self.assertTrue(Invoice.objects.filter(is_production=False).exists())

        data = self.run_action(self.summary_1.id)
        resp = self.client.post(self.url, data, follow=True)

        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertEqual(2, InvoicingSummary.objects.count())
        self.assertEqual(4, Invoice.objects.count())
        self.assertEqual(1, Invoice.objects.filter(is_production=False).count())
        self.assertContains(resp, self.expected_msg)

    def test_remove_invoices_no_permissions(self):
        self.user.groups.set([])

        data = self.run_action(self.summary_1.id, self.summary_2.id)
        resp = self.client.post(self.url, data, follow=True)

        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertEqual(6, Invoice.objects.count())
        self.assertTrue(Invoice.objects.filter(is_production=False).exists())


class TestTaxRateAdmin(DjangoTestCase):
    def setUp(self):
        super().setUp()
        self.user = self.login_admin()

    def test_changelist_works(self):
        url = reverse('admin:navision_taxrate_changelist')

        baker.make(
            TaxRate,
            tax_rate=Decimal('0.1725'),
            _fill_optional=True,
        )

        resp = self.client.get(url, follow=True)

        self.assertContains(resp, '0.1725')

    def test_change_view_works(self):
        tax_rate = baker.make(
            TaxRate,
            tax_rate=Decimal('0.1725'),
            _fill_optional=True,
        )

        url = reverse('admin:navision_taxrate_change', args=(tax_rate.id,))

        resp = self.client.get(url, follow=True)
        self.assertContains(resp, '0.1725')


class TaxGroupsAdminPermissions(DjangoTestCase):
    @staticmethod
    def url(action, *args):
        if args:
            return reverse(f'admin:navision_taxgroup_{action}', args=args)
        return reverse(f'admin:navision_taxgroup_{action}')

    def setUp(self) -> None:
        super().setUp()

        user = user_recipe.make(
            groups=[baker.make(Group, name=GroupName.NAVISION_ADMIN)],
            is_superuser=False,
            is_staff=True,
        )
        self.user = self.login_admin(admin=user)

    @override_settings(API_COUNTRY=Country.ES, LIVE_DEPLOYMENT=True)
    def test_superuser_in_es(self):
        self.user.groups.set([])
        self.user.is_superuser = True
        self.user.save()
        tax_group = baker.make(
            TaxGroup,
            name='Spain-Nowa-Tax-Grupa',
            _fill_optional=True,
        )

        resp = self.client.post(self.url('change', tax_group.id), follow=True)
        self.assertEqual(status.HTTP_200_OK, resp.status_code)
        self.assertContains(resp, 'Spain-Nowa')

    @override_settings(API_COUNTRY=Country.ES, LIVE_DEPLOYMENT=True)
    def test_no_nav_superuser_in_es__prod_env(self):
        self.user.groups.set([])
        tax_group = baker.make(
            TaxGroup,
            name='Spain-Nowa-Tax-Grupa',
            _fill_optional=True,
        )

        resp = self.client.post(self.url('change', tax_group.id), follow=True)
        self.assertEqual(status.HTTP_403_FORBIDDEN, resp.status_code)

    @override_settings(API_COUNTRY=Country.ES)
    def test_no_nav_superuser_in_es__test_env(self):
        self.user.groups.set([])
        tax_group = baker.make(
            TaxGroup,
            name='Spain-Nowa-Tax-Grupa',
            _fill_optional=True,
        )

        resp = self.client.post(self.url('change', tax_group.id), follow=True)
        self.assertEqual(status.HTTP_403_FORBIDDEN, resp.status_code)

    @override_settings(API_COUNTRY=Country.ES, LIVE_DEPLOYMENT=True)
    def test_nav_superuser_in_es(self):
        tax_group = baker.make(
            TaxGroup,
            name='Spain-Stara-Tax-Grupa',
            _fill_optional=True,
        )

        resp = self.client.post(
            self.url('change', tax_group.id), {'name': 'Spain-Nowa'}, follow=True
        )
        self.assertContains(resp, 'Spain-Nowa')

    @parameterized.expand(
        [
            (Country.ES,),
            (Country.US,),
        ]
    )
    def test_nav_superuser_in_us_changelist(self, api_country):
        with override_settings(API_COUNTRY=api_country):
            resp = self.client.get(self.url('changelist'), {}, follow=True)
            self.assertEqual(status.HTTP_200_OK, resp.status_code)

    def test_nav_superuser_in_us__test_env(self):
        resp = self.client.post(self.url('add'), {}, follow=True)
        self.assertEqual(status.HTTP_403_FORBIDDEN, resp.status_code)

    @override_settings(LIVE_DEPLOYMENT=True)
    def test_nav_superuser_in_us__prod_env(self):
        resp = self.client.post(self.url('add'), {}, follow=True)
        self.assertEqual(status.HTTP_403_FORBIDDEN, resp.status_code)

    @parameterized.expand(
        [
            ('change',),
            ('delete',),
        ]
    )
    @override_settings(LIVE_DEPLOYMENT=True)
    def test_nav_superuser_in_us_change(self, action):
        tax_group = baker.make(
            TaxGroup,
            name='US-Stara-Tax-Grupa',
            _fill_optional=True,
        )

        resp = self.client.post(self.url(action, tax_group.id), {}, follow=True)
        self.assertEqual(status.HTTP_403_FORBIDDEN, resp.status_code)


class TestNavisionSettings(DjangoTestCase):
    def setUp(self):
        self.navision_settings = navision_settings_recipe.make()
        self.country = self.navision_settings.region

        return super().setUp()

    def test_changelist_works(self):
        url = reverse('admin:navision_navisionsettings_changelist')

        response = self.client.get(url, follow=True)

        self.assertEqual(response.status_code, 200)

    def test_change_view_works(self):
        url = reverse('admin:navision_navisionsettings_change', args=(self.navision_settings.id,))

        response = self.client.get(url, follow=True)

        self.assertEqual(response.status_code, 200)

    @freeze_time(datetime(2022, 6, 1))
    @patch('webapps.navision.admin.create_new_billing_saas_invoices_task.delay')
    @patch('webapps.navision.admin.create_boost_online_invoices_task.delay')
    def test_trigger_saas_online_invoicing(
        self,
        braintree_online_mock,
        saas_online_mock,
    ):
        form = NavisionSettingsForm(
            instance=self.navision_settings,
            data={
                'region': self.country.id,
                'sms_offline_invoicing_day': 1,
                'saas_offline_invoicing_day': 7,
                'boost_offline_invoicing_day': 15,
                'merchants_send_per_hour': 300,
                'invoices_send_per_hour': 300,
                'run_online_saas_invoicing': True,
                'run_online_invoicing_from': '2022-05-15',
                'run_online_invoicing_to': '2022-05-20',
            },
        )

        self.assertTrue(form.is_valid(), form.errors)

        form.schedule_online_invoicing()

        self.assertEqual(saas_online_mock.call_count, 6)

        expected_calls = [
            call(offset_days=17, user_id=None),  # 2022-05-15
            call(offset_days=16, user_id=None),  # 2022-05-16
            call(offset_days=15, user_id=None),  # 2022-05-17
            call(offset_days=14, user_id=None),  # 2022-05-18
            call(offset_days=13, user_id=None),  # 2022-05-19
            call(offset_days=12, user_id=None),  # 2022-05-20
        ]

        saas_online_mock.assert_has_calls(
            expected_calls,
            any_order=True,
        )

        braintree_online_mock.assert_not_called()

    @parameterized.expand(
        [
            ('2022-05-20', 'End of date range cannot be set before start'),
            ('2022-31-31', 'Enter a valid date.'),
            ('', 'This field is required.'),
            (None, 'This field is required.'),
        ]
    )
    @freeze_time(datetime(2022, 6, 1))
    def test_trigger_saas_online_invoicing_invalid_from_to_dates(self, date_to, expected_error):
        form = NavisionSettingsForm(
            instance=self.navision_settings,
            data={
                'region': self.country.id,
                'sms_offline_invoicing_day': 1,
                'saas_offline_invoicing_day': 7,
                'boost_offline_invoicing_day': 15,
                'merchants_send_per_hour': 300,
                'invoices_send_per_hour': 300,
                'run_online_saas_invoicing': True,
                'run_online_invoicing_from': '2022-05-25',
                'run_online_invoicing_to': date_to,
            },
        )

        self.assertFalse(form.is_valid())
        expected_dates_error = {'run_online_invoicing_to': [expected_error]}
        self.assertTrue(expected_dates_error.items() <= form.errors.items())

    @parameterized.expand(
        [
            (Country.US, 'live', '2022-05-20', '2022-06-02', False, True),
            (Country.US, 'test', '2022-05-20', '2022-06-02', True, False),
            (Country.US, 'live', '2022-05-20', '2022-06-01', True, False),
            (Country.US, 'test', '2022-05-20', '2022-06-01', True, False),
            (Country.PL, 'live', '20.05.2022', '02.06.2022', False, True),
            (Country.PL, 'test', '20.05.2022', '02.06.2022', True, False),
            (Country.PL, 'live', '20.05.2022', '01.06.2022', True, False),
            (Country.PL, 'test', '20.05.2022', '01.06.2022', True, False),
        ]
    )
    @freeze_time(datetime(2022, 6, 1))
    def test_trigger_saas_online_invoicing_to_date_after_today(
        self, country, deploy_lvl, date_from, date_to, is_valid, is_error
    ):
        with override_settings(
            LANGUAGE_CODE=CountryConfig(country).language_code,
            **get_settings(deployment_level=deploy_lvl),
        ):
            form = NavisionSettingsForm(
                instance=self.navision_settings,
                data={
                    'region': self.country.id,
                    'sms_offline_invoicing_day': 1,
                    'saas_offline_invoicing_day': 7,
                    'boost_offline_invoicing_day': 15,
                    'merchants_send_per_hour': 300,
                    'invoices_send_per_hour': 300,
                    'run_online_saas_invoicing': True,
                    'run_online_invoicing_from': date_from,
                    'run_online_invoicing_to': date_to,
                },
            )

            self.assertEqual(is_valid, form.is_valid())

        expected_dates_error = {
            'run_online_invoicing_to': ['You cannot set a future date on production']
        }
        self.assertEqual(is_error, expected_dates_error.items() <= form.errors.items())

    @freeze_time(datetime(2022, 4, 9))
    @patch('webapps.navision.admin.create_new_billing_saas_invoices_task.delay')
    @patch('webapps.navision.admin.create_boost_online_invoices_task.delay')
    def test_trigger_boost_online_invoicing(
        self,
        braintree_online_mock,
        saas_online_mock,
    ):
        form = NavisionSettingsForm(
            instance=self.navision_settings,
            data={
                'region': self.country.id,
                'sms_offline_invoicing_day': 1,
                'saas_offline_invoicing_day': 7,
                'boost_offline_invoicing_day': 15,
                'merchants_send_per_hour': 300,
                'invoices_send_per_hour': 300,
                'run_online_boost_invoicing': True,
                'run_online_invoicing_from': '2022-04-01',
                'run_online_invoicing_to': '2022-04-09',
            },
        )

        self.assertTrue(form.is_valid(), form.errors)

        form.schedule_online_invoicing()

        expected_calls = [
            call(offset_days=8, user_id=None),  # 2022-04-01
            call(offset_days=7, user_id=None),  # 2022-04-02
            call(offset_days=6, user_id=None),  # 2022-04-03
            call(offset_days=5, user_id=None),  # 2022-04-04
            call(offset_days=4, user_id=None),  # 2022-04-05
            call(offset_days=3, user_id=None),  # 2022-04-06
            call(offset_days=2, user_id=None),  # 2022-04-07
            call(offset_days=1, user_id=None),  # 2022-04-08
            call(offset_days=0, user_id=None),  # 2022-04-09
        ]

        braintree_online_mock.assert_has_calls(
            expected_calls,
            any_order=True,
        )

        saas_online_mock.assert_not_called()
