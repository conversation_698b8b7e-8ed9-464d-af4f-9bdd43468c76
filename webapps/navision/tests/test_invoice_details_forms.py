from http import HTTPStatus
from unittest.mock import patch
import pytest
import mock
from django.conf import settings
from django.shortcuts import reverse
from django.test import override_settings
from mock import MagicMock
from model_bakery import baker
from parameterized import parameterized
from rest_framework import status
from rest_framework.test import APIClient, APITestCase

from country_config.enums import Country
from lib.baker_utils import get_or_create_booking_source
from lib.elasticsearch.consts import ESDocType, ESIndex
from lib.feature_flag.feature.billing import (
    ForceConfirmBillingInvoiceDataFlag,
    NewInvoicingProcessIEFlag,
)
from lib.feature_flag.bug import DoNotRemoveEmptyTaxGroupInInvoiceDetailsForm
from lib.tests.utils import override_eppo_feature_flag
from lib.tools import tznow
from webapps.booking.models import BookingSources
from webapps.business.baker_recipes import business_recipe, resource_recipe
from webapps.business.elasticsearch.account import BusinessAccountDocument
from webapps.business.models import Resource
from webapps.business.models.models import Business
from webapps.consts import WEB
from webapps.elasticsearch.elastic import ELASTIC
from webapps.elasticsearch.tasks import document_river_task
from webapps.navision.baker_recipes import navision_settings_recipe
from webapps.navision.enums import InvoiceDetailsFormsEnum
from webapps.navision.invoice_details_forms_serializers import (
    EditAndConfirmaInvoiceDetailsSerializer,
    MandatoryTaxInvoiceDetailsSerializer,
    OptionalTaxInvoiceDetailsSerializer,
    TaxIfVATRegisteredInvoiceDetailsSerializer,
    TwoFieldsTaxIdInvoiceDetailsSerializer,
)
from webapps.navision.models import TaxGroup
from webapps.navision.models.invoice_details import InvoiceDetailsBusinessSettings
from webapps.navision.models.merchant import Merchant
from webapps.purchase.models import (
    InvoiceAddress,
    SubscriptionBuyer,
)
from webapps.structure.baker_recipes import usa_recipe
from webapps.structure.models import Region
from webapps.user.baker_recipes import user_recipe
from webapps.user.enums import AuthOriginEnum
from webapps.user.models import User


class TestSetUpMixin(APITestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.booking_source = get_or_create_booking_source(
            app_type=BookingSources.BUSINESS_APP,
            name=WEB,
        )
        cls.headers = {'HTTP_X_API_KEY': cls.booking_source.api_key, 'format': 'json'}
        cls.user = user_recipe.make()

    def setUp(self):
        super().setUp()
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        self.client.credentials(HTTP_X_ACCESS_TOKEN=self.session.session_key)

        self.business = business_recipe.make()
        self.resource = resource_recipe.make(
            business=self.business,
            staff_user=self.user,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_OWNER,
        )

        usa = usa_recipe.make()

        self.nav_settings = navision_settings_recipe.make(
            enable_invoice_details_editing=True,
            region=usa,
        )


class InvoiceDetailsTestMixin(TestSetUpMixin):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls._zipcode = None
        cls._address = None
        cls.entity_name = 'My company'
        cls.invoice_email = '<EMAIL>'
        cls.tax_id = None
        cls.tax_id_non_vat_registered = None
        cls.vat_registered = None
        cls.required_fields = (
            'entity_name',
            'address_details',
            'city',
            'zipcode',
            'invoice_email',
        )
        cls.optional_fields = ('tax_group',)

    @staticmethod
    def url_fnc(business_id):
        return reverse('invoice_details_forms', args=(business_id,))

    @property
    def zipcode(self):
        if self._zipcode is None:
            self._zipcode = baker.make(Region, name='90111', type=Region.Type.ZIP)
        return self._zipcode

    @property
    def address(self):
        if self._address is None:
            self._address = baker.make(
                InvoiceAddress,
                address_details1='Address 1',
                city='City',
                zipcode=self.zipcode,
            )
        return self._address

    @property
    def _buyer_kwargs(self):
        initial = dict(
            entity_name=self.entity_name,
            invoice_email=self.invoice_email,
            tax_id=self.tax_id,
            vat_registered=self.vat_registered,
        )
        kwargs = dict(
            businesses=[self.business],
            invoice_address=self.address,
        )
        for field_name in self.required_fields:
            if field_name in initial:
                kwargs[field_name] = initial[field_name]

        for field_name in self.optional_fields:
            if field_name in initial:
                kwargs[field_name] = initial[field_name]
        return kwargs

    def create_buyer(self):
        return baker.make(
            SubscriptionBuyer,
            **self._buyer_kwargs,
        )

    def create_buyer_sent_to_prod(self):
        return baker.make(
            SubscriptionBuyer,
            **self._buyer_kwargs,
            is_verified=True,
            merchant=baker.make(
                Merchant,
                synced_at=tznow(),
                sent_to_production=True,
            ),
        )

    @property
    def body(self):
        initial = dict(
            address_details='Address 1',
            city='City',
            zipcode=self.zipcode.name if settings.CHECK_ZIPCODE_IN_REGION_TABLE else '90123',
            entity_name=self.entity_name,
            invoice_email=self.invoice_email,
            tax_id=self.tax_id,
            tax_group=None,
            vat_registered=self.vat_registered,
            tax_id_non_vat_registered=self.tax_id_non_vat_registered,
        )

        body = {
            'form_id': self.form_id,
        }
        for field_name in self.required_fields:
            if field_name in initial:
                body[field_name] = initial[field_name]

        for field_name in self.optional_fields:
            if field_name in initial:
                body[field_name] = initial[field_name]

        return body

    def assert_buyer_is_correct(self, buyer, body):
        body.pop('form_id', None)
        body.pop('tax_id_non_vat_registered', None)
        actual = {}
        for field_name in self.required_fields:
            if field_name == 'tax_id_non_vat_registered':
                continue
            actual[field_name] = getattr(buyer, field_name, None)
        for field_name in self.optional_fields:
            actual[field_name] = getattr(buyer, field_name, None)
        invoice_address = dict(
            address_details=buyer.invoice_address.address_details1,
            city=buyer.invoice_address.city,
            zipcode=buyer.invoice_address.zipcode_str,
        )
        actual.update(invoice_address)
        self.assertDictEqual(actual, body)


class CommonInvoiceDetailsTests:
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_post_invoice_details__as_staffer(self):
        self.resource.staff_access_level = Resource.STAFF_ACCESS_LEVEL_STAFF
        self.resource.save()

        url = self.url_fnc(business_id=self.business.id) + '?dry_run=False'
        response = self.client.post(url, data=self.body, **self.headers)

        self.assertEqual(status.HTTP_404_NOT_FOUND, response.status_code)
        self.business.refresh_from_db()
        self.assertIsNone(self.business.buyer_id)

    @parameterized.expand(
        [
            (Resource.STAFF_ACCESS_LEVEL_MANAGER,),
            (Resource.STAFF_ACCESS_LEVEL_OWNER,),
        ]
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_post_invoice_details__as_owner_and_manager(self, staff_access_level):
        self.resource.staff_access_level = staff_access_level
        self.resource.save()

        url = self.url_fnc(business_id=self.business.id) + '?dry_run=False'
        response = self.client.post(url, data=self.body, **self.headers)

        self.assertEqual(status.HTTP_201_CREATED, response.status_code)
        self.business.refresh_from_db()
        self.assertIsNotNone(self.business.buyer_id)
        self.assert_buyer_is_correct(self.business.buyer, self.body)

    @override_settings(NAVISION_USE_TAX_GROUPS=False)
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_post_subscription_buyer_with_tax_groups_when_tax_groups_are_not_supported(self):
        url = self.url_fnc(business_id=self.business.id)

        some_group = baker.make(
            TaxGroup,
            name='Some group',
        )

        body = {**self.body}
        body['tax_group'] = some_group.name

        response = self.client.post(url, data=body, **self.headers)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    @override_settings(NAVISION_USE_TAX_GROUPS=True)
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_post_invoice_details_with_tax_region(self):
        url = self.url_fnc(business_id=self.business.id)

        some_group = baker.make(
            TaxGroup,
            name='Some group',
        )

        body = {**self.body}
        body['tax_group'] = some_group.name

        response = self.client.post(url, data=body, **self.headers)

        self.assertEqual(status.HTTP_201_CREATED, response.status_code)
        self.business.refresh_from_db()
        self.assertIsNotNone(self.business.buyer_id)
        self.assertEqual(self.business.buyer.tax_group_id, some_group.id)

    @parameterized.expand(
        [
            ('',),
            ('?dry_run=true',),
        ]
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_post_invalid_input_data(self, expanded_url):
        body = {**self.body}
        fields_names = {'entity_name', 'city', 'zipcode', 'address_details'}
        for field_name in fields_names:
            body[field_name] = None
        url = self.url_fnc(business_id=self.business.id) + expanded_url
        response = self.client.post(url, data=body, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(len(fields_names), len(response.data['errors']))
        self.assertSetEqual(fields_names, set(i['field'] for i in response.data['errors']))
        self.business.refresh_from_db()
        self.assertIsNone(self.business.buyer_id)

    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_post_with_dry_run_does_not_save_changes(self):
        url = self.url_fnc(business_id=self.business.id) + '?dry_run=1'
        response = self.client.post(url, data=self.body, **self.headers)

        self.assertEqual(status.HTTP_200_OK, response.status_code)

        self.business.refresh_from_db()
        self.assertIsNone(self.business.buyer_id)

        self.assertEqual(SubscriptionBuyer.objects.count(), 0)

    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_update_entity_name_if_merchant_is_sent_to_prod(self):
        url = self.url_fnc(business_id=self.business.id)
        self.create_buyer_sent_to_prod()
        data = {'form_id': self.form_id, 'entity_name': 'nazwa'}
        response = self.client.patch(url, data=data, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_update_tax_id_if_merchant_is_sent_to_prod(self):
        if self.form_id in (
            EditAndConfirmaInvoiceDetailsSerializer.FORM_ID,
            TwoFieldsTaxIdInvoiceDetailsSerializer.FORM_ID,
        ):
            # for EditAndConfirm form it's allowed to edit buyer tax_id
            # look at TestPathConfirmInvoiceDetails.test_can_update_invoice_details_sent_to_prod
            return
        url = self.url_fnc(business_id=self.business.id)
        self.create_buyer_sent_to_prod()
        body = {
            'form_id': self.form_id,
            'tax_id': '123',
        }
        response = self.client.patch(url, data=body, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            {
                'errors': [
                    {
                        'field': 'tax_id',
                        'description': 'This field cannot be edited',
                        'code': 'buyer_sent_to_production',
                    },
                ]
            },
            response.data,
        )


@override_settings(SUPPORTED_INVOICE_FORM=InvoiceDetailsFormsEnum.PATH_1)
class TestPath1InvoiceDetails(InvoiceDetailsTestMixin, CommonInvoiceDetailsTests):
    @classmethod
    def setUpTestData(cls) -> None:
        super().setUpTestData()
        cls.invoice_form = InvoiceDetailsFormsEnum.PATH_1
        cls.form_id = MandatoryTaxInvoiceDetailsSerializer.FORM_ID
        cls.tax_id = '5252344078'
        cls.vat_registered = True
        cls.required_fields = cls.required_fields + ('tax_id',)

    @patch('webapps.navision.views.invoice_details_changed_request.send')
    def test_send_invoice_details_event__get(self, mocked_event):
        url = self.url_fnc(business_id=self.business.id)
        self.client.get(url, **self.headers)
        mocked_event.assert_not_called()

    @patch('webapps.navision.views.invoice_details_changed_request.send')
    def test_send_invoice_details_event__post(self, mocked_event):
        url = self.url_fnc(business_id=self.business.id)
        self.client.post(url, data=self.body, **self.headers)
        request_data = {
            'form_id': self.form_id,
            'entity_name': 'My company',
            'address_details': 'Address 1',
            'city': 'City',
            'zipcode': '90111',
            'invoice_email': '<EMAIL>',
            'tax_id': self.tax_id,
            'tax_group': None,
        }
        mocked_event.assert_called_once_with(
            None,
            request_data=request_data,
            business_id=self.business.id,
        )

    @patch('webapps.navision.views.invoice_details_changed_request.send')
    def test_send_invoice_details_event__patch(self, mocked_event):
        buyer = baker.make('purchase.SubscriptionBuyer')
        self.business.buyer = buyer
        self.business.save()

        url = self.url_fnc(business_id=self.business.id)
        self.client.patch(url, data=self.body, **self.headers)

        request_data = {
            'form_id': self.form_id,
            'entity_name': 'My company',
            'address_details': 'Address 1',
            'city': 'City',
            'zipcode': '90111',
            'invoice_email': '<EMAIL>',
            'tax_id': self.tax_id,
            'tax_group': None,
        }
        mocked_event.assert_called_once_with(
            None,
            request_data=request_data,
            business_id=self.business.id,
            buyer_id=buyer.id,
            buyer_tax_id=buyer.tax_id,
        )

    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_get_empty_invoice_details(self):
        url = self.url_fnc(business_id=self.business.id)
        response = self.client.get(url, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertDictEqual(
            {
                'invoice_details_id': None,
                'data': {
                    'tax_id': '',
                    'entity_name': '',
                    'address_details': '',
                    'city': '',
                    'zipcode': '',
                    'invoice_email': '',
                },
                'form': {
                    'id': 'MandatoryTaxID',
                    'properties': {
                        'tax_id': {
                            'label': 'TAX ID (Employer Identification Number)',
                            'required': True,
                            'editable': True,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            response.data,
        )

    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_get_invoice_details_data_disable_invoice_details_editing(self):
        self.nav_settings.enable_invoice_details_editing = False
        self.nav_settings.save()

        self.create_buyer()
        url = self.url_fnc(business_id=self.business.id)
        response = self.client.get(url, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertDictEqual(
            {
                'invoice_details_id': mock.ANY,
                'data': {
                    'tax_id': '5252344078',
                    'entity_name': 'My company',
                    'address_details': 'Address 1',
                    'city': 'City',
                    'zipcode': self.zipcode.name,
                    'invoice_email': '<EMAIL>',
                },
                'form': {
                    'id': 'MandatoryTaxID',
                    'properties': {
                        'tax_id': {
                            'label': 'TAX ID (Employer Identification Number)',
                            'required': True,
                            'editable': False,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': False,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': False,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': False,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': False,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': False,
                        },
                    },
                },
            },
            response.data,
        )

    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_when_no_buyer_fields_editable_even_with_nav_setting_disabled(self):
        self.nav_settings.enable_invoice_details_editing = False
        self.nav_settings.save()

        url = self.url_fnc(business_id=self.business.id)
        response = self.client.get(url, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertDictEqual(
            {
                'invoice_details_id': None,
                'data': {
                    'tax_id': '',
                    'entity_name': '',
                    'address_details': '',
                    'city': '',
                    'zipcode': '',
                    'invoice_email': '',
                },
                'form': {
                    'id': 'MandatoryTaxID',
                    'properties': {
                        'tax_id': {
                            'label': 'TAX ID (Employer Identification Number)',
                            'required': True,
                            'editable': True,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            response.data,
        )

    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_post_happy_path(self):
        url = self.url_fnc(business_id=self.business.id)
        response = self.client.post(url, data=self.body, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(
            {
                'invoice_details_id': mock.ANY,
                'data': {
                    'tax_id': '5252344078',
                    'entity_name': 'My company',
                    'address_details': 'Address 1',
                    'city': 'City',
                    'zipcode': '90111',
                    'invoice_email': '<EMAIL>',
                },
                'form': {
                    'id': 'MandatoryTaxID',
                    'properties': {
                        'tax_id': {
                            'label': 'TAX ID (Employer Identification Number)',
                            'required': True,
                            'editable': True,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            response.data,
        )
        self.business.refresh_from_db()
        self.assertIsNotNone(self.business.buyer_id)

    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_post_invoice_details__as_owner(self):
        url = self.url_fnc(business_id=self.business.id) + '?dry_run=False'
        response = self.client.post(url, data=self.body, **self.headers)
        self.assertEqual(status.HTTP_201_CREATED, response.status_code)

        document_river_task.run(ESDocType.BUSINESS_ACCOUNT)
        index = ELASTIC.indices[ESIndex.BUSINESS_ACCOUNT]
        index.refresh()
        business_account = (
            BusinessAccountDocument.search()
            .filter(
                'term',
                id=self.business.id,
            )
            .execute()
            .hits[0]
        )

        self.assertEqual(business_account.tax_id, self.tax_id)

    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_put_tax_id_sent_to_prod(self):
        self.create_buyer_sent_to_prod()
        url = self.url_fnc(business_id=self.business.id)
        data = {'form_id': self.form_id, 'tax_id': '**********'}
        response = self.client.patch(url, data=data, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.business.refresh_from_db()
        self.assert_buyer_is_correct(self.business.buyer, self.body)

    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    @override_settings(CHECK_ZIPCODE_IN_REGION_TABLE=False)
    def test_create_buyer_with_null_state(self):
        body = {**self.body}
        body['zipcode'] = '42200'
        body['state'] = None
        url = self.url_fnc(business_id=self.business.id)
        response = self.client.post(url, data=body, **self.headers)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED, response.data)

        self.business.refresh_from_db()
        self.assertIsNotNone(self.business.buyer_id)
        self.assertIsNone(self.business.buyer.invoice_address.state)


@override_settings(SUPPORTED_INVOICE_FORM=InvoiceDetailsFormsEnum.PATH_2)
class TestPath2InvoiceDetails(InvoiceDetailsTestMixin, CommonInvoiceDetailsTests):
    @classmethod
    def setUpTestData(cls) -> None:
        super().setUpTestData()
        cls.invoice_form = InvoiceDetailsFormsEnum.PATH_2
        cls.form_id = TaxIfVATRegisteredInvoiceDetailsSerializer.FORM_ID
        cls.vat_registered = True
        cls.tax_id = '123'
        cls.required_fields = cls.required_fields + ('vat_registered', 'tax_id')

    @patch('webapps.navision.views.invoice_details_changed_request.send')
    def test_send_invoice_details_event__get(self, mocked_event):
        url = self.url_fnc(business_id=self.business.id)
        self.client.get(url, **self.headers)
        mocked_event.assert_not_called()

    @patch('webapps.navision.views.invoice_details_changed_request.send')
    def test_send_invoice_details_event__post(self, mocked_event):
        url = self.url_fnc(business_id=self.business.id)
        self.client.post(url, data=self.body, **self.headers)
        request_data = {
            'form_id': self.form_id,
            'entity_name': 'My company',
            'address_details': 'Address 1',
            'city': 'City',
            'zipcode': '90111',
            'invoice_email': '<EMAIL>',
            'tax_id': self.tax_id,
            'vat_registered': True,
            'tax_group': None,
        }
        mocked_event.assert_called_once_with(
            None,
            request_data=request_data,
            business_id=self.business.id,
        )

    @patch('webapps.navision.views.invoice_details_changed_request.send')
    def test_send_invoice_details_event__patch(self, mocked_event):
        buyer = baker.make('purchase.SubscriptionBuyer')
        self.business.buyer = buyer
        self.business.save()

        url = self.url_fnc(business_id=self.business.id)
        self.client.patch(url, data=self.body, **self.headers)

        request_data = {
            'form_id': self.form_id,
            'entity_name': 'My company',
            'address_details': 'Address 1',
            'city': 'City',
            'zipcode': '90111',
            'invoice_email': '<EMAIL>',
            'vat_registered': True,
            'tax_id': self.tax_id,
            'tax_group': None,
        }

        mocked_event.assert_called_once_with(
            None,
            request_data=request_data,
            business_id=self.business.id,
            buyer_id=buyer.id,
            buyer_tax_id=buyer.tax_id,
        )

    def test_patch_cannot_edit_invoice_details(self):
        url = self.url_fnc(business_id=self.business.id)
        buyer = baker.make('purchase.SubscriptionBuyer', invoice_details_editable=False)
        self.business.buyer = buyer
        self.business.save()
        response = self.client.patch(url, data=self.body, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_get_buyer_no_address_details(self):
        buyer = self.create_buyer()
        buyer.invoice_address = None
        buyer.save()
        url = self.url_fnc(business_id=self.business.id)
        response = self.client.get(url, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            {
                'invoice_details_id': mock.ANY,
                'data': {
                    'vat_registered': True,
                    'tax_id': '123',
                    'entity_name': 'My company',
                    'address_details': '',
                    'city': '',
                    'zipcode': '',
                    'invoice_email': '<EMAIL>',
                },
                'form': {
                    'id': 'TaxIDIfVatRegistered',
                    'properties': {
                        'vat_registered': {
                            'label': "I'm registered for VAT",
                            'required': True,
                            'editable': True,
                        },
                        'tax_id': {
                            'label': 'TAX ID (Employer Identification Number)',
                            'required': True,
                            'editable': True,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            response.data,
        )

    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_get_empty_invoice_details(self):
        url = self.url_fnc(business_id=self.business.id)
        response = self.client.get(url, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertDictEqual(
            {
                'invoice_details_id': None,
                'data': {
                    'vat_registered': True,
                    'tax_id': '',
                    'entity_name': '',
                    'address_details': '',
                    'city': '',
                    'zipcode': '',
                    'invoice_email': '',
                },
                'form': {
                    'id': 'TaxIDIfVatRegistered',
                    'properties': {
                        'vat_registered': {
                            'label': "I'm registered for VAT",
                            'required': True,
                            'editable': True,
                        },
                        'tax_id': {
                            'label': 'TAX ID (Employer Identification Number)',
                            'required': True,
                            'editable': True,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            response.data,
        )

    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_post_happy_path_vat_registered_true(self):
        url = self.url_fnc(business_id=self.business.id)
        response = self.client.post(url, data=self.body, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(
            {
                'invoice_details_id': mock.ANY,
                'data': {
                    'vat_registered': True,
                    'tax_id': '123',
                    'entity_name': 'My company',
                    'address_details': 'Address 1',
                    'city': 'City',
                    'zipcode': '90111',
                    'invoice_email': '<EMAIL>',
                },
                'form': {
                    'id': 'TaxIDIfVatRegistered',
                    'properties': {
                        'vat_registered': {
                            'label': "I'm registered for VAT",
                            'required': True,
                            'editable': True,
                        },
                        'tax_id': {
                            'label': 'TAX ID (Employer Identification Number)',
                            'required': True,
                            'editable': True,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            response.data,
        )
        self.business.refresh_from_db()
        self.assertIsNotNone(self.business.buyer_id)

    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_post_happy_path_vat_registered_false(self):
        self.vat_registered = False
        url = self.url_fnc(business_id=self.business.id)
        response = self.client.post(url, data=self.body, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(
            {
                'invoice_details_id': mock.ANY,
                'data': {
                    'vat_registered': False,
                    'tax_id': None,
                    'entity_name': 'My company',
                    'address_details': 'Address 1',
                    'city': 'City',
                    'zipcode': '90111',
                    'invoice_email': '<EMAIL>',
                },
                'form': {
                    'id': 'TaxIDIfVatRegistered',
                    'properties': {
                        'vat_registered': {
                            'label': "I'm registered for VAT",
                            'required': True,
                            'editable': True,
                        },
                        'tax_id': {
                            'label': 'TAX ID (Employer Identification Number)',
                            'required': True,
                            'editable': True,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            response.data,
        )
        self.business.refresh_from_db()
        self.assertIsNotNone(self.business.buyer_id)

    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_update_invoice_details_sent_to_prod(self):
        url = self.url_fnc(business_id=self.business.id)
        self.create_buyer_sent_to_prod()
        data = {'form_id': self.form_id, 'vat_registered': True, 'tax_id': '999'}
        response = self.client.patch(url, data=data, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.business.refresh_from_db()
        self.business.buyer.refresh_from_db()
        self.assert_buyer_is_correct(self.business.buyer, self.body)

    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_update_invoice_details_address(self):
        url = self.url_fnc(business_id=self.business.id)
        self.create_buyer()
        body_put = dict(
            form_id=self.form_id,
            address_details=self.body['address_details'],
            city=self.body['city'],
            zipcode=self.body['zipcode'],
        )
        body_put['address_details'] += 'changed'
        response = self.client.patch(url, data=body_put, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.business.refresh_from_db()
        updated_body = self.body
        updated_body.update(**body_put)
        self.assert_buyer_is_correct(self.business.buyer, updated_body)

    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    @override_settings(CHECK_ZIPCODE_IN_REGION_TABLE=False)
    def test_update_invoice_details_address_without_region_table_check(self):
        url = self.url_fnc(business_id=self.business.id)
        self.create_buyer()
        body_put = dict(
            form_id=self.form_id,
            address_details=self.body['address_details'],
            city=self.body['city'],
            zipcode=self.body['zipcode'],
        )
        body_put['address_details'] += 'changed'

        response = self.client.patch(url, data=body_put, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.business.refresh_from_db()
        updated_body = self.body
        updated_body.update(**body_put)
        self.assert_buyer_is_correct(self.business.buyer, updated_body)

    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_post_with_existing_buyer(self):
        url = self.url_fnc(business_id=self.business.id)
        self.client.post(url, data=self.body, **self.headers)
        response = self.client.post(url, data=self.body, **self.headers)
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
        self.assertEqual('invalid_method', response.data['detail'].code)


@override_settings(SUPPORTED_INVOICE_FORM=InvoiceDetailsFormsEnum.PATH_3)
class TestPath3InvoiceDetails(InvoiceDetailsTestMixin, CommonInvoiceDetailsTests):
    @classmethod
    def setUpTestData(cls) -> None:
        super().setUpTestData()
        cls.invoice_form = InvoiceDetailsFormsEnum.PATH_3
        cls.form_id = OptionalTaxInvoiceDetailsSerializer.FORM_ID
        cls.optional_fields = cls.optional_fields + ('tax_id',)

    @patch('webapps.navision.views.invoice_details_changed_request.send')
    def test_send_invoice_details_event__get(self, mocked_event):
        url = self.url_fnc(business_id=self.business.id)
        self.client.get(url, **self.headers)
        mocked_event.assert_not_called()

    @patch('webapps.navision.views.invoice_details_changed_request.send')
    def test_send_invoice_details_event__post(self, mocked_event):
        url = self.url_fnc(business_id=self.business.id)
        self.client.post(url, data=self.body, **self.headers)
        request_data = {
            'form_id': self.form_id,
            'entity_name': 'My company',
            'address_details': 'Address 1',
            'city': 'City',
            'zipcode': '90111',
            'invoice_email': '<EMAIL>',
            'tax_id': self.tax_id,
            'tax_group': None,
        }
        mocked_event.assert_called_once_with(
            None,
            request_data=request_data,
            business_id=self.business.id,
        )

    @patch('webapps.navision.views.invoice_details_changed_request.send')
    def test_send_invoice_details_event__patch(self, mocked_event):
        buyer = baker.make('purchase.SubscriptionBuyer')
        self.business.buyer = buyer
        self.business.save()

        url = self.url_fnc(business_id=self.business.id)
        self.client.patch(url, data=self.body, **self.headers)

        request_data = {
            'form_id': self.form_id,
            'entity_name': 'My company',
            'address_details': 'Address 1',
            'city': 'City',
            'zipcode': '90111',
            'invoice_email': '<EMAIL>',
            'tax_id': self.tax_id,
            'tax_group': None,
        }
        mocked_event.assert_called_once_with(
            None,
            request_data=request_data,
            business_id=self.business.id,
            buyer_id=buyer.id,
            buyer_tax_id=buyer.tax_id,
        )

    # todo add test for verified merchants
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_get_empty_invoice_details(self):
        url = self.url_fnc(business_id=self.business.id)
        response = self.client.get(url, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertDictEqual(
            {
                'invoice_details_id': None,
                'data': {
                    'tax_id': '',
                    'entity_name': '',
                    'address_details': '',
                    'city': '',
                    'zipcode': '',
                    'invoice_email': '',
                },
                'form': {
                    'id': 'OptionalTaxID',
                    'properties': {
                        'tax_id': {
                            'label': 'TAX ID (Employer Identification Number) - optional',
                            'required': False,
                            'editable': True,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            response.data,
        )

    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_post_happy_path(self):
        url = self.url_fnc(business_id=self.business.id)
        response = self.client.post(url, data=self.body, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(
            {
                'invoice_details_id': mock.ANY,
                'data': {
                    'tax_id': None,
                    'entity_name': 'My company',
                    'address_details': 'Address 1',
                    'city': 'City',
                    'zipcode': '90111',
                    'invoice_email': '<EMAIL>',
                },
                'form': {
                    'id': 'OptionalTaxID',
                    'properties': {
                        'tax_id': {
                            'label': 'TAX ID (Employer Identification Number) - optional',
                            'required': False,
                            'editable': True,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            response.data,
        )
        self.business.refresh_from_db()
        self.assertIsNotNone(self.business.buyer_id)

    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_tax_id_too_long(self):
        body = self.body
        body['tax_id'] = 1000 * 'a'
        url = self.url_fnc(business_id=self.business.id)
        response = self.client.post(url, data=body, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            {
                'errors': [
                    {
                        'field': 'tax_id',
                        'description': 'Ensure this field has no more than 20 characters.',
                        'code': 'max_length',
                    }
                ]
            },
            response.data,
        )

    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_update_not_existing_invoice_details(self):
        url = self.url_fnc(business_id=self.business.id)
        response = self.client.patch(url, data=self.body, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual('invoice_details_does_not_exist', response.data['detail'].code)

    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_update_disabled_editing_in_nav_settings(self):
        self.nav_settings.enable_invoice_details_editing = False
        self.nav_settings.save()

        field_name = 'entity_name'
        url = self.url_fnc(business_id=self.business.id) + '?dry_run=false'
        self.create_buyer()
        body_put = {'form_id': self.form_id, field_name: self.body[field_name] + 'changed'}
        response = self.client.patch(url, data=body_put, **self.headers)
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
        self.assertEqual('invoice_form_cannot_be_edited', response.data['detail'].code)

    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_put_tax_group_can_be_set_to_null(self):
        url = self.url_fnc(business_id=self.business.id)
        self.create_buyer()
        data = {'form_id': self.form_id, 'tax_group': None}
        response = self.client.patch(url, data=data, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_put_update_invoice_details_simple_field(self):
        field_name = 'entity_name'
        url = self.url_fnc(business_id=self.business.id) + '?dry_run=false'
        self.create_buyer()
        body_put = {'form_id': self.form_id, field_name: self.body[field_name] + 'changed'}
        response = self.client.patch(url, data=body_put, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.business.refresh_from_db()
        updated_body = self.body
        updated_body.update(**body_put)
        self.assert_buyer_is_correct(self.business.buyer, updated_body)

    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_put_with_dry_run_doesnt_change_anything(self):
        field_name = 'entity_name'
        url = self.url_fnc(business_id=self.business.id) + '?dry_run=true'
        self.create_buyer()
        body_put = {'form_id': self.form_id, field_name: self.body[field_name] + 'changed'}
        response = self.client.patch(url, data=body_put, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.business.refresh_from_db()
        self.assert_buyer_is_correct(self.business.buyer, self.body)


@override_eppo_feature_flag({ForceConfirmBillingInvoiceDataFlag.flag_name: True})
@override_settings(SUPPORTED_INVOICE_FORM=InvoiceDetailsFormsEnum.PATH_2)
class TestPathConfirmInvoiceDetails(InvoiceDetailsTestMixin, CommonInvoiceDetailsTests):
    @classmethod
    def setUpTestData(cls) -> None:
        super().setUpTestData()
        cls.invoice_form = InvoiceDetailsFormsEnum.CONFIRM
        cls.form_id = EditAndConfirmaInvoiceDetailsSerializer.FORM_ID
        cls.vat_registered = True
        cls.tax_id = '123'
        cls.required_fields = cls.required_fields + ('vat_registered', 'tax_id')

    @patch('webapps.navision.views.invoice_details_changed_request.send')
    def test_send_invoice_details_event_and_task__get(self, mocked_event):
        url = self.url_fnc(business_id=self.business.id)
        self.client.get(url, **self.headers)
        mocked_event.assert_not_called()

    @patch(
        'webapps.navision.invoice_details_forms_serializers.invoice_details_confirmed_task.delay'
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send')
    def test_send_invoice_details_event_and_task__post(self, mocked_event, mocked_task):
        url = self.url_fnc(business_id=self.business.id)
        body = self.body
        body['is_confirmed'] = True
        self.client.post(url, data=body, **self.headers)
        request_data = {
            'form_id': self.form_id,
            'entity_name': 'My company',
            'address_details': 'Address 1',
            'city': 'City',
            'zipcode': '90111',
            'invoice_email': '<EMAIL>',
            'tax_id': self.tax_id,
            'vat_registered': True,
            'tax_group': None,
        }
        mocked_event.assert_called_once_with(
            None,
            request_data=request_data,
            business_id=self.business.id,
        )

        mocked_task.assert_called_once_with(
            business_id=self.business.id,
        )

    @patch(
        'webapps.navision.invoice_details_forms_serializers.invoice_details_confirmed_task.delay'
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_send_invoice_details_not_confirmed__post(self, mocked_task):
        url = self.url_fnc(business_id=self.business.id)
        body = self.body
        body['is_confirmed'] = False
        self.client.post(url, data=body, **self.headers)

        mocked_task.assert_not_called()

    @patch(
        'webapps.navision.invoice_details_forms_serializers.invoice_details_confirmed_task.delay'
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send')
    def test_send_invoice_details_event_and_task__patch(self, mocked_event, mocked_task):
        buyer = baker.make('purchase.SubscriptionBuyer')
        self.business.buyer = buyer
        self.business.save()
        body = self.body
        body['is_confirmed'] = True
        url = self.url_fnc(business_id=self.business.id)
        self.client.patch(url, data=body, **self.headers)

        request_data = {
            'form_id': self.form_id,
            'entity_name': 'My company',
            'address_details': 'Address 1',
            'city': 'City',
            'zipcode': '90111',
            'invoice_email': '<EMAIL>',
            'vat_registered': True,
            'tax_id': self.tax_id,
            'tax_group': None,
        }

        mocked_event.assert_called_once_with(
            None,
            request_data=request_data,
            business_id=self.business.id,
            buyer_id=buyer.id,
            buyer_tax_id=buyer.tax_id,
        )

        mocked_task.assert_called_once_with(
            business_id=self.business.id,
        )

    @patch(
        'webapps.navision.invoice_details_forms_serializers.invoice_details_confirmed_task.delay'
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_send_invoice_details_not_confirmed__patch(self, mocked_task):
        buyer = baker.make('purchase.SubscriptionBuyer')
        self.business.buyer = buyer
        self.business.save()
        body = self.body
        body['is_confirmed'] = False
        url = self.url_fnc(business_id=self.business.id)
        self.client.patch(url, data=body, **self.headers)
        mocked_task.assert_not_called()

    def test_patch_cannot_edit_invoice_details(self):
        url = self.url_fnc(business_id=self.business.id)
        buyer = baker.make('purchase.SubscriptionBuyer', invoice_details_editable=False)
        self.business.buyer = buyer
        self.business.save()
        response = self.client.patch(url, data=self.body, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    @patch(
        'webapps.navision.invoice_details_forms_serializers.invoice_details_confirmed_task.delay'
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_get_buyer_no_address_details(self, mocked_task):
        buyer = self.create_buyer()
        buyer.invoice_address = None
        buyer.save()
        url = self.url_fnc(business_id=self.business.id)
        response = self.client.get(url, **self.headers)
        mocked_task.assert_not_called()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            {
                'invoice_details_id': mock.ANY,
                'data': {
                    'vat_registered': True,
                    'tax_id': '123',
                    'entity_name': 'My company',
                    'address_details': '',
                    'city': '',
                    'zipcode': '',
                    'invoice_email': '<EMAIL>',
                    'is_confirmed': False,
                },
                'form': {
                    'id': 'EditAndConfirm',
                    'properties': {
                        'vat_registered': {
                            'label': "I'm registered for VAT",
                            'required': True,
                            'editable': True,
                        },
                        'tax_id': {
                            'label': 'TAX ID (Employer Identification Number)',
                            'required': True,
                            'editable': True,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            response.data,
        )

    @patch(
        'webapps.navision.invoice_details_forms_serializers.invoice_details_confirmed_task.delay'
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_get_empty_invoice_details(self, mocked_task):
        url = self.url_fnc(business_id=self.business.id)
        response = self.client.get(url, **self.headers)
        mocked_task.assert_not_called()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertDictEqual(
            {
                'invoice_details_id': None,
                'data': {
                    'vat_registered': True,
                    'tax_id': '',
                    'entity_name': '',
                    'address_details': '',
                    'city': '',
                    'zipcode': '',
                    'invoice_email': '',
                    'is_confirmed': False,
                },
                'form': {
                    'id': 'EditAndConfirm',
                    'properties': {
                        'vat_registered': {
                            'label': "I'm registered for VAT",
                            'required': True,
                            'editable': True,
                        },
                        'tax_id': {
                            'label': 'TAX ID (Employer Identification Number)',
                            'required': True,
                            'editable': True,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            response.data,
        )

    @patch(
        'webapps.navision.invoice_details_forms_serializers.invoice_details_confirmed_task.delay',
        MagicMock(),
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_post_happy_path_vat_registered_true(self):
        url = self.url_fnc(business_id=self.business.id)
        response = self.client.post(url, data=self.body, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(
            {
                'invoice_details_id': mock.ANY,
                'data': {
                    'vat_registered': True,
                    'tax_id': '123',
                    'entity_name': 'My company',
                    'address_details': 'Address 1',
                    'city': 'City',
                    'zipcode': '90111',
                    'invoice_email': '<EMAIL>',
                    'is_confirmed': False,
                },
                'form': {
                    'id': 'EditAndConfirm',
                    'properties': {
                        'vat_registered': {
                            'label': "I'm registered for VAT",
                            'required': True,
                            'editable': True,
                        },
                        'tax_id': {
                            'label': 'TAX ID (Employer Identification Number)',
                            'required': True,
                            'editable': True,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            response.data,
        )
        self.business.refresh_from_db()
        self.assertIsNotNone(self.business.buyer_id)

    @patch(
        'webapps.navision.invoice_details_forms_serializers.invoice_details_confirmed_task.delay',
        MagicMock(),
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_post_happy_path_vat_registered_false(self):
        self.vat_registered = False
        url = self.url_fnc(business_id=self.business.id)
        response = self.client.post(url, data=self.body, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(
            {
                'invoice_details_id': mock.ANY,
                'data': {
                    'vat_registered': False,
                    'tax_id': None,
                    'entity_name': 'My company',
                    'address_details': 'Address 1',
                    'city': 'City',
                    'zipcode': '90111',
                    'invoice_email': '<EMAIL>',
                    'is_confirmed': False,
                },
                'form': {
                    'id': 'EditAndConfirm',
                    'properties': {
                        'vat_registered': {
                            'label': "I'm registered for VAT",
                            'required': True,
                            'editable': True,
                        },
                        'tax_id': {
                            'label': 'TAX ID (Employer Identification Number)',
                            'required': True,
                            'editable': True,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            response.data,
        )
        self.business.refresh_from_db()
        self.assertIsNotNone(self.business.buyer_id)

    @patch(
        'webapps.navision.invoice_details_forms_serializers.invoice_details_confirmed_task.delay',
        MagicMock(),
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_can_update_invoice_details_sent_to_prod(self):
        url = self.url_fnc(business_id=self.business.id)
        self.create_buyer_sent_to_prod()
        body_put = dict(
            vat_registered=True,
            tax_id='999',
            form_id=self.form_id,
            address_details=self.body['address_details'],
            city=self.body['city'],
            zipcode=self.body['zipcode'],
            is_confirmed=True,
        )
        body_put['address_details'] += 'changed'
        response = self.client.patch(url, data=body_put, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.business.refresh_from_db()
        self.business.buyer.refresh_from_db()
        body_with_modification = self.body
        body_with_modification.update(**body_put)
        body_with_modification.pop('is_confirmed')
        self.assert_buyer_is_correct(self.business.buyer, body_with_modification)

    @patch(
        'webapps.navision.invoice_details_forms_serializers.invoice_details_confirmed_task.delay',
        MagicMock(),
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_post_with_existing_buyer(self):
        url = self.url_fnc(business_id=self.business.id)
        self.client.post(url, data=self.body, **self.headers)
        response = self.client.post(url, data=self.body, **self.headers)
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
        self.assertEqual('invalid_method', response.data['detail'].code)

    @parameterized.expand(
        [
            (True,),
            (False,),
        ]
    )
    @patch(
        'webapps.navision.invoice_details_forms_serializers.invoice_details_confirmed_task.delay'
    )
    def test_get_with_is_confirmed(self, is_confirmed, mocked_task):
        self.create_buyer()
        url = self.url_fnc(business_id=self.business.id)
        InvoiceDetailsBusinessSettings.objects.update_or_create(
            business_id=self.business.id,
            invoice_details_last_confirmed_dt=(tznow() if is_confirmed else None),
        )
        response = self.client.get(url, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertEqual(response.data['data']['is_confirmed'], is_confirmed)
        mocked_task.assert_not_called()  # make sure nothing is updated during GET


@override_eppo_feature_flag({NewInvoicingProcessIEFlag.flag_name: True})
@override_settings(
    SUPPORTED_INVOICE_FORM=InvoiceDetailsFormsEnum.TWO_TAX_ID_FIELDS,
    INVOICE_DETAILS_TAX_ID_PREFIX='IE',
)
class TestPathTwoFieldsTaxIdInvoiceDetails(InvoiceDetailsTestMixin, CommonInvoiceDetailsTests):
    @classmethod
    def setUpTestData(cls) -> None:
        super().setUpTestData()
        cls.invoice_form = InvoiceDetailsFormsEnum.TWO_TAX_ID_FIELDS
        cls.form_id = TwoFieldsTaxIdInvoiceDetailsSerializer.FORM_ID
        cls.vat_registered = True
        cls.tax_id = '123'
        cls.tax_id_non_vat_registered = None
        cls.required_fields = cls.required_fields + (
            'vat_registered',
            'tax_id',
            'tax_id_non_vat_registered',
        )

    @patch('webapps.navision.views.invoice_details_changed_request.send')
    def test_send_invoice_details_event_and_task__get(self, mocked_event):
        url = self.url_fnc(business_id=self.business.id)
        self.client.get(url, **self.headers)
        mocked_event.assert_not_called()

    @patch(
        'webapps.navision.invoice_details_forms_serializers.invoice_details_confirmed_task.delay'
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send')
    def test_send_invoice_details_event_and_task__post(self, mocked_event, mocked_task):
        url = self.url_fnc(business_id=self.business.id)
        body = self.body
        body['is_confirmed'] = True
        body['tax_id_non_vat_registered'] = self.tax_id
        self.client.post(url, data=body, **self.headers)
        request_data = {
            'form_id': self.form_id,
            'entity_name': 'My company',
            'address_details': 'Address 1',
            'city': 'City',
            'zipcode': '90111',
            'invoice_email': '<EMAIL>',
            'tax_id': self.tax_id,
            'tax_id_non_vat_registered': self.tax_id,
            'vat_registered': True,
            'tax_group': None,
        }
        mocked_event.assert_called_once_with(
            None,
            request_data=request_data,
            business_id=self.business.id,
        )

        mocked_task.assert_called_once_with(
            business_id=self.business.id,
        )

    @patch(
        'webapps.navision.invoice_details_forms_serializers.invoice_details_confirmed_task.delay'
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_send_invoice_details_not_confirmed__post(self, mocked_task):
        url = self.url_fnc(business_id=self.business.id)
        body = self.body
        body['is_confirmed'] = False
        self.client.post(url, data=body, **self.headers)

        mocked_task.assert_not_called()

    @patch(
        'webapps.navision.invoice_details_forms_serializers.invoice_details_confirmed_task.delay'
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send')
    def test_send_invoice_details_event_and_task__patch(self, mocked_event, mocked_task):
        buyer = baker.make('purchase.SubscriptionBuyer')
        self.business.buyer = buyer
        self.business.save()
        body = self.body
        body['is_confirmed'] = True
        body['tax_id_non_vat_registered'] = self.tax_id
        url = self.url_fnc(business_id=self.business.id)
        self.client.patch(url, data=body, **self.headers)

        request_data = {
            'form_id': self.form_id,
            'entity_name': 'My company',
            'address_details': 'Address 1',
            'city': 'City',
            'zipcode': '90111',
            'invoice_email': '<EMAIL>',
            'vat_registered': True,
            'tax_id': self.tax_id,
            'tax_id_non_vat_registered': self.tax_id,
            'tax_group': None,
        }

        mocked_event.assert_called_once_with(
            None,
            request_data=request_data,
            business_id=self.business.id,
            buyer_id=buyer.id,
            buyer_tax_id=buyer.tax_id,
        )

        mocked_task.assert_called_once_with(
            business_id=self.business.id,
        )

    @patch(
        'webapps.navision.invoice_details_forms_serializers.invoice_details_confirmed_task.delay'
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_send_invoice_details_not_confirmed__patch(self, mocked_task):
        buyer = baker.make('purchase.SubscriptionBuyer')
        self.business.buyer = buyer
        self.business.save()
        body = self.body
        body['is_confirmed'] = False
        url = self.url_fnc(business_id=self.business.id)
        self.client.patch(url, data=body, **self.headers)
        mocked_task.assert_not_called()

    def test_patch_cannot_edit_invoice_details(self):
        url = self.url_fnc(business_id=self.business.id)
        buyer = baker.make('purchase.SubscriptionBuyer', invoice_details_editable=False)
        self.business.buyer = buyer
        self.business.save()
        response = self.client.patch(url, data=self.body, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    @patch(
        'webapps.navision.invoice_details_forms_serializers.invoice_details_confirmed_task.delay'
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_get_buyer_no_address_details(self, mocked_task):
        buyer = self.create_buyer()
        buyer.invoice_address = None
        buyer.save()
        url = self.url_fnc(business_id=self.business.id)
        response = self.client.get(url, **self.headers)
        mocked_task.assert_not_called()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            {
                'invoice_details_id': mock.ANY,
                'data': {
                    'vat_registered': True,
                    'tax_id': '123',
                    'tax_id_non_vat_registered': None,
                    'entity_name': 'My company',
                    'address_details': '',
                    'city': '',
                    'zipcode': '',
                    'invoice_email': '<EMAIL>',
                    'is_confirmed': False,
                },
                'form': {
                    'id': 'TwoTaxIdFields',
                    'properties': {
                        'vat_registered': {
                            'label': "I'm registered for VAT",
                            'required': True,
                            'editable': True,
                        },
                        'tax_id': {
                            'label': 'VAT number',
                            'required': True,
                            'editable': True,
                        },
                        'tax_id_non_vat_registered': {
                            'label': 'Tax Registration Number (TRN)',
                            'required': False,
                            'editable': True,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            response.data,
        )

    @patch(
        'webapps.navision.invoice_details_forms_serializers.invoice_details_confirmed_task.delay'
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_get_buyer_vat_registered_false(self, mocked_task):
        buyer = self.create_buyer()
        buyer.vat_registered = False
        buyer.save()
        url = self.url_fnc(business_id=self.business.id)
        response = self.client.get(url, **self.headers)
        mocked_task.assert_not_called()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            {
                'invoice_details_id': mock.ANY,
                'data': {
                    'vat_registered': False,
                    'tax_id': None,
                    'tax_id_non_vat_registered': '123',
                    'entity_name': 'My company',
                    'address_details': 'Address 1',
                    'city': 'City',
                    'zipcode': '90111',
                    'invoice_email': '<EMAIL>',
                    'is_confirmed': False,
                },
                'form': {
                    'id': 'TwoTaxIdFields',
                    'properties': {
                        'vat_registered': {
                            'label': "I'm registered for VAT",
                            'required': True,
                            'editable': True,
                        },
                        'tax_id': {
                            'label': 'VAT number',
                            'required': True,
                            'editable': True,
                        },
                        'tax_id_non_vat_registered': {
                            'label': 'Tax Registration Number (TRN)',
                            'required': False,
                            'editable': True,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            response.data,
        )

    @patch(
        'webapps.navision.invoice_details_forms_serializers.invoice_details_confirmed_task.delay'
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_get_empty_invoice_details_no_buyer(self, mocked_task):
        url = self.url_fnc(business_id=self.business.id)
        response = self.client.get(url, **self.headers)
        mocked_task.assert_not_called()
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertDictEqual(
            {
                'invoice_details_id': None,
                'data': {
                    'vat_registered': True,
                    'tax_id': 'IE',
                    'tax_id_non_vat_registered': '',
                    'entity_name': '',
                    'address_details': '',
                    'city': '',
                    'zipcode': '',
                    'invoice_email': '',
                    'is_confirmed': False,
                },
                'form': {
                    'id': 'TwoTaxIdFields',
                    'properties': {
                        'vat_registered': {
                            'label': "I'm registered for VAT",
                            'required': True,
                            'editable': True,
                        },
                        'tax_id': {
                            'label': 'VAT number',
                            'required': True,
                            'editable': True,
                        },
                        'tax_id_non_vat_registered': {
                            'label': 'Tax Registration Number (TRN)',
                            'required': False,
                            'editable': True,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            response.data,
        )

    @patch(
        'webapps.navision.invoice_details_forms_serializers.invoice_details_confirmed_task.delay',
        MagicMock(),
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_post_happy_path_vat_registered_true(self):
        url = self.url_fnc(business_id=self.business.id)
        response = self.client.post(url, data=self.body, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(
            {
                'invoice_details_id': mock.ANY,
                'data': {
                    'vat_registered': True,
                    'tax_id': '123',
                    'tax_id_non_vat_registered': None,
                    'entity_name': 'My company',
                    'address_details': 'Address 1',
                    'city': 'City',
                    'zipcode': '90111',
                    'invoice_email': '<EMAIL>',
                    'is_confirmed': False,
                },
                'form': {
                    'id': 'TwoTaxIdFields',
                    'properties': {
                        'vat_registered': {
                            'label': "I'm registered for VAT",
                            'required': True,
                            'editable': True,
                        },
                        'tax_id': {
                            'label': 'VAT number',
                            'required': True,
                            'editable': True,
                        },
                        'tax_id_non_vat_registered': {
                            'label': 'Tax Registration Number (TRN)',
                            'required': False,
                            'editable': True,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            response.data,
        )
        self.business.refresh_from_db()
        self.assertIsNotNone(self.business.buyer_id)

    @patch(
        'webapps.navision.invoice_details_forms_serializers.invoice_details_confirmed_task.delay',
        MagicMock(),
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_post_happy_path_vat_registered_false(self):
        self.vat_registered = False
        self.tax_id = None
        self.tax_id_non_vat_registered = '123'
        url = self.url_fnc(business_id=self.business.id)
        response = self.client.post(url, data=self.body, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(
            {
                'invoice_details_id': mock.ANY,
                'data': {
                    'vat_registered': False,
                    'tax_id': None,
                    'tax_id_non_vat_registered': '123',
                    'entity_name': 'My company',
                    'address_details': 'Address 1',
                    'city': 'City',
                    'zipcode': '90111',
                    'invoice_email': '<EMAIL>',
                    'is_confirmed': False,
                },
                'form': {
                    'id': 'TwoTaxIdFields',
                    'properties': {
                        'vat_registered': {
                            'label': "I'm registered for VAT",
                            'required': True,
                            'editable': True,
                        },
                        'tax_id': {
                            'label': 'VAT number',
                            'required': True,
                            'editable': True,
                        },
                        'tax_id_non_vat_registered': {
                            'label': 'Tax Registration Number (TRN)',
                            'required': False,
                            'editable': True,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            response.data,
        )
        self.business.refresh_from_db()
        self.assertIsNotNone(self.business.buyer_id)

    @patch(
        'webapps.navision.invoice_details_forms_serializers.invoice_details_confirmed_task.delay',
        MagicMock(),
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_update_tax_id_vat_registered_true(self):
        self.vat_registered = False
        self.tax_id = 'OLDTRN123'
        buyer = self.create_buyer()
        self.assertFalse(buyer.vat_registered)
        self.assertEqual('OLDTRN123', buyer.tax_id)

        self.vat_registered = True
        self.tax_id = 'NEWID999'
        self.tax_id_non_vat_registered = 'OLDTRN123'
        url = self.url_fnc(business_id=self.business.id)
        response = self.client.patch(url, data=self.body, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            {
                'vat_registered': True,
                'tax_id': 'NEWID999',
                'tax_id_non_vat_registered': None,
                'entity_name': 'My company',
                'address_details': 'Address 1',
                'city': 'City',
                'zipcode': '90111',
                'invoice_email': '<EMAIL>',
                'is_confirmed': False,
            },
            response.data['data'],
        )

    @patch(
        'webapps.navision.invoice_details_forms_serializers.invoice_details_confirmed_task.delay',
        MagicMock(),
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_update_tax_id_vat_registered_false(self):
        self.vat_registered = True
        self.tax_id = 'OLDTRN123'
        buyer = self.create_buyer()
        self.assertTrue(buyer.vat_registered)
        self.assertEqual('OLDTRN123', buyer.tax_id)

        self.vat_registered = False
        self.tax_id = '1234'
        self.tax_id_non_vat_registered = 'NEWID999'
        url = self.url_fnc(business_id=self.business.id)
        response = self.client.patch(url, data=self.body, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            {
                'vat_registered': False,
                'tax_id': None,
                'tax_id_non_vat_registered': 'NEWID999',
                'entity_name': 'My company',
                'address_details': 'Address 1',
                'city': 'City',
                'zipcode': '90111',
                'invoice_email': '<EMAIL>',
                'is_confirmed': False,
            },
            response.data['data'],
        )

    @patch(
        'webapps.navision.invoice_details_forms_serializers.invoice_details_confirmed_task.delay',
        MagicMock(),
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_can_update_invoice_details_sent_to_prod(self):
        url = self.url_fnc(business_id=self.business.id)
        self.create_buyer_sent_to_prod()
        body_put = dict(
            vat_registered=True,
            tax_id='999',
            tax_id_non_vat_registered=None,
            form_id=self.form_id,
            address_details=self.body['address_details'],
            city=self.body['city'],
            zipcode=self.body['zipcode'],
            is_confirmed=True,
        )
        body_put['address_details'] += 'changed'
        response = self.client.patch(url, data=body_put, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.business.refresh_from_db()
        self.business.buyer.refresh_from_db()
        body_with_modification = self.body
        body_with_modification.update(**body_put)
        body_with_modification.pop('is_confirmed')
        self.assert_buyer_is_correct(self.business.buyer, body_with_modification)

    @patch(
        'webapps.navision.invoice_details_forms_serializers.invoice_details_confirmed_task.delay',
        MagicMock(),
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_post_with_existing_buyer(self):
        url = self.url_fnc(business_id=self.business.id)
        self.client.post(url, data=self.body, **self.headers)
        response = self.client.post(url, data=self.body, **self.headers)
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
        self.assertEqual('invalid_method', response.data['detail'].code)

    @parameterized.expand(
        [
            (True,),
            (False,),
        ]
    )
    @patch(
        'webapps.navision.invoice_details_forms_serializers.invoice_details_confirmed_task.delay'
    )
    def test_get_with_is_confirmed(self, is_confirmed, mocked_task):
        self.create_buyer()
        url = self.url_fnc(business_id=self.business.id)
        InvoiceDetailsBusinessSettings.objects.update_or_create(
            business_id=self.business.id,
            invoice_details_last_confirmed_dt=(tznow() if is_confirmed else None),
        )
        response = self.client.get(url, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.assertEqual(response.data['data']['is_confirmed'], is_confirmed)
        mocked_task.assert_not_called()  # make sure nothing is updated during GET


class ProperTaxSerializerBasedOnPath(TestSetUpMixin):
    common_fields = (
        'entity_name',
        'address_details',
        'city',
        'zipcode',
        'invoice_email',
    )
    expected_data_fields_path_1 = ('tax_id',) + common_fields
    expected_data_fields_path_2_with_vat = ('vat_registered', 'tax_id') + common_fields
    expected_data_fields_path_confirm_with_vat = (
        'vat_registered',
        'tax_id',
        'is_confirmed',
    ) + common_fields
    expected_data_fields_path_3 = ('tax_id',) + common_fields

    def setUp(self) -> None:
        super().setUp()
        self.zipcode = baker.make(Region, name='90210', type=Region.Type.ZIP)
        self.body = dict(
            vat_registered=True,
            tax_id='123',
            entity_name='xxx',
            address_details='Address 1',
            city='City',
            zipcode=self.zipcode.name,
            invoice_email='<EMAIL>',
            is_confirmed=True,
        )

    @staticmethod
    def url_fnc(business_id):
        return reverse('invoice_details_forms', args=(business_id,))

    @parameterized.expand(
        [
            (
                InvoiceDetailsFormsEnum.PATH_1,
                MandatoryTaxInvoiceDetailsSerializer.FORM_ID,
                expected_data_fields_path_1,
            ),
            (
                InvoiceDetailsFormsEnum.PATH_2,
                TaxIfVATRegisteredInvoiceDetailsSerializer.FORM_ID,
                expected_data_fields_path_2_with_vat,
            ),
            (
                InvoiceDetailsFormsEnum.PATH_3,
                OptionalTaxInvoiceDetailsSerializer.FORM_ID,
                expected_data_fields_path_3,
            ),
        ]
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_check_proper_fields_create_invoice_details_forms_flag_off(
        self, path, form_id, expected_fields
    ):
        with override_settings(SUPPORTED_INVOICE_FORM=path):
            url = self.url_fnc(business_id=self.business.id)
            self.body['form_id'] = form_id
            response = self.client.post(url, data=self.body, **self.headers)
            self.assertEqual(status.HTTP_201_CREATED, response.status_code)
            self.assertSetEqual(set(response.data['data'].keys()), set(expected_fields))

    @parameterized.expand(
        [
            (
                InvoiceDetailsFormsEnum.PATH_1,
                MandatoryTaxInvoiceDetailsSerializer.FORM_ID,
                expected_data_fields_path_1,
            ),
            (
                InvoiceDetailsFormsEnum.PATH_2,
                EditAndConfirmaInvoiceDetailsSerializer.FORM_ID,
                expected_data_fields_path_confirm_with_vat,
            ),
            (
                InvoiceDetailsFormsEnum.PATH_3,
                OptionalTaxInvoiceDetailsSerializer.FORM_ID,
                expected_data_fields_path_3,
            ),
        ]
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    @override_eppo_feature_flag({ForceConfirmBillingInvoiceDataFlag.flag_name: True})
    def test_check_proper_fields_create_invoice_details_form(self, path, form_id, expected_fields):
        with override_settings(SUPPORTED_INVOICE_FORM=path):
            url = self.url_fnc(business_id=self.business.id)
            self.body['form_id'] = form_id
            response = self.client.post(url, data=self.body, **self.headers)
            self.assertEqual(status.HTTP_201_CREATED, response.status_code)
            self.assertSetEqual(set(response.data['data'].keys()), set(expected_fields))

    @parameterized.expand(
        [
            (InvoiceDetailsFormsEnum.PATH_1, expected_data_fields_path_1),
            (InvoiceDetailsFormsEnum.PATH_2, expected_data_fields_path_2_with_vat),
            (InvoiceDetailsFormsEnum.PATH_3, expected_data_fields_path_3),
        ]
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_get_invoice_details_fields_flag_off(self, path, expected_fields):
        baker.make(
            SubscriptionBuyer,
            businesses=[self.business],
            invoice_address=baker.make(
                InvoiceAddress,
                zipcode=self.zipcode,
                address_details1='jakis adres 10/12',
                city='Gotham City',
            ),
            invoice_email='<EMAIL>',
            entity_name='Example Co.',
            vat_registered=None,
            tax_id='123',
        )
        with override_settings(SUPPORTED_INVOICE_FORM=path):
            url = self.url_fnc(business_id=self.business.id)
            response = self.client.get(url, **self.headers)
            self.assertSetEqual(set(response.data['data'].keys()), set(expected_fields))

    @parameterized.expand(
        [
            (InvoiceDetailsFormsEnum.PATH_1, expected_data_fields_path_1),
            (InvoiceDetailsFormsEnum.PATH_2, expected_data_fields_path_confirm_with_vat),
            (InvoiceDetailsFormsEnum.PATH_3, expected_data_fields_path_3),
        ]
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    @override_eppo_feature_flag({ForceConfirmBillingInvoiceDataFlag.flag_name: True})
    def test_get_invoice_details_fields(self, path, expected_fields):
        baker.make(
            SubscriptionBuyer,
            businesses=[self.business],
            invoice_address=baker.make(
                InvoiceAddress,
                zipcode=self.zipcode,
                address_details1='jakis adres 10/12',
                city='Gotham City',
            ),
            invoice_email='<EMAIL>',
            entity_name='Example Co.',
            vat_registered=None,
            tax_id='123',
        )
        with override_settings(SUPPORTED_INVOICE_FORM=path):
            url = self.url_fnc(business_id=self.business.id)
            response = self.client.get(url, **self.headers)
            self.assertSetEqual(set(response.data['data'].keys()), set(expected_fields))

    @parameterized.expand(
        [
            (InvoiceDetailsFormsEnum.PATH_1,),
            (None,),
        ]
    )
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_post_invalid_form_id(self, path):
        with override_settings(SUPPORTED_INVOICE_FORM=path):
            url = self.url_fnc(business_id=self.business.id)
            self.body['form_id'] = 'przykladowa_nazwa'
            response = self.client.post(url, data=self.body, **self.headers)
            self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
            self.assertEqual(
                {
                    'errors': [
                        {
                            'code': 'invalid_form',
                            'description': 'Invalid invoice details form',
                        }
                    ]
                },
                response.data,
            )

    @override_settings(SUPPORTED_INVOICE_FORM=None)
    @patch('webapps.navision.views.invoice_details_changed_request.send', MagicMock())
    def test_get_invalid_path_none(self):
        url = self.url_fnc(business_id=self.business.id)
        response = self.client.get(url, **self.headers)
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
        self.assertEqual(
            {
                'errors': [
                    {
                        'code': 'invalid_form',
                        'description': 'Invalid invoice details form',
                    }
                ]
            },
            response.data,
        )


@pytest.mark.parametrize(
    'feature_flag_state', (False, True), ids=['feature_flag_off', 'feature_flag_on']
)
@pytest.mark.django_db
class TestTaxGroupsAreShownInForm:
    @override_settings(API_COUNTRY=Country.FR)
    def test_tax_groups_are_not_shown_if_only_one_group(
        self,
        feature_flag_state: bool,
        api_client: APIClient,
        fr_business: Business,
        fr_business_owner: User,
        booking_source: BookingSources,
        france_tax_group: TaxGroup,
    ):
        session = fr_business_owner.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

        with override_eppo_feature_flag(
            {DoNotRemoveEmptyTaxGroupInInvoiceDetailsForm.flag_name: feature_flag_state}
        ):
            response = api_client.get(
                reverse('invoice_details_forms', args=(fr_business.id,)),
                HTTP_X_ACCESS_TOKEN=session.session_key,
                HTTP_X_API_KEY=booking_source.api_key,
            )

        assert response.status_code == HTTPStatus.OK
        response_json = response.json()
        tax_group_form_field_present = 'tax_group' in response_json['form']['properties']
        tax_group_data_field_present = 'tax_group' in response_json['data']
        assert not tax_group_data_field_present
        assert not tax_group_form_field_present

    @override_settings(API_COUNTRY=Country.FR)
    def test_tax_groups_are_shown(
        self,
        feature_flag_state: bool,
        api_client: APIClient,
        fr_business: Business,
        fr_business_owner: User,
        booking_source: BookingSources,
        france_tax_group: TaxGroup,
        corse_tax_group: TaxGroup,
    ):
        session = fr_business_owner.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

        with override_eppo_feature_flag(
            {DoNotRemoveEmptyTaxGroupInInvoiceDetailsForm.flag_name: feature_flag_state}
        ):
            response = api_client.get(
                reverse('invoice_details_forms', args=(fr_business.id,)),
                HTTP_X_ACCESS_TOKEN=session.session_key,
                HTTP_X_API_KEY=booking_source.api_key,
            )

        assert response.status_code == HTTPStatus.OK
        response_json = response.json()
        tax_group_form_field_present = 'tax_group' in response_json['form']['properties']
        tax_group_form_field_value = response_json['form']['properties'].get('tax_group')
        tax_group_data_field_present = 'tax_group' in response_json['data']
        tax_group_data_field_value = response_json['data'].get('tax_group')
        assert tax_group_form_field_present
        assert tax_group_form_field_value == {
            'label': 'Registered tax territory',
            'required': True,
            'editable': True,
            'available_values': [
                {
                    'label': france_tax_group.name,
                    'value': france_tax_group.name,
                    'default': True,
                },
                {
                    'label': corse_tax_group.name,
                    'value': corse_tax_group.name,
                    'default': False,
                },
            ],
        }
        assert tax_group_data_field_present
        assert tax_group_data_field_value is None

    @override_settings(API_COUNTRY=Country.FR)
    def test_tax_groups_are_shown_for_existing_subscription_buyer(
        self,
        feature_flag_state: bool,
        api_client: APIClient,
        fr_business: Business,
        fr_business_owner: User,
        booking_source: BookingSources,
        france_tax_group: TaxGroup,
        corse_tax_group: TaxGroup,
        fr_subscription_buyer: SubscriptionBuyer,
    ):
        session = fr_business_owner.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

        with override_eppo_feature_flag(
            {DoNotRemoveEmptyTaxGroupInInvoiceDetailsForm.flag_name: feature_flag_state}
        ):
            response = api_client.get(
                reverse('invoice_details_forms', args=(fr_business.id,)),
                HTTP_X_ACCESS_TOKEN=session.session_key,
                HTTP_X_API_KEY=booking_source.api_key,
            )

        assert response.status_code == HTTPStatus.OK
        response_json = response.json()
        tax_group_form_field_present = 'tax_group' in response_json['form']['properties']
        tax_group_form_field_value = response_json['form']['properties'].get('tax_group')
        tax_group_data_field_present = 'tax_group' in response_json['data']
        tax_group_data_field_value = response_json['data'].get('tax_group')
        assert tax_group_form_field_present
        assert tax_group_form_field_value == {
            'label': 'Registered tax territory',
            'required': True,
            'editable': False,
            'available_values': [
                {
                    'label': france_tax_group.name,
                    'value': france_tax_group.name,
                    'default': True,
                },
                {
                    'label': corse_tax_group.name,
                    'value': corse_tax_group.name,
                    'default': False,
                },
            ],
        }
        assert tax_group_data_field_present
        assert tax_group_data_field_value == france_tax_group.name

    @override_settings(API_COUNTRY=Country.FR)
    def test_tax_groups_are_shown_for_existing_subscription_buyer_without_tax_group(
        self,
        feature_flag_state: bool,
        api_client: APIClient,
        fr_business: Business,
        fr_business_owner: User,
        booking_source: BookingSources,
        france_tax_group: TaxGroup,
        corse_tax_group: TaxGroup,
        fr_subscription_buyer: SubscriptionBuyer,
    ):
        fr_subscription_buyer.tax_group = None
        fr_subscription_buyer.save()
        fr_subscription_buyer.refresh_from_db()
        session = fr_business_owner.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

        with override_eppo_feature_flag(
            {DoNotRemoveEmptyTaxGroupInInvoiceDetailsForm.flag_name: feature_flag_state}
        ):
            response = api_client.get(
                reverse('invoice_details_forms', args=(fr_business.id,)),
                HTTP_X_API_KEY=booking_source.api_key,
                HTTP_X_ACCESS_TOKEN=session.session_key,
            )

        assert response.status_code == HTTPStatus.OK
        response_json = response.json()
        tax_group_form_field_present = 'tax_group' in response_json['form']['properties']
        tax_group_form_field_value = response_json['form']['properties'].get('tax_group')
        tax_group_data_field_present = 'tax_group' in response_json['data']
        tax_group_data_field_value = response_json['data'].get('tax_group')
        if feature_flag_state:
            assert tax_group_form_field_present
            assert tax_group_form_field_value == {
                'label': 'Registered tax territory',
                'required': True,
                'editable': False,
                'available_values': [
                    {
                        'label': france_tax_group.name,
                        'value': france_tax_group.name,
                        'default': True,
                    },
                    {
                        'label': corse_tax_group.name,
                        'value': corse_tax_group.name,
                        'default': False,
                    },
                ],
            }
            assert tax_group_data_field_present
            assert tax_group_data_field_value is None
        else:
            assert not tax_group_form_field_present
            assert not tax_group_data_field_present
