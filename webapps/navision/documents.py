from decimal import Decimal

from django.contrib.contenttypes.models import ContentType
from django.db.models import Count, Q, Min, Sum, Max, Case, When, F, Value, DecimalField
from django.db.models.functions import Coalesce

from lib.cache import lru_booksy_cache

from webapps.billing.models import BillingCycleProductCharge, BillingCycle
from webapps.business.models import Business
from webapps.invoice.models import BusinessInvoice
from webapps.navision.invoice_storage import invoice_file_exists
from webapps.navision.models import Invoice, InvoiceItem


@lru_booksy_cache(timeout=5 * 60, skip_in_pytest=True)
def check_invoice_storage(business: Business):
    invoices_to_update = []

    for invoice in Invoice.objects.filter(
        invoice_file__isnull=False,
        invoice_number__isnull=False,
        business_summary__business_id=business.id,
        invoice_file_exists=False,
    ):
        if invoice_file_exists(invoice):
            invoice.invoice_file_exists = True
            invoices_to_update.append(invoice)

    Invoice.objects.bulk_update(invoices_to_update, ['invoice_file_exists'])


def calculate_sum_with_discount(field_name: str):
    """
    field_name only from InvoiceItem "returned from Navision" fields
    >>> Invoice.objects.annotate(total=calculate_sum_with_discount('items__final_gross_value'))
    """
    return Sum(
        Case(
            When(
                items__service=Invoice.Service.DISCOUNT,
                then=Coalesce(F(field_name), Value(0)) * Value(-1),
            ),
            default=Coalesce(F(field_name), Value(0)),
            output_field=DecimalField(),
        ),
    )


def internal_invoices_qs(business: Business):
    """
    Queryset containing invoices issued from internal system (admin)
    """

    check_invoice_storage(business)

    return Invoice.objects.filter(
        invoice_file__isnull=False,
        invoice_number__isnull=False,
        invoice_file_exists=True,
        business_summary__business_id=business.id,
    ).annotate(
        billing_cycle_start=Min('items__billing_cycle_start'),
        billing_cycle_end=Max('items__billing_cycle_end'),
        total_gross_amount=calculate_sum_with_discount('items__final_gross_value'),
    )


def external_invoices_qs(business: Business):
    """
    Queryset containing invoices issued from external system (enova, chargebee, navision)
    """

    return business.invoices.all()


def billing_cycles_qs(business: Business):
    """
    Queryset containing data about past payments
    """

    return business.billing_cycles.all().annotate(
        total_gross_amount=Coalesce(
            Sum('product_charges__gross_final_price'),
            Sum('product_charges__final_price'),
        ),
        currency_iso_code=Max('product_charges__currency'),
    )


def billing_cycles_without_invoice_qs(business: Business):
    """
    Queryset containing data about past payments excluding already invoiced billing cycles
    """

    billing_charge_type = ContentType.objects.get_for_model(BillingCycleProductCharge)
    invoiced_charges = InvoiceItem.objects.filter(
        invoice__business_summary__business_id=business.id,
        invoice__invoice_file_exists=True,
        content_type=billing_charge_type,
    ).values_list('object_id', flat=True)

    return (
        business.billing_cycles.alias(
            invoiced_charges_cnt=Count(
                'product_charges',
                filter=Q(
                    product_charges__id__in=invoiced_charges,
                ),
            ),
        )
        .filter(
            invoiced_charges_cnt=0,
        )
        .annotate(
            total_gross_amount=Coalesce(
                Sum('product_charges__gross_final_price'),
                Sum('product_charges__final_price'),
            ),
            currency_iso_code=Max('product_charges__currency'),
        )
    )


def get_confirmation_qs(business_id: int):
    """
    Queryset containing payment history
    """

    return BillingCycle.objects.filter(
        business_id=business_id,
    ).annotate(
        total_net_amount=Sum('product_charges__final_price'),
        total_gross_amount=Coalesce(
            Sum('product_charges__gross_final_price'),
            'total_net_amount',
        ),
        total_tax_amount=Coalesce(Sum('product_charges__tax'), Decimal(0)),
        currency_iso_code=Max('product_charges__currency'),
    )


def get_confirmation(identifier: int, business_id: int):
    return get_confirmation_qs(business_id).filter(id=identifier)


def get_invoice_qs(business_id: int):
    """
    Queryset containing invoices issued in admin
    """

    return Invoice.objects.filter(
        business_summary__business_id=business_id,
        invoice_file_exists=True,
    ).annotate(
        total_gross_amount=calculate_sum_with_discount('items__final_gross_value'),
        total_net_amount=calculate_sum_with_discount('items__final_net_value'),
        total_tax_amount=calculate_sum_with_discount('items__final_tax_value'),
    )


def get_invoice(identifier: int, business_id: int):
    return get_invoice_qs(business_id).filter(id=identifier).first()


def get_external_invoice_qs(business_id: int):
    return BusinessInvoice.objects.filter(
        business_id=business_id,
    )


def get_external_invoice(identifier: int, business_id: int):
    """
    Queryset containing invoices issued in external systems
    """

    return get_external_invoice_qs(business_id).filter(id=identifier).first()
