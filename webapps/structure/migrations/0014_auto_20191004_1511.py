# Generated by Django 2.0.13 on 2019-10-04 15:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('structure', '0013_auto_20190517_1340'),
    ]

    operations = [
        migrations.AlterField(
            model_name='region',
            name='time_zone_name',
            field=models.TextField(
                blank=True,
                choices=[
                    ('Zulu', 'Zulu'),
                    ('W-SU', 'W-SU'),
                    ('UCT', 'UCT'),
                    ('Turkey', 'Turkey'),
                    ('Singapore', 'Singapore'),
                    ('ROK', 'ROK'),
                    ('ROC', 'ROC'),
                    ('Portugal', 'Portugal'),
                    ('Poland', 'Poland'),
                    ('PRC', 'PRC'),
                    ('Navajo', 'Navajo'),
                    ('NZ-CHAT', 'NZ-CHAT'),
                    ('NZ', 'NZ'),
                    ('Mexico/BajaNorte', 'Mexico/BajaNorte'),
                    ('Mexico/BajaSur', 'Mexico/BajaSur'),
                    ('Mexico/General', 'Mexico/General'),
                    ('Libya', 'Libya'),
                    ('<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'),
                    ('Japan', 'Japan'),
                    ('Jamaica', 'Jamaica'),
                    ('Israel', 'Israel'),
                    ('Iran', 'Iran'),
                    ('Iceland', 'Iceland'),
                    ('Hongkong', 'Hongkong'),
                    ('Greenwich', 'Greenwich'),
                    ('GB-Eire', 'GB-Eire'),
                    ('Eire', 'Eire'),
                    ('Egypt', 'Egypt'),
                    ('Cuba', 'Cuba'),
                    ('Chile/Continental', 'Chile/Continental'),
                    ('Chile/EasterIsland', 'Chile/EasterIsland'),
                    ('Canada/Atlantic', 'Canada/Atlantic'),
                    ('Canada/Central', 'Canada/Central'),
                    ('Canada/Eastern', 'Canada/Eastern'),
                    ('Canada/Mountain', 'Canada/Mountain'),
                    ('Canada/Newfoundland', 'Canada/Newfoundland'),
                    ('Canada/Pacific', 'Canada/Pacific'),
                    ('Canada/Saskatchewan', 'Canada/Saskatchewan'),
                    ('Canada/Yukon', 'Canada/Yukon'),
                    ('Brazil/Acre', 'Brazil/Acre'),
                    ('Brazil/DeNoronha', 'Brazil/DeNoronha'),
                    ('Brazil/East', 'Brazil/East'),
                    ('Brazil/West', 'Brazil/West'),
                    ('US/Alaska', 'US/Alaska'),
                    ('US/Aleutian', 'US/Aleutian'),
                    ('US/Arizona', 'US/Arizona'),
                    ('US/Central', 'US/Central'),
                    ('US/East-Indiana', 'US/East-Indiana'),
                    ('US/Eastern', 'US/Eastern'),
                    ('US/Hawaii', 'US/Hawaii'),
                    ('US/Indiana-Starke', 'US/Indiana-Starke'),
                    ('US/Michigan', 'US/Michigan'),
                    ('US/Pacific', 'US/Pacific'),
                    ('US/Samoa', 'US/Samoa'),
                    ('Arctic/Longyearbyen', 'Arctic/Longyearbyen'),
                    ('Factory', 'Factory'),
                    ('Etc/GMT+1', 'Etc/GMT+1'),
                    ('Etc/GMT+10', 'Etc/GMT+10'),
                    ('Etc/GMT+11', 'Etc/GMT+11'),
                    ('Etc/GMT+12', 'Etc/GMT+12'),
                    ('Etc/GMT+2', 'Etc/GMT+2'),
                    ('Etc/GMT+3', 'Etc/GMT+3'),
                    ('Etc/GMT+4', 'Etc/GMT+4'),
                    ('Etc/GMT+5', 'Etc/GMT+5'),
                    ('Etc/GMT+6', 'Etc/GMT+6'),
                    ('Etc/GMT+7', 'Etc/GMT+7'),
                    ('Etc/GMT+8', 'Etc/GMT+8'),
                    ('Etc/GMT+9', 'Etc/GMT+9'),
                    ('Etc/GMT-1', 'Etc/GMT-1'),
                    ('Etc/GMT-10', 'Etc/GMT-10'),
                    ('Etc/GMT-11', 'Etc/GMT-11'),
                    ('Etc/GMT-12', 'Etc/GMT-12'),
                    ('Etc/GMT-13', 'Etc/GMT-13'),
                    ('Etc/GMT-14', 'Etc/GMT-14'),
                    ('Etc/GMT-2', 'Etc/GMT-2'),
                    ('Etc/GMT-3', 'Etc/GMT-3'),
                    ('Etc/GMT-4', 'Etc/GMT-4'),
                    ('Etc/GMT-5', 'Etc/GMT-5'),
                    ('Etc/GMT-6', 'Etc/GMT-6'),
                    ('Etc/GMT-7', 'Etc/GMT-7'),
                    ('Etc/GMT-8', 'Etc/GMT-8'),
                    ('Etc/GMT-9', 'Etc/GMT-9'),
                    ('PST8PDT', 'PST8PDT'),
                    ('MST7MDT', 'MST7MDT'),
                    ('CST6CDT', 'CST6CDT'),
                    ('EST5EDT', 'EST5EDT'),
                    ('HST', 'HST'),
                    ('MST', 'MST'),
                    ('EST', 'EST'),
                    ('America/Anguilla', 'America/Anguilla'),
                    ('America/Araguaina', 'America/Araguaina'),
                    ('America/Argentina/Buenos_Aires', 'America/Argentina/Buenos_Aires'),
                    ('America/Argentina/Catamarca', 'America/Argentina/Catamarca'),
                    ('America/Argentina/Cordoba', 'America/Argentina/Cordoba'),
                    ('America/Argentina/Jujuy', 'America/Argentina/Jujuy'),
                    ('America/Argentina/La_Rioja', 'America/Argentina/La_Rioja'),
                    ('America/Argentina/Mendoza', 'America/Argentina/Mendoza'),
                    ('America/Argentina/Rio_Gallegos', 'America/Argentina/Rio_Gallegos'),
                    ('America/Argentina/Salta', 'America/Argentina/Salta'),
                    ('America/Argentina/San_Juan', 'America/Argentina/San_Juan'),
                    ('America/Argentina/San_Luis', 'America/Argentina/San_Luis'),
                    ('America/Argentina/Tucuman', 'America/Argentina/Tucuman'),
                    ('America/Argentina/Ushuaia', 'America/Argentina/Ushuaia'),
                    ('America/Aruba', 'America/Aruba'),
                    ('America/Asuncion', 'America/Asuncion'),
                    ('America/Atikokan', 'America/Atikokan'),
                    ('America/Bahia', 'America/Bahia'),
                    ('America/Bahia_Banderas', 'America/Bahia_Banderas'),
                    ('America/Barbados', 'America/Barbados'),
                    ('America/Belem', 'America/Belem'),
                    ('America/Belize', 'America/Belize'),
                    ('America/Blanc-Sablon', 'America/Blanc-Sablon'),
                    ('America/Boa_Vista', 'America/Boa_Vista'),
                    ('America/Bogota', 'America/Bogota'),
                    ('America/Boise', 'America/Boise'),
                    ('America/Cambridge_Bay', 'America/Cambridge_Bay'),
                    ('America/Campo_Grande', 'America/Campo_Grande'),
                    ('America/Cancun', 'America/Cancun'),
                    ('America/Caracas', 'America/Caracas'),
                    ('America/Cayenne', 'America/Cayenne'),
                    ('America/Cayman', 'America/Cayman'),
                    ('America/Chihuahua', 'America/Chihuahua'),
                    ('America/Costa_Rica', 'America/Costa_Rica'),
                    ('America/Creston', 'America/Creston'),
                    ('America/Cuiaba', 'America/Cuiaba'),
                    ('America/Danmarkshavn', 'America/Danmarkshavn'),
                    ('America/Dawson', 'America/Dawson'),
                    ('America/Dawson_Creek', 'America/Dawson_Creek'),
                    ('America/Eirunepe', 'America/Eirunepe'),
                    ('America/El_Salvador', 'America/El_Salvador'),
                    ('America/Fort_Nelson', 'America/Fort_Nelson'),
                    ('America/Fortaleza', 'America/Fortaleza'),
                    ('America/Glace_Bay', 'America/Glace_Bay'),
                    ('America/Godthab', 'America/Godthab'),
                    ('America/Goose_Bay', 'America/Goose_Bay'),
                    ('America/Grand_Turk', 'America/Grand_Turk'),
                    ('America/Guatemala', 'America/Guatemala'),
                    ('America/Guayaquil', 'America/Guayaquil'),
                    ('America/Guyana', 'America/Guyana'),
                    ('America/Hermosillo', 'America/Hermosillo'),
                    ('America/Indiana/Marengo', 'America/Indiana/Marengo'),
                    ('America/Indiana/Petersburg', 'America/Indiana/Petersburg'),
                    ('America/Indiana/Tell_City', 'America/Indiana/Tell_City'),
                    ('America/Indiana/Vevay', 'America/Indiana/Vevay'),
                    ('America/Indiana/Vincennes', 'America/Indiana/Vincennes'),
                    ('America/Indiana/Winamac', 'America/Indiana/Winamac'),
                    ('America/Inuvik', 'America/Inuvik'),
                    ('America/Iqaluit', 'America/Iqaluit'),
                    ('America/Juneau', 'America/Juneau'),
                    ('America/Kentucky/Louisville', 'America/Kentucky/Louisville'),
                    ('America/Kentucky/Monticello', 'America/Kentucky/Monticello'),
                    ('America/La_Paz', 'America/La_Paz'),
                    ('America/Lima', 'America/Lima'),
                    ('America/Maceio', 'America/Maceio'),
                    ('America/Managua', 'America/Managua'),
                    ('America/Martinique', 'America/Martinique'),
                    ('America/Matamoros', 'America/Matamoros'),
                    ('America/Menominee', 'America/Menominee'),
                    ('America/Merida', 'America/Merida'),
                    ('America/Metlakatla', 'America/Metlakatla'),
                    ('America/Miquelon', 'America/Miquelon'),
                    ('America/Moncton', 'America/Moncton'),
                    ('America/Monterrey', 'America/Monterrey'),
                    ('America/Montevideo', 'America/Montevideo'),
                    ('America/Nassau', 'America/Nassau'),
                    ('America/Nipigon', 'America/Nipigon'),
                    ('America/Nome', 'America/Nome'),
                    ('America/North_Dakota/Beulah', 'America/North_Dakota/Beulah'),
                    ('America/North_Dakota/Center', 'America/North_Dakota/Center'),
                    ('America/North_Dakota/New_Salem', 'America/North_Dakota/New_Salem'),
                    ('America/Ojinaga', 'America/Ojinaga'),
                    ('America/Pangnirtung', 'America/Pangnirtung'),
                    ('America/Paramaribo', 'America/Paramaribo'),
                    ('America/Port-au-Prince', 'America/Port-au-Prince'),
                    ('America/Porto_Velho', 'America/Porto_Velho'),
                    ('America/Puerto_Rico', 'America/Puerto_Rico'),
                    ('America/Punta_Arenas', 'America/Punta_Arenas'),
                    ('America/Rainy_River', 'America/Rainy_River'),
                    ('America/Rankin_Inlet', 'America/Rankin_Inlet'),
                    ('America/Recife', 'America/Recife'),
                    ('America/Resolute', 'America/Resolute'),
                    ('America/Santarem', 'America/Santarem'),
                    ('America/Santo_Domingo', 'America/Santo_Domingo'),
                    ('America/Scoresbysund', 'America/Scoresbysund'),
                    ('America/Sitka', 'America/Sitka'),
                    ('America/Swift_Current', 'America/Swift_Current'),
                    ('America/Tegucigalpa', 'America/Tegucigalpa'),
                    ('America/Thule', 'America/Thule'),
                    ('America/Thunder_Bay', 'America/Thunder_Bay'),
                    ('America/Yakutat', 'America/Yakutat'),
                    ('America/Yellowknife', 'America/Yellowknife'),
                    ('EET', 'EET'),
                    ('MET', 'MET'),
                    ('CET', 'CET'),
                    ('WET', 'WET'),
                    ('Europe/Amsterdam', 'Europe/Amsterdam'),
                    ('Europe/Andorra', 'Europe/Andorra'),
                    ('Europe/Astrakhan', 'Europe/Astrakhan'),
                    ('Europe/Athens', 'Europe/Athens'),
                    ('Europe/Belgrade', 'Europe/Belgrade'),
                    ('Europe/Berlin', 'Europe/Berlin'),
                    ('Europe/Bratislava', 'Europe/Bratislava'),
                    ('Europe/Brussels', 'Europe/Brussels'),
                    ('Europe/Bucharest', 'Europe/Bucharest'),
                    ('Europe/Budapest', 'Europe/Budapest'),
                    ('Europe/Busingen', 'Europe/Busingen'),
                    ('Europe/Chisinau', 'Europe/Chisinau'),
                    ('Europe/Copenhagen', 'Europe/Copenhagen'),
                    ('Europe/Gibraltar', 'Europe/Gibraltar'),
                    ('Europe/Helsinki', 'Europe/Helsinki'),
                    ('Europe/Kaliningrad', 'Europe/Kaliningrad'),
                    ('Europe/Kiev', 'Europe/Kiev'),
                    ('Europe/Kirov', 'Europe/Kirov'),
                    ('Europe/Luxembourg', 'Europe/Luxembourg'),
                    ('Europe/Madrid', 'Europe/Madrid'),
                    ('Europe/Malta', 'Europe/Malta'),
                    ('Europe/Minsk', 'Europe/Minsk'),
                    ('Europe/Monaco', 'Europe/Monaco'),
                    ('Europe/Nicosia', 'Europe/Nicosia'),
                    ('Europe/Paris', 'Europe/Paris'),
                    ('Europe/Riga', 'Europe/Riga'),
                    ('Europe/Rome', 'Europe/Rome'),
                    ('Europe/Samara', 'Europe/Samara'),
                    ('Europe/Saratov', 'Europe/Saratov'),
                    ('Europe/Simferopol', 'Europe/Simferopol'),
                    ('Europe/Sofia', 'Europe/Sofia'),
                    ('Europe/Stockholm', 'Europe/Stockholm'),
                    ('Europe/Tallinn', 'Europe/Tallinn'),
                    ('Europe/Tirane', 'Europe/Tirane'),
                    ('Europe/Ulyanovsk', 'Europe/Ulyanovsk'),
                    ('Europe/Uzhgorod', 'Europe/Uzhgorod'),
                    ('Europe/Vienna', 'Europe/Vienna'),
                    ('Europe/Vilnius', 'Europe/Vilnius'),
                    ('Europe/Volgograd', 'Europe/Volgograd'),
                    ('Europe/Zaporozhye', 'Europe/Zaporozhye'),
                    ('Pacific/Apia', 'Pacific/Apia'),
                    ('Pacific/Bougainville', 'Pacific/Bougainville'),
                    ('Pacific/Chuuk', 'Pacific/Chuuk'),
                    ('Pacific/Efate', 'Pacific/Efate'),
                    ('Pacific/Enderbury', 'Pacific/Enderbury'),
                    ('Pacific/Fakaofo', 'Pacific/Fakaofo'),
                    ('Pacific/Fiji', 'Pacific/Fiji'),
                    ('Pacific/Funafuti', 'Pacific/Funafuti'),
                    ('Pacific/Galapagos', 'Pacific/Galapagos'),
                    ('Pacific/Gambier', 'Pacific/Gambier'),
                    ('Pacific/Guadalcanal', 'Pacific/Guadalcanal'),
                    ('Pacific/Guam', 'Pacific/Guam'),
                    ('Pacific/Kiritimati', 'Pacific/Kiritimati'),
                    ('Pacific/Kosrae', 'Pacific/Kosrae'),
                    ('Pacific/Majuro', 'Pacific/Majuro'),
                    ('Pacific/Marquesas', 'Pacific/Marquesas'),
                    ('Pacific/Nauru', 'Pacific/Nauru'),
                    ('Pacific/Niue', 'Pacific/Niue'),
                    ('Pacific/Norfolk', 'Pacific/Norfolk'),
                    ('Pacific/Noumea', 'Pacific/Noumea'),
                    ('Pacific/Palau', 'Pacific/Palau'),
                    ('Pacific/Pitcairn', 'Pacific/Pitcairn'),
                    ('Pacific/Pohnpei', 'Pacific/Pohnpei'),
                    ('Pacific/Port_Moresby', 'Pacific/Port_Moresby'),
                    ('Pacific/Rarotonga', 'Pacific/Rarotonga'),
                    ('Pacific/Tahiti', 'Pacific/Tahiti'),
                    ('Pacific/Tarawa', 'Pacific/Tarawa'),
                    ('Pacific/Tongatapu', 'Pacific/Tongatapu'),
                    ('Pacific/Wake', 'Pacific/Wake'),
                    ('Pacific/Wallis', 'Pacific/Wallis'),
                    ('Australia/ACT', 'Australia/ACT'),
                    ('Australia/Adelaide', 'Australia/Adelaide'),
                    ('Australia/Brisbane', 'Australia/Brisbane'),
                    ('Australia/Broken_Hill', 'Australia/Broken_Hill'),
                    ('Australia/Currie', 'Australia/Currie'),
                    ('Australia/Darwin', 'Australia/Darwin'),
                    ('Australia/Eucla', 'Australia/Eucla'),
                    ('Australia/Hobart', 'Australia/Hobart'),
                    ('Australia/LHI', 'Australia/LHI'),
                    ('Australia/Lindeman', 'Australia/Lindeman'),
                    ('Australia/Melbourne', 'Australia/Melbourne'),
                    ('Australia/Perth', 'Australia/Perth'),
                    ('Asia/Aden', 'Asia/Aden'),
                    ('Asia/Almaty', 'Asia/Almaty'),
                    ('Asia/Amman', 'Asia/Amman'),
                    ('Asia/Anadyr', 'Asia/Anadyr'),
                    ('Asia/Aqtau', 'Asia/Aqtau'),
                    ('Asia/Aqtobe', 'Asia/Aqtobe'),
                    ('Asia/Ashgabat', 'Asia/Ashgabat'),
                    ('Asia/Atyrau', 'Asia/Atyrau'),
                    ('Asia/Baghdad', 'Asia/Baghdad'),
                    ('Asia/Bahrain', 'Asia/Bahrain'),
                    ('Asia/Baku', 'Asia/Baku'),
                    ('Asia/Bangkok', 'Asia/Bangkok'),
                    ('Asia/Barnaul', 'Asia/Barnaul'),
                    ('Asia/Beirut', 'Asia/Beirut'),
                    ('Asia/Bishkek', 'Asia/Bishkek'),
                    ('Asia/Brunei', 'Asia/Brunei'),
                    ('Asia/Calcutta', 'Asia/Calcutta'),
                    ('Asia/Chita', 'Asia/Chita'),
                    ('Asia/Choibalsan', 'Asia/Choibalsan'),
                    ('Asia/Colombo', 'Asia/Colombo'),
                    ('Asia/Dacca', 'Asia/Dacca'),
                    ('Asia/Damascus', 'Asia/Damascus'),
                    ('Asia/Dili', 'Asia/Dili'),
                    ('Asia/Dubai', 'Asia/Dubai'),
                    ('Asia/Dushanbe', 'Asia/Dushanbe'),
                    ('Asia/Famagusta', 'Asia/Famagusta'),
                    ('Asia/Gaza', 'Asia/Gaza'),
                    ('Asia/Hanoi', 'Asia/Hanoi'),
                    ('Asia/Hebron', 'Asia/Hebron'),
                    ('Asia/Ho_Chi_Minh', 'Asia/Ho_Chi_Minh'),
                    ('Asia/Hovd', 'Asia/Hovd'),
                    ('Asia/Irkutsk', 'Asia/Irkutsk'),
                    ('Asia/Jakarta', 'Asia/Jakarta'),
                    ('Asia/Jayapura', 'Asia/Jayapura'),
                    ('Asia/Kabul', 'Asia/Kabul'),
                    ('Asia/Kamchatka', 'Asia/Kamchatka'),
                    ('Asia/Karachi', 'Asia/Karachi'),
                    ('Asia/Kashgar', 'Asia/Kashgar'),
                    ('Asia/Kathmandu', 'Asia/Kathmandu'),
                    ('Asia/Khandyga', 'Asia/Khandyga'),
                    ('Asia/Krasnoyarsk', 'Asia/Krasnoyarsk'),
                    ('Asia/Kuala_Lumpur', 'Asia/Kuala_Lumpur'),
                    ('Asia/Kuching', 'Asia/Kuching'),
                    ('Asia/Macao', 'Asia/Macao'),
                    ('Asia/Magadan', 'Asia/Magadan'),
                    ('Asia/Makassar', 'Asia/Makassar'),
                    ('Asia/Manila', 'Asia/Manila'),
                    ('Asia/Novokuznetsk', 'Asia/Novokuznetsk'),
                    ('Asia/Novosibirsk', 'Asia/Novosibirsk'),
                    ('Asia/Omsk', 'Asia/Omsk'),
                    ('Asia/Oral', 'Asia/Oral'),
                    ('Asia/Pontianak', 'Asia/Pontianak'),
                    ('Asia/Pyongyang', 'Asia/Pyongyang'),
                    ('Asia/Qostanay', 'Asia/Qostanay'),
                    ('Asia/Qyzylorda', 'Asia/Qyzylorda'),
                    ('Asia/Rangoon', 'Asia/Rangoon'),
                    ('Asia/Sakhalin', 'Asia/Sakhalin'),
                    ('Asia/Samarkand', 'Asia/Samarkand'),
                    ('Asia/Srednekolymsk', 'Asia/Srednekolymsk'),
                    ('Asia/Tashkent', 'Asia/Tashkent'),
                    ('Asia/Tbilisi', 'Asia/Tbilisi'),
                    ('Asia/Thimbu', 'Asia/Thimbu'),
                    ('Asia/Tomsk', 'Asia/Tomsk'),
                    ('Asia/Ulaanbaatar', 'Asia/Ulaanbaatar'),
                    ('Asia/Ust-Nera', 'Asia/Ust-Nera'),
                    ('Asia/Vladivostok', 'Asia/Vladivostok'),
                    ('Asia/Yakutsk', 'Asia/Yakutsk'),
                    ('Asia/Yekaterinburg', 'Asia/Yekaterinburg'),
                    ('Asia/Yerevan', 'Asia/Yerevan'),
                    ('Antarctica/Casey', 'Antarctica/Casey'),
                    ('Antarctica/Davis', 'Antarctica/Davis'),
                    ('Antarctica/DumontDUrville', 'Antarctica/DumontDUrville'),
                    ('Antarctica/Macquarie', 'Antarctica/Macquarie'),
                    ('Antarctica/Mawson', 'Antarctica/Mawson'),
                    ('Antarctica/Palmer', 'Antarctica/Palmer'),
                    ('Antarctica/Rothera', 'Antarctica/Rothera'),
                    ('Antarctica/Syowa', 'Antarctica/Syowa'),
                    ('Antarctica/Troll', 'Antarctica/Troll'),
                    ('Antarctica/Vostok', 'Antarctica/Vostok'),
                    ('Indian/Antananarivo', 'Indian/Antananarivo'),
                    ('Indian/Chagos', 'Indian/Chagos'),
                    ('Indian/Christmas', 'Indian/Christmas'),
                    ('Indian/Cocos', 'Indian/Cocos'),
                    ('Indian/Kerguelen', 'Indian/Kerguelen'),
                    ('Indian/Mahe', 'Indian/Mahe'),
                    ('Indian/Maldives', 'Indian/Maldives'),
                    ('Indian/Mauritius', 'Indian/Mauritius'),
                    ('Indian/Reunion', 'Indian/Reunion'),
                    ('Atlantic/Azores', 'Atlantic/Azores'),
                    ('Atlantic/Bermuda', 'Atlantic/Bermuda'),
                    ('Atlantic/Canary', 'Atlantic/Canary'),
                    ('Atlantic/Cape_Verde', 'Atlantic/Cape_Verde'),
                    ('Atlantic/Faeroe', 'Atlantic/Faeroe'),
                    ('Atlantic/Madeira', 'Atlantic/Madeira'),
                    ('Atlantic/South_Georgia', 'Atlantic/South_Georgia'),
                    ('Atlantic/St_Helena', 'Atlantic/St_Helena'),
                    ('Atlantic/Stanley', 'Atlantic/Stanley'),
                    ('Africa/Accra', 'Africa/Accra'),
                    ('Africa/Algiers', 'Africa/Algiers'),
                    ('Africa/Bangui', 'Africa/Bangui'),
                    ('Africa/Bissau', 'Africa/Bissau'),
                    ('Africa/Blantyre', 'Africa/Blantyre'),
                    ('Africa/Casablanca', 'Africa/Casablanca'),
                    ('Africa/Ceuta', 'Africa/Ceuta'),
                    ('Africa/El_Aaiun', 'Africa/El_Aaiun'),
                    ('Africa/Johannesburg', 'Africa/Johannesburg'),
                    ('Africa/Juba', 'Africa/Juba'),
                    ('Africa/Khartoum', 'Africa/Khartoum'),
                    ('Africa/Monrovia', 'Africa/Monrovia'),
                    ('Africa/Ndjamena', 'Africa/Ndjamena'),
                    ('Africa/Sao_Tome', 'Africa/Sao_Tome'),
                    ('Africa/Tunis', 'Africa/Tunis'),
                    ('Africa/Windhoek', 'Africa/Windhoek'),
                    ('Universal', 'Universal'),
                    ('UTC', 'UTC'),
                    ('GMT0', 'GMT0'),
                    ('GMT-0', 'GMT-0'),
                    ('GMT+0', 'GMT+0'),
                    ('GB', 'GB'),
                    ('GMT', 'GMT'),
                    ('US/Mountain', 'US/Mountain'),
                    ('US/Pacific-New', 'US/Pacific-New'),
                    ('Etc/GMT', 'Etc/GMT'),
                    ('Etc/GMT+0', 'Etc/GMT+0'),
                    ('Etc/GMT-0', 'Etc/GMT-0'),
                    ('Etc/GMT0', 'Etc/GMT0'),
                    ('Etc/Greenwich', 'Etc/Greenwich'),
                    ('Etc/UCT', 'Etc/UCT'),
                    ('Etc/UTC', 'Etc/UTC'),
                    ('Etc/Universal', 'Etc/Universal'),
                    ('Etc/Zulu', 'Etc/Zulu'),
                    ('America/Adak', 'America/Adak'),
                    ('America/Anchorage', 'America/Anchorage'),
                    ('America/Antigua', 'America/Antigua'),
                    ('America/Argentina/ComodRivadavia', 'America/Argentina/ComodRivadavia'),
                    ('America/Atka', 'America/Atka'),
                    ('America/Buenos_Aires', 'America/Buenos_Aires'),
                    ('America/Catamarca', 'America/Catamarca'),
                    ('America/Chicago', 'America/Chicago'),
                    ('America/Coral_Harbour', 'America/Coral_Harbour'),
                    ('America/Cordoba', 'America/Cordoba'),
                    ('America/Curacao', 'America/Curacao'),
                    ('America/Denver', 'America/Denver'),
                    ('America/Detroit', 'America/Detroit'),
                    ('America/Dominica', 'America/Dominica'),
                    ('America/Edmonton', 'America/Edmonton'),
                    ('America/Ensenada', 'America/Ensenada'),
                    ('America/Fort_Wayne', 'America/Fort_Wayne'),
                    ('America/Grenada', 'America/Grenada'),
                    ('America/Guadeloupe', 'America/Guadeloupe'),
                    ('America/Halifax', 'America/Halifax'),
                    ('America/Havana', 'America/Havana'),
                    ('America/Indiana/Indianapolis', 'America/Indiana/Indianapolis'),
                    ('America/Indiana/Knox', 'America/Indiana/Knox'),
                    ('America/Indianapolis', 'America/Indianapolis'),
                    ('America/Jamaica', 'America/Jamaica'),
                    ('America/Jujuy', 'America/Jujuy'),
                    ('America/Knox_IN', 'America/Knox_IN'),
                    ('America/Kralendijk', 'America/Kralendijk'),
                    ('America/Los_Angeles', 'America/Los_Angeles'),
                    ('America/Louisville', 'America/Louisville'),
                    ('America/Lower_Princes', 'America/Lower_Princes'),
                    ('America/Manaus', 'America/Manaus'),
                    ('America/Marigot', 'America/Marigot'),
                    ('America/Mazatlan', 'America/Mazatlan'),
                    ('America/Mendoza', 'America/Mendoza'),
                    ('America/Mexico_City', 'America/Mexico_City'),
                    ('America/Montreal', 'America/Montreal'),
                    ('America/Montserrat', 'America/Montserrat'),
                    ('America/New_York', 'America/New_York'),
                    ('America/Noronha', 'America/Noronha'),
                    ('America/Panama', 'America/Panama'),
                    ('America/Phoenix', 'America/Phoenix'),
                    ('America/Port_of_Spain', 'America/Port_of_Spain'),
                    ('America/Porto_Acre', 'America/Porto_Acre'),
                    ('America/Regina', 'America/Regina'),
                    ('America/Rio_Branco', 'America/Rio_Branco'),
                    ('America/Rosario', 'America/Rosario'),
                    ('America/Santa_Isabel', 'America/Santa_Isabel'),
                    ('America/Santiago', 'America/Santiago'),
                    ('America/Sao_Paulo', 'America/Sao_Paulo'),
                    ('America/Shiprock', 'America/Shiprock'),
                    ('America/St_Barthelemy', 'America/St_Barthelemy'),
                    ('America/St_Johns', 'America/St_Johns'),
                    ('America/St_Kitts', 'America/St_Kitts'),
                    ('America/St_Lucia', 'America/St_Lucia'),
                    ('America/St_Thomas', 'America/St_Thomas'),
                    ('America/St_Vincent', 'America/St_Vincent'),
                    ('America/Tijuana', 'America/Tijuana'),
                    ('America/Toronto', 'America/Toronto'),
                    ('America/Tortola', 'America/Tortola'),
                    ('America/Vancouver', 'America/Vancouver'),
                    ('America/Virgin', 'America/Virgin'),
                    ('America/Whitehorse', 'America/Whitehorse'),
                    ('America/Winnipeg', 'America/Winnipeg'),
                    ('Europe/Belfast', 'Europe/Belfast'),
                    ('Europe/Dublin', 'Europe/Dublin'),
                    ('Europe/Guernsey', 'Europe/Guernsey'),
                    ('Europe/Isle_of_Man', 'Europe/Isle_of_Man'),
                    ('Europe/Istanbul', 'Europe/Istanbul'),
                    ('Europe/Jersey', 'Europe/Jersey'),
                    ('Europe/Lisbon', 'Europe/Lisbon'),
                    ('Europe/Ljubljana', 'Europe/Ljubljana'),
                    ('Europe/London', 'Europe/London'),
                    ('Europe/Mariehamn', 'Europe/Mariehamn'),
                    ('Europe/Moscow', 'Europe/Moscow'),
                    ('Europe/Oslo', 'Europe/Oslo'),
                    ('Europe/Podgorica', 'Europe/Podgorica'),
                    ('Europe/Prague', 'Europe/Prague'),
                    ('Europe/San_Marino', 'Europe/San_Marino'),
                    ('Europe/Sarajevo', 'Europe/Sarajevo'),
                    ('Europe/Skopje', 'Europe/Skopje'),
                    ('Europe/Tiraspol', 'Europe/Tiraspol'),
                    ('Europe/Vaduz', 'Europe/Vaduz'),
                    ('Europe/Vatican', 'Europe/Vatican'),
                    ('Europe/Warsaw', 'Europe/Warsaw'),
                    ('Europe/Zagreb', 'Europe/Zagreb'),
                    ('Europe/Zurich', 'Europe/Zurich'),
                    ('Pacific/Auckland', 'Pacific/Auckland'),
                    ('Pacific/Chatham', 'Pacific/Chatham'),
                    ('Pacific/Easter', 'Pacific/Easter'),
                    ('Pacific/Honolulu', 'Pacific/Honolulu'),
                    ('Pacific/Johnston', 'Pacific/Johnston'),
                    ('Pacific/Kwajalein', 'Pacific/Kwajalein'),
                    ('Pacific/Midway', 'Pacific/Midway'),
                    ('Pacific/Pago_Pago', 'Pacific/Pago_Pago'),
                    ('Pacific/Ponape', 'Pacific/Ponape'),
                    ('Pacific/Saipan', 'Pacific/Saipan'),
                    ('Pacific/Samoa', 'Pacific/Samoa'),
                    ('Pacific/Truk', 'Pacific/Truk'),
                    ('Pacific/Yap', 'Pacific/Yap'),
                    ('Australia/Canberra', 'Australia/Canberra'),
                    ('Australia/Lord_Howe', 'Australia/Lord_Howe'),
                    ('Australia/NSW', 'Australia/NSW'),
                    ('Australia/North', 'Australia/North'),
                    ('Australia/Queensland', 'Australia/Queensland'),
                    ('Australia/South', 'Australia/South'),
                    ('Australia/Sydney', 'Australia/Sydney'),
                    ('Australia/Tasmania', 'Australia/Tasmania'),
                    ('Australia/Victoria', 'Australia/Victoria'),
                    ('Australia/West', 'Australia/West'),
                    ('Australia/Yancowinna', 'Australia/Yancowinna'),
                    ('Asia/Ashkhabad', 'Asia/Ashkhabad'),
                    ('Asia/Chongqing', 'Asia/Chongqing'),
                    ('Asia/Chungking', 'Asia/Chungking'),
                    ('Asia/Dhaka', 'Asia/Dhaka'),
                    ('Asia/Harbin', 'Asia/Harbin'),
                    ('Asia/Hong_Kong', 'Asia/Hong_Kong'),
                    ('Asia/Istanbul', 'Asia/Istanbul'),
                    ('Asia/Jerusalem', 'Asia/Jerusalem'),
                    ('Asia/Katmandu', 'Asia/Katmandu'),
                    ('Asia/Kolkata', 'Asia/Kolkata'),
                    ('Asia/Kuwait', 'Asia/Kuwait'),
                    ('Asia/Macau', 'Asia/Macau'),
                    ('Asia/Muscat', 'Asia/Muscat'),
                    ('Asia/Nicosia', 'Asia/Nicosia'),
                    ('Asia/Phnom_Penh', 'Asia/Phnom_Penh'),
                    ('Asia/Qatar', 'Asia/Qatar'),
                    ('Asia/Riyadh', 'Asia/Riyadh'),
                    ('Asia/Saigon', 'Asia/Saigon'),
                    ('Asia/Seoul', 'Asia/Seoul'),
                    ('Asia/Shanghai', 'Asia/Shanghai'),
                    ('Asia/Singapore', 'Asia/Singapore'),
                    ('Asia/Taipei', 'Asia/Taipei'),
                    ('Asia/Tehran', 'Asia/Tehran'),
                    ('Asia/Tel_Aviv', 'Asia/Tel_Aviv'),
                    ('Asia/Thimphu', 'Asia/Thimphu'),
                    ('Asia/Tokyo', 'Asia/Tokyo'),
                    ('Asia/Ujung_Pandang', 'Asia/Ujung_Pandang'),
                    ('Asia/Ulan_Bator', 'Asia/Ulan_Bator'),
                    ('Asia/Urumqi', 'Asia/Urumqi'),
                    ('Asia/Vientiane', 'Asia/Vientiane'),
                    ('Asia/Yangon', 'Asia/Yangon'),
                    ('Antarctica/McMurdo', 'Antarctica/McMurdo'),
                    ('Antarctica/South_Pole', 'Antarctica/South_Pole'),
                    ('Indian/Comoro', 'Indian/Comoro'),
                    ('Indian/Mayotte', 'Indian/Mayotte'),
                    ('Atlantic/Faroe', 'Atlantic/Faroe'),
                    ('Atlantic/Jan_Mayen', 'Atlantic/Jan_Mayen'),
                    ('Atlantic/Reykjavik', 'Atlantic/Reykjavik'),
                    ('Africa/Abidjan', 'Africa/Abidjan'),
                    ('Africa/Addis_Ababa', 'Africa/Addis_Ababa'),
                    ('Africa/Asmara', 'Africa/Asmara'),
                    ('Africa/Asmera', 'Africa/Asmera'),
                    ('Africa/Bamako', 'Africa/Bamako'),
                    ('Africa/Banjul', 'Africa/Banjul'),
                    ('Africa/Brazzaville', 'Africa/Brazzaville'),
                    ('Africa/Bujumbura', 'Africa/Bujumbura'),
                    ('Africa/Cairo', 'Africa/Cairo'),
                    ('Africa/Conakry', 'Africa/Conakry'),
                    ('Africa/Dakar', 'Africa/Dakar'),
                    ('Africa/Dar_es_Salaam', 'Africa/Dar_es_Salaam'),
                    ('Africa/Djibouti', 'Africa/Djibouti'),
                    ('Africa/Douala', 'Africa/Douala'),
                    ('Africa/Freetown', 'Africa/Freetown'),
                    ('Africa/Gaborone', 'Africa/Gaborone'),
                    ('Africa/Harare', 'Africa/Harare'),
                    ('Africa/Kampala', 'Africa/Kampala'),
                    ('Africa/Kigali', 'Africa/Kigali'),
                    ('Africa/Kinshasa', 'Africa/Kinshasa'),
                    ('Africa/Lagos', 'Africa/Lagos'),
                    ('Africa/Libreville', 'Africa/Libreville'),
                    ('Africa/Lome', 'Africa/Lome'),
                    ('Africa/Luanda', 'Africa/Luanda'),
                    ('Africa/Lubumbashi', 'Africa/Lubumbashi'),
                    ('Africa/Lusaka', 'Africa/Lusaka'),
                    ('Africa/Malabo', 'Africa/Malabo'),
                    ('Africa/Maputo', 'Africa/Maputo'),
                    ('Africa/Maseru', 'Africa/Maseru'),
                    ('Africa/Mbabane', 'Africa/Mbabane'),
                    ('Africa/Mogadishu', 'Africa/Mogadishu'),
                    ('Africa/Nairobi', 'Africa/Nairobi'),
                    ('Africa/Niamey', 'Africa/Niamey'),
                    ('Africa/Nouakchott', 'Africa/Nouakchott'),
                    ('Africa/Ouagadougou', 'Africa/Ouagadougou'),
                    ('Africa/Porto-Novo', 'Africa/Porto-Novo'),
                    ('Africa/Timbuktu', 'Africa/Timbuktu'),
                    ('Africa/Tripoli', 'Africa/Tripoli'),
                    ('', ''),
                ],
                default='',
            ),
        ),
    ]
