from django.contrib import admin
from django.contrib.admin.models import (
    ADDITION,
    CHANGE,
    DELETION,
    LogEntry,
)
from django.urls import (
    NoReverseMatch,
    re_path,
    reverse,
)
from django.utils.html import format_html

from lib.admin_helpers import (
    BaseModelAdmin,
    NoAddDelMixin,
    ReadOnlyFieldsMixin,
)
from webapps.admin_extra.models import (
    BusinessCustomerImportLog,
    ManualTransferFund,
    SuperuserLoginEntry,
)
from webapps.admin_extra.views.customer import CustomerImportLogView
from webapps.payment_gateway.ports import PaymentGatewayPort

ACTIONS = {
    ADDITION: "ADDITION",
    CHANGE: "CHANGE",
    DELETION: "DELETION",
}


class ActionListFilter(admin.SimpleListFilter):
    title = "action"
    parameter_name = 'action_flag'

    def lookups(self, request, model_admin):
        return sorted(ACTIONS.items())

    def queryset(self, request, queryset):
        value = self.value()
        if value is None:
            return queryset
        return queryset.filter(action_flag=self.value())


class LogEntryAdmin(ReadOnlyFieldsMixin, NoAddDelMixin, BaseModelAdmin):
    date_hierarchy = 'action_time'

    list_filter = [
        ('user', admin.RelatedOnlyFieldListFilter),
        'content_type',
        ActionListFilter,
    ]

    search_fields = ['object_repr', 'change_message']

    list_display = [
        'action_time',
        'user',
        'user_email',
        'content_type',
        'object_link',
        'action_flag_pretty',
        'change_message',
    ]

    def has_change_permission(self, request, obj=None):
        return False

    def has_view_permission(self, request, obj=None):
        return request.user.is_superuser

    def get_readonly_fields(self, request, obj=None):
        fields = super().get_readonly_fields(request, obj)
        fields.append('user_email')
        return fields

    @staticmethod
    def user_email(obj):
        return obj.user.email

    @staticmethod
    def object_link(obj):
        if obj.action_flag == DELETION:
            link = obj.object_repr
        else:
            content_type = obj.content_type
            try:
                link = format_html(
                    '<a href="{}">{}</a>',
                    reverse(
                        f'admin:{content_type.app_label}_{content_type.model}_change',
                        args=[obj.object_id],
                    ),
                    obj.object_repr,
                )
            except NoReverseMatch:
                link = obj.object_repr
        return link

    object_link.admin_order_field = 'object_repr'
    object_link.short_description = 'object'

    @staticmethod
    def action_flag_pretty(obj):
        return ACTIONS.get(obj.action_flag, obj.action_flag)

    action_flag_pretty.admin_order_field = 'action_flag'
    action_flag_pretty.short_description = 'action'

    def queryset(self, request):
        return super().queryset(request).prefetch_related('content_type')


class SuperuserLoginEntryAdmin(ReadOnlyFieldsMixin, NoAddDelMixin, BaseModelAdmin):
    list_display = [
        'superuser_email',
        'user_email',
        'created',
    ]
    search_fields = [
        'superuser_email',
        'user_email',
    ]
    date_hierarchy = 'created'
    list_per_page = 100

    def has_module_permission(self, request):
        return request.user.is_superuser

    def has_change_permission(self, request, obj=None):
        return False

    def has_view_permission(self, request, obj=None):
        return request.user.is_superuser


class BusinessCustomerImportLogAdmin(NoAddDelMixin, BaseModelAdmin):
    list_display = [
        'business',
        'import_type',
        'imported_count',
        'created',
        'import_uid',
        'import_log',
    ]
    fields = list_display + ['device']
    readonly_fields = fields
    search_fields = (
        'business__name',
        'business__owner__email',
    )
    raw_id_fields = ['business']

    @staticmethod
    def import_log(obj):
        return format_html(
            '<a href="{}">Open</a>', reverse('admin:customer_import_log', args=(obj.id,))
        )

    def get_urls(self):
        urls = super().get_urls()
        return [
            re_path(
                r'^(?P<import_log_id>\d+)/customer_import_log/$',
                self.admin_site.admin_view(CustomerImportLogView.as_view()),
                name='customer_import_log',
            ),
        ] + urls


class ManualTransferFundAdmin(ReadOnlyFieldsMixin, NoAddDelMixin, BaseModelAdmin):
    readonly_fields = ['status']

    @staticmethod
    def status(obj):
        business_id = max(obj.source, obj.destination)
        wallet_id = PaymentGatewayPort.get_business_wallet(
            business_id=business_id,
        ).id
        return PaymentGatewayPort.get_balance_transaction(
            balance_transaction_id=obj.balance_transaction_id,
            wallet_id=wallet_id,
        ).status


admin.site.register(LogEntry, LogEntryAdmin)
admin.site.register(SuperuserLoginEntry, SuperuserLoginEntryAdmin)
admin.site.register(BusinessCustomerImportLog, BusinessCustomerImportLogAdmin)
admin.site.register(ManualTransferFund, ManualTransferFundAdmin)
