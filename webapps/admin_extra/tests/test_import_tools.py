from webapps.admin_extra.import_tools.helpers import split_full_name


class TestSplitFullNameFunction:
    def test_full_name_has_first_and_last_name(self):
        full_name = '<PERSON>'
        stripped_full_name, first_name, last_name = split_full_name(full_name)

        assert first_name == '<PERSON>'
        assert last_name == '<PERSON><PERSON>'
        assert stripped_full_name == '<PERSON>'

    def test_full_name_only_has_first_name(self):
        full_name = '<PERSON> '
        stripped_full_name, first_name, last_name = split_full_name(full_name)

        assert first_name == '<PERSON>'
        assert last_name == ''
        assert stripped_full_name == '<PERSON>'

    def test_full_name_is_made_up_of_3_names(self):
        full_name = 'John Doe1 Doe2'
        stripped_full_name, first_name, last_name = split_full_name(full_name)

        assert first_name == '<PERSON>'
        assert last_name == 'Doe1'
        assert stripped_full_name == 'John Doe1 Doe2'

    def test_full_name_is_stripped(self):
        full_name = '  <PERSON>   '
        stripped_full_name, _, _ = split_full_name(full_name)

        assert len(stripped_full_name) == 8
