from urllib.parse import parse_qs

from django.contrib.auth.hashers import make_password
from django.urls.base import reverse
from mock import (
    call,
    patch,
)

from webapps.admin_extra.tests import DjangoTestCase
from webapps.user.baker_recipes import user_recipe


class LoginTestCase(DjangoTestCase):
    @patch('allauth.socialaccount.providers.google.views.requests')
    @patch('allauth.socialaccount.providers.oauth2.client.requests')
    def test_login_admin_valid(
        self, mock_requests_in_get_access_token, mock_requests_in_complete_login
    ):
        google_app = self._create_social_app_google()
        admin = user_recipe.make(
            email='<EMAIL>',
            is_superuser=True,
            superuser=True,
            is_staff=True,
        )
        google_login = reverse('google_login')

        # check button to sign in by google exist
        resp_login = self.client.get(reverse('admin:login'))
        resp_login = resp_login.content.decode('utf-8')
        assert google_login in resp_login
        assert google_login in resp_login

        # check google_login url is valid redirect to google auth
        resp_google_login = self.client.get(google_login)
        url, url_params = resp_google_login.url.split('?')
        url_params = parse_qs(url_params)
        host = resp_google_login.wsgi_request.build_absolute_uri().rsplit('/', 6)[0]
        google_callback = reverse('google_callback')
        # step 1: redirect to Google's authorization server
        assert resp_google_login.status_code == 302
        assert url == 'https://accounts.google.com/o/oauth2/auth'
        assert url_params['client_id'] == [google_app.client_id]
        assert url_params['redirect_uri'] == [f'{host}{google_callback}']

        # "state" is also saved in session['socialaccount_state']
        state, code = url_params['state'], 'some_random_code'
        # mocked data for step: 2 - response from oauth server
        data = self._prepare_callback_data(code, state)

        # mocked data for step: 3 - get oauth2 token
        mock_requests_in_get_token = self._prepare_mock_in_get_access_token(
            mock_requests_in_get_access_token, code
        )
        # mocked data for step: 4 - get userinfo
        mock_requests_get_in_complete = self._prepare_mock_in_complete_login(
            mock_requests_in_complete_login,
            admin.email,
            admin.id,
        )

        # step 2: the OAuth server sends a response to the redirect_uri
        # from request
        resp_callback = self.client.get(google_callback, data)
        assert resp_callback.status_code == 302
        assert resp_callback.url == reverse('admin:index')
        # step 3: get oauth2 token (this step is called in step 2)
        self._check_mock_in_get_token(
            mock_requests_in_get_token, code, google_app.client_id, google_app.secret
        )
        # step 4: get user info by oauth2 token (this step is called in step 2)
        self._check_mock_in_complete_login(mock_requests_get_in_complete, code)
        resp_loged = self.client.get(resp_callback.url, data)
        content = resp_loged.content.decode('utf-8')
        assert resp_loged.status_code == 200
        assert f'Successfully signed in as {admin.email}' in content

        resp = self.client.get(reverse('admin:logout'), follow=True)
        assert 'You have signed out' in resp.content.decode('utf-8')

    @patch('allauth.socialaccount.providers.google.views.requests')
    @patch('allauth.socialaccount.providers.oauth2.client.requests')
    def test_login_admin_not_staff(
        self, mock_requests_in_get_access_token, mock_requests_in_complete_login
    ):
        google_app = self._create_social_app_google()
        admin = user_recipe.make(
            email='<EMAIL>',
            is_superuser=True,
            superuser=True,
            is_staff=False,
        )
        google_login = reverse('google_login')

        resp_google_login = self.client.get(google_login)
        _, url_params = resp_google_login.url.split('?')
        state, code = parse_qs(url_params)['state'], 'some_random_code'
        data = self._prepare_callback_data(code, state)

        # mocked data for step: 3 - get oauth2 token
        mock_requests_in_get_token = self._prepare_mock_in_get_access_token(
            mock_requests_in_get_access_token, code
        )
        # mocked data for step: 4 - get userinfo
        mock_requests_get_in_complete = self._prepare_mock_in_complete_login(
            mock_requests_in_complete_login,
            admin.email,
            admin.id,
        )

        # step 2: the OAuth server sends a response to the redirect_uri
        # from request
        resp_callback = self.client.get(reverse('google_callback'), data)
        assert resp_callback.status_code == 302
        assert resp_callback.url == reverse('admin:login')

        # step 3: get oauth2 token (this step is called in step 2)
        self._check_mock_in_get_token(
            mock_requests_in_get_token, code, google_app.client_id, google_app.secret
        )
        # step 4: get user info by oauth2 token (this step is called in step 2)
        self._check_mock_in_complete_login(mock_requests_get_in_complete, code)

        resp_loged = self.client.get(resp_callback.url, data)
        content = resp_loged.content.decode('utf-8')
        assert resp_loged.status_code == 200
        assert f'User is not staff: {admin.email}' in content

    @patch('allauth.socialaccount.providers.google.views.requests')
    @patch('allauth.socialaccount.providers.oauth2.client.requests')
    def test_login_admin_wrong_user(
        self, mock_requests_in_get_access_token, mock_requests_in_complete_login
    ):
        google_app = self._create_social_app_google()
        admin = user_recipe.make(
            email='<EMAIL>',
            is_superuser=True,
            superuser=True,
            is_staff=True,
        )
        google_login = reverse('google_login')

        resp_google_login = self.client.get(google_login)
        _, url_params = resp_google_login.url.split('?')
        state, code = parse_qs(url_params)['state'], 'some_random_code'
        data = self._prepare_callback_data(code, state)

        # mocked data for step: 3 - get oauth2 token
        mock_requests_in_get_token = self._prepare_mock_in_get_access_token(
            mock_requests_in_get_access_token, code
        )
        # mocked data for step: 4 - get userinfo
        invalid_email = 'in_' + admin.email
        mock_requests_get_in_complete = self._prepare_mock_in_complete_login(
            mock_requests_in_complete_login,
            invalid_email,
            admin.id + 1,
        )

        # step 2: the OAuth server sends a response to the redirect_uri
        # from request
        resp_callback = self.client.get(reverse('google_callback'), data)
        assert resp_callback.status_code == 302
        assert resp_callback.url == reverse('admin:login')

        # step 3: get oauth2 token (this step is called in step 2)
        self._check_mock_in_get_token(
            mock_requests_in_get_token, code, google_app.client_id, google_app.secret
        )
        # step 4: get user info by oauth2 token (this step is called in step 2)
        self._check_mock_in_complete_login(mock_requests_get_in_complete, code)

        resp_loged = self.client.get(resp_callback.url, data)
        content = resp_loged.content.decode('utf-8')
        assert resp_loged.status_code == 200
        assert f'Invalid user: {invalid_email}' in content

    @staticmethod
    def _check_mock_in_get_token(mock_requests, code, client_id, client_secret):
        assert mock_requests.call_args_list == [
            call(
                'POST',
                'https://accounts.google.com/o/oauth2/token',
                auth=None,
                data={
                    'redirect_uri': 'http://testserver/admin/us/auth/google/login/callback/',
                    'grant_type': 'authorization_code',
                    'code': code,
                    'client_id': client_id,
                    'client_secret': client_secret,
                },
                headers=None,
                params=None,
            )
        ]

    @staticmethod
    def _check_mock_in_complete_login(mock_requests, code):
        assert mock_requests.call_args_list == [
            call(
                'https://www.googleapis.com/oauth2/v1/userinfo',
                params={'access_token': code, 'alt': 'json'},
            )
        ]

    def test_old_login_admin(self):
        password = 'some_strong_password'
        admin = user_recipe.make(
            is_superuser=True,
            superuser=True,
            is_staff=True,
            password=make_password(password),
        )
        resp = self.client.post(
            reverse('admin:login'),
            dict(
                username=admin.email,
                password=password,
            ),
        )

        # POST is not allowed
        assert resp.status_code == 405

        resp = self.client.get(reverse('admin:index'))
        assert resp.status_code == 302
        assert resp.url.startswith(reverse('admin:login'))

    @patch('allauth.socialaccount.providers.google.views.requests')
    @patch('allauth.socialaccount.providers.oauth2.client.requests')
    def test_login_after_revoke_permission(
        self, mock_requests_in_get_access_token, mock_requests_in_complete_login
    ):
        # prepare all data and mocks required to login
        self._create_social_app_google()
        admin = user_recipe.make(
            email='<EMAIL>',
            is_superuser=True,
            superuser=True,
            is_staff=True,
            is_active=True,
        )
        google_login = reverse('google_login')

        resp_google_login = self.client.get(google_login)
        _, url_params = resp_google_login.url.split('?')
        state, code = parse_qs(url_params)['state'], 'some_random_code'
        data = self._prepare_callback_data(code, state)

        self._prepare_mock_in_get_access_token(mock_requests_in_get_access_token, code)
        self._prepare_mock_in_complete_login(
            mock_requests_in_complete_login,
            admin.email,
            admin.id,
        )
        resp_callback = self.client.get(reverse('google_callback'), data)

        resp_loged = self.client.get(resp_callback.url, data)
        content = resp_loged.content.decode('utf-8')
        url_logout = reverse('admin:logout')

        # check if user can login
        assert resp_loged.status_code == 200
        assert url_logout in content
        # logout
        self.client.get(url_logout, follow=True)

        # change permission
        admin.is_staff = False
        admin.save()

        # try login without permission
        resp_google_login = self.client.get(google_login)
        _, url_params = resp_google_login.url.split('?')
        state, code = parse_qs(url_params)['state'], 'some_random_code'
        data = self._prepare_callback_data(code, state)
        resp_callback = self.client.get(reverse('google_callback'), data)
        assert resp_callback.status_code == 302
        assert resp_callback.url == reverse('admin:login')

        resp_login = self.client.get(resp_callback.url, data)

        assert resp_login.status_code == 200
        content = resp_login.content.decode('utf-8')
        assert f'User is not staff: {admin.email}' in content
