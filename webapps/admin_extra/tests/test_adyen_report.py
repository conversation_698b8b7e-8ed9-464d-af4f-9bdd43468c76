import random
from datetime import (
    datetime,
    timedelta,
)
from decimal import Decimal
from uuid import uuid4

import pytest
from django.conf import settings
from django.db import connection
from django.test import TestCase
from django.test.utils import override_settings
from model_bakery import baker
from openpyxl import load_workbook

from country_config.enums import Country
from lib.tools import tznow
from webapps.admin_extra import consts
from webapps.admin_extra.views.adyen import (
    AdyenPayoutReportView,
    AdyenTxnsReportView,
)
from webapps.adyen.consts import oper_result
from webapps.adyen.models import Capture
from webapps.booking.models import SubBooking
from webapps.business.models import Business
from webapps.pos.enums import PaymentTypeEnum, receipt_status
from webapps.pos.models import (
    POS,
    PaymentRow,
    PaymentType,
    POSPlan,
    Receipt,
    Transaction,
)


def _add_booking_to_txn(booking_source, business, txn, user):
    booking = baker.prepare(SubBooking)
    booking.appointment.source = booking_source
    booking.appointment.updated_by = user
    booking.appointment.business = business
    booking.appointment.save()
    booking.save(override=True)
    txn.appointment = booking.appointment
    txn.save()


@pytest.mark.django_db
class PayoutReportGetDataTestCase(TestCase):
    @classmethod
    @override_settings(POS__PLANS=settings.POS__PLANS_PER_COUNTRY['pl'])
    def setUpTestData(cls):
        cls.business = baker.make(Business)
        cls.pos = baker.make(POS, business=cls.business)
        cls.business.pos = cls.pos
        cls.business.save()
        for plan in settings.POS__PLANS:
            cls.pos.pos_plans.add(POSPlan.smart_create(plan))

        cls.pba = baker.make(PaymentType, code=PaymentTypeEnum.PAY_BY_APP, pos=cls.pos)

        cls.prepayment = baker.make(PaymentType, code=PaymentTypeEnum.PREPAYMENT, pos=cls.pos)

        cls.cash = baker.make(PaymentType, code=PaymentTypeEnum.CASH, pos=cls.pos)

        cls.today = tznow().date()

        txns = baker.make(
            Transaction,
            pos=cls.pos,
            settled=False,
            ready_for_settle=False,
            total=1,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            _quantity=100,
        )

        # Create not included transactions
        cls.test_data = [
            # PaymentType, Status, Settled
            (cls.cash, receipt_status.PAYMENT_SUCCESS, False),
            (cls.pba, receipt_status.CALL_FOR_PAYMENT, False),
            (cls.pba, receipt_status.PAYMENT_FAILED, False),
            (cls.pba, receipt_status.PAYMENT_SUCCESS, True),
            (cls.prepayment, receipt_status.PAYMENT_SUCCESS, True),
            (cls.prepayment, receipt_status.PREPAYMENT_SUCCESS, True),
        ]

        for txn in txns:
            payment = random.choice(cls.test_data)
            receipt = baker.make(
                Receipt,
                payment_type=payment[0],
                status_code=payment[1],
                transaction=txn,
            )

            txn.latest_receipt = receipt
            txn.save()

            pnref = uuid4().hex
            baker.make(
                PaymentRow,
                pnref=pnref,
                payment_type=payment[0],
                status=payment[1],
                amount=txn.total,
                receipt=receipt,
                settled=payment[2],
            )

            baker.make(
                Capture,
                reference=pnref,
                oper_result=oper_result.SUCCESS,
            )

        # Create old transactions

        txn = baker.make(
            Transaction,
            pos=cls.pos,
            settled=False,
            ready_for_settle=False,
            total=1,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        receipt = baker.make(
            Receipt,
            payment_type=cls.cash,
            status_code=receipt_status.PAYMENT_SUCCESS,
            transaction=txn,
        )

        cursor = connection.cursor()
        cursor.execute(
            """
            UPDATE pos_receipt
            SET created=%s
            WHERE receipt_id=%s;
        """,
            ((tznow() - timedelta(days=1)).isoformat(), receipt.id),
        )

        txn.latest_receipt = receipt
        txn.save()

        pnref = uuid4().hex
        baker.make(
            PaymentRow,
            pnref=pnref,
            payment_type=cls.cash,
            status=receipt_status.PAYMENT_SUCCESS,
            amount=txn.total,
            receipt=receipt,
            settled=False,
        )

        baker.make(
            Capture,
            reference=pnref,
            oper_result=oper_result.SUCCESS,
        )

    def test_success_pba(self):
        txn = baker.make(
            Transaction,
            pos=self.pos,
            settled=False,
            ready_for_settle=True,
            total=1.23,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        pnref = uuid4().hex
        receipt = baker.make(
            Receipt,
            payment_type=self.pba,
            status_code=receipt_status.PAYMENT_SUCCESS,
            transaction=txn,
        )
        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            PaymentRow,
            pnref=pnref,
            payment_type=self.pba,
            status=receipt_status.PAYMENT_SUCCESS,
            amount=txn.total,
            receipt=receipt,
            settled=False,
        )

        baker.make(
            Capture,
            reference=pnref,
            oper_result=oper_result.SUCCESS,
        )

        result = AdyenPayoutReportView.get_data(self.today, self.today)

        assert len(result) == 1
        assert result[self.business.id]['pr_num'] == 1
        assert result[self.business.id]['prs_total'] == Decimal('1.23')
        assert result[self.business.id]['total_fee'] == Decimal('0.51')

    def test_success_cf(self):
        txn = baker.make(
            Transaction,
            pos=self.pos,
            settled=False,
            ready_for_settle=True,
            total=1.23,
            transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
        )

        pnref = uuid4().hex
        receipt = baker.make(
            Receipt,
            payment_type=self.pba,
            status_code=receipt_status.PAYMENT_SUCCESS,
            transaction=txn,
        )
        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            PaymentRow,
            pnref=pnref,
            payment_type=self.pba,
            status=receipt_status.PAYMENT_SUCCESS,
            amount=txn.total,
            receipt=receipt,
            settled=False,
        )

        baker.make(
            Capture,
            reference=pnref,
            oper_result=oper_result.SUCCESS,
        )

        result = AdyenPayoutReportView.get_data(self.today, self.today)

        assert len(result) == 1
        assert result[self.business.id]['pr_num'] == Decimal('1')
        assert result[self.business.id]['prs_total'] == Decimal('1.23')
        assert result[self.business.id]['total_fee'] == Decimal('0.51')

    def test_success_prepayment(self):
        txn = baker.make(
            Transaction,
            pos=self.pos,
            settled=False,
            ready_for_settle=True,
            total=1.23,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        pnref = uuid4().hex
        receipt = baker.make(
            Receipt,
            payment_type=self.prepayment,
            status_code=receipt_status.PREPAYMENT_SUCCESS,
            transaction=txn,
        )
        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            PaymentRow,
            pnref=pnref,
            payment_type=self.prepayment,
            status=receipt_status.PREPAYMENT_SUCCESS,
            amount=txn.total,
            receipt=receipt,
            settled=False,
        )

        baker.make(
            Capture,
            reference=pnref,
            oper_result=oper_result.SUCCESS,
        )

        result = AdyenPayoutReportView.get_data(self.today, self.today)

        assert len(result) == 1

    def test_success_prepayment_pba(self):
        txn = baker.make(
            Transaction,
            pos=self.pos,
            settled=False,
            ready_for_settle=True,
            total=1.23,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        # First receipt
        pnref = uuid4().hex
        receipt = baker.make(
            Receipt,
            payment_type=self.prepayment,
            status_code=receipt_status.PREPAYMENT_SUCCESS,
            transaction=txn,
        )

        baker.make(
            PaymentRow,
            pnref=pnref,
            payment_type=self.prepayment,
            status=receipt_status.PREPAYMENT_SUCCESS,
            amount=1,
            receipt=receipt,
            settled=False,
        )
        baker.make(
            Capture,
            reference=pnref,
            oper_result=oper_result.SUCCESS,
        )

        pnref2 = uuid4().hex
        receipt = baker.make(
            Receipt,
            payment_type=self.prepayment,
            status_code=receipt_status.PAYMENT_SUCCESS,
            transaction=txn,
        )
        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            PaymentRow,
            pnref=pnref,
            payment_type=self.prepayment,
            status=receipt_status.PAYMENT_SUCCESS,
            amount=1,
            receipt=receipt,
            settled=False,
        )
        baker.make(
            PaymentRow,
            pnref=pnref2,
            payment_type=self.pba,
            status=receipt_status.PAYMENT_SUCCESS,
            amount=0.23,
            receipt=receipt,
            settled=False,
        )
        baker.make(
            Capture,
            reference=pnref2,
            oper_result=oper_result.SUCCESS,
        )
        result = AdyenPayoutReportView.get_data(self.today, self.today)

        assert len(result) == 1
        assert result[self.business.id]['pr_num'] == Decimal('2')
        assert result[self.business.id]['prs_total'] == Decimal('1.23')
        assert result[self.business.id]['total_fee'] == Decimal('1.0')


@pytest.mark.django_db
@override_settings(API_COUNTRY=Country.PL)
def test_payout_report_get_report():
    ystrdy = (tznow() - timedelta(days=1)).date()
    today = tznow().date()
    data = {
        1: {
            'pr_pct_fee': 0.28,
            'prs_total': 15.0,
            'pr_num': Decimal('10'),
            'pr_fixed_fees': 4.9,
            'payout': 9.82,
            'total_fee': 5.18,
            'business_name': 'Zażółć gęślą jaźń',
            'payment_row_ids': '1, 2, 3',
        },
        2: {
            'pr_pct_fee': 0.14,
            'prs_total': 7.5,
            'pr_num': Decimal('5'),
            'pr_fixed_fees': 2.45,
            'payout': 4.91,
            'total_fee': 2.59,
            'business_name': 'Barber',
            'payment_row_ids': '11, 22, 33',
        },
    }

    myfile, mime, filename = AdyenPayoutReportView.get_report(data, ystrdy, today)

    assert mime == consts.XLS_MIME
    assert filename == f'adyen_payout_report_{ystrdy}_{today}.xlsx'

    workbook = load_workbook(myfile)
    assert len(workbook.worksheets) == 1

    rows = list(workbook.worksheets[0].rows)
    assert rows[0][0].value == 'date range'
    assert rows[0][1].value == 'business name'
    assert rows[0][2].value == 'business id'
    assert rows[0][3].value == 'transactions number'
    assert rows[0][4].value == 'transactions fixed fees'
    assert rows[0][5].value == 'transactions % fees'
    assert rows[0][6].value == 'total transactions amount'
    assert rows[0][7].value == 'total fee'
    assert rows[0][8].value == 'payout amount'
    assert rows[0][9].value == 'payment_row_ids'

    assert rows[1][0].value == f"{ystrdy.strftime('%Y-%m-%d')} - {today.strftime('%Y-%m-%d')}"
    assert rows[1][1].value == 'Zażółć gęślą jaźń'
    assert rows[1][2].value == 1
    assert rows[1][3].value == 10
    assert rows[1][4].value == 4.9
    assert rows[1][5].value == 0.28
    assert rows[1][6].value == 15
    assert rows[1][7].value == 5.18
    assert rows[1][8].value == 9.82
    assert rows[1][9].value == '1, 2, 3'

    assert rows[2][0].value == f"{ystrdy.strftime('%Y-%m-%d')} - {today.strftime('%Y-%m-%d')}"
    assert rows[2][1].value == 'Barber'
    assert rows[2][2].value == 2
    assert rows[2][3].value == 5
    assert rows[2][4].value == 2.45
    assert rows[2][5].value == 0.14
    assert rows[2][6].value == 7.5
    assert rows[2][7].value == 2.59
    assert rows[2][8].value == 4.91
    assert rows[2][9].value == '11, 22, 33'


@pytest.mark.django_db
class TxnReportGetDataTestCase(TestCase):
    @classmethod
    @override_settings(POS__PLANS=settings.POS__PLANS_PER_COUNTRY['pl'])
    def setUpTestData(cls):
        cls.business = baker.make(Business)
        cls.pos = baker.make(POS, business=cls.business)
        cls.business.pos = cls.pos
        cls.business.save()
        for plan in settings.POS__PLANS:
            cls.pos.pos_plans.add(POSPlan.smart_create(plan))

        cls.pba = baker.make(PaymentType, code=PaymentTypeEnum.PAY_BY_APP, pos=cls.pos)

        cls.prepayment = baker.make(PaymentType, code=PaymentTypeEnum.PREPAYMENT, pos=cls.pos)

        cls.cash = baker.make(PaymentType, code=PaymentTypeEnum.CASH, pos=cls.pos)

        cls.today = tznow().date()

        txns = baker.make(
            Transaction,
            pos=cls.pos,
            settled=False,
            ready_for_settle=False,
            total=1,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            _quantity=100,
        )

        # Create not included transactions
        cls.test_data = [
            # PaymentType, Status, Settled
            (cls.cash, receipt_status.PAYMENT_SUCCESS, False),
            (cls.pba, receipt_status.CALL_FOR_PAYMENT, False),
            (cls.pba, receipt_status.PAYMENT_FAILED, False),
            (cls.pba, receipt_status.PAYMENT_SUCCESS, True),
            (cls.prepayment, receipt_status.PAYMENT_SUCCESS, True),
            (cls.prepayment, receipt_status.PREPAYMENT_SUCCESS, True),
        ]

        for txn in txns:
            payment = random.choice(cls.test_data)
            receipt = baker.make(
                Receipt,
                payment_type=payment[0],
                status_code=payment[1],
                transaction=txn,
            )

            txn.latest_receipt = receipt
            txn.save()

            pnref = uuid4().hex
            baker.make(
                PaymentRow,
                pnref=pnref,
                payment_type=payment[0],
                status=payment[1],
                amount=txn.total,
                receipt=receipt,
                settled=payment[2],
            )

            baker.make(
                Capture,
                reference=pnref,
                oper_result=oper_result.SUCCESS,
            )

        # Create old transactions

        txn = baker.make(
            Transaction,
            pos=cls.pos,
            settled=False,
            ready_for_settle=False,
            total=1,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        receipt = baker.make(
            Receipt,
            payment_type=cls.cash,
            status_code=receipt_status.PAYMENT_SUCCESS,
            transaction=txn,
        )

        cursor = connection.cursor()
        cursor.execute(
            """
            UPDATE pos_receipt
            SET created=%s
            WHERE receipt_id=%s;
        """,
            ((tznow() - timedelta(days=1)).isoformat(), receipt.id),
        )

        txn.latest_receipt = receipt
        txn.save()

        pnref = uuid4().hex
        baker.make(
            PaymentRow,
            pnref=pnref,
            payment_type=cls.cash,
            status=receipt_status.PAYMENT_SUCCESS,
            amount=txn.total,
            receipt=receipt,
            settled=False,
        )

        baker.make(
            Capture,
            reference=pnref,
            oper_result=oper_result.SUCCESS,
        )

    def test_success_pba(self):
        txn = baker.make(
            Transaction,
            pos=self.pos,
            settled=False,
            ready_for_settle=True,
            total=1.23,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        pnref = uuid4().hex
        receipt = baker.make(
            Receipt,
            payment_type=self.pba,
            status_code=receipt_status.PAYMENT_SUCCESS,
            transaction=txn,
        )
        txn.latest_receipt = receipt
        txn.save()

        pr = baker.make(
            PaymentRow,
            pnref=pnref,
            payment_type=self.pba,
            status=receipt_status.PAYMENT_SUCCESS,
            amount=txn.total,
            receipt=receipt,
            settled=False,
        )

        baker.make(
            Capture,
            reference=pnref,
            oper_result=oper_result.SUCCESS,
        )

        result = AdyenTxnsReportView.get_data(self.today, self.today)

        assert len(result) == 1
        assert result[0]['date'] == pr.created
        assert result[0]['business_id'] == self.business.id
        assert result[0]['pr_id'] == pr.id
        assert result[0]['amount'] == Decimal('1.23')
        assert result[0]['fixed_fee'] == Decimal('0.49')
        assert result[0]['pct_fee'] == Decimal('0.02')
        assert result[0]['total_fee'] == Decimal('0.51')
        assert result[0]['payout'] == Decimal('0.72')

    def test_success_cf(self):
        txn = baker.make(
            Transaction,
            pos=self.pos,
            settled=False,
            ready_for_settle=True,
            total=1.23,
            transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
        )

        pnref = uuid4().hex
        receipt = baker.make(
            Receipt,
            payment_type=self.pba,
            status_code=receipt_status.PAYMENT_SUCCESS,
            transaction=txn,
        )
        txn.latest_receipt = receipt
        txn.save()

        pr = baker.make(
            PaymentRow,
            pnref=pnref,
            payment_type=self.pba,
            status=receipt_status.PAYMENT_SUCCESS,
            amount=txn.total,
            receipt=receipt,
            settled=False,
        )

        baker.make(
            Capture,
            reference=pnref,
            oper_result=oper_result.SUCCESS,
        )

        result = AdyenTxnsReportView.get_data(self.today, self.today)

        assert len(result) == 1
        assert result[0]['date'] == pr.created
        assert result[0]['business_id'] == self.business.id
        assert result[0]['pr_id'] == pr.id
        assert result[0]['amount'] == Decimal('1.23')
        assert result[0]['fixed_fee'] == Decimal('0.49')
        assert result[0]['pct_fee'] == Decimal('0.02')
        assert result[0]['total_fee'] == Decimal('0.51')
        assert result[0]['payout'] == Decimal('0.72')

    def test_success_prepayment(self):
        txn = baker.make(
            Transaction,
            pos=self.pos,
            settled=False,
            ready_for_settle=True,
            total=1.23,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        pnref = uuid4().hex
        receipt = baker.make(
            Receipt,
            payment_type=self.prepayment,
            status_code=receipt_status.PREPAYMENT_SUCCESS,
            transaction=txn,
        )
        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            PaymentRow,
            pnref=pnref,
            payment_type=self.prepayment,
            status=receipt_status.PREPAYMENT_SUCCESS,
            amount=txn.total,
            receipt=receipt,
            settled=False,
        )

        baker.make(
            Capture,
            reference=pnref,
            oper_result=oper_result.SUCCESS,
        )

        result = AdyenTxnsReportView.get_data(self.today, self.today)

        assert len(result) == 1

    def test_success_prepayment_pba(self):
        txn = baker.make(
            Transaction,
            pos=self.pos,
            settled=False,
            ready_for_settle=True,
            total=3,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

        # First receipt
        pnref = uuid4().hex
        receipt = baker.make(
            Receipt,
            payment_type=self.prepayment,
            status_code=receipt_status.PREPAYMENT_SUCCESS,
            transaction=txn,
        )

        pr0 = baker.make(
            PaymentRow,
            pnref=pnref,
            payment_type=self.prepayment,
            status=receipt_status.PREPAYMENT_SUCCESS,
            amount=1,
            receipt=receipt,
            settled=False,
        )
        baker.make(
            Capture,
            reference=pnref,
            oper_result=oper_result.SUCCESS,
        )

        pnref2 = uuid4().hex
        receipt = baker.make(
            Receipt,
            payment_type=self.prepayment,
            status_code=receipt_status.PAYMENT_SUCCESS,
            transaction=txn,
        )
        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            PaymentRow,
            pnref=pnref,
            payment_type=self.prepayment,
            status=receipt_status.PAYMENT_SUCCESS,
            amount=1,
            receipt=receipt,
            settled=False,
        )
        pr2 = baker.make(
            PaymentRow,
            pnref=pnref2,
            payment_type=self.pba,
            status=receipt_status.PAYMENT_SUCCESS,
            amount=2,
            receipt=receipt,
            settled=False,
        )
        baker.make(
            Capture,
            reference=pnref2,
            oper_result=oper_result.SUCCESS,
        )
        result = AdyenTxnsReportView.get_data(self.today, self.today)

        assert len(result) == 2
        assert result[0]['date'] == pr0.created
        assert result[0]['business_id'] == self.business.id
        assert result[0]['pr_id'] == pr0.id
        assert result[0]['amount'] == Decimal('1')
        assert result[0]['fixed_fee'] == Decimal('0.49')
        assert result[0]['pct_fee'] == Decimal('0.02')
        assert result[0]['total_fee'] == Decimal('0.51')
        assert result[0]['payout'] == Decimal('0.49')

        assert result[1]['date'] == pr2.created
        assert result[1]['business_id'] == self.business.id
        assert result[1]['pr_id'] == pr2.id
        assert result[1]['amount'] == Decimal('2')
        assert result[1]['fixed_fee'] == Decimal('0.49')
        assert result[1]['pct_fee'] == Decimal('0.04')
        assert result[1]['total_fee'] == Decimal('0.53')
        assert result[1]['payout'] == Decimal('1.47')


@pytest.mark.django_db
@override_settings(API_COUNTRY=Country.PL)
def test_txns_report_get_report():
    ystrdy = (tznow() - timedelta(days=1)).date()
    today = tznow().date()
    dt1 = datetime(2016, 9, 21, 9, 16, 50)
    dt2 = datetime(2016, 9, 22, 9, 16, 51)
    dt3 = datetime(2016, 9, 23, 9, 16, 52)
    data = [
        {
            'payout': 97.62,
            'amount': 100.0,
            'pr_id': 1,
            'total_fee': 2.38,
            'fixed_fee': 0.49,
            'date': dt1,
            'business_name': 'Zażółć gęślą jaźń',
            'business_id': 1,
            'pct_fee': 1.89,
        },
        {
            'payout': 197.62,
            'amount': 200.0,
            'pr_id': 2,
            'total_fee': 3.38,
            'fixed_fee': 0.49,
            'date': dt2,
            'business_name': 'fgh',
            'business_id': 1,
            'pct_fee': 2.89,
        },
        {
            'payout': 297.62,
            'amount': 300.0,
            'pr_id': 9,
            'total_fee': 4.38,
            'fixed_fee': 0.39,
            'date': dt3,
            'business_name': 'zxc',
            'business_id': 2,
            'pct_fee': 3.89,
        },
    ]

    myfile, mime, filename = AdyenTxnsReportView.get_report(data, ystrdy, today)

    assert mime == consts.XLS_MIME
    assert filename == f'adyen_transactions_report_{ystrdy}_{today}.xlsx'

    workbook = load_workbook(myfile)
    assert len(workbook.worksheets) == 1

    rows = list(workbook.worksheets[0].rows)
    assert rows[0][0].value == 'date'
    assert rows[0][1].value == 'business name'
    assert rows[0][2].value == 'business id'
    assert rows[0][3].value == 'transaction id'
    assert rows[0][4].value == 'transaction amount'
    assert rows[0][5].value == 'transaction fixed fee'
    assert rows[0][6].value == 'transaction % fee'
    assert rows[0][7].value == 'total fee'
    assert rows[0][8].value == 'payout amount'

    assert rows[1][0].value == dt1.strftime('%Y-%m-%d')
    assert rows[1][1].value == 'Zażółć gęślą jaźń'
    assert rows[1][2].value == 1
    assert rows[1][3].value == 1
    assert rows[1][4].value == 100
    assert rows[1][5].value == 0.49
    assert rows[1][6].value == 1.89
    assert rows[1][7].value == 2.38
    assert rows[1][8].value == 97.62

    assert rows[2][0].value == dt2.strftime('%Y-%m-%d')
    assert rows[2][1].value == 'fgh'
    assert rows[2][2].value == 1
    assert rows[2][3].value == 2
    assert rows[2][4].value == 200
    assert rows[2][5].value == 0.49
    assert rows[2][6].value == 2.89
    assert rows[2][7].value == 3.38
    assert rows[2][8].value == 197.62

    assert rows[3][0].value == dt3.strftime('%Y-%m-%d')
    assert rows[3][1].value == 'zxc'
    assert rows[3][2].value == 2
    assert rows[3][3].value == 9
    assert rows[3][4].value == 300
    assert rows[3][5].value == 0.39
    assert rows[3][6].value == 3.89
    assert rows[3][7].value == 4.38
    assert rows[3][8].value == 297.62
