#!/usr/bin/env python
# it's better to keep readable SQL queries rather than 80 characters line long
# pylint: disable-all
"""
How to add new raport on production without deploy:
from webapps.admin_extra.quick_reports import SafeSerialize
prefix = 'https://booksy.com/admin/pl/quick_reports_2?new_report='
print prefix + SafeSerialize.dumps(a)
"""
from django.conf import settings

from webapps.business.models import Business
from webapps.notification.models import NotificationHistory


def _get_business_status_whens():
    whens = u' '.join(
        [u"WHEN '{}' THEN '{}' ".format(k, v.upper()) for k, v in Business.Status.choices()]
    )
    whens += u"ELSE '???'"
    return whens


def _get_sms_per_business_query(period_type, months_back):
    date_format = 'YYYY-MM-DD' if period_type in ['week', 'day'] else 'YYYY-MM'

    query = """
        WITH notifications AS (
          SELECT
            to_char(
              date_trunc('{period_type}', notification_notificationhistory.created),
              '{date_format}'
            ) AS date,
            business_id,
            sender,
            count(*) AS total_count,
            sum(sms_count) AS total_sms
          FROM notification_notificationhistory
          WHERE
            business_id IS NOT NULL
            AND notification_notificationhistory.created >=
                date_trunc('month', now() - interval '{months_back} {period_type}')
            AND business_id IN (
              SELECT business_id from business_business
            )
          GROUP BY 1, 2, 3
          ORDER BY 1, 2, 3
        )
        SELECT
          rows.business_id,
          rows.date,
          rows.kind,
          sum(rows.value)::INTEGER
        FROM (
          SELECT
            notifications.date,
            notifications.business_id,
            'all_notifications' AS kind,
            notifications.total_count AS value
          FROM notifications
          UNION ALL
          SELECT
            notifications.date,
            notifications.business_id,
            'all_sms' AS kind,
            notifications.total_sms AS value
          FROM notifications
          UNION ALL

          SELECT
            notifications.date,
            notifications.business_id,
            'system_notifications' AS kind,
            notifications.total_count AS value
          FROM notifications
          WHERE sender='{notif_his.SENDER_SYSTEM}'
          UNION ALL

          SELECT
            notifications.date,
            notifications.business_id,
            'system_sms' AS kind,
            notifications.total_sms AS value
          FROM notifications
          WHERE sender='{notif_his.SENDER_SYSTEM}'
          UNION ALL

          SELECT
            notifications.date,
            notifications.business_id,
            'marketing_notifications' AS kind,
            notifications.total_count AS value
          FROM notifications
          WHERE sender='{notif_his.SENDER_BUSINESS}'
          UNION ALL

          SELECT
            notifications.date,
            notifications.business_id,
            'marketing_sms' AS kind,
            notifications.total_sms AS value
          FROM notifications
          WHERE sender='{notif_his.SENDER_BUSINESS}'
        ) as rows
        GROUP BY 1, 2, 3
        ORDER BY 1, 2, 3;
    """.format(  # nosec
        notif_his=NotificationHistory,
        period_type=period_type,
        date_format=date_format,
        months_back=months_back,
    )
    return query


subscriptions_new_in_period_time_sql = """
            SELECT
              to_char(minis.min_start, 'YYYY-MM-DD HH24:MI:SS')  AS "Subscription date",
              ps.business_id AS "Biz id",
              '%(country_code)s' AS "Country Code",
              busines.name AS "Busines Name",
              product.name AS "Subscription plan",
              product.price_amount AS "Subscription price",
              product.currency_code AS "Subscriptions currency",
              (CASE ps.source
               WHEN 'U' THEN 'Unknown'  WHEN 'O' THEN 'Offline'  WHEN 'B' THEN 'Braintree'  WHEN 'I' THEN 'Apple iTunes'  WHEN 'P' THEN 'Google Play'
               END
              ) AS "Subscription source"
            FROM purchase_subscription AS ps
                JOIN (
                  SELECT
                    business_id,
                    min(start) AS min_start
                  FROM
                    purchase_subscription
                  WHERE
                    start::DATE >= %%(date_start)s::DATE AND start::DATE <= %%(date_end)s::DATE
                  GROUP BY business_id
                ) AS minis ON minis.business_id = ps.business_id
                JOIN purchase_subscriptionlisting AS product ON (ps.product_id = product.id)
                JOIN business_business AS busines ON (ps.business_id = busines.business_id)
            WHERE
              start=min_start AND (SELECT COUNT(*) FROM purchase_subscription WHERE purchase_subscription.business_id = ps.business_id ) = 1
            ORDER BY
              ps.business_id
         """ % {  # nosec
    'country_code': settings.API_COUNTRY
}

subscriptions_expired_in_period_time_sql = """
            SELECT
              to_char(ps.start, 'YYYY-MM-DD HH24:MI:SS') AS "Subscription date",
              ps.business_id AS "Biz id",
              '%(country_code)s' AS "Country Code",
              busines.name AS "Busines Name",
              product.name AS "Subscription plan",
              product.price_amount AS "Subscription price",
              product.currency_code AS "Subscriptions currency",
              (CASE ps.source
                WHEN 'U' THEN 'Unknown'
                WHEN 'O' THEN 'Offline'
                WHEN 'B' THEN 'Braintree'
                WHEN 'I' THEN 'Apple iTunes'
                WHEN 'P' THEN 'Google Play'
              END) AS "Subscription source",
              to_char(maxis.max_expiry, 'YYYY-MM-DD HH24:MI:SS') AS "Subscription expiration date",
              maxis.sub_count AS "Subscription count",
              (CASE another_sub.sub_count
                 WHEN another_sub.sub_count THEN 'Y' ELSE 'N'
               END) "Another active subscription Y/N",
              now()::date - maxis.max_expiry::date AS no_days,
              busines.status,
              busines.active
            FROM purchase_subscription AS ps
              JOIN (
                SELECT
                  business_id,
                  max(expiry) AS max_expiry,
                  COUNT(*) AS sub_count
                FROM
                  purchase_subscription
                WHERE expiry::DATE >= %%(date_start)s::DATE AND expiry::DATE <= %%(date_end)s::DATE
                GROUP BY business_id
              ) AS maxis ON maxis.business_id = ps.business_id
              JOIN purchase_subscriptionlisting AS product ON (ps.product_id = product.id)
              JOIN business_business AS busines ON (ps.business_id = busines.business_id)
              FULL JOIN (
                SELECT
                business_id,
                COUNT(*) AS sub_count
                FROM
                  purchase_subscription
                WHERE
                  expiry::DATE > now()::DATE
                GROUP BY business_id) AS another_sub ON another_sub.business_id = ps.business_id
            WHERE expiry=max_expiry
        """ % {  # nosec
    'country_code': settings.API_COUNTRY
}

business_with_registrationcode_sql = """
            SELECT
                business_business.business_id,
                business_business.name,
                to_char(business_business.created, 'YYYY-MM-DD HH24:MI:SS') AS created_utc,
                business_business.active,
                (
                    CASE business_business.status
                        %(status_whens)s
                    END
                ) AS status_name,
                registrationcode_registrationcode.code,
                registrationcode_registrationcode.description
            FROM
                business_business LEFT OUTER JOIN
                registrationcode_registrationcode ON (business_business.registration_code_id = registrationcode_registrationcode.code_id)
            ORDER BY 1
        """ % {  # nosec
    'status_whens': _get_business_status_whens()
}

monthly_bookings_sql = """
            WITH
                date_range AS (
                    SELECT
                        date_trunc('month', NOW() - INTERVAL '1 months') AS date_from,
                        date_trunc('month', NOW() - INTERVAL '0 months') AS date_till
                )
            SELECT
                b.booking_id AS booking_id,
                b.type AS booking_type,
                to_char(b.created, 'YYYY-MM-DD') AS booking_date,
                to_char(b.created, 'HH24:MI:SS') AS booking_time,
                to_char(b.booked_from, 'YYYY-MM-DD HH24:MI:SS') AS booking_booked_from,
                (
                    SELECT bs.name
                    FROM booking_bookingsources AS bs
                    WHERE b.source_id = bs.booking_source_id
                ) AS booking_source,
                (
                    SELECT servar.price::real
                    FROM business_servicevariant AS servar
                    WHERE servar.service_variant_id = b.service_variant_id
                ) AS booking_price,
                COALESCE(
                    (
                        SELECT (
                            CASE
                                WHEN servcat.name IS NOT NULL THEN servcat.name || ' :: ' || serv.name
                                ELSE serv.name
                            END
                            )
                        FROM
                            business_servicevariant AS servar
                            JOIN business_service AS serv on (serv.service_id = servar.service_id)
                            LEFT OUTER JOIN business_servicecategory AS servcat on (serv.service_category_id = servcat.service_category_id)
                        WHERE servar.service_variant_id = b.service_variant_id
                    ),
                    b.service_name
                ) AS booking_service,
                b.service_variant_id AS booking_service_variant_id,
                biz.business_id AS merchant_id,
                to_char(biz.created, 'YYYY-MM-DD HH24:MI:SS') AS merchant_created,
                biz.name AS merchant_name,
                (
                    CASE biz.status
                        %(status_whens)s
                    END
                ) AS merchant_status,
                b.booked_for_id AS customer_bci_id,
                (
                    SELECT bci.user_id
                    FROM business_businesscustomerinfo AS bci
                    WHERE bci.business_customer_info_id = b.booked_for_id
                ) AS customer_user_id,
                b.customer_name AS customer_name
            FROM
                date_range,
                booking_booking AS b
                JOIN business_business AS biz ON (b.business_id = biz.business_id)
            WHERE
                b.type in ('C', 'B')
                AND date_range.date_from <= b.created AND b.created < date_range.date_till
            ORDER BY b.created
            ;
        """ % {  # nosec
    'status_whens': _get_business_status_whens()
}

weekly_summary_bookings_sql = """
            WITH
                date_range AS (
                    SELECT
                        (now()::date - extract(dow FROM now())::integer - 6)::timestamp AS date_from,
                        (now()::date - extract(dow FROM now())::integer + 1)::timestamp AS date_till
                )
            SELECT
                b.booking_id AS booking_id,
                b.type AS booking_type,
                to_char(b.created, 'YYYY-MM-DD') AS booking_date,
                to_char(b.created, 'HH24:MI:SS') AS booking_time,
                to_char(b.booked_from, 'YYYY-MM-DD HH24:MI:SS') AS booking_booked_from,
                (
                    SELECT bs.name
                    FROM booking_bookingsources AS bs
                    WHERE b.source_id = bs.booking_source_id
                ) AS booking_source,
                (
                    SELECT servar.price::real
                    FROM business_servicevariant AS servar
                    WHERE servar.service_variant_id = b.service_variant_id
                ) AS booking_price,
                COALESCE(
                    (
                        SELECT (
                            CASE
                                WHEN servcat.name IS NOT NULL THEN servcat.name || ' :: ' || serv.name
                                ELSE serv.name
                            END
                            )
                        FROM
                            business_servicevariant AS servar
                            JOIN business_service AS serv on (serv.service_id = servar.service_id)
                            LEFT OUTER JOIN business_servicecategory AS servcat on (serv.service_category_id = servcat.service_category_id)
                        WHERE servar.service_variant_id = b.service_variant_id
                    ),
                    b.service_name
                ) AS booking_service,
                b.service_variant_id AS booking_service_variant_id,
                biz.business_id AS merchant_id,
                to_char(biz.created, 'YYYY-MM-DD HH24:MI:SS') AS merchant_created,
                biz.name AS merchant_name,
                (
                    CASE biz.status
                        %(status_whens)s
                    END
                ) AS merchant_status,
                b.booked_for_id AS customer_bci_id,
                (
                    SELECT bci.user_id
                    FROM business_businesscustomerinfo AS bci
                    WHERE bci.business_customer_info_id = b.booked_for_id
                ) AS customer_user_id,
                b.customer_name AS customer_name
            FROM
                date_range,
                booking_booking AS b
                JOIN business_business AS biz ON (b.business_id = biz.business_id)
            WHERE
                b.type in ('C', 'B')
                AND date_range.date_from <= b.created AND b.created < date_range.date_till
            ORDER BY b.created
            ;
        """ % {  # nosec
    'status_whens': _get_business_status_whens()
}


LIMITED_REPORTS = [
    {
        'label': 'customer_card_by_business',
        'sql': """
                        SELECT * FROM (
                          SELECT
                            business_businesscustomerinfo.business_customer_info_id as client_card_id,
                            trim(
                                CONCAT(
                                    COALESCE(NULLIF(business_businesscustomerinfo.first_name, ''), auth_user.first_name),
                                    ' ',
                                    COALESCE(NULLIF(business_businesscustomerinfo.last_name, ''), auth_user.last_name)
                                )
                            ) AS name,
                            COALESCE(NULLIF(business_businesscustomerinfo.cell_phone, ''), user_user.cell_phone) AS cell_phone,
                            COALESCE(NULLIF(business_businesscustomerinfo.email, ''), auth_user.email) AS email,
                            TRIM(
                                CONCAT(
                                    COALESCE(NULLIF(business_businesscustomerinfo.address_line_1, ''), ''),
                                    CASE
                                        WHEN business_businesscustomerinfo.address_line_1 IS NOT NULL AND business_businesscustomerinfo.address_line_1 != ''
                                        THEN ' '
                                        ELSE ''
                                    END,
                                    COALESCE(NULLIF(business_businesscustomerinfo.address_line_2, ''), ''),
                                    CASE
                                        WHEN (business_businesscustomerinfo.address_line_1 IS NOT NULL AND business_businesscustomerinfo.address_line_1 != '') 
                                            OR (business_businesscustomerinfo.address_line_2 IS NOT NULL AND business_businesscustomerinfo.address_line_2 != '') 
                                        THEN ' '
                                        ELSE ''
                                    END
                                )
                            ) AS address,
                            TRIM(
                                CONCAT(
                                    COALESCE(NULLIF(business_businesscustomerinfo.zipcode, ''), ''),
                                    ' ',
                                    COALESCE(NULLIF(business_businesscustomerinfo.city, ''), '')
                                )
                            ) AS city,
                            COALESCE(NULLIF(business_businesscustomerinfo.allergens, ''), '') AS allergens,
                            business_businesscustomerinfo.discount AS discount,
                            (
                              SELECT count(*) as booking_count
                              FROM booking_appointment
                              WHERE booking_appointment.booked_for_id =
                                    business_businesscustomerinfo.business_customer_info_id
                            ),
                            business_businesscustomerinfo.web_communication_agreement::integer AS web_communication_agreement,
                            business_businesscustomerinfo.processing_consent::integer AS processing_consent,
                            business_businesscustomerinfo.blacklisted::integer AS blacklisted,
                            business_businesscustomerinfo.business_secret_note AS business_secret_note
                          FROM
                            user_user JOIN
                            auth_user ON (user_user.user_ptr_id = auth_user.id) RIGHT OUTER JOIN
                            business_businesscustomerinfo ON (user_id = user_ptr_id)
                          WHERE
                            business_id = %(business_id)s
                            AND business_businesscustomerinfo.visible_in_business = TRUE
                        ) x
                        ORDER BY
                          NULLIF(x.name, '') ASC NULLS LAST
                    """,
        'filters': [
            {
                'type': 'text',
                'name': 'business_id',
            },
        ],
    },
    {
        'label': 'business_stafferlockedaccess',
        'sql': """
                        SELECT
                          bsla.created,
                          bci.user_id,
                          COALESCE(au.first_name, bci.first_name) AS first_name,
                          COALESCE(au.last_name, bci.last_name) AS last_name,
                          COALESCE(au.email, bci.email) AS email,
                          COALESCE(uu.cell_phone, bci.cell_phone) AS cell_phone
                        FROM business_stafferlockedaccess bsla
                          LEFT JOIN business_businesscustomerinfo bci
                            ON bsla.business_customer_info_id = bci.business_customer_info_id
                          LEFT JOIN user_user uu
                            ON uu.user_ptr_id = bci.user_id
                          LEFT JOIN auth_user au
                            ON au.id = uu.user_ptr_id
                        WHERE bsla.business_id = %(business_id)s
                          AND bsla.staffer_id = %(staffer_id)s
                    """,
        'filters': [
            {
                'type': 'text',
                'name': 'business_id',
            },
            {
                'type': 'text',
                'name': 'staffer_id',
            },
        ],
    },
]


REPORTS = [
    {
        'label': 'notifications_by_business',
        'sql': """
            SELECT
                to_char(created, 'YYYY-MM-DD HH24:MI:SS') AS created_utc,
                sender,
                type,
                booking_id,
                task_id,
                title
            FROM notification_notificationhistory
            WHERE business_id = %(business_id)s
        """,
        'filters': [
            {
                'type': 'text',
                'name': 'business_id',
            },
        ],
        'hidden': True,
    },
    {
        'label': 'customer_card_EX_by_business',
        'sql': """
            SELECT * FROM (
              SELECT
                user_id,
                trim(
                    concat(
                        COALESCE(NULLIF(business_businesscustomerinfo.first_name, ''), auth_user.first_name),
                        ' ',
                        COALESCE(NULLIF(business_businesscustomerinfo.last_name, ''), auth_user.last_name)
                    )
                ) AS name,
                COALESCE(NULLIF(business_businesscustomerinfo.cell_phone, ''), user_user.cell_phone) AS cell_phone,
                COALESCE(NULLIF(business_businesscustomerinfo.email, ''), auth_user.email) AS email,
                (
                    SELECT count(*) AS cnt_all
                    FROM booking_booking
                    WHERE booking_booking.booked_for_id = business_businesscustomerinfo.business_customer_info_id
                ),
                (
                    SELECT count(*) AS cnt_business
                    FROM booking_booking
                    WHERE
                        booking_booking.booked_for_id = business_businesscustomerinfo.business_customer_info_id
                        AND type = 'B'
                ),
                (
                    SELECT count(*) AS cnt_customer
                    FROM booking_booking
                    WHERE
                        booking_booking.booked_for_id = business_businesscustomerinfo.business_customer_info_id
                        AND type = 'C'
                ),
                (
                    SELECT count(*) AS cnt_finished
                    FROM booking_booking
                    WHERE
                        booking_booking.booked_for_id = business_businesscustomerinfo.business_customer_info_id
                        AND status = 'F'
                ),
                (
                    SELECT count(*) AS cnt_accepted
                    FROM booking_booking
                    WHERE
                        booking_booking.booked_for_id = business_businesscustomerinfo.business_customer_info_id
                        AND status = 'A'
                ),
                (
                    SELECT count(*) AS cnt_canceled
                    FROM booking_booking
                    WHERE
                        booking_booking.booked_for_id = business_businesscustomerinfo.business_customer_info_id
                        AND status = 'C'
                ),
                (
                    SELECT count(*) AS cnt_noshow
                    FROM booking_booking
                    WHERE
                    booking_booking.booked_for_id = business_businesscustomerinfo.business_customer_info_id
                    AND status = 'N'
                ),
                (
                    SELECT sum(price) AS total_price
                    FROM
                        booking_booking JOIN
                        business_servicevariant ON (booking_booking.service_variant_id = business_servicevariant.service_variant_id)
                    WHERE
                        booking_booking.booked_for_id = business_businesscustomerinfo.business_customer_info_id
                        AND status = 'F'
                ),
                business_businesscustomerinfo.web_communication_agreement::integer AS web_communication_agreement
                FROM
                    user_user JOIN
                    auth_user ON (user_user.user_ptr_id = auth_user.id) RIGHT OUTER JOIN
                    business_businesscustomerinfo ON (user_id = user_ptr_id)
                WHERE
                    business_id = %(business_id)s
            ) x
            ORDER BY
                NULLIF(x.name, '') ASC NULLS LAST
        """,
        'filters': [
            {
                'type': 'text',
                'name': 'business_id',
            },
        ],
        'hidden': True,
    },
    {
        'label': 'business_data',
        'sql': "SELECT business_id, to_char(created, 'YYYY-MM-DD HH24:MI:SS') AS created_utc, active, sms_priority, sms_limit, name FROM business_business ORDER BY 1",
        'hidden': True,
    },
    {
        'label': 'business_booking_counts',
        'sql': """
            SELECT
                business_id,
                to_char(created, 'YYYY-MM-DD HH24:MI:SS') AS created_utc,
                (
                    SELECT count(*) AS cnt_customer
                    FROM booking_booking
                    WHERE
                        booking_booking.business_id = business_business.business_id
                        AND type = 'C'
                ),
                (
                    SELECT count(*) AS cnt_business
                    FROM booking_booking
                    WHERE
                        booking_booking.business_id = business_business.business_id
                        AND type = 'B'
                ),
                name
            FROM business_business
            WHERE active = true
            ORDER BY 1
        """,
        'hidden': True,
    },
    {
        'ng': True,
        'label': 'business_data_full',
        'sql': """
            SELECT
                business_id,
                to_char(created, 'YYYY-MM-DD HH24:MI:SS') AS created_utc,
                name,
                active,
                ( {{ models.business.Business.Status|enum_cases('status') }} ) AS status,
                EXISTS (
                    SELECT 1
                    FROM purchase_subscription AS subs
                    WHERE
                        subs.business_id = business_business.business_id
                ) AS has_subscription,
                (
                    WITH RECURSIVE temptab(parent) AS (
                        SELECT root.region_id AS parent
                        FROM structure_regiongraph AS root
                        WHERE root.related_region_id = (
                           SELECT region_id FROM business_business_regions AS biz_reg WHERE biz_reg.business_id = business_business.business_id LIMIT 1
                        )
                    UNION ALL
                        SELECT s.region_id AS parent
                        FROM structure_regiongraph AS s JOIN temptab AS t ON (s.related_region_id = t.parent)
                    )
                    SELECT string_agg(DISTINCT structure_region.name, ', ')
                    FROM temptab JOIN structure_region ON (temptab.parent = structure_region.region_id)
                    WHERE structure_region.type IN ('city', 'state')
                    LIMIT 1
                ) AS city_structure,
                city AS city_user,
                (to_char(latitude, '999.99999') || ' ' || to_char(longitude, '999.99999')) AS lat_lng,
                (
                    SELECT
                        string_agg(cat.internal_name, ', ') AS category
                    FROM
                        business_business_categories AS biz_cat JOIN
                        business_businesscategory AS cat ON (biz_cat.businesscategory_id = cat.category_id)
                    WHERE
                        biz_cat.business_id = business_business.business_id
                ) AS categories,
                (
                    SELECT
                        cat.internal_name
                    FROM
                        business_business AS biz JOIN
                        business_businesscategory AS cat ON (biz.primary_category_id = cat.category_id)
                    WHERE
                        biz.business_id = business_business.business_id
                ) AS primary_category,

                (
                    SELECT app_type || name
                    FROM booking_bookingsources
                    WHERE booking_source_id = business_business.registration_source_id
                ) AS registration_source,
                website,
                facebook_link,
                instagram_link,
                (
                    SELECT count(*)::integer
                    FROM images_image
                    WHERE
                        business_id = business_business.business_id
                        AND active AND visible
                ) AS image_count,
                (
                    SELECT count(*)::integer
                    FROM reviews_review
                    WHERE business_id = business_business.business_id
                ) AS review_count,
                (
                    SELECT avg(rank)::float
                    FROM reviews_review
                    WHERE business_id = business_business.business_id
                ) AS review_avg

            FROM business_business
            ORDER BY 1
        """,
        'hidden': True,
    },
    {
        'label': 'business_data_status_active',
        'tags': ['for:MarcinBartoszek', "for:CountryManagers"],
        'help': "Simple status of all businesses in database. Includes Active/Inactive",
        'sql': """
            SELECT
                business_id,
                (
                    CASE status
                        WHEN 'S' THEN 'SETUP'  WHEN 'T' THEN 'TRIAL'  WHEN 'E' THEN 'TRIAL END'  WHEN 'F' THEN 'TRIAL BLOCKED'  WHEN 'P' THEN 'PAID'  WHEN 'O' THEN 'PAYMENT OVERDUE'  WHEN 'L' THEN 'BLOCKED PAYMENT OVERDUE'  WHEN 'B' THEN 'BLOCKED'  WHEN 'D' THEN 'DEMO'  WHEN 'M' THEN 'DEMO TEMPLATE' ELSE '???'
                    END
                ) AS business_status,
                (
                    CASE active
                        WHEN TRUE THEN 'Active'  WHEN FALSE THEN 'Inactive' ELSE '???'
                    END
                ) AS business_active
            FROM business_business
            ORDER BY 1
        """,
        'columns_spec': [
            'business_id',
            'business_status',
            'business_active',
        ],
        'hidden': True,
    },
    {
        'label': 'business_registration_source',
        'tags': ['for:MarcinBartoszek'],
        'help': "List of businesses with owner email and registration source",
        'sql': """
        SELECT
        "business_business"."business_id" AS business_id,
        "business_business"."name" AS business_name,
        "auth_user"."email" AS business_owner_email,
        "booking_bookingsources"."name" AS business_registration_source,
        (
        CASE "business_business"."status"
        WHEN 'S' THEN 'SETUP' 
        WHEN 'T' THEN 'TRIAL' 
        WHEN 'E' THEN 'TRIAL END' 
        WHEN 'F' THEN 'TRIAL BLOCKED' 
        WHEN 'P' THEN 'PAID' 
        WHEN 'O' THEN 'PAYMENT OVERDUE' 
        WHEN 'L' THEN 'BLOCKED PAYMENT OVERDUE' 
        WHEN 'B' THEN 'BLOCKED' 
        WHEN 'D' THEN 'DEMO' 
        WHEN 'M' THEN 'DEMO TEMPLATE' ELSE '???'
        END
        ) AS business_status,
        (
        CASE "business_business"."active"
        WHEN TRUE THEN 'Active' WHEN FALSE THEN 'Inactive' ELSE '???'
        END
        ) AS business_active,
        to_char("business_business"."created", 'YYYY-MM-DD HH24:MI:SS') AS business_created_utc,
        cat.internal_name as primary_category_name,
        (
        SELECT count(1) FROM business_resource br
        WHERE
        br.business_id = "business_business"."business_id" AND
        br.active=TRUE AND
        br.type='S' AND
        br.visible = TRUE
        ) as "staffers_active_visible_count",
        (
        SELECT count(1) FROM business_resource br
        WHERE
        br.business_id = "business_business"."business_id" AND
        br.active=TRUE AND
        br.type='S'
        ) as "staffers_active_count"
        FROM "business_business"
        INNER JOIN "user_user" ON ("business_business"."owner_id" = "user_user"."user_ptr_id")
        INNER JOIN "auth_user" ON ("user_user"."user_ptr_id" = "auth_user"."id")
        LEFT OUTER JOIN "booking_bookingsources" ON ("business_business"."registration_source_id" = "booking_bookingsources"."booking_source_id")
        LEFT OUTER JOIN business_businesscategory AS cat ON (cat.category_id = "business_business".primary_category_id)
        ORDER BY 1
        """,
        'columns_spec': [
            'business_id',
            'business_name',
            'business_owner_email',
            'business_registration_source',
            'business_status',
            'business_active',
            'business_created_utc',
            'primary_category_name',
            'staffers_active_visible_count',
            'staffers_active_count',
        ],
        'hidden': True,
    },
    {
        'label': 'subscriptions_new_in_period_time',
        'sql': subscriptions_new_in_period_time_sql,
        'filters': [
            {
                'type': 'text',
                'name': 'date_start',
                'help': "Start date. Date format: 'YYYY-MM-DD'",
            },
            {
                'type': 'text',
                'name': 'date_end',
                'help': "End date. Date format: 'YYYY-MM-DD'",
            },
        ],
    },
    {
        'label': 'subscriptions_expired_in_period_time',
        'sql': subscriptions_expired_in_period_time_sql,
        'filters': [
            {
                'type': 'text',
                'name': 'date_start',
                'help': "Start date. Date format: 'YYYY-MM-DD'",
            },
            {
                'type': 'text',
                'name': 'date_end',
                'help': "End date. Date format: 'YYYY-MM-DD'",
            },
        ],
        'hidden': True,
    },
    {
        'label': 'marketplace_bookings',
        'sql': """
            SELECT
                CASE
                    WHEN (bb.booking_id=bci.first_booking_id AND bci.client_type='CN')
                    THEN 'chargable' ELSE 'not chargable'
                END AS chargable,
                bb.booking_id,
                to_char(bb.created, 'YYYY-MM-DD') AS book_created,
                bci.user_id,
                bb.business_id,
                biz.name,
                biz.city,
                biz.status,
                bsv.price,
                bci.client_type,
                bci.first_booking_id
            FROM booking_booking AS bb
            LEFT JOIN business_businesscustomerinfo AS bci ON
            bci.business_customer_info_id=bb.booked_for_id
            LEFT JOIN business_business biz ON
            biz.business_id=bb.business_id
            LEFT JOIN business_servicevariant bsv ON
            bsv.service_variant_id = bb.service_variant_id
            WHERE
                bb.type = 'C' AND
                bb.created > %(date)s::DATE
            ORDER BY 2 DESC
        """,
        'filters': [
            {
                'type': 'text',
                'name': 'date',
                'help': "Date. Date format: 'YYYY-MM-DD'",
            },
        ],
        'hidden': True,
    },
    {
        'label': 'business_status_bookings',
        'sql': """
            SELECT
                biz.business_id,
                biz.status,
                biz.active,
                to_char(biz.created, 'YYYY-MM-DD') AS created,
                bk.type AS pivoted,
                COUNT(booking_id)
            FROM business_business AS biz
            LEFT JOIN booking_booking AS bk
                ON bk.business_id = biz.business_id
            WHERE bk.type IN ('C', 'B')
            GROUP BY 4, 2, 1, 5
            ORDER BY 6 DESC
        """,
        'pivot': True,
        'hidden': True,
    },
    {
        'label': 'paying_and_churned_merchants',
        'sql': """
            SELECT
                business_id,
                status,
                active
            FROM business_business
            WHERE status IN ('P', 'L', 'O', 'B')
            ORDER BY business_id
        """,
    },
    {
        'ng': True,
        'label': 'business_data_location',
        'tags': ['for:MarcinBartoszek'],
        'help': (
            "Business data with extensive location info.\n\n"
            "Bear in mind that city is calculated from region hierarchy and city_user has been entered by business owner."
        ),
        'sql': """
            SELECT
                business_id,
                to_char(created, 'YYYY-MM-DD HH24:MI:SS') AS created_utc,
                active,
                ( {{ models.business.Business.Status|enum_cases('status') }} ) AS status,
                name,
                address AS street_address,
                (
                    WITH RECURSIVE temptab(parent) AS (
                        SELECT root.region_id AS parent
                        FROM structure_regiongraph AS root
                        WHERE root.related_region_id = (
                           SELECT region_id FROM business_business_regions AS biz_reg WHERE biz_reg.business_id = business_business.business_id LIMIT 1
                        )
                    UNION ALL
                        SELECT s.region_id AS parent
                        FROM structure_regiongraph AS s JOIN temptab AS t ON (s.related_region_id = t.parent)
                    )
                    SELECT string_agg(DISTINCT structure_region.name, ', ')
                    FROM temptab JOIN structure_region ON (temptab.parent = structure_region.region_id)
                    WHERE structure_region.type = 'city'
                    LIMIT 1
                ) AS city,
                city AS city_user,
                (
                    WITH RECURSIVE temptab(parent) AS (
                        SELECT root.region_id AS parent
                        FROM structure_regiongraph AS root
                        WHERE root.related_region_id = (
                           SELECT region_id FROM business_business_regions AS biz_reg WHERE biz_reg.business_id = business_business.business_id LIMIT 1
                        )
                    UNION ALL
                        SELECT s.region_id AS parent
                        FROM structure_regiongraph AS s JOIN temptab AS t ON (s.related_region_id = t.parent)
                    )
                    SELECT string_agg(DISTINCT COALESCE(NULLIF(structure_region.abbrev,''), structure_region.name ,''), ', ')
                    FROM temptab JOIN structure_region ON (temptab.parent = structure_region.region_id)
                    WHERE structure_region.type = 'state'
                    LIMIT 1
                ) AS state,
                (
                    SELECT string_agg(structure_region.name, ', ')
                    FROM business_business_regions JOIN structure_region ON (business_business_regions.region_id = structure_region.region_id)
                    WHERE structure_region.type = 'zip' AND business_business_regions.business_id = business_business.business_id
                    LIMIT 1
                ) AS zipcode,
                phone,
                (to_char(latitude, '999.99999') || ' ' || to_char(longitude, '999.99999')) AS lat_lng
            FROM business_business
            ORDER BY 1
        """,
        'hidden': True,
    },
    {
        'ng': True,
        'label': 'business_with_owner',
        'sql': """
            SELECT
                business_business.business_id,
                business_business.name AS business_name,
                ( {{ models.business.Business.Status|enum_cases('status') }} ) AS business_status,
                auth_user.first_name AS owner_first_name,
                auth_user.last_name AS owner_last_name,
                auth_user.email AS owner_email,
                user_user.cell_phone AS owner_phone
            FROM "business_business"
                INNER JOIN user_user ON (business_business.owner_id = user_user.user_ptr_id)
                INNER JOIN auth_user ON (user_user.user_ptr_id = auth_user.id)
            ORDER BY 1
        """,
        'hidden': True,
    },
    {
        'label': 'business_city_per_month',
        'sql': """
            SELECT
                (
                    WITH RECURSIVE temptab(parent) AS (
                        SELECT root.region_id AS parent
                        FROM structure_regiongraph AS root
                        WHERE root.related_region_id = (
                           SELECT region_id FROM business_business_regions AS biz_reg WHERE biz_reg.business_id = business_business.business_id LIMIT 1
                        )
                    UNION ALL
                        SELECT s.region_id AS parent
                        FROM structure_regiongraph AS s JOIN temptab AS t ON (s.related_region_id = t.parent)
                    )
                    SELECT string_agg(structure_region.name, ', ')
                    FROM temptab JOIN structure_region ON (temptab.parent = structure_region.region_id)
                    WHERE structure_region.type = 'city'
                    LIMIT 1
                ) AS city_structure,
                to_char(created, 'YYYY-MM') AS month,
                count(*)
            FROM business_business
            GROUP BY 1,2
            ORDER BY 1,2
        """,
        'pivot': True,
        'hidden': True,
    },
    {
        'label': 'business_state_per_month',
        'sql': """
            SELECT
                (
                    WITH RECURSIVE temptab(parent) AS (
                        SELECT root.region_id AS parent
                        FROM structure_regiongraph AS root
                        WHERE root.related_region_id = (
                           SELECT region_id FROM business_business_regions AS biz_reg WHERE biz_reg.business_id = business_business.business_id LIMIT 1
                        )
                    UNION ALL
                        SELECT s.region_id AS parent
                        FROM structure_regiongraph AS s JOIN temptab AS t ON (s.related_region_id = t.parent)
                    )
                    SELECT string_agg(structure_region.name, ', ')
                    FROM temptab JOIN structure_region ON (temptab.parent = structure_region.region_id)
                    WHERE structure_region.type = 'state'
                    LIMIT 1
                ) AS city_structure,
                to_char(created, 'YYYY-MM') AS month,
                count(*)
            FROM business_business
            GROUP BY 1,2
            ORDER BY 1,2
        """,
        'pivot': True,
        'hidden': True,
    },
    {
        'label': 'business_with_subdomain',
        'sql': """
            SELECT
                business_business.business_id,
                business_business.name,
                auth_user.email AS owner_email,
                subdomain_subdomain.subdomain
            FROM
                business_business LEFT OUTER JOIN
                subdomain_subdomain ON (business_business.business_id = subdomain_subdomain.business_id) INNER JOIN
                user_user ON (business_business.owner_id = user_user.user_ptr_id) INNER JOIN
                auth_user ON (user_user.user_ptr_id = auth_user.id)
            WHERE
                business_business.active = true AND
                COALESCE(subdomain_subdomain.country_code, 'ZZ') IN (substring(current_database() from '..$'), 'ZZ')
            ORDER BY 1
        """,
    },
    {
        'label': 'business_with_registrationcode',
        'sql': business_with_registrationcode_sql,
        'hidden': True,
    },
    {
        'label': 'bookings_per_business_per_type',
        'sql': "SELECT business_id, type, count(*) FROM booking_booking GROUP BY 1, 2 ORDER BY 1, 2",
        'pivot': True,
        'hidden': True,
    },
    {
        'label': 'bookings_per_business_per_month_per_type',
        'sql': """
            SELECT
                booking_booking.business_id,
                business_business.name AS business_name,
                to_char(booking_booking.created, 'YYYY-MM') AS booking_created,
                type AS pivoted,
                count(*)
            FROM booking_booking JOIN
                business_business
                    ON (booking_booking.business_id = business_business.business_id)
            WHERE type IN ('B', 'C')
            GROUP BY 1, 2, 3, 4
            ORDER BY 1, 2, 3, 4
        """,
        'pivot': True,
        'hidden': True,
    },
    {
        'ng': True,
        'label': 'bookings_per_business_per_period_per_type',
        'help': (
            "You may enter reasonable date_after, for example 2016-06-01 00:00. "
            "If date_after will be empty, then beginning of the previous month is used."
        ),
        'tags': ['for:MarcinBartoszek'],
        'sql': """
            SELECT
                booking_booking.business_id,
                business_business.name AS business_name,
                to_char(
                    date_trunc(
                        {{ params.period_type|sql_escape }},
                        booking_booking.created
                    ),
                    {{ params.date_format|sql_escape }}
                ) AS booking_created,
                type AS pivoted,
                count(*)
            FROM booking_booking JOIN
                business_business
                    ON (booking_booking.business_id = business_business.business_id)
            WHERE
                1 = 1
                {% if params.date_after %}
                    AND booking_booking.created >= {{ params.date_after|sql_escape }}::timestamp
                {% else %}
                    AND booking_booking.created >= date_trunc('month', NOW() - INTERVAL '1 months')
                {% endif %}
                {% if params.date_after %}
                    AND booking_booking.created < {{ params.date_before|sql_escape }}::timestamp
                {% endif %}
            GROUP BY 1, 2, 3, 4
            ORDER BY 1, 2, 3, 4
        """,
        'pivot': True,
        'filters': [
            {
                'type': 'text',
                'name': 'date_after',
                'help': "Count only bookings created after this date. "
                "Date format: 'YYYY-MM-DD' or 'YYYY-MM-DD hh:mm', like '2016-06-01 00:00'",
            },
            {
                'type': 'text',
                'name': 'date_before',
                'help': "Count only bookings created before this date. "
                "Format like in date_after. "
                "Date is not inclusive, so entering 2016-06-03 will "
                "result in bookings created not later than 2016-06-02 23:59:59 ",
            },
            {
                'type': 'select',
                'name': 'period',
                'value': 'month',
                'help': "How to group data",
                'options': [
                    {'name': 'day', 'vars': {'period_type': 'day', 'date_format': 'YYYY-MM-DD'}},
                    {'name': 'week', 'vars': {'period_type': 'week', 'date_format': 'YYYY-MM-DD'}},
                    {'name': 'month', 'vars': {'period_type': 'month', 'date_format': 'YYYY-MM'}},
                    {
                        'name': 'quarter',
                        'vars': {'period_type': 'quarter', 'date_format': 'YYYY" Q"Q'},
                    },
                    {'name': 'year', 'vars': {'period_type': 'year', 'date_format': 'YYYY'}},
                    {'name': 'all', 'vars': {'period_type': 'century', 'date_format': '"all"'}},
                ],
            },
        ],
        'hidden': True,
    },
    {
        'label': 'bookings_per_month',
        'sql': "SELECT to_char(created, 'YYYY-MM') AS created_month, type, count(*) FROM booking_booking WHERE type IN ('C', 'B') GROUP BY 1,2 ORDER BY 1,2",
        'pivot': True,
        'sum_x': 1,
        'sum_y': 1,
        'hidden': True,
    },
    {
        'label': 'bookings_per_day_localtime',
        'tags': ['for:MarcinBartoszek'],
        'sql': """
            SELECT
                to_char(date_trunc('day', timezone(bb.time_zone_name, booking.created)), 'YYYY-MM-DD') AS created_local,
                appointment.type,
                count(*)
            FROM
                booking_booking AS booking JOIN
                booking_appointment AS appointment ON appointment.appointment_id = booking.appointment_id
                business_business AS bb ON bb.business_id = appointment.business_id
            WHERE
                appointment.business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                AND
                date_trunc('day', now()) - '8 days'::INTERVAL <
                date_trunc('day', timezone(bb.time_zone_name, booking.created))
                AND
                date_trunc('day', timezone(bb.time_zone_name, booking.created)) <
                date_trunc('day', now()) - '1 days'::INTERVAL

            GROUP BY 1,2
            ORDER BY 1,2
        """,
        'pivot': True,
        'hidden': True,
    },
    {
        'label': 'bookings_creation_diff_histogram',
        'sql': """
            SELECT
                booked_from_month,
                CASE
                    WHEN age < 24 THEN 'h' || to_char(age, 'FM099')
                    ELSE 's' || to_char(age / 24, 'FM0999') END AS diff,
                count(*)
            FROM (
                    SELECT
                        to_char(booked_from, 'YYYY-MM') AS booked_from_month,
                        EXTRACT(EPOCH FROM age(booked_from, created)) / 3600 AS age
                    FROM booking_booking
                    WHERE type in ('C', 'B') AND booked_from >= created
                ) AS ages
            GROUP BY 1,2
            ORDER BY 1,2
        """,
        'pivot': True,
        'sum_x': 1,
        'sum_y': 1,
        'hidden': True,
    },
    {
        'label': 'customer_bookings_per_business_per_month',
        'sql': "SELECT business_id, to_char(created, 'YYYY-MM') AS month, count(*) FROM booking_booking WHERE type = 'C' GROUP BY 1, 2 ORDER BY 1, 2",
        'pivot': True,
        'hidden': True,
    },
    {
        'label': 'customer_bookings_per_business_per_hour',
        'sql': "SELECT business_id, to_char(created, 'HH24') AS created_hour_utc, count(*) FROM booking_booking WHERE type = 'C' GROUP BY 1, 2 ORDER BY 1, 2",
        'pivot': True,
        'hidden': True,
    },
    {
        'label': 'customer_bookings_per_business_per_weekday',
        'sql': """
            SELECT
                business_id,
                to_char(created, 'D') || (CASE
                    WHEN to_char(created, 'D') = '1' THEN 'sun'
                    WHEN to_char(created, 'D') = '2' THEN 'mon'
                    WHEN to_char(created, 'D') = '3' THEN 'tue'
                    WHEN to_char(created, 'D') = '4' THEN 'wed'
                    WHEN to_char(created, 'D') = '5' THEN 'thu'
                    WHEN to_char(created, 'D') = '6' THEN 'fri'
                    WHEN to_char(created, 'D') = '7' THEN 'sat'
                    ELSE '???'
                    END
                ) AS created_weekday,
                count(*)
            FROM booking_booking
            WHERE type = 'C'
            GROUP BY 1,2
            ORDER BY 1,2
        """,
        'pivot': True,
        'hidden': True,
    },
    {
        'label': 'sms_per_month',
        'sql': "SELECT to_char(created, 'YYYY-MM') AS month, sender, count(*) FROM notification_notificationhistory WHERE type = 'S' GROUP BY 1,2 ORDER BY 1,2",
        'pivot': True,
        'sum_x': 1,
        'sum_y': 1,
        'help': "Te statystyki z instancji PL należy wysyłać do Ewy (Ewa Kielkowska <<EMAIL>>) na początku każdego miesiąca",
    },
    {
        'label': 'sms_parts_per_business_per_month',
        'help': "USE THIS to bill Businesses (this are numbers which Business see in its SMS Managment Panel)",
        'sql': """
            SELECT
                nh.business_id,
                business_business.name,
                to_char(nh.created, 'YYYY-MM') AS month,
                sum(sms_count) AS cnt
            FROM notification_notificationhistory nh
                INNER JOIN business_business ON (nh.business_id = business_business.business_id)
            WHERE type = 'S' AND sender = 'B'
            GROUP BY 1,2,3 ORDER BY 1,3
        """,
        'pivot': True,
    },
    {
        'label': 'sms_parts_per_business_per_month_last_month',
        'help': "USE THIS to bill Businesses (this are numbers which Business see in its SMS Managment Panel)",
        'sql': """
            SELECT
                nh.business_id,
                business_business.name,
                to_char(nh.created, 'YYYY-MM') AS month,
                sum(sms_count) AS cnt
            FROM notification_notificationhistory nh
                INNER JOIN business_business ON (nh.business_id = business_business.business_id)
            WHERE
                type = 'S' AND
                sender = 'B' AND
                nh.created >= date_trunc('month', current_date - interval '1' month) AND
                nh.created < date_trunc('month', current_date)
            GROUP BY 1,2,3 ORDER BY 1,3
        """,
        'pivot': True,
    },
    {
        'label': 'sms_parts_per_business_per_month_v2',
        'help': "Summary of sms parts sent BUT ONLY for billing cycle counted in calendar months. Month 0001-01 corresponds to a trial period",
        'sql': """
            SELECT
                stats.business_id,
                business_business.name,
                to_char(stats.date, 'YYYY-MM') AS month,
                sum(stats.parts_count) AS parts_count 
            FROM
                notification_notificationsmsstatistics stats 
                INNER JOIN
                    business_business 
                    ON (stats.business_id = business_business.business_id) 
            GROUP BY 1,2,3 
            ORDER BY 1,3
    """,
        'pivot': True,
    },
    {
        'label': 'sms_parts_per_business_per_day_last_month_v2',
        'help': "Stats of sent sms per day from last month. Businesses that are still on a trial are not shown",
        'sql': """
            SELECT
                stats.business_id,
                business_business.name,
                to_char(stats.date, 'YYYY-MM-DD') as date,
                stats.parts_count AS cnt
            FROM
                notification_notificationsmsstatistics stats
                INNER JOIN
                    business_business
                    ON (stats.business_id = business_business.business_id)
            WHERE
                stats.date >= CURRENT_DATE - INTERVAL '1' MONTH
            ORDER BY
                1
    """,
        'pivot': True,
    },
    {
        'label': 'sms_listing',
        'sql': """
            SELECT
                to_char(created, 'YYYY-MM-DD HH24:MI:SS') AS created_utc,
                sender, business_id, booking_id,
                metadata::json->'to' AS phone,
                sms_count, title
            FROM notification_notificationhistory
            WHERE type = 'S'
            ORDER BY created
        """,
        'hidden': True,
    },
    {
        'label': 'notificationschedule_status_per_month',
        'sql': "SELECT to_char(scheduled, 'YYYY-MM') AS month, state, count(*) FROM notification_notificationschedule GROUP BY 1,2 ORDER BY 1,2",
        'pivot': True,
        'sum_x': 1,
        'sum_y': 1,
        'hidden': True,
    },
    {
        'label': 'notificationschedule_status_per_day',
        'sql': "SELECT to_char(scheduled, 'YYYY-MM-DD') AS day, state, count(*) FROM notification_notificationschedule GROUP BY 1,2 ORDER BY 1,2",
        'pivot': True,
        'sum_x': 1,
        'sum_y': 1,
        'hidden': True,
    },
    {
        'label': 'notificationhistory_per_taskid_per_day',
        'sql': r"""
            SELECT substring(task_id FROM '(\w+:\w+):'), to_char(created, 'YYYY-MM-DD') AS day, count(*)
            FROM notification_notificationhistory
            WHERE created >= date_trunc('day', now() - interval '10 days')
            GROUP BY 1,2 ORDER BY 1, 2
        """,
        'pivot': True,
        'sum_x': 1,
        'sum_y': 1,
        'hidden': True,
    },
    {
        'label': 'stafferlockedaccess',
        'sql': """
            SELECT
                to_char(created, 'YYYY-MM-DD HH24:MI:SS') AS created_utc,
                business_id,
                staffer_id,
                business_customer_info_id,
                field_name
            FROM business_stafferlockedaccess
            ORDER BY created DESC
        """,
    },
    {
        'label': 'staffer_count_per_business',
        'help': "Returns count of active (not the same as working today or something similar) staffers per business.",
        'sql': """
            SELECT
                business_business.business_id,
                business_business.name as business_name,
                count(*) AS staffer_count
            FROM
                business_resource INNER JOIN
                business_business
                    ON (business_resource.business_id = business_business.business_id)
            WHERE
                business_resource.type = 'S' AND
                business_resource.active = true
            GROUP BY 1 ORDER BY 1
        """,
        'hidden': True,
    },
    {
        'label': 'service_count_per_business',
        'help': "Returns count of services per business.",
        'sql': """
            SELECT
                business_business.business_id,
                to_char(business_business.created, 'YYYY-MM-DD HH24:MI:SS') AS business_created_utc,
                business_business.name as business_name,
                (
                    SELECT count(DISTINCT service_category_id)
                    FROM business_service
                    WHERE business_service.business_id = business_business.business_id
                ) AS service_category_count,
                (
                    SELECT count(*)
                    FROM business_service
                    WHERE business_service.business_id = business_business.business_id
                ) AS service_count,
                (
                    SELECT count(*)
                    FROM
                        business_servicevariant JOIN business_service
                            ON (business_service.service_id = business_servicevariant.service_id)
                    WHERE business_service.business_id = business_business.business_id
                ) AS service_variant_count
            FROM
                business_business
            WHERE
                business_business.active = true
            ORDER BY 1
        """,
        'hidden': True,
    },
    {
        'label': 'bonus',
        'tags': ["for:AgataMroczkowska"],
        'sql': """
            WITH booking_summary AS (
                SELECT business_id, to_char(created, 'YYYY-MM') AS created, CASE WHEN count(*) < 10 THEN 0 WHEN count(*) < 50 THEN 1 WHEN count(*) < 100 THEN 2 ELSE 3 END AS brace
                FROM booking_booking
                WHERE type = 'C'
                GROUP BY 1, 2
                ORDER BY 1, 2
            ), booking_summary_ranked AS (
                SELECT business_id, created, brace, rank() OVER (PARTITION BY business_id ORDER BY created DESC) AS brace_rank
                FROM booking_summary
            ), final_result AS (
                SELECT
                    business_id,
                    ((
                        SELECT booking_summary_ranked.brace FROM booking_summary_ranked WHERE brace_rank = 1 AND booking_summary_ranked.business_id = qx.business_id
                    ) - (
                        SELECT max(booking_summary_ranked.brace) FROM booking_summary_ranked WHERE brace_rank != 1 AND booking_summary_ranked.business_id = qx.business_id
                    )) AS diff
                FROM booking_summary_ranked AS qx
            )
            SELECT DISTINCT * FROM final_result WHERE diff > 0 ORDER BY business_id
        """,
        'hidden': True,
    },
    {
        'label': 'business_categories',
        'help': (
            "Returns count of active businesses per category.\n\n"
            "<ul>"
            "<li>in rows with <tt>type=all</tt> one business can be counted multiple times, depending on how many categories it belongs</li>"
            "<li>in rows with <tt>type=primary</tt> one business is counted maximum one time</li>"
            "</ul>"
            "Date is registration beginning time in UTC."
        ),
        'tags': ['for:AnnaPenar'],
        'sql': """
            WITH
                cat_all AS (
                    SELECT
                        to_char(
                            date_trunc(%(period_type)s, biz.created),
                            %(date_format)s
                        ) AS created,
                        'all' AS type,
                        cat.internal_name AS pivoted,
                        count(*) AS cnt
                    FROM
                        business_business AS biz JOIN
                        business_business_categories AS biz_cat ON (biz.business_id = biz_cat.business_id) JOIN
                        business_businesscategory AS cat ON (biz_cat.businesscategory_id = cat.category_id)
                    WHERE status != 'S'
                    GROUP BY 1,2,3
                ),
                cat_primary AS (
                    SELECT
                        to_char(
                            date_trunc(%(period_type)s, biz.created),
                            %(date_format)s
                        ) AS created,
                        'primary' AS type,
                        cat.internal_name AS pivoted,
                        count(*) AS cnt
                    FROM
                        business_business AS biz JOIN
                        business_businesscategory AS cat ON (biz.primary_category_id = cat.category_id)
                    WHERE status != 'S'
                    GROUP BY 1,2,3
                )
            SELECT
                rows.created,
                rows.type,
                rows.pivoted,
                sum(rows.cnt)::integer
            FROM (
                SELECT * FROM cat_all
                UNION ALL
                SELECT * FROM cat_primary
            ) AS rows
            GROUP BY 1, 2, 3
            ORDER BY 1, 2, 3
        """,
        'pivot': True,
        'freeze_panes': 'C2',
        'filters': [
            {
                'type': 'select',
                'name': 'period',
                'value': 'day',
                'help': "How to group data",
                'options': [
                    {'name': 'day', 'vars': {'period_type': 'day', 'date_format': 'YYYY-MM-DD'}},
                    {'name': 'week', 'vars': {'period_type': 'week', 'date_format': 'YYYY-MM-DD'}},
                    {'name': 'month', 'vars': {'period_type': 'month', 'date_format': 'YYYY-MM'}},
                    {'name': 'year', 'vars': {'period_type': 'year', 'date_format': 'YYYY'}},
                ],
            }
        ],
        'hidden': True,
    },
    # Ticket 24044 - paying businesses percentage per category per month
    {
        'label': 'business_categories_per_month_paying_percent',
        'help': (
            'Returns percent of paying businesses per category per month. '
            'One business can be counted multiple times, depending on how many '
            'categories it belongs. '
            'Date is registration beginning time in UTC.'
        ),
        'sql': """
            WITH booking_summary_paid AS (
                SELECT
                    to_char(biz.created, 'YYYY-MM') AS created_month,
                    cat.name AS category,
                    count(*) AS cnt
                FROM
                    business_business AS biz JOIN
                    business_business_categories AS biz_cat ON (biz.business_id = biz_cat.business_id) JOIN
                    business_businesscategory AS cat ON (biz_cat.businesscategory_id = cat.category_id)
                WHERE active = true AND status = 'P'
                GROUP BY 1,2
            ),
            booking_summary_total AS (
                SELECT
                    to_char(biz.created, 'YYYY-MM') AS created_month,
                    cat.name AS category,
                    count(*) AS cnt
                FROM
                    business_business AS biz JOIN
                    business_business_categories AS biz_cat ON (biz.business_id = biz_cat.business_id) JOIN
                    business_businesscategory AS cat ON (biz_cat.businesscategory_id = cat.category_id)
                WHERE active = true
                GROUP BY 1,2
            )
            SELECT
                sum_paid.created_month,
                sum_paid.category,
                round((100::float * sum_paid.cnt / sum_total.cnt)::numeric, 2)::float AS percent
            FROM
                booking_summary_paid AS sum_paid JOIN
                booking_summary_total AS sum_total ON (
                    sum_paid.created_month = sum_total.created_month AND
                    sum_paid.category = sum_total.category
                )
            ORDER BY 1,2

        """,
        'pivot': True,
        'hidden': True,
    },
    {
        'label': 'business_categories_per_month_paying_ratio',
        'help': (
            "Returns count of active businesses per category, splited into 2 columns: paying and remaining ones. "
            "One business can be counted multiple times, depending on how many categories it belongs. "
            "Date is registration beginning time in UTC."
        ),
        'sql': """
            SELECT
                to_char(biz.created, 'YYYY-MM') AS created_month,
                (
                    cat.name ||
                    CASE WHEN biz.status = 'P' THEN ' PAID' ELSE ' REST' END
                ) AS category_status,
                count(*) AS cnt
            FROM
                business_business AS biz JOIN
                business_business_categories AS biz_cat ON (biz.business_id = biz_cat.business_id) JOIN
                business_businesscategory AS cat ON (biz_cat.businesscategory_id = cat.category_id)
            WHERE active = true
            GROUP BY 1,2
            ORDER BY 1,2
        """,
        'pivot': True,
        'hidden': True,
    },
    {
        'label': 'sources_of_bookings_per_month',
        'sql': """
            SELECT
                to_char(booking_booking.created, 'YYYY-MM') AS month,
                booking_bookingsources.app_type || '_' || booking_bookingsources.name AS source,
                count(*)
            FROM
                booking_booking JOIN
                booking_bookingsources ON (booking_booking.source_id = booking_bookingsources.booking_source_id)
            WHERE
                type IN ('C', 'B')
            GROUP BY 1, 2
            ORDER BY 1, 2
        """,
        'pivot': True,
        'hidden': True,
    },
    {
        'label': 'sources_of_business_owners_per_month',
        'sql': """
            SELECT
                to_char(auth_user.date_joined, 'YYYY-MM') AS month,
                booking_bookingsources.app_type || '_' || booking_bookingsources.name AS source,
                count(*)
            FROM
                user_userprofile JOIN booking_bookingsources ON (user_userprofile.source_id = booking_bookingsources.booking_source_id) JOIN
                user_user ON (user_userprofile.user_id = user_user.user_ptr_id) JOIN
                auth_user ON (user_user.user_ptr_id = auth_user.id)
            WHERE
                profile_type = 'B'
            GROUP BY 1, 2
            ORDER BY 1, 2
        """,
        'pivot': True,
        'hidden': True,
    },
    {
        'label': 'customer_emails_unique',
        'sql': """
            SELECT
                auth_user.email AS email
            FROM
                user_userprofile JOIN
                user_user ON (user_userprofile.user_id = user_user.user_ptr_id) JOIN
                auth_user ON (user_user.user_ptr_id = auth_user.id)
            WHERE
                profile_type = 'C'
            GROUP BY 1
            ORDER BY 1
        """,
        'hidden': True,
    },
    {
        'label': 'business_emails_unique',
        'sql': """
            SELECT
                auth_user.email AS email
            FROM
                business_business JOIN
                user_user ON (business_business.owner_id = user_user.user_ptr_id) JOIN
                auth_user ON (user_user.user_ptr_id = auth_user.id)
            WHERE
                business_business.active = true
                AND business_business.status in ('R', 'T', 'E', 'P', 'O')
            GROUP BY 1
            ORDER BY 1
        """,
        'hidden': True,
    },
    {
        'label': 'weekly_summary_old',
        'help': "See ticket #24145",
        'sql': """
            WITH
            new_sign as (SELECT count(1) as count FROM auth_user as a JOIN user_userprofile as u ON u.user_id = a.id),
            new_sign_last_week as (SELECT count(1) as count FROM auth_user as a JOIN user_userprofile as u ON u.user_id = a.id WHERE a.date_joined > current_date - interval '7 days'),
            new_merchants as
            (
                SELECT count(1) as count
                FROM business_business biz
                LEFT JOIN business_resource br ON biz.business_id = br.business_id
                LEFT JOIN booking_bookingresource bbr USING (resource_id)
                LEFT JOIN booking_booking as b USING (booking_id)
                WHERE b.type::TEXT = 'C' AND
                b.created > current_date - interval '7 days'
            ),
            all_merchants as
            (
            SELECT count(1) as count
                FROM business_business biz
                LEFT JOIN business_resource br ON biz.business_id = br.business_id
                LEFT JOIN booking_bookingresource bbr USING (resource_id)
                LEFT JOIN booking_booking as b USING (booking_id)
            WHERE
                b.type::TEXT = 'C'
            ),
            new_bookings as (SELECT count(1) as count FROM booking_booking as b WHERE b.created > current_date - interval '7 days'),
            acc_customer_bookings as (SELECT count(1) as count FROM booking_booking as b WHERE b.type::TEXT = 'C'),
            acc_biz_bookings as (SELECT count(1) as count FROM booking_booking as b WHERE b.type::TEXT = 'B'),
            all_bookings as (SELECT count(1) as count FROM booking_booking),
            mobile_customer_bookings as (SELECT count(1) as count FROM booking_booking b JOIN booking_bookingsources bbs ON bbs.booking_source_id = b.source_id WHERE b.type::TEXT = 'C' AND (bbs.name::TEXT = 'iPhone' OR bbs.name::TEXT = 'Android') AND b.created > current_date - interval '7 days')
            SELECT
                ns.count as new_sign,
                lw.count as new_sign_last_week,
                me.count as new_merchants,
                ame.count as all_marchants,
                lb.count as new_bookings,
                acb.count as acc_customer_bookings,
                abb.count as acc_biz_bookings,
                ab.count as all_bookings,
                mcb.count as mobile_customer_bookings
            FROM
                new_sign ns,
                new_sign_last_week lw,
                new_merchants me,
                all_merchants ame,
                new_bookings lb,
                acc_customer_bookings acb,
                acc_biz_bookings abb,
                all_bookings ab,
                mobile_customer_bookings as mcb;
        """,
        'hidden': True,
    },
    {
        'label': 'weekly_summary',
        'help': "See ticket #24145",
        'tags': ["for:ReportsSender"],
        'sql': """
            WITH
                date_range AS (
                    SELECT
                        (now()::date - extract(dow FROM now())::integer - 6 - 0 * 7)::timestamp AS date_from,
                        (now()::date - extract(dow FROM now())::integer + 1 - 0 * 7)::timestamp AS date_till
                )
            SELECT
                (
                    to_char(date_range.date_from, 'YYYY-MM-DD')
                    || ' ' ||
                    to_char(extract(week FROM date_range.date_from), 'fm00')
                ) AS date,
                (
                    SELECT count(*)
                    FROM business_business
                    WHERE status != 'S'
                        AND date_range.date_from <= created AND created < date_range.date_till
                        AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                ) AS new_signup,
                (
                    SELECT count(*)
                    FROM business_business
                    WHERE active = true
                        AND created < date_range.date_till
                        AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                ) AS acc_signup,
                (
                    SELECT count(*)
                    FROM business_business
                    WHERE active = true
                        AND date_range.date_from <= paid_from AND paid_from < date_range.date_till
                        AND status = 'P'
                        AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                ) AS new_paying,
                (
                    SELECT count(*)
                    FROM business_business
                    WHERE active = true
                        AND paid_from < date_range.date_till
                        AND status = 'P'
                        AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                ) AS acc_paying,


                (
                    SELECT count(*)
                    FROM business_business
                    WHERE
                        active = true
                        AND date_range.date_from <= created AND created < date_range.date_till
                        AND registration_source_id = (
                            SELECT booking_source_id
                            FROM booking_bookingsources
                            WHERE name = 'Android' AND app_type = 'B'
                        )
                        AND status NOT IN ('D', 'M')
                ) AS new_biz_created_android,
                (
                    SELECT count(*)
                    FROM business_business
                    WHERE
                        active = true
                        AND created < date_range.date_till
                        AND registration_source_id = (
                            SELECT booking_source_id
                            FROM booking_bookingsources
                            WHERE name = 'Android' AND app_type = 'B'
                        )
                        AND status NOT IN ('D', 'M')
                ) AS acc_biz_created_android,
                (
                    SELECT count(*)
                    FROM business_business
                    WHERE
                        active = true
                        AND date_range.date_from <= created AND created < date_range.date_till
                        AND registration_source_id = (
                            SELECT booking_source_id
                            FROM booking_bookingsources
                            WHERE name = 'iPhone' AND app_type = 'B'
                        )
                        AND status NOT IN ('D', 'M')
                ) AS new_biz_created_iphone,
                (
                    SELECT count(*)
                    FROM business_business
                    WHERE
                        active = true
                        AND created < date_range.date_till
                        AND registration_source_id = (
                            SELECT booking_source_id
                            FROM booking_bookingsources
                            WHERE name = 'iPhone' AND app_type = 'B'
                        )
                        AND status NOT IN ('D', 'M')
                ) AS acc_biz_created_iphone,
                (
                    SELECT count(*)
                    FROM business_business
                    WHERE
                        active = true
                        AND date_range.date_from <= created AND created < date_range.date_till
                        AND registration_source_id = (
                            SELECT booking_source_id
                            FROM booking_bookingsources
                            WHERE name = 'Web' AND app_type = 'B'
                        )
                        AND status NOT IN ('D', 'M')
                ) AS new_biz_created_web,
                (
                    SELECT count(*)
                    FROM business_business
                    WHERE
                        active = true
                        AND created < date_range.date_till
                        AND registration_source_id = (
                            SELECT booking_source_id
                            FROM booking_bookingsources
                            WHERE name = 'Web' AND app_type = 'B'
                        )
                        AND status NOT IN ('D', 'M')
                ) AS acc_biz_created_web,


                (
                    SELECT count(*)
                    FROM (
                        SELECT 1 AS cnt
                        FROM booking_booking
                        WHERE
                            type = 'B'
                            AND created < date_range.date_till
                            AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                        GROUP BY business_id
                        HAVING count(*) >= 1
                    ) AS foo
                ) - (
                    SELECT count(*)
                    FROM (
                        SELECT 1 AS cnt
                        FROM booking_booking
                        WHERE
                            type = 'B'
                            AND created < date_range.date_from
                            AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                        GROUP BY business_id
                        HAVING count(*) >= 1
                    ) AS foo
                ) AS new_with_biz_booking_1plus,
                (
                    SELECT count(*)
                    FROM (
                        SELECT 1 AS cnt
                        FROM booking_booking
                        WHERE
                            type = 'B'
                            AND created < date_range.date_till
                            AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                        GROUP BY business_id
                        HAVING count(*) >= 1
                    ) AS foo
                ) AS acc_with_biz_booking_1plus,

                (
                    SELECT count(*)
                    FROM (
                        SELECT 1 AS cnt
                        FROM booking_booking
                        WHERE
                            type = 'C'
                            AND created < date_range.date_till
                            AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                        GROUP BY business_id
                        HAVING count(*) >= 1
                    ) AS foo
                ) - (
                    SELECT count(*)
                    FROM (
                        SELECT 1 AS cnt
                        FROM booking_booking
                        WHERE
                            type = 'C'
                            AND created < date_range.date_from
                            AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                        GROUP BY business_id
                        HAVING count(*) >= 1
                    ) AS foo
                ) AS new_with_cus_booking_1plus,
                (
                    SELECT count(*)
                    FROM (
                        SELECT 1 AS cnt
                        FROM booking_booking
                        WHERE
                            type = 'C'
                            AND created < date_range.date_till
                            AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                        GROUP BY business_id
                        HAVING count(*) >= 1
                    ) AS foo
                ) AS acc_with_cus_booking_1plus,

                (
                    SELECT count(*)
                    FROM booking_booking
                    WHERE type = 'C'
                        AND date_range.date_from <= created AND created < date_range.date_till
                        AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                ) AS new_booking_cus,
                (
                    SELECT count(*)
                    FROM booking_booking
                    WHERE type = 'C'
                        AND created < date_range.date_till
                        AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                ) AS acc_booking_cus,
                (
                    SELECT count(*)
                    FROM booking_booking
                    WHERE type = 'B'
                        AND date_range.date_from <= created AND created < date_range.date_till
                        AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                ) AS new_booking_biz,
                (
                    SELECT count(*)
                    FROM booking_booking
                    WHERE type = 'B'
                        AND created < date_range.date_till
                        AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                ) AS acc_booking_biz,
                (
                    SELECT count(*)
                    FROM booking_booking
                    WHERE type != 'C'
                        AND date_range.date_from <= created AND created < date_range.date_till
                        AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                ) AS new_booking_noncus,
                (
                    SELECT count(*)
                    FROM booking_booking
                    WHERE type != 'C'
                        AND created < date_range.date_till
                        AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                ) AS acc_booking_noncus,
                (
                    SELECT count(*)
                    FROM booking_booking
                    WHERE
                        type = 'C'
                        AND date_range.date_from <= created AND created < date_range.date_till
                        AND source_id = (
                            SELECT booking_source_id
                            FROM booking_bookingsources
                            WHERE name = 'Android' AND app_type = 'C'
                        )
                        AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                ) AS new_booking_cus_android,
                (
                    SELECT count(*)
                    FROM booking_booking
                    WHERE
                        type = 'C'
                        AND created < date_range.date_till
                        AND source_id = (
                            SELECT booking_source_id
                            FROM booking_bookingsources
                            WHERE name = 'Android' AND app_type = 'C'
                        )
                        AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                ) AS acc_booking_cus_android,
                (
                    SELECT count(*)
                    FROM booking_booking
                    WHERE
                        type = 'C'
                        AND date_range.date_from <= created AND created < date_range.date_till
                        AND source_id = (
                            SELECT booking_source_id
                            FROM booking_bookingsources
                            WHERE name = 'iPhone' AND app_type = 'C'
                        )
                        AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                ) AS new_booking_cus_iphone,
                (
                    SELECT count(*)
                    FROM booking_booking
                    WHERE
                        type = 'C'
                        AND created < date_range.date_till
                        AND source_id = (
                            SELECT booking_source_id
                            FROM booking_bookingsources
                            WHERE name = 'iPhone' AND app_type = 'C'
                        )
                        AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                ) AS acc_booking_cus_iphone
            FROM date_range;
        """,
        'hidden': True,
    },
    {
        'label': 'monthly_summary',
        'help': "See ticket #24145",
        'tags': ["for:ReportsSender"],
        'sql': """
            WITH
                date_range AS (
                    SELECT
                        date_trunc('month', NOW() - INTERVAL '1 months') AS date_from,
                        date_trunc('month', NOW() - INTERVAL '0 months') AS date_till
                )
            SELECT
                (
                    to_char(date_range.date_from, 'YYYY-MM-DD')
                ) AS date,
                (
                    SELECT count(*)
                    FROM business_business
                    WHERE active = true
                        AND date_range.date_from <= created AND created < date_range.date_till
                ) AS new_signup,
                (
                    SELECT count(*)
                    FROM business_business
                    WHERE active = true
                        AND created < date_range.date_till
                ) AS acc_signup,
                (
                    SELECT count(*)
                    FROM business_business
                    WHERE active = true
                        AND date_range.date_from <= paid_from AND paid_from < date_range.date_till
                        AND status = 'P'
                ) AS new_paying,
                (
                    SELECT count(*)
                    FROM business_business
                    WHERE active = true
                        AND paid_from < date_range.date_till
                        AND status = 'P'
                ) AS acc_paying,


                (
                    SELECT count(*)
                    FROM (
                        SELECT 1 AS cnt
                        FROM booking_booking
                        WHERE type = 'B' AND created < date_range.date_till
                        GROUP BY business_id
                        HAVING count(*) >= 1
                    ) AS foo
                ) - (
                    SELECT count(*)
                    FROM (
                        SELECT 1 AS cnt
                        FROM booking_booking
                        WHERE type = 'B' AND created < date_range.date_from
                        GROUP BY business_id
                        HAVING count(*) >= 1
                    ) AS foo
                ) AS new_with_biz_booking_1plus,
                (
                    SELECT count(*)
                    FROM (
                        SELECT 1 AS cnt
                        FROM booking_booking
                        WHERE type = 'B' AND created < date_range.date_till
                        GROUP BY business_id
                        HAVING count(*) >= 1
                    ) AS foo
                ) AS acc_with_biz_booking_1plus,

                (
                    SELECT count(*)
                    FROM (
                        SELECT 1 AS cnt
                        FROM booking_booking
                        WHERE type = 'C' AND created < date_range.date_till
                        GROUP BY business_id
                        HAVING count(*) >= 1
                    ) AS foo
                ) - (
                    SELECT count(*)
                    FROM (
                        SELECT 1 AS cnt
                        FROM booking_booking
                        WHERE type = 'C' AND created < date_range.date_from
                        GROUP BY business_id
                        HAVING count(*) >= 1
                    ) AS foo
                ) AS new_with_cus_booking_1plus,
                (
                    SELECT count(*)
                    FROM (
                        SELECT 1 AS cnt
                        FROM booking_booking
                        WHERE type = 'C' AND created < date_range.date_till
                        GROUP BY business_id
                        HAVING count(*) >= 1
                    ) AS foo
                ) AS acc_with_cus_booking_1plus,

                (
                    SELECT count(*)
                    FROM booking_booking
                    WHERE type = 'C'
                        AND date_range.date_from <= created AND created < date_range.date_till
                ) AS new_booking_cus,
                (
                    SELECT count(*)
                    FROM booking_booking
                    WHERE type = 'C'
                        AND created < date_range.date_till
                ) AS acc_booking_cus,
                (
                    SELECT count(*)
                    FROM booking_booking
                    WHERE type = 'B'
                        AND date_range.date_from <= created AND created < date_range.date_till
                ) AS new_booking_biz,
                (
                    SELECT count(*)
                    FROM booking_booking
                    WHERE type = 'B'
                        AND created < date_range.date_till
                ) AS acc_booking_biz,
                (
                    SELECT count(*)
                    FROM booking_booking
                    WHERE type != 'C'
                        AND date_range.date_from <= created AND created < date_range.date_till
                ) AS new_booking_noncus,
                (
                    SELECT count(*)
                    FROM booking_booking
                    WHERE type != 'C'
                        AND created < date_range.date_till
                ) AS acc_booking_noncus,
                (
                    SELECT count(*)
                    FROM booking_booking
                    WHERE
                        type = 'C'
                        AND date_range.date_from <= created AND created < date_range.date_till
                        AND source_id = (
                            SELECT booking_source_id
                            FROM booking_bookingsources
                            WHERE name = 'Android' AND app_type = 'C'
                        )
                ) AS new_booking_cus_android,
                (
                    SELECT count(*)
                    FROM booking_booking
                    WHERE
                        type = 'C'
                        AND created < date_range.date_till
                        AND source_id = (
                            SELECT booking_source_id
                            FROM booking_bookingsources
                            WHERE name = 'Android' AND app_type = 'C'
                        )
                ) AS acc_booking_cus_android,
                (
                    SELECT count(*)
                    FROM booking_booking
                    WHERE
                        type = 'C'
                        AND date_range.date_from <= created AND created < date_range.date_till
                        AND source_id = (
                            SELECT booking_source_id
                            FROM booking_bookingsources
                            WHERE name = 'iPhone' AND app_type = 'C'
                        )
                ) AS new_booking_cus_iphone,
                (
                    SELECT count(*)
                    FROM booking_booking
                    WHERE
                        type = 'C'
                        AND created < date_range.date_till
                        AND source_id = (
                            SELECT booking_source_id
                            FROM booking_bookingsources
                            WHERE name = 'iPhone' AND app_type = 'C'
                        )
                ) AS acc_booking_cus_iphone
            FROM date_range;
        """,
        'hidden': True,
    },
    {
        'label': 'monthly_bookings',
        'help': "See ticket #24144",
        'tags': ["for:ReportsSender"],
        'sql': monthly_bookings_sql,
        'hidden': True,
    },
    {
        'label': 'weekly_summary_bookings',
        'help': "See ticket #24144",
        'tags': ["for:ReportsSender"],
        'sql': weekly_summary_bookings_sql,
        'hidden': True,
    },
    {
        'label': 'zzz_customer_user_summary',
        'tags': ["for:ReportsSender"],
        'sql': """
            SELECT
                to_char(date_trunc('week', auth_user.date_joined), 'YYYY-MM-DD') AS created_week,
                count(*)
            FROM
                user_userprofile JOIN
                user_user ON (user_userprofile.user_id = user_user.user_ptr_id) JOIN
                auth_user ON (user_user.user_ptr_id = auth_user.id)
            WHERE
                profile_type = 'C'
            GROUP BY 1
            ORDER BY 1
        """,
        'hidden': True,
    },
    {
        'label': 'zzz_customer_user_summary_monthly',
        'tags': ["for:ReportsSender"],
        'sql': """
            SELECT
                to_char(date_trunc('month', auth_user.date_joined), 'YYYY-MM-DD') AS created_month,
                count(*)
            FROM
                user_userprofile JOIN
                user_user ON (user_userprofile.user_id = user_user.user_ptr_id) JOIN
                auth_user ON (user_user.user_ptr_id = auth_user.id)
            WHERE
                profile_type = 'C'
            GROUP BY 1
            ORDER BY 1
        """,
        'hidden': True,
    },
    {
        'label': 'zzz_paying_business_summary',
        'tags': ["for:ReportsSender"],
        'sql': """
            SELECT
                to_char(date_trunc('week', business_business.paid_from), 'YYYY-MM-DD') AS paid_from_week,
                count(*)
            FROM
                business_business
            WHERE
                business_business.active = true AND
                business_business.status = 'P'
            GROUP BY 1
            ORDER BY 1
        """,
        'hidden': True,
    },
    {
        'label': 'customer_bookings_per_weekday',
        'tags': ["for:ReportsSender"],
        'freeze_panes': 'B2',
        'sql': """
            SELECT * FROM (
                SELECT
                    to_char(date_trunc('week', created), 'YYYY-MM-DD') AS created_week,
                    (CASE
                        WHEN to_char(created, 'D') = '1' THEN '7sun'
                        WHEN to_char(created, 'D') = '2' THEN '1mon'
                        WHEN to_char(created, 'D') = '3' THEN '2tue'
                        WHEN to_char(created, 'D') = '4' THEN '3wed'
                        WHEN to_char(created, 'D') = '5' THEN '4thu'
                        WHEN to_char(created, 'D') = '6' THEN '5fri'
                        WHEN to_char(created, 'D') = '7' THEN '6sat'
                        ELSE '???'
                        END
                    ) AS pivoted,
                    count(*)
                FROM booking_booking
                WHERE
                    type = 'C'
                    AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                GROUP BY 1,2
                ORDER BY 1,2
            ) AS real_data
        """,
        'pivot': True,
        'columns_spec': [
            'created_week',
            '1mon=>Monday',
            '2tue=>Tuesday',
            '3wed=>Wednesday',
            '4thu=>Thursday',
            '5fri=>Friday',
            '6sat=>Saturday',
            '7sun=>Sunday',
        ],
        'hidden': True,
    },
    {
        'label': 'customer_bookings_per_weekday_localtime',
        'tags': ["for:ReportsSender"],
        'freeze_panes': 'B2',
        'sql': """
            SELECT
                to_char(date_trunc('week', timezone(bb.time_zone_name, booking.created)), 'YYYY-MM-DD') AS created_week,
                (CASE to_char(timezone(bb.time_zone_name, booking.created), 'D')
                    WHEN '1' THEN '7sun'
                    WHEN '2' THEN '1mon'
                    WHEN '3' THEN '2tue'
                    WHEN '4' THEN '3wed'
                    WHEN '5' THEN '4thu'
                    WHEN '6' THEN '5fri'
                    WHEN '7' THEN '6sat'
                    ELSE '???'
                    END
                ) AS pivoted,
                count(*)
            FROM
                booking_booking AS booking JOIN
                booking_appointment AS appointment ON bb.business_id = appointment.business_id
                business_business AS bb ON bb.business_id = appointment.business_id
            WHERE
                appointment.type = 'C'
                AND appointment.business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
            GROUP BY 1,2
            ORDER BY 1,2
        """,
        'pivot': True,
        'columns_spec': [
            'created_week',
            '1mon=>Monday',
            '2tue=>Tuesday',
            '3wed=>Wednesday',
            '4thu=>Thursday',
            '5fri=>Friday',
            '6sat=>Saturday',
            '7sun=>Sunday',
        ],
        'hidden': True,
    },
    {
        'label': 'customer_bookings_sameday_localtime',
        'sql': """
            SELECT
                to_char(date_trunc('week', timezone(bb.time_zone_name, booking.created)), 'YYYY-MM-DD') AS created_week,
                (
                    CASE
                    to_char(timezone(bb.time_zone_name, booking.created), 'YYYY-MM-DD') = to_char(timezone(bb.time_zone_name, booking.booked_from), 'YYYY-MM-DD')
                    WHEN 't' THEN 'sameday'
                    WHEN 'f' THEN 'otherday'
                    ELSE '???'
                    END
                ) AS pivoted,
                count(*)
            FROM
                booking_booking AS booking JOIN
                booking_appointment AS appointment ON bb.business_id = appointment.business_id
                business_business AS bb ON bb.business_id = appointment.business_id
            WHERE
                appointment.type = 'C'
                AND appointment.business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
            GROUP BY 1,2
            ORDER BY 1,2
        """,
        'pivot': True,
        'hidden': True,
    },
    {
        'label': 'customer_bookings_sameday_localtime',
        'sql': """
            SELECT
                to_char(date_trunc('week', timezone(bb.time_zone_name, booking.created)), 'YYYY-MM-DD') AS created_week,
                (
                    CASE
                    WHEN (
                        date_trunc('day', timezone(bb.time_zone_name, booking.booked_from)) -
                        date_trunc('day', timezone(bb.time_zone_name, booking.created))
                        ) < '7 days'::interval
                        THEN
                            'days_' || to_char((
                                date_trunc('day', timezone(bb.time_zone_name, booking.booked_from)) -
                                date_trunc('day', timezone(bb.time_zone_name, booking.created))
                                ), 'DD'
                            )
                    ELSE 'week_plus'
                    END
                ) AS pivoted,
                count(*)
            FROM
                booking_booking AS booking JOIN
                booking_appointment AS appointment ON bb.business_id = appointment.business_id
                business_business AS bb ON bb.business_id = appointment.business_id
            WHERE
                appointment.type = 'C'
                AND appointment.business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
            GROUP BY 1,2
            ORDER BY 1,2
        """,
        'pivot': True,
        'hidden': True,
    },
    #######################################################################################################################
    #######################################################################################################################
    #######################################################################################################################
    #######################################################################################################################
    #######################################################################################################################
    #######################################################################################################################
    {
        'label': 'business_categories_per_month_paying_ratio',
        'help': (
            "Returns count of active businesses per category, splited into 2 columns: paying and remaining ones. "
            "One business can be counted multiple times, depending on how many categories it belongs. "
            "Date is registration beginning time in UTC."
        ),
        'sql': """
            WITH
                first_biz AS (
                    SELECT business_id, min(created) AS first
                    FROM booking_appointment
                    WHERE type = 'B'
                    GROUP BY 1
                )
            SELECT
                to_char(biz.created, 'YYYY-MM') AS created_month,
                (
                    cat.name ||
                    CASE
                        WHEN (SELECT first FROM first_biz WHERE first_biz.business_id = biz.business_id) < (biz.created + interval '1 days') THEN '__biz_1day'
                        ELSE '__biz_no1day'
                    END
                ) AS category_status,
                count(*) AS cnt
            FROM
                business_business AS biz JOIN
                business_business_categories AS biz_cat ON (biz.business_id = biz_cat.business_id) JOIN
                business_businesscategory AS cat ON (biz_cat.businesscategory_id = cat.category_id)
            WHERE active = true
            GROUP BY 1,2
            ORDER BY 1,2
        """,
        'pivot': True,
        'hidden': True,
    },
    {
        'label': 'business_categories_per_month_paying_ratio',
        'help': (
            "Returns count of active businesses per category, splited into 2 columns: paying and remaining ones. "
            "One business can be counted multiple times, depending on how many categories it belongs. "
            "Date is registration beginning time in UTC."
        ),
        'sql': """
            WITH
                first_biz AS (
                    SELECT business_id, min(created) AS first
                    FROM booking_appointment
                    WHERE type = 'B'
                    GROUP BY 1
                )
            SELECT
                to_char(biz.created, 'YYYY-MM') AS created_month,
                (
                    cat.name ||
                    CASE
                        WHEN (SELECT first FROM first_biz WHERE first_biz.business_id = biz.business_id) < (biz.created + interval '1 days') THEN '__biz_1day'
                        ELSE '__biz_no1day'
                    END
                ) AS category_status,
                count(*) AS cnt
            FROM
                business_business AS biz JOIN
                business_business_categories AS biz_cat ON (biz.business_id = biz_cat.business_id) JOIN
                business_businesscategory AS cat ON (biz_cat.businesscategory_id = cat.category_id)
            WHERE active = true
            GROUP BY 1,2
            ORDER BY 1,2
        """,
        'pivot': True,
        'hidden': True,
    },
    {
        'label': 'businesscategory_names_json_onesky',
        'help': "OneSky Categories and Treatments exporter",
        'sql': """
            SELECT
                '    "' || (CASE type WHEN 'C' THEN '_' ELSE '' END) ||
                replace(replace(replace(replace(replace(lower(internal_name), ' ', '_'), '-', ''), '&', ''), '/', ''), '__', '_')
                || '": "' || name || '",' AS json
            FROM business_businesscategory
        """,
    },
    {
        'label': 'bookings_per_business',
        'help': 'Booking report per Business',
        'tags': ['for:MaciejMalysz'],
        'sql': """
            SELECT DISTINCT
                booking.subbooking_id,
                to_char(timezone(business.time_zone_name, booking.booked_from), 'YYYY-MM-DD HH24:MI') AS booked_from,
                to_char(timezone(business.time_zone_name, booking.booked_till), 'YYYY-MM-DD HH24:MI') AS booked_till,
                to_char(booking.created, 'YYYY-MM-DD HH24:MI:SS') AS created,
                biz_resource.name AS worker,
                appointment.customer_name AS client,
                COALESCE(NULLIF(bci.cell_phone, ''), user_user.cell_phone) AS cell_phone,
                COALESCE(
                    (
                        SELECT (
                            CASE
                                WHEN servcat.name IS NOT NULL THEN servcat.name || ' :: ' || serv.name
                                ELSE serv.name
                            END
                            )
                        FROM
                            business_service AS serv
                            LEFT OUTER JOIN business_servicecategory AS servcat ON (serv.service_category_id = servcat.service_category_id)
                        WHERE serv.service_id = business_servicevariant.service_id
                    ),
                    booking.service_name
                ) AS booking_service,
                booking.service_variant_id,
                business_servicevariant.price AS service_variant_price,
                business_servicevariant.type AS service_variant_type,
                appointment.business_note AS note,
                appointment.business_secret_note AS internal_note,
                bs.app_type || '_' || bs.name AS booking_source,
                (
                    CASE appointment.type
                    WHEN 'C' THEN 'CUSTOMER'
                    WHEN 'B' THEN 'BUSINESS'
                    WHEN 'T' THEN 'TIME_OFF'
                    WHEN 'R' THEN 'RESERVATION_BOOKING'
                    END
                ) AS type,
                (
                    CASE appointment.status
                    WHEN 'A' THEN 'ACCEPTED'
                    WHEN 'C' THEN 'CANCELED'
                    WHEN 'D' THEN 'DECLINED'
                    WHEN 'F' THEN 'FINISHED'
                    WHEN 'M' THEN 'MODIFIED'
                    WHEN 'N' THEN 'NOSHOW'
                    WHEN 'P' THEN 'PROPOSED'
                    WHEN 'R' THEN 'REJECTED'
                    WHEN 'V' THEN 'UNVERIFIED'
                    WHEN 'W' THEN 'UNCONFIRMED'
                    ELSE 'other'
                    END
                ) AS status
            FROM
                booking_subbooking AS booking LEFT JOIN
                booking_appointment AS appointment ON booking.appointment_id = appointment.appointment_id LEFT JOIN
                business_businesscustomerinfo AS bci ON (bci.business_customer_info_id = appointment.booked_for_id) LEFT JOIN
                user_user ON (user_user.user_ptr_id = bci.user_id) LEFT JOIN
                booking_bookingresource AS book_resource ON (book_resource.subbooking_id = booking.subbooking_id) LEFT JOIN
                booking_bookingsources AS bs ON (bs.booking_source_id = appointment.source_id) LEFT JOIN
                business_resource AS biz_resource ON (biz_resource.resource_id = book_resource.resource_id) LEFT JOIN
                business_business AS business ON (business.business_id =  %(business_id)s) LEFT JOIN
                business_servicevariant ON (business_servicevariant.service_variant_id = booking.service_variant_id)
            WHERE
                appointment.business_id = %(business_id)s
            ORDER BY
                booked_from, subbooking_id
        """,
        'filters': [
            {
                'type': 'text',
                'name': 'business_id',
            },
        ],
        'freeze_panes': 'B2',
    },
    {
        'label': 'booking_pricing',
        'help': "",
        'tags': ["for:ReportsSender"],
        'pivot': True,
        'freeze_panes': 'B2',
        'sql': """
            WITH
                types AS (
                    SELECT
                        to_char(
                            date_trunc(
                                %(period_type)s,
                                booking.created
                            ),
                            %(date_format)s
                        ) AS booking_created,
                        appointment.type || srv.type AS pivoted,
                        count(*)::float AS val
                    FROM
                        booking_booking AS booking JOIN
                        booking_appointment AS appointment ON booking.appointment_id = appointment.appointment_id JOIN
                        business_servicevariant AS srv ON (booking.service_variant_id = srv.service_variant_id)
                    WHERE
                        appointment.type IN ('C', 'B')
                        AND appointment.business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                    GROUP BY 1,2
                    ORDER BY 1,2
                ),
                prices AS (
                    SELECT
                        to_char(
                            date_trunc(
                                %(period_type)s,
                                booking.created
                            ),
                            %(date_format)s
                        ) AS booking_created,
                        appointment.type || srv.type || '_price' AS pivoted,
                        sum(srv.price)::float AS val
                    FROM
                        booking_booking AS booking JOIN
                        booking_appointment AS appointment ON booking.appointment_id = appointment.appointment_id JOIN
                        business_servicevariant AS srv ON (booking.service_variant_id = srv.service_variant_id)
                    WHERE
                        appointment.type IN ('C', 'B')
                        AND srv.type IN ('X', 'S')
                        AND appointment.business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                    GROUP BY 1,2
                    ORDER BY 1,2
                )
            SELECT
                rows_all.booking_created, rows_all.pivoted, rows_all.val
            FROM (
                SELECT * FROM types
                UNION ALL
                SELECT * FROM prices
            ) AS rows_all
            ORDER BY 1,2
        """,
        'columns_spec': [
            ('booking_created', 'booking_created', 'Booking creation time in UTC'),
            ('BX_price', 'biz_fixed_price', 'Total price of Business bookings with fixed price'),
            (
                'BS_price',
                'biz_startsat_price',
                'Total price of Business bookings with starts at price',
            ),
            ('BX', 'biz_fixed', 'Total number of Business bookings with fixed price'),
            ('BS', 'biz_startsat', 'Total number of Business bookings with starts at price'),
            ('BV', 'biz_varies'),
            ('BD', 'biz_dontshow'),
            ('BF', 'biz_free'),
            ('CX_price', 'cus_fixed_price'),
            ('CS_price', 'cus_startsat_price'),
            ('CX', 'cus_fixed'),
            ('CS', 'cus_startsat'),
            ('CV', 'cus_varies'),
            ('CD', 'cus_dontshow'),
            ('CF', 'cus_free'),
        ],
        'filters': [
            {
                'type': 'select',
                'name': 'period',
                'value': 'month',
                'help': "How to group data",
                'options': [
                    {'name': 'day', 'vars': {'period_type': 'day', 'date_format': 'YYYY-MM-DD'}},
                    {'name': 'week', 'vars': {'period_type': 'week', 'date_format': 'YYYY-MM-DD'}},
                    {'name': 'month', 'vars': {'period_type': 'month', 'date_format': 'YYYY-MM'}},
                    {
                        'name': 'quarter',
                        'vars': {'period_type': 'quarter', 'date_format': 'YYYY" Q"Q'},
                    },
                    {'name': 'year', 'vars': {'period_type': 'year', 'date_format': 'YYYY'}},
                ],
            }
        ],
        'hidden': True,
    },
    {
        'label': 'retention_business',
        'sql': """
            SELECT
                to_char(business_business.created, 'YYYY-MM') AS business_created,
                to_char(booking_booking.created, 'YYYY-MM') AS pivoted,
                count(*)
            FROM booking_booking JOIN
                business_business
                    ON (booking_booking.business_id = business_business.business_id)
            WHERE type IN ('B', 'C')
            GROUP BY 1, 2
            ORDER BY 1, 2
        """,
        'pivot': True,
        'hidden': True,
    },
    {
        'label': 'retention_business_paying',
        'tags': ['for:MarcinBartoszek'],
        'sql': """
            WITH
                bookings AS (
                SELECT
                    to_char(business_business.created, 'YYYY-MM') AS business_created,
                    to_char(booking_booking.created, 'YYYY-MM') AS pivoted,
                    count(*)
                FROM booking_booking JOIN
                    business_business
                        ON (booking_booking.business_id = business_business.business_id)
                WHERE type IN ('B', 'C') AND business_business.status = 'P'
                GROUP BY 1, 2
                ORDER BY 1, 2
            ), businesses AS (
                SELECT
                    to_char(business_business.created, 'YYYY-MM') AS business_created,
                    'biz_cnt' AS pivoted,
                    count(*)
                FROM business_business
                WHERE business_business.status = 'P'
                GROUP BY 1, 2
                ORDER BY 1, 2
            )
            SELECT * FROM (
                select * from bookings
                UNION ALL
                select * from businesses
            ) as foo
            ORDER BY 1, 2
        """,
        'pivot': True,
        'hidden': True,
    },
    {
        'label': 'retention_business_with_type',
        'help': 'Since 2015-01',
        'sql': """
            SELECT
                to_char(business_business.created, 'YYYY-MM') || '_' || type AS business_created_type,
                to_char(booking_booking.created, 'YYYY-MM') AS pivoted,
                count(*)
            FROM booking_booking JOIN
                business_business
                    ON (booking_booking.business_id = business_business.business_id)
            WHERE
                type IN ('B', 'C')
                AND business_business.created >= '2015-01-01'::timestamp
            GROUP BY 1, 2
            ORDER BY 1, 2
        """,
        'pivot': True,
        'hidden': True,
    },
    {
        'label': 'retention_booking_type',
        'sql': """
            SELECT
                to_char(booking_booking.created - business_business.created, 'DDD')::integer AS date_diff,
                type AS pivoted,
                count(*)
            FROM booking_booking JOIN
                business_business
                    ON (booking_booking.business_id = business_business.business_id)
            WHERE type IN ('B', 'C')
            GROUP BY 1, 2
            ORDER BY 1, 2
        """,
        'pivot': True,
        'hidden': True,
    },
    {
        'label': 'retention_booking_type_paying',
        'sql': """
            SELECT
                to_char(booking_booking.created - business_business.created, 'DDD')::integer AS date_diff,
                type AS pivoted,
                count(*)
            FROM booking_booking JOIN
                business_business
                    ON (booking_booking.business_id = business_business.business_id)
            WHERE type IN ('B', 'C') AND business_business.status = 'P'
            GROUP BY 1, 2
            ORDER BY 1, 2
        """,
        'pivot': True,
        'hidden': True,
    },
    {
        'label': 'retention_bookings_per_business',
        'sql': """
            SELECT
                business_business.business_id,
                to_char(business_business.created, 'YYYY')::integer AS biz_year,
                to_char(business_business.created, 'MM')::integer AS biz_month,
                to_char(booking_booking.created, 'YYYY')::integer AS book_year,
                to_char(booking_booking.created, 'MM')::integer AS book_month,
                type AS pivoted,
                count(*)
            FROM booking_booking JOIN
                business_business
                    ON (booking_booking.business_id = business_business.business_id)
            WHERE type IN ('B', 'C')
            GROUP BY 1, 2, 3, 4, 5, 6
            ORDER BY 1, 2, 3, 4, 5, 6
        """,
        'pivot': True,
        'hidden': True,
    },
    {
        'label': 'retention_customer',
        'sql': """
            SELECT
                to_char(auth_user.date_joined, 'YYYY-MM') AS customer_created,
                to_char(booking_booking.created, 'YYYY-MM') AS pivoted,
                count(*)
            FROM
                user_user JOIN
                auth_user ON (user_user.user_ptr_id = auth_user.id) JOIN
                business_businesscustomerinfo ON (user_id = user_ptr_id) JOIN
                booking_booking ON (booking_booking.booked_for_id = business_businesscustomerinfo.business_customer_info_id)
            WHERE type IN ('B', 'C')
            GROUP BY 1, 2
            ORDER BY 1, 2
        """,
        'pivot': True,
        'hidden': True,
    },
    {
        'label': 'bookings_per_source_by_business',
        'sql': """
            SELECT
                to_char(booking_appointment.created, 'YYYY-MM') AS month,
                app_type||'_'||name AS source,
                count(*)
            FROM booking_appointment
            JOIN booking_bookingsources
              ON booking_bookingsources.booking_source_id=booking_appointment.source_id
            WHERE
              business_id = %(business_id)s
              AND booking_appointment.type IN ('C', 'B')
            GROUP BY 1, 2
            ORDER BY 1, 2;
        """,
        'pivot': True,
        'filters': [
            {
                'type': 'text',
                'name': 'business_id',
            },
        ],
    },
    {
        'label': 'bookings_per_source_per_business',
        'tags': ['for:PiotrKnitter'],
        'sql': """
            SELECT
                business_id,
                to_char(booking_booking.created, 'YYYY-MM') AS month,
                app_type||'_'||name AS pivot,
                count(*)
            FROM booking_appointment
                JOIN booking_bookingsources
                    ON booking_bookingsources.booking_source_id=booking_appointment.source_id
            WHERE
                booking_appointment.type IN ('C', 'B')
            GROUP BY 1, 2, 3
            ORDER BY 1, 2, 3;
        """,
        'pivot': True,
        'hidden': True,
    },
    {
        'label': 'notification_reminder_state',
        'sql': """
            SELECT
                to_char(scheduled, 'YYYY-MM-DD') AS created_utc,
                state,
                count(*)
            FROM notification_notificationschedule
            WHERE
                task_id LIKE 'booking_changed:customer_booking_reminder:%%'
                AND scheduled BETWEEN (now() - interval '20 day') AND (now() + interval '1 day')
            GROUP BY 1,2
            ORDER BY 1,2
            ;
        """,
        'pivot': True,
        'hidden': True,
    },
    {
        'label': 'intervals_by_type',
        'help': "Intervals (in days) between bookings by booking type",
        'sql': """
            WITH
                per_bci AS (
                    SELECT
                        business_businesscustomerinfo.business_customer_info_id,
                        booking_booking.appointment.type AS type,
                        (max(booking_booking.booked_from) - min(booking_booking.booked_from)) AS range,
                        (count(*) - 1) AS cnt
                    FROM
                        business_businesscustomerinfo JOIN
                        booking_booking ON (booking_booking.booked_for_id = business_businesscustomerinfo.business_customer_info_id)
                    WHERE booking_booking.appointment.type IN ('B', 'C') AND booking_booking.multibooking_id IS NULL
                    GROUP BY 1, 2
                    HAVING
                        count(*) BETWEEN 2 AND 30
                        AND (max(booking_booking.booked_from) - min(booking_booking.booked_from)) / (count(*) - 1) > '7 days'::interval
                    ORDER BY 1, 2
                )
            SELECT
                '' AS ignore,
                type,
                EXTRACT(epoch FROM sum(range) / sum(cnt)) / 86400.0
            FROM
                per_bci
            GROUP BY 1,2
        """,
        'pivot': True,
        'hidden': True,
    },
    {
        'label': 'customer_converted_to_business',
        'sql': """
            WITH
                foo AS (
                    SELECT
                        user_ptr_id,
                        auth_user.first_name || ' ' || auth_user.last_name AS full_name,
                        auth_user.email,
                        (
                            SELECT min(created) AS first_booking
                            FROM booking_booking
                            WHERE booking_booking.booked_for_id = business_businesscustomerinfo.business_customer_info_id
                        ),
                        (
                            SELECT min(created) AS first_business
                            FROM business_business
                            WHERE
                                business_business.owner_id = user_user.user_ptr_id
                                AND business_business.active = false
                        )
                    FROM
                        user_user JOIN
                        auth_user ON (user_user.user_ptr_id = auth_user.id) JOIN
                        business_businesscustomerinfo ON (user_id = user_ptr_id)
                )
            SELECT
                foo.user_ptr_id AS user_id,
                foo.full_name AS full_name,
                foo.email AS email,
                to_char(foo.first_booking, 'YYYY-MM-DD HH24:MI:SS') AS first_booking_utc,
                to_char(foo.first_business, 'YYYY-MM-DD HH24:MI:SS') AS first_business_utc
            FROM foo
            WHERE
                foo.first_booking < foo.first_business
            ORDER BY 1
            ;
        """,
        'hidden': True,
    },
    {
        'label': 'sms_data_by_business',
        'help': "Return list of all SMSes business send",
        'sql': """
            SELECT
                business_id,
                to_char(
                    notification_notificationhistory.created,
                    'YYYY-MM-DD HH24:MI:SS'
                ) AS sent_utc,
                sms_count,
                task_id,
                metadata::json->'to' AS phone_number,
                title
            FROM notification_notificationhistory
            WHERE
                business_id = %(business_id)s
                AND type = 'S'
            ORDER BY 1,2
        """,
        'filters': [
            {
                'type': 'text',
                'name': 'business_id',
            },
        ],
    },
    {
        'ng': True,
        'label': 'notifications_counts',
        'help': "Return count of notifications per type.",
        'sql': """
            SELECT
                to_char(
                    date_trunc(
                        {{ params.period_type|sql_escape }},
                        notification_notificationhistory.created
                    ),
                    {{ params.date_format|sql_escape }}
                ) AS date,
                type AS pivoted,
                count(*) AS total_count
            FROM notification_notificationhistory
            {% if params.business_id %}
                WHERE business_id = {{ params.business_id|sql_escape }}
            {% endif %}
            GROUP BY 1,2
            ORDER BY 1,2
        """,
        'pivot': True,
        'filters': [
            {
                'type': 'text',
                'name': 'business_id',
            },
            {
                'type': 'select',
                'name': 'period',
                'value': 'week',
                'options': [
                    {'name': 'day', 'vars': {'period_type': 'day', 'date_format': 'YYYY-MM-DD'}},
                    {'name': 'week', 'vars': {'period_type': 'week', 'date_format': 'YYYY-MM-DD'}},
                    {'name': 'month', 'vars': {'period_type': 'month', 'date_format': 'YYYY-MM'}},
                    {'name': 'year', 'vars': {'period_type': 'year', 'date_format': 'YYYY'}},
                ],
            },
        ],
        'hidden': True,
    },
    {
        'ng': True,
        'label': 'sms_per_business',
        'help': "Ticket #25138",
        'sql': """
            WITH
                notifications AS (
                    SELECT
                        to_char(
                            date_trunc({{ params.period_type|sql_escape }}, notification_notificationhistory.created),
                            {{ params.date_format|sql_escape }}
                        ) AS date,
                        business_id,
                        sender,
                        count(*) AS total_count,
                        sum(sms_count) AS total_sms
                    FROM notification_notificationhistory
                    WHERE
                        business_id IS NOT NULL
                        AND notification_notificationhistory.created >=
                            date_trunc({{ params.period_type|sql_escape }}, now() - interval '{{ params.period_count }} {{ params.period_type }}')
                        AND business_id IN (
                            SELECT business_id FROM business_business
                        )
                    GROUP BY 1, 2, 3
                    ORDER BY 1, 2, 3
                )
            SELECT
                rows.business_id,
                rows.date,
                rows.kind AS pivoted,
                sum(rows.value)::INTEGER
            FROM (
                    SELECT
                        notifications.date,
                        notifications.business_id,
                        'all_notifications' AS kind,
                        notifications.total_count AS value
                    FROM notifications
                UNION ALL
                    SELECT
                        notifications.date,
                        notifications.business_id,
                        'all_sms' AS kind,
                        notifications.total_sms AS value
                    FROM notifications
                UNION ALL
                    SELECT
                        notifications.date,
                        notifications.business_id,
                        'system_notifications' AS kind,
                        notifications.total_count AS value
                    FROM notifications
                    WHERE sender = {{ models.notification.NotificationHistory.SENDER_SYSTEM|sql_escape }}
                UNION ALL
                    SELECT
                        notifications.date,
                        notifications.business_id,
                        'system_sms' AS kind,
                        notifications.total_sms AS value
                    FROM notifications
                    WHERE sender = {{ models.notification.NotificationHistory.SENDER_SYSTEM|sql_escape }}
                UNION ALL
                    SELECT
                        notifications.date,
                        notifications.business_id,
                        'marketing_notifications' AS kind,
                        notifications.total_count AS value
                    FROM notifications
                    WHERE sender = {{ models.notification.NotificationHistory.SENDER_BUSINESS|sql_escape }}
                UNION ALL
                    SELECT
                        notifications.date,
                        notifications.business_id,
                        'marketing_sms' AS kind,
                        notifications.total_sms AS value
                    FROM notifications
                    WHERE sender = {{ models.notification.NotificationHistory.SENDER_BUSINESS|sql_escape }}
            ) AS rows
            GROUP BY 1, 2, 3
            ORDER BY 1, 2, 3
        """,
        'pivot': True,
        'filters': [
            {
                'type': 'select',
                'name': 'period',
                'value': 'month',
                'help': "How to group data (60 days, 10 weeks, 3 months, 1 year)",
                'options': [
                    {
                        'name': 'day',
                        'vars': {
                            'period_type': 'day',
                            'period_count': 60,
                            'date_format': 'YYYY-MM-DD',
                        },
                    },
                    {
                        'name': 'week',
                        'vars': {
                            'period_type': 'week',
                            'period_count': 10,
                            'date_format': 'YYYY-MM-DD',
                        },
                    },
                    {
                        'name': 'month',
                        'vars': {
                            'period_type': 'month',
                            'period_count': 3,
                            'date_format': 'YYYY-MM',
                        },
                    },
                    {
                        'name': 'year',
                        'vars': {'period_type': 'year', 'period_count': 1, 'date_format': 'YYYY'},
                    },
                ],
            }
        ],
    },
    {
        'label': 'email_unsubscribed',
        'help': "List of all unsubscribed email addresses.",
        'sql': """
            SELECT
                to_char(created, 'YYYY-MM-DD') AS created,
                email
            FROM user_unsubscribedemail
            ORDER BY 1, 2
        """,
    },
    {
        'label': 'business_category_histogram',
        'sql': """
            WITH foo AS (
                SELECT
                    biz.business_id,
                    count(*) AS cnt
                FROM
                    business_business AS biz JOIN
                    business_business_categories AS biz_cat ON (biz.business_id = biz_cat.business_id) JOIN
                    business_businesscategory AS cat ON (biz_cat.businesscategory_id = cat.category_id)
                WHERE active = true
                GROUP BY 1
                ORDER BY 1
            )
            SELECT
                '' AS ignore,
                to_char(foo.cnt, 'FM00') AS pivoted,
                count(*)
            FROM
                foo
            GROUP BY 1,2
            ORDER BY 1,2
        """,
        'pivot': True,
        'hidden': True,
    },
    {
        'ng': True,
        'label': 'business_active__changed_by',
        'help': (
            "Returns list of businesses, which were <b>modified</b> by admin user specified by <tt>user_email</tt>.\n\n"
            "If <tt>changed_by</tt> is set to <tt>no</tt> then return businesses <b>not modified</b> this user"
        ),
        'sql': """
            SELECT
                business_business.business_id,
                to_char(business_business.created, 'YYYY-MM-DD HH24:MI:SS') AS created,
                business_business.name AS business_name
            FROM business_business
            WHERE
                business_business.active = true
                AND business_business.business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                AND {{ params.operator }} EXISTS (
                    SELECT 1
                    FROM business_businesschange
                    WHERE
                        metadata LIKE '%' || {{ params.user_email|sql_escape }} || '%'
                        AND business_businesschange.business_id = business_business.business_id
                        {% if params.changed_after %}AND business_businesschange.created >= {{ params.changed_after|sql_escape }}::timestamp{% endif %}
                )
            ORDER BY 1 DESC
        """,
        'filters': [
            {
                'type': 'text',
                'name': 'user_email',
            },
            {
                'type': 'text',
                'name': 'changed_after',
                'value': '2016-06-02 06:00',
                'help': "Analyze only changes done after this date. Date format: 'YYYY-MM-DD' or 'YYYY-MM-DD hh:mm'",
            },
            {
                'type': 'select',
                'name': 'changed_by',
                'value': 'week',
                'options': [
                    {'name': 'yes', 'vars': {'operator': ''}},
                    {'name': 'no', 'vars': {'operator': 'NOT'}},
                ],
            },
        ],
        'hidden': True,
    },
    {
        'ng': True,
        'label': 'customer_abuse',
        'help': '',
        'sql': """
            WITH bookings AS (
                SELECT
                    trim(
                        COALESCE(NULLIF(business_businesscustomerinfo.first_name, ''), auth_user.first_name) ||
                        ' ' ||
                        COALESCE(NULLIF(business_businesscustomerinfo.last_name, ''), auth_user.last_name)
                    ) AS name,
                    COALESCE(NULLIF(business_businesscustomerinfo.cell_phone, ''), user_user.cell_phone) AS cell_phone,
                    COALESCE(NULLIF(business_businesscustomerinfo.email, ''), auth_user.email) AS email,
                    booking_booking.appointment.status
                FROM
                    user_user JOIN
                    auth_user ON (user_user.user_ptr_id = auth_user.id) RIGHT OUTER JOIN
                    business_businesscustomerinfo ON (user_id = user_ptr_id) JOIN
                    booking_booking ON (booking_booking.booked_for_id = business_businesscustomerinfo.business_customer_info_id)
                WHERE
                    booking_booking.appointment.type = 'C'
                    {% if params.date_after %}
                        AND booking_booking.created >= {{ params.date_after|sql_escape }}::timestamp
                    {% else %}
                        AND booking_booking.created >= date_trunc('month', NOW() - INTERVAL '1 months')
                    {% endif %}
                    {% if params.date_before %}
                        AND booking_booking.created < {{ params.date_before|sql_escape }}::timestamp
                    {% endif %}
            )
            SELECT
                name,
                cell_phone,
                email,
                kind AS pivoted,
                count(*) AS cnt
            FROM (
                    SELECT
                        name,
                        cell_phone,
                        email,
                        'NOSHOW' AS kind
                    FROM bookings
                    WHERE
                        status = {{ models.booking.Appointment.STATUS.NOSHOW|sql_escape }}
                UNION ALL
                    SELECT
                        name,
                        cell_phone,
                        email,
                        'CANCELED' AS kind
                    FROM bookings
                    WHERE
                        status = {{ models.booking.Appointment.STATUS.CANCELED|sql_escape }}
                UNION ALL
                    SELECT
                        name,
                        cell_phone,
                        email,
                        'ALL' AS kind
                    FROM bookings
            ) AS foo
            GROUP BY 1,2,3,4
            ORDER BY 1,2,3,4
        """,
        'pivot': True,
        'filters': [
            {
                'type': 'text',
                'name': 'date_after',
                'help': "Count only bookings created after this date. Date format: 'YYYY-MM-DD' or 'YYYY-MM-DD hh:mm', like '2016-06-01 00:00'. Default: 1 month ago",
            },
            {
                'type': 'text',
                'name': 'date_before',
                'help': "Count only bookings created before this date. Format like in date_after. Date is not inclusive, so entering 2016-06-03 will result in bookings created not later than 2016-06-02 23:59:59. Default: now",
            },
        ],
        'hidden': True,
    },
    {
        'label': 'business_activity_delay',
        'tags': ["for:ReportsSender"],
        'sql': """
            WITH
                biz AS (
                    SELECT
                        biz.business_id,
                        to_char(
                            date_trunc(
                                %(period_type)s,
                                biz.created
                            ),
                            %(date_format)s
                        ) AS biz_created,
                        biz.created,
                        biz.active,
                        biz.status
                    FROM
                        business_business AS biz
                    WHERE
                        biz.business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                ),
                first_biz AS (
                    SELECT business_id, min(created) AS first
                    FROM booking_booking
                    WHERE
                        type = 'B'
                        AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                    GROUP BY 1
                ),
                dates_biz AS (
                    SELECT
                        biz.biz_created,
                        CASE
                            WHEN (first_biz.first - biz.created) < interval '1 days' THEN 'biz_1day'
                            WHEN (first_biz.first - biz.created) < interval '7 days' THEN 'biz_2week'
                            WHEN (first_biz.first - biz.created) < interval '30 days' THEN 'biz_3month'
                            ELSE 'biz_4any'
                        END AS booking_delay
                    FROM
                        biz JOIN first_biz ON (biz.business_id = first_biz.business_id)
                ),
                first_noncus AS (
                    SELECT business_id, min(created) AS first
                    FROM booking_booking
                    WHERE
                        type != 'C'
                        AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                    GROUP BY 1
                ),
                dates_noncus AS (
                    SELECT
                        biz.biz_created,
                        CASE
                            WHEN (first_noncus.first - biz.created) < interval '1 days' THEN 'noncus_1day'
                            WHEN (first_noncus.first - biz.created) < interval '7 days' THEN 'noncus_2week'
                            WHEN (first_noncus.first - biz.created) < interval '30 days' THEN 'noncus_3month'
                            ELSE 'noncus_4any'
                        END AS booking_delay
                    FROM
                        biz JOIN first_noncus ON (biz.business_id = first_noncus.business_id)
                ),
                first_cus AS (
                    SELECT business_id, min(created) AS first
                    FROM booking_booking
                    WHERE
                        type = 'C'
                        AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                    GROUP BY 1
                ),
                dates_cus AS (
                    SELECT
                        biz.biz_created,
                        CASE
                            WHEN (first_cus.first - biz.created) < interval '1 days' THEN 'cus_1day'
                            WHEN (first_cus.first - biz.created) < interval '7 days' THEN 'cus_2week'
                            WHEN (first_cus.first - biz.created) < interval '30 days' THEN 'cus_3month'
                            ELSE 'cus_4any'
                        END AS booking_delay
                    FROM
                        biz JOIN first_cus ON (biz.business_id = first_cus.business_id)
                ),
                biz_account AS (
                    SELECT
                        biz.biz_created,
                        'account'::text AS booking_delay
                    FROM
                        biz
                    WHERE
                        business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                ),
                biz_active AS (
                    SELECT
                        biz.biz_created,
                        'active'::text AS booking_delay
                    FROM
                        biz
                    WHERE
                        biz.active = true
                        AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                ),
                biz_annuitet AS (
                    SELECT
                        biz.biz_created,
                        'annuitet'::text AS booking_delay
                    FROM
                        biz
                    WHERE
                        biz.active = true AND biz.status = 'P'
                ),
                biz_annuitet_ever AS (
                    SELECT
                        biz.biz_created,
                        'annuitet_ever'::text AS booking_delay
                    FROM
                        biz
                    WHERE
                        biz.active = true AND biz.status IN ('P', 'O', 'L')
                )
            SELECT
                biz_created, booking_delay, count(*)
            FROM (
                SELECT * FROM dates_biz
                UNION ALL
                SELECT * FROM dates_noncus
                UNION ALL
                SELECT * FROM dates_cus
                UNION ALL
                SELECT * FROM biz_active
                UNION ALL
                SELECT * FROM biz_account
                UNION ALL
                SELECT * FROM biz_annuitet
                UNION ALL
                SELECT * FROM biz_annuitet_ever
            ) AS dates_all
            GROUP BY 1,2
            ORDER BY 1,2
            ;
        """,
        'pivot': True,
        'filters': [
            {
                'type': 'select',
                'name': 'period',
                'value': 'month',
                'help': "How to group data",
                'options': [
                    {'name': 'day', 'vars': {'period_type': 'day', 'date_format': 'YYYY-MM-DD'}},
                    {'name': 'week', 'vars': {'period_type': 'week', 'date_format': 'YYYY-MM-DD'}},
                    {'name': 'month', 'vars': {'period_type': 'month', 'date_format': 'YYYY-MM'}},
                    {
                        'name': 'quarter',
                        'vars': {'period_type': 'quarter', 'date_format': 'YYYY" Q"Q'},
                    },
                    {'name': 'year', 'vars': {'period_type': 'year', 'date_format': 'YYYY'}},
                    {'name': 'all', 'vars': {'period_type': 'century', 'date_format': '"all"'}},
                ],
            }
        ],
        'hidden': True,
    },
    {
        'label': 'business_activity_delay__bizid',
        'tags': ["for:PiotrKnitter"],
        'sql': """
            WITH
                biz AS (
                    SELECT
                        biz.business_id,
                        biz.created,
                        biz.active,
                        biz.status
                    FROM
                        business_business AS biz
                    WHERE
                        biz.business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                ),
                first_biz AS (
                    SELECT business_id, min(created) AS first
                    FROM booking_booking
                    WHERE
                        type = 'B'
                        AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                    GROUP BY 1
                ),
                dates_biz AS (
                    SELECT
                        biz.business_id,
                        CASE
                            WHEN (first_biz.first - biz.created) < interval '1 days' THEN 'biz_1day'
                            WHEN (first_biz.first - biz.created) < interval '7 days' THEN 'biz_2week'
                            WHEN (first_biz.first - biz.created) < interval '30 days' THEN 'biz_3month'
                            ELSE 'biz_4any'
                        END AS booking_delay
                    FROM
                        biz JOIN first_biz ON (biz.business_id = first_biz.business_id)
                ),
                first_noncus AS (
                    SELECT business_id, min(created) AS first
                    FROM booking_booking
                    WHERE
                        type != 'C'
                        AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                    GROUP BY 1
                ),
                dates_noncus AS (
                    SELECT
                        biz.business_id,
                        CASE
                            WHEN (first_noncus.first - biz.created) < interval '1 days' THEN 'noncus_1day'
                            WHEN (first_noncus.first - biz.created) < interval '7 days' THEN 'noncus_2week'
                            WHEN (first_noncus.first - biz.created) < interval '30 days' THEN 'noncus_3month'
                            ELSE 'noncus_4any'
                        END AS booking_delay
                    FROM
                        biz JOIN first_noncus ON (biz.business_id = first_noncus.business_id)
                ),
                first_cus AS (
                    SELECT business_id, min(created) AS first
                    FROM booking_booking
                    WHERE
                        type = 'C'
                        AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                    GROUP BY 1
                ),
                dates_cus AS (
                    SELECT
                        biz.business_id,
                        CASE
                            WHEN (first_cus.first - biz.created) < interval '1 days' THEN 'cus_1day'
                            WHEN (first_cus.first - biz.created) < interval '7 days' THEN 'cus_2week'
                            WHEN (first_cus.first - biz.created) < interval '30 days' THEN 'cus_3month'
                            ELSE 'cus_4any'
                        END AS booking_delay
                    FROM
                        biz JOIN first_cus ON (biz.business_id = first_cus.business_id)
                ),
                biz_account AS (
                    SELECT
                        biz.business_id,
                        'account'::text AS booking_delay
                    FROM
                        biz
                    WHERE
                        business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                ),
                biz_active AS (
                    SELECT
                        biz.business_id,
                        'active'::text AS booking_delay
                    FROM
                        biz
                    WHERE
                        biz.active = true
                        AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                ),
                biz_annuitet AS (
                    SELECT
                        biz.business_id,
                        'annuitet'::text AS booking_delay
                    FROM
                        biz
                    WHERE
                        biz.active = true AND biz.status = 'P'
                ),
                biz_annuitet_ever AS (
                    SELECT
                        biz.business_id,
                        'annuitet_ever'::text AS booking_delay
                    FROM
                        biz
                    WHERE
                        biz.active = true AND biz.status IN ('P', 'O', 'L')
                )
            SELECT
                business_id, booking_delay, count(*)
            FROM (
                SELECT * FROM dates_biz
                UNION ALL
                SELECT * FROM dates_noncus
                UNION ALL
                SELECT * FROM dates_cus
                UNION ALL
                SELECT * FROM biz_active
                UNION ALL
                SELECT * FROM biz_account
                UNION ALL
                SELECT * FROM biz_annuitet
                UNION ALL
                SELECT * FROM biz_annuitet_ever
            ) AS dates_all
            GROUP BY 1,2
            ORDER BY 1,2
            ;
        """,
        'pivot': True,
        'hidden': True,
    },
    {
        'label': 'business_category_activity_delay',
        'tags': ["for:ReportsSender"],
        'sql': """
            WITH
                biz AS (
                    SELECT
                        biz.business_id,
                        to_char(
                            date_trunc(
                                %(period_type)s,
                                biz.created
                            ),
                            %(date_format)s
                        ) AS biz_created,
                        biz.created,
                        biz.active,
                        biz.status,
                        cat.internal_name AS category
                    FROM
                        business_business AS biz JOIN
                        business_business_categories AS biz_cat ON (biz.business_id = biz_cat.business_id) JOIN
                        business_businesscategory AS cat ON (biz_cat.businesscategory_id = cat.category_id)
                ),
                first_biz AS (
                    SELECT business_id, min(created) AS first
                    FROM booking_booking
                    WHERE type = 'B'
                    GROUP BY 1
                ),
                dates_biz AS (
                    SELECT
                        biz.biz_created,
                        biz.category,
                        CASE
                            WHEN (first_biz.first - biz.created) < interval '1 days' THEN 'biz_1day'
                            WHEN (first_biz.first - biz.created) < interval '7 days' THEN 'biz_2week'
                            WHEN (first_biz.first - biz.created) < interval '30 days' THEN 'biz_3month'
                            ELSE 'biz_4any'
                        END AS booking_delay
                    FROM
                        biz JOIN first_biz ON (biz.business_id = first_biz.business_id)
                ),
                first_noncus AS (
                    SELECT business_id, min(created) AS first
                    FROM booking_booking
                    WHERE type != 'C'
                    GROUP BY 1
                ),
                dates_noncus AS (
                    SELECT
                        biz.biz_created,
                        biz.category,
                        CASE
                            WHEN (first_noncus.first - biz.created) < interval '1 days' THEN 'noncus_1day'
                            WHEN (first_noncus.first - biz.created) < interval '7 days' THEN 'noncus_2week'
                            WHEN (first_noncus.first - biz.created) < interval '30 days' THEN 'noncus_3month'
                            ELSE 'noncus_4any'
                        END AS booking_delay
                    FROM
                        biz JOIN first_noncus ON (biz.business_id = first_noncus.business_id)
                ),
                first_cus AS (
                    SELECT business_id, min(created) AS first
                    FROM booking_booking
                    WHERE type = 'C'
                    GROUP BY 1
                ),
                dates_cus AS (
                    SELECT
                        biz.biz_created,
                        biz.category,
                        CASE
                            WHEN (first_cus.first - biz.created) < interval '1 days' THEN 'cus_1day'
                            WHEN (first_cus.first - biz.created) < interval '7 days' THEN 'cus_2week'
                            WHEN (first_cus.first - biz.created) < interval '30 days' THEN 'cus_3month'
                            ELSE 'cus_4any'
                        END AS booking_delay
                    FROM
                        biz JOIN first_cus ON (biz.business_id = first_cus.business_id)
                ),
                biz_account AS (
                    SELECT
                        biz.biz_created,
                        biz.category,
                        'account'::text AS booking_delay
                    FROM
                        biz
                ),
                biz_active AS (
                    SELECT
                        biz.biz_created,
                        biz.category,
                        'active'::text AS booking_delay
                    FROM
                        biz
                    WHERE
                        biz.active = true
                ),
                biz_annuitet AS (
                    SELECT
                        biz.biz_created,
                        biz.category,
                        'annuitet'::text AS booking_delay
                    FROM
                        biz
                    WHERE
                        biz.active = true AND biz.status = 'P'
                ),
                biz_annuitet_ever AS (
                    SELECT
                        biz.biz_created,
                        biz.category,
                        'annuitet_eve'::text AS booking_delay
                    FROM
                        biz
                    WHERE
                        biz.active = true AND biz.status IN ('P', 'O', 'L')
                )
            SELECT
                biz_created, category, booking_delay, count(*)
            FROM (
                SELECT * FROM dates_biz
                UNION ALL
                SELECT * FROM dates_noncus
                UNION ALL
                SELECT * FROM dates_cus
                UNION ALL
                SELECT * FROM biz_active
                UNION ALL
                SELECT * FROM biz_account
                UNION ALL
                SELECT * FROM biz_annuitet
                UNION ALL
                SELECT * FROM biz_annuitet_ever
            ) AS dates_all
            GROUP BY 1,2,3
            ORDER BY 1,2,3
            ;
        """,
        'pivot': True,
        'filters': [
            {
                'type': 'select',
                'name': 'period',
                'value': 'month',
                'help': "How to group data",
                'options': [
                    {'name': 'day', 'vars': {'period_type': 'day', 'date_format': 'YYYY-MM-DD'}},
                    {'name': 'week', 'vars': {'period_type': 'week', 'date_format': 'YYYY-MM-DD'}},
                    {'name': 'month', 'vars': {'period_type': 'month', 'date_format': 'YYYY-MM'}},
                    {
                        'name': 'quarter',
                        'vars': {'period_type': 'quarter', 'date_format': 'YYYY" Q"Q'},
                    },
                    {'name': 'year', 'vars': {'period_type': 'year', 'date_format': 'YYYY'}},
                    {'name': 'all', 'vars': {'period_type': 'century', 'date_format': '"all"'}},
                ],
            }
        ],
        'hidden': True,
    },
    ######################################################################
    {
        'label': 'business_primary_category_activity_delay',
        'tags': ["for:PiotrKnitter"],
        'help': "<tt>category</tt> is a primary_category",
        'sql': """
            WITH
                biz AS (
                    SELECT
                        biz.business_id,
                        to_char(
                            date_trunc(
                                %(period_type)s,
                                biz.created
                            ),
                            %(date_format)s
                        ) AS biz_created,
                        biz.created,
                        biz.active,
                        biz.status,
                        cat.internal_name AS category
                    FROM
                        business_business AS biz JOIN
                        business_businesscategory AS cat ON (biz.primary_category_id = cat.category_id)
                ),
                first_biz AS (
                    SELECT business_id, min(created) AS first
                    FROM booking_booking
                    WHERE type = 'B'
                    GROUP BY 1
                ),
                dates_biz AS (
                    SELECT
                        biz.biz_created,
                        biz.category,
                        CASE
                            WHEN (first_biz.first - biz.created) < interval '1 days' THEN 'biz_1day'
                            WHEN (first_biz.first - biz.created) < interval '7 days' THEN 'biz_2week'
                            WHEN (first_biz.first - biz.created) < interval '30 days' THEN 'biz_3month'
                            ELSE 'biz_4any'
                        END AS booking_delay
                    FROM
                        biz JOIN first_biz ON (biz.business_id = first_biz.business_id)
                ),
                first_noncus AS (
                    SELECT business_id, min(created) AS first
                    FROM booking_booking
                    WHERE type != 'C'
                    GROUP BY 1
                ),
                dates_noncus AS (
                    SELECT
                        biz.biz_created,
                        biz.category,
                        CASE
                            WHEN (first_noncus.first - biz.created) < interval '1 days' THEN 'noncus_1day'
                            WHEN (first_noncus.first - biz.created) < interval '7 days' THEN 'noncus_2week'
                            WHEN (first_noncus.first - biz.created) < interval '30 days' THEN 'noncus_3month'
                            ELSE 'noncus_4any'
                        END AS booking_delay
                    FROM
                        biz JOIN first_noncus ON (biz.business_id = first_noncus.business_id)
                ),
                first_cus AS (
                    SELECT business_id, min(created) AS first
                    FROM booking_booking
                    WHERE type = 'C'
                    GROUP BY 1
                ),
                dates_cus AS (
                    SELECT
                        biz.biz_created,
                        biz.category,
                        CASE
                            WHEN (first_cus.first - biz.created) < interval '1 days' THEN 'cus_1day'
                            WHEN (first_cus.first - biz.created) < interval '7 days' THEN 'cus_2week'
                            WHEN (first_cus.first - biz.created) < interval '30 days' THEN 'cus_3month'
                            ELSE 'cus_4any'
                        END AS booking_delay
                    FROM
                        biz JOIN first_cus ON (biz.business_id = first_cus.business_id)
                ),
                biz_account AS (
                    SELECT
                        biz.biz_created,
                        biz.category,
                        'account'::text AS booking_delay
                    FROM
                        biz
                ),
                biz_active AS (
                    SELECT
                        biz.biz_created,
                        biz.category,
                        'active'::text AS booking_delay
                    FROM
                        biz
                    WHERE
                        biz.active = true
                ),
                biz_annuitet AS (
                    SELECT
                        biz.biz_created,
                        biz.category,
                        'annuitet'::text AS booking_delay
                    FROM
                        biz
                    WHERE
                        biz.active = true AND biz.status = 'P'
                ),
                biz_annuitet_ever AS (
                    SELECT
                        biz.biz_created,
                        biz.category,
                        'annuitet_ever'::text AS booking_delay
                    FROM
                        biz
                    WHERE
                        biz.active = true AND biz.status IN ('P', 'O', 'L')
                )
            SELECT
                biz_created, category, booking_delay, count(*)
            FROM (
                SELECT * FROM dates_biz
                UNION ALL
                SELECT * FROM dates_noncus
                UNION ALL
                SELECT * FROM dates_cus
                UNION ALL
                SELECT * FROM biz_active
                UNION ALL
                SELECT * FROM biz_account
                UNION ALL
                SELECT * FROM biz_annuitet
                UNION ALL
                SELECT * FROM biz_annuitet_ever
            ) AS dates_all
            GROUP BY 1,2,3
            ORDER BY 1,2,3
            ;
        """,
        'pivot': True,
        'filters': [
            {
                'type': 'select',
                'name': 'period',
                'value': 'month',
                'help': "How to group data",
                'options': [
                    {'name': 'day', 'vars': {'period_type': 'day', 'date_format': 'YYYY-MM-DD'}},
                    {'name': 'week', 'vars': {'period_type': 'week', 'date_format': 'YYYY-MM-DD'}},
                    {'name': 'month', 'vars': {'period_type': 'month', 'date_format': 'YYYY-MM'}},
                    {
                        'name': 'quarter',
                        'vars': {'period_type': 'quarter', 'date_format': 'YYYY" Q"Q'},
                    },
                    {'name': 'year', 'vars': {'period_type': 'year', 'date_format': 'YYYY'}},
                    {'name': 'all', 'vars': {'period_type': 'century', 'date_format': '"all"'}},
                ],
            }
        ],
        'hidden': True,
    },
    ######################################################################
    {
        'label': 'business_category_location_staffers_activity_delay',
        'tags': ["for:ReportsSender"],
        'sql': """
            WITH
                biz AS (
                    SELECT
                        biz.business_id,
                        to_char(
                            date_trunc(
                                %(period_type)s,
                                biz.created
                            ),
                            %(date_format)s
                        ) AS biz_created,
                        biz.created,
                        biz.active,
                        biz.status,
                        cat.internal_name AS category,
                        (
                            WITH RECURSIVE temptab(parent) AS (
                                SELECT root.region_id AS parent
                                FROM structure_regiongraph AS root
                                WHERE root.related_region_id = (
                                   SELECT region_id FROM business_business_regions AS biz_reg WHERE biz_reg.business_id = biz.business_id LIMIT 1
                                )
                            UNION ALL
                                SELECT s.region_id AS parent
                                FROM structure_regiongraph AS s JOIN temptab AS t ON (s.related_region_id = t.parent)
                            )
                            SELECT string_agg(DISTINCT structure_region.name, ', ')
                            FROM temptab JOIN structure_region ON (temptab.parent = structure_region.region_id)
                            WHERE structure_region.type IN ('state')
                            LIMIT 1
                        ) AS location,
                        (
                            SELECT CASE count(*)
                                WHEN 0 THEN '0'
                                WHEN 1 THEN '1'
                                WHEN 2 THEN '2'
                                ELSE '3plus'
                            END AS staffers_count
                            FROM business_resource
                            WHERE type = 'S' AND business_id = biz.business_id
                        ) AS staffers
                    FROM
                        business_business AS biz JOIN
                        business_business_categories AS biz_cat ON (biz.business_id = biz_cat.business_id) JOIN
                        business_businesscategory AS cat ON (biz_cat.businesscategory_id = cat.category_id)
                    WHERE
                        biz.business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                ),
                first_biz AS (
                    SELECT business_id, min(created) AS first
                    FROM booking_booking
                    WHERE
                        type = 'B'
                        AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                    GROUP BY 1
                ),
                dates_biz AS (
                    SELECT
                        biz.biz_created,
                        biz.category,
                        biz.location,
                        biz.staffers,
                        CASE
                            WHEN (first_biz.first - biz.created) < interval '1 days' THEN 'biz_1day'
                            WHEN (first_biz.first - biz.created) < interval '7 days' THEN 'biz_2week'
                            WHEN (first_biz.first - biz.created) < interval '30 days' THEN 'biz_3month'
                            ELSE 'biz_4any'
                        END AS booking_delay
                    FROM
                        biz JOIN first_biz ON (biz.business_id = first_biz.business_id)
                ),
                first_noncus AS (
                    SELECT business_id, min(created) AS first
                    FROM booking_booking
                    WHERE
                        type != 'C'
                        AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                    GROUP BY 1
                ),
                dates_noncus AS (
                    SELECT
                        biz.biz_created,
                        biz.category,
                        biz.location,
                        biz.staffers,
                        CASE
                            WHEN (first_noncus.first - biz.created) < interval '1 days' THEN 'noncus_1day'
                            WHEN (first_noncus.first - biz.created) < interval '7 days' THEN 'noncus_2week'
                            WHEN (first_noncus.first - biz.created) < interval '30 days' THEN 'noncus_3month'
                            ELSE 'noncus_4any'
                        END AS booking_delay
                    FROM
                        biz JOIN first_noncus ON (biz.business_id = first_noncus.business_id)
                ),
                first_cus AS (
                    SELECT business_id, min(created) AS first
                    FROM booking_booking
                    WHERE
                        type = 'C'
                        AND business_id NOT IN (SELECT business_id FROM business_business WHERE status IN ('D', 'M'))
                    GROUP BY 1
                ),
                dates_cus AS (
                    SELECT
                        biz.biz_created,
                        biz.category,
                        biz.location,
                        biz.staffers,
                        CASE
                            WHEN (first_cus.first - biz.created) < interval '1 days' THEN 'cus_1day'
                            WHEN (first_cus.first - biz.created) < interval '7 days' THEN 'cus_2week'
                            WHEN (first_cus.first - biz.created) < interval '30 days' THEN 'cus_3month'
                            ELSE 'cus_4any'
                        END AS booking_delay
                    FROM
                        biz JOIN first_cus ON (biz.business_id = first_cus.business_id)
                ),
                biz_account AS (
                    SELECT
                        biz.biz_created,
                        biz.category,
                        biz.location,
                        biz.staffers,
                        'account'::text AS booking_delay
                    FROM
                        biz
                ),
                biz_active AS (
                    SELECT
                        biz.biz_created,
                        biz.category,
                        biz.location,
                        biz.staffers,
                        'active'::text AS booking_delay
                    FROM
                        biz
                    WHERE
                        biz.active = true
                ),
                biz_annuitet AS (
                    SELECT
                        biz.biz_created,
                        biz.category,
                        biz.location,
                        biz.staffers,
                        'annuitet'::text AS booking_delay
                    FROM
                        biz
                    WHERE
                        biz.active = true AND biz.status = 'P'
                )
            SELECT
                biz_created, category, location, staffers, booking_delay, count(*)
            FROM (
                SELECT * FROM dates_biz
                UNION ALL
                SELECT * FROM dates_noncus
                UNION ALL
                SELECT * FROM dates_cus
                UNION ALL
                SELECT * FROM biz_active
                UNION ALL
                SELECT * FROM biz_account
                UNION ALL
                SELECT * FROM biz_annuitet
            ) AS dates_all
            GROUP BY 1,2,3,4,5
            ORDER BY 1,2,3,4,5
            ;
        """,
        'pivot': True,
        'filters': [
            {
                'type': 'select',
                'name': 'period',
                'value': 'month',
                'help': "How to group data",
                'options': [
                    {'name': 'day', 'vars': {'period_type': 'day', 'date_format': 'YYYY-MM-DD'}},
                    {'name': 'week', 'vars': {'period_type': 'week', 'date_format': 'YYYY-MM-DD'}},
                    {'name': 'month', 'vars': {'period_type': 'month', 'date_format': 'YYYY-MM'}},
                    {
                        'name': 'quarter',
                        'vars': {'period_type': 'quarter', 'date_format': 'YYYY" Q"Q'},
                    },
                    {'name': 'year', 'vars': {'period_type': 'year', 'date_format': 'YYYY'}},
                    {'name': 'all', 'vars': {'period_type': 'century', 'date_format': '"all"'}},
                ],
            }
        ],
        'hidden': True,
    },
    {
        'label': 'list_of_products_per_business',
        'sql': """
            SELECT
              prod.name AS name,
              net_price AS price,
              cat.name AS category
            FROM warehouse_commodity AS prod
              LEFT JOIN warehouse_commoditycategory AS cat
                ON cat.id = prod.category_id
            WHERE prod.business_id = %(business_id)s
                  AND prod.archived = FALSE; 
        """,
        'filters': [
            {
                'type': 'text',
                'name': 'business_id',
            },
        ],
    },
    {
        'label': 'list_of_services_per_business',
        'sql': """
            SELECT
              bs.name,
              bs.description,
              EXTRACT(epoch from sv.duration)/60 AS duration,
              cat.name       AS cat_name,
              sv.price
            FROM business_service bs
              LEFT JOIN business_servicecategory AS cat
                ON cat.service_category_id = bs.service_category_id
              LEFT JOIN business_servicevariant AS sv
                ON bs.service_id = sv.service_id
            WHERE bs.business_id = %(business_id)s
                AND bs.active = TRUE
                AND sv.active = TRUE;
        """,
        'filters': [
            {
                'type': 'text',
                'name': 'business_id',
            },
        ],
    },
    {
        'label': 'clients_with_no_booking_for_last_x_days',
        'sql': """
        select business_customer_info_id, first_name, last_name, cell_phone from business_businesscustomerinfo 
        where business_customer_info_id not in 
        (
            select  booked_for_id from booking_appointment where 
            created >= (SELECT CURRENT_DATE - integer %(x_days)s)::timestamp
            and booked_for_id is not null and type in ('C', 'B') 
            and business_id=%(business_id)s
        ) and business_id=%(business_id)s;
        """,
        'filters': [
            {
                'type': 'text',
                'name': 'business_id',
            },
            {
                'type': 'text',
                'name': 'x_days',
            },
        ],
    },
    {
        'label': 'business_status_phone',
        'sql': """
            SELECT
              business_id,
              (
                CASE "business_business"."status"
                WHEN 'S'
                  THEN 'SETUP'
                WHEN 'T'
                  THEN 'TRIAL'
                WHEN 'E'
                  THEN 'TRIAL END'
                WHEN 'F'
                  THEN 'TRIAL BLOCKED'
                WHEN 'P'
                  THEN 'PAID'
                WHEN 'O'
                  THEN 'PAYMENT OVERDUE'
                WHEN 'L'
                  THEN 'BLOCKED PAYMENT OVERDUE'
                WHEN 'B'
                  THEN 'BLOCKED'
                WHEN 'D'
                  THEN 'DEMO'
                WHEN 'M'
                  THEN 'DEMO TEMPLATE'
                ELSE '???'
                END
              ) AS business_status,
              active,
              phone
            FROM business_business
            ORDER BY 1;
        """,
        'hidden': True,
    },
    {
        'label': 'bookings_per_staffer_per_service',
        'sql': """
              SELECT
                bk.business_id,
                bz.name AS business_name,
                to_char(bk.created, 'YYYY-MM-DD')::date AS booking_created,
                to_char(bk.booked_from, 'YYYY-MM-DD')::date AS booking_happened,
                CASE
                  WHEN bk.type = 'C' THEN 'Customer Booking'
                  WHEN bk.type = 'B' THEN 'Business Booking'
                  ELSE 'Reservation or Time-Off'
                END AS booking_type,
                sv.price,
                br.name AS staffer_name,
                bs.name AS service_name,
                bk.customer_name,
                COALESCE (uu.cell_phone, bc.cell_phone) AS client_phone_number,
                COALESCE (au.email, bc.email) AS client_email_address
              FROM booking_booking bk
                LEFT JOIN booking_bookingresource AS bkr
                  ON bk.booking_id = bkr.booking_id
                LEFT JOIN business_resource AS br
                  ON bkr.resource_id = br.resource_id
                  AND br.type = 'S'
                LEFT JOIN business_servicevariant sv
                  ON bk.service_variant_id = sv.service_variant_id
                LEFT JOIN business_service bs
                  ON bs.service_id = sv.service_id
                LEFT JOIN business_businesscustomerinfo bc
                  ON bk.booked_for_id = bc.business_customer_info_id
                LEFT JOIN business_business bz
                  ON bk.business_id = bz.business_id
                LEFT JOIN auth_user au
                  ON bc.user_id = au.id
                LEFT JOIN user_user uu
                  ON bc.user_id = uu.user_ptr_id
              WHERE date_trunc('day', bk.booked_from) BETWEEN %(booking_start_from)s::DATE AND %(booking_start_to)s::DATE
                AND bk.business_id = %(business_id)s
                AND bk.type IN ('C', 'B')
        """,
        'filters': [
            {
                'type': 'text',
                'name': 'business_id',
            },
            {
                'type': 'text',
                'name': 'booking_start_from',
                'help': "Oldest possible date booking has started. Date format: 'YYYY-MM-DD'",
            },
            {
                'type': 'text',
                'name': 'booking_start_to',
                'help': "Newest possible date booking has started. Date format: 'YYYY-MM-DD'",
            },
        ],
        'hidden': True,
    },
    {
        'label': 'at_least_10_CBs_between_two_dates',
        'sql': """
    with cust_bookings_from as(select
    bb.business_id,
    count(distinct bb.booking_id) filter (where bb.type='C') as CB

    from booking_booking bb
    where bb.created::DATE < %(date_start)s::DATE
    group by 1),

    cust_bookings_to as (select
    bb.business_id,
    count(distinct bb.booking_id) filter (where bb.type='C') as No_bookings

    from booking_booking bb
    where bb.business_id in (select cust_bookings_from.business_id from cust_bookings_from 
    where cust_bookings_from.CB < 10) and bb.created::DATE < %(date_end)s::DATE
    group by 1)

    select * from cust_bookings_to where No_bookings >=10;
    """,
        'filters': [
            {
                'type': 'text',
                'name': 'date_start',
                'value': '2018-01-01',
                'help': "Start date. Date format: 'YYYY-MM-DD'",
            },
            {
                'type': 'text',
                'name': 'date_end',
                'value': '2018-10-18',
                'help': "End date. Date format: 'YYYY-MM-DD'",
            },
        ],
        'hidden': True,
    },
    {
        'ng': True,
        'label': 'last_transactions_in_period_of_time',
        'tags': ['for:AleksandraPiotrowska'],
        'sql': """
                with subscriptions as
                (
                select distinct on (business_id) *
                from
                purchase_subscription ps
                where
                source in ('P','O','I')
                order by
                business_id, expiry desc
                ),
                ----
                b as (with a as (select distinct on (pst.business_id)
                pst.business_id,
                pst.subscription_id,
                pst.charged_on,
                ps.source as braintree_source,
                sub.source as other_source,
                bb."status",
                bb.active,
                sub.id,
                sub.expiry,
                psl.name,
                greatest(sub.expiry, pst.charged_on) as data_ostatniej_platnosci,
                extract('days' from (now()-greatest(sub.expiry, pst.charged_on))) as ile_dni
                from
                purchase_subscription ps
                left join
                purchase_subscriptiontransaction pst
                on ps.id=pst.subscription_id
                left join
                purchase_subscriptionlisting psl
                on psl.id=ps.product_id
                left join
                business_business bb
                on pst.business_id=bb.business_id
                left join
                subscriptions sub
                on sub.business_id = ps.business_id
                where
                pst.state='C'
                order by
                pst.business_id, charged_on desc)
                
                ------
                select 
                    business_id, 
                    data_ostatniej_platnosci,
                    expiry, 
                    status, 
                    active, 
                    ile_dni, 
                    braintree_source, 
                    other_source, 
                    name,
                (case when
                data_ostatniej_platnosci=expiry and other_source='O' then 'O'
                when
                data_ostatniej_platnosci=expiry and other_source='I' then 'I'
                when
                data_ostatniej_platnosci=expiry and other_source='P' then 'P'
                else 'B'
                end) as wskaznik
                
                from a
                )
                select
                business_id,
                 (case when wskaznik in ('P','I')
                    then (data_ostatniej_platnosci- interval '30 DAY')
                    else data_ostatniej_platnosci end) 
                        as ostatnia_udana_platnosc,
                status,
                active,
                (case when
                wskaznik='B' then ile_dni
                else ile_dni+30 end) as liczba_dni,
                (case when
                wskaznik='B' then braintree_source
                else other_source end) as source
                ,
                (case when
                wskaznik='O' then 'Offline'
                else name
                end) as nazwa
                from b
                where
                (
                    wskaznik='B' and 
                        (ile_dni> {{ params.minimal_no_of_days|sql_escape }} and 
                        ile_dni< {{ params.maximal_no_of_days|sql_escape }}
                        )
                )
                OR
                (
                    wskaznik in ('P','I','O') and 
                    (ile_dni+30> {{ params.minimal_no_of_days|sql_escape }} and 
                    ile_dni+30< {{ params.maximal_no_of_days|sql_escape }}));
    """,
        'filters': [
            {
                'type': 'text',
                'name': 'minimal_no_of_days',
                'value': '90',
            },
            {
                'type': 'text',
                'name': 'maximal_no_of_days',
                'value': '180',
            },
        ],
        'hidden': True,
    },
    {
        'label': 'reviews_per_business',
        'sql': """
            SELECT
                to_char(r.created, 'YYYY-MM-DD') AS "date",
                COALESCE(au.first_name, r.first_name) AS first_name,
                COALESCE(au.last_name, r.last_name) AS last_name,
                r.rank,
                r.title,
                r.review AS body,
                STRING_AGG(service_arr->>'name', ', ') AS services,
                STRING_AGG(staff_arr->>'name', ' ') AS staffers,
                to_char(r.reply_updated, 'YYYY-MM-DD') AS "date of reply",
                r.reply_content AS reply
            FROM
                reviews_review r
                    LEFT JOIN auth_user au ON au.id = r.user_id,
                jsonb_array_elements(CASE WHEN r.services = '{}' OR r.services = '[]' 
                                            OR r.services = '""'
                                            THEN '[{"name": ""}]'
                                          ELSE r.services
                                     END) AS service_arr,
                jsonb_array_elements(CASE WHEN r.staff = '{}' or r.staff = '[]' or r.staff = '""'
                                            THEN '[{"name": ""}]' 
                                        ELSE r.staff 
                                    END) AS staff_arr
            WHERE r.business_id = %(business_id)s
            GROUP BY r.review_id, au.id;
        """,
        'filters': [
            {
                'type': 'text',
                'name': 'business_id',
            },
        ],
    },
    {
        'label': 'business_3d_secure_verification_status_all',
        'sql': """
            WITH business AS(
                SELECT 
                    business_id,
                    CASE 
                        WHEN custom_data::jsonb ? 'is_3d_secure_verified' 
                    THEN
                        CAST(custom_data::json->>'is_3d_secure_verified' as BOOLEAN)
                    ELSE
                        NULL
                    END as is_3d_secure_verified
                FROM business_business
            )
            SELECT
              business.business_id,
              CASE 
                  WHEN is_3d_secure_verified = True THEN 'Successful verification'
                  WHEN is_3d_secure_verified = False THEN 'Not verified'
                  WHEN is_3d_secure_verified IS NULL THEN 'Not eligible'
                  ELSE 'Invalid status'
                END as verification_status
            FROM business
        """,
    },
]
