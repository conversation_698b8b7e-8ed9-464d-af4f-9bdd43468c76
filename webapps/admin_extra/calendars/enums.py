from django.utils.translation import gettext_lazy as _

from lib.enums import StrEnum


class ImportErrors(StrEnum):
    NO_BUSINESS_FOUND = _('Given business was not found')
    RETRIEVE_CALENDAR_ERROR = _('Cannot retrieve Google Calendar')
    RETRIEVE_STAFFER_ERROR = _('Business owner is not staffer')


class EventErrors(StrEnum):
    EXTERNAL_EVENT = _('External event')
    NOT_ALL_DATES = _('Not all dates were given')
