import abc
import typing as t

from datetime import (
    datetime,
    timedelta,
)
from decimal import (
    Decimal,
    InvalidOperation,
)

import pandas as pd
import pytz
from bs4 import BeautifulSoup
from dateutil import parser
from django import forms
from django.conf import settings

from django.utils.encoding import (
    force_str,
    smart_str,
)
from rest_framework import serializers

from country_config.enums import Country
from lib.booksy_sms import parse_phone_number

from lib.enums import (
    BaseEnum,
    StrChoicesEnum,
)
from lib.serializers import zip_code_validator
from lib.tools import tznow
from lib.validators import booksy_validate_email
from webapps import consts
from webapps.admin_extra.import_tools.enums import (
    VersumFieldNames,
    ErrorFieldsImport,
    CustomerImportFields,
    WarehouseCommoditiesImportFields,
    WholesalerCommoditiesImportFields,
    BListingImportFields,
    SubscriptionsImportFields,
    SubscriptionsEditFields,
    ProductsImportFields,
)
from webapps.admin_extra.import_tools.helpers import (
    split_full_name,
    BrazilPhoneCodeCompletion,
    parse_excel_date,
    T,
)
from webapps.admin_extra.tools import VersumException
from webapps.business.business_categories.cache import CategoryCache, SubcategoryCache
from webapps.business.enums import PriceType
from webapps.business.models import Business, BusinessCategory
from webapps.pos.models import TaxRate
from webapps.purchase.models import (
    Subscription,
    SubscriptionBuyer,
    SubscriptionDiscount,
    SubscriptionListing,
)
from webapps.structure.models import get_zipcode_set, Region

from webapps.warehouse.models import (
    Barcode,
    Commodity,
)
from webapps.warehouse.volume_measures import VolumeMeasureEnum


class ValidationFunctions:  # pylint: disable=too-many-public-methods
    """ValidationFunctions class is only namespace not real class"""

    NEGATIVE_ANSWER_VALUES = {'nie', 'no', 'not'}
    MAX_HOURS_DURATION = 23
    MAX_MINUTES_DURATION = 55
    MAX_DESCRIPTION_LEN = 5000
    MAX_SERVICE_NAME_LEN = 50
    MAX_SERVICE_CATEGORY_LEN = 50
    MAX_CELL_PHONE_LEN = 75
    MAX_EMAIL_LEN = 75
    MAX_ADDRESS_LINE = 100
    MAX_NATIONAL_IDENTITY_NUMBER_LEN = 30
    MAX_SERVICE_CODE_LEN = 64

    @staticmethod
    def set_full_name(data, full_name):
        """
        Function overwrites full_name,first_name,last_name in data dict
        Last two fields result of split of full_name
        :param data: dict object
        :param full_name: unicode name and surname of customer separated by
                          space
        :return: void
        """
        try:
            # delete full_name
            full_name_stripped, first_name, last_name = split_full_name(full_name)
            if len(full_name_stripped) > (consts.FIRST_NAME_LEN + consts.LAST_NAME_LEN + 1):
                return
            data[VersumFieldNames.CUS_FULL_NAME] = full_name_stripped
            data[VersumFieldNames.CUS_FIRST_NAME] = first_name
            data[VersumFieldNames.CUS_LAST_NAME] = last_name
        except IndexError:
            # invalid full_name
            data['warning'] = (
                ErrorFieldsImport.FIELD_ERRORS.get('warning')
                .get(VersumFieldNames.CUS_FULL_NAME)
                .format(full_name)
            )

    @staticmethod
    def set_first_or_second(data, field, name_field):
        """
        Function overwrites full_name,first_name,last_name in data dict
        Form full_name, concatenation of first_name,last_name
        :param data:  dict object
        :param field: unicode customer name or surname
        :param name_field: str possible options (first_name, last_name)
        :return: void
        """
        value = field.strip()
        # overwrite if client has first and last name
        if name_field == VersumFieldNames.CUS_FIRST_NAME:
            data[VersumFieldNames.CUS_FIRST_NAME] = (
                value if len(value) < consts.FIRST_NAME_LEN else ''
            )
        elif name_field == VersumFieldNames.CUS_LAST_NAME:
            data[VersumFieldNames.CUS_LAST_NAME] = (
                value if len(value) < consts.LAST_NAME_LEN else ''
            )

        if data.get(VersumFieldNames.CUS_FIRST_NAME) and data.get(VersumFieldNames.CUS_LAST_NAME):
            # if field has first and last name overwrite full_name
            data[VersumFieldNames.CUS_FULL_NAME] = (
                f'{str(data[VersumFieldNames.CUS_FIRST_NAME])} '
                f'{str(data[VersumFieldNames.CUS_LAST_NAME])}'
            )

        elif data.get(VersumFieldNames.CUS_FIRST_NAME):
            data[VersumFieldNames.CUS_FULL_NAME] = str(data[VersumFieldNames.CUS_FIRST_NAME])

        elif data.get(VersumFieldNames.CUS_LAST_NAME):
            data[VersumFieldNames.CUS_FULL_NAME] = str(data[VersumFieldNames.CUS_LAST_NAME])

    @staticmethod
    def set_email(data, email):
        """
        Function overwrites email in data dict
        One step validation of email performed:
            - Is email in valid format.
        After that it overwrite dict attr.
        In other case add error attribute to dict
        :param data: dict object
        :param email: str customer email.
        :return: void
        """
        email = email.strip()
        try:
            if len(email) > ValidationFunctions.MAX_EMAIL_LEN:
                return
            booksy_validate_email(email)
            data[VersumFieldNames.CUS_EMAIL] = email
        except forms.ValidationError:
            data['warning'] = (
                ErrorFieldsImport.FIELD_ERRORS.get('warning').get('invalid_email').format(email)
            )

    @staticmethod
    def delete_dot_zero(value):
        """
        When import column as text from xlsx or xls
        sometimes it adds . at the end and number for
        example if will look like:
        '502286423' => '502286423.0'

        :param value: unicode
        :return: unicode
        """
        # in order to fix that
        return value.split('.')[0]

    @staticmethod
    def set_phone(data, phone, max_len=None):
        """
        Function overwrites phone in data dict.
        :param data: dict object
        :param phone: str customer phone
        :param max_len: int. optional parameter for phone validation
        :return: void
        """
        phone = (phone if phone else '').replace('-', '').replace(' ', '')
        if phone:
            phone = ValidationFunctions.delete_dot_zero(phone)

        # Brazil dial code hack
        if settings.API_COUNTRY == Country.BR and settings.BRAZIL_PHONE_COMPLETION:
            phone = BrazilPhoneCodeCompletion.complete_br_phone(phone)

        data[VersumFieldNames.CUS_CELL_PHONE] = phone
        parsed_phone = parse_phone_number(phone)
        if max_len is None:
            max_len = ValidationFunctions.MAX_CELL_PHONE_LEN
        if not parsed_phone.is_valid:
            # invalid phone message
            data['warning'] = (
                ErrorFieldsImport.FIELD_ERRORS.get('warning').get('invalid_phone').format(phone)
            )
        else:
            if len(phone) > max_len:
                # phone len cant' be greater than 75 characters
                data['warning'] = (
                    ErrorFieldsImport.FIELD_ERRORS.get('warning')
                    .get('invalid_phone_len')
                    .format(phone)
                )
            else:
                if parsed_phone.country.lower() != settings.API_COUNTRY:
                    data[VersumFieldNames.CUS_CELL_PHONE] = parsed_phone.global_short
                else:
                    data[VersumFieldNames.CUS_CELL_PHONE] = parsed_phone.local_short

    @staticmethod
    def set_address_line(data, address):
        """
        Function overwrites address_line in data dict
        :param data: dict object
        :param address: unicode address
        :return: void
        """
        if address and len(address) < ValidationFunctions.MAX_ADDRESS_LINE:
            data[VersumFieldNames.CUS_ADDRESS_LINE] = address

    @staticmethod
    def set_business_secret_note(data, note):
        """
        Function overwrites business_secret_note in data dict.
        :param data: dict object
        :param note: business note about customer
        :return: void
        """
        data[VersumFieldNames.BUSINESS_SECRET_NOTE] = note if note else ''

    @staticmethod
    def set_zip_code(data, zip_code):
        """
        Function overwrites zip_code in data dict.
        Before that zip_code is validated.
        :param data: dict object
        :param zip_code: str zip code of customer
        :return: void
        """
        if zip_code and zip_code in get_zipcode_set():
            data[VersumFieldNames.ZIP_CODE] = zip_code
        else:
            # add warning to this field
            # this zip_code is invalid
            data['warning'] = (
                ErrorFieldsImport.FIELD_ERRORS.get('warning').get('zip_code').format(zip_code)
            )

    @staticmethod
    def set_tags(data: dict, tags: str):
        """Extracts tags from string with proper tag format."""
        from webapps.business_customer_info.bci_import import tags_split_regex

        tags_ = [
            tag if str(tag).startswith('#') else f'#{tag}' for tag in tags_split_regex.findall(tags)
        ]

        if tags and tags_:
            data[CustomerImportFields.TAGS] = tags_

        else:
            data['error'] = ErrorFieldsImport.FIELD_ERRORS.get('error').get('tags')

    @staticmethod
    def set_customer_birthday(data, birthday):
        """Function overwrites customer_birthday in data dict.
        Field type in xlsx or xls file is DateType.
        :param data: dict object
        :param birthday: str in format 'YYYY-MM-DD hh:mm:ss'
        :return: void
        """
        if birthday:
            # EXCEL format of date in sheet YYYY-MM-DD
            try:
                customer_birthday = parse_excel_date(birthday)
                if not customer_birthday:
                    data['warning'] = (
                        ErrorFieldsImport.FIELD_ERRORS.get('warning')
                        .get('birthday')
                        .format(birthday)
                    )
                elif customer_birthday and customer_birthday.year < 1900:
                    data['warning'] = (
                        ErrorFieldsImport.FIELD_ERRORS.get('warning')
                        .get('too_old_birthday')
                        .format(birthday)
                    )
                elif customer_birthday.year == tznow().year:
                    # excel automatically sets current year when no year is given
                    customer_birthday = customer_birthday.replace(year=1900)
                    data[VersumFieldNames.CUS_BIRTHDAY] = customer_birthday
                    data['warning'] = (
                        ErrorFieldsImport.FIELD_ERRORS.get('warning')
                        .get('current_year_birthday')
                        .format(birthday)
                    )
                elif customer_birthday and customer_birthday < tznow(None).date():
                    data[VersumFieldNames.CUS_BIRTHDAY] = customer_birthday
                else:
                    # date in future
                    data['warning'] = (
                        ErrorFieldsImport.FIELD_ERRORS.get('warning')
                        .get('future_birthday')
                        .format(birthday)
                    )

            except IndexError:
                data['warning'] = (
                    ErrorFieldsImport.FIELD_ERRORS.get('warning').get('birthday').format(birthday)
                )

    @staticmethod
    def parse_date_from_text(orig_date, check_epoch=True):
        """Parse date in str or unicode to a naive datetime object.
           Removes timezone offset if present.
        :param orig_date: str or unicode
        :param check_epoch: bool, default True - whether to check against the epoch (1970-01-01)
        :return: datetime object
        """
        try:
            result = parser.parse(orig_date, ignoretz=True)

        except (ValueError, TypeError) as e:
            raise ValueError(
                f"Could not parse date '{orig_date}'. "
                f"Please ensure it is in a valid date format. Error: {e}"
            ) from e

        if check_epoch:
            epoch = datetime.fromtimestamp(0)
            if result <= epoch:
                raise ValueError(f"Date cannot be earlier or equal to the epoch: {result}")

        return result

    @staticmethod
    def set_versum_birthday(data, birthday):
        """
        Function overwrites unsubscribe in data dict.
        :param data: dict object
        :param birthday: unicode in form 'YYYY-MM-DD'
        :return: void
        """
        # optional field
        if birthday:
            try:
                ValidationFunctions.parse_date_from_text(birthday, check_epoch=False)
                data[VersumFieldNames.CUS_BIRTHDAY] = birthday
            except (IndexError, ValueError):
                data['warning'] = (
                    ErrorFieldsImport.FIELD_ERRORS.get('warning')
                    .get(
                        'birthday',
                    )
                    .format(birthday)
                )

    @staticmethod
    def set_customer_discount(data, discount):
        """
        Function overwrites discount in data dict.
        :param data: dict object
        :param discount: unicode
        :return: void
        """

        discount = ValidationFunctions.delete_dot_zero(discount)
        if not discount.isdigit():
            data['warning'] = (
                ErrorFieldsImport.FIELD_ERRORS.get('warning').get('discount').format(discount)
            )
            return
        discount = int(discount)
        if discount < 0:
            data['warning'] = (
                ErrorFieldsImport.FIELD_ERRORS.get('warning')
                .get('discount_too_small')
                .format(discount)
            )
        elif discount > 100:
            data['warning'] = (
                ErrorFieldsImport.FIELD_ERRORS.get('warning')
                .get('discount_too_big')
                .format(discount)
            )
        else:
            data[VersumFieldNames.CUS_DISCOUNT] = discount

    @staticmethod
    def set_unsubscribe(data, unsubscribe):
        """
        Function overwrites unsubscribe in data dict.
        :param data: dict object
        :param unsubscribe: unicode
        :return: void
        """
        data[VersumFieldNames.UNSUBSCRIBE] = (
            unsubscribe.lower() in ValidationFunctions.NEGATIVE_ANSWER_VALUES
        )

    @staticmethod
    def compose_set_address_line(data, city):
        """
        Function overwrites address_line in data dict.
        :param data: dict object
        :param city: unicode
        :return: void
        """
        # Try collect all possible information
        # Zip_code appear in address line because it can fail validation
        # and then we lose this info
        result = []
        street = data.get(VersumFieldNames.CUS_STREET)
        zip_code = data.get(VersumFieldNames.ZIP_CODE)
        max_len_one_field = ValidationFunctions.MAX_ADDRESS_LINE / 3
        if zip_code and len(zip_code) < max_len_one_field:
            result.append(str(zip_code))
        if city and len(city) < max_len_one_field:
            result.append(str(city))
        if street and len(street) < max_len_one_field:
            result.append(str(street))

        data[VersumFieldNames.CUS_ADDRESS_LINE] = ', '.join(result) if result else ''

    @staticmethod
    def set_id_customer(data, id_customer):
        """
        Function overwrites id_customer in data dict.
        :param data: dict object
        :param id_customer: unicode
        :return: void
        """

        potential_id = ValidationFunctions.delete_dot_zero(id_customer)
        if not potential_id.isdigit():
            return
        potential_id = int(potential_id)
        if potential_id > 0:
            data[VersumFieldNames.ID_CUS] = potential_id

    @staticmethod
    def append_business_secret_note(data, loyalty_info):
        """
        Function overwrites business_secret_note in data dict.
        :param data: dict object
        :param loyalty_info: unicode
        :return: void
        """
        result = ''
        note = data.get(VersumFieldNames.BUSINESS_SECRET_NOTE)
        if loyalty_info and note:
            result = f'{note}, Loyalty points: {loyalty_info}'
        elif loyalty_info:
            result = f'Loyalty points: {loyalty_info}'

        data[VersumFieldNames.BUSINESS_SECRET_NOTE] = result

    @staticmethod
    def set_id_service(data, id_service):
        """
        Function overwrites id_customer in data dict.
        :param data: dict object
        :param id_service: unicode
        :return: void
        """
        potential_id = ValidationFunctions.delete_dot_zero(id_service)
        if not potential_id.isdigit():
            data[VersumFieldNames.ID_SERVICE] = None
        else:
            potential_id = int(potential_id)
            data[VersumFieldNames.ID_SERVICE] = potential_id

    @staticmethod
    def set_service_name(data, name):
        """
        Function overwrites name in data dict.
        :param data: dict object
        :param name: unicode service name
        :return: void
        """
        # max number of ch in description field
        # delete extra spaces
        name = name.strip()
        if name:
            if len(name) < ValidationFunctions.MAX_SERVICE_NAME_LEN:
                data[VersumFieldNames.SERVICE_NAME] = str(name)
            else:
                ValidationFunctions.add_to_description(data, name)
                cut_name = name[: ValidationFunctions.MAX_SERVICE_NAME_LEN]
                data[VersumFieldNames.SERVICE_NAME] = str(cut_name)

    @staticmethod
    def set_service_code(data, service_code):
        """
        Function overwrites name in data dict.
        :param data: dict object
        :param service_code: unicode service code
        :return: void
        """
        service_code = service_code.strip()
        if service_code:
            if len(service_code) <= ValidationFunctions.MAX_SERVICE_CODE_LEN:
                data[VersumFieldNames.SERVICE_CODE] = str(service_code)
            else:
                raise VersumException('Service code too long. Max length is 64 characters.')

    @staticmethod
    def add_to_description(data, field):
        if data.get(VersumFieldNames.DESCRIPTION):
            data[VersumFieldNames.DESCRIPTION] = f'{field} {data.get(VersumFieldNames.DESCRIPTION)}'
        else:
            data[VersumFieldNames.DESCRIPTION] = field

    @staticmethod
    def set_service_category(data, category):
        """
        Function overwrites category in data dict.
        :param data: dict object
        :param category: unicode service category name
        :return: void
        """
        # TODO possible match with existing categories of business
        if category:
            if len(category) < ValidationFunctions.MAX_SERVICE_CATEGORY_LEN:
                data[VersumFieldNames.CATEGORY_NAME] = str(category)
            else:
                cut_category = category[: ValidationFunctions.MAX_SERVICE_CATEGORY_LEN]
                data[VersumFieldNames.CATEGORY_NAME] = str(cut_category)
        else:
            # service will be assigned to Uncategorized
            data[VersumFieldNames.CATEGORY_NAME] = None

    @staticmethod
    def convert_to_decimal(value):
        try:
            return Decimal(value)
        except InvalidOperation:
            return Decimal(0)

    @staticmethod
    def is_zero(values):
        return int(values[0]) == 0 and int(values[-1]) == 0

    @staticmethod
    def set_gdpr_web_communication_agreement(data, value):
        ValidationFunctions.set_boolean(
            data,
            value,
            VersumFieldNames.GDPR_WEB_COMMUNICATION_AGREEMENT,
        )

    @staticmethod
    def set_gdpr_processing_consent(data, value):
        ValidationFunctions.set_boolean(
            data,
            value,
            VersumFieldNames.GDPR_PROCESSING_CONSENT,
        )

    @staticmethod
    def set_national_identity_number(data, value):
        """
        Function overwrites national_identity_number to data dict.
        :param data: dict object
        :param value: personal id number as string
        :return: void
        """
        value = value.strip()
        if value:
            max_len = ValidationFunctions.MAX_NATIONAL_IDENTITY_NUMBER_LEN
            value = value[:max_len]
            data[VersumFieldNames.NATIONAL_IDENTITY_NUMBER] = str(value)

    @staticmethod
    def set_boolean(data, value, field_name):
        """
        :param data: dict object
        :param value: unicode or str
        :param field_name: unicode or str
        :return:
        """
        val = ValidationFunctions.delete_dot_zero(value)
        if val.isdigit():
            data[field_name] = bool(int(val))
        elif not val:
            data[field_name] = None
        else:
            data[field_name] = None
            data['warning'] = (
                ErrorFieldsImport.FIELD_ERRORS.get('warning')
                .get('invalid_boolean')
                .format(value, field_name)
            )

    @staticmethod
    def set_service_price(data, price):
        """
        Function overwrites price in data dict. and service_variant_type
        to PriceType.FIXED or PriceType.FREE
        :param data: dict object
        :param price: unicode  or str
        :return: void
        """

        price = (price if price else '').replace('-', '').replace(' ', '')
        price = force_str(price, encoding='ascii')
        converted_price = ValidationFunctions.convert_to_decimal(price)
        if converted_price:
            data[VersumFieldNames.PRICE] = converted_price
            # validator self.set_type_to_starts_at can set type earlier
            # do not overwrite it
            # TODO refactor with order of call validators call
            if data.get(VersumFieldNames.SERVICE_VARIANT_TYPE) is None:
                data[VersumFieldNames.SERVICE_VARIANT_TYPE] = PriceType.FIXED
        else:
            data[VersumFieldNames.PRICE] = Decimal('0.0')
            data[VersumFieldNames.SERVICE_VARIANT_TYPE] = PriceType.FREE

    @staticmethod
    def set_service_final_price(data, price):
        """
        Function writes final_price in data dict
        :param data: dict object
        :param price: unicode  or str
        :return: void
        """

        price = (price if price else '').replace('-', '').replace(' ', '')
        price = force_str(price, encoding='ascii')
        converted_price = ValidationFunctions.convert_to_decimal(price)
        if converted_price:
            data[VersumFieldNames.FINAL_PRICE] = converted_price
        else:
            data[VersumFieldNames.FINAL_PRICE] = Decimal('0.0')

    @staticmethod
    def valid_duration(duration):
        return timedelta(minutes=duration) < timedelta(
            hours=ValidationFunctions.MAX_HOURS_DURATION,
            minutes=ValidationFunctions.MAX_MINUTES_DURATION,
        )

    @staticmethod
    def set_time(data, time):
        """
        Function overwrites time in data dict.
        :param data: dict object
        :param time: unicode  or str
        :return: void
        """

        time = ValidationFunctions.delete_dot_zero(time)
        time = (time if time else '').replace('-', '').replace(' ', '').encode('ascii', 'ignore')
        if time.isdigit() and ValidationFunctions.valid_duration(int(time)):
            data[VersumFieldNames.TIME] = int(time)

    @staticmethod
    def set_type_to_starts_at(data, max_price):
        """
        Function overwrites price in data dict. and service_variant_type
        to PriceType.FIXED or PriceType.FREE
        :param data: dict object
        :param max_price: unicode  or str
        :return: void
        """
        max_price = (max_price if max_price else '').replace('-', '').replace(' ', '')
        if ValidationFunctions.convert_to_decimal(max_price):
            data[VersumFieldNames.SERVICE_VARIANT_TYPE] = PriceType.STARTS_AT

    @staticmethod
    def set_service_description(data, description):
        """
        Function overwrites description in data dict.
        :param data: dict object
        :param description: unicode service description
        :return: void
        """
        # max number of ch in description field
        if description and len(description) < ValidationFunctions.MAX_DESCRIPTION_LEN:
            soup = BeautifulSoup(description, 'html.parser')
            data[VersumFieldNames.DESCRIPTION] = str(soup.text)

    @staticmethod
    def set_id_booking(data, id_booking):
        """
        Function overwrites id_booking in data dict.
        :param data: dict object
        :param id_booking: unicode
        :return: void
        """
        potential_id = ValidationFunctions.delete_dot_zero(id_booking)
        if potential_id.isdigit():
            data[VersumFieldNames.ID_BOOKING] = int(potential_id)
        else:
            # versum uses negative id for services out of
            # business service scope
            data[VersumFieldNames.ID_BOOKING] = None

    @staticmethod
    def set_staffer_full_name(data, full_name):
        """
        Function overwrites staffer_full_name in data dict
        :param data: dict object
        :param full_name: unicode
        :return: void
        """
        data[VersumFieldNames.STAFFER_FULL_NAME] = full_name.strip()

    @staticmethod
    def set_booked_from(data, booked_from):
        """
        Function overwrites booked_from in data dict
        :param data: dict object
        :param booked_from:  str or unicode.'
        :return: void
        """
        try:
            booked_from = ValidationFunctions.parse_date_from_text(booked_from)
            data[VersumFieldNames.BOOKED_FROM] = booked_from
        except (IndexError, ValueError) as e:
            data['error'] = (
                ErrorFieldsImport.FIELD_ERRORS.get('error')
                .get('error_parse_date')
                .format('booked_from', booked_from, e)
            )

    @staticmethod
    def set_booked_till(data, booked_till):
        """
        Function overwrites booked_till in data dict
        :param data: dict object
        :param booked_till: str or unicode.
        :return: void
        """
        try:
            booked_till = ValidationFunctions.parse_date_from_text(booked_till)
            data[VersumFieldNames.BOOKED_TILL] = booked_till

        except (IndexError, ValueError) as e:
            data['error'] = (
                ErrorFieldsImport.FIELD_ERRORS.get('error')
                .get('error_parse_date')
                .format('booked_till', booked_till, e)
            )

    @staticmethod
    def set_added_by(data, added_by):
        """
        Function overwrites added_by in data dict
        :param data: dict object
        :param added_by: str or unicode
        :return: void
        """
        # optional field
        if added_by:
            data[VersumFieldNames.ADDED_BY] = added_by

    @staticmethod
    def set_created_at(data, created_at):
        """
        Function overwrites created_at in data dict
        :param data: dict object
        :param created_at: str or unicode. Date in format YYYY-MM-DD hh:mm'
        :return: void
        """
        # optional field
        if created_at:
            try:
                ValidationFunctions.parse_date_from_text(created_at)
                data[VersumFieldNames.CREATED_AT] = created_at
            except IndexError as e:
                data['warning'] = (
                    ErrorFieldsImport.FIELD_ERRORS.get('warning')
                    .get(
                        'invalid_data',
                    )
                    .format(e)
                )

    @staticmethod
    def set_last_modified_at(data, last_modified_at):
        """
        Function overwrites last_modified_at in data dict
        :param data: dict object
        :param last_modified_at: str or unicode.
        Date in format YYYY-MM-DD hh:mm'
        :return: void
        """
        # optional field
        if last_modified_at:
            try:
                ValidationFunctions.parse_date_from_text(last_modified_at)
                data[VersumFieldNames.LAST_MODIFIED_AT] = last_modified_at
            except IndexError as e:
                data['warning'] = (
                    ErrorFieldsImport.FIELD_ERRORS.get('warning')
                    .get(
                        'invalid_data',
                    )
                    .format(e)
                )

    @staticmethod
    def set_booking_business_note(data, booking_biz_note):
        """
        Function overwrites booking_business_note in data dict
        :param data: dict object
        :param booking_biz_note: str or unicode.
        :return: void
        """
        # optionally price and other information might be inserted
        max_len = consts.BUSINESS_SECRET_NOTE__MAX_LENGTH - 20
        if booking_biz_note and len(booking_biz_note) < max_len:
            data[VersumFieldNames.BOOKING_BUSINESS_NOTE] = booking_biz_note

    @staticmethod
    def set_finished_at(data, finished_at):
        """
        Function overwrites booking_business_note in data dict
        :param data: dict object
        :param finished_at: str or unicode. Date in format YYYY-MM-DD hh:mm'
        :return: void
        """
        # optional field
        if finished_at:
            try:
                ValidationFunctions.parse_date_from_text(finished_at)
                data[VersumFieldNames.BOOKING_FINISHED_AT] = finished_at
            except IndexError as e:
                data['warning'] = (
                    ErrorFieldsImport.FIELD_ERRORS.get('warning')
                    .get(
                        'invalid_data',
                    )
                    .format(e)
                )

    @staticmethod
    def set_finished_by(data, finished_by):
        """
        Function overwrites booking_business_note in data dict
        :param data: dict object
        :param finished_by: str or unicode.
        :return: void
        """
        # optional field
        if finished_by:
            data[VersumFieldNames.BOOKING_FINISHED_BY] = finished_by


class BaseCommoditiesAndProductsValidationFunctions:
    """
    BaseCommoditiesAndProductsValidationFunctions class is only namespace
    not real class
    """

    MAX_NAME_LEN = 200
    MAX_CODE_LEN = 50
    MAX_BARCODE_LEN = 50
    MAX_CATEGORY_LEN = 200
    MAX_SUPPLIER_LEN = 200
    MAX_BRAND_LEN = 100
    DEFAULT_VOLUME_UNIT = VolumeMeasureEnum.PIECE
    DEFAULT_PACK_CAPACITY = 1
    IMPORT_FIELDS_CLASS = None
    REQUIRED_ERROR_CODE = ''

    @staticmethod
    def append_warning(data: dict, warning: str) -> None:
        if not data.get('warnings'):
            data['warnings'] = []
        data['warnings'].append(warning)

    @classmethod
    def set_name(cls, data: dict, name: t.AnyStr) -> None:
        """Function overwrites name in data dict."""
        # max number of ch in commodity name field
        # delete extra spaces
        name = name.strip()
        if name:
            data[cls.IMPORT_FIELDS_CLASS.NAME] = str(name)[: cls.MAX_NAME_LEN]

        if not data.get(cls.IMPORT_FIELDS_CLASS.NAME):
            data['error'] = ErrorFieldsImport.FIELD_ERRORS.get('error').get(cls.REQUIRED_ERROR_CODE)

    @classmethod
    def set_code(cls, data: dict, code: str, business: Business) -> None:
        """Function overwrites code in data dict."""
        # max number of ch in commodity code field
        # delete extra spaces
        code = code.strip()
        if code:
            if len(code) < cls.MAX_CODE_LEN:
                data[cls.IMPORT_FIELDS_CLASS.CODE] = str(code)
            else:
                cut_code = code[: cls.MAX_CODE_LEN]
                data[cls.IMPORT_FIELDS_CLASS.CODE] = str(cut_code)
            if business.commodities.filter(
                product_code=data[cls.IMPORT_FIELDS_CLASS.CODE],
            ).exists():
                data['error'] = ErrorFieldsImport.FIELD_ERRORS.get('error').get(
                    'code_already_exists'
                )

        if not data.get(cls.IMPORT_FIELDS_CLASS.CODE):
            data['error'] = ErrorFieldsImport.FIELD_ERRORS.get('error').get(cls.REQUIRED_ERROR_CODE)

    @classmethod
    def set_type(cls, data: dict, commodity_type: t.AnyStr) -> None:
        """Function overwrites type in data dict."""
        commodity_type = commodity_type.strip().lower()

        for option_key, _ in Commodity.PRODUCT_TYPE_CHOICES:
            if commodity_type == option_key:
                data[cls.IMPORT_FIELDS_CLASS.TYPE] = str(commodity_type)
                continue

        if not data.get(cls.IMPORT_FIELDS_CLASS.TYPE):
            data[cls.IMPORT_FIELDS_CLASS.TYPE] = Commodity.TYPE_RETAIL
            cls.append_warning(
                data,
                ErrorFieldsImport.FIELD_ERRORS.get('warning')
                .get('commodity_type')
                .format(commodity_type),
            )

    @classmethod
    def set_net_price(cls, data: dict, price: t.AnyStr) -> None:
        """Function overwrites net price in data dict."""
        price = (price if price else '').replace('-', '').replace(' ', '')
        price = force_str(price, encoding='ascii')
        if not price:
            data[cls.IMPORT_FIELDS_CLASS.NET_PRICE] = None
            return
        converted_price = ValidationFunctions.convert_to_decimal(price)
        data[cls.IMPORT_FIELDS_CLASS.NET_PRICE] = converted_price

    @classmethod
    def set_tax_rate(cls, data: dict, tax_rate: t.AnyStr, business: Business) -> None:
        """Function overwrites tax rate in data dict."""
        original_tax_rate = tax_rate
        tax_rate = (tax_rate if tax_rate else '').replace('-', '').replace(' ', '').rstrip('%')
        tax_rate = force_str(tax_rate, encoding='ascii')
        converted_tax_rate = ValidationFunctions.convert_to_decimal(tax_rate)

        available_tax_rate = TaxRate.objects.filter(
            pos=business.pos,
            rate=converted_tax_rate if converted_tax_rate != 0 else None,
        )

        if available_tax_rate.exists():
            available_tax_rate = available_tax_rate.first()
            data[cls.IMPORT_FIELDS_CLASS.TAX_RATE] = available_tax_rate.rate
            return

        data['error'] = ErrorFieldsImport.FIELD_ERRORS.get('error').get('tax_rate').format(tax_rate)
        data[cls.IMPORT_FIELDS_CLASS.TAX_RATE] = original_tax_rate

    @classmethod
    def set_barcode_type(cls, data: dict, barcode_type: t.AnyStr) -> None:
        """Function overwrites barcode type in data dict."""
        barcode_type = barcode_type.strip().lower()

        if barcode_type:
            for option_key, _ in Barcode.BARCODE_TYPES:
                if barcode_type == option_key:
                    data[cls.IMPORT_FIELDS_CLASS.BARCODE_TYPE] = str(barcode_type)
                    continue

            if not data.get(cls.IMPORT_FIELDS_CLASS.BARCODE_TYPE):
                data[cls.IMPORT_FIELDS_CLASS.BARCODE_TYPE] = None
                cls.append_warning(
                    data,
                    ErrorFieldsImport.FIELD_ERRORS.get('warning')
                    .get('commodity_barcode_type')
                    .format(barcode_type),
                )

    @classmethod
    def set_barcode(cls, data: dict, barcode: t.AnyStr) -> None:
        """Function overwrites barcode in data dict."""
        # max number of ch in commodity barcode field
        # delete extra spaces
        barcode = barcode.strip()
        if barcode:
            if len(barcode) < cls.MAX_BARCODE_LEN:
                data[cls.IMPORT_FIELDS_CLASS.BARCODE] = str(barcode)
            else:
                data[cls.IMPORT_FIELDS_CLASS.BARCODE] = None
                cls.append_warning(
                    data,
                    ErrorFieldsImport.FIELD_ERRORS.get('warning')
                    .get('commodity_barcode')
                    .format(barcode),
                )

    @classmethod
    def set_supplier(cls, data: dict, supplier: t.AnyStr) -> None:
        """Function overwrites supplier in data dict."""
        # max number of ch in commodity category field
        # delete extra spaces
        supplier = supplier.strip()
        if supplier:
            data[cls.IMPORT_FIELDS_CLASS.SUPPLIER] = str(supplier)[: cls.MAX_SUPPLIER_LEN]

    @classmethod
    def set_brand(cls, data: dict, brand: t.AnyStr) -> None:
        """Function overwrites brand in data dict."""
        # max number of ch in commodity category field
        # delete extra spaces
        brand = brand.strip()
        if brand:
            data[cls.IMPORT_FIELDS_CLASS.BRAND] = str(brand)[: cls.MAX_BRAND_LEN]

    @classmethod
    def set_description(cls, data: dict, description: t.AnyStr) -> None:
        """Function overwrites description in data dict."""
        description = description.strip()
        data[cls.IMPORT_FIELDS_CLASS.DESCRIPTION] = str(description)

    @classmethod
    def set_category(cls, data: dict, category: t.AnyStr) -> None:
        """Function overwrites name in data dict."""
        # max number of ch in commodity category field
        # delete extra spaces
        category = category.strip()
        if category:
            data[cls.IMPORT_FIELDS_CLASS.CATEGORY] = category[: cls.MAX_CATEGORY_LEN]

    @classmethod
    def set_volume_unit(
        cls, data: dict, volume_unit: str, available_units: t.Dict[str, str]
    ) -> None:
        """Function overwrites volume unit in data dict."""
        volume_unit = volume_unit.strip().lower()

        if not volume_unit:
            volume_unit = cls.DEFAULT_VOLUME_UNIT.lower()

        if available_units.get(volume_unit):
            data[cls.IMPORT_FIELDS_CLASS.VOLUME_UNIT] = volume_unit
            return

        data['error'] = (
            ErrorFieldsImport.FIELD_ERRORS.get('error')
            .get('volume_unit')
            .format(volume_unit, cls.IMPORT_FIELDS_CLASS.VOLUME_UNIT)
        )
        data[cls.IMPORT_FIELDS_CLASS.VOLUME_UNIT] = volume_unit

    @classmethod
    def set_pack_capacity(cls, data: dict, capacity: str) -> None:
        """Function overwrites total pack capacity in data dict."""
        if not capacity:
            data[cls.IMPORT_FIELDS_CLASS.TOTAL_PACK_CAPACITY] = cls.DEFAULT_PACK_CAPACITY
            return

        potential_capacity = ValidationFunctions.delete_dot_zero(capacity)
        if potential_capacity.isdigit() and int(potential_capacity) > 0:
            data[cls.IMPORT_FIELDS_CLASS.TOTAL_PACK_CAPACITY] = int(potential_capacity)
            return

        data['error'] = (
            ErrorFieldsImport.FIELD_ERRORS.get('error')
            .get('total_pack_capacity')
            .format(potential_capacity)
        )

    @classmethod
    def set_remaining_volume(cls, data: dict, remaining_volume: str) -> None:
        """Function overwrites remaining_volume in data dict."""
        remaining_volume = remaining_volume.strip()
        if not remaining_volume:
            return

        try:
            remaining_volume = int(remaining_volume)
        except ValueError:
            data['error'] = ErrorFieldsImport.FIELD_ERRORS.get('error').get('remaining_volume')
        data[cls.IMPORT_FIELDS_CLASS.REMAINING_VOLUME] = remaining_volume

    @classmethod
    def set_warehouse(cls, data: dict, warehouse: t.AnyStr):
        """Function overwrites warehouse in data dict."""
        warehouse = warehouse.strip()
        data[cls.IMPORT_FIELDS_CLASS.WAREHOUSE] = str(warehouse)


class WarehouseCommoditiesValidationFunctions(BaseCommoditiesAndProductsValidationFunctions):
    IMPORT_FIELDS_CLASS = WarehouseCommoditiesImportFields
    REQUIRED_ERROR_CODE = 'has_no_required'


class ProductsValidationFunctions(BaseCommoditiesAndProductsValidationFunctions):
    IMPORT_FIELDS_CLASS = ProductsImportFields
    REQUIRED_ERROR_CODE = 'has_no_required_products_importer'

    @classmethod
    def set_catalog_name(cls, data: dict, catalog_name: t.AnyStr) -> None:
        """Function overwrites catalog_name in data dict."""
        if catalog_name:
            catalog_name = catalog_name.strip()
            data[cls.IMPORT_FIELDS_CLASS.CATALOG_NAME] = catalog_name

    @classmethod
    def set_other_categories(cls, data: dict, other_categories: t.AnyStr) -> None:
        """Function overwrites other_categories in data dict."""
        if other_categories:
            data[cls.IMPORT_FIELDS_CLASS.OTHER_CATEGORIES] = [
                cat[: cls.MAX_CATEGORY_LEN]
                for category in other_categories.split(",")
                if (cat := category.strip())
            ]

    @classmethod
    def set_volume_unit(
        cls, data: dict, volume_unit: str, available_units: t.Dict[str, str]
    ) -> None:
        """Function overwrites volume unit in data dict."""
        volume_unit = volume_unit.strip().lower()

        if available_units.get(volume_unit):
            data[cls.IMPORT_FIELDS_CLASS.VOLUME_UNIT] = volume_unit
            return

        data['error'] = (
            ErrorFieldsImport.FIELD_ERRORS.get('error')
            .get('volume_unit')
            .format(volume_unit, cls.IMPORT_FIELDS_CLASS.VOLUME_UNIT)
        )
        data[cls.IMPORT_FIELDS_CLASS.VOLUME_UNIT] = volume_unit

    @classmethod
    def set_pack_capacity(cls, data: dict, capacity: str) -> None:
        """Function overwrites total pack capacity in data dict."""
        potential_capacity = ValidationFunctions.delete_dot_zero(capacity)

        if potential_capacity and potential_capacity.isdigit() and int(potential_capacity) > 0:
            data[cls.IMPORT_FIELDS_CLASS.TOTAL_PACK_CAPACITY] = int(potential_capacity)
            return

        data['error'] = (
            ErrorFieldsImport.FIELD_ERRORS.get('error')
            .get('total_pack_capacity')
            .format(potential_capacity)
        )
        data[cls.IMPORT_FIELDS_CLASS.TOTAL_PACK_CAPACITY] = capacity

    @classmethod
    def set_tax_rate(cls, data: dict, tax_rate: t.AnyStr, business: Business) -> None:
        """Function overwrites tax rate in data dict."""
        if not tax_rate:
            cls._set_empty_tax_rate(data, business)
            return

        original_tax_rate = tax_rate

        tax_rate = (tax_rate if tax_rate else '').replace(' ', '').rstrip('%')
        tax_rate = force_str(tax_rate, encoding='ascii')

        converted_tax_rate = cls._convert_to_decimal(tax_rate)

        if converted_tax_rate is None or not 0 <= converted_tax_rate <= 100:
            data['error'] = (
                ErrorFieldsImport.FIELD_ERRORS.get('error').get('tax_rate').format(tax_rate)
            )
            data[cls.IMPORT_FIELDS_CLASS.TAX_RATE] = original_tax_rate
        else:
            available_tax_rate, _ = TaxRate.objects.get_or_create(
                pos=business.pos,
                rate=converted_tax_rate,
            )
            data[cls.IMPORT_FIELDS_CLASS.TAX_RATE] = available_tax_rate.rate

    @classmethod
    def _set_empty_tax_rate(cls, data: dict, business: Business) -> None:
        """Gets or creates blank tax rate for business and sets it in data dict"""
        available_tax_rate, _ = TaxRate.objects.get_or_create(
            pos=business.pos,
            rate=None,
        )

        data[cls.IMPORT_FIELDS_CLASS.TAX_RATE] = available_tax_rate.rate

    @staticmethod
    def _convert_to_decimal(value: str):
        try:
            if float(value) < 0:
                return None
            return Decimal(value).quantize(Decimal('.01'))
        except (InvalidOperation, ValueError):
            return None


class WholesalerCommoditiesValidationFunctions:
    """
    WholesalerCommoditiesValidationFunctions class is only namespace
    not real class
    """

    MAX_NAME_LEN = 200
    MAX_CODE_LEN = 50
    MAX_BARCODE_LEN = 50
    MAX_CAT_LEN = 200
    MAX_VOLUME_UNIT_LEN = 50

    LOREAL_VOLUME_UNITS = {
        'pce': 'piece',
        'mlt': 'milliliter',
        'grm': 'gram',
        'n/a': 'piece',
    }

    @staticmethod
    def append_warning(data, warning):
        if not data.get('warnings'):
            data['warnings'] = []
        data['warnings'].append(warning)

    @staticmethod
    def set_full_category(data, field, name_field):
        """
        Function overwrites name in data dict.
        :param data: dict object
        :param category: unicode commodity category
        :return: void
        """
        # max number of ch in commodity category field
        # delete extra spaces
        value = field.strip()
        if value:
            if len(value) < WholesalerCommoditiesValidationFunctions.MAX_CAT_LEN:
                data[name_field] = str(value)
            else:
                data[name_field] = str(
                    value[: WholesalerCommoditiesValidationFunctions.MAX_CAT_LEN]
                )

            if data.get(WholesalerCommoditiesImportFields.CATEGORY) and data.get(
                WholesalerCommoditiesImportFields.SUBCATEGORY
            ):
                data[WholesalerCommoditiesImportFields.FULL_CATEGORY] = (
                    f'{str(data[WholesalerCommoditiesImportFields.CATEGORY])} '
                    f'{str(data[WholesalerCommoditiesImportFields.SUBCATEGORY])}'
                )

            elif data.get(WholesalerCommoditiesImportFields.CATEGORY):
                data[WholesalerCommoditiesImportFields.FULL_CATEGORY] = str(
                    data[WholesalerCommoditiesImportFields.CATEGORY]
                )
            elif data.get(WholesalerCommoditiesImportFields.SUBCATEGORY):
                data[WholesalerCommoditiesImportFields.FULL_CATEGORY] = str(
                    data[WholesalerCommoditiesImportFields.SUBCATEGORY]
                )

            if (
                len(data[WholesalerCommoditiesImportFields.FULL_CATEGORY])
                >= WholesalerCommoditiesValidationFunctions.MAX_CAT_LEN
            ):
                data[WholesalerCommoditiesImportFields.FULL_CATEGORY] = data[
                    WholesalerCommoditiesImportFields.FULL_CATEGORY
                ][: WholesalerCommoditiesValidationFunctions.MAX_CAT_LEN]

    @staticmethod
    def set_name(data, name):
        """
        Function overwrites name in data dict.
        :param data: dict object
        :param name: unicode commodity name
        :return: void
        """
        name = name.strip()
        if name:
            if len(name) < WholesalerCommoditiesValidationFunctions.MAX_NAME_LEN:
                data[WholesalerCommoditiesImportFields.NAME] = str(name)
            else:
                cut_name = name[: WholesalerCommoditiesValidationFunctions.MAX_NAME_LEN]
                data[WholesalerCommoditiesImportFields.NAME] = str(cut_name)

        if not data.get(WholesalerCommoditiesImportFields.NAME):
            data['error'] = ErrorFieldsImport.FIELD_ERRORS.get('error').get(
                'wholesaler_commodity_has_no_required'
            )

    @staticmethod
    def set_product_code(data, code):
        """
        Function overwrites code in data dict.
        :param data: dict object
        :param code: unicode commodity code
        :return: void
        """
        code = code.strip()
        if code:
            if len(code) < WholesalerCommoditiesValidationFunctions.MAX_CODE_LEN:
                data[WholesalerCommoditiesImportFields.PRODUCT_CODE] = str(code)
            else:
                cut_code = code[: WholesalerCommoditiesValidationFunctions.MAX_CODE_LEN]
                data[WholesalerCommoditiesImportFields.PRODUCT_CODE] = str(cut_code)

        if not data.get(WholesalerCommoditiesImportFields.PRODUCT_CODE):
            data['error'] = ErrorFieldsImport.FIELD_ERRORS.get('error').get(
                'wholesaler_commodity_has_no_required'
            )

    @staticmethod
    def set_net_price(data, price):
        """
        Function overwrites net price in data dict.
        :param data: dict object
        :param price: unicode  or str
        :return: void
        """
        price = (price if price else '').replace('-', '').replace(' ', '')
        price = force_str(price, encoding='ascii')
        converted_price = ValidationFunctions.convert_to_decimal(price)
        if converted_price:
            data[WholesalerCommoditiesImportFields.NET_PRICE] = converted_price
        else:
            data[WholesalerCommoditiesImportFields.NET_PRICE] = Decimal('0.0')

    @staticmethod
    def set_tax_rate(data, tax_rate, available_rates):
        """
        Function overwrites tax rate in data dict.
        :param data: dict object
        :param tax_rate: unicode  or str
        :return: void
        """
        tax_rate = (tax_rate if tax_rate else '').replace('-', '').replace(' ', '').rstrip('%')
        tax_rate = force_str(tax_rate, encoding='ascii')
        if tax_rate:
            converted_tax_rate = ValidationFunctions.convert_to_decimal(tax_rate)
        else:
            converted_tax_rate = None

        if available_rates.get(converted_tax_rate):
            data[WholesalerCommoditiesImportFields.TAX_RATE] = converted_tax_rate
            return
        data['error'] = ErrorFieldsImport.FIELD_ERRORS.get('error').get('tax_rate').format(tax_rate)

    @staticmethod
    def set_barcode(data, barcode):
        """
        Function overwrites barcode in data dict.
        :param data: dict object
        :param barcode: unicode commodity barcode
        :return: void
        """
        barcode = barcode.strip()
        if barcode:
            if len(barcode) < WholesalerCommoditiesValidationFunctions.MAX_BARCODE_LEN:
                data[WholesalerCommoditiesImportFields.BARCODE] = str(barcode)
            else:
                data[WholesalerCommoditiesImportFields.BARCODE] = None
                WholesalerCommoditiesValidationFunctions.append_warning(
                    data,
                    ErrorFieldsImport.FIELD_ERRORS.get('warning')
                    .get('commodity_barcode')
                    .format(barcode),
                )

    @staticmethod
    def set_barcode_type(data, barcode_type):
        """
        Function overwrites barcode type in data dict.
        :param data: dict object
        :param barcode_type: unicode commodity barcode type
        :return: void
        """
        barcode_type = barcode_type.strip().lower()

        if barcode_type:
            for option_key, _ in Barcode.BARCODE_TYPES:
                if barcode_type == option_key:
                    data[WholesalerCommoditiesImportFields.BARCODE_TYPE] = str(barcode_type)
                    continue

            if not data.get(WholesalerCommoditiesImportFields.BARCODE_TYPE):
                data[WholesalerCommoditiesImportFields.BARCODE_TYPE] = None
                WholesalerCommoditiesValidationFunctions.append_warning(
                    data,
                    ErrorFieldsImport.FIELD_ERRORS.get('warning')
                    .get('wholesaler_commodity_barcode_type')
                    .format(barcode_type),
                )

    @staticmethod
    def set_volume_unit(data, volume_unit, measures):
        """
        Function overwrites volume unit in data dict.
        :param data: dict object
        :param volume_unit: unicode commodity volume unit
        :return: void
        """
        volume_unit = volume_unit.strip().lower()
        volume_unit = WholesalerCommoditiesValidationFunctions.LOREAL_VOLUME_UNITS.get(  # pylint: disable=line-too-long
            volume_unit,
            volume_unit,
        )

        if volume_unit and measures.get(volume_unit, False):
            data[WholesalerCommoditiesImportFields.VOLUME_UNIT] = measures.get(volume_unit)
            return

        WholesalerCommoditiesValidationFunctions.append_warning(
            data,
            ErrorFieldsImport.FIELD_ERRORS.get('warning').get(
                'commodity_volume_unit',
            ),
        )

    @staticmethod
    def set_total_pack_capacity(data, capacity):
        """
        Function overwrites id_customer in data dict.
        :param data: dict object
        :param capacity: unicode
        :return: void
        """
        if capacity:
            potential_capacity = ValidationFunctions.delete_dot_zero(capacity)
            if potential_capacity.isdigit():
                data[WholesalerCommoditiesImportFields.TOTAL_PACK_CAPACITY] = int(
                    potential_capacity
                )

    @staticmethod
    def set_description(data, description):
        """
        Function overwrites description in data dict.
        :param data: dict object
        :param description: unicode commodity description
        :return: void
        """
        description = description.strip()
        data[WholesalerCommoditiesImportFields.DESCRIPTION] = str(description)


class BListingValidationFunctions:
    MAX_ADDRESS_LINE = 100
    MAX_BUSINESS_NAME = 250

    @staticmethod
    def set_zip_code(data, zip_code):
        zip_code = ValidationFunctions.delete_dot_zero(zip_code.strip())
        if zip_code and zip_code in get_zipcode_set():
            data[BListingImportFields.ZIP_CODE] = zip_code
        else:
            data['error'] = (
                ErrorFieldsImport.FIELD_ERRORS.get('warning').get('zip_code').format(zip_code)
            )

    @staticmethod
    def set_address_line(data, address):
        if address and len(address) < ValidationFunctions.MAX_ADDRESS_LINE:
            data[BListingImportFields.ADDRESS] = address

    @staticmethod
    def set_categories(data, categories):
        categories_list = [x.strip() for x in categories.split(',')]
        all_categories = [
            c['internal_name'] for c in CategoryCache.get_all() + SubcategoryCache.get_all()
        ]
        for category in categories_list:
            if category not in all_categories:
                data['error'] = (
                    ErrorFieldsImport.FIELD_ERRORS.get('error')
                    .get('not_valid_category')
                    .format(category)
                )
                return
        data[BListingImportFields.CATEGORIES] = categories_list

    @staticmethod
    def set_business_name(data, business_name):
        if len(business_name) <= BListingValidationFunctions.MAX_BUSINESS_NAME:
            data[BListingImportFields.BUSINESS_NAME] = business_name

    @staticmethod
    def set_pipedrive_id(data, pipedrive_id):
        if pipedrive_id:
            pipedrive_id = ValidationFunctions.delete_dot_zero(pipedrive_id)
            data[BListingImportFields.PIPEDRIVE_ID] = pipedrive_id

    @staticmethod
    def set_phone(data, phone, max_len=None):
        phone = (phone if phone else '').replace('-', '').replace(' ', '').encode('ascii', 'ignore')
        phone = smart_str(phone)
        if phone:
            phone = ValidationFunctions.delete_dot_zero(phone)

        # Brazil dial code hack
        if settings.API_COUNTRY == Country.BR and settings.BRAZIL_PHONE_COMPLETION:
            phone = BrazilPhoneCodeCompletion.complete_br_phone(phone)

        parsed_phone = parse_phone_number(phone)
        if max_len is None:
            max_len = ValidationFunctions.MAX_CELL_PHONE_LEN
        if not parsed_phone.is_valid:
            # invalid phone message
            data['error'] = (
                ErrorFieldsImport.FIELD_ERRORS.get('warning').get('invalid_phone').format(phone)
            )
        else:
            if len(phone) > max_len:
                # phone len cant' be greater than 75 characters
                data['error'] = (
                    ErrorFieldsImport.FIELD_ERRORS.get('warning')
                    .get('invalid_phone_len')
                    .format(phone)
                )
            else:
                data[BListingImportFields.PHONE] = parsed_phone.local_short

    @staticmethod
    def set_email(data, email):
        email = email.strip()
        try:
            if len(email) > ValidationFunctions.MAX_EMAIL_LEN:
                return
            booksy_validate_email(email)
            data[BListingImportFields.EMAIL] = email
        except forms.ValidationError:
            data['warning'] = (
                ErrorFieldsImport.FIELD_ERRORS.get('warning').get('invalid_email').format(email)
            )

    @staticmethod
    def set_website(data, website):
        if website:
            data[BListingImportFields.WEBSITE] = website

    @staticmethod
    def set_photo_address(data, photo_address):
        if photo_address:
            data[BListingImportFields.PHOTO_ADDRESS] = photo_address

    @staticmethod
    def set_photo_source(data, photo_source):
        if photo_source:
            data[BListingImportFields.PHOTO_SOURCE] = photo_source

    @staticmethod
    def set_services(data, services):
        if services:
            data[BListingImportFields.SERVICES] = services

    @staticmethod
    def set_latitude(data, latitude):
        if latitude:
            data[BListingImportFields.LATITUDE] = latitude

    @staticmethod
    def set_longitude(data, longitude):
        if longitude:
            data[BListingImportFields.LONGITUDE] = longitude

    @staticmethod
    def set_description(data, description):
        if description:
            data[BListingImportFields.DESCRIPTION] = description

    @staticmethod
    def set_active(data, active):
        data[BListingImportFields.ACTIVE] = bool(active)

    @staticmethod
    def set_region(data, region_id):
        if not Region.objects.filter(id=region_id).exists():
            data['error'] = (
                ErrorFieldsImport.FIELD_ERRORS.get('error')
                .get('not_valid_object_id')
                .format('Region', region_id)
            )
        data[BListingImportFields.REGION] = region_id

    @staticmethod
    def set_primary_category(data, category_id):
        if not BusinessCategory.objects.filter(id=category_id).exists():
            data['error'] = (
                ErrorFieldsImport.FIELD_ERRORS.get('error')
                .get('not_valid_object_id')
                .format('Business Category', category_id)
            )
        data[BListingImportFields.PRIMARY_CATEGORY] = category_id

    @staticmethod
    def set_source_email(data, source_email):
        if source_email:
            try:
                booksy_validate_email(source_email)
                data[BListingImportFields.SOURCE_EMAIL] = source_email
            except forms.ValidationError:
                data['warning'] = (
                    ErrorFieldsImport.FIELD_ERRORS.get('warning')
                    .get('invalid_email')
                    .format(source_email)
                )

    @staticmethod
    def set_city(data, city):
        if city:
            data[BListingImportFields.CITY] = city


class FieldValidation(abc.ABC):
    ERRORS = 'errors'
    WARNINGS = 'warnings'
    DEFAULT_ROW_DATA = {ERRORS: {}, WARNINGS: {}}
    PandaCellType = t.Union[str, int, float, pd.Timestamp]
    RowType = t.Dict[str, t.Union[str, int, dict, datetime, None, BaseEnum]]
    fields: BaseEnum
    simple_cases_mapping: t.Dict[str, dict] = {}

    @classmethod
    def set(
        cls,
        field: SubscriptionsImportFields,
        data: RowType,
        value: PandaCellType,
    ) -> None:
        """
        Main and only public method for setting validated value into data dict
        """

        if field not in cls.fields:
            raise ValueError('Unknown field (not included in specs).')

        method_name = f'_set_{field.name.lower()}'
        method = getattr(cls, method_name, None)
        if method:
            return method(field.value, data, value)  # pylint: disable=not-callable

        method_and_args = cls.simple_cases_mapping.get(method_name)
        if method_and_args:
            method_name = method_and_args['func']
            method = getattr(cls, method_name, None)
            args = method_and_args['args']
            return method(field.value, data, value, **args)

        raise AttributeError(
            f'{cls.__name__} does not have method for field '
            f'"{field.name}" implemented or declared in simple cases mapping.'
        )

    @classmethod
    def _common_set_model_id(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        cls, field_name: str, data: RowType, value: PandaCellType, model: T, optional: bool = False
    ) -> t.Optional[T]:
        if cls._is_empty(field_name, data, value, optional=optional):
            return

        try:
            obj = model.objects.get(pk=value)
        except (model.DoesNotExist, ValueError):
            error = f'{model._meta.verbose_name.title()} with id={value} does not exist.'
            data[cls.ERRORS][field_name] = error
        else:
            data[field_name] = value
            return obj

    @classmethod
    def _common_set_business_id(  # pylint: disable=too-many-arguments
        cls, field_name: str, data: RowType, value: PandaCellType, optional: bool = False
    ) -> None:
        business = cls._common_set_model_id(field_name, data, value, Business, optional=optional)

        if not business:
            return

        if business.has_new_billing:
            error = f'Business with id={value} have has_new_billing flag set to True'
            data[cls.ERRORS][field_name] = error
        else:
            data[field_name] = value

    @classmethod
    def _common_set_datetime(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        cls,
        field_name: str,
        data: RowType,
        value: PandaCellType,
        date_only: bool = False,
        tzinfo: t.Optional[pytz.BaseTzInfo] = None,
        optional: bool = False,
    ) -> None:
        if cls._is_empty(field_name, data, value, optional):
            return

        try:
            value = parser.parse(str(value))
            if date_only:
                value = value.date()
            if tzinfo:
                data[field_name] = value.replace(tzinfo=tzinfo)
            else:
                data[field_name] = value
        except (ValueError, TypeError):
            message = f'Unknown date format for "{value}".'
            if optional:
                data[cls.WARNINGS][field_name] = f'{message} Set to empty.'
                data[field_name] = None
            else:
                data[cls.ERRORS][field_name] = message

    @classmethod
    def _common_set_integer(
        cls,
        field_name: str,
        data: RowType,
        value: PandaCellType,
        optional: bool = False,
    ) -> None:
        if cls._is_empty(field_name, data, value, optional):
            return

        try:
            data[field_name] = int(value)
        except (ValueError, TypeError):
            message = f'Cannot convert "{value}" to integer.'
            if optional:
                data[cls.WARNINGS][field_name] = f'{message} Set to empty.'
                data[field_name] = None
            else:
                data[cls.ERRORS][field_name] = message

    @classmethod
    def _common_set_decimal(
        cls,
        field_name: str,
        data: RowType,
        value: PandaCellType,
        optional: bool = False,
    ) -> None:
        if cls._is_empty(field_name, data, value, optional):
            return

        try:
            data[field_name] = Decimal(str(value).replace(',', '.'))
        except InvalidOperation:
            message = f'Cannot convert "{value}" to number.'
            if optional:
                data[cls.WARNINGS][field_name] = f'{message} Set to empty.'
                data[field_name] = None
            else:
                data[cls.ERRORS][field_name] = message

    @classmethod
    def _common_set_string(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        cls,
        field_name: str,
        data: RowType,
        value: PandaCellType,
        max_length: int = 0,
        trim: bool = False,
        optional: bool = False,
    ) -> None:
        if cls._is_empty(field_name, data, value, optional):
            return

        value = str(value)

        if max_length and len(value) > max_length:
            message = f'Value longer than {max_length}.'
            if optional and trim:
                data[field_name] = value[:max_length]
                data[cls.WARNINGS][field_name] = f'{message} Trimmed to {max_length} chars.'
            else:
                data[cls.ERRORS][field_name] = message
            return

        data[field_name] = value

    @classmethod
    def _common_set_str_choices_enum(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        cls,
        field_name: str,
        data: RowType,
        value: PandaCellType,
        str_choices_enum: t.Type[StrChoicesEnum],
        optional: bool = False,
    ) -> None:
        if cls._is_empty(field_name, data, value, optional):
            return
        choices_map = {en.label.lower(): en for en in str_choices_enum}
        choice = choices_map.get(value.lower())

        if not choice:
            error = (
                f'Unrecognized "{value}" of {str_choices_enum.__name__}, '
                f'should be one of the {list(choices_map.keys())}'
                f'{", or empty" if optional else ""}.'
            )
            data[cls.ERRORS][field_name] = error
        else:
            data[field_name] = choice

    @classmethod
    def _common_set_email(
        cls,
        field_name: str,
        data: RowType,
        value: PandaCellType,
        optional: bool = False,
    ) -> None:
        if cls._is_empty(field_name, data, value, optional):
            return
        value = value.strip()
        try:
            booksy_validate_email(value)
            data[field_name] = value
        except forms.ValidationError:
            warning = f'Unable to parse email "{value}". Set to empty.'
            data[cls.WARNINGS][field_name] = warning
            data[field_name] = None

    @classmethod
    def _common_set_boolean(
        cls,
        field_name: str,
        data: RowType,
        value: PandaCellType,
        optional: bool = False,
    ) -> None:
        if cls._is_empty(field_name, data, value, optional):
            return

        ValidationFunctions.set_boolean(
            data=data,
            value=str(value),
            field_name=field_name,
        )

    @classmethod
    def _common_set_zip_code(
        cls,
        field_name: str,
        data: RowType,
        value: PandaCellType,
        optional: bool = False,
    ) -> None:
        if cls._is_empty(field_name, data, value, optional):
            return

        if (
            settings.CHECK_ZIPCODE_IN_REGION_TABLE
            and not Region.objects.filter(
                type=Region.Type.ZIP,
                name=str(value),
            ).exists()
        ):
            data[cls.ERRORS][field_name] = f'Zipcode {value} does not exists in region table'

        try:
            zip_code_validator(str(value))
        except serializers.ValidationError as ex:
            data[cls.ERRORS][field_name] = ex.default_detail

    @classmethod
    def _common_set_payment_period(
        cls,
        field_name: str,
        data: RowType,
        value: PandaCellType,
        optional: bool = False,
    ) -> None:
        if cls._is_empty(field_name, data, value, optional):
            return

        try:
            number = int(value)
            if 1 <= number <= 24:
                data[field_name] = number
            else:
                data[cls.ERRORS][field_name] = 'Payment period must be a number in range <1, 24>'
        except (ValueError, TypeError):
            message = f'Cannot convert "{value}" to integer.'
            if optional:
                data[cls.WARNINGS][field_name] = f'{message} Set to empty.'
                data[field_name] = None
            else:
                data[cls.ERRORS][field_name] = message

    @classmethod
    def _is_empty(
        cls,
        field_name: str,
        data: RowType,
        value: PandaCellType,
        optional: bool,
    ) -> bool:
        if pd.isnull(value) and optional:
            data[field_name] = None
            return True
        if pd.isnull(value):
            error = 'This field is required.'
            data[cls.ERRORS][field_name] = error
            return True
        return False


class _SubscriptionsBaseFieldValidation(FieldValidation):
    common_simple_cases_mapping = {
        '_set_payment_period': {
            'func': '_common_set_payment_period',
            'args': {'optional': True},
        },
        '_set_agreement_signed_date': {
            'func': '_common_set_datetime',
            'args': {'optional': True, 'date_only': True},
        },
        '_set_discount_percent': {
            'func': '_common_set_decimal',
            'args': {'optional': True},
        },
        '_set_discount_type': {
            'func': '_common_set_str_choices_enum',
            'args': {
                'str_choices_enum': SubscriptionDiscount.DiscountType,
                'optional': True,
            },
        },
        '_set_discount_from_date': {
            'func': '_common_set_datetime',
            'args': {'optional': True, 'tzinfo': pytz.UTC},
        },
        '_set_discount_to_date': {
            'func': '_common_set_datetime',
            'args': {'optional': True, 'tzinfo': pytz.UTC},
        },
        '_set_buyer_id': {
            'func': '_common_set_model_id',
            'args': {'model': SubscriptionBuyer, 'optional': True},
        },
        '_set_tax_id': {
            'func': '_common_set_string',
            'args': {'max_length': 20, 'optional': True, 'trim': False},
        },
        '_set_is_verified': {
            'func': '_common_set_boolean',
            'args': {},
        },
        '_set_zip_code': {
            'func': '_common_set_zip_code',
            'args': {'optional': True},
        },
        '_set_navision_invoicing_allowed': {
            'func': '_common_set_boolean',
            'args': {},
        },
    }

    @classmethod
    def _set_instalments(
        cls,
        field_name: str,
        data: FieldValidation.RowType,
        value: FieldValidation.PandaCellType,
    ) -> None:
        if cls._is_empty(field_name, data, value, optional=True):
            return

        types_enums = Subscription.InstalmentType
        instalments_type_map = {en.name.lower(): en.value for en in types_enums}

        try:
            instalment_type = instalments_type_map.get(value.lower())
        except AttributeError:
            instalment_type = None

        if not instalment_type:
            names = list(instalments_type_map.keys())
            error = (
                f'Unrecognized "{value}" of instalment type, '
                f'should be one of the {names}, or empty.'
            )
            data[cls.ERRORS][field_name] = error
        else:
            data[field_name] = instalment_type

    @classmethod
    def _set_navision_invoicing_exclusion_reason(
        cls, field_name: str, data: dict, reason: t.AnyStr
    ) -> None:
        navision_invoicing_allowed = data.get('Navision invoicing allowed')
        if pd.isna(reason):
            reason = None
        reason_is_null_or_blank = not reason or (isinstance(reason, str) and reason.isspace())

        if (not navision_invoicing_allowed) and reason_is_null_or_blank:
            error = 'Please provide a reason for invoicing exclusion.'
            data[cls.ERRORS][field_name] = error
            return

        data[field_name] = reason


class SubscriptionsImportFieldValidation(_SubscriptionsBaseFieldValidation):
    fields = SubscriptionsImportFields
    simple_cases_mapping = {
        **_SubscriptionsBaseFieldValidation.common_simple_cases_mapping,
        '_set_product_id': {
            'func': '_common_set_model_id',
            'args': {'model': SubscriptionListing},
        },
        '_set_business_id': {
            'func': '_common_set_business_id',
            'args': {},
        },
        '_set_next_billing_date': {
            'func': '_common_set_datetime',
            'args': {'optional': True, 'tzinfo': pytz.UTC},
        },
        '_set_max_staffer_num': {
            'func': '_common_set_integer',
            'args': {'optional': True},
        },
        '_set_start': {
            'func': '_common_set_datetime',
            'args': {'tzinfo': pytz.UTC},
        },
        '_set_expiry': {
            'func': '_common_set_datetime',
            'args': {'tzinfo': pytz.UTC},
        },
        '_set_final_price_amount': {
            'func': '_common_set_decimal',
            'args': {},
        },
        '_set_monthly_base_price': {
            'func': '_common_set_decimal',
            'args': {},
        },
    }


class SubscriptionsEditFieldValidation(_SubscriptionsBaseFieldValidation):
    fields = SubscriptionsEditFields
    simple_cases_mapping = {
        **_SubscriptionsBaseFieldValidation.common_simple_cases_mapping,
        '_set_product_id': {
            'func': '_common_set_model_id',
            'args': {'model': SubscriptionListing, 'optional': True},
        },
        '_set_business_id': {
            'func': '_common_set_business_id',
            'args': {},
        },
        '_set_start': {
            'func': '_common_set_datetime',
            'args': {'tzinfo': pytz.UTC, 'optional': True},
        },
        '_set_expiry': {
            'func': '_common_set_datetime',
            'args': {'tzinfo': pytz.UTC, 'optional': True},
        },
        '_set_final_price_amount': {
            'func': '_common_set_decimal',
            'args': {'optional': True},
        },
        '_set_monthly_base_price': {
            'func': '_common_set_decimal',
            'args': {'optional': True},
        },
        '_set_navision_invoicing_allowed': {
            'func': '_common_set_boolean',
            'args': {'optional': True},
        },
    }

    @classmethod
    def _set_subscription_id(
        cls,
        field_name: str,
        data: FieldValidation.RowType,
        value: FieldValidation.PandaCellType,
    ) -> None:
        if cls._is_empty(field_name, data, value, optional=False):
            return

        try:
            subscription = Subscription.objects.get(pk=value)
        except (Subscription.DoesNotExist, ValueError):
            error = f'{Subscription._meta.verbose_name.title()} with id={value} does not exist.'
            data[cls.ERRORS][field_name] = error
            return
        if not subscription.source == Business.PaymentSource.OFFLINE.value:
            error = f'{subscription} is not offline.'
            data[cls.ERRORS][field_name] = error
            return

        data[field_name] = value
