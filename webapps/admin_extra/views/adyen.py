import datetime
import io
import json
from collections import defaultdict
from decimal import Decimal
from typing import (
    Dict,
    List,
    Iterable,
    Optional,
)

from django.conf import settings
from django.contrib.messages.views import SuccessMessageMixin
from django.db import transaction
from django.db.models import Q
from django.http import HttpResponse
from django.utils.timezone import make_aware
from openpyxl import Workbook
from rest_framework.fields import get_attribute


from country_config import Country
from lib.spreadsheet import (
    columns_auto_width,
    load_spreadsheet_with_pandas,
)
from lib.tools import (
    minor_unit,
    tznow,
)
from webapps.admin_extra import consts
from webapps.admin_extra.custom_permissions_classes import FormView
from webapps.admin_extra.forms.adyen import (
    AdyenReportForm,
    AdyenSettledReportForm,
    AutoAcceptPaymentSwitchForm,
    BulkManualTransferFundForm,
    BulkPayoutSchemeUpdaterForm,
    ManualTransferFundForm,
)
from webapps.admin_extra.import_tools.enums import ManualFundTransferFields
from webapps.admin_extra.import_utils import strip_xlsx_data
from webapps.admin_extra.views.utils import ImportFileMixin
from webapps.adyen.helpers import get_reference
from webapps.adyen.tasks import bulk_transfer_funds
from webapps.adyen.typing import (
    AmountDict,
    FundTransferData,
)
from webapps.market_pay.consts import FUND_TRANSFER_MANUAL
from webapps.market_pay.enums import TransferFundsStatus
from webapps.market_pay.models import (
    AccountHolder,
    FundTransfer,
)
from webapps.market_pay.payout import hold_payouts, release_payouts
from webapps.market_pay.requests import transfer_funds
from webapps.market_pay.transfer import has_enough_funds
from webapps.market_pay.typing import TransferFundsRequestArgs
from webapps.pos.calculations import round_currency
from webapps.pos.enums import POSPlanPaymentTypeEnum
from webapps.pos.models import (
    PaymentRow,
    PaymentRowChange,
    POSPlan,
)
from webapps.user.models import User
from webapps.user.tools import get_user_from_django_request


class AdyenReportMixin:
    @staticmethod
    def get_payment_rows(from_date, till_date):
        return (
            PaymentRow.objects.get_dwolla_record(add_settled_param=True)
            .filter(
                created__range=(
                    make_aware(datetime.datetime.combine(from_date, datetime.time.min)),
                    make_aware(datetime.datetime.combine(till_date, datetime.time.max)),
                )
            )
            .prefetch_related('receipt__transaction__pos__business')
            .select_related('payment_type')
            .order_by('id')
        )

    @classmethod
    def get_business_data(cls, from_date, till_date):
        prs = cls.get_payment_rows(from_date, till_date)

        # get txns by business
        prs_by_business = defaultdict(list)
        pos_plans = {}
        for pr in prs:
            business = pr.receipt.transaction.pos.business
            if pos_plans.get(business.id) is None:
                pos_plans[business.id] = business.get_pos_plan(
                    POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT
                )
            prs_by_business[business.id].append(pr)

        return pos_plans, prs_by_business


class AdyenTxnsReportView(AdyenReportMixin, FormView):
    """
    Report used for Mobile Payments all single transactions processed
    within selected date ranges.
    """

    permission_required = 'user.create_reports'
    form_class = AdyenReportForm
    template_name = 'admin/custom_views/adyen_txns_report.html'
    success_url = 'adyen_txns_report'

    def form_valid(self, form):
        # get date from form
        from_date_str = form.cleaned_data['from_date']
        from_date = datetime.datetime.strptime(from_date_str, '%Y-%m-%d').date()
        till_date_str = form.cleaned_data['till_date']
        till_date = datetime.datetime.strptime(till_date_str, '%Y-%m-%d').date()
        business_id = form.cleaned_data['business_id']

        # get data
        data = self.get_data(from_date, till_date)

        # get xlsx
        file, mime, filename = self.get_report(
            data,
            from_date,
            till_date,
            business_id,
        )

        # create response
        response = HttpResponse(file.getvalue(), content_type=mime)
        response['Content-Disposition'] = 'attachment; filename=' + filename

        return response

    @staticmethod
    def get_data(from_date, till_date):
        pos_plans, prs_by_business = AdyenTxnsReportView.get_business_data(
            from_date,
            till_date,
        )

        # get report row
        result = []
        donation_plan = POSPlan.get_donation_plan()

        for business_id, business_prs in sorted(prs_by_business.items()):
            business = business_prs[0].receipt.transaction.pos.business
            business_name = business.name

            plan = pos_plans[business_id]
            donation_plan = donation_plan or plan

            for pr in business_prs:
                data = AdyenPayoutReportView.calculate_splits(plan, donation_plan, [pr])
                result.append(
                    {
                        'date': pr.created,
                        'business_id': business_id,
                        'business_name': business_name,
                        'pr_id': pr.id,
                        'amount': round_currency(pr.amount),
                        'fixed_fee': round_currency(data['details']['prs_fee']),
                        'pct_fee': round_currency(data['details']['provision']),
                        'total_fee': round_currency(data['details']['total_fee']),
                        'payout': data['amount'],
                    }
                )

        return result

    @staticmethod
    def get_report(data, from_date, till_date, filter_business_id=None):
        # create file
        workbook = Workbook()
        filename = f'adyen_transactions_report_{from_date}_{till_date}.xlsx'
        mime = consts.XLS_MIME
        file = io.BytesIO()

        # create sheet
        worksheet = workbook.active
        worksheet.append(
            [
                'date',
                'business name',
                'business id',
                'transaction id',
                'transaction amount',
                'transaction fixed fee',
                'transaction % fee',
                'total fee',
                'payout amount',
            ]
        )

        for row in data:
            if filter_business_id and int(row['business_id']) != filter_business_id:
                continue

            worksheet.append(
                [
                    row['date'].strftime('%Y-%m-%d'),
                    row['business_name'],
                    row['business_id'],
                    row['pr_id'],
                    row['amount'],
                    row['fixed_fee'],
                    row['pct_fee'],
                    row['total_fee'],
                    row['payout'],
                ]
            )

        columns_auto_width(worksheet)
        workbook.save(file)

        return file, mime, filename


class AdyenPayoutReportView(AdyenReportMixin, FormView):
    """
    Report used for Mobile Payments payout batch purposes.
    Takes into account all transaction within selected date range.
    """

    permission_required = 'user.create_reports'
    form_class = AdyenSettledReportForm
    template_name = 'admin/custom_views/adyen_payout_report.html'
    success_url = 'adyen_payout_report'

    def form_valid(self, form):
        # get date from form
        from_date_str = form.cleaned_data['from_date']
        from_date = datetime.datetime.strptime(from_date_str, '%Y-%m-%d').date()
        till_date_str = form.cleaned_data['till_date']
        till_date = datetime.datetime.strptime(till_date_str, '%Y-%m-%d').date()
        mark_settled = form.cleaned_data['mark_settled']
        business_id = form.cleaned_data['business_id']

        # get data
        data = self.get_data(from_date, till_date)

        # get xlsx
        file, mime, filename = self.get_report(data, from_date, till_date, business_id)

        if mark_settled:
            pr_pnrefs = [pr.pnref for pr in self.get_payment_rows(from_date, till_date)]

            filters = [Q(pnref__in=pr_pnrefs)]

            if business_id:
                filters.append(Q(receipt__transaction__pos__business__id=business_id))

            operator = get_user_from_django_request(self.request)
            prs = PaymentRow.objects.filter(*filters)
            with transaction.atomic():
                prs.update(settled=True)

                PaymentRowChange.objects.bulk_create(
                    [
                        PaymentRowChange(
                            payment_row=pr,
                            operator=operator,
                            reason=PaymentRowChange.SWITCH_SETTLED_ON,
                            metadata="Mark as settled from Adyen CS report",
                        )
                        for pr in prs
                    ]
                )

        # create response
        response = HttpResponse(file.getvalue(), content_type=mime)
        response['Content-Disposition'] = 'attachment; filename=' + filename

        return response

    @staticmethod
    def __merge_dicts(dict_1, dict_2):
        """Sums values in two dicts."""

        return {
            key: (
                AdyenPayoutReportView.__merge_dicts(dict_1.get(key), dict_2.get(key))
                if type(dict_1.get(key)) is dict  # pylint: disable=unidiomatic-typecheck
                else dict_1.get(key, 0) + dict_2.get(key, 0)
            )
            for key in set(dict_1) | set(dict_2)
        }

    @staticmethod
    def __calculate_settle_amount(plan: POSPlan, prs: List[PaymentRow]):
        """
        Calculate amount to settle.

        :param plan: Plan for calculations.
        :param prs: List of payment_rows to settle.
        :return: Amount, details: full_amount, txn_fees, provision.
                Amount is the value we need to send to our client.

        Examples:
            >>> import datetime
            >>> b = Business.objects.first()
            >>> prs = b.get_transactions_to_settle()
            >>> plan = b.get_pos_plan(POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT)
            >>> AdyenPayoutReportView.__calculate_settle_amount(plan, prs)
            {
                'amount': Decimal('123.45'),  # 127.45 - 1.5 - 2.5
                'details': {
                    'full_amount': Decimal('127.45'),  # 123.45 + 1.5 + 2.5
                    'txns_fee': Decimal('1.5'),
                    'provision': Decimal('2.5'),
                },
            }
        """

        if not prs:
            return {
                'amount': Decimal('0'),
                'details': {
                    'full_amount': Decimal('0'),
                    'prs_fee': Decimal('0'),
                    'provision': Decimal('0'),
                    'total_fee': Decimal('0'),
                },
            }

        # math, bitch!
        provision = Decimal(str(plan.provision))
        total_amount = sum(get_attribute(pr, ['amount']) for pr in prs) or Decimal('0')
        provision_amount = provision * total_amount
        prs_fee_amount = Decimal(plan.txn_fee * len(prs))
        total_costs = provision_amount + prs_fee_amount
        amount_to_settle = total_amount - total_costs

        return {
            'amount': amount_to_settle.quantize(Decimal('1.00')),
            'details': {
                'full_amount': total_amount.quantize(Decimal('1.00')),
                'prs_fee': prs_fee_amount.quantize(Decimal('1.00')),
                'provision': provision_amount.quantize(Decimal('1.00')),
                'total_fee': total_costs.quantize(Decimal('1.00')),
            },
        }

    @staticmethod
    def calculate_splits(
        regular_plan: POSPlan,
        donation_plan: POSPlan,
        prs: List[PaymentRow],
    ):
        """Calculate settle amount for payment rows.

        Donation may use different payment plan.
        """
        donation_payment_rows = []
        regular_payment_rows = []

        from webapps.pos.enums import PaymentTypeEnum

        for pr in prs:
            if pr.payment_type.code == PaymentTypeEnum.PAY_BY_APP_DONATIONS:
                donation_payment_rows.append(pr)
            else:
                regular_payment_rows.append(pr)

        return AdyenPayoutReportView.__merge_dicts(
            AdyenPayoutReportView.__calculate_settle_amount(regular_plan, regular_payment_rows),
            AdyenPayoutReportView.__calculate_settle_amount(donation_plan, donation_payment_rows),
        )

    @classmethod
    def get_data(cls, from_date, till_date):
        pos_plans, prs_by_business = cls.get_business_data(
            from_date,
            till_date,
        )

        # get report row
        result_by_business = {}
        donation_plan = POSPlan.get_donation_plan()

        for business_id, business_prs in list(prs_by_business.items()):
            business = business_prs[0].receipt.transaction.pos.business
            business_name = business.name

            plan = pos_plans[business_id]
            donation_plan = donation_plan or plan
            data = AdyenPayoutReportView.calculate_splits(plan, donation_plan, business_prs)

            result_by_business[business_id] = {
                'pr_num': len(business_prs),
                'pr_fixed_fees': round_currency(data['details']['prs_fee']),
                'pr_pct_fee': round_currency(data['details']['provision']),
                'prs_total': round_currency(data['details']['full_amount']),
                'payout': round_currency(data['amount']),
                'total_fee': round_currency(data['details']['total_fee']),
                'business_name': business_name,
                'json': json.dumps(
                    {
                        "amount": {
                            'value': minor_unit(data['amount']),
                            'currency': settings.CURRENCY_CODE,
                        },
                        "recurring": {"contract": "PAYOUT"},
                        "reference": f"{tznow().strftime('%y%m%d')} - GB-{business_id}",
                        "merchantAccount": settings.ADYEN_MERCHANT_ACCOUNT,
                        "shopperReference": business_name,
                        "shopperEmail": business.owner.email,
                        "shopperStatement": f"Booksy_payout_{tznow().strftime('%y%m%d')}",
                        "selectedRecurringDetailReference": "LATEST",
                    }
                ),
                'payment_row_ids': ', '.join(str(pr.id) for pr in business_prs),
            }

        return result_by_business

    @staticmethod
    def get_report(data, from_date, till_date, filter_business_id=None):
        # create file
        workbook = Workbook()
        filename = f'adyen_payout_report_{from_date}_{till_date}.xlsx'
        mime = consts.XLS_MIME
        file = io.BytesIO()

        # JSON for curl, used only in GB, IE
        json_column = settings.API_COUNTRY in [
            Country.GB,
            Country.IE,
            Country.ES,
        ]

        # create sheet
        worksheet = workbook.active
        worksheet.append(
            [
                'date range',
                'business name',
                'business id',
                'transactions number',
                'transactions fixed fees',
                'transactions % fees',
                'total transactions amount',
                'total fee',
                'payout amount',
                'payment_row_ids',
                'json' if json_column else '',
            ]
        )

        for business_id, row in list(data.items()):
            if filter_business_id and business_id != filter_business_id:
                continue
            worksheet.append(
                [
                    f'{from_date.strftime("%Y-%m-%d")} - {till_date.strftime("%Y-%m-%d")}',
                    row['business_name'],
                    business_id,
                    row['pr_num'],
                    row['pr_fixed_fees'],
                    row['pr_pct_fee'],
                    row['prs_total'],
                    row['total_fee'],
                    row['payout'],
                    row['payment_row_ids'],
                    row['json'] if json_column else '',
                ]
            )

        columns_auto_width(worksheet)
        workbook.save(file)

        return file, mime, filename


class AdyenDonationsPayoutReportView(AdyenPayoutReportView):
    """
    Report used for Mobile Payments Donations payout batch purposes.
    Takes into account all transaction within selected date range.
    """

    @staticmethod
    def get_payment_rows(from_date, till_date):
        return (
            PaymentRow.objects.get_dwolla_record(
                add_settled_param=True,
                donations=True,
            )
            .filter(
                created__range=(
                    make_aware(datetime.datetime.combine(from_date, datetime.time.min)),
                    make_aware(datetime.datetime.combine(till_date, datetime.time.max)),
                )
            )
            .prefetch_related('receipt__transaction__pos__business')
            .select_related('payment_type')
            .order_by('id')
        )


class ManualTransferFundView(SuccessMessageMixin, FormView):
    """
    This is supposed to proceed with transferring money from one account
    to another on MarketPay Adyen integration.
    """

    form_class = ManualTransferFundForm
    success_url = 'manual_transfer_fund'
    permission_required = ()
    template_name = 'admin/custom_views/manual_transfer_fund.html'

    def get_success_message(self, cleaned_data):
        return 'Successfully sent fund transfer request.'

    def form_valid(self, form: ManualTransferFundForm):
        cleaned_data = form.cleaned_data
        self.send_manual_fund_transfer(
            cleaned_data['source_account_code'],
            cleaned_data['destination_account_code'],
            cleaned_data['source_account_description'],
            cleaned_data['destination_account_description'],
            cleaned_data['amount'],
        )
        return super().form_valid(form)

    @staticmethod
    @transaction.atomic
    def send_manual_fund_transfer(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        source_account_code: str,
        destination_account_code: str,
        source_account_description: str,
        destination_account_description: str,
        amount: int,
        dry_run: bool = False,
    ) -> FundTransfer:
        """
        Creates FundTransfer object and, if `dry_run` is set to False, sends
        the "transfer fund" request to Adyen.
        If the `dry_run` flag is set to True, the created FundTransfer object
        is not saved to the database and special care should be taken to not
        call `.save()` method on it.

        :raises ValueError: if the source account code is the same as
                            destination one
        :raises ValueError: if any of the source/destination account codes
                            does not exist in the database
        """
        if source_account_code == destination_account_code:
            raise ValueError('Destination account cannot be the source account')
        source_account_exists = AccountHolder.objects.filter(
            account_code=source_account_code
        ).exists()
        destination_account_exists = AccountHolder.objects.filter(
            account_code=destination_account_code
        ).exists()
        if not source_account_exists or not destination_account_exists:
            source_account_error = f'Source account "{source_account_code}" not recognized'
            destination_account_error = (
                f'Destination account "{destination_account_code}" not recognized'
            )
            raise ValueError(
                ', '.join(
                    (
                        source_account_error if not source_account_exists else '',
                        destination_account_error if not destination_account_exists else '',
                    )
                )
            )
        source_account_holder_id = AccountHolder.objects.filter(
            account_code=source_account_code,
        ).values_list('id', flat=True)[0]
        fund_transfer = FundTransfer(
            source_account_code=source_account_code,
            destination_account_code=destination_account_code,
            account_holder_id=source_account_holder_id,
            currency=settings.CURRENCY_CODE,
            amount=amount,
            fee_refs=[],
            status=TransferFundsStatus.PREPARED.value,
            transfer_code=FUND_TRANSFER_MANUAL,
            source_account_description=source_account_description,
            destination_account_description=destination_account_description,
            merchant_reference=get_reference(),
        )
        if not dry_run:
            response = transfer_funds(
                TransferFundsRequestArgs(
                    sourceAccountCode=source_account_code,
                    destinationAccountCode=destination_account_code,
                    amount=AmountDict(
                        value=amount,
                        currency=settings.CURRENCY_CODE,
                    ),
                    transferCode='manual_transfer',
                    merchantReference=fund_transfer.merchant_reference,
                )
            )
            fund_transfer.psp_reference = response['pspReference']
            fund_transfer.status = TransferFundsStatus.from_result_code(response['resultCode'])
            fund_transfer.save()

        return fund_transfer


class BulkManualTransferFundView(ImportFileMixin, FormView):
    """
    This is supposed to proceed with transferring money batches
    from one account to another on MarketPay Adyen integration.
    """

    form_class = BulkManualTransferFundForm
    success_url = 'bulk_manual_transfer_fund'
    permission_required = ()
    template_name = 'admin/custom_views/bulk_manual_transfer_fund.html'

    def post(self, request, *args, **kwargs):
        form = self.get_form()
        errors = []

        if form.is_valid():
            dry_run = form.cleaned_data['dry_run']
            import_file = form.cleaned_data['import_file']
            import_file_data = import_file.read()
            fund_transfers_data = self._parse_sent_xlsx_file(import_file_data)
            errors = self._check_fund_transfer_data(fund_transfers_data)
            if not errors and not dry_run:
                bulk_transfer_funds.delay(request.user.id, fund_transfers_data)
        context = self.get_context_data(form=form)
        context.update(errors=errors)
        return self.render_to_response(context)

    @staticmethod
    def _parse_sent_xlsx_file(xlsx_file_data) -> Iterable[FundTransferData]:
        stripped_data = strip_xlsx_data(load_spreadsheet_with_pandas(xlsx_file_data))
        headers_field_name_mapping = dict(
            ((header, i) for i, header in enumerate(stripped_data[0]))
        )
        fund_transfers_data = []
        for row in stripped_data[1:]:
            fund_transfers_data.append(
                {
                    "source_account_code": str(
                        row[
                            headers_field_name_mapping[ManualFundTransferFields.SOURCE_ACCOUNT_CODE]
                        ]
                    ),
                    "destination_account_code": str(
                        row[
                            headers_field_name_mapping[
                                ManualFundTransferFields.DESTINATION_ACCOUNT_CODE
                            ]
                        ]
                    ),
                    "source_account_description": row[
                        headers_field_name_mapping[
                            ManualFundTransferFields.SOURCE_ACCOUNT_DESCRIPTION
                        ]
                    ],
                    "destination_account_description": row[
                        headers_field_name_mapping[
                            ManualFundTransferFields.DESTINATION_ACCOUNT_DESCRIPTION
                        ]
                    ],
                    "amount": int(
                        100
                        * float(row[headers_field_name_mapping[ManualFundTransferFields.AMOUNT]])
                    ),
                }
            )
        return fund_transfers_data

    @staticmethod
    def _check_fund_transfer_data(
        data: Iterable[FundTransferData],
    ) -> List[str]:
        """
        Checks correctness of the fund transfer data.
        Returns list of errors, where each entry is in form of tuple:
        * first item is the offending line and
        * the second object being the string representation of the
          encountered error.
        """
        errors = []
        for row_number, row in enumerate(data, 2):
            try:
                ManualTransferFundView.send_manual_fund_transfer(
                    source_account_code=row['source_account_code'],
                    destination_account_code=row['destination_account_code'],
                    source_account_description=row['source_account_description'],
                    destination_account_description=row['destination_account_description'],
                    amount=row['amount'],
                    dry_run=True,
                )
                account_holder = AccountHolder.objects.get(account_code=row['source_account_code'])
                if not has_enough_funds(account_holder, row['amount'], allowed_debit=0):
                    raise ValueError('Not enough money to perform this action')
            except Exception as e:  # pylint: disable=broad-except
                errors.append(f'in line {row_number}: {e}')
        return errors


class AutoAcceptPaymentSwitchView(ImportFileMixin, FormView):
    """
    Switches status of `auto accept payment` setting for the provided users
    list.
    """

    form_class = AutoAcceptPaymentSwitchForm
    success_url = 'auto_accept_payment_switch'
    permission_required = ()
    template_name = 'admin/custom_views/auto_accept_payment_switch.html'

    def post(self, request, *args, **kwargs):
        form = self.get_form()
        errors = []

        if form.is_valid():
            dry_run = form.cleaned_data['dry_run']
            enable_auto_accept = form.cleaned_data['turn_auto_accept']
            unsafe_express = form.cleaned_data['unsafe_express']
            import_file = form.cleaned_data['import_file']
            stripped_data = strip_xlsx_data(load_spreadsheet_with_pandas(import_file.read()))
            user_ids = [row[0] for row in stripped_data[1:]]  # skip header
            if not dry_run and unsafe_express:
                if error := self._unsafe_set_auto_accept_payment_for_users(
                    user_ids,
                    enable_auto_accept,
                ):
                    errors.append(error)
            else:
                errors.extend(
                    self._set_auto_accept_payment_for_users(
                        dry_run,
                        user_ids,
                        enable_auto_accept,
                    )
                )
        context = self.get_context_data(form=form)
        context.update(errors=errors)
        return self.render_to_response(context)

    @staticmethod
    def _set_auto_accept_payment_for_users(
        dry_run: bool,
        user_ids: List[str],
        enable_auto_accept: bool,
    ) -> List[Optional[str]]:
        """
        Updates provided users' `payment_auto_accept` setting to provided
        `enable_auto_accept` value.
        Returns list of encountered errors in form of string descriptions.

        This method does not perform bulk database update because it needs to
        ensure that every user id is correct one, and each object's update
        was successful.
        """
        errors = []
        user_id_to_object: Dict[str, User] = {
            str(user.id): user for user in User.objects.filter(id__in=user_ids)
        }
        for user_id in user_ids:
            user = user_id_to_object.get(user_id)
            if not user:
                errors.append(f'User with id {user_id} not found.')
            elif not dry_run:
                try:
                    user.payment_auto_accept = enable_auto_accept
                    user.save(update_fields=['payment_auto_accept'])
                except Exception as e:  # pylint: disable=broad-except
                    errors.append(f'Got error when updating User #{user_id}: {e}')
        return errors

    @staticmethod
    def _unsafe_set_auto_accept_payment_for_users(
        user_ids: List[str],
        enable_auto_accept: bool,
    ) -> Optional[str]:
        updated_count = User.objects.filter(id__in=user_ids).update(
            payment_auto_accept=enable_auto_accept,
        )
        return (
            None
            if updated_count == len(user_ids)
            else 'Not all entries were updated. Use regular update method to '
            'check for the problems'
        )


class BulkPayoutSchemeUpdaterView(ImportFileMixin, FormView):
    """
    Updates payout schedule to HOLD for the account holders provided
    within imported .xlsx file.
    """

    form_class = BulkPayoutSchemeUpdaterForm

    success_url = 'bulk_payout_scheme_updater'
    permission_required = ()
    template_name = 'admin/custom_views/bulk_payout_scheme_updater.html'

    def post(self, request, *args, **kwargs):
        form = self.get_form()
        errors = []
        user = get_user_from_django_request(request)

        if form.is_valid():
            dry_run = form.cleaned_data['dry_run']
            enable_auto_adyen_payouts = form.cleaned_data['enable_auto_adyen_payouts']
            import_file = form.cleaned_data['import_file']
            stripped_data = strip_xlsx_data(load_spreadsheet_with_pandas(import_file.read()))
            account_codes = [row[0] for row in stripped_data[1:]]
            errors.extend(
                self._set_payout_schedules(
                    user,
                    dry_run,
                    account_codes,
                    enable_auto_adyen_payouts,
                )
            )
        context = self.get_context_data(form=form)
        context.update(errors=errors)
        return self.render_to_response(context)

    @staticmethod
    def _set_payout_schedules(
        user: User,
        dry_run: bool,
        account_codes: List[str],
        enable_auto_adyen_payouts: bool,
    ) -> List[str]:
        errors = []
        account_holder_mapping = {
            account_holder.account_code: account_holder
            for account_holder in AccountHolder.objects.filter(account_code__in=account_codes)
        }
        errors.extend(
            [
                f'AccountHolder for account_code "{account_code}" not found'
                for account_code in account_codes
                if account_code not in account_holder_mapping
            ]
        )
        payout_method = release_payouts if enable_auto_adyen_payouts else hold_payouts
        if not dry_run:
            for account_holder in account_holder_mapping.values():
                payout_method(account_holder, user)
        return errors
