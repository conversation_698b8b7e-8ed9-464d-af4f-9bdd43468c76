# pylint: disable=redefined-outer-name
import csv
import io
import json
from unittest.mock import MagicMock, patch

import pytest
from django.core import mail
from django.test import override_settings
from model_bakery import baker
from parameterized import parameterized
from rest_framework.exceptions import ValidationError

from country_config import Country, CountryConfig
from webapps.admin_extra.tasks import (
    batch_update_subscription_buyers_task,
)
from webapps.admin_extra.tasks.subscription_buyer_tools import (
    CSVLoader,
    CSV_READ_FIELDS,
    S3CSVLoader,
)
from webapps.admin_extra.tests import DjangoTestCase
from webapps.business.models import Business
from webapps.business.models.business_change import BusinessChange
from webapps.navision.models import TaxGroup
from webapps.purchase.models import (
    InvoiceAddress,
    SubscriptionBuyer,
)
from webapps.purchase.recipes import (
    tax_id_subscription_buyer_recipe,
    sales_subscription_buyer_recipe,
)
from webapps.structure.models import Region
from webapps.user.models import User

TEST_FILE_NAME_SINGLE_LINE = "subscription_buyer_single_line.csv"
TEST_FILE_NAME_MULTIPLE_LINES = "subscription_buyer_multiple_lines.csv"
TEST_FILE_NAME_WITHOUT_TAX_ID = "subscription_buyer_without_tax_id.csv"
TEST_FILE_NAME_INVALID_BOOLEAN_FIELDS = "subscription_buyer_invalid_boolean_fields.csv"
TEST_FILE_NAME_INVALID_EMAIL = "subscription_buyer_invalid_email.csv"
TEST_FILE_NAME_INVALID_TAX_ID = "subscription_buyer_invalid_tax_id.csv"
TEST_FILE_NAME_INVALID_TAX_ID2 = "subscription_buyer_invalid_tax_id2.csv"
TEST_FILE_NAME_INVALID_BUSINESS_ID = "subscription_buyer_invalid_business_id.csv"
TEST_FILE_NOT_UNIQUE_BUSINESS_ID = "subscription_buyer_not_unique_business_id.csv"
TEST_FILE_NOT_UNIQUE_ADDITIONAL_BUSINESS_IDS = (
    "subscription_buyer_not_unique_additional_business_ids.csv"
)
TEST_FILE_NAME_NOT_VERIFIED_BUYER = "subscription_buyer_not_verified.csv"
TEST_FILE_NAME_ADDITIONAL_BUSINESSES = "subscription_buyer_additional_businesses.csv"
TEST_FILE_INVOICING_EXCLUDED_NO_REASON = "subscription_buyer_invoicing_excluded_no_reason.csv"
TEST_FILE_INVOICING_EXCLUDED_VALID_REASON = "subscription_buyer_invoicing_excluded_valid_reason.csv"
INVALID_NIP_FILE = "sub_buyer_invalid_nip.csv"
TAX_GROUPS_NOT_SUPPORTED = "tax_groups_not_supported.csv"
TEST_FILE_FRENCH_BUYER = "french_buyer_valid.csv"
TEST_FILE_INVALID_FRENCH_BUYER = "french_buyer_invalid.csv"

CONTENT_MAPPING = {
    TEST_FILE_NAME_SINGLE_LINE: [
        {
            'buyer_id': '1',
            'entity_name': 'Firma testowa',
            'tax_id': '4444444444',
            'invoice_email': '<EMAIL>',
            'active': 'True',
            'is_verified': 'True',
            'batch_invoices': 'False',
            'address_details1': 'ul. Testowa 5',
            'city': 'Warszawa',
            'zipcode': '11-111',
            'invoicing_allowed': 'True',
            'invoicing_exclusion_reason': 'NULL',
        }
    ],
    TEST_FILE_NAME_MULTIPLE_LINES: [
        {
            'buyer_id': '1',
            'entity_name': 'Firma testowa',
            'tax_id': '4444444444',
            'invoice_email': '<EMAIL>',
            'active': 'True',
            'is_verified': 'True',
            'batch_invoices': 'False',
            'address_details1': 'ul. Testowa 5',
            'city': 'Warszawa',
            'zipcode': '11-111',
            'invoicing_allowed': 'True',
            'invoicing_exclusion_reason': 'NULL',
        },
        {
            'buyer_id': '2',
            'entity_name': 'John Test Co.',
            'tax_id': 'NULL',
            'invoice_email': '<EMAIL>',
            'active': 'True',
            'is_verified': 'True',
            'batch_invoices': 'False',
            'address_details1': 'ul. jakaś 13',
            'city': 'Warszawa',
            'zipcode': '11-111',
            'invoicing_allowed': 'True',
            'invoicing_exclusion_reason': 'NULL',
        },
        {
            'buyer_id': '3',
            'entity_name': 'Entity None',
            'tax_id': '4444444444',
            'invoice_email': '<EMAIL>',
            'active': 'True',
            'is_verified': 'True',
            'batch_invoices': 'False',
            'address_details1': 'ul. jakaś 13',
            'city': 'Warszawa',
            'zipcode': '11-111',
            'businesses': 'EMPTY',
            'invoicing_allowed': 'True',
            'invoicing_exclusion_reason': 'NULL',
        },
        {
            'buyer_id': '4',
            'entity_name': '',
            'tax_id': '',
            'invoice_email': '',
            'active': 'NULL',
            'is_verified': 'NULL',
            'batch_invoices': 'null',
            'address_details1': 'n/a',
            'city': 'nan',
            'zipcode': 'NaN',
            'invoicing_allowed': 'NULL',
            'invoicing_exclusion_reason': 'NULL',
        },
        {
            'buyer_id': '5',
            'entity_name': 'Entity5',
            'tax_id': '',
            'invoice_email': '',
            'active': '',
            'is_verified': '',
            'batch_invoices': '',
            'address_details1': '',
            'city': '',
            'zipcode': '',
            'businesses': '',
            'invoicing_allowed': 'True',
            'invoicing_exclusion_reason': 'NULL',
        },
    ],
    TEST_FILE_NAME_WITHOUT_TAX_ID: [
        {
            'buyer_id': '1',
            'entity_name': 'Some Company',
            'tax_id': 'NULL',
            'invoice_email': '<EMAIL>',
            'active': 'True',
            'is_verified': 'True',
            'batch_invoices': 'False',
            'address_details1': 'ul. jakaś 13',
            'city': 'Warszawa',
            'zipcode': '11-111',
            'invoicing_allowed': 'True',
            'invoicing_exclusion_reason': 'NULL',
        }
    ],
    TEST_FILE_NAME_ADDITIONAL_BUSINESSES: [
        {
            'buyer_id': '1',
            'entity_name': 'Firma testowa',
            'tax_id': '4444444444',
            'invoice_email': '<EMAIL>',
            'active': 'True',
            'is_verified': 'True',
            'batch_invoices': 'False',
            'address_details1': 'ul. Testowa 5',
            'city': 'Warszawa',
            'zipcode': '11-111',
            'businesses': '2222;3333',
            'invoicing_allowed': 'True',
            'invoicing_exclusion_reason': 'NULL',
        }
    ],
    TEST_FILE_NAME_INVALID_EMAIL: [
        {
            'buyer_id': '1',
            'entity_name': 'Firma testowa',
            'tax_id': '4444444444',
            'invoice_email': 'invalid mail',
            'active': 'True',
            'is_verified': 'True',
            'batch_invoices': 'False',
            'address_details1': 'ul. Testowa 5',
            'city': 'Warszawa',
            'zipcode': 'NULL',
            'invoicing_allowed': 'True',
            'invoicing_exclusion_reason': 'NULL',
        }
    ],
    TEST_FILE_NAME_INVALID_TAX_ID: [
        {
            'buyer_id': '1',
            'entity_name': 'Firma testowa',
            'tax_id': 'tax id zle',
            'invoice_email': 'invalid mail',
            'active': 'True',
            'is_verified': 'True',
            'batch_invoices': 'False',
            'address_details1': 'ul. Testowa 5',
            'city': 'Warszawa',
            'zipcode': 'NULL',
            'invoicing_allowed': 'True',
            'invoicing_exclusion_reason': 'NULL',
        }
    ],
    TEST_FILE_NAME_INVALID_TAX_ID2: [
        {
            'buyer_id': '1',
            'entity_name': 'Firma testowa',
            'tax_id': '4444444441',
            'invoice_email': 'invalid mail',
            'active': 'True',
            'is_verified': 'True',
            'batch_invoices': 'False',
            'address_details1': 'ul. Testowa 5',
            'city': 'Warszawa',
            'zipcode': 'NULL',
        }
    ],
    TEST_FILE_NAME_INVALID_BUSINESS_ID: [
        {
            'buyer_id': '3ĄŻŻŻŻIII',
            'entity_name': 'Firma testowa',
            'tax_id': '4444444444',
            'invoice_email': '<EMAIL>',
            'active': 'True',
            'is_verified': 'True',
            'batch_invoices': 'False',
            'address_details1': 'ul. Testowa 5',
            'city': 'Warszawa',
            'zipcode': 'NULL',
        }
    ],
    TEST_FILE_NAME_INVALID_BOOLEAN_FIELDS: [
        {
            'buyer_id': '1',
            'entity_name': 'Firma testowa',
            'tax_id': '4444444444',
            'invoice_email': '<EMAIL>',
            'active': 'weee',
            'is_verified': 'EMPTY',
            'batch_invoices': 'False',
            'address_details1': 'ul. Testowa 5',
            'city': 'Warszawa',
            'zipcode': 'NULL',
        }
    ],
    TEST_FILE_NAME_NOT_VERIFIED_BUYER: [
        {
            'buyer_id': '1',
            'entity_name': 'Firma testowa',
            'tax_id': '4444444444',
            'invoice_email': '<EMAIL>',
            'active': 'True',
            'is_verified': 'False',
            'batch_invoices': 'True',
            'address_details1': 'ul. Testowa 5',
            'city': 'Warszawa',
            'zipcode': 'NULL',
        }
    ],
    TEST_FILE_NOT_UNIQUE_BUSINESS_ID: [
        {
            'buyer_id': '1',
            'entity_name': 'Firma testowa',
            'tax_id': '4444444444',
            'invoice_email': '<EMAIL>',
            'active': 'True',
            'is_verified': 'False',
            'batch_invoices': 'True',
            'address_details1': 'ul. Testowa 5',
            'city': 'Warszawa',
            'zipcode': 'NULL',
        },
        {
            'buyer_id': '1',
            'entity_name': 'Firma testowa 2',
            'tax_id': '4444444444',
            'invoice_email': '<EMAIL>',
            'active': 'True',
            'is_verified': 'False',
            'batch_invoices': 'True',
            'address_details1': 'ul. Testowa 5',
            'city': 'Warszawa',
            'zipcode': 'NULL',
        },
    ],
    TEST_FILE_NOT_UNIQUE_ADDITIONAL_BUSINESS_IDS: [
        {
            'buyer_id': '1',
            'entity_name': 'Firma testowa',
            'tax_id': '4444444444',
            'invoice_email': '<EMAIL>',
            'active': 'True',
            'is_verified': 'False',
            'batch_invoices': 'True',
            'address_details1': 'ul. Testowa 5',
            'city': 'Warszawa',
            'zipcode': 'NULL',
            'businesses': '3333;4444;5555',
        },
        {
            'buyer_id': '2',
            'entity_name': 'Firma testowa 2',
            'tax_id': '4444444444',
            'invoice_email': '<EMAIL>',
            'active': 'True',
            'is_verified': 'False',
            'batch_invoices': 'True',
            'address_details1': 'ul. Testowa 5',
            'city': 'Warszawa',
            'zipcode': 'NULL',
            'businesses': '7777;4444;6666',
        },
        {
            'buyer_id': '3',
            'entity_name': 'Firma testowa 3',
            'tax_id': '4444444444',
            'invoice_email': '<EMAIL>',
            'active': 'True',
            'is_verified': 'False',
            'batch_invoices': 'True',
            'address_details1': 'ul. Testowa 5',
            'city': 'Warszawa',
            'zipcode': 'NULL',
            'businesses': '8888;7777',
        },
    ],
    TEST_FILE_INVOICING_EXCLUDED_NO_REASON: [
        {
            'buyer_id': '1',
            'entity_name': 'Firma testowa',
            'tax_id': '4444444444',
            'invoice_email': '<EMAIL>',
            'active': 'True',
            'is_verified': 'True',
            'batch_invoices': 'False',
            'address_details1': 'ul. Testowa 5',
            'city': 'Warszawa',
            'zipcode': '11-111',
            'invoicing_allowed': 'False',
            'invoicing_exclusion_reason': 'NULL',
        }
    ],
    TEST_FILE_INVOICING_EXCLUDED_VALID_REASON: [
        {
            'buyer_id': '1',
            'entity_name': 'Firma testowa',
            'tax_id': '4444444444',
            'invoice_email': '<EMAIL>',
            'active': 'True',
            'is_verified': 'True',
            'batch_invoices': 'False',
            'address_details1': 'ul. Testowa 5',
            'city': 'Warszawa',
            'zipcode': '11-111',
            'invoicing_allowed': 'False',
            'invoicing_exclusion_reason': 'Enterprise customer',
        }
    ],
    INVALID_NIP_FILE: [
        {
            'buyer_id': '1',
            'entity_name': 'Firma testowa',
            'tax_id': '1079842977',
            'invoice_email': '<EMAIL>',
            'active': 'True',
            'is_verified': 'True',
            'batch_invoices': 'False',
            'address_details1': 'ul. Testowa 5',
            'city': 'Warszawa',
            'zipcode': '11-111',
        },
    ],
    TAX_GROUPS_NOT_SUPPORTED: [
        {
            'buyer_id': '1',
            'entity_name': 'Firma testowa',
            'tax_id': '4444444444',
            'invoice_email': '<EMAIL>',
            'active': 'True',
            'is_verified': 'True',
            'batch_invoices': 'False',
            'address_details1': 'ul. Testowa 5',
            'city': 'Warszawa',
            'zipcode': '11-111',
            'invoicing_allowed': 'True',
            'invoicing_exclusion_reason': 'NULL',
            'tax_group_id': '1',
        },
    ],
    TEST_FILE_FRENCH_BUYER: [
        {
            'buyer_id': '1',
            'entity_name': 'La companie test',
            'tax_id': 'FR*********01',
            'invoice_email': '<EMAIL>',
            'active': 'True',
            'is_verified': 'True',
            'batch_invoices': 'False',
            'address_details1': '6 rue Francis de Croisset',
            'city': 'Paris',
            'zipcode': '75018',
            'invoicing_allowed': 'True',
            'invoicing_exclusion_reason': 'NULL',
            'tax_group_id': '1',
        },
    ],
    TEST_FILE_INVALID_FRENCH_BUYER: [
        {
            'buyer_id': '1',
            'entity_name': 'La companie test',
            'tax_id': 'FR*********01',
            'invoice_email': '<EMAIL>',
            'active': 'True',
            'is_verified': 'True',
            'batch_invoices': 'False',
            'address_details1': '6 rue Francis de Croisset',
            'city': 'Paris',
            'zipcode': '75018',
            'invoicing_allowed': 'True',
            'invoicing_exclusion_reason': 'NULL',
            'tax_group_id': '2',
        }
    ],
}

RECORDS_MAPPING = {
    TEST_FILE_NAME_SINGLE_LINE: {
        1: dict(
            entity_name='Firma testowa',
            tax_id='4444444444',
            invoice_email='<EMAIL>',
            active=True,
            is_verified=True,
            batch_invoices=False,
            address_details1='ul. Testowa 5',
            city='Warszawa',
            zipcode='11-111',
            invoicing_allowed=True,
            result='not processed',
            reason='SubscriptionBuyer not found',
        ),
    },
    TEST_FILE_NAME_MULTIPLE_LINES: {
        1: dict(
            entity_name='Firma testowa',
            tax_id='4444444444',
            invoice_email='<EMAIL>',
            active=True,
            is_verified=True,
            batch_invoices=False,
            address_details1='ul. Testowa 5',
            city='Warszawa',
            zipcode='11-111',
            invoicing_allowed=True,
            result='not processed',
            reason='SubscriptionBuyer not found',
        ),
        2: dict(
            entity_name='John Test Co.',
            invoice_email='<EMAIL>',
            active=True,
            is_verified=True,
            batch_invoices=False,
            address_details1='ul. jakaś 13',
            city='Warszawa',
            zipcode='11-111',
            invoicing_allowed=True,
            result='not processed',
            reason='SubscriptionBuyer not found',
        ),
        3: dict(
            entity_name='Entity None',
            tax_id='4444444444',
            invoice_email='<EMAIL>',
            active=True,
            is_verified=True,
            batch_invoices=False,
            address_details1='ul. jakaś 13',
            city='Warszawa',
            zipcode='11-111',
            invoicing_allowed=True,
            result='not processed',
            reason='SubscriptionBuyer not found',
            **{'businesses': ()},
        ),
        5: dict(
            entity_name='Entity5',
            invoicing_allowed=True,
            result='not processed',
            reason='SubscriptionBuyer not found',
        ),
    },
    TEST_FILE_NAME_ADDITIONAL_BUSINESSES: {
        1: dict(
            entity_name='Firma testowa',
            tax_id='4444444444',
            invoice_email='<EMAIL>',
            active=True,
            is_verified=True,
            batch_invoices=False,
            address_details1='ul. Testowa 5',
            city='Warszawa',
            zipcode='11-111',
            invoicing_allowed=True,
            result='not processed',
            reason='SubscriptionBuyer not found',
            **{'businesses': (2222, 3333)},
        ),
    },
}


def get_csv_attachment_data(content: str) -> list:
    return list(csv.reader(io.StringIO(content)))


@pytest.fixture(autouse=True)
def no_requests(monkeypatch):
    monkeypatch.setattr("requests.sessions.Session.request", MagicMock())


def load_records(file_path):
    output = io.StringIO()
    output.write(','.join(CSV_READ_FIELDS))
    output.write('\n')
    for row in CONTENT_MAPPING[file_path]:
        line = [row.get(field, 'NULL') for field in CSV_READ_FIELDS]
        output.write(','.join(line))
        output.write('\n')
    output.seek(0)
    data_frame = CSVLoader.read_csv(output)
    return CSVLoader.clean_data_frame(data_frame)  # pylint: disable=protected-access


@patch.object(CSVLoader, "load_records", load_records)
@override_settings(
    API_COUNTRY=Country.PL,
    COUNTRY_CONFIG=CountryConfig(Country.PL),
)
@override_settings(API_COUNTRY=Country.PL)
class TestCSVLoader(DjangoTestCase):
    @parameterized.expand(
        [
            (TEST_FILE_NAME_SINGLE_LINE,),
            (TEST_FILE_NAME_MULTIPLE_LINES,),
            (TEST_FILE_NAME_ADDITIONAL_BUSINESSES,),
        ]
    )
    def test_load_records(self, test_file_name):
        records = CSVLoader.load_records(test_file_name)
        expected = RECORDS_MAPPING.get(test_file_name)
        for buyer_id, expected_record in expected.items():
            expected_record['buyer_id'] = buyer_id

        self.assertDictEqual(records, expected)

    def test_load_records_invalid_tax_id(self):
        with self.assertRaisesRegex(ValidationError, ".*NIP must consist of exactly 10 digits.*"):
            CSVLoader.load_records(TEST_FILE_NAME_INVALID_TAX_ID)

    def test_load_records_invalid_tax_id2(self):
        with self.assertRaisesRegex(ValidationError, ".*Invalid NIP number.*"):
            CSVLoader.load_records(TEST_FILE_NAME_INVALID_TAX_ID2)

    def test_load_records_invalid_business_id(self):
        with self.assertRaisesMessage(
            ValueError, "invalid literal for int() with base 10: '3ĄŻŻŻŻIII'"
        ):
            CSVLoader.load_records(TEST_FILE_NAME_INVALID_BUSINESS_ID)

    def test_load_records_not_unique_business_id(self):
        with self.assertRaisesRegex(ValueError, '"buyer_id" values have to be unique.*1.*'):
            CSVLoader.load_records(TEST_FILE_NOT_UNIQUE_BUSINESS_ID)

    def test_load_records_not_unique_additional_business_ids(self):
        with self.assertRaisesRegex(
            ValueError, "Ids in \"businesses\" have to be unique along CSV.*4444.*7777.*"
        ):
            CSVLoader.load_records(TEST_FILE_NOT_UNIQUE_ADDITIONAL_BUSINESS_IDS)


@patch.object(S3CSVLoader, "load_records", load_records)
@override_settings(
    API_COUNTRY=Country.PL,
    COUNTRY_CONFIG=CountryConfig(Country.PL),
)
@patch('webapps.navision.tasks.subscription_buyer.update_entity_type_task.delay')
@patch('webapps.navision.tasks.buyer_merchant_integration.sync_buyer_with_merchant_task.delay')
class TestSubscriptionBuyerImportTool(DjangoTestCase):
    def setUp(self):
        super().setUp()
        self.user = baker.make(User)

        self.zipcode = baker.make(Region, type=Region.Type.ZIP, name="11-111")

        self.french_zipcode = baker.make(Region, type=Region.Type.ZIP, name='75018')

    def test_run_script_single_record(
        self,
        sync_buyer_mock,
        update_entity_type_mock,
    ):
        business = baker.make(Business, id=1111)
        subscription_buyer = tax_id_subscription_buyer_recipe.make(
            pk=1,
            tax_id=4444444444,
            businesses=[business],
            invoice_address=baker.make(InvoiceAddress, zipcode=self.zipcode),
        )

        batch_update_subscription_buyers_task(
            TEST_FILE_NAME_SINGLE_LINE, "<EMAIL>", self.user.id
        )

        _, attachment_content, _ = mail.outbox[0].attachments[0]
        report = get_csv_attachment_data(attachment_content)
        self.assertEqual(len(report), 2)
        self.assertIn('success', report[1][-1])

        subscription_buyer.refresh_from_db()
        business.refresh_from_db()
        self.assertEqual(business.buyer, subscription_buyer)
        self.assertEqual(subscription_buyer.verified_by, self.user)
        self.assertEqual(subscription_buyer.entity_name, 'Firma testowa')
        self.assertEqual(subscription_buyer.tax_id, '4444444444')
        self.assertTrue(subscription_buyer.vat_registered)
        self.assertEqual(subscription_buyer.invoice_email, '<EMAIL>')
        self.assertTrue(subscription_buyer.active)
        self.assertTrue(subscription_buyer.is_verified)
        self.assertFalse(subscription_buyer.batch_invoices)

        self.assertIsNotNone(subscription_buyer.invoice_address)
        invoice_address = subscription_buyer.invoice_address
        self.assertEqual(invoice_address.address_details1, 'ul. Testowa 5')
        self.assertEqual(invoice_address.city, 'Warszawa')
        self.assertEqual(invoice_address.zipcode, self.zipcode)

        self.assertEqual(sync_buyer_mock.call_count, 1)

    def test_run_script_multiple_records(  # pylint: disable=too-many-statements
        self,
        sync_buyer_mock,
        update_entity_type_mock,
    ):
        business1 = baker.make(Business, id=1111)
        subscription_buyer1 = baker.make(
            SubscriptionBuyer,
            pk=1,
            tax_id=4444444444,
            businesses=[business1],
            invoice_address=baker.make(InvoiceAddress, zipcode=self.zipcode),
        )

        business2 = baker.make(Business, id=2222)
        baker.make(
            SubscriptionBuyer,
            pk=2,
            businesses=[business2],
            invoice_address=baker.make(InvoiceAddress, zipcode=self.zipcode),
        )

        business3 = baker.make(Business, id=3333)
        subscription_buyer3 = baker.make(
            SubscriptionBuyer,
            pk=3,
            entity_name='Example Co.',
            businesses=[business3],
            invoice_address=baker.make(InvoiceAddress, zipcode=self.zipcode),
        )
        business4 = baker.make(Business, id=4444)
        subscription_buyer4 = baker.make(
            SubscriptionBuyer,
            pk=4,
            businesses=[business4],
            invoice_address=baker.make(InvoiceAddress, zipcode=self.zipcode),
        )
        business5 = baker.make(Business, id=5555)
        subscription_buyer5 = tax_id_subscription_buyer_recipe.make(
            pk=5,
            businesses=[business5],
            active=False,
            is_verified=True,
            batch_invoices=True,
            invoice_address=baker.make(InvoiceAddress, zipcode=self.zipcode),
        )

        batch_update_subscription_buyers_task(
            TEST_FILE_NAME_MULTIPLE_LINES, "<EMAIL>", self.user.id
        )

        _, attachment_content, _ = mail.outbox[0].attachments[0]
        report = get_csv_attachment_data(attachment_content)
        self.assertEqual(len(report), 5)
        self.assertIn('success', report[1][-1])
        self.assertIn('success', report[2][-1])
        self.assertIn('success', report[3][-1])
        self.assertIn('success', report[4][-1])

        subscription_buyer1.refresh_from_db()
        business1.refresh_from_db()

        self.assertEqual(business1.buyer, subscription_buyer1)
        self.assertEqual(subscription_buyer1.verified_by, self.user)
        self.assertEqual(subscription_buyer1.entity_name, 'Firma testowa')
        self.assertEqual(subscription_buyer1.tax_id, '4444444444')
        self.assertTrue(subscription_buyer1.vat_registered)
        self.assertEqual(subscription_buyer1.invoice_email, '<EMAIL>')
        self.assertTrue(subscription_buyer1.active)
        self.assertTrue(subscription_buyer1.is_verified)
        self.assertFalse(subscription_buyer1.batch_invoices)

        self.assertIsNotNone(subscription_buyer1.invoice_address)
        invoice_address = subscription_buyer1.invoice_address
        self.assertEqual(invoice_address.address_details1, 'ul. Testowa 5')
        self.assertEqual(invoice_address.city, 'Warszawa')
        self.assertEqual(invoice_address.zipcode, self.zipcode)

        subscription_buyer3.refresh_from_db()
        business3.refresh_from_db()

        self.assertEqual(business3.buyer, subscription_buyer3)
        self.assertEqual(subscription_buyer3.verified_by, self.user)
        self.assertEqual(subscription_buyer3.entity_name, 'Entity None')
        self.assertEqual(subscription_buyer3.tax_id, '4444444444')
        self.assertTrue(subscription_buyer3.vat_registered)
        self.assertEqual(subscription_buyer3.invoice_email, '<EMAIL>')
        self.assertTrue(subscription_buyer3.active)
        self.assertTrue(subscription_buyer3.is_verified)
        self.assertFalse(subscription_buyer3.batch_invoices)

        self.assertIsNotNone(subscription_buyer3.invoice_address)
        invoice_address = subscription_buyer3.invoice_address
        self.assertEqual(invoice_address.address_details1, 'ul. jakaś 13')
        self.assertEqual(invoice_address.city, 'Warszawa')
        self.assertEqual(invoice_address.zipcode, self.zipcode)

        last_update = subscription_buyer4.updated
        subscription_buyer4.refresh_from_db()
        self.assertEqual(subscription_buyer4.updated, last_update)

        subscription_buyer5.refresh_from_db()
        business5.refresh_from_db()

        self.assertEqual(business5.buyer, subscription_buyer5)
        self.assertEqual(subscription_buyer5.entity_name, 'Entity5')
        self.assertEqual(subscription_buyer5.tax_id, '***********')
        self.assertEqual(subscription_buyer5.invoice_email, '<EMAIL>')
        self.assertTrue(subscription_buyer5.is_verified)
        self.assertFalse(subscription_buyer5.active)
        self.assertTrue(subscription_buyer5.batch_invoices)

        self.assertEqual(sync_buyer_mock.call_count, 4)

    def test_run_script_invalid_email(self, sync_buyer_mock, update_entity_type_mock):
        business = baker.make(Business, id=1111)
        subscription_buyer = baker.make(
            SubscriptionBuyer,
            pk=1,
            tax_id=4444444444,
            businesses=[business],
            invoice_address=baker.make(InvoiceAddress, zipcode=self.zipcode),
        )

        batch_update_subscription_buyers_task(
            TEST_FILE_NAME_INVALID_EMAIL, "<EMAIL>", self.user.id
        )

        _, attachment_content, _ = mail.outbox[0].attachments[0]
        report = get_csv_attachment_data(attachment_content)
        self.assertEqual(len(report), 2)
        self.assertIn('failed', report[1][-1])
        self.assertIn('Enter a valid email address', report[1][-2])

        subscription_buyer.refresh_from_db()
        business.refresh_from_db()

        self.assertEqual(sync_buyer_mock.call_count, 0)

    @parameterized.expand(
        [
            (TEST_FILE_NAME_INVALID_TAX_ID,),
            (TEST_FILE_NAME_INVALID_TAX_ID2,),
        ]
    )
    def test_run_script_invalid_tax_id(self, sync_buyer_mock, update_entity_type_mock, file_name):
        business = baker.make(Business, id=1111)
        baker.make(
            SubscriptionBuyer,
            tax_id=4444444444,
            businesses=[business],
            invoice_address=baker.make(InvoiceAddress, zipcode=self.zipcode),
        )

        batch_update_subscription_buyers_task(file_name, '<EMAIL>', self.user.id)

        self.assertIn('No report', mail.outbox[0].body)
        self.assertEqual(sync_buyer_mock.call_count, 0)

    def test_run_script_invalid_boolean_fields(self, sync_buyer_mock, update_entity_type_mock):
        business = baker.make(Business, id=1111)
        subscription_buyer = baker.make(
            SubscriptionBuyer,
            pk=1,
            tax_id=4444444444,
            businesses=[business],
            invoice_address=baker.make(InvoiceAddress, zipcode=self.zipcode),
        )

        batch_update_subscription_buyers_task(
            TEST_FILE_NAME_INVALID_BOOLEAN_FIELDS, '<EMAIL>', self.user.id
        )

        _, attachment_content, _ = mail.outbox[0].attachments[0]
        report = get_csv_attachment_data(attachment_content)
        self.assertEqual(len(report), 2)
        self.assertIn('failed', report[1][-1], report)
        self.assertIn('is_verified', report[1][-2], report)
        self.assertIn('active', report[1][-2], report)

        subscription_buyer.refresh_from_db()
        business.refresh_from_db()

        self.assertEqual(sync_buyer_mock.call_count, 0)

    def test_run_script_invalid_business_id(self, sync_buyer_mock, update_entity_type_mock):
        business = baker.make(Business, id=1111)
        baker.make(
            SubscriptionBuyer,
            tax_id=4444444444,
            businesses=[business],
            invoice_address=baker.make(InvoiceAddress, zipcode=self.zipcode),
        )

        batch_update_subscription_buyers_task(
            TEST_FILE_NAME_INVALID_BUSINESS_ID, "<EMAIL>", self.user.id
        )

        self.assertIn('invalid literal', mail.outbox[0].body)
        self.assertEqual(sync_buyer_mock.call_count, 0)

    def test_update_email_sent(self, sync_buyer_mock, update_entity_type_mock):
        business = baker.make(Business, id=1111)
        baker.make(
            SubscriptionBuyer,
            pk=1,
            tax_id=4444444444,
            businesses=[business],
            invoice_address=baker.make(InvoiceAddress, zipcode=self.zipcode),
        )
        batch_update_subscription_buyers_task(
            TEST_FILE_NAME_SINGLE_LINE, "<EMAIL>", self.user.id
        )

        self.assertEqual(len(mail.outbox), 1)
        self.assertEqual(mail.outbox[0].to, ["<EMAIL>"])
        _, attachment_content, mimetype = mail.outbox[0].attachments[0]
        report = get_csv_attachment_data(attachment_content)
        self.assertEqual(mimetype, "text/csv")
        self.assertEqual(len(report), 2)
        csv_read_fields_with_reason_and_result = CSV_READ_FIELDS + ['reason', 'result']
        self.assertEqual(
            set(csv_read_fields_with_reason_and_result),
            set(report[0]),
        )
        self.assertEqual(
            report[-1][:-2],
            [
                CONTENT_MAPPING[TEST_FILE_NAME_SINGLE_LINE][0].get(field, 'NULL')
                for field in CSV_READ_FIELDS
            ],
            report,
        )
        self.assertEqual(sync_buyer_mock.call_count, 1)

    def test_update_not_matched_business(self, sync_buyer_mock, update_entity_type_mock):
        business = baker.make(Business, id=1)
        subscription_buyer = baker.make(
            SubscriptionBuyer,
            tax_id=4444444444,
            businesses=[business],
            invoice_address=baker.make(InvoiceAddress, zipcode=self.zipcode),
        )
        last_update = subscription_buyer.updated
        batch_update_subscription_buyers_task(
            TEST_FILE_NAME_SINGLE_LINE, '<EMAIL>', self.user.id
        )
        subscription_buyer.refresh_from_db()
        self.assertEqual(subscription_buyer.updated, last_update)

        _, attachment_content, _ = mail.outbox[0].attachments[0]
        report = get_csv_attachment_data(attachment_content)
        self.assertEqual(len(report), 2)
        self.assertEqual('not processed', report[1][-1])
        self.assertEqual('SubscriptionBuyer not found', report[1][-2])
        self.assertEqual(sync_buyer_mock.call_count, 0)

    def test_update_when_more_than_one_business(self, sync_buyer_mock, update_entity_type_mock):
        business = baker.make(Business, id=1111)
        business2 = baker.make(Business, id=2)
        subscription_buyer = baker.make(
            SubscriptionBuyer,
            pk=1,
            tax_id=4444444444,
            businesses=[business, business2],
            invoice_address=baker.make(InvoiceAddress, zipcode=self.zipcode),
        )

        batch_update_subscription_buyers_task(
            TEST_FILE_NAME_SINGLE_LINE, '<EMAIL>', self.user.id
        )

        _, attachment_content, _ = mail.outbox[0].attachments[0]
        report = get_csv_attachment_data(attachment_content)
        self.assertEqual(len(report), 2)
        self.assertIn('success', report[1][-1])

        subscription_buyer.refresh_from_db()
        business.refresh_from_db()

        self.assertIn(business, subscription_buyer.businesses.all())
        self.assertIn(business2, subscription_buyer.businesses.all())

        self.assertEqual(business.buyer, subscription_buyer)
        self.assertEqual(subscription_buyer.verified_by, self.user)
        self.assertEqual(subscription_buyer.entity_name, 'Firma testowa')
        self.assertEqual(subscription_buyer.tax_id, '4444444444')
        self.assertTrue(subscription_buyer.vat_registered)
        self.assertEqual(subscription_buyer.invoice_email, '<EMAIL>')
        self.assertTrue(subscription_buyer.active)
        self.assertTrue(subscription_buyer.is_verified)
        self.assertFalse(subscription_buyer.batch_invoices)

        self.assertIsNotNone(subscription_buyer.invoice_address)
        invoice_address = subscription_buyer.invoice_address
        self.assertEqual(invoice_address.address_details1, 'ul. Testowa 5')
        self.assertEqual(invoice_address.city, 'Warszawa')
        self.assertEqual(invoice_address.zipcode, self.zipcode)

        self.assertEqual(sync_buyer_mock.call_count, 1)

    @parameterized.expand(
        [
            ("", False),
            (None, False),
            ("*********", False),
            ("*********", True),
        ],
    )
    def test_update_when_tax_id_mismatch_and_tax_id_in_csv(
        self,
        sync_buyer_mock,
        update_entity_type_mock,
        tax_id,
        is_verified,
    ):
        business = baker.make(Business, id=1111)
        if tax_id:
            subscription_buyer = tax_id_subscription_buyer_recipe.make(
                pk=1,
                tax_id=tax_id,
                businesses=[business],
                invoice_address=baker.make(InvoiceAddress, zipcode=self.zipcode),
                is_verified=is_verified,
            )
        else:
            subscription_buyer = sales_subscription_buyer_recipe.make(
                pk=1,
                tax_id=tax_id,
                businesses=[business],
                invoice_address=baker.make(InvoiceAddress, zipcode=self.zipcode),
                is_verified=is_verified,
            )
        batch_update_subscription_buyers_task(
            TEST_FILE_NAME_SINGLE_LINE, '<EMAIL>', self.user.id
        )
        subscription_buyer.refresh_from_db()
        self.assertEqual(subscription_buyer.tax_id, '4444444444')
        self.assertTrue(subscription_buyer.vat_registered)

        _, attachment_content, _ = mail.outbox[-1].attachments[0]
        report = get_csv_attachment_data(attachment_content)
        self.assertEqual(len(report), 2)
        self.assertIn("success", report[1][-1])

    def test_update_cannot_unverify_buyer(self, sync_buyer_mock, update_entity_type_mock):
        business = baker.make(Business, id='1111')
        subscription_buyer = tax_id_subscription_buyer_recipe.make(
            pk=1,
            tax_id="4444444444",
            invoice_address=baker.make(InvoiceAddress, zipcode=self.zipcode),
            businesses=[business],
            is_verified=True,
        )
        last_update = subscription_buyer.updated
        batch_update_subscription_buyers_task(
            TEST_FILE_NAME_NOT_VERIFIED_BUYER,
            "<EMAIL>",
            self.user.id,
        )
        subscription_buyer.refresh_from_db()
        self.assertEqual(subscription_buyer.updated, last_update)
        _, attachment_content, _ = mail.outbox[0].attachments[0]
        report = get_csv_attachment_data(attachment_content)
        self.assertEqual(len(report), 2)
        self.assertIn(
            "Can't unverify buyer once is_verified is checked",
            report[1][-2],
        )
        self.assertIn("failed", report[1][-1])
        self.assertEqual(sync_buyer_mock.call_count, 0)

    def test_update_run_twice(self, sync_buyer_mock, update_entity_type_mock):
        business = baker.make(Business, id=1111)
        subscription_buyer = baker.make(
            SubscriptionBuyer,
            pk=1,
            tax_id='4444444444',
            businesses=[business],
            invoice_address=baker.make(InvoiceAddress, zipcode=self.zipcode),
        )
        batch_update_subscription_buyers_task(
            TEST_FILE_NAME_SINGLE_LINE, '<EMAIL>', self.user.id
        )
        batch_update_subscription_buyers_task(
            TEST_FILE_NAME_SINGLE_LINE, '<EMAIL>', self.user.id
        )

        self.assertEqual(InvoiceAddress.objects.count(), 1)

        self.assertEqual(len(mail.outbox), 2)

        subscription_buyer.refresh_from_db()
        business.refresh_from_db()
        self.assertEqual(business.buyer, subscription_buyer)
        self.assertEqual(subscription_buyer.verified_by, self.user)
        self.assertEqual(subscription_buyer.entity_name, 'Firma testowa')
        self.assertEqual(subscription_buyer.tax_id, '4444444444')
        self.assertTrue(subscription_buyer.vat_registered)
        self.assertEqual(subscription_buyer.invoice_email, '<EMAIL>')
        self.assertTrue(subscription_buyer.active)
        self.assertTrue(subscription_buyer.is_verified)
        self.assertFalse(subscription_buyer.batch_invoices)

        self.assertIsNotNone(subscription_buyer.invoice_address)
        invoice_address = subscription_buyer.invoice_address
        self.assertEqual(invoice_address.address_details1, 'ul. Testowa 5')
        self.assertEqual(invoice_address.city, 'Warszawa')
        self.assertEqual(invoice_address.zipcode, self.zipcode)

        self.assertEqual(sync_buyer_mock.call_count, 2)

    def test_update_add_additional_businesses(self, sync_buyer_mock, update_entity_type_mock):
        business = baker.make(Business, id=1111)
        business2 = baker.make(
            Business,
            id=2222,
            buyer=baker.make(SubscriptionBuyer, id=7),
        )  # added to buyer
        business3 = baker.make(
            Business,
            id=3333,
            buyer=baker.make(SubscriptionBuyer, id=8),
        )  # added to buyer
        business4 = baker.make(
            Business,
            id=4444,
            buyer=baker.make(SubscriptionBuyer, id=9),
        )  # not added anywhere
        subscription_buyer = baker.make(
            SubscriptionBuyer,
            id=1,
            tax_id=4444444444,
            businesses=[business],
            invoice_address=baker.make(InvoiceAddress, zipcode=self.zipcode),
        )

        batch_update_subscription_buyers_task(
            TEST_FILE_NAME_ADDITIONAL_BUSINESSES, '<EMAIL>', self.user.id
        )

        _, attachment_content, _ = mail.outbox[0].attachments[0]
        report = get_csv_attachment_data(attachment_content)
        self.assertEqual(len(report), 2)
        self.assertEqual('success', report[1][-1])

        subscription_buyer.refresh_from_db()
        business.refresh_from_db()
        business2.refresh_from_db()
        business3.refresh_from_db()
        business4.refresh_from_db()

        # objects general check
        self.assertEqual(SubscriptionBuyer.objects.count(), 4)
        self.assertEqual(Business.objects.count(), 4)

        self.assertEqual(sync_buyer_mock.call_count, 1)
        self.assertEqual(business.buyer_id, 1)
        self.assertEqual(business2.buyer_id, 1)
        self.assertEqual(business3.buyer_id, 1)
        self.assertEqual(business4.buyer_id, 9)

        self.assertEqual(BusinessChange.objects.count(), 2)
        change_2222 = BusinessChange.objects.get(business_id=2222)
        change_3333 = BusinessChange.objects.get(business_id=3333)
        self.assertEqual(change_2222.operator_id, self.user.id)
        self.assertEqual(
            json.loads(change_2222.data),
            {'business.buyer_id': [7, 1]},
        )
        self.assertEqual(
            json.loads(change_2222.metadata),
            {
                'task': 'batch_update_subscription_buyers_task',
            },
        )
        self.assertEqual(change_3333.operator_id, self.user.id)
        self.assertEqual(
            json.loads(change_3333.data),
            {'business.buyer_id': [8, 1]},
        )
        self.assertEqual(
            json.loads(change_3333.metadata),
            {
                'task': 'batch_update_subscription_buyers_task',
            },
        )

    def test_nip_validation_fail_email_no_update(
        self,
        sync_buyer_mock,
        update_entity_type_mock,
    ):
        business = baker.make(Business, id=1111)
        subscription_buyer = tax_id_subscription_buyer_recipe.make(
            pk=1,
            tax_id=4444444444,
            businesses=[business],
            invoice_address=baker.make(InvoiceAddress, zipcode=self.zipcode),
        )

        batch_update_subscription_buyers_task(INVALID_NIP_FILE, "<EMAIL>", self.user.id)

        mail_body = mail.outbox[0].body
        self.assertIn(CONTENT_MAPPING['sub_buyer_invalid_nip.csv'][0]['tax_id'], mail_body)
        self.assertIn('Invalid NIP', mail_body)
        business.refresh_from_db()
        self.assertEqual(subscription_buyer.tax_id, 4444444444)
        self.assertEqual(sync_buyer_mock.call_count, 0)

    def test_tax_groups_not_supported_but_given(
        self,
        sync_buyer_mock,
        update_entity_type_mock,
    ):
        business = baker.make(Business, id=1111)
        baker.make(TaxGroup, pk=1, name='Poland')
        baker.make(
            SubscriptionBuyer,
            pk=1,
            tax_id='4444444444',
            businesses=[business],
            invoice_address=baker.make(InvoiceAddress, zipcode=self.zipcode),
        )

        batch_update_subscription_buyers_task(
            TAX_GROUPS_NOT_SUPPORTED, "<EMAIL>", self.user.id
        )
        _, attachment_content, _ = mail.outbox[0].attachments[0]
        report = get_csv_attachment_data(attachment_content)

        self.assertEqual(len(report), 2)
        self.assertIn('failed', report[1][-1])
        self.assertRegex(report[1][-2], '.*tax_group_id.*Tax Groups are not supported.*')
        self.assertEqual(sync_buyer_mock.call_count, 0)

    def test_run_script_invoicing_excluded_valid_reason(
        self,
        sync_buyer_mock,
        update_entity_type_mock,
    ):
        business = baker.make(Business, id=1111)
        subscription_buyer = tax_id_subscription_buyer_recipe.make(
            pk=1,
            tax_id=4444444444,
            businesses=[business],
            invoice_address=baker.make(InvoiceAddress, zipcode=self.zipcode),
            invoicing_allowed=True,
        )

        batch_update_subscription_buyers_task(
            TEST_FILE_INVOICING_EXCLUDED_VALID_REASON, "<EMAIL>", self.user.id
        )

        _, attachment_content, _ = mail.outbox[0].attachments[0]
        report = get_csv_attachment_data(attachment_content)
        self.assertEqual(len(report), 2)
        self.assertIn('success', report[1][-1])
        self.assertEqual(sync_buyer_mock.call_count, 1)
        subscription_buyer.refresh_from_db()
        self.assertEqual(subscription_buyer.invoicing_allowed, False)
        self.assertEqual(subscription_buyer.invoicing_exclusion_reason, 'Enterprise customer')

    def test_run_script_invoicing_excluded_no_reason(
        self, sync_buyer_mock, update_entity_type_mock
    ):
        business = baker.make(Business, id=1111)
        subscription_buyer = tax_id_subscription_buyer_recipe.make(
            pk=1,
            tax_id=4444444444,
            businesses=[business],
            invoice_address=baker.make(InvoiceAddress, zipcode=self.zipcode),
            invoicing_allowed=True,
        )

        batch_update_subscription_buyers_task(
            TEST_FILE_INVOICING_EXCLUDED_NO_REASON, "<EMAIL>", self.user.id
        )

        _, attachment_content, _ = mail.outbox[0].attachments[0]
        report = get_csv_attachment_data(attachment_content)
        self.assertEqual(len(report), 2)
        self.assertIn('failed', report[1][-1])
        self.assertEqual(sync_buyer_mock.call_count, 0)
        subscription_buyer.refresh_from_db()
        self.assertEqual(subscription_buyer.invoicing_allowed, True)

    @override_settings(
        API_COUNTRY=Country.FR,
        NAVISION_USE_TAX_GROUPS=True,
    )
    def test_run_script_valid_french_buyer(self, sync_buyer_mock, update_entity_type_mock):
        business = baker.make(Business, id=1111)
        tax_group = baker.make(TaxGroup, pk=1, name='France')
        subscription_buyer = tax_id_subscription_buyer_recipe.make(
            pk=1,
            tax_id='FR*********01',
            businesses=[business],
            invoice_address=baker.make(InvoiceAddress, zipcode=self.french_zipcode),
            tax_group=tax_group,
        )

        batch_update_subscription_buyers_task(
            TEST_FILE_FRENCH_BUYER, "<EMAIL>", self.user.id
        )

        _, attachment_content, _ = mail.outbox[0].attachments[0]
        report = get_csv_attachment_data(attachment_content)
        self.assertEqual(len(report), 2)
        self.assertIn('success', report[1][-1])

        subscription_buyer.refresh_from_db()
        business.refresh_from_db()
        self.assertEqual(business.buyer, subscription_buyer)
        self.assertEqual(subscription_buyer.verified_by, self.user)
        self.assertEqual(subscription_buyer.entity_name, 'La companie test')
        self.assertEqual(subscription_buyer.tax_id, 'FR*********01')
        self.assertTrue(subscription_buyer.vat_registered)
        self.assertEqual(subscription_buyer.invoice_email, '<EMAIL>')
        self.assertTrue(subscription_buyer.active)
        self.assertTrue(subscription_buyer.is_verified)
        self.assertFalse(subscription_buyer.batch_invoices)

        self.assertIsNotNone(subscription_buyer.invoice_address)
        invoice_address = subscription_buyer.invoice_address
        self.assertEqual(invoice_address.address_details1, '6 rue Francis de Croisset')
        self.assertEqual(invoice_address.city, 'Paris')
        self.assertEqual(invoice_address.zipcode, self.french_zipcode)

        self.assertEqual(sync_buyer_mock.call_count, 1)

    @override_settings(
        API_COUNTRY=Country.FR,
        NAVISION_USE_TAX_GROUPS=True,
    )
    def test_run_script_invalid_french_buyer_no_tax_group(
        self, sync_buyer_mock, update_entity_type_mock
    ):
        business = baker.make(Business, id=1111)
        tax_group = baker.make(TaxGroup, pk=1, name='France')
        tax_id_subscription_buyer_recipe.make(
            pk=1,
            tax_id='FR*********01',
            businesses=[business],
            invoice_address=baker.make(InvoiceAddress, zipcode=self.french_zipcode),
            tax_group=tax_group,
        )

        batch_update_subscription_buyers_task(
            TEST_FILE_INVALID_FRENCH_BUYER, "<EMAIL>", self.user.id
        )

        _, attachment_content, _ = mail.outbox[0].attachments[0]
        report = get_csv_attachment_data(attachment_content)
        self.assertEqual(len(report), 2)
        self.assertIn('failed', report[1][-1])
        self.assertRegex(report[1][-2], '.*tax_group_id.*object does not exist.*')
        self.assertEqual(sync_buyer_mock.call_count, 0)
