import csv
import datetime
import io
from unittest import TestCase
from unittest.mock import MagicMock, patch

import pandas as pd
import pytest
from django.core import mail
from django.test import TestCase as DjangoTestCase
from django.test import override_settings
from model_bakery import baker
from parameterized import parameterized

from lib.tools import tznow
from webapps.admin_extra.tasks.mass_billing_offers_changer import (
    CSVLoader,
    PurchaseResult,
    ValidationException,
    business_and_offer_validation,
    mass_billing_offers_changer_task,
    new_purchase,
)
from webapps.admin_extra.tasks.tests.test_subscription_buyer_update_task import (
    get_csv_attachment_data,
)
from webapps.admin_extra.tasks.utils import CSVLoaderMaxRecordsInBatchError, CSVLoaderParseError
from webapps.billing.enums import SubscriptionStatus
from webapps.billing.models import BillingSubscription
from webapps.billing.tests.admin.test_subscription import TestBillingSubscriptionMixin
from webapps.billing.tests.utils import billing_business, billing_subscription
from webapps.business.models import Business
from webapps.user.models import User


def csv_load_mocked(rows: list[dict]):
    output = io.StringIO()
    fieldnames = ['business_id', 'offer_id']
    writer = csv.DictWriter(output, fieldnames=fieldnames, delimiter=',')
    writer.writeheader()
    writer.writerows(rows)

    output.seek(0)
    return CSVLoader.read_csv(output)


@pytest.mark.django_db
class CSVLoaderTestCase(TestCase):
    @patch('webapps.admin_extra.tasks.utils.AdminImporterS3.open_file', MagicMock())
    @patch.object(CSVLoader, 'read_csv')
    def test_load_too_many_records_data(self, data_frame_mock):
        data_frame_mock.return_value = pd.DataFrame(range(CSVLoader.max_records_in_batch + 1))

        with self.assertRaisesRegex(CSVLoaderMaxRecordsInBatchError, 'There is more than*'):
            CSVLoader.load_from_file('file')

    @parameterized.expand([(None,), ('NULL',), ('abc',), ('',)])
    @patch.object(CSVLoader, 'load_from_file', csv_load_mocked)
    def test_clean_data_frame_when_error__business_id(self, entry_data):
        with self.assertRaisesRegex(CSVLoaderParseError, 'CSV loading error.*'):
            CSVLoader.load_from_file(
                [
                    {
                        'business_id': entry_data,
                        'offer_id': 1,
                    }
                ]
            )

    @parameterized.expand([(None,), ('NULL',), ('abc',), ('',)])
    @patch.object(CSVLoader, 'load_from_file', csv_load_mocked)
    def test_clean_data_frame_when_error__offer_id(self, entry_data):
        with self.assertRaisesRegex(CSVLoaderParseError, 'CSV loading error.*'):
            CSVLoader.load_from_file(
                [
                    {
                        'business_id': 1,
                        'offer_id': entry_data,
                    }
                ]
            )

    @patch.object(CSVLoader, 'load_from_file', csv_load_mocked)
    def test_clean_data_frames_ok(self):
        data_frames = CSVLoader.load_from_file(
            [
                {
                    'business_id': '1',
                    'offer_id': '2',
                },
                {
                    'business_id': '11',
                    'offer_id': '22',
                },
                {
                    'business_id': '111',
                    'offer_id': '222',
                },
            ]
        )

        records = CSVLoader.clean_data_frames(data_frames, business_index=True)

        self.assertEqual(len(records), 3)

        self.assertEqual(records[1]['offer_id'], 2)
        self.assertEqual(records[1]['result'], 'SUCCESS')
        self.assertFalse(records[1]['message'])

        self.assertEqual(records[11]['offer_id'], 22)
        self.assertEqual(records[11]['result'], 'SUCCESS')
        self.assertFalse(records[11]['message'])

        self.assertEqual(records[111]['offer_id'], 222)
        self.assertEqual(records[111]['result'], 'SUCCESS')
        self.assertFalse(records[111]['message'])


class BusinessAndOfferMixin(TestBillingSubscriptionMixin):
    def setUp(self):  # pylint: disable=invalid-name
        super().setUp()
        self.operator = baker.make(User)

        self.business1 = billing_business(status=Business.Status.PAID)
        self.subscription1 = billing_subscription(
            business=self.business1,
            date_start=tznow(),
            status=SubscriptionStatus.ACTIVE,
            next_billing_date=tznow() + datetime.timedelta(days=4),
        )

        self.business2 = billing_business(status=Business.Status.PAID)
        self.subscription2 = billing_subscription(
            business=self.business2,
            date_start=tznow(),
            status=SubscriptionStatus.ACTIVE,
            next_billing_date=tznow() + datetime.timedelta(days=3),
        )


@pytest.mark.django_db
class BusinessAndOfferValidationTestCase(
    BusinessAndOfferMixin,
    TestCase,
):
    def test_business_does_not_exist(self):
        with self.assertRaisesRegex(ValidationException, 'Business does not exist'):
            business_and_offer_validation(
                business=None,
                offer_id=self.offer.id,
            )

    def test_business_new_billing_set_to_false(self):
        self.business1.has_new_billing = False
        self.business1.save()

        with self.assertRaisesRegex(
            ValidationException,
            'Business.has_new_billing flag is set to False',
        ):
            business_and_offer_validation(
                business=self.business1,
                offer_id=self.offer.id,
            )

    def test_business_payment_source_mismatch(self):
        self.business1.payment_source = Business.PaymentSource.OFFLINE
        self.business1.save()

        with self.assertRaisesRegex(
            ValidationException,
            'Payment source different from Booksy Billing.*(current source: Offline)',
        ):
            business_and_offer_validation(
                business=self.business1,
                offer_id=self.offer.id,
            )

    def test_business_status_mismatch(self):
        self.business1.status = Business.Status.OVERDUE
        self.business1.save()

        with self.assertRaisesRegex(
            ValidationException,
            'Business status different from: PAID.*(current status: Payment Overdue Active)',
        ):
            business_and_offer_validation(
                business=self.business1,
                offer_id=self.offer.id,
            )

    def test_business_without_subscription(self):
        self.subscription1.delete()

        with self.assertRaisesRegex(
            ValidationException,
            'Business has not an active subscription.',
        ):
            business_and_offer_validation(
                business=self.business1,
                offer_id=self.offer.id,
            )

    def test_business_with_pending_subscription(self):
        baker.make(
            BillingSubscription,
            business=self.business1,
            date_start=tznow() + datetime.timedelta(days=3),
        )

        with self.assertRaisesRegex(
            ValidationException,
            '.*\n.*Business has at least one pending subscription.*',
        ):
            business_and_offer_validation(
                business=self.business1,
                offer_id=self.offer.id,
            )

    def test_subscription_form_validation_failed(self):
        self.subscription1.next_billing_date = tznow()
        self.subscription1.save()

        with self.assertRaisesRegex(
            ValidationException,
            '.*\n.*The start date must be in the future.*',
        ):
            business_and_offer_validation(
                business=self.business1,
                offer_id=self.offer.id,
            )

    def test_ok(self):
        purchase_from = business_and_offer_validation(
            business=self.business1,
            offer_id=self.offer.id,
        )

        self.assertTrue(purchase_from)


@override_settings(SAVE_HISTORY=True)
class NewPurchaseTestCase(BusinessAndOfferMixin, DjangoTestCase):
    @patch('webapps.billing.admin_views.SubscriptionCreator.new_purchase')
    def test_purchase_error(self, purchase_mocked):
        purchase_mocked.return_value = {
            'error': 'Some error',
        }

        purchase_form = business_and_offer_validation(
            business=self.business1,
            offer_id=self.offer.id,
        )
        result = new_purchase(
            purchase_form=purchase_form,
            operator=self.operator,
        )

        self.assertFalse(result.success)
        self.assertIn('Some error', result.message)

    def test_purchase_success(self):
        purchase_form = business_and_offer_validation(
            business=self.business1,
            offer_id=self.offer.id,
        )

        result = new_purchase(
            purchase_form=purchase_form,
            operator=self.operator,
        )

        self.assertTrue(result.success)

        self.subscription1.refresh_from_db()
        self.assertTrue(self.subscription1.date_expiry)

        subscription = BillingSubscription.objects.get(status=SubscriptionStatus.PENDING)
        self.assertEqual(
            subscription.date_start.date(), self.subscription1.next_billing_date.date()
        )


@override_settings(SAVE_HISTORY=True)
class MassBillingOffersChangerTaskTestCase(BusinessAndOfferMixin, DjangoTestCase):
    @patch(
        'webapps.admin_extra.tasks.mass_billing_offers_changer.business_and_offer_validation',
        MagicMock(side_effect=ValidationException('Boom!')),
    )
    @patch.object(CSVLoader, 'load_from_file')
    def test_validation_errors(self, load_mocked):
        load_mocked.return_value = csv_load_mocked(
            [
                {'business_id': self.business1.id, 'offer_id': self.offer.id},
                {'business_id': self.business2.id, 'offer_id': self.offer.id},
            ]
        )

        mass_billing_offers_changer_task.run(
            file_path='path',
            email='<EMAIL>',
            operator_id=self.operator.id,
        )

        self.assertIn('report', mail.outbox[0].subject)
        self.assertIn('report in attachment', mail.outbox[0].body)

        _, attachment_content, _ = mail.outbox[0].attachments[0]
        report = get_csv_attachment_data(attachment_content)
        self.assertEqual(len(report), 3)

        for row in report[1:]:
            self.assertEqual(row[-2], 'ERROR')
            self.assertEqual(row[-1], 'Boom!')

    @patch(
        'webapps.admin_extra.tasks.mass_billing_offers_changer.new_purchase',
        MagicMock(return_value=PurchaseResult(message='Error ...')),
    )
    @patch.object(CSVLoader, 'load_from_file')
    def test_new_purchase_errors(self, load_mocked):
        load_mocked.return_value = csv_load_mocked(
            [
                {'business_id': self.business1.id, 'offer_id': self.offer.id},
                {'business_id': self.business2.id, 'offer_id': self.offer.id},
            ]
        )

        mass_billing_offers_changer_task.run(
            file_path='path',
            email='<EMAIL>',
            operator_id=self.operator.id,
        )

        self.assertIn('report', mail.outbox[0].subject)
        self.assertIn('report in attachment', mail.outbox[0].body)

        _, attachment_content, _ = mail.outbox[0].attachments[0]
        report = get_csv_attachment_data(attachment_content)
        self.assertEqual(len(report), 3)

        for row in report[1:]:
            self.assertEqual(row[-2], 'ERROR')
            self.assertEqual(row[-1], 'Error ...')

    @patch.object(CSVLoader, 'load_from_file')
    @patch(
        'webapps.admin_extra.tasks.mass_billing_offers_changer.new_purchase',
        MagicMock(return_value=PurchaseResult(success=True)),
    )
    def test_run_the_entire_process_ok(self, load_mocked):
        load_mocked.return_value = csv_load_mocked(
            [
                {'business_id': self.business1.id, 'offer_id': self.offer.id},
                {'business_id': self.business2.id, 'offer_id': self.offer.id},
            ]
        )

        mass_billing_offers_changer_task.run(
            file_path='path',
            email='<EMAIL>',
            operator_id=self.operator.id,
        )

        self.assertIn('report', mail.outbox[0].subject)
        self.assertIn('report in attachment', mail.outbox[0].body)

        _, attachment_content, _ = mail.outbox[0].attachments[0]
        report = get_csv_attachment_data(attachment_content)
        self.assertEqual(len(report), 3)

        for row in report[1:]:
            self.assertEqual(row[-2], 'SUCCESS')
