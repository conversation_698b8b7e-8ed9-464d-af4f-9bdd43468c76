from datetime import datetime

from django import forms
from django.utils.translation import gettext_lazy as _
from rest_framework.exceptions import ValidationError

from webapps.admin_extra.forms.mixins import (
    DryRunFormMixin,
    ImportFileFormMixin,
)
from webapps.admin_extra.import_tools.enums import (
    AutoAcceptPaymentSwitchFields,
    BulkPayoutSchemeUpdaterFields,
    ManualFundTransferFields,
)
from webapps.market_pay.models import AccountHolder
from webapps.market_pay.transfer import has_enough_funds


class AdyenReportForm(forms.Form):
    from_date = forms.CharField(required=True)
    till_date = forms.CharField(required=True)
    business_id = forms.IntegerField(required=False)

    @staticmethod
    def _clean_date(date):
        try:
            datetime.strptime(date, '%Y-%m-%d')
        except ValueError:
            raise forms.ValidationError(
                'Date "{}" doesn\'t match format YYYY-MM-DD'.format(date)
            ) from ValueError

        return date

    def clean_from_date(self):
        return self._clean_date(self.cleaned_data['from_date'])

    def clean_till_date(self):
        return self._clean_date(self.cleaned_data['till_date'])

    def clean(self):
        from_date = self.cleaned_data.get('from_date')
        till_date = self.cleaned_data.get('till_date')
        if from_date and till_date and from_date > till_date:
            raise forms.ValidationError(f'wrong dates, {from_date} > {till_date}')


class AdyenSettledReportForm(AdyenReportForm):
    mark_settled = forms.BooleanField(required=False)


class ManualTransferFundForm(forms.Form):
    source_account_code = forms.CharField(
        help_text=_('Account code from which the funds will be withdrawn'),
        required=True,
    )
    source_account_description = forms.CharField(
        help_text=_('Description of the transfer for the source account'),
        required=True,
        max_length=256,
        widget=forms.Textarea(attrs=dict(rows=3)),
    )
    destination_account_code = forms.CharField(
        help_text=_('Account code to which the funds will be sent'),
        required=True,
    )
    destination_account_description = forms.CharField(
        help_text=_('Description of the transfer for the destination account'),
        required=True,
        max_length=256,
        widget=forms.Textarea(attrs=dict(rows=3)),
    )
    amount = forms.DecimalField(
        help_text=_('Amount to send'),
        decimal_places=2,
        required=True,
    )

    def clean(self) -> dict:
        cleaned_data = super().clean()
        source_account_code = cleaned_data['source_account_code']
        source_account_holder = AccountHolder.objects.get(
            account_code=source_account_code,
        )
        if not has_enough_funds(source_account_holder, cleaned_data['amount'], allowed_debit=0):
            raise forms.ValidationError('Not enough money to perform this action')
        return cleaned_data

    def clean_amount(self) -> int:
        # Adyen requires amount in percentiles
        return self.cleaned_data['amount'] * 100

    def clean_source_account_code(self) -> str:
        return self._check_account_holder_code('source_account_code')

    def clean_destination_account_code(self) -> str:
        return self._check_account_holder_code('destination_account_code')

    def _check_account_holder_code(self, field_name: str) -> str:
        account_code = self.cleaned_data[field_name]
        if not AccountHolder.objects.filter(
            account_code=account_code,
        ).exists():
            raise ValidationError(f'Cannot find AccountHolder for the provided "{field_name}"')
        return account_code


class BulkManualTransferFundForm(ImportFileFormMixin, DryRunFormMixin, forms.Form):
    SPEC_IMPORT_FIELDS = (
        ManualFundTransferFields.SOURCE_ACCOUNT_CODE,
        ManualFundTransferFields.DESTINATION_ACCOUNT_CODE,
        ManualFundTransferFields.SOURCE_ACCOUNT_DESCRIPTION,
        ManualFundTransferFields.DESTINATION_ACCOUNT_DESCRIPTION,
        ManualFundTransferFields.AMOUNT,
    )


class AutoAcceptPaymentSwitchForm(
    ImportFileFormMixin,
    DryRunFormMixin,
    forms.Form,
):
    SPEC_IMPORT_FIELDS = (AutoAcceptPaymentSwitchFields.USER_ID,)

    turn_auto_accept = forms.BooleanField(
        help_text='Turn the auto accept payment setting on',
        required=True,
        initial=False,
    )
    unsafe_express = forms.BooleanField(
        help_text='Perform express bulk operation without checking for errors (may be unsafe!)',
        required=True,
        initial=False,
    )


class BulkPayoutSchemeUpdaterForm(
    ImportFileFormMixin,
    DryRunFormMixin,
    forms.Form,
):
    SPEC_IMPORT_FIELDS = (BulkPayoutSchemeUpdaterFields.ACCOUNT_CODE,)
    enable_auto_adyen_payouts = forms.BooleanField(
        help_text='Check if the payouts should be done automatically by Adyen, '
        'uncheck if the payout schedule should be set to HOLD',
        required=True,
        initial=False,
    )
