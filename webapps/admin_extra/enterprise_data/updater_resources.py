from collections import Counter
from functools import partial
from io import BytesIO
from itertools import groupby
from typing import Dict

import pandas as pd
from django.conf import settings
from django.db import transaction
from django.db.models import Q
from django.db.transaction import atomic
from django.utils.crypto import get_random_string

from lib.feature_flag.bug import ReindexResourcesOnBatchResourceUpdateToolFlag
from lib.rivers import bump_document, River
from lib.tools import tznow
from webapps.admin_extra.bank_importer_utils import (
    EnterpriseDataHandlingException,
)
from webapps.admin_extra.enterprise_data.common import (
    BatchUpdateException,
    UpdaterSheetLabelEnum,
    segregate_actions_by_type,
)
from webapps.admin_extra.enterprise_data.constants import (
    REMOVE_VALUE_INDICATOR,
)
from webapps.admin_extra.enterprise_data.parsers import (
    ResourceUpdateParser,
    ServiceMappingUpdateParser,
)
from webapps.business.elasticsearch import BusinessDocument
from webapps.business.models import (
    Business,
    Resource,
    Service,
)
from webapps.notification.models import UserNotification
from webapps.notification.scenarios.scenarios_access_rights import (
    StafferInvitationScenario,
)
from webapps.notification.tools import set_notification_receivers
from webapps.schedule.ports import (
    get_business_default_hours,
    set_many_resources_default_hours,
)
from webapps.user.models import User


def update_resources(xlsx_stream: BytesIO) -> Dict:
    excel = pd.ExcelFile(BytesIO(xlsx_stream.read()))
    return run_update_resources(excel)


@atomic
def run_update_resources(excel):
    all_affected_businesses = set()
    all_errors = []
    all_warnings = []

    service_mappings, warnings = read_service_mappings_from_excel(excel)
    all_warnings.extend(warnings)

    resources_sheet = pd.read_excel(
        excel,
        UpdaterSheetLabelEnum.UPDATE_RESOURCES,
    )
    resource_actions, warnings = ResourceUpdateParser().parse(resources_sheet)
    all_warnings.extend(warnings)

    add_actions, update_actions, delete_actions = segregate_actions_by_type(
        resource_actions,
    )

    try:
        affected_businesses, warnings = run_add_resource_actions(
            service_mappings,
            add_actions,
        )
        all_affected_businesses.update(affected_businesses)
        all_warnings.extend(warnings)
    except EnterpriseDataHandlingException as ex:
        all_errors.extend(ex.errors)
        raise EnterpriseDataHandlingException(
            "There are breaking errors that stopped updater from making " "changes to DB.",
            all_errors,
        ) from ex

    affected_businesses, warnings = run_update_resource_actions(
        service_mappings,
        update_actions,
    )
    all_affected_businesses.update(affected_businesses)
    all_warnings.extend(warnings)

    affected_businesses, warnings = run_delete_resource_actions(
        delete_actions,
    )
    all_affected_businesses.update(affected_businesses)
    all_warnings.extend(warnings)

    affected_business_ids = [business.id for business in all_affected_businesses]

    BusinessDocument.reindex(ids=affected_business_ids)

    report_kwargs = {'affected_business_ids': affected_business_ids, 'warnings': warnings}

    return report_kwargs


def read_service_mappings_from_excel(excel):
    services_sheet = pd.read_excel(
        excel,
        UpdaterSheetLabelEnum.UPDATE_SERVICES,
        index_col=0,
    )
    service_entries, warnings = ServiceMappingUpdateParser().parse(
        services_sheet,
    )
    return prepare_service_mappings(service_entries), warnings


def prepare_service_mappings(service_entries):
    return {service_entry.service_number: service_entry for service_entry in service_entries}


def run_add_resource_actions(service_mappings, actions):
    affected_businesses = set()
    resources_to_create = []
    services_for_resources = []

    errors = dry_run_add_resource_actions(actions)
    if errors:
        raise EnterpriseDataHandlingException(
            f"Dry run for add actions returned {len(errors)} errors.",
            errors,
        )

    for add_action in actions:
        try:
            resource = prepare_add_resource_action(add_action)

            services = prepare_resource_services(
                resource,
                add_action.get_service_numbers(),
                service_mappings,
            )

            if services is None:
                services_for_resources.append([])
            else:
                services_for_resources.append(services)

            resources_to_create.append(resource)

            affected_businesses.add(resource.business)
        except BatchUpdateException as ex:
            errors.append(str(ex))

    resources = Resource.objects.bulk_create(resources_to_create)
    if ReindexResourcesOnBatchResourceUpdateToolFlag():
        transaction.on_commit(
            partial(bump_document, River.RESOURCE, [resource.id for resource in resources])
        )

    for resource, services in zip(resources, services_for_resources):
        services_ids = [service.id for service in services]
        resource.add_services(services_ids)

    set_opening_hours(resources)
    send_invitations(resources)

    return affected_businesses, errors


def dry_run_add_resource_actions(actions):
    errors = []

    action_counted = Counter(actions)
    for action, count in action_counted.items():
        if count > 1:
            errors.append(
                f"Add action with values: (name={action.resource_name}, "
                f"booksy_business_id={action.booksy_business_id}) occurred "
                "more than once which is not correct."
            )

    business_ids = {add_action.booksy_business_id for add_action in actions}
    businesses = Business.objects.filter(
        id__in=business_ids,
    ).prefetch_related(
        'resources',
    )
    businesses_map = {b.id: b for b in businesses}

    for add_action in actions:
        business = businesses_map.get(add_action.booksy_business_id)

        if business:
            for resource in business.resources.all():
                if resource.name == add_action.resource_name:
                    errors.append(
                        "Attempt to ADD a resource with the same name and "
                        "type as the existing one for business with ID "
                        f"'{business.id}'. Action details: {add_action}"
                    )
        else:
            errors.append(
                f"Business with ID '{add_action.booksy_business_id}' does not "
                f"exist. Actions details: {add_action}"
            )

    return errors


def prepare_add_resource_action(add_action):
    # MAYBE: Add batch checking for existing businesses
    business_id = add_action.booksy_business_id

    business_exist = Business.objects.filter(id=business_id).exists()
    if not business_exist:
        raise BatchUpdateException(f"There is no business with ID '{business_id}'.")

    return Resource(
        business_id=business_id,
        type=Resource.STAFF,
        description=str(add_action.resource_id),
        name=add_action.resource_name,
        staff_email=add_action.resource_email,
        staff_cell_phone=add_action.resource_phone_number,
        staff_access_level=add_action.resource_type,
    )


def prepare_resource_services(resource, service_numbers, service_mappings):
    if service_numbers is None:
        return None

    service_ids = [
        service_mappings[service_number].service_booksy_id
        for service_number in service_numbers
        if service_number in service_mappings
    ]

    service_names = [
        service_mappings[service_number].name
        for service_number in service_numbers
        if service_number in service_mappings
    ]

    services = Service.objects.filter(
        Q(business=resource.business) & (Q(id__in=service_ids) | Q(name__in=service_names)),
    )

    return services


def set_opening_hours(resources):
    for biz_id, biz_res in groupby(resources, key=lambda r: r.business_id):
        resources = list(biz_res)
        set_many_resources_default_hours(
            business_id=biz_id,
            resource_ids=[res.id for res in resources],
            hours=get_business_default_hours(business_id=biz_id),
            tz=resources[0].business.get_timezone(),
        )


def send_invitations(resources):
    if not resources:
        return

    receivers = []

    for resource in resources:
        user = User.objects.filter(email=resource.staff_email).first()
        if user:
            resource.staff_user = user
        else:
            resource.staff_user = User.objects.create(
                email=resource.staff_email, username=User.generate_username(resource.staff_email)
            )
        user_password = get_random_string(length=10)
        resource.staff_user.set_password(user_password)
        resource.staff_user.password_change_required = True
        resource.staff_user.save()
        resource.save()

        access_level = resource.staff_access_level
        if access_level == Resource.STAFF_ACCESS_LEVEL_MANAGER:
            options = {'resources': []}
        else:
            options = {'resources': [resource.id]}

        receivers.append(
            {
                'identifier': resource.staff_email,
                'business_id': resource.business_id,
                'options': options,
                'device': 'email',
            }
        )

        if settings.LIVE_DEPLOYMENT:
            StafferInvitationScenario.send_mail(
                resource,
                user_password,
            )

    # We assume that all the resources here have the same owner
    owner_profile = resources[0].business.owner.profiles.first()

    set_notification_receivers(
        owner_profile,
        UserNotification.EMAIL_NOTIFICATION,
        receivers,
        is_superuser=None,
    )


def run_update_resource_actions(service_mappings, update_actions):
    affected_businesses = set()
    all_warnings = []

    resources_to_update = []
    users_to_update = []
    services_for_resources = []

    for update_action in update_actions:
        try:
            resource, warnings = prepare_update_resource_action(update_action)
            all_warnings.extend(warnings)

            services_for_resources.append(
                prepare_resource_services(
                    resource,
                    update_action.get_service_numbers(),
                    service_mappings,
                )
            )

            resources_to_update.append(resource)

            if resource.staff_user:
                users_to_update.append(resource.staff_user)

            affected_businesses.add(resource.business)
        except Resource.DoesNotExist:
            all_warnings.append(
                f"Resource with ID '{update_action.booksy_resource_id}' "
                f"does not exist. Action details: {update_action}"
            )
        except BatchUpdateException as ex:
            all_warnings.append(str(ex))

    Resource.objects.bulk_update(
        resources_to_update,
        ['name', 'staff_cell_phone', 'staff_email', 'description', 'staff_access_level', 'visible'],
    )
    if ReindexResourcesOnBatchResourceUpdateToolFlag():
        transaction.on_commit(
            partial(
                bump_document,
                River.RESOURCE,
                [resource.id for resource in resources_to_update],
            )
        )

    User.objects.bulk_update(users_to_update, ['email'])

    for resource, services in zip(resources_to_update, services_for_resources):
        if services is not None:
            services_ids = [service.id for service in services]
            resource.add_services(services_ids)

    return affected_businesses, all_warnings


def prepare_update_resource_action(action):
    warnings = []

    resource = Resource.objects.get(id=action.booksy_resource_id)

    if action.resource_name:
        resource.name = action.resource_name

    if action.resource_phone_number:
        if action.resource_phone_number == REMOVE_VALUE_INDICATOR:
            resource.staff_cell_phone = ''
        else:
            resource.staff_cell_phone = action.resource_phone_number

    if action.resource_email:
        resource.staff_email = action.resource_email

        if resource.staff_user:
            resource.staff_user.email = action.resource_email
        else:
            warnings.append(f"Resource with ID '{resource.id}' does not have staff_user.")

    if action.resource_id:
        if action.resource_id == REMOVE_VALUE_INDICATOR:
            resource.description = ''
        else:
            resource.description = str(action.resource_id)

    if action.resource_type:
        resource.staff_access_level = action.resource_type

    if action.resource_visibility is not None:
        resource.visible = action.resource_visibility

    return resource, warnings


def run_delete_resource_actions(delete_actions):
    affected_businesses = set()
    errors = []

    resources_to_delete = []

    for delete_action in delete_actions:
        try:
            resource = prepare_resource_delete_action(delete_action)
            resources_to_delete.append(resource)

            affected_businesses.add(resource.business)
        except Resource.DoesNotExist:
            errors.append(
                f"Resource with ID '{delete_action.booksy_resource_id}' "
                f"does not exist. Action details: {delete_action}"
            )

    Resource.objects.bulk_update(
        resources_to_delete,
        ['deleted', 'active'],
    )

    if ReindexResourcesOnBatchResourceUpdateToolFlag():
        transaction.on_commit(
            partial(
                bump_document,
                River.RESOURCE,
                [resource.id for resource in resources_to_delete],
            )
        )

    return affected_businesses, errors


def prepare_resource_delete_action(delete_action):
    resource = Resource.objects.get(id=delete_action.booksy_resource_id)
    resource.deleted = tznow()
    resource.active = False
    return resource
