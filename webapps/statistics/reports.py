import datetime
from collections import defaultdict
from decimal import Decimal
from decimal import ROUND_HALF_UP
from io import BytesIO

from django.conf import settings
from django.db.models import (
    Avg,
    Case,
    Count,
    F,
    Sum,
    Q,
    When,
)
from django.db.models.functions import Coalesce, TruncDate
from django.utils.encoding import force_str
from django.utils.functional import cached_property
from django.utils.translation import gettext, gettext_lazy
from openpyxl import Workbook, styles
from openpyxl.utils import get_column_letter, column_index_from_string

from drf_api.service.other.routing.views import ConfigView
from lib.time_24_hour import format_datetime
from webapps.booking.models import Appointment
from webapps.booking.querysets import QuerySetHelpersMixin
from webapps.boost.enums import BoostAppointmentStatus
from webapps.boost.models import BoostAppointment
from webapps.business.models import (
    Business,
    Resource,
    BusinessPromotion,
    PAID_PROMO,
)
from webapps.pos.enums import PaymentTypeEnum
from webapps.pos.models import (
    TransactionRow,
)
from webapps.register.models import (
    RegisterOperation,
)
from webapps.statistics.tools import count_flatfee
from webapps.stats_and_reports.reports.spreadsheet_utils import (
    set_columns_width,
)

UNASSIGNED_STAFFER_ID = -1


def _(text):
    return force_str(gettext(text))


class SpreadsheetReport:
    SHEETS = []

    SPREADSHEET_REPORT__ADMIN_TYPE = 'A'

    def __init__(
        self, stats, dates_label, business_label, language, request_type=None, *args, **kwargs
    ):
        self.wb = Workbook()
        self.wb.properties.language = language
        self.row = None
        self.buffer = BytesIO()
        self.now = (
            datetime.datetime.now(tz=stats.business.get_timezone())
            if stats and stats.business
            else datetime.datetime.now()
        )

        self.stats = stats
        self.dates_label = dates_label
        self.business_label = business_label
        self.language = language
        self.request_type = request_type

        self.payment_types = {code: force_str(label) for code, label in PaymentTypeEnum.choices()}
        self.transaction_row_types = {
            code: force_str(label) for code, label in TransactionRow.TRANSACTION_ROW_TYPES
        }
        self.transaction_row_types[TransactionRow.TRANSACTION_ROW_TYPE__DEPOSIT] = _(
            'Cancellation fee'
        )
        self.register_operation_types = {
            code: force_str(label) for code, label in RegisterOperation.REGISTER_OPERATION_TYPES
        }
        if self.stats and self.stats.business:
            self.business_id = self.stats.business.id
            self.tz = self.stats.business.get_timezone()
            self.staffers_dict = dict(
                Resource.objects.filter(
                    type=Resource.STAFF,
                    business_id=self.business_id,
                ).values_list('id', 'name')
            )
        else:
            self.business_id = None
            self.tz = None
            self.staffers_dict = {}

    def run(self):
        self.run_sheet(self.SHEETS[0][0], self.SHEETS[0][1], self.wb.active)
        for name, title in self.SHEETS[1:]:
            self.run_sheet(name, title)

        self.wb.save(self.buffer)
        # self.wb.save('/tmp/report.xlsx')
        return self.buffer.getvalue()

    def run_sheet(self, name, title, ws=None):
        if ws is None:
            ws = self.wb.create_sheet()
        self.ws = ws
        ws.title = force_str(title)

        getattr(self, 'sheet_{}'.format(name))()

    def range(self, label):
        left, right = label.split(':')
        range_ = '{left}{r}:{right}{r}'.format(left=left, right=right, r=self.row)
        return self.ws[range_][0]

    def range_n_cols(self, start_col_letter, n_cols):
        """A, 1 -> A:A
        A, 4 -> A:D
        """
        start_index = column_index_from_string(start_col_letter)
        end_col_letter = get_column_letter(start_index + n_cols - 1)
        range_ = '{left}{row}:{right}{row}'.format(
            left=start_col_letter, right=end_col_letter, row=self.row
        )
        return self.ws[range_][0]

    def col_range(self, col, start, end):
        range_ = '{col}{start}:{col}{end}'.format(col=col, start=start, end=end)
        return self.ws[range_][0]

    def append(self, content):
        self.row += 1
        self.ws.append(content)

    #
    # Formats
    #
    def format_title(self, cell, **kwargs):
        height = kwargs.get('height', 40)

        cell.font = styles.Font(size=16, bold=True)
        cell.alignment = styles.Alignment(vertical='center')
        cell.parent.row_dimensions[cell.row].height = height

    def format_address(self, ws_range):
        ws_range[0].font = styles.Font(size=12)
        ws_range[-1].font = styles.Font(size=12)
        self.format_border(ws_range, bottom='thin')
        self.format_alignment(ws_range[-1:], horizontal='right')

    def format_header(self, ws_range, **kwargs):
        left = kwargs.get('left', 'thin')
        top = kwargs.get('top', 'thin')
        bottom = kwargs.get('bottom', 'thin')
        right = kwargs.get('right', 'thin')

        ws_range[0].font = styles.Font(size=11, bold=True)
        self.format_border(ws_range, left=left, top=top, bottom=bottom, right=right)
        for cell in ws_range:
            cell.fill = styles.PatternFill(
                patternType='solid', bgColor='FFFFFFCC', fgColor='FFF2F2F2'
            )

    def format_subheader(self, ws_range, number_column=2, **kwargs):
        left = kwargs.get('left', 'thin')
        bottom = kwargs.get('bottom', 'thin')
        right = kwargs.get('right', 'thin')

        self.format_font(ws_range, bold=True)
        self.format_alignment([ws_range[0]], horizontal='center', vertical='center')
        self.format_alignment(
            ws_range[1:number_column], horizontal='left', vertical='center', wrapText=True
        )
        self.format_alignment(
            ws_range[number_column:], horizontal='right', vertical='center', wrapText=True
        )
        self.format_border(ws_range, bottom=bottom, left=left, right=right)

    def format_data_row(self, ws_range, number_column=2, **kwargs):
        left = kwargs.get('left', 'thin')
        right = kwargs.get('right', 'thin')
        last_number_column = kwargs.get('last_number_column')

        self.format_alignment(ws_range[:1], horizontal='center')
        self.format_border(ws_range[:1], left=left)
        self.format_border(ws_range[-1:], right=right)

        if last_number_column is None:
            self.format_number(ws_range[number_column:], self.currency_format)
        else:
            self.format_number(
                ws_range[number_column:last_number_column],
                self.currency_format,
            )

    def format_total_row(self, ws_range, number_column=2):
        self.format_border(ws_range, top='double')
        self.format_font(ws_range, bold=True)
        self.format_number(ws_range[number_column:], self.currency_format)

    def format_subtotal_row(self, ws_range, number_column=2):
        self.format_border(ws_range, top='thin', left='thin', right='thin')
        self.format_font(ws_range, bold=True)
        self.format_number(ws_range[number_column:], self.currency_format)

    #
    # Format helpers
    #
    @staticmethod
    def format_border(ws_range, **kwargs):
        color = 'FF7F7F7F'
        if kwargs.get('clear'):
            for cell in ws_range:
                cell.border = None
        else:
            border_kwargs = {
                k: styles.Side(border_style=v, color=color) for k, v in list(kwargs.items())
            }
            if len(ws_range) == 1:
                for cell in ws_range:
                    cell.border = styles.Border(**border_kwargs)
            else:
                left = border_kwargs.pop('left', None)
                right = border_kwargs.pop('right', None)
                left_kwargs = border_kwargs.copy()
                if left:
                    left_kwargs['left'] = left
                right_kwargs = border_kwargs.copy()
                if right:
                    right_kwargs['right'] = right
                ws_range[0].border = styles.Border(**left_kwargs)
                ws_range[-1].border = styles.Border(**right_kwargs)
                if len(ws_range) > 2:
                    for cell in ws_range[1:-1]:
                        cell.border = styles.Border(**border_kwargs)

    @staticmethod
    def format_alignment(ws_range, **kwargs):
        a = styles.Alignment(**kwargs)
        for cell in ws_range:
            cell.alignment = a

    @staticmethod
    def format_font(ws_range, **kwargs):
        for cell in ws_range:
            cell.font = styles.Font(**kwargs)

    @staticmethod
    def format_style(ws_range, style):
        for cell in ws_range:
            cell.style = style

    @staticmethod
    def format_number(ws_range, style):
        for cell in ws_range:
            cell.number_format = style

    def format_date(self, dt):
        if not dt:
            return _('N/A')
        dt = dt.astimezone(self.tz)
        return format_datetime(dt, 'date_ymd', self.language)

    def format_created(self, dt):
        return self.format_datetime(dt)

    def format_datetime(self, dt):
        if not dt:
            return _('N/A')
        dt = dt.astimezone(self.tz)
        return ' '.join(
            (
                format_datetime(dt, 'date_ymd', self.language),
                format_datetime(dt, 'time_hm', self.language),
            )
        )

    @staticmethod
    def format_client(data):
        return force_str(data.split(',')[0]) or _('Walk-in')

    def row_staffer(self, row):
        if 'commission' in row and row['commission']:
            staffer_id = row['commission'][0]['resource_id']
        else:
            staffer_id = row['booking_staffer_id']

        return self.staffers_dict.get(
            staffer_id,
            (
                _('N/A')
                if row['type'] in (TransactionRow.TRANSACTION_ROW_TYPE__DEPOSIT,)
                else _('Not selected')
            ),
        )

    @staticmethod
    def set_columns_width(ws_range, values):
        set_columns_width(ws_range, values)

    @staticmethod
    def set_rows_height(ws_range, values):
        for cell, height in zip(ws_range, values):
            cell.parent.row_dimensions[cell.row].height = height

    @staticmethod
    def base_currency_format(show_zero=False):
        config = ConfigView.currency_locale_config()
        c = config['currency'][config['default_currency']]

        before = u'"{}"'.format(force_str(c['symbol'])) if c['precedes'] else u''
        after = u'' if c['precedes'] else u'" {}"'.format(force_str(c['symbol']))
        #
        # always use '.' as decimal separator! Exel will interpret it
        # according to locale settings
        #
        number = u'{before}#{sep}##0'.format(before=before, sep=',')
        if c['decimal_length'] > 0:
            number = '{number}{sep}{decimals}'.format(
                number=number, sep='.', decimals=''.join(c['decimal_length'] * ['0'])
            )
        number = '{number}{after}'.format(number=number, after=after)
        if show_zero:
            return r'{number};\-{number};{before}0{after};'.format(
                number=number, after=after, before=before
            )
        else:
            return r'{number};\-{number};{before}\-{after};'.format(
                number=number, after=after, before=before
            )

    @cached_property
    def currency_format(self):
        return self.base_currency_format(False)

    @cached_property
    def percentage_format(self):
        return u'#,##0.00 "%"'

    @cached_property
    def currency_format_show_zero(self):
        return self.base_currency_format(True)

    def generated_info(self, empty_cols=1):
        date_time = (
            format_datetime(self.now, 'date_ymd', self.language)
            + ' '
            + format_datetime(self.now, 'time_hms', self.language)
        )
        text = gettext('Generated especially for you at {}, with love, from Booksy')
        label = text.format(date_time)

        self.append([])
        self.append([])
        self.append([])
        self.append(
            [
                '',
            ]
            * empty_cols
            + [
                label,
            ]
        )


class InvoiceSummarySpreadsheet(SpreadsheetReport):
    SHEETS = [
        ('invoice_summary', gettext_lazy('invoice_summary')),
    ]

    def sheet_invoice_summary(self):

        data = self.get_data()
        self.row = 0
        self.append(
            [
                _('business id'),
                _('business name'),
                _('business email'),
                _('business phone'),
                _('new customer count'),
                # xgettext:no-python-format
                _('Avg % commission'),
                _('commission amount'),
                _('flat fee'),
                _('final amount'),
                _('days promoted'),
                _('boost status'),
            ]
        )

        for row in data:
            self.append(
                [
                    row['id'],
                    row['name'],
                    row['email'],
                    row['phone'],
                    row['count_transactions'],
                    row['avg_commission'],
                    row['sum_amount'],
                    row['flat_fee'],
                    row['total'],
                    row['days_promoted'],
                    row['boost_status'],
                ]
            )
        self.set_columns_width(self.range('A:G'), [15, 25] + [20] * 5)

    def get_data(self):
        start_date = self.stats.scope_date
        end_date = self.stats.scope_till

        amount_field_name = 'gross_amount' if settings.BOOST.GROSS_VALUE else 'amount'
        lookup = f'boost_appointment__{amount_field_name}__isnull'
        appointments = Appointment.objects.filter_payable_boost_appointments().filter(
            booked_till__date__lt=end_date,
            booked_till__date__gte=start_date,
            **{lookup: False},
        )
        businesses_summary = appointments.values(
            'business',
            'business__name',
            'business__owner__email',
            'business__owner__cell_phone',
            'business__boost_status',
        ).annotate(
            visits=Count('id'),
            promotion_cost=Sum(f'boost_appointment__{amount_field_name}'),
            avg_commission=Avg('boost_appointment__boost_promotion__commission__commission'),
        )

        business_ids = list(appointments.values_list('business', flat=True).distinct())

        promotion_dates = (
            BusinessPromotion.objects.annotate(
                date_from=Case(
                    When(promotion_start__date__lte=start_date, then=start_date),
                    default=TruncDate('promotion_start'),
                ),
                date_to=Case(
                    When(promotion_end__isnull=True, then=end_date),
                    When(promotion_end__date__gte=end_date, then=end_date),
                    default=TruncDate('promotion_end'),
                ),
            )
            .exclude(
                Q(promotion_start__date__gte=end_date) | Q(promotion_end__date__lte=start_date),
            )
            .filter(
                type=PAID_PROMO,
                business__in=business_ids,
            )
            .values('date_from', 'date_to', 'business_id', flatfee=F('commission__flat_fee'))
            .distinct()
        )

        promotion_dates_dict = defaultdict(list)
        for promotion_date in promotion_dates:
            promotion_dates_dict[promotion_date['business_id']].append(promotion_date)

        businesses_summary = {b['business']: b for b in businesses_summary}

        business_dicts = []
        for b in business_ids:
            flat_fee = 0
            days_promoted = 0

            for promotion in promotion_dates_dict.get(b, []):
                if promotion['flatfee'] is None:
                    promotion['flatfee'] = Decimal(0)
                flat_fee += count_flatfee(
                    promotion['date_from'], promotion['date_to'], promotion['flatfee']
                )
                days_promoted = (promotion['date_to'] - promotion['date_from']).days

            flat_fee = Decimal(flat_fee).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)

            business_data = businesses_summary.get(b, {})
            sum_amount = business_data.get('promotion_cost', Decimal(0))
            total = sum_amount + flat_fee
            if not total:
                continue

            biz = {
                'id': business_data['business'],
                'count_transactions': business_data.get('visits', Decimal(0)),
                'name': business_data['business__name'],
                'email': business_data['business__owner__email'],
                'phone': business_data['business__owner__cell_phone'],
                'avg_commission': business_data.get('avg_commission', 0),
                'sum_amount': sum_amount,
                'flat_fee': flat_fee,
                'total': total,
                'days_promoted': days_promoted,
                'boost_status': force_str(
                    Business.BoostStatus(business_data['business__boost_status']).label
                ),
            }

            business_dicts.append(biz)

        return business_dicts


class BookingSummarySpreadsheet(SpreadsheetReport):
    SHEETS = [
        ('new_clients', gettext_lazy('New clients from Booksy')),
    ]

    def __init__(self, *args, **kwargs):
        super(BookingSummarySpreadsheet, self).__init__(*args, **kwargs)
        self.sources = ['Web', 'iPhone', 'Android', 'Lavito']
        self.chargeable_status = [
            Appointment.STATUS.ACCEPTED,
            Appointment.STATUS.UNCONFIRMED,
            Appointment.STATUS.MODIFIED,
            Appointment.STATUS.PROPOSED,
            Appointment.STATUS.FINISHED,
        ]

    @cached_property
    def first_bookings_completed(self):
        response = (
            Appointment.objects.filter_payable_boost_appointments()
            .filter(
                booked_till__gte=self.stats.scope_date,
                booked_till__lt=self.stats.scope_till,
            )
            .annotate_customer_full_name()
            .annotate_staffers()
            .annotate_services()
            .values(
                'id',
                'customer_full_name',
                'staffers',
                'services',
                biz_id=F('business_id'),
                business_name=F('business__name'),
                book_created=TruncDate('created'),
                book_happened=TruncDate('booked_from'),
                booking_value=F('boost_appointment__appointment_price'),
                commission=F('boost_appointment__boost_promotion__commission__commission'),
                cost=Coalesce(F('boost_appointment__gross_amount'), F('boost_appointment__amount')),
            )
        )

        for record in response:
            record['staffers'] = '\n'.join(record['staffers'] or [])
            record['services'] = '\n'.join(record['services'] or [])

        count = len(response)
        return response, count

    def sheet_new_clients(self):
        self.ws['B1'] = self.ws.title
        self.ws['H2'] = self.dates_label

        self.row = 2
        self.set_columns_width(self.range('A:M'), [5, 5] + 12 * [20])
        self.append([''])
        self.append([''])
        self.header_row(_('Completed appointments of ' 'clients acquired by Booksy'))
        self.table_content(self.first_bookings_completed[0])

    def header_row(self, title):
        self.append(['', title])
        self.format_header(self.range('B:M'))
        self.append(
            [
                '',
                '#',
                _('Business ID'),
                _('Business Name'),
                _('Multibooking ID'),  # do not confuse merchant with appointment
                _('Reservation date'),
                _('Booking date'),
                _('Client'),
                _('Services'),
                _('Staffers'),
                _('Gross revenue'),
                _('Commission'),
                _('Booksy commission'),
            ]
        )
        self.format_subheader(self.range('B:M'), number_column=12)

    def table_content(self, query):
        for i, booking in enumerate(query):
            self.append(
                [
                    '',
                    i + 1,
                    booking['biz_id'],
                    booking['business_name'],
                    booking['id'],
                    booking['book_created'],
                    booking['book_happened'],
                    booking['customer_full_name'],
                    booking['services'],
                    booking['staffers'],
                    booking['booking_value'],
                    booking['commission'],
                    booking['cost'],
                ]
            )
            self.format_data_row(self.range('B:M'), number_column=12)
            self.format_number(self.range('L:L'), self.percentage_format)
            self.format_number(self.range('K:K'), self.currency_format)

        self.format_border(self.range('B:M'), left='thin', right='thin', bottom='thick')


class ClaimPendingSummarySpreadsheet(SpreadsheetReport, QuerySetHelpersMixin):
    SHEETS = [
        ('claim_pending_summary', gettext_lazy('claim_pending_summary')),
    ]

    def sheet_claim_pending_summary(self):

        data = self.get_data()
        self.row = 0
        self.append(
            [
                _('boost_appointment_id'),
                _('name'),
                _('business id'),
                _('client card id'),
                _('booked from'),
                _('client name'),
                _('phone number'),
                _('claim reason'),
                _('marketplace claim'),
                _('global claim percent'),
                _('monthly claim percent'),
                (
                    _('Booksy gross commission')
                    if settings.BOOST.GROSS_VALUE
                    else _('Booksy commission')
                ),
            ]
        )

        for row in data:
            self.append(
                [
                    row['boost_appointment_id'],
                    row['name'],
                    row['business_id'],
                    row['bci_id'],
                    self.format_datetime(row['booked_from']),
                    row['client_name'],
                    row['phone_number'],
                    row['claim_reason'],
                    row['claim_description'],
                    row['global_claim_percent'],
                    row['monthly_claim_percent'],
                    row['gross_amount'] if settings.BOOST.GROSS_VALUE else row['amount'],
                ]
            )
        self.set_columns_width(ws_range=self.range('A:K'), values=[15, 25] + [20] * 9)

    def get_data(self):
        start_date = self.stats.scope_date
        end_date = self.stats.scope_till
        filtered_boost_appointments = BoostAppointment.objects.filter(
            appointment__booked_from__range=(start_date, end_date),
            status=BoostAppointmentStatus.CLAIM_PENDING,
        )
        b_ids = filtered_boost_appointments.values_list(
            'appointment__business', flat=True
        ).distinct()
        b_claim_ratios = {
            b.id: {
                'global_claim_percent': (f'{round(b.claim_ratio() * 100, 2)}%'),
                'monthly_claim_percent': (f'{round(b.claim_ratio(days=30) * 100, 2)}%'),
            }
            for b in Business.objects.filter(id__in=b_ids).only('id')
        }
        result = filtered_boost_appointments.annotate(
            client_name=self.clause_to_obtain_customer_name_through_appointment(
                appointment_prefix='appointment',
            ),
            phone_number=self.clause_to_obtain_phone_through_appointment(
                appointment_prefix='appointment',
            ),
        ).values(
            'client_name',
            'phone_number',
            'gross_amount' if settings.BOOST.GROSS_VALUE else 'amount',
            boost_appointment_id=F('id'),
            name=F('appointment__business__name'),
            business_id=F('appointment__business_id'),
            bci_id=F('appointment__booked_for_id'),
            booked_from=F('appointment__booked_from'),
            claim_reason=F('claim__choice'),
            claim_description=F('appointment__booked_for__marketplace_claim'),
        )
        for row in result:
            row.update(b_claim_ratios[row['business_id']])
        return result
