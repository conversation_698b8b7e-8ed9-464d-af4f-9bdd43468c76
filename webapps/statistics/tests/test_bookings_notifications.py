import pytest

from webapps.business.baker_recipes import business_recipe
from webapps.notification.enums import ScheduleState
from webapps.statistics.notifications.bookings import (
    Last7DaysPoorlyBookedNotification,
    TodayFullyBookedNotification,
    Next7DaysFullyBookedNotification,
    Last30DaysFullyBookedNotification,
    MonthBookingsGrowthNotification,
)


@pytest.mark.django_db
@pytest.mark.parametrize(
    'notif_class,extra_params',
    [
        (
            TodayFullyBookedNotification,
            None,
        ),
        (
            Next7DaysFullyBookedNotification,
            None,
        ),
        (
            Last30DaysFullyBookedNotification,
            None,
        ),
        (MonthBookingsGrowthNotification, dict(growth=10, month=2)),
    ],
)
def test_notifications(notif_class, extra_params):
    business = business_recipe.make()
    parameters = extra_params or {}
    notification = notif_class(None, business_id=business.id, **parameters)
    notification.send()
    assert notification.schedule_record.state == ScheduleState.SUCCESS


@pytest.mark.django_db
def test_last_7_days_poorly_booked():
    business = business_recipe.make()
    notification = Last7DaysPoorlyBookedNotification(
        None,
        business_id=business.id,
    )
    # test first_variant
    assert notification.get_target().type == 'boost_dashboard'

    notification.send()
    assert notification.schedule_record.state == ScheduleState.SUCCESS
    print(notification.schedule_record.parameters)

    # create again
    notification = Last7DaysPoorlyBookedNotification(
        None,
        business_id=business.id,
    )
    # now it should be the same
    assert notification.get_target().type == 'boost_dashboard'
