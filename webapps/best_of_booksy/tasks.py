import logging

from lib.celery_tools import celery_task
from lib.tools import grouper
from service.best_of_booksy.tools import create_certificate
from webapps.best_of_booksy.models import BestOfBooksyAward, BestOfBooksyBusinessAward
from webapps.best_of_booksy.notifications import WinnerAwardNotification
from webapps.business.elasticsearch import BusinessDocument

logger = logging.getLogger('booksy.best_of_booksy_awards')


@celery_task
def create_business_booksy_awards(file_path):
    """
    Will parse raw xlsx file and create best of booksy business awards + generate certificate
    :param file_path: s3 path to xlsx file
    :return:
    """

    from xlrd import XLRDError
    from lib.spreadsheet import load_spreadsheet_with_pandas
    from webapps.admin_extra.import_utils import strip_xlsx_data
    from webapps.admin_extra.admin_import_s3 import AdminImporterS3

    with AdminImporterS3.open_file(file_path, delete_file=True) as import_file:
        try:
            striped_data = strip_xlsx_data(load_spreadsheet_with_pandas(import_file.read()))
        except XLRDError as e:
            logger.error('Error while loading data: %s, %s', str(e), file_path)
            return

    business_awards = []
    awards = {}
    for (
        business_id,
        business_name,
        business_category_id,
        business_category_name,
        region_name,
        score,
        period_name,
        award_name,
        had_equal_score,
    ) in striped_data[1:]:
        award_key = f"{award_name}_{period_name}"
        if award_key not in awards:
            award = BestOfBooksyAward.objects.filter(
                name=award_name,
                award_period__period_name=period_name,
            ).first()
            if not award:
                continue
            awards[award_key] = award

        business_awards.append(
            BestOfBooksyBusinessAward(
                business_id=business_id,
                business_name=business_name,
                business_category_id=business_category_id,
                business_category_name=business_category_name,
                region_name=region_name,
                score=score,
                award=awards[award_key],
                had_equal_score=had_equal_score in ('y', 'yes', 't', 'true', '1'),
            )
        )

    created_business_awards = BestOfBooksyBusinessAward.objects.bulk_create(
        business_awards, batch_size=1000
    )

    business_award_ids = [business_award.id for business_award in created_business_awards]
    for package_ids in grouper(business_award_ids, 100):
        create_best_of_booksy_certificates.delay(package_ids)


def _create_certificate(business_award):
    try:
        business_award.certificate = create_certificate(business_award)
    except Exception as e:  # pylint: disable=broad-except
        logger.error(
            'Error while creating certificate: %s,  business award id: %s',
            str(e),
            business_award.id,
        )
        return
    business_award.save(update_fields=["certificate"])
    notification = WinnerAwardNotification(business_award.business)
    notification.send()
    return business_award.business_id


@celery_task
def create_best_of_booksy_certificates(business_award_ids):
    qs = BestOfBooksyBusinessAward.objects.prefetch_related('business').filter(
        pk__in=business_award_ids,
        certificate__isnull=True,
    )
    business_ids = list(
        filter(
            None,
            (_create_certificate(business_award) for business_award in qs),
        )
    )
    BusinessDocument.reindex(ids=business_ids, use_celery=True)
