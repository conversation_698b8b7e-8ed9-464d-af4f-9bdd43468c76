# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: booksy_auth/protos/auth.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1d\x62ooksy_auth/protos/auth.proto\"\xde\x06\n\x08UserData\x12\x17\n\x0f\x63ountry_user_id\x18\x01 \x01(\r\x12\x14\n\x0c\x63ountry_code\x18\x02 \x01(\t\x12\r\n\x05\x65mail\x18\x03 \x01(\t\x12\x18\n\x0b\x66\x61\x63\x65\x62ook_id\x18\x04 \x01(\tH\x00\x88\x01\x01\x12\x1c\n\x0f\x61pple_user_uuid\x18\x05 \x01(\tH\x01\x88\x01\x01\x12\x12\n\nfirst_name\x18\x06 \x01(\t\x12\x11\n\tlast_name\x18\x07 \x01(\t\x12\x12\n\ncell_phone\x18\x08 \x01(\t\x12\x12\n\nhome_phone\x18\t \x01(\t\x12\x12\n\nwork_phone\x18\n \x01(\t\x12\x15\n\rpassword_hash\x18\x0b \x01(\t\x12\x14\n\x0cis_superuser\x18\x0c \x01(\x08\x12 \n\x18password_change_required\x18\r \x01(\x08\x12\x10\n\x08language\x18\x0e \x01(\t\x12\x16\n\x0e\x61\x64\x64ress_line_1\x18\x0f \x01(\t\x12\x16\n\x0e\x61\x64\x64ress_line_2\x18\x10 \x01(\t\x12\x15\n\x08latitude\x18\x11 \x01(\x02H\x02\x88\x01\x01\x12\x16\n\tlongitude\x18\x12 \x01(\x02H\x03\x88\x01\x01\x12\x15\n\x08\x62irthday\x18\x13 \x01(\tH\x04\x88\x01\x01\x12\x1b\n\x13payment_auto_accept\x18\x14 \x01(\x08\x12\x15\n\rbooking_score\x18\x15 \x01(\x05\x12 \n\x13include_in_analysis\x18\x16 \x01(\x08H\x05\x88\x01\x01\x12\x14\n\x0cprofile_type\x18\x17 \x01(\t\x12\x10\n\x08\x61\x62out_me\x18\x18 \x01(\t\x12\x11\n\x04\x63ity\x18\x19 \x01(\tH\x06\x88\x01\x01\x12\x14\n\x07zipcode\x18\x1a \x01(\tH\x07\x88\x01\x01\x12\x16\n\tphoto_url\x18\x1b \x01(\tH\x08\x88\x01\x01\x12\x16\n\tgoogle_id\x18\x1c \x01(\tH\t\x88\x01\x01\x12\x1f\n\x12\x61uthenticator_code\x18\x1d \x01(\tH\n\x88\x01\x01\x42\x0e\n\x0c_facebook_idB\x12\n\x10_apple_user_uuidB\x0b\n\t_latitudeB\x0c\n\n_longitudeB\x0b\n\t_birthdayB\x16\n\x14_include_in_analysisB\x07\n\x05_cityB\n\n\x08_zipcodeB\x0c\n\n_photo_urlB\x0c\n\n_google_idB\x15\n\x13_authenticator_code\"T\n\x14UserDataWithSessions\x12\x1c\n\tuser_data\x18\x01 \x01(\x0b\x32\t.UserData\x12\x1e\n\x08sessions\x18\x02 \x03(\x0b\x32\x0c.SessionData\"Y\n\x0fSyncUserRequest\x12\x36\n\x17user_data_with_sessions\x18\x01 \x01(\x0b\x32\x15.UserDataWithSessions\x12\x0e\n\x06\x64omain\x18\x02 \x01(\t\"f\n\x11\x43reateUserRequest\x12\x1c\n\tuser_data\x18\x01 \x01(\x0b\x32\t.UserData\x12\x0e\n\x06origin\x18\x02 \x01(\t\x12\x0e\n\x06\x64omain\x18\x03 \x01(\t\x12\x13\n\x0b\x66ingerprint\x18\x04 \x01(\t\"n\n\x10LoginUserRequest\x12\r\n\x05\x65mail\x18\x01 \x01(\t\x12\x10\n\x08password\x18\x02 \x01(\t\x12\x13\n\x0b\x66ingerprint\x18\x03 \x01(\t\x12\x14\n\x0c\x63ountry_code\x18\x04 \x01(\t\x12\x0e\n\x06\x64omain\x18\x05 \x01(\t\"8\n\x11LogoutUserRequest\x12\x13\n\x0bsession_key\x18\x01 \x01(\t\x12\x0e\n\x06\x64omain\x18\x02 \x01(\t\"Q\n\x14SessionExistsRequest\x12\x13\n\x0bsession_key\x18\x01 \x01(\t\x12\x14\n\x0c\x63ountry_code\x18\x02 \x01(\t\x12\x0e\n\x06\x64omain\x18\x03 \x01(\t\"\xec\x01\n\x0bSessionData\x12\x13\n\x0bsession_key\x18\x01 \x01(\t\x12\x1c\n\x0fsuperuser_email\x18\x02 \x01(\tH\x00\x88\x01\x01\x12\x0f\n\x07\x65xpired\x18\x03 \x01(\t\x12\x17\n\nlast_login\x18\x04 \x01(\tH\x01\x88\x01\x01\x12\x17\n\x0f\x63ountry_user_id\x18\x05 \x01(\r\x12\x14\n\x0c\x63ountry_code\x18\x06 \x01(\t\x12\x0e\n\x06\x64omain\x18\x07 \x01(\t\x12\x13\n\x06origin\x18\x08 \x01(\tH\x02\x88\x01\x01\x42\x12\n\x10_superuser_emailB\r\n\x0b_last_loginB\t\n\x07_origin\"\x93\x01\n\x14\x43reateSessionRequest\x12\x17\n\x0f\x63ountry_user_id\x18\x01 \x01(\r\x12\x17\n\x0fsuperuser_email\x18\x02 \x01(\t\x12\x14\n\x0c\x63ountry_code\x18\x03 \x01(\t\x12\x0e\n\x06\x64omain\x18\x04 \x01(\t\x12\x0e\n\x06origin\x18\x05 \x01(\t\x12\x13\n\x0b\x66ingerprint\x18\x06 \x01(\t\"U\n\x14\x44\x65leteAccountRequest\x12\x17\n\x0f\x63ountry_user_id\x18\x01 \x01(\r\x12\x14\n\x0c\x63ountry_code\x18\x02 \x01(\t\x12\x0e\n\x06\x64omain\x18\x03 \x01(\t\"\xa9\x01\n\x11\x41ppleLoginRequest\x12\x13\n\x0b\x61pple_token\x18\x01 \x01(\t\x12\x12\n\nfirst_name\x18\x02 \x01(\t\x12\x11\n\tlast_name\x18\x03 \x01(\t\x12\r\n\x05\x65mail\x18\x04 \x01(\t\x12\r\n\x05nonce\x18\x05 \x01(\t\x12\x16\n\x0eidentity_token\x18\x06 \x01(\t\x12\x12\n\nservice_id\x18\x07 \x01(\t\x12\x0e\n\x06\x64omain\x18\x08 \x01(\t\"|\n\x1c\x44\x65leteAllUserSessionsRequest\x12\x17\n\x0f\x63ountry_user_id\x18\x01 \x01(\r\x12\x14\n\x0c\x63ountry_code\x18\x02 \x01(\t\x12\x0e\n\x06\x64omain\x18\x03 \x01(\t\x12\x1d\n\x15\x65xcluded_session_keys\x18\x04 \x03(\t\"7\n\x10SyncUserResponse\x12\x12\n\nis_created\x18\x01 \x01(\x08\x12\x0f\n\x07user_id\x18\x02 \x01(\t\"_\n\x12\x43reateUserResponse\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x13\n\x0bsession_key\x18\x02 \x01(\t\x12\x12\n\nis_created\x18\x03 \x01(\x08\x12\x0f\n\x07\x65xpired\x18\x04 \x01(\t\"\xd3\x01\n\x11LoginUserResponse\x12\x16\n\x0e\x61\x63\x63ount_exists\x18\x01 \x01(\x08\x12\x1c\n\x0fsuperuser_email\x18\x02 \x01(\tH\x00\x88\x01\x01\x12\x17\n\x0f\x63ountry_user_id\x18\x03 \x01(\r\x12 \n\x18password_change_required\x18\x04 \x01(\x08\x12\x13\n\x0bsession_key\x18\x05 \x01(\t\x12\x0f\n\x07\x65xpired\x18\x06 \x01(\t\x12\x13\n\x0buser_exists\x18\x07 \x01(\x08\x42\x12\n\x10_superuser_email\"%\n\x12LogoutUserResponse\x12\x0f\n\x07\x64\x65leted\x18\x01 \x01(\x08\"\x93\x01\n\x15SessionExistsResponse\x12\x16\n\x0esession_exists\x18\x01 \x01(\x08\x12\x17\n\x0f\x63ountry_user_id\x18\x02 \x01(\r\x12\x0f\n\x07user_id\x18\x03 \x01(\t\x12\x0f\n\x07\x65xpired\x18\x04 \x01(\t\x12\x0e\n\x06origin\x18\x06 \x01(\t\x12\x17\n\x0fsuperuser_email\x18\x07 \x01(\t\"Q\n\x15\x43reateSessionResponse\x12\x12\n\nis_created\x18\x01 \x01(\x08\x12\x13\n\x0bsession_key\x18\x02 \x01(\t\x12\x0f\n\x07\x65xpired\x18\x03 \x01(\t\"/\n\x1c\x44\x65leteCountryAccountResponse\x12\x0f\n\x07\x64\x65leted\x18\x01 \x01(\x08\"\x9b\x01\n\x12\x41ppleLoginResponse\x12\x17\n\x0f\x63ountry_user_id\x18\x01 \x01(\r\x12\x0f\n\x07\x63reated\x18\x02 \x01(\x08\x12\x13\n\x0bsession_key\x18\x03 \x01(\t\x12\x14\n\x0cis_superuser\x18\x04 \x01(\x08\x12 \n\x18password_change_required\x18\x05 \x01(\x08\x12\x0e\n\x06\x65rrors\x18\x06 \x01(\t\"5\n\x1d\x44\x65leteAllUserSessionsResponse\x12\x14\n\x0csession_keys\x18\x01 \x03(\t\"\x8b\x01\n\x11\x45rrorUserResponse\x12.\n\x06reason\x18\x01 \x03(\x0b\x32\x1e.ErrorUserResponse.ReasonEntry\x12\x17\n\x04user\x18\x02 \x01(\x0b\x32\t.UserData\x1a-\n\x0bReasonEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"B\n\x13\x43reatedUserResponse\x12\x17\n\x0f\x63ountry_user_id\x18\x01 \x01(\r\x12\x12\n\nis_created\x18\x02 \x01(\x08\".\n\x13SkippedUserResponse\x12\x17\n\x0f\x63ountry_user_id\x18\x01 \x01(\r\"V\n\x14\x42ulkSyncUsersRequest\x12\x18\n\x05users\x18\x01 \x03(\x0b\x32\t.UserData\x12\x14\n\x0c\x63ountry_code\x18\x02 \x01(\t\x12\x0e\n\x06\x64omain\x18\x03 \x01(\t\"n\n\x15\x42ulkSyncUsersResponse\x12\x0f\n\x07\x63reated\x18\x01 \x03(\r\x12\x0f\n\x07updated\x18\x02 \x03(\r\x12\x0f\n\x07skipped\x18\x03 \x03(\r\x12\"\n\x06\x65rrors\x18\x04 \x03(\x0b\x32\x12.ErrorUserResponse\"I\n\x17\x42ulkSyncSessionsRequest\x12\x1e\n\x08sessions\x18\x01 \x03(\x0b\x32\x0c.SessionData\x12\x0e\n\x06\x64omain\x18\x02 \x01(\t\"\x1b\n\x19\x42ulkCreateSessionResponse\"\xb4\x01\n\x12GenerateOTPRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x11\n\tcode_size\x18\x02 \x01(\x05\x12\x10\n\x08interval\x18\x03 \x01(\x05\x12\x12\n\nevent_type\x18\x04 \x01(\t\x12\x1b\n\x0eproduced_by_id\x18\x05 \x01(\tH\x00\x88\x01\x01\x12\x14\n\x0c\x63ountry_code\x18\x06 \x01(\t\x12\x0e\n\x06\x64omain\x18\x07 \x01(\tB\x11\n\x0f_produced_by_id\"\x7f\n\x13GenerateOTPResponse\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x10\n\x08otp_code\x18\x02 \x01(\t\x12\x14\n\x0c\x63ountry_code\x18\x03 \x01(\t\x12\x13\n\x06status\x18\x04 \x01(\tH\x00\x88\x01\x01\x12\x0f\n\x07\x63reated\x18\x05 \x01(\x08\x42\t\n\x07_status\"[\n\x10VerifyOTPRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x10\n\x08otp_code\x18\x02 \x01(\t\x12\x14\n\x0c\x63ountry_code\x18\x05 \x01(\t\x12\x0e\n\x06\x64omain\x18\x06 \x01(\t\"4\n\x11VerifyOTPResponse\x12\x0e\n\x06status\x18\x01 \x01(\t\x12\x0f\n\x07user_id\x18\x02 \x01(\t\"]\n$GetLastSuccessfulVerificationRequest\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x14\n\x0c\x63ountry_code\x18\x02 \x01(\t\x12\x0e\n\x06\x64omain\x18\x03 \x01(\t\"\x82\x01\n%GetLastSuccessfulVerificationResponse\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x13\n\x06status\x18\x02 \x01(\tH\x00\x88\x01\x01\x12\x18\n\x0bverified_at\x18\x03 \x01(\tH\x01\x88\x01\x01\x42\t\n\x07_statusB\x0e\n\x0c_verified_at2\xa4\x05\n\x04\x41uth\x12\x32\n\tsync_user\x12\x10.SyncUserRequest\x1a\x11.SyncUserResponse\"\x00\x12\x38\n\x0b\x63reate_user\x12\x12.CreateUserRequest\x1a\x13.CreateUserResponse\"\x00\x12\x35\n\nlogin_user\x12\x11.LoginUserRequest\x1a\x12.LoginUserResponse\"\x00\x12\x38\n\x0blogout_user\x12\x12.LogoutUserRequest\x1a\x13.LogoutUserResponse\"\x00\x12\x41\n\x0esession_exists\x12\x15.SessionExistsRequest\x1a\x16.SessionExistsResponse\"\x00\x12\x41\n\x0e\x63reate_session\x12\x15.CreateSessionRequest\x1a\x16.CreateSessionResponse\"\x00\x12H\n\x0e\x64\x65lete_account\x12\x15.DeleteAccountRequest\x1a\x1d.DeleteCountryAccountResponse\"\x00\x12[\n\x18\x64\x65lete_all_user_sessions\x12\x1d.DeleteAllUserSessionsRequest\x1a\x1e.DeleteAllUserSessionsResponse\"\x00\x12\x42\n\x0f\x62ulk_sync_users\x12\x15.BulkSyncUsersRequest\x1a\x16.BulkSyncUsersResponse\"\x00\x12L\n\x12\x62ulk_sync_sessions\x12\x18.BulkSyncSessionsRequest\x1a\x1a.BulkCreateSessionResponse\"\x00\x32\xf2\x01\n\x03OTP\x12>\n\x11generate_otp_code\x12\x13.GenerateOTPRequest\x1a\x14.GenerateOTPResponse\x12\x38\n\x0fverify_otp_code\x12\x11.VerifyOTPRequest\x1a\x12.VerifyOTPResponse\x12q\n get_last_successful_verification\x12%.GetLastSuccessfulVerificationRequest\x1a&.GetLastSuccessfulVerificationResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'booksy_auth.protos.auth_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_ERRORUSERRESPONSE_REASONENTRY']._options = None
  _globals['_ERRORUSERRESPONSE_REASONENTRY']._serialized_options = b'8\001'
  _globals['_USERDATA']._serialized_start=34
  _globals['_USERDATA']._serialized_end=896
  _globals['_USERDATAWITHSESSIONS']._serialized_start=898
  _globals['_USERDATAWITHSESSIONS']._serialized_end=982
  _globals['_SYNCUSERREQUEST']._serialized_start=984
  _globals['_SYNCUSERREQUEST']._serialized_end=1073
  _globals['_CREATEUSERREQUEST']._serialized_start=1075
  _globals['_CREATEUSERREQUEST']._serialized_end=1177
  _globals['_LOGINUSERREQUEST']._serialized_start=1179
  _globals['_LOGINUSERREQUEST']._serialized_end=1289
  _globals['_LOGOUTUSERREQUEST']._serialized_start=1291
  _globals['_LOGOUTUSERREQUEST']._serialized_end=1347
  _globals['_SESSIONEXISTSREQUEST']._serialized_start=1349
  _globals['_SESSIONEXISTSREQUEST']._serialized_end=1430
  _globals['_SESSIONDATA']._serialized_start=1433
  _globals['_SESSIONDATA']._serialized_end=1669
  _globals['_CREATESESSIONREQUEST']._serialized_start=1672
  _globals['_CREATESESSIONREQUEST']._serialized_end=1819
  _globals['_DELETEACCOUNTREQUEST']._serialized_start=1821
  _globals['_DELETEACCOUNTREQUEST']._serialized_end=1906
  _globals['_APPLELOGINREQUEST']._serialized_start=1909
  _globals['_APPLELOGINREQUEST']._serialized_end=2078
  _globals['_DELETEALLUSERSESSIONSREQUEST']._serialized_start=2080
  _globals['_DELETEALLUSERSESSIONSREQUEST']._serialized_end=2204
  _globals['_SYNCUSERRESPONSE']._serialized_start=2206
  _globals['_SYNCUSERRESPONSE']._serialized_end=2261
  _globals['_CREATEUSERRESPONSE']._serialized_start=2263
  _globals['_CREATEUSERRESPONSE']._serialized_end=2358
  _globals['_LOGINUSERRESPONSE']._serialized_start=2361
  _globals['_LOGINUSERRESPONSE']._serialized_end=2572
  _globals['_LOGOUTUSERRESPONSE']._serialized_start=2574
  _globals['_LOGOUTUSERRESPONSE']._serialized_end=2611
  _globals['_SESSIONEXISTSRESPONSE']._serialized_start=2614
  _globals['_SESSIONEXISTSRESPONSE']._serialized_end=2761
  _globals['_CREATESESSIONRESPONSE']._serialized_start=2763
  _globals['_CREATESESSIONRESPONSE']._serialized_end=2844
  _globals['_DELETECOUNTRYACCOUNTRESPONSE']._serialized_start=2846
  _globals['_DELETECOUNTRYACCOUNTRESPONSE']._serialized_end=2893
  _globals['_APPLELOGINRESPONSE']._serialized_start=2896
  _globals['_APPLELOGINRESPONSE']._serialized_end=3051
  _globals['_DELETEALLUSERSESSIONSRESPONSE']._serialized_start=3053
  _globals['_DELETEALLUSERSESSIONSRESPONSE']._serialized_end=3106
  _globals['_ERRORUSERRESPONSE']._serialized_start=3109
  _globals['_ERRORUSERRESPONSE']._serialized_end=3248
  _globals['_ERRORUSERRESPONSE_REASONENTRY']._serialized_start=3203
  _globals['_ERRORUSERRESPONSE_REASONENTRY']._serialized_end=3248
  _globals['_CREATEDUSERRESPONSE']._serialized_start=3250
  _globals['_CREATEDUSERRESPONSE']._serialized_end=3316
  _globals['_SKIPPEDUSERRESPONSE']._serialized_start=3318
  _globals['_SKIPPEDUSERRESPONSE']._serialized_end=3364
  _globals['_BULKSYNCUSERSREQUEST']._serialized_start=3366
  _globals['_BULKSYNCUSERSREQUEST']._serialized_end=3452
  _globals['_BULKSYNCUSERSRESPONSE']._serialized_start=3454
  _globals['_BULKSYNCUSERSRESPONSE']._serialized_end=3564
  _globals['_BULKSYNCSESSIONSREQUEST']._serialized_start=3566
  _globals['_BULKSYNCSESSIONSREQUEST']._serialized_end=3639
  _globals['_BULKCREATESESSIONRESPONSE']._serialized_start=3641
  _globals['_BULKCREATESESSIONRESPONSE']._serialized_end=3668
  _globals['_GENERATEOTPREQUEST']._serialized_start=3671
  _globals['_GENERATEOTPREQUEST']._serialized_end=3851
  _globals['_GENERATEOTPRESPONSE']._serialized_start=3853
  _globals['_GENERATEOTPRESPONSE']._serialized_end=3980
  _globals['_VERIFYOTPREQUEST']._serialized_start=3982
  _globals['_VERIFYOTPREQUEST']._serialized_end=4073
  _globals['_VERIFYOTPRESPONSE']._serialized_start=4075
  _globals['_VERIFYOTPRESPONSE']._serialized_end=4127
  _globals['_GETLASTSUCCESSFULVERIFICATIONREQUEST']._serialized_start=4129
  _globals['_GETLASTSUCCESSFULVERIFICATIONREQUEST']._serialized_end=4222
  _globals['_GETLASTSUCCESSFULVERIFICATIONRESPONSE']._serialized_start=4225
  _globals['_GETLASTSUCCESSFULVERIFICATIONRESPONSE']._serialized_end=4355
  _globals['_AUTH']._serialized_start=4358
  _globals['_AUTH']._serialized_end=5034
  _globals['_OTP']._serialized_start=5037
  _globals['_OTP']._serialized_end=5279
# @@protoc_insertion_point(module_scope)
