# pylint: skip-file
from webapps.message_blast.enums import (
    MessageBlastDateType,
    MessageBlastGroupEnum,
    MessageBlastInternalNames,
    MessageBlastTemplateStatus,
)

PL_MESSAGE_BLAST_SPECIAL_OCCASIONS = [
    dict(
        name='<PERSON>witaj nowego klienta',
        description='Wysyłane do klientów dwie godziny po pierwszej wizycie',
        title='Dziękujemy za wizytę!',
        body=(
            "Ma<PERSON> nadzieję, że jesteś zadowolony z wizyty w {business_name}. "
            "Cieszymy się, że nas odwiedziłeś!\n\n"
            "Wejdź na {subdomain}, odkryj inne oferowane przez nas usługi i "
            "już dziś zarezerwuj kolejną wizytę!\n\n"
            "Do zobaczenia wkrótce,\n"
            "{business_name}"
        ),
        body_short=(
            "<PERSON><PERSON> nad<PERSON>, ze jestes zadowolony z wizyty w {business_name}! "
            "Wejdz na {subdomain}, odkryj inne uslugi i zarezerwuj kolejna "
            "wizyte!"
        ),
        image=None,
        recommended=True,
        order=1,
        group__internal_name=MessageBlastGroupEnum.FIRST_IMPRESSION,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.WELCOME_NEW_CLIENT,
    ),
    dict(
        name='Poinformuj nowego klienta o swoich usługach',
        description='Wysyłane do klientów 21 dni po pierwszej wizycie ',
        title='Nowe usługi, które pokochasz',
        body=(
            "Dziękujemy za Twoją ostatnią wizytę w {business_name}! Czy wiesz, "
            "że w swojej ofercie mamy wiele usług stworzonych z myślą o "
            "Tobie?\n\n"
            "Wejdź na {subdomain}, odkryj nasze usługi i zarezerwuj kolejną "
            "wizytę już dziś!\n\n"
            "Do zobaczenia wkrótce!\n"
            "{business_name}"
        ),
        body_short=(
            "Dziekujemy za Twoja ostatnia wizyte w {business_name}! Wejdz na "
            "{subdomain}, odkryj nasze uslugi i umow sie juz dzis!"
        ),
        image=None,
        recommended=True,
        order=2,
        group__internal_name=MessageBlastGroupEnum.FIRST_IMPRESSION,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.INFORM_ABOUT_OTHER_SERVICES,
    ),
    dict(
        name='Poinformuj nowego klienta o Kartach Podarunkowych',
        description=(
            'Wysyłane do klientów 30 dni po pierwszej wizycie jeśli masz '
            'dostępną minimum jedną Kartę Podarunkową'
        ),
        title='Karty podarunkowe już dostępne!',
        body=(
            "Szukasz wyjątkowego prezentu na specjalną okazję? Chcesz pokazać "
            "komuś bliskiemu, że o nim pamiętasz? Masz szczęście, ponieważ w "
            "{business_name} znajdziesz teraz Karty podarunkowe!\n\n"
            "Wejdź na {subdomain} i znajdź Karty podarunkowe, aby dokonać "
            "zakupu.\n\n"
            "Do zobaczenia wkrótce!\n"
            "{business_name}"
        ),
        body_short=(
            "Szukasz wyjatkowego prezentu? W {business_name} znajdziesz teraz "
            "Karty podarunkowe! Wejdz na {subdomain} aby dokonac zakupu.\n\n"
        ),
        image=None,
        recommended=True,
        order=3,
        group__internal_name=MessageBlastGroupEnum.FIRST_IMPRESSION,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.PROMOTE_GIFT_CARDS,
    ),
    dict(
        name='Zaproś na kolejną wizytę',
        description=(
            'Wysyłane do klientów 21 dni po ich ostatniej wizycie, jeśli nie '
            'mają zarezerwowanej kolejnej wizyty'
        ),
        title='Czas na kolejną wizytę!',
        body=(
            "Tęksnimy za Tobą! Czas zarezerwować kolejną wizytę?\n\n"
            "Wejdź na {subdomain} i upoluj jeden z wolnych terminów!\n\n"
            "Do zobaczenia wkrótce\n"
            "{business_name}"
        ),
        body_short=("Tesknimy za Toba! Wejdz na {subdomain} i upoluj jeden z " "wolnych terminow!"),
        image=None,
        recommended=True,
        order=1,
        group__internal_name=MessageBlastGroupEnum.REACTIVATE,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.INVITE_FOR_VISIT,
    ),
    dict(
        name='Zaproś ponownie i zaoferuj bezpłatną usługę',
        description=('Wysyłane do klientów, którzy nie odwiedzili Cię w ciągu ' 'ostatnich 90 dni'),
        title='Zarezerwuj wizytę i odbierz usługę GRATIS',
        body=(
            "Pomyśleliśmy, że zasługujesz na coś wyjątkowego!\n\n"
            "Owiedź {subdomain} i upoluj jeden z wolnych terminów! Zachowaj "
            "tą wiadomość i pokaż ją w salonie, a dorzucimy bezpłatnie "
            "[NAZWA USŁUGI]!\n\n"
            "Do zobaczenia wkrótce!\n"
            "{business_name}"
        ),
        body_short=(
            "Zaslugujesz na cos wyjatkowego! Wejdz na {subdomain} i upoluj "
            "wolny termin. Pokaz ta wiadomosc w salonie, a dorzucimy "
            "bezplatnie [NAZWA USLUGI]!"
        ),
        image=None,
        recommended=False,
        order=2,
        group__internal_name=MessageBlastGroupEnum.REACTIVATE,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.REINVITE_FREE_SERVICE,
    ),
    dict(
        name='Zaproś ponownie i zaoferuj zniżkę',
        description=('Wysyłane do klientów, którzy nie odwiedzili Cię w ciągu ' 'ostatnich 45 dni'),
        title='Specjalna oferta, ponieważ za Tobą tęsknimy',
        body=(
            "Dawno Cię u nas nie było, dlatego chcielibyśmy powitać Cię "
            "wyjątkową zniżką!\n\n"
            "Wejdź na {subdomain} i upoluj jeden z wolnych terminów. Zachowaj "
            "tą wiadomość i pokaż ją w salonie, aby odebrać zniżkę w "
            "wysokości [WYSOKOŚĆ ZNIŻKI]!\n\n"
            "Do zobaczenia wkrótce!\n"
            "{business_name}"
        ),
        body_short=(
            "Dawno Cie u nas nie bylo, dlatego chcemy przywitac Cie wyjatkowa "
            "znizka! Wejdz na {subdomain} i zarezerwuj wizyte."
        ),
        image=None,
        recommended=False,
        order=3,
        group__internal_name=MessageBlastGroupEnum.REACTIVATE,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.REINVITE_DISCOUNT,
    ),
    dict(
        name='Podaruj urodzinową zniżkę',
        description=(
            'Wyślij wiadomość do klientów, którzy będą obchodzić urodziny w '
            'ciągu najbliższych 7 dni.'
        ),
        title='Taki dzień zdarza się tylko raz w roku!',
        body=(
            "Wszystkiego najlepszego z okazji urodzin!\n\n"
            "Aby uczcić ten wyjątkowy dzień, postanowiliśmy przyznać Ci "
            "urodzinowy rabat! Zachowaj wiadomość i pokaż ją w salonie podczas "
            "kolejnej wizyty, aby otrzymać wyjątkową zniżkę "
            "[WYSOKOŚĆ ZNIŻKI].\n\n"
            "Wejdź na {subdomain} aby zarezerwować wizytę już dziś.\n\n"
            "Mamy nadzieję, że ten dzień będzie naprawdę wyjątkowy!\n"
            "{business_name}"
        ),
        body_short=(
            "Wszystkiego najlepszego! Pokaz te wiadomosc podczas wizyty i "
            "odbierz znizke [WYSOKOSC ZNIZKI]. Zarezerwuj wizyte na "
            "{subdomain}!"
        ),
        image=None,
        recommended=False,
        order=1,
        group__internal_name=MessageBlastGroupEnum.SPECIAL_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.HAPPY_BIRTHDAY,
    ),
    #  Links
    dict(
        name='Specjalne okazje',
        description=(
            'Świąteczne wiadomości wysyłane do klientów w celu zachęcenia do '
            'rezerwacji wizyty i nie tylko!'
        ),
        title='',
        body='',
        body_short='',
        image=None,
        recommended=False,
        order=2,
        group__internal_name=MessageBlastGroupEnum.SPECIAL_OCCASIONS,
        automated_status=None,
        link_to_group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        internal_name=MessageBlastInternalNames.MORE_OCCASIONS_LINK,
    ),
    dict(
        name='Aktualne i spersonalizowane',
        description=(
            'Wysyłaj wiadomości do swoich klientów w czasie rzeczywistym! '
            'Wybierz jeden z naszych szablonów lub stwórz swój własny!'
        ),
        title='',
        body='',
        body_short='',
        image=None,
        recommended=False,
        order=1,
        group__internal_name=MessageBlastGroupEnum.YOUR_OWN_MESSAGES,
        automated_status=None,
        link_to_group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
        internal_name=MessageBlastInternalNames.ONE_TIME_MESSAGE_LINK,
    ),
    dict(
        name='Kampanie marketingowe na specjalne okazje',
        description='Bądź w kontakcie ze swoimi klientami',
        title='',
        body='',
        body_short='',
        image=None,
        recommended=False,
        order=2,
        group__internal_name=MessageBlastGroupEnum.YOUR_OWN_MESSAGES,
        automated_status=None,
        link_to_group__internal_name=MessageBlastGroupEnum.SPECIAL_MESSAGE_BLAST,
        internal_name=MessageBlastInternalNames.SPECIAL_MESSAGE_BLASTS_LINK,
    ),
    # Special Occasions
    dict(
        name='Promuj swoje usługi na święta!',
        description=('Wyślij do wszystkich klientów • ' 'Drugi poniedziałek listopada'),
        title='Nie przegap ostatniej szansy na wizytę przed świętami!',
        body=(
            "Święta tuż za rogiem, a nasz kalendarz zapełnia się coraz "
            "szybciej! Wejdź na {subdomain} i upoluj jeden z ostanich "
            "terminów!\n\n"
            "Życzymy Ci wesołych świąt!\n\n"
            "Do zobaczenia wkrótce,\n"
            "{business_name}"
        ),
        body_short=(
            "Swieta tuz za rogiem, a nasz kalendarz zapelnia sie coraz "
            "szybciej! Wejdz na {subdomain} i upoluj jeden z ostatnich "
            "terminow!"
        ),
        image=None,
        recommended=False,
        order=1,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.HOLIDAY_BOOKING,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=11,
        date_monday=2,
    ),
    dict(
        name='Zachęć klientów do wiosennych rezerwacji!',
        description=('Wyślij do wszystkich klientów • ' '21 marca'),
        title='Zadbaj o odrobinę świeżości na wiosnę!',
        body=(
            "To już oficjalne - wróciła wiosna, a my pomożemy Ci odzyskać "
            "energię i dobry nastrój! Wejdź na {subdomain}, zarezerwuj "
            "wizytę i zadbaj o swoje samopoczucie już dziś!\n\n"
            "Do zobaczenia wkrótce!\n"
            "{business_name}"
        ),
        body_short=(
            "Wrocila wiosna, a my pomozemy Ci odzyskac energie i dobry "
            "nastroj! Wejdz na {subdomain}, zarezerwuj wizyte i zadbaj o "
            "siebie juz dzis!"
        ),
        image=None,
        recommended=False,
        order=2,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.SPRING,
        date_type=MessageBlastDateType.STRICT,
        date_month=3,
        date_day=21,
    ),
    dict(
        name='Sprzedawaj Karty podarunkowe na Dzień Ojca!',
        description=('Wyślij do wszystkich klientów • ' 'Drugi poniedziałek czerwca'),
        title='Podziękuj swojemu tacie!',
        body=(
            "Pokaż swojemu tacie, jak bardzo doceniasz jego obecność - podaruj "
            "mu Kartę podarunkową do {business_name} i pozwól mu  wybrać "
            "wymarzoną usługę. Wejdź na {subdomain} i znajdź Karty "
            "podarunkowe, aby dokonać zakupu już dziś!\n\n"
            "Najlepsze życzenia dla każdego Taty,\n"
            "{business_name}"
        ),
        body_short=(
            "Pokaz swojemu tacie, jak bardzo doceniasz jego obecnosc i "
            "podaruj mu Karte podarunkowa do {business_name}! Wejdz na "
            "{subdomain}, aby dokonac zakupu!"
        ),
        image=None,
        recommended=False,
        order=3,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.FATHERS_DAY,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=6,
        date_monday=2,
    ),
    dict(
        name='Sprzedawaj Karty podarunkowe na Dzień Matki!',
        description=('Wyślij do wszystkich klientów • ' 'Drugi poniedziałek maja'),
        title='Idealny prezent na Dzień Matki!',
        body=(
            "Szukasz idealnego prezentu dla mamy? Zaskocz ją Kartą podarunkową "
            "do {business_name} i pozwól jej wybrać wymarzoną usługę! Wejdź "
            "na {subdomain} i znajdź Karty podarunkowe, aby dokonać zakupu!\n\n"
            "Najlepsze życzenia dla wszystkich Mam!\n"
            "{business_name}"
        ),
        body_short=(
            "Szukasz idealnego prezentu dla mamy? Zaskocz ja Karta "
            "podarunkowa do {business_name}! Wejdz na {subdomain} aby "
            "dokonac zakupu!"
        ),
        image=None,
        recommended=False,
        order=4,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.MOTHERS_DAY,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=5,
        date_monday=1,
    ),
    dict(
        name='Promuj swoje usługi na Halloween!',
        description=('Wyślij do wszystkich klientów • ' 'Drugi poniedziałek października'),
        title='Zadbaj o oryginalny look na Halloween!',
        body=(
            "Marzy Ci się oryginalny look na Halloween? W {business_name} "
            "zadbamy o Twój upiorny wygląd! Wejdź na {subdomain}, aby "
            "zarezerwować wizytę zanim inne czarownice i czarnoksiężnicy "
            "zapełnią cały nasz kalendarz!\n\n"
            "Do zobaczenia wkrótce,\n"
            "{business_name}"
        ),
        body_short=(
            "W {business_name} zadbamy o Twoj look na Halloween! Wejdz na "
            "{subdomain} i zarezerwuj wizyte, zanim inne czarownice i "
            "czarnoksieznicy zapelnia nasz kalendarz!"
        ),
        image=None,
        recommended=False,
        order=5,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.HALLOWEEN,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=10,
        date_monday=1,
    ),
    dict(
        name='Zadbaj o rezerwacje w nowym roku!',
        description=('Wyślij do wszystkich klientów • ' 'Pierwszy poniedziałek w nowym roku'),
        title='Nowy Rok, nowa wersja Ciebie?',
        body=(
            "Nadszedł Nowy Rok - powitaj go stając się zupełnie nową "
            "wersją siebie!\n\n"
            "Wejdź na {subdomain} aby zarezerwować wizytę i przygotować się "
            "na nowy start.\n\n"
            "Szczęśliwego Nowego Roku!\n"
            "{business_name}"
        ),
        body_short=(
            "Nadszedl Nowy Rok - powitaj go stajac sie zupelnie nowa wersja "
            "siebie! Wejdz na {subdomain} aby zarezerwowac wizyte i "
            "przygotowac sie na nowy start."
        ),
        image=None,
        recommended=False,
        order=6,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.NEW_YEAR,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=1,
        date_monday=1,
    ),
    dict(
        name='Sprzedawaj Karty podarunkowe na święta!',
        description=('Wyślij do wszystkich klientów • ' 'Pierwszy poniedziałek grudnia'),
        title='Świąteczne zakupy są teraz jeszcze łatwiejsze!',
        body=(
            "W tym roku świąteczne zakupy stają się jeszcze łatwiejsze! "
            "Zaskocz swoich bliskich Kartami podarunkowymi do {business_name} "
            "i pozwól im wybrać wymarzoną usługę. Wybierz Kartę podarunkową "
            "i podaruj ukochanej osobie chwilę relaksu.\n\n"
            "Wejdź na {subdomain} i znajdź Karty podarunkowe, aby "
            "dokonać zakupu.\n\n"
            "Wesołych Świąt!\n"
            "{business_name}"
        ),
        body_short=(
            "Swiateczne zakupy sa teraz jeszcze latwiejsze! Zaskocz swoich "
            "bliskich Kartami podarunkowymi - odwiedz {subdomain} aby "
            "dokonac zakupu!"
        ),
        image=None,
        recommended=False,
        order=7,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.HOLDAY_GIFTCARD,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=12,
        date_monday=1,
    ),
    dict(
        name='Sprzedawaj Karty podarunkowe na Walentynki!',
        description=('Wyślij do wszystkich klientów • ' 'Pierwszy poniedziałek lutego'),
        title='Oczaruj swoją Walentynkę!',
        body=(
            "Walentynki zbliżają się wielkimi krokami! Wybierz Kartę "
            "podarunkową i podaruj ukochanej osobie chwilę relaksu.\n\n"
            "Odwiedź {subdomain} i znajdź Karty podarunkowe, aby dokonać "
            "zakupu.\n\n"
            "Pozdrawiamy!\n"
            "{business_name}"
        ),
        body_short=(
            "Zaskocz swoja Walentynke Karta podarunkowa do {business_name}. "
            "Odwiedz {subdomain} aby dokonac zakupu! "
        ),
        image=None,
        recommended=False,
        order=8,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.VALENTINES_DAY,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=2,
        date_monday=1,
    ),
    dict(
        name='Promuj usługi z okazji powrotu do szkoły!',
        description=('Wyślij do wszystkich klientów • ' 'Pierwszy poniedziałek sierpnia'),
        title='Umów wizytę przed powrotem do szkoły!',
        body=(
            "Nadchodzi rok szkolny! Przygotuj swoje dziecko na powrót do "
            "szkolnej ławki - odwiedź {subdomain} i zarezerwuj wizytę "
            "już dziś!\n\n"
            "Do zobaczenia wkrótce!\n"
            "{business_name}"
        ),
        body_short=(
            "Nadchodzi rok szkolny! Przygotuj swoje dziecko na powrot do "
            "szkolnej lawki - odwiedz {subdomain} i zarezerwuj wizyte!"
        ),
        image=None,
        recommended=False,
        order=9,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.BACK_TO_SCHOOL,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=8,
        date_monday=1,
    ),
    dict(
        name='Promuj swoje usługi na Wielkanoc!',
        description=('Wyślij do wszystkich klientów • ' 'Trzeci poniedziałek marca'),
        title='Nie przegap ostatniej szansy na wizytę przed świętami!',
        body=(
            "Święta tuż za rogiem, a nasz kalendarz zapełnia się coraz "
            "szybciej! Odwiedź {subdomain} i upoluj jeden z ostanich "
            "terminów!\n\n"
            "Spokojnych Świąt Wielkanocnych!\n\n"
            "Do zobaczenia wkrótce,\n"
            "{business_name}"
        ),
        body_short=(
            "Swieta tuz za rogiem, a nasz kalendarz zapelnia sie coraz "
            "szybciej! Odwiedz {subdomain} i upoluj jeden z ostatnich "
            "terminow!"
        ),
        image=None,
        recommended=False,
        order=10,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.EASTER,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=3,
        date_monday=3,
    ),
    dict(
        name='Promuj swoje usługi na Black Friday!',
        description=('Wyślij do wszystkich klientów • ' 'Drugi poniedziałek listopada'),
        title='Upoluj najlepsze okazje na Black Friday!',
        body=(
            "Nadchodzi Black Friday! Z tej okazji przygotowaliśmy dla Ciebie "
            "wyjątkowy rabat w wysokości [WYSOKOŚĆ RABATU] na kolejną wizytę. "
            "Odwiedź {subdomain} i zarezerwuj czas dla siebie.\n\n"
            "Do zobaczenia wkrótce!\n"
            "{business_name}"
        ),
        body_short=(
            "Nadchodzi Black Friday! Odwiedz {subdomain} i zarezerwuj wizyte "
            "z wyjatkowym rabatem [WYSOKOSC RABATU]."
        ),
        image=None,
        recommended=False,
        order=11,
        group__internal_name=MessageBlastGroupEnum.MORE_OCCASIONS,
        automated_status=MessageBlastTemplateStatus.INACTIVE,
        internal_name=MessageBlastInternalNames.BLACK_FRIDAY,
        date_type=MessageBlastDateType.FIRST_MONDAY,
        date_month=11,
        date_monday=2,
    ),
    # One time message
    dict(
        name='Poinformuj klientów o dostępnych terminach',
        description='',
        title='Sprawdź dostępne terminy Last Minute',
        body=(
            "Dowiadujesz się o tym jako pierwszy! Mamy kilka wolnych terminów"
            "[DATA]. Wejdź na {subdomain} i zarezerwuj wizytę!\n\n"
            "Do zobaczenia wkrótce!\n"
            "{business_name}"
        ),
        body_short=(
            "Dowiadujesz sie o tym jako pierwszy! Mamy kilka wolnych terminow "
            "[DATA]. Wejdz na {subdomain} i zarezerwuj wizyte."
        ),
        image=None,
        recommended=False,
        order=1,
        group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
        automated_status=None,
        internal_name=MessageBlastInternalNames.INFORM_ABOUT_NEW_OPENING,
    ),
    dict(
        name='Poinformuj klientów o nadchodzących dniach wolnych',
        description='',
        title='Wybieram się na urlop',
        body=(
            "Biorę odrobinę wolnego i wizyty u mnie nie będą dostępne od "
            "[DATY]. Chcesz skorzystać z ulubionych usług przed moim urlopem? "
            "Wejdź na {subdomain} i upoluj jeden z wolnych terminów!\n\n"
            "Do zobaczenia wkrótce!\n"
            "{business_name}"
        ),
        body_short=(
            "Biore odrobine wolnego. Chcesz skorzystac z ulubionych uslug "
            "przed moim urlopem? Wejdz na {subdomain} i zarezerwuj wizyte!"
        ),
        image=None,
        recommended=False,
        order=2,
        group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
        automated_status=None,
        internal_name=MessageBlastInternalNames.INFORM_ABOUT_TIME_OFF,
    ),
    dict(
        name='Poinformuj klientów o nowych usługach',
        description='',
        title='Sprawdź nasze nowe usługi!',
        body=(
            "Nadchodzą ekscytujące wiadomości! Dodaliśmy właśnie zupełnie nowe "
            "usługi, które mogą Cię zainteresować. Wejdź na {subdomain}, "
            "odkryj nowe możliwości w {business_name} i zarezerwuj wizytę "
            "już dziś!"
            "Do zobaczenia wkrótce!\n"
            "{business_name}"
        ),
        body_short=(
            "Dodalismy wlasnie zupelnie nowe uslugi, ktore moga Cie "
            "zainteresowac! Wejdz na {subdomain} i zarezerwuj wizyte!"
        ),
        image=None,
        recommended=False,
        order=3,
        group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
        automated_status=None,
        internal_name=MessageBlastInternalNames.INFORM_ABOUT_NEW_SERVICES,
    ),
    dict(
        name='Zaproś klientów na ważne wydarzenie',
        description='',
        title='Zaproszenie! ',
        body=(
            "W {business_name} czeka nas wyjątkowe wydarzenie i nie może Cię "
            "na nim zabraknąć! Dołącz do nas na [NAZWA WYDARZENIA] w dniu "
            "[DATA WYDARZENIA] - gwarantujemy świetną atmosferę i najlepsze "
            "towarzystwo!\n\n"
            "[WSTAW SZCZEGÓŁY WYDARZENIA]\n\n"
            "Mamy nadzieję, że do nas dołączysz!\n"
            "{business_name}"
        ),
        body_short=(
            "W {business_name} czeka nas wyjatkowe wydarzenie i nie moze Cie "
            "na nim zabraknac! Gwarantujemy swietna atmosfere i najlepsze "
            "towarzystwo!"
        ),
        image=None,
        recommended=False,
        order=4,
        group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
        automated_status=None,
        internal_name=MessageBlastInternalNames.INVITE_TO_EVENT,
    ),
    dict(
        name='Poproś klientów o polecenie Twojego biznesu',
        description='',
        title='Powiedz znajomemu... a nawet kilku znajomym!',
        body=(
            "Chcesz skorzystać z wyjątkowej zniżki podczas kolejnej wizyty? "
            "Poleć {business_name} znajomemu! Poproś go, aby odwiedził "
            "{subdomain} w celu rezerwacji wizyty, a w salonie wymienił "
            "Twoje imię i nazwisko!\n\n"
            "Zarówno Ty, jak i Twój znajomy otrzymacie wyjątkową zniżkę - "
            "każdy wygrywa!\n\n"
            "Do zobaczenia wkrótce!\n"
            "{business_name}"
        ),
        body_short=(
            "Polec {business_name} znajomemu, a oboje otrzymacie wyjatkowa "
            "znizke! Do zobaczenia wkrotce!"
        ),
        image=None,
        recommended=False,
        order=5,
        group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
        automated_status=None,
        internal_name=MessageBlastInternalNames.ASK_FOR_REFERRAL,
    ),
    dict(
        name='Poinformuj klientów o zmianach w cenniku',
        description='',
        title='Wpadnij do nas przed zmianą cen!',
        body=(
            "Chcielibyśmy poinformować Cię, że nasze ceny ulegną wkrótce "
            "zmianie. Wejdź na {subdomain} i skorzystaj z ostatniej szansy, "
            "aby zarezerwować wizytę w starej cenie!\n\n"
            "Doceniamy naszych klientów, dlatego nie możemy się doczekać, aż "
            "odwiedzisz nas ponownie!\n"
            "{business_name}"
        ),
        body_short=(
            "Informujemy, ze nasze ceny ulegna wkrotce zmianie. Wejdz na "
            "{subdomain} i skorzystaj z ostatniej szansy na rezerwacje wizyty "
            "przed zmianami!"
        ),
        image=None,
        recommended=False,
        order=6,
        group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
        automated_status=None,
        internal_name=MessageBlastInternalNames.INFORM_ABOUT_PRICE_CHANGES,
    ),
    dict(
        name='Stwórz swoją własną wiadomość',
        description='',
        title='',
        body='',
        body_short='',
        image=None,
        recommended=False,
        order=7,
        group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE,
        automated_status=None,
        internal_name=MessageBlastInternalNames.OWN_MESSAGE,
    ),
    dict(
        name='Zachęcaj klientów do korzystania z kart podarunkowych',
        description='',
        title='Skorzystaj z karty podarunkowej',
        body=(
            "Wygląda na to, że masz pod ręką wyjątkową kartę podarunkową "
            "{business_name}. Zajrzyj na {subdomain}, aby z niej "
            "skorzystać i zapisać się na wizytę.\n\n"
            "Do zobaczenia niebawem!\n\n"
            "Pozdrawiamy,\n"
            "{business_name}"
        ),
        body_short=(
            "Wykorzystaj swoją kartę podarunkową w {business_name}. "
            "Zajrzyj na {subdomain}, aby umówić wizytę."
        ),
        image=None,
        recommended=False,
        order=1,
        group__internal_name=MessageBlastGroupEnum.SPECIAL_MESSAGE_BLAST,
        automated_status=None,
        internal_name=MessageBlastInternalNames.GIFT_CARD_BALANCES,
    ),
    dict(
        name='Poproś klientów o opinie i oceny',
        description='',
        title='Podziel się z nami swoją opinią',
        body=(
            "To właśnie dzięki naszym klientom możemy robić to, co uwielbiamy! "
            "Daj znać, jak nam idzie, abyśmy mieli pewność, że jesteś "
            "zadowolony z wizyt w {business_name}. I śmiało powiedz o nas "
            "innym. Wejdź na {subdomain} i zostaw recenzję na naszym profilu "
            "w Booksy.\n\n"
            "Cieszymy się, że jesteś z nami.\n\n"
            "Pozdrawiamy,\n"
            "{business_name}"
        ),
        body_short=(
            "Daj znać, jak nam idzie i śmiało opowiadaj o nas innym. Wejdź na "
            "{subdomain} i zostaw recenzję na naszym profilu w Booksy."
        ),
        image=None,
        recommended=False,
        order=2,
        group__internal_name=MessageBlastGroupEnum.SPECIAL_MESSAGE_BLAST,
        automated_status=None,
        internal_name=MessageBlastInternalNames.ASK_FOR_REVIEWS,
    ),
    dict(
        name='Daj znać swoim klientom, że są dla Ciebie ważni',
        description='',
        title='Musisz wiedzieć, że...',
        body=(
            "Prowadzenie biznesu nie zawsze jest proste, mimo że może się tak "
            "wydawać na pierwszy rzut oka. Czasem trzeba posiedzieć do późna. "
            "Czasem trzeba wstać bardzo wcześnie. Czasem pojawia się "
            "niepewność, bo świat nieustannie się zmienia i pędzi do "
            "przodu.\n\n"
            "Obserwujemy naszą społeczność i gdy patrzymy na osoby, które "
            "każdego dnia nas odwiedzają, czujemy, że to wszystko ma sens. "
            "Dzięki Tobie mamy siłę do działania: do stawania się jeszcze "
            "lepszymi.\n\n"
            "Dziękujemy, że wspierasz {business_name}. "
            "Chcemy Ci przypomnieć, ile to dla nas znaczy."
        ),
        body_short=(
            "Dziękujemy, że wspierasz {business_name}. "
            "Chcemy Ci przypomnieć, ile to dla nas znaczy."
        ),
        image=None,
        recommended=False,
        order=3,
        group__internal_name=MessageBlastGroupEnum.SPECIAL_MESSAGE_BLAST,
        automated_status=None,
        internal_name=MessageBlastInternalNames.APPRECIATE_CUSTOMER,
    ),
    dict(
        name='Poinformuj klientów, że zmieniła się Twoja dostępność',
        description='',
        title='Sprawdź, w jakich godzinach jesteśmy dostępni',
        body=(
            "Sprawdź, w jakich godzinach jesteśmy dostępni. "
            "Wejdź na {subdomain}, zobacz co nowego i zarezerwuj wolny "
            "termin, zanim zrobią to inni. Nie możemy się już doczekać "
            "Twojej wizyty!\n\n"
            "Pozdrawiamy,\n"
            "{business_name}"
        ),
        body_short=(
            "Właśnie zaktualizowaliśmy nasz profil. Wejdź na {subdomain}, "
            "zobacz, co nowego, i umów się na wizytę. "
        ),
        image=None,
        recommended=False,
        order=4,
        group__internal_name=MessageBlastGroupEnum.SPECIAL_MESSAGE_BLAST,
        automated_status=None,
        internal_name=MessageBlastInternalNames.UPDATED_BOOKS,
    ),
    dict(
        name='Poinformuj klientów, gdzie mogą zrobić rezerwację',
        description='',
        title='Rezerwuj wizyty tak, jak Ci wygodnie',
        body=(
            "Połączyliśmy nasz profil Booksy z innymi kanałami online, "
            "żebyś mógł rezerwować wizyty tak jak Ci wygodnie: przez nasze "
            "profile na Facebooku i Instagramie, przez wyszukiwarkę Google "
            "oraz przez naszą stronę internetową.\n\n"
            "Aplikacja Booksy dla klienta to najprostszy sposób na umówienie "
            "wizyty, ale jak widzisz, nie jedyny!\n\n"
            "Do zobaczenia wkrótce,\n"
            "{business_name}"
        ),
        body_short=(
            "Połączyliśmy nasz profil Booksy z innymi portalami, żebyś mógł "
            "rezerwować wizyty równiez przez Facebooka, Instagram, Google i "
            "naszą stronę internetową."
        ),
        image=None,
        recommended=False,
        order=5,
        group__internal_name=MessageBlastGroupEnum.SPECIAL_MESSAGE_BLAST,
        automated_status=None,
        internal_name=MessageBlastInternalNames.WHERE_YOU_CAN_BOOK,
    ),
]
