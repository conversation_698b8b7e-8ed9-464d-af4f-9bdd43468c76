import logging
import re

import datetime
import typing as t
import uuid
from collections import Counter
from decimal import Decimal
from typing import Union

from django.contrib.postgres.fields import ArrayField
from django.db import models
from django.db.models import Count, F, Func, OuterRef, Q, QuerySet, Subquery, Sum, Value
from django.db.models.functions import Coalesce
from django.utils.translation import gettext_lazy as _

from lib.feature_flag.feature.message_blast import ReturnMessageBlastImagesForTherapyCategory
from lib.fields.phone_number import BooksyPhoneNumberField
from lib.models import ArchiveManager, ArchiveModel, AutoAddHistoryModel, HistoryModel
from lib.tools import tznow
from settings.local import AUTO_BLAST_LIMIT, MANUAL_BLAST_LIMIT
from webapps.booking.enums import AppointmentStatus
from webapps.booking.models import Appointment
from webapps.business.enums import BusinessCategoryEnum
from webapps.business.models import Business
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business.models.category import BusinessCategory
from webapps.images.models import Image
from webapps.message_blast.channels import MessageBlastChannelType
from webapps.message_blast.enums import (
    MESSAGE_BLAST_GROUP_WITH_IMAGES,
    MESSAGE_BLAST_GROUP_WITH_IMAGES_WITH_SPECIAL_MB,
    MessageBlastDateType,
    MessageBlastGroupEnum,
    MessageBlastImageCategoryNames,
    MessageBlastInternalNames,
    MessageBlastTemplateStatus,
    MessageBlastTimeType,
)
from webapps.message_blast.schedules import MessageBlastSendingSchedules
from webapps.message_blast.tools import calculate_monday, is_gdpr_enabled
from webapps.notification.enums import BlastSendType, NotificationService
from webapps.profile_completeness.events import step_send_message_blast
from webapps.profile_completeness.models import Reward
from webapps.profile_completeness.rewards import MessageBlastTemplateReward
from webapps.user.models import User
from webapps.voucher.models import Voucher, VoucherTemplate


logger = logging.getLogger('booksy.message_blast')


REMOVE_NON_BOOOKSY_DOMAINS_REGEX = (
    r"\b(https?:\/\/(www\.)?)?(?![\w\-.]*(\.booksy\.com|instagram\.com))"
    r"([-\w@%.+~#=]{1,256}\.[a-zA-Z]{2,10}\b)(?<!\bbooksy\.com)"
)


class MessageBlastGroup(ArchiveModel):
    title = models.CharField(max_length=128)
    parent_group = models.ForeignKey(
        'self',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
    )
    order = models.IntegerField()
    internal_name = models.CharField(max_length=128)

    objects = ArchiveManager()
    all_objects = models.Manager()

    class Meta:
        ordering = ['order']

    def __str__(self) -> str:
        return f'{self.title} [{self.parent_group and self.parent_group.title}]'


class MessageBlastGeneralInfo(models.Model):
    """Contains general information about template.

    Used in CommonMessageBlastTemplate and MessageBlastTemplate"""

    name = models.CharField(max_length=256, null=True)
    description = models.CharField(max_length=256, blank=True, null=True)

    class Meta:
        abstract = True


class MessageBlastContent(models.Model):
    """Contains only content of template.

    Used in CommonMessageBlastTemplate, MessageBlastTemplate and MessageBlast.
    """

    title = models.CharField(max_length=256, null=True, blank=True)
    body = models.TextField(blank=True, null=True)
    body_short = models.CharField(max_length=320, null=True, blank=True)
    image = models.ForeignKey(
        Image,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
    )
    internal_name = models.CharField(max_length=128, choices=MessageBlastInternalNames.choices())
    channel_priority = models.JSONField(
        default=dict,
        # One cannot give mutable object
        # default={
        #     MessageBlastChannelType.DEFAULT.value: True,
        #     MessageBlastChannelType.SMS.value: False,
        #     MessageBlastChannelType.PREFER_PUSH.value: False,
        #     MessageBlastChannelType.PUSH.value: False,
        #     MessageBlastChannelType.EMAIL.value: False,
        # }
        blank=True,
    )

    class Meta:
        abstract = True

    def save(self, force_insert=False, force_update=False, using=None, update_fields=None):
        not_allowed_links = None
        string_to_check = None

        # removal works "silently", because returning validation-error
        # will allow to "hack" regex by Px
        pattern = re.compile(REMOVE_NON_BOOOKSY_DOMAINS_REGEX, re.MULTILINE)
        string_to_check = f"{self.title}; {self.body}; {self.body_short};"
        if not_allowed_links := re.findall(pattern, string_to_check):
            if self.title:
                self.title = re.sub(pattern, "", self.title)
            if self.body:
                self.body = re.sub(pattern, "", self.body)
            if self.body_short:
                self.body_short = re.sub(pattern, "", self.body_short)

        super().save(
            force_insert=False,
            force_update=False,
            using=None,
            update_fields=None,
        )

        if not_allowed_links:
            logger.warning(
                "%s ID: %s looks susceptible so text in the message was cleaned. "
                "Original text (Title; Body; BodyShort;): %s",
                self.__class__.__name__,
                self.id,
                string_to_check,
            )


class CommonMessageBlastTemplateManager(ArchiveManager):
    def filter_baseclass(self):
        return self.filter(
            messageblasttemplate__isnull=True,  # Find only base class
        )

    def filter_childclass(self):
        return self.filter(messageblasttemplate__isnull=False)

    def filter_not_overridden(self, business_id: int):
        """Checks if business is not already using overridden template"""
        return self.filter(link_to_group__isnull=True).exclude(
            child_templates__business_id=business_id,
        )

    def filter_one_time_messages(self, business_id):
        return (
            self.filter(
                link_to_group__isnull=True,
            )
            .filter(
                Q(group__internal_name=MessageBlastGroupEnum.ONE_TIME_MESSAGE)
                | Q(group__internal_name=MessageBlastGroupEnum.SPECIAL_MESSAGE_BLAST)
            )
            .exclude(
                child_templates__business_id=business_id,
            )
        )


class CommonMessageBlastTemplate(MessageBlastGeneralInfo, MessageBlastContent, ArchiveModel):
    """Describes common templates for all business."""

    order = models.IntegerField()
    group = models.ForeignKey(
        MessageBlastGroup,
        on_delete=models.PROTECT,
        related_name='templates',
    )

    link_to_group = models.ForeignKey(
        MessageBlastGroup,
        on_delete=models.PROTECT,
        related_name='template_links',
        null=True,
        blank=True,
    )

    automated_status = models.CharField(
        max_length=1,
        choices=MessageBlastTemplateStatus.choices(),
        null=True,
        blank=True,
        default=MessageBlastTemplateStatus.INACTIVE,
    )
    date_type = models.CharField(
        choices=MessageBlastDateType.choices(),
        null=True,
        blank=True,
        max_length=2,
    )
    date_monday = models.IntegerField(null=True, blank=True)
    date_month = models.IntegerField(null=True, blank=True)
    date_day = models.IntegerField(null=True, blank=True)
    date_hour = models.PositiveSmallIntegerField(
        choices=MessageBlastTimeType.choices(),
        default=MessageBlastTimeType.MORNING.value,
        verbose_name='Preferable business time for send',
    )
    date_schedule = models.PositiveSmallIntegerField(
        null=True,
        blank=True,
        verbose_name='Preferable option for send before or after some event',
    )
    recommended = models.BooleanField(default=False)

    objects = CommonMessageBlastTemplateManager()
    all_objects = models.Manager()

    class Meta:
        ordering = ['group__order', 'order']

    def __str__(self) -> str:
        return f'{self.name} | {self.group.title}'

    @property
    def is_one_time(self):
        return self.group and (
            self.group.internal_name
            in [MessageBlastGroupEnum.ONE_TIME_MESSAGE, MessageBlastGroupEnum.SPECIAL_MESSAGE_BLAST]
        )

    @staticmethod
    def get_template(internal_name: str, business_id: id):
        """Return either MessageBlastTemplate or CommonMessageBlastTemplate
        specific for business.
        """
        return (
            CommonMessageBlastTemplate.objects.filter(
                link_to_group__isnull=True,
                internal_name=internal_name,
            )
            .exclude(
                child_templates__business_id=business_id,
            )
            .first()
        )

    @staticmethod
    def create_blast_template_for_promotions(business: Business, title: str, text: str):
        """Creates data for one time blast template for promotions."""
        # pylint: disable=cyclic-import
        from webapps.images.serializers import ImageSerializer
        from webapps.message_blast.serializers import get_default_image_for_blasts

        own_template = CommonMessageBlastTemplate.get_template(
            MessageBlastInternalNames.OWN_MESSAGE, business.id
        )
        image = get_default_image_for_blasts(
            MessageBlastInternalNames.BUSINESS_PROMOTIONS, business
        )
        image_serializer = ImageSerializer(instance=image)

        return {
            'id': own_template and own_template.id,
            'title': title,
            'body': text,
            'body_short': text,
            'image': image_serializer.data,
        }

    def get_date(self, next_possible=False) -> t.Optional[datetime.datetime]:
        """Get date from Message Blast Template"""
        if not self.date_type:
            return None

        now = tznow()
        if self.date_type == MessageBlastDateType.STRICT:
            message_blast_date_tuple = (self.date_month, self.date_day)
            message_blast_date = datetime.datetime(now.year, *message_blast_date_tuple)
            if next_possible and message_blast_date <= datetime.datetime.now():
                message_blast_date = message_blast_date.replace(year=message_blast_date.year + 1)

        else:
            message_blast_date_tuple = calculate_monday(self.date_month, self.date_monday)
            message_blast_date = datetime.datetime(now.year, *message_blast_date_tuple)
            if next_possible and message_blast_date <= datetime.datetime.now():
                year = now.year + 1
                message_blast_date_tuple = calculate_monday(
                    self.date_month, self.date_monday, year=year
                )
                message_blast_date = datetime.datetime(year, *message_blast_date_tuple)

        return message_blast_date

    def get_text_recommended(self) -> t.Optional[str]:
        if self.date_type == MessageBlastDateType.FIRST_MONDAY and self.description:
            sep = ' • '
            if sep in self.description:
                try:
                    return self.description.split(sep=sep)[1]
                except IndexError:
                    pass
        return None


class MessageBlastTemplate(AutoAddHistoryModel, CommonMessageBlastTemplate):
    """Describes template customized for particular business."""

    common_template = models.ForeignKey(
        CommonMessageBlastTemplate,
        related_name='child_templates',
        on_delete=models.CASCADE,
    )
    business = models.ForeignKey(
        Business,
        on_delete=models.CASCADE,
    )
    objects = ArchiveManager()
    all_objects = models.Manager()

    def __str__(self) -> str:
        return f'{self.name}[{self.common_template.name}] | {self.business_id}'

    @classmethod
    def get_used_common_templates(cls, business_id: int) -> t.List[int]:
        """Return common templates id which are already overridden by business."""
        return list(
            cls.objects.filter(
                business_id=business_id,
            ).values_list('common_template_id', flat=True)
        )

    def create_blast(
        self,
        bcis: t.List[int] = None,
        _history: dict = None,
    ):
        """Creates blast from MessageBlastTemplate."""
        if not bcis:
            return

        if not self.business.active:
            return

        message_blast = MessageBlast.objects.create(
            title=self.title,
            body=self.body,
            body_short=self.body_short,
            image=self.image,
            template=self,
            business=self.business,
            internal_name=self.internal_name,
            bcis=bcis if bcis else [],
            recipients_filter=_history or {},
            channel_priority=self.channel_priority,
        )

        if self.internal_name == MessageBlastInternalNames.WELCOME_NEW_CLIENT:
            message_blast.send_message_welcome_new_client()
        else:
            message_blast.send_message()

        # profile completeness
        step_send_message_blast.send(self.business)

    def check_date(self) -> bool:
        """Checks if MessageBlastTemplate is date dependant. If yes and
        today is the day when MessageBlastTemplate should be processed returns
        True. Otherwise False.

        Templates which are not date dependant always return True.
        """

        if not self.date_type:
            return True

        message_blast_date = self.get_date().date()

        if tznow().date() == message_blast_date:
            return True
        return False

    @staticmethod
    def bcis_excluded_by_limits(
        business: Business,
        manual_blast: bool,
    ) -> t.List[int]:
        """Antispam filters. Return ids of bci which already exceeded limit
        of messages in past 30 days.
        """
        bci_ids = (
            MessageBlast.objects.filter(
                business=business,
                created__gte=tznow() - datetime.timedelta(days=30),
                template__common_template__automated_status__isnull=manual_blast,
            )
            .annotate(array_unnest=Func(F('bcis'), function='unnest'))
            .values_list('array_unnest', flat=True)
        )

        limit = MANUAL_BLAST_LIMIT if manual_blast else AUTO_BLAST_LIMIT

        counter = Counter(bci_ids)
        excluded_by_limits_bci = [bci_ids for bci_ids in counter if counter[bci_ids] >= limit]

        return excluded_by_limits_bci

    def _bcis_with_message(self, days_back=None) -> t.List[int]:
        filters = [
            Q(business=self.business),
            Q(internal_name=self.internal_name),
        ]

        if days_back:
            filters.append(Q(created__gte=tznow() - datetime.timedelta(days=days_back)))

        return list(
            MessageBlast.objects.filter(*filters)
            .annotate(array_unnest=Func(F('bcis'), function='unnest'))
            .values_list('array_unnest', flat=True)
            .distinct()
        )

    def _get_bcis_with_first_visit(self, days) -> QuerySet(BusinessCustomerInfo):
        """Returns BCIs for business who have their first booking exactly
        X days ago.
        """
        return BusinessCustomerInfo.objects.filter(
            business=self.business,
            first_appointment__booked_till__lte=(tznow() - datetime.timedelta(days=days - 1)),
            first_appointment__booked_till__gt=(tznow() - datetime.timedelta(days=days)),
        ).exclude(
            id__in=self._bcis_with_message(),
        )

    def _get_bcis_promote_gitft_cards(self, days: int) -> QuerySet(BusinessCustomerInfo):
        """Returns new clients X days after first appointment.
        Business has to have at least one gift card template created.
        """
        gift_card_exists = VoucherTemplate.objects.filter(
            pos__business=self.business,
            active=True,
            type=Voucher.VOUCHER_TYPE__EGIFT_CARD,
        ).exists()

        if not gift_card_exists:
            return BusinessCustomerInfo.objects.none()

        return self._get_bcis_with_first_visit(days=days)

    def _get_bcis_with_last_booking_but_not_first_visit(
        self,
        days: int,
    ) -> QuerySet(BusinessCustomerInfo):
        """Returns bci with last booking X days ago, excluding first time
        visitors."""
        return (
            BusinessCustomerInfo.objects.filter(
                business=self.business,
            )
            .annotate(
                bookings_count=Count('appointments'),
                last_booking_date=Subquery(
                    Appointment.objects.filter(booked_for_id=OuterRef('id'))
                    .order_by('-booked_till')
                    .values('booked_till')[:1]
                ),
            )
            .filter(
                last_booking_date__lte=tznow() - datetime.timedelta(days=days - 1),
                last_booking_date__gt=tznow() - datetime.timedelta(days=days),
            )
            .exclude(
                Q(bookings_count=1),
            )
        )

    def _get_bcis_with_last_booking(self, days: int) -> QuerySet(BusinessCustomerInfo):
        """Returns bcis with last booking exactly X days ago."""
        return (
            BusinessCustomerInfo.objects.filter(
                business=self.business,
            )
            .annotate(
                last_booking_date=Subquery(
                    Appointment.objects.filter(booked_for_id=OuterRef('id'))
                    .order_by('-booked_till')
                    .values('booked_till')[:1]
                )
            )
            .filter(
                last_booking_date__lte=tznow() - datetime.timedelta(days=days - 1),
                last_booking_date__gt=tznow() - datetime.timedelta(days=days),
            )
        )

    def _get_bcis_reinvite_happy_birthday(
        self,
        days: int,
    ) -> QuerySet(BusinessCustomerInfo):
        """Return bcis with birthdays in next X days."""
        return BusinessCustomerInfo.objects.filter(
            business=self.business,
            birthday__month__lte=(tznow() + datetime.timedelta(days=days)).month,
            birthday__month__gte=tznow().month,
            birthday__day__lte=(tznow() + datetime.timedelta(days=days)).day,
            birthday__day__gte=tznow().day,
        ).exclude(
            # More than 7 days
            id__in=self._bcis_with_message(days + 1),
        )

    def get_bcis(self) -> t.List[int]:
        """Return BusinessCustomerInfos who should get messageBlast.

        All automated blasts has specific method calculating receiver group.
        Other blast will get all business BCIS.
        """

        # Default qs is used ony for date dependant blasts. We need to check
        # if selected templates was processed today. If not we can send
        # message.
        default_qs = BusinessCustomerInfo.objects.filter(
            business=self.business,
        ).exclude(
            id__in=(
                self._bcis_with_message(days_back=30)
                + self.bcis_excluded_by_limits(business=self.business, manual_blast=False)
            )
        )

        qs = {
            MessageBlastInternalNames.WELCOME_NEW_CLIENT: BusinessCustomerInfo.objects.none(),
            MessageBlastInternalNames.INFORM_ABOUT_OTHER_SERVICES: self._get_bcis_with_first_visit(
                days=(
                    self.date_schedule
                    or MessageBlastSendingSchedules.get(
                        MessageBlastInternalNames.INFORM_ABOUT_OTHER_SERVICES
                    ).get_default_value()
                ),
            ),
            MessageBlastInternalNames.PROMOTE_GIFT_CARDS: self._get_bcis_promote_gitft_cards(
                days=(
                    self.date_schedule
                    or MessageBlastSendingSchedules.get(
                        MessageBlastInternalNames.PROMOTE_GIFT_CARDS
                    ).get_default_value()
                ),
            ),
            MessageBlastInternalNames.INVITE_FOR_VISIT: (
                self._get_bcis_with_last_booking_but_not_first_visit(
                    days=(
                        self.date_schedule
                        or MessageBlastSendingSchedules.get(
                            MessageBlastInternalNames.INVITE_FOR_VISIT
                        ).get_default_value()
                    ),
                )
            ),
            MessageBlastInternalNames.REINVITE_FREE_SERVICE: self._get_bcis_with_last_booking(
                days=(
                    self.date_schedule
                    or MessageBlastSendingSchedules.get(
                        MessageBlastInternalNames.REINVITE_FREE_SERVICE
                    ).get_default_value()
                ),
            ),
            MessageBlastInternalNames.REINVITE_DISCOUNT: self._get_bcis_with_last_booking(
                days=(
                    self.date_schedule
                    or MessageBlastSendingSchedules.get(
                        MessageBlastInternalNames.REINVITE_DISCOUNT
                    ).get_default_value()
                ),
            ),
            MessageBlastInternalNames.HAPPY_BIRTHDAY: self._get_bcis_reinvite_happy_birthday(
                days=(
                    self.date_schedule
                    or MessageBlastSendingSchedules.get(
                        MessageBlastInternalNames.INVITE_FOR_VISIT
                    ).get_default_value()
                ),
            ),
        }.get(self.internal_name, default_qs)

        filters = [Q(blasts_unsubscribed__isnull=True), Q(visible_in_business=True)]
        if is_gdpr_enabled():
            filters.append(Q(web_communication_agreement=True))

        return list(qs.filter(*filters).values_list('id', flat=True))


class MessageBlast(MessageBlastContent, ArchiveModel):
    """Describes sent MessageBlasts"""

    template = models.ForeignKey(
        MessageBlastTemplate,
        related_name='message_blasts',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
    )

    business = models.ForeignKey(
        Business,
        related_name='message_blasts',
        on_delete=models.CASCADE,
    )
    bcis = ArrayField(models.IntegerField(), default=list)
    with_agreements = models.BooleanField(default=True)
    recipients_filter = models.JSONField(
        default=dict,
        blank=True,
        null=True,
    )
    date_hour = models.PositiveSmallIntegerField(
        choices=MessageBlastTimeType.choices(),
        verbose_name='Preferable business time for send',
        blank=True,
        null=True,
    )
    scheduled_date = models.DateField(
        null=True,
        blank=True,
    )
    estimated_sms_cost = models.FloatField(
        null=True,
        blank=True,
    )
    name = models.CharField(max_length=256, null=True, blank=True)
    sent = models.BooleanField(default=True, db_index=True)
    sent_by = models.ForeignKey(
        User,
        on_delete=models.DO_NOTHING,
        null=True,
        blank=True,
        db_constraint=False,
        help_text="User who send Message Blast",
    )
    out_of_sms = models.BooleanField(default=False, null=True, blank=True)
    blast_send_type = models.CharField(
        max_length=1,
        choices=BlastSendType.choices(),
        null=True,
        default=BlastSendType.AUTOMATIC,
    )

    objects = ArchiveManager()
    all_objects = models.Manager()

    def get_appointments_summary_aggregate(self) -> dict:
        required_appointment_status = [
            AppointmentStatus.ACCEPTED,
            AppointmentStatus.FINISHED,
        ]
        one_day = datetime.timedelta(days=1)

        appointments_count = Count('id', distinct=True)
        estimated_revenue = Coalesce(
            Sum('total_value'),
            Value(Decimal(0)),
        )

        return Appointment.objects.filter(
            status__in=required_appointment_status,
            booked_for_id__in=self.bcis,
            created__gt=self.created,
            created__lte=self.created + one_day,
        ).aggregate(
            appointments_count=appointments_count,
            estimated_revenue=estimated_revenue,
        )

    def send_message(self):
        from webapps.message_blast.events import send_message_blast

        send_message_blast.send(self)

    def send_message_welcome_new_client(self):
        from webapps.message_blast.events import send_welcome_new_client_blast

        if self.internal_name != MessageBlastInternalNames.WELCOME_NEW_CLIENT:
            raise ValueError(
                'send_message_welcome_new_client can only send WELCOME_NEW_CLIENT Message Blast'
            )

        send_welcome_new_client_blast.send(self)

    def send_message_report(self):
        from webapps.message_blast.events import send_message_blast_report

        send_message_blast_report.send(self)

    def send_test_message(self):
        from webapps.message_blast.events import send_test_message_blast

        send_test_message_blast.send(self)

    def is_channel_valid(self, channel_type: Union['MessageBlastChannelType', 'Channel.Type']):
        if not self.channel_priority:
            return True
        return bool(
            self.channel_priority[MessageBlastChannelType.DEFAULT]
            or self.channel_priority[channel_type.value]
        )

    @property
    def channel_sms(self):
        return self.is_channel_valid(MessageBlastChannelType.SMS)

    @property
    def channel_push(self):
        return self.is_channel_valid(MessageBlastChannelType.PUSH)

    @property
    def channel_email(self):
        return self.is_channel_valid(MessageBlastChannelType.EMAIL)


class MessageBlastCategoryImage(ArchiveModel):
    image = models.ForeignKey(
        Image,
        related_name='message_blast_images',
        on_delete=models.CASCADE,
    )

    message_blast_group = models.ForeignKey(
        MessageBlastGroup,
        related_name='message_blast_images',
        on_delete=models.CASCADE,
    )

    category = models.CharField(
        choices=MessageBlastImageCategoryNames.choices(),
        default=MessageBlastImageCategoryNames.OTHER,
        max_length=32,
    )

    template_internal_name = models.CharField(
        choices=MessageBlastInternalNames.choices(),
        default=MessageBlastInternalNames.WELCOME_NEW_CLIENT,
        max_length=128,
    )

    def __str__(self):
        return f'{self.image.id} | {self.message_blast_group.title} | {self.category}'

    @staticmethod
    def normalize_business_category(category: BusinessCategory):
        if not category:
            return MessageBlastImageCategoryNames.OTHER
        return {
            BusinessCategoryEnum.BARBERS: MessageBlastImageCategoryNames.BARBERS,
            BusinessCategoryEnum.HAIR_SALONS: MessageBlastImageCategoryNames.HAIR_SALONS,
            BusinessCategoryEnum.NAIL_SALONS: MessageBlastImageCategoryNames.NAIL_SALONS,
        }.get(category.internal_name, MessageBlastImageCategoryNames.OTHER)

    @classmethod
    def get_grouped_images(
        cls,
        business: Business,
        prioritized_group_internal_name: str = None,
    ):
        """Gets images grouped with groups.

        If there is prioritized_group_internal_name put it on the top.
        """
        search_category = cls.normalize_business_category(
            business.primary_category,
        )

        list_with_images = MESSAGE_BLAST_GROUP_WITH_IMAGES

        # if business has profile completeness reward: special_message_blast
        mb_reward_granted = Reward.objects.filter(
            business=business,
            reward_internal_name=MessageBlastTemplateReward.reward_internal_name,
        ).exists()
        if mb_reward_granted:
            list_with_images = MESSAGE_BLAST_GROUP_WITH_IMAGES_WITH_SPECIAL_MB

        # If there is prioritized group put it on the top
        if prioritized_group_internal_name:
            prioritized_group = MessageBlastGroup.objects.get(
                internal_name=prioritized_group_internal_name
            )

            groups = list(
                MessageBlastGroup.objects.exclude(
                    internal_name=prioritized_group_internal_name,
                ).filter(
                    internal_name__in=list_with_images,
                )
            )
            groups.insert(0, prioritized_group)
        else:
            groups = list(
                MessageBlastGroup.objects.filter(
                    internal_name__in=list_with_images,
                )
            )

        for group in groups:
            group.images = [
                x.image
                for x in cls.objects.filter(category=search_category, message_blast_group=group)
                .exclude(
                    # WHERE_YOU_CAN_BOOK are the same as UPDATED_BOOKS images
                    template_internal_name=MessageBlastInternalNames.WHERE_YOU_CAN_BOOK
                )
                .select_related('image')
            ]
        return groups


class MessageBlastImage(ArchiveModel):
    image = models.ForeignKey(
        Image,
        related_name='blast_images',
        on_delete=models.CASCADE,
    )

    category = models.CharField(
        choices=MessageBlastImageCategoryNames.choices(),
        max_length=64,
        null=True,
        blank=True,
    )

    template_internal_name = models.CharField(
        choices=MessageBlastInternalNames.choices(),
        max_length=128,
        null=True,
        blank=True,
    )

    order = models.IntegerField(default=100)

    class Meta:
        ordering = ['order', 'id']

    @staticmethod
    def normalize_business_category(category: BusinessCategory):
        if not category:
            return MessageBlastImageCategoryNames.OTHER

        result = {
            BusinessCategoryEnum.BARBERS: MessageBlastImageCategoryNames.BARBERS,
            BusinessCategoryEnum.BRAIDS: MessageBlastImageCategoryNames.BRAIDS,
            BusinessCategoryEnum.SKIN_CARE: MessageBlastImageCategoryNames.BEAUTY_SALONS,
            BusinessCategoryEnum.BROWS_AND_LASHES: MessageBlastImageCategoryNames.BROWS_AND_LASHES,
            BusinessCategoryEnum.DAY_SPA: MessageBlastImageCategoryNames.DAY_SPA,
            BusinessCategoryEnum.AESTHETIC_MEDICINE: (
                MessageBlastImageCategoryNames.AESTHETIC_MEDICINE
            ),
            BusinessCategoryEnum.HAIR_REMOVAL: MessageBlastImageCategoryNames.HAIR_REMOVAL,
            BusinessCategoryEnum.HAIR_SALONS: MessageBlastImageCategoryNames.HAIR_SALONS,
            BusinessCategoryEnum.MAKE_UP: MessageBlastImageCategoryNames.MAKE_UP,
            BusinessCategoryEnum.MASSAGE: MessageBlastImageCategoryNames.MASSAGE,
            BusinessCategoryEnum.NAIL_SALONS: MessageBlastImageCategoryNames.NAIL_SALONS,
        }

        if ReturnMessageBlastImagesForTherapyCategory():
            result.update({BusinessCategoryEnum.THERAPY: MessageBlastImageCategoryNames.THERAPY})

        return result.get(category.internal_name, MessageBlastImageCategoryNames.OTHER)

    @classmethod
    def get_images_for_template(
        cls, template_internal_names: t.List[str], business: Business = None
    ) -> t.List[Image]:
        if not business:
            return [
                x.image
                for x in cls.objects.filter(
                    category__isnull=True,
                    template_internal_name__in=template_internal_names,
                ).order_by(
                    # Keep the old order and order by name ascending
                    F('template_internal_name').asc(nulls_last=True)
                )
            ]

        search_category = cls.normalize_business_category(business.primary_category)

        return [
            x.image
            for x in cls.objects.filter(
                Q(category__isnull=True, template_internal_name__in=template_internal_names)
                | Q(category=search_category, template_internal_name__isnull=True)
            ).order_by(
                # Template images goes first
                F('template_internal_name').asc(nulls_last=True)
            )
        ]


class MessageBlastTemplateHistory(HistoryModel):
    model = models.ForeignKey(
        MessageBlastTemplate,
        on_delete=models.CASCADE,
        related_name='history',
    )


class BlockedPhoneNumber(ArchiveModel):
    """
    Phone numbers which don't allow marketing communication
    """

    class Meta:
        verbose_name = _('Blocked Phone Number')
        verbose_name_plural = _('Blocked Phone Numbers')
        constraints = [
            models.UniqueConstraint(
                name='unique_cell_phone_service_not_deleted',
                fields=['cell_phone', 'service'],
                condition=Q(deleted__isnull=True),
            )
        ]

    objects = ArchiveManager()
    all_objects = models.Manager()

    cell_phone = BooksyPhoneNumberField(null=False, blank=False, db_index=True)
    service = models.CharField(
        max_length=1,
        choices=NotificationService.choices(),
        null=True,
    )

    def __str__(self):
        return f'{self.id}, {self.cell_phone}, {self.created}'


class SMSBlastMarketingConsent(ArchiveModel):
    consented = models.BooleanField(default=False)
    consented_at = models.DateTimeField(blank=True, null=True)
    confirmation_key = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    cell_phone = BooksyPhoneNumberField(null=False, blank=False)
    business = models.ForeignKey('business.Business', on_delete=models.CASCADE)
    bci = models.ForeignKey(
        'business.BusinessCustomerInfo', null=False, blank=False, on_delete=models.CASCADE
    )

    objects = ArchiveManager()
    all_objects = models.Manager()

    class Meta:
        verbose_name = _('Double opt-in consent')
        verbose_name_plural = _('Double opt-in consents')
        indexes = [
            models.Index(fields=['business', 'cell_phone']),
            models.Index(
                fields=['business', 'cell_phone', 'consented'],
                condition=Q(consented=True),
                name='sms_blast_constent_idx',
            ),
        ]


class MBSpamAnalyzerSettings(ArchiveModel):
    name = models.CharField(max_length=128, unique=True)
    ai_model = models.CharField(
        max_length=128,
        default='gemini-2.0-flash',
    )
    explanation_for_ai = models.TextField(
        default="""
        Check if a message follows spam guidelines.

        Guidelines:
        1. Allowed content:
            - Promotional campaigns from verified Booksy merchants
            - Gift cards redeemable on booksy.com
            - Credits for use within Booksy platform
            - Links to *.booksy.com domains
        
        2. Not allowed:
            - External promotional links
            - Non-Booksy gift cards/credits
            - Gibberish or meaningless content
            - Malicious content"
        )
        """
    )
    explanation_for_response = models.TextField(
        default="""
        Please response in JSON format: 'message_id': 'id', 'is_spam': true/false, score:0-100,
        explain_result_for_dummers: few sentences.  
        ##########
        Message to check for spam is: [placeholder]  
        Output schema:
        {
            "message_id": str,          # Same as input ID
            "is_spam": bool,           # True if spam detected
            "score": int,              # Spam confidence score (0-100)
            "explain_result": str,     # User-friendly explanation of decision
            "flags": List[str]         # Optional: List of specific rule violations
        }
        Return: Resp
        """
    )
    ai_params = models.JSONField(
        default={"top_k": 10, "top_p": 0.8, "temperature": 1.0, "max_output_tokens": 800},
        null=True,
        blank=True,
    )
    active = models.BooleanField(default=False)

    objects = ArchiveManager()
    all_objects = models.Manager()

    class Meta:
        verbose_name = 'Spam Analyzer Settings'
        verbose_name_plural = 'Spam Analyzer Settings'
