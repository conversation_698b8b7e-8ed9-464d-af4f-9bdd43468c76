import pytest
from babel.dates import format_date
from django.test import TestCase, override_settings
from mock import patch
from model_bakery import baker

from country_config import Country, CountryConfig
from lib import safe_json
from lib.validators import abusive_words
from webapps.business.baker_recipes import business_recipe
from webapps.business.enums import BusinessCategoryEnum
from webapps.business.models import (
    Business,
    BusinessEvent,
)
from webapps.business.models.bci import BusinessCustomerInfo, Tag
from webapps.business.models.category import BusinessCategory
from webapps.images.enums import ImageTypeEnum
from webapps.images.models import Image
from webapps.message_blast.enums import (
    MessageBlastDateType,
    MessageBlastImageCategoryNames,
    MessageBlastInternalNames,
)
from webapps.message_blast.helpers import (
    turn_on_blasts_for_business,
    update_message_blast_groups,
)
from webapps.message_blast.models import (
    BlockedPhoneNumber,
    CommonMessageBlastTemplate,
    MessageBlastImage,
    MessageBlastTemplate,
)
from webapps.message_blast.serializers import (
    ActivateMessageBlastSerializer,
    MessageBlastChannelsSerializer,
    MessageBlastTemplateSerializer,
    RecipientsSerializer,
    get_default_image_for_blasts,
)
from webapps.message_blast.tests.test_helpers import create_templates
from webapps.structure.models import Region


def prepare_data():
    business = baker.make(Business)
    bcis = baker.make(BusinessCustomerInfo, business=business, _quantity=3)

    return business, bcis


@pytest.mark.django_db
def test_bci_with_blocked_phone_number_should_resolve_recipient(business, bci_common_phone):
    data = {'ids': [bci_common_phone.id]}
    bci_common_phone.reindex(refresh_index=True)

    bci_ids = RecipientsSerializer.resolve_bcis(recipients=data, business=business)
    assert set(bci_ids) == {bci_common_phone.id}

    baker.make(BlockedPhoneNumber, cell_phone=bci_common_phone.cell_phone)

    bci_ids = RecipientsSerializer.resolve_bcis(recipients=data, business=business)
    assert set(bci_ids) == {bci_common_phone.id}


@pytest.mark.django_db
def test_validation_ids():
    business, bcis = prepare_data()

    data = {
        'ids': [
            bcis[1].id,
            bcis[2].id + 999,
        ]
    }
    serializer = RecipientsSerializer(data=data, context={'business': business})
    assert serializer.is_valid() is False
    assert serializer.errors['ids'][0].code == 'doesnt_exist_ids'

    data = {
        'ids': [
            bcis[1].id,
            bcis[2].id,
        ]
    }
    serializer = RecipientsSerializer(data=data, context={'business': business})
    assert serializer.is_valid() is True, serializer.errors


@pytest.mark.django_db
def test_validation_excluded():
    business, bcis = prepare_data()

    data = {
        'excluded': [
            bcis[1].id,
            bcis[2].id + 999,
        ]
    }
    serializer = RecipientsSerializer(data=data, context={'business': business})
    assert serializer.is_valid() is False
    assert serializer.errors['excluded'][0].code == 'doesnt_exist_excluded'

    data = {
        'ids': [
            bcis[1].id,
            bcis[2].id,
        ]
    }
    serializer = RecipientsSerializer(data=data, context={'business': business})
    assert serializer.is_valid() is True, serializer.errors


@pytest.mark.django_db
def test_validation_tags():
    business, bcis = prepare_data()

    tag = Tag.objects.create(business=business, name='#existing')
    bcis[0].tag = tag
    bcis[0].save()

    data = {'tags': ['#jakis']}
    serializer = RecipientsSerializer(data=data, context={'business': business})
    assert serializer.is_valid() is False
    assert serializer.errors['tags'][0].code == 'tag_not_supported'

    data = {'tag': ['#existing']}
    serializer = RecipientsSerializer(data=data, context={'business': business})
    assert serializer.is_valid() is True, serializer.error


@pytest.mark.django_db
def test_recipients_serializer_dump():
    business, _bcis = prepare_data()

    data = {
        'bookings_start': '2020-08-01',
        'bookings_end': '2020-08-31',
    }
    serializer = RecipientsSerializer(data=data, context={'business': business})
    assert serializer.is_valid() is True, serializer.errors
    recipients_dump = (
        '{"visit_frequency": 0, '
        '"bookings_start": {"type": "date", "data": [2020, 8, 1]}, '
        '"bookings_end": {"type": "date", "data": [2020, 8, 31]}, '
        '"with_agreements": true}'
    )
    assert safe_json.dumps(serializer.validated_data) == recipients_dump


@pytest.mark.django_db
@patch('webapps.message_blast.tasks.get_timezones_with_hour')
def test_forbidden_words(region_mock):
    region_mock.return_value = ['Europe/Warsaw']
    region = baker.make(
        Region,
        time_zone_name='Europe/Warsaw',
    )
    business = baker.make(
        Business,
        name='To jest nazwa',
        active=True,
        status=Business.Status.PAID,
        region=region,
        primary_category=baker.make(
            BusinessCategory,
            internal_name=BusinessCategoryEnum.BARBERS,
        ),
    )
    turn_on_blasts_for_business(business)

    template = MessageBlastTemplate.objects.filter(business=business).last()

    serializer = MessageBlastTemplateSerializer(
        instance=template,
        context={
            'business': business,
        },
    )

    data = serializer.data
    data['title'] = 'Title gumtree'
    data['image'] = None
    data['body'] = data['body'] + ' gumtree'
    data['body_short'] = data['body_short'] + ' gumtree'

    serializer = MessageBlastTemplateSerializer(
        data=data,
        instance=template,
        context={
            'business': business,
        },
    )
    assert serializer.is_valid() is False
    print(serializer.errors)
    assert serializer.errors['title'][0].code == 'forbidden_words'
    assert serializer.errors['body'][0].code == 'forbidden_words'
    assert serializer.errors['body_short'][0].code == 'forbidden_words'


class AbusiveWordsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.business = business_recipe.make()
        turn_on_blasts_for_business(cls.business)

        cls.template = MessageBlastTemplate.objects.filter(business=cls.business).last()

        cls.serializer = MessageBlastTemplateSerializer(
            instance=cls.template,
            context={
                'business': cls.business,
            },
        )

    def setUp(self) -> None:
        super().setUp()

        abusive_words.cache_clear()

    def asserts_checking_abusive_words(self, serializer):
        error_code = 'forbidden_words'
        self.assertFalse(serializer.is_valid())
        self.assertEqual(error_code, serializer.errors['title'][0].code)
        self.assertEqual(
            'Please remove forbidden words in the title field.', serializer.errors['title'][0]
        )

        self.assertEqual(error_code, serializer.errors['body'][0].code)
        self.assertEqual(
            'Please remove forbidden words in the message body field.', serializer.errors['body'][0]
        )

        self.assertEqual(error_code, serializer.errors['body_short'][0].code)
        self.assertEqual(
            'Please remove forbidden words in the short message body field.',
            serializer.errors['body_short'][0],
        )

    @patch('webapps.message_blast.tasks.get_timezones_with_hour')
    def test_abusive_words_us(self, region_mock):
        region_mock.return_value = ['America/Chicago']

        data = self.serializer.data
        data['title'] = 'Title anal'
        data['image'] = None
        data['body'] = data['body'] + ' ass'
        data['body_short'] = data['body_short'] + ' cock'

        serializer = MessageBlastTemplateSerializer(
            data=data,
            instance=self.template,
            context={
                'business': self.business,
            },
        )

        self.asserts_checking_abusive_words(serializer)

    @patch('builtins.open')
    @patch('webapps.message_blast.tasks.get_timezones_with_hour')
    def test_abusive_words_missing_file(self, region_mock, open_mock):
        open_mock.side_effect = FileNotFoundError
        region_mock.return_value = ['Europe/Warsaw']

        data = self.serializer.data
        data['title'] = 'Title gumtree'
        data['image'] = None
        data['body'] = data['body'] + ' gumtree'
        data['body_short'] = data['body_short'] + ' gumtree'

        serializer = MessageBlastTemplateSerializer(
            data=data,
            instance=self.template,
            context={
                'business': self.business,
            },
        )

        self.asserts_checking_abusive_words(serializer)

    @override_settings(API_COUNTRY=Country.PL)
    @patch('webapps.message_blast.tasks.get_timezones_with_hour')
    def test_abusive_words_pl(self, region_mock):
        region_mock.return_value = ['Europe/Warsaw']

        data = self.serializer.data
        data['title'] = 'Title dupa'
        data['image'] = None
        data['body'] = data['body'] + ' gówno'
        data['body_short'] = data['body_short'] + ' jebany'

        serializer = MessageBlastTemplateSerializer(
            data=data,
            instance=self.template,
            context={
                'business': self.business,
            },
        )

        self.asserts_checking_abusive_words(serializer)


@pytest.mark.parametrize(
    'data',
    (
        pytest.param(
            {'default': False, 'sms': False, 'prefer_push': False, 'push': False, 'email': False},
            id='all false',
        ),
        pytest.param({'default': False}, id='all false, omit some'),
        pytest.param({'sms': True}, id='default true, sms true, omit some'),
        pytest.param(
            {'default': False, 'sms': True, 'prefer_push': True, 'push': False, 'email': False},
            id='prefer push true when no push channel',
        ),
        pytest.param(
            {'default': True, 'sms': False, 'prefer_push': True, 'push': False, 'email': False},
            id='default true and something else true',
        ),
    ),
)
def test_channels(data):
    serializer = MessageBlastChannelsSerializer(data=data)
    assert serializer.is_valid() is False
    assert serializer.errors['non_field_errors'][0].code == 'wrong_channels'


@pytest.mark.django_db
def test_reset_to_default():
    business = baker.make(Business)
    update_message_blast_groups()
    create_templates()
    common_template = CommonMessageBlastTemplate.objects.get(
        internal_name=MessageBlastInternalNames.HOLIDAY_BOOKING
    )
    MessageBlastTemplate.objects.create(
        common_template=common_template,
        order=common_template.order,
        group=common_template.group,
        name=common_template.name,
        automated_status=common_template.automated_status,
        description=common_template.description,
        business=business,
        internal_name=common_template.internal_name,
        date_type=MessageBlastDateType.STRICT,
        date_month=9,
        date_day=1,
        title='title',
        body='body',
        body_short='short body',
    )

    template = MessageBlastTemplate.objects.get()
    assert template.date_type == MessageBlastDateType.STRICT
    assert template.date_month == 9
    assert template.date_day == 1

    data = {
        'title': 'new title',
        'body': 'new body',
        'body_short': 'new short body',
        'automated_status': common_template.automated_status,
        'date_day': None,
        'date_month': None,
    }

    serializer = MessageBlastTemplateSerializer(
        instance=template,
        data=data,
        context={
            'business': business,
        },
    )
    assert serializer.is_valid(), serializer.errors
    template = serializer.save()

    assert template.title == 'new title'
    assert template.date_type == MessageBlastDateType.FIRST_MONDAY.value
    assert template.date_month == 11
    assert template.date_monday == 2


@pytest.mark.django_db
@patch(
    'webapps.message_blast.serializers.get_language',
    return_value=CountryConfig(Country.MX).language_code,
)
def test_date_in_message_blast_template_serializer_for_es_mx(_get_language_mock):
    business = baker.make(Business)
    template = baker.make(
        CommonMessageBlastTemplate,
        date_type=MessageBlastDateType.STRICT,
        date_month=9,
        date_day=1,
        body='body',
        body_short='short body',
    )
    serializer = MessageBlastTemplateSerializer(context={'business': business})
    result = serializer.to_representation(template)
    assert result['date_text'] == format_date(template.get_date(next_possible=True), locale='es_MX')


@pytest.mark.django_db
def test_validation_activation_already_activated():
    business = baker.make(
        Business,
    )
    update_message_blast_groups()
    create_templates(1)
    common_template = CommonMessageBlastTemplate.objects.last()
    serializer = ActivateMessageBlastSerializer(
        data={
            'common_message_blast_template_ids': [common_template.id],
        },
        context={
            'business': business,
        },
    )

    baker.make(
        MessageBlastTemplate,
        common_template=common_template,
        business=business,
    )
    assert serializer.is_valid() is False
    assert serializer.errors['non_field_errors'][0].code == 'already_activated'


@pytest.mark.django_db
def test_validation_activation_already_seen():
    business = baker.make(
        Business,
    )
    update_message_blast_groups()
    create_templates(1)
    common_template = CommonMessageBlastTemplate.objects.last()
    serializer = ActivateMessageBlastSerializer(
        data={
            'common_message_blast_template_ids': [common_template.id],
        },
        context={
            'business': business,
        },
    )

    BusinessEvent.objects.update_or_create(
        business=business,
        defaults={
            'ask_for_message_blast_activation': False,
        },
    )
    assert serializer.is_valid() is False
    assert serializer.errors['non_field_errors'][0].code == 'already_seen'


@pytest.mark.django_db
def test_validation_activation():
    business = baker.make(
        Business,
    )
    update_message_blast_groups()
    create_templates(1)
    common_template = CommonMessageBlastTemplate.objects.last()
    serializer = ActivateMessageBlastSerializer(
        data={
            'common_message_blast_template_ids': [common_template.id],
        },
        context={
            'business': business,
        },
    )

    assert serializer.is_valid() is True


@pytest.mark.django_db
def test_get_default_image_for_blast():
    business = baker.make(
        Business,
        name='To jest nazwa',
        primary_category=baker.make(
            BusinessCategory,
            internal_name=BusinessCategoryEnum.BARBERS,
        ),
    )
    update_message_blast_groups()
    create_templates(amount=5)
    turn_on_blasts_for_business(business)

    assert (
        get_default_image_for_blasts(
            template_internal_name=MessageBlastInternalNames.WELCOME_NEW_CLIENT,
            business=business,
        )
        is None
    )

    img1 = baker.make(
        Image,
        business=business,
        category=ImageTypeEnum.MESSAGE_BLAST_BUSINESS_CATEGORIES,
    )
    baker.make(
        MessageBlastImage,
        image=img1,
        category=MessageBlastImageCategoryNames.BARBERS,
    )

    img2 = baker.make(
        Image,
        business=business,
        category=ImageTypeEnum.MESSAGE_BLAST_TEMPLATE,
    )
    baker.make(
        MessageBlastImage,
        image=img2,
        template_internal_name=MessageBlastInternalNames.WELCOME_NEW_CLIENT,
    )

    assert (
        get_default_image_for_blasts(
            template_internal_name=MessageBlastInternalNames.WELCOME_NEW_CLIENT,
            business=business,
        )
        == img2
    )

    # Own template should not have default image.
    assert (
        get_default_image_for_blasts(
            template_internal_name=MessageBlastInternalNames.OWN_MESSAGE, business=business
        )
        is None
    )
