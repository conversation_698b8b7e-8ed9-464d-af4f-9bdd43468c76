from webapps.consents.fields import CheckboxField, TextField, InputField

COVID_19_CONSENT_FORM = {
    'en': {
        'title': 'Covid-19 Disclaimer Form',
        'fields': [
            TextField(
                "As Coronavirus has had a big impact on our lives and because "
                "we want to put your safety first, we’d like to ask you some "
                "questions before your visit. Please provide your answers "
                "below:"
            ),
            CheckboxField(
                "I confirm that I am not infected with COVID-19 and I am not "
                "presenting any of the following symptoms: fever, shortness of "
                "breath, loss of sense of taste or smell, dry cough, runny "
                "nose or sore throat."
            ),
            CheckboxField(
                "I confirm that I haven't been around anyone exhibiting these "
                "symptoms within the past 14 days"
            ),
            CheckboxField(
                "I confirm that I am not living with anyone who is diagnosed "
                "with COVID-19 or quarantined."
            ),
        ],
    },
    'pl': {
        'title': 'Ankieta COVID-19',
        'fields': [
            TextField(
                "W trosce o nasze wspólne bezpieczeństwo prosimy o "
                "potwierdzenie poniższych stwierdzeń dotyczących Twojego "
                "zdrowia i informację na temat ewentualnych symptomów choroby "
                "COVID-19. Wszystkie pola w ankiecie są obowiązkowe."
            ),
            CheckboxField(
                "Oświadczam, że w moim najbliższym otoczeniu nie ma osoby " "poddanej kwarantannie."
            ),
            CheckboxField(
                "Oświadczam, że w moim najbliższym otoczeniu nie ma "
                "potwierdzonego przypadku COVID-19."
            ),
            CheckboxField(
                "Oświadczam, że nie zdiagnozowano u mnie zakażenia " "wirusem SARS-CoV-2."
            ),
            InputField(
                "Temperatura ciała (tutaj wpisz wynik pomiaru, a jeśli nie "
                "masz możliwości wykonania pomiaru wpisz “NIE ZNAM”):"
            ),
            InputField(
                "Jeśli obserwujesz u siebie któryś z objawów: gorączka "
                "(powyżej 38°C), kaszel, duszności, problemy z oddychaniem, "
                "ból mięśni, zmęczenie, katar, ból gardła, wpisz które z nich. "
                "Jeśli nie obserwujesz u siebie wymienionych objawów, "
                "wpisz “BRAK”."
            ),
            CheckboxField(
                "Oświadczam, że jeśli któreś z powyższych informacji ulegnie "
                "zmianie do dnia wizyty, poinformuję o tym niezwłocznie "
                "usługodawcę."
            ),
            CheckboxField("Oświadczam, że podane wyżej informacje są zgodne z prawdą."),
        ],
    },
    'es': {
        'title': 'Formulario de consentimiento COVID-19',
        'fields': [
            TextField(
                "Debido al gran impacto que ha tenido el Coronavirus en "
                "nuestras vidas y porque su seguridad es lo primero, nos "
                "gustaría hacerle algunas preguntas antes de su visita. Por "
                "favor, añada sus respuestas a continuación:"
            ),
            CheckboxField(
                "Confirmo que no estoy diagnosticado/a de COVID-19 y que no "
                "presento ninguno de los siguientes síntomas: fiebre, "
                "dificultad respiratoria, pérdida del sentido del gusto u "
                "olfato, tos seca, secreción nasal o dolor de garganta."
            ),
            CheckboxField(
                "Confirmo que no he estado cerca de alguien que presentara los "
                "anteriores síntomas en los últimos 14 días."
            ),
            CheckboxField(
                "Confirmo que no estoy cohabitando con alguien diagnosticado "
                "de COVID-19 o que se encuentre en cuarentena."
            ),
        ],
    },
    'pt': {
        'title': 'Aviso Covid-19',
        'fields': [
            TextField(
                "Devido ao grande impacto do Coronavírus em nossas vidas e por "
                "querermos garantir que a sua segurança esteja em primeiro "
                "lugar, gostaríamos de fazer algumas perguntas antes da sua "
                "visita. Por favor, insira suas respostas abaixo:"
            ),
            CheckboxField(
                "Confirmo que não fui diagnosticado(a) com COVID-19 e que não "
                "estou apresentando nenhum dos seguintes sintomas: febre, "
                "falta de ar, perda do olfato e/ou paladar, tosse seca, "
                "coriza ou dor de garganta. "
            ),
            CheckboxField(
                "Confirmo que não estive com alguém que apresentou os sintomas "
                "citados acima nos últimos 14 dias."
            ),
            CheckboxField(
                "Confirmo que não estou morando com alguém diagnosticado com "
                "COVID-19 ou que esteja em quarentena por suspeita "
                "de estar contaminado."
            ),
        ],
    },
}
