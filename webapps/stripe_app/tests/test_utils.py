from dataclasses import dataclass
from datetime import datetime
from stripe.error import Strip<PERSON><PERSON>rro<PERSON>
from dataclasses_json import DataClassJsonMixin
from dateutil.parser import parse
from django.test import TestCase

from webapps.billing.enums import (
    CARD_TYPE_MAP,
    PAYMENT_TYPE_MAP,
    CreditCardType,
    PaymentMethodType,
    get_card_type,
    get_payment_type,
)
from webapps.stripe_app.errors import parse_stripe_error
from webapps.stripe_app.models.payment_method import CardObject
from webapps.stripe_app.models.utils import model_to_dataclass
from webapps.stripe_app.utils import clean_cc_billing_details


@dataclass(frozen=True)
class DummyOrderObject(DataClassJsonMixin):
    id: int  # pylint: disable=invalid-name
    title: str


@model_to_dataclass(DummyOrderObject)
class DummyOrder:
    def __init__(self, data):
        self.id = data['id']  # pylint: disable=invalid-name
        self.title = data['title']
        self.date = data['date']


@dataclass(frozen=True)
class DummyCustomerObject(DataClassJsonMixin):
    id: int  # pylint: disable=invalid-name
    stripe_id: str
    booksy_id: str
    name: str
    order: DummyOrder


@model_to_dataclass(DummyCustomerObject)
class DummyCustomer:
    def __init__(self, data):
        self.id = data['id']  # pylint: disable=invalid-name
        self.name = data['name']
        self.booksy_id = data['booksy_id']
        self.stripe_id = data['stripe_id']
        self.order = data['order']
        self.date = data['date']


class TestUtils(TestCase):
    def test_model_to_dataclass_decorator(self):
        data = dict(
            name='nameval',
            stripe_id='stripe_idval',
            booksy_id='booksy_idval',
            id='idval',
            order=None,
            date='2022-01-20',
        )

        test_customer = DummyCustomer(data)
        self.assertTrue(hasattr(test_customer, 'to_dataclass'))

        dataclass_object = test_customer.to_dataclass()

        self.assertFalse(hasattr(dataclass_object, 'date'))
        del data['date']

        for key, value in data.items():
            self.assertEqual(getattr(dataclass_object, key), value)

    def test_model_to_dataclass_decorator_nested(self):
        data = dict(
            name='nameval',
            stripe_id='stripe_idval',
            booksy_id='booksy_idval',
            id='idval',
            order=DummyOrder(dict(id='orderid', title='order title', date='2022-01-15')),
            date='2022-01-13',
        )

        test_customer = DummyCustomer(data)
        self.assertTrue(hasattr(test_customer.order, 'to_dataclass'))

        dataclass_object = test_customer.order.to_dataclass()

        self.assertEqual(dataclass_object.id, data['order'].id)
        self.assertEqual(dataclass_object.title, data['order'].title)
        self.assertFalse(hasattr(dataclass_object, data['date']))

    def test_model_to_dataclass_decorator_data_overridden(self):
        data = dict(
            name='nameval',
            stripe_id='stripe_idval',
            booksy_id='booksy_idval',
            id='idval',
            order=None,
            date='2022-01-20',
        )

        test_customer = DummyCustomer(data)

        dataclass_object = test_customer.to_dataclass(booksy_id='New value!')
        self.assertEqual(dataclass_object.booksy_id, 'New value!')

    def test_parse_stripe_error(self):
        stripe_error_data = {
            "error": {
                "type": "card_error",
                "code": "card_declined",
                "decline_code": "authentication_required",
                "message": "Authentications required",
                "param": "",
                "payment_intent": {},
                "charge": "ch-123",
                "doc_url": "",
                "payment_method": {},
                "setup_intent": {},
                "source": {},
            }
        }

        with self.subTest('data parsing success'):
            stripe_error = StripeError(json_body=stripe_error_data)
            error_object = parse_stripe_error(stripe_error)
            for key, value in stripe_error_data['error'].items():
                self.assertEqual(getattr(error_object, key), value)

        with self.subTest('data parsing success with missing keys in error data'):
            for key, _ in stripe_error_data['error'].items():
                stripe_error = StripeError(json_body=stripe_error_data)
                stripe_error.error.pop(key)
                error_object = parse_stripe_error(stripe_error)
                self.assertFalse(getattr(error_object, key))

        with self.subTest('data parsing success without error data'):
            stripe_error = StripeError(code='error code')
            error_object = parse_stripe_error(stripe_error)
            self.assertEqual(error_object.code, 'error code')
            self.assertEqual(error_object.message, repr(stripe_error))

    def test_get_card_type(self):
        with self.subTest('known card type'):
            for key, _ in CARD_TYPE_MAP.items():
                self.assertEqual(type(get_card_type(key)), CreditCardType)

        with self.subTest('unknown card type'):
            self.assertTrue(get_card_type('nonexistent card type'), CreditCardType.UNKNOWN)

    def test_get_payment_type(self):
        with self.subTest('known payment type'):
            for key, _ in PAYMENT_TYPE_MAP.items():
                self.assertEqual(type(get_payment_type(key)), PaymentMethodType)

        with self.subTest('not exsisting payment type'):
            self.assertFalse(get_payment_type('nonexistent payment type'))

    def test_card_object_exp_iso_date(self):
        expire_date = datetime(2032, 8, 31)
        data = dict(
            brand="Visa",
            country="PL",
            exp_month=expire_date.month,
            exp_year=expire_date.year,
            last4="1234",
        )

        with self.subTest('missing expire month'):
            data_without_month = data.copy()
            data_without_month.pop('exp_month')
            card = CardObject(**data_without_month)
            self.assertFalse(card.exp_iso_date)

        with self.subTest('missing expire year'):
            data_without_year = data.copy()
            data_without_year.pop('exp_year')
            card = CardObject(**data_without_year)
            self.assertFalse(card.exp_iso_date)

        with self.subTest('iso date includes last day of month'):
            card = CardObject(**data)
            self.assertEqual(parse(card.exp_iso_date), expire_date)

    def test_strip_values_from_business_details(self):
        test_billing_details = {
            'address': {
                'city': "<br/>test0",
                'country': "<br/>test1",
                'line1': "<br/>test2",
                'line2': "<br/>test3",
                'postal_code': "<br/>test4",
                'state': "<br/>test5",
            },
            'email': None,
            'name': "<br/>test8",
            'phone': "<br/>test9",
        }
        striped_data = clean_cc_billing_details(test_billing_details)
        for idx, item in enumerate(striped_data['address'].values()):
            self.assertEqual(item, f'test{idx}')
        self.assertIsNone(striped_data['email'])
        self.assertEqual(striped_data['phone'], 'test9')

    def test_strip_values_from_business_details_empty(self):
        test_billing_details = {}
        striped_data = clean_cc_billing_details(test_billing_details)
        self.assertDictEqual(striped_data, {})

    def test_strip_values_from_business_details_too_long_postal_code(self):
        test_billing_details = {
            'address': {
                'postal_code': 'a' * 21,
            }
        }
        cleared_data = clean_cc_billing_details(test_billing_details)
        self.assertEqual(len(cleared_data['address']['postal_code']), 20)

    def test_strip_values_from_business_details_too_long_state(self):
        test_billing_details = {
            'address': {
                'state': 'a' * 101,
            }
        }
        cleared_data = clean_cc_billing_details(test_billing_details)
        self.assertEqual(len(cleared_data['address']['state']), 100)

    def test_strip_values_from_business_details_too_long_name(self):
        test_billing_details = {
            'name': 'a' * 256,
        }
        cleared_data = clean_cc_billing_details(test_billing_details)
        self.assertEqual(len(cleared_data['name']), 255)
