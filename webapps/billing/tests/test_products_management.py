from datetime import datetime, timedelta
from decimal import Decimal
from unittest.mock import patch

from dateutil.relativedelta import relativedelta
from django.test.utils import override_settings

from model_bakery import baker
from pytz import UTC

from lib.tools import tznow
from webapps.admin_extra.tests import DjangoTestCase
from webapps.billing.enums import PaymentPeriod, ProductType
from webapps.billing.models import (
    BillingSubscribedProduct,
    BillingSubscribedProductHistory,
    BillingSubscription,
)
from webapps.billing.products_management import (
    BusinessException,
    ManageProductData,
    ManageProductPrice,
)
from webapps.business.models import Business

from webapps.user.models import User


class TestManageSetUp(DjangoTestCase):
    def setUp(self):
        self.operator = baker.make(User)
        self.business = baker.make(Business)
        self.subscription = baker.make(
            BillingSubscription,
            payment_period=PaymentPeriod.ONE_MONTH.value[0],
            business=self.business,
            date_start=datetime(2021, 2, 1, tzinfo=UTC),
            next_billing_date=datetime(2021, 3, 1, tzinfo=UTC),
        )
        self.product_attributes = (
            'product_type',
            'name',
            'currency',
            'max_qty',
            'free_staff_qty',
            'duration',
            'auto_renew',
            'payment_period',
            'staff_add_on_id',
            'sms_add_on_id',
            'discount_type',
            'discount_frac',
            'discount_duration',
            'subscription_id',
            'business_id',
        )
        self.history_metadata = {'endpoint': 'mass_sms_limit_changer'}
        self.new_prices = dict(
            total_price=Decimal('12.31'),
            discounted_price=Decimal('1.1'),
            discount_granted=Decimal('2.3'),
            final_price=Decimal('0'),
        )

    def _create_subscribed_product(
        self,
        date_start: datetime,
        date_end: datetime = None,
        product_type: str = ProductType.SAAS,
        **kwargs,
    ) -> BillingSubscribedProduct:
        return baker.make(
            BillingSubscribedProduct,
            subscription=self.subscription,
            date_start=date_start,
            date_end=date_end,
            product_type=product_type,
            sms_amount=15,
            unit_price=Decimal('0.2'),
            quantity=4,
            **kwargs,
        )


class TestManageProductData(TestManageSetUp):
    @override_settings(SAVE_HISTORY=True)
    def test_update_sms_limit_one_product_ok(self):
        saas_subscribed_product = self._create_subscribed_product(
            date_start=self.subscription.date_start,
        )

        ManageProductData.change_sms_limits(
            subscription=self.subscription,
            sms_amount=4000,
            operator_id=self.operator.id,
            history_metadata=self.history_metadata,
        )
        new_saas_product = BillingSubscribedProduct.objects.get(
            subscription_id=self.subscription.id,
            product_type=ProductType.SAAS,
            date_end__isnull=True,
            date_start=self.subscription.next_billing_date,
        )
        saas_subscribed_product.refresh_from_db()
        self.assertEqual(saas_subscribed_product.date_end, self.subscription.next_billing_date)
        self.assertEqual(new_saas_product.sms_amount, 4000)
        self.assertFalse(saas_subscribed_product.id == new_saas_product.id)

        for attr in self.product_attributes:
            self.assertEqual(
                getattr(saas_subscribed_product, attr), getattr(new_saas_product, attr)
            )

        self.assertEqual(BillingSubscribedProduct.objects.count(), 2)

        self.assertEqual(
            BillingSubscribedProductHistory.objects.filter(
                model__in=[saas_subscribed_product.id, new_saas_product.id],
                metadata__icontains='mass_sms_limit_changer',
                operator=self.operator,
            ).count(),
            2,
        )

    def test_update_sms_limit_with_end_date(self):
        saas_subscribed_product = self._create_subscribed_product(
            date_start=self.subscription.date_start,
            date_end=self.subscription.date_start + relativedelta(months=2),
        )

        ManageProductData.change_sms_limits(
            subscription=self.subscription,
            sms_amount=200,
        )

        new_saas_product = BillingSubscribedProduct.objects.get(
            subscription_id=self.subscription.id,
            product_type=ProductType.SAAS,
            date_start=self.subscription.next_billing_date,
            date_end=saas_subscribed_product.date_end,
        )

        saas_subscribed_product.refresh_from_db()
        self.assertEqual(saas_subscribed_product.date_end, self.subscription.next_billing_date)
        self.assertEqual(new_saas_product.sms_amount, 200)
        self.assertFalse(saas_subscribed_product.id == new_saas_product.id)

        for attr in self.product_attributes:
            self.assertEqual(
                getattr(saas_subscribed_product, attr), getattr(new_saas_product, attr)
            )

        self.assertEqual(BillingSubscribedProduct.objects.count(), 2)

    @override_settings(SAVE_HISTORY=True)
    def test_update_sms_limit_product_start_date_same_as_subscription_current_cycle_date_end(self):
        saas_subscribed_product = self._create_subscribed_product(
            date_start=self.subscription.next_billing_date,
            date_end=self.subscription.date_start + relativedelta(months=2),
        )

        ManageProductData.change_sms_limits(
            subscription=self.subscription,
            sms_amount=200,
            operator_id=self.operator.id,
            history_metadata=self.history_metadata,
        )

        saas_subscribed_product.refresh_from_db()

        self.assertEqual(BillingSubscribedProduct.objects.count(), 1)
        self.assertEqual(saas_subscribed_product.sms_amount, 200)
        self.assertEqual(
            BillingSubscribedProductHistory.objects.filter(
                model=saas_subscribed_product,
                metadata__icontains='mass_sms_limit_changer',
                operator=self.operator,
            ).count(),
            1,
        )

    @override_settings(SAVE_HISTORY=True)
    def test_update_sms_limit_more_than_one_product(self):
        saas_subscribed_product = self._create_subscribed_product(
            date_start=self.subscription.date_start,
            date_end=self.subscription.date_start + relativedelta(months=2),
        )
        saas_subscribed_product2 = self._create_subscribed_product(
            date_start=saas_subscribed_product.date_end,
            date_end=saas_subscribed_product.date_end + relativedelta(months=3),
        )
        saas_subscribed_product3 = self._create_subscribed_product(
            date_start=saas_subscribed_product2.date_end,
        )

        ManageProductData.change_sms_limits(
            subscription=self.subscription,
            sms_amount=300,
            operator_id=self.operator.id,
            history_metadata=self.history_metadata,
        )

        new_saas_product = BillingSubscribedProduct.objects.get(
            subscription_id=self.subscription.id,
            product_type=ProductType.SAAS,
            date_start=self.subscription.next_billing_date,
            date_end=saas_subscribed_product.date_end,
        )

        saas_subscribed_product.refresh_from_db()
        saas_subscribed_product2.refresh_from_db()
        saas_subscribed_product3.refresh_from_db()
        self.assertEqual(saas_subscribed_product.date_end, self.subscription.next_billing_date)
        self.assertEqual(new_saas_product.sms_amount, 300)
        self.assertFalse(saas_subscribed_product.id == new_saas_product.id)

        for attr in self.product_attributes:
            self.assertEqual(
                getattr(saas_subscribed_product, attr), getattr(new_saas_product, attr)
            )

        self.assertEqual(saas_subscribed_product2.sms_amount, 300)
        self.assertEqual(saas_subscribed_product3.sms_amount, 300)

        self.assertEqual(BillingSubscribedProduct.objects.count(), 4)

        self.assertEqual(
            BillingSubscribedProductHistory.objects.filter(
                model__in=[
                    saas_subscribed_product.id,
                    new_saas_product.id,
                    saas_subscribed_product2.id,
                    saas_subscribed_product3.id,
                ],
                metadata__icontains='mass_sms_limit_changer',
                operator=self.operator,
            ).count(),
            4,
        )

    def test_update_sms_limit_nothing_to_change(self):
        saas_subscribed_product = self._create_subscribed_product(
            date_start=self.subscription.date_start,
            date_end=self.subscription.next_billing_date,
        )
        ManageProductData.change_sms_limits(subscription=self.subscription, sms_amount=300)
        saas_subscribed_product.refresh_from_db()
        self.assertEqual(saas_subscribed_product.sms_amount, 15)
        self.assertEqual(BillingSubscribedProduct.objects.count(), 1)

    def test_update_the_same_value(self):
        self._create_subscribed_product(
            date_start=self.subscription.date_start,
        )
        with self.assertRaisesRegex(BusinessException, '.*Nothing to change, product already*'):
            ManageProductData.change_sms_limits(
                subscription=self.subscription,
                sms_amount=15,
            )

    @override_settings(SAVE_HISTORY=True)
    def test_update_future_subscription_ok(self):
        self.subscription.date_start = tznow() + timedelta(days=10)
        sms_subscribed_product = self._create_subscribed_product(
            date_start=self.subscription.date_start,
            date_end=self.subscription.date_start + relativedelta(months=2),
        )
        sms_subscribed_product2 = self._create_subscribed_product(
            date_start=self.subscription.date_start,
        )

        ManageProductData.change_sms_limits(
            subscription=self.subscription,
            sms_amount=34,
            operator_id=self.operator.id,
            history_metadata=self.history_metadata,
        )
        sms_subscribed_product.refresh_from_db()
        sms_subscribed_product2.refresh_from_db()

        self.assertEqual(
            sms_subscribed_product.date_end, self.subscription.date_start + relativedelta(months=2)
        )
        self.assertIsNone(sms_subscribed_product2.date_end)
        self.assertEqual(sms_subscribed_product.sms_amount, 34)
        self.assertEqual(sms_subscribed_product2.sms_amount, 34)
        self.assertEqual(BillingSubscribedProduct.objects.count(), 2)
        self.assertEqual(
            BillingSubscribedProductHistory.objects.filter(
                model__in=[sms_subscribed_product.id, sms_subscribed_product2.id],
                metadata__icontains='mass_sms_limit_changer',
                operator=self.operator,
            ).count(),
            2,
        )


class TestManageProductPrice(TestManageSetUp):
    def test_update_sms_unit_price_date_end(self):
        self._create_subscribed_product(
            date_start=self.subscription.date_start,
            date_end=self.subscription.date_start + relativedelta(months=2),
            product_type=ProductType.POSTPAID_SMS,
        )
        with self.assertRaisesRegex(
            BusinessException, '.*Postpaid product should not have date end.*'
        ):
            ManageProductPrice.change_postpaid_sms(
                subscription=self.subscription,
                unit_price=Decimal('0.01'),
            )

    @override_settings(SAVE_HISTORY=True)
    @patch.object(BillingSubscribedProduct, 'get_product_prices')
    def test_update_sms_unit_price_ok(self, mock_get_product_prices):
        mock_get_product_prices.return_value = self.new_prices
        sms_subscribed_product = self._create_subscribed_product(
            date_start=self.subscription.date_start,
            product_type=ProductType.POSTPAID_SMS,
        )

        ManageProductPrice.change_postpaid_sms(
            subscription=self.subscription,
            unit_price=Decimal('0.01'),
            operator_id=self.operator.id,
            history_metadata=self.history_metadata,
        )

        new_sms_product = BillingSubscribedProduct.objects.get(
            subscription_id=self.subscription.id,
            product_type=ProductType.POSTPAID_SMS,
            date_start=self.subscription.next_billing_date,
        )

        sms_subscribed_product.refresh_from_db()
        self.assertEqual(sms_subscribed_product.date_end, self.subscription.next_billing_date)
        self.assertEqual(new_sms_product.unit_price, Decimal('0.01'))
        self.assertEqual(new_sms_product.total_price, self.new_prices['total_price'])
        self.assertEqual(new_sms_product.discounted_price, self.new_prices['discounted_price'])
        self.assertEqual(new_sms_product.discount_granted, self.new_prices['discount_granted'])
        self.assertEqual(new_sms_product.final_price, self.new_prices['final_price'])

        self.assertFalse(new_sms_product.id == sms_subscribed_product.id)

        for attr in self.product_attributes:
            self.assertEqual(getattr(sms_subscribed_product, attr), getattr(new_sms_product, attr))

        self.assertEqual(BillingSubscribedProduct.objects.count(), 2)

        self.assertEqual(
            BillingSubscribedProductHistory.objects.filter(
                model__in=[sms_subscribed_product.id, new_sms_product.id],
                metadata__icontains='mass_sms_limit_changer',
                operator=self.operator,
            ).count(),
            2,
        )

    @patch.object(BillingSubscribedProduct, 'get_product_prices')
    def test_update_sms_unit_price_more_than_one_time(self, mock_get_product_prices):
        mock_get_product_prices.return_value = self.new_prices
        sms_subscribed_product = self._create_subscribed_product(
            date_start=self.subscription.date_start,
            product_type=ProductType.POSTPAID_SMS,
        )

        ManageProductPrice.change_postpaid_sms(
            subscription=self.subscription,
            unit_price=Decimal('0.01'),
        )

        new_sms_product = BillingSubscribedProduct.objects.get(
            subscription_id=self.subscription.id,
            product_type=ProductType.POSTPAID_SMS,
            date_start=self.subscription.next_billing_date,
        )

        sms_subscribed_product.refresh_from_db()

        self.assertEqual(sms_subscribed_product.date_end, self.subscription.next_billing_date)
        self.assertEqual(new_sms_product.unit_price, Decimal('0.01'))
        self.assertEqual(new_sms_product.total_price, self.new_prices['total_price'])
        self.assertEqual(new_sms_product.discounted_price, self.new_prices['discounted_price'])
        self.assertEqual(new_sms_product.discount_granted, self.new_prices['discount_granted'])
        self.assertEqual(new_sms_product.final_price, self.new_prices['final_price'])

        new_prices = dict(
            total_price=Decimal('1.31'),
            discounted_price=Decimal('4.1'),
            discount_granted=Decimal('2.3'),
            final_price=Decimal('11'),
        )
        mock_get_product_prices.return_value = new_prices

        ManageProductPrice.change_postpaid_sms(
            subscription=self.subscription,
            unit_price=Decimal('0.06'),
        )

        sms_subscribed_product.refresh_from_db()
        new_sms_product.refresh_from_db()
        self.assertEqual(sms_subscribed_product.date_end, self.subscription.next_billing_date)
        self.assertEqual(new_sms_product.unit_price, Decimal('0.06'))
        self.assertEqual(new_sms_product.total_price, new_prices['total_price'])
        self.assertEqual(new_sms_product.discounted_price, new_prices['discounted_price'])
        self.assertEqual(new_sms_product.discount_granted, new_prices['discount_granted'])
        self.assertEqual(new_sms_product.final_price, new_prices['final_price'])

        self.assertFalse(new_sms_product.id == sms_subscribed_product.id)

        self.assertEqual(BillingSubscribedProduct.objects.count(), 2)

    def test_update_sms_unit_price_nothing_to_change(self):
        sms_subscribed_product = self._create_subscribed_product(
            date_start=self.subscription.date_start,
            date_end=self.subscription.next_billing_date,
            product_type=ProductType.POSTPAID_SMS,
        )
        with self.assertRaisesRegex(BusinessException, '.*or none postpaid products.*'):
            ManageProductPrice.change_postpaid_sms(
                subscription=self.subscription, unit_price=Decimal('0.01')
            )
        self.assertEqual(sms_subscribed_product.unit_price, Decimal('0.2'))
        self.assertEqual(BillingSubscribedProduct.objects.count(), 1)

    def test_update_sms_unit_price_more_than_one(self):
        self._create_subscribed_product(
            date_start=self.subscription.date_start,
            product_type=ProductType.POSTPAID_SMS,
        )
        self._create_subscribed_product(
            date_start=self.subscription.date_start,
            product_type=ProductType.POSTPAID_SMS,
        )
        with self.assertRaisesRegex(BusinessException, 'Found more than one or none postpaid'):
            ManageProductPrice.change_postpaid_sms(
                subscription=self.subscription,
                unit_price=Decimal('0.01'),
            )

    def test_update_the_same_value(self):
        self._create_subscribed_product(
            date_start=self.subscription.date_start,
            product_type=ProductType.POSTPAID_SMS,
        )
        with self.assertRaisesRegex(
            BusinessException, '.*Nothing to change sms product already has the same price.*'
        ):
            ManageProductPrice.change_postpaid_sms(
                subscription=self.subscription,
                unit_price=Decimal('0.2'),
            )

    @override_settings(SAVE_HISTORY=True)
    @patch.object(BillingSubscribedProduct, 'get_product_prices')
    def test_update_future_subscription_ok(self, mock_get_product_prices):
        mock_get_product_prices.return_value = self.new_prices
        self.subscription.date_start = tznow() + timedelta(days=10)
        sms_subscribed_product = self._create_subscribed_product(
            date_start=self.subscription.date_start,
            product_type=ProductType.POSTPAID_SMS,
        )
        ManageProductPrice.change_postpaid_sms(
            subscription=self.subscription,
            unit_price=Decimal('0.45'),
            operator_id=self.operator.id,
            history_metadata=self.history_metadata,
        )
        sms_subscribed_product.refresh_from_db()
        self.assertIsNone(sms_subscribed_product.date_end)
        self.assertEqual(sms_subscribed_product.unit_price, Decimal('0.45'))
        self.assertEqual(sms_subscribed_product.total_price, self.new_prices['total_price'])
        self.assertEqual(
            sms_subscribed_product.discounted_price, self.new_prices['discounted_price']
        )
        self.assertEqual(
            sms_subscribed_product.discount_granted, self.new_prices['discount_granted']
        )
        self.assertEqual(sms_subscribed_product.final_price, self.new_prices['final_price'])

        self.assertEqual(BillingSubscribedProduct.objects.count(), 1)
        self.assertEqual(
            BillingSubscribedProductHistory.objects.filter(
                model=sms_subscribed_product,
                metadata__icontains='mass_sms_limit_changer',
                operator=self.operator,
            ).count(),
            1,
        )
