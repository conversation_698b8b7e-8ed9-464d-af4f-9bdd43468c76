# pylint: disable=unused-argument
import typing as t
from datetime import datetime, timedelta
from decimal import Decimal

import mock
import pytest
from dateutil.relativedelta import relativedelta
from django.test import TestCase, override_settings
from mock import MagicMock, call, patch
from model_bakery import baker
from parameterized import parameterized
from pytz import UTC

from lib.feature_flag.feature.billing import BillingDisableBTPaymentProcessor
from lib.locks import BillingSubscriptionLock
from lib.tests.utils import override_eppo_feature_flag
from lib.tools import tznow
from webapps.billing.enums import (
    BillingErrorEventType,
    DiscountType,
    PaymentProcessorType,
    ProductType,
    SubscriptionStatus,
    TransactionSource,
    TransactionStatus,
)
from webapps.billing.error_handling.errors import BillingErrorGroupEnum
from webapps.billing.models import (
    BillingBusinessOffer,
    BillingBusinessSettings,
    BillingCycle,
    BillingCycleHistory,
    BillingCycleProductCharge,
    BillingCycleProductChargeHistory,
    BillingPaymentMethod,
    BillingProduct,
    BillingProductOffer,
    BillingProductOfferItem,
    BillingSubscribedProduct,
    BillingSubscribedProductHistory,
    BillingSubscription,
    BillingSubscriptionHistory,
    BillingTransaction,
    PaymentProcessorError,
)
from webapps.billing.models.offline_migration import (
    BillingOfflineMigration,
    BillingOfflineMigrationRequest,
)
from webapps.billing.notifications import MigratedFromOfflineSubscriptionNotification
from webapps.billing.payment_processor import (
    PaymentProcessor,
    StripePaymentProcessor,
    PaymentProcessorBridge,
)
from webapps.billing.subscription_creator import SubscriptionCreator
from webapps.billing.tasks import purchase_subscription_task
from webapps.business.enums import BoostPaymentSource
from webapps.business.models import Business
from webapps.business.models.models import BusinessPolicyAgreement
from webapps.navision.models import TaxRate
from webapps.purchase.models import Subscription
from webapps.purchase.tests.utils_test import create_subscription_product
from webapps.stripe_app.errors import ErrorObject
from webapps.structure.baker_recipes import bake_region_graphs
from webapps.structure.models import Region


def create_test_braintree_transaction(
    business_id,
    amount,
    currency,
    description,
    metadata=None,
):
    common_data = dict(
        id='create_subscription123',
        amount=amount,
        currency_iso_code=currency,
        processor_response_type=None,
        processor_response_text='Some processor response text',
        credit_card_details=MagicMock(token='toooken'),
    )
    if currency == 'USD':
        is_success = True
        transaction = MagicMock(
            processor_response_code=1000,
            status='authorized',
            **common_data,
        )
    elif currency == 'PLN':
        is_success = False
        transaction = MagicMock(
            processor_response_code='2001',
            status='failed',
            **common_data,
        )
    else:
        is_success = False
        transaction = None
    return MagicMock(
        is_success=is_success,
        transaction=transaction,
        message='Braintree message',
    )


def create_test_stripe_transaction(business_id, amount, currency, description, metadata=None):
    common_data = dict(
        stripe_id='create_subscription123',
        decimal_amount=amount,
        currency=currency,
        payment_method=MagicMock(stripe_id='toooken'),
        metadata=metadata,
    )
    if currency == 'USD':
        is_success = True
        transaction = MagicMock(
            status='succeeded',
            last_payment_error=None,
            next_action=None,
            **common_data,
        )
        error = None
    elif currency == 'PLN':
        is_success = False
        transaction = MagicMock(
            status='requires_action',
            last_payment_error={'last_payment_error': 'error'},
            next_action={'next_action': 'error'},
            **common_data,
        )
        error = ErrorObject(
            decline_code='insufficient_funds',
            message='Stripe message',
        )
    else:
        is_success = False
        transaction = None
        error = ErrorObject(
            decline_code='unknown_error',
            message='Unknown error.',
        )

    return MagicMock(
        is_success=is_success,
        transaction=transaction,
        error=error,
    )


# pylint: enable=unused-argument


@patch.object(BillingProduct, 'get_payable_staff_by_biz_id')
class TestProductQuantity(TestCase):
    def setUp(self):
        self.business = baker.make(Business)

    def test_product_quantity__default(self, payable_mock):
        product = baker.make(
            BillingProduct,
            product_type=ProductType.SAAS,
        )
        payable_mock.return_value = 15
        result = SubscriptionCreator.get_product_quantity(
            product=product,
            business_id=self.business.id,
        )
        self.assertEqual(result, 1)
        self.assertEqual(payable_mock.call_count, 0)

    def test_product_quantity__staffer(self, payable_mock):
        product = baker.make(
            BillingProduct,
            product_type=ProductType.STAFFER_SAAS,
        )
        payable_mock.return_value = 15
        result = SubscriptionCreator.get_product_quantity(
            product=product,
            business_id=self.business.id,
        )
        self.assertEqual(result, 15)
        self.assertEqual(payable_mock.mock_calls, [call(self.business.id)])

    def test_product_quantity__postpaid_sms(self, payable_mock):
        product = baker.make(
            BillingProduct,
            product_type=ProductType.POSTPAID_SMS,
        )
        payable_mock.return_value = 15
        result = SubscriptionCreator.get_product_quantity(
            product=product,
            business_id=self.business.id,
        )
        self.assertEqual(result, 0)
        self.assertEqual(payable_mock.call_count, 0)


# pylint: disable=too-many-instance-attributes
# pylint: disable=unused-argument
class OfferCreatorMixin:
    def _create_dummy_offer(self):
        other_product = baker.make(
            BillingProduct,
            product_type=ProductType.PREPAID_SMS,
            unit_price=Decimal(1000),
            sms_amount=1000,
            active=True,
            source=Business.PaymentSource.BRAINTREE_BILLING,
            currency='USD',
        )
        other_offer = baker.make(BillingProductOffer)
        baker.make(
            BillingProductOfferItem,
            offer_id=other_offer.id,
            product_id=other_product.id,
            discount_type=DiscountType.NO_DISCOUNT,
            discount_amount=None,
            discount_frac=None,
            discount_duration=None,
        )
        baker.make(
            BillingProductOfferItem,
            offer_id=other_offer.id,
            product_id=self.saas.id,
            discount_type=DiscountType.FIXED,
            discount_amount=Decimal('99.99'),
            discount_frac=None,
            discount_duration=None,
        )

    def _create_offer(self):
        self.saas_item = baker.make(
            BillingProductOfferItem,
            offer_id=self.offer.id,
            product_id=self.saas.id,
            discount_type=DiscountType.NO_DISCOUNT,
            discount_amount=None,
            discount_frac=None,
            discount_duration=None,
        )
        self.staffer_saas_item = baker.make(
            BillingProductOfferItem,
            offer_id=self.offer.id,
            product_id=self.staffer_saas.id,
            discount_type=DiscountType.PERCENTAGE,
            discount_amount=None,
            # 10%
            discount_frac=Decimal('0.100'),
            discount_duration=None,
        )
        self.postpaid_sms_item = baker.make(
            BillingProductOfferItem,
            offer_id=self.offer.id,
            product_id=self.postpaid_sms.id,
            discount_type=DiscountType.NO_DISCOUNT,
            discount_amount=None,
            discount_frac=None,
            discount_duration=None,
        )
        self.busy_item = baker.make(
            BillingProductOfferItem,
            offer_id=self.offer.id,
            product_id=self.busy.id,
            discount_type=DiscountType.FIXED,
            discount_amount=Decimal('20.00'),
            discount_frac=None,
            discount_duration=5,
        )

    def _prepare_offline_migration(self):
        self.business.has_new_billing = False
        self.business.payment_source = Business.PaymentSource.OFFLINE
        self.business.save()
        self.business.owner.email = '<EMAIL>'
        self.business.owner.save()
        self.offline_subscription = baker.make(
            Subscription,
            source=Business.PaymentSource.OFFLINE,
            start=datetime(2021, 1, 1, tzinfo=UTC),
            expiry=datetime(2021, 10, 10, tzinfo=UTC),
            business_id=self.business.id,
            current_billing_cycle_end=None,
            product=create_subscription_product(
                {
                    'price': 120,
                    'discount_amount': 5,
                    'payment_time_in_months': 12,
                    'discount_duration': 1,
                }
            ),
        )
        self.business_offer = baker.make(
            BillingBusinessOffer,
            offer=self.offer,
            business=self.business,
            active=True,
        )
        self.offline_migration = baker.make(
            BillingOfflineMigration,
            business=self.business,
            migration_campaign_valid_to=tznow() + timedelta(days=1),
        )
        baker.make(BillingOfflineMigrationRequest, offline_migration=self.offline_migration)
        self.policy_agreemnt = baker.make(
            BusinessPolicyAgreement,
            business=self.business,
        )

    def setUp(self):  # pylint: disable=invalid-name
        super().setUp()

        self.state = baker.make(
            Region,
            name='Arkansas',
            type=Region.Type.STATE,
        )
        self.zipcode = baker.make(
            Region,
            name='66000',
            type=Region.Type.ZIP,
        )
        bake_region_graphs(self.state, self.zipcode)
        self.business = baker.make(
            Business,
            zipcode=self.zipcode.name,
            status=Business.Status.TRIAL_BLOCKED,
            has_new_billing=True,
        )
        TaxRate.update_or_create(
            region=self.zipcode,
            service=TaxRate.Service.SAAS,
            valid_from=tznow(),
            tax_rate=Decimal('0.0150'),
        )
        self.business_settings = baker.make(
            BillingBusinessSettings,
            business=self.business,
            payment_processor=PaymentProcessorType.BRAINTREE,
        )
        self.offer = baker.make(BillingProductOffer)
        common_product_data = dict(
            active=True,
            source=Business.PaymentSource.BRAINTREE_BILLING,
            currency='USD',
        )
        self.staffer_saas = baker.make(
            BillingProduct,
            product_type=ProductType.STAFFER_SAAS,
            unit_price=Decimal('10.00'),
            sms_amount=10,
            **common_product_data,
        )
        self.postpaid_sms = baker.make(
            BillingProduct,
            product_type=ProductType.POSTPAID_SMS,
            unit_price=Decimal('0.04'),
            **common_product_data,
        )
        self.saas = baker.make(
            BillingProduct,
            product_type=ProductType.SAAS,
            unit_price=Decimal('100.00'),
            staff_add_on_id=self.staffer_saas.id,
            sms_add_on_id=self.postpaid_sms.id,
            sms_amount=100,
            **common_product_data,
        )
        self.busy = baker.make(
            BillingProduct,
            product_type=ProductType.BUSY,
            unit_price=Decimal('150.00'),
            sms_amount=200,
            **common_product_data,
        )
        self.payment_method = baker.make(
            BillingPaymentMethod,
            business_id=self.business.id,
            token='toooken',
        )
        self._create_offer()
        self._create_dummy_offer()


@pytest.mark.freeze_time(datetime(2021, 3, 1, 14, 13, 16, tzinfo=UTC))
@patch.object(BillingProduct, 'get_payable_staff_by_biz_id')
@patch.object(PaymentProcessor, 'create_transaction')
@override_settings(SAVE_HISTORY=True)
class TestSubscriptionCreator(OfferCreatorMixin, TestCase):
    def test_no_saas_provided(self, braintree_create_transaction_mock, payable_mock):
        with self.assertRaises(RuntimeError):
            SubscriptionCreator.new_purchase(
                business_id=self.business.id,
                offer_id=self.offer.id,
                product_ids=[self.busy.id],
            )
        self.assertEqual(payable_mock.call_count, 0)
        self.assertEqual(braintree_create_transaction_mock.call_count, 0)

    def test_get_products__no_extra_products(self, *mocks):
        result = SubscriptionCreator.get_products(
            offer_id=self.offer.id,
            product_ids=[
                self.saas.id,
            ],
        )
        self.assertEqual(len(result), 3)
        self.assertIn(self.saas_item, result)
        self.assertIn(self.staffer_saas_item, result)
        self.assertIn(self.postpaid_sms_item, result)

    def test_get_products__extra_products(self, *mocks):
        result = SubscriptionCreator.get_products(
            offer_id=self.offer.id,
            product_ids=[
                self.saas.id,
                self.busy.id,
            ],
        )
        self.assertEqual(len(result), 4)
        self.assertIn(self.saas_item, result)
        self.assertIn(self.staffer_saas_item, result)
        self.assertIn(self.postpaid_sms_item, result)
        self.assertIn(self.busy_item, result)

    def test_get_products__duplicated_products(self, *mocks):
        result = SubscriptionCreator.get_products(
            offer_id=self.offer.id,
            product_ids=[
                self.saas.id,
                self.saas.id,
            ],
        )
        self.assertEqual(len(result), 3)
        self.assertIn(self.saas_item, result)
        self.assertIn(self.staffer_saas_item, result)
        self.assertIn(self.postpaid_sms_item, result)

    def test_get_products__duplicated_add_ons(self, *mocks):
        result = SubscriptionCreator.get_products(
            offer_id=self.offer.id,
            product_ids=[
                self.saas.id,
                self.staffer_saas.id,
            ],
        )
        self.assertEqual(len(result), 3)
        self.assertIn(self.saas_item, result)
        self.assertIn(self.staffer_saas_item, result)
        self.assertIn(self.postpaid_sms_item, result)

    def _test_subscription_params(
        self,
        subscription,
        date_start=datetime(2021, 3, 1, tzinfo=UTC),
        next_billing_date=datetime(2021, 4, 1, tzinfo=UTC),
        status=SubscriptionStatus.ACTIVE,
    ):
        self.assertEqual(subscription.business_id, self.business.id)
        self.assertEqual(subscription.date_start, date_start)
        self.assertIsNone(subscription.date_expiry)
        self.assertEqual(subscription.next_billing_date, next_billing_date)
        self.assertEqual(subscription.paid_through_date, subscription.next_billing_date)
        self.assertEqual(subscription.balance, Decimal(0))
        self.assertEqual(subscription.payment_period, self.saas.payment_period)
        self.assertEqual(subscription.offer_id, self.offer.id)
        self.assertEqual(subscription.status, status)

    def _test_billing_cycle_params(self, billing_cycle, subscription):
        self.assertEqual(billing_cycle.business_id, self.business.id)
        self.assertEqual(billing_cycle.subscription_id, subscription.id)
        self.assertEqual(billing_cycle.date_start, subscription.date_start)
        self.assertEqual(billing_cycle.date_end, subscription.next_billing_date)
        self.assertEqual(billing_cycle.sms_allowance, 320)
        self.assertTrue(billing_cycle.is_open)

    def _test_saas(
        self,
        subscription: BillingSubscription,
        billing_cycle=t.Optional[BillingCycle],
    ):
        subscribed = BillingSubscribedProduct.objects.filter(
            subscription_id=subscription.id,
            product_id=self.saas.id,
        ).first()

        self.assertEqual(subscribed.date_start, subscription.date_start)
        self.assertIsNone(subscribed.date_end)
        self.assertEqual(subscribed.quantity, 1)
        self.assertEqual(subscribed.unit_price, Decimal('100.00'))
        self.assertEqual(subscribed.discount_type, DiscountType.NO_DISCOUNT)
        self.assertIsNone(subscribed.discount_amount)
        self.assertIsNone(subscribed.discount_frac)
        self.assertIsNone(subscribed.discount_duration)
        self.assertEqual(subscribed.discounted_price, Decimal('100.00'))
        self.assertEqual(subscribed.total_price, Decimal('100.00'))
        self.assertEqual(subscribed.final_price, Decimal('100.00'))
        self.assertEqual(subscribed.discount_granted, Decimal(0))
        self.assertEqual(subscribed.currency, 'USD')
        self.assertEqual(subscribed.product_type, self.saas.product_type)

        if billing_cycle is None:
            return

        charge = BillingCycleProductCharge.objects.filter(
            billing_cycle_id=billing_cycle.id,
            product_id=subscribed.id,
        ).first()

        self.assertEqual(charge.usage_from, billing_cycle.date_start)
        self.assertEqual(charge.usage_to, billing_cycle.date_end)
        self.assertEqual(charge.quantity, subscribed.quantity)
        self.assertEqual(charge.unit_price, subscribed.unit_price)
        self.assertEqual(charge.discounted_price, subscribed.discounted_price)
        self.assertEqual(charge.total_price, subscribed.total_price)
        self.assertEqual(charge.final_price, subscribed.final_price)
        self.assertEqual(charge.gross_final_price, subscribed.final_price * Decimal('1.0150'))
        self.assertEqual(charge.discount_granted, subscribed.discount_granted)
        self.assertEqual(charge.currency, subscribed.currency)
        self.assertEqual(
            charge.tax_additional_data,
            {'area': None, 'tax_rate_id': mock.ANY, 'tax_area_name': '66000'},
        )

    def _test_staffer_saas(
        self,
        subscription: BillingSubscription,
        billing_cycle=t.Optional[BillingCycle],
    ):
        subscribed = BillingSubscribedProduct.objects.filter(
            subscription_id=subscription.id,
            product_id=self.staffer_saas.id,
        ).first()

        self.assertEqual(subscribed.date_start, subscription.date_start)
        self.assertIsNone(subscribed.date_end)
        self.assertEqual(subscribed.quantity, 2)
        self.assertEqual(subscribed.unit_price, Decimal('10.00'))
        self.assertEqual(subscribed.discount_type, DiscountType.PERCENTAGE)
        self.assertIsNone(subscribed.discount_amount)
        self.assertEqual(subscribed.discount_frac, Decimal('0.100'))
        self.assertIsNone(subscribed.discount_duration)
        self.assertEqual(subscribed.discounted_price, Decimal('9.00'))
        self.assertEqual(subscribed.total_price, Decimal('20.00'))
        self.assertEqual(subscribed.final_price, Decimal('18.00'))
        self.assertEqual(subscribed.discount_granted, Decimal('2.00'))
        self.assertEqual(subscribed.currency, 'USD')
        self.assertEqual(subscribed.product_type, self.staffer_saas.product_type)

        if billing_cycle is None:
            return

        charge = BillingCycleProductCharge.objects.filter(
            billing_cycle_id=billing_cycle.id,
            product_id=subscribed.id,
        ).first()

        self.assertEqual(charge.usage_from, billing_cycle.date_start)
        self.assertEqual(charge.usage_to, billing_cycle.date_end)
        self.assertEqual(charge.quantity, subscribed.quantity)
        self.assertEqual(charge.unit_price, subscribed.unit_price)
        self.assertEqual(charge.discounted_price, subscribed.discounted_price)
        self.assertEqual(charge.total_price, subscribed.total_price)
        self.assertEqual(charge.final_price, subscribed.final_price)
        self.assertEqual(charge.discount_granted, subscribed.discount_granted)
        self.assertEqual(charge.currency, subscribed.currency)
        self.assertEqual(
            charge.tax_additional_data,
            {'area': None, 'tax_rate_id': mock.ANY, 'tax_area_name': '66000'},
        )

    def _test_postpaid_sms(
        self,
        subscription: BillingSubscription,
        billing_cycle=t.Optional[BillingCycle],
    ):
        subscribed = BillingSubscribedProduct.objects.filter(
            subscription_id=subscription.id,
            product_id=self.postpaid_sms.id,
        ).first()

        if billing_cycle is not None:
            charge_exists = BillingCycleProductCharge.objects.filter(
                billing_cycle_id=billing_cycle.id,
                product_id=subscribed.id,
            ).exists()
            self.assertFalse(charge_exists)

        self.assertEqual(subscribed.date_start, subscription.date_start)
        self.assertIsNone(subscribed.date_end)
        self.assertEqual(subscribed.quantity, 0)
        self.assertEqual(subscribed.unit_price, Decimal('0.04'))
        self.assertEqual(subscribed.discount_type, DiscountType.NO_DISCOUNT)
        self.assertIsNone(subscribed.discount_amount)
        self.assertIsNone(subscribed.discount_frac)
        self.assertIsNone(subscribed.discount_duration)
        self.assertEqual(subscribed.discounted_price, Decimal('0.04'))
        self.assertEqual(subscribed.total_price, Decimal(0))
        self.assertEqual(subscribed.final_price, Decimal(0))
        self.assertEqual(subscribed.discount_granted, Decimal(0))
        self.assertEqual(subscribed.currency, 'USD')
        self.assertEqual(subscribed.product_type, self.postpaid_sms.product_type)

    def _test_busy(
        self,
        subscription: BillingSubscription,
        billing_cycle=t.Optional[BillingCycle],
    ):
        subscribed_qs = BillingSubscribedProduct.objects.filter(
            subscription_id=subscription.id,
            product_id=self.busy.id,
        ).order_by('date_start')
        subscribed, subscribed_future = subscribed_qs

        self.assertEqual(subscribed.date_start, subscription.date_start)
        self.assertEqual(
            subscribed.date_end,
            (
                subscribed.date_start
                + (self.busy_item.discount_duration * subscription.payment_period)
            ),
        )
        self.assertEqual(subscribed.quantity, 1)
        self.assertEqual(subscribed.unit_price, Decimal('150.00'))
        self.assertEqual(subscribed.discount_type, DiscountType.FIXED)
        self.assertEqual(subscribed.discount_amount, Decimal('20.00'))
        self.assertIsNone(subscribed.discount_frac)
        self.assertEqual(subscribed.discount_duration, 5)
        self.assertEqual(subscribed.discounted_price, Decimal('130.00'))
        self.assertEqual(subscribed.total_price, Decimal('150.00'))
        self.assertEqual(subscribed.final_price, Decimal('130.00'))
        self.assertEqual(subscribed.discount_granted, Decimal('20.00'))
        self.assertEqual(subscribed.currency, 'USD')
        self.assertEqual(subscribed.product_type, self.busy.product_type)

        self.assertEqual(subscribed_future.date_start, subscribed.date_end)
        self.assertIsNone(subscribed_future.date_end)
        self.assertEqual(subscribed_future.quantity, 1)
        self.assertEqual(subscribed_future.unit_price, Decimal('150.00'))
        self.assertEqual(subscribed_future.discount_type, DiscountType.NO_DISCOUNT)
        self.assertIsNone(subscribed_future.discount_amount)
        self.assertIsNone(subscribed_future.discount_frac)
        self.assertIsNone(subscribed_future.discount_duration)
        self.assertEqual(subscribed_future.discounted_price, Decimal('150.00'))
        self.assertEqual(subscribed_future.total_price, Decimal('150.00'))
        self.assertEqual(subscribed_future.final_price, Decimal('150.00'))
        self.assertEqual(subscribed_future.discount_granted, Decimal(0))
        self.assertEqual(subscribed_future.currency, 'USD')
        self.assertEqual(subscribed_future.product_type, self.busy.product_type)

        if billing_cycle is None:
            return

        charge = BillingCycleProductCharge.objects.filter(
            billing_cycle_id=billing_cycle.id,
            product_id=subscribed.id,
        ).first()

        self.assertEqual(charge.usage_from, billing_cycle.date_start)
        self.assertEqual(charge.usage_to, billing_cycle.date_end)
        self.assertEqual(charge.quantity, subscribed.quantity)
        self.assertEqual(charge.unit_price, subscribed.unit_price)
        self.assertEqual(charge.discounted_price, subscribed.discounted_price)
        self.assertEqual(charge.total_price, subscribed.total_price)
        self.assertEqual(charge.final_price, subscribed.final_price)
        self.assertEqual(charge.discount_granted, subscribed.discount_granted)
        self.assertEqual(charge.currency, subscribed.currency)
        self.assertIsNone(charge.tax_additional_data)

    def _test_products(
        self,
        subscription: BillingSubscription,
        billing_cycle: t.Optional[BillingCycle],
    ):
        self._test_saas(subscription, billing_cycle)
        self._test_staffer_saas(subscription, billing_cycle)
        self._test_postpaid_sms(subscription, billing_cycle)
        self._test_busy(subscription, billing_cycle)

    def _test_successful_transaction(
        self,
        subscription,
        billing_cycle_id,
        expected_amount,
        payment_processor,
    ):
        transaction = BillingTransaction.objects.first()
        self.assertEqual(transaction.business_id, self.business.id)
        self.assertEqual(transaction.subscription_id, subscription.id)
        self.assertEqual(transaction.billing_cycle_id, billing_cycle_id)
        self.assertEqual(transaction.amount, expected_amount)
        self.assertEqual(transaction.currency, subscription.currency)
        self.assertEqual(
            transaction.response_code,
            '1000' if payment_processor == PaymentProcessorType.BRAINTREE else None,
        )
        self.assertEqual(transaction.status, TransactionStatus.CHARGED)
        self.assertEqual(transaction.external_id, 'create_subscription123')
        self.assertEqual(transaction.payment_method_id, self.payment_method.id)
        self.assertEqual(transaction.payment_processor, payment_processor)

    def _test_failed_transaction(self, expected_amount, payment_processor):
        transaction = BillingTransaction.objects.first()
        self.assertEqual(transaction.business_id, self.business.id)
        self.assertIsNone(transaction.subscription_id)
        self.assertIsNone(transaction.billing_cycle_id)
        self.assertEqual(transaction.amount, expected_amount)
        self.assertEqual(transaction.currency, 'PLN')
        self.assertEqual(
            transaction.response_code,
            '2001' if payment_processor == PaymentProcessorType.BRAINTREE else None,
        )
        if payment_processor == PaymentProcessorType.BRAINTREE:
            self.assertIsNone(transaction.response_errors)
        else:
            self.assertDictEqual(
                transaction.response_errors,
                {
                    'last_payment_error': {'last_payment_error': 'error'},
                    'next_action': {'next_action': 'error'},
                },
            )
        self.assertEqual(transaction.status, TransactionStatus.FAILED)
        self.assertEqual(transaction.external_id, 'create_subscription123')
        self.assertEqual(transaction.payment_method_id, self.payment_method.id)

    @parameterized.expand(
        [
            (PaymentProcessorType.STRIPE,),
            (PaymentProcessorType.BRAINTREE,),
        ]
    )
    @patch.object(StripePaymentProcessor, 'create_transaction')
    def test_new_purchase_current__success(
        self,
        payment_processor,
        stripe_create_transaction_mock,
        braintree_create_transaction_mock,
        payable_mock,
    ):
        # All products from an offer are purchased - discount is applicable
        stripe_create_transaction_mock.side_effect = create_test_stripe_transaction
        braintree_create_transaction_mock.side_effect = create_test_braintree_transaction
        payable_mock.return_value = 2

        self.business_settings.payment_processor = payment_processor
        self.business_settings.save()

        result = SubscriptionCreator.new_purchase(
            business_id=self.business.id,
            offer_id=self.offer.id,
            product_ids=[
                self.saas.id,
                self.busy.id,
            ],
            operator_id=self.business.owner.id,
        )
        self.assertTrue(
            BillingSubscriptionHistory.objects.filter(
                model__in=[result['subscription_id']],
                metadata__icontains='SubscriptionCreator',
                operator=self.business.owner,
            ).exists()
        )
        self.assertEqual(BillingSubscription.objects.count(), 1)
        # Busy created also for period after discount
        self.assertEqual(BillingSubscribedProduct.objects.count(), 5)
        self.assertEqual(BillingCycle.objects.count(), 1)
        # All except postpaid sms
        self.assertEqual(BillingCycleProductCharge.objects.count(), 3)
        self.assertEqual(BillingTransaction.objects.count(), 1)

        subscription = BillingSubscription.objects.first()
        self.assertEqual(subscription.id, result.get('subscription_id'))
        self.assertEqual(
            result,
            dict(
                message='Your subscription has been activated',
                subscription_id=subscription.id,
            ),
        )
        self._test_subscription_params(subscription)

        billing_cycle = BillingCycle.objects.first()
        self._test_billing_cycle_params(
            billing_cycle=billing_cycle,
            subscription=subscription,
        )

        self._test_products(subscription, billing_cycle)

        self.business.refresh_from_db()
        self.assertEqual(self.business.status, Business.Status.PAID)
        self.assertEqual(self.business.paid_till, subscription.paid_through_date)

        expected_amount = Decimal('249.78')
        self._test_successful_transaction(
            subscription=subscription,
            billing_cycle_id=billing_cycle.id,
            expected_amount=expected_amount,
            payment_processor=payment_processor,
        )

        self.assertEqual(payable_mock.call_count, 1)

        mock_calls = (
            braintree_create_transaction_mock.mock_calls
            if payment_processor == PaymentProcessorType.BRAINTREE
            else stripe_create_transaction_mock.mock_calls
        )

        self.assertEqual(
            mock_calls,
            [
                call(
                    business_id=self.business.id,
                    amount=expected_amount,
                    currency='USD',
                    description='Billing subscription',
                    metadata={
                        'operator_id': self.business.owner.id,
                        'action': 'initial_payment_with_card_auth',
                        'transaction_source': 'billing_subscription',
                    },
                )
            ],
        )

    @patch.object(StripePaymentProcessor, 'create_transaction')
    def test_new_purchase_current_with_payment_result__success(
        self,
        stripe_create_transaction_mock,
        braintree_create_transaction_mock,
        payable_mock,
    ):
        payable_mock.return_value = 2

        self.business_settings.payment_processor = PaymentProcessorType.STRIPE
        self.business_settings.save()

        offer_items = SubscriptionCreator.get_products(
            self.offer.id,
            [
                self.saas.id,
                self.busy.id,
            ],
        )
        charges = SubscriptionCreator.calculate_charges(
            self.business.id, self.offer.id, offer_items
        )

        result = SubscriptionCreator.new_purchase(
            business_id=self.business.id,
            offer_id=self.offer.id,
            product_ids=[
                self.saas.id,
                self.busy.id,
            ],
            operator_id=self.business.owner.id,
            payment_result=create_test_stripe_transaction(
                business_id=self.business.id,
                amount=charges['charge_amount'],
                currency='USD',
                description=TransactionSource.BILLING_SUBSCRIPTION.label,
            ),
        )
        self.assertTrue(
            BillingSubscriptionHistory.objects.filter(
                model__in=[result['subscription_id']],
                metadata__icontains='SubscriptionCreator',
                operator=self.business.owner,
            ).exists()
        )
        self.assertEqual(BillingSubscription.objects.count(), 1)
        # Busy created also for period after discount
        self.assertEqual(BillingSubscribedProduct.objects.count(), 5)
        self.assertEqual(BillingCycle.objects.count(), 1)
        # All except postpaid sms
        self.assertEqual(BillingCycleProductCharge.objects.count(), 3)
        self.assertEqual(BillingTransaction.objects.count(), 1)

        subscription = BillingSubscription.objects.first()
        self.assertEqual(subscription.id, result.get('subscription_id'))
        self.assertEqual(
            result,
            dict(
                message='Your subscription has been activated',
                subscription_id=subscription.id,
            ),
        )
        self._test_subscription_params(subscription)

        billing_cycle = BillingCycle.objects.first()
        self._test_billing_cycle_params(
            billing_cycle=billing_cycle,
            subscription=subscription,
        )

        self._test_products(subscription, billing_cycle)

        self.business.refresh_from_db()
        self.assertEqual(self.business.status, Business.Status.PAID)
        self.assertEqual(self.business.paid_till, subscription.paid_through_date)

        expected_amount = Decimal('249.78')
        self._test_successful_transaction(
            subscription=subscription,
            billing_cycle_id=billing_cycle.id,
            expected_amount=expected_amount,
            payment_processor=self.business_settings.payment_processor,
        )

        self.assertEqual(payable_mock.call_count, 2)
        self.assertEqual(stripe_create_transaction_mock.call_count, 0)
        self.assertEqual(braintree_create_transaction_mock.call_count, 0)

    @override_eppo_feature_flag({BillingDisableBTPaymentProcessor.flag_name: True})
    @patch.object(StripePaymentProcessor, 'create_transaction')
    @patch.object(PaymentProcessorBridge, 'create_transaction')
    def test_disabling_payment_processor_bridge_payment_result__success(
        self,
        bridge_create_transaction_mock,
        stripe_create_transaction_mock,
        braintree_create_transaction_mock,
        payable_mock,
    ):
        payable_mock.return_value = 2

        self.business_settings.payment_processor = PaymentProcessorType.STRIPE
        self.business_settings.save()

        offer_items = SubscriptionCreator.get_products(
            self.offer.id,
            [
                self.saas.id,
                self.busy.id,
            ],
        )
        charges = SubscriptionCreator.calculate_charges(
            self.business.id, self.offer.id, offer_items
        )

        result = SubscriptionCreator.new_purchase(
            business_id=self.business.id,
            offer_id=self.offer.id,
            product_ids=[
                self.saas.id,
                self.busy.id,
            ],
            operator_id=self.business.owner.id,
            payment_result=create_test_stripe_transaction(
                business_id=self.business.id,
                amount=charges['charge_amount'],
                currency='USD',
                description=TransactionSource.BILLING_SUBSCRIPTION.label,
            ),
        )
        self.assertTrue(
            BillingSubscriptionHistory.objects.filter(
                model__in=[result['subscription_id']],
                metadata__icontains='SubscriptionCreator',
                operator=self.business.owner,
            ).exists()
        )
        self.assertEqual(1, BillingSubscription.objects.count())
        # Busy created also for period after discount
        self.assertEqual(5, BillingSubscribedProduct.objects.count())
        self.assertEqual(
            1,
            BillingCycle.objects.count(),
        )
        # All except postpaid sms
        self.assertEqual(3, BillingCycleProductCharge.objects.count())
        self.assertEqual(1, BillingTransaction.objects.count())

        subscription = BillingSubscription.objects.first()
        self.assertEqual(result.get('subscription_id'), subscription.id)
        self.assertEqual(
            dict(
                message='Your subscription has been activated',
                subscription_id=subscription.id,
            ),
            result,
        )
        self._test_subscription_params(subscription)

        billing_cycle = BillingCycle.objects.first()
        self._test_billing_cycle_params(
            billing_cycle=billing_cycle,
            subscription=subscription,
        )

        self._test_products(subscription, billing_cycle)

        self.business.refresh_from_db()
        self.assertEqual(
            Business.Status.PAID,
            self.business.status,
        )
        self.assertEqual(self.business.paid_till, subscription.paid_through_date)

        expected_amount = Decimal('249.78')
        self._test_successful_transaction(
            subscription=subscription,
            billing_cycle_id=billing_cycle.id,
            expected_amount=expected_amount,
            payment_processor=self.business_settings.payment_processor,
        )

        self.assertEqual(2, payable_mock.call_count)
        self.assertFalse(stripe_create_transaction_mock.called)
        self.assertFalse(braintree_create_transaction_mock.called)
        self.assertFalse(bridge_create_transaction_mock.called)

    @parameterized.expand(
        [
            (PaymentProcessorType.STRIPE,),
            (PaymentProcessorType.BRAINTREE,),
        ]
    )
    @patch.object(StripePaymentProcessor, 'create_transaction')
    def test_new_purchase_current__long_sub(
        self,
        payment_processor,
        stripe_create_transaction_mock,
        braintree_create_transaction_mock,
        payable_mock,
    ):
        # All products from an offer are purchased - discount is applicable
        stripe_create_transaction_mock.side_effect = create_test_stripe_transaction
        braintree_create_transaction_mock.side_effect = create_test_braintree_transaction
        payable_mock.return_value = 2

        self.staffer_saas.free_staff_qty = 5
        self.staffer_saas.max_qty = 5
        self.staffer_saas.save()
        self.business_settings.payment_processor = payment_processor
        self.business_settings.save()
        # Subscription set for 3 months
        self.offer.subscription_duration = 3
        self.offer.save()

        result = SubscriptionCreator.new_purchase(
            business_id=self.business.id,
            offer_id=self.offer.id,
            product_ids=[
                self.saas.id,
                self.busy.id,
            ],
            operator_id=self.business.owner.id,
        )
        self.assertEqual(BillingSubscription.objects.count(), 1)
        # Saas created also for period after discount
        self.assertEqual(BillingSubscribedProduct.objects.count(), 6)
        self.assertEqual(BillingCycle.objects.count(), 1)
        # All except postpaid sms
        self.assertEqual(BillingCycleProductCharge.objects.count(), 3)
        self.assertEqual(BillingTransaction.objects.count(), 1)

        subscription = BillingSubscription.objects.first()
        self.assertEqual(subscription.id, result.get('subscription_id'))
        self.assertEqual(
            result,
            dict(
                message='Your subscription has been activated',
                subscription_id=subscription.id,
            ),
        )
        self._test_subscription_params(subscription)

        subscribed = BillingSubscribedProduct.objects.filter(
            subscription_id=subscription.id,
            product_id=self.saas.id,
        )
        current_saas = subscribed.first()
        self.assertEqual(current_saas.date_start, subscription.date_start)
        self.assertEqual(current_saas.date_end, subscription.date_start + relativedelta(months=1))
        self.assertEqual(current_saas.final_price, Decimal('100.00'))
        future_sass = subscribed.last()
        self.assertEqual(future_sass.date_start, current_saas.date_end)
        self.assertIsNone(future_sass.date_end)
        self.assertEqual(future_sass.final_price, Decimal('0.0'))

        self.business.refresh_from_db()
        self.assertEqual(self.business.status, Business.Status.PAID)
        self.assertEqual(self.business.paid_till, subscription.paid_through_date)

    @parameterized.expand(
        [
            (PaymentProcessorType.STRIPE,),
            (PaymentProcessorType.BRAINTREE,),
        ]
    )
    @patch.object(StripePaymentProcessor, 'create_transaction')
    def test_new_purchase_current__long_sub_with_failed_payment(
        self,
        payment_processor,
        stripe_create_transaction_mock,
        braintree_create_transaction_mock,
        payable_mock,
    ):
        # All products from an offer are purchased - discount is applicable
        stripe_create_transaction_mock.side_effect = create_test_stripe_transaction
        braintree_create_transaction_mock.side_effect = create_test_braintree_transaction
        payable_mock.return_value = 2

        self.business_settings.payment_processor = payment_processor
        self.business_settings.save()
        self.offer.subscription_duration = 6
        self.offer.save()

        # 6 months long subscribtion
        BillingProduct.objects.update(currency='PLN')
        SubscriptionCreator.new_purchase(
            business_id=self.business.id,
            offer_id=self.offer.id,
            product_ids=[self.saas.id],
            operator_id=self.business.owner.id,
        )

        self.assertFalse(BillingSubscription.objects.exists())
        self.assertFalse(BillingSubscribedProduct.objects.exists())
        self.assertFalse(BillingCycle.objects.exists())
        self.assertFalse(BillingCycleProductCharge.objects.exists())
        self.assertEqual(BillingTransaction.objects.count(), 1)
        self.business.refresh_from_db()
        self.assertEqual(self.business.status, Business.Status.TRIAL_BLOCKED)
        self.assertIsNone(self.business.paid_till)

    def test_new_purchase_current__payment_without_amount(self, transaction, payable_mock):
        payable_mock.return_value = 2

        BillingProductOfferItem.objects.filter(offer=self.offer).update(
            discount_type=DiscountType.PERCENTAGE,
            discount_frac=Decimal('1'),
        )

        result = SubscriptionCreator.new_purchase(
            business_id=self.business.id,
            offer_id=self.offer.id,
            product_ids=[
                self.saas.id,
                self.busy.id,
            ],
            operator_id=self.business.owner.id,
        )

        self.assertTrue(
            BillingSubscriptionHistory.objects.filter(
                model__in=[result['subscription_id']],
                metadata__icontains='SubscriptionCreator',
                operator=self.business.owner,
            ).exists()
        )
        self.assertEqual(BillingSubscription.objects.count(), 1)
        self.assertEqual(BillingSubscribedProduct.objects.count(), 5)
        self.assertEqual(BillingCycle.objects.count(), 1)
        self.assertEqual(BillingCycleProductCharge.objects.count(), 3)

        subscription = BillingSubscription.objects.first()
        self.assertEqual(subscription.id, result.get('subscription_id'))
        self.assertEqual(
            result,
            dict(
                message='Your subscription has been activated',
                subscription_id=subscription.id,
            ),
        )
        self._test_subscription_params(subscription)

        billing_cycle = BillingCycle.objects.first()
        self._test_billing_cycle_params(
            billing_cycle=billing_cycle,
            subscription=subscription,
        )

        self.business.refresh_from_db()
        self.assertEqual(self.business.status, Business.Status.PAID)
        self.assertEqual(self.business.paid_till, subscription.paid_through_date)
        transaction = BillingTransaction.objects.first()
        self.assertEqual(transaction.business_id, self.business.id)
        self.assertEqual(transaction.subscription_id, subscription.id)
        self.assertEqual(transaction.billing_cycle_id, billing_cycle.id)
        self.assertEqual(transaction.amount, Decimal('0'))
        self.assertEqual(transaction.currency, subscription.currency)

        self.assertEqual(transaction.status, TransactionStatus.SKIPPED)
        self.assertIsNone(transaction.payment_processor)

    def test_new_purchase_current__success_no_extra_products(
        self,
        braintree_create_transaction_mock,
        payable_mock,
    ):
        # All products from an offer are purchased - discount is applicable
        braintree_create_transaction_mock.side_effect = create_test_braintree_transaction
        payable_mock.return_value = 2
        subscription = SubscriptionCreator.new_purchase(
            business_id=self.business.id,
            offer_id=self.offer.id,
            product_ids=[
                self.saas.id,
            ],
            operator_id=self.business.owner.id,
        )
        self.assertTrue(
            BillingSubscriptionHistory.objects.filter(
                model__in=[subscription['subscription_id']],
                metadata__icontains='SubscriptionCreator',
                operator=self.business.owner,
            ).exists()
        )
        self.assertTrue(
            BillingSubscribedProductHistory.objects.filter(
                metadata__icontains='SubscriptionCreator',
                operator=self.business.owner,
            ).exists()
        )
        self.assertTrue(
            BillingCycleHistory.objects.filter(
                metadata__icontains='SubscriptionCreator',
                operator=self.business.owner,
            ).exists()
        )
        self.assertTrue(
            BillingCycleProductChargeHistory.objects.filter(
                metadata__icontains='SubscriptionCreator',
                operator=self.business.owner,
            ).exists()
        )
        self.assertEqual(BillingSubscription.objects.count(), 1)
        self.assertEqual(BillingSubscribedProduct.objects.count(), 3)
        self.assertEqual(BillingCycle.objects.count(), 1)
        # All except postpaid sms
        self.assertEqual(BillingCycleProductCharge.objects.count(), 2)
        self.assertEqual(BillingTransaction.objects.count(), 1)

        # Other params are covered in test_new_purchase__success
        # No discount applicable
        # 120 + 1.7(9) ~= 121.80
        expected_amount = Decimal('121.80')

        self.assertEqual(payable_mock.call_count, 1)
        self.assertEqual(braintree_create_transaction_mock.call_count, 1)
        self.assertListEqual(
            braintree_create_transaction_mock.mock_calls,
            [
                call(
                    business_id=self.business.id,
                    amount=expected_amount,
                    currency='USD',
                    description='Billing subscription',
                    metadata={
                        'operator_id': self.business.owner.id,
                        'action': 'initial_payment_with_card_auth',
                        'transaction_source': 'billing_subscription',
                    },
                )
            ],
        )

    @parameterized.expand(
        [
            (PaymentProcessorType.STRIPE,),
            (PaymentProcessorType.BRAINTREE,),
        ]
    )
    @patch.object(StripePaymentProcessor, 'create_transaction')
    def test_new_purchase_current__failure(
        self,
        payment_processor,
        stripe_create_transaction_mock,
        braintree_create_transaction_mock,
        payable_mock,
    ):
        # All products from an offer are purchased - discount is applicable
        braintree_create_transaction_mock.side_effect = create_test_braintree_transaction
        stripe_create_transaction_mock.side_effect = create_test_stripe_transaction
        payable_mock.return_value = 2

        self.business_settings.payment_processor = payment_processor
        self.business_settings.save()

        BillingProduct.objects.update(currency='PLN')
        result = SubscriptionCreator.new_purchase(
            business_id=self.business.id,
            offer_id=self.offer.id,
            product_ids=[
                self.saas.id,
            ],
        )

        error_group = BillingErrorGroupEnum.INSUFFICIENT_FUNDS
        if payment_processor == PaymentProcessorType.BRAINTREE:
            error_result = dict(
                message=error_group.label,
                error_code=error_group.value,
                internal_errors={'processor_response_code': '2001'},
                internal_message='Braintree message, 2001',
            )
        else:
            error_result = dict(
                message=error_group.label,
                error_code=error_group.value,
                internal_errors={
                    'charge': None,
                    'code': None,
                    'decline_code': 'insufficient_funds',
                    'doc_url': None,
                    'message': 'Stripe message',
                    'param': None,
                    'payment_intent': None,
                    'payment_method': None,
                    'setup_intent': None,
                    'source': None,
                    'type': None,
                },
                internal_message=None,
            )
        self.assertDictEqual(result, error_result)
        self.assertFalse(BillingSubscription.objects.exists())
        self.assertFalse(BillingSubscribedProduct.objects.exists())
        self.assertFalse(BillingCycle.objects.exists())
        self.assertFalse(BillingCycleProductCharge.objects.exists())
        self.assertEqual(BillingTransaction.objects.count(), 1)

        self.business.refresh_from_db()
        self.assertEqual(self.business.status, Business.Status.TRIAL_BLOCKED)
        self.assertIsNone(self.business.paid_till)

        # No discount applicable
        expected_amount = Decimal('121.80')
        self._test_failed_transaction(expected_amount, payment_processor)

        self.assertEqual(payable_mock.call_count, 1)

        mock_calls = (
            braintree_create_transaction_mock.mock_calls
            if payment_processor == PaymentProcessorType.BRAINTREE
            else stripe_create_transaction_mock.mock_calls
        )
        self.assertEqual(
            mock_calls,
            [
                call(
                    business_id=self.business.id,
                    amount=expected_amount,
                    currency='PLN',
                    description='Billing subscription',
                    metadata={
                        'operator_id': None,
                        'action': 'initial_payment_with_card_auth',
                        'transaction_source': 'billing_subscription',
                    },
                )
            ],
        )

    @parameterized.expand(
        [
            (PaymentProcessorType.STRIPE,),
            (PaymentProcessorType.BRAINTREE,),
        ]
    )
    @patch.object(StripePaymentProcessor, 'create_transaction')
    def test_new_purchase_current__failure__unknown_error(
        self,
        payment_processor,
        stripe_create_transaction_mock,
        braintree_create_transaction_mock,
        payable_mock,
    ):
        braintree_create_transaction_mock.side_effect = create_test_braintree_transaction
        stripe_create_transaction_mock.side_effect = create_test_stripe_transaction
        payable_mock.return_value = 2

        self.business_settings.payment_processor = payment_processor
        self.business_settings.save()
        BillingProduct.objects.update(currency='EUR')

        with patch('webapps.billing.utils.payment_processor_response_to_dict') as fn_patched:
            fn_patched.return_value = {'object': payment_processor}
            result = SubscriptionCreator.new_purchase(
                business_id=self.business.id,
                offer_id=self.offer.id,
                product_ids=[
                    self.saas.id,
                ],
            )

        error_group = BillingErrorGroupEnum.CONTACT_WITH_US
        if payment_processor == PaymentProcessorType.BRAINTREE:
            error_result = dict(
                message=error_group.label,
                error_code=error_group.value,
                internal_errors=[],
                internal_message='Braintree message, contact_with_us',
            )
        else:
            error_result = dict(
                message=error_group.label,
                error_code=error_group.value,
                internal_errors={
                    'charge': None,
                    'code': None,
                    'decline_code': 'unknown_error',
                    'doc_url': None,
                    'message': 'Unknown error.',
                    'param': None,
                    'payment_intent': None,
                    'payment_method': None,
                    'setup_intent': None,
                    'source': None,
                    'type': None,
                },
                internal_message=None,
            )

        self.assertDictEqual(result, error_result)
        self.assertFalse(BillingSubscription.objects.exists())
        self.assertFalse(BillingSubscribedProduct.objects.exists())
        self.assertFalse(BillingCycle.objects.exists())
        self.assertFalse(BillingCycleProductCharge.objects.exists())
        self.assertEqual(BillingTransaction.objects.count(), 0)

        self.business.refresh_from_db()
        self.assertEqual(self.business.status, Business.Status.TRIAL_BLOCKED)
        self.assertIsNone(self.business.paid_till)

        mock_calls = (
            braintree_create_transaction_mock.mock_calls
            if payment_processor == PaymentProcessorType.BRAINTREE
            else stripe_create_transaction_mock.mock_calls
        )
        self.assertListEqual(
            mock_calls,
            [
                call(
                    business_id=self.business.id,
                    amount=Decimal('121.80'),
                    currency='EUR',
                    description='Billing subscription',
                    metadata={
                        'operator_id': None,
                        'action': 'initial_payment_with_card_auth',
                        'transaction_source': 'billing_subscription',
                    },
                )
            ],
        )

        pp_error = PaymentProcessorError.objects.get()
        self.assertEqual(pp_error.business, self.business)
        self.assertEqual(pp_error.event_type, BillingErrorEventType.SUBSCRIPTION_CREATION)
        self.assertDictEqual(pp_error.response, {'object': payment_processor})

    def test_calculate_charges(
        self,
        braintree_create_transaction_mock,
        payable_mock,
    ):
        payable_mock.return_value = 1
        offer_items = [
            self.saas_item,  # 100 | 100 + 1.50 = 101.50 gross
            self.busy_item,  # 150 - 20 = 130
            self.staffer_saas_item,  # 10 - 10% = 9 | 10.15 gross - 10% = 9.135 -> 9.14 gross
            self.postpaid_sms_item,  # need to be added to assign discount
        ]
        charges = SubscriptionCreator.calculate_charges(
            self.business.id,
            self.offer.id,
            offer_items,
        )
        # 101.50 + 9.14 + 130 = 240.64
        self.assertEqual(charges['charge_amount'], Decimal('240.64'))
        self.assertDictEqual(
            charges['charge_details'][self.saas_item.id],
            {
                'quantity': 1,
                'total_price': Decimal('100.00'),
                'discounted_price': Decimal('100.00'),
                'discount_granted': Decimal('0.00'),
                'final_price': Decimal('100.00'),
                'discounted_price_gross': Decimal('101.50'),
                'total_price_gross': Decimal('101.50'),
                'discount_granted_gross': Decimal('0.00'),
                'final_price_gross': Decimal('101.50'),
            },
        )

        self.assertDictEqual(
            charges['charge_details'][self.staffer_saas_item.id],
            {
                'quantity': 1,
                'total_price': Decimal('10.00'),
                'discounted_price': Decimal('9.00'),
                'discount_granted': Decimal('1.00'),
                'final_price': Decimal('9.00'),
                'total_price_gross': Decimal('10.15'),
                'discounted_price_gross': Decimal('9.14'),
                'discount_granted_gross': Decimal('1.01'),
                'final_price_gross': Decimal('9.14'),
            },
        )
        self.assertDictEqual(
            charges['charge_details'][self.busy_item.id],
            {
                'quantity': 1,
                'total_price': Decimal('150.00'),
                'discounted_price': Decimal('130.00'),
                'discount_granted': Decimal('20.00'),
                'final_price': Decimal('130.00'),
                'total_price_gross': Decimal('150.00'),
                'discounted_price_gross': Decimal('130.00'),
                'discount_granted_gross': Decimal('20.00'),
                'final_price_gross': Decimal('130.00'),
            },
        )
        # 3 frac digits but charge should be rounded to 2 decimals
        self.saas_item.product.unit_price = Decimal('10.005')  # 10.155075 gross
        self.saas_item.product.save()
        charges = SubscriptionCreator.calculate_charges(
            self.business.id,
            self.offer.id,
            [self.saas_item],
        )
        self.assertEqual(charges['charge_amount'], Decimal('10.16'))

    @parameterized.expand(
        [
            (PaymentProcessorType.STRIPE,),
            (PaymentProcessorType.BRAINTREE,),
        ]
    )
    @patch.object(StripePaymentProcessor, 'create_transaction')
    def test_new_purchase_pending__success(
        self,
        payment_processor,
        stripe_create_transaction_mock,
        braintree_create_transaction_mock,
        payable_mock,
    ):
        payable_mock.return_value = 2

        self.business_settings.payment_processor = payment_processor
        self.business_settings.save()

        # All products from an offer are purchased - discount is applicable
        result = SubscriptionCreator.new_purchase(
            business_id=self.business.id,
            offer_id=self.offer.id,
            product_ids=[
                self.saas.id,
                self.busy.id,
            ],
            subscription_start=datetime(2021, 4, 1, 14, 13, 16, tzinfo=UTC),
        )
        self.assertEqual(BillingSubscription.objects.count(), 1)
        # Busy created also for period after discount
        self.assertEqual(BillingSubscribedProduct.objects.count(), 5)
        self.assertEqual(BillingCycle.objects.count(), 0)
        self.assertEqual(BillingCycleProductCharge.objects.count(), 0)
        self.assertEqual(BillingTransaction.objects.count(), 0)

        subscription = BillingSubscription.objects.get()
        self.assertEqual(subscription.id, result.get('subscription_id'))
        self._test_subscription_params(
            subscription=subscription,
            date_start=datetime(2021, 4, 1, tzinfo=UTC),
            next_billing_date=datetime(2021, 4, 1, tzinfo=UTC),
            status=SubscriptionStatus.PENDING,
        )
        self.assertFalse(
            BillingSubscriptionHistory.objects.filter(
                operator=self.business.owner,
            ).exists()
        )
        self.assertFalse(
            BillingSubscribedProductHistory.objects.filter(
                operator=self.business.owner,
            ).exists()
        )
        self.assertFalse(
            BillingCycleHistory.objects.filter(
                operator=self.business.owner,
            ).exists()
        )
        self.assertFalse(
            BillingCycleProductChargeHistory.objects.filter(
                operator=self.business.owner,
            ).exists()
        )
        self._test_products(subscription, billing_cycle=None)

        self.business.refresh_from_db()
        self.assertEqual(self.business.status, Business.Status.TRIAL_BLOCKED)
        self.assertIsNone(self.business.paid_till)

        self.assertEqual(payable_mock.call_count, 1)
        self.assertEqual(braintree_create_transaction_mock.call_count, 0)
        self.assertEqual(stripe_create_transaction_mock.call_count, 0)

    def test_new_purchase_pending__success_no_extra_products(
        self,
        braintree_create_transaction_mock,
        payable_mock,
    ):
        # All products from an offer are purchased - discount is applicable
        payable_mock.return_value = 2
        subscription = SubscriptionCreator.new_purchase(
            business_id=self.business.id,
            offer_id=self.offer.id,
            product_ids=[
                self.saas.id,
            ],
            subscription_start=datetime(2021, 4, 1, 14, 13, 17, tzinfo=UTC),
            operator_id=self.business.owner.id,
        )
        self.assertTrue(
            BillingSubscriptionHistory.objects.filter(
                model__in=[subscription.get('subscription_id')],
                metadata__icontains='SubscriptionCreator',
                operator=self.business.owner,
            ).exists()
        )
        self.assertEqual(BillingSubscription.objects.count(), 1)
        self.assertEqual(BillingSubscribedProduct.objects.count(), 3)
        self.assertEqual(BillingCycle.objects.count(), 0)
        self.assertEqual(BillingCycleProductCharge.objects.count(), 0)
        self.assertEqual(BillingTransaction.objects.count(), 0)

        self.assertEqual(payable_mock.call_count, 1)
        self.assertEqual(braintree_create_transaction_mock.call_count, 0)

    @patch.object(StripePaymentProcessor, 'create_transaction')
    def test_new_purchase__success_with_deactivate_offer(
        self,
        stripe_create_transaction_mock,
        braintree_create_transaction_mock,
        payable_mock,
    ):
        stripe_create_transaction_mock.side_effect = create_test_stripe_transaction
        braintree_create_transaction_mock.side_effect = create_test_braintree_transaction
        payable_mock.return_value = 2
        business_offer = baker.make(
            BillingBusinessOffer,
            offer=self.offer,
            business=self.business,
            active=True,
        )

        result = SubscriptionCreator.new_purchase(
            business_id=self.business.id,
            offer_id=self.offer.id,
            product_ids=[
                self.saas.id,
                self.busy.id,
            ],
            operator_id=self.business.owner.id,
        )

        business_offer.refresh_from_db()
        subscription = BillingSubscription.objects.first()
        self.assertEqual(
            result,
            dict(
                message='Your subscription has been activated',
                subscription_id=subscription.id,
            ),
        )
        self.assertFalse(business_offer.active)

    @patch.object(StripePaymentProcessor, 'create_transaction')
    @patch.object(MigratedFromOfflineSubscriptionNotification, 'send')
    def test_new_purchase__success_with_billing_offline_migration(
        self,
        send_mock,
        stripe_create_transaction_mock,
        braintree_create_transaction_mock,
        payable_mock,
    ):
        stripe_create_transaction_mock.side_effect = create_test_stripe_transaction
        braintree_create_transaction_mock.side_effect = create_test_braintree_transaction
        payable_mock.return_value = 2
        self._prepare_offline_migration()
        region = baker.make(Region, name='A', type=Region.Type.ZIP)
        self.business.region = region
        self.business.enable_boost_availability()
        self.business.boost_status = Business.BoostStatus.DISABLED
        self.business.boost_payment_source = BoostPaymentSource.OFFLINE
        self.business.save()
        self.business.refresh_from_db()

        self.assertFalse(self.offline_migration.agreed_at)
        self.assertFalse(self.policy_agreemnt.reagreed_at)

        SubscriptionCreator.new_purchase(
            business_id=self.business.id,
            offer_id=self.offer.id,
            product_ids=[
                self.saas.id,
                self.busy.id,
            ],
            operator_id=self.business.owner.id,
        )

        self.offline_migration.refresh_from_db()
        self.offline_subscription.refresh_from_db()
        self.business.refresh_from_db()

        self.assertTrue(self.offline_migration.agreed_at)
        self.assertTrue(self.offline_subscription.expiry, self.offline_migration.agreed_at)
        self.assertEqual(self.business.boost_payment_source, BoostPaymentSource.OFFLINE)
        offline_migration_history = self.offline_migration.history.order_by('-pk').first()
        self.assertIn('SubscriptionCreator.new_purchase', offline_migration_history.metadata)
        send_mock.assert_called_once()

    @patch.object(StripePaymentProcessor, 'create_transaction')
    @patch.object(MigratedFromOfflineSubscriptionNotification, 'send')
    def test_new_purchase__success_with_boost_offline_migration(
        self,
        send_mock,
        stripe_create_transaction_mock,
        braintree_create_transaction_mock,
        payable_mock,
    ):
        stripe_create_transaction_mock.side_effect = create_test_stripe_transaction
        braintree_create_transaction_mock.side_effect = create_test_braintree_transaction
        payable_mock.return_value = 2
        region = baker.make(Region, name='A', type=Region.Type.ZIP)
        self.business.region = region
        self.business.enable_boost_availability()
        self.business.boost_status = Business.BoostStatus.ENABLED
        self.business.boost_payment_source = BoostPaymentSource.OFFLINE
        self.business.save()

        self._prepare_offline_migration()
        self.assertFalse(self.offline_migration.agreed_at)
        self.assertFalse(self.policy_agreemnt.reagreed_at)
        self.assertFalse(self.policy_agreemnt.boost_policy_reagreement)

        SubscriptionCreator.new_purchase(
            business_id=self.business.id,
            offer_id=self.offer.id,
            product_ids=[
                self.saas.id,
                self.busy.id,
            ],
            operator_id=self.business.owner.id,
        )

        self.offline_migration.refresh_from_db()
        self.offline_subscription.refresh_from_db()
        self.business.refresh_from_db()

        self.assertTrue(self.offline_migration.agreed_at)
        self.assertTrue(self.offline_migration.boost_agreed_at)
        self.assertEqual(self.business.boost_payment_source, BoostPaymentSource.ONLINE)

    @patch.object(StripePaymentProcessor, 'create_transaction')
    @patch.object(MigratedFromOfflineSubscriptionNotification, 'send')
    def test_new_purchase__failure_with_billing_offline_migration(
        self,
        send_mock,
        stripe_create_transaction_mock,
        braintree_create_transaction_mock,
        payable_mock,
    ):
        stripe_create_transaction_mock.side_effect = create_test_stripe_transaction
        braintree_create_transaction_mock.side_effect = create_test_braintree_transaction
        payable_mock.return_value = 2
        self._prepare_offline_migration()
        self.assertFalse(self.offline_migration.agreed_at)
        self.assertFalse(self.policy_agreemnt.reagreed_at)

        BillingProduct.objects.update(currency='PLN')
        SubscriptionCreator.new_purchase(
            business_id=self.business.id,
            offer_id=self.offer.id,
            product_ids=[
                self.saas.id,
                self.busy.id,
            ],
            operator_id=self.business.owner.id,
        )

        self.offline_migration.refresh_from_db()
        self.assertFalse(self.offline_migration.agreed_at)

        self.policy_agreemnt.refresh_from_db()
        self.assertFalse(self.policy_agreemnt.reagreed_at)

        self.business.refresh_from_db()
        self.assertFalse(self.business.has_new_billing)
        self.assertEqual(self.business.payment_source, Business.PaymentSource.OFFLINE)
        send_mock.assert_not_called()

    def test_get_currency(self, *_mocks):
        self.saas.currency = 'EUR'
        self.saas.save()

        offer_items = SubscriptionCreator.get_products(
            self.offer.id,
            product_ids=[self.saas.id, self.busy.id],
        )

        result = SubscriptionCreator.saas_currency(offer_items)
        self.assertEqual(result, 'EUR')


class TestPurchaseSubscriptionTask(TestCase):
    @patch.object(SubscriptionCreator, 'new_purchase')
    def test__success(self, new_purchase_mock):
        business = baker.make(Business, has_new_billing=True)

        purchase_subscription_task.run(
            business_id=business.id,
            offer_id=1,
            product_ids=[2],
            operator_id=business.owner_id,
        )

        self.assertEqual(
            new_purchase_mock.mock_calls, [call(business.id, 1, [2], business.owner_id)]
        )

    @patch.object(SubscriptionCreator, 'new_purchase')
    def test__locked(self, new_purchase_mock):
        business = baker.make(Business, has_new_billing=True)

        lock_ = BillingSubscriptionLock.lock(business.id)
        result = purchase_subscription_task.run(
            business_id=business.id,
            offer_id=1,
            product_ids=[1],
        )
        BillingSubscriptionLock.try_to_unlock(lock_)

        self.assertEqual(result['error_code'], 130)
        self.assertFalse(new_purchase_mock.call_count)

    @patch.object(SubscriptionCreator, 'new_purchase')
    def test__already_has_subscription(self, new_purchase_mock):
        business = baker.make(Business, has_new_billing=True)
        baker.make(
            BillingSubscription,
            business_id=business.id,
            date_start=tznow(),
        )

        result = purchase_subscription_task.run(
            business_id=business.id,
            offer_id=1,
            product_ids=[1],
        )

        self.assertEqual(result['error_code'], 130)
        self.assertFalse(new_purchase_mock.call_count)


# pylint: enable=too-many-instance-attributes
# pylint: enable=unused-argument
