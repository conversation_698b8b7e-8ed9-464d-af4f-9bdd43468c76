from datetime import datetime, timed<PERSON>ta
from decimal import Decimal
from unittest.mock import call, patch

import pytest
from django.test import override_settings
from django.urls.base import reverse
from model_bakery import baker
from parameterized import parameterized
from pytz import UTC
from lib.tools import tznow

from webapps.admin_extra.tests import DjangoTestCase
from webapps.billing.enums import (
    DiscountDuration,
    DiscountReason,
    DiscountType,
    ProductType,
    SubscriptionStatus,
)
from webapps.billing.forms import BillingDiscountCodeForm
from webapps.billing.models import (
    BillingBusinessDiscount,
    BillingDiscountCode,
    BillingDiscountCodeUsage,
    BillingProductOffer,
    BillingProductOfferItem,
    BillingSubscription,
)
from webapps.business.models import Business


@pytest.mark.freeze_time(datetime(2021, 3, 1, tzinfo=UTC))
class TestBillingBusinessDiscountAdmin(DjangoTestCase):
    def setUp(self):
        super().setUp()
        self.login_admin()

    @staticmethod
    def get_payload(business: Business) -> dict:
        payload = {
            'business': business.pk,
            'product_type': ProductType.SAAS,
            'discount_type': DiscountType.PERCENTAGE,
            'duration': 3,
            'discount_value': 20,
            'reason': DiscountReason.OTHER,
            'comment': 'comment ...',
        }
        return payload

    def test_changelist_works(self):
        url = reverse('admin:billing_billingbusinessdiscount_changelist')

        baker.make(BillingBusinessDiscount, business__name='Test business name')

        resp = self.client.get(url)
        self.assertContains(resp, 'Test business name')

    def test_can_not_change_object(self):
        discount = baker.make(BillingBusinessDiscount)

        url = reverse('admin:billing_billingbusinessdiscount_change', args=(discount.pk,))

        resp = self.client.get(url)
        self.assertNotContains(resp, 'Save</button>')

    def test_search_view_works(self):
        instance = baker.make(BillingBusinessDiscount)
        instance2 = baker.make(BillingBusinessDiscount)
        self.url = reverse('admin:billing_billingbusinessoffer_changelist')
        resp = self.client.get(self.url + f'?q={instance.business.name}')
        self.assertContains(resp, instance.business.name)
        self.assertNotContains(resp, instance2.business.name)

    def test_can_not_delete_object(self):
        discount = baker.make(
            BillingBusinessDiscount,
            date_start=datetime(2021, 3, 1, tzinfo=UTC),
        )

        url = reverse('admin:billing_billingbusinessdiscount_change', args=(discount.pk,))

        resp = self.client.get(url)

        self.assertNotContains(
            resp,
            reverse('admin:billing_billingbusinessdiscount_delete', args=(discount.pk,)),
        )

    def test_can_delete_object(self):
        discount = baker.make(
            BillingBusinessDiscount,
            date_start=datetime(2021, 3, 2, tzinfo=UTC),
        )

        url = reverse('admin:billing_billingbusinessdiscount_change', args=(discount.pk,))

        resp = self.client.get(url)

        self.assertContains(
            resp,
            reverse('admin:billing_billingbusinessdiscount_delete', args=(discount.pk,)),
        )

    def test_deletion_calls_soft_delete(self):
        discount = baker.make(
            BillingBusinessDiscount,
            date_start=datetime(2021, 3, 2, tzinfo=UTC),
        )

        url = reverse('admin:billing_billingbusinessdiscount_delete', args=(discount.pk,))

        resp = self.client.post(url, data={'post': 'yes'}, follow=True)

        self.assertContains(resp, 'was deleted successfully')

        discount.refresh_from_db()
        self.assertTrue(discount.deleted)

    def test_business_has_no_new_billing(self):
        business = baker.make(
            Business,
            status=Business.Status.PAID,
            has_new_billing=False,
        )

        url = reverse('admin:billing_billingbusinessdiscount_add')

        resp = self.client.post(
            url,
            data=self.get_payload(business),
            follow=True,
        )

        form = resp.context['adminform'].form

        self.assertFalse(form.is_valid())
        self.assertIn(
            'Only businesses with a new billing are allowed.',
            form.errors['business'][0],
        )

        self.assertEqual(BillingBusinessDiscount.objects.count(), 0)

    def test_business_is_not_paid(self):
        business = baker.make(
            Business,
            status=Business.Status.CHURNED,
            has_new_billing=True,
        )

        url = reverse('admin:billing_billingbusinessdiscount_add')

        resp = self.client.post(
            url,
            data=self.get_payload(business),
            follow=True,
        )

        form = resp.context['adminform'].form

        self.assertFalse(form.is_valid())
        self.assertIn(
            'Only PAID businesses are allowed.',
            form.errors['business'][0],
        )

        self.assertEqual(BillingBusinessDiscount.objects.count(), 0)

    def test_comment_is_required_if_other_reason(self):
        business = baker.make(
            Business,
            status=Business.Status.PAID,
            has_new_billing=True,
        )

        url = reverse('admin:billing_billingbusinessdiscount_add')

        payload = self.get_payload(business)
        payload['reason'] = DiscountReason.OTHER
        del payload['comment']

        resp = self.client.post(
            url,
            data=payload,
            follow=True,
        )

        form = resp.context['adminform'].form

        self.assertFalse(form.is_valid())
        self.assertIn(
            'This field is required.',
            form.errors['comment'][0],
        )

        self.assertEqual(BillingBusinessDiscount.objects.count(), 0)

    @parameterized.expand(
        [
            (Decimal('0.001'), 'Ensure this value is greater than or equal to 0.1.'),
            (Decimal('0.1'), None),
            (Decimal('99.9'), None),
            (Decimal('100'), None),
            (Decimal('100.1'), 'Ensure this value is less than or equal to 100.'),
        ]
    )
    def test_discount_value_validation_for_percentage(
        self,
        discount_value,
        error_message,
    ):
        business = baker.make(
            Business,
            status=Business.Status.CHURNED,
        )

        url = reverse('admin:billing_billingbusinessdiscount_add')

        payload = self.get_payload(business)
        payload['discount_type'] = DiscountType.PERCENTAGE
        payload['discount_value'] = discount_value

        resp = self.client.post(
            url,
            data=payload,
            follow=True,
        )

        form = resp.context['adminform'].form

        self.assertFalse(form.is_valid())

        if error_message:
            self.assertIn(
                error_message,
                form.errors['discount_value'][0],
            )
        else:
            self.assertNotIn('discount_value', form.errors)

        self.assertEqual(BillingBusinessDiscount.objects.count(), 0)

    @parameterized.expand(
        [
            (
                Decimal('0.001'),
                'Ensure this value is greater than or equal to 0.01.',
            ),
            (Decimal('0.1'), None),
            (Decimal('99.9'), None),
            (Decimal('9999.9'), None),
            (
                Decimal('99999999.9'),
                'Ensure that there are no more than 7 digits before the decimal point.',
            ),
            (
                Decimal('10_000_000.99'),
                'Ensure that there are no more than 9 digits in total.',
            ),
        ]
    )
    def test_discount_value_validation_for_fixed_price(
        self,
        discount_value,
        error_message,
    ):
        business = baker.make(
            Business,
            status=Business.Status.CHURNED,
        )

        url = reverse('admin:billing_billingbusinessdiscount_add')

        payload = self.get_payload(business)
        payload['discount_type'] = DiscountType.FIXED
        payload['discount_value'] = discount_value

        resp = self.client.post(
            url,
            data=payload,
            follow=True,
        )

        form = resp.context['adminform'].form

        self.assertFalse(form.is_valid())

        if error_message:
            self.assertIn(
                error_message,
                form.errors['discount_value'][0],
            )
        else:
            self.assertNotIn('discount_value', form.errors)

        self.assertEqual(BillingBusinessDiscount.objects.count(), 0)

    def test_no_subscription(self):
        business = baker.make(
            Business,
            status=Business.Status.PAID,
            has_new_billing=True,
        )

        url = reverse('admin:billing_billingbusinessdiscount_add')

        resp = self.client.post(
            url,
            data=self.get_payload(business),
            follow=True,
        )

        form = resp.context['adminform'].form

        self.assertFalse(form.is_valid())
        self.assertIn(
            'Business has no active subscription.',
            form.errors['business'][0],
        )

        self.assertEqual(BillingBusinessDiscount.objects.count(), 0)

    def test_no_active_subscription(self):
        business = baker.make(
            Business,
            status=Business.Status.PAID,
            has_new_billing=True,
        )
        baker.make(
            BillingSubscription,
            business=business,
            status=SubscriptionStatus.BLOCKED,
            date_start=tznow(),
            next_billing_date=datetime(2021, 4, 1, tzinfo=UTC),
        )

        url = reverse('admin:billing_billingbusinessdiscount_add')

        resp = self.client.post(
            url,
            data=self.get_payload(business),
            follow=True,
        )

        form = resp.context['adminform'].form

        self.assertFalse(form.is_valid())
        self.assertIn(
            'Business has no active subscription.',
            form.errors['business'][0],
        )

        self.assertEqual(BillingBusinessDiscount.objects.count(), 0)

    def test_subscription_has_date_expiry(self):
        business = baker.make(
            Business,
            status=Business.Status.PAID,
            has_new_billing=True,
        )
        baker.make(
            BillingSubscription,
            business=business,
            status=SubscriptionStatus.ACTIVE,
            date_expiry=datetime(2021, 5, 1, tzinfo=UTC),
            date_start=tznow(),
            next_billing_date=datetime(2021, 4, 1, tzinfo=UTC),
        )

        url = reverse('admin:billing_billingbusinessdiscount_add')

        resp = self.client.post(
            url,
            data=self.get_payload(business),
            follow=True,
        )

        form = resp.context['adminform'].form

        self.assertFalse(form.is_valid())
        self.assertIn(
            'Subscription is not properly configured.',
            form.errors['business'][0],
        )

        self.assertEqual(BillingBusinessDiscount.objects.count(), 0)

    def test_subscription_in_the_past(self):
        business = baker.make(
            Business,
            status=Business.Status.PAID,
            has_new_billing=True,
        )
        baker.make(
            BillingSubscription,
            business=business,
            status=SubscriptionStatus.ACTIVE,
            date_start=datetime(2021, 1, 1, tzinfo=UTC),
            next_billing_date=datetime(2021, 2, 1, tzinfo=UTC),
        )

        url = reverse('admin:billing_billingbusinessdiscount_add')

        resp = self.client.post(
            url,
            data=self.get_payload(business),
            follow=True,
        )

        form = resp.context['adminform'].form

        self.assertFalse(form.is_valid())
        self.assertIn(
            'Subscription is not properly configured.',
            form.errors['business'][0],
        )

        self.assertEqual(BillingBusinessDiscount.objects.count(), 0)

    @override_settings(SAVE_HISTORY=True)
    def test_create_percentage_discount(self):
        business = baker.make(
            Business,
            status=Business.Status.PAID,
            has_new_billing=True,
        )
        subscription = baker.make(
            BillingSubscription,
            business=business,
            status=SubscriptionStatus.ACTIVE,
            date_start=datetime(2021, 2, 1, tzinfo=UTC),
            next_billing_date=datetime(2021, 4, 1, tzinfo=UTC),
        )

        url = reverse('admin:billing_billingbusinessdiscount_add')

        payload = self.get_payload(business)
        payload['duration'] = 2
        payload['discount_type'] = DiscountType.PERCENTAGE
        payload['discount_value'] = Decimal('20.1')

        resp = self.client.post(
            url,
            data=payload,
            follow=True,
        )

        self.assertContains(resp, 'was added successfully')

        discount = BillingBusinessDiscount.objects.get()

        self.assertEqual(discount.frac, Decimal('0.201'))
        self.assertIsNone(discount.amount)
        self.assertEqual(discount.date_start, datetime(2021, 4, 1, tzinfo=UTC))
        self.assertEqual(discount.date_end, datetime(2021, 6, 1, tzinfo=UTC))
        self.assertEqual(discount.subscription, subscription)
        self.assertIsNone(discount.used)

        self.assertTrue(discount.history.filter(operator__email='<EMAIL>').exists())

    def test_create_fixed_price_discount(self):
        business = baker.make(
            Business,
            status=Business.Status.PAID,
            has_new_billing=True,
        )
        subscription = baker.make(
            BillingSubscription,
            business=business,
            status=SubscriptionStatus.ACTIVE,
            date_start=datetime(2021, 2, 1, tzinfo=UTC),
            next_billing_date=datetime(2021, 4, 1, tzinfo=UTC),
        )

        url = reverse('admin:billing_billingbusinessdiscount_add')

        payload = self.get_payload(business)
        payload['duration'] = 3
        payload['discount_type'] = DiscountType.FIXED
        payload['discount_value'] = Decimal('33.02')

        resp = self.client.post(
            url,
            data=payload,
            follow=True,
        )

        self.assertContains(resp, 'was added successfully')

        discount = BillingBusinessDiscount.objects.get()

        self.assertEqual(discount.amount, Decimal('33.02'))
        self.assertEqual(discount.subscription, subscription)
        self.assertIsNone(discount.frac)
        self.assertEqual(discount.date_start, datetime(2021, 4, 1, tzinfo=UTC))
        self.assertEqual(discount.date_end, datetime(2021, 7, 1, tzinfo=UTC))
        self.assertIsNone(discount.used)


@pytest.mark.freeze_time(datetime(2021, 3, 1, tzinfo=UTC))
class TestBillingDiscountCodeAdmin(DjangoTestCase):
    def setUp(self):
        super().setUp()
        self.login_admin()
        self.offer = baker.make(BillingProductOffer, default=False, active=True)

    def get_data(
        self,
        for_new_providers=False,
        for_existing_providers=False,
        discount_type=DiscountType.FIXED,
    ) -> dict:
        data = dict(
            discount_code='TEST.CODE.123#!',
            active=True,
            name='test code here!',
            valid_from=tznow(),
            valid_to=tznow() + timedelta(days=7),
            max_usage_limit=50,
            for_new_providers=for_new_providers,
            for_existing_providers=for_existing_providers,
        )
        if for_new_providers:
            data['offer'] = self.offer.id
        if for_existing_providers:
            data.update(
                dict(
                    discount_type=discount_type.value,
                    discount_value_saas=20,
                    discount_value_staffer_saas=10,
                    discount_duration=DiscountDuration.THREE.value,
                )
            )
        return data

    def assert_common_part(self, discount_code):
        self.assertEqual(discount_code.discount_code, 'TEST.CODE.123#!')
        self.assertEqual(discount_code.valid_from, tznow())
        self.assertEqual(discount_code.valid_to, tznow() + timedelta(days=7))
        self.assertEqual(discount_code.max_usage_limit, 50)
        self.assertEqual(discount_code.name, 'test code here!')

    def test_form__ok__new_providers__no_discount_duration(self):
        form = BillingDiscountCodeForm(data=self.get_data(for_new_providers=True))
        self.assertTrue(form.is_valid())
        discount_code = form.save()
        self.assert_common_part(discount_code)
        self.assertTrue(discount_code.for_new_providers)
        self.assertFalse(discount_code.for_existing_providers)

        self.assertEqual(discount_code.offer, self.offer)

        self.assertIsNone(discount_code.discount_type)
        self.assertIsNone(discount_code.discount_duration)
        self.assertIsNone(discount_code.amount_saas)
        self.assertIsNone(discount_code.frac_saas)
        self.assertIsNone(discount_code.amount_staffer_saas)
        self.assertIsNone(discount_code.frac_staffer_saas)

    def test_form__ok__new_providers__with_discount_duration(self):
        baker.make(
            BillingProductOfferItem,
            product__product_type=ProductType.SAAS,
            active=True,
            discount_duration=5,
            offer=self.offer,
        )

        form = BillingDiscountCodeForm(data=self.get_data(for_new_providers=True))
        self.assertTrue(form.is_valid())
        discount_code = form.save()
        self.assert_common_part(discount_code)
        self.assertTrue(discount_code.for_new_providers)
        self.assertFalse(discount_code.for_existing_providers)

        self.assertEqual(discount_code.offer, self.offer)

        self.assertIsNone(discount_code.discount_type)
        self.assertEqual(discount_code.discount_duration, 5)
        self.assertIsNone(discount_code.amount_saas)
        self.assertIsNone(discount_code.frac_saas)
        self.assertIsNone(discount_code.amount_staffer_saas)
        self.assertIsNone(discount_code.frac_staffer_saas)

    def test_form__ok__existing_providers__fixed(self):
        form = BillingDiscountCodeForm(data=self.get_data(for_existing_providers=True))
        self.assertTrue(form.is_valid())
        discount_code = form.save()
        self.assert_common_part(discount_code)
        self.assertFalse(discount_code.for_new_providers)
        self.assertTrue(discount_code.for_existing_providers)
        self.assertIsNone(discount_code.offer)

        self.assertEqual(discount_code.discount_type, DiscountType.FIXED.value)
        self.assertEqual(discount_code.discount_duration, DiscountDuration.THREE.value)
        self.assertEqual(discount_code.amount_saas, 20)
        self.assertIsNone(discount_code.frac_saas)
        self.assertEqual(discount_code.amount_staffer_saas, 10)
        self.assertIsNone(discount_code.frac_staffer_saas)

    def test_form__ok__existing_providers__percentage(self):
        form = BillingDiscountCodeForm(
            data=self.get_data(for_existing_providers=True, discount_type=DiscountType.PERCENTAGE)
        )

        self.assertTrue(form.is_valid())
        discount_code = form.save()
        self.assert_common_part(discount_code)
        self.assertFalse(discount_code.for_new_providers)
        self.assertTrue(discount_code.for_existing_providers)

        self.assertIsNone(discount_code.offer)

        self.assertEqual(discount_code.discount_type, DiscountType.PERCENTAGE.value)
        self.assertEqual(discount_code.discount_duration, DiscountDuration.THREE.value)
        self.assertIsNone(discount_code.amount_saas)
        self.assertEqual(discount_code.frac_saas, 0.2)
        self.assertIsNone(discount_code.amount_staffer_saas)
        self.assertEqual(discount_code.frac_staffer_saas, 0.1)

    def test_form__ok__new_providers__inactive_offer(self):
        self.offer.active = False
        self.offer.save()
        form = BillingDiscountCodeForm(data=self.get_data(for_new_providers=True))
        self.assertFalse(form.is_valid())

    def test_form__invalid__no_providers(self):
        form = BillingDiscountCodeForm(
            data=self.get_data(
                for_new_providers=False,
                for_existing_providers=False,
            )
        )
        self.assertFalse(form.is_valid())
        self.assertIn(
            'No target group to apply discounts '
            '(please choose at least one from new and existing providers checkbox',
            form.errors.get('__all__'),
        )

    def test_form__invalid__missing_data(self):
        form = BillingDiscountCodeForm(
            data=dict(
                for_new_providers=True,
                for_existing_providers=True,
            )
        )
        self.assertFalse(form.is_valid())
        self.assertIn('valid_from', form.errors)
        self.assertIn('valid_to', form.errors)
        self.assertIn('max_usage_limit', form.errors)
        self.assertIn('name', form.errors)
        self.assertIn(
            'Need to select active offer for new providers discount code',
            form.errors.get('offer'),
        )
        self.assertIn(
            'Need to pass at least one of discount_value_saas / discount_value_staffer_saas',
            form.errors.get('discount_value_saas'),
        )
        self.assertIn(
            'Need to pass at least one of discount_value_saas / discount_value_staffer_saas',
            form.errors.get('discount_value_staffer_saas'),
        )
        self.assertIn(
            'Need to choose Discount type if discount code is applicable for existing providers',
            form.errors.get('discount_type'),
        )

    def test_form__discount_value_saas_none(self):
        data = self.get_data(
            for_new_providers=False,
            for_existing_providers=True,
            discount_type=DiscountType.PERCENTAGE,
        )
        data.update(
            discount_value_saas=None,
        )
        form = BillingDiscountCodeForm(data)
        self.assertTrue(form.is_valid())

    def test_form__invalid__add_same_discount_code(self):
        baker.make(
            BillingDiscountCode,
            discount_code='TEST.CODE.123',
            active=True,
        )

        data = self.get_data(for_new_providers=True, for_existing_providers=True)
        data['discount_code'] = 'TEST.CODE.123'
        form = BillingDiscountCodeForm(
            data=data,
        )
        self.assertFalse(form.is_valid())
        self.assertIn(
            'Same active discount code already exists! Choose different code.',
            form.errors.get('discount_code'),
        )

    def test_form__invalid__cannot_activate_same_discount_code(self):
        baker.make(
            BillingDiscountCode,
            discount_code='TEST.CODE.123',
            active=True,
        )
        discount_code_second = baker.make(
            BillingDiscountCode,
            discount_code='TEST.CODE.123',
            active=False,
        )

        form = BillingDiscountCodeForm(
            instance=discount_code_second,
            data=dict(
                active=True,
            ),
        )
        self.assertFalse(form.is_valid())
        self.assertIn(
            'Same active discount code already exists! Choose different code.',
            form.errors.get('discount_code'),
        )

    @patch('webapps.billing.forms.align_discount_field')
    def test_form__initialization_from_instance(
        self,
        align_discount_field_mock,
    ):
        discount_code_fixed = baker.make(
            BillingDiscountCode,
            discount_code='TEST.CODE.123.FIXED',
            name='test code here!',
            valid_from=tznow(),
            valid_to=tznow() + timedelta(days=7),
            max_usage_limit=50,
            for_new_providers=True,
            for_existing_providers=True,
            offer=self.offer,
            discount_type=DiscountType.FIXED,
            discount_duration=DiscountDuration.FIVE,
            amount_saas=20,
            amount_staffer_saas=10,
        )
        form_fixed = BillingDiscountCodeForm(
            instance=discount_code_fixed,
        )
        self.assertEqual(form_fixed.initial.get('discount_code'), 'TEST.CODE.123.FIXED')
        self.assertTrue(form_fixed.initial.get('for_new_providers'))
        self.assertTrue(form_fixed.initial.get('for_existing_providers'))
        self.assertEqual(form_fixed.initial.get('valid_from'), tznow())
        self.assertEqual(form_fixed.initial.get('valid_to'), tznow() + timedelta(days=7))
        self.assertEqual(form_fixed.initial.get('name'), 'test code here!')
        self.assertEqual(form_fixed.initial.get('offer'), self.offer.pk)
        self.assertEqual(form_fixed.initial.get('discount_type'), DiscountType.FIXED)
        self.assertEqual(form_fixed.initial.get('discount_duration'), DiscountDuration.FIVE)
        self.assertEqual(form_fixed.initial.get('discount_value_saas'), Decimal('20'))
        self.assertEqual(form_fixed.initial.get('discount_value_staffer_saas'), Decimal('10'))

        discount_code_percentage = baker.make(
            BillingDiscountCode,
            discount_code='TEST.CODE.123.PCT',
            name='test code here!',
            valid_from=tznow(),
            valid_to=tznow() + timedelta(days=7),
            max_usage_limit=50,
            for_new_providers=True,
            for_existing_providers=True,
            offer=self.offer,
            discount_type=DiscountType.PERCENTAGE,
            discount_duration=DiscountDuration.FIVE,
            frac_saas=0.05,
            frac_staffer_saas=0.03,
        )
        form_percentage = BillingDiscountCodeForm(
            instance=discount_code_percentage,
        )
        self.assertEqual(form_percentage.initial.get('discount_code'), 'TEST.CODE.123.PCT')
        self.assertTrue(form_percentage.initial.get('for_new_providers'))
        self.assertTrue(form_percentage.initial.get('for_existing_providers'))
        self.assertEqual(form_percentage.initial.get('valid_from'), tznow())
        self.assertEqual(form_percentage.initial.get('valid_to'), tznow() + timedelta(days=7))
        self.assertEqual(form_percentage.initial.get('name'), 'test code here!')
        self.assertEqual(form_percentage.initial.get('offer'), self.offer.pk)
        self.assertEqual(form_percentage.initial.get('discount_type'), DiscountType.PERCENTAGE)
        self.assertEqual(form_percentage.initial.get('discount_duration'), DiscountDuration.FIVE)
        self.assertEqual(form_percentage.initial.get('discount_value_saas'), Decimal('5'))
        self.assertEqual(form_percentage.initial.get('discount_value_staffer_saas'), Decimal('3'))

        # test if fields were aligned properly
        self.assertEqual(align_discount_field_mock.call_count, 4)
        self.assertIn(
            call(form_fixed.fields['discount_value_saas'], DiscountType.FIXED),
            align_discount_field_mock.mock_calls,
        )

        self.assertIn(
            call(form_fixed.fields['discount_value_staffer_saas'], DiscountType.FIXED),
            align_discount_field_mock.mock_calls,
        )
        self.assertIn(
            call(form_percentage.fields['discount_value_saas'], DiscountType.PERCENTAGE),
            align_discount_field_mock.mock_calls,
        )

        self.assertIn(
            call(form_percentage.fields['discount_value_staffer_saas'], DiscountType.PERCENTAGE),
            align_discount_field_mock.mock_calls,
        )

    def test_from__can_edit_some_fields_only(self):
        discount_code = baker.make(
            BillingDiscountCode,
            discount_code='TEST.CODE.123',
            name='test code here!',
            valid_from=tznow(),
            valid_to=tznow() + timedelta(days=7),
            max_usage_limit=50,
            for_new_providers=True,
            for_existing_providers=True,
            offer=self.offer,
            discount_type=DiscountType.FIXED,
            discount_duration=DiscountDuration.FIVE.value,
            amount_saas=20,
            amount_staffer_saas=10,
        )
        form = BillingDiscountCodeForm(
            instance=discount_code,
            data=dict(
                discount_code='TEST.CODE.123.NEW',
                name='new test code coming!',
                valid_from=tznow() + timedelta(days=5),
                valid_to=tznow() + timedelta(days=12),
                max_usage_limit=100,
                for_new_providers=False,
                for_existing_providers=True,
                offer=baker.make(BillingProductOffer, default=True),
                discount_type='P',
                discount_duration='7 bc',
                discount_value_saas=50,
                discount_value_staffer_saas=65,
            ),
        )
        self.assertTrue(form.is_valid())
        form.save()
        discount_code.refresh_from_db()

        self.assertEqual(discount_code.discount_code, 'TEST.CODE.123')
        self.assertEqual(discount_code.valid_from, tznow() + timedelta(days=5))
        self.assertEqual(discount_code.valid_to, tznow() + timedelta(days=12))
        self.assertEqual(discount_code.max_usage_limit, 100)
        self.assertEqual(discount_code.name, 'new test code coming!')

        self.assertTrue(discount_code.for_new_providers)
        self.assertTrue(discount_code.for_existing_providers)
        self.assertEqual(discount_code.offer, self.offer)

        self.assertEqual(discount_code.discount_type, DiscountType.FIXED.value)
        self.assertEqual(discount_code.discount_duration, DiscountDuration.FIVE.value)
        self.assertEqual(discount_code.amount_saas, 20)
        self.assertIsNone(discount_code.frac_saas)
        self.assertEqual(discount_code.amount_staffer_saas, 10)
        self.assertIsNone(discount_code.frac_staffer_saas)

    def test_changelist_works(self):
        url = reverse('admin:billing_billingdiscountcode_changelist')

        baker.make(BillingDiscountCode, discount_code='TEST.CODE.123')

        resp = self.client.get(url, follow=True)
        self.assertContains(resp, 'TEST.CODE.123')

    def test_edit_discount_code_works(self):
        discount_code = baker.make(BillingDiscountCode, discount_code='TEST.CODE.123')

        url = reverse('admin:billing_billingdiscountcode_change', args=(discount_code.pk,))
        resp = self.client.get(url, follow=True)
        self.assertContains(resp, 'Save</button>')

    def test_delete__code_not_used(self):
        discount_code = baker.make(BillingDiscountCode, discount_code='TEST.CODE.123')

        url = reverse('admin:billing_billingdiscountcode_change', args=(discount_code.pk,))
        resp = self.client.get(url)
        url_delete = reverse('admin:billing_billingdiscountcode_delete', args=(discount_code.pk,))
        self.assertContains(
            resp,
            url_delete,
        )

        # make sure that we do only soft_delete
        self.client.post(url_delete, data={'post': 'yes'}, follow=True)
        discount_code.refresh_from_db()
        self.assertIsNotNone(discount_code.deleted)
        self.assertFalse(discount_code.active)

    def test_delete__code_used(self):
        discount_code = baker.make(BillingDiscountCode, discount_code='TEST.CODE.123')
        baker.make(
            BillingDiscountCodeUsage,
            discount_code=discount_code,
        )

        url = reverse('admin:billing_billingdiscountcode_change', args=(discount_code.pk,))
        resp = self.client.get(url, follow=True)
        url_delete = reverse('admin:billing_billingdiscountcode_delete', args=(discount_code.pk,))
        self.assertNotContains(
            resp,
            url_delete,
        )
        # make sure that we cannot delete used discount_code
        resp = self.client.post(
            url_delete,
            data={'post': 'yes'},
            follow=True,
        )
        self.assertEqual(resp.status_code, 403)
        discount_code.refresh_from_db()
        self.assertIsNone(discount_code.deleted)
        self.assertTrue(discount_code.active)
