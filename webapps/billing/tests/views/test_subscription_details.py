from datetime import datetime
from decimal import Decimal

import pytest
from django.test import override_settings
from django.urls import reverse
from model_bakery import baker
from rest_framework import status

from drf_api.service.tests.base import AuthenticatedBusinessAPITestCase
from lib.tools import UTC
from webapps.billing.enums import DiscountType, ProductType, SubscriptionStatus
from webapps.billing.models import (
    BillingCycle,
    BillingProduct,
    BillingProductOffer,
    BillingProductOfferItem,
    BillingSubscribedProduct,
    BillingSubscription,
)
from webapps.business.baker_recipes import business_recipe
from webapps.business.enums import AutoCancellationReasonType
from webapps.business.models.models import Business, CancellationReason, Resource
from webapps.session.utils import get_user
from webapps.user.baker_recipes import user_recipe


class TestSubscriptionDetails(AuthenticatedBusinessAPITestCase):
    def setup(self):
        super().setup()
        self.business.has_new_billing = True
        self.business.save()
        self._create_offer()
        self._create_subscription()

    def _create_offer(self):
        self.postpaid_sms = baker.make(
            BillingProduct,
            product_type=ProductType.POSTPAID_SMS,
            unit_price=Decimal('0.01'),
        )
        self.saas = baker.make(
            BillingProduct,
            product_type=ProductType.SAAS,
            sms_add_on_id=self.postpaid_sms.id,
            name='Best plan ever',
            unit_price=Decimal('149.99'),
            currency='USD',
        )
        self.busy = baker.make(
            BillingProduct,
            product_type=ProductType.BUSY,
            unit_price=Decimal('200.00'),
        )
        self.offer = baker.make(BillingProductOffer)
        baker.make(
            BillingProductOfferItem,
            offer_id=self.offer.id,
            product_id=self.saas.id,
            discount_type=DiscountType.FIXED,
            discount_amount=Decimal('50.00'),
            discount_duration=3,
            active=True,
        )
        baker.make(
            BillingProductOfferItem,
            offer_id=self.offer.id,
            product_id=self.postpaid_sms.id,
            active=True,
        )
        baker.make(
            BillingProductOfferItem,
            offer_id=self.offer.id,
            product_id=self.busy.id,
            discount_type=DiscountType.NO_DISCOUNT,
            active=True,
        )

    def _create_subscription(self):
        self.subscription = baker.make(
            BillingSubscription,
            business_id=self.business.id,
            offer_id=self.offer.id,
            date_start=datetime(2021, 1, 20, tzinfo=UTC),
            next_billing_date=datetime(2021, 3, 20, tzinfo=UTC),
            paid_through_date=datetime(2021, 3, 20, tzinfo=UTC),
            currency='USD',
            balance=Decimal(0),
            status=SubscriptionStatus.ACTIVE,
        )
        self.billing_cycle = baker.make(
            BillingCycle,
            subscription_id=self.subscription.id,
            business_id=self.business.id,
            date_start=datetime(2021, 2, 20, tzinfo=UTC),
            date_end=datetime(2021, 3, 20, tzinfo=UTC),
        )
        self.subscribed_sms = baker.make(
            BillingSubscribedProduct,
            subscription_id=self.subscription.id,
            business_id=self.business.id,
            product_id=self.postpaid_sms.id,
            product_type=ProductType.POSTPAID_SMS,
            date_start=self.subscription.date_start,
            unit_price=Decimal('0.01'),
            currency='USD',
            quantity=0,
        )
        self.subscribed_saas = baker.make(
            BillingSubscribedProduct,
            subscription_id=self.subscription.id,
            business_id=self.business.id,
            product_id=self.saas.id,
            product_type=ProductType.SAAS,
            quantity=1,
            sms_add_on_id=self.postpaid_sms.id,
            date_start=self.subscription.date_start,
            final_price=Decimal('100.0'),
            name='Best plan ever',
            currency='USD',
        )

    @override_settings(BILLING_ALLOW_AUTO_SUBSCRIPTION_CANCELLATION=True)
    @pytest.mark.freeze_time(datetime(2021, 3, 15, 12, tzinfo=UTC))
    def test_get(self):
        self._create_offer()
        self._create_subscription()
        cancelation_reason = baker.make(
            CancellationReason,
            business_id=self.business.id,
            business_cancellation_reason=AutoCancellationReasonType.BUSINESS_CHANGE,
            churn_done=False,
            cancellation_date=datetime(2022, 3, 15, 12, tzinfo=UTC),
        )
        url = reverse('subscription_details', args=(self.business.id,))

        response = self.client.get(url, **self.headers)

        self.assertEqual(response.status_code, 200)
        data = response.data
        self.assertEqual(data['id'], self.subscription.id)
        self.assertEqual(data['offer_id'], self.offer.id)
        self.assertEqual(data['status'], SubscriptionStatus.ACTIVE)
        self.assertEqual(data['status_repr'], 'Active')
        self.assertEqual(data['staff_count'], 1)
        self.assertEqual(data['next_billing_date'], '2021-03-20T00:00:00Z')
        self.assertEqual(data['subscription_period'], 1)
        self.assertFalse(data['is_long_subscription'])
        self.assertFalse(data['can_cancel'])
        self.assertEqual(data['cancellation_date'], cancelation_reason.cancellation_date)

        self.assertIn('products', data)
        self.assertIn('discount_codes', data)
        products = data['products']
        self.assertIn(ProductType.SAAS, products)
        # Postpaid sms add-on is merged with saas
        self.assertNotIn(ProductType.POSTPAID_SMS, products)
        # Busy is not subscribed but still can be purchased
        self.assertIn(ProductType.BUSY, products)
        saas = products[ProductType.SAAS.value][0]
        busy = products[ProductType.BUSY.value][0]
        # Other tests are in TestSubscribedProductsSerializer and
        # TestProductOfferItemsSerializer classes
        self.assertTrue(saas['is_subscribed'])
        self.assertEqual(saas['product_id'], self.saas.id)
        self.assertFalse(busy['is_subscribed'])
        self.assertEqual(busy['product_id'], self.busy.id)

    @override_settings(BILLING_ALLOW_AUTO_SUBSCRIPTION_CANCELLATION=True)
    @pytest.mark.freeze_time(datetime(2021, 3, 15, 12, tzinfo=UTC))
    def test_get_can_cancel_with_more_than_one_staffer(self):
        self._create_offer()
        self._create_subscription()
        baker.make(
            Resource,
            business_id=self.business.id,
            type=Resource.STAFF,
            active=True,
        )
        url = reverse('subscription_details', args=(self.business.id,))

        response = self.client.get(url, **self.headers)

        self.assertEqual(response.status_code, 200)
        self.assertFalse(response.data['can_cancel'])

    @override_settings(BILLING_ALLOW_AUTO_SUBSCRIPTION_CANCELLATION=True)
    @pytest.mark.freeze_time(datetime(2021, 3, 15, 12, tzinfo=UTC))
    def test_get_can_cancel(self):
        self._create_offer()
        self._create_subscription()
        url = reverse('subscription_details', args=(self.business.id,))

        response = self.client.get(url, **self.headers)

        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.data['can_cancel'])

    @override_settings(BILLING_ALLOW_AUTO_SUBSCRIPTION_CANCELLATION=True)
    @pytest.mark.freeze_time(datetime(2021, 3, 15, 12, tzinfo=UTC))
    def test_get_can_cancel_business_with_status_overdue(self):
        self.business.status = Business.Status.OVERDUE
        self.business.save()
        self._create_offer()
        self._create_subscription()
        url = reverse('subscription_details', args=(self.business.id,))

        response = self.client.get(url, **self.headers)

        self.assertEqual(response.status_code, 200)
        self.assertFalse(response.data['can_cancel'])

    @override_settings(BILLING_ALLOW_AUTO_SUBSCRIPTION_CANCELLATION=True)
    @pytest.mark.freeze_time(datetime(2021, 3, 15, 12, tzinfo=UTC))
    def test_get_long_subscription(self):
        self._create_offer()
        self._create_subscription()
        self.subscription.subscription_duration = 3
        self.subscription.date_end = datetime(2021, 4, 20, tzinfo=UTC)
        self.subscription.save()
        url = reverse('subscription_details', args=(self.business.id,))

        response = self.client.get(url, **self.headers)

        self.assertEqual(response.status_code, 200)
        data = response.data
        self.assertEqual(data['id'], self.subscription.id)
        self.assertEqual(data['offer_id'], self.offer.id)
        self.assertEqual(data['status'], SubscriptionStatus.ACTIVE)
        self.assertEqual(data['status_repr'], 'Active')
        self.assertEqual(data['staff_count'], 1)
        self.assertEqual(data['next_billing_date'], datetime(2021, 4, 20, tzinfo=UTC))
        self.assertEqual(data['is_long_subscription'], True)
        self.assertEqual(data['subscription_period'], 3)
        self.assertFalse(data['can_cancel'])
        self.assertIsNone(data['cancellation_date'])

        self.assertIn('products', data)
        self.assertIn('discount_codes', data)
        products = data['products']
        self.assertIn(ProductType.SAAS, products)
        self.assertNotIn(ProductType.POSTPAID_SMS, products)
        self.assertIn(ProductType.BUSY, products)
        saas = products[ProductType.SAAS.value][0]
        busy = products[ProductType.BUSY.value][0]
        # Other tests are in TestSubscribedProductsSerializer and
        # TestProductOfferItemsSerializer classes
        self.assertTrue(saas['is_subscribed'])
        self.assertEqual(saas['product_id'], self.subscribed_saas.product_id)
        self.assertEqual(saas['total_final_price'], '100.00')
        self.assertFalse(busy['is_subscribed'])
        self.assertEqual(busy['product_id'], self.busy.id)

    @override_settings(BILLING_ALLOW_AUTO_SUBSCRIPTION_CANCELLATION=True)
    @pytest.mark.freeze_time(datetime(2021, 1, 18, 12, tzinfo=UTC))
    def test_get_pending(self):
        self._create_offer()
        self._create_subscription()
        self.subscription.status = SubscriptionStatus.PENDING
        self.subscription.save()
        BillingCycle.all_objects.all().delete()
        url = reverse('subscription_details', args=(self.business.id,))

        response = self.client.get(url, **self.headers)

        self.assertEqual(response.status_code, 200)
        data = response.data
        self.assertEqual(data['id'], self.subscription.id)
        self.assertEqual(data['offer_id'], self.offer.id)
        self.assertEqual(data['status'], SubscriptionStatus.PENDING)
        self.assertEqual(data['status_repr'], 'Pending')
        self.assertEqual(data['staff_count'], 1)
        self.assertEqual(data['next_billing_date'], '2021-03-20T00:00:00Z')
        self.assertFalse(data['can_cancel'])
        self.assertIsNone(data['cancellation_date'])

        self.assertIn('products', data)
        products = data['products']
        self.assertIn(ProductType.SAAS, products)
        # Postpaid sms add-on is merged with saas
        self.assertNotIn(ProductType.POSTPAID_SMS, products)
        # Busy is not subscribed but still can be purchased
        self.assertIn(ProductType.BUSY, products)
        saas = products[ProductType.SAAS.value][0]
        busy = products[ProductType.BUSY.value][0]
        # Other tests are in TestSubscribedProductsSerializer and
        # TestProductOfferItemsSerializer classes
        self.assertTrue(saas['is_subscribed'])
        self.assertEqual(saas['product_id'], self.saas.id)
        self.assertFalse(busy['is_subscribed'])
        self.assertEqual(busy['product_id'], self.busy.id)

    @override_settings(BILLING_ALLOW_AUTO_SUBSCRIPTION_CANCELLATION=False)
    @pytest.mark.freeze_time(datetime(2021, 1, 21, 12, tzinfo=UTC))
    def test_get_with_settings_off(self):
        self._create_offer()
        self._create_subscription()
        url = reverse('subscription_details', args=(self.business.id,))
        response = self.client.get(url, **self.headers)

        self.assertEqual(response.status_code, 200)
        data = response.data
        self.assertFalse(data['can_cancel'])
        self.assertIsNone(data['cancellation_date'])

    def test_get_no_subscription(self):
        url = reverse('subscription_details', args=(self.business.id,))

        response = self.client.get(url, **self.headers)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.json()), 0)

    def test_get__not_authenticated(self):
        self.session.delete()
        get_user.clear_from_cache(user_id=self.user.id)
        business = business_recipe.make(has_new_billing=True)
        url = reverse('subscription_details', args=(business.id,))

        response = self.client.get(url, **self.headers)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_get_not_authorized(self):
        self.business.owner = user_recipe.make()
        self.business.save()
        self.resource.staff_access_level = Resource.STAFF_ACCESS_LEVEL_STAFF
        self.resource.save()

        url = reverse('subscription_details', args=(self.business.id,))
        response = self.client.get(url, **self.headers)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
