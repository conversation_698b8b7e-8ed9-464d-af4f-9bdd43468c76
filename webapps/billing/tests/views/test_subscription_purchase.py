from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal
from unittest.mock import MagicMock, patch

from django.test import TestCase
from django.urls import reverse
from model_bakery import baker
from parameterized import parameterized
from pytz import UTC
from rest_framework import status

from drf_api.service.tests.base import AuthenticatedBusinessAPITestCase
from lib.tools import tznow
from webapps.billing.enums import PaymentProcessorType, ProductType
from webapps.billing.models import (
    BillingBusinessOffer,
    BillingBusinessSettings,
    BillingProduct,
    BillingProductOffer,
    BillingProductOfferItem,
    BillingSubscription,
    SubscriptionPurchaseRequest,
    SubscriptionRetryChargeRequest,
)
from webapps.billing.tasks import cancel_stripe_payment_intent_task
from webapps.business.models import Business
from webapps.navision.baker_recipes import navision_settings_recipe
from webapps.stripe_app.services.charge import PaymentIntentCancel


class TestSubscriptionInitializePurchaseHandler(AuthenticatedBusinessAPITestCase):
    def setUp(self):
        super().setUp()
        navision_settings_recipe.make()
        self.business.has_new_billing = True
        self.business.save()

        self.postpaid_sms = baker.make(
            BillingProduct,
            product_type=ProductType.POSTPAID_SMS,
            unit_price=Decimal('0.01'),
            source=Business.PaymentSource.BRAINTREE_BILLING,
            active=True,
        )
        self.saas = baker.make(
            BillingProduct,
            product_type=ProductType.SAAS,
            sms_add_on_id=self.postpaid_sms.id,
            source=Business.PaymentSource.BRAINTREE_BILLING,
            active=True,
        )
        self.offer = baker.make(BillingProductOffer, default=True)
        baker.make(
            BillingProductOfferItem,
            offer_id=self.offer.id,
            product_id=self.saas.id,
        )
        baker.make(
            BillingProductOfferItem,
            offer_id=self.offer.id,
            product_id=self.postpaid_sms.id,
        )
        self.settings = baker.make(
            BillingBusinessSettings,
            business=self.business,
            payment_processor=PaymentProcessorType.STRIPE,
        )
        self.url = reverse('subscriptions_purchase', args=(self.business.id,))

    @patch('webapps.billing.views.subscription.purchase.initialize_purchase_subscription_task')
    def test_ok(self, initialize_purchase_mock):
        response = self.client.post(
            self.url,
            data={'offer_id': self.offer.id, 'product_ids': [self.saas.id]},
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_202_ACCEPTED)
        self.assertIn('task_id', response.data)

        self.assertEqual(initialize_purchase_mock.delay.call_count, 1)

        initialize_purchase_mock.delay.assert_called_with(
            business_id=self.business.id,
            offer_id=self.offer.id,
            product_ids=[self.saas.id],
            operator_id=self.user.id,
        )

    @patch('webapps.billing.views.subscription.purchase.initialize_purchase_subscription_task')
    def test_ok_with_custom_offer(self, initialize_purchase_mock):
        baker.make(
            BillingSubscription,
            business_id=self.business.id,
            date_expiry=datetime.now(tz=UTC) - timedelta(days=1),
        )
        saas = baker.make(
            BillingProduct,
            product_type=ProductType.SAAS,
            sms_add_on_id=self.postpaid_sms.id,
            source=Business.PaymentSource.BRAINTREE_BILLING,
            active=True,
        )
        offer = baker.make(
            BillingBusinessOffer,
            business=self.business,
            offer=baker.make(
                BillingProductOffer,
                default=False,
            ),
        ).offer
        baker.make(
            BillingProductOfferItem,
            offer_id=offer.id,
            product_id=saas.id,
        )

        response = self.client.post(
            self.url,
            data={'offer_id': offer.id, 'product_ids': [saas.id]},
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_202_ACCEPTED)
        self.assertIn('task_id', response.data)

        self.assertEqual(initialize_purchase_mock.delay.call_count, 1)

        initialize_purchase_mock.delay.assert_called_with(
            business_id=self.business.id,
            offer_id=offer.id,
            product_ids=[saas.id],
            operator_id=self.user.id,
        )

    @patch('webapps.billing.views.subscription.purchase.initialize_purchase_subscription_task')
    def test_no_default_offer(self, initialize_purchase_mock):
        self.offer.default = False
        self.offer.save()

        response = self.client.post(
            self.url,
            data={'offer_id': self.offer.id, 'product_ids': [self.saas.id]},
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Invalid product offer', str(response.data))
        self.assertEqual(initialize_purchase_mock.delay.call_count, 0)

    @patch('webapps.billing.views.subscription.purchase.initialize_purchase_subscription_task')
    def test_wrong_id_of_custom_offer(self, initialize_purchase_mock):
        baker.make(BillingBusinessOffer, business=self.business)

        response = self.client.post(
            self.url,
            data={'offer_id': self.offer.id, 'product_ids': [self.saas.id]},
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Invalid product offer', str(response.data))
        self.assertEqual(initialize_purchase_mock.delay.call_count, 0)

    @patch('webapps.billing.views.subscription.purchase.initialize_purchase_subscription_task')
    def test_has_new_billing_is_false(self, initialize_purchase_mock):
        self.business.has_new_billing = False
        self.business.save()

        response = self.client.post(
            self.url,
            data={'offer_id': self.offer.id, 'product_ids': [self.saas.id]},
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn(
            'You\'re not eligible to purchase this subscription.',
            str(response.data),
        )
        self.assertEqual(initialize_purchase_mock.delay.call_count, 0)

    @parameterized.expand(
        [
            (datetime.now(tz=UTC) - timedelta(days=1),),
            (datetime.now(tz=UTC) + timedelta(days=1),),
        ]
    )
    @patch('webapps.billing.views.subscription.purchase.initialize_purchase_subscription_task')
    def test_has_active_sub_false(self, date_start, initialize_purchase_mock):
        baker.make(
            BillingSubscription,
            business_id=self.business.id,
            date_expiry=None,
            date_start=date_start,
        )
        response = self.client.post(
            self.url,
            data={'offer_id': self.offer.id, 'product_ids': [self.saas.id]},
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn(
            'The business already has an active or pending subscription',
            str(response.data),
        )
        self.assertEqual(initialize_purchase_mock.delay.call_count, 0)

    @patch('webapps.billing.views.subscription.purchase.initialize_purchase_subscription_task')
    def test_unknown_product(self, initialize_purchase_mock):
        response = self.client.post(
            self.url,
            data={'offer_id': self.offer.id, 'product_ids': [self.saas.id, self.saas.id + 99999]},
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(initialize_purchase_mock.delay.call_count, 0)

    @patch('webapps.billing.views.subscription.purchase.initialize_purchase_subscription_task')
    def test_price_eq_0(self, initialize_purchase_mock):
        self.saas.unit_price = 0
        self.saas.save()

        response = self.client.post(
            self.url,
            data={'offer_id': self.offer.id, 'product_ids': [self.saas.id]},
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('Invalid offer price', str(response.data))
        self.assertEqual(initialize_purchase_mock.delay.call_count, 0)

    @patch(
        'webapps.billing.views.subscription.purchase.initialize_purchase_subscription_task',
        new=MagicMock(),
    )
    def test_braintree(self):
        self.settings.payment_processor = PaymentProcessorType.BRAINTREE
        self.settings.save()

        response = self.client.post(
            self.url,
            data={'offer_id': self.offer.id, 'product_ids': [self.saas.id]},
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


class TestSubscriptionFinalizePurchaseHandler(AuthenticatedBusinessAPITestCase):
    def setUp(self):
        super().setUp()
        self.settings = baker.make(
            BillingBusinessSettings,
            business=self.business,
            payment_processor=PaymentProcessorType.STRIPE,
        )

        self.purchase_request = baker.make(
            SubscriptionPurchaseRequest,
            business=self.business,
            purchase_key='tnx_123',
            subscription_params={},
            total_price=Decimal(10),
            currency='USD',
        )
        self.url = reverse('subscriptions_purchase', args=(self.business.id,))

    @patch('webapps.billing.views.subscription.purchase.finalize_purchase_subscription_task')
    def test_ok(self, finalize_purchase_mock):
        response = self.client.put(
            self.url,
            data={'transaction_id': self.purchase_request.purchase_key},
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_202_ACCEPTED)
        self.assertIn('task_id', response.data)

        self.assertEqual(finalize_purchase_mock.delay.call_count, 1)

        finalize_purchase_mock.delay.assert_called_with(
            purchase_request_id=self.purchase_request.id,
            operator_id=self.user.id,
        )

    def test_braintree(self):
        self.settings.payment_processor = PaymentProcessorType.BRAINTREE
        self.settings.save()

        response = self.client.put(
            self.url,
            data={'transaction_id': self.purchase_request.purchase_key},
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    @patch('webapps.billing.views.subscription.purchase.finalize_purchase_subscription_task')
    def test_already_purchased(self, finalize_purchase_mock):
        self.purchase_request.purchased_at = tznow()
        self.purchase_request.save()

        response = self.client.put(
            self.url,
            data={'transaction_id': self.purchase_request.purchase_key},
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn(
            'Your subscription has been activated',
            str(response.data),
        )
        self.assertEqual(finalize_purchase_mock.delay.call_count, 0)

    @patch('webapps.billing.views.subscription.purchase.finalize_purchase_subscription_task')
    def test_wrong_transaction_id(self, finalize_purchase_mock):
        response = self.client.put(
            self.url,
            data={'transaction_id': 'abc'},
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn(
            'Invalid token',
            str(response.data),
        )
        self.assertEqual(finalize_purchase_mock.delay.call_count, 0)

    @patch('webapps.billing.views.subscription.purchase.finalize_purchase_subscription_task')
    def test_no_request_object(self, finalize_purchase_mock):
        self.purchase_request.business = baker.make(Business)
        self.purchase_request.save()

        response = self.client.put(
            self.url,
            data={'transaction_id': self.purchase_request.purchase_key},
            **self.headers,
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn(
            'Invalid token',
            str(response.data),
        )
        self.assertEqual(finalize_purchase_mock.delay.call_count, 0)


class TestSubscriptionRequestManager(TestCase):
    def setUp(self) -> None:
        self.purchase_requests = baker.make(
            SubscriptionPurchaseRequest, subscription_params={}, _quantity=4
        )
        for purchase_request in self.purchase_requests:
            purchase_request.created = tznow() - timedelta(days=4)
            purchase_request.save()

    def test_is_already_canceled(self):
        self.purchase_requests[0].canceled_at = tznow()
        self.purchase_requests[0].save()
        self.assertEqual(SubscriptionPurchaseRequest.objects.abandoned().count(), 3)

    def test_created_in_less_than_one_day(self):
        self.purchase_requests[0].created = tznow()
        self.purchase_requests[0].save()
        self.assertEqual(SubscriptionPurchaseRequest.objects.abandoned().count(), 3)

    def test_attempted_to_cancel(self):
        self.purchase_requests[0].attempted_to_cancel = True
        self.purchase_requests[0].save()
        self.assertEqual(SubscriptionPurchaseRequest.objects.abandoned().count(), 3)

    def test_used(self):
        self.purchase_requests[0].used_at = tznow()
        self.purchase_requests[0].save()
        self.assertEqual(SubscriptionPurchaseRequest.objects.abandoned().count(), 3)


class TestCancelStripePaymentIntentTask(TestCase):
    def setUp(self) -> None:
        self.retry_charge = baker.make(
            SubscriptionRetryChargeRequest,
            attempted_to_cancel=False,
        )
        self.purchase_request = baker.make(
            SubscriptionPurchaseRequest,
            attempted_to_cancel=False,
            subscription_params={},
        )

    @patch('webapps.billing.tasks.cancel_payment_intent')
    def test_cancel_retry_charge_request_ok(self, mocked_cancel):
        mocked_cancel.return_value = PaymentIntentCancel(is_success=True)
        cancel_stripe_payment_intent_task.run(
            retry_charge_request_id=self.retry_charge.id,
        )
        self.retry_charge.refresh_from_db()
        self.assertTrue(self.retry_charge.attempted_to_cancel)
        self.assertIsNotNone(self.retry_charge.canceled_at)

    @patch('webapps.billing.tasks.cancel_payment_intent')
    def test_cancel_retry_charge_request_unsuccessful_cancel(self, mocked_cancel):
        mocked_cancel.return_value = PaymentIntentCancel(is_success=False)
        cancel_stripe_payment_intent_task.run(
            retry_charge_request_id=self.retry_charge.id,
        )
        self.retry_charge.refresh_from_db()
        self.assertTrue(self.retry_charge.attempted_to_cancel)
        self.assertIsNone(self.retry_charge.canceled_at)

    @patch('webapps.billing.tasks.cancel_payment_intent')
    def test_cancel_purchase_request_ok(self, mocked_cancel):
        mocked_cancel.return_value = PaymentIntentCancel(is_success=True)
        cancel_stripe_payment_intent_task.run(
            purchase_request_id=self.purchase_request.id,
        )
        self.purchase_request.refresh_from_db()
        self.assertTrue(self.purchase_request.attempted_to_cancel)
        self.assertIsNotNone(self.purchase_request.canceled_at)

    @patch('webapps.billing.tasks.cancel_payment_intent')
    def test_cancel_purchase_request_unsuccessful_cancel(self, mocked_cancel):
        mocked_cancel.return_value = PaymentIntentCancel(is_success=False)
        cancel_stripe_payment_intent_task.run(
            purchase_request_id=self.purchase_request.id,
        )
        self.purchase_request.refresh_from_db()
        self.assertTrue(self.purchase_request.attempted_to_cancel)
        self.assertIsNone(self.purchase_request.canceled_at)

    @patch('webapps.billing.tasks.cancel_payment_intent')
    def test_empty(self, mocked_cancel):
        cancel_stripe_payment_intent_task.run()
        self.assertFalse(mocked_cancel.called)
