import typing as t
from datetime import datetime
from decimal import Decimal

from django.conf import settings
from django.contrib import messages
from django.contrib.auth.mixins import PermissionRequiredMixin
from django.db import transaction
from django.db.models import Q, Sum
from django.db.models.functions import Coalesce
from django.http import Http404, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse
from django.utils.html import format_html
from django.views.generic import DetailView
from pytz import UTC

from lib.tools import tznow
from service.billing.serializers.stripe.payment_method import FinalizePaymentMethodSerializer
from service.tools import json_request
from webapps.admin_extra.custom_permissions_classes import (
    FormView,
    GroupPermissionMixin,
    View,
)
from webapps.billing.cache import get_business_payment_processor
from webapps.billing.enums import (
    BillingRefundStatus,
    PaymentProcessorType,
    ProductType,
    SubscriptionStatus,
    TransactionStatus,
    TransactionSource,
)
from webapps.billing.forms import (
    BillingBusinessSwitchFormWithPaymentSource,
    BillingRefundProductForm,
    BillingSubscriptionCancellationForm,
    BillingSubscriptionForm,
    MigrateStripeTransactionForm,
)
from webapps.billing.models import (
    BillingCycleProductCharge,
    BillingProductOfferItem,
    BillingRefundProduct,
    BillingSubscription,
    BillingTransaction,
)
from webapps.billing.permissions import BillingAdvancedPermissionsNoDelMixin
from webapps.billing.subscription_creator import SubscriptionCreator
from webapps.billing.tasks import (
    finalize_stripe_payment_method_task,
    setup_stripe_payment_method_task,
)
from webapps.business.models import Business
from webapps.stripe_app.locks import StripeAppResourceLock
from webapps.user.groups import GroupName
from webapps.user.tools import get_user_from_django_request


class AddPaymentMethodView(View):
    permission_required = ('billing.add_billingsubscription',)

    def validate_stripe_input(self) -> tuple[dict, dict]:
        serializer = FinalizePaymentMethodSerializer(data=self.data)
        if not serializer.is_valid():
            return {}, serializer.errors
        return serializer.validated_data, {}

    @staticmethod
    def get_business(business_id: int) -> Business:
        return get_object_or_404(Business, id=business_id, has_new_billing=True)

    def get(self, request, business_id: int, *args, **kwargs):
        business = self.get_business(business_id)
        payment_processor = get_business_payment_processor(business_id=business.id)

        match payment_processor:
            case PaymentProcessorType.STRIPE:
                setup_stripe = setup_stripe_payment_method_task.run(business_id=business_id)
                context = {
                    'client_token': setup_stripe.get('user_data', {}).get('token'),
                    'public_api_key': settings.BILLING_STRIPE_APP_CONFIG.publishable_key,
                    'setup_error': setup_stripe.get('internal_errors'),
                    'is_stripe': True,
                }
            case _:
                context = {}

        context.update(
            {
                'business': business,
            }
        )
        return render(request, 'admin/custom_views/add_billing_payment_method.html', context)

    def post_stripe(self, business_id: int) -> JsonResponse:
        validated_data, errors = self.validate_stripe_input()
        if errors:
            return JsonResponse(data=errors, status=400)

        StripeAppResourceLock.try_to_lock(validated_data['payment_id'])

        result = finalize_stripe_payment_method_task.run(
            business_id=business_id,
            stripe_id=validated_data['setup_id'],
            default=validated_data['default'],
        )

        if 'user_data' not in result:
            return JsonResponse(data=result, status=400)
        return JsonResponse(
            data={'payment_method_id': result['user_data']['payment_method_id']},
            status=201,
        )

    @json_request
    def post(self, request, business_id: int, **kwargs) -> JsonResponse:
        business = self.get_business(business_id)
        payment_processor = get_business_payment_processor(business_id=business.id)

        if payment_processor == PaymentProcessorType.STRIPE:
            return self.post_stripe(business_id)
        raise Http404


class BillingRefundTransactionView(View, GroupPermissionMixin):
    form_class = BillingRefundProductForm
    template_name = 'admin/custom_views/generic_form_template.html'
    permission_required = ()
    groups = (
        GroupName.BILLING_SUPERUSER,
        GroupName.BILLING_ADMIN,
        GroupName.BILLING_MANAGER,
        GroupName.BILLING_ADVANCED_USER,
        GroupName.BILLING_USER,
    )

    @staticmethod
    def get_product_charge(billing_cycle_id: int) -> list:
        return (
            BillingCycleProductCharge.objects.filter(
                billing_cycle=billing_cycle_id,
            )
            .annotate(
                amount_refunded=Sum(
                    'refunds__amount',
                    filter=Q(
                        refunds__refund__status__in=[
                            BillingRefundStatus.SUCCEEDED,
                            BillingRefundStatus.PENDING,
                            BillingRefundStatus.INITIAL,
                        ]
                    ),
                ),
                gross_or_final_price=Coalesce('gross_final_price', 'final_price'),
            )
            .values('id', 'product__product_type', 'gross_or_final_price', 'amount_refunded')
        )

    def calculate_products_to_refund(
        self,
        billing_cycle_id: int,
    ) -> dict:
        products_charge = self.get_product_charge(billing_cycle_id)

        product_to_refund = []
        for product in products_charge:
            if not product['gross_or_final_price']:
                continue
            if not product['amount_refunded']:
                product['available_amount_to_refund'] = product['gross_or_final_price']
            else:
                product['available_amount_to_refund'] = (
                    product['gross_or_final_price'] - product['amount_refunded']
                )
            del product['gross_or_final_price']
            del product['amount_refunded']

            if product['available_amount_to_refund'] > Decimal(0):
                product_to_refund.append(product)

        return product_to_refund

    def get_products_to_refund_and_error(self, transaction_id: int) -> t.Tuple[dict, str]:
        error_message = ''

        if not (
            billing_transaction := BillingTransaction.objects.filter(
                status=TransactionStatus.CHARGED,
                transaction_source=TransactionSource.BILLING_SUBSCRIPTION,
                id=transaction_id,
            )
            .annotate(
                amount_refunded=Sum(
                    'refunds__amount',
                    filter=Q(
                        refunds__status__in=[
                            BillingRefundStatus.SUCCEEDED,
                            BillingRefundStatus.PENDING,
                            BillingRefundStatus.INITIAL,
                        ]
                    ),
                ),
            )
            .first()
        ):
            error_message = 'Transaction does not exists, or has wrong status.'

        elif (
            billing_transaction.amount_refunded
            and billing_transaction.amount <= billing_transaction.amount_refunded
        ):
            error_message = 'This transaction is already refunded'

        elif not billing_transaction.billing_cycle_id:
            error_message = 'Unable to match billing cycle with transaction'

        elif (
            billing_transaction.amount_refunded
            and not BillingRefundProduct.objects.filter(
                refund__transaction_id=billing_transaction.id
            ).exists()
        ):
            error_message = (
                'This Transaction was partially refunded without split to products,'
                'please contact with Manager to make refund'
            )

        elif not (
            products_to_refund := self.calculate_products_to_refund(
                billing_transaction.billing_cycle_id
            )
        ):
            error_message = 'Transaction has no product to refund'

        if not error_message:
            return products_to_refund, error_message
        return {}, error_message

    def get(self, request, transaction_id, *args, **kwargs):
        products_to_refund, error_message = self.get_products_to_refund_and_error(transaction_id)

        if error_message:
            messages.error(
                self.request,
                error_message,
            )
            return redirect(reverse('admin:billing_billingtransaction_changelist'))

        form = self.form_class(
            products_to_refund=products_to_refund,
            operator=get_user_from_django_request(self.request),
            transaction_id=transaction_id,
        )

        return render(request, self.template_name, {'form': form})

    def post(self, request, transaction_id, *args, **kwargs):
        products_to_refund, error_message = self.get_products_to_refund_and_error(transaction_id)
        if error_message:
            messages.error(
                request,
                error_message,
            )
            return redirect(reverse('admin:billing_billingtransaction_changelist'))

        form = self.form_class(
            request.POST,
            products_to_refund=products_to_refund,
            operator=get_user_from_django_request(self.request),
            transaction_id=transaction_id,
        )

        if form.is_valid():
            task = form.save()
            task_url = reverse('admin:celery_task_status', args=[task.id])
            message = format_html(  # nosemgrep: formathtml-fstring-parameter
                'Check the result of the task <a href="{}">here</a>.', task_url
            )
            messages.success(request, message)
            return redirect(reverse('admin:billing_billingbusiness_changelist'))

        return render(request, self.template_name, {'form': form})


class AddBillingSubscriptionView(FormView):
    permission_required = ('billing.add_billingsubscription',)
    template_name = 'admin/custom_views/add_billing_subscription.html'
    success_message = 'Subscription {id} added successfully.'
    error_message = 'Subscription not added! Errors: {error}'
    form_class = BillingSubscriptionForm

    def get_form_kwargs(self):
        request = super().get_form_kwargs()
        request['request'] = self.request
        return request

    @staticmethod
    def save_subscription(form, operator):
        business_id = form.cleaned_data['business'].id
        offer_id = form.cleaned_data['offer'].id
        _subscription_start = form.cleaned_data.get('subscription_start')

        if _subscription_start:
            subscription_start = datetime(
                year=_subscription_start.year,
                month=_subscription_start.month,
                day=_subscription_start.day,
                tzinfo=UTC,
            )
            current_subscription = BillingSubscription.get_current_subscription(
                business_id=business_id
            )
        else:
            subscription_start = None
            current_subscription = None

        product_ids = BillingProductOfferItem.objects.filter(
            offer_id=offer_id, product__product_type__in=ProductType.standalone()
        ).values_list('product_id', flat=True)

        params = dict(
            business_id=business_id,
            offer_id=offer_id,
            product_ids=list(product_ids),
            subscription_start=subscription_start,
            operator_id=operator.id,
        )

        if subscription_start and current_subscription:
            # new pending subscription with currently ongoing one
            with transaction.atomic():
                result = SubscriptionCreator.new_purchase(**params)
                sub_id = result.get('subscription_id')
                if sub_id:
                    current_subscription.date_expiry = subscription_start
                    current_subscription.save(
                        update_fields=['date_expiry'],
                        _history={
                            'metadata': {
                                'endpoint': 'admin::new_pending_subscription',
                            },
                            'operator': operator,
                        },
                    )
                    BillingSubscription.objects.filter(
                        pk=sub_id,
                    ).update(
                        previous=current_subscription,
                    )
        else:
            result = SubscriptionCreator.new_purchase(**params)
            sub_id = result.get('subscription_id')
        return result, sub_id

    def post(self, request, *args, **kwargs):
        form = self.get_form()
        if form.is_valid():
            result, sub_id = self.save_subscription(form, get_user_from_django_request(request))

            if sub_id:
                messages.add_message(
                    request, messages.SUCCESS, self.success_message.format(id=sub_id)
                )
                return redirect(reverse('admin:billing_billingsubscription_change', args=(sub_id,)))

            messages.add_message(request, messages.ERROR, self.error_message.format(error=result))
            return redirect(reverse('admin:billing_billingsubscription_changelist'))

        # nor form is valid neither new_purchase is successful
        return self.form_invalid(form)


class MigrateStripeTransactionView(FormView):
    permission_required = ()
    form_class = MigrateStripeTransactionForm
    template_name = 'admin/custom_views/generic_form_template.html'
    success_message = 'Stripe Transaction Migrated'
    success_url = 'admin:billing_billingbusiness_changelist'

    def get_initial(self):
        initial = super().get_initial()
        business_id = self.request.resolver_match.kwargs.get('business_id')
        try:
            Business.objects.get(id=business_id)
        except Business.DoesNotExist:
            raise Http404(f'Business with id {business_id} does not exists') from None
        else:
            initial['business_id'] = business_id
        return initial

    def post(self, request, *args, **kwargs):
        form = self.get_form()
        if form.is_valid():
            business_id = form.save()
            messages.add_message(
                request,
                messages.SUCCESS,
                self.success_message,
            )
            return redirect(reverse('admin:billing_billingbusiness_change', args=(business_id,)))

        return self.form_invalid(form)


class BusinessBillingSwitchView(FormView):
    permission_required = ('billing.add_billingsubscription',)
    form_class = BillingBusinessSwitchFormWithPaymentSource
    template_name = 'admin/custom_views/generic_form_template.html'
    success_message = 'Business switched to {which} billing'
    success_url = 'admin:billing_billingbusiness_changelist'

    def get_initial(self):
        initial = super().get_initial()
        business_id = self.request.resolver_match.kwargs.get('business_id')
        try:
            business = Business.objects.get(id=business_id)
        except Business.DoesNotExist:
            raise Http404(f'business with id {business_id} does not exists') from None
        else:
            initial['business_id'] = business_id
            initial['has_new_billing'] = business.has_new_billing
        return initial

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['operator'] = get_user_from_django_request(self.request)
        return kwargs

    def post(self, request, *args, **kwargs):
        form = self.get_form()
        if form.is_valid():
            business = form.save()
            messages.add_message(
                request,
                messages.SUCCESS,
                self.success_message.format(which=('new' if business.has_new_billing else 'old')),
            )
            return redirect(reverse('admin:billing_billingbusiness_change', args=(business.id,)))

        messages.add_message(
            request,
            messages.ERROR,
            form.errors,
        )
        return self.form_invalid(form)


class BillingSubscriptionCancellationView(
    BillingAdvancedPermissionsNoDelMixin,
    PermissionRequiredMixin,
    DetailView,
):
    template_name = 'admin/custom_views/generic_form_template.html'
    pk_url_kwarg = 'subscription_id'
    queryset = BillingSubscription.objects.all()
    form_class = BillingSubscriptionCancellationForm

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        subscription = kwargs['object']
        context['page_title'] = (
            f'Subscription (ID: {subscription.pk}) cancellation for '
            f'Business: {subscription.business}'
        )
        context['save_button_title'] = 'Cancel subscription'
        context['form'] = self.form_class()
        return context

    def post(self, request, *args, **kwargs):
        instance = self.get_object()
        form = self.form_class(request.POST)
        if not form.is_valid():
            messages.add_message(request, messages.ERROR, form.errors)
            return redirect(request.path)

        cancel_immediately = form.cleaned_data.get('cancel_immediately')

        if instance.date_expiry and (
            not cancel_immediately or instance.is_pending or instance.date_expiry < tznow()
        ):
            messages.add_message(
                request,
                messages.ERROR,
                'Subscription already marked as expired',
            )
        else:
            instance.date_expiry = (
                instance.current_cycle_end
                if not cancel_immediately or instance.is_pending
                else tznow().replace(hour=0, minute=0, second=0, microsecond=0)
            )
            instance.status = SubscriptionStatus.CLOSED
            instance.save(
                update_fields=['date_expiry', 'status'],
                _history={
                    'metadata': {
                        'endpoint': 'admin::billing_subscription_cancellation',
                    },
                    'operator': get_user_from_django_request(request),
                },
            )
            messages.add_message(request, messages.SUCCESS, 'Subscription set as expired')
        return redirect(reverse('admin:billing_billingsubscription_change', args=(instance.id,)))
