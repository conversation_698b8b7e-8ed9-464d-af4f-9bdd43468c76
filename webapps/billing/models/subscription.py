import logging
import typing as t
from datetime import datetime, timed<PERSON>ta
from decimal import ROUND_HALF_EVEN, Decimal

from django.core.validators import MinValueValidator
from django.db import models
from django.db.models import Count, F, Q
from django.utils.functional import cached_property

from lib.interval.fields import IntervalField
from lib.models import ArchiveModel, AutoAddHistoryModel
from lib.tools import tznow
from webapps import consts
from webapps.billing.enums import (
    BillingLongSubscriptionDuration,
    ExtendedDiscountType,
    MerchantMigrationSource,
    MigratedSubscriptionInitialTaskType,
    PaymentActionType,
    ProductType,
    SubscriptionStatus,
    TransactionSource,
    get_default_payment_period,
)
from webapps.billing.models.base import BillingDiscountBase, BillingHistoryModel, BillingProductBase
from webapps.billing.models.managers import (
    ArchiveAndAutoAddHistoryManager,
    AutoUpdateAndAutoAddHistoryManager,
)
from webapps.business.models import Business
from webapps.navision.ports.tax_rates import TaxMatrix
from webapps.notification.models import NotificationSMSStatistics
from webapps.pos.calculations import round_currency

_logger = logging.getLogger('booksy.billing')


class BillingSubscriptionManager(AutoUpdateAndAutoAddHistoryManager):
    @staticmethod
    def to_switch(with_pending_churn_requests: bool = True) -> t.Iterable['BillingSubscription']:
        """
        Fetches subscriptions ready to switch to the next billing cycle.
        """

        now_ = tznow()

        qs = BillingSubscription.objects.filter(
            # Expired subscriptions won't be charged, even if past due
            Q(
                Q(date_expiry__isnull=True) | Q(date_expiry__gt=now_),
            ),
            business__has_new_billing=True,
            date_start__lte=now_,
            next_billing_date__lte=now_,
        )

        if with_pending_churn_requests:
            qs = qs.annotate(
                pending_churn_requests=Count(
                    'business__cancellation_reason',
                    filter=Q(
                        business__cancellation_reason__churn_done=False,
                        business__cancellation_reason__deleted__isnull=True,
                        business__cancellation_reason__cancellation_date__lte=(now_),
                    ),
                )
            )
        return qs.select_related('business')

    @staticmethod
    def to_auto_retry_charge(
        days_since_cycle_change: int,
    ) -> t.Iterable['BillingSubscription']:
        """
        Fetches unpaid subscriptions to re-pay.

        Need to meet:
        - subscription is not expired
        - subscription is BLOCKED
        - subscription balance is greater than 0
        - subscription is the current one:
            - date_start <= NOW
            - next_billing_date >= NOW
        - subscription is in checking window:
            - paid_through_date is equal (NOW - delta_days)

        - business in a new billing (has_new_billing)
        - business status in POA/POB

        - no pending CHURN requests
        - no Braintree transactions in the checked period
        """

        now_ = tznow()

        return (
            BillingSubscription.objects.filter(
                Q(
                    Q(date_expiry__isnull=True) | Q(date_expiry__gt=now_),
                ),
                status=SubscriptionStatus.BLOCKED,
                balance__gt=0,
                date_start__lte=now_,
                next_billing_date__gte=now_,
                paid_through_date__date=(now_ - timedelta(days=days_since_cycle_change)).date(),
            )
            .filter(
                business__has_new_billing=True,
                business__status__in=[
                    Business.Status.OVERDUE,
                    Business.Status.BLOCKED_OVERDUE,
                ],
            )
            .annotate(
                pending_churn_requests=Count(
                    'business__cancellation_reason',
                    filter=Q(
                        business__cancellation_reason__churn_done=False,
                        business__cancellation_reason__deleted__isnull=True,
                    ),
                ),
                transactions_count=Count(
                    'transactions',
                    filter=Q(
                        transactions__transaction_source__in=[
                            TransactionSource.BILLING_SUBSCRIPTION.value
                        ],
                        transactions__created__date=now_.date(),
                    ),
                ),
            )
            .filter(
                pending_churn_requests=0,
                transactions_count=0,
            )
        )

    def active_subscriptions(self):
        now_ = tznow()
        return self.filter(
            Q(date_expiry__isnull=True) | Q(date_expiry__gt=now_), date_start__lte=now_
        )

    def active_and_pending_subscriptions(self):
        now_ = tznow()
        return self.filter(Q(date_expiry__isnull=True) | Q(date_expiry__gt=now_))


class BillingSubscription(AutoAddHistoryModel, ArchiveModel):
    class Meta:
        verbose_name = 'Subscription'
        verbose_name_plural = 'Subscriptions'

    business = models.ForeignKey(
        'business.Business', on_delete=models.PROTECT, related_name='billing_subscriptions'
    )
    offer = models.ForeignKey(
        'billing.BillingProductOffer',
        on_delete=models.PROTECT,
        null=True,
        related_name='+',
    )
    payment_period = IntervalField(
        verbose_name='Payment period (# of months)',
        default=get_default_payment_period,
    )
    status = models.CharField(
        max_length=1,
        choices=SubscriptionStatus.choices(),
    )
    next_billing_date = models.DateTimeField()
    paid_through_date = models.DateTimeField(null=True)
    # Always set to purchase date at midnight, so subscription start
    # will be earlier than first charge and business status change
    date_start = models.DateTimeField()
    date_end = models.DateTimeField(null=True, blank=True)
    date_expiry = models.DateTimeField(null=True)
    balance = models.DecimalField(
        max_digits=consts.PRICE_MAX_DIGITS,
        decimal_places=consts.PRICE_MAX_DECIMALS,
    )
    currency = models.CharField(max_length=3)
    # Optional self-relation pointing on previous subscription
    # (e.g. continuation of PENDING subscriptions)
    previous = models.ForeignKey(
        'self',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        default=None,
    )
    subscription_duration = models.IntegerField(
        verbose_name='Subscription duration (# of months)',
        choices=BillingLongSubscriptionDuration.choices(),
        null=True,
        blank=True,
        default=None,
    )

    objects = BillingSubscriptionManager()

    @cached_property
    def latest_cycle(self):
        return self.billing_cycles.order_by('-date_end').first()

    def get_products(
        self,
        date_start: datetime,
        date_end: datetime,
        **kwargs,
    ):
        """Get subscribed products from time range."""
        products = self.subscribed_products.filter(**kwargs).exclude(
            Q(Q(date_start__gte=date_end) | Q(date_end__lte=date_start))
        )
        return products

    @property
    def is_pending(self):
        return self.date_start > tznow()

    @property
    def has_expired(self):
        return self.date_expiry is not None and self.date_expiry < tznow()

    @property
    def current_cycle_start(self):
        return self.next_billing_date - self.payment_period

    @property
    def current_cycle_end(self):
        return self.next_billing_date

    @property
    def next_cycle_start(self):
        return self.next_billing_date

    @property
    def next_cycle_end(self):
        return self.next_billing_date + self.payment_period

    @property
    def is_long_subscription(self):
        return bool(
            self.date_end
            and self.subscription_duration
            and self.subscription_duration in BillingLongSubscriptionDuration.values()
        )

    @cached_property
    def current_cycle_products(self):
        return self.get_products(
            self.current_cycle_start,
            self.current_cycle_end,
        )

    @cached_property
    def next_cycle_products(self):
        return self.get_products(self.next_billing_date, self.next_cycle_end)

    @classmethod
    def get_current_subscription(cls, business_id):
        now_ = tznow()
        return (
            cls.objects.filter(
                Q(date_expiry__isnull=True) | Q(date_expiry__gt=now_),
                business_id=business_id,
                date_start__lte=now_,
            )
            .order_by('-date_start', '-id')
            .first()
        )

    @classmethod
    def get_future_subscriptions(cls, business_id):
        now_ = tznow()
        return cls.objects.filter(
            Q(date_expiry__isnull=True) | Q(date_expiry__gt=now_),
            business_id=business_id,
            date_start__gt=now_,
        ).exclude(  # cancelled before start
            date_expiry__isnull=False,
            date_start=F('date_expiry'),
        )

    @classmethod
    def get_current_or_pending_subscription(cls, business_id):
        if (subscription := cls.get_current_subscription(business_id)) is None:
            subscription = (
                cls.get_future_subscriptions(
                    business_id,
                )
                .order_by(
                    '-date_start',
                    '-id',
                )
                .first()
            )
        return subscription

    @property
    def can_be_recharged(self):
        return all(
            [
                not self.has_expired,
                self.status == SubscriptionStatus.BLOCKED,
                self.balance > 0,
            ]
        )

    @property
    def can_cancel(self):
        if self.is_pending or self.is_long_subscription:
            return False
        return True


class BillingSubscriptionHistory(BillingHistoryModel):
    model = models.ForeignKey(
        BillingSubscription,
        on_delete=models.CASCADE,
        related_name='history',
    )


class BillingSubscribedProduct(BillingProductBase, BillingDiscountBase):
    COPIED_FROM_PRODUCT = (
        'product_type',
        'name',
        'unit_price',
        'currency',
        'sms_amount',
        'max_qty',
        'free_staff_qty',
        'duration',
        'auto_renew',
        'payment_period',
        # id only
        'staff_add_on_id',
        'sms_add_on_id',
    )
    COPIED_FROM_OFFER = (
        'discount_type',
        'discount_amount',
        'discount_frac',
        'discount_duration',
    )

    business = models.ForeignKey(
        'business.Business',
        on_delete=models.PROTECT,
    )
    subscription = models.ForeignKey(
        "billing.BillingSubscription",
        on_delete=models.PROTECT,
        related_name='subscribed_products',
    )
    product = models.ForeignKey(
        'billing.BillingProduct',
        on_delete=models.PROTECT,
    )
    # Allow 0, but not include in invoice if quantity == 0.
    # In that way required add-ons are always subscribed, even if no charge
    # is applied.
    quantity = models.PositiveIntegerField()
    date_start = models.DateTimeField()
    date_end = models.DateTimeField(null=True)

    # FK is not needed here because those products should be subscribed too
    # Valid only for SaaS & Busy
    # This is id of BillingProduct, not BillingSubscribedProduct
    staff_add_on_id = models.PositiveIntegerField(null=True)
    # Valid only for SaaS
    sms_add_on_id = models.PositiveIntegerField(null=True)

    # discounted price for quantity == 1
    discounted_price = models.DecimalField(
        max_digits=consts.PRICE_MAX_DIGITS + 1,
        decimal_places=consts.PRICE_MAX_DECIMALS + 1,
    )
    # use both discount types (`ExtendedDiscountType`) at the same time
    discount_type = models.CharField(
        max_length=1,
        choices=ExtendedDiscountType.choices(),
        default=ExtendedDiscountType.NO_DISCOUNT,
    )

    # quantity x unit price, before discounts
    total_price = models.DecimalField(
        max_digits=consts.PRICE_MAX_DIGITS,
        decimal_places=consts.PRICE_MAX_DECIMALS,
    )
    # total discount granted
    discount_granted = models.DecimalField(
        max_digits=consts.PRICE_MAX_DIGITS,
        decimal_places=consts.PRICE_MAX_DECIMALS,
    )
    # quantity x discounted_price
    final_price = models.DecimalField(
        max_digits=consts.PRICE_MAX_DIGITS,
        decimal_places=consts.PRICE_MAX_DECIMALS,
    )

    objects = ArchiveAndAutoAddHistoryManager()

    def round_unit_price(self, price: Decimal):
        if self.product_type == ProductType.POSTPAID_SMS:
            return price.quantize(Decimal('0.0001'), rounding=ROUND_HALF_EVEN)

        return round_currency(price)

    def unit_price_tax_summary(self, tax_matrix: TaxMatrix = None):
        if tax_matrix is None:
            matrix = TaxMatrix.for_business_id(self.business_id)
        else:
            matrix = tax_matrix

        if service := ProductType.as_tax_rate_service(self.product_type):
            if matrix:
                return matrix.tax_from_net(service, self.unit_price)

    def unit_price_gross(self, tax_matrix: TaxMatrix = None):
        if tax_summary := self.unit_price_tax_summary(tax_matrix):
            return self.round_unit_price(tax_summary.gross_price)

        return self.round_unit_price(self.unit_price)

    def total_price_gross(self, tax_matrix: TaxMatrix = None):
        return round_currency(self.unit_price_gross(tax_matrix) * self.quantity)

    def discounted_price_tax_summary(self, tax_matrix: TaxMatrix = None):
        if tax_matrix is None:
            matrix = TaxMatrix.for_business_id(self.business_id)
        else:
            matrix = tax_matrix

        if service := ProductType.as_tax_rate_service(self.product_type):
            if matrix:
                return matrix.tax_from_net(service, self.discounted_price)

        return self.discounted_price

    def discounted_price_gross(self, tax_matrix: TaxMatrix = None):
        if tax_summary := self.discounted_price_tax_summary(tax_matrix):
            return self.round_unit_price(tax_summary.gross_price)

        return self.round_unit_price(self.discounted_price)

    def final_price_gross(self, tax_matrix: TaxMatrix = None):
        return round_currency(self.discounted_price_gross(tax_matrix) * self.quantity)

    @cached_property
    def subscribed_staff_add_on(self):
        if self.staff_add_on_id is not None:
            return BillingSubscribedProduct.objects.filter(
                subscription_id=self.subscription_id,
                product_id=self.staff_add_on_id,
            ).first()

    @cached_property
    def subscribed_sms_add_on(self):
        if self.sms_add_on_id is not None:
            return BillingSubscribedProduct.objects.filter(
                subscription_id=self.subscription_id,
                product_id=self.sms_add_on_id,
            ).first()

    def get_gross_summary(self, tax_matrix: TaxMatrix) -> dict:
        total_gross_price = self.total_price_gross(tax_matrix)
        final_gross_price = self.final_price_gross(tax_matrix)
        gross_discount = total_gross_price - final_gross_price
        return {
            'total_gross_price': total_gross_price,
            'final_gross_price': final_gross_price,
            'gross_discount': gross_discount,
        }

    def get_gross_summary_with_add_ons(
        self,
        staff_count: int,
        tax_matrix: TaxMatrix,
    ) -> dict:
        # Initial values
        price_with_add_ons = self.total_price_gross(tax_matrix)
        final_price_with_add_ons = self.final_price_gross(tax_matrix)

        staff_add_on = self.subscribed_staff_add_on

        if staff_add_on:
            payable_staff_count = staff_add_on.get_payable_staff_count(
                staff_count=staff_count,
            )
            staff_price = staff_add_on.unit_price_gross(tax_matrix) * payable_staff_count
            price_with_add_ons += staff_price

            staff_discounted_price = staff_add_on.discounted_price_gross(tax_matrix)
            final_staff_price = staff_discounted_price * payable_staff_count
            final_price_with_add_ons += final_staff_price

        # Discount granted
        discount_with_add_ons = price_with_add_ons - final_price_with_add_ons

        return {
            'price_with_add_ons_gross': price_with_add_ons,
            'discount_with_add_ons_gross': discount_with_add_ons,
            'final_price_with_add_ons_gross': final_price_with_add_ons,
        }

    def get_summary(self) -> dict:
        return {
            'total_price': self.total_price,
            'final_price': self.final_price,
            'discount': self.total_price - self.final_price,
            'sms_amount': self.sms_amount * self.quantity,
        }

    def get_summary_with_add_ons(
        self,
        staff_count: int,
    ) -> dict:
        """
        Calculates prices & sms allowance for subscribed product, based on
        provided staff count and required add-ons, as if those add-ons were
        included in main product.
        Sms add-on is postpaid that's why only staff add-on is here.
        This method is used ONLY TO DISPLAY prices. We can't use this approach
        during charge, as we want to have main product charges & staff charges
        separated on invoice.
        """
        # Initial values
        price_with_add_ons = self.total_price
        final_price_with_add_ons = self.final_price
        sms_with_add_ons = self.sms_amount * self.quantity

        staff_add_on = self.subscribed_staff_add_on

        if staff_add_on:
            payable_staff_count = staff_add_on.get_payable_staff_count(
                staff_count=staff_count,
            )
            staff_price = staff_add_on.unit_price * payable_staff_count
            price_with_add_ons += staff_price

            sms_with_add_ons += payable_staff_count * staff_add_on.sms_amount

            staff_discounted_price = staff_add_on.discounted_price
            final_staff_price = staff_discounted_price * payable_staff_count
            final_price_with_add_ons += final_staff_price

        # Discount granted
        discount_with_add_ons = price_with_add_ons - final_price_with_add_ons

        return {
            'price_with_add_ons': price_with_add_ons,
            'discount_with_add_ons': discount_with_add_ons,
            'final_price_with_add_ons': final_price_with_add_ons,
            'sms_with_add_ons': sms_with_add_ons,
        }


class BillingSubscribedProductHistory(BillingHistoryModel):
    model = models.ForeignKey(
        BillingSubscribedProduct,
        on_delete=models.CASCADE,
        related_name='history',
    )


class BillingCycle(AutoAddHistoryModel, ArchiveModel):
    business = models.ForeignKey(
        'business.Business',
        on_delete=models.PROTECT,
        related_name='billing_cycles',
    )
    subscription = models.ForeignKey(
        'billing.BillingSubscription',
        on_delete=models.PROTECT,
        related_name='billing_cycles',
    )
    date_start = models.DateTimeField()
    date_end = models.DateTimeField()
    is_open = models.BooleanField(default=True)
    number_of_cycle = models.IntegerField(
        validators=[
            MinValueValidator(1),
        ],
    )
    # Sum from all subscribed products
    sms_allowance = models.PositiveIntegerField(default=0)
    # Sms usage before applying sms_allowance
    sms_usage = models.PositiveIntegerField(default=0)

    objects = ArchiveAndAutoAddHistoryManager()

    @cached_property
    def products(self):
        return self.subscription.get_products(date_start=self.date_start, date_end=self.date_end)

    @property
    def prepaid_products(self):
        prepaid = filter(lambda p: p.is_prepaid, self.products)
        return prepaid

    @property
    def postpaid_products(self):
        postpaid = filter(lambda p: p.is_postpaid, self.products)
        return postpaid

    @property
    def is_closed(self):
        return not self.is_open

    @cached_property
    def current_sms_usage(self):
        date_start = self.date_start.date()
        date_end = (self.date_end - timedelta(days=1)).date()
        return NotificationSMSStatistics.get_parts_count_in_period(
            business_id=self.business_id,
            date_start=date_start,
            date_end=date_end,
        )

    @property
    def current_sms_usage_paid(self):
        return max(0, self.current_sms_usage - self.sms_allowance)

    @property
    def current_limit_free(self):
        return max(self.sms_allowance - self.current_sms_usage, 0)

    @property
    def current_sms_price(self):
        sms_add_on = next((p for p in self.products if p.is_postpaid_sms), None)
        # No discounts for sms - unit price is used
        # TODO: After #71999 is done, we will know what to do in case of no
        #  postpaid sms add-on assigned
        return getattr(sms_add_on, 'unit_price', None)

    @cached_property
    def current_total_sms_price(self):
        try:
            # TODO: After #71999 is done, we will know what to do in case of no
            #  postpaid sms add-on assigned
            return self.current_sms_price * self.current_sms_usage_paid
        except AttributeError:
            return Decimal(0)

    def set_initial_values(self, save=True):
        self.set_sms_allowance(save=False)
        if save:
            self.save(update_fields=['sms_allowance'])

    def set_sms_allowance(self, save=True):
        self.sms_allowance = self.calculate_sms_allowance()
        if save:
            self.save(update_fields=['sms_allowance'])

    def close(self):
        self.is_open = False
        self.sms_usage = self.current_sms_usage
        self.save(
            update_fields=['is_open', 'sms_usage'],
            _history={
                'metadata': {'endpoint': 'BillingCycle.close'},
            },
        )

    def calculate_sms_allowance(self):
        sms_allowance = sum(product.sms_amount * product.quantity for product in self.products)
        return sms_allowance


class BillingCycleHistory(BillingHistoryModel):
    model = models.ForeignKey(
        BillingCycle,
        on_delete=models.CASCADE,
        related_name='history',
    )


class BillingCycleProductCharge(AutoAddHistoryModel, ArchiveModel):
    billing_cycle = models.ForeignKey(
        'billing.BillingCycle',
        on_delete=models.PROTECT,
        related_name='product_charges',
    )
    product = models.ForeignKey(
        'billing.BillingSubscribedProduct',
        on_delete=models.PROTECT,
    )
    # Can be successful or not
    last_transaction = models.ForeignKey(
        'billing.BillingTransaction',
        on_delete=models.PROTECT,
        null=True,
        related_name='products',
    )
    # Copied from product name
    name = models.CharField(max_length=255)

    # In case of prepaid products dates will be coherent with billing cycle
    # dates, in case of postpaid it will be one period back
    usage_from = models.DateTimeField()
    usage_to = models.DateTimeField()

    quantity = models.PositiveIntegerField()
    # 3 decimal places for postpaid SMS, eg. US (0.005)
    unit_price = models.DecimalField(
        max_digits=consts.PRICE_MAX_DIGITS + 1,
        decimal_places=consts.PRICE_MAX_DECIMALS + 1,
    )
    # discounted price for quantity == 1
    # 3 decimal places for postpaid SMS, eg. US (0.005)
    discounted_price = models.DecimalField(
        max_digits=consts.PRICE_MAX_DIGITS + 1,
        decimal_places=consts.PRICE_MAX_DECIMALS + 1,
    )
    # quantity x unit price, before discounts
    total_price = models.DecimalField(
        max_digits=consts.PRICE_MAX_DIGITS,
        decimal_places=consts.PRICE_MAX_DECIMALS,
    )
    discount_granted = models.DecimalField(
        max_digits=consts.PRICE_MAX_DIGITS,
        decimal_places=consts.PRICE_MAX_DECIMALS,
    )
    # quantity x discounted_price
    final_price = models.DecimalField(
        max_digits=consts.PRICE_MAX_DIGITS,
        decimal_places=consts.PRICE_MAX_DECIMALS,
    )
    gross_unit_price = models.DecimalField(
        max_digits=consts.PRICE_MAX_DIGITS + 2,
        decimal_places=consts.PRICE_MAX_DECIMALS + 2,
        null=True,
    )
    gross_discounted_price = models.DecimalField(
        max_digits=consts.PRICE_MAX_DIGITS + 2,
        decimal_places=consts.PRICE_MAX_DECIMALS + 2,
        null=True,
    )
    gross_final_price = models.DecimalField(
        max_digits=consts.PRICE_MAX_DIGITS,
        decimal_places=consts.PRICE_MAX_DECIMALS,
        null=True,
    )
    gross_total_price = models.DecimalField(
        max_digits=consts.PRICE_MAX_DIGITS,
        decimal_places=consts.PRICE_MAX_DECIMALS,
        null=True,
    )
    gross_discount_granted = models.DecimalField(
        max_digits=consts.PRICE_MAX_DIGITS,
        decimal_places=consts.PRICE_MAX_DECIMALS,
        null=True,
    )
    tax_per_unit = models.DecimalField(
        max_digits=consts.PRICE_MAX_DIGITS + 2,
        decimal_places=consts.PRICE_MAX_DECIMALS + 2,
        null=True,
    )
    tax = models.DecimalField(
        max_digits=consts.PRICE_MAX_DIGITS,
        decimal_places=consts.PRICE_MAX_DECIMALS,
        null=True,
    )
    tax_rate = models.DecimalField(
        max_digits=5,
        decimal_places=4,
        null=True,
    )
    tax_additional_data = models.JSONField(
        null=True,
        blank=True,
    )

    currency = models.CharField(max_length=3)

    objects = ArchiveAndAutoAddHistoryManager()

    def __str__(self):
        return self.product.get_product_type_display()

    def refresh_tax_related_data(self, save=True):
        tax_matrix = TaxMatrix.for_business_id(self.product.business_id)
        service = ProductType.as_tax_rate_service(self.product.product_type)
        add_gross_price = service and tax_matrix and service in tax_matrix.tax_rates

        self.tax_additional_data = tax_matrix.get_tax_rate_info(service)

        if add_gross_price:
            unit_price_summary = tax_matrix.tax_from_net(service, self.unit_price)
            discounted_price_summary = tax_matrix.tax_from_net(service, self.discounted_price)

            self.gross_unit_price = unit_price_summary.gross_price
            self.gross_discounted_price = discounted_price_summary.gross_price

            self.gross_final_price = BillingDiscountBase.get_chargeable_amount(
                self.gross_discounted_price * self.quantity
            )
            self.gross_total_price = BillingDiscountBase.get_chargeable_amount(
                self.gross_unit_price * self.quantity
            )

            self.gross_discount_granted = self.gross_total_price - self.gross_final_price

            self.tax_per_unit = discounted_price_summary.tax
            self.tax = BillingDiscountBase.get_chargeable_amount(
                discounted_price_summary.tax * self.quantity
            )

            if discounted_price_summary.tax_rate is not None:
                self.tax_rate = discounted_price_summary.tax_rate.tax_rate
            else:
                self.tax_rate = Decimal('0.0000')
        else:
            self.gross_unit_price = self.unit_price
            self.gross_discounted_price = self.discounted_price
            self.gross_final_price = self.final_price
            self.gross_total_price = self.total_price
            self.gross_discount_granted = self.discount_granted
            self.tax_per_unit = Decimal('0.00')
            self.tax = Decimal('0.00')
            self.tax_rate = Decimal('0.0000')

        if save:
            self.save(
                update_fields=[
                    'gross_unit_price',
                    'gross_discounted_price',
                    'gross_final_price',
                    'gross_total_price',
                    'gross_discount_granted',
                    'tax_per_unit',
                    'tax',
                    'tax_rate',
                    'tax_additional_data',
                ]
            )


class BillingCycleProductChargeHistory(BillingHistoryModel):
    model = models.ForeignKey(
        BillingCycleProductCharge,
        on_delete=models.CASCADE,
        related_name='history',
    )


class SubscriptionRequestManager(AutoUpdateAndAutoAddHistoryManager):
    def abandoned(self):
        return self.filter(
            canceled_at__isnull=True,
            attempted_to_cancel=False,
            used_at__isnull=True,
            created__lt=tznow() - timedelta(days=1),
        )


class SubscriptionPurchaseRequest(ArchiveModel):
    class Meta:
        verbose_name = 'Subscription purchase request'
        verbose_name_plural = 'Subscription purchase requests'

    business = models.ForeignKey(
        'business.Business',
        on_delete=models.PROTECT,
        related_name='billing_subscription_purchase_requests',
    )
    currency = models.CharField(max_length=3)
    subscription_params = models.JSONField()
    total_price = models.DecimalField(
        max_digits=consts.PRICE_MAX_DIGITS,
        decimal_places=consts.PRICE_MAX_DECIMALS,
    )
    action_type = models.CharField(
        max_length=50,
        choices=[(value, value) for value in PaymentActionType.initial_payment_values()],
    )
    purchase_key = models.CharField(max_length=120, unique=True)
    purchased_at = models.DateTimeField(null=True, blank=True)
    attempted_to_cancel = models.BooleanField(default=False)
    canceled_at = models.DateTimeField(null=True, blank=True)
    used_at = models.DateTimeField(null=True, blank=True)
    operator = models.ForeignKey(
        'user.User',
        on_delete=models.DO_NOTHING,
        null=True,
        blank=True,
        db_constraint=False,
    )

    objects = SubscriptionRequestManager()


class SubscriptionRetryChargeRequest(ArchiveModel):
    class Meta:
        verbose_name = 'Subscription retry charge request'
        verbose_name_plural = 'Subscription retry charge requests'

    business = models.ForeignKey(
        'business.Business',
        on_delete=models.PROTECT,
        related_name='billing_subscription_retry_charge_requests',
    )
    subscription = models.ForeignKey(BillingSubscription, on_delete=models.PROTECT)
    billing_cycle = models.ForeignKey(BillingCycle, on_delete=models.PROTECT)
    total_price = models.DecimalField(
        max_digits=consts.PRICE_MAX_DIGITS,
        decimal_places=consts.PRICE_MAX_DECIMALS,
    )
    action_type = models.CharField(
        max_length=50, choices=[(value, value) for value in PaymentActionType.retry_charge_values()]
    )
    purchase_key = models.CharField(max_length=120, unique=True)
    purchased_at = models.DateTimeField(null=True, blank=True)
    attempted_to_cancel = models.BooleanField(default=False)
    canceled_at = models.DateTimeField(null=True, blank=True)
    used_at = models.DateTimeField(null=True, blank=True)
    operator = models.ForeignKey(
        'user.User',
        on_delete=models.DO_NOTHING,
        null=True,
        blank=True,
        db_constraint=False,
    )

    objects = SubscriptionRequestManager()


class MigratedSubscriptionInitialTask(ArchiveModel):
    """
    Models for storing newly created subscription in migration process
    (e.g. from Versum to Booksy) and pending for some initial task (e.g. charge).
    """

    class Meta:
        verbose_name = 'Migrated subscription initial task'
        verbose_name_plural = 'Migrated subscriptions initial tasks'

    subscription = models.OneToOneField(
        BillingSubscription,
        on_delete=models.PROTECT,
    )
    offer = models.ForeignKey(
        'billing.BillingProductOffer',
        on_delete=models.PROTECT,
        blank=True,
        null=True,
    )
    migration_source = models.CharField(
        max_length=50,
        choices=MerchantMigrationSource.choices(),
    )
    initial_task_type = models.CharField(
        max_length=50,
        choices=MigratedSubscriptionInitialTaskType.choices(),
    )
    task_done = models.DateTimeField(blank=True, null=True)
    error_traceback = models.TextField(blank=True, null=True)

    def mark_as_done(self) -> None:
        self.task_done = tznow()
        self.save()
