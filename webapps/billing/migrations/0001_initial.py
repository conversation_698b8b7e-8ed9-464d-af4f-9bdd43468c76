# Generated by Django 3.1.2 on 2021-02-18 15:45

import django.contrib.postgres.fields.jsonb
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import lib.interval.fields
import lib.models
import webapps.billing.enums
import webapps.billing.models
import webapps.business.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('user', '0045_auto_20201127_1051'),
    ]

    operations = [
        migrations.CreateModel(
            name='BillingBusinessSettings',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'sms_cost_alert_level',
                    models.IntegerField(
                        default=0,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name='SMS cost alert level',
                    ),
                ),
                (
                    'business',
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='billing_settings',
                        to='business.business',
                    ),
                ),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BillingCreditCardInfo',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'card_type',
                    models.CharField(
                        blank=True,
                        choices=[
                            ('A', 'American Express'),
                            ('V', 'Visa'),
                            ('M', 'Mastercard'),
                            ('E', 'Maestro'),
                            ('U', 'Unknown'),
                        ],
                        max_length=1,
                        null=True,
                    ),
                ),
                ('cardholder_name', models.CharField(blank=True, max_length=255, null=True)),
                (
                    'first_6_digits',
                    models.CharField(
                        blank=True,
                        max_length=6,
                        null=True,
                        verbose_name='Bank Identification Number (first 6 digits)',
                    ),
                ),
                ('last_4_digits', models.CharField(blank=True, max_length=4, null=True)),
                ('expiration_date', models.DateField(blank=True, null=True)),
                ('country_of_issuance', models.CharField(blank=True, max_length=100, null=True)),
                ('address_line_1', models.CharField(blank=True, max_length=100, null=True)),
                ('address_line_2', models.CharField(blank=True, max_length=100, null=True)),
                ('city', models.CharField(blank=True, max_length=100, null=True)),
                ('zipcode', models.CharField(blank=True, max_length=20, null=True)),
                ('country', models.CharField(blank=True, max_length=100, null=True)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BillingCycle',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('date_start', models.DateTimeField()),
                ('date_end', models.DateTimeField()),
                ('is_open', models.BooleanField(default=True)),
                (
                    'number_of_cycle',
                    models.IntegerField(validators=[django.core.validators.MinValueValidator(1)]),
                ),
                ('sms_allowance', models.PositiveIntegerField(default=0)),
                ('sms_usage', models.PositiveIntegerField(default=0)),
                (
                    'business',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to='business.business'
                    ),
                ),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BillingCycleProductCharge',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('name', models.CharField(max_length=255)),
                ('usage_from', models.DateTimeField()),
                ('usage_to', models.DateTimeField()),
                ('quantity', models.PositiveIntegerField()),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=6)),
                ('discounted_price', models.DecimalField(decimal_places=2, max_digits=6)),
                ('total_price', models.DecimalField(decimal_places=2, max_digits=6)),
                ('discount_granted', models.DecimalField(decimal_places=2, max_digits=6)),
                ('final_price', models.DecimalField(decimal_places=2, max_digits=6)),
                ('currency', models.CharField(max_length=3)),
                (
                    'billing_cycle',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='product_charges',
                        to='billing.billingcycle',
                    ),
                ),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BillingPaymentMethod',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'payment_method_type',
                    models.CharField(
                        choices=[('C', 'CREDIT_CARD'), ('P', 'PAYPAL'), ('V', 'VENMO')],
                        max_length=1,
                    ),
                ),
                (
                    'payment_processor',
                    models.CharField(
                        choices=[
                            ('U', 'Unknown'),
                            ('O', 'Offline'),
                            ('B', 'Braintree'),
                            ('R', 'Braintree (new)'),
                            ('I', 'Apple iTunes'),
                            ('P', 'Google Play'),
                        ],
                        default=webapps.business.models.Business.PaymentSource['BRAINTREE_BILLING'],
                        editable=False,
                        max_length=1,
                    ),
                ),
                ('token', models.CharField(max_length=36)),
                ('default', models.BooleanField(default=False)),
                (
                    'business',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='payment_methods',
                        to='business.business',
                    ),
                ),
                (
                    'credit_card',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='payment_methods',
                        to='billing.billingcreditcardinfo',
                    ),
                ),
            ],
            options={
                'index_together': {('payment_processor', 'token')},
            },
        ),
        migrations.CreateModel(
            name='BillingProduct',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('name', models.CharField(max_length=255)),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=6)),
                ('currency', models.CharField(max_length=3)),
                (
                    'product_type',
                    models.CharField(
                        choices=[
                            ('SA', 'SaaS'),
                            ('SS', 'Staffer SaaS'),
                            ('BU', 'Busy'),
                            ('SB', 'Staffer Busy'),
                            ('PS', 'Postpaid SMS'),
                            ('RS', 'Prepaid SMS'),
                            ('BO', 'Boost'),
                        ],
                        max_length=2,
                    ),
                ),
                (
                    'sms_amount',
                    models.PositiveIntegerField(default=0, verbose_name='Free SMS included'),
                ),
                (
                    'max_qty',
                    models.PositiveIntegerField(
                        blank=True,
                        help_text='If set, product quantity will not exceed this value',
                        null=True,
                        verbose_name='Maximum quantity',
                    ),
                ),
                (
                    'free_staff_qty',
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name='Free staff members count'
                    ),
                ),
                (
                    'payment_period',
                    lib.interval.fields.IntervalField(verbose_name='Payment period (# of months)'),
                ),
                (
                    'duration',
                    models.CharField(
                        choices=[('M', '1 month'), ('I', 'Indefinite')],
                        default=webapps.billing.enums.SubscriptionDuration['INDEFINITE'],
                        max_length=1,
                        verbose_name='Duration of subscription',
                    ),
                ),
                ('auto_renew', models.BooleanField(default=True)),
                ('active', models.BooleanField(default=False)),
                (
                    'source',
                    models.CharField(
                        choices=[
                            ('U', 'Unknown'),
                            ('O', 'Offline'),
                            ('B', 'Braintree'),
                            ('R', 'Braintree (new)'),
                            ('I', 'Apple iTunes'),
                            ('P', 'Google Play'),
                        ],
                        default=webapps.business.models.Business.PaymentSource['BRAINTREE_BILLING'],
                        max_length=1,
                    ),
                ),
                ('external_id', models.CharField(blank=True, max_length=100, null=True)),
                (
                    'sms_add_on',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='+',
                        to='billing.billingproduct',
                        verbose_name='Postpaid SMS price',
                    ),
                ),
                (
                    'staff_add_on',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='+',
                        to='billing.billingproduct',
                        verbose_name='Staff add-on',
                    ),
                ),
            ],
            options={
                'verbose_name': 'Product',
                'verbose_name_plural': 'Products',
            },
        ),
        migrations.CreateModel(
            name='BillingProductOffer',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('name', models.CharField(max_length=255)),
                ('active', models.BooleanField(default=False)),
            ],
            options={
                'verbose_name': 'Product offer',
                'verbose_name_plural': 'Product offers',
            },
        ),
        migrations.CreateModel(
            name='BillingProductOfferItem',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'discount_type',
                    models.CharField(
                        choices=[('X', 'No discount'), ('P', '%'), ('F', 'Fixed amount')],
                        default=webapps.billing.enums.DiscountType['NO_DISCOUNT'],
                        max_length=1,
                    ),
                ),
                (
                    'discount_amount',
                    models.DecimalField(blank=True, decimal_places=2, max_digits=6, null=True),
                ),
                (
                    'discount_frac',
                    models.DecimalField(
                        blank=True,
                        decimal_places=3,
                        max_digits=4,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(1),
                        ],
                    ),
                ),
                (
                    'discount_duration',
                    models.IntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(12),
                        ],
                        verbose_name='Discount duration (# of billing cycles)',
                    ),
                ),
                (
                    'offer',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='products',
                        to='billing.billingproductoffer',
                    ),
                ),
                (
                    'product',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to='billing.billingproduct'
                    ),
                ),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BillingSubscribedProduct',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('name', models.CharField(max_length=255)),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=6)),
                ('currency', models.CharField(max_length=3)),
                (
                    'product_type',
                    models.CharField(
                        choices=[
                            ('SA', 'SaaS'),
                            ('SS', 'Staffer SaaS'),
                            ('BU', 'Busy'),
                            ('SB', 'Staffer Busy'),
                            ('PS', 'Postpaid SMS'),
                            ('RS', 'Prepaid SMS'),
                            ('BO', 'Boost'),
                        ],
                        max_length=2,
                    ),
                ),
                (
                    'sms_amount',
                    models.PositiveIntegerField(default=0, verbose_name='Free SMS included'),
                ),
                (
                    'max_qty',
                    models.PositiveIntegerField(
                        blank=True,
                        help_text='If set, product quantity will not exceed this value',
                        null=True,
                        verbose_name='Maximum quantity',
                    ),
                ),
                (
                    'free_staff_qty',
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name='Free staff members count'
                    ),
                ),
                (
                    'payment_period',
                    lib.interval.fields.IntervalField(verbose_name='Payment period (# of months)'),
                ),
                (
                    'duration',
                    models.CharField(
                        choices=[('M', '1 month'), ('I', 'Indefinite')],
                        default=webapps.billing.enums.SubscriptionDuration['INDEFINITE'],
                        max_length=1,
                        verbose_name='Duration of subscription',
                    ),
                ),
                ('auto_renew', models.BooleanField(default=True)),
                ('quantity', models.PositiveIntegerField()),
                ('date_start', models.DateTimeField()),
                ('date_end', models.DateTimeField(null=True)),
                ('staff_add_on_id', models.PositiveIntegerField(null=True)),
                ('sms_add_on_id', models.PositiveIntegerField(null=True)),
                (
                    'discount_type',
                    models.CharField(
                        choices=[('X', 'No discount'), ('P', '%'), ('F', 'Fixed amount')],
                        default=webapps.billing.enums.DiscountType['NO_DISCOUNT'],
                        max_length=1,
                    ),
                ),
                (
                    'discount_amount',
                    models.DecimalField(blank=True, decimal_places=2, max_digits=6, null=True),
                ),
                (
                    'discount_frac',
                    models.DecimalField(
                        blank=True,
                        decimal_places=3,
                        max_digits=4,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(1),
                        ],
                    ),
                ),
                (
                    'discount_duration',
                    models.IntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(12),
                        ],
                        verbose_name='Discount duration (# of billing cycles)',
                    ),
                ),
                ('discounted_price', models.DecimalField(decimal_places=2, max_digits=6)),
                ('total_price', models.DecimalField(decimal_places=2, max_digits=6)),
                ('discount_granted', models.DecimalField(decimal_places=2, max_digits=6)),
                ('final_price', models.DecimalField(decimal_places=2, max_digits=6)),
                (
                    'business',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to='business.business'
                    ),
                ),
                (
                    'product',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to='billing.billingproduct'
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BillingSubscription',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'payment_period',
                    lib.interval.fields.IntervalField(verbose_name='Payment period (# of months)'),
                ),
                (
                    'status',
                    models.CharField(
                        choices=[
                            ('A', 'Active'),
                            ('B', 'Blocked'),
                            ('S', 'Suspended'),
                            ('U', 'Suspension pending'),
                        ],
                        max_length=1,
                    ),
                ),
                ('next_billing_date', models.DateTimeField()),
                ('paid_through_date', models.DateTimeField(null=True)),
                ('date_start', models.DateTimeField()),
                ('date_expiry', models.DateTimeField(null=True)),
                ('balance', models.DecimalField(decimal_places=2, max_digits=10)),
                ('currency', models.CharField(max_length=3)),
                (
                    'business',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT, to='business.business'
                    ),
                ),
                (
                    'offer',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='+',
                        to='billing.billingproductoffer',
                    ),
                ),
            ],
            options={
                'verbose_name': 'Subscription',
                'verbose_name_plural': 'Subscriptions',
            },
        ),
        migrations.CreateModel(
            name='BillingBusiness',
            fields=[],
            options={
                'verbose_name': 'Billing details',
                'verbose_name_plural': 'Billing details search',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('business.business',),
            managers=[
                ('objects', webapps.business.models.BusinessManager()),
            ],
        ),
        migrations.CreateModel(
            name='BillingTransaction',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'status',
                    models.CharField(
                        choices=[
                            ('P', 'Pending'),
                            ('C', 'Charged'),
                            ('F', 'Failed'),
                            ('X', 'Canceled'),
                            ('R', 'Refunded'),
                        ],
                        max_length=1,
                    ),
                ),
                ('amount', models.DecimalField(decimal_places=2, max_digits=6)),
                ('currency', models.CharField(max_length=3)),
                ('response_code', models.CharField(editable=False, max_length=4, null=True)),
                ('response_text', models.CharField(editable=False, max_length=255, null=True)),
                (
                    'response_type',
                    models.CharField(
                        choices=[('A', 'Approved'), ('S', 'Soft declined'), ('H', 'Hard declined')],
                        max_length=1,
                        null=True,
                    ),
                ),
                ('external_id', models.CharField(db_index=True, max_length=255)),
                ('extra_data', django.contrib.postgres.fields.jsonb.JSONField(default=dict)),
                (
                    'billing_cycle',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='transactions',
                        to='billing.billingcycle',
                    ),
                ),
                (
                    'business',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name='business',
                        to='business.business',
                    ),
                ),
                (
                    'payment_method',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='transactions',
                        to='billing.billingpaymentmethod',
                    ),
                ),
                (
                    'subscription',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='transactions',
                        to='billing.billingsubscription',
                    ),
                ),
            ],
            options={
                'verbose_name': 'Subscription transaction',
                'verbose_name_plural': 'Subscription transactions',
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.CreateModel(
            name='BillingSubscriptionHistory',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                ('data', models.TextField()),
                ('metadata', models.TextField()),
                (
                    'model',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='history',
                        to='billing.billingsubscription',
                    ),
                ),
                (
                    'operator',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to='user.user',
                    ),
                ),
            ],
            options={
                'ordering': ['-created'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BillingSubscribedProductHistory',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                ('data', models.TextField()),
                ('metadata', models.TextField()),
                (
                    'model',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='history',
                        to='billing.billingsubscribedproduct',
                    ),
                ),
                (
                    'operator',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to='user.user',
                    ),
                ),
            ],
            options={
                'ordering': ['-created'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='billingsubscribedproduct',
            name='subscription',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name='subscribed_products',
                to='billing.billingsubscription',
            ),
        ),
        migrations.CreateModel(
            name='BillingProductOfferItemHistory',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                ('data', models.TextField()),
                ('metadata', models.TextField()),
                (
                    'model',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='history',
                        to='billing.billingproductofferitem',
                    ),
                ),
                (
                    'operator',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to='user.user',
                    ),
                ),
            ],
            options={
                'ordering': ['-created'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BillingProductOfferHistory',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                ('data', models.TextField()),
                ('metadata', models.TextField()),
                (
                    'model',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='history',
                        to='billing.billingproductoffer',
                    ),
                ),
                (
                    'operator',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to='user.user',
                    ),
                ),
            ],
            options={
                'ordering': ['-created'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BillingProductHistory',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                ('data', models.TextField()),
                ('metadata', models.TextField()),
                (
                    'model',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='history',
                        to='billing.billingproduct',
                    ),
                ),
                (
                    'operator',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to='user.user',
                    ),
                ),
            ],
            options={
                'ordering': ['-created'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BillingPaymentMethodHistory',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                ('data', models.TextField()),
                ('metadata', models.TextField()),
                (
                    'model',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='history',
                        to='billing.billingpaymentmethod',
                    ),
                ),
                (
                    'operator',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to='user.user',
                    ),
                ),
            ],
            options={
                'ordering': ['-created'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BillingCycleProductChargeHistory',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                ('data', models.TextField()),
                ('metadata', models.TextField()),
                (
                    'model',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='history',
                        to='billing.billingcycleproductcharge',
                    ),
                ),
                (
                    'operator',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to='user.user',
                    ),
                ),
            ],
            options={
                'ordering': ['-created'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='billingcycleproductcharge',
            name='last_transaction',
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name='products',
                to='billing.billingtransaction',
            ),
        ),
        migrations.AddField(
            model_name='billingcycleproductcharge',
            name='product',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT, to='billing.billingsubscribedproduct'
            ),
        ),
        migrations.CreateModel(
            name='BillingCycleHistory',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                ('data', models.TextField()),
                ('metadata', models.TextField()),
                (
                    'model',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='history',
                        to='billing.billingcycle',
                    ),
                ),
                (
                    'operator',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to='user.user',
                    ),
                ),
            ],
            options={
                'ordering': ['-created'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='billingcycle',
            name='subscription',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name='billing_cycles',
                to='billing.billingsubscription',
            ),
        ),
        migrations.CreateModel(
            name='BillingCreditCardVerification',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('active', models.BooleanField(default=True)),
                (
                    'business',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to='business.business',
                    ),
                ),
                (
                    'credit_card',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='verification_attempts',
                        to='billing.billingcreditcardinfo',
                    ),
                ),
                (
                    'payment_method',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to='billing.billingpaymentmethod',
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated',
                'abstract': False,
            },
            managers=[
                ('objects', webapps.billing.models.BillingCreditCardManager()),
            ],
        ),
        migrations.CreateModel(
            name='BillingCreditCardInfoHistory',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                ('data', models.TextField()),
                ('metadata', models.TextField()),
                (
                    'model',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='history',
                        to='billing.billingcreditcardinfo',
                    ),
                ),
                (
                    'operator',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to='user.user',
                    ),
                ),
            ],
            options={
                'ordering': ['-created'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BillingBusinessSettingsHistory',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                ('data', models.TextField()),
                ('metadata', models.TextField()),
                (
                    'model',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='history',
                        to='billing.billingbusinesssettings',
                    ),
                ),
                (
                    'operator',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to='user.user',
                    ),
                ),
            ],
            options={
                'ordering': ['-created'],
                'get_latest_by': 'created',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='BillingBusyProduct',
            fields=[],
            options={
                'verbose_name': '"Busy" product',
                'verbose_name_plural': '"Busy" products',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('billing.billingproduct',),
        ),
        migrations.CreateModel(
            name='BillingPostpaidSMSProduct',
            fields=[],
            options={
                'verbose_name': 'Postpaid SMS product',
                'verbose_name_plural': 'Postpaid SMS product',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('billing.billingproduct',),
        ),
        migrations.CreateModel(
            name='BillingPrepaidSMSProduct',
            fields=[],
            options={
                'verbose_name': 'Prepaid SMS package',
                'verbose_name_plural': 'Postpaid SMS packages',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('billing.billingproduct',),
        ),
        migrations.CreateModel(
            name='BillingSaaSProduct',
            fields=[],
            options={
                'verbose_name': 'SaaS product',
                'verbose_name_plural': 'SaaS products',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('billing.billingproduct',),
        ),
        migrations.CreateModel(
            name='BillingStafferBusyProduct',
            fields=[],
            options={
                'verbose_name': '"Busy" staffer add-on',
                'verbose_name_plural': '"Busy" staffer add-ons',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('billing.billingproduct',),
        ),
        migrations.CreateModel(
            name='BillingStafferSaaSProduct',
            fields=[],
            options={
                'verbose_name': 'SaaS staffer add-on',
                'verbose_name_plural': 'SaaS staffer add-ons',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('billing.billingproduct',),
        ),
    ]
