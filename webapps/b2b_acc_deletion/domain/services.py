import hashlib


class AnonymizationService:
    @staticmethod
    def anonymize_data(value_object):
        if not value_object:
            return value_object

        fields_to_anonymize = getattr(value_object, 'fields_to_anonymize', lambda: [])()
        fields_to_set_none = getattr(value_object, 'fields_to_set_none', lambda: [])()
        fields_to_set_blank = getattr(value_object, 'fields_to_set_blank', lambda: [])()
        anonymized_data = {}

        for field in value_object.__dataclass_fields__.keys():
            field_value = getattr(value_object, field)
            match field:
                case _ if field in fields_to_anonymize and field_value:
                    # ToDo: simplify hash
                    #  like webapps.user.utils.BasicDeleteUserStrategy.clear_user_data
                    anonymized_data[field] = hashlib.sha256(field_value.encode()).hexdigest()
                case _ if field in fields_to_set_none:
                    anonymized_data[field] = None
                case _ if field in fields_to_set_blank:
                    anonymized_data[field] = ''
                case _:
                    anonymized_data[field] = field_value

        return value_object.__class__(**{**value_object.__dict__, **anonymized_data})
