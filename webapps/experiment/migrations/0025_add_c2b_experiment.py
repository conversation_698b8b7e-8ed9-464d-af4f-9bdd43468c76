# Generated by Django 1.11.11 on 2018-07-02 09:33
from django.db import migrations


def create_c2b_experiment(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    Experiment = apps.get_model('experiment', 'Experiment')
    ExperimentVariant = apps.get_model('experiment', 'ExperimentVariant')

    exp, _ = Experiment.objects.using(db_alias).get_or_create(name='c2b_referral')
    ExperimentVariant.objects.using(db_alias).get_or_create(
        name='enabled',
        experiment=exp,
        defaults={'weight': 0},
    )
    ExperimentVariant.objects.using(db_alias).get_or_create(
        name='disabled',
        experiment=exp,
        defaults={'weight': 1},
    )


class Migration(migrations.Migration):

    dependencies = [
        ('experiment', '0024_merge_20180523_0910'),
    ]

    operations = [
        migrations.RunPython(
            code=create_c2b_experiment,
            reverse_code=migrations.RunPython.noop,
        ),
    ]
