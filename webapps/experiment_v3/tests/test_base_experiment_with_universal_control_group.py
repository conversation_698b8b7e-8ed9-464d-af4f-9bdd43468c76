import pytest
from django.test import override_settings

from country_config import Country
from webapps.experiment_v3.exp.base import (
    UniversalControlGroupExperimentMixin,
    BaseExperimentV3,
)
from webapps.experiment_v3.filters.api_country import ApiCountry
from webapps.experiment_v3.models import ExperimentSlot


class UniversalControlGroupTestExperiment(UniversalControlGroupExperimentMixin, BaseExperimentV3):
    name = 'some_experiment'
    config = {
        'variants': [
            {
                'name': 'control',
                'weight': 1,
            },
            {
                'name': 'test_group',
                'weight': 1,
            },
        ]
    }

    filters = [
        (ApiCountry, {'allowed_countries': [Country.US]}),
    ]

    @property
    def related_user_id(self):
        return self.relation_id


@pytest.mark.django_db
def test_variant_for_non_zero_relation_id_us():
    UniversalControlGroupTestExperiment.initialize()
    relation_ids_list = [1, 113, 1547, 3399]

    for relation_id in relation_ids_list:
        exp = UniversalControlGroupTestExperiment(relation_id)
        assert exp.get_variant()

    assert (
        ExperimentSlot.objects.filter(
            relation_id__in=relation_ids_list,
        ).count()
        == 4
    )


@pytest.mark.django_db
def test_variant_for_zero_relation_id_us():
    UniversalControlGroupTestExperiment.initialize()
    relation_ids_list = [0, 100, 1500, 3300]

    for relation_id in relation_ids_list:
        exp = UniversalControlGroupTestExperiment(relation_id)
        assert not exp.get_variant()

    assert not ExperimentSlot.objects.filter(
        relation_id__in=relation_ids_list,
    ).exists()


@pytest.mark.django_db
@override_settings(API_COUNTRY=Country.BR)
def test_variant_for_non_zero_relation_id_br():
    UniversalControlGroupTestExperiment.initialize()
    relation_ids_list = [1, 113, 1547, 3399]

    for relation_id in relation_ids_list:
        exp = UniversalControlGroupTestExperiment(relation_id)
        assert not exp.get_variant()

    assert not ExperimentSlot.objects.filter(
        relation_id__in=relation_ids_list,
    ).exists()


@pytest.mark.django_db
@override_settings(API_COUNTRY=Country.BR)
def test_variant_for_zero_relation_id_br():
    UniversalControlGroupTestExperiment.initialize()
    relation_ids_list = [0, 100, 1500, 3300]

    for relation_id in relation_ids_list:
        exp = UniversalControlGroupTestExperiment(relation_id)
        assert not exp.get_variant()

    assert not ExperimentSlot.objects.filter(
        relation_id__in=relation_ids_list,
    ).exists()
