import pytest
from django.core.exceptions import ValidationError
from mock import patch, PropertyMock
from model_bakery import baker

from webapps.experiment_v3.exp.base import BaseExperimentV3
from webapps.experiment_v3.filters.router import RouterFilter
from webapps.experiment_v3.models import (
    ExperimentSlot,
    ExperimentRouterConfig,
    Experiment,
)


class SampleRoutedExperimentBase(BaseExperimentV3):
    config = {
        'active': True,
        'variants': [
            {
                'name': 'control',
                'control_group': True,
                'weight': 1,
            },
            {
                'name': 'test',
                'weight': 1,
            },
        ],
    }
    filters = [
        (RouterFilter, None),
    ]


class SampleExperiment1(SampleRoutedExperimentBase):
    name = 'sample_experiment_1'


class SampleExperiment2(SampleRoutedExperimentBase):
    name = 'sample_experiment_2'


@pytest.fixture(scope='module', autouse=True)
def patch_allowed_experiments():
    with patch(
        'webapps.experiment_v3.models.router.ExperimentRouterConfig.allowed_experiments',
        new_callable=PropertyMock,
    ) as allowed_experiments_mock:
        allowed_experiments_mock.return_value = {
            SampleExperiment1.name,
            SampleExperiment2.name,
        }
        yield


@pytest.mark.django_db
def test_no_config():
    _initialize_experiments()
    _touch_experiments(relation_id=1)
    assert not ExperimentSlot.objects.exists()


@pytest.mark.django_db
def test_config():
    _initialize_experiments()

    baker.make(
        ExperimentRouterConfig,
        experiment=Experiment.objects.get(name=SampleExperiment1.name),
        range_from=1,
        range_to=2,
    )
    baker.make(
        ExperimentRouterConfig,
        experiment=Experiment.objects.get(name=SampleExperiment2.name),
        range_from=32,
        range_to=64,
    )

    _touch_experiments(3)
    assert not ExperimentSlot.objects.exists()

    _touch_experiments(1)
    assert ExperimentSlot.objects.count() == 1
    assert (
        ExperimentSlot.objects.values_list('variant__experiment__name', flat=True).first()
        == SampleExperiment1.name
    )

    _touch_experiments(42)
    assert ExperimentSlot.objects.count() == 2
    assert (
        ExperimentSlot.objects.values_list('variant__experiment__name', flat=True).first()
        == SampleExperiment2.name
    )


def test_validation_bad_experiment():
    experiment = baker.prepare(Experiment, id=1, name='other_experiment')
    config = ExperimentRouterConfig(experiment=experiment)
    with pytest.raises(ValidationError):
        config.clean()


def test_validation_min_value():
    config = ExperimentRouterConfig(range_from=0)
    with pytest.raises(ValidationError):
        config.clean_fields(exclude=['experiment', 'range_to'])


def test_validation_max_value():
    config = ExperimentRouterConfig(range_to=100)
    with pytest.raises(ValidationError):
        config.clean_fields(exclude=['experiment', 'range_from'])


def test_validation_bad_range():
    experiment = baker.prepare(Experiment, name=SampleExperiment1.name)
    config = ExperimentRouterConfig(experiment=experiment, range_from=2, range_to=1)
    with pytest.raises(ValidationError):
        config.clean()


@pytest.mark.django_db
@pytest.mark.parametrize(
    ('range_from', 'range_to', 'is_overlapping'),
    [
        # range [2,4] is already reserved
        (1, 1, False),
        (1, 3, True),
        (1, 5, True),
        (3, 4, True),
        (3, 5, True),
        (5, 5, False),
    ],
)
def test_validation_overlapping_range(range_from, range_to, is_overlapping):
    experiment = baker.make(Experiment, name=SampleExperiment1.name)
    baker.make(ExperimentRouterConfig, experiment=experiment, range_from=2, range_to=4)
    config = ExperimentRouterConfig(experiment=experiment, range_from=range_from, range_to=range_to)
    if is_overlapping:
        with pytest.raises(ValidationError):
            config.clean()
    else:
        config.clean()


@pytest.mark.django_db
def test_validation_overlapping_update():
    experiment = baker.make(Experiment, name=SampleExperiment1.name)
    config = baker.make(ExperimentRouterConfig, experiment=experiment, range_from=2, range_to=4)
    config.range_to = 5
    config.clean()


def _initialize_experiments():
    SampleExperiment1.initialize()
    SampleExperiment2.initialize()


def _touch_experiments(relation_id):
    SampleExperiment1(relation_id).pick_variant()
    SampleExperiment2(relation_id).pick_variant()
