# Generated by Django 3.1.8 on 2021-06-23 10:14

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0334_auto_20210610_0831'),
        ('business_related', '0008_rename_facilities_to_amenities'),
    ]

    operations = [
        migrations.RenameField(
            model_name='bookedforclaimlog',
            old_name='bci',
            new_name='target_bci',
        ),
        migrations.AddField(
            model_name='bookedforclaimlog',
            name='merge_reason',
            field=models.CharField(
                blank=True,
                choices=[
                    ('C', 'CustomForm secret'),
                    ('S', 'Appointment secret'),
                    ('M', 'Manual'),
                    ('A', 'AUTO'),
                ],
                max_length=1,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name='bookedforclaimlog',
            name='source_bci',
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name='+',
                to='business.businesscustomerinfo',
            ),
        ),
        migrations.AddField(
            model_name='bookedforclaimlog',
            name='source_model_related_objects',
            field=models.JSONField(blank=True, default=dict),
        ),
        migrations.AddField(
            model_name='bookedforclaimlog',
            name='type',
            field=models.CharField(
                choices=[('A', 'Appointment'), ('C', 'CustomForm'), ('B', 'Customer Card')],
                default='A',
                max_length=1,
            ),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='bookedforclaimlog',
            name='status',
            field=models.CharField(
                choices=[
                    ('A', 'Aborted'),
                    ('C', 'Claimed'),
                    ('M', 'Merged'),
                    ('P', 'Proposed'),
                    ('D', 'Declined'),
                    ('R', 'Reverted'),
                ],
                max_length=1,
            ),
        ),
        migrations.AlterField(
            model_name='businesscustomerinfomergelog',
            name='merge_reason',
            field=models.CharField(
                choices=[
                    ('C', 'CustomForm secret'),
                    ('S', 'Appointment secret'),
                    ('M', 'Manual'),
                    ('A', 'AUTO'),
                ],
                max_length=1,
            ),
        ),
        migrations.AlterField(
            model_name='businesscustomerinfomergelog',
            name='status',
            field=models.CharField(
                choices=[
                    ('A', 'Aborted'),
                    ('C', 'Claimed'),
                    ('M', 'Merged'),
                    ('P', 'Proposed'),
                    ('D', 'Declined'),
                    ('R', 'Reverted'),
                ],
                max_length=1,
            ),
        ),
        migrations.RenameModel(
            old_name='BookedForClaimLog',
            new_name='ClaimLog',
        ),
    ]
