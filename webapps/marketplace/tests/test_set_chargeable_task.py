from datetime import datetime, timedelta

import pytest
import pytz
from freezegun import freeze_time

from lib.feature_flag.feature.boost import (
    BoostChargingForBusinessBookingsFlag,
    RefactorSetChargeableTaskFlag,
)
from lib.tests.utils import override_eppo_feature_flag
from lib.tools import tznow
from webapps.booking.models import Appointment
from webapps.boost.tests.utils import (
    create_non_chargeable_appointment,
    create_potentially_boost_appointment,
)
from webapps.business.enums import BoostPaymentSource
from webapps.business.models import Business
from webapps.marketplace.models import BoostClientCard
from webapps.marketplace.tasks import (
    set_chargeable_task,
)
from webapps.marketplace.tests.utils import (
    ExpectedChargeablePayable,
    assert_appointment_chargeable_payable,
    assert_bci_and_appointment_setup,
    assert_bci_first_appointment,
    prepare_bci,
)
from webapps.marketplace.utils import get_open_billing_cycle_start_date

_BASE_DATETIME = datetime(2023, 3, 11, tzinfo=pytz.utc)  # after Mar 9, see: BOOS-734
_SUBBOOKING_DATA = {
    'booked_from': _BASE_DATETIME - timedelta(hours=10),
    'booked_till': _BASE_DATETIME - timedelta(hours=9),
}


def manually_set_first_appointment(bci, appointment):
    if bci.first_appointment is None:
        bci.first_appointment = appointment
        bci.save()
    return bci


@pytest.mark.parametrize(
    ('refactor_flag, bb_flag, boost_status, expected_chargeable_payable'),
    [
        (True, True, Business.BoostStatus.DISABLED, ExpectedChargeablePayable(True, False)),
        (True, True, Business.BoostStatus.ENABLED, ExpectedChargeablePayable(True, True)),
        (False, True, Business.BoostStatus.DISABLED, ExpectedChargeablePayable(True, False)),
        (False, True, Business.BoostStatus.ENABLED, ExpectedChargeablePayable(True, True)),
        (False, False, Business.BoostStatus.DISABLED, ExpectedChargeablePayable(True, False)),
        (False, False, Business.BoostStatus.ENABLED, ExpectedChargeablePayable(True, True)),
    ],
)
@pytest.mark.django_db
@pytest.mark.freeze_time(_BASE_DATETIME)
def test_first_appointment_not_set_before_task_execution(
    refactor_flag, bb_flag, boost_status, expected_chargeable_payable
):
    """
    This case is handled by the task, but it isn't the primary scenario for creating appointments.
    In this situation, the task works correctly without refactoring.
    """
    bci = prepare_bci(boost_status)
    appointment = create_potentially_boost_appointment(bci, subbookings=[{**_SUBBOOKING_DATA}])
    assert appointment.booked_for.first_appointment is None, 'wrong setup'

    with override_eppo_feature_flag(
        {
            RefactorSetChargeableTaskFlag.flag_name: refactor_flag,
            BoostChargingForBusinessBookingsFlag.flag_name: bb_flag,
        }
    ):
        set_chargeable_task.run(appointment.id)

    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, expected_chargeable_payable)


@pytest.mark.parametrize(
    ('refactor_flag, bb_flag, boost_status, expected_chargeable_payable'),
    [
        (True, True, Business.BoostStatus.DISABLED, ExpectedChargeablePayable(True, False)),
        (True, True, Business.BoostStatus.ENABLED, ExpectedChargeablePayable(True, True)),
        (False, True, Business.BoostStatus.DISABLED, ExpectedChargeablePayable(False, False)),
        (False, True, Business.BoostStatus.ENABLED, ExpectedChargeablePayable(True, True)),
        (False, False, Business.BoostStatus.DISABLED, ExpectedChargeablePayable(False, False)),
        (False, False, Business.BoostStatus.ENABLED, ExpectedChargeablePayable(True, True)),
    ],
)
@pytest.mark.django_db
@pytest.mark.freeze_time(_BASE_DATETIME)
def test_first_appointment_already_set_before_task_execution(
    refactor_flag, bb_flag, boost_status, expected_chargeable_payable
):
    """
    The primary scenario for creating appointments: If an appointment is created for a new BCI,
    the first_appointment is set during the creation of this BCI
    """
    bci = prepare_bci(boost_status)
    appointment = create_potentially_boost_appointment(bci, subbookings=[{**_SUBBOOKING_DATA}])
    bci = manually_set_first_appointment(bci, appointment)
    assert_bci_and_appointment_setup(bci, appointment, ExpectedChargeablePayable(False, False))

    with override_eppo_feature_flag(
        {
            RefactorSetChargeableTaskFlag.flag_name: refactor_flag,
            BoostChargingForBusinessBookingsFlag.flag_name: bb_flag,
        }
    ):
        set_chargeable_task.run(appointment.id)

    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, expected_chargeable_payable)

    # check whether the next appointment does not change anything
    second_appointment = create_potentially_boost_appointment(
        bci, subbookings=[{**_SUBBOOKING_DATA}]
    )
    with override_eppo_feature_flag(
        {
            RefactorSetChargeableTaskFlag.flag_name: refactor_flag,
            BoostChargingForBusinessBookingsFlag.flag_name: bb_flag,
        }
    ):
        set_chargeable_task.run(second_appointment.id)
    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, expected_chargeable_payable)
    assert_appointment_chargeable_payable(
        second_appointment, ExpectedChargeablePayable(False, False)
    )


@pytest.mark.django_db
@pytest.mark.freeze_time(_BASE_DATETIME)
def test_first_appointment_no_longer_chargeable():
    """
    The first visit was marked as chargeable, then the second appointment was created with its time
    earlier than the first one's time.
    """
    bci = prepare_bci(Business.BoostStatus.DISABLED)
    appointment = create_potentially_boost_appointment(bci, subbookings=[{**_SUBBOOKING_DATA}])
    bci = manually_set_first_appointment(bci, appointment)
    assert_bci_and_appointment_setup(bci, appointment, ExpectedChargeablePayable(False, False))

    with override_eppo_feature_flag(
        {
            RefactorSetChargeableTaskFlag.flag_name: True,
            BoostChargingForBusinessBookingsFlag.flag_name: True,
        }
    ):
        set_chargeable_task.run(appointment.id)

    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, ExpectedChargeablePayable(True, False))

    subbooking_data = {
        'booked_from': _SUBBOOKING_DATA['booked_from'] - timedelta(hours=2),
        'booked_till': _SUBBOOKING_DATA['booked_from'] - timedelta(hours=1),
    }
    with freeze_time(_BASE_DATETIME + timedelta(days=1)):
        second_appointment = create_potentially_boost_appointment(
            bci, subbookings=[{**subbooking_data}]
        )
    with override_eppo_feature_flag(
        {
            RefactorSetChargeableTaskFlag.flag_name: True,
            BoostChargingForBusinessBookingsFlag.flag_name: True,
        }
    ):
        set_chargeable_task.run(second_appointment.id)
    assert_bci_first_appointment(bci, second_appointment)
    assert_appointment_chargeable_payable(appointment, ExpectedChargeablePayable(False, False))
    assert_appointment_chargeable_payable(
        second_appointment, ExpectedChargeablePayable(True, False)
    )


@pytest.mark.parametrize(
    ('refactor_flag, bb_flag'),
    [
        (True, True),
        (False, True),
        (False, False),
    ],
)
@pytest.mark.django_db
@pytest.mark.freeze_time(_BASE_DATETIME)
def test_first_appointment_no_longer_payable(refactor_flag, bb_flag):
    """
    The first visit was marked as payable, then the second appointment was created with its time
    earlier than the first one's time.
    """
    bci = prepare_bci(Business.BoostStatus.ENABLED)
    appointment = create_potentially_boost_appointment(bci, subbookings=[{**_SUBBOOKING_DATA}])
    bci = manually_set_first_appointment(bci, appointment)
    assert_bci_and_appointment_setup(bci, appointment, ExpectedChargeablePayable(False, False))

    with override_eppo_feature_flag(
        {
            RefactorSetChargeableTaskFlag.flag_name: refactor_flag,
            BoostChargingForBusinessBookingsFlag.flag_name: bb_flag,
        }
    ):
        set_chargeable_task.run(appointment.id)

    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, ExpectedChargeablePayable(True, True))

    bcc = BoostClientCard.objects.get(client_card=bci)
    assert bcc.from_promo

    subbooking_data = {
        'booked_from': _SUBBOOKING_DATA['booked_from'] - timedelta(hours=2),
        'booked_till': _SUBBOOKING_DATA['booked_from'] - timedelta(hours=1),
    }
    with freeze_time(_BASE_DATETIME + timedelta(days=1)):
        second_appointment = create_potentially_boost_appointment(
            bci, subbookings=[{**subbooking_data}]
        )
    with override_eppo_feature_flag(
        {
            RefactorSetChargeableTaskFlag.flag_name: refactor_flag,
            BoostChargingForBusinessBookingsFlag.flag_name: bb_flag,
        }
    ):
        set_chargeable_task.run(second_appointment.id)
    assert_bci_first_appointment(bci, second_appointment)
    assert_appointment_chargeable_payable(appointment, ExpectedChargeablePayable(False, False))
    assert_appointment_chargeable_payable(second_appointment, ExpectedChargeablePayable(True, True))

    assert bcc.from_promo
    assert appointment.boost_appointment.deleted is not None
    assert second_appointment.boost_appointment.deleted is None


@pytest.mark.parametrize(
    ('refactor_flag, bb_flag, boost_status, expected_chargeable_payable'),
    [
        (True, True, Business.BoostStatus.DISABLED, ExpectedChargeablePayable(True, False)),
        (True, True, Business.BoostStatus.ENABLED, ExpectedChargeablePayable(True, True)),
        (False, True, Business.BoostStatus.ENABLED, ExpectedChargeablePayable(True, True)),
        (False, False, Business.BoostStatus.ENABLED, ExpectedChargeablePayable(True, True)),
    ],
)
@pytest.mark.django_db
@pytest.mark.freeze_time(_BASE_DATETIME)
def test_no_show(refactor_flag, bb_flag, boost_status, expected_chargeable_payable):
    """
    The status of the first visit was changed to NO_SHOW, second visit is chargeable/payable
    """
    bci = prepare_bci(boost_status)
    appointment = create_potentially_boost_appointment(bci, subbookings=[{**_SUBBOOKING_DATA}])
    bci = manually_set_first_appointment(bci, appointment)
    assert_bci_and_appointment_setup(bci, appointment, ExpectedChargeablePayable(False, False))

    with override_eppo_feature_flag(
        {
            RefactorSetChargeableTaskFlag.flag_name: refactor_flag,
            BoostChargingForBusinessBookingsFlag.flag_name: bb_flag,
        }
    ):
        set_chargeable_task.run(appointment.id)

    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, expected_chargeable_payable)

    Appointment.objects.filter(id=appointment.id).update(status=Appointment.STATUS.NOSHOW)
    with override_eppo_feature_flag(
        {
            RefactorSetChargeableTaskFlag.flag_name: refactor_flag,
            BoostChargingForBusinessBookingsFlag.flag_name: bb_flag,
        }
    ):
        set_chargeable_task.run(appointment.id)
    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, ExpectedChargeablePayable(False, False))

    second_appointment = create_potentially_boost_appointment(
        bci, subbookings=[{**_SUBBOOKING_DATA}]
    )
    with override_eppo_feature_flag(
        {
            RefactorSetChargeableTaskFlag.flag_name: refactor_flag,
            BoostChargingForBusinessBookingsFlag.flag_name: bb_flag,
        }
    ):
        set_chargeable_task.run(second_appointment.id)
    assert_bci_first_appointment(bci, second_appointment)
    assert_appointment_chargeable_payable(appointment, ExpectedChargeablePayable(False, False))
    assert_appointment_chargeable_payable(second_appointment, expected_chargeable_payable)


@pytest.mark.parametrize('bb_flag', (True, False))
@pytest.mark.django_db
@pytest.mark.freeze_time(_BASE_DATETIME)
@override_eppo_feature_flag({RefactorSetChargeableTaskFlag.flag_name: False})
def test_no_show_for_disabled_boost_and_without_refactor(bb_flag):
    """
    The status of the first visit was changed to NO_SHOW, second visit should be chargeable/payable.
    These tests just show how the old logic works.
    """
    bci = prepare_bci(Business.BoostStatus.DISABLED)
    appointment = create_potentially_boost_appointment(bci, subbookings=[{**_SUBBOOKING_DATA}])
    bci = manually_set_first_appointment(bci, appointment)
    assert_bci_and_appointment_setup(bci, appointment, ExpectedChargeablePayable(False, False))

    with override_eppo_feature_flag({BoostChargingForBusinessBookingsFlag.flag_name: bb_flag}):
        set_chargeable_task.run(appointment.id)

    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, ExpectedChargeablePayable(False, False))

    Appointment.objects.filter(id=appointment.id).update(status=Appointment.STATUS.NOSHOW)
    with override_eppo_feature_flag({BoostChargingForBusinessBookingsFlag.flag_name: bb_flag}):
        set_chargeable_task.run(appointment.id)
    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, ExpectedChargeablePayable(False, False))

    second_appointment = create_potentially_boost_appointment(
        bci, subbookings=[{**_SUBBOOKING_DATA}]
    )
    with override_eppo_feature_flag({BoostChargingForBusinessBookingsFlag.flag_name: bb_flag}):
        set_chargeable_task.run(second_appointment.id)
    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, ExpectedChargeablePayable(False, False))
    assert_appointment_chargeable_payable(
        second_appointment, ExpectedChargeablePayable(False, False)
    )


@pytest.mark.parametrize(
    ('refactor_flag, bb_flag, boost_status, expected_chargeable_payable'),
    [
        (True, True, Business.BoostStatus.DISABLED, ExpectedChargeablePayable(True, False)),
        (True, True, Business.BoostStatus.ENABLED, ExpectedChargeablePayable(True, True)),
        (False, True, Business.BoostStatus.ENABLED, ExpectedChargeablePayable(True, True)),
        (False, False, Business.BoostStatus.ENABLED, ExpectedChargeablePayable(True, True)),
    ],
)
@pytest.mark.django_db
@pytest.mark.freeze_time(_BASE_DATETIME)
def test_first_visit_canceled(refactor_flag, bb_flag, boost_status, expected_chargeable_payable):
    """The first visit was canceled, second visit is chargeable/payable"""
    bci = prepare_bci(boost_status)
    appointment = create_potentially_boost_appointment(bci, subbookings=[{**_SUBBOOKING_DATA}])
    bci = manually_set_first_appointment(bci, appointment)
    assert_bci_and_appointment_setup(bci, appointment, ExpectedChargeablePayable(False, False))

    with override_eppo_feature_flag(
        {
            RefactorSetChargeableTaskFlag.flag_name: refactor_flag,
            BoostChargingForBusinessBookingsFlag.flag_name: bb_flag,
        }
    ):
        set_chargeable_task.run(appointment.id)

    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, expected_chargeable_payable)

    Appointment.objects.filter(id=appointment.id).update(status=Appointment.STATUS.CANCELED)
    with override_eppo_feature_flag(
        {
            RefactorSetChargeableTaskFlag.flag_name: refactor_flag,
            BoostChargingForBusinessBookingsFlag.flag_name: bb_flag,
        }
    ):
        set_chargeable_task.run(appointment.id)
    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, ExpectedChargeablePayable(False, False))

    second_appointment = create_potentially_boost_appointment(
        bci, subbookings=[{**_SUBBOOKING_DATA}]
    )
    with override_eppo_feature_flag(
        {
            RefactorSetChargeableTaskFlag.flag_name: refactor_flag,
            BoostChargingForBusinessBookingsFlag.flag_name: bb_flag,
        }
    ):
        set_chargeable_task.run(second_appointment.id)
    assert_bci_first_appointment(bci, second_appointment)
    assert_appointment_chargeable_payable(appointment, ExpectedChargeablePayable(False, False))
    assert_appointment_chargeable_payable(second_appointment, expected_chargeable_payable)


@pytest.mark.parametrize(
    ('refactor_flag, bb_flag, boost_status, expected_chargeable_payable'),
    [
        (True, True, Business.BoostStatus.DISABLED, ExpectedChargeablePayable(True, False)),
        (True, True, Business.BoostStatus.ENABLED, ExpectedChargeablePayable(True, True)),
        (False, True, Business.BoostStatus.ENABLED, ExpectedChargeablePayable(True, True)),
        (False, False, Business.BoostStatus.ENABLED, ExpectedChargeablePayable(True, True)),
    ],
)
@pytest.mark.django_db
@pytest.mark.freeze_time(_BASE_DATETIME - timedelta(weeks=1))  # before Mar 9, see: BOOS-734
def test_first_visit_canceled_second_too_old(
    refactor_flag, bb_flag, boost_status, expected_chargeable_payable
):
    """
    The first visit was canceled, second visit should not be chargeable/payable because it's too old
    """
    bci = prepare_bci(boost_status)
    datetime_ = tznow()
    subbooking_data = {
        'booked_from': datetime_ - timedelta(hours=10),
        'booked_till': datetime_ - timedelta(hours=9),
    }
    appointment = create_potentially_boost_appointment(bci, subbookings=[{**subbooking_data}])
    bci = manually_set_first_appointment(bci, appointment)
    assert_bci_and_appointment_setup(bci, appointment, ExpectedChargeablePayable(False, False))

    with override_eppo_feature_flag(
        {
            RefactorSetChargeableTaskFlag.flag_name: refactor_flag,
            BoostChargingForBusinessBookingsFlag.flag_name: bb_flag,
        }
    ):
        set_chargeable_task.run(appointment.id)

    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, expected_chargeable_payable)

    Appointment.objects.filter(id=appointment.id).update(status=Appointment.STATUS.CANCELED)
    with override_eppo_feature_flag(
        {
            RefactorSetChargeableTaskFlag.flag_name: refactor_flag,
            BoostChargingForBusinessBookingsFlag.flag_name: bb_flag,
        }
    ):
        set_chargeable_task.run(appointment.id)
    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, ExpectedChargeablePayable(False, False))

    second_appointment = create_potentially_boost_appointment(
        bci, subbookings=[{**subbooking_data}]
    )
    with override_eppo_feature_flag(
        {
            RefactorSetChargeableTaskFlag.flag_name: refactor_flag,
            BoostChargingForBusinessBookingsFlag.flag_name: bb_flag,
        }
    ):
        set_chargeable_task.run(second_appointment.id)
    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, ExpectedChargeablePayable(False, False))
    assert_appointment_chargeable_payable(
        second_appointment, ExpectedChargeablePayable(False, False)
    )


@pytest.mark.parametrize(
    ('refactor_flag, bb_flag, boost_status, expected_chargeable_payable'),
    [
        (True, True, Business.BoostStatus.DISABLED, ExpectedChargeablePayable(True, False)),
        (True, True, Business.BoostStatus.ENABLED, ExpectedChargeablePayable(True, True)),
        (False, True, Business.BoostStatus.ENABLED, ExpectedChargeablePayable(True, True)),
        (False, False, Business.BoostStatus.ENABLED, ExpectedChargeablePayable(True, True)),
    ],
)
@pytest.mark.django_db
@pytest.mark.freeze_time(_BASE_DATETIME - timedelta(weeks=1))  # before Mar 9, see: BOOS-734
def test_first_visit_finished_second_too_old(
    refactor_flag, bb_flag, boost_status, expected_chargeable_payable
):
    """
    The first visit was finished, second visit should not be chargeable/payable because it's too old
    so the first one should stay chargeable/payable
    """
    bci = prepare_bci(boost_status)
    datetime_ = tznow()
    subbooking_data = {
        'booked_from': datetime_ - timedelta(hours=10),
        'booked_till': datetime_ - timedelta(hours=9),
    }
    appointment = create_potentially_boost_appointment(bci, subbookings=[{**subbooking_data}])
    bci = manually_set_first_appointment(bci, appointment)
    assert_bci_and_appointment_setup(bci, appointment, ExpectedChargeablePayable(False, False))

    with override_eppo_feature_flag(
        {
            RefactorSetChargeableTaskFlag.flag_name: refactor_flag,
            BoostChargingForBusinessBookingsFlag.flag_name: bb_flag,
        }
    ):
        set_chargeable_task.run(appointment.id)

    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, expected_chargeable_payable)

    second_appointment = create_potentially_boost_appointment(
        bci, subbookings=[{**subbooking_data}]
    )
    with override_eppo_feature_flag(
        {
            RefactorSetChargeableTaskFlag.flag_name: refactor_flag,
            BoostChargingForBusinessBookingsFlag.flag_name: bb_flag,
        }
    ):
        set_chargeable_task.run(second_appointment.id)
    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, expected_chargeable_payable)
    assert_appointment_chargeable_payable(
        second_appointment, ExpectedChargeablePayable(False, False)
    )


@pytest.mark.django_db
@pytest.mark.freeze_time(_BASE_DATETIME)
@override_eppo_feature_flag(
    {
        RefactorSetChargeableTaskFlag.flag_name: True,
        BoostChargingForBusinessBookingsFlag.flag_name: True,
    }
)
def test_first_visit_chargeable_second_not_payable_same_billing_cycle():
    """
    The first visit occurred when boost was disabled, so the next visit should not be payable, even
    when boost is enabled. However, the second visit becomes chargeable, because both visits occur
    in the same billing cycle, but the second one happened earlier than the first one.
    """
    boost_start_datetime = _BASE_DATETIME + timedelta(weeks=1)

    subbooking_data = {
        'booked_from': boost_start_datetime + timedelta(days=1, hours=2),
        'booked_till': boost_start_datetime + timedelta(days=1, hours=3),
    }

    bci = prepare_bci(Business.BoostStatus.DISABLED)
    appointment = create_potentially_boost_appointment(bci, subbookings=[{**subbooking_data}])
    bci = manually_set_first_appointment(bci, appointment)
    assert_bci_and_appointment_setup(bci, appointment, ExpectedChargeablePayable(False, False))

    set_chargeable_task.run(appointment.id)

    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, ExpectedChargeablePayable(True, False))

    with freeze_time(boost_start_datetime):
        bci.business.enable_boost_availability()
        bci.business.boost_status = Business.BoostStatus.ENABLED
        bci.business.boost_payment_source = BoostPaymentSource.OFFLINE
        bci.business.save()

    assert bci.business.boost_status == Business.BoostStatus.ENABLED, 'wrong setup'

    subbooking_data = {
        'booked_from': boost_start_datetime + timedelta(days=1, hours=1),
        'booked_till': boost_start_datetime + timedelta(days=1, hours=2),
    }
    with freeze_time(boost_start_datetime + timedelta(days=1)):
        second_appointment = create_potentially_boost_appointment(
            bci, subbookings=[{**subbooking_data}]
        )
        assert second_appointment.booked_till < appointment.booked_till, 'wrong setup'
        opened_billing_cycle_start_date = get_open_billing_cycle_start_date(bci.business)
        assert appointment.booked_till > opened_billing_cycle_start_date, 'wrong setup'
        assert second_appointment.booked_till > opened_billing_cycle_start_date, 'wrong setup'

        set_chargeable_task.run(second_appointment.id)

    assert_bci_first_appointment(bci, second_appointment)
    assert_appointment_chargeable_payable(appointment, ExpectedChargeablePayable(False, False))
    assert_appointment_chargeable_payable(
        second_appointment, ExpectedChargeablePayable(True, False)
    )


@override_eppo_feature_flag(
    {
        RefactorSetChargeableTaskFlag.flag_name: True,
        BoostChargingForBusinessBookingsFlag.flag_name: True,
    }
)
@pytest.mark.django_db
@pytest.mark.freeze_time(_BASE_DATETIME)
def test_first_visit_chargeable_second_not_payable_different_billing_cycle():
    """
    The first visit occurred when boost was disabled, so the next visit should not be payable, even
    when boost is enabled. The first visit stays chargeable, because second visit has its time later
    than first one's time, even when visits occur across different billing cycles.
    """
    boost_start_datetime = _BASE_DATETIME + timedelta(weeks=10)

    subbooking_data = {
        'booked_from': _BASE_DATETIME + timedelta(days=1, hours=2),
        'booked_till': _BASE_DATETIME + timedelta(days=1, hours=3),
    }

    bci = prepare_bci(Business.BoostStatus.DISABLED)
    appointment = create_potentially_boost_appointment(bci, subbookings=[{**subbooking_data}])
    bci = manually_set_first_appointment(bci, appointment)
    assert_bci_and_appointment_setup(bci, appointment, ExpectedChargeablePayable(False, False))

    set_chargeable_task.run(appointment.id)

    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, ExpectedChargeablePayable(True, False))

    with freeze_time(boost_start_datetime):
        bci.business.enable_boost_availability()
        bci.business.boost_status = Business.BoostStatus.ENABLED
        bci.business.boost_payment_source = BoostPaymentSource.OFFLINE
        bci.business.save()

    assert bci.business.boost_status == Business.BoostStatus.ENABLED, 'wrong setup'

    subbooking_data = {
        'booked_from': boost_start_datetime + timedelta(days=1, hours=1),
        'booked_till': boost_start_datetime + timedelta(days=1, hours=2),
    }
    with freeze_time(boost_start_datetime + timedelta(days=1)):
        second_appointment = create_potentially_boost_appointment(
            bci, subbookings=[{**subbooking_data}]
        )
        assert second_appointment.booked_till > appointment.booked_till, 'wrong setup'
        opened_billing_cycle_start_date = get_open_billing_cycle_start_date(bci.business)
        assert appointment.booked_till < opened_billing_cycle_start_date, 'wrong setup'
        assert second_appointment.booked_till > opened_billing_cycle_start_date, 'wrong setup'

        set_chargeable_task.run(second_appointment.id)

    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, ExpectedChargeablePayable(True, False))
    assert_appointment_chargeable_payable(
        second_appointment, ExpectedChargeablePayable(False, False)
    )


@pytest.mark.django_db
@pytest.mark.freeze_time(_BASE_DATETIME)
@override_eppo_feature_flag(
    {
        RefactorSetChargeableTaskFlag.flag_name: True,
        BoostChargingForBusinessBookingsFlag.flag_name: True,
    }
)
def test_first_visit_chargeable_but_marked_as_noshow_second_not_payable_different_billing_cycle():
    """
    The first visit occurred when boost was disabled, so the next visit should not be payable, even
    when boost is enabled. The status of the first visit was changed to NoShow in the next billing
    cycle.
    """
    boost_start_datetime = _BASE_DATETIME + timedelta(weeks=10)

    subbooking_data = {
        'booked_from': _BASE_DATETIME + timedelta(days=1, hours=2),
        'booked_till': _BASE_DATETIME + timedelta(days=1, hours=3),
    }

    bci = prepare_bci(Business.BoostStatus.DISABLED)
    appointment = create_potentially_boost_appointment(bci, subbookings=[{**subbooking_data}])
    bci = manually_set_first_appointment(bci, appointment)
    assert_bci_and_appointment_setup(bci, appointment, ExpectedChargeablePayable(False, False))

    set_chargeable_task.run(appointment.id)

    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, ExpectedChargeablePayable(True, False))

    Appointment.objects.filter(id=appointment.id).update(status=Appointment.STATUS.NOSHOW)
    with freeze_time(boost_start_datetime + timedelta(days=1)):
        opened_billing_cycle_start_date = get_open_billing_cycle_start_date(bci.business)
        assert appointment.booked_till < opened_billing_cycle_start_date, 'wrong setup'
        set_chargeable_task.run(appointment.id)
    assert_bci_first_appointment(bci, appointment)
    # appointment is in next billing cycle, so there is no permission to modify first appointment
    # and update chargeable/payable
    assert_appointment_chargeable_payable(appointment, ExpectedChargeablePayable(True, False))

    with freeze_time(boost_start_datetime):
        bci.business.enable_boost_availability()
        bci.business.boost_status = Business.BoostStatus.ENABLED
        bci.business.boost_payment_source = BoostPaymentSource.OFFLINE
        bci.business.save()

    assert bci.business.boost_status == Business.BoostStatus.ENABLED, 'wrong setup'

    subbooking_data = {
        'booked_from': boost_start_datetime + timedelta(days=1, hours=1),
        'booked_till': boost_start_datetime + timedelta(days=1, hours=2),
    }
    with freeze_time(boost_start_datetime + timedelta(days=1)):
        second_appointment = create_potentially_boost_appointment(
            bci, subbookings=[{**subbooking_data}]
        )
        assert second_appointment.booked_till > appointment.booked_till, 'wrong setup'
        opened_billing_cycle_start_date = get_open_billing_cycle_start_date(bci.business)
        assert appointment.booked_till < opened_billing_cycle_start_date, 'wrong setup'
        assert second_appointment.booked_till > opened_billing_cycle_start_date, 'wrong setup'

        set_chargeable_task.run(second_appointment.id)
    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, ExpectedChargeablePayable(True, False))
    assert_appointment_chargeable_payable(
        second_appointment, ExpectedChargeablePayable(False, False)
    )


@pytest.mark.django_db
@pytest.mark.freeze_time(_BASE_DATETIME)
@override_eppo_feature_flag({BoostChargingForBusinessBookingsFlag.flag_name: True})
def test_first_visit_incorrectly_set_as_not_chargeable_second_not_payable():
    """
    The first visit occurred when boost was disabled, but the chargeable value was incorrectly set
    to False. The next visit should not be chargeable/payable, even when boost is enabled.
    """
    boost_start_datetime = _BASE_DATETIME + timedelta(weeks=10)

    subbooking_data = {
        'booked_from': _BASE_DATETIME + timedelta(days=1, hours=2),
        'booked_till': _BASE_DATETIME + timedelta(days=1, hours=3),
    }

    bci = prepare_bci(Business.BoostStatus.DISABLED)
    appointment = create_potentially_boost_appointment(bci, subbookings=[{**subbooking_data}])
    bci = manually_set_first_appointment(bci, appointment)
    assert_bci_and_appointment_setup(bci, appointment, ExpectedChargeablePayable(False, False))

    # simulate incorrect behavior
    with override_eppo_feature_flag({RefactorSetChargeableTaskFlag.flag_name: False}):
        set_chargeable_task.run(appointment.id)

    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, ExpectedChargeablePayable(False, False))

    with freeze_time(boost_start_datetime):
        bci.business.enable_boost_availability()
        bci.business.boost_status = Business.BoostStatus.ENABLED
        bci.business.boost_payment_source = BoostPaymentSource.OFFLINE
        bci.business.save()

    assert bci.business.boost_status == Business.BoostStatus.ENABLED, 'wrong setup'

    subbooking_data = {
        'booked_from': boost_start_datetime + timedelta(days=1, hours=1),
        'booked_till': boost_start_datetime + timedelta(days=1, hours=2),
    }
    with freeze_time(boost_start_datetime + timedelta(days=1)):
        second_appointment = create_potentially_boost_appointment(
            bci, subbookings=[{**subbooking_data}]
        )
        assert second_appointment.booked_till > appointment.booked_till, 'wrong setup'
        opened_billing_cycle_start_date = get_open_billing_cycle_start_date(bci.business)
        assert appointment.booked_till < opened_billing_cycle_start_date, 'wrong setup'
        assert second_appointment.booked_till > opened_billing_cycle_start_date, 'wrong setup'

        with override_eppo_feature_flag({RefactorSetChargeableTaskFlag.flag_name: True}):
            set_chargeable_task.run(second_appointment.id)

    assert_bci_first_appointment(
        bci, second_appointment
    )  # wrong appointment is set, because of different billing cycle - it will be fixed by script
    assert_appointment_chargeable_payable(appointment, ExpectedChargeablePayable(False, False))
    assert_appointment_chargeable_payable(
        second_appointment, ExpectedChargeablePayable(True, False)
    )


@pytest.mark.django_db
@pytest.mark.freeze_time(_BASE_DATETIME)
def test_first_visit_not_chargeable_second_not_payable_same_billing_cycle():
    """
    The first visit was correctly set as not chargeable, so the next visit should not be
    chargeable/payable even when boost is enabled.
    """

    bci = prepare_bci(Business.BoostStatus.ENABLED)

    subbooking_data = {
        'booked_from': _BASE_DATETIME + timedelta(hours=2),
        'booked_till': _BASE_DATETIME + timedelta(hours=3),
    }
    appointment = create_non_chargeable_appointment(bci, subbookings=[{**subbooking_data}])
    bci = manually_set_first_appointment(bci, appointment)
    assert_bci_and_appointment_setup(bci, appointment, ExpectedChargeablePayable(False, False))

    with override_eppo_feature_flag(
        {
            RefactorSetChargeableTaskFlag.flag_name: True,
            BoostChargingForBusinessBookingsFlag.flag_name: True,
        }
    ):
        set_chargeable_task.run(appointment.id)

    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, ExpectedChargeablePayable(False, False))

    subbooking_data = {
        'booked_from': _BASE_DATETIME + timedelta(hours=1),
        'booked_till': _BASE_DATETIME + timedelta(hours=2),
    }
    second_appointment = create_potentially_boost_appointment(
        bci, subbookings=[{**subbooking_data}]
    )
    assert second_appointment.booked_till < appointment.booked_till, 'wrong setup'
    opened_billing_cycle_start_date = get_open_billing_cycle_start_date(bci.business)
    assert appointment.booked_till > opened_billing_cycle_start_date, 'wrong setup'
    assert second_appointment.booked_till > opened_billing_cycle_start_date, 'wrong setup'

    with override_eppo_feature_flag(
        {
            RefactorSetChargeableTaskFlag.flag_name: True,
            BoostChargingForBusinessBookingsFlag.flag_name: True,
        }
    ):
        set_chargeable_task.run(second_appointment.id)
    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, ExpectedChargeablePayable(False, False))
    assert_appointment_chargeable_payable(
        second_appointment, ExpectedChargeablePayable(False, False)
    )


@pytest.mark.django_db
@pytest.mark.freeze_time(_BASE_DATETIME)
def test_first_visit_not_chargeable_second_not_payable_different_billing_cycle():
    """
    The first visit was correctly set as not chargeable, so the next visit should not be
    chargeable/payable even when boost is enabled.
    """

    bci = prepare_bci(Business.BoostStatus.ENABLED)

    subbooking_data = {
        'booked_from': _BASE_DATETIME + timedelta(hours=2),
        'booked_till': _BASE_DATETIME + timedelta(hours=3),
    }
    appointment = create_non_chargeable_appointment(bci, subbookings=[{**subbooking_data}])
    bci = manually_set_first_appointment(bci, appointment)
    assert_bci_and_appointment_setup(bci, appointment, ExpectedChargeablePayable(False, False))

    with override_eppo_feature_flag(
        {
            RefactorSetChargeableTaskFlag.flag_name: True,
            BoostChargingForBusinessBookingsFlag.flag_name: True,
        }
    ):
        set_chargeable_task.run(appointment.id)

    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, ExpectedChargeablePayable(False, False))

    new_billing_cycle = _BASE_DATETIME + timedelta(weeks=10)
    subbooking_data = {
        'booked_from': new_billing_cycle + timedelta(hours=1),
        'booked_till': new_billing_cycle + timedelta(hours=2),
    }
    with freeze_time(new_billing_cycle):
        second_appointment = create_potentially_boost_appointment(
            bci, subbookings=[{**subbooking_data}]
        )
        assert second_appointment.booked_till > appointment.booked_till, 'wrong setup'
        opened_billing_cycle_start_date = get_open_billing_cycle_start_date(bci.business)
        assert appointment.booked_till < opened_billing_cycle_start_date, 'wrong setup'
        assert second_appointment.booked_till > opened_billing_cycle_start_date, 'wrong setup'

    with override_eppo_feature_flag(
        {
            RefactorSetChargeableTaskFlag.flag_name: True,
            BoostChargingForBusinessBookingsFlag.flag_name: True,
        }
    ):
        set_chargeable_task.run(second_appointment.id)
    assert_bci_first_appointment(bci, appointment)
    assert_appointment_chargeable_payable(appointment, ExpectedChargeablePayable(False, False))
    assert_appointment_chargeable_payable(
        second_appointment, ExpectedChargeablePayable(False, False)
    )
