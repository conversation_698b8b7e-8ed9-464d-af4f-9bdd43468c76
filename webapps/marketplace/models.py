from __future__ import annotations

import logging
import urllib.parse
from datetime import date as datetime_date, datetime, timedelta
from decimal import Decimal, ROUND_HALF_UP
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from dateutil.relativedelta import relativedelta
from dateutil.tz import gettz
from dirtyfields import DirtyFieldsMixin
from django.conf import settings
from django.contrib.postgres.fields import ArrayField
from django.core import validators
from django.core.exceptions import ValidationError
from django.core.validators import MaxValueValidator, MinValueValidator
from django.db import models, transaction, IntegrityError
from django.db.models import (
    Count,
    DO_NOTHING,
    JSONField,
    OuterRef,
    Q,
    Subquery,
    Sum,
    Max,
    Value,
    DecimalField,
)
from django.db.models.functions import Coalesce
from django.db.models.signals import post_save, pre_save
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _

from lib.deeplink import generate_deeplink
from lib.elasticsearch.consts import ESDocType
from lib.elasticsearch.tools import ESDocMixin
from lib.feature_flag.adapter import UserData
from lib.feature_flag.enums import CustomUserAttributes
from lib.feature_flag.old_experiment import MarketplaceCommissionQuerysetOptimizationFlag
from lib.feature_flag.feature.boost import (
    BoostChargingForBusinessBookingsFlag,
    BoostChargingForBusinessBookingsNoPromoFixFlag,
    BoostOverduesBasedOnCutoffDateFlag,
    BoostOriginallyCameFromBoostFlag,
)
from lib.fields.deprecate_field import deprecate_field
from lib.fields.phone_number import BooksyPhoneNumberField
from lib.locks import BoostChargeLock, RedlockError, BoostRefundLock
from lib.models import (
    ArchiveManager,
    ArchiveModel,
    ArchiveQuerySet,
    AutoUpdateManager,
    AutoUpdateQuerySet,
    BaseArchiveManager,
    OneToOneExtendModel,
    SoftDeleteQuerySet,
    AutoAddHistoryModel,
    HistoryModel,
)
from lib.rivers import River, bump_document
from lib.tools import build_or_update_url, grouper, mp_deeplink, tznow
from settings.storage import CMSContentStorage, MarketPlaceSlotStorage
from webapps import consts
from webapps.billing.cache import get_business_payment_processor
from webapps.booking.enums import AppointmentStatus
from webapps.boost.enums import (
    BoostAppointmentStatus,
    MarketplaceTransactionDeclineType,
    MarketplaceTransactionProgressStatus,
    MarketplaceTransactionStatusEnum,
    MarketplaceTransactionType,
    TransactionPaymentSource,
    BoostClientCardStatus,
)
from webapps.boost.events import (
    marketplace_transaction_status_created,
    marketplace_transaction_successfully_paid,
)
from webapps.boost.payment.utils import get_payment_provider
from webapps.boost.tools import value_or_0
from webapps.business.enums import (
    BoostPaymentSource,
    PriceType,
)
from webapps.business.models import (
    Business,
    BusinessPromotion,
    Service,
    SERVICE_VARIANT_CLIENT_DISCOUNT,
)
from webapps.business.models.business_change import BusinessChange
from webapps.business.models.category import BusinessCategory
from webapps.images.enums import ImageTypeEnum
from webapps.images.tools import (
    ImageThumbnailsMixin,
    get_cms_content_path,
    get_mp_slots_path,
    get_seo_content_path,
)
from webapps.marketplace.cms.consts import SEO_CMS_MAX_CATEGORY_PROMOTED_REGIONS
from webapps.marketplace.cms.enums import SeoCmsContentType, SeoFeatureFlagValueType
from webapps.marketplace.enums import (
    SEO_METADATA_SUBTYPES,
    SEO_METADATA_TYPES,
    SEO_METADATA_TYPE__CUSTOM,
)
from webapps.marketplace.overdues_cutoff_date import (
    get_cutoff_date_for_business,
    is_the_global_cutoff_date_valid,
)
from webapps.marketplace.utils import get_business_id
from webapps.navision.enums import TransactionStatus, TransactionType
from webapps.navision.models import TaxRate
from webapps.navision.ports.tax_rates import TaxForNetSummary
from webapps.pos.enums import receipt_status
from webapps.pos.models import Transaction, TransactionRow
from webapps.structure.models import Region
from webapps.user.models import User

logger = logging.getLogger('booksy.marketplace')

BUSINESS_ACQUISITION_TYPE__INVITE = 'I'
BUSINESS_ACQUISITION_TYPE__CLAIM = 'C'
BUSINESS_ACQUISITION_TYPES = (
    (BUSINESS_ACQUISITION_TYPE__INVITE, 'Invite'),
    (BUSINESS_ACQUISITION_TYPE__CLAIM, 'Claim'),
)

MAX_NUMBER_SLOTS = 7
MAX_NUMBER_CH_DESCRIPTION_MP = 1200
MAX_NUMBER_CH_TITLE_MP = 120
MAX_NUMBER_CH_BUTTON_MP = 120

MATCH_TRATMENT_ASSIGNED = 'A'
MATCH_TRATMENT_MATCHED = 'M'
MATCH_TRATMENT_CORRECT = 'C'
MATCH_TRATMENT_REJECTED = 'R'

MATCH_TREATMENT_CHOICES = (
    (MATCH_TRATMENT_ASSIGNED, 'Assigned'),
    (MATCH_TRATMENT_MATCHED, 'Matched'),
    (MATCH_TRATMENT_CORRECT, 'Correct'),
    (MATCH_TRATMENT_REJECTED, 'Rejected'),
)

MATCH_TREATMENT_BATCH_SIZE = 200

# As for now - only [score, price] choice
# issues 47116 + 49215
CMS_SEARCH_ORDER_CHOICES = [
    ('score', 'score (default)'),
    ('price', 'price'),
]

SEO_METADATA_SEARCH_ORDER_CHOICES = [
    ('score', 'score (default)'),
    ('price', 'price'),
    ('popularity', 'popularity'),
]


class TypeChoice(Enum):
    Art = 'article'  # pylint: disable=invalid-name
    # Iterv = 'interview'


class CmsContent(ArchiveModel, ESDocMixin):
    es_doc_type = ESDocType.CMS_CONTENT

    region = models.ForeignKey(
        'structure.Region',
        related_name='search_region_slots',
        on_delete=models.CASCADE,
    )

    category = models.ForeignKey(
        'business.BusinessCategory',
        related_name='search_category_slots',
        on_delete=models.CASCADE,
    )
    search_order = models.CharField(
        choices=CMS_SEARCH_ORDER_CHOICES,
        default=settings.DEFAULT_SORT_ORDER,
        max_length=10,
    )

    title = models.CharField(max_length=500, null=True, blank=True)
    body = models.TextField(null=True, blank=True)
    image = models.ImageField(
        blank=True,
        null=True,
        storage=CMSContentStorage(),
        upload_to=get_cms_content_path,
    )
    # image_url = models.URLField(blank=True, null=True)
    author = models.CharField(max_length=250, null=True, blank=True)
    url = models.CharField(max_length=2000, null=True, blank=True)
    type = models.CharField(
        max_length=5, choices=[(slot_type.name, slot_type.value) for slot_type in TypeChoice]
    )
    visible = models.BooleanField(default=False, blank=True)

    class Meta:
        unique_together = ('region', 'category', 'search_order')
        verbose_name = 'SEO Content listings'
        verbose_name_plural = verbose_name

    @classmethod
    def get_cms_content(
        cls,
        region_id: int,
        category_ids: List[int],
        treatment_ids: List[int],
        order: Optional[str] = None,
    ) -> Optional[Dict[str, Any]]:
        # pylint: disable=cyclic-import
        from webapps.marketplace.elasticsearch.elasticsearch import CmsContentHitSerializer
        from webapps.marketplace.elasticsearch.searchables import CmsContentSearchable

        category_ids = treatment_ids if treatment_ids else category_ids
        if category_ids and len(category_ids) == 1 and region_id:
            order = order or settings.DEFAULT_SORT_ORDER
            cms_content = (
                CmsContentSearchable(
                    ESDocType.CMS_CONTENT,
                    serializer=CmsContentHitSerializer,
                )
                .params(
                    size=1,
                )
                .execute(
                    {
                        'region_id': region_id,
                        'category_id': category_ids[0],
                        'order': order,
                    }
                )
            )
            if cms_content.hits.total.value > 0:
                return cms_content[0].to_dict()

    def __str__(self):
        return (
            f"CmsContent: {self.title}, region[{self.region_id}], "
            f"category[{self.category_id}], order[{self.search_order}]"
        )


class SeoMetadata(ArchiveModel, ESDocMixin):
    es_doc_type = ESDocType.SEO_METADATA

    class Meta:
        verbose_name = 'SEO metadata'
        verbose_name_plural = 'SEO metadata'
        unique_together = (
            'region',
            'category',
            'sub_type',
        )

    def __str__(self):
        return (
            f'{self.__class__.__name__} id={self.id}, sub_type={self.get_sub_type_display()}, '
            f'region={self.region}, category={self.category}'
        )

    def validate_unique(self, exclude=None):
        super().validate_unique(exclude)
        if (
            SeoMetadata.objects.exclude(
                id=self.id,
            )
            .filter(
                type=SEO_METADATA_TYPE__CUSTOM,
                sub_type=self.sub_type,
                region=self.region,
                category=self.category,
            )
            .exists()
        ):
            raise ValidationError(
                'SEO metadata with this subtype, region, and category already exists.'
            )

    type = models.CharField(
        choices=SEO_METADATA_TYPES,
        default=SEO_METADATA_TYPE__CUSTOM,
        max_length=1,
    )
    sub_type = models.CharField(  # Only for type FIXED
        choices=SEO_METADATA_SUBTYPES,
        max_length=2,
    )
    region = models.ForeignKey(
        'structure.Region',
        related_name='seo_metadata',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    category = models.ForeignKey(
        'business.BusinessCategory',
        related_name='seo_metadata',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    search_order = deprecate_field(
        models.CharField(
            choices=SEO_METADATA_SEARCH_ORDER_CHOICES,
            max_length=10,
            null=True,
            blank=True,
        )
    )
    title_pattern = models.TextField(max_length=500, blank=False)
    description_pattern = models.TextField(max_length=500, blank=False)
    h1_pattern = models.TextField(max_length=500, blank=True)
    active = models.BooleanField(default=True)


class BusinessNetwork(DirtyFieldsMixin, ArchiveModel):
    DEEPLINK_MAPPING = {
        'show_businesses': 'deeplink_ids',
        'show_businesses_network': 'deeplink_slug',
    }
    DEEPLINK_REGENERATE = {
        'show_businesses': 'business_ids',
        'show_businesses_network': 'slug',
    }

    business_ids = ArrayField(models.IntegerField(), blank=False, null=False)
    name = models.CharField(max_length=128, blank=False, null=False, unique=True)
    slug = models.SlugField(max_length=32, blank=False, null=False, unique=True)
    deeplink_ids = models.CharField(max_length=128, blank=True, null=True)
    deeplink_slug = models.CharField(max_length=128, blank=True, null=True)

    is_enterprise = models.BooleanField(
        default=False,
        help_text='Mark this if the network is for an enterprise deal.',
    )

    objects = ArchiveManager()
    all_objects = models.Manager()

    def get_mobile_link(self, link_code):
        if link_code == 'show_businesses':
            business_ids = ','.join(str(i) for i in self.business_ids)
            return f'show_businesses/{business_ids}'
        if link_code == 'show_businesses_network':
            return f'show_businesses_network/{self.slug}'

    def get_desktop_url(self, link_code):
        if link_code == 'show_businesses':
            business_ids = ','.join(str(i) for i in self.business_ids)
            url = urllib.parse.urljoin(
                settings.MARKETPLACE_URL,
                f'/{settings.MARKETPLACE_LANG_COUNTRY}/net/{business_ids}',
            )
        elif link_code == 'show_businesses_network':
            url = urllib.parse.urljoin(
                settings.MARKETPLACE_URL,
                f'/{settings.MARKETPLACE_LANG_COUNTRY}/net/{self.slug}',
            )
        else:
            raise NotImplementedError
        params = {
            'do': 'invite',
        }
        return build_or_update_url(url, params)

    def get_mp_deeplink(self, link_code='show_businesses_network', force=False, dry_run=False):
        """Deeplink to Marketplace page."""

        if link_code not in self.DEEPLINK_MAPPING:
            raise NotImplementedError

        attr_name = self.DEEPLINK_MAPPING[link_code]
        branchio_url = getattr(self, attr_name, '')

        if not branchio_url or force:
            # generate deeplink
            branchio_url = generate_deeplink(
                app_type='C',
                data={
                    'mobile_deeplink': self.get_mobile_link(link_code),
                    '$desktop_url': self.get_desktop_url(link_code),
                },
            )
            if branchio_url is not None:
                # save it in DB
                setattr(self, attr_name, branchio_url)
                if not dry_run:
                    self.save(update_fields=[attr_name])

        if branchio_url is not None:
            return branchio_url

        # fallback to mp_deeplink
        return f'{mp_deeplink(link_code)}/{self.slug}'

    def __repr__(self):
        return f'<BusinessNetwork: {self.id}>'

    def __str__(self):
        return f'BusinessNetwork {self.name}: {self.business_ids}'

    @staticmethod
    def pre_save_handler(
        sender, instance, update_fields, **kwargs
    ):  # pylint: disable=unused-argument
        """Regenerate deeplinks if data has been modified."""
        dirty = instance.get_dirty_fields()
        for link_code, attr_name in BusinessNetwork.DEEPLINK_REGENERATE.items():
            if attr_name in dirty or not getattr(instance, attr_name, ''):
                instance.get_mp_deeplink(
                    link_code=link_code,
                    force=True,
                    dry_run=True,  # only set an attr that will later be saved
                )


pre_save.connect(BusinessNetwork.pre_save_handler, BusinessNetwork)


def get_default_min_commission():
    return settings.BOOST.MIN_COMMISSION


class MarketplaceCommissionQuerySet(AutoUpdateQuerySet):
    def minimal_computable_objects(self):
        return self.only(*self.model.COMPUTABLE_FIELDS)

    def get_by_business(self, business, date=None):
        """returns MarketplaceCommission valid to given business during given date"""
        date = date or tznow()
        if MarketplaceCommissionQuerysetOptimizationFlag():
            return (
                self.filter(  # consider creating separated QuerySet common with Promotion
                    Q(
                        business_promotions__business=business,
                        business_promotions__promotion_start__lte=date,
                    )
                    & Q(
                        Q(business_promotions__promotion_end__isnull=True)
                        | Q(business_promotions__promotion_end__gte=date)
                    )
                )
                .order_by('-created')
                .first()
            )

        return (
            self.filter(  # consider creating separated QuerySet common with Promotion
                business_promotions__business=business,
                business_promotions__promotion_start__lte=date,
            )
            .filter(
                Q(business_promotions__promotion_end__isnull=True)
                | Q(business_promotions__promotion_end__gte=date)
            )
            .order_by('-created')
            .first()
        )

    def delete(self, recalculate=True):
        commissions_ids = list(self.values_list('id', flat=True))
        MarketplaceCommission.objects.filter(id__in=commissions_ids).update(end_date=tznow())
        if recalculate:
            MarketplaceCommission.recalculate_commission(commissions=commissions_ids)
        return len(commissions_ids)


class MarketplaceCommissionManager(AutoUpdateManager.from_queryset(MarketplaceCommissionQuerySet)):
    pass


class MarketplaceCommission(ArchiveModel):
    # used to calculate get_mp_promotion in
    # case business do not have MarketplaceCommission object
    DEFAULT_ES_COMMISSION = 20.0

    class Meta:
        constraints = [
            models.CheckConstraint(
                check=(
                    Q(business__isnull=False, region__isnull=True)
                    | Q(business__isnull=True, region__isnull=False)
                ),
                name='exactly_one_business_or_region',
            ),
        ]

    business = models.ForeignKey(
        'business.Business',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    region = models.ForeignKey(
        'structure.Region',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    commission = models.IntegerField(
        null=True,
        default=0,
        validators=[
            MinValueValidator(0),
            MaxValueValidator(100),
        ],
        verbose_name='Commision (%)',
    )
    bottom_cap = models.DecimalField(
        null=True,
        default=0,
        decimal_places=2,
        max_digits=10,
        validators=[
            MinValueValidator(0.00),
        ],
        verbose_name='Unpriced service',
    )
    # TODO change `max` to `maximum_commission`
    max = models.DecimalField(
        default=0,
        decimal_places=2,
        max_digits=10,
        validators=[
            MinValueValidator(0.00),
        ],
    )
    minimum_commission = models.DecimalField(
        default=get_default_min_commission,
        decimal_places=2,
        max_digits=10,
    )
    marketplace = models.BooleanField(
        default=False,
        verbose_name='Marketplace availability',
    )
    start_date = models.DateTimeField(default=tznow)
    end_date = models.DateTimeField(null=True, blank=True)

    parent = models.ForeignKey(
        'self',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
    )

    flat_fee = models.DecimalField(
        decimal_places=2,
        default=Decimal(0),
        max_digits=10,
        validators=[
            MinValueValidator(0.00),
        ],
    )

    COMPUTABLE_FIELDS = (  # only fields needed to calculate commission
        'commission',
        'bottom_cap',
        'max',
        'minimum_commission',
        'marketplace',
        'flat_fee',
    )

    objects = MarketplaceCommissionManager()

    def clean(self):
        super().clean()
        if self.business and not self.business.region:
            raise ValidationError('Cannot add commission to business without region')
        if self.bottom_cap is not None and self.max is not None and self.bottom_cap > self.max:
            raise ValidationError('Bottom cap value should be smaller than Max value')
        if (
            self.minimum_commission is not None
            and self.max is not None
            and self.minimum_commission > self.max
        ):
            raise ValidationError('Min value should be smaller than Max value')

    def save(
        self, force_insert=False, force_update=False, using=None, update_fields=None, **kwargs
    ):
        self.clean()

        now = tznow()
        old_id = self.id  # pylint: disable=access-member-before-definition
        if old_id:
            self.id = None  # pylint: disable=invalid-name
            self.start_date = now
            self.parent_id = old_id
            self.end_date = None
            MarketplaceCommission.objects.filter(id=old_id).delete(recalculate=False)

        super().save(force_insert, force_update, using, update_fields, **kwargs)

        MarketplaceCommission.objects.exclude(id=self.id).filter(
            Q(business=self.business, business__isnull=False)
            | Q(region=self.region, region__isnull=False)
        ).filter(end_date__isnull=True,).delete(recalculate=False)

    def delete(self, using=None, keep_parents=False):
        MarketplaceCommission.objects.filter(
            id=self.id,
            end_date__isnull=True,
        ).delete()

    @staticmethod
    def is_appointment_paid_by_pos(appointment):
        return (
            Transaction.objects.filter(
                appointment=appointment,
                latest_receipt__status_code__in=[
                    receipt_status.PAYMENT_SUCCESS,
                    receipt_status.SENT_FOR_REFUND,
                ],
                children__isnull=True,
            )
            .exclude(
                transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
            )
            .exists()
        )

    @staticmethod
    def get_pos_transaction(booking):
        rows = TransactionRow.objects.filter(
            subbooking=booking,
            transaction__latest_receipt__status_code__in=[
                receipt_status.PAYMENT_SUCCESS,
                receipt_status.SENT_FOR_REFUND,
            ],
            transaction__children__isnull=True,
        ).exclude(transaction__transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE)
        return rows.first()

    def get_price_and_conditions_of_subbooking(self, subbooking, return_0_on_no_pos=False):
        # pylint: disable=too-many-return-statements
        pos_row = self.get_pos_transaction(subbooking)
        if pos_row and pos_row.discounted_total is not None:
            return pos_row.discounted_total, {}

        if not pos_row and return_0_on_no_pos:
            return Decimal('0.00'), {}

        service_variant_type = subbooking.service_data.service_variant_type
        if service_variant_type not in PriceType.has_price():
            return Decimal('0.00'), {}

        if subbooking.resolved_price is not None:
            addons_value_to_subtract = Decimal('0.00')
            if subbooking.resolved_promotion_type == SERVICE_VARIANT_CLIENT_DISCOUNT:
                for addon in subbooking.addons_set.all():
                    if not addon.total_price.value:
                        continue
                    addon_discounted_value = addon.total_price * (
                        Decimal('1.00') - Decimal(subbooking.resolved_discount) / 100
                    )
                    addons_value_to_subtract += addon_discounted_value.value
            final_price = subbooking.resolved_price - addons_value_to_subtract
            addons_value_subtracted = (
                {'addons_value_subtracted': float(addons_value_to_subtract)}
                if addons_value_to_subtract
                else {}
            )
            return final_price, {
                'promotion': {
                    'type': subbooking.resolved_promotion_type,
                    'amount': float(final_price),
                    'discount': f'{subbooking.resolved_discount}%',
                    **addons_value_subtracted,
                }
            }

        return subbooking.service_data.service_variant_price or Decimal(0), {}

    def get_commission_by_booking_price(self, booking_price):
        if not self.marketplace or self.commission == 0:
            return Decimal('0.00')

        if booking_price:
            return (self.commission * booking_price / Decimal(100)).quantize(
                Decimal('.01'), rounding=ROUND_HALF_UP
            )

        return self.bottom_cap

    def count_commission(self, booking):
        conditions = {}
        booking_price = Decimal(0)

        if not booking or not booking.payable:
            return {
                'amount': Decimal(0),
                'booking_price': Decimal(0),
            }

        is_appointment_paid_by_pos = MarketplaceCommission.is_appointment_paid_by_pos(
            booking.appointment
        )

        combo_children_components = []
        for combo_child in booking.combo_children:
            price, conditions = self.get_price_and_conditions_of_subbooking(
                combo_child, return_0_on_no_pos=is_appointment_paid_by_pos
            )
            price = price.quantize(Decimal('.01'), rounding=ROUND_HALF_UP)
            combo_children_components.append(
                {
                    "type": "combo_child",
                    "subbooking_id": combo_child.id,
                    "price": float(price),
                    "conditions": conditions,
                }
            )
            booking_price += price

        if not combo_children_components:
            booking_price, conditions = self.get_price_and_conditions_of_subbooking(booking)

        amount = self.get_commission_by_booking_price(booking_price)

        return {
            'amount': amount,
            'booking_price': booking_price,
            'conditions': conditions,
            **({'components': combo_children_components} if combo_children_components else {}),
        }

    @classmethod
    def _commission_search(cls, business, query, region):  # consider moving to model's queryset
        commission = cls.objects.filter(business=business).filter(query).order_by('-id').first()

        if commission:
            return commission

        regions = region.get_parents_with_me()
        regions = Region.sort_regions(regions)

        commissions = cls.objects.filter(region__in=regions).filter(query).order_by('-id')

        if not commissions:
            return None
        return sorted(commissions, key=lambda obj: -list(regions).index(obj.region))[0]

    @classmethod
    def get_commission(cls, business, date=None):  # consider moving to model's queryset
        if date:
            query = Q(start_date__lte=date) & (Q(end_date__gte=date) | Q(end_date__isnull=True))
        else:
            query = Q(end_date__isnull=True)

        region = business.region
        if not region:
            return None

        return cls._commission_search(business, query, region)

    def _copy_for_business_with_defined_marketplace_availability(self, business_id, marketplace):
        return MarketplaceCommission.objects.create(
            business_id=business_id,
            commission=self.commission,
            bottom_cap=self.bottom_cap,
            max=self.max,
            minimum_commission=self.minimum_commission,
            marketplace=marketplace,
        )

    def create_blocking_commission(self, business_id):
        return self._copy_for_business_with_defined_marketplace_availability(
            business_id, marketplace=False
        )

    def create_commission_after_ban(self, business_id):
        return self._copy_for_business_with_defined_marketplace_availability(
            business_id, marketplace=True
        )

    @classmethod
    def get_commission_value(cls, booking):
        from webapps.booking.models import Appointment

        appointment = (
            Appointment.objects.filter(id=booking.appointment_id)
            .only('created', 'business_id', 'booked_for')
            .first()
        )
        if (
            BoostChargingForBusinessBookingsFlag(UserData(subject_key=appointment.business_id))
            and BoostChargingForBusinessBookingsNoPromoFixFlag()
        ):
            originally_first_appointment = appointment.booked_for.originally_first_appointment
            commission = cls.objects.minimal_computable_objects().get_by_business(
                business=appointment.business_id,
                date=originally_first_appointment.created,
            )
        else:
            commission = cls.objects.minimal_computable_objects().get_by_business(
                business=appointment.business_id,
                date=appointment.created,
            )

        if commission:
            commission_dict = commission.count_commission(booking)
            return commission_dict

        return {
            'amount': None,
            'booking_price': None,
        }

    @classmethod
    def get_future_commission_value(
        cls,
        business: Business,
        price: Union[int, float, Decimal, None],
        date: datetime = None,
    ) -> Decimal:
        if price is None:
            return Decimal(0)

        commission = cls.objects.minimal_computable_objects().get_by_business(
            business=business, date=date
        )
        if not commission:
            return Decimal(0)

        commission_rate = Decimal((commission.commission or 0) / 100)
        return Decimal(price * commission_rate)

    @classmethod
    def recalculate_commission(cls, commissions):
        # pylint: disable=cyclic-import
        from webapps.marketplace.tasks import recalculate_commissions_task

        businesses = list(
            Business.objects.filter(
                businesspromotion__commission__in=commissions,
                boost_status__in=Business.BoostStatus.active_statuses(),
            )
            .distinct()
            .values_list('id', flat=True)
        )
        for i, package in enumerate(grouper(businesses, 100)):
            recalculate_commissions_task.apply_async((package,), countdown=i * 12)

    def __str__(self):
        if self.business:
            return f'Commission for business {self.business}'
        if self.region:
            return f'Commission for region {self.region.name}'

        return f'Commission id {self.id}'

    @staticmethod
    def push_waiting_businesses(business):
        from webapps.notification.tasks.push import send_push_notification
        from webapps.notification.push import notification_receivers_list

        send_push_notification(
            (
                notification_receivers_list(user_id=business.owner.id, customer=False)
                if business.owner is not None
                else []
            ),
            str(
                _(
                    "Boost is here! Your time on the waiting list is up. "
                    "Learn how to grow your business."
                )
            ),
            target=('marketplace_available',),
        )

    @staticmethod
    def post_save_handler(sender, instance, **kwargs):  # pylint: disable=unused-argument
        from webapps.marketplace.tasks import (
            push_waiting_businesses_task,
            recalculate_commissions_task,
        )

        if instance.business:
            recalculate_commissions_task.delay([instance.business.id])
        else:  # regional commission; algorithm described by BOOS-470
            should_check_business_region = instance.region.type != Region.Type.COUNTRY
            region_checking_filters = {}
            if should_check_business_region:
                region_checking_filters = {'region__in': instance.region.get_children_with_me()}
                # otherwise - assuming all regions belongs to the country,
                # there is no need to check if it belongs to any of them
            businesses = set(
                Business.objects.filter(
                    (
                        Q(businesspromotion__promotion_end__isnull=True)
                        | Q(businesspromotion__promotion_end__gt=tznow())
                    ),  # join with only active BusinessPromotion
                    boost_status__in=Business.BoostStatus.active_statuses(),
                    businesspromotion__commission__region__in=(
                        instance.region.get_parents_with_me()
                    ),  # these businesses have a chance to get more narrowed region commission...
                    **region_checking_filters,
                    # ...if their business.region is a part of the new commissions region
                ).values_list('id', flat=True)
            )

            for i, package in enumerate(grouper(list(businesses), 100)):
                recalculate_commissions_task.apply_async((package,), countdown=i * 12)

        if instance.marketplace:
            push_waiting_businesses_task.delay(instance.id)


post_save.connect(MarketplaceCommission.post_save_handler, MarketplaceCommission)


class MarketplaceTransactionQuerySet(ArchiveQuerySet):
    def annotate_total_amount(self):
        from webapps.boost.models import BoostAppointment

        if settings.BOOST.GROSS_VALUE:
            amount = 'gross_amount'
        else:
            amount = 'amount'
        return self.annotate(
            total_amount=Subquery(
                BoostAppointment.objects.payable_boost_appointments()
                .filter(
                    transaction_id=OuterRef('pk'),
                )
                .values('transaction')
                .annotate(total=Sum(amount))
                .values('total')
            )
        )

    def failed(self):
        return self.annotate(
            tran_status=Subquery(
                MarketplaceTransactionStatus.objects.filter(
                    transaction_id=OuterRef('pk'),
                    type=MarketplaceTransactionType.CHARGE,
                )
                .order_by('-charge_date')
                .values_list('status', flat=True)[:1]
            )
        ).filter(tran_status__in=MarketplaceTransactionStatusEnum.failed_statuses())

    def all_debts(self, business_id):  # will be removed with BOOS-743
        user_data = UserData(
            custom={CustomUserAttributes.BUSINESS_ID: get_business_id(business_id)}
        )
        if BoostOverduesBasedOnCutoffDateFlag(user_data) and is_the_global_cutoff_date_valid():
            return self.boost_debts(business_id=business_id)
        return self.filter(business_id=business_id).failed().order_by('id')

    def boost_debts(self, business_id):
        threshold = MarketplaceTransaction.debt_collection_datetime_limit(business_id=business_id)
        return (
            self.failed()
            .annotate_total_amount()
            .filter(business_id=business_id, created__gte=threshold, total_amount__gt=0)
            .order_by('id')
        )

    def all_debts_value(self, business_id):
        from webapps.boost.models import BoostAppointment

        if settings.BOOST.GROSS_VALUE:
            amount = 'gross_amount'
        else:
            amount = 'amount'
        debts_amount = (
            BoostAppointment.objects.filter(transaction__in=self.boost_debts(business_id))
            .payable_boost_appointments()
            .aggregate(total_amount=Sum(amount))['total_amount']
        )
        return Decimal(0) if debts_amount is None else debts_amount

    def first_debt(self, business_id):
        return self.all_debts(business_id=business_id).first()


class MarketplaceTransactionManager(
    ArchiveManager.from_queryset(MarketplaceTransactionQuerySet),
):
    pass


class MarketplaceTransaction(ArchiveModel):
    class Meta:
        constraints = [
            models.UniqueConstraint(
                name='unique_open_transaction',
                fields=['business'],
                condition=Q(status=MarketplaceTransactionProgressStatus.OPEN),
            )
        ]

    status = models.CharField(
        max_length=1,
        choices=MarketplaceTransactionProgressStatus.choices(),
        default=MarketplaceTransactionProgressStatus.OPEN,
    )
    business = models.ForeignKey(
        'business.Business',
        related_name='marketplace_transactions',
        on_delete=models.PROTECT,
    )
    payment_source = models.CharField(
        max_length=1, choices=TransactionPaymentSource.choices(), null=False
    )

    @property
    def public_id(self):
        return f'marketplace_transaction:{self.id}'

    @property
    def payable_rows(self):
        return MarketplaceTransactionRow.objects.filter(
            boost_appointment__transaction=self,
            boost_appointment__deleted__isnull=True,
            refunded=False,
            subbooking__payable=True,
            status__in=BoostAppointmentStatus.payable_statuses(),
        )

    @property
    def payable_boost_appointments(self):
        return self.boost_appointments.payable_boost_appointments()

    @property
    def total(self):
        if settings.BOOST.GROSS_VALUE:
            amount = 'gross_amount'
        else:
            amount = 'amount'
        _total = self.payable_boost_appointments.aggregate(sum=Sum(amount))['sum']
        return _total if _total else Decimal(0)

    @property
    def transaction_status(self):
        return (
            self.statuses.filter(type=MarketplaceTransactionType.CHARGE)
            .order_by('charge_date')
            .last()
        )

    def set_status(
        self, tran_resp, errors, objs, transaction_type, are_these_boost_appointments=False
    ):  # pylint: disable=too-many-arguments
        # pylint: disable=too-many-positional-arguments
        # pylint: disable=cyclic-import
        from webapps.boost.models import BoostAppointment, BoostAppointmentToStatus

        if not tran_resp.is_success and not tran_resp.id:
            status = MarketplaceTransactionStatusEnum.FAILED
        else:
            status = tran_resp.status

        transaction_status = MarketplaceTransactionStatus(
            status=status,
            charge_date=tznow(),
            transaction=self,
            external_id=tran_resp.id,
            errors=errors,
            type=transaction_type,
            response_code=tran_resp.response_code,
            response_text=tran_resp.response_text,
            response_type=tran_resp.response_type,
            gateway_rejection_reason=tran_resp.gateway_rejection_reason,
            amount=tran_resp.amount,
        )
        transaction_status.save()

        if are_these_boost_appointments:
            batses = BoostAppointmentToStatus.create_bats(objs, transaction_status)
        else:
            batses = BoostAppointmentToStatus.create_bats(
                BoostAppointment.objects.filter(rows__in=objs).distinct(), transaction_status
            )

        statuses_wanted_by_navision = {
            *MarketplaceTransactionStatusEnum.succeeded_statuses(),
            *MarketplaceTransactionStatusEnum.failed_statuses(),
        } - {MarketplaceTransactionStatusEnum.VOIDED}
        if status in statuses_wanted_by_navision:
            boost_appointments_ids = [bats.boost_appointment.id for bats in batses]
            self.send_event_after_creating_mts(
                tran_resp, transaction_status, status, transaction_type, boost_appointments_ids
            )

        return transaction_status

    def shall_be_charged(self):
        if self.transaction_status and not self.transaction_status.is_failed:
            last_transaction_status = self.statuses.order_by('charge_date').last()
            return last_transaction_status.status == MarketplaceTransactionStatusEnum.VOIDED
        return True

    def payment_issue_notification_to_be_sent(self, last_transaction_response, success_rate):
        from webapps.marketplace.notifications import (
            BoostPaymentIssueEmailAppleGoogleNotification,
        )

        notification_class = {
            Business.PaymentSource.ITUNES: BoostPaymentIssueEmailAppleGoogleNotification,
            Business.PaymentSource.PLAY: BoostPaymentIssueEmailAppleGoogleNotification,
        }.get(self.business.payment_source)

        if not notification_class:
            return None

        hard_declined = MarketplaceTransactionDeclineType.HARD_DECLINE.label
        if (
            self.business.payment_source
            in (
                Business.PaymentSource.BRAINTREE_BILLING,
                Business.PaymentSource.ITUNES,
                Business.PaymentSource.PLAY,
            )
        ) and (
            last_transaction_response.response_type == hard_declined
            or (success_rate <= 0.1 and not last_transaction_response.is_success)
            or self.check_three_soft_declines(last_response=last_transaction_response)
        ):
            return notification_class

    def pay_async(self):
        from webapps.marketplace.tasks import boost_pay_task

        boost_pay_task.delay(self.id)

    def pay(self):
        try:
            lock = BoostChargeLock.lock(self.id)
        except RedlockError:
            lock = None

        if not lock:
            logger.warning('Skip charge for marketplace_transaction %s', self.id)
            return

        if not self.shall_be_charged():
            BoostChargeLock.unlock(lock)
            return self.transaction_status

        update_fields = []

        payment_source = self.get_payment_source()
        if self.payment_source != payment_source:
            self.payment_source = payment_source
            update_fields.append('payment_source')

        self.update_amounts()

        if self.status != MarketplaceTransactionProgressStatus.CLOSED:
            self.status = MarketplaceTransactionProgressStatus.CLOSED
            update_fields.append('status')

        if update_fields:
            self.save(update_fields=update_fields)

        payment_processor = get_payment_provider(self.payment_source)
        if not payment_processor:
            BoostChargeLock.unlock(lock)
            return

        errors, transaction_response = payment_processor.pay(self)
        last_status = self.handle_payment_response(errors, transaction_response)
        BoostChargeLock.unlock(lock)
        return last_status

    def handle_payment_response(self, errors, transaction_response):
        if not transaction_response.is_success:
            if transaction_response.gateway_rejection_reason == 'duplicate':
                last_status = self.statuses.order_by('-charge_date').first()
                if (
                    last_status.type == MarketplaceTransactionType.CHARGE
                    and last_status.status
                    in (
                        *MarketplaceTransactionStatusEnum.processing_statuses(),
                        *MarketplaceTransactionStatusEnum.finished_statuses(),
                    )
                ):
                    # we consider this our mistake and do not save 'duplicated' status;
                    # it solves the problem described by BOOS-264
                    return last_status

            business = self.business
            success_rate = MarketplaceTransaction.get_success_rate(business)
            if payment_issue_notification_class := self.payment_issue_notification_to_be_sent(
                last_transaction_response=transaction_response, success_rate=success_rate
            ):
                payment_issue_notification_class(business=business).send_limited()

        last_status = self.set_status(
            transaction_response,
            errors,
            self.payable_boost_appointments,
            MarketplaceTransactionType.CHARGE,
            True,
        )

        if not transaction_response.is_success and self.boost_should_be_disabled(self.business):
            self.disable_boost_for_business(self.business)

        if transaction_response.is_success:
            marketplace_transaction_successfully_paid.send(
                self.__class__.__name__,
                business_id=self.business.id,
            )

        return last_status

    @classmethod
    def boost_should_be_disabled(cls, business):
        from webapps.boost.models import BoostSettings

        business_debts = cls.objects.boost_debts(business)
        if not business_debts:
            return False

        boost_settings = BoostSettings.get_current_settings()

        if boost_settings.total_threshold_enabled:
            business_total_debt = cls.objects.all_debts_value(business)
            if business_total_debt >= boost_settings.total_threshold:
                return True

        if boost_settings.days_threshold_enabled:
            business_in_debt_from = (
                business_debts.first().created.astimezone(business.get_timezone()).date()
            )
            today = business.tznow.date()
            time_in_debt = today - business_in_debt_from

            if time_in_debt >= timedelta(days=boost_settings.days_threshold):
                return True

        return False

    def disable_boost_for_business(self, business):
        BusinessPromotion.cache_promotion_updater(
            business.id,
            BusinessPromotion.CARD,
        )
        _metadata = {'function': f'MarketplaceTransaction <{self.id}>.pay()'}
        with BusinessChange.recording_with_saving(business, metadata=_metadata):
            business.boost_status = Business.BoostStatus.DISABLED
            business.has_braintree = False

    @staticmethod
    def get_success_rate(business):
        failed_statuses = MarketplaceTransactionStatusEnum.failed_statuses()
        successes = (
            business.marketplace_transactions.all()
            .annotate(
                last_status=Subquery(
                    MarketplaceTransactionStatus.objects.filter(
                        transaction=OuterRef('id'),
                        type=MarketplaceTransactionType.CHARGE,
                    )
                    .order_by('-created')[:1]
                    .values_list('status', flat=True)
                )
            )
            .aggregate(
                sum_fails=Count('last_status', filter=Q(last_status__in=failed_statuses)),
                num_total=Count('last_status'),
            )
        )
        num_total = successes['num_total']
        sum_fails = successes['sum_fails']
        if num_total < 10:
            return 1

        return 1 - (sum_fails / num_total)

    def check_three_soft_declines(self, last_response):
        past_responses_num_to_check = 2  # 2 from the base + 1 as `last_response`
        soft_declined = MarketplaceTransactionDeclineType.SOFT_DECLINE.label

        previous_response_types = (
            self.statuses.filter(type=MarketplaceTransactionType.CHARGE)
            .order_by('-charge_date')
            .values_list('response_type', flat=True)[:past_responses_num_to_check]
        )

        return (
            last_response.response_type == soft_declined
            and len(previous_response_types) == past_responses_num_to_check
            and all(response_type == soft_declined for response_type in previous_response_types)
        )

    def refund(self, objs, are_these_boost_appointments=False):
        raw_ids = tuple(sorted([obj.id for obj in objs]))
        hashed_rows = hash(raw_ids)
        try:
            lock_ = BoostRefundLock.lock(hashed_rows)
        except RedlockError:
            lock_ = None

        if not lock_:
            what_is_refunded = (
                'boost_appointments'
                if are_these_boost_appointments
                else 'marketplace_transaction_rows'
            )
            logger.warning('Skip refund for %s %s', what_is_refunded, raw_ids)
            return

        status = self.transaction_status
        if not status or not status.external_id:
            return

        payment_processor = get_payment_provider(self.payment_source)
        if not payment_processor:
            return

        if settings.BOOST.GROSS_VALUE:
            amount = 'gross_amount'
        else:
            amount = 'amount'
        total = sum(getattr(obj, amount) for obj in objs)

        errors, transaction_response = payment_processor.refund(self, total)

        if transaction_response.is_success:
            if are_these_boost_appointments:
                MarketplaceTransactionRow.objects.filter(
                    boost_appointment__in=raw_ids,
                    deleted__isnull=True,
                ).update(refunded=True)
                # we keep updating `refunded` field, because otherwise we couldn't disable
                # BoostRefundByBAFlag
            else:
                MarketplaceTransactionRow.objects.filter(id__in=(r.id for r in objs)).update(
                    refunded=True
                )

        self.set_status(
            transaction_response,
            errors,
            objs,
            MarketplaceTransactionType.REFUND,
            are_these_boost_appointments,
        )

        if (
            transaction_response.is_success
            and transaction_response.status == MarketplaceTransactionStatusEnum.VOIDED
        ):
            self.pay_async()

    def update_amounts(self):
        for boost_appointment in self.payable_boost_appointments:
            boost_appointment.update_amount()

    @classmethod
    def get(cls, business):
        try:
            marketplace_transaction, _created = cls.objects.get_or_create(
                business=business, status=MarketplaceTransactionProgressStatus.OPEN
            )
        except IntegrityError:
            marketplace_transaction = cls.objects.get(
                business=business, status=MarketplaceTransactionProgressStatus.OPEN
            )
        return marketplace_transaction

    @staticmethod
    def get_cost(business, start_date, end_date):
        txn_values = (
            MarketplaceTransactionStatus.objects.filter(
                transaction__business=business,
                charge_date__gte=start_date,
                charge_date__lte=end_date,
                status__in=[
                    MarketplaceTransactionStatusEnum.SETTLED,
                    MarketplaceTransactionStatusEnum.SUCCEEDED,
                ],
            )
            .values('type')
            .annotate(
                cost=Coalesce(
                    Sum(value_or_0('boost_appointments__amount')), 0, output_field=DecimalField()
                ),
                gross_cost=Coalesce(
                    Sum(value_or_0('boost_appointments__gross_amount')),
                    0,
                    output_field=DecimalField(),
                ),
                tax=Coalesce(
                    Max('boost_appointments__tax_rate__tax_rate') * Value(100),
                    0,
                    output_field=DecimalField(),
                ),
            )
            .values('type', 'cost', 'gross_cost', 'tax')
        )
        txn_values = {item.pop('type'): item for item in txn_values}

        ret_dict = {
            'cost': txn_values.get('C', {}).get('cost', 0) - txn_values.get('R', {}).get('cost', 0),
            'gross_cost': (
                txn_values.get('C', {}).get('gross_cost', 0)
                - txn_values.get('R', {}).get('gross_cost', 0)
            ),
            'tax': txn_values.get('C', {}).get('tax', 0) or txn_values.get('R', {}).get('tax', 0),
        }

        return ret_dict

    def get_payment_source(self):
        if self.business.boost_payment_source == BoostPaymentSource.OFFLINE:
            return TransactionPaymentSource.OFFLINE
        if not self.business.has_new_billing:
            return TransactionPaymentSource.BRAINTREE
        return TransactionPaymentSource(get_business_payment_processor(self.business_id))

    @staticmethod
    def send_event_after_creating_mts(
        tran_resp, transaction_status, status, transaction_type, boost_appointments_ids
    ):
        transaction_navision_status = (
            TransactionStatus.SUCCESS
            if status in MarketplaceTransactionStatusEnum.succeeded_statuses()
            else TransactionStatus.FAILED
        )
        transaction_navision_type = (
            TransactionType.CHARGE
            if transaction_type == MarketplaceTransactionType.CHARGE
            else TransactionType.REFUND
        )
        event_signal_dict = {
            'external_transaction_id': str(tran_resp.id),
            'boost_appointments_ids': list(boost_appointments_ids),
            'transaction_status': transaction_navision_status,
            'transaction_type': transaction_navision_type,
            'transaction_dt': transaction_status.charge_date,
        }

        marketplace_transaction_status_created.send(_instance=None, **event_signal_dict)

    @staticmethod
    def debt_collection_datetime_limit(business_id: int | None = None):
        country_tz = gettz(settings.COUNTRY_CONFIG.default_time_zone)

        user_data = UserData(
            custom={CustomUserAttributes.BUSINESS_ID: get_business_id(business_id)}
        )
        if BoostOverduesBasedOnCutoffDateFlag(user_data) and is_the_global_cutoff_date_valid():
            business_cutoff_date = get_cutoff_date_for_business(business_id=business_id)
        else:
            business_cutoff_date = datetime_date(2023, 1, 1)
        date_limit = max(
            datetime(
                business_cutoff_date.year,
                business_cutoff_date.month,
                business_cutoff_date.day,
                tzinfo=country_tz,
            ),
            tznow(country_tz) - relativedelta(years=1),
        ).date()
        return datetime.combine(date_limit, datetime.min.time(), tzinfo=country_tz)

    def save(
        self, force_insert=False, force_update=False, using=None, update_fields=None, **kwargs
    ):
        if self.id is None:
            self.payment_source = self.get_payment_source()

            if update_fields and 'payment_source' not in update_fields:
                update_fields.append('payment_source')
        super().save(
            force_insert=force_insert,
            force_update=force_update,
            using=using,
            update_fields=update_fields,
            **kwargs,
        )

    objects = MarketplaceTransactionManager()
    all_objects = models.Manager()


# pylint: disable=too-many-instance-attributes
class MarketplaceTransactionRow(ArchiveModel, DirtyFieldsMixin):
    class Meta:
        indexes = [
            models.Index(fields=['subbooking', 'status'], name='mtr_booking_status_idx'),
            models.Index(fields=['transaction', 'subbooking'], name='mtr_transaction_booking_idx'),
            models.Index(
                fields=['status', 'refunded', 'created'], name='mtr_status_refund_created_idx'
            ),
        ]

    Status = BoostAppointmentStatus

    subbooking = models.OneToOneField(
        related_name='marketplace_transactions',
        to='booking.SubBooking',
        on_delete=models.PROTECT,
    )
    appointment = models.ForeignKey(
        to='booking.Appointment',
        on_delete=models.PROTECT,
    )
    boost_appointment = models.ForeignKey(
        to='boost.BoostAppointment',
        on_delete=models.PROTECT,
        related_name='rows',
        null=True,
    )
    amount = models.DecimalField(
        max_digits=10, decimal_places=2, verbose_name='Booksy commission'
    )  # DEPRECATED - use boost_appointment.amount (BOOS-309)
    # pylint: disable=duplicate-code
    gross_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='Booksy gross commission',
        null=True,
        default=None,
    )  # DEPRECATED - use boost_appointment.gross_amount (BOOS-309)
    tax_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='Commission tax',
        null=True,
        blank=True,
    )  # DEPRECATED - use boost_appointment.tax_amount (BOOS-309)
    tax_rate = models.ForeignKey(
        TaxRate, on_delete=models.PROTECT, null=True, blank=True
    )  # DEPRECATED - use boost_appointment.tax_rate (BOOS-309)
    # pylint: enable=duplicate-code
    uncapped_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='Uncapped Booksy commission',
    )
    booking_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
    )
    transaction = models.ForeignKey(
        related_name='rows',
        to='marketplace.MarketplaceTransaction',
        on_delete=models.PROTECT,
    )  # DEPRECATED - use boost_appointment.transaction (BOOS-309)
    components = models.JSONField(null=True)

    status = models.CharField(
        max_length=16,
        choices=Status.choices(),
        default=Status.PAYABLE,
    )  # DEPRECATED - use boost_appointment.status (BOOS-309)
    refunded = models.BooleanField(default=False)
    conditions = JSONField(blank=True, default=dict)
    claim = models.ForeignKey(
        'survey.PollChoice',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
    )  # replacement for marketplace_claim  # DEPRECATED - use boost_appointment.claim (BOOS-309)

    @classmethod
    def create_for_boost_appointment(cls, subbooking, boost_appointment):
        commision_dict = MarketplaceCommission.get_commission_value(subbooking)

        return MarketplaceTransactionRow.objects.create(
            subbooking=subbooking,
            amount=commision_dict['amount'],
            uncapped_amount=commision_dict['amount'],
            booking_price=commision_dict['booking_price'],
            conditions=commision_dict.get('conditions', {}),
            appointment=boost_appointment.appointment,
            status=boost_appointment.status,
            transaction_id=boost_appointment.transaction_id,
            boost_appointment_id=boost_appointment.id,
            components=commision_dict.get('components'),
        )

    def save(
        self, force_insert=False, force_update=False, using=None, update_fields=None, **kwargs
    ):
        dirty = self.get_dirty_fields()
        super().save(force_insert, force_update, using, update_fields, **kwargs)
        if 'status' in dirty:
            MarketplaceTransactionLog(row=self, status=self.status).save()

    def get_subookings_rows(self):
        return MarketplaceTransactionRow.all_objects.filter(
            appointment_id=self.appointment_id
        ).order_by('subbooking__booked_till')

    def update_amount_helper(self, max_amount, commission):
        commission_val = commission.count_commission(booking=self.subbooking)

        self.uncapped_amount = commission_val['amount']
        self.amount = min(self.uncapped_amount, max_amount)
        if tax_summary := TaxForNetSummary.for_business(
            business=self.appointment.business,
            service=TaxRate.Service.BOOST,
            net_price=self.amount,
            round_results=True,
        ):
            self.gross_amount = tax_summary.gross_price
            self.tax_amount = tax_summary.tax
            self.tax_rate = tax_summary.tax_rate

        self.booking_price = commission_val['booking_price']
        self.conditions = commission_val.get('conditions', {})
        self.save()

        return self.amount

    def recalculate_amount_with_minimum_commission(self, amount):
        self.amount = amount
        if tax_summary := TaxForNetSummary.for_business(
            business=self.appointment.business,
            service=TaxRate.Service.BOOST,
            net_price=self.amount,
            round_results=True,
        ):
            self.gross_amount = tax_summary.gross_price
            self.tax_amount = tax_summary.tax
            self.tax_rate = tax_summary.tax_rate

        self.save()

    objects = ArchiveManager()
    all_objects = models.Manager()


class MarketplaceTransactionLog(models.Model):
    row = models.ForeignKey(
        'marketplace.MarketplaceTransactionRow',
        on_delete=DO_NOTHING,
    )
    status = models.CharField(
        max_length=16,
        choices=BoostAppointmentStatus.choices(),
        null=True,
        blank=True,
    )
    updated = models.DateTimeField(auto_now=True)


class MarketplaceTransactionRowToStatus(models.Model):  # will be removed in BOOS-465
    status = models.ForeignKey(
        'MarketplaceTransactionStatus',
        on_delete=models.CASCADE,
        db_column='marketplacetransactionstatus_id',
    )
    transaction_row = models.ForeignKey(
        MarketplaceTransactionRow,
        on_delete=models.CASCADE,
        db_column='marketplacetransactionrow_id',
    )
    created = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Created (UTC)',
    )

    class Meta:
        db_table = 'marketplace_marketplacetransactionstatus_rows'


class MarketplaceTransactionStatus(ArchiveModel):
    class Meta:
        indexes = [
            models.Index(fields=['status', 'external_id'], name='mts_status_external_idx'),
            models.Index(fields=['type', 'transaction'], name='mts_type_transaction_idx'),
        ]

    status = models.CharField(max_length=32, choices=MarketplaceTransactionStatusEnum.choices())
    transaction = models.ForeignKey(
        related_name='statuses',
        to='marketplace.MarketplaceTransaction',
        on_delete=models.PROTECT,
    )

    external_id = models.CharField(max_length=32, null=True)
    charge_date = models.DateTimeField(null=True, blank=True)
    errors = JSONField(null=True)
    type = models.CharField(
        max_length=1,
        choices=MarketplaceTransactionType.choices(),
        default=MarketplaceTransactionType.CHARGE,
    )
    rows = models.ManyToManyField(  # will be removed in BOOS-465
        'marketplace.MarketplaceTransactionRow',
        related_name='statuses',
        through=MarketplaceTransactionRowToStatus,
    )
    boost_appointments = models.ManyToManyField(
        'boost.BoostAppointment',
        related_name='statuses',
        through='boost.BoostAppointmentToStatus',
    )
    response_code = models.CharField(max_length=32, null=True)
    response_text = models.CharField(max_length=512, null=True)
    response_type = models.CharField(max_length=256, null=True)
    gateway_rejection_reason = models.CharField(max_length=256, null=True)
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
    )

    @property
    def refundable(self):
        return self.status in MarketplaceTransactionStatusEnum.refundable_statuses()

    @property
    def is_failed(self):
        return self.status in MarketplaceTransactionStatusEnum.failed_statuses()

    objects = ArchiveManager()
    all_objects = models.Manager()


class MarketplaceBusiness(ArchiveModel, OneToOneExtendModel, AutoAddHistoryModel):
    business = models.OneToOneField(
        'business.Business',
        on_delete=models.CASCADE,
    )
    boost_completed_sended = models.BooleanField(default=False)
    manually_verified = models.BooleanField(default=False)
    planned_boost_activate_date = models.DateField(null=True, blank=True)
    planned_boost_deactivate_date = models.DateField(null=True, blank=True)
    activate_planned_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="planned_boost_activations",
    )
    deactivate_planned_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="planned_boost_deactivations",
    )
    waiver_of_withdrawal_datetime = models.DateTimeField(
        null=True, blank=True, default=None, verbose_name='Consent for immediate service (UTC)'
    )

    @property
    def is_verified(self):
        registration_source = self.business.registration_source
        if registration_source:
            registered_via_web = registration_source.name in (
                consts.FRONTDESK,
                consts.WEB,
            )
        else:
            registered_via_web = False
        return self.manually_verified or registered_via_web or self.business.has_customer_booking()

    def clear_dates(self, activation=False, deactivation=False, history_params=None):
        update_fields = []
        if activation:
            update_fields.extend(['planned_boost_activate_date', 'activate_planned_by'])
        if deactivation:
            update_fields.extend(['planned_boost_deactivate_date', 'deactivate_planned_by'])
        if update_fields:
            for field_name in update_fields:
                setattr(self, field_name, None)
            self.save(**({'_history': history_params} if history_params else {}))


class MarketplaceBusinessHistory(HistoryModel):
    model = models.ForeignKey(
        MarketplaceBusiness,
        on_delete=models.CASCADE,
        related_name='history',
    )


class MarketplaceStageStatus(ArchiveModel):
    business = models.ForeignKey(
        'business.Business',
        on_delete=models.CASCADE,
    )
    date = models.DateTimeField(default=tznow)
    stage = models.ForeignKey(
        'marketplace.MarketplaceStage',
        on_delete=models.CASCADE,
    )

    @classmethod
    def add(cls, stage_name, business):
        trial = stage_name == MarketplaceStage.WAITING_LIST

        stage = MarketplaceStage.objects.filter(
            stage_name=stage_name, marketplace_stage=trial
        ).first()
        if stage:
            cls(business=business, stage=stage).save()


class MarketplaceStage(ArchiveModel):
    WAITING_LIST = 'waitinglist'

    id = models.AutoField(primary_key=True, db_column='stage_id')
    stage_name = models.CharField(max_length=64)
    marketplace_stage = models.BooleanField()
    stage_cardinality = models.IntegerField()
    view_name = models.CharField(max_length=64)
    available_for_sync = models.BooleanField(default=True)


class MarketplacePromotionStatus(models.Model):
    PROMOTION_STATUS__AWAITING = 'A'
    PROMOTION_STATUS__REJECTED = 'R'
    PROMOTION_STATUS__DELAYED = 'D'
    PROMOTION_STATUS__AFTER_SAAS_TRIAL = 'F'
    PROMOTION_STATUS__CONDITION_NOT_MET = 'C'
    PROMOTION_STATUS__NEW_MARKETPLACE = 'N'
    PROMOTION_STATUSES = (
        (PROMOTION_STATUS__AWAITING, 'Awaiting'),
        (PROMOTION_STATUS__REJECTED, 'Rejected'),
        (PROMOTION_STATUS__DELAYED, 'Delayed'),
        (PROMOTION_STATUS__AFTER_SAAS_TRIAL, 'First login after saas trial'),
        (PROMOTION_STATUS__CONDITION_NOT_MET, 'Condition not met'),
        (PROMOTION_STATUS__NEW_MARKETPLACE, 'Marketplace just enabled'),
    )

    STATUSES_REQUIRED_ACTIONS = [PROMOTION_STATUS__NEW_MARKETPLACE]

    class Meta:
        ordering = ('created',)
        verbose_name = 'Promotion status'
        verbose_name_plural = 'Promotion statuses'

    status = models.CharField(
        max_length=1,
        choices=PROMOTION_STATUSES,
        null=True,
        blank=True,
    )
    created = models.DateTimeField(auto_now=True)
    business = models.ForeignKey(
        'business.Business',
        related_name='promotion_statuses',
        on_delete=models.CASCADE,
    )
    action_taken = models.BooleanField(default=True)

    @classmethod
    def get_status_for_business(cls, business):
        obj = (
            cls.objects.filter(
                business=business,
            )
            .order_by('created')
            .last()
        )
        if obj:
            return obj.status
        return None

    @staticmethod
    def pre_save_handler(
        sender, instance, update_fields, **kwargs
    ):  # pylint: disable=unused-argument
        """Regenerate deeplinks if data has been modified."""
        if instance.id or instance.status not in sender.STATUSES_REQUIRED_ACTIONS:
            return
        instance.action_taken = True


pre_save.connect(MarketplacePromotionStatus.pre_save_handler, MarketplacePromotionStatus)


class BusinessMarketPlaceSlot(ImageThumbnailsMixin, models.Model):
    THUMBNAILS_CATEGORY = ImageTypeEnum.MARKET_PLACE_SLOT

    class Meta:
        verbose_name = _('Business Marketplace Slot')
        verbose_name_plural = _('Business Marketplace Slots')

        unique_together = (
            'region',
            'slot_number',
        )

        ordering = ['region_id', 'slot_number']

    business = models.ForeignKey(
        'business.Business',
        related_name='marketplace_slot',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    region = models.ForeignKey(
        'structure.Region',
        related_name='marketplace_region_slot',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )

    image = models.ImageField(
        blank=True,
        null=True,
        storage=MarketPlaceSlotStorage(),
        upload_to=get_mp_slots_path,
    )

    title = models.CharField(max_length=MAX_NUMBER_CH_TITLE_MP)

    description = models.CharField(max_length=MAX_NUMBER_CH_DESCRIPTION_MP)

    slot_number = models.PositiveSmallIntegerField(
        validators=[validators.MaxValueValidator(MAX_NUMBER_SLOTS)]
    )

    redirect_url = models.URLField(blank=True, null=True)

    button_text = models.CharField(
        max_length=MAX_NUMBER_CH_BUTTON_MP,
        default='Book appointment',
    )


# TODO: Remove when UTT2 will be fully operational
# region treatment matching
class MatchTreatmentLog(ArchiveModel):
    service = models.ForeignKey(
        'business.Service',
        on_delete=models.CASCADE,
    )
    treatment = models.ForeignKey(
        'business.BusinessCategory',
        null=True,
        blank=True,
        default=None,
        on_delete=models.SET_NULL,
    )
    user = models.ForeignKey(
        'user.User',
        on_delete=models.CASCADE,
    )
    status = models.CharField(
        choices=MATCH_TREATMENT_CHOICES,
        default=MATCH_TRATMENT_ASSIGNED,
        max_length=1,
    )
    batch = models.ForeignKey(
        'marketplace.MatchTreatmentBatch',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    no_match = models.BooleanField(blank=True, default=False)

    @classmethod
    def assign_new(cls, user):
        assigned = list(
            cls.objects.filter(user_id=user, status=MATCH_TRATMENT_ASSIGNED, batch__isnull=True)
        )

        services = Service.get_unassigned_to_treatment()[
            : MATCH_TREATMENT_BATCH_SIZE - len(assigned)
        ]

        match_list = []
        for service in services:
            match_list.append(cls(service=service, user_id=user))

        cls.objects.bulk_create(match_list)
        return match_list + assigned

    @classmethod
    def done(cls, user):
        matches = MatchTreatmentLog.objects.filter(
            user_id=user,
            status=MATCH_TRATMENT_ASSIGNED,
        ).filter(Q(treatment__isnull=False) | Q(no_match=True))
        batch = MatchTreatmentBatch.objects.filter(
            user_id=user, status=MATCH_TRATMENT_ASSIGNED
        ).last()
        if not batch:
            batch = MatchTreatmentBatch(user_id=user)
            batch.save()
        batch_count = batch.matchtreatmentlog_set.count()
        if batch_count + len(matches) <= MATCH_TREATMENT_BATCH_SIZE:
            matches.update(status=MATCH_TRATMENT_MATCHED, batch=batch)
            if batch.matchtreatmentlog_set.count() >= MATCH_TREATMENT_BATCH_SIZE:
                batch.status = MATCH_TRATMENT_MATCHED
                batch.save()
        else:
            new_batch = MatchTreatmentBatch(user_id=user)
            new_batch.save()
            MatchTreatmentLog.objects.filter(
                id__in=[match.id for match in matches[: MATCH_TREATMENT_BATCH_SIZE - batch_count]]
            ).update(batch=batch, status=MATCH_TRATMENT_MATCHED)
            MatchTreatmentLog.objects.filter(
                id__in=[match.id for match in matches[MATCH_TREATMENT_BATCH_SIZE - batch_count :]]
            ).update(batch=new_batch, status=MATCH_TRATMENT_MATCHED)
            batch.status = MATCH_TRATMENT_MATCHED
            batch.save()


class MatchTreatmentBatch(ArchiveModel):
    user = models.ForeignKey(
        'user.User',
        related_name='created_batches',
        on_delete=models.CASCADE,
    )
    status = models.CharField(
        choices=MATCH_TREATMENT_CHOICES,
        default=MATCH_TRATMENT_ASSIGNED,
        max_length=1,
    )
    moderator = models.ForeignKey(
        'user.User',
        null=True,
        blank=True,
        related_name='moderated_batches',
        on_delete=models.SET_NULL,
    )

    objects = ArchiveManager()
    all_objects = models.Manager()

    def accept(self, user_id):
        matches = MatchTreatmentLog.objects.filter(batch=self)
        with transaction.atomic():
            for match in matches:
                match.service.assign_treatment(match.treatment_id, match.no_match)
        matches.update(status=MATCH_TRATMENT_CORRECT)
        self.status = MATCH_TRATMENT_CORRECT
        self.moderator_id = user_id
        self.save()

    def reject(self, user_id):
        MatchTreatmentLog.objects.filter(batch=self).update(status=MATCH_TRATMENT_REJECTED)
        self.status = MATCH_TRATMENT_REJECTED
        self.moderator_id = user_id
        self.save()


# endregion treatment matching


class MarketplaceBListingAcquisition(ArchiveModel, models.Model):
    class Meta:
        ordering = ['created']

    user = models.ForeignKey(
        'user.User',
        related_name='b_listing_acquisitions',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    b_listing = models.ForeignKey(
        'business.BListing',
        related_name='b_listing_acquisitions',
        on_delete=models.CASCADE,
    )
    acquisition_type = models.CharField(
        choices=BUSINESS_ACQUISITION_TYPES,
        max_length=1,
        blank=False,
        null=False,
    )
    created = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Created (UTC)',
    )
    email = models.EmailField(max_length=75, blank=True)
    name = models.CharField(max_length=250)
    phone = BooksyPhoneNumberField()


class MarketplaceClaimDenyReason(ArchiveModel):
    short_name = models.CharField(max_length=32, unique=True)
    description = models.CharField(max_length=256)
    visible = models.BooleanField(default=True)

    objects = ArchiveManager()
    all_objects = models.Manager()


class BoostClientCard(ArchiveModel, OneToOneExtendModel):
    client_card = models.OneToOneField(
        'business.BusinessCustomerInfo',
        null=True,
        blank=True,
        unique=True,
        on_delete=models.SET_NULL,
        related_name='boost_client_card',
    )
    status = models.CharField(
        max_length=1, choices=BoostClientCardStatus.choices(), null=True, blank=True
    )
    status_description = models.TextField(null=True, blank=True)
    from_promo = models.BooleanField(
        _('from boost'),
        default=False,
        help_text=_('Is a boost customer'),
    )
    originally_came_from_boost = models.BooleanField(null=True)

    def update_by_first_appointment(self, first_appointment):
        update_fields = []
        if not first_appointment or (
            first_appointment.payable is False and self.from_promo is True
        ):
            self.from_promo = False
            update_fields.append('from_promo')
        elif first_appointment.payable and first_appointment.status == AppointmentStatus.FINISHED:
            self.from_promo = True
            update_fields.append('from_promo')

        if (
            first_appointment
            and first_appointment.payable
            and not self.originally_came_from_boost
            and BoostOriginallyCameFromBoostFlag()
        ):
            self.originally_came_from_boost = True
            update_fields.append('originally_came_from_boost')

        if update_fields:
            self.save(update_fields=update_fields)
            bump_document(River.BUSINESS_CUSTOMER, [self.client_card_id])


class SeoRegionHomepageCategoryTemplate(ArchiveModel):
    class Meta:
        verbose_name = 'SEO Regions Homepage Template'
        verbose_name_plural = verbose_name

    name = models.CharField(max_length=32)
    business_category = models.ManyToManyField(
        'business.BusinessCategory',
        related_name='seo_regions_homepage_categories',
    )

    def __str__(self):
        return f'{self.name}'


class SeoRegionHomepage(ArchiveModel, ESDocMixin):
    es_doc_type = ESDocType.SEO_REGION_HOMEPAGE
    river = River.SEO_REGION_HOMEPAGE

    class Meta:
        verbose_name = 'SEO Regions Homepage'
        verbose_name_plural = verbose_name
        constraints = [
            models.UniqueConstraint(
                condition=Q(deleted__isnull=True, active=True),
                fields=('region', 'active'),
                name='only_one_active_not_deleted_region',
            )
        ]

    template = models.ForeignKey(
        SeoRegionHomepageCategoryTemplate,
        on_delete=models.CASCADE,
        related_name='seo_regions_homepage_categories',
    )
    region = models.ForeignKey(
        'structure.Region',
        on_delete=models.CASCADE,
        related_name='seo_regions_homepage',
        db_constraint=False,
    )
    active = models.BooleanField(default=False)

    objects = BaseArchiveManager.from_queryset(ArchiveQuerySet)()
    all_objects = models.Manager()

    @cached_property
    def active_categories(self) -> list['BusinessCategory']:
        region_ids = [r.id for r in self.region.get_children_with_me()]
        businesses_qs = Business.objects.filter(
            active=True,
            visible=True,
            region_id__in=region_ids,
        )

        active_categories_ = []
        for category in self.template.business_category.all():
            if businesses_qs.filter(categories__in=[category]).exists():
                active_categories_.append(category)

        return active_categories_

    def __str__(self):
        return f'{self.template_id}, {self.region_id}'


class SeoRegionCategoryListingTemplate(ArchiveModel):
    class Meta:
        verbose_name = 'SEO Regions cat&treat Template'
        verbose_name_plural = verbose_name

    name = models.CharField(max_length=32)
    regions = models.ManyToManyField('structure.Region')

    objects = BaseArchiveManager.from_queryset(SoftDeleteQuerySet)()

    def __str__(self):
        return f'{self.name}'


class SeoRegionCategoryListing(ArchiveModel, ESDocMixin):
    es_doc_type = ESDocType.SEO_REGION_CATEGORY
    river = River.SEO_REGION_CATEGORY

    class Meta:
        verbose_name = 'SEO Regions cat&treat'
        verbose_name_plural = verbose_name

        constraints = [
            models.UniqueConstraint(
                condition=Q(
                    category__isnull=True,
                    active=True,
                    deleted__isnull=True,
                ),
                fields=('active',),
                name='only_one_active_in_default_category',
            ),
            models.UniqueConstraint(
                condition=Q(
                    active=True,
                    deleted__isnull=True,
                ),
                fields=('active', 'category'),
                name='only_one_active_in_category',
            ),
        ]

    category = models.ForeignKey(
        'business.BusinessCategory',
        db_constraint=False,
        null=True,
        default=None,
        on_delete=models.CASCADE,
        related_name='seo_regions_category',
    )
    template = models.ForeignKey(
        SeoRegionCategoryListingTemplate,
        on_delete=models.CASCADE,
        related_name='seo_regions_category',
    )
    active = models.BooleanField(default=False)
    max_regions = models.PositiveSmallIntegerField(default=SEO_CMS_MAX_CATEGORY_PROMOTED_REGIONS)

    objects = BaseArchiveManager.from_queryset(SoftDeleteQuerySet)()
    all_objects = models.Manager()

    @cached_property
    def active_regions(self) -> list[Region]:
        """Regions with active businesses in category up to max_regions."""
        business_qs = Business.objects.filter(active=True, visible=True)
        if self.category:
            business_qs = business_qs.filter(categories=self.category)

        active_regions_ = []
        for region in self.template.regions.all():
            region_ids = [r.id for r in region.get_children_with_me()]
            if business_qs.filter(region_id__in=region_ids).exists():
                active_regions_.append(region)

            if len(active_regions_) >= self.max_regions:
                break

        return active_regions_

    def __str__(self):
        return f'{self.category_id}, {self.template_id}, {self.active}, {self.max_regions}'

    @classmethod
    def initialize_listings(cls):
        template = SeoRegionCategoryListingTemplate.objects.earliest('id')

        category_without_template_ids = BusinessCategory.objects.filter(
            ~Q(
                id__in=Subquery(
                    cls.objects.exclude(category__isnull=True).values_list('category__id')
                )
            ),
            type=BusinessCategory.CATEGORY,
        ).values_list('pk', flat=True)

        # create missing SEO pages
        cls.objects.bulk_create(
            [
                cls(
                    category_id=category_id,
                    template=template,
                    active=True,
                )
                for category_id in category_without_template_ids
            ],
            ignore_conflicts=True,
        )

        # refresh all active pages - there are pages, which have active status,
        # but are still not visible in web-customer
        listing_ids = list(cls.objects.filter(active=True).values_list('pk', flat=True))
        bump_document(River.SEO_REGION_CATEGORY, listing_ids)


class SeoFeatureFlag(ArchiveModel, ESDocMixin):
    es_doc_type = ESDocType.SEO_FEATURE_FLAG
    river = River.SEO_FEATURE_FLAG

    class Meta:
        verbose_name = 'SEO Feature Flags'
        verbose_name_plural = verbose_name

    key = models.CharField(max_length=32, unique=True)
    value = models.CharField(max_length=16)
    value_type = models.CharField(
        choices=SeoFeatureFlagValueType.choices(),
        max_length=8,
    )
    active = models.BooleanField(default=False)

    def __str__(self):
        return f'{self.key}, {self.value!r}, {self.active!r}, {self.value_type!r}'


class SeoContentQuerySet(SoftDeleteQuerySet):
    def soft_delete(self):
        return self.update(deleted=tznow(), active=False)


class SeoContentManager(BaseArchiveManager.from_queryset(SeoContentQuerySet)):
    def get_queryset(self):
        return super().get_queryset().filter(content_type=SeoCmsContentType.GENERIC)


class SeoContentAllObjectsManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(content_type=SeoCmsContentType.GENERIC)


class SeoContent(ArchiveModel):
    class Meta:
        constraints = [
            models.UniqueConstraint(
                condition=Q(
                    active=True,
                    deleted__isnull=True,
                )
                & ~Q(
                    content_type=SeoCmsContentType.RECOMMENDED4U,
                ),
                fields=('category', 'position', 'active'),
                name='one_active_position_per_type_in_category',
            ),
            models.UniqueConstraint(
                condition=Q(
                    active=True,
                    category__isnull=True,
                    content_type=SeoCmsContentType.RECOMMENDED4U,
                    deleted__isnull=True,
                ),
                fields=('position', 'active'),
                name='one_active_position_for_recommended4u',
            ),
        ]
        verbose_name = 'SEO Content cats&treat'
        verbose_name_plural = verbose_name

    name = models.CharField(max_length=32)
    active = models.BooleanField(default=False)
    category = models.ForeignKey(
        'business.BusinessCategory',
        on_delete=models.CASCADE,
        related_name='seo_content',
        null=True,
        db_constraint=False,
        blank=True,
    )
    content_type = models.CharField(
        max_length=1,
        choices=SeoCmsContentType.choices(),
        default=SeoCmsContentType.GENERIC,
    )
    position = models.PositiveSmallIntegerField(default=1)

    objects = SeoContentManager()
    all_objects = SeoContentAllObjectsManager()

    def delete(self, using=None, keep_parents=False):
        return self.soft_delete()

    @property
    def content_data_ids(self):
        return list(self.content_data.values_list('id', flat=True))

    def __str__(self):
        return f'{self.name}-{self.category_id} ({self.active}, {self.position})'


class SeoContentDataManager(BaseArchiveManager.from_queryset(SeoContentQuerySet)):
    def get_queryset(self):
        return super().get_queryset().order_by('seo_content__position')


class SeoContentData(ArchiveModel, ESDocMixin):
    es_doc_type = ESDocType.SEO_CMS_CONTENT_DATA
    river = River.SEO_CMS_CONTENT_DATA

    class Meta:
        constraints = [
            models.UniqueConstraint(
                condition=Q(active=True),
                fields=('seo_content', 'language', 'active'),
                name='one_active_entry_per_language',
            ),
        ]

    objects = SeoContentDataManager()
    all_objects = models.Manager()

    seo_content = models.ForeignKey(
        SeoContent,
        on_delete=models.CASCADE,
        related_name='content_data',
    )
    language = models.CharField(max_length=5, choices=settings.LANGUAGES)
    data = JSONField(default=dict)
    image = models.ImageField(
        blank=True,
        null=True,
        storage=CMSContentStorage(),
        upload_to=get_seo_content_path,
    )
    active = models.BooleanField(default=False)

    def __str__(self):
        return f'{self.seo_content_id}, {self.language}, {self.active}'


class SeoRecommended4UManager(BaseArchiveManager.from_queryset(SeoContentQuerySet)):
    def get_queryset(self):
        return super().get_queryset().filter(content_type=SeoCmsContentType.RECOMMENDED4U)


class SeoRecommended4UAllObjectsManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(content_type=SeoCmsContentType.RECOMMENDED4U)


class SeoRecommended4U(SeoContent):
    es_doc_type = ESDocType.SEO_CMS_CONTENT_DATA

    class Meta:
        proxy = True
        verbose_name = 'SEO Recommended for You'
        verbose_name_plural = 'SEO Recommended for You'

    objects = SeoRecommended4UManager()
    all_objects = SeoRecommended4UAllObjectsManager()


class CategorySitemap(ArchiveModel):
    category = models.OneToOneField('business.BusinessCategory', on_delete=models.CASCADE)
    last_modification = models.DateField(auto_now_add=True)


class BusinessCategorySitemap(ArchiveModel):
    class Meta:
        unique_together = ('business', 'category')

    business = models.ForeignKey('business.Business', on_delete=models.DO_NOTHING)
    category = models.ForeignKey('business.BusinessCategory', on_delete=models.CASCADE)
