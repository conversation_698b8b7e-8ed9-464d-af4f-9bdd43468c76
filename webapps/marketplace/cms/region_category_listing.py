from __future__ import annotations

from lib.elasticsearch.consts import ESDocType

from webapps.marketplace.cms.searchables.seo_homepage import SeoRegionCategorySearchable
from webapps.marketplace.cms.searchables.serializers import SeoRegionCategoryHitSerializer


def get_regions_category_for_listing(category_id: int = None) -> list:
    searchable = SeoRegionCategorySearchable(
        ESDocType.SEO_REGION_CATEGORY,
        serializer=SeoRegionCategoryHitSerializer,
    )
    category_id = category_id or 0
    resp = searchable.params(size=1).search(dict(category=[category_id])).execute()
    return resp.hits[0].regions if resp.hits else []
