# Generated by Django 1.11.11 on 2018-08-23 09:35
from django.db import migrations, models
import django.db.models.deletion


def assign_business_promotions(apps, schema_editor):
    db_name = schema_editor.connection.alias
    marketplate_transaction_model = apps.get_model("marketplace", "MarketplaceTransaction")
    for marketplace_trans in marketplate_transaction_model.objects.using(db_name):
        marketplace_trans.business_promotion = (
            marketplace_trans.booking.business.businesspromotion_set.last()
        )
        marketplace_trans.save()


class Migration(migrations.Migration):

    dependencies = [
        ('business', '0158_auto_20180823_0934'),
        ('marketplace', '0027_auto_20180822_1330'),
    ]

    operations = [
        migrations.AddField(
            model_name='marketplacetransaction',
            name='business_promotion',
            field=models.ForeignKey(
                null=True,
                blank=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name='marketplace_transactions',
                to='business.BusinessPromotion',
            ),
        ),
        migrations.RunPython(
            code=assign_business_promotions,
            reverse_code=migrations.RunPython.noop,
        ),
        migrations.AlterField(
            model_name='marketplacetransaction',
            name='business_promotion',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='marketplace_transactions',
                to='business.BusinessPromotion',
            ),
        ),
    ]
