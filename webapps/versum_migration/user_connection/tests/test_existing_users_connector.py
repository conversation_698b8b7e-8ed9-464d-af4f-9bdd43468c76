from unittest.mock import patch, call

from django.test import TestCase
from model_bakery import baker

from webapps.user.models import User
from webapps.versum_migration.user_connection.existing_users_connector import ExistingUsersConnector
from webapps.versum_migration.user_connection.user_connector import UserConnector
from webapps.versum_migration.models import VersumUser


class ExistingUsersConnectorTest(TestCase):
    @patch.object(UserConnector, 'call')
    def test_successful_connection(self, user_connector_call_mock):
        versum_user_1 = baker.make(
            VersumUser,
            versum_user_id=101,
            versum_phone='+48500500501',
            versum_email='<EMAIL>',
            versum_first_name='SomeFirstName',
            versum_last_name='SomeLastName',
        )

        _user_1_1 = baker.make(
            User,
            cell_phone='+48500500501',
            email='<EMAIL>',
            first_name='SomeOtherFirstName',
            last_name='SomeOtherLastName',
        )

        _user_1_2 = baker.make(
            User,
            cell_phone='+48500500501',
            email='<EMAIL>',
            first_name='SomeOtherFirstName',
            last_name='SomeOtherLastName',
        )

        user_1_3 = baker.make(
            User,
            cell_phone='+48500500501',
            email='<EMAIL>',
            first_name='SomeFirstName',
            last_name='SomeLastName',
        )

        _user_1_4 = baker.make(
            User,
            cell_phone='+48500500501',
            email='<EMAIL>',
            first_name='SomeOtherFirstName',
            last_name='SomeOtherLastName',
        )

        _versum_user_2 = baker.make(
            VersumUser,
            versum_user_id=102,
            versum_phone='+48500500502',
            versum_email='<EMAIL>',
            versum_first_name='SomeFirstName',
            versum_last_name='SomeLastName',
        )

        _user_2_1 = baker.make(
            User,
            cell_phone='+48500500502',
            email='<EMAIL>',
            first_name='SomeOtherFirstName',
            last_name='SomeOtherLastName',
        )

        _user_2_2 = baker.make(
            User,
            cell_phone='+48500500502',
            email='<EMAIL>',
            first_name='SomeOtherFirstName',
            last_name='SomeOtherLastName',
        )

        versum_user_3 = baker.make(
            VersumUser,
            versum_user_id=103,
            versum_phone='+48500500503',
            versum_email='<EMAIL>',
            versum_first_name='SomeFirstName',
            versum_last_name='SomeLastName',
        )

        user_3_1 = baker.make(
            User,
            cell_phone='+48500500503',
            email='<EMAIL>',
            first_name='SomeOtherFirstName',
            last_name='SomeOtherLastName',
        )

        _versum_user_4 = baker.make(
            VersumUser,
            versum_user_id=104,
            versum_phone='+48500500504',
            versum_email='<EMAIL>',
            versum_first_name='SomeFirstName',
            versum_last_name='SomeLastName',
        )

        ExistingUsersConnector.call(with_progress_bar=False)

        self.assertEqual(
            user_connector_call_mock.mock_calls,
            [
                call(user=user_1_3, versum_user=versum_user_1),
                call(user=user_3_1, versum_user=versum_user_3),
            ],
        )
