from django.core.management.base import BaseCommand

from webapps.versum_migration.user_connection.missing_versum_branches_connector import (
    MissingVersumBranchesConnector,
)


class Command(BaseCommand):
    help = 'Connects missing versum branches to existing booksy users'

    def add_arguments(self, parser):
        parser.add_argument('versum_branch_ids', type=str)

    def handle(self, *args, **options):
        versum_branch_ids_str = options['versum_branch_ids']
        versum_branch_ids = [int(vb_id) for vb_id in versum_branch_ids_str.split(',')]

        MissingVersumBranchesConnector.call(
            with_progress_bar=True,
            versum_branch_ids=versum_branch_ids,
        )
