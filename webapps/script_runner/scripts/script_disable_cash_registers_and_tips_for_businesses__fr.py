import logging

from django.db.models import Q
from tqdm import tqdm

from lib.tools import chunker
from webapps.pos.enums import PaymentTypeEnum
from webapps.pos.models import (
    POS,
    PaymentType,
)
from webapps.pos.tasks import CloseAllCashRegisters
from webapps.script_runner.mixins import ReusableScript
from webapps.script_runner.runners import DBScriptRunner

logger = logging.getLogger('booksy.script_runner')


BUSINESS_IDS = [
    6089,
    2094,
    31915,
    14944,
    34679,
    26559,
    29425,
    26560,
    25474,
    8593,
    27264,
    26202,
    32899,
    25370,
    34132,
    15375,
    33978,
    22069,
    25897,
    17911,
    35003,
    21070,
    32960,
    32961,
    3538,
    23369,
    17182,
    10898,
    34412,
    29988,
    28486,
    30369,
    12048,
    25920,
    27021,
    26558,
    23477,
    29623,
    28906,
    33584,
    18903,
    26201,
    34475,
    23359,
    15402,
    33834,
    3254,
    25265,
    33620,
    22615,
    24164,
    27647,
    12340,
    14681,
    23357,
    33707,
    25264,
    29849,
    34085,
    24538,
    25651,
]


class Script(DBScriptRunner, ReusableScript):
    version = 2
    chunk_size = 100

    def run(self):
        logger.warning('START script_disable_cash_registers_and_tips_for_businesses__fr')
        qs = (
            POS.objects.filter(business_id__in=BUSINESS_IDS).values_list('id', flat=True).distinct()
        )

        for ids in tqdm(
            chunker(qs.iterator(), self.chunk_size),
            total=qs.count() // self.chunk_size,
        ):
            logger.warning('Updating POSes ids %s:', ids)
            POS.objects.filter(
                Q(id__in=ids) & (Q(registers_enabled=True) | Q(tips_enabled=True))
            ).update(
                registers_enabled=False,
                tips_enabled=False,
            )
            for pos_id in ids:
                CloseAllCashRegisters.delay(pos_id, None)
            PaymentType.objects.filter(
                Q(pos_id__in=ids)
                & Q(code__in=[PaymentTypeEnum.MEMBERSHIP.value, PaymentTypeEnum.PACKAGE.value])
                & (Q(enabled=True) | Q(available=True))
            ).update(
                enabled=False,
                available=False,
            )
        logger.warning('END script_disable_cash_registers_and_tips_for_businesses__fr')
