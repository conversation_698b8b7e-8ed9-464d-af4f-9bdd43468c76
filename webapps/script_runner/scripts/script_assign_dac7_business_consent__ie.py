import logging
from itertools import islice
from typing import Iterable, Protocol

from webapps.business_consents.enums import ConsentCode
from webapps.business_consents.ports import BusinessConsentsPort
from webapps.script_runner.runners import DBScriptRunner

logger = logging.getLogger('booksy.script_runner')

CONSENT_CODE = ConsentCode.DAC7_IE
BUSINESS_IDS = {
    8194,
    7171,
    5126,
    13329,
    2578,
    7700,
    4632,
    2585,
    7194,
    7198,
    5150,
    4128,
    10289,
    13361,
    7217,
    2101,
    4151,
    3641,
    3651,
    9284,
    7238,
    5192,
    8266,
    3662,
    7247,
    12368,
    13393,
    12882,
    2132,
    5718,
    12888,
    7257,
    8792,
    3675,
    4188,
    8290,
    2147,
    7780,
    6246,
    4200,
    6760,
    3693,
    2674,
    8310,
    6776,
    4218,
    4219,
    3708,
    11388,
    8830,
    6270,
    7805,
    5758,
    7300,
    7301,
    9350,
    6279,
    10889,
    10385,
    3730,
    2205,
    3742,
    4767,
    7326,
    2212,
    10920,
    8872,
    8368,
    7346,
    7348,
    8885,
    4791,
    12472,
    5816,
    4282,
    1211,
    1212,
    12475,
    12989,
    4283,
    13504,
    4797,
    5817,
    699,
    8379,
    11973,
    8385,
    8901,
    8903,
    4811,
    8396,
    1743,
    5841,
    10450,
    8402,
    1237,
    14037,
    5334,
    5846,
    725,
    7894,
    10459,
    4828,
    4318,
    8927,
    8930,
    9955,
    5864,
    5866,
    14060,
    3824,
    13554,
    5878,
    13053,
    10499,
    4870,
    5382,
    3848,
    1807,
    6421,
    2840,
    12569,
    5402,
    13083,
    14620,
    13085,
    4891,
    13087,
    8484,
    13095,
    3881,
    5419,
    14124,
    12589,
    11055,
    13105,
    6457,
    10555,
    13117,
    12606,
    3903,
    6974,
    831,
    13634,
    13126,
    1867,
    5455,
    8528,
    6994,
    6489,
    11102,
    12126,
    8033,
    11621,
    9579,
    11628,
    12655,
    12152,
    1402,
    11131,
    5498,
    8574,
    4993,
    11654,
    2951,
    7049,
    12682,
    8079,
    9616,
    3985,
    5009,
    8083,
    12698,
    13212,
    11165,
    1439,
    10150,
    13735,
    5543,
    4521,
    10666,
    4524,
    8110,
    4015,
    7600,
    8115,
    7604,
    8120,
    1982,
    10178,
    12230,
    11207,
    7112,
    2506,
    8138,
    12748,
    11725,
    2510,
    7120,
    14293,
    5592,
    8665,
    984,
    13276,
    13279,
    4576,
    5088,
    12770,
    12259,
    13796,
    3556,
    7654,
    7143,
    12264,
    12265,
    7144,
    7146,
    4588,
    10221,
    7659,
    9708,
    3568,
    4080,
    5620,
    8191,
}


class BusinessConsentCreator(Protocol):
    def create_consents(self, business_ids: Iterable[int], consent_code: ConsentCode) -> None: ...


class DatabaseBusinessConsentCreator:
    def create_consents(self, business_ids: Iterable[int], consent_code: ConsentCode) -> None:
        BusinessConsentsPort.bulk_create_consents(
            business_ids=business_ids,
            consent_code=consent_code,
            visible=True,
            ignore_conflicts=True,
        )


class Script(DBScriptRunner):
    def __init__(
        self, consent_creator: BusinessConsentCreator | None = None, batch_size: int = 100
    ):
        self.consent_creator = consent_creator or DatabaseBusinessConsentCreator()
        self.batch_size = batch_size

    def run(self):
        created_count = 0
        logger.info('Generating business consents')
        business_ids_iter = iter(BUSINESS_IDS)

        while True:
            batch = list(islice(business_ids_iter, self.batch_size))

            if not batch:
                break

            batch_size = len(batch)
            logger.info('Inserting %s business consents', batch_size)
            self.consent_creator.create_consents(batch, CONSENT_CODE)
            created_count += batch_size
            logger.info('Inserted %s business consents', batch_size)

        logger.info('Done. Created %s %s business consents.', created_count, CONSENT_CODE)
