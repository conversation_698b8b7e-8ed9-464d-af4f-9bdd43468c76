import re
from datetime import (
    datetime,
    time,
    timedelta,
)
from decimal import Decimal
from time import time as timestamp
from typing import Any

from django.conf import settings
from django.db.models import DateField, Subquery
from django.db.models.functions import Cast

from country_config import Country
from lib.cache import lru_booksy_cache
from lib.feature_flag.adapter import SubjectType, UserData
from lib.feature_flag.feature.analytics import (
    UnrestrictCustomerEventsFlag,
    UnrestrictCustomerEventsForUSFlag,
)
from lib.tools import id_to_external_api, sget, tznow
from settings import API_COUNTRY
from webapps.consts import (
    ANDROID,
    ANDROID_SOLO,
    FRONTDESK,
    FRONTDESK_ANDROID,
    FRONTDESK_IOS,
    IPHONE,
    IPHONE_SOLO,
    STRIPE_HARDWARE_ORDER,
    WEB,
)
from webapps.segment.consts import (
    CUSTOMER_EVENTS_ALLOWED_LOCATIONS,
    UserRoleEnum,
)
from webapps.segment.enums import (
    AnalyticEventEnums,
    BooksyAppVersions,
    DeviceTypeName,
)
from webapps.user.const import Gender

BOOKSY_SOURCES_NAMES: list[str] = [
    ANDROID_SOLO,
    IPHONE_SOLO,
    FRONTDESK,
    FRONTDESK_ANDROID,
    FRONTDESK_IOS,
]
BOOKSY_DEVICE_NAME_TO_SOURCES_NAMES = {
    DeviceTypeName.ANDROID: {
        ANDROID,
        ANDROID_SOLO,
    },
    DeviceTypeName.IOS: {
        IPHONE,
        IPHONE_SOLO,
    },
    DeviceTypeName.DESKTOP: {
        WEB,
        FRONTDESK,
        STRIPE_HARDWARE_ORDER,
    },
    DeviceTypeName.TABLET: {
        FRONTDESK_IOS,
        FRONTDESK_ANDROID,
    },
}


def get_days_with_cb_in_week(business, last_week=False):
    """
    Returns number of CBs finished in week for given date
    :param business: business object
    :param last_week: True if count for last week, False if for current
    """
    from webapps.booking.models import Appointment

    begin_date, end_date = get_week(last_week=last_week)

    return (
        Appointment.objects.filter(
            business=business,
            type=Appointment.TYPE.CUSTOMER,
            status=Appointment.STATUS.FINISHED,
            booked_till__gte=begin_date,
            booked_till__lte=end_date,
        )
        .annotate(date_only=Cast('booked_from', DateField()))
        .distinct('date_only')
        .count()
    )


def get_week(last_week=False):
    """
    Get current or previous week boundaries

    :param last_week: True if get dates for last week, False if for current

    :return: last week monday 00:00:00, last week sunday 23:59:59
    :rtype: tuple
    """
    now = tznow()
    mon = now - timedelta(days=now.weekday() + (7 if last_week else 0))
    sun = mon + timedelta(days=6)

    mon = datetime.combine(mon.date(), time.min).replace(tzinfo=now.tzinfo)
    sun = datetime.combine(sun.date(), time.max).replace(tzinfo=now.tzinfo)

    return mon, sun


def get_booking_score(booking):
    if not booking:
        return round(Decimal(0), 2)
    revenue_gross = get_revenue_gross([booking])
    return round(revenue_gross * Decimal(987 / 3.1415), 2)  # variable obfuscation


def get_revenue_gross(all_bookings):
    from webapps.marketplace.models import MarketplaceCommission

    revenue_gross = 0
    if not all_bookings:
        return revenue_gross

    business = all_bookings[0].appointment.business
    for booking in all_bookings:
        if not booking.service_variant:
            continue
        revenue_gross += MarketplaceCommission.get_future_commission_value(
            business=business,
            price=booking.resolved_price or booking.service_data.service_variant_price or 0,
        )
    return revenue_gross


def get_booking_score_optimized(booking, business):
    # TODO change this func into classmethod of Appointment MAR-1012
    if not booking:
        return round(Decimal(0), 2)
    revenue_gross = get_revenue_gross_optimized(booking, business)
    return round(revenue_gross * Decimal(987 / 3.1415), 2)  # variable obfuscation


def get_revenue_gross_optimized(booking, business):
    # TODO change this func into classmethod of Appointment MAR-1012
    from webapps.marketplace.models import MarketplaceCommission

    if not booking or not booking.get('service_variant__service_id'):
        return 0
    return MarketplaceCommission.get_future_commission_value(
        business=business,
        price=booking.get('booking_value'),
    )


def spawn_booking_created_analytics_events(
    appointment: 'Appointment',
) -> None:
    from lib.feature_flag.adapter import UserData
    from lib.feature_flag.feature.booking import TrackAppointmentCreatedAnalytics
    from lib.segment_analytics.api import get_default_segment_client

    if not TrackAppointmentCreatedAnalytics(UserData(subject_key=appointment.business_id)):
        return

    track_properties = {
        'appointment_id': id_to_external_api(appointment.id),
        'booksy_pay_eligible': appointment.is_booksy_pay_eligible,
        'booksy_pay_available': appointment.is_booksy_pay_available,
        'country': settings.API_COUNTRY,
    }
    client = get_default_segment_client()
    client.track(
        event=AnalyticEventEnums.APPOINTMENT_CREATED.value,
        properties=track_properties,
        user_id=id_to_external_api(appointment.updated_by_id),
    )


def spawn_booking_finished_analytics_events(
    appointment: 'Appointment', booking_source_id: int = None
) -> None:
    from webapps.booking.models import Appointment
    from webapps.segment.tasks import (
        analytics_cb_finished_for_business_task,
        analytics_cb_finished_for_customer_task,
    )
    from webapps.family_and_friends.helpers.appointment import get_user_id_creator

    if appointment.status != Appointment.STATUS.FINISHED:
        return
    if appointment.type == Appointment.TYPE.CUSTOMER:
        if user_id_family_and_friends_creator := get_user_id_creator(appointment.id):
            session_user_id = user_id_family_and_friends_creator
        else:
            session_user_id = sget(appointment, ['booked_for', 'user_id'])

        context_data = {
            'business_id': appointment.business_id,
            'session_user_id': session_user_id,
        }
        if booking_source_id:
            context_data['source_id'] = booking_source_id
        analytics_cb_finished_for_business_task.delay(
            appointment_id=appointment.id,
            is_first_finished=appointment.is_only_cb_finished_for_business(),
            context=context_data,
        )
        analytics_cb_finished_for_customer_task.delay(
            appointment_id=appointment.id,
            is_first_finished=appointment.is_only_cb_finished_for_customer(),
            context=context_data,
        )


def is_analytics_func_killed(func):
    from webapps.kill_switch.models import KillSwitch
    from webapps.segment.consts import ANALYTICS_TASK_TO_KILLSWITCH_MAP

    killswitch_name = ANALYTICS_TASK_TO_KILLSWITCH_MAP.get(func.__name__)
    return KillSwitch.killed(killswitch_name)


@lru_booksy_cache(timeout=3600, skip_in_pytest=True)
def get_device_type_name_to_api_key_mapping():
    from webapps.booking.models import BookingSources

    result = {}
    for device, source_names in BOOKSY_DEVICE_NAME_TO_SOURCES_NAMES.items():
        result[device] = set(
            BookingSources.objects.filter(name__in=source_names).values_list('api_key', flat=True)
        )
    return result


def get_device_type_name_from_api_key(api_key):
    for device_name, keys in get_device_type_name_to_api_key_mapping().items():
        if api_key in keys:
            return device_name.value
    return DeviceTypeName.UNKNOWN.value


def get_user_role_by_id(user_id: int) -> str:
    from webapps.business.models import (
        Business,
        Resource,
    )

    user_role = UserRoleEnum.CUSTOMER.value
    if not user_id:
        return user_role

    if Business.objects.filter(owner_id=user_id).exists():
        user_role = UserRoleEnum.OWNER.value
    elif Resource.objects.filter(staff_user_id=user_id, active=True).exists():
        user_role = UserRoleEnum.STAFFER.value
    return user_role


def post_first_paid_status_action(business_id, context_data=None):
    from webapps.marketing.models import DelayedGTMEventAuthData
    from webapps.segment.tasks import (
        analytics_1st_paid_status_achieved,
        analytics_1st_paid_status_achieved_gtm_task,
        analytics_1st_paid_status_achieved_branchio_task,
    )
    from lib.facebook.task import business_facebook_conversion_event_task
    from lib.facebook.enums import EventName

    analytics_1st_paid_status_achieved.delay(
        business_id=business_id,
        context=context_data,
    )
    analytics_1st_paid_status_achieved_branchio_task.delay(
        business_id=business_id, context=context_data
    )

    business_facebook_conversion_event_task.delay(
        business_id=business_id,
        event_name=EventName.FIRST_PAID_STATUS_ACHIEVED,
        event_time=int(timestamp()),
    )
    # TODO replace with _queue_task_if_auth_data_exist after is done PAYM-257.BCR_firebase_events
    if event_data := DelayedGTMEventAuthData.objects.filter(
        business=business_id,
        event_type=DelayedGTMEventAuthData.EventType.FIRST_PAID_STATUS_ACHIEVED,
        event_sent=False,
    ).first():
        analytics_1st_paid_status_achieved_gtm_task.delay(
            business_id=business_id,
            context=context_data,
            auth_data=event_data.auth_data,
        )


def should_trigger_customer_event(
    latitude: float = None, longitude: float = None, region_id: int = None, user_id: int = None
):
    trigger_event = False
    if region_id:
        trigger_event = should_trigger_customer_event_by_region_id(region_id=region_id, user_id=user_id)

    if not trigger_event and latitude and longitude:
        trigger_event = should_trigger_customer_event_by_geo(latitude=latitude, longitude=longitude, user_id=user_id)
    return trigger_event


def should_trigger_customer_event_by_geo(latitude: float, longitude: float, user_id: int = None):
    user_data = (
        UserData(subject_key=user_id, subject_type=SubjectType.USER_ID.value)
        if user_id
        else None
    )

    if settings.API_COUNTRY == Country.PL:
        return True

    if settings.API_COUNTRY == Country.US and UnrestrictCustomerEventsForUSFlag(user_data):
        return True

    if UnrestrictCustomerEventsFlag(user_data):
        return True

    for geo in CUSTOMER_EVENTS_ALLOWED_LOCATIONS.values():
        if geo.check_point(latitude, longitude):
            return True
    return False


def should_trigger_customer_event_by_region_id(region_id: int, user_id: int = None):
    from webapps.structure.models import Region

    user_data = (
        UserData(subject_key=user_id, subject_type=SubjectType.USER_ID.value)
        if user_id
        else None
    )

    if settings.API_COUNTRY == Country.PL:
        return True

    if settings.API_COUNTRY == Country.US and UnrestrictCustomerEventsForUSFlag(user_data):
        return True

    if UnrestrictCustomerEventsFlag(user_data):
        return True

    region = (
        Region.objects.filter(
            id=region_id,
        )
        .values('latitude', 'longitude')
        .first()
    )

    if region:
        latitude = region.get('latitude')
        longitude = region.get('longitude')
        if latitude and longitude:
            return should_trigger_customer_event_by_geo(latitude=latitude, longitude=longitude)
    return False


@lru_booksy_cache()
def analytics_business_any_no_show_protection_on(business_id):
    from webapps.business.models import ServiceVariantPayment

    return ServiceVariantPayment.objects.filter(
        service_variant__service__business_id=business_id,
        service_variant__active=True,
    ).exists()


#  ANALYTIC TEST UTILS


def assert_events_triggered(
    expected_events_dict: dict,
    segment_track_mock=None,
    segment_identify_mock=None,
    gtm_push_mock=None,
):
    """
    Func checks if analytic evetns have been triggered correct number of times.
    sample expected_events_dict:
     {'Some event name':{
        expected_tracks: 1,
        expected_identifies: 1,
        expected_pushes: 1,
        }
    }`
    """
    if not (segment_track_mock or segment_identify_mock or gtm_push_mock):
        assert False, 'No mock has been provided'
        return

    names = []
    expected_push_dict = {}
    expected_identify_dict = {}
    expected_track_dict = {}

    for name, expected_dict in expected_events_dict.items():
        if push := expected_dict.get('expected_pushes'):
            expected_push_dict[name] = push
        if identify := expected_dict.get('expected_identifies'):
            expected_identify_dict[name] = identify
        if track := expected_dict.get('expected_tracks'):
            expected_track_dict[name] = track

        names.append(name)

    if segment_track_mock:
        assert segment_track_mock.call_count == sum(expected_track_dict.values()), (
            f'actual tracks:{segment_track_mock.call_count} !='
            f' expected tracks = {sum(expected_track_dict.values())}'
        )

        for call in segment_track_mock.call_args_list:
            triggered_event_name = call[1]['event']
            if triggered_event_name in expected_track_dict.keys():
                expected_track_dict[triggered_event_name] -= 1
            else:
                assert False, (
                    f'track of {triggered_event_name} was triggered,'
                    f' but was not specified in expected_events_dict'
                )
        _assert_is_event_call_number_correct(expected_track_dict)

    if segment_identify_mock:
        assert segment_identify_mock.call_count == sum(expected_identify_dict.values()), (
            f'actual identifies:{segment_identify_mock.call_count} !='
            f' expected identifies = {sum(expected_identify_dict.values())}'
        )

        for call in segment_identify_mock.call_args_list:
            triggered_event_name = call[1]['traits']['event_name']
            is_expected = False
            for expected_name in expected_identify_dict.keys():
                if re.search(
                    f'analytics_{expected_name}',
                    triggered_event_name,
                    re.IGNORECASE,
                ):
                    expected_identify_dict[expected_name] -= 1
                    is_expected = True
            if not is_expected:
                assert False, (
                    f'identify of {triggered_event_name} was triggered,'
                    f' but was not specified in expected_events_dict'
                )
        _assert_is_event_call_number_correct(expected_identify_dict)

    if gtm_push_mock:
        assert gtm_push_mock.call_count == sum(expected_push_dict.values()), (
            f'actual pushes:{gtm_push_mock.call_count} !='
            f' expected identifies = {sum(expected_push_dict.values())}'
        )

        for call in gtm_push_mock.call_args_list:
            triggered_event_name = call[1]['payload']['events'][0]['name']
            if triggered_event_name in expected_push_dict.keys():
                expected_push_dict[triggered_event_name] -= 1
            else:
                assert False, (
                    f'push of {triggered_event_name} was triggered,'
                    f' but was not specified in expected_events_dict'
                )
        _assert_is_event_call_number_correct(expected_push_dict)


def _assert_is_event_call_number_correct(expected_dict):
    for key, value in expected_dict.items():
        if value != 0:
            param = 'little' if value > 0 else 'many'
            assert False, f'push of {key} has been called {value} times too {param}.'


def get_track_common_params_for_tests(business, user_role=UserRoleEnum.OWNER, user=None):
    from webapps.business.models import Business  # circular import

    return {
        'country': settings.API_COUNTRY,
        'phone': user.phone_with_prefix if user else business.owner.cell_phone_with_prefix,
        'user_role': user_role,
        'offer_type': Business.Package(business.package).label,
        'business_id': id_to_external_api(business.id),
        'user_id': id_to_external_api(user.id if user else business.owner.id),
        'email': user.email if user else business.owner.email,
    }


def build_firebase_auth_data(context: 'AnalyticsContext') -> dict:
    if not context.source_id or not context.user_pseudo_id:
        return {}

    from lib.segment_analytics.enums import PseudoIdSourceName
    from webapps.segment.tasks import get_device_type_name_from_booking_source

    device_type = get_device_type_name_from_booking_source(context.source)

    if device_type in (DeviceTypeName.IOS, DeviceTypeName.ANDROID):
        source_name = PseudoIdSourceName.APP_INSTANCE_ID.value
    elif device_type in (DeviceTypeName.DESKTOP, DeviceTypeName.TABLET):
        source_name = PseudoIdSourceName.CLIENT_ID.value
    else:
        source_name = None

    if not source_name:
        return {}

    return {
        'booking_source_id': context.source_id,
        source_name: context.user_pseudo_id,
    }


def get_user_properties(
    user_id,
    include_user_role=True,
    include_locale=True,
) -> dict[str, Any]:
    from webapps.user.models import User, UserProfile  # circular import

    fields = [
        'cell_phone',
        'email',
        'first_name',
        'gender',
        'last_name',
    ]

    user_data_qs = User.objects.filter(id=user_id)

    if include_locale:
        user_data_qs = user_data_qs.annotate(
            language=Subquery(
                UserProfile.objects.filter(
                    user__id=user_id,
                    profile_type=UserProfile.Type.CUSTOMER,
                ).values_list('language')[:1]
            )
        )
        fields.append('language')

    if common_properties := user_data_qs.values(*fields).first():
        if not common_properties['gender']:
            common_properties['gender'] = str(Gender.Both)

        if include_user_role:
            common_properties['user_role'] = get_user_role_by_id(user_id)
        return common_properties

    return {}
