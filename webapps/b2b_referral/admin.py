from django.contrib import admin
from django.db.models import Prefetch
from django.shortcuts import redirect
from django.urls import re_path as url
from django.urls import reverse
from django.utils.html import format_html

from lib.admin_helpers import (
    BaseModelAdmin,
    NoAddDelMixin,
    NoDelMixin,
    ReadOnlyFieldsMixin,
)
from lib.tools import format_currency, get_object_or_404
from webapps.b2b_referral.enums import (
    B2BReferralStatus,
    B2BRewardStatus,
    B2BRewardType,
)
from webapps.b2b_referral.forms import B2BReferralSettingForm, B2bReferralForm
from webapps.b2b_referral.models import (
    B2BReferral,
    B2BReferralCode,
    B2BReferralReward,
    B2BReferralSetting,
    Prize,
)
from webapps.b2b_referral.tasks import (
    create_invite_notification_for_setting_task,
)
from webapps.business.forms.fields import TableCreator
from webapps.market_pay.models import ManualPayout


class ArchiveModelReadOnlyMixin:
    readonly_fields = (
        'created',
        'updated',
        'deleted',
        'id',
    )


class B2BReferralRewardInline(ReadOnlyFieldsMixin, NoAddDelMixin, admin.StackedInline):
    model = B2BReferralReward
    fields = ['id', 'type', 'status', 'formatted_amount']
    readonly_fields = ('formatted_amount',)

    @staticmethod
    def formatted_amount(obj):
        return format_currency(obj.amount)


class B2BReferralRewardReferrerStatusListFilter(admin.SimpleListFilter):
    parameter_name = 'referrer_reward__status'
    title = 'referrer_reward__status'

    def lookups(self, request, model_admin):
        return B2BRewardStatus.choices()

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(
                rewards__status=self.value(),
                rewards__type=B2BRewardType.REFERRER,
            )
        return queryset


class B2BReferralRewardInvitedStatusListFilter(admin.SimpleListFilter):
    parameter_name = 'invited_reward__status'
    title = 'invited_reward__status'

    def lookups(self, request, model_admin):
        return B2BRewardStatus.choices()

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(
                rewards__status=self.value(),
                rewards__type=B2BRewardType.INVITED,
            )
        return queryset


class B2BReferralAdmin(ArchiveModelReadOnlyMixin, BaseModelAdmin):
    form = B2bReferralForm
    raw_id_fields = (
        'referrer',
        'invited',
        'base_referral_setting',
    )

    inlines = [
        B2BReferralRewardInline,
    ]

    list_display = (
        'id',
        'referrer',
        'invited',
        'base_referral_setting',
        'status',
        'referrer_reward_status',
        'invited_reward_status',
    )

    readonly_fields = (
        'created',
        'updated',
        'deleted',
        'id',
        'invited_reward_status',
        'referrer_reward_status',
    )

    search_fields = (
        '=referrer_id',
        '=invited_id',
    )
    hide_keyword_field = True

    list_filter = (
        'status',
        B2BReferralRewardReferrerStatusListFilter,
        B2BReferralRewardInvitedStatusListFilter,
    )

    def __init__(self, *args, **kwargs):
        self.mark_as_pending_reward_btn = None
        super().__init__(*args, **kwargs)

    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .prefetch_related(
                Prefetch(
                    'rewards',
                    queryset=B2BReferralReward.objects.filter(type=B2BRewardType.INVITED),
                    to_attr='_invited_reward',
                ),
                Prefetch(
                    'rewards',
                    queryset=B2BReferralReward.objects.filter(type=B2BRewardType.REFERRER),
                    to_attr='_referrer_reward',
                ),
            )
        )

    @staticmethod
    def invited_reward_status(obj):
        return obj.invited_reward and obj.invited_reward.get_status_display()

    @staticmethod
    def referrer_reward_status(obj):
        return obj.referrer_reward and obj.referrer_reward.get_status_display()

    def get_urls(self):
        urls = super().get_urls()

        additional_urls = [
            url(
                r'^(?P<referral_id>\d+)/mark_as_pending',
                self.admin_site.admin_view(self.mark_as_pending_reward_view),
                name='mark_as_pending_reward',
            ),
        ]
        return additional_urls + urls

    def get_form(self, request, obj=None, change=False, **kwargs):
        mark_as_pending_reward_btn = self.get_mark_as_pending_reward_btn(obj)
        self.mark_as_pending_reward_btn = mark_as_pending_reward_btn
        return super().get_form(request, obj, **kwargs)

    @staticmethod
    def get_mark_as_pending_reward_btn(obj):
        if obj is None:
            return ''
        if obj.status != B2BReferralStatus.CURRENTLY_ON_TRIAL:
            return ''

        return '<a href={}" class="btn">Mark as pending reward</a>'.format(
            reverse("admin:mark_as_pending_reward", args=(obj.id,))
        )

    @staticmethod
    def mark_as_pending_reward_view(request, referral_id):
        referral = get_object_or_404(B2BReferral, id=referral_id)

        referral.mark_as_pending_reward()

        return redirect(request.META.get('HTTP_REFERER', '/'))


admin.site.register(B2BReferral, B2BReferralAdmin)


class B2BReferralSettingAdmin(ArchiveModelReadOnlyMixin, NoDelMixin, BaseModelAdmin):
    form = B2BReferralSettingForm
    raw_id_fields = (
        'business',
        'region',
    )
    readonly_fields = (
        'series_id',
        'active',
    )

    list_display = (
        'id',
        'referrer_reward_amount',
        'invited_reward_amount',
        'business',
        'region',
        'referral_enabled',
        'ambassador',
        'series_id',
        'active',
    )

    search_fields = (
        '=business_id',
        '=region_id',
    )
    hide_keyword_field = True

    list_filter = (
        'referral_enabled',
        'ambassador',
        'active',
    )

    def __init__(self, *args, **kwargs):
        self.deactivate_reward_btn = None
        super().__init__(*args, **kwargs)

    def get_form(self, request, obj=None, change=False, **kwargs):
        self.deactivate_reward_btn = self.get_deactivate_reward_btn(obj)
        return super().get_form(request, obj, **kwargs)

    def get_readonly_fields(self, request, obj=None):
        """Allow editing only last setting in series."""
        if obj and obj.active is False:
            return [field.name for field in self.opts.fields]

        return super().get_readonly_fields(request, obj)

    def save_model(self, request, obj, form, change):
        if obj.id:
            # Never allow to edit plan, always create new and
            # connect them into series
            old_obj = B2BReferralSetting.objects.get(id=obj.id)
            old_obj.active = False
            old_obj.save()

            obj.id = None
            obj.series_id = old_obj.series_id

        super().save_model(request, obj, form, change)

        create_invite_notification_for_setting_task.delay(obj.id)

        return obj

    def delete_model(self, request, obj):
        """Does not delete model. Makes inactive instead."""
        obj.active = False
        obj.save()

        return obj

    def get_urls(self):
        urls = super().get_urls()
        additional_urls = [
            url(
                r'(?P<setting_id>\d+)/deactivate',
                self.admin_site.admin_view(self.deactivate_view),
                name='deactivate',
            ),
        ]
        return additional_urls + urls

    @staticmethod
    def get_deactivate_reward_btn(obj):
        if not obj or obj and obj.active is False:
            return ''

        return '<a class="btn" href="{}">Deactivate</a>'.format(
            reverse('admin:deactivate', args=(obj.id,)),
        )

    @staticmethod
    def deactivate_view(request, setting_id):
        setting = B2BReferralSetting.objects.get(id=setting_id)
        setting.active = False
        setting.save(update_fields=['active'])

        return redirect(request.META.get('HTTP_REFERER', '/'))


admin.site.register(B2BReferralSetting, B2BReferralSettingAdmin)


class B2BReferralCodeAdmin(ArchiveModelReadOnlyMixin, BaseModelAdmin):
    raw_id_fields = ('business',)

    list_display = (
        'id',
        'business_id',
        'code',
        'usage_limit',
    )

    search_fields = (
        '=business_id',
        '=code',
    )
    hide_keyword_field = True


admin.site.register(B2BReferralCode, B2BReferralCodeAdmin)


class B2BReferralRewardAdmin(NoAddDelMixin, BaseModelAdmin):
    raw_id_fields = ('referral',)

    list_display = (
        'id',
        'referral',
        'type',
        'business',
        'status',
        'amount',
    )

    readonly_fields = (
        'created',
        'updated',
        'deleted',
        'id',
        'manual_payouts',
        'prizes',
    )

    search_fields = [
        '=id',
        '=referral__id',
        '=referral__referrer__id',
        '=referral__invited__id',
    ]

    list_filter = [
        'type',
        'status',
    ]

    def __init__(self, *args, **kwargs):
        self.mark_as_paid_btn = None
        super().__init__(*args, **kwargs)

    @staticmethod
    def manual_payouts(obj):
        manual_payouts = ManualPayout.objects.filter(
            referral_reward=obj,
        )

        manual_payouts_table = TableCreator(('Manual payout', 'status_code'))
        rows = []

        for manual_payout in manual_payouts:
            row = [
                manual_payouts_table.get_id_link(manual_payout),
                manual_payout.response and manual_payout.response['status_code'],
            ]

            rows.append(row)

        return format_html(manual_payouts_table.form_table(rows))

    @staticmethod
    def prizes(obj):
        prizes = Prize.objects.filter(
            referral_reward=obj,
        )

        prizes_table = TableCreator(
            (
                'Prize',
                'Settled',
                'Reference',
                'Payout reference',
                'Payout booking date',
            )
        )
        rows = []

        for prize in prizes:
            row = [
                prizes_table.get_id_link(prize),
                prize.settled,
                prize.reference,
                prize.payout_reference,
                prize.payout_booking_date,
            ]

            rows.append(row)

        return format_html(prizes_table.form_table(rows))

    def get_urls(self):
        urls = super().get_urls()

        additional_urls = [
            url(
                r'^(?P<referral_reward_id>\d+)/mark_as_paid',
                self.admin_site.admin_view(self.mark_as_paid_view),
                name='mark_as_paid',
            ),
        ]
        return additional_urls + urls

    def get_form(self, request, obj=None, change=False, **kwargs):
        self.mark_as_paid_btn = self.get_mark_as_paid_btn(obj)
        return super().get_form(request, obj, **kwargs)

    @staticmethod
    def get_mark_as_paid_btn(obj):
        if obj is None:
            return ''
        if obj.status != B2BRewardStatus.READY:
            return ''

        return '<a href={}" class="btn">Mark as paid</a>'.format(
            reverse("admin:mark_as_paid", args=(obj.id,))
        )

    @staticmethod
    def mark_as_paid_view(request, referral_reward_id):
        reward = get_object_or_404(B2BReferralReward, id=referral_reward_id)

        reward.mark_as_paid_manually()

        return redirect(request.META.get('HTTP_REFERER', '/'))


admin.site.register(B2BReferralReward, B2BReferralRewardAdmin)


class PrizeAdmin(ReadOnlyFieldsMixin, NoAddDelMixin, BaseModelAdmin):
    list_max_show_all = 20000
    list_per_page = 100
    list_display = [
        'id',
        'created',
        'formatted_amount',
    ]

    readonly_fields = [
        'id',
        'created',
        'formatted_amount',
    ]


admin.site.register(Prize, PrizeAdmin)
