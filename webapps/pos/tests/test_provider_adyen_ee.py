from datetime import timedelta
from decimal import Decimal

import pytest
from django.core.cache import cache
from mock import patch
from model_bakery import baker
from segment.analytics import Client

from lib.tools import tznow
from webapps.adyen import models
from webapps.adyen.consts import oper_result
from webapps.adyen.models import Auth
from webapps.adyen.tests import fixt
from webapps.business.models import Business
from webapps.pos import enums
from webapps.pos.enums import PaymentTypeEnum, receipt_status
from webapps.pos.models import (
    PaymentMethod,
    PaymentType,
    POS,
    Receipt,
    Transaction,
    PaymentRow,
)
from webapps.pos.provider import get_payment_provider
from webapps.pos.provider.adyen_ee import (
    AdyenEEPaymentProvider,
)
from webapps.pos.provider.base import PaymentProviderError
from webapps.user.models import User

provider = AdyenEEPaymentProvider()


def test_get_provider():
    provider_ = get_payment_provider(AdyenEEPaymentProvider.codename)
    assert isinstance(provider_, AdyenEEPaymentProvider)


@pytest.mark.django_db
@patch('webapps.adyen.flow.first_auth_ee')
def test_add_payment_method_success(first_auth_mock):
    card = baker.make(
        models.Card,
        last_4_digits='4321',
        brand='mc',
        active=True,
    )
    baker.make(models.Auth, reference='abc123', card=card)

    first_auth_mock.return_value = {
        'oper_result': oper_result.SUCCESS,
        'auth_ref': 'abc123',
    }

    user = baker.make(User)
    data = {
        'encrypted_data': 'asdf',
        'default': True,
    }
    device_data = {
        'fingerprint': 'somefingerprint',
        'phone_number': '123456789',
    }
    ctx = {
        'ip': '127.0.0.1',
        'allow_empty': True,
    }

    with patch('webapps.adyen.flow.make_request'):
        payment_method = provider.add_payment_method(user, data, device_data, **ctx)

    assert payment_method.provider == provider.codename
    assert payment_method.user == user
    assert payment_method.default is True
    assert payment_method.provider_ref == 'abc123'
    assert payment_method.method_type == enums.PAYMENT_METHOD_TYPE__CREDIT_CARD
    assert payment_method.card_last_digits == '4321'
    assert payment_method.card_type == enums.CARD_TYPE__MASTERCARD


def _test_receipt(
    payment_method,
    receipt,
    txn,
    pr,
    status_code,
    pnref='abc123',
):
    assert receipt.status_code == status_code
    assert receipt.transaction == txn
    assert receipt.payment_type.code == PaymentTypeEnum.PAY_BY_APP
    assert receipt.card_type is None
    assert receipt.card_last_digits is None
    assert receipt.pnref is None
    assert receipt.provider is None

    assert pr.status == status_code
    assert pr.payment_type.code == PaymentTypeEnum.PAY_BY_APP
    assert pr.card_type == payment_method.card_type
    assert pr.card_last_digits is None
    assert pr.pnref == pnref
    assert pr.provider == provider.codename


# Test problem discussed: https://booksy.slack.com/archives/GAXP4B7LM/p1653921031045649
@pytest.mark.skip('https://booksy.atlassian.net/browse/SRE-861')
@pytest.mark.django_db
@patch('webapps.adyen.flow.capture')
@patch('webapps.adyen.flow.auth')
@patch('webapps.pos.provider.adyen_ee._additional_data')
@patch.object(Client, 'identify')
@patch.object(Client, 'track')
def test_make_payment_method_success(
    analytics_track_mock,
    analytics_identify_mock,
    additional_mock,
    auth_mock,
    capture_mock,
):
    additional_mock.return_value = {'test': 'test'}
    auth_mock.return_value = {
        'oper_result': oper_result.SUCCESS,
        'auth_ref': 'abc123',
    }
    capture_mock.return_value = {
        'oper_result': oper_result.PENDING,
        'capture_ref': 'abc123',
    }

    # create objects
    pos = baker.make(POS, business=baker.make(Business))
    payment_type = baker.make(PaymentType, code=PaymentTypeEnum.PAY_BY_APP, pos=pos)
    txn = baker.make(
        Transaction,
        customer=baker.make(User),
        transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        pos=pos,
        total=Decimal('3'),
    )
    first_receipt = baker.make(
        Receipt,
        transaction=txn,
        payment_type=payment_type,
        status_code=receipt_status.CALL_FOR_PAYMENT,
    )
    txn.latest_receipt = first_receipt
    txn.save()

    pr = PaymentRow.create_with_status(
        receipt=first_receipt,
        amount=txn.total,
        payment_type=payment_type,
    )
    pr.save()
    payment_method = baker.make(PaymentMethod)

    txn = provider.make_payment(
        txn,
        payment_method=payment_method,
        payment_row=pr,
        extra_data=dict(cardholder_ip='127.0.0.1'),
    )
    cache_lock_key = f'adyen.lock.{pr.id}'
    cache.delete(cache_lock_key)
    pr = txn.latest_receipt.payment_rows.get()
    receipt = txn.latest_receipt

    assert receipt != first_receipt
    status_code = receipt_status.PAYMENT_SUCCESS
    _test_receipt(payment_method, receipt, txn, pr, status_code)

    assert analytics_track_mock.call_count == 4
    assert analytics_identify_mock.call_count == 3
    assert analytics_track_mock.call_args_list[2][1]['event'] == 'Checkout_Transaction_Completed'


def prepare_data_for_success(offset=0):
    first_auth = baker.make(models.Auth)
    ref = first_auth.reference

    if offset:
        first_auth.created = first_auth.created - timedelta(offset)
        first_auth.save()

    pos = baker.make(POS, business=baker.make(Business))
    payment_type = baker.make(PaymentType, code=PaymentTypeEnum.PAY_BY_APP, pos=pos)
    txn = baker.make(Transaction, customer=baker.make(User), pos=pos, total=Decimal('123.66'))
    first_receipt = baker.make(
        Receipt,
        payment_type=payment_type,
        transaction=txn,
        status_code=receipt_status.CALL_FOR_DEPOSIT,
        already_paid=Decimal('0'),
    )
    txn.latest_receipt = first_receipt
    txn.save()

    pr = PaymentRow.create_with_status(
        receipt=first_receipt,
        amount=txn.total,
        is_deposit=True,
        payment_type=payment_type,
    )
    pr.save()

    payment_method = baker.make(PaymentMethod, provider_ref=ref)

    return txn, payment_method, pr, first_receipt, first_auth


def check_data_for_success(txn, payment_method, pr, first_receipt, ref='a-123'):
    txn = provider.authorize_deposit(
        txn,
        payment_method=payment_method,
        payment_row=pr,
    )
    pr = txn.latest_receipt.payment_rows.get()
    receipt = txn.latest_receipt

    assert receipt != first_receipt
    status_code = receipt_status.DEPOSIT_AUTHORISATION_SUCCESS
    _test_receipt(payment_method, receipt, txn, pr, status_code, ref)


@pytest.mark.django_db
@patch('webapps.pos.provider.adyen_ee.get_reference')
@patch('webapps.adyen.flow.auth')
@patch('webapps.pos.provider.adyen_ee._additional_data')
def test_authorize_deposit_method_success_reauth(additional_mock, auth_mock, pnref_mock):
    additional_mock.return_value = {'test': 'test'}
    pnref_mock.return_value = 'a-123'

    (txn, payment_method, pr, first_receipt, _) = prepare_data_for_success(offset=60)

    check_data_for_success(txn, payment_method, pr, first_receipt)
    assert auth_mock.call_count == 0


@pytest.mark.django_db
@patch('webapps.pos.provider.adyen_ee.get_reference')
@patch('webapps.adyen.flow.auth')
@patch('webapps.pos.provider.adyen_ee._additional_data')
def test_authorize_deposit_method_success_reuse(additional_mock, auth_mock, pnref_mock):
    additional_mock.return_value = {'test': 'test'}
    pnref_mock.return_value = 'a-123'

    (txn, payment_method, pr, first_receipt, _) = prepare_data_for_success(offset=0)

    check_data_for_success(txn, payment_method, pr, first_receipt)
    assert auth_mock.call_count == 0


@pytest.mark.django_db
@patch('webapps.pos.provider.adyen_ee.get_reference')
@patch('webapps.adyen.flow.auth')
@patch('webapps.pos.provider.adyen_ee._additional_data')
def test_authorize_prepayent_method_success_reuse(additional_mock, auth_mock, pnref_mock):
    additional_mock.return_value = {'test': 'test'}
    pnref_mock.return_value = 'a-123'
    first_auth = baker.make(models.Auth)
    ref = first_auth.reference

    pos = baker.make(POS, business=baker.make(Business))
    payment_type = baker.make(PaymentType, code=PaymentTypeEnum.PREPAYMENT, pos=pos)
    txn = baker.make(Transaction, customer=baker.make(User), pos=pos, total=Decimal('123.66'))
    first_receipt = baker.make(
        Receipt,
        payment_type=payment_type,
        transaction=txn,
        status_code=receipt_status.CALL_FOR_PREPAYMENT,
        already_paid=Decimal('0'),
    )
    txn.latest_receipt = first_receipt
    txn.save()

    pr = PaymentRow.create_with_status(
        receipt=first_receipt,
        amount=txn.total,
        payment_type=payment_type,
        status=receipt_status.CALL_FOR_PREPAYMENT,
    )
    pr.save()

    payment_method = baker.make(PaymentMethod, provider_ref=ref)

    txn = provider.authorize_deposit(
        txn,
        payment_method=payment_method,
        payment_row=pr,
    )
    pr = txn.latest_receipt.payment_rows.get()
    receipt = txn.latest_receipt

    assert auth_mock.call_count == 0

    assert receipt != first_receipt
    assert receipt.status_code == receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS
    assert receipt.transaction == txn
    assert receipt.payment_type.code == PaymentTypeEnum.PREPAYMENT
    assert receipt.card_type is None
    assert receipt.card_last_digits is None
    assert receipt.pnref is None
    assert receipt.provider is None

    assert pr.status == receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS
    assert pr.payment_type.code == PaymentTypeEnum.PREPAYMENT
    assert pr.card_type == payment_method.card_type
    assert pr.card_last_digits is None
    assert pr.pnref == 'a-123'
    assert pr.provider == provider.codename


@pytest.mark.django_db
@patch('webapps.pos.provider.adyen_ee.get_reference')
@patch('webapps.adyen.flow.auth')
@patch('webapps.pos.provider.adyen_ee._additional_data')
def test_authorize_deposit_method_success_reuse_aut(additional_mock, auth_mock, pnref_mock):
    additional_mock.return_value = {'test': 'test'}
    pnref_mock.return_value = 'a-123'

    (txn, payment_method, pr, first_receipt, first_auth) = prepare_data_for_success(offset=60)

    baker.make(models.Auth, first_auth=first_auth, oper_result=oper_result.SUCCESS)

    check_data_for_success(txn, payment_method, pr, first_receipt)
    assert auth_mock.call_count == 0


@pytest.mark.django_db
@patch('webapps.pos.provider.adyen_ee.get_reference')
@patch('webapps.adyen.flow.auth')
@patch('webapps.pos.provider.adyen_ee._additional_data')
def test_authorize_deposit_method_success_aut_old(additional_mock, auth_mock, pnref_mock):
    additional_mock.return_value = {'test': 'test'}
    pnref_mock.return_value = 'a-123'

    (txn, payment_method, pr, first_receipt, first_auth) = prepare_data_for_success(offset=60)

    auth = baker.make(models.Auth, first_auth=first_auth, oper_result=oper_result.SUCCESS)
    auth.created = auth.created - timedelta(days=31)
    auth.save()

    check_data_for_success(txn, payment_method, pr, first_receipt)
    assert auth_mock.call_count == 0


@pytest.mark.django_db
@patch('webapps.pos.provider.adyen_ee.get_reference')
@patch('webapps.adyen.flow.auth')
@patch('webapps.pos.provider.adyen_ee._additional_data')
def test_authorize_deposit_method_success_aut_fail(additional_mock, auth_mock, pnref_mock):
    additional_mock.return_value = {'test': 'test'}
    pnref_mock.return_value = 'a-123'

    (txn, payment_method, pr, first_receipt, first_auth) = prepare_data_for_success(offset=60)

    baker.make(models.Auth, first_auth=first_auth, oper_result=oper_result.FAILED)

    check_data_for_success(txn, payment_method, pr, first_receipt)
    assert auth_mock.call_count == 0


@pytest.mark.django_db
@patch('webapps.pos.provider.adyen_ee.get_reference')
@patch('webapps.adyen.flow.auth')
@patch('webapps.pos.provider.adyen_ee._additional_data')
def test_authorize_deposit_method_success_aut_fail2(additional_mock, auth_mock, pnref_mock):
    """
    This time authorization is in proper range, but due to failed authorize,
    authorization should be done again.
    """
    additional_mock.return_value = {'test': 'test'}
    pnref_mock.return_value = 'a-123'

    (txn, payment_method, pr, first_receipt, first_auth) = prepare_data_for_success()

    baker.make(models.Auth, first_auth=first_auth, oper_result=oper_result.FAILED)

    check_data_for_success(txn, payment_method, pr, first_receipt)
    assert auth_mock.call_count == 0


@pytest.mark.django_db
@patch('webapps.pos.provider.adyen_ee.get_reference')
@patch('webapps.adyen.flow.auth')
@patch('webapps.pos.provider.adyen_ee._additional_data')
def test_authorize_deposit_method_success_reuse_cap(additional_mock, auth_mock, pnref_mock):
    additional_mock.return_value = {'test': 'test'}
    pnref_mock.return_value = 'a-123'

    (txn, payment_method, pr, first_receipt, auth) = prepare_data_for_success(offset=60)

    baker.make(models.Capture, auth=auth, oper_result=oper_result.PENDING)

    check_data_for_success(txn, payment_method, pr, first_receipt)
    assert auth_mock.call_count == 0


@pytest.mark.django_db
@patch('webapps.pos.provider.adyen_ee.get_reference')
@patch('webapps.adyen.flow.auth')
@patch('webapps.pos.provider.adyen_ee._additional_data')
def test_authorize_deposit_method_success_cap_old(additional_mock, auth_mock, pnref_mock):
    additional_mock.return_value = {'test': 'test'}
    pnref_mock.return_value = 'a-123'

    (txn, payment_method, pr, first_receipt, auth) = prepare_data_for_success(offset=60)

    cap = baker.make(models.Capture, auth=auth, oper_result=oper_result.PENDING)
    cap.created = cap.created - timedelta(days=31)
    cap.save()

    check_data_for_success(txn, payment_method, pr, first_receipt)
    assert auth_mock.call_count == 0


@pytest.mark.django_db
@patch('webapps.pos.provider.adyen_ee.get_reference')
@patch('webapps.adyen.flow.auth')
@patch('webapps.pos.provider.adyen_ee._additional_data')
def test_authorize_deposit_method_success_cap_fail(additional_mock, auth_mock, pnref_mock):
    additional_mock.return_value = {'test': 'test'}
    pnref_mock.return_value = 'a-123'

    (txn, payment_method, pr, first_receipt, auth) = prepare_data_for_success(offset=60)

    baker.make(models.Capture, auth=auth, oper_result=oper_result.FAILED)

    check_data_for_success(txn, payment_method, pr, first_receipt)
    assert auth_mock.call_count == 0


@pytest.mark.django_db
@patch('webapps.pos.provider.adyen_ee.get_reference')
@patch('webapps.adyen.flow.auth')
@patch('webapps.pos.provider.adyen_ee._additional_data')
def test_authorize_deposit_method_success_cap_fail2(additional_mock, auth_mock, pnref_mock):
    """
    This time authorization is in proper range, but due to failed capture,
    authorization should be done again.
    """
    additional_mock.return_value = {'test': 'test'}
    pnref_mock.return_value = 'a-123'

    (txn, payment_method, pr, first_receipt, auth) = prepare_data_for_success()

    baker.make(models.Capture, auth=auth, oper_result=oper_result.FAILED)

    check_data_for_success(txn, payment_method, pr, first_receipt)
    assert auth_mock.call_count == 0


# Test problem discussed: https://booksy.slack.com/archives/GAXP4B7LM/p1653921031045649
@pytest.mark.skip('https://booksy.atlassian.net/browse/SRE-861')
@pytest.mark.django_db
@patch('webapps.pos.provider.adyen_ee.get_reference')
@patch('webapps.adyen.flow.auth')
@patch('webapps.adyen.flow.capture')
@patch('webapps.pos.provider.adyen_ee._additional_data')
@patch.object(Client, 'identify')
@patch.object(Client, 'track')
def test_charge_deposit_method_success(
    analytics_track_mock,
    analytics_identify_mock,
    additional_mock,
    capture_mock,
    auth_mock,
    pnref_mock,
):
    pnref = 'zxcvb'
    additional_mock.return_value = {'test': 'test'}
    auth_mock.return_value = {
        'oper_result': oper_result.SUCCESS,
        'auth_ref': pnref,
    }
    capture_mock.return_value = {
        'oper_result': oper_result.PENDING,
        'capture_ref': 'abc123',
    }
    pnref_mock.return_value = 'a-123'

    # create objects
    first_auth = baker.make(models.Auth)
    baker.make(models.Auth, reference=pnref, first_auth=first_auth)
    pos = baker.make(POS, business=baker.make(Business))
    paymt_type = baker.make(PaymentType, code=PaymentTypeEnum.PAY_BY_APP, pos=pos)

    txn = baker.make(
        Transaction,
        customer=baker.make(User),
        pos=pos,
        total=Decimal('3.5'),
    )
    first_receipt = baker.make(
        Receipt,
        transaction=txn,
        payment_type=paymt_type,
        status_code=receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
    )
    txn.latest_receipt = first_receipt
    txn.save()

    pr = PaymentRow.create_with_status(
        receipt=first_receipt,
        payment_type=paymt_type,
        status=receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
        is_deposit=True,
        amount=Decimal('3.5'),
        pnref=pnref,
    )
    pr.save()

    payment_method = baker.make(PaymentMethod, provider_ref=first_auth.reference)
    txn = provider.charge_deposit(
        txn,
        operator=pos.business.owner,
        payment_row=pr,
    )
    pr = txn.latest_receipt.payment_rows.get()
    receipt = txn.latest_receipt

    assert receipt != first_receipt
    status_code = receipt_status.DEPOSIT_CHARGE_SUCCESS
    assert analytics_track_mock.call_count == 2
    assert analytics_identify_mock.call_count == 2
    _test_receipt(payment_method, receipt, txn, pr, status_code)


@pytest.mark.django_db
@patch('webapps.adyen.flow.cancel_or_refund')
def test_cancel_deposit_method_success(cancel_or_refund_mock):
    pnref = 'zxcvb'
    # create objects
    first_auth = baker.make(models.Auth)
    baker.make(models.Auth, reference=pnref, first_auth=first_auth)

    pos = baker.make(POS, business=baker.make(Business))
    paymt_type = baker.make(PaymentType, code=PaymentTypeEnum.PAY_BY_APP, pos=pos)

    txn = baker.make(
        Transaction,
        customer=baker.make(User),
        pos=pos,
        total=Decimal('3.5'),
        transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
    )
    first_receipt = baker.make(
        Receipt,
        transaction=txn,
        payment_type=paymt_type,
        status_code=receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
    )
    txn.latest_receipt = first_receipt
    txn.save()

    pr = PaymentRow.create_with_status(
        receipt=first_receipt,
        payment_type=paymt_type,
        status=receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
        is_deposit=True,
        amount=Decimal('3.5'),
    )
    pr.save()

    payment_method = baker.make(PaymentMethod, provider_ref=first_auth.reference)
    txn = provider.cancel_deposit(txn, payment_row=pr)
    receipt = Receipt.objects.first()

    assert cancel_or_refund_mock.call_count == 0
    assert receipt.status_code == receipt_status.DEPOSIT_CHARGE_CANCELED
    assert receipt.transaction == txn
    assert receipt.payment_type.code == PaymentTypeEnum.PAY_BY_APP
    assert receipt.card_type is None
    assert receipt.card_last_digits is None
    assert receipt.pnref is None
    assert receipt.provider is None

    pr = txn.latest_receipt.payment_rows.get()

    assert pr.card_type == payment_method.card_type
    assert pr.card_last_digits is None
    assert pr.pnref is None
    assert pr.provider == provider.codename


@pytest.mark.django_db
@patch('webapps.adyen.flow.cancel_or_refund')
def test_reject_prepayment_method_success(cancel_or_refund_mock):
    # create objects
    pos = baker.make(POS, business=baker.make(Business))
    paymt_type = baker.make(PaymentType, code=PaymentTypeEnum.PREPAYMENT, pos=pos)

    txn = baker.make(
        Transaction,
        customer=baker.make(User),
        pos=pos,
        total=Decimal('3.5'),
        transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
    )
    first_receipt = baker.make(
        Receipt,
        transaction=txn,
        payment_type=paymt_type,
        status_code=receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS,
    )
    txn.latest_receipt = first_receipt
    txn.save()

    pr = PaymentRow.create_with_status(
        receipt=first_receipt,
        payment_type=paymt_type,
        status=receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS,
        is_deposit=True,
        amount=Decimal('3.5'),
    )
    pr.save()

    payment_method = baker.make(PaymentMethod)
    txn = provider.cancel_deposit(txn, payment_row=pr)
    receipt = Receipt.objects.first()

    assert cancel_or_refund_mock.call_count == 0
    assert receipt.status_code == receipt_status.DEPOSIT_CHARGE_CANCELED
    assert receipt.transaction == txn
    assert receipt.payment_type.code == PaymentTypeEnum.PREPAYMENT
    assert receipt.card_type is None
    assert receipt.card_last_digits is None
    assert receipt.pnref is None
    assert receipt.provider is None

    pr = txn.latest_receipt.payment_rows.get()

    assert pr.card_type == payment_method.card_type
    assert pr.card_last_digits is None
    assert pr.pnref is None
    assert pr.provider == provider.codename


@pytest.mark.django_db
@patch('webapps.adyen.flow.first_auth_ee')
def test_add_payment_method_fail(first_auth_mock):
    first_auth_mock.return_value = {
        'oper_result': oper_result.FAILED,
        'auth_ref': 'abc123',
    }

    user = baker.make(User)
    data = {
        'encrypted_data': 'asdf',
        'default': True,
    }
    ctx = {
        'ip': '127.0.0.1',
    }

    with pytest.raises(PaymentProviderError):
        provider.add_payment_method(user, data, None, **ctx)


@pytest.mark.django_db
@patch('webapps.adyen.flow.auth')
@patch('webapps.pos.provider.adyen_ee._additional_data')
def test_make_payment_method_auth_fail(additional_mock, auth_mock):
    additional_mock.return_value = {'test': 'test'}
    auth_mock.return_value = {
        'oper_result': oper_result.FAILED,
        'auth_ref': 'abc123',
    }

    # create objects
    pos = baker.make(POS, business=baker.make(Business))
    payment_type = baker.make(PaymentType, code=PaymentTypeEnum.PAY_BY_APP, pos=pos)
    txn = baker.make(
        Transaction,
        customer=baker.make(User),
        pos=pos,
        total=Decimal('3.5'),
    )
    first_receipt = baker.make(
        Receipt,
        transaction=txn,
        payment_type=payment_type,
        status_code=receipt_status.CALL_FOR_PAYMENT,
    )
    txn.latest_receipt = first_receipt
    txn.save()

    pr = PaymentRow.create_with_status(
        receipt=first_receipt,
        payment_type=payment_type,
        amount=Decimal('3.5'),
    )
    pr.save()

    payment_method = baker.make(PaymentMethod)

    txn = provider.make_payment(
        txn,
        payment_method=payment_method,
        payment_row=pr,
    )
    pr = txn.latest_receipt.payment_rows.get()
    receipt = txn.latest_receipt

    assert receipt != first_receipt
    status_code = receipt_status.PAYMENT_FAILED
    _test_receipt(payment_method, receipt, txn, pr, status_code)


@pytest.mark.django_db
@patch('webapps.adyen.flow.capture')
@patch('webapps.adyen.flow.auth')
@patch('webapps.pos.provider.adyen_ee._additional_data')
def test_make_payment_method_capture_failed(additional_mock, auth_mock, capture_mock):
    additional_mock.return_value = {'test': 'test'}
    auth_mock.return_value = {
        'oper_result': oper_result.SUCCESS,
        'auth_ref': 'abc123',
    }
    capture_mock.return_value = {
        'oper_result': oper_result.FAILED,
        'capture_ref': 'dsf123',
    }

    # create objects
    baker.make(Auth, reference='abc123', oper_result=oper_result.SUCCESS)
    pos = baker.make(POS, business=baker.make(Business))
    payment_type = baker.make(PaymentType, code=PaymentTypeEnum.PAY_BY_APP, pos=pos)
    txn = baker.make(
        Transaction,
        customer=baker.make(User),
        pos=pos,
        total=Decimal('3.5'),
    )
    first_receipt = baker.make(
        Receipt,
        transaction=txn,
        payment_type=payment_type,
        status_code=receipt_status.CALL_FOR_PAYMENT,
    )
    txn.latest_receipt = first_receipt
    txn.save()

    pr = PaymentRow.create_with_status(
        receipt=first_receipt,
        payment_type=payment_type,
        amount=Decimal('3.5'),
    )
    pr.save()
    payment_method = baker.make(PaymentMethod)

    provider.make_payment(
        txn,
        payment_method=payment_method,
        payment_row=pr,
        extra_data=dict(cardholder_ip='127.0.0.1'),
    )

    txn.refresh_from_db()
    pr = txn.latest_receipt.payment_rows.get()
    receipt = txn.latest_receipt

    assert receipt != first_receipt
    status_code = receipt_status.PAYMENT_FAILED
    _test_receipt(payment_method, receipt, txn, pr, status_code, 'dsf123')


@pytest.mark.django_db
@patch('webapps.adyen.flow.auth')
@patch('webapps.adyen.flow.capture')
@patch('webapps.pos.provider.adyen_ee._additional_data')
def test_charge_deposit_method_fail(additional_mock, capture_mock, auth_mock):
    pnref = 'zxcvb'
    additional_mock.return_value = {}
    auth_mock.return_value = {
        'oper_result': oper_result.SUCCESS,
        'auth_ref': pnref,
    }
    capture_mock.return_value = {
        'oper_result': oper_result.FAILED,
        'capture_ref': 'abc123',
    }

    # create objects
    first_auth = baker.make(models.Auth)
    baker.make(models.Auth, reference=pnref, first_auth=first_auth)
    pos = baker.make(POS, business=baker.make(Business))
    payment_type = baker.make(PaymentType, code=PaymentTypeEnum.PAY_BY_APP, pos=pos)

    txn = baker.make(
        Transaction,
        customer=baker.make(User),
        pos=pos,
        total=Decimal('3.5'),
    )
    first_receipt = baker.make(
        Receipt,
        transaction=txn,
        payment_type=payment_type,
        status_code=receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
    )
    txn.latest_receipt = first_receipt
    txn.save()

    pr = PaymentRow.create_with_status(
        receipt=first_receipt,
        payment_type=payment_type,
        amount=Decimal('3.5'),
        status=receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
        pnref=pnref,
    )
    pr.save()

    payment_method = baker.make(PaymentMethod, provider_ref=first_auth.reference)
    provider.charge_deposit(
        txn,
        operator=pos.business.owner,
        payment_row=pr,
    )

    txn.refresh_from_db()
    pr = txn.latest_receipt.payment_rows.get()
    receipt = txn.latest_receipt

    assert receipt != first_receipt
    status_code = receipt_status.DEPOSIT_CHARGE_FAILED
    _test_receipt(
        payment_method,
        receipt,
        txn,
        pr,
        status_code,
        'abc123',
    )


@pytest.mark.django_db
@patch('webapps.adyen.flow.disable')
def test_remove_payment_method_success(disable_mock):
    payment_method = baker.make(PaymentMethod, provider_ref='zxcv')
    fixt.create_first_auth(
        auth_kws={'reference': 'zxcv'},
        card_kws={'recurr_detail_ref': 'qwer', 'adyen_confirmed': True},
        cardholder_kws={'shopper_reference': 'asdf'},
    )
    models.Card.all_objects.update(created=tznow() - timedelta(hours=1))

    with patch('webapps.adyen.flow.make_request'):
        provider.remove_payment_method(payment_method)

    assert disable_mock.call_count == 1

    card = models.Card.all_objects.get()
    assert card.active is False


GENERIC_MSG = (
    'The issuing bank did not approve this action. Please check the '
    'card details or, if the problem persists, try a different card.'
)
CVV_MSG = (
    'The provided security code (CVC/CVV) is incorrect. Please verify '
    'and try again. If the problem persists, please try a different card.'
)
EXPIRY_DATE_MSG = 'This card has expired. Please try another card.'
CCN_MSG = (
    'The provided card number is incorrect. Please verify and try '
    'again. If the problem persists, please try a different card.'
)
GTFO_MSG = (
    'The issuing bank did not approve this action. Please try a '
    'different card or contact the bank.'
)
NOT_ENOUGH_BALANCE_MSG = (
    'There are insufficient funds or the transaction exceeds the ' 'limit. Please try another card.'
)
FRAUD_MSG = (
    'Unfortunately, the payment service provider did not approve this '
    'action. Please try a different card or get in touch with '
    'Booksy\'s customer support.'
)


@pytest.mark.parametrize(
    'result_code, expected',
    [
        (oper_result.FAILED, GENERIC_MSG),
        (oper_result.CVC_DECLINED, CVV_MSG),
        (oper_result.INVALID_CVC_LENGTH, CVV_MSG),
        (oper_result.INVALID_CVC, CVV_MSG),
        (oper_result.EXPIRED_CARD, EXPIRY_DATE_MSG),
        (oper_result.INVALID_EXPIRY_DATE, EXPIRY_DATE_MSG),
        (oper_result.INVALID_EXPIRY_MONTH_OR_BEFORE_NOW, EXPIRY_DATE_MSG),
        (oper_result.INVALID_EXPIRY_MONTH, EXPIRY_DATE_MSG),
        (oper_result.INVALID_CCN, CCN_MSG),
        (oper_result.INVALID_CARD_NUMBER, CCN_MSG),
        (oper_result.REFUSED, GTFO_MSG),
        (oper_result.BLOCKED_CARD, GTFO_MSG),
        (oper_result.NOT_SUPPORTED, GTFO_MSG),
        (oper_result.RESTRICTED_CARD, GTFO_MSG),
        (oper_result.CONTRACT_NOT_FOUND, GTFO_MSG),
        (oper_result.TRANSACTION_NOT_PERMITTED, GTFO_MSG),
        (oper_result.PAYMENT_DETAILS_ARE_NOT_SUPPORTED, GTFO_MSG),
        (oper_result.NOT_ENOUGH_BALANCE, NOT_ENOUGH_BALANCE_MSG),
        (oper_result.FRAUD, FRAUD_MSG),
    ],
    ids=[
        'generic',
        'cvv_declined',
        'cvv_length',
        'cvv_invalid',
        'expired_card',
        'expired_card_date',
        'expired_card_month_or_before_now',
        'expired_card_month',
        'invalid_ccn',
        'invalid_card_number',
        'refused',
        'blocked_card',
        'not_supported',
        'restricted_card',
        'contract_not_found',
        'not_permitted',
        'details_not_supported',
        'not_enought_balance',
        'fraud',
    ],
)
def test_add_payment_method_err_msg(result_code, expected):
    payment_row = PaymentRow()
    payment_row.oper_result = result_code
    result = AdyenEEPaymentProvider.add_payment_method_err_msg(payment_row)
    assert result['msg'] == expected
