import unittest
from datetime import timedelta
from unittest.mock import patch

import pytest
from model_bakery import baker
from pytz import UTC
from segment.analytics import Client

from lib.timezone_hours import get_timezones_with_hour
from lib.test_utils import create_subbooking
from lib.tools import tznow
from webapps.booking.models import Appointment
from webapps.booking.models import BookingSources
from webapps.booking.tests.utils import create_appointment
from webapps.business.models import Business
from webapps.pos.enums import PaymentTypeEnum, receipt_status
from webapps.pos.models import Transaction, PaymentType, Receipt, PaymentRow, POS
from webapps.pos.tools import unfinished_prepayment_query
from webapps.segment.utils import assert_events_triggered
from webapps.structure.models import Region
from webapps.user.models import User

test_date_time = tznow(tz=UTC)

setup_time = test_date_time.replace(year=2018, month=3, day=1, hour=10, minute=0)
test_date_time = test_date_time.replace(year=2018, month=4, day=1, hour=10, minute=0)


@pytest.mark.django_db
class TestGetBusiness(unittest.TestCase):

    def setUp(self):
        super().setUp()
        self.test_zone = 'Europe/Warsaw'

        list_regions = [
            'Europe/Warsaw',
            'Europe/Helsinki',
            'America/New_York',
            'Indian/Comoro',
            'America/Sao_Paulo',
        ]

        self.source = baker.make(BookingSources)
        for key, region_name in enumerate(list_regions):
            # business
            business = baker.make(
                Business,
                time_zone_name=region_name,
            )
            baker.make(
                Region,
                time_zone_name=region_name,
            )

            tz = business.get_timezone()
            assert tz._long_name == region_name  # pylint: disable=protected-access

            booking, *_ = create_subbooking(
                business=business,
                booking_kws=dict(
                    updated_by=baker.make(User),
                    source=self.source,
                    type=Appointment.TYPE.BUSINESS,
                    status=Appointment.STATUS.FINISHED,
                ),
            )

            pos = baker.make(POS, business=business)
            txn = baker.make(
                Transaction,
                pos=pos,
                total=100,
                transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
                appointment_id=booking.appointment_id,
            )
            prep = baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.PREPAYMENT)
            receipt = baker.make(
                Receipt,
                payment_type=prep,
                status_code=receipt_status.PREPAYMENT_SUCCESS,
                transaction=txn,
            )
            txn.latest_receipt = receipt
            txn.save()

            baker.make(
                PaymentRow,
                amount=100 if key != 4 else 50,
                payment_type=prep,
                status=receipt_status.PREPAYMENT_SUCCESS,
                receipt=receipt,
            )

        self.business = baker.make(
            Business,
            time_zone_name=self.test_zone,
        )

        tz = self.business.get_timezone()
        assert tz._long_name == self.test_zone  # pylint: disable=protected-access

    @pytest.mark.freeze_time(test_date_time)
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_closing_transaction_number(self, analytics_track_mock, analytics_identify_mock):
        result = {
            6: 1,
            12: 1,
            13: 2,
            7: 0,  # Transaction is not fully paid
        }

        for i in range(25):
            closing_timezones = get_timezones_with_hour(i)
            txs = unfinished_prepayment_query(closing_timezones)

            assert len(txs) == result.get(i, 0)

            for trn in txs:
                pr = trn.latest_receipt.payment_rows.get()
                pr.update_status(status=receipt_status.PAYMENT_SUCCESS)

        for i in range(25):
            closing_timezones = get_timezones_with_hour(i)
            txs = unfinished_prepayment_query(closing_timezones)

            assert len(txs) == 0

        assert_events_triggered(
            {
                'Payment_Transaction_Completed': {
                    'expected_tracks': 4,
                    'expected_identifies': 4,
                },
                'Checkout_Transaction_Completed': {
                    'expected_tracks': 4,
                },
            },
            segment_identify_mock=analytics_identify_mock,
            segment_track_mock=analytics_track_mock,
        )

    @pytest.mark.freeze_time(test_date_time)
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_booking(self, analytics_track_mock, analytics_identify_mock):
        booking, *_ = create_subbooking(
            business=self.business,
            booking_kws=dict(
                source=self.source,
                type=Appointment.TYPE.BUSINESS,
                status=Appointment.STATUS.FINISHED,
            ),
        )

        pos = baker.make(POS, business=self.business)
        txn = baker.make(
            Transaction,
            pos=pos,
            total=100,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            appointment_id=booking.appointment_id,
        )
        prep = baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.PREPAYMENT)
        receipt = baker.make(
            Receipt,
            payment_type=prep,
            status_code=receipt_status.PREPAYMENT_SUCCESS,
            transaction=txn,
        )
        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            PaymentRow,
            amount=100,
            payment_type=prep,
            status=receipt_status.PREPAYMENT_SUCCESS,
            receipt=receipt,
        )

        #  MAKE TEST
        result = {
            6: 1,
            12: 2,
            13: 2,
            7: 0,  # Transaction is not fully paid
        }

        for i in range(25):
            closing_timezones = get_timezones_with_hour(i)
            txs = unfinished_prepayment_query(closing_timezones)
            assert len(txs) == result.get(i, 0)

            for trn in txs:
                pr = trn.latest_receipt.payment_rows.get()
                pr.update_status(status=receipt_status.PAYMENT_SUCCESS)

        for i in range(25):
            closing_timezones = get_timezones_with_hour(i)
            txs = unfinished_prepayment_query(closing_timezones)

            assert len(txs) == 0

        assert analytics_track_mock.call_count == 10
        assert analytics_identify_mock.call_count == 5
        self.assertEqual(
            {call[1]['event'] for call in analytics_track_mock.call_args_list},
            {'Payment_Transaction_Completed', 'Checkout_Transaction_Completed'},
        )
        assert_events_triggered(
            {
                'Payment_Transaction_Completed': {
                    'expected_tracks': 5,
                    'expected_identifies': 5,
                },
                'Checkout_Transaction_Completed': {
                    'expected_tracks': 5,
                },
            },
            segment_identify_mock=analytics_identify_mock,
            segment_track_mock=analytics_track_mock,
        )

    @pytest.mark.freeze_time(test_date_time)
    def test_nowshow_booking(self):
        booking, *_ = create_subbooking(
            business=self.business,
            booking_kws=dict(
                source=self.source,
                type=Appointment.TYPE.BUSINESS,
                status=Appointment.STATUS.NOSHOW,
            ),
        )

        pos = baker.make(POS, business=self.business)
        txn = baker.make(
            Transaction,
            pos=pos,
            total=100,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            appointment_id=booking.appointment_id,
        )
        prep = baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.PREPAYMENT)
        receipt = baker.make(
            Receipt,
            payment_type=prep,
            status_code=receipt_status.PREPAYMENT_SUCCESS,
            transaction=txn,
        )
        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            PaymentRow,
            amount=100,
            payment_type=prep,
            status=receipt_status.PREPAYMENT_SUCCESS,
            receipt=receipt,
        )

        #  MAKE TEST
        result = {
            6: 1,
            12: 2,
            13: 2,
            7: 0,  # Transaction is not fully paid
        }

        for i in range(25):
            closing_timezones = get_timezones_with_hour(i)
            txs = unfinished_prepayment_query(closing_timezones)
            assert len(txs) == result.get(i, 0)

            for trn in txs:
                pr = trn.latest_receipt.payment_rows.get()
                pr.update_status(status=receipt_status.PAYMENT_SUCCESS)

        for i in range(25):
            closing_timezones = get_timezones_with_hour(i)
            txs = unfinished_prepayment_query(closing_timezones)

            assert len(txs) == 0

    @pytest.mark.freeze_time(test_date_time)
    def test_nowshow_not_fully_paid_booking(self):
        booking, *_ = create_subbooking(
            business=self.business,
            booking_kws=dict(
                source=self.source,
                type=Appointment.TYPE.BUSINESS,
                status=Appointment.STATUS.NOSHOW,
            ),
        )

        pos = baker.make(POS, business=self.business)
        txn = baker.make(
            Transaction,
            pos=pos,
            total=100,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            appointment_id=booking.appointment_id,
        )
        prep = baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.PREPAYMENT)
        receipt = baker.make(
            Receipt,
            payment_type=prep,
            status_code=receipt_status.PREPAYMENT_SUCCESS,
            transaction=txn,
        )
        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            PaymentRow,
            amount=70,
            payment_type=prep,
            status=receipt_status.PREPAYMENT_SUCCESS,
            receipt=receipt,
        )

        #  MAKE TEST
        result = {
            6: 1,
            12: 2,
            13: 2,
            7: 0,  # Transaction is not fully paid
        }

        for i in range(25):
            closing_timezones = get_timezones_with_hour(i)
            txs = unfinished_prepayment_query(closing_timezones)
            assert len(txs) == result.get(i, 0)

            for trn in txs:
                pr = trn.latest_receipt.payment_rows.get()
                pr.update_status(status=receipt_status.PAYMENT_SUCCESS)

        for i in range(25):
            closing_timezones = get_timezones_with_hour(i)
            txs = unfinished_prepayment_query(closing_timezones)

            assert len(txs) == 0

    @pytest.mark.freeze_time(test_date_time)
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_canceled_booking(self, analytics_track_mock, analytics_identify_mock):
        booking, *_ = create_subbooking(
            business=self.business,
            booking_kws=dict(
                source=self.source,
                type=Appointment.TYPE.BUSINESS,
                status=Appointment.STATUS.NOSHOW,
            ),
        )

        pos = baker.make(POS, business=self.business)
        txn = baker.make(
            Transaction,
            pos=pos,
            total=150,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            appointment_id=booking.appointment_id,
        )
        prep = baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.PREPAYMENT)

        baker.make(
            PaymentRow,
            amount=100,
            payment_type=prep,
            status=receipt_status.PREPAYMENT_SUCCESS,
            receipt=baker.make(
                Receipt,
                payment_type=prep,
                status_code=receipt_status.PREPAYMENT_SUCCESS,
                transaction=txn,
            ),
        )

        baker.make(
            PaymentRow,
            amount=100,
            payment_type=prep,
            status=receipt_status.PREPAYMENT_SUCCESS,
            receipt=baker.make(
                Receipt,
                payment_type=prep,
                status_code=receipt_status.SENT_FOR_REFUND,
                transaction=txn,
            ),
        )

        receipt = baker.make(
            Receipt,
            payment_type=prep,
            status_code=receipt_status.PREPAYMENT_SUCCESS,
            transaction=txn,
        )
        baker.make(
            PaymentRow,
            amount=100,
            payment_type=prep,
            status=receipt_status.REFUNDED,
            receipt=receipt,
        )
        txn.latest_receipt = receipt
        txn.save()

        #  MAKE TEST
        result = {
            6: 1,
            12: 1,
            13: 2,
            7: 0,  # Transaction is not fully paid
        }

        for i in range(25):
            closing_timezones = get_timezones_with_hour(i)
            txs = unfinished_prepayment_query(closing_timezones)
            assert len(txs) == result.get(i, 0), i

            for trn in txs:
                pr = trn.latest_receipt.payment_rows.get()
                pr.update_status(status=receipt_status.PAYMENT_SUCCESS)

        for i in range(25):
            closing_timezones = get_timezones_with_hour(i)
            txs = unfinished_prepayment_query(closing_timezones)

            assert len(txs) == 0

        assert analytics_track_mock.call_count == 8
        assert analytics_identify_mock.call_count == 4
        assert_events_triggered(
            {
                'Payment_Transaction_Completed': {
                    'expected_tracks': 4,
                    'expected_identifies': 4,
                },
                'Checkout_Transaction_Completed': {
                    'expected_tracks': 4,
                },
            },
            segment_identify_mock=analytics_identify_mock,
            segment_track_mock=analytics_track_mock,
        )

    @pytest.mark.freeze_time(test_date_time)
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_future_booking(self, analytics_track_mock, analytics_identify_mock):
        booking, *_ = create_subbooking(
            business=self.business,
            booking_kws=dict(
                source=self.source,
                type=Appointment.TYPE.BUSINESS,
                status=Appointment.STATUS.ACCEPTED,
            ),
        )

        pos = baker.make(POS, business=self.business)
        txn = baker.make(
            Transaction,
            pos=pos,
            total=100,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            appointment_id=booking.appointment_id,
        )
        prep = baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.PREPAYMENT)
        receipt = baker.make(
            Receipt,
            payment_type=prep,
            status_code=receipt_status.PREPAYMENT_SUCCESS,
            transaction=txn,
        )
        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            PaymentRow,
            amount=100,
            payment_type=prep,
            status=receipt_status.PREPAYMENT_SUCCESS,
            receipt=receipt,
        )

        #  MAKE TEST
        result = {
            6: 1,
            12: 1,
            13: 2,
            7: 0,  # Transaction is not fully paid
        }

        for i in range(25):
            closing_timezones = get_timezones_with_hour(i)
            txs = unfinished_prepayment_query(closing_timezones)
            assert len(txs) == result.get(i, 0)

            for trn in txs:
                pr = trn.latest_receipt.payment_rows.get()
                pr.update_status(status=receipt_status.PAYMENT_SUCCESS)

        for i in range(25):
            closing_timezones = get_timezones_with_hour(i)
            txs = unfinished_prepayment_query(closing_timezones)

            assert len(txs) == 0

        assert_events_triggered(
            {
                'Payment_Transaction_Completed': {
                    'expected_tracks': 4,
                    'expected_identifies': 4,
                },
                'Checkout_Transaction_Completed': {
                    'expected_tracks': 4,
                },
            },
            segment_identify_mock=analytics_identify_mock,
            segment_track_mock=analytics_track_mock,
        )

    @pytest.mark.freeze_time(test_date_time)
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_finished_multibooking(self, analytics_track_mock, analytics_identify_mock):
        # Create booking
        booking, *_ = create_subbooking(
            business=self.business,
            booking_kws=dict(
                source=self.source,
                type=Appointment.TYPE.BUSINESS,
                status=Appointment.STATUS.CANCELED,
            ),
        )
        pos = baker.make(POS, business=self.business)
        txn = baker.make(
            Transaction,
            created=tznow() + timedelta(days=3),
            pos=pos,
            total=100,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            appointment_id=booking.appointment_id,
        )
        prep = baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.PREPAYMENT)
        receipt = baker.make(
            Receipt,
            payment_type=prep,
            status_code=receipt_status.PREPAYMENT_SUCCESS,
            transaction=txn,
        )
        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            PaymentRow,
            amount=100,
            payment_type=prep,
            status=receipt_status.PREPAYMENT_SUCCESS,
            receipt=receipt,
        )

        #  MAKE TEST
        result = {
            6: 1,
            12: 2,  # finished here
            13: 2,
            7: 0,  # Transaction is not fully paid
        }

        for i in range(25):
            closing_timezones = get_timezones_with_hour(i)
            txs = unfinished_prepayment_query(closing_timezones)
            assert len(txs) == result.get(i, 0)

            for trn in txs:
                pr = trn.latest_receipt.payment_rows.get()
                pr.update_status(status=receipt_status.PAYMENT_SUCCESS)

        for i in range(25):
            closing_timezones = get_timezones_with_hour(i)
            txs = unfinished_prepayment_query(closing_timezones)

            assert len(txs) == 0

        assert analytics_track_mock.call_count == 10
        assert analytics_identify_mock.call_count == 5
        assert_events_triggered(
            {
                'Payment_Transaction_Completed': {
                    'expected_tracks': 5,
                    'expected_identifies': 5,
                },
                'Checkout_Transaction_Completed': {
                    'expected_tracks': 5,
                },
            },
            segment_identify_mock=analytics_identify_mock,
            segment_track_mock=analytics_track_mock,
        )

    @pytest.mark.freeze_time(test_date_time)
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_noshow_multibooking(self, analytics_track_mock, analytics_identify_mock):
        # Create booking
        booking, *_ = create_subbooking(
            business=self.business,
            booking_kws=dict(
                source=self.source,
                type=Appointment.TYPE.BUSINESS,
                status=Appointment.STATUS.NOSHOW,
            ),
        )
        pos = baker.make(POS, business=self.business)
        txn = baker.make(
            Transaction,
            created=tznow() + timedelta(days=3),
            pos=pos,
            total=100,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            appointment_id=booking.appointment_id,
        )
        prep = baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.PREPAYMENT)
        receipt = baker.make(
            Receipt,
            payment_type=prep,
            status_code=receipt_status.PREPAYMENT_SUCCESS,
            transaction=txn,
        )
        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            PaymentRow,
            amount=100,
            payment_type=prep,
            status=receipt_status.PREPAYMENT_SUCCESS,
            receipt=receipt,
        )

        #  MAKE TEST
        result = {
            6: 1,
            12: 2,  # No show here
            13: 2,
            7: 0,  # Transaction is not fully paid
        }

        for i in range(25):
            closing_timezones = get_timezones_with_hour(i)
            txs = unfinished_prepayment_query(closing_timezones)
            assert len(txs) == result.get(i, 0)

            for trn in txs:
                pr = trn.latest_receipt.payment_rows.get()
                pr.update_status(status=receipt_status.PAYMENT_SUCCESS)

        for i in range(25):
            closing_timezones = get_timezones_with_hour(i)
            txs = unfinished_prepayment_query(closing_timezones)

            assert len(txs) == 0

        assert_events_triggered(
            {
                'Payment_Transaction_Completed': {
                    'expected_tracks': 5,
                    'expected_identifies': 5,
                },
                'Checkout_Transaction_Completed': {
                    'expected_tracks': 5,
                },
            },
            segment_identify_mock=analytics_identify_mock,
            segment_track_mock=analytics_track_mock,
        )

    @pytest.mark.freeze_time(test_date_time)
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_canceled_multibooking(self, analytics_track_mock, analytics_identify_mock):
        # Create booking
        booking, *_ = create_subbooking(
            business=self.business,
            booking_kws=dict(
                source=self.source,
                type=Appointment.TYPE.BUSINESS,
                status=Appointment.STATUS.CANCELED,
            ),
        )

        pos = baker.make(POS, business=self.business)
        txn = baker.make(
            Transaction,
            created=tznow() + timedelta(days=3),
            pos=pos,
            total=100,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            appointment_id=booking.appointment_id,
        )
        prep = baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.PREPAYMENT)
        receipt = baker.make(
            Receipt,
            payment_type=prep,
            status_code=receipt_status.PREPAYMENT_SUCCESS,
            transaction=txn,
        )
        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            PaymentRow,
            amount=100,
            payment_type=prep,
            status=receipt_status.PREPAYMENT_SUCCESS,
            receipt=receipt,
        )

        #  MAKE TEST
        result = {
            6: 1,
            12: 2,  # No show here
            13: 2,
            7: 0,  # Transaction is not fully paid
        }

        for i in range(25):
            closing_timezones = get_timezones_with_hour(i)
            txs = unfinished_prepayment_query(closing_timezones)
            assert len(txs) == result.get(i, 0)

            for trn in txs:
                pr = trn.latest_receipt.payment_rows.get()
                pr.update_status(status=receipt_status.PAYMENT_SUCCESS)

        for i in range(25):
            closing_timezones = get_timezones_with_hour(i)
            txs = unfinished_prepayment_query(closing_timezones)

            assert len(txs) == 0

        assert_events_triggered(
            {
                'Payment_Transaction_Completed': {
                    'expected_tracks': 5,
                    'expected_identifies': 5,
                },
                'Checkout_Transaction_Completed': {
                    'expected_tracks': 5,
                },
            },
            segment_identify_mock=analytics_identify_mock,
            segment_track_mock=analytics_track_mock,
        )

    @pytest.mark.freeze_time(test_date_time)
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_modified_multibooking(self, analytics_track_mock, analytics_identify_mock):

        appointment = create_appointment(
            [{}, {}],
            business=self.business,
            source=self.source,
            type=Appointment.TYPE.BUSINESS,
            status=Appointment.STATUS.ACCEPTED,
        )

        pos = baker.make(POS, business=self.business)
        txn = baker.make(
            Transaction,
            created=tznow() + timedelta(days=3),
            pos=pos,
            total=100,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            appointment_id=appointment.id,
        )
        prep = baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.PREPAYMENT)
        receipt = baker.make(
            Receipt,
            payment_type=prep,
            status_code=receipt_status.PREPAYMENT_SUCCESS,
            transaction=txn,
        )
        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            PaymentRow,
            amount=50,
            payment_type=prep,
            status=receipt_status.PREPAYMENT_SUCCESS,
            receipt=receipt,
        )

        #  MAKE TEST
        result = {
            6: 1,
            12: 1,
            13: 2,
            7: 0,  # Transaction is not fully paid
        }

        for i in range(25):
            closing_timezones = get_timezones_with_hour(i)
            txs = unfinished_prepayment_query(closing_timezones)
            assert len(txs) == result.get(i, 0)

            for trn in txs:
                pr = trn.latest_receipt.payment_rows.get()
                pr.update_status(status=receipt_status.PAYMENT_SUCCESS)

        for i in range(25):
            closing_timezones = get_timezones_with_hour(i)
            txs = unfinished_prepayment_query(closing_timezones)

            assert len(txs) == 0

        assert_events_triggered(
            {
                'Payment_Transaction_Completed': {
                    'expected_tracks': 4,
                    'expected_identifies': 4,
                },
                'Checkout_Transaction_Completed': {
                    'expected_tracks': 4,
                },
            },
            segment_identify_mock=analytics_identify_mock,
            segment_track_mock=analytics_track_mock,
        )

    @pytest.mark.freeze_time(test_date_time)
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_modified2_multibooking(self, analytics_track_mock, analytics_identify_mock):
        # Create booking

        appointment = create_appointment(
            [{}, {}],
            business=self.business,
            source=self.source,
            type=Appointment.TYPE.BUSINESS,
            status=Appointment.STATUS.FINISHED,
        )

        pos = baker.make(POS, business=self.business)
        txn = baker.make(
            Transaction,
            created=tznow() + timedelta(days=3),
            pos=pos,
            total=100,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            appointment_id=appointment.id,
        )
        prep = baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.PREPAYMENT)
        receipt = baker.make(
            Receipt,
            payment_type=prep,
            status_code=receipt_status.PREPAYMENT_SUCCESS,
            transaction=txn,
        )
        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            PaymentRow,
            amount=100,
            payment_type=prep,
            status=receipt_status.PREPAYMENT_SUCCESS,
            receipt=receipt,
        )

        #  MAKE TEST
        result = {
            6: 1,
            12: 2,  # Modified here
            13: 2,
            7: 0,  # Transaction is not fully paid
        }

        for i in range(25):
            closing_timezones = get_timezones_with_hour(i)
            txs = unfinished_prepayment_query(closing_timezones)
            assert len(txs) == result.get(i, 0)

            for trn in txs:
                pr = trn.latest_receipt.payment_rows.get()
                pr.update_status(status=receipt_status.PAYMENT_SUCCESS)
        for i in range(25):
            closing_timezones = get_timezones_with_hour(i)
            txs = unfinished_prepayment_query(closing_timezones)

            assert len(txs) == 0

    @pytest.mark.freeze_time(test_date_time)
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_future_multibooking(self, analytics_track_mock, analytics_identify_mock):
        # Create booking
        booking, *_ = create_subbooking(
            business=self.business,
            booking_kws=dict(
                source=self.source,
                type=Appointment.TYPE.BUSINESS,
                status=Appointment.STATUS.ACCEPTED,
            ),
        )

        pos = baker.make(POS, business=self.business)
        txn = baker.make(
            Transaction,
            created=tznow() + timedelta(days=3),
            pos=pos,
            total=100,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            appointment_id=booking.appointment_id,
        )
        prep = baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.PREPAYMENT)
        receipt = baker.make(
            Receipt,
            payment_type=prep,
            status_code=receipt_status.PREPAYMENT_SUCCESS,
            transaction=txn,
        )
        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            PaymentRow,
            amount=100,
            payment_type=prep,
            status=receipt_status.PREPAYMENT_SUCCESS,
            receipt=receipt,
        )

        #  MAKE TEST
        result = {
            6: 1,
            12: 1,
            13: 2,
            7: 0,  # Transaction is not fully paid
        }

        for i in range(25):
            closing_timezones = get_timezones_with_hour(i)
            txs = unfinished_prepayment_query(closing_timezones)
            assert len(txs) == result.get(i, 0)

            for trn in txs:
                pr = trn.latest_receipt.payment_rows.get()
                pr.update_status(status=receipt_status.PAYMENT_SUCCESS)

        for i in range(25):
            closing_timezones = get_timezones_with_hour(i)
            txs = unfinished_prepayment_query(closing_timezones)

            assert len(txs) == 0
        assert_events_triggered(
            {
                'Payment_Transaction_Completed': {
                    'expected_tracks': 4,
                    'expected_identifies': 4,
                },
                'Checkout_Transaction_Completed': {
                    'expected_tracks': 4,
                },
            },
            segment_identify_mock=analytics_identify_mock,
            segment_track_mock=analytics_track_mock,
        )

    @pytest.mark.freeze_time(test_date_time)
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_parent_transaction(self, analytics_track_mock, analytics_identify_mock):
        booking, *_ = create_subbooking(
            business=self.business,
            booking_kws=dict(
                source=self.source,
                type=Appointment.TYPE.BUSINESS,
                status=Appointment.STATUS.FINISHED,
            ),
        )

        pos = baker.make(POS, business=self.business)
        txn = baker.make(
            Transaction,
            pos=pos,
            total=100,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            appointment_id=booking.appointment_id,
        )
        prep = baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.PREPAYMENT)
        receipt = baker.make(
            Receipt,
            payment_type=prep,
            status_code=receipt_status.PREPAYMENT_SUCCESS,
            transaction=txn,
        )
        txn.latest_receipt = receipt
        txn.save()

        txn2 = baker.make(
            Transaction,
            pos=pos,
            total=100,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            parent_txn=txn,
            appointment_id=booking.appointment_id,
        )
        receipt2 = baker.make(
            Receipt,
            payment_type=prep,
            status_code=receipt_status.PREPAYMENT_SUCCESS,
            transaction=txn2,
        )
        txn2.latest_receipt = receipt2
        txn2.save()

        baker.make(
            PaymentRow,
            amount=100,
            payment_type=prep,
            status=receipt_status.PREPAYMENT_SUCCESS,
            receipt=receipt2,
        )

        #  MAKE TEST
        result = {
            6: 1,
            12: 2,
            13: 2,
        }

        for i in range(25):
            closing_timezones = get_timezones_with_hour(i)
            txs = unfinished_prepayment_query(closing_timezones)
            assert len(txs) == result.get(i, 0)

            for trn in txs:
                pr = trn.latest_receipt.payment_rows.get()
                pr.update_status(status=receipt_status.PAYMENT_SUCCESS)

        for i in range(25):
            closing_timezones = get_timezones_with_hour(i)
            txs = unfinished_prepayment_query(closing_timezones)

            assert len(txs) == 0

        assert_events_triggered(
            {
                'Payment_Transaction_Completed': {
                    'expected_tracks': 5,
                    'expected_identifies': 5,
                },
                'Checkout_Transaction_Completed': {
                    'expected_tracks': 5,
                },
            },
            segment_identify_mock=analytics_identify_mock,
            segment_track_mock=analytics_track_mock,
        )
