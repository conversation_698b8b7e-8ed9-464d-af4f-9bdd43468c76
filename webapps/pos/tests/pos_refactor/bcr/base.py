# pylint: disable=duplicate-code
import mock
from model_bakery import baker

from lib.payment_gateway.enums import (
    BalanceTransactionStatus,
    BalanceTransactionType,
    PaymentStatus,
)
from lib.payment_providers.enums import (
    PaymentOperationStatus,
    PaymentOperationType,
)
from lib.payments.enums import (
    PaymentError,
    PaymentProviderCode,
)
from lib.point_of_sale.enums import (
    BasketPaymentAnalyticsTrigger,
    BasketPaymentStatus,
    BasketPaymentType,
    BasketTipType,
)
from lib.point_of_sale.enums import PaymentMethodType as PointOfSalePaymentMethodType
from lib.tools import (
    major_unit,
    minor_unit,
)
from webapps.payment_gateway.models import (
    BalanceTransaction,
    Wallet,
)
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_providers.consts.stripe import (
    PaymentIntentStripeStatus,
    RefundStripeStatus,
)
from webapps.payment_providers.models import (
    AccountHolder,
    Payment,
    PaymentOperation,
    StripeAccountHolder,
)
from webapps.payment_providers.ports.customer_ports import PaymentProvidersCustomerPort
from webapps.point_of_sale.models import (
    Basket,
    BasketPayment,
    BasketPaymentAnalytics,
    BasketTip,
)
from webapps.pos.enums import (
    PaymentTypeEnum,
    POSPlanPaymentTypeEnum,
    receipt_status,
)
from webapps.pos.models import (
    PaymentRow,
    PaymentType,
    POSPlan,
)
from webapps.pos.tests.pos_refactor import TestTransactionSerializerBase
from webapps.pos.tip_calculations import SimpleTip
from webapps.stripe_integration.models import StripeAccount


class TestTransactionSerializerStripeBase(TestTransactionSerializerBase):
    def setUp(self):
        super().setUp()
        self.stripe = baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.STRIPE_TERMINAL)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.SPLIT)
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.TAP_TO_PAY)

        self.provider_code = PaymentProviderCode.STRIPE
        self.stripe_plan = baker.make(
            POSPlan,
            min_txn_num=0,
            provision=0,
            txn_fee=0,
            individual=False,
            plan_type=POSPlanPaymentTypeEnum.STRIPE_TERMINAL,
        )
        self.tap_to_pay_plan = baker.make(
            POSPlan,
            min_txn_num=0,
            provision=0,
            txn_fee=0,
            individual=False,
            plan_type=POSPlanPaymentTypeEnum.TAP_TO_PAY,
        )
        self.booksy_pay_plan = baker.make(
            POSPlan,
            min_txn_num=0,
            provision=0,
            txn_fee=0,
            individual=False,
            plan_type=POSPlanPaymentTypeEnum.BOOKSY_PAY,
        )
        self.pos.pos_plans.add(self.stripe_plan)
        self.pos.pos_plans.add(self.tap_to_pay_plan)
        self.pos.pos_plans.add(self.booksy_pay_plan)
        baker.make(
            StripeAccount,
            pos=self.pos,
            kyc_verified_at_least_once=True,
            external_id='123',
        )

        with mock.patch(
            'webapps.payment_providers.providers.stripe.stripe.Customer.create'
        ) as stripe_mock:
            stripe_mock.return_value = mock.MagicMock(id='customer_external_id')
            PaymentProvidersCustomerPort.create_provider_customer(
                customer_id=self.customer_wallet.customer_id,
                payment_provider_code=PaymentProviderCode.STRIPE,
            )

        booksy_wallet = PaymentGatewayPort.get_booksy_wallet()
        booksy_account_holder = baker.make(
            AccountHolder,
        )
        baker.make(
            StripeAccountHolder,
            account_holder=booksy_account_holder,
        )
        wallet = Wallet.objects.get(id=booksy_wallet.id)
        wallet.account_holder_id = booksy_account_holder.id
        wallet.save()

    @staticmethod
    def _get_trigger(payment_method: PointOfSalePaymentMethodType):

        if payment_method == PointOfSalePaymentMethodType.TAP_TO_PAY:
            return BasketPaymentAnalyticsTrigger.BUSINESS__TAP_TO_PAY
        if payment_method == PointOfSalePaymentMethodType.KEYED_IN_PAYMENT:
            return BasketPaymentAnalyticsTrigger.BUSINESS__KEYED_IN_PAYMENT
        return BasketPaymentAnalyticsTrigger.BUSINESS__BOOKSY_CARD_READER_CFP

    def check_payment(
        self,
        payment_row: PaymentRow,
        payment_status: PaymentStatus,
        is_payment_intent_created: bool = True,
        payment_intent_status=None,
    ):
        basket_payment = BasketPayment.objects.filter(id=payment_row.basket_payment_id).last()
        balance_transaction = BalanceTransaction.objects.get(
            id=basket_payment.balance_transaction_id,
        )
        payment = Payment.objects.get(id=balance_transaction.external_id)
        self.assertEqual(payment.status, payment_status)
        self.assertEqual(hasattr(payment, 'stripe_payment'), is_payment_intent_created)
        if payment_intent_status:
            self.assertEqual(payment.stripe_payment.stripe_status, payment_intent_status)
        return payment

    def check_payment_operation(
        self,
        payment_row: PaymentRow,
        payment_operation_type: PaymentOperationType,
        payment_operation_status: PaymentOperationStatus,
        stripe_payment_operation_status: RefundStripeStatus,
    ):
        basket_payment = BasketPayment.objects.filter(id=payment_row.basket_payment_id).last()
        balance_transaction = BalanceTransaction.objects.get(
            id=basket_payment.balance_transaction_id,
        )
        payment_operation = PaymentOperation.objects.get(id=balance_transaction.external_id)
        self.assertEqual(payment_operation.status, payment_operation_status)
        if payment_operation_type == PaymentOperationType.REFUND:
            stripe_refund = payment_operation.stripe_refund
            self.assertEqual(stripe_refund.stripe_status, stripe_payment_operation_status)

    def _check_stripe_pending(self, txn):
        txn.refresh_from_db()
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PENDING)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.TERMINAL,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.PENDING,
            balance_transaction_related_obj_status=PaymentStatus.NEW,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        self.check_payment(
            payment_row=txn.payment_rows[0],
            payment_status=PaymentStatus.NEW,
            is_payment_intent_created=False,
        )

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__BOOKSY_CARD_READER_CFP,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_stripe_pending_with_intent(
        self,
        txn,
        payment_method: PointOfSalePaymentMethodType = PointOfSalePaymentMethodType.TERMINAL,
    ):
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PENDING)
        payment_row = txn.payment_rows[0]
        self.check_basket_payment(
            basket=basket,
            payment_row=payment_row,
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.PENDING,
            balance_transaction_related_obj_status=PaymentStatus.NEW,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        # check synchronization of PaymentIntent object
        basket_payment = BasketPayment.objects.filter(id=payment_row.basket_payment_id).last()
        balance_transaction = BalanceTransaction.objects.get(
            id=basket_payment.balance_transaction_id,
        )
        payment = Payment.objects.get(id=balance_transaction.external_id)
        self.assertEqual(payment.provider_code, PaymentProviderCode.STRIPE)
        self.assertEqual(PaymentRow.objects.count(), 1)
        self.assertEqual(payment.stripe_payment.external_id, payment_row.intents.last().external_id)
        self.assertEqual(
            payment.stripe_payment.stripe_status,
            PaymentIntentStripeStatus.REQUIRES_PAYMENT_METHOD,
        )
        assert (
            payment_row.payment_splits
        )  # should be filled during sync (required for send_for_refund)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=self._get_trigger(payment_method),
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_stripe_success(
        self,
        txn,
        payment_method: PointOfSalePaymentMethodType = PointOfSalePaymentMethodType.TERMINAL,
    ):
        txn.refresh_from_db()

        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        self.check_payment(
            payment_row=txn.payment_rows[0],
            payment_status=PaymentStatus.CAPTURED,
            payment_intent_status=PaymentIntentStripeStatus.SUCCEEDED,
        )

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=self._get_trigger(payment_method),
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_stripe_success_with_tip(
        self,
        txn,
        payment_method: PointOfSalePaymentMethodType = PointOfSalePaymentMethodType.TERMINAL,
    ):
        txn.refresh_from_db()
        self.assertEqual(self.staffer.staff_user_id, txn.operator_id)

        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        payment = self.check_payment(
            payment_row=txn.payment_rows[0],
            payment_status=PaymentStatus.CAPTURED,
            payment_intent_status=PaymentIntentStripeStatus.SUCCEEDED,
        )
        self.assertEqual(payment.amount, 14545)
        self.assertEqual(payment.amount, minor_unit(txn.payment_rows[0].amount))

        # BasketTip
        basket_tip = BasketTip.objects.filter(basket=basket)
        self.assertEqual(len(basket_tip), 1)
        self.assertEqual(BasketTip.all_objects.filter(basket=basket).count(), 1)

        self.assertEqual(basket_tip[0].rate, None)
        self.assertEqual(basket_tip[0].amount, minor_unit(22))
        self.assertEqual(basket_tip[0].type, BasketTipType.AMOUNT)
        self.assertEqual(basket_tip[0].staffer_id, self.staffer.id)

        self.assertEqual(minor_unit(txn.tip.amount), basket_tip[0].amount)
        self.assertEqual(txn.tip.type, SimpleTip.TIP_TYPE__HAND)
        self.assertEqual(txn.tip.rate, major_unit(basket_tip[0].amount))
        self.assertEqual(txn.tip.basket_tip_id, basket_tip[0].id)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=self._get_trigger(payment_method),
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_stripe_success_with_fees(
        self,
        txn,
        payment_method: PointOfSalePaymentMethodType = PointOfSalePaymentMethodType.TERMINAL,
    ):
        txn.refresh_from_db()

        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            fee_amount=minor_unit(39.52),
        )
        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        self.check_payment(
            payment_row=txn.payment_rows[0],
            payment_status=PaymentStatus.CAPTURED,
            payment_intent_status=PaymentIntentStripeStatus.SUCCEEDED,
        )

    def _check_stripe_cancel(
        self,
        txn,
        payment_method: PointOfSalePaymentMethodType = PointOfSalePaymentMethodType.TERMINAL,
    ):
        txn.refresh_from_db()

        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_CANCELED)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.CANCELED,
            balance_transaction_related_obj_status=PaymentStatus.CANCELED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        self.check_payment(
            payment_row=txn.payment_rows[0],
            payment_status=PaymentStatus.CANCELED,
            payment_intent_status=PaymentIntentStripeStatus.CANCELED,
        )

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=self._get_trigger(payment_method),
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_stripe_cancel_retry_cash(
        self,
        txn,
        txn2,
        payment_method: PointOfSalePaymentMethodType = PointOfSalePaymentMethodType.TERMINAL,
    ):
        txn.refresh_from_db()
        txn2.refresh_from_db()

        self.assertEqual(txn.basket_id, txn2.basket_id)
        self.assertEqual(txn.receipts.all().count(), 2)
        self.assertEqual(txn2.receipts.all().count(), 1)

        basket = Basket.objects.get(id=txn2.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_CANCELED)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.CANCELED,
            balance_transaction_related_obj_status=PaymentStatus.CANCELED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.assertEqual(len(txn2.payment_rows), 1)
        self.assertEqual(txn2.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)

        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CASH,
            provider_code=None,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=self._get_trigger(payment_method),
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__OFFLINE_PAYMENT,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 2)

    def _check_stripe_cancel_retry_stripe(
        self,
        txn,
        txn2,
        payment_method: PointOfSalePaymentMethodType = PointOfSalePaymentMethodType.TERMINAL,
    ):
        txn.refresh_from_db()
        txn2.refresh_from_db()

        self.assertEqual(txn.basket_id, txn2.basket_id)
        self.assertEqual(txn.receipts.all().count(), 2)
        self.assertEqual(txn2.receipts.all().count(), 1)

        basket = Basket.objects.get(id=txn2.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_CANCELED)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.CANCELED,
            balance_transaction_related_obj_status=PaymentStatus.CANCELED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.assertEqual(len(txn2.payment_rows), 1)
        self.assertEqual(txn2.payment_rows[0].status, receipt_status.PENDING)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[0],
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.PENDING,
            balance_transaction_related_obj_status=PaymentStatus.NEW,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)
        self.assertEqual(BalanceTransaction.objects.count(), 2)

        self.check_payment(
            payment_row=txn.payment_rows[0],
            payment_status=PaymentStatus.CANCELED,
            payment_intent_status=PaymentIntentStripeStatus.CANCELED,
        )
        self.check_payment(
            payment_row=txn2.payment_rows[0],
            payment_status=PaymentStatus.NEW,
            is_payment_intent_created=True,
        )

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=self._get_trigger(payment_method),
            ).count(),
            2,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 2)

    def _check_stripe_cancel_retry_stripe_with_intent(
        self,
        txn,
        txn2,
        payment_method: PointOfSalePaymentMethodType = PointOfSalePaymentMethodType.TERMINAL,
    ):
        self.assertEqual(txn.basket_id, txn2.basket_id)
        self.assertEqual(txn.receipts.all().count(), 2)
        self.assertEqual(txn2.receipts.all().count(), 1)

        basket = Basket.objects.get(id=txn2.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_CANCELED)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.CANCELED,
            balance_transaction_related_obj_status=PaymentStatus.CANCELED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.assertEqual(len(txn2.payment_rows), 1)
        self.assertEqual(txn2.payment_rows[0].status, receipt_status.PENDING)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[0],
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.PENDING,
            balance_transaction_related_obj_status=PaymentStatus.NEW,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)
        self.assertEqual(BalanceTransaction.objects.count(), 2)

        self.check_payment(
            payment_row=txn.payment_rows[0],
            payment_status=PaymentStatus.CANCELED,
            payment_intent_status=PaymentIntentStripeStatus.CANCELED,
        )
        self.check_payment(
            payment_row=txn2.payment_rows[0],
            payment_status=PaymentStatus.NEW,
            payment_intent_status=PaymentIntentStripeStatus.REQUIRES_PAYMENT_METHOD,
        )

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=self._get_trigger(payment_method),
            ).count(),
            2,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 2)

    def _check_stripe_cancel_retry_stripe_success(
        self,
        txn,
        txn2,
        payment_method: PointOfSalePaymentMethodType = PointOfSalePaymentMethodType.TERMINAL,
    ):
        txn.refresh_from_db()
        txn2.refresh_from_db()

        self.assertEqual(txn.basket_id, txn2.basket_id)
        self.assertEqual(txn.receipts.all().count(), 2)
        self.assertEqual(txn2.receipts.all().count(), 2)

        basket = Basket.objects.get(id=txn2.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_CANCELED)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.CANCELED,
            balance_transaction_related_obj_status=PaymentStatus.CANCELED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.assertEqual(len(txn2.payment_rows), 1)
        self.assertEqual(txn2.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn2.payment_rows[0],
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)
        self.assertEqual(BalanceTransaction.objects.count(), 2)

        self.check_payment(
            payment_row=txn.payment_rows[0],
            payment_status=PaymentStatus.CANCELED,
            payment_intent_status=PaymentIntentStripeStatus.CANCELED,
        )
        self.check_payment(
            payment_row=txn2.payment_rows[0],
            payment_status=PaymentStatus.CAPTURED,
            payment_intent_status=PaymentIntentStripeStatus.SUCCEEDED,
        )

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=self._get_trigger(payment_method),
            ).count(),
            2,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 2)

    def _check_stripe_fail(
        self,
        txn,
        payment_intent_status=PaymentIntentStripeStatus.REQUIRES_PAYMENT_METHOD,
        payment_method: PointOfSalePaymentMethodType = PointOfSalePaymentMethodType.TERMINAL,
    ):
        txn.refresh_from_db()

        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_FAILED)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.FAILED,
            balance_transaction_related_obj_status=PaymentStatus.AUTHORIZATION_FAILED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            error_code=PaymentError.RESTRICTED_CARD,
        )
        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        self.check_payment(
            payment_row=txn.payment_rows[0],
            payment_status=PaymentStatus.AUTHORIZATION_FAILED,
            payment_intent_status=payment_intent_status,
        )

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=self._get_trigger(payment_method),
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_stripe_fail_cancel(
        self,
        txn,
        payment_method: PointOfSalePaymentMethodType = PointOfSalePaymentMethodType.TERMINAL,
    ):
        txn.refresh_from_db()

        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_CANCELED)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.FAILED,
            balance_transaction_related_obj_status=PaymentStatus.AUTHORIZATION_FAILED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            error_code=PaymentError.RESTRICTED_CARD,
        )
        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 1)
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        self.check_payment(
            payment_row=txn.payment_rows[0],
            payment_status=PaymentStatus.AUTHORIZATION_FAILED,
            payment_intent_status=PaymentIntentStripeStatus.REQUIRES_PAYMENT_METHOD,
        )

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=self._get_trigger(payment_method),
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_stripe_send_for_refund(
        self,
        txn,
        success_row,
        payment_method: PointOfSalePaymentMethodType = PointOfSalePaymentMethodType.TERMINAL,
    ):
        txn.refresh_from_db()

        # test pyment_providers objects
        basket_payment = BasketPayment.objects.filter(
            id=txn.payment_rows[0].basket_payment_id
        ).last()
        balance_transaction = BalanceTransaction.objects.get(
            id=basket_payment.balance_transaction_id,
        )
        payment_operation = PaymentOperation.objects.get(id=balance_transaction.external_id)
        self.assertEqual(payment_operation.status, PaymentOperationStatus.PROCESSING)
        stripe_refund = payment_operation.stripe_refund
        self.assertEqual(stripe_refund.stripe_status, RefundStripeStatus.PENDING)

        # Basket
        self.assertEqual(txn.receipts.all().count(), 3)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(success_row.status, receipt_status.PAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=success_row,
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.SENT_FOR_REFUND)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.PENDING,
            basket_payment_type=BasketPaymentType.REFUND,
            balance_transaction_existing=True,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)
        self.assertEqual(BalanceTransaction.objects.count(), 2)

        # checking payment for success_row cuz refund row has not Payment object
        self.check_payment(
            payment_row=success_row,
            payment_status=PaymentStatus.CAPTURED,
            payment_intent_status=PaymentIntentStripeStatus.SUCCEEDED,
        )
        self.check_payment_operation(
            payment_row=txn.payment_rows[0],
            payment_operation_type=PaymentOperationType.REFUND,
            payment_operation_status=PaymentOperationStatus.PROCESSING,
            stripe_payment_operation_status=RefundStripeStatus.PENDING,
        )

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=self._get_trigger(payment_method),
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_stripe_refund(
        self,
        txn,
        success_row,
        payment_method: PointOfSalePaymentMethodType = PointOfSalePaymentMethodType.TERMINAL,
    ):
        txn.refresh_from_db()

        # test payment_providers objects
        basket_payment = BasketPayment.objects.filter(
            id=txn.payment_rows[0].basket_payment_id
        ).last()
        balance_transaction = BalanceTransaction.objects.get(
            id=basket_payment.balance_transaction_id,
            transaction_type=BalanceTransactionType.REFUND,
        )
        self.assertEqual(PaymentOperation.objects.count(), 1)
        payment_operation = PaymentOperation.objects.get(id=balance_transaction.external_id)
        self.assertEqual(payment_operation.status, PaymentOperationStatus.SUCCESS)
        stripe_refund = payment_operation.stripe_refund
        self.assertEqual(stripe_refund.stripe_status, RefundStripeStatus.SUCCEEDED)
        self.assertEqual(balance_transaction.status, BalanceTransactionStatus.SUCCESS)

        # Basket
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        self.assertEqual(
            set(basket.payments.all().values_list('type', flat=True)),
            {BasketPaymentType.PAYMENT, BasketPaymentType.REFUND},
        )
        self.assertEqual(
            set(basket.payments.all().values_list('status', flat=True)),
            {BasketPaymentStatus.SUCCESS},
        )

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(success_row.status, receipt_status.PAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=success_row,
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(
            set(txn.receipts.all().values_list('status_code', flat=True)),
            {
                receipt_status.PENDING,
                receipt_status.PAYMENT_SUCCESS,
                receipt_status.SENT_FOR_REFUND,
                receipt_status.REFUNDED,
            },
        )
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.REFUNDED)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.REFUND,
            balance_transaction_existing=True,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)
        self._check_refund_balance_transactions()

        # checking payment for success_row cuz refund row has not Payment object
        self.check_payment(
            payment_row=success_row,
            payment_status=PaymentStatus.CAPTURED,
            payment_intent_status=PaymentIntentStripeStatus.SUCCEEDED,
        )
        self.check_payment_operation(
            payment_row=txn.payment_rows[0],
            payment_operation_type=PaymentOperationType.REFUND,
            payment_operation_status=PaymentOperationStatus.SUCCESS,
            stripe_payment_operation_status=RefundStripeStatus.SUCCEEDED,
        )

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=self._get_trigger(payment_method),
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

        all_payment_rows_ids = []
        for receipt in txn.receipts.all():
            all_payment_rows_ids.extend(receipt.payment_rows.values_list('id', flat=True))

        self.assertEqual(
            set(txn.payment_rows.last().intents.last().payment_rows.values_list('id', flat=True)),
            set(all_payment_rows_ids),
        )

    def _check_stripe_refund_with_fees(
        self,
        txn,
        success_row,
        payment_method: PointOfSalePaymentMethodType = PointOfSalePaymentMethodType.TERMINAL,
    ):
        txn.refresh_from_db()

        # test pyment_providers objects
        basket_payment = BasketPayment.objects.filter(
            id=txn.payment_rows[0].basket_payment_id
        ).last()
        balance_transaction = BalanceTransaction.objects.get(
            id=basket_payment.balance_transaction_id,
        )
        payment_operation = PaymentOperation.objects.get(id=balance_transaction.external_id)
        self.assertEqual(payment_operation.status, PaymentOperationStatus.SUCCESS)
        stripe_refund = payment_operation.stripe_refund
        self.assertEqual(stripe_refund.stripe_status, RefundStripeStatus.SUCCEEDED)

        # Basket
        self.assertEqual(txn.receipts.all().count(), 4)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(success_row.status, receipt_status.PAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=success_row,
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            fee_amount=minor_unit(39.52),
        )

        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.REFUNDED)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.REFUND,
            balance_transaction_existing=True,
            fee_amount=minor_unit(34.35),
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)
        self._check_refund_balance_transactions()

        # checking payment for success_row cuz refund row has not Payment object
        self.check_payment(
            payment_row=success_row,
            payment_status=PaymentStatus.CAPTURED,
            payment_intent_status=PaymentIntentStripeStatus.SUCCEEDED,
        )
        self.check_payment_operation(
            payment_row=txn.payment_rows[0],
            payment_operation_type=PaymentOperationType.REFUND,
            payment_operation_status=PaymentOperationStatus.SUCCESS,
            stripe_payment_operation_status=RefundStripeStatus.SUCCEEDED,
        )

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=self._get_trigger(payment_method),
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_stripe_refund_fail(
        self,
        txn,
        success_row,
        payment_method: PointOfSalePaymentMethodType = PointOfSalePaymentMethodType.TERMINAL,
    ):
        txn.refresh_from_db()

        # Basket
        self.assertEqual(txn.receipts.all().count(), 4)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(success_row.status, receipt_status.PAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=success_row,
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.FAILED,
            basket_payment_type=BasketPaymentType.REFUND,
            balance_transaction_existing=True,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)
        self._check_refund_balance_transactions()

        # checking payment for success_row cuz refund row has not Payment object
        self.check_payment(
            payment_row=success_row,
            payment_status=PaymentStatus.CAPTURED,
            payment_intent_status=PaymentIntentStripeStatus.SUCCEEDED,
        )
        self.check_payment_operation(
            payment_row=txn.payment_rows[0],
            payment_operation_type=PaymentOperationType.REFUND,
            payment_operation_status=PaymentOperationStatus.FAILED,
            stripe_payment_operation_status=RefundStripeStatus.FAILED,
        )

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=self._get_trigger(payment_method),
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_stripe_chargeback(
        self,
        txn,
        success_row,
        payment_method: PointOfSalePaymentMethodType = PointOfSalePaymentMethodType.TERMINAL,
    ):
        txn.refresh_from_db()
        self.assertEqual(txn.receipts.all().count(), 3)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(success_row.status, receipt_status.PAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=success_row,
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        # Chargeback row
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.CHARGEBACK)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.CHARGEBACK,
            balance_transaction_existing=True,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)

        self._check_chargeback_balance_transactions()
        # checking payment for success_row cuz chargeback row has not Payment object
        self.check_payment(
            payment_row=success_row,
            payment_status=PaymentStatus.CAPTURED,
            payment_intent_status=PaymentIntentStripeStatus.SUCCEEDED,
        )
        self.check_payment_operation(
            payment_row=txn.payment_rows[0],
            payment_operation_type=PaymentOperationType.CHARGEBACK,
            payment_operation_status=PaymentOperationStatus.SUCCESS,
            stripe_payment_operation_status=RefundStripeStatus.SUCCEEDED,
        )

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=self._get_trigger(payment_method),
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_stripe_chargeback_with_fee(
        self,
        txn,
        success_row,
        payment_method: PointOfSalePaymentMethodType = PointOfSalePaymentMethodType.TERMINAL,
    ):
        txn.refresh_from_db()
        self.assertEqual(txn.receipts.all().count(), 3)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(success_row.status, receipt_status.PAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=success_row,
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
            fee_amount=minor_unit(39.52),
        )

        # Chargeback row
        self.assertEqual(len(txn.payment_rows), 1)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.CHARGEBACK)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.CHARGEBACK,
            balance_transaction_existing=True,
            fee_amount=minor_unit(34.35),
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)

        self._check_chargeback_balance_transactions()
        # checking payment for success_row cuz chargeback row has not Payment object
        self.check_payment(
            payment_row=success_row,
            payment_status=PaymentStatus.CAPTURED,
            payment_intent_status=PaymentIntentStripeStatus.SUCCEEDED,
        )
        self.check_payment_operation(
            payment_row=txn.payment_rows[0],
            payment_operation_type=PaymentOperationType.CHARGEBACK,
            payment_operation_status=PaymentOperationStatus.SUCCESS,
            stripe_payment_operation_status=RefundStripeStatus.SUCCEEDED,
        )

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=self._get_trigger(payment_method),
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 1)

    def _check_split_cash_stripe_pending(
        self,
        txn,
        payment_method: PointOfSalePaymentMethodType = PointOfSalePaymentMethodType.TERMINAL,
    ):
        txn.refresh_from_db()

        self.assertEqual(txn.receipts.all().count(), 1)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 2)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn.payment_rows[1].status, receipt_status.PENDING)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CASH,
            provider_code=None,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[1],
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.PENDING,
            balance_transaction_related_obj_status=PaymentStatus.NEW,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)
        self.assertEqual(BalanceTransaction.objects.count(), 1)

        with self.assertRaises(BalanceTransaction.DoesNotExist):
            self.check_payment(
                payment_row=txn.payment_rows[0],
                payment_status=PaymentStatus.NEW,
                payment_intent_status=PaymentIntentStripeStatus.REQUIRES_PAYMENT_METHOD,
            )
        self.check_payment(
            payment_row=txn.payment_rows[1],
            payment_status=PaymentStatus.NEW,
            payment_intent_status=PaymentIntentStripeStatus.REQUIRES_PAYMENT_METHOD,
        )

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=self._get_trigger(payment_method),
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__OFFLINE_PAYMENT,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 2)

    def _check_split_cash_stripe_success(
        self,
        txn,
        payment_method: PointOfSalePaymentMethodType = PointOfSalePaymentMethodType.TERMINAL,
    ):
        txn.refresh_from_db()

        self.assertEqual(txn.receipts.all().count(), 2)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 2)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn.payment_rows[1].status, receipt_status.PAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CASH,
            provider_code=None,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[1],
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 2)

        with self.assertRaises(BalanceTransaction.DoesNotExist):
            self.check_payment(
                payment_row=txn.payment_rows[0],
                payment_status=PaymentStatus.CAPTURED,
                payment_intent_status=PaymentIntentStripeStatus.SUCCEEDED,
            )
        self.check_payment(
            payment_row=txn.payment_rows[1],
            payment_status=PaymentStatus.CAPTURED,
            payment_intent_status=PaymentIntentStripeStatus.SUCCEEDED,
        )

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=self._get_trigger(payment_method),
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__OFFLINE_PAYMENT,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 2)

    def _check_split_cash_stripe_send_for_refund(
        self,
        txn,
        success_row,
        payment_method: PointOfSalePaymentMethodType = PointOfSalePaymentMethodType.TERMINAL,
    ):
        txn.refresh_from_db()

        self.assertEqual(txn.receipts.all().count(), 3)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 2)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn.payment_rows[1].status, receipt_status.SENT_FOR_REFUND)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CASH,
            provider_code=None,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=success_row,
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[1],
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.PENDING,
            basket_payment_type=BasketPaymentType.REFUND,
            balance_transaction_existing=True,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 3)
        self.assertEqual(BalanceTransaction.objects.count(), 2)

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=self._get_trigger(payment_method),
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__OFFLINE_PAYMENT,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 2)

    def _check_split_cash_stripe_refund(
        self,
        txn,
        success_row,
        payment_method: PointOfSalePaymentMethodType = PointOfSalePaymentMethodType.TERMINAL,
    ):
        txn.refresh_from_db()

        self.assertEqual(txn.receipts.all().count(), 4)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 2)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn.payment_rows[1].status, receipt_status.REFUNDED)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CASH,
            provider_code=None,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=success_row,
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[1],
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.REFUND,
            balance_transaction_existing=True,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 3)
        self._check_refund_balance_transactions()

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=self._get_trigger(payment_method),
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__OFFLINE_PAYMENT,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 2)

    def _check_split_cash_stripe_refund_fail(
        self,
        txn,
        success_row,
        payment_method: PointOfSalePaymentMethodType = PointOfSalePaymentMethodType.TERMINAL,
    ):
        txn.refresh_from_db()

        self.assertEqual(txn.receipts.all().count(), 4)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 2)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn.payment_rows[1].status, receipt_status.PAYMENT_SUCCESS)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CASH,
            provider_code=None,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=success_row,
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[1],
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.FAILED,
            basket_payment_type=BasketPaymentType.REFUND,
            balance_transaction_existing=True,
        )

        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 3)
        self._check_refund_balance_transactions()

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=self._get_trigger(payment_method),
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__OFFLINE_PAYMENT,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 2)

    def _check_split_cash_stripe_chargeback(
        self,
        txn,
        success_row,
        payment_method: PointOfSalePaymentMethodType = PointOfSalePaymentMethodType.TERMINAL,
    ):
        txn.refresh_from_db()
        self.assertEqual(txn.receipts.all().count(), 3)
        basket = Basket.objects.get(id=txn.basket_id)
        self.check_basic_basket(basket)

        # BasketItem
        self.get_and_check_basket_item(txn)

        # BasketPayment
        self.assertEqual(len(txn.payment_rows), 2)
        self.assertEqual(txn.payment_rows[0].status, receipt_status.PAYMENT_SUCCESS)
        self.assertEqual(txn.payment_rows[1].status, receipt_status.CHARGEBACK)
        self.assertEqual(BasketPayment.objects.filter(basket=basket).count(), 3)
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[0],
            payment_method=PointOfSalePaymentMethodType.CASH,
            provider_code=None,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=success_row,
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            balance_transaction_related_obj_status=PaymentStatus.CAPTURED,
            basket_payment_type=BasketPaymentType.PAYMENT,
        )
        self.check_basket_payment(
            basket=basket,
            payment_row=txn.payment_rows[1],
            payment_method=payment_method,
            provider_code=PaymentProviderCode.STRIPE,
            basket_payment_status=BasketPaymentStatus.SUCCESS,
            basket_payment_type=BasketPaymentType.CHARGEBACK,
            balance_transaction_existing=True,
        )
        self._check_chargeback_balance_transactions()

        # BasketPaymentAnalytics
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=self._get_trigger(payment_method),
            ).count(),
            1,
        )
        self.assertEqual(
            BasketPaymentAnalytics.objects.filter(
                *self.analytics_filters,
                trigger=BasketPaymentAnalyticsTrigger.BUSINESS__OFFLINE_PAYMENT,
            ).count(),
            1,
        )
        self.assertEqual(BasketPaymentAnalytics.objects.count(), 2)
