from decimal import Decimal
from unittest.mock import patch
from uuid import uuid4

import mock
import pytest
from dateutil.relativedelta import relativedelta
from django.db import connection
from django.test.utils import override_settings
from model_bakery import baker
from parameterized import parameterized
from segment.analytics import Client

from lib.enums import StrEnum
from lib.test_utils import create_subbooking
from lib.tools import tznow
from webapps.adyen.consts import oper_result
from webapps.adyen.models import Capture
from webapps.booking.models import Appointment
from webapps.business.enums import PriceType
from webapps.business.models import (
    Business,
    Service,
    ServiceVariant,
    ServiceVariantPayment,
    ServiceVariantChangelog,
)
from webapps.pos.enums import (
    POSPlanPaymentTypeEnum,
    PaymentProviderEnum,
    PaymentTypeEnum,
    receipt_status,
)
from webapps.pos.models import (
    POS,
    POSPlan,
    PaymentRow,
    PaymentType,
    Receipt,
    Transaction,
    TransactionRow,
)
from webapps.pos.provider.adyen_ee import AdyenEEPaymentProvider
from webapps.pos.provider.fake import _CARDS
from webapps.pos.tasks import (
    ChargeCancellationFeeTransactions,
    CheckoutBooksyPayTransaction,
    CheckoutPrepaidTransaction,
    DisablePrepayments,
    ReleaseAllDepositOnServicesAndBookings,
    ReleaseDepositOnPayment,
    batch_change_default_payment_method_to_pba_task,
    recalculate_pos_plans,
    cancel_cfp_task,
)
from webapps.pos.tests import TestCaseWithSetUp
from webapps.register.models import Register
from webapps.segment.utils import assert_events_triggered


@pytest.mark.django_db
class TestCheckoutPrepaidTransaction(TestCaseWithSetUp):
    def setUp(self):
        super().setUp()

        self.booking, *_ = create_subbooking(
            business=self.business,
            booking_kws={
                'source': self.booking_source,
                'type': Appointment.TYPE.BUSINESS,
                'status': Appointment.STATUS.FINISHED,
            },
        )

        self.txn = baker.make(
            Transaction,
            pos=self.pos,
            total=100,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            appointment_id=self.booking.appointment_id,
        )
        receipt = baker.make(
            Receipt,
            payment_type=self.prepayment,
            status_code=receipt_status.PREPAYMENT_SUCCESS,
            transaction=self.txn,
        )
        self.txn.latest_receipt = receipt
        self.txn.save()

        baker.make(
            PaymentRow,
            amount=100,
            payment_type=self.prepayment,
            status=receipt_status.PREPAYMENT_SUCCESS,
            receipt=receipt,
        )

    def test_txn_open_register(self):
        register = Register(
            pos=self.pos,
            is_open=True,
            opened_by=self.pos.business.owner,
            opening_cash=0,
        )
        register.save()

        CheckoutPrepaidTransaction(transaction_id=self.txn.id)

        # refresh
        txn = Transaction.objects.get(id=self.txn.id)

        assert txn.register == register
        assert txn.latest_receipt.status_code == receipt_status.PAYMENT_SUCCESS
        assert txn.register.is_open is True

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_booking_open_register(self, analytics_track_mock, analytics_identify_mock):
        register = Register(
            pos=self.pos,
            is_open=True,
            opened_by=self.pos.business.owner,
            opening_cash=0,
        )
        register.save()

        CheckoutPrepaidTransaction(
            appointment_id=self.booking.appointment_id,
            business_id=self.pos.business_id,
        )

        # refresh
        txn = Transaction.objects.get(id=self.txn.id)

        assert txn.register == register
        assert txn.latest_receipt.status_code == receipt_status.PAYMENT_SUCCESS
        assert txn.register.is_open is True
        assert_events_triggered(
            {
                'Payment_Transaction_Completed': {
                    'expected_tracks': 1,
                    'expected_identifies': 1,
                },
                'Checkout_Transaction_Completed': {
                    'expected_tracks': 1,
                },
            },
            segment_identify_mock=analytics_identify_mock,
            segment_track_mock=analytics_track_mock,
        )

    @override_settings(
        POS=True,
    )
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_txn_closed_register(self, analytics_track_mock, analytics_identify_mock):
        self.pos.registers_enabled = True
        self.pos.save()

        register = Register(
            pos=self.pos,
            is_open=False,
            opened_by=self.pos.business.owner,
            opening_cash=0,
        )
        register.save()

        CheckoutPrepaidTransaction(transaction_id=self.txn.id)

        # refresh
        txn = Transaction.objects.get(id=self.txn.id)

        txn_register = txn.register

        assert txn_register.is_open is False
        # open, transaction, close
        assert txn_register.operations.all().count() == 3
        assert txn.latest_receipt.status_code == receipt_status.PAYMENT_SUCCESS
        assert_events_triggered(
            {
                'Payment_Transaction_Completed': {
                    'expected_tracks': 1,
                    'expected_identifies': 1,
                },
                'Checkout_Transaction_Completed': {
                    'expected_tracks': 1,
                },
            },
            segment_identify_mock=analytics_identify_mock,
            segment_track_mock=analytics_track_mock,
        )

    @override_settings(
        POS=True,
    )
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_booking_closed_register(self, analytics_track_mock, analytics_identify_mock):
        self.pos.registers_enabled = True
        self.pos.save()

        register = Register(
            pos=self.pos,
            is_open=False,
            opened_by=self.pos.business.owner,
            opening_cash=0,
        )
        register.save()

        CheckoutPrepaidTransaction(
            appointment_id=self.booking.appointment_id,
            business_id=self.pos.business_id,
        )

        # refresh
        txn = Transaction.objects.get(id=self.txn.id)

        txn_register = txn.register

        assert txn_register.is_open is False
        # open, transaction, close
        assert txn_register.operations.all().count() == 3
        assert txn.latest_receipt.status_code == receipt_status.PAYMENT_SUCCESS
        assert_events_triggered(
            {
                'Payment_Transaction_Completed': {
                    'expected_tracks': 1,
                    'expected_identifies': 1,
                },
                'Checkout_Transaction_Completed': {
                    'expected_tracks': 1,
                },
            },
            segment_identify_mock=analytics_identify_mock,
            segment_track_mock=analytics_track_mock,
        )

    @override_settings(
        POS=True,
    )
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_txn_turned_off_registers(self, analytics_track_mock, analytics_identify_mock):
        self.pos.registers_enabled = False
        self.pos.save()

        register = Register(
            pos=self.pos,
            is_open=False,
            opened_by=self.pos.business.owner,
            opening_cash=0,
        )
        register.save()

        CheckoutPrepaidTransaction(transaction_id=self.txn.id)

        # refresh
        txn = Transaction.objects.get(id=self.txn.id)
        txn_register = txn.register

        assert txn_register is None
        assert txn.latest_receipt.status_code == receipt_status.PAYMENT_SUCCESS
        assert_events_triggered(
            {
                'Payment_Transaction_Completed': {
                    'expected_tracks': 1,
                    'expected_identifies': 1,
                },
                'Checkout_Transaction_Completed': {
                    'expected_tracks': 1,
                },
            },
            segment_identify_mock=analytics_identify_mock,
            segment_track_mock=analytics_track_mock,
        )

    @override_settings(
        POS=True,
    )
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_booking_turned_off_registers(self, analytics_track_mock, analytics_identify_mock):
        self.pos.registers_enabled = False
        self.pos.save()

        register = Register(
            pos=self.pos,
            is_open=False,
            opened_by=self.pos.business.owner,
            opening_cash=0,
        )
        register.save()

        CheckoutPrepaidTransaction(
            appointment_id=self.booking.appointment_id,
            business_id=self.pos.business_id,
        )

        # refresh
        txn = Transaction.objects.get(id=self.txn.id)
        txn_register = txn.register

        assert txn_register is None
        assert txn.latest_receipt.status_code == receipt_status.PAYMENT_SUCCESS
        assert_events_triggered(
            {
                'Payment_Transaction_Completed': {
                    'expected_tracks': 1,
                    'expected_identifies': 1,
                },
                'Checkout_Transaction_Completed': {
                    'expected_tracks': 1,
                },
            },
            segment_identify_mock=analytics_identify_mock,
            segment_track_mock=analytics_track_mock,
        )

    @patch('webapps.pos.provider.base.PaymentProvider.cancel_deposit', return_value=None)
    @patch('webapps.pos.tasks.ReleaseDepositOnPayment.delay')
    def test_release_deposit_on_payment_task_works(self, task, cancel_deposit):
        release_deposit_task_result = []

        def side_effect(*args, **kwargs):
            release_deposit_task_result.append(ReleaseDepositOnPayment.run(*args, **kwargs))

        task.side_effect = side_effect
        receipt = baker.make(Receipt, status_code=receipt_status.DEPOSIT_AUTHORISATION_SUCCESS)
        deposit = baker.make(
            Transaction,
            pos=self.pos,
            total=100,
            transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
            appointment_id=self.booking.appointment_id,
            latest_receipt=receipt,
        )
        baker.make(
            PaymentRow,
            receipt=receipt,
            amount=deposit.total,
            status=receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
            provider=PaymentProviderEnum.ADYEN_PROVIDER,
        )

        self.txn.deposit = deposit
        self.txn.save()
        register = Register(
            pos=self.pos,
            is_open=True,
            opened_by=self.pos.business.owner,
            opening_cash=0,
        )
        register.save()

        CheckoutPrepaidTransaction(
            appointment_id=self.booking.appointment_id,
            business_id=self.pos.business_id,
        )

        assert cancel_deposit.call_count == 1
        assert release_deposit_task_result[0][0] == 'ok'

    @parameterized.expand(
        [
            (True, False, None, receipt_status.PAYMENT_SUCCESS),
            (False, True, None, receipt_status.PAYMENT_SUCCESS),
            (True, True, 'transaction_id xor booking required', receipt_status.PREPAYMENT_SUCCESS),
            (
                False,
                False,
                'transaction_id xor booking required',
                receipt_status.PREPAYMENT_SUCCESS,
            ),
        ]
    )
    def test_transaction_id_xor_booking(
        self,
        txn_provided: bool,
        appointment_provided: bool,
        expected_error: str,
        expected_status_code: str,
    ):
        kwargs = {
            'transaction_id': self.txn.id if txn_provided else None,
            'appointment_id': self.booking.appointment_id if appointment_provided else None,
            'business_id': self.pos.business_id,
        }

        result = CheckoutPrepaidTransaction(**kwargs)
        # refresh
        txn = Transaction.objects.get(id=self.txn.id)

        if expected_error:
            assert result == expected_error

        assert txn.latest_receipt.status_code == expected_status_code

    @parameterized.expand(
        [
            (True, False, None, receipt_status.PAYMENT_SUCCESS),
            (False, True, None, receipt_status.PAYMENT_SUCCESS),
            (True, True, 'Business xor Customer_user required', receipt_status.PREPAYMENT_SUCCESS),
            (
                False,
                False,
                'Business xor Customer_user required',
                receipt_status.PREPAYMENT_SUCCESS,
            ),
        ]
    )
    def test_business_xor_customer_user(
        self,
        business_id_provided: bool,
        customer_user_id_provided: bool,
        expected_error: str,
        expected_status_code: str,
    ):
        kwargs = {
            'business_id': self.pos.business_id if business_id_provided else None,
            'customer_user_id': self.user.id if customer_user_id_provided else None,
            'appointment_id': self.booking.appointment_id,
        }

        result = CheckoutPrepaidTransaction(**kwargs)
        # refresh
        txn = Transaction.objects.get(id=self.txn.id)

        if expected_error:
            assert result == expected_error

        assert txn.latest_receipt.status_code == expected_status_code


@pytest.mark.django_db
class TestCheckoutBooksyPayTransaction(TestCaseWithSetUp):

    class _Origin(StrEnum):
        BOOKING: str = 'booking'
        TRANSACTION: str = 'transaction'

    class _RegisterState(StrEnum):
        OPEN: str = 'open'
        CLOSED: str = 'closed'
        DISABLED: str = 'disabled'

    def setUp(self):
        super().setUp()

        self.booking, *_ = create_subbooking(
            business=self.business,
            booking_kws={
                'source': self.booking_source,
                'type': Appointment.TYPE.BUSINESS,
                'status': Appointment.STATUS.FINISHED,
            },
        )

        self.txn = baker.make(
            Transaction,
            pos=self.pos,
            total=100,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            appointment_id=self.booking.appointment_id,
        )
        receipt = baker.make(
            Receipt,
            payment_type=self.booksy_pay,
            status_code=receipt_status.BOOKSY_PAY_SUCCESS,
            transaction=self.txn,
        )
        self.txn.latest_receipt = receipt
        self.txn.save()

        baker.make(
            PaymentRow,
            amount=100,
            payment_type=self.booksy_pay,
            status=receipt_status.BOOKSY_PAY_SUCCESS,
            receipt=receipt,
        )

    @parameterized.expand(
        [
            ('Txn - open register', _Origin.TRANSACTION, _RegisterState.OPEN),
            ('Booking - open register', _Origin.BOOKING, _RegisterState.OPEN),
            ('Txn - closed register', _Origin.TRANSACTION, _RegisterState.CLOSED),
            ('Booking - closed register', _Origin.BOOKING, _RegisterState.CLOSED),
            ('Txn - disabled register', _Origin.TRANSACTION, _RegisterState.DISABLED),
            ('Booking - disabled register', _Origin.BOOKING, _RegisterState.DISABLED),
        ]
    )
    @override_settings(POS=True)
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_checkout_booksy_pay_transaction(
        self,
        __,
        origin: _Origin,
        register_state: _RegisterState,
        analytics_track_mock: mock.MagicMock,
        analytics_identify_mock: mock.MagicMock,
    ):
        self.pos.registers_enabled = register_state is not self._RegisterState.DISABLED
        self.pos.save()

        register = Register(
            pos=self.pos,
            is_open=register_state is self._RegisterState.OPEN,
            opened_by=self.pos.business.owner,
            opening_cash=0,
        )
        register.save()

        if origin == self._Origin.TRANSACTION:
            CheckoutBooksyPayTransaction(transaction_id=self.txn.id)
        else:
            CheckoutBooksyPayTransaction(
                appointment_id=self.booking.appointment_id,
                business_id=self.pos.business_id,
            )

        # refresh
        txn = Transaction.objects.get(id=self.txn.id)

        if register_state is self._RegisterState.OPEN:
            assert txn.register == register
            assert txn.register.is_open is True
            # open, transaction
            assert txn.register.operations.all().count() == 2
        elif register_state is self._RegisterState.CLOSED:
            # A temporary register is created when there's no open register
            assert txn.register != register
            assert txn.register is not None
            assert txn.register.is_open is False
            # open, transaction, close
            assert txn.register.operations.all().count() == 3
        elif register_state is self._RegisterState.DISABLED:
            assert txn.register is None

        assert txn.latest_receipt.status_code == receipt_status.PAYMENT_SUCCESS
        assert_events_triggered(
            {
                'Checkout_Transaction_Completed': {
                    'expected_tracks': 1,
                },
            },
            segment_identify_mock=analytics_identify_mock,
            segment_track_mock=analytics_track_mock,
        )

    @parameterized.expand(
        [
            (True, False, None, receipt_status.PAYMENT_SUCCESS),
            (False, True, None, receipt_status.PAYMENT_SUCCESS),
            (True, True, 'transaction_id xor booking required', receipt_status.BOOKSY_PAY_SUCCESS),
            (
                False,
                False,
                'transaction_id xor booking required',
                receipt_status.BOOKSY_PAY_SUCCESS,
            ),
        ]
    )
    def test_transaction_id_xor_booking(
        self,
        txn_provided: bool,
        appointment_provided: bool,
        expected_error: str,
        expected_status_code: str,
    ):
        kwargs = {
            'transaction_id': self.txn.id if txn_provided else None,
            'appointment_id': self.booking.appointment_id if appointment_provided else None,
            'business_id': self.pos.business_id,
        }

        result = CheckoutBooksyPayTransaction(**kwargs)
        # refresh
        txn = Transaction.objects.get(id=self.txn.id)

        if expected_error:
            assert result == expected_error

        assert txn.latest_receipt.status_code == expected_status_code

    @parameterized.expand(
        [
            (True, False, None, receipt_status.PAYMENT_SUCCESS),
            (False, True, None, receipt_status.PAYMENT_SUCCESS),
            (True, True, 'Business xor Customer_user required', receipt_status.BOOKSY_PAY_SUCCESS),
            (
                False,
                False,
                'Business xor Customer_user required',
                receipt_status.BOOKSY_PAY_SUCCESS,
            ),
        ]
    )
    def test_business_xor_customer_user(
        self,
        business_id_provided: bool,
        customer_user_id_provided: bool,
        expected_error: str,
        expected_status_code: str,
    ):
        kwargs = {
            'business_id': self.pos.business_id if business_id_provided else None,
            'customer_user_id': self.user.id if customer_user_id_provided else None,
            'appointment_id': self.booking.appointment_id,
        }

        result = CheckoutBooksyPayTransaction(**kwargs)
        # refresh
        txn = Transaction.objects.get(id=self.txn.id)

        if expected_error:
            assert result == expected_error

        assert txn.latest_receipt.status_code == expected_status_code


@pytest.mark.django_db
@patch('lib.locks.AbstractLock.lock', return_value=None)
@patch('lib.locks.AbstractLock.unlock', return_value=None)
class TestDisablePayments(TestCaseWithSetUp):
    def setUp(self):
        super().setUp()
        self.service = baker.make(
            Service,
            business=self.business,
            active=True,
        )

    def test_prepayments(self, *_):
        """Check if prepayment are disabled."""
        with_prepayment = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )

        baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=9.99,
        )

        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=27.21,
        )

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=7.21,
        )

        DisablePrepayments(self.business.id)

        assert len(self.business.services.all()) == 1

        service = self.business.services.get()
        assert len(service.service_variants.filter(active=True)) == 2

        assert (
            ServiceVariantPayment.objects.filter(service_variant__service_id=service.id).count()
            == 1
        )
        assert (
            ServiceVariantPayment.all_objects.filter(service_variant__service_id=service.id).count()
            == 2
        )
        assert (
            ServiceVariantChangelog.objects.filter(service_variant__service_id=service.id).count()
            == 1
        )

        sv = service.service_variants.get(active=True, price=Decimal('17.3'))
        assert sv.has_prepayment is False
        assert sv.has_cancellation_fee is False
        assert sv.id == with_prepayment.id

        sv2 = service.service_variants.get(active=True, price=Decimal('27.21'))
        assert sv2.has_prepayment is False
        assert sv2.has_cancellation_fee is True
        assert sv2.id == with_deposit.id

        DisablePrepayments(self.business.id)
        # additional changelogs are not generated
        assert (
            ServiceVariantChangelog.objects.filter(service_variant__service_id=service.id).count()
            == 1
        )

    @mock.patch('lib.locks.BusinessActionLock.lock', return_value=True)
    def test_deposits(self, *_):
        """Test if service variants has no now_show_protection."""
        with_prepayment = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=17.30,
        )

        baker.make(
            ServiceVariantPayment,
            service_variant=with_prepayment,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=9.99,
        )

        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=relativedelta(minutes=55),
            time_slot_interval=relativedelta(minutes=15),
            type=PriceType.FIXED,
            price=27.21,
        )

        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=7.21,
        )

        ReleaseAllDepositOnServicesAndBookings(self.business.id)

        assert len(self.business.services.all()) == 1

        service = self.business.services.get()
        assert len(service.service_variants.filter(active=True)) == 2

        assert not ServiceVariantPayment.objects.filter(
            service_variant__service_id=service.id
        ).exists()
        assert (
            ServiceVariantPayment.all_objects.filter(service_variant__service_id=service.id).count()
            == 2
        )
        assert (
            ServiceVariantChangelog.objects.filter(service_variant__service_id=service.id).count()
            == 2
        )

        sv = service.service_variants.get(active=True, price=Decimal('17.3'))
        assert sv.has_prepayment is False
        assert sv.has_cancellation_fee is False
        assert sv.id == with_prepayment.id

        sv2 = service.service_variants.get(active=True, price=Decimal('27.21'))
        assert sv2.has_prepayment is False
        assert sv2.has_cancellation_fee is False
        assert sv2.id == with_deposit.id

        ReleaseAllDepositOnServicesAndBookings(self.business.id)
        # additional changelogs are not generated
        assert (
            ServiceVariantChangelog.objects.filter(service_variant__service_id=service.id).count()
            == 2
        )

    @mock.patch('lib.locks.BusinessActionLock.lock', return_value=True)
    def test_deposit_transactions(self, *_):
        """Test if deposits are released."""

        def create_txn(status):
            txn = baker.make(
                Transaction,
                pos=self.pos,
                total=12.34,
                transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
            )

            receipt = baker.make(
                Receipt,
                payment_type=self.pba,
                status_code=status,
                provider='adyen_ee',
                transaction=txn,
            )

            txn.latest_receipt = receipt
            txn.save()

            baker.make(
                PaymentRow,
                payment_type=self.pba,
                status=status,
                amount=txn.total,
                receipt=receipt,
                settled=True,
                provider=AdyenEEPaymentProvider.codename,
            )

            ReleaseAllDepositOnServicesAndBookings(self.business.id)

            txn = Transaction.objects.get(id=txn.id)

            assert txn.latest_receipt.status_code == receipt_status.DEPOSIT_CHARGE_CANCELED

        create_txn(receipt_status.DEPOSIT_AUTHORISATION_SUCCESS)
        create_txn(receipt_status.DEPOSIT_CHARGE_FAILED)


@pytest.mark.django_db
class TestAutoChargingCF(TestCaseWithSetUp):
    def prepare_data(self, days=1, status=Appointment.STATUS.NOSHOW):
        booking, *_ = create_subbooking(
            business=self.business,
            booking_kws={
                'business': self.business,
                'source': self.booking_source,
                'type': Appointment.TYPE.BUSINESS,
                'status': status,
            },
        )

        txn = baker.make(
            Transaction,
            pos=self.pos,
            charge_date=tznow() - relativedelta(days=days),
            transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
            appointment=booking.appointment,
        )

        baker.make(
            TransactionRow,
            type=TransactionRow.TRANSACTION_ROW_TYPE__DEPOSIT,
            transaction=txn,
            subbooking=booking,
        )

        receipt = baker.make(
            Receipt,
            payment_type=self.pba,
            status_code=receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
            transaction=txn,
        )

        txn.latest_receipt = receipt
        txn.save()

        baker.make(
            PaymentRow,
            payment_type=self.pba,
            status=receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
            receipt=receipt,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            amount=10,
        )

        return txn

    def test_cancellation_fee_finished_last_day(self):
        txn = self.prepare_data(days=1, status=Appointment.STATUS.FINISHED)
        ChargeCancellationFeeTransactions(self.pos.id)

        new_receipt = Transaction.objects.get(id=txn.id).latest_receipt
        assert new_receipt.status_code == receipt_status.DEPOSIT_AUTHORISATION_SUCCESS

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_cancellation_fee_no_show_last_day(self, analytics_track_mock, analytics_identify_mock):
        txn = self.prepare_data(days=1, status=Appointment.STATUS.NOSHOW)
        ChargeCancellationFeeTransactions(self.pos.id)

        new_receipt = Transaction.objects.get(id=txn.id).latest_receipt
        assert new_receipt.status_code == receipt_status.DEPOSIT_CHARGE_SUCCESS
        assert analytics_track_mock.call_count == 0
        assert analytics_identify_mock.call_count == 0

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_cancellation_fee_cancelled_last_day(
        self, analytics_track_mock, analytics_identify_mock
    ):
        txn = self.prepare_data(days=1, status=Appointment.STATUS.CANCELED)
        ChargeCancellationFeeTransactions(self.pos.id)

        new_receipt = Transaction.objects.get(id=txn.id).latest_receipt
        assert new_receipt.status_code == receipt_status.DEPOSIT_CHARGE_SUCCESS
        assert analytics_track_mock.call_count == 0
        assert analytics_identify_mock.call_count == 0

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_cancellation_fee_no_show_two_days(self, analytics_track_mock, analytics_identify_mock):
        txn = self.prepare_data(days=1, status=Appointment.STATUS.NOSHOW)
        ChargeCancellationFeeTransactions(self.pos.id)

        new_receipt = Transaction.objects.get(id=txn.id).latest_receipt
        assert new_receipt.status_code == receipt_status.DEPOSIT_CHARGE_SUCCESS
        assert analytics_track_mock.call_count == 0
        assert analytics_identify_mock.call_count == 0

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_cancellation_fee_cancelled_two_days(
        self, analytics_track_mock, analytics_identify_mock
    ):
        txn = self.prepare_data(days=1, status=Appointment.STATUS.CANCELED)
        assert analytics_track_mock.call_count == 0
        assert analytics_identify_mock.call_count == 0
        ChargeCancellationFeeTransactions(self.pos.id)

        assert analytics_track_mock.call_count == 0
        assert analytics_identify_mock.call_count == 0

        new_receipt = Transaction.objects.get(id=txn.id).latest_receipt
        assert new_receipt.status_code == receipt_status.DEPOSIT_CHARGE_SUCCESS
        assert analytics_track_mock.call_count == 0
        assert analytics_identify_mock.call_count == 0

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_cancellation_fee_no_show_last_week(
        self, analytics_track_mock, analytics_identify_mock
    ):
        txn = self.prepare_data(days=7, status=Appointment.STATUS.NOSHOW)
        ChargeCancellationFeeTransactions(self.pos.id)

        new_receipt = Transaction.objects.get(id=txn.id).latest_receipt
        assert new_receipt.status_code == receipt_status.DEPOSIT_AUTHORISATION_SUCCESS
        assert analytics_track_mock.call_count == 0
        assert analytics_identify_mock.call_count == 0

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_cancellation_fee_cancelled_last_week(
        self, analytics_track_mock, analytics_identify_mock
    ):
        txn = self.prepare_data(days=7, status=Appointment.STATUS.CANCELED)
        ChargeCancellationFeeTransactions(self.pos.id)

        new_receipt = Transaction.objects.get(id=txn.id).latest_receipt
        assert new_receipt.status_code == receipt_status.DEPOSIT_AUTHORISATION_SUCCESS

        assert analytics_track_mock.call_count == 0
        assert analytics_identify_mock.call_count == 0
        # transaction doesn't happen when older than 24h


@pytest.mark.django_db
class TestCalculatingPosPlan(TestCaseWithSetUp):
    def make_txn(self, pos, payment_type):
        txn = baker.make(Transaction, pos=pos)
        receipt = baker.make(
            Receipt,
            status_code=receipt_status.PAYMENT_SUCCESS,
            payment_type=payment_type,
            transaction=txn,
        )
        txn.latest_receipt = receipt
        txn.save()

        pnref = uuid4().hex
        baker.make(
            PaymentRow,
            payment_type=payment_type,
            status=receipt_status.PAYMENT_SUCCESS,
            amount=txn.total,
            receipt=receipt,
            pnref=pnref,
            settled=True,
            provider=PaymentProviderEnum.ADYEN_PROVIDER,
        )

        baker.make(
            Capture,
            reference=pnref,
            oper_result=oper_result.SUCCESS,
        )

    def test_one_plan(self):
        plan1 = self.pos_plan

        pos1 = baker.make(POS, active=True, business=baker.make(Business))
        recalculate_pos_plans()

        assert pos1.pos_plans.count() == 1
        assert pos1.pos_plans.last() == plan1

    def test_two_plans_with_lock(self):
        plan2 = baker.make(
            POSPlan,
            min_txn_num=0,
            plan_type=POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT,
            individual=True,
        )

        pos1 = baker.make(POS, active=True, business=baker.make(Business))
        pos1.pos_plans.set([plan2])

        recalculate_pos_plans()

        assert pos1.pos_plans.count() == 1
        assert pos1.pos_plans.last() == plan2

    def test_simple(self):
        plan1 = self.pos_plan  # baker.make(POSPlan, min_txn_num=0)
        plan2 = baker.make(
            POSPlan, min_txn_num=2, plan_type=POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT
        )
        baker.make(
            POSPlan,
            min_txn_num=2,
            individual=True,
            plan_type=POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT,
        )

        pos1 = baker.make(POS, active=True, business=baker.make(Business))
        payment_type1 = baker.make(PaymentType, pos=pos1, code=PaymentTypeEnum.PAY_BY_APP)
        pos2 = baker.make(POS, active=True, business=baker.make(Business))
        payment_type2 = baker.make(PaymentType, pos=pos2, code=PaymentTypeEnum.PAY_BY_APP)

        # make 1 transaction in pos1 and 2 in pos2
        self.make_txn(pos1, payment_type1)
        self.make_txn(pos2, payment_type2)
        self.make_txn(pos2, payment_type2)

        recalculate_pos_plans()

        assert pos1.pos_plans.count() == 1
        assert pos1.pos_plans.last() == plan1
        assert pos2.pos_plans.count() == 1
        assert pos2.pos_plans.last() == plan2

    def test_second_posplan(self):
        plan1 = self.pos_plan
        plan2 = baker.make(
            POSPlan,
            min_txn_num=0,
            plan_type=POSPlanPaymentTypeEnum.ADYEN_DONATIONS,
        )

        pos1 = baker.make(POS, active=True, business=baker.make(Business))

        recalculate_pos_plans()

        assert pos1.pos_plans.count() == 2
        plans = pos1.pos_plans.order_by('created')
        assert plans[0] == plan1
        assert plans[1] == plan2

    def test_pos_which_needs_downgrade(self):
        """
        Test downgrading and if lock works properly.
        :return:
        """
        plan1 = self.pos_plan  # baker.make(POSPlan, min_txn_num=0)
        plan2 = baker.make(
            POSPlan,
            min_txn_num=2,
            plan_type=POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT,
        )

        plan3 = baker.make(
            POSPlan,
            min_txn_num=2,
            individual=True,
            plan_type=POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT,
        )

        pos1 = baker.make(
            POS,
            active=True,
            business=baker.make(Business),
        )
        pos1.pos_plans.add(plan2)

        pos2 = baker.make(
            POS,
            active=True,
            business=baker.make(Business),
        )
        pos2.pos_plans.add(plan3)

        recalculate_pos_plans()
        pos1.refresh_from_db()
        pos2.refresh_from_db()
        assert pos1.pos_plans.count() == 1
        assert pos1.pos_plans.last() == plan1
        assert pos2.pos_plans.count() == 1
        assert pos2.pos_plans.last() == plan3

    def test_with_only_one_plan(self):
        plan1 = self.pos_plan  # baker.make(POSPlan, min_txn_num=0)
        plan2 = baker.make(
            POSPlan, min_txn_num=2, plan_type=POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT
        )
        plan3 = baker.make(
            POSPlan,
            min_txn_num=2,
            individual=True,
            plan_type=POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT,
        )

        pos1 = baker.make(
            POS,
            active=True,
            business=baker.make(Business),
        )
        pos1.pos_plans.set([plan2])

        pos2 = baker.make(
            POS,
            active=True,
            business=baker.make(Business),
        )
        pos2.pos_plans.set([plan3])

        recalculate_pos_plans()
        pos1.refresh_from_db()
        pos2.refresh_from_db()
        assert pos1.pos_plans.count() == 1
        assert pos1.pos_plans.last() == plan1
        assert pos2.pos_plans.count() == 1
        assert pos2.pos_plans.last() == plan3


@pytest.mark.django_db
class TestCancelCFPAfter3Days(TestCaseWithSetUp):
    def test_normall_transaction(self):
        """
        Test if only CALL FOR PAYMENTS older than 3 days are cancelled
        """

        txn1 = baker.make(
            Transaction,
            pos=self.pos,
            total=100,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        receipt = baker.make(
            Receipt,
            payment_type=self.prepayment,
            status_code=receipt_status.CALL_FOR_PAYMENT,
            transaction=txn1,
        )
        txn1.latest_receipt = receipt
        txn1.save()

        baker.make(
            PaymentRow,
            amount=100,
            payment_type=self.prepayment,
            status=receipt_status.CALL_FOR_PAYMENT,
            receipt=receipt,
        )

        # Transaction no. 2
        txn2 = baker.make(
            Transaction,
            pos=self.pos,
            total=100,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        receipt2 = baker.make(
            Receipt,
            payment_type=self.prepayment,
            status_code=receipt_status.CALL_FOR_PAYMENT,
            transaction=txn2,
        )
        new_created = tznow() - relativedelta(days=4)
        cursor = connection.cursor()
        cursor.execute(
            """
            UPDATE pos_receipt
            SET created=%s
            WHERE receipt_id=%s;
        """,
            (new_created.isoformat(), receipt2.id),
        )

        txn2.latest_receipt = receipt2
        txn2.save()

        baker.make(
            PaymentRow,
            amount=100,
            payment_type=self.prepayment,
            status=receipt_status.CALL_FOR_PAYMENT,
            receipt=receipt2,
        )

        cancel_cfp_task.run()

        rtxn1 = Transaction.objects.get(id=txn1.id).latest_receipt.status_code
        assert rtxn1 == receipt_status.CALL_FOR_PAYMENT

        rtxn2 = Transaction.objects.get(id=txn2.id).latest_receipt.status_code
        assert rtxn2 == receipt_status.PAYMENT_CANCELED


@pytest.mark.django_db
class TestReleaseAllDepositOnServicesAndBookingsTask(TestCaseWithSetUp):
    def setUp(self):
        super().setUp()
        self.service_variant: ServiceVariant = baker.make(
            ServiceVariant,
            duration='7200',
            active=True,
            service=baker.make(Service, business=self.business),
            type=PriceType.FIXED,
            time_slot_interval='0300',
            price=100,
        )
        baker.make(
            ServiceVariantPayment,
            service_variant=self.service_variant,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=56,
        )

    def test_task_disables_payment(self) -> None:
        ReleaseAllDepositOnServicesAndBookings(self.business.id)

        self.service_variant.refresh_from_db()
        self.assertFalse(self.service_variant.has_payment)


@pytest.mark.django_db
class TestBatchChangeDefaultPaymentMethodToPbaTask(TestCaseWithSetUp):
    def test_task_for_business_with_disabled_pba(self) -> None:
        self.pos.disable_pay_by_app()
        assert not self.pos.is_pay_by_app_active

        batch_change_default_payment_method_to_pba_task([self.business.id])
        assert not self.pos.is_pay_by_app_active
        assert not self.pos.pay_by_app.filter(default=True).exists()

    def test_task_for_business_with_enabled_and_non_default_pba(self) -> None:
        assert self.pos.is_pay_by_app_active
        self.pos.pay_by_app.update(default=False)
        assert not self.pos.pay_by_app.filter(default=True).exists()

        batch_change_default_payment_method_to_pba_task([self.business.id])
        assert self.pos.pay_by_app.filter(default=True).exists()
        assert self.pos.payment_types.filter(default=True).count() == 1

    def test_task_for_business_with_enabled_and_default_pba(self) -> None:
        assert self.pos.is_pay_by_app_active
        self.pos.pay_by_app.update(default=True)
        assert self.pos.pay_by_app.filter(default=True).exists()

        batch_change_default_payment_method_to_pba_task([self.business.id])
        assert self.pos.pay_by_app.filter(default=True).exists()
        assert self.pos.payment_types.filter(default=True).count() == 1
