# Generated by Django 4.0.7 on 2022-09-09 06:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pos', '0230_alter_pos_fast_payouts_visible'),
    ]

    operations = [
        migrations.AddField(
            model_name='paymentrow',
            name='basket_payment_id',
            field=models.UUIDField(null=True),
        ),
        migrations.AddField(
            model_name='pos',
            name='pos_refactor_stage1_enabled',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='transaction',
            name='basket_id',
            field=models.UUIDField(null=True),
        ),
        migrations.AddField(
            model_name='transaction',
            name='cancellation_fee_auth_id',
            field=models.UUIDField(null=True),
        ),
        migrations.AddField(
            model_name='transactionrow',
            name='basket_item_id',
            field=models.UUIDField(blank=True, null=True),
        ),
    ]
