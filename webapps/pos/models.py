# pylint: disable=too-many-lines, duplicate-code, cyclic-import
import bisect
import datetime
import json
import logging
import re
import string
import typing as t
from collections import Counter
from dataclasses import asdict
from decimal import Decimal
from difflib import Differ
from functools import partial
from typing import Optional

from dateutil.relativedelta import relativedelta
from dirtyfields.dirtyfields import DirtyFieldsMixin
from django.conf import settings
from django.core import validators
from django.core.exceptions import ValidationError
from django.db import models
from django.db import transaction as db_transaction
from django.db.models import (
    Exists,
    JSONField,
    OuterRef,
    Prefetch,
    Q,
    QuerySet,
)
from django.db.models.constraints import UniqueConstraint
from django.db.models.expressions import (
    Case,
    F,
    RawSQL,
    When,
)
from django.db.models.signals import post_save
from django.db.transaction import atomic
from django.dispatch import receiver
from django.utils.encoding import force_str
from django.utils.functional import cached_property
from django.utils.translation import gettext
from django.utils.translation import gettext_lazy as _
from django.utils.translation import (
    override,
    pgettext_lazy,
)

from country_config.enums import Country
from lib.enums import StrEnum
from lib.feature_flag.adapter import UserData
from lib.feature_flag.enums import CustomUserAttributes
from lib.feature_flag.feature import (
    BooksyGiftcardsEnabledFlag,
    EnablePBAOnlyAfterAcceptingFeesFlag,
)
from lib.feature_flag.feature.payment import (
    PaymentTypeStatusEnabled,
    PayoutMethodChangeAllowedTimedeltaFlag,
    PrepaymentsForBusinessAppointmentEnabled,
    UpdateBGCPaymentRowIfPBASucceededFlag,
)
from lib.fields.deprecate_field import deprecate_field
from lib.fields.unique_foreign_key_field import UniqueForeignKey
from lib.interval.fields import IntervalField
from lib.models import (
    ActiveManager,
    ArchiveManager,
    ArchiveModel,
    ArchiveQuerySet,
    AutoUpdateManager,
    AutoUpdateQuerySet,
    BaseArchiveManager,
    SoftDeleteManager,
    UndeletableMixin,
)
from lib.payment_gateway.entities import WalletEntity
from lib.payment_providers.entities import (
    DeviceDataEntity,
    AccountHolderEntity,
    AccountHolderSettingsData,
)
from lib.payment_providers.enums import ProviderAccountHolderStatus
from lib.payments.enums import PaymentProviderCode
from lib.pos.entities import PaymentRowEntity
from lib.pos.entities import TransactionEntity
from lib.pos.utils import (
    pos_refactor_stage2_enabled,
    txn_refactor_stage2_enabled,
)
from lib.queryset import MidnightQuerySet
from lib.rivers import (
    River,
    bump_document,
)
from lib.segment_analytics.enums import EventType
from lib.serializers import (
    RelativedeltaField,
    safe_get,
)
from lib.tools import (
    create_uuid,
    format_currency,
    get_locale_from_language,
    major_unit,
    minor_unit,
    tznow,
    sget_v2,
)
from lib.unicode_utils import force_unicode
from service.pos.enums import BCRPromoSplashType
from webapps.adyen.consts import oper_result as adyen_oper_result
from webapps.adyen.helpers import get_reference
from webapps.adyen.models import Capture
from webapps.booking.models import (
    Appointment,
    SubBooking,
)
from webapps.business.enums import PriceType
from webapps.business.models import (
    Business,
    BusinessVersion,
    Resource,
    ServiceAddOnUse,
    ServiceVariant,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business.tasks import trust_clients_by_business_task
from webapps.donation.models import Donation
from webapps.invoicing.models import CustomerInvoice
from webapps.kill_switch.models import KillSwitch
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_providers.ports.account_holder_ports import PaymentProvidersAccountHolderPort
from webapps.pos import enums
from webapps.pos.booksy_gift_cards.models import BooksyGiftCard
from webapps.pos.calculations import round_currency
from webapps.pos.enums import (
    CommissionAction,
    CommissionForType,
    CommissionRateAction,
    CommissionType,
    PaymentProviderEnum,
    PaymentTypeEnum,
    PaymentTypeStatus,
    POSMigrationGroup,
    POSPlanPaymentTypeEnum,
    StaffCommissionRateAction,
    TapToPayStatus,
    receipt_status,
)
from webapps.pos.enums.consts import SPLITTING_STATUSES
from webapps.pos.enums.splash import (
    SplashType,
)
from webapps.pos.enums.tip_mode import PosTipMode
from webapps.pos.provider import (
    PAYMENT_PROVIDER_CHOICES,
    get_payment_provider,
)
from webapps.pos.tasks import (
    CheckoutPrepaidTransaction,
    CloseAllCashRegisters,
    POSActivityChange,
    ReleaseAllDepositOnServicesAndBookings,
    ReleaseDepositOnPayment,
    initialize_basket_payments,
    recalculate_pos_plans,
)
from webapps.pos.tip_calculations import SimpleTip
from webapps.profile_completeness.events import step_activate_mobile_payments
from webapps.register.models import Register
from webapps.segment.tasks import (
    analytics_business_pba_enabled_task,
    analytics_business_pos_updated_task,
    analytics_checkout_transaction_completed_task,
    analytics_payment_transaction_completed_task,
)
from webapps.sequencing_number.models import SALES_DOCUMENT
from webapps.sequencing_number.utils import create_sequence_record
from webapps.stripe_integration.enums import (
    StripeAccountStatus,
    StripePayoutMethodType,
    StripePayoutStatus,
)
from webapps.user.models import (
    User,
    UserProfile,
)
from webapps.voucher.events import voucher_activated_event
from webapps.voucher.models import (
    Voucher,
    VoucherTemplate,
)
from webapps.warehouse.models import (
    Commodity,
    WarehouseDocument,
    WarehouseDocumentRow,
    WarehouseDocumentType,
)

# pylint: disable=line-too-long
DEFAULT_DEPOSIT_POLICY_TRANSLATION_KEY = "\nThis business enforces a cancellation policy on select services and reserves the right to charge a cancellation fee for any no-shows and late cancellations. Note that you may be considered a no-show if you are late for your scheduled appointment, and it is up to the business to decide whether the fee is applied.\n\nThe cancellation fee is only charged if you violate the terms of the cancellation policy. If you show up to the appointment on time or cancel before the deadline listed on your confirmation page, no cancellation fee is withdrawn from your account. As the fee is not charged for successful appointments, you will be expected to pay for your appointment in full once the service is completed.\n"
# pylint: enable=line-too-long

_logger = logging.getLogger('booksy.pos_activity')


def get_default_auto_charge_cancellation_fee() -> bool:
    return settings.API_COUNTRY == 'us'


class POS(
    UndeletableMixin, DirtyFieldsMixin, ArchiveModel
):  # pylint: disable=abstract-method, too-many-public-methods
    POS_TAX_MODE__EXCLUDED = 'E'
    POS_TAX_MODE__INCLUDED = 'I'
    POS_TAX_MODES = (
        (POS_TAX_MODE__EXCLUDED, _('Tax excluded, added to subtotal (US specific)')),
        (POS_TAX_MODE__INCLUDED, _('Tax included in the product price (EU specific)')),
    )
    PAY_BY_APP_DISABLED = enums.PAY_BY_APP_DISABLED
    PAY_BY_APP_PENDING = enums.PAY_BY_APP_PENDING
    PAY_BY_APP_ENABLED = enums.PAY_BY_APP_ENABLED
    PAY_BY_APP_ENABLE_PENDING_PROVIDER_APPROVAL = enums.PAY_BY_APP_PENDING_ADYEN_APPROVAL
    PAY_BY_APP_DISABLED_DUE_TO_KYC_ERRORS = enums.PAY_BY_APP_DISABLED_DUE_TO_KYC_ERRORS
    PAY_BY_APP_RESTRICTED_ACCESS = enums.PAY_BY_APP_RESTRICTED_ACCESS

    PAY_BY_APP_CHOICES = (
        (PAY_BY_APP_DISABLED, _('Disabled pay by app')),
        (PAY_BY_APP_PENDING, _("Business requested to enable 'Pay By_App'")),
        (PAY_BY_APP_ENABLED, _('Enabled pay by app')),
        (PAY_BY_APP_ENABLE_PENDING_PROVIDER_APPROVAL, _('Awaiting payment provider approval')),
        (PAY_BY_APP_DISABLED_DUE_TO_KYC_ERRORS, _('Disabled due to KYC errors')),
        (PAY_BY_APP_RESTRICTED_ACCESS, _('Restricted access')),
    )

    FRAUD_STATUS_UNKNOWN = 'U'
    FRAUD_STATUS_POSSIBLE_FRAUD = 'P'
    FRAUD_STATUS_FRAUD = 'F'
    FRAUD_STATUS_WHITELIST = 'W'

    FRAUD_STATUS_CHOICES = (
        (FRAUD_STATUS_UNKNOWN, _('Unknown')),
        (FRAUD_STATUS_POSSIBLE_FRAUD, _("Possible fraud status")),
        (FRAUD_STATUS_FRAUD, _('Fraud')),
        (FRAUD_STATUS_WHITELIST, _('Whitelist')),
    )
    FRAUD_STATUS_TRANSFER = {
        # old status, new status => allowed
        (FRAUD_STATUS_POSSIBLE_FRAUD, FRAUD_STATUS_UNKNOWN): False,
        (FRAUD_STATUS_FRAUD, FRAUD_STATUS_UNKNOWN): False,
        (FRAUD_STATUS_FRAUD, FRAUD_STATUS_POSSIBLE_FRAUD): False,
        (FRAUD_STATUS_UNKNOWN, FRAUD_STATUS_WHITELIST): True,
    }

    # see RelativedeltaField
    # """relativedelta(days=1) <-> {"days": 1}""".
    THREE_DAYS = {'days': 3}
    POS_CANCEL_TIME_OPTIONS = [
        {'hours': 1},
        {'hours': 2},
        {'hours': 3},
        {'hours': 6},
        {'hours': 12},
        {'days': 1},
        {'days': 2},
        THREE_DAYS,
        {'days': 4},
        {'days': 5},
        {'days': 7},
        {'days': 10},
        {'days': 14},
        {'days': 21},
        {'days': 25},
        {'days': 30},
    ]

    id = models.AutoField(primary_key=True, db_column='pos_id')
    business = UniqueForeignKey(
        Business,
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
        db_constraint=False,
        unique=True,
    )
    active = models.BooleanField(null=False, blank=False, default=True)
    service_tax_mode = models.CharField(
        max_length=1,
        choices=POS_TAX_MODES,
        default=POS_TAX_MODE__INCLUDED,
        db_index=True,
    )
    product_tax_mode = models.CharField(
        max_length=1,
        choices=POS_TAX_MODES,
        default=POS_TAX_MODE__INCLUDED,
        db_index=True,
    )
    voucher_tax_mode = models.CharField(
        max_length=1,
        choices=POS_TAX_MODES,
        default=POS_TAX_MODE__INCLUDED,
        db_index=True,
    )
    tips_enabled = models.BooleanField(null=False, blank=False, default=False)
    tip_calculation_mode = models.CharField(
        max_length=1,
        choices=PosTipMode.choices(),
        default=PosTipMode.SERVICES,
    )
    # tip_rounding_mode as ndigits param from round function
    # default=2 means rounding to 2 decimal places (we dont store more places!)
    # -2 means rounding to 100
    deprecated_tip_rounding_mode = models.SmallIntegerField(default=2)
    tax_in_receipt_visible = models.BooleanField(default=True)
    commissions_enabled = models.BooleanField(default=True)
    products_stock_enabled = models.BooleanField(default=False)
    payment_auto_accept = models.BooleanField(default=True)
    # Force pay_by_app for bookigs with cancellation fee operation
    # Issue: https://redmine.booksy.pm/issues/72435
    force_pba_for_cf = models.BooleanField(default=False)
    item_discount_enabled = models.BooleanField(default=True)
    global_discount_enabled = models.BooleanField(default=True)
    deposit_policy = models.CharField(max_length=5000, null=False, blank=True)
    deposit_cancel_time = IntervalField(
        null=False, blank=False, default=relativedelta(**THREE_DAYS)
    )
    auto_charge_cancellation_fee = models.BooleanField(
        default=get_default_auto_charge_cancellation_fee
    )
    # Register settings
    registers_enabled = models.BooleanField(
        null=False,
        blank=False,
        default=False,
        help_text='Is Register feature enabled for this Business?',
    )
    registers_max_opened = models.PositiveIntegerField(
        default=1,
        help_text='How many Registers can be open at the same time?',
    )
    registers_reopening = models.BooleanField(
        null=False,
        blank=False,
        default=True,
        help_text='Is reopening of Registers allowed?',
    )

    registers_shared_enabled = models.BooleanField(
        null=False, blank=False, default=False, help_text='Is Shared Registers feature enabled?'
    )

    pay_by_app_status = models.CharField(
        max_length=1,
        choices=PAY_BY_APP_CHOICES,
        default=PAY_BY_APP_DISABLED,
    )
    pay_by_app_request_date = models.DateTimeField(blank=True, null=True)

    fraud_status = models.CharField(
        max_length=1,
        choices=FRAUD_STATUS_CHOICES,
        default=FRAUD_STATUS_UNKNOWN,
    )

    prepayment_enabled = models.BooleanField(default=False)
    refund_enabled = models.BooleanField(default=False)

    # 45909 - Trusted clients
    waive_amount = models.IntegerField(default=0)

    # Service Fee
    service_fee = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal(0))

    # 49995 - POSPlan
    deprecated_pos_plan = models.ForeignKey(
        'POSPlan',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
    )
    deprecated_pos_plan_locked = models.BooleanField(
        default=False,
        null=False,
        blank=False,
    )

    pos_plans = models.ManyToManyField(
        'POSPlan',
        related_name='poses',
    )

    # 51650 - Receipt custom line
    receipt_footer_line_1 = models.CharField(
        max_length=50, blank=True, null=True, help_text='First line of the receipt footer'
    )
    receipt_footer_line_2 = models.CharField(
        max_length=50, blank=True, null=True, help_text='Second line of the receipt footer'
    )
    marketpay_enabled = models.BooleanField(
        default=False,
        null=False,
        blank=False,
    )
    stripe_terminal_enabled = models.BooleanField(
        default=False,
        null=False,
        blank=False,
    )
    donations_enabled = models.BooleanField(
        default=False,
        null=False,
        blank=False,
    )
    fast_payouts_visible = models.BooleanField(
        default=True,
        null=False,
        blank=False,
    )
    fast_payouts_admin_blocked = models.BooleanField(
        default=False,
        null=False,
        blank=False,
    )
    fast_payouts_merchant_enabled = models.BooleanField(
        default=True,
        null=False,
        blank=False,
    )
    pos_refactor_stage2_enabled = models.BooleanField(
        default=False,
        null=False,
        blank=False,
    )
    # field: _force_stripe_pba shouldn't be used separately
    # property extended with LD flag - force_stripe_pba
    _force_stripe_pba = models.BooleanField(default=False)
    force_stripe_kyc = models.BooleanField(default=False)

    pos_refactor_stage2_migration_date = models.DateTimeField(
        default=None,
        null=True,
        blank=True,
    )

    pos_refactor_stage2_migration_group = models.CharField(
        max_length=50, default=None, null=True, blank=True, choices=POSMigrationGroup.choices()
    )
    use_custom_as_stripe_account_type = deprecate_field(
        # deprecated, as now every new account is always custom
        models.BooleanField(default=False)
    )
    tap_to_pay_enabled = models.BooleanField(
        default=False,
        null=False,
        blank=False,
    )
    payout_method_change_allowed = models.BooleanField(default=False)
    tap_to_pay_status = models.CharField(
        max_length=20,
        choices=TapToPayStatus.choices(),
        default=TapToPayStatus.DISABLED,
    )
    blik_in_prepayment_promo_enabled = models.BooleanField(
        # by default to enable to everybody, False == blacklisted;
        # global campaign control is managed through a feature flag, not here
        default=True
    )
    booksy_pay_enabled = models.BooleanField(
        default=False,
        null=False,
        blank=False,
    )
    booksy_pay_late_cancellation_window = IntervalField(null=True, blank=True)
    promote_payment_method_availability_to_customers = models.BooleanField(
        # when True, a notification will be sent to the client prior to the visit,
        # informing him that he can pay using tap to pay or bcr
        db_column='promote_ttp_payments_to_customers',
        default=False,
        null=True,
    )
    ba_deposit_enabled = models.BooleanField(
        default=False,
        null=False,
        blank=False,
    )

    LOAD_DEFAULTS_FIELDS = [
        # all non-related except ArchiveModel fields and 'business', obviously
        'active',
        'service_tax_mode',
        'product_tax_mode',
        'tips_enabled',
        'tip_calculation_mode',
        'deprecated_tip_rounding_mode',
        'tax_in_receipt_visible',
        'commissions_enabled',
        'products_stock_enabled',
        'payment_auto_accept',
        'global_discount_enabled',
        'deposit_policy',
        'deposit_cancel_time',
        'registers_enabled',
        'registers_max_opened',
        'registers_reopening',
        'marketpay_enabled',
        'stripe_terminal_enabled',
        'fast_payouts_admin_blocked',
        'fast_payouts_merchant_enabled',
        'pos_refactor_stage2_enabled',
        '_force_stripe_pba',
        'force_stripe_kyc',
        'pos_refactor_stage2_migration_group',
        'tap_to_pay_enabled',
        'promote_payment_method_availability_to_customers',
    ]

    class Meta:
        verbose_name_plural = 'POSes'

    all_objects = SoftDeleteManager()

    def __str__(self):
        if self.business:
            return f'POS Settings for Business [{self.business.id}] {self.business.name}'
        if self.id == 1:
            return 'POS Settings Defaults'
        return 'Corrupted POS Settings'

    def _validate_fraud_status_transfer(self):
        original_fraud_status = self.get_dirty_fields().get('fraud_status')
        transfer_allowed = self.FRAUD_STATUS_TRANSFER.get(
            (original_fraud_status, self.fraud_status), True
        )
        if self.id and not transfer_allowed:
            fraud_status = self.fraud_status
            raise ValidationError(
                {
                    'fraud_status': 'Forbidden transfer fraud_status '
                    f'POS can\'t be transfer from {original_fraud_status} to {fraud_status}'
                }
            )

    def _update_trusted_clients(self):
        """Check if waive amount has changed. If yes updated TrustedClients for
        related business."""

        if (
            self.get_dirty_fields().get('waive_amount') != self.waive_amount
            and self.waive_amount != 0
        ):
            trust_clients_by_business_task.delay(
                business_id=self.business_id,
            )

    @atomic()
    def save(self, *args, **kwargs):
        self._validate_fraud_status_transfer()
        self._update_trusted_clients()
        dirty_fields = self.get_dirty_fields()
        if self.business and 'auto_charge_cancellation_fee' in dirty_fields:
            if self.business.package == self.business.Package.LITE:
                self.auto_charge_cancellation_fee = True
        super().save(*args, **kwargs)

        analytics_changed_fields = self._analytics_tasks_changed_fields(
            dirty_fields=dirty_fields,
        )
        if self.business:
            if analytics_changed_fields:
                business_id = self.business.id
                analytics_business_pos_updated_task.delay(
                    business_id=business_id,
                    context={
                        'business_id': business_id,
                    },
                )
                if KillSwitch.alive(KillSwitch.MarTech.BUSINESS_PBA_ENABLED):
                    if 'pay_by_app_status' in analytics_changed_fields:
                        analytics_business_pba_enabled_task.delay(
                            business_id=business_id,
                            context={
                                'business_id': business_id,
                            },
                        )
            if 'stripe_terminal_enabled' in dirty_fields and not self.stripe_terminal_enabled:
                self.disable_stripe_terminal_payments()

    def _analytics_tasks_changed_fields(self, dirty_fields):
        fields = set()
        if (
            'pay_by_app_status' in dirty_fields
            and dirty_fields.get('pay_by_app_status') != self.pay_by_app_status
        ):
            fields.add('pay_by_app_status')
        if 'donations_enabled' in dirty_fields and bool(
            dirty_fields.get('donations_enabled')
        ) != bool(self.donations_enabled):
            fields.add('donations_enabled')
        return fields

    @cached_property
    def all_payment_types(self):
        return self.payment_types.exclude(
            code__in=[PaymentTypeEnum.SPLIT, *PaymentType.DEPRECATED_TYPES],
        )

    @staticmethod
    def get_default_pos():
        return POS.objects.get(id=1)

    def load_defaults(self):
        # fetch default POS settings
        defaults = self.get_default_pos()

        # copy related items
        Tip.objects.bulk_create(
            [
                Tip(
                    pos=self,
                    rate=t.rate,
                    default=t.default,
                )
                for t in defaults.tips.all()
            ]
        )
        TaxRate.objects.bulk_create(
            [
                TaxRate(
                    pos=self,
                    rate=t.rate,
                    default_for_service=t.default_for_service,
                    default_for_product=t.default_for_product,
                )
                for t in defaults.tax_rates.all()
            ]
        )

        _payment_types_disabled_by_default = [
            enums.PAY_BY_APP,
            PaymentTypeEnum.STRIPE_TERMINAL,
            PaymentTypeEnum.KEYED_IN_PAYMENT,
            *PaymentType.DEPRECATED_TYPES,
        ]
        if not settings.POS__SQUARE:
            _payment_types_disabled_by_default.append(PaymentTypeEnum.SQUARE)
        if not settings.POS__TAP_TO_PAY:
            _payment_types_disabled_by_default.append(PaymentTypeEnum.TAP_TO_PAY)

        payments_types = [
            PaymentType(
                pos=self,
                order=t.order,
                code=t.code,
                default=t.default,
                enabled=(False if t.code in _payment_types_disabled_by_default else t.enabled),
            )
            for t in defaults.payment_types.exclude(
                code__in=_payment_types_disabled_by_default,
            )
        ]
        if settings.API_COUNTRY == Country.FR:
            payments_types = [
                PaymentType(
                    pos=self,
                    order=t.order,
                    code=t.code,
                    default=t.default,
                    enabled=(False if t.code in _payment_types_disabled_by_default else t.enabled),
                    available=t.available,
                )
                for t in defaults.payment_types.exclude(
                    code__in=_payment_types_disabled_by_default,
                )
            ]

        PaymentType.objects.bulk_create(payments_types)
        Donation.objects.bulk_create(
            [
                Donation(
                    pos=self,
                    amount=d.amount,
                    default=d.default,
                )
                for d in defaults.donations.all()
            ]
        )

        # copy instance fields and save instance
        for field in self.LOAD_DEFAULTS_FIELDS:
            setattr(self, field, getattr(defaults, field))

        with override(get_locale_from_language(settings.LANGUAGE_CODE)):
            self.deposit_policy = gettext(DEFAULT_DEPOSIT_POLICY_TRANSLATION_KEY)

        self.save(update_fields=self.LOAD_DEFAULTS_FIELDS + ['deposit_policy'])

        self.recalculate_pos_plans()

    @property
    def default_tip(self):
        for tip in self.tips.all():
            if tip.default:
                return tip

    @property
    def default_payment_type(self):
        for type_ in self.payment_types.all():
            if type_.default:
                return type_

    @property
    def default_product_tax(self):
        for tax in self.tax_rates.all():
            if tax.default_for_product:
                return tax

    @property
    def default_service_tax(self):
        for tax in self.tax_rates.all():
            if tax.default_for_service:
                return tax

    def log_changes(self, operator_id, data):
        json_data = json.dumps(data)
        POSChangeLog.objects.create(pos=self, data=json_data, operator_id=operator_id)

    def can_takeover_register(self, operator: t.Optional[User]) -> bool:
        if not operator:
            return False

        staffer: Resource = (
            operator.staffers.filter(
                business_id=self.business_id,
            )
            .only('staff_access_level')
            .first()
            if operator
            else None
        )

        is_basic_staffer = (
            staffer and staffer.staff_access_level == Resource.STAFF_ACCESS_LEVEL_STAFF
        )

        return not is_basic_staffer or operator and operator.id == self.business.owner_id

    def _get_register_with_prefetches(self, filters: t.List[Q]) -> Register:
        return self.registers.filter(*filters).prefetch_related('operations').last()

    def _get_simple_register(self, operator: User):
        register_count = self.registers.filter(is_open=True, opened_by=operator).count()

        if register_count != 1:
            return None

        return self._get_register_with_prefetches([Q(is_open=True), Q(opened_by=operator)])

    def _get_shared_register(
        self,
        selected_register_id: t.Optional[int] = None,
        takeover_when_only_one_is_open: bool = False,
    ):
        only_one_register_opened = self.registers.filter(is_open=True).count() == 1

        if selected_register_id is not None:
            # return register selected by id.
            return self._get_register_with_prefetches([Q(is_open=True), Q(id=selected_register_id)])

        if (
            # if merchant can open max 1 register or api has permission to
            # takeover existing.
            (self.registers_max_opened == 1 or takeover_when_only_one_is_open)
            and only_one_register_opened
            and selected_register_id is None
        ):
            return self._get_register_with_prefetches([Q(is_open=True)])

        return None

    def get_open_register(
        self,
        selected_register_id: t.Optional[int] = None,
        operator: t.Optional[User] = None,
        takeover_when_only_one_is_open: bool = False,
    ):
        if self.registers_shared_enabled:
            return self._get_shared_register(
                selected_register_id,
                takeover_when_only_one_is_open,
            )

        return self._get_simple_register(operator)

    def clear_cash_registers(self, operator_id):
        CloseAllCashRegisters.delay(self.id, operator_id)

    @staticmethod
    def post_save_handler(sender, instance, **kwargs):  # pylint: disable=unused-argument
        dirty = instance.get_dirty_fields()
        _logger.info('[POS] %s', dirty)
        if 'active' in dirty:
            POSActivityChange.delay(instance.id, instance.active)

    def _update_pay_by_app_status(self):
        if 'pay_by_app' in self.__dict__:
            del self.pay_by_app
        if 'is_pay_by_app_exist' in self.__dict__:
            del self.is_pay_by_app_exist
        if 'is_pay_by_app_active' in self.__dict__:
            del self.is_pay_by_app_active

    def update_enable_pba(self):
        self._change_pba_status(POS.PAY_BY_APP_ENABLED)

    def update_pba_awaiting_approval(self):
        """
        Updates pba (pay by app) pos settings, denoting the fact that the
        request for the KYC account has been forwarded to the Adyen.

        The POS in this state is awaiting information about KYC verification
        status (via Adyen notification hooks) and can have its pay_by_app
        enabled or disabled based on the received result.
        """
        self._change_pba_status(POS.PAY_BY_APP_ENABLE_PENDING_PROVIDER_APPROVAL)

    def update_pba_disabled_due_to_errors(self):
        self._change_pba_status(POS.PAY_BY_APP_DISABLED_DUE_TO_KYC_ERRORS)

    def update_disable_pba(self):
        self._change_pba_status(POS.PAY_BY_APP_DISABLED)

    def update_restrict_pba(self):
        self._change_pba_status(POS.PAY_BY_APP_RESTRICTED_ACCESS)

    def _change_pba_status(self, new_pba_status: str):
        """
        Updates `pay_by_app` pos field value.
        The `pay_by_app` is closely related to the KYC (know you customer)
        Adyen process - payments cannot be enabled as long as there are any
        problems with validating business' formal data.
        """
        self._update_pay_by_app_status()
        self.pay_by_app_status = new_pba_status
        payments_enabled = new_pba_status == POS.PAY_BY_APP_ENABLED
        self.prepayment_enabled = payments_enabled
        self.refund_enabled = payments_enabled
        self.save()

        # profile completeness
        if payments_enabled and self.business:
            step_activate_mobile_payments.send(self.business)

        if not payments_enabled and self.business:
            # cleanup all business services
            # with cancellation fee(deposit)
            ReleaseAllDepositOnServicesAndBookings.delay(business_id=self.business.id)

    @cached_property
    def pay_by_app(self):
        return PaymentType.all_objects.filter(
            code=PaymentTypeEnum.PAY_BY_APP,
            pos=self,
        )

    @cached_property
    def stripe_terminal_payment(self):
        return PaymentType.all_objects.filter(
            code=PaymentTypeEnum.STRIPE_TERMINAL,
            pos=self,
        )

    @cached_property
    def is_pay_by_app_exist(self):
        return self.pay_by_app.filter(deleted__isnull=False).exists()

    @cached_property
    def is_pay_by_app_active(self):
        return self.pay_by_app.filter(deleted__isnull=True).exists()

    @cached_property
    def is_stripe_terminal_payments_active(self):
        return self.stripe_terminal_payment.filter(
            deleted__isnull=True,
        ).exists()

    @cached_property
    def is_stripe_terminal_payments_exists(self):
        return self.stripe_terminal_payment.filter(
            deleted__isnull=False,
        ).exists()

    @cached_property
    def can_enable_pba(self):
        if EnablePBAOnlyAfterAcceptingFeesFlag() and self.force_stripe_pba:
            wallet_entity: WalletEntity = PaymentGatewayPort.get_business_wallet(
                business_id=self.business_id,
            )
            account_holder_settings_entity: AccountHolderSettingsData = (
                PaymentProvidersAccountHolderPort.get_account_holder_settings(
                    account_holder_id=wallet_entity.account_holder_id,
                    payment_provider_code=PaymentProviderCode.STRIPE,
                ).entity
            )
            if not account_holder_settings_entity.stripe.pba_fees_accepted:
                return False
        return True

    @property
    def get_pba_fees_accepted(self) -> bool:
        wallet_entity: WalletEntity = PaymentGatewayPort.get_business_wallet(
            business_id=self.business.id,
        )
        if not wallet_entity:
            return False
        account_holder_settings_entity: AccountHolderSettingsData = (
            PaymentProvidersAccountHolderPort.get_account_holder_settings(
                account_holder_id=wallet_entity.account_holder_id,
                payment_provider_code=PaymentProviderCode.STRIPE,
            ).entity
        )
        return account_holder_settings_entity.stripe.pba_fees_accepted

    def enable_pay_by_app(self):
        if not self.can_enable_pba:
            return False

        if self.is_pay_by_app_active:
            # pay by app is active
            return False

        if self.is_pay_by_app_exist:
            self.pay_by_app.filter(deleted__isnull=False).update(deleted=None)
        else:
            pay_by_app = PaymentType(
                pos=self,
                order=1,
                code=PaymentTypeEnum.PAY_BY_APP,
                default=False,
            )
            pay_by_app.save()
        self.update_enable_pba()
        self.recalculate_pos_plans()
        return True

    def enable_blik(self):
        PaymentType.all_objects.update_or_create(
            pos=self,
            code=PaymentTypeEnum.BLIK,
            defaults={'order': 0, 'enabled': True, 'deleted': None},
        )

    def disable_blik(self):
        PaymentType.objects.filter(
            pos=self,
            code=PaymentTypeEnum.BLIK,
        ).delete()  # soft delete

    def disable_keyed_in_payment(self):
        PaymentType.objects.filter(
            pos=self,
            code=PaymentTypeEnum.KEYED_IN_PAYMENT,
        ).delete()  # soft delete

    def can_enable_stripe_terminal(self):
        return self.stripe_terminal_enabled

    @atomic
    def enable_stripe_terminal_payments(self, set_as_default=True):
        if not self.can_enable_stripe_terminal():
            return False

        if self.is_stripe_terminal_payments_active:
            # stripe terminal is active
            return False

        if self.is_stripe_terminal_payments_exists:
            self.stripe_terminal_payment.filter(
                deleted__isnull=False,
            ).update(
                deleted=None,
                default=set_as_default,
            )
        else:
            PaymentType.objects.create(
                pos=self,
                order=2,
                code=PaymentTypeEnum.STRIPE_TERMINAL,
                default=set_as_default,
            )

        if set_as_default:
            self.payment_types.filter(default=True).exclude(
                code=PaymentTypeEnum.STRIPE_TERMINAL,
            ).update(default=False)

        # self.update_enable_stp()  # TODO - czy potrzeba ?
        self.recalculate_pos_plans()

        return True

    @atomic
    def disable_stripe_terminal_payments(self):
        if not self.is_stripe_terminal_payments_active:
            return False

        stp_payment_type = self.stripe_terminal_payment.first()
        if stp_payment_type.default:
            if self.is_pay_by_app_active:
                self.enable_pay_by_app_default()
            else:
                PaymentType.all_objects.filter(
                    code=PaymentTypeEnum.CASH,
                    pos=self,
                ).update(
                    default=True,
                )
                # Save is in soft_delete
                stp_payment_type.default = False

        stp_payment_type.soft_delete()

        return True

    def disable_ba_deposit_payments(self):
        if self.ba_deposit_enabled:
            self.ba_deposit_enabled = False
            self.save(update_fields=['ba_deposit_enabled'])

    def await_pba_approval(self):
        # Disable all pay by app information prior to updating POS status
        self.disable_pay_by_app()
        self.update_pba_awaiting_approval()

    def restrict_pba(self):
        self.disable_pay_by_app()
        self.update_restrict_pba()

    def restrict_stripe_terminal(self):
        self.disable_stripe_terminal_payments()
        self.stripe_account.blocked = True
        self.stripe_account.save(update_fields=['blocked'])

    def restore_stripe_terminal(self):
        self.enable_stripe_terminal_payments()
        self.stripe_account.blocked = False
        self.stripe_account.save(update_fields=['blocked'])

    def disable_pay_by_app(self):
        if self.is_pay_by_app_active:
            pba_payment_type = self.pay_by_app.first()

            # If PBA was default method set CASH as new default
            if pba_payment_type.default:
                PaymentType.all_objects.filter(
                    code=PaymentTypeEnum.CASH,
                    pos=self,
                ).update(
                    default=True,
                )

                # Save is in soft_delete
                pba_payment_type.default = False

            pba_payment_type.soft_delete()
            self.update_disable_pba()
            return True

        return False

    def disable_pay_by_app_due_to_kyc_errors(self):
        self.disable_pay_by_app()
        if self.can_enable_pba:
            self.update_pba_disabled_due_to_errors()

    def enable_pay_by_app_default(self):
        if not self.can_enable_pba:
            return False

        self.enable_pay_by_app()

        # We don't want to set PBA as default if BCR was set as default before.
        is_bcr_default = (
            safe_get(
                self.payment_types.filter(code=PaymentTypeEnum.STRIPE_TERMINAL).first(),
                ['default'],
            )
            or False
        )
        if is_bcr_default:
            return

        self.payment_types.filter(default=True).exclude(
            code=PaymentTypeEnum.PAY_BY_APP,
        ).update(default=False)
        self.payment_types.exclude(default=True).filter(code=PaymentTypeEnum.PAY_BY_APP).update(
            default=True
        )

    @staticmethod
    def rd_to_seconds(relative_delta_dict):
        """
        Convert relative delta to seconds , calculating from now
        :param relative_delta_dict: dict. {'days': 3, 'months': 5}
        :return: int. seconds
        """
        keys = Counter()
        # for simplicity comparision
        days_in_year = 365
        days_in_month = 30
        for key, value in list(relative_delta_dict.items()):
            if key == 'years':
                keys['days'] += value * days_in_year
            elif key == 'months':
                keys['days'] += value * days_in_month
            else:
                keys[key] += value

        return datetime.timedelta(**keys).total_seconds()

    @property
    def no_show_cancel_time_options(self):
        current_option = RelativedeltaField().to_representation(self.deposit_cancel_time)
        # option is already sorted so use left ot right search
        options = self.POS_CANCEL_TIME_OPTIONS[::]
        # find place for current option of business
        # in option list
        index = bisect.bisect_left(
            # convert {'days': 3} => seconds,
            # so objects can be compared
            [self.rd_to_seconds(option) for option in options],
            self.rd_to_seconds(current_option),
        )
        if index == len(options):
            # current option should be placed in the end
            options.append(current_option)
        elif options[index] != current_option:
            # if current business option
            # is not present in sequence insert it
            options.insert(index, current_option)

        return options

    @cached_property
    def has_bank_account(self):
        return BankAccount.objects.filter(pos=self).exists()

    @cached_property
    def account_holder(self):
        return self.account_holders.first() if self.id else None

    @property
    def adyen_kyc_completed(self) -> bool:
        return self.account_holder and self.account_holder.status == 'Active'

    @property  # don't change to cached_property, it will break ie. get_or_create_stripe_account
    def stripe_account(self):
        return self.stripe_accounts.first()

    @property
    def stripe_kyc_completed(self) -> bool:
        return self.stripe_account and self.stripe_account.status == StripeAccountStatus.VERIFIED

    @cached_property
    def fast_payouts_available(self):
        from webapps.stripe_integration.provider import StripeProvider

        payout_methods = StripeProvider.get_available_payout_methods(self.stripe_account)
        return StripePayoutMethodType.INSTANT in payout_methods

    @property
    def fast_payouts_available_today(self):
        from webapps.stripe_integration.models import StripePayout

        if KillSwitch.killed(KillSwitch.System.FAST_PAYOUTS_DAILY_LIMIT):
            return True

        business_ids = [self.business_id]

        if (
            StripePayout.objects.after_midnight(
                disable_weekend=False,
                business_ids=business_ids,
            )
            .filter(
                account_id=self.stripe_account.id,
                method=StripePayoutMethodType.INSTANT,
            )
            .exclude(
                status=StripePayoutStatus.FAILED,
            )
            .exists()
        ):
            return False
        return self.fast_payouts_available

    @property
    def new_fast_payouts_available_now(self):
        from webapps.stripe_integration.models import StripePayout

        # not available until provision_charge settlement
        if (
            StripePayout.objects.filter(
                payout_created__gte=tznow() - datetime.timedelta(hours=3),
                method=StripePayoutMethodType.INSTANT,
                provision_charged=False,
                account_id=self.stripe_account.id,
            )
            .exclude(
                status=StripePayoutStatus.FAILED,
            )
            .exists()
        ):
            return False
        return True

    @property
    def closest_fast_payout_available_date(self):
        today = datetime.datetime.now()
        if self.fast_payouts_available:
            if self.fast_payouts_available_today:
                return today
            return today + datetime.timedelta(days=1)
        return None

    def recalculate_pos_plans(self):
        recalculate_pos_plans(pos_ids=[self.pk])

    def get_pos_plan(self, plan_type: POSPlanPaymentTypeEnum) -> 'POSPlan':
        plan = self.pos_plans.filter(plan_type=plan_type).first()

        # pos should always have pos_plan of a given type assigned, if it doesnt,
        # pos was created incorrectly (not with create_pos), pos_plan was removed manually,
        # or there is an error in the code.
        if not plan:
            raise ValueError("This POS has no plan assigned (corrupted state)")
        return plan

    @property
    def force_stripe_pba(self):
        return pos_refactor_stage2_enabled(self) and self._force_stripe_pba

    def stripe_kyc_enabled(self):
        return self.force_stripe_pba or self.force_stripe_kyc

    @property
    def stage2_enabled(self):
        return pos_refactor_stage2_enabled(self)

    @property
    def has_stripe_account(self) -> bool:
        return self.stripe_accounts.exists()

    @cached_property
    def any_kyc_ever_passed(self) -> bool:
        """
        The property checks whether any KYC processes have been successfully completed.
        """
        from webapps.market_pay.models import AccountHolder
        from webapps.stripe_integration.models import StripeAccount

        if AccountHolder.objects.filter(
            pos=self,
            ever_passed_kyc=True,
        ).exists() or StripeAccount.objects.filter(
            pos=self,
            kyc_verified_at_least_once=True,
        ):
            return True
        return False

    @property
    def is_payout_method_change_allowed(self) -> bool:
        if settings.BUSINESS_GLOBAL_PAYOUT_METHOD_CHANGE_ALLOWED:  # global switch
            return True

        wallet: WalletEntity = PaymentGatewayPort.get_business_wallet(business_id=self.business_id)
        account_holder_entity: AccountHolderEntity = (
            PaymentProvidersAccountHolderPort.get_account_holder_info(
                account_holder_id=wallet.account_holder_id,
                payment_provider_code=PaymentProviderCode.STRIPE,
            ).entity
        )
        stripe_account_created = safe_get(
            account_holder_entity,
            ['providers', PaymentProviderCode.STRIPE, 'created'],
        )
        timedelta_allowed = PayoutMethodChangeAllowedTimedeltaFlag() or 0
        if stripe_account_created and tznow() - stripe_account_created < datetime.timedelta(
            hours=timedelta_allowed
        ):
            return True

        return self.payout_method_change_allowed

    @property
    def booksy_pay_late_cancellation_window_or_default(self):
        return self.booksy_pay_late_cancellation_window or relativedelta(days=8)


post_save.connect(POS.post_save_handler, POS)


class POSPlan(ArchiveModel):
    min_txn_num = models.IntegerField(default=0)
    provision = models.FloatField(
        help_text='Value is decimal number -  0.0269 means 2.69%',
    )
    txn_fee = models.FloatField(
        help_text='Value is decimal number in local currency',
    )
    individual = models.BooleanField(default=False)
    deprecated_donation_priority = models.BooleanField(default=False)

    refund_provision = models.FloatField(default=0)
    refund_txn_fee = models.FloatField(default=0)

    chargeback_provision = models.FloatField(default=0)
    chargeback_txn_fee = models.FloatField(default=0)

    plan_type = models.CharField(
        choices=POSPlanPaymentTypeEnum.choices(),
        max_length=50,
        default=POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT,
    )

    PLAN_TYPE_MAP = {
        POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT: (
            PaymentProviderEnum.ADYEN_PROVIDER,
            [
                PaymentTypeEnum.PAY_BY_APP,
                PaymentTypeEnum.PREPAYMENT,
            ],
        ),
        POSPlanPaymentTypeEnum.ADYEN_DONATIONS: (
            PaymentProviderEnum.ADYEN_PROVIDER,
            [
                PaymentTypeEnum.PAY_BY_APP_DONATIONS,
            ],
        ),
        POSPlanPaymentTypeEnum.STRIPE_MOBILE_PAYMENT: (
            PaymentProviderEnum.STRIPE_PROVIDER,
            [
                PaymentTypeEnum.PAY_BY_APP,
                PaymentTypeEnum.PREPAYMENT,
            ],
        ),
        POSPlanPaymentTypeEnum.STRIPE_TERMINAL: (
            PaymentProviderEnum.STRIPE_PROVIDER,
            [
                PaymentTypeEnum.STRIPE_TERMINAL,
            ],
        ),
        POSPlanPaymentTypeEnum.FAST_PAYOUT: (
            PaymentProviderEnum.STRIPE_PROVIDER,
            [PaymentTypeEnum.STRIPE_TERMINAL],
        ),
        POSPlanPaymentTypeEnum.TAP_TO_PAY: (
            PaymentProviderEnum.STRIPE_PROVIDER,
            [PaymentTypeEnum.TAP_TO_PAY],
        ),
        POSPlanPaymentTypeEnum.BOOKSY_PAY: (
            PaymentProviderEnum.STRIPE_PROVIDER,
            [
                PaymentTypeEnum.BOOKSY_PAY,
            ],
        ),
    }

    class Meta:
        ordering = ['-min_txn_num']

    def __str__(self):
        plan = 'individual' if self.individual else 'global'
        min_txn = self.min_txn_num
        provision = self.provision
        fee = self.txn_fee
        return f'''
            POSPlan {plan} (id={self.id}) min_txn: {min_txn}
             | provision: {provision} | txn_fee: {fee}
        '''

    def save(self, *args, **kwargs):
        self.validate_non_individual_unique(self)
        super().save(*args, **kwargs)

    @classmethod
    def validate_non_individual_unique(cls, instance=None, **kwargs):
        """
        Validate the Global POSPlan with min_txn_num is unique.

        It should allow to update existing POSPlan.

        :param instance: POSPlan instance if any
        :param kwargs: new attribute values for instance
        :raises: ValidationError if instance should not be saved
        """
        individual = kwargs.get('individual')
        min_txn_num = kwargs.get('min_txn_num')
        plan_type = kwargs.get('plan_type')

        if instance is not None:
            if individual is None:
                individual = instance.individual
            if min_txn_num is None:
                min_txn_num = instance.min_txn_num
            if plan_type is None:
                plan_type = instance.plan_type

        if individual:
            return

        qs = POSPlan.objects.filter(min_txn_num=min_txn_num, individual=False, plan_type=plan_type)

        if instance is not None and instance.pk:
            qs = qs.exclude(pk=instance.pk)

        if qs.exists():
            raise ValidationError('This plan is not unique')

    @classmethod
    def smart_create(cls, params):
        fields = [field.name for field in cls._meta.fields]
        keys_to_remove = set(params.keys()) - set(fields)
        cleared = {k: params[k] for k in params if k not in keys_to_remove}
        return POSPlan.objects.create(**cleared)

    @classmethod
    def get_donation_plan(cls) -> 'POSPlan':
        """Gets POSPlan dedicated for donations."""
        return cls.objects.filter(plan_type=POSPlanPaymentTypeEnum.ADYEN_DONATIONS).last()


class Tip(ArchiveModel):
    id = models.AutoField(primary_key=True, db_column='tip_id')
    pos = models.ForeignKey(
        POS,
        related_name='tips',
        on_delete=models.CASCADE,
    )
    rate = models.PositiveSmallIntegerField(
        validators=[
            validators.MinValueValidator(0),
            validators.MaxValueValidator(100),
        ]
    )
    default = models.BooleanField(null=False, blank=False, default=False)

    class Meta:
        ordering = ['rate']
        unique_together = ('pos', 'rate')


class TaxRate(ArchiveModel):
    id = models.AutoField(primary_key=True, db_column='taxrate_id')
    pos = models.ForeignKey(
        POS,
        related_name='tax_rates',
        on_delete=models.CASCADE,
    )
    # blank rate (No tax) is not the same as rate 0%

    rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True,
        validators=[
            validators.MinValueValidator(0),
            validators.MaxValueValidator(100),
        ],
    )

    default_for_service = models.BooleanField(default=False)
    default_for_product = models.BooleanField(default=False)

    class Meta:
        ordering = ['-rate']
        unique_together = ('pos', 'rate')

    def __str__(self):
        default_for_service = ' default_for_service' if self.default_for_service else ''
        default_for_product = ' default_for_product' if self.default_for_product else ''
        return f'TaxRate {self.rate}{default_for_service}{default_for_product}'

    @property
    def label(self):
        return self.label_for_rate(self.rate)

    @staticmethod
    def label_for_rate(rate, tax_mode=None):
        if rate is None or rate <= Decimal(0):
            return force_unicode(_('Tax Free'))
        formatted_rate = f'{rate:.2f}'.replace('.00', '')
        formatted_tax_type = (
            force_unicode(_('excl.'))
            if tax_mode == POS.POS_TAX_MODE__EXCLUDED
            else force_unicode(_('incl.')) if tax_mode == POS.POS_TAX_MODE__INCLUDED else ''  # None
        )
        tax_type = (' ' + formatted_tax_type).rstrip()
        return f'{force_unicode(_("Tax"))} ({formatted_rate}%{tax_type})'

    @property
    def history_name(self):
        return self.label


class PaymentType(ArchiveModel):
    DEFAULT_PAYMENT_ROW_STATUS = {
        PaymentTypeEnum.BOOKSY_GIFT_CARD: receipt_status.GIFT_CARD_DEPOSIT,
        PaymentTypeEnum.PAY_BY_APP: receipt_status.CALL_FOR_PAYMENT,
        PaymentTypeEnum.PAY_BY_APP_DONATIONS: receipt_status.CALL_FOR_PAYMENT,
        PaymentTypeEnum.SQUARE: receipt_status.PENDING,
        PaymentTypeEnum.PREPAYMENT: receipt_status.CALL_FOR_PREPAYMENT,
        PaymentTypeEnum.STRIPE_TERMINAL: receipt_status.PENDING,
        PaymentTypeEnum.TAP_TO_PAY: receipt_status.PENDING,
        PaymentTypeEnum.BOOKSY_PAY: receipt_status.CALL_FOR_BOOKSY_PAY,
        PaymentTypeEnum.KEYED_IN_PAYMENT: receipt_status.PENDING,
    }

    UNDELETABLE_TYPES = [
        PaymentTypeEnum.CASH,
        PaymentTypeEnum.PREPAYMENT,
        PaymentTypeEnum.SPLIT,
    ]

    PERMANENTLY_ENABLED_TYPES = [
        PaymentTypeEnum.CASH,
        PaymentTypeEnum.BOOKSY_GIFT_CARD,
    ]

    FISCAL_TYPES = [
        PaymentTypeEnum.CASH,
        PaymentTypeEnum.CREDIT_CARD,
        PaymentTypeEnum.BANK_TRANSFER,
        PaymentTypeEnum.AMERICAN_EXPRESS,
        PaymentTypeEnum.PREPAYMENT,
        PaymentTypeEnum.PAY_BY_APP,
        PaymentTypeEnum.BOOKSY_PAY,
    ]
    VOUCHER_TYPES = [
        PaymentTypeEnum.EGIFT_CARD,
        PaymentTypeEnum.MEMBERSHIP,
        PaymentTypeEnum.PACKAGE,
    ]
    OLD_STYLE_VOUCHERS = [
        PaymentTypeEnum.GIFT_CARD,
        PaymentTypeEnum.VOUCHER,
        PaymentTypeEnum.SUBSCRIPTION,
    ]
    DEPRECATED_TYPES = OLD_STYLE_VOUCHERS

    id = models.AutoField(primary_key=True, db_column='payment_type_id')
    pos = models.ForeignKey(
        POS,
        related_name='payment_types',
        on_delete=models.PROTECT,
    )
    order = models.PositiveSmallIntegerField(default=0)
    # in fact code duplicate id; it was introduced to have payment_type
    # in human readable form easy to understand for instance in logs
    code = models.CharField(max_length=16, choices=PaymentTypeEnum.choices())
    default = models.BooleanField(blank=False, default=False)
    enabled = models.BooleanField(blank=True, default=True)
    # field will be used to hide payment method for specific business.
    # available is not related to enabled field.
    # It is just impossible to select this payment method in checkout anymore
    # but it is possible to process payment created with this payment type
    available = models.BooleanField(blank=True, default=True)

    objects = ArchiveManager()
    all_objects = models.Manager()

    class Meta:
        ordering = ('order',)
        unique_together = ('pos', 'code')

    @property
    def label(self):
        return self.get_code_display()

    @property
    def pay_by_app(self):
        return self.code == PaymentTypeEnum.PAY_BY_APP

    @property
    def split(self):
        return self.code == PaymentTypeEnum.SPLIT

    @property
    def stripe_terminal(self):
        return self.code == PaymentTypeEnum.STRIPE_TERMINAL

    @property
    def tap_to_pay(self):
        return self.code == PaymentTypeEnum.TAP_TO_PAY

    @property
    def callback_requiring(self):
        """Logic determining if selected code should wait for callback."""
        callback_requiring = [
            PaymentTypeEnum.PAY_BY_APP,
            PaymentTypeEnum.SQUARE,
            PaymentTypeEnum.PREPAYMENT,
            PaymentTypeEnum.PAY_BY_APP_DONATIONS,
            PaymentTypeEnum.BOOKSY_PAY,
            PaymentTypeEnum.BLIK,
        ]
        return self.code in callback_requiring

    @property
    def forbidden_in_free_transaction(self):
        return self.callback_requiring or self.stripe_terminal

    @property
    def should_lock(self):
        """Logic determining if related transaction, payment_row, receipt etc. should be editable"""
        return self.callback_requiring or self.stripe_terminal

    @property
    def multi_usage(self):
        """Logic determining if selected code can be used multiple times."""
        multi_usage = [
            PaymentTypeEnum.MEMBERSHIP,
            # TODO in future
            # PaymentTypeEnum.EGIFT_CARD,
            PaymentTypeEnum.PACKAGE,
        ]
        return self.code in multi_usage

    @property
    def forbidden_in_checkout(self):
        forbidden_in_checkout = [
            PaymentTypeEnum.PREPAYMENT,
            PaymentTypeEnum.PAY_BY_APP_DONATIONS,
            PaymentTypeEnum.DIRECT_PAYMENT,
            PaymentTypeEnum.BOOKSY_PAY,
        ]
        return self.code in forbidden_in_checkout

    @property
    def editable(self):
        """It means that selected PaymentType can be enabled/disabled."""
        is_permanently_enabled = self.code not in self.PERMANENTLY_ENABLED_TYPES
        return is_permanently_enabled or not self.available

    @property
    def default_status(self):
        return self.DEFAULT_PAYMENT_ROW_STATUS.get(self.code, receipt_status.PAYMENT_SUCCESS)

    @property
    def compatibility_requiring(self):
        compatibilities_requiring = [
            PaymentTypeEnum.SPLIT,
            PaymentTypeEnum.SQUARE,
            PaymentTypeEnum.PREPAYMENT,
            PaymentTypeEnum.EGIFT_CARD,
            PaymentTypeEnum.MEMBERSHIP,
            PaymentTypeEnum.PACKAGE,
        ]
        return self.code in compatibilities_requiring

    @staticmethod
    def _add_payment_type(pt_code):
        poses_already_with_pt = PaymentType.all_objects.filter(code=pt_code).values_list(
            'pos__id', flat=True
        )
        poses_ids = POS.objects.exclude(id__in=poses_already_with_pt).values_list('id', flat=True)
        order_nums = {
            # PaymentTypeEnum.VOUCHER: 5,
            # PaymentTypeEnum.GIFT_CARD: 6,
            PaymentTypeEnum.MEMBERSHIP: 11,
            PaymentTypeEnum.EGIFT_CARD: 12,
            PaymentTypeEnum.PACKAGE: 13,
        }

        PaymentType.objects.bulk_create(
            [
                PaymentType(
                    pos_id=pos_id,
                    code=pt_code,
                    enabled=True,
                    order=order_nums.get(pt_code, 11),
                )
                for pos_id in poses_ids
            ]
        )

    @staticmethod
    def _update_new_vouchers(deleted: bool):
        PaymentType.all_objects.filter(
            code__in=PaymentType.VOUCHER_TYPES,
        ).update(deleted=tznow() if deleted else None)

    @staticmethod
    def _update_old_vouchers(deleted: bool):
        PaymentType.all_objects.filter(
            code__in=PaymentType.OLD_STYLE_VOUCHERS,
        ).update(deleted=tznow() if deleted else None)

    @classmethod
    @db_transaction.atomic
    def enable_new_vouchers(cls):
        cls._update_new_vouchers(deleted=False)
        cls._add_payment_type(PaymentTypeEnum.EGIFT_CARD)
        cls._add_payment_type(PaymentTypeEnum.MEMBERSHIP)
        cls._add_payment_type(PaymentTypeEnum.PACKAGE)

    @classmethod
    @db_transaction.atomic
    def disable_new_vouchers(cls):
        cls._update_new_vouchers(deleted=True)

    def __str__(self):
        return force_str(self.label)

    def delete(self, using=None, keep_parents=False):
        """NEVER delete - use soft delete."""
        if self.code in self.UNDELETABLE_TYPES:
            # 'cash', 'prepayment' and 'split' are
            # undeletable (not even soft delete)
            return
        self.soft_delete()

        if self.code == PaymentTypeEnum.PAY_BY_APP:
            self.pos.update_disable_pba()

    def save(
        self, force_insert=False, force_update=False, using=None, update_fields=None, **kwargs
    ):
        # check if there is an existing softly deleted object
        if self.id is None:
            softly_deleted = PaymentType.all_objects.filter(
                pos=self.pos,
                code=self.code,
                deleted__isnull=False,
            ).first()
            if softly_deleted:
                # reuse it's ID to resurrect it
                self.id = softly_deleted.id  # pylint: disable=invalid-name
                self.created = softly_deleted.created
        # now we can create or update
        super().save(
            force_insert=force_insert,
            force_update=force_update,
            using=using,
            update_fields=update_fields,
        )
        if self.code == PaymentTypeEnum.PAY_BY_APP:
            if self.deleted is None:
                self.pos.update_enable_pba()
            else:
                self.pos.disable_pay_by_app()

    @cached_property
    def status(self) -> Optional[str]:
        # pylint: disable=line-too-long, too-many-return-statements
        if not (self.tap_to_pay or self.pay_by_app):
            return None
        if not PaymentTypeStatusEnabled():
            return None

        # check status of stripe account
        wallet_entity: WalletEntity = PaymentGatewayPort.get_business_wallet(
            business_id=self.pos.business_id,
        )
        details = PaymentProvidersAccountHolderPort.get_provider_account_status(
            account_holder_id=wallet_entity.account_holder_id,
            payment_provider_code=PaymentProviderCode.STRIPE,
        ).entity
        stripe_status = safe_get(details, ['status'])
        if not details or stripe_status == ProviderAccountHolderStatus.TURNED_OFF:
            return PaymentTypeStatus.TURNED_OFF
        if stripe_status == ProviderAccountHolderStatus.NOT_VERIFIED:
            return PaymentTypeStatus.NOT_VERIFIED
        if stripe_status == ProviderAccountHolderStatus.VERIFICATION_PENDING:
            return PaymentTypeStatus.VERIFICATION_PENDING

        # stripe account is verified, now check fees
        account_holder_settings_entity: AccountHolderSettingsData = (
            PaymentProvidersAccountHolderPort.get_account_holder_settings(
                account_holder_id=wallet_entity.account_holder_id,
                payment_provider_code=PaymentProviderCode.STRIPE,
            ).entity
        )
        if self.tap_to_pay:
            fees_accepted = account_holder_settings_entity.stripe.tap_to_pay_fees_accepted
        else:
            fees_accepted = account_holder_settings_entity.stripe.pba_fees_accepted

        if not fees_accepted:
            return PaymentTypeStatus.FEES_NOT_ACCEPTED
        return PaymentTypeStatus.VERIFIED


class POSChangeLog(UndeletableMixin, models.Model):  # pylint: disable=abstract-method
    pos = models.ForeignKey(
        POS,
        related_name='change_logs',
        on_delete=models.CASCADE,
    )
    created = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Created (UTC)',
    )
    operator = models.ForeignKey(
        User,
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
        db_constraint=False,
    )
    data = models.TextField()

    class Meta:
        ordering = ('-created',)

    def __str__(self):
        return f'operator_id={self.operator_id} {self.created}'

    @staticmethod
    def create_bulk_history(changes, operator_id):
        """
        Create bulk POSChange logs
        :param changes: Serialized poses. POSChangeLog
        :param operator_id: int. valid user id
        :return: None
        """

        POSChangeLog.objects.bulk_create(
            [
                POSChangeLog(
                    pos_id=pos['id'],
                    data=json.dumps(pos),
                    operator_id=operator_id,
                )
                for pos in changes
            ]
        )

    @cached_property
    def previous(self):
        return POSChangeLog.objects.filter(pos_id=self.pos_id, created__lt=self.created).first()

    @property
    def diff(self):
        """Current data diff against previous data; used in admin"""
        previous = self.previous
        if not previous or not previous.data:
            return 'no previous settings'

        def format_data(data):
            if not data:
                return ''

            decoded_data = json.loads(data)

            if not hasattr(decoded_data, 'items'):
                return data

            return json.dumps(
                {k: json.dumps(v) for k, v in list(decoded_data.items())},
                indent=True,
                sort_keys=True,
            )

        try:
            prev_data = format_data(previous.data)
            curr_data = format_data(self.data)

        except ValueError:
            return 'data error'

        differ = Differ()
        _diff = differ.compare(prev_data.split('\n'), curr_data.split('\n'))

        return '\n'.join((line for line in _diff if line[0] in '+-'))


# adref from verticals
def base(number, radix, width=1):
    """inverse function to int(str,radix) and long(str,radix)"""
    try:
        # python2
        letters = string.letters[len(string.letters) / 2 :]
    except (TypeError, AttributeError):
        # python3
        letters = string.ascii_uppercase

    abc = string.digits + letters

    if not 2 <= radix <= 36:
        raise ValueError("radix must be in 2..36")

    result = []
    if number < 0:
        number *= -1
        result.append('-')

    while number:
        number, rdigit = divmod(number, radix)
        result.append(abc[rdigit])
    result.append("0" * (width - len(result)))

    result.reverse()
    return ''.join(result)


class Receipt(UndeletableMixin, ArchiveModel):  # pylint: disable=abstract-method
    NUMBER_RE = re.compile('^[A-Z]{2}-[A-Z0-9]{3}-[A-Z0-9]{3}$')

    RECEIPT_STATUSES = (
        (receipt_status.CALL_FOR_PAYMENT, _('Call for Payment')),
        (receipt_status.CALL_FOR_PAYMENT_3DS, _('Payment 3DS required')),
        (receipt_status.PENDING, _('Pending')),
        (receipt_status.PARK_SALE, _('Park sale')),
        # Prepayment
        (receipt_status.CALL_FOR_PREPAYMENT, _('Call for deposit')),
        (receipt_status.CALL_FOR_PREPAYMENT_3DS, _('Deposit 3DS required')),
        (receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS, _('Deposit verification completed')),
        (receipt_status.PREPAYMENT_AUTHORISATION_FAILED, _('Deposit verification failed')),
        (receipt_status.PREPAYMENT_SUCCESS, _('Deposit success')),
        (receipt_status.PREPAYMENT_FAILED, _('Deposit failed')),
        # Voucher Partial Payments
        (receipt_status.VOUCHER_PARTIAL_REDEEM, _('Voucher partial redeem')),
        # Payments
        (receipt_status.PAYMENT_AWAITING, _('Payment Awaiting')),
        (receipt_status.PAYMENT_SUCCESS, _('Payment Completed')),
        (receipt_status.PAYMENT_FAILED, _('Payment Failed')),
        (receipt_status.PAYMENT_CANCELED, _('Payment Cancelled')),
        # Deposit / Cancellation Fee
        (receipt_status.CALL_FOR_DEPOSIT, _('Call for cancellation fee')),
        (receipt_status.CALL_FOR_DEPOSIT_3DS, _('Cancellation fee 3DS required')),
        (
            receipt_status.DEPOSIT_AUTHORISATION_AWAITING,
            _('Cancellation fee verification awaiting'),
        ),
        (
            receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
            _('Cancellation fee verification completed'),
        ),
        (receipt_status.DEPOSIT_AUTHORISATION_FAILED, _('Cancellation fee verification failed')),
        (receipt_status.DEPOSIT_CHARGE_AWAITING, _('Cancellation fee charge awaiting')),
        (receipt_status.DEPOSIT_CHARGE_SUCCESS, _('Cancellation fee charge completed')),
        (receipt_status.DEPOSIT_CHARGE_FAILED, _('Cancellation fee charge failed')),
        (receipt_status.DEPOSIT_CHARGE_CANCELED, _('Cancellation fee charge removed')),
        (receipt_status.DEPOSIT_CANCEL_AWAITING, _('Cancellation fee pending removal')),
        (receipt_status.DEPOSIT_CANCEL_FAILED, _('Cancellation fee removal failed')),
        (receipt_status.ARCHIVED, _('Payment archived')),
        (receipt_status.GIFT_CARD_DEPOSIT, _('Booksy Gift Card Deposit')),
        # Refunds
        (receipt_status.SENT_FOR_REFUND, _('Sent for refund')),
        (receipt_status.REFUNDED, _('Refunded')),
        (receipt_status.CHARGEBACK, _('Charged back')),
        (receipt_status.CHARGEBACK_REVERSED, _('Charged back reversed')),
        (receipt_status.SECOND_CHARGEBACK, _('Charged back again')),
        # Booksy Pay
        (receipt_status.CALL_FOR_BOOKSY_PAY, _('Call for Booksy Pay')),
        (receipt_status.CALL_FOR_BOOKSY_PAY_3DS, _('Booksy Pay 3DS required')),
        (receipt_status.BOOKSY_PAY_SUCCESS, _('Booksy Pay Success')),
        (receipt_status.BOOKSY_PAY_FAILED, _('Booksy Pay Failed')),
    )

    id = models.AutoField(primary_key=True, db_column='receipt_id')
    transaction = models.ForeignKey(
        'Transaction',
        related_name='receipts',
        on_delete=models.PROTECT,
    )
    receipt_number = models.CharField(max_length=128)
    status_code = models.CharField(max_length=1, choices=RECEIPT_STATUSES)
    payment_type = models.ForeignKey(
        PaymentType,
        related_name='receipts',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    card_type = models.CharField(max_length=64, null=True, blank=True, choices=enums.CARD_TYPES)
    card_last_digits = models.CharField(max_length=4, null=True, blank=True)
    pnref = models.CharField(
        max_length=128,
        null=True,
        blank=True,
    )
    provider = models.CharField(
        null=True, blank=True, max_length=16, choices=PAYMENT_PROVIDER_CHOICES
    )
    already_paid = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)

    class Meta:
        ordering = ('-created',)
        verbose_name = _('Receipt')
        verbose_name_plural = _('Receipts')

        indexes = [
            models.Index(fields=['status_code', 'created'], name='rcpt_rows_status_created_idx')
        ]

    all_objects = SoftDeleteManager()

    def __str__(self):
        return f'Receipt [{self.receipt_number}] status_code={self.status_code}'

    def save(
        self, force_insert=False, force_update=False, using=None, update_fields=None, **kwargs
    ):
        if self.id:
            # pylint: disable=broad-exception-raised
            raise Exception('Saved Receipt should not be updated')

        super().save(**kwargs)

        if not self.receipt_number:
            self.receipt_number = self.id_to_receipt_number(self.id)
            super().save(update_fields=['receipt_number'])

    @property
    def status_type(self):
        return receipt_status.STATUS_TYPES_REVERSE.get(self.status_code, '')

    def get_status_type_display(self):
        return receipt_status.STATUS_TYPES_DISPLAY.get(self.status_type, '')

    @classmethod
    def id_to_receipt_number(cls, id_):
        num = base(id_, 36, 6)
        country = settings.API_COUNTRY
        return '-'.join([country, num[:3], num[3:]]).upper()

    @classmethod
    def receipt_number_to_id(cls, num):
        if not num or not cls.NUMBER_RE.match(num[:10]):
            raise ValueError('Not a valid receipt number')
        return int(num[2:10].replace('-', ''), 36)

    @property
    def card_info(self):
        return (
            (f'{self.get_card_type_display()} ****{self.card_last_digits}')
            if self.card_last_digits
            else ''
        )

    @property
    def total(self):
        return self.transaction.total

    @property
    def remaining(self):
        total = self.total or Decimal(0)
        already_paid = self.already_paid or Decimal(0)
        return round_currency(total - already_paid)

    @property
    def assigned_number(self):
        from webapps.sequencing_number.models import SequenceRecord

        business = self.transaction.pos.business
        txn_id = self.transaction.id
        business_document = SequenceRecord.objects.filter(
            business=business,
            type=SALES_DOCUMENT,
            related_document_id=txn_id,
        ).first()
        if business_document:
            return business_document.assigned_number
        return self.receipt_number


class PaymentRowMidnightQuerySet(MidnightQuerySet):
    tz_field = 'receipt__transaction__pos__business__time_zone_name'
    dt_field = 'created'


class PaymentRowQuerySet(AutoUpdateQuerySet, MidnightQuerySet):
    def get_dwolla_record(self, add_settled_param=False, exclude_marketpay=None, donations=False):
        allowed_statuses = [
            POS.FRAUD_STATUS_WHITELIST,
            POS.FRAUD_STATUS_UNKNOWN,
        ]

        if donations:
            part_filers = Q(
                Q(
                    status__in=[
                        receipt_status.PAYMENT_SUCCESS,
                        receipt_status.DEPOSIT_CHARGE_SUCCESS,
                    ]
                )
                & Q(payment_type__code=PaymentTypeEnum.PAY_BY_APP_DONATIONS)
            )
        else:
            part_filers = Q(
                (
                    Q(
                        status__in=[
                            receipt_status.PAYMENT_SUCCESS,
                            receipt_status.DEPOSIT_CHARGE_SUCCESS,
                        ]
                    )
                    & Q(
                        payment_type__code__in=[
                            PaymentTypeEnum.PAY_BY_APP,
                            PaymentTypeEnum.PAY_BY_APP_DONATIONS,
                        ]
                    )
                )
                | (
                    Q(status=receipt_status.PREPAYMENT_SUCCESS)
                    & Q(payment_type__code=PaymentTypeEnum.PREPAYMENT)
                )
            )

        filters = [
            Q(receipt__transaction__pos__fraud_status__in=allowed_statuses),
            part_filers,
            Q(
                pnref__in=(
                    Capture.objects.filter(
                        oper_result__in=[
                            adyen_oper_result.SUCCESS,
                            adyen_oper_result.PENDING,
                        ]
                    ).values_list('reference', flat=True)
                )
            ),
        ]

        if add_settled_param:
            filters.append(Q(settled=False))
            if exclude_marketpay is None:
                exclude_marketpay = True

        if exclude_marketpay:
            filters.append(Q(marketpay_splits__isnull=True))

        prs = (
            self.filter(*filters)
            .select_related('payment_type')
            .values('id', 'pnref')
            .order_by('pnref')
            .distinct('pnref')
            .values_list('id', flat=True)
        )

        return self.filter(id__in=prs)

    def get_marketpay_record(self, add_settled_param=False):
        filters = [
            Q(marketpay_splits__isnull=False),
            (
                (
                    (
                        Q(
                            status__in=[
                                receipt_status.PAYMENT_SUCCESS,
                                receipt_status.DEPOSIT_CHARGE_SUCCESS,
                                receipt_status.SENT_FOR_REFUND,
                                # receipt_status.REFUNDED,
                                receipt_status.CHARGEBACK,
                            ]
                        )
                        & Q(
                            payment_type__code__in=[
                                PaymentTypeEnum.PAY_BY_APP,
                                PaymentTypeEnum.PAY_BY_APP_DONATIONS,
                            ]
                        )
                    )
                    | (
                        Q(
                            status__in=[
                                receipt_status.PREPAYMENT_SUCCESS,
                                receipt_status.SENT_FOR_REFUND,
                                # receipt_status.REFUNDED,
                                receipt_status.CHARGEBACK,
                            ]
                        )
                        & Q(payment_type__code=PaymentTypeEnum.PREPAYMENT)
                    )
                )
                & Q(
                    pnref__in=(
                        Capture.objects.filter(
                            oper_result__in=[
                                adyen_oper_result.SUCCESS,
                                adyen_oper_result.PENDING,
                            ]
                        ).values_list('reference', flat=True)
                    )
                )
            )
            | Q(
                status=receipt_status.SENT_FOR_REFUND,
            ),
        ]

        if add_settled_param:
            filters.append(Q(settled=False))

        prs = self.filter(*filters)
        # if add_settled_param:
        #     prs = prs.exclude(marketpay_payouts__failed=False)
        prs = (
            prs.select_related('payment_type')  # pylint: disable=not-callable
            .values('id', 'pnref')
            .order_by('pnref')
            .distinct('pnref')
            .values_list('id', flat=True)
        )

        return self.filter(id__in=prs)

    def get_online_payments(self):
        allowed_statuses = [
            POS.FRAUD_STATUS_WHITELIST,
            POS.FRAUD_STATUS_UNKNOWN,
        ]

        filters = [
            Q(receipt__transaction__pos__fraud_status__in=allowed_statuses),
            Q(
                (
                    Q(
                        status__in=[
                            receipt_status.PAYMENT_SUCCESS,
                            receipt_status.DEPOSIT_CHARGE_SUCCESS,
                        ]
                    )
                    & Q(
                        payment_type__code__in=[
                            PaymentTypeEnum.PAY_BY_APP,
                            PaymentTypeEnum.PAY_BY_APP_DONATIONS,
                        ]
                    )
                )
                | (
                    Q(status=receipt_status.PREPAYMENT_SUCCESS)
                    & Q(payment_type__code=PaymentTypeEnum.PREPAYMENT)
                )
                | (
                    Q(status=receipt_status.BOOKSY_PAY_SUCCESS)
                    & Q(payment_type__code=PaymentTypeEnum.BOOKSY_PAY)
                )
                # HERE ADD STRIPE
            ),
            Q(
                pnref__in=(
                    Capture.objects.filter(
                        oper_result__in=[
                            adyen_oper_result.SUCCESS,
                            adyen_oper_result.PENDING,
                        ]
                    ).values_list('reference', flat=True)
                )
            ),
        ]

        prs = (
            self.filter(*filters)
            .select_related('payment_type')
            .values('id', 'pnref')
            .order_by('pnref')
            .distinct('pnref')
            .values_list('id', flat=True)
        )

        return self.filter(id__in=prs)


class PaymentRowChange(models.Model):

    CREATE_TRANSACTION = 'C'
    SWITCH_SETTLED_ON = 'S'
    SWITCH_SETTLED_OFF = 'O'
    SENT_FOR_REFUND = 'R'
    SINGLE_ROW_UPDATE = 'U'
    MULTI_ROW_UPDATE = 'A'
    REFUND_REQUESTED = 'Q'

    REASON_CHOICES = (
        (CREATE_TRANSACTION, _('Create transaction')),
        (SINGLE_ROW_UPDATE, _('Single row update')),
        (MULTI_ROW_UPDATE, _('Multi row update')),
        (SWITCH_SETTLED_ON, _('Switch settled state - ON')),
        (SWITCH_SETTLED_OFF, _('Switch settled state - OFF')),
        (SENT_FOR_REFUND, _('Sent for refund')),
        (REFUND_REQUESTED, _('Refund requested')),
    )

    created = models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')
    payment_row = models.ForeignKey(
        'PaymentRow',
        on_delete=models.PROTECT,
        related_name='changes',
    )
    reason = models.CharField(max_length=1, null=True, blank=True, choices=REASON_CHOICES)
    operator = models.ForeignKey(
        User,
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
        db_constraint=False,
    )
    metadata = models.TextField(blank=True, null=True)

    class Meta:
        ordering = ('-id',)
        verbose_name = _('Payment row change')
        verbose_name_plural = _('Payment rows change')

    def __str__(self):
        return f'PR Change: {self.payment_row.id} - {self.get_reason_display()} {self.operator}'

    @classmethod
    def add(cls, *args, **kwargs):
        pr = cls(**kwargs)
        pr.save()


class InvalidPaymentRowUpdateError(Exception):
    pass


class PaymentRow(UndeletableMixin, ArchiveModel):  # pylint: disable=abstract-method
    ALLOWED_STATUS_TRANSITION = {
        receipt_status.CALL_FOR_PAYMENT: [
            receipt_status.PAYMENT_SUCCESS,
            receipt_status.PAYMENT_FAILED,
            receipt_status.CALL_FOR_PAYMENT_3DS,
            # receipt_status.PAYMENT_CANCELED,  # Possible only with ALL_BY_ONE
        ],
        receipt_status.CALL_FOR_PAYMENT_3DS: [
            receipt_status.PAYMENT_SUCCESS,
            receipt_status.PAYMENT_FAILED,
        ],
        receipt_status.PENDING: [
            receipt_status.PAYMENT_SUCCESS,
            receipt_status.PAYMENT_FAILED,
            # receipt_status.PAYMENT_CANCELED, # Possible only with ALL_BY_ONE
        ],
        receipt_status.PAYMENT_FAILED: [  # Case of retrying transaction via SQUARE
            receipt_status.PAYMENT_FAILED,
            receipt_status.PAYMENT_SUCCESS,
        ],
        receipt_status.PAYMENT_SUCCESS: {
            receipt_status.SENT_FOR_REFUND,
            receipt_status.CHARGEBACK,
        },
        receipt_status.SENT_FOR_REFUND: {
            receipt_status.REFUNDED,
            receipt_status.PAYMENT_SUCCESS,
            receipt_status.PREPAYMENT_SUCCESS,
            receipt_status.BOOKSY_PAY_SUCCESS,
        },
        receipt_status.REFUNDED: {
            receipt_status.REFUNDED,
        },
        receipt_status.CHARGEBACK: {
            receipt_status.PAYMENT_SUCCESS,
            receipt_status.PREPAYMENT_SUCCESS,
            receipt_status.CHARGEBACK_REVERSED,
            receipt_status.BOOKSY_PAY_SUCCESS,
        },
        receipt_status.CHARGEBACK_REVERSED: {
            receipt_status.PAYMENT_SUCCESS,
            receipt_status.PREPAYMENT_SUCCESS,
            receipt_status.SECOND_CHARGEBACK,
        },
        # PREPAYMENTS
        receipt_status.CALL_FOR_PREPAYMENT: [
            # For businesses with semiauto
            receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS,
            receipt_status.PREPAYMENT_AUTHORISATION_FAILED,
            receipt_status.CALL_FOR_PREPAYMENT_3DS,
            # For 'normal' businesses
            receipt_status.PREPAYMENT_SUCCESS,
            receipt_status.PREPAYMENT_FAILED,
        ],
        receipt_status.CALL_FOR_PREPAYMENT_3DS: [
            receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS,
            receipt_status.PREPAYMENT_AUTHORISATION_FAILED,
            receipt_status.PREPAYMENT_SUCCESS,
            receipt_status.PREPAYMENT_FAILED,
        ],
        receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS: [
            receipt_status.PREPAYMENT_SUCCESS,
            receipt_status.PREPAYMENT_FAILED,
            receipt_status.DEPOSIT_CHARGE_CANCELED,
        ],
        receipt_status.PREPAYMENT_SUCCESS: [
            receipt_status.PAYMENT_SUCCESS,
            receipt_status.SENT_FOR_REFUND,
            receipt_status.CHARGEBACK,
            receipt_status.PARK_SALE,
        ],
        receipt_status.VOUCHER_PARTIAL_REDEEM: [
            receipt_status.PAYMENT_SUCCESS,
        ],
        # DEPOSIT
        receipt_status.CALL_FOR_DEPOSIT: [
            receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
            receipt_status.DEPOSIT_AUTHORISATION_FAILED,
            receipt_status.CALL_FOR_DEPOSIT_3DS,
        ],
        receipt_status.CALL_FOR_DEPOSIT_3DS: [
            receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
            receipt_status.DEPOSIT_AUTHORISATION_FAILED,
        ],
        receipt_status.DEPOSIT_AUTHORISATION_SUCCESS: [
            receipt_status.DEPOSIT_CHARGE_SUCCESS,
            receipt_status.DEPOSIT_CHARGE_FAILED,
            receipt_status.DEPOSIT_CHARGE_CANCELED,
            receipt_status.DEPOSIT_CANCEL_FAILED,
        ],
        receipt_status.DEPOSIT_CHARGE_FAILED: [
            receipt_status.DEPOSIT_CHARGE_SUCCESS,
            receipt_status.DEPOSIT_CHARGE_FAILED,
            receipt_status.DEPOSIT_CHARGE_CANCELED,
        ],
        receipt_status.DEPOSIT_CHARGE_SUCCESS: [
            receipt_status.SENT_FOR_REFUND,
            receipt_status.CHARGEBACK,
        ],
        receipt_status.GIFT_CARD_DEPOSIT: [
            receipt_status.PAYMENT_SUCCESS,
            receipt_status.PAYMENT_FAILED,
            receipt_status.PAYMENT_CANCELED,
        ],
        # Booksy Pay
        receipt_status.CALL_FOR_BOOKSY_PAY: [
            receipt_status.CALL_FOR_BOOKSY_PAY_3DS,
            receipt_status.BOOKSY_PAY_SUCCESS,
            receipt_status.BOOKSY_PAY_FAILED,
        ],
        receipt_status.CALL_FOR_BOOKSY_PAY_3DS: [
            receipt_status.BOOKSY_PAY_SUCCESS,
            receipt_status.BOOKSY_PAY_FAILED,
        ],
        receipt_status.BOOKSY_PAY_SUCCESS: [
            receipt_status.PAYMENT_SUCCESS,
            receipt_status.SENT_FOR_REFUND,
            receipt_status.CHARGEBACK,
        ],
    }

    ALLOWED_STATUS_ALL_BY_ONE_TRANSITION = {
        receipt_status.CALL_FOR_PAYMENT: {
            receipt_status.PAYMENT_CANCELED,
            receipt_status.PAYMENT_FAILED,
        },
        receipt_status.CALL_FOR_PREPAYMENT: {
            receipt_status.PAYMENT_CANCELED,
            receipt_status.PAYMENT_FAILED,
        },
        receipt_status.PENDING: {
            receipt_status.PAYMENT_CANCELED,
            receipt_status.PAYMENT_FAILED,
        },
        receipt_status.PAYMENT_SUCCESS: [
            receipt_status.ARCHIVED,
            receipt_status.PAYMENT_CANCELED,
            receipt_status.PAYMENT_FAILED,
        ],
        receipt_status.PAYMENT_FAILED: [
            receipt_status.PAYMENT_CANCELED,
        ],
        receipt_status.PREPAYMENT_FAILED: [
            receipt_status.PAYMENT_CANCELED,
        ],
        receipt_status.PARK_SALE: [
            receipt_status.ARCHIVED,
        ],
        receipt_status.PAYMENT_CANCELED: [
            receipt_status.ARCHIVED,
        ],
        receipt_status.PREPAYMENT_SUCCESS: [
            receipt_status.ARCHIVED,
        ],
        receipt_status.VOUCHER_PARTIAL_REDEEM: [
            receipt_status.ARCHIVED,
        ],
        receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS: [
            receipt_status.ARCHIVED,
            receipt_status.PAYMENT_CANCELED,
        ],
        receipt_status.REFUNDED: [
            receipt_status.ARCHIVED,
        ],
        receipt_status.DEPOSIT_AUTHORISATION_SUCCESS: [
            receipt_status.ARCHIVED,
        ],
        receipt_status.GIFT_CARD_DEPOSIT: [
            receipt_status.PAYMENT_SUCCESS,
            receipt_status.ARCHIVED,
            receipt_status.PAYMENT_CANCELED,
        ],
        receipt_status.BOOKSY_PAY_SUCCESS: [
            receipt_status.ARCHIVED,
        ],
        receipt_status.BOOKSY_PAY_FAILED: [
            receipt_status.PAYMENT_CANCELED,
        ],
    }

    PAYMENT_ROW__REJECT_REASON = 'reject_reason'

    PAYMENT_ROW_STATUSES = Receipt.RECEIPT_STATUSES

    PAYMENT_ROW_MODE__COMPLETE = 'C'
    PAYMENT_ROW_MODE__KEEP = 'K'

    PAYMENT_ROW_MODES = (
        (PAYMENT_ROW_MODE__COMPLETE, _('Complete Payment Row')),
        (PAYMENT_ROW_MODE__KEEP, _('Keep Payment Row')),
    )

    id = models.AutoField(primary_key=True, db_column='payment_row_id')

    created = models.DateTimeField(
        default=tznow,
        verbose_name='Created (UTC)',
    )

    receipt = models.ForeignKey(
        'Receipt',
        related_name='payment_rows',
        on_delete=models.PROTECT,
    )

    payment_type = models.ForeignKey(
        PaymentType,
        related_name='payment_rows',
        on_delete=models.PROTECT,
    )
    amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, blank=True)
    tip_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)

    status = models.CharField(
        choices=PAYMENT_ROW_STATUSES,
        max_length=1,
        default=receipt_status.PAYMENT_SUCCESS,
    )

    card_type = models.CharField(max_length=64, null=True, blank=True, choices=enums.CARD_TYPES)
    card_last_digits = models.CharField(max_length=4, null=True, blank=True)
    pnref = models.CharField(
        max_length=128,
        null=True,
        blank=True,
        db_index=True,
    )
    provider = models.CharField(
        null=True, blank=True, max_length=16, choices=PAYMENT_PROVIDER_CHOICES
    )
    settled = models.BooleanField(default=False)
    refund_operator = models.ForeignKey(
        User,
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
        db_constraint=False,
        help_text="User who requested refund",
    )
    refund_requested = models.DateTimeField(null=True, blank=True)
    voucher = models.ForeignKey(
        'voucher.Voucher',
        related_name='payments',
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    voucher_service = models.ForeignKey(
        ServiceVariant,
        related_name='payments',
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )

    parent_payment_row = models.ForeignKey(
        'self',
        null=True,
        blank=True,
        related_name='children',
        on_delete=models.PROTECT,
    )
    oper_result = models.IntegerField(blank=True, null=True)
    # adyen splits
    marketpay_splits = JSONField(null=True, blank=True)
    # other splits, ie. stripe
    payment_splits = JSONField(null=True, blank=True)
    # It's not Adyen reference. It's kind of FK for Payout object.
    # Payout.psp_reference
    payout_reference = models.CharField(
        max_length=64,
        blank=True,
    )
    payout_booking_date = models.DateField(
        null=True,
    )

    basket_payment_id = models.UUIDField(
        null=True,
        db_index=True,
    )  # Model: PointOfSale.BasketPayment
    payment_link = models.BooleanField(default=False)

    # custom manager
    objects = BaseArchiveManager.from_queryset(PaymentRowQuerySet)()
    all_objects = SoftDeleteManager()

    class Meta:
        ordering = ('id',)
        verbose_name = _('Payment row')
        verbose_name_plural = _('Payment rows')

    def __str__(self):
        return f'Payment row: {self.payment_type} [{self.amount}] - {self.get_status_display()}'

    def save(
        self, force_insert=False, force_update=False, using=None, update_fields=None, **kwargs
    ):
        if self.amount is None:
            self.amount = 0
        super().save(force_insert, force_update, using, update_fields)

    @classmethod
    def create_with_status(cls, *args, **kwargs):
        status, cleared_kwargs = cls.get_status(**kwargs)
        id_ = cleared_kwargs.pop('id', None)

        if id_ and kwargs.get('locked', False):
            instance = PaymentRow.objects.get(id=kwargs.get('id'))
            instance.parent_payment_row_id = instance.pk
            instance.pk = None

            # Allow only these statuses to be set on locked rows
            if status in [
                receipt_status.PAYMENT_SUCCESS,
                receipt_status.ARCHIVED,
            ]:
                instance.status = status
        else:
            instance = cls(*args, **cleared_kwargs)
            instance.status = status

        return instance

    @cached_property
    def amount_to_display(self):
        if self.status in receipt_status.NEGATIVE_VALUE_STATUSES:
            return -self.amount
        return self.amount

    @staticmethod
    def get_status(**kwargs):
        locked = kwargs.pop('locked', None)
        status = kwargs.pop('status', None)
        is_deposit = kwargs.pop('is_deposit', None)
        payment_type = kwargs.get('payment_type', None)
        if status:
            ret = status
        elif locked:
            ret = receipt_status.PAYMENT_SUCCESS
        elif is_deposit:
            ret = receipt_status.CALL_FOR_DEPOSIT
        else:
            ret = payment_type.default_status

        return ret, kwargs

    @property
    def locked(self):
        return (
            (self.status == receipt_status.PAYMENT_SUCCESS and self.payment_type.should_lock)
            or (
                self.status in [receipt_status.PREPAYMENT_SUCCESS, receipt_status.PAYMENT_SUCCESS]
                and self.payment_type.code == PaymentTypeEnum.PREPAYMENT
            )
            or (
                self.status == receipt_status.PAYMENT_SUCCESS
                and self.payment_type.code
                in [
                    PaymentTypeEnum.EGIFT_CARD,
                    PaymentTypeEnum.MEMBERSHIP,
                ]
            )
            or (
                self.status == receipt_status.VOUCHER_PARTIAL_REDEEM
                and self.payment_type.code
                in [
                    PaymentTypeEnum.EGIFT_CARD,
                ]
            )
            or (self.payment_type.code == PaymentTypeEnum.BOOKSY_GIFT_CARD)
            or (
                self.status in [receipt_status.BOOKSY_PAY_SUCCESS, receipt_status.PAYMENT_SUCCESS]
                and self.payment_type.code == PaymentTypeEnum.BOOKSY_PAY
            )
        )

    @property
    def card_info(self):
        return (
            (f'{self.get_card_type_display()} ****{self.card_last_digits}')
            if self.card_last_digits
            else ''
        )

    @property
    def service_amount(self):
        return round_currency((self.amount or 0) - (self.tip_amount if self.tip_amount else 0))

    @property
    def processing_fee(self):
        if self.marketpay_splits is None:
            return

        return round_currency(
            (self.marketpay_splits.get('provision', 0) + self.marketpay_splits.get('txn_fee', 0))
            / 100
        )

    @atomic
    def update_status(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        status,
        card_type=None,
        card_last_digits=None,
        pnref=None,
        provider=None,
        settled=None,
        oper_result=None,
        marketpay_splits=None,
        payment_splits=None,
        log_action=None,
        log_note=None,
        operator=None,
        disable_allowed_status_validation=False,
    ):
        if not disable_allowed_status_validation and status not in (
            self.ALLOWED_STATUS_TRANSITION.get(self.status) or []
        ):
            # pylint: disable=broad-exception-raised
            raise Exception(f'Invalid status transition Transaction {self.status} -> {status}')

        if self.receipt.transaction.latest_receipt != self.receipt:
            raise InvalidPaymentRowUpdateError(
                f'Cannot edit not last receipt transaction_rows'
                f' {self.receipt.transaction.latest_receipt} != {self.receipt}'
            )
        # temporary solution to success bgc payment row if other online method succeeded
        # https://booksy.atlassian.net/browse/HAM-2083
        update_bgc_status_to_success = (
            UpdateBGCPaymentRowIfPBASucceededFlag()
            and self.payment_type.code in PaymentTypeEnum.online_methods()
            and status == receipt_status.PAYMENT_SUCCESS
        )

        payment_rows = [
            PaymentRow.create_with_status(
                created=row.created,
                amount=row.amount,
                status=(
                    receipt_status.PAYMENT_SUCCESS
                    if (
                        row.status == receipt_status.GIFT_CARD_DEPOSIT
                        and update_bgc_status_to_success
                    )
                    else row.status
                ),
                card_type=row.card_type,
                card_last_digits=row.card_last_digits,
                pnref=row.pnref,
                provider=row.provider,
                payment_type=row.payment_type,
                locked=row.locked,
                id=row.id,
                settled=row.settled,
                oper_result=row.oper_result,
                tip_amount=row.tip_amount,
                parent_payment_row=row,
                basket_payment_id=row.basket_payment_id,
                payment_link=row.payment_link,
            )
            for row in (self.receipt.payment_rows.exclude(id=self.id)).select_related(
                'payment_type'
            )
        ]

        index = self.receipt.payment_rows.filter(id__lt=self.id).count()

        finish_prepayment_transition = (
            status == receipt_status.PAYMENT_SUCCESS
            and self.status == receipt_status.PREPAYMENT_SUCCESS
        )

        payment_rows.insert(
            index,
            PaymentRow.create_with_status(
                created=self.created if finish_prepayment_transition else tznow(),
                amount=self.amount,
                status=status,
                card_type=card_type or self.card_type,
                card_last_digits=card_last_digits or self.card_last_digits,
                pnref=pnref or self.pnref,
                provider=provider or self.provider,
                payment_type=self.payment_type,
                settled=settled if settled is not None else self.settled,
                oper_result=oper_result or self.oper_result,
                tip_amount=self.tip_amount,
                parent_payment_row=self,
                marketpay_splits=marketpay_splits or self.marketpay_splits,
                payment_splits=payment_splits or self.payment_splits,
                # failed refund should go back to pending refunds
                refund_requested=self.refund_requested,
                refund_operator=self.refund_operator,
                payment_link=self.payment_link,
                basket_payment_id=(
                    None
                    if (
                        status in SPLITTING_STATUSES
                        or
                        # Square retry scenario
                        status == receipt_status.PAYMENT_SUCCESS
                        and self.status == receipt_status.PAYMENT_FAILED
                    )
                    else self.basket_payment_id
                ),
            ),
        )

        PaymentRowChange.add(
            operator=operator, reason=log_action, payment_row=self, metadata=log_note
        )

        self.receipt.transaction.update_status(payment_rows)

        return payment_rows[index]

    def send_for_refund(self, operator, from_admin=True):
        provider = get_payment_provider(
            codename=self.provider,
            txn=self.receipt.transaction,
        )
        return provider.send_for_refund(self, operator, from_admin)

    def get_marketpay_splits(self) -> Optional[dict]:
        """Market Pay splits data to be saved in PaymentRow

        It will be further converted to adyen format in adyen_ee module
        """
        pos = self.receipt.transaction.pos
        if not pos.account_holder:
            return None

        plan = pos.get_pos_plan(POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT)

        total_amount = minor_unit(self.amount or 0)
        provision_amount = int(round(plan.provision * total_amount))
        txn_fee_amount = minor_unit(plan.txn_fee)
        amount = total_amount - provision_amount - txn_fee_amount

        # webapps/pos/provider/adyen_ee.py:_additional_data:marketpay_splits
        return {
            'total_amount': total_amount,
            'currency_code': settings.CURRENCY_CODE,
            'txn_fee': txn_fee_amount,
            'provision': provision_amount,
            'amount': amount,
            'account_code': pos.account_holder.account_code,
            'reference': get_reference(),
            # field not send to adyen
            '_payout_amount': amount,
        }

    def get_refund_splits(self):
        """Market Pay splits data for refund

        returns 2 parts:
            data to be saved in PaymentRow and data converted to
            adyen additional_data format since refund call omits
            provider logic
        """
        pos = self.receipt.transaction.pos
        if not self.marketpay_splits:
            return None, None

        total_amount = self.marketpay_splits['total_amount']

        pr_data = {
            'total_amount': total_amount,
            'currency_code': settings.CURRENCY_CODE,
            'amount': total_amount,
            'account_code': pos.account_holder.account_code,
            'reference': self.marketpay_splits['reference'],
            '_payout_amount': -total_amount,
        }

        #  it should be ...provider/adyen_ee._refund_splits
        splits = [
            {
                'account': pr_data['account_code'],
                'type': 'MarketPlace',
                'amount': {
                    'value': pr_data['total_amount'],
                    'currency': pr_data['currency_code'],
                },
                'reference': pr_data['reference'],
            }
        ]
        return pr_data, splits

    def process_refund(self, success: bool, pnref: str = None):
        """
        Methods handles result of refund notification. After success payment row will get
        REFUNDED state, if not it will return to its previous state
        PAYMENT_SUCCESS / PREPAYMENT_SUCCESS / BOOKSY_PAY_SUCCESS
        """
        from webapps.pos.tools import get_new_receipt_status

        old_status = self.receipt.status_code
        new_status = get_new_receipt_status(old_status, success)
        settled = True

        # Revert pnref from parent when refund failed
        if old_status == receipt_status.SENT_FOR_REFUND and new_status in [
            receipt_status.PAYMENT_SUCCESS,
            receipt_status.PREPAYMENT_SUCCESS,
            receipt_status.BOOKSY_PAY_SUCCESS,
        ]:
            pnref = self.parent_payment_row.pnref
            settled = False

        # Replace status for prepayment to return it to its basic state
        if (
            old_status == receipt_status.SENT_FOR_REFUND
            and (self.parent_payment_row.status == receipt_status.PREPAYMENT_SUCCESS)
            and new_status == receipt_status.PAYMENT_SUCCESS
        ):
            new_status = receipt_status.PREPAYMENT_SUCCESS
        # Replace status for Booksy Pay to return it to its basic state
        elif (
            old_status == receipt_status.SENT_FOR_REFUND
            and (self.parent_payment_row.status == receipt_status.BOOKSY_PAY_SUCCESS)
            and new_status == receipt_status.PAYMENT_SUCCESS
        ):
            new_status = receipt_status.BOOKSY_PAY_SUCCESS

        if old_status != new_status:
            self.update_status(
                new_status,
                pnref=pnref,
                settled=settled,
                provider=self.provider,
                log_action=PaymentRowChange.SINGLE_ROW_UPDATE,
                log_note='Refund notification handler',
            )

    @property
    def entity(self) -> PaymentRowEntity:
        return PaymentRowEntity(
            id=self.id,
            payment_link=self.payment_link,
            receipt_id=self.receipt_id,
        )


# Model used on in admin for generating listing.
class RefundRow(PaymentRow):  # pylint: disable=abstract-method
    class Meta:
        proxy = True


class OperationFeeMidnightQuerySet(MidnightQuerySet, AutoUpdateQuerySet):
    tz_field = 'pos__business__time_zone_name'
    dt_field = 'created'


class OperationFeeQuerySet(OperationFeeMidnightQuerySet):
    def pending(self):
        return self.filter(
            settled=False,
        )


class OperationFee(UndeletableMixin, ArchiveModel):  # pylint: disable=abstract-method
    class Meta:
        # for admin page
        verbose_name_plural = 'Refund/Chargeback Fees'

    pos = models.ForeignKey(
        POS,
        null=True,
        on_delete=models.DO_NOTHING,
    )
    # This is not pnref for Adyen objects. Its kind of FK for TransferFunds,
    # UUID for OperationFee
    reference = models.CharField(
        max_length=64,
        default=create_uuid,
        db_index=True,
    )
    currency = models.CharField(max_length=3)
    amount = models.IntegerField()  # minor unit
    payment_row = models.ForeignKey(
        PaymentRow,
        related_name='operation_fees',
        null=True,
        on_delete=models.DO_NOTHING,
    )
    parts = JSONField(default=list)
    settled = models.BooleanField(
        default=False,
    )
    payout_reference = models.CharField(
        max_length=64,
        blank=True,
    )
    payout_booking_date = models.DateField(
        null=True,
    )

    objects = BaseArchiveManager.from_queryset(OperationFeeQuerySet)()
    all_objects = SoftDeleteManager()

    @property
    def formatted_amount(self):
        return format_currency(major_unit(self.amount or 0))

    @property
    def type(self):
        if self.status == receipt_status.CHARGEBACK:
            return 'Chargeback'
        if self.status in [receipt_status.SENT_FOR_REFUND, receipt_status.REFUNDED]:
            return 'Refund'
        return 'Unknown'


class TransactionQuerySet(AutoUpdateQuerySet):

    def by_appointment_id(
        self,
        appointment_id: int | None = None,
        **kwargs,
    ) -> "TransactionQuerySet":
        """Filter transactions associated with given (multi-)booking."""
        if appointment_id is None:
            return self.none()

        return self.filter(
            latest_receipt__isnull=False,
            appointment_id=appointment_id,
            **kwargs,
        ).order_by(
            'latest_receipt__created',
        )

    def prefetch_all(self):
        from webapps.stripe_integration.models import StripePaymentIntent

        return (
            self.select_related(  # pylint: disable=not-callable
                'pos__business',
                'tip',
                'parent_txn',
                'deposit__latest_receipt__payment_type',
                'register',
                'customer',
                'customer_card',
            )
            .prefetch_related(
                'tax_subtotals',
                'formula_rows',
                'children',
                Prefetch(
                    'operator__staffers',
                    queryset=Resource.objects.all(),
                    to_attr='_prefetched_staffers',
                ),
                Prefetch(
                    'customer_card__user',
                    queryset=User.objects.prefetch_related(
                        Prefetch(
                            'profiles',
                            UserProfile.objects.select_related(
                                'photo',
                                'region',
                            ),
                        ),
                    ),
                ),
                Prefetch(
                    'customer_invoices',
                    queryset=CustomerInvoice.objects.all(),
                    to_attr='_prefetched_customer_invoices',
                ),
                Prefetch(
                    'latest_receipt',
                    queryset=Receipt.objects.all()
                    .select_related('payment_type')
                    .prefetch_related(
                        Prefetch(
                            'payment_rows',
                            queryset=PaymentRow.objects.select_related(
                                'payment_type',
                                'refund_operator',
                            ).prefetch_related(
                                Prefetch(
                                    'intents',
                                    StripePaymentIntent.objects.only('id'),
                                )
                            ),
                        )
                    ),
                ),
                Prefetch(
                    'receipts',
                    queryset=Receipt.objects.all()
                    .select_related('payment_type')
                    .prefetch_related(
                        Prefetch(
                            'payment_rows',
                            queryset=PaymentRow.objects.select_related(
                                'payment_type',
                                'refund_operator',
                            ).prefetch_related(
                                Prefetch(
                                    'intents',
                                    StripePaymentIntent.objects.only('id'),
                                )
                            ),
                        ),
                    ),
                ),
                Prefetch(
                    'rows',
                    queryset=TransactionRow.objects.all()
                    .select_related(
                        'voucher',
                        'voucher__customer',
                        'voucher__voucher_template',
                        'transaction__pos__business',
                        'commissions_last_editor',
                        'commission_staffer',
                        'service_variant__service',
                    )
                    .prefetch_related('commissions_last_editor__staffers'),
                ),
            )
            .annotate(
                _transaction_with_invoice_exists=Exists(
                    Transaction.objects.filter(
                        series_id=OuterRef('series_id'),
                        customer_invoices__isnull=False,
                        customer_invoices__deleted__isnull=True,
                    ),
                )
            )
            .distinct()
        )

    def prefetch_for_get(self):
        return self.select_related(  # pylint: disable=not-callable
            'customer_card',
            'operator',
            'deposit__latest_receipt__payment_type',
        ).prefetch_related(
            'rows',
            Prefetch(
                'latest_receipt',
                queryset=Receipt.objects.select_related('payment_type').prefetch_related(
                    Prefetch(
                        'payment_rows',
                        queryset=PaymentRow.objects.select_related(
                            'payment_type',
                            'refund_operator',
                        ),
                    ),
                ),
            ),
            Prefetch(
                'receipts',
                queryset=Receipt.objects.all()
                .select_related('payment_type')
                .prefetch_related(
                    Prefetch(
                        'payment_rows',
                        queryset=PaymentRow.objects.select_related(
                            'payment_type',
                            'refund_operator',
                        ),
                    )
                ),
            ),
        )

    def for_user(self, user_id: int):
        """Filter by customer or appointment__booked_for__user."""
        return self.filter(Q(customer_id=user_id) | Q(appointment__booked_for__user_id=user_id))

    def delete(self):
        raise NotImplementedError('You cannot delete Transaction instances')

    def get_transactions_with_receipt_and_appointment_details(self):
        from webapps.stripe_integration.models import StripePaymentIntent

        return (
            self.select_related(  # pylint: disable=not-callable
                'appointment',
                'latest_receipt',
                'latest_receipt__payment_type',
                'latest_receipt__transaction',
                'latest_receipt__transaction__pos',
            )
            .prefetch_related(
                Prefetch(
                    'latest_receipt__payment_rows',
                    queryset=PaymentRow.objects.select_related(
                        'payment_type',
                        'refund_operator',
                    )
                    .prefetch_related(
                        Prefetch(
                            'intents',
                            StripePaymentIntent.objects.only('id'),
                        )
                    )
                    .only(
                        'id',
                        'created',
                        'card_type',
                        'card_last_digits',
                        'status',
                        'amount',
                        'payment_type_id',
                        'receipt_id',
                        'payment_type__code',
                        'pnref',
                        'provider',
                        'refund_operator__last_name',
                        'refund_operator__first_name',
                        'refund_operator__is_staff',
                        'refund_operator__deleted',
                    ),
                )
            )
            .only(
                'appointment_id',
                'appointment__created',
                'appointment__status',
                'total',
                'currency_symbol',
                'transaction_type',
                'latest_receipt__created',
                'latest_receipt__status_code',
                'latest_receipt__receipt_number',
                'latest_receipt__card_type',
                'latest_receipt__card_last_digits',
                'latest_receipt__already_paid',
                'latest_receipt__payment_type__code',
                'latest_receipt__transaction_id',
                'latest_receipt__transaction__total',
                'latest_receipt__transaction__pos__id',
                'latest_receipt__transaction__pos__pos_refactor_stage2_enabled',
            )
        )


class Transaction(UndeletableMixin, ArchiveModel):  # pylint: disable=abstract-method
    TRANSACTION_TYPE__PAYMENT = 'P'
    TRANSACTION_TYPE__CANCELLATION_FEE = 'D'
    TRANSACTION_TYPES = (
        (TRANSACTION_TYPE__PAYMENT, pgettext_lazy('transaction type', 'Sale')),
        (TRANSACTION_TYPE__CANCELLATION_FEE, pgettext_lazy('transaction type', 'Cancellation Fee')),
    )
    ALLOWED_ACTIONS = {
        # <action>: (<transaction_type>, [<Receipt.status_code>, ...])
        # business actions
        enums.BUSINESS_ACTION__SET_PAYMENT_TYPE: (
            TRANSACTION_TYPE__PAYMENT,
            [
                receipt_status.CALL_FOR_PAYMENT,
                receipt_status.PARK_SALE,
                receipt_status.PAYMENT_CANCELED,
            ],
        ),
        enums.BUSINESS_ACTION__SET_PAYMENT_STATUS: (
            TRANSACTION_TYPE__PAYMENT,
            [
                receipt_status.PENDING,  # external provider
                receipt_status.CALL_FOR_PAYMENT,  # Pay By App
                receipt_status.PAYMENT_FAILED,
            ],
        ),
        enums.BUSINESS_ACTION__CANCEL_PAYMENT: (
            TRANSACTION_TYPE__PAYMENT,
            [
                receipt_status.CALL_FOR_PAYMENT,
                receipt_status.PENDING,
                receipt_status.PAYMENT_FAILED,
                receipt_status.PAYMENT_AWAITING,
                receipt_status.PARK_SALE,
                receipt_status.PENDING,
                receipt_status.PREPAYMENT_FAILED,
                receipt_status.BOOKSY_PAY_FAILED,
            ],
        ),
        enums.BUSINESS_ACTION__PARK_TRANSACTION: (
            TRANSACTION_TYPE__PAYMENT,
            [
                receipt_status.PARK_SALE,
                receipt_status.PREPAYMENT_SUCCESS,
            ],
        ),
        enums.BUSINESS_ACTION__CHARGE_DEPOSIT: (
            TRANSACTION_TYPE__CANCELLATION_FEE,
            [
                receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
                receipt_status.DEPOSIT_CHARGE_FAILED,
            ],
        ),
        enums.BUSINESS_ACTION__CANCEL_DEPOSIT: (
            TRANSACTION_TYPE__CANCELLATION_FEE,
            [
                receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
                receipt_status.DEPOSIT_CHARGE_FAILED,
            ],
        ),
        # customer actions
        enums.CUSTOMER_ACTION__SET_TIP_RATE: (
            TRANSACTION_TYPE__PAYMENT,
            [
                receipt_status.CALL_FOR_PAYMENT,
                receipt_status.CALL_FOR_PREPAYMENT,
                receipt_status.CALL_FOR_BOOKSY_PAY,
            ],
        ),
        enums.CUSTOMER_ACTION__MAKE_PAYMENT: (
            TRANSACTION_TYPE__PAYMENT,
            [
                receipt_status.CALL_FOR_PAYMENT,
                receipt_status.CALL_FOR_PREPAYMENT,
            ],
        ),
        enums.CUSTOMER_ACTION__RETRY_PAYMENT: (
            TRANSACTION_TYPE__PAYMENT,
            [
                receipt_status.PREPAYMENT_FAILED,
                receipt_status.PREPAYMENT_AUTHORISATION_FAILED,
            ],
        ),
        enums.BUSINESS_ACTION__RETRY_KIP: (
            TRANSACTION_TYPE__PAYMENT,
            [
                receipt_status.PREPAYMENT_FAILED,
                receipt_status.PREPAYMENT_AUTHORISATION_FAILED,
            ],
        ),
        # enums.CUSTOMER_ACTION__CANCEL_PAYMENT -- the same as business one
    }

    id = models.AutoField(primary_key=True, db_column='transaction_id')
    charge_date = models.DateTimeField(null=True, blank=True)
    pos = models.ForeignKey(
        POS,
        related_name='transactions',
        on_delete=models.PROTECT,
    )
    operator = models.ForeignKey(
        User,
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
        db_constraint=False,
        help_text="User who created the Transaction",
    )
    register = models.ForeignKey(
        'register.Register',
        null=True,
        blank=True,
        related_name='transactions',
        on_delete=models.SET_NULL,
        help_text='A Register associated with the (parent) Transaction.',
    )
    transaction_type = models.CharField(max_length=1, choices=TRANSACTION_TYPES)
    business_name = models.CharField(max_length=250, blank=True)
    business_address = models.CharField(max_length=250, blank=True)
    appointment = models.ForeignKey(
        Appointment,
        null=True,
        blank=True,
        related_name='transactions',
        on_delete=models.DO_NOTHING,
        db_constraint=False,
        verbose_name='Appointment',
        help_text="Appointment associated with this Transaction"
        "All of SubBookings in TransactionRows are part of this Appointment.",
    )
    deposit = models.ForeignKey(
        'self',
        null=True,
        blank=True,
        related_name='payments',
        on_delete=models.DO_NOTHING,
        db_constraint=False,
        help_text="Cancellation fee associated with this payment transaction. "
        "Successful payment will release the cancellation fee.",
    )
    customer = models.ForeignKey(
        User,
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
        db_constraint=False,
        related_name='transactions',
    )
    customer_card = models.ForeignKey(
        BusinessCustomerInfo,
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
        db_constraint=False,
        related_name='transactions',
    )
    customer_data = models.CharField(max_length=250, blank=True)
    service_tax_mode = models.CharField(max_length=1, choices=POS.POS_TAX_MODES)
    product_tax_mode = models.CharField(max_length=1, choices=POS.POS_TAX_MODES)
    currency_symbol = models.CharField(max_length=3, default=settings.CURRENCY_CODE)
    subtotal_services = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    subtotal_products = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    subtotal = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    discount_rate = models.PositiveSmallIntegerField(default=0)
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    discounted_subtotal_services = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True
    )
    discounted_subtotal_products = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True
    )
    taxed_subtotal_services = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True
    )
    taxed_subtotal_products = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True
    )
    deprecated_tip = JSONField(null=True, blank=True)
    deprecated_tip_rate = models.PositiveSmallIntegerField(null=True, blank=True)
    deprecated_tip_amount = models.DecimalField(
        max_digits=10, decimal_places=2, null=True, blank=True
    )
    total = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    latest_receipt = models.ForeignKey(
        Receipt,
        null=True,
        blank=True,
        related_name='transactions',
        on_delete=models.PROTECT,
    )
    # for dwola
    settled = models.BooleanField(default=False)
    # for adyen notifications
    ready_for_settle = models.BooleanField(default=False)
    parent_txn = models.ForeignKey(
        'self',
        null=True,
        blank=True,
        related_name='children',
        on_delete=models.PROTECT,
    )

    # 51650 - Receipt custom line
    receipt_footer_line_1 = models.CharField(
        max_length=50, blank=True, null=True, help_text='First line of the receipt footer'
    )
    receipt_footer_line_2 = models.CharField(
        max_length=50, blank=True, null=True, help_text='Second line of the receipt footer'
    )

    # Service fee
    service_fee = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)

    # 54099 - before series of transaction were generated recursively by
    # parent_txn. Now each transaction from series has same id here -
    # ID of first transaction in series
    series_id = models.IntegerField(null=True, blank=True, db_index=True)

    basket_id = models.UUIDField(
        null=True,
        db_index=True,
    )  # model: PointOfSale.Basket
    cancellation_fee_auth_id = models.UUIDField(
        null=True,
        db_index=True,
    )  #  model: PointOfSale.CancellationFeeAuth

    # custom manager
    objects = BaseArchiveManager.from_queryset(TransactionQuerySet)()
    all_objects = SoftDeleteManager()

    class Meta:
        indexes = [
            models.Index(fields=['pos', 'transaction_type'], name='txn_pos_type_idx'),
        ]

    def __str__(self):
        payment_type = (
            'Payment'
            if self.transaction_type == self.TRANSACTION_TYPE__PAYMENT
            else 'Cancellation Fee'
        )
        return f'{payment_type} id={self.id} total={self.total}'

    def prepare_save(self):
        """Prepares charge_date for save. Charge date differs, when transaction
        has booking/multibooking or it's result of editing.


        """
        if self.charge_date:
            #  If transaction has already charge_date filled leave it as it is.
            return
        if not self.appointment:
            if self.parent_txn:
                # If this is edited transaction without booking, use
                # charge_date from parent
                self.charge_date = self.parent_txn.charge_date
            else:
                self.charge_date = tznow()
        else:
            self.charge_date = self.appointment.booked_till

    def save(self, *args, **kwargs):
        self.prepare_save()

        super().save(**kwargs)

        # Series_id should be id of the first transaction in series
        # If transaction doesn't have one, check parent.
        if not self.series_id:
            if self.parent_txn:
                self.series_id = self.parent_txn.series_id
            else:
                self.series_id = self.id

            super().save(update_fields=['series_id'])

    def tax_services(self):
        return self.tax_subtotals.filter(subtotal_type=self.tax_subtotals.SUBTOTAL_TYPE__SERVICES)

    def tax_products(self):
        return self.tax_subtotals.filter(subtotal_type=self.tax_subtotals.SUBTOTAL_TYPE__PRODUCTS)

    @property
    def payment_rows(self):
        return self.latest_receipt.payment_rows.all()

    def get_used_booksy_gift_cards(self) -> list[BooksyGiftCard]:
        """Returns all Booksy Gift Cards used in transaction"""
        if self.latest_receipt is None:
            return []

        payments = self.latest_receipt.payment_rows.filter(
            booksy_gift_cards__isnull=False
        ).distinct()
        bgcs_list = []
        for payment in payments:
            for bgc in payment.booksy_gift_cards.all():
                bgcs_list.append(bgc)
        return bgcs_list

    @property
    def used_booksy_gift_card(self) -> list[BooksyGiftCard]:
        if self.parent_txn is not None:
            return self.parent_txn.get_used_booksy_gift_cards()

        return self.get_used_booksy_gift_cards()

    @cached_property
    def lock(self):  # pylint: disable=too-many-return-statements
        if self.latest_receipt.status_code not in receipt_status.NOT_LOCKING_STATUSES:
            return True

        if len(self.children.all()):
            return True

        if any(pr.payment_type.should_lock for pr in self.latest_receipt.payment_rows.all()) and (
            self.latest_receipt.status_code not in receipt_status.FAKE_EDIT_STATUSES
        ):
            return True

        if self.pos.registers_enabled and self.register and not self.register.is_open:
            return True

        if (
            self.appointment
            and
            # current booking must not be canceled etc.
            self.appointment.status in Appointment.STATUSES_INACTIVE
        ):
            return True

        #  Lock transaction edit. TODO in future
        return self.latest_receipt.payment_rows.filter(
            payment_type__code__in=[
                PaymentTypeEnum.MEMBERSHIP,
                PaymentTypeEnum.EGIFT_CARD,
                PaymentTypeEnum.PACKAGE,
                PaymentTypeEnum.PAY_BY_APP,
                PaymentTypeEnum.PREPAYMENT,
                PaymentTypeEnum.PAY_BY_APP_DONATIONS,
                PaymentTypeEnum.BOOKSY_PAY,
                PaymentTypeEnum.BOOKSY_GIFT_CARD,
            ]
        ).exists()

    @property
    def can_be_modified(self):
        return self.receipts.count() == 0

    @property
    def success_auth_ref(self):
        # try except instead of .first(), because
        # we want to see MultipleObjectsReturned errors
        try:
            receipt = Receipt.objects.get(
                transaction=self,
                status_code__in=receipt_status.AUTHORIZED_STATUSES,
            )
        except Receipt.DoesNotExist:
            return None
        return receipt.pnref

    @property
    def has_tip(self):
        return hasattr(self, 'tip')

    @staticmethod
    def get_status(payment_rows):
        """
        Get new status for receipt based on payment rows.
        """

        new_status = None
        payment_row_statuses = {x.status for x in payment_rows}

        code_priority = [
            receipt_status.PARK_SALE,
            receipt_status.CHARGEBACK,
            receipt_status.CHARGEBACK_REVERSED,
            receipt_status.SECOND_CHARGEBACK,
            receipt_status.PAYMENT_FAILED,
            receipt_status.CALL_FOR_PAYMENT,
            receipt_status.CALL_FOR_PAYMENT_3DS,
            receipt_status.PENDING,
            receipt_status.SENT_FOR_REFUND,
            receipt_status.REFUNDED,
            receipt_status.PAYMENT_CANCELED,
            receipt_status.VOUCHER_PARTIAL_REDEEM,
        ]

        for code in code_priority:
            if code in payment_row_statuses:
                new_status = code
                break

        # Hack for refunded transactions
        if new_status == receipt_status.REFUNDED:
            # Find row which requested refund
            pr = [pr for pr in payment_rows if pr.status == receipt_status.REFUNDED][0]

            if (
                not pr.parent_payment_row
                or pr.parent_payment_row.parent_payment_row.status
                == receipt_status.PREPAYMENT_SUCCESS
            ):
                new_status = receipt_status.PREPAYMENT_SUCCESS
            elif (
                pr.parent_payment_row.parent_payment_row.status == receipt_status.BOOKSY_PAY_SUCCESS
            ):
                new_status = receipt_status.BOOKSY_PAY_SUCCESS
            else:
                new_status = receipt_status.REFUNDED

        if not new_status:
            if len(payment_row_statuses) == 1:
                # all rows have same status or there is only 1 row
                new_status = payment_row_statuses.pop()
            else:
                raise AssertionError(  # pylint: disable=broad-exception-raised
                    'Cannot select new receipt status'
                )

        return new_status

    @atomic
    def clone_payment_rows(self, payment_rows=None):
        if payment_rows is None:
            payment_rows = self.latest_receipt.payment_rows.all()

        return [
            PaymentRow.create_with_status(
                amount=row.amount,
                status=row.status,
                card_type=row.card_type,
                card_last_digits=row.card_last_digits,
                pnref=row.pnref,
                provider=row.provider,
                payment_type=row.payment_type,
                locked=row.locked,
                id=row.id,
                basket_payment_id=row.basket_payment_id,
            )
            for row in payment_rows
        ]

    @atomic
    def update_payment_rows(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        status,
        receipt_number=None,
        operator=None,
        log_action=None,
        log_note=None,
        update_basket_payments=True,
    ):
        """
        Update all payment rows.
        Should be use only with Archived and Cancelled status.
        """
        payment_rows = self.latest_receipt.payment_rows.all()

        for row in payment_rows:
            if status not in (PaymentRow.ALLOWED_STATUS_ALL_BY_ONE_TRANSITION.get(row.status, [])):
                raise Exception(  # pylint: disable=broad-exception-raised
                    f'Invalid status transition PaymentRow [{row.status}]->[{status}]'
                )

        for row in payment_rows:
            PaymentRowChange.add(
                operator=operator, reason=log_action, payment_row=row, metadata=log_note
            )

        payment_rows = [
            PaymentRow.create_with_status(
                amount=row.amount,
                status=status,
                card_type=row.card_type,
                card_last_digits=row.card_last_digits,
                pnref=row.pnref,
                provider=row.provider,
                payment_type=row.payment_type,
                locked=row.locked,
                oper_result=row.oper_result,
                id=row.id,
                parent_payment_row=row,
                basket_payment_id=row.basket_payment_id,
                payment_link=row.payment_link,
            )
            for row in payment_rows
        ]

        self.update_status(
            payment_rows,
            receipt_number,
            operator=operator,
            update_basket_payments=update_basket_payments,
        )

        # Editing transaction in closed registers is forbidden.
        # Protect from leaving unfinished transaction in some register.
        if status in [receipt_status.PAYMENT_CANCELED, receipt_status.PAYMENT_FAILED]:
            self.register = None
            self.save(update_fields=['register'])

    @atomic
    def update_status(
        self,
        payment_rows,
        receipt_number=None,
        operator=None,
        ignore_analytics=False,
        update_basket_payments=True,
        device_data: DeviceDataEntity | None = None,
        payment_token: str = None,
    ):  # pylint: disable=too-many-branches,too-many-statements,too-many-arguments,too-many-positional-arguments
        """Create new Receipt with given status.

        If status changes to success, apply product stock changes.

        """
        from webapps.pos.tools import update_pending_payment_appointment

        prev_status = self.latest_receipt and self.latest_receipt.status_code
        status = self.get_status(payment_rows)
        extra = {}
        if receipt_number:
            extra['receipt_number'] = receipt_number

        if len(payment_rows) > 1:
            payment_type = self.pos.payment_types.get(code=PaymentTypeEnum.SPLIT)
        else:
            code = payment_rows[0].payment_type.code
            payment_type = self.pos.payment_types(manager='all_objects').get(code=code)

        extra['already_paid'] = round_currency(
            sum(
                row.amount or 0
                for row in payment_rows
                if row.status not in receipt_status.STATUSES_WITHOUT_MONEY
            )
        )

        receipt = Receipt.objects.create(
            transaction=self, status_code=status, payment_type=payment_type, **extra
        )
        self.latest_receipt = receipt
        self.save(update_fields=['latest_receipt'])

        for row in payment_rows:
            row.receipt = receipt

        payment_rows = PaymentRow.objects.bulk_create(payment_rows)

        for row in payment_rows:
            if (
                row.payment_type.code == PaymentTypeEnum.BOOKSY_GIFT_CARD
                and sget_v2(row, ['parent_payment_row'])
                and row.parent_payment_row.booksy_gift_cards.exists()
            ):
                for booksy_gift_card in row.parent_payment_row.booksy_gift_cards.all():
                    BooksyGiftCard.objects.create(
                        payment=row,
                        external_id=booksy_gift_card.external_id,
                    )

        # TODO DELETE AFTER POS REFACTOR WILL BE COMPLETED
        if update_basket_payments and txn_refactor_stage2_enabled(self):
            initialize_basket_payments.run(
                txn_id=self.id,
                txn_status=status,
                payment_row_ids=[x.id for x in payment_rows],
                device_data_dict=asdict(device_data) if device_data else None,
                payment_token=payment_token,
            )
        # <<<<

        from webapps.market_pay.notifications import CustomerNotificationHandler

        CustomerNotificationHandler.handle_notification(payment_rows[-1])

        from webapps.pos.notifications import BooksyPayPaymentCompletedNotification

        if self.appointment and BooksyPayPaymentCompletedNotification.should_send_notification(
            payment_rows[-1]
        ):
            BooksyPayPaymentCompletedNotification(payment_rows[-1].receipt.transaction).send()

        from webapps.stripe_integration.models import StripePaymentIntent

        StripePaymentIntent.assign_payment_rows(payment_rows)

        # handle stocks
        if prev_status is None and status in (
            receipt_status.CALL_FOR_PAYMENT,
            receipt_status.PENDING,
            receipt_status.PARK_SALE,
            receipt_status.PAYMENT_AWAITING,
            receipt_status.PAYMENT_SUCCESS,
            receipt_status.VOUCHER_PARTIAL_REDEEM,
        ):
            self._voucher_payment_apply()

        restore_statuses = (
            receipt_status.PAYMENT_CANCELED,
            receipt_status.ARCHIVED,
        )

        if status in restore_statuses:
            self._products_stock_restore_at_warehouse(
                operator,
                WarehouseDocumentType.WZ,
            )
        if status == receipt_status.ARCHIVED:
            self._products_stock_restore_at_warehouse(
                operator,
                WarehouseDocumentType.RW,
            )

        # only payment_success before refund / chargeback
        if (
            status == receipt_status.PAYMENT_SUCCESS
            and
            # These statuses can return to payment success
            prev_status
            not in [
                receipt_status.CHARGEBACK,
                receipt_status.SENT_FOR_REFUND,
            ]
        ):
            # handle commissions
            if self.pos.commissions_enabled:
                self._commissions_apply()

            # TICKET: #48059
            # Deposit can be released only if payment success is result of
            # user activity - processing checkout.
            # Transaction closed aromatically by CheckoutPrepaidTransaction with
            # bookings in NOSHOW or CANCELLED should not release deposits

            if (
                self.deposit_id is not None
                and self.appointment
                and self.appointment.status
                not in [
                    Appointment.STATUS.NOSHOW,
                    Appointment.STATUS.CANCELED,
                ]
            ):
                ReleaseDepositOnPayment.delay(self.deposit_id)

            self._vouchers_activate()

        # Check if there is connected prepaid transaction
        if status == receipt_status.DEPOSIT_CHARGE_SUCCESS:
            prepayment_tx = self.payments.filter(
                latest_receipt__payment_type__code=PaymentTypeEnum.PREPAYMENT
            ).first()

            if prepayment_tx:
                CheckoutPrepaidTransaction.delay(transaction_id=prepayment_tx.id)

        # Normal transaction edit / fake edit
        refund_receipt = any(
            row.status in [receipt_status.REFUNDED, receipt_status.CHARGEBACK]
            for row in payment_rows
        )

        from webapps.register.models import RegisterOperation

        if (
            self.pos.registers_enabled
            and self.register
            and status
            in [
                # was paid
                # VOUCHER_PARTIAL_REDEEM,
                receipt_status.PAYMENT_SUCCESS,
                receipt_status.DEPOSIT_CHARGE_SUCCESS,
                # was edited out - successor is paid
                receipt_status.ARCHIVED,
            ]
            and not refund_receipt
        ):

            RegisterOperation.objects.bulk_create(
                [
                    RegisterOperation(
                        register=self.register,
                        receipt=receipt,
                        payment_type=row.payment_type,
                        payment_row=row,
                        amount=(
                            row.amount
                            if status != receipt_status.ARCHIVED
                            else -row.amount  # remove from register
                        ),
                        type=RegisterOperation.REGISTER_OPERATION_TYPES__TRANSACTION,
                        operator=self.operator,
                    )
                    for row in payment_rows
                    if row.status not in receipt_status.STATUSES_WITHOUT_MONEY
                ]
            )

        # If transacion is type refunded or chargeBack, but register is closed
        # open new one
        operator = self.operator if self.operator else self.pos.business.owner

        if self.pos.registers_enabled and self.register and refund_receipt:
            temp_register = None
            # handle register
            if not self.register.is_open:
                temp_register = Register(
                    pos=self.pos,
                    is_open=True,
                    opened_by=operator,
                    opening_cash=0,
                )
                temp_register.save()

            RegisterOperation.objects.bulk_create(
                [
                    RegisterOperation(
                        register=temp_register if temp_register else self.register,
                        receipt=receipt,
                        payment_type=row.payment_type,
                        payment_row=row,
                        amount=(-row.amount),  # remove from register
                        type=RegisterOperation.REGISTER_OPERATION_TYPES__TRANSACTION,
                        operator=operator,
                    )
                    for row in payment_rows
                    if row.status in [receipt_status.CHARGEBACK, receipt_status.REFUNDED]
                ]
            )

            if temp_register:
                temp_register.is_open = False
                temp_register.closed_by = operator
                temp_register.closed_at = tznow(tz=self.pos.business.get_timezone())

                # save RegisterClosingAmounts
                for summary in temp_register.all_summaries:
                    if summary.countable:
                        summary.save()

                temp_register.save()

        # aggregate statistics for past charge dates
        business = self.pos.business
        tz = business.get_timezone()
        charge_date = self.charge_date.astimezone(tz).date()
        if charge_date <= business.tznow.date():
            bump_document(
                River.AGGREGATE_STATISTICS,
                [(business.id, charge_date.strftime(settings.DATE_FORMAT))],
            )

        from webapps.marketplace.tasks import update_transacion_row_amount

        update_transacion_row_amount.delay(self.id)

        # update business timestamp
        BusinessVersion.update(business.id)
        if status == receipt_status.PAYMENT_SUCCESS:
            analytics_checkout_transaction_completed_task.delay(
                transaction_id=self.id,
                context={
                    'business_id': business.id,
                    'event_type': EventType.BUSINESS,
                },
            )
        if not ignore_analytics:
            self._analytics_pba_completed_trigger(
                payment_rows[-1].payment_type.code,
                status,
                business.id,
            )

        if status == receipt_status.PAYMENT_SUCCESS:
            self.send_info_transaction_succeeded()
        update_pending_payment_appointment(
            transaction=self,
            changed_user=operator,
        )

    def send_info_transaction_succeeded(self):
        from lib.feature_flag.feature import LoyaltyProgramFlag
        from service.pos.publishers.loyalty_program import LoyaltyProgramTransactionDonePublisher
        from webapps.pos.messages.transaction import TransactionSucceededMessage

        user_data = UserData(key=f'business_{self.pos.business_id}')
        if (
            self.customer_card_id
            and self.transaction_type == self.TRANSACTION_TYPE__PAYMENT
            and LoyaltyProgramFlag(user_data)
        ):
            TransactionSucceededMessage(self).publish()
            # DEPRECATED should be removed after LoyaltyProgram is migrated fully to new topics
            LoyaltyProgramTransactionDonePublisher(  # pylint: disable=unexpected-keyword-arg
                business=self.pos.business, transaction=self
            ).publish()

        elif (
            BooksyGiftcardsEnabledFlag()
            and self.transaction_type == self.TRANSACTION_TYPE__PAYMENT
            and self.used_booksy_gift_card
        ):
            TransactionSucceededMessage(self).publish()

    def _analytics_pba_completed_trigger(self, analytics_payment_code, status, business_id):
        analytics_pba_statuses = (
            receipt_status.PAYMENT_SUCCESS,
            receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
            receipt_status.PREPAYMENT_SUCCESS,
        )
        analytics_payment_codes = (PaymentTypeEnum.PAY_BY_APP, PaymentTypeEnum.PREPAYMENT)
        if status in analytics_pba_statuses and analytics_payment_code in analytics_payment_codes:
            analytics_payment_transaction_completed_task.delay(
                transaction_id=self.id,
                analytics_payment_code=analytics_payment_code,
                context={
                    'business_id': business_id,
                },
            )

    def _products_stock_restore_at_warehouse(self, operator, doc_type):
        warehouse_documents = WarehouseDocument.objects.filter(
            transaction=self,
            type=doc_type,
        )
        for warehouse_document in warehouse_documents:
            warehouse = warehouse_document.warehouse
            pm_document = WarehouseDocument(
                warehouse=warehouse,
                transaction=self,
                type=WarehouseDocumentType.PM,
            )
            issuing_staffer = Resource.objects.filter(
                business=warehouse.business,
                staff_user=operator,
            ).first()
            pm_document.issuing_staffer = issuing_staffer
            pm_document_rows = []
            for document_row in warehouse_document.rows.all():
                commodity = document_row.commodity
                document_row = WarehouseDocumentRow(
                    document=pm_document,
                    commodity_name=commodity.name,
                    commodity=commodity,
                    quantity=document_row.quantity,
                    net_price=document_row.net_price,
                    gross_price=document_row.gross_price,
                    tax=document_row.tax,
                    is_full_package_expenditure=document_row.is_full_package_expenditure,
                )
                pm_document_rows.append(document_row)
                if doc_type == WarehouseDocumentType.WZ:
                    commodity.increase_packages(warehouse, document_row.quantity)
                elif doc_type == WarehouseDocumentType.RW:
                    commodity.increase_volume(warehouse, document_row.quantity)
            if pm_document_rows:
                pm_document.save()
                for pm_row in pm_document_rows:
                    pm_row.document = pm_document
                WarehouseDocumentRow.objects.bulk_create(pm_document_rows)

    def _voucher_payment_apply(self):
        from webapps.voucher.models import VoucherChangeLog

        payment_rows = self.latest_receipt.payment_rows.filter(
            parent_payment_row__isnull=True,
        )
        for pr in payment_rows:
            if not pr.voucher:
                continue

            if pr.payment_type.code == PaymentTypeEnum.EGIFT_CARD:
                pr.voucher.current_balance -= pr.amount
                pr.voucher.save()

            if pr.payment_type.code == PaymentTypeEnum.PACKAGE:
                sv_id = pr.voucher_service.id
                voucher_sv = pr.voucher.services.get(service_variant_id=sv_id)
                voucher_sv.amount -= 1
                voucher_sv.save()

                if pr.voucher.current_balance:
                    pr.voucher.current_balance -= voucher_sv.item_price
                    pr.voucher.save()

            pr.voucher.update_status()

            pr.voucher.log_changes_async(
                log_type=VoucherChangeLog.VOUCHER_CHANGE__SALE, payment_row_id=pr.id
            )

    def _commissions_apply(self):
        from webapps.pos.tools import CommissionRater

        rater = CommissionRater(pos=self.pos, transaction=self)
        rater.create_commission_reports()

    def _vouchers_activate(self):
        from webapps.voucher.models import VoucherChangeLog

        transaction_rows = (
            self.rows.filter(
                voucher__isnull=False,
            )
            .select_related(
                'voucher',
                'voucher__voucher_template',
                'voucher__customer',
                'voucher__customer__user',
                'voucher__customer__business',
                'voucher__customer__business__owner',
                'voucher__pos',
                'voucher__pos__business',
            )
            .prefetch_related(
                'voucher__services',
                Prefetch(
                    'voucher__transaction_rows',
                    queryset=TransactionRow.objects.select_related(
                        'transaction__customer_card',
                    ),
                    to_attr='cached_tr_rows',
                ),
            )
        )
        for row in transaction_rows:
            row.voucher.update_status()
            db_transaction.on_commit(
                partial(
                    voucher_activated_event.send,
                    row.voucher.id,
                    bci_id=self.customer_card.id if self.customer_card else None,
                )
            )
            row.voucher.log_changes_async(
                log_type=VoucherChangeLog.VOUCHER_CHANGE__EDIT,
            )

    def action_allowed(self, action):
        # base transaction_type/status check
        required_type, required_statuses = self.ALLOWED_ACTIONS[action]
        allowed = (
            self.transaction_type == required_type
            and (self.latest_receipt and self.latest_receipt.status_code) in required_statuses
        )

        # other action-specific checks
        if action == enums.CUSTOMER_ACTION__SET_TIP_RATE:
            # tips allowed if enabled in pos
            allowed = allowed and self.pos.tips_enabled
        elif action in [
            enums.BUSINESS_ACTION__CHARGE_DEPOSIT,
            enums.BUSINESS_ACTION__CANCEL_DEPOSIT,
        ]:
            # appointment status must be cancel or no-show
            allowed_statuses = [
                Appointment.STATUS.NOSHOW,
                Appointment.STATUS.CANCELED,
            ]
            allowed = self._check_allowed_appointment_statuses(allowed, allowed_statuses)
        elif action == enums.CUSTOMER_ACTION__RETRY_PAYMENT:
            allowed = (
                allowed
                and self.appointment.type == Appointment.TYPE.BUSINESS
                and PrepaymentsForBusinessAppointmentEnabled(
                    UserData(
                        custom={CustomUserAttributes.BUSINESS_ID: self.appointment.business_id}
                    )
                )
            )
            # appointment status must be waiting_for_prepayment
            allowed_statuses = [Appointment.STATUS.PENDING_PAYMENT]
            allowed = self._check_allowed_appointment_statuses(allowed, allowed_statuses)
        elif action == enums.BUSINESS_ACTION__RETRY_KIP:
            return self.latest_receipt.status_code in (
                receipt_status.PREPAYMENT_FAILED,
                receipt_status.PAYMENT_FAILED,
            )
        return allowed

    def _check_allowed_appointment_statuses(self, allowed: bool, allowed_statuses: list):
        if not allowed:
            return False
        appointment = self._state.fields_cache.get('appointment')
        qset = TransactionRow.objects.filter(
            transaction_id=self.id,
            type=TransactionRow.TRANSACTION_ROW_TYPE__DEPOSIT,
        )
        if appointment is None:
            qset = qset.exclude(
                Q(subbooking__appointment__status__in=allowed_statuses)
                | Q(addon_use__subbooking__appointment__status__in=allowed_statuses)
            )
        elif appointment.status in allowed_statuses:
            return True
        return not qset.exists()

    def series(self) -> QuerySet:
        """Returns all related Transactions connected in series."""
        return Transaction.objects.filter(series_id=self.series_id).prefetch_all().order_by('id')

    def readable_customer(self):
        if self.customer is not None:
            return f'Customer id {self.customer.id}. email: {self.customer.email}'
        return 'No customer was found'

    def readable_fullname(self):
        if self.customer_card is not None:
            return f'{self.customer_card.first_name} {self.customer_card.last_name}'

    def readable_appointment(self):
        if self.appointment is not None:
            return f'Appointment ID {self.appointment.id}.'
        return 'No appointment was found'

    @staticmethod
    def post_save_handler(sender, instance, created, **kwargs):  # pylint: disable=unused-argument
        from webapps.sequencing_number.models import SequenceRecord

        try:
            business = instance.pos.business
        except AttributeError:
            return
        if not business:
            return

        if created and not instance.parent_txn:
            create_sequence_record(business.id, SALES_DOCUMENT, instance.id)
        elif instance.parent_txn:
            parent_document = SequenceRecord.objects.filter(
                business=business,
                type=SALES_DOCUMENT,
                related_document_id=instance.parent_txn.id,
            ).first()
            if not parent_document:
                return
            parent_document.related_document_id = instance.id
            parent_document.save(
                ignore_duplicate=True,
                checked_before_save=True,
            )

    @property
    def commodity_usage(self):
        from webapps.warehouse.models import WarehouseFormulaRow

        if not self.id:
            return []
        reported_usage = WarehouseFormulaRow.objects.filter(
            transactions__id=self.id,
        )
        return reported_usage

    def can_generate_invoice(self):
        msg = None

        transaction_with_invoice_exists_in_series = Transaction.objects.filter(
            series_id=self.series_id,
            customer_invoices__isnull=False,
            customer_invoices__deleted__isnull=True,
        ).exists()

        is_paid_with_voucher = (
            self.latest_receipt
            and self.latest_receipt.payment_rows.filter(
                payment_type__code__in=PaymentType.VOUCHER_TYPES,
            ).exists()
        )

        if not self.latest_receipt:
            msg = _('Transaction has no receipt')
        elif self.latest_receipt.status_code != receipt_status.PAYMENT_SUCCESS:
            msg = _('You can only create invoices for fully paid transactions')
        elif self.customer_invoices.first():
            msg = _('Transaction already has an invoice')
        elif self.total == 0:
            msg = _('Cannot create invoice for transaction with total amount equal to 0')
        elif transaction_with_invoice_exists_in_series:
            msg = _('An invoice already exists for a previous receipt')
        elif is_paid_with_voucher:
            msg = _('Cannot create invoice for transaction that is paid with voucher')

        return bool(not msg), msg

    @cached_property
    def warehouse_document(self):
        return self.warehouse_documents.last()

    @property
    def issuing_staffer(self):
        return self.warehouse_document and self.warehouse_document.issuing_staffer

    @property
    def confirming_staffer(self):
        return self.warehouse_document and self.warehouse_document.confirming_staffer

    @property
    def note(self):
        return self.warehouse_document and self.warehouse_document.note

    @property
    def entity(self) -> TransactionEntity:
        return TransactionEntity(id=self.id)


post_save.connect(Transaction.post_save_handler, Transaction)


class TransactionTaxSubtotal(UndeletableMixin, ArchiveModel):  # pylint: disable=abstract-method
    SUBTOTAL_TYPE__SERVICES = 'S'
    SUBTOTAL_TYPE__PRODUCTS = 'P'
    SUBTOTAL_TYPES = (
        (SUBTOTAL_TYPE__SERVICES, _('Tax Services')),
        (SUBTOTAL_TYPE__PRODUCTS, _('Tax Products')),
    )
    SUBTOTAL_TYPES_DICT = dict(SUBTOTAL_TYPES)

    id = models.AutoField(primary_key=True, db_column='transaction_taxsubtotal_id')
    transaction = models.ForeignKey(
        Transaction,
        related_name='tax_subtotals',
        on_delete=models.PROTECT,
    )
    tax_rate = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    tax_amount = models.DecimalField(max_digits=10, decimal_places=2)
    subtotal_type = models.CharField(max_length=1, choices=SUBTOTAL_TYPES)

    objects = ArchiveManager()
    all_objects = SoftDeleteManager()


class TransactionRowQuerySet(AutoUpdateQuerySet):

    def annotate_booking_staffer_id(self):
        """Annotate a 'booking_staffer_id' field to simplify statistics."""
        return self.annotate(
            # TODO fix in separate flow
            booking_staffer_id=RawSQL(  # nosemgrep: avoid-raw-sql
                """(
                    SELECT bbr.resource_id
                    FROM booking_bookingresource bbr
                    JOIN business_resource br USING (resource_id)
                    WHERE
                        br.type=%s AND
                        bbr.subbooking_id = pos_transactionrow.subbooking_id
                    LIMIT 1
                )""",
                (Resource.STAFF,),
            ),
        )

    def annotate_transaction_bci_first_last_name(self):
        empty_first_name = Q(transaction__customer_card__first_name__isnull=True) | Q(
            transaction__customer_card__first_name=''
        )
        empty_user_first_name = Q(transaction__customer_card__user__first_name__isnull=True) | Q(
            transaction__customer_card__user__first_name=''
        )
        empty_last_name = Q(transaction__customer_card__last_name__isnull=True) | Q(
            transaction__customer_card__last_name=''
        )
        empty_user_last_name = Q(transaction__customer_card__user__last_name__isnull=True) | Q(
            transaction__customer_card__user__last_name=''
        )
        email_exists = Q(transaction__customer_card__email__isnull=False) & ~Q(
            transaction__customer_card__email=''
        )
        phone_exists = Q(transaction__customer_card__cell_phone__isnull=False) & ~Q(
            transaction__customer_card__cell_phone=''
        )

        missing_name = Q(
            empty_first_name,
            empty_last_name,
            empty_user_first_name,
            empty_user_last_name,
        )

        return self.annotate(
            # If BCI was created through logging from biz owner account
            # the first and last name fields are empty
            first_name=Case(
                When(
                    empty_first_name & ~email_exists,
                    then=F('transaction__customer_card__user__first_name'),
                ),
                default=F('transaction__customer_card__first_name'),
            ),
            last_name=Case(
                When(
                    empty_last_name & ~empty_user_last_name,
                    then=F('transaction__customer_card__user__last_name'),
                ),
                When(
                    missing_name & email_exists,
                    then=F('transaction__customer_card__email'),
                ),
                When(
                    missing_name & phone_exists & ~email_exists,
                    then=F('transaction__customer_card__cell_phone'),
                ),
                default=F('transaction__customer_card__last_name'),
                output_field=models.CharField(),
            ),
        )


class TransactionRowManager(AutoUpdateManager.from_queryset(TransactionRowQuerySet)):
    pass


class TransactionRow(UndeletableMixin, ArchiveModel):  # pylint: disable=abstract-method
    TRANSACTION_ROW_TYPE__ADDON = 'A'
    TRANSACTION_ROW_TYPE__SERVICE = 'S'
    TRANSACTION_ROW_TYPE__PRODUCT = 'P'
    TRANSACTION_ROW_TYPE__DEPOSIT = 'D'
    TRANSACTION_ROW_TYPE__VOUCHER = 'V'
    TRANSACTION_ROW_TYPE__TRAVEL_FEE = 'T'
    TRANSACTION_ROW_TYPES = (
        (TRANSACTION_ROW_TYPE__SERVICE, _('Service')),
        (TRANSACTION_ROW_TYPE__PRODUCT, _('Product')),
        (TRANSACTION_ROW_TYPE__ADDON, _('Add-on')),
        (TRANSACTION_ROW_TYPE__DEPOSIT, _('Cancellation Fee')),
        (TRANSACTION_ROW_TYPE__VOUCHER, _('Voucher')),
        (TRANSACTION_ROW_TYPE__TRAVEL_FEE, _('Travel Fee')),
    )
    id = models.AutoField(primary_key=True, db_column='transaction_row_id')
    type = models.CharField(
        max_length=1, choices=TRANSACTION_ROW_TYPES, default=TRANSACTION_ROW_TYPE__SERVICE
    )
    transaction = models.ForeignKey(
        Transaction,
        related_name='rows',
        on_delete=models.PROTECT,
    )
    subbooking = models.ForeignKey(
        SubBooking,
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
        db_constraint=False,
        related_name='transaction_rows',
    )
    service_variant = models.ForeignKey(
        ServiceVariant,
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
        db_constraint=False,
        related_name='transaction_rows',
    )
    addon_use = models.ForeignKey(
        ServiceAddOnUse,
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
        db_constraint=False,
        related_name='transaction_rows',
    )
    product = models.ForeignKey(
        'warehouse.Commodity',
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
        db_constraint=False,
        related_name='transaction_rows',
    )
    voucher = models.ForeignKey(
        'voucher.Voucher',
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
        db_constraint=False,
        related_name='transaction_rows',
    )
    commission_staffer = models.ForeignKey(
        Resource,
        null=True,
        blank=True,
        db_constraint=False,
        on_delete=models.DO_NOTHING,
    )
    commissions_last_edit = models.DateTimeField(null=True, blank=True)
    commissions_last_editor = models.ForeignKey(
        User,
        null=True,
        blank=True,
        on_delete=models.PROTECT,
    )
    order = models.PositiveSmallIntegerField(default=0)
    name_line_1 = models.CharField(max_length=255)
    name_line_2 = models.CharField(max_length=255, blank=True)
    quantity = models.PositiveSmallIntegerField(default=1)
    item_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    discount_rate = models.PositiveSmallIntegerField(
        default=0, help_text='Row discount rate (only for this row)'
    )
    discounted_item_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text='Item price after row discount, but before global discount',
    )
    tax_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    tax_rate = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    tax_type = models.CharField(
        max_length=1,
        null=True,
        blank=True,
        choices=POS.POS_TAX_MODES,
        default=None,
    )
    tax_excluded = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
    )
    tax_included = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
    )
    total = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text='Total before global discount but with excluded tax added '
        '(as displayed to the User)',
    )
    discounted_total = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text='Total after global discount, but without excluded tax ' '(used in commissions)',
    )
    net_total_wo_discount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text='What would a net total be if there were no discounts ' '(used in sales report)',
    )
    real_discount_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text='A real (row and global) discount amount ' '(used in sales report)',
    )
    net_total = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text='Total after global discount, but without all taxes ' '(used in sales report)',
    )
    gross_total = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text='Total after global discount, but with all taxes ' '(used in sales report)',
    )
    warehouse = models.ForeignKey(
        'warehouse.Warehouse',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )

    service_variant_version = models.PositiveIntegerField(null=True, default=0)
    service_variant_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
    )
    service_variant_type = models.CharField(
        max_length=1,
        null=True,
        blank=True,
        choices=PriceType.choices(),
    )
    service_variant_duration = IntervalField(
        null=True,
        blank=True,
        format='HM',
    )
    service_variant_combo_parent = models.ForeignKey(
        ServiceVariant,
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
        db_constraint=False,
    )

    basket_item_id = models.UUIDField(
        null=True,
        blank=True,
        db_index=True,
    )  # Model: PointOfSale.BasketItem

    objects = TransactionRowManager()
    all_objects = SoftDeleteManager()

    class Meta:
        ordering = ('order', 'id')
        indexes = [models.Index(fields=['service_variant', 'deleted', 'created'])]

    def __repr__(self):
        if self.product_id:
            object_type = 'product_id'
        elif self.service_variant_id:
            object_type = 'service_variant_id'
        elif self.voucher_id:
            object_type = 'voucher_id'
        else:
            object_type = 'subbooking_id'

        object_type_attr = getattr(self, object_type)
        return f'<TransactionRow {object_type}={object_type_attr} {self.discounted_total}>'

    def __str__(self):
        if self.product_id:
            object_type = 'product_id'
        elif self.service_variant_id:
            object_type = 'service_variant_id'
        elif self.voucher_id:
            object_type = 'voucher_id'
        else:
            object_type = 'subbooking_id'
        return f'TransactionRow {object_type}={getattr(self, object_type)} {self.discounted_total}'

    @property
    def voucher_template(self):
        return self.voucher and self.voucher.voucher_template

    @property
    def voucher_customer(self):
        return self.voucher and self.voucher.customer

    @property
    def valid_from(self):
        return self.voucher and self.voucher.valid_from

    @property
    def type_identifier(self):
        return self.subbooking or self.deposit or self.service_variant or self.id

    @property
    def commissions_last_editor_name(self):
        biz = self.transaction.pos.business
        usr = self.commissions_last_editor

        if not usr:
            return ''

        resources = self.commissions_last_editor.staffers.all()

        for resource in resources:
            if resource.business == biz and resource.type == Resource.STAFF:
                return resource.name

        return usr.full_name

    @property
    def total_wo_discount(self):
        item_price = self.item_price if self.item_price is not None else Decimal(0)
        return item_price * self.quantity

    @classmethod
    def get_staffer_name(cls, biz, usr):
        if not usr:
            return ''
        resource = Resource.objects.filter(
            type=Resource.STAFF,
            business=biz,
            staff_user=usr,
            active=True,
            deleted__isnull=True,
        ).first()
        if resource:
            return resource.name

        # deleted staffer
        resource = Resource.objects.filter(
            type=Resource.STAFF,
            business=biz,
            staff_user=usr,
        ).first()
        if resource:
            return resource.name

        return usr.full_name

    @classmethod
    def get_empty_row(cls):
        return {
            'subbooking': None,
            'service_variant': None,
            'service_variant_version': 0,
            'service_variant_price': None,
            'service_variant_type': None,
            'service_variant_duration': None,
            'service_variant_combo_parent': None,
            'product': None,
            'voucher': None,
            'voucher_template': None,
            'voucher_customer': None,
            'warehouse': None,
        }

    def log_commission_changes(self, operator=None):
        """
        Logs related Commission. If TransactionRow doesn't have it use only
        dict with back-relation.

        """
        from webapps.pos.serializers import CommissionLogSerializer

        if hasattr(self, 'commission'):
            commission = self.commission
        else:
            commission = {'row': self}

        data = CommissionLogSerializer(
            instance=commission,
        ).data

        CommissionChangeLog.objects.create(
            transaction_row=self,
            operator_id=operator.id if operator else None,
            data=json.dumps(data, sort_keys=True),
        )


class TransactionTip(ArchiveModel):
    id = models.AutoField(primary_key=True, db_column='tip_id')
    rate = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    type = models.CharField(max_length=1, choices=SimpleTip.TIP_TYPES)
    amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    transaction = models.OneToOneField(
        'Transaction', related_name='tip', on_delete=models.DO_NOTHING
    )
    basket_tip_id = models.UUIDField(
        null=True,
        db_index=True,
    )  # Model: PointOfSale: BasketTip


class TransactionTipRow(ArchiveModel):

    rate = models.PositiveSmallIntegerField()
    amount = models.DecimalField(
        decimal_places=2,
        max_digits=10,
    )
    staffer = models.ForeignKey(Resource, on_delete=models.DO_NOTHING)
    transaction_tip = models.ForeignKey(
        TransactionTip,
        related_name='tip_rows',
        on_delete=models.CASCADE,
    )
    basket_tip_id = models.UUIDField(
        null=True,
        db_index=True,
    )  # Model: PointOfSale: BasketTip

    def __str__(self):
        return (
            f'{self.staffer_id}, ' f'{self.rate}, ' f'{self.amount}, ' f'{self.transaction_tip_id}'
        )


class ActionMixin:
    def execute_action(self, action, data=None):
        action_method = f'{action}_action'
        if hasattr(self, action_method):
            return getattr(self, action_method)(data)


class ActiveAutoUpdateManager(ActiveManager.from_queryset(AutoUpdateQuerySet)):
    use_in_migrations = True


class PaymentMethod(ArchiveModel):
    id = models.AutoField(primary_key=True, db_column='payment_method_id')
    user = models.ForeignKey(
        User,
        related_name='payment_methods',
        on_delete=models.CASCADE,
    )
    provider = models.CharField(max_length=16, choices=PAYMENT_PROVIDER_CHOICES)
    provider_ref = models.CharField(max_length=128, null=False, blank=False)
    default = models.BooleanField(blank=False, default=False)
    method_type = models.CharField(
        max_length=16,
        choices=enums.PAYMENT_METHOD_TYPES,
    )
    # specific to some types
    card_type = models.CharField(max_length=12, null=True, blank=True, choices=enums.CARD_TYPES)
    card_last_digits = models.CharField(max_length=4, null=True, blank=True)
    active = models.BooleanField(default=True)
    alias = models.CharField(max_length=128, blank=True, null=True)
    expiry_month = models.IntegerField(blank=True, null=True)
    expiry_year = models.IntegerField(blank=True, null=True)
    cardholder_name = models.CharField(max_length=128, blank=True, null=True)
    zip_code = models.CharField(max_length=32, blank=True, null=True)

    tokenized_pm_id = models.UUIDField(
        null=True,
        blank=True,
        db_index=True,
    )  # Model: PaymentProviders.TokenizedPaymentMethod

    objects = ActiveAutoUpdateManager()
    all_objects = models.Manager()

    class Meta:
        ordering = ('-default', '-created')

    @staticmethod
    def post_save_handler(sender, instance, **kwargs):  # pylint: disable=unused-argument
        """Unset default on other PaymentMethods of the user."""
        if instance.default:
            instance.user.payment_methods.exclude(
                id=instance.id,
            ).filter(
                provider=instance.provider,
                default=True,
            ).update(
                default=False,
            )


post_save.connect(PaymentMethod.post_save_handler, PaymentMethod)


class CommissionDefaults(ActionMixin, ArchiveModel):
    COMMISSION_TYPE__AMOUNT = CommissionType.AMOUNT
    COMMISSION_TYPE__PERCENT = CommissionType.PERCENT
    COMMISSION_TYPES = CommissionType.choices()

    COMMISSION_FOR__CASHIER = CommissionForType.CASHIER
    COMMISSION_FOR__RESOURCE = CommissionForType.RESOURCE
    COMMISSION_FOR_TYPES = CommissionForType.choices()

    ACTION__APPLY = CommissionAction.APPLY
    ACTION__APPLY_PRODUCTS = CommissionAction.APPLY_PRODUCTS
    ACTION__APPLY_SERVICES = CommissionAction.APPLY_SERVICES
    ACTION__CLEAR = CommissionAction.CLEAR
    ACTION__STAFF_APPLY = CommissionAction.STAFF_APPLY
    ACTION__STAFF_APPLY_PRODUCTS = CommissionAction.STAFF_APPLY_PRODUCTS
    ACTION__STAFF_APPLY_SERVICES = CommissionAction.STAFF_APPLY_SERVICES
    ACTION__STAFF_CLEAR = CommissionAction.STAFF_CLEAR
    ACTIONS = CommissionAction.choices()

    id = models.AutoField(primary_key=True, db_column='commission_defaults_id')
    pos = models.OneToOneField(
        POS,
        related_name='commission_defaults',
        on_delete=models.CASCADE,
    )
    product_commission_type = models.CharField(
        max_length=1, choices=COMMISSION_TYPES, default=COMMISSION_TYPE__PERCENT
    )
    product_commission_rate = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    product_commission_for = models.CharField(
        max_length=1, choices=COMMISSION_FOR_TYPES, default=COMMISSION_FOR__RESOURCE
    )
    service_commission_type = models.CharField(
        max_length=1, choices=COMMISSION_TYPES, default=COMMISSION_TYPE__PERCENT
    )
    service_commission_rate = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    # Vouchers
    egift_commission_type = models.CharField(
        max_length=1, choices=COMMISSION_TYPES, default=COMMISSION_TYPE__PERCENT
    )
    egift_commission_rate = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    egift_commission_for = models.CharField(
        max_length=1, choices=COMMISSION_FOR_TYPES, default=COMMISSION_FOR__RESOURCE
    )

    membership_commission_type = models.CharField(
        max_length=1, choices=COMMISSION_TYPES, default=COMMISSION_TYPE__PERCENT
    )
    membership_commission_rate = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    membership_commission_for = models.CharField(
        max_length=1, choices=COMMISSION_FOR_TYPES, default=COMMISSION_FOR__RESOURCE
    )

    package_commission_type = models.CharField(
        max_length=1, choices=COMMISSION_TYPES, default=COMMISSION_TYPE__PERCENT
    )
    package_commission_rate = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    package_commission_for = models.CharField(
        max_length=1, choices=COMMISSION_FOR_TYPES, default=COMMISSION_FOR__RESOURCE
    )

    objects = ArchiveManager()
    all_objects = models.Manager()

    def __repr__(self):
        return f'<CommissionDefaults: pos_id={self.pos_id}>'

    def apply_action(self, data=None):  # pylint: disable=unused-argument
        """Deletes commissions"""
        CommissionRate.objects.filter(pos=self.pos).delete()
        StaffCommissionRate.objects.filter(pos=self.pos).delete()
        StaffCommissionDefault.objects.filter(resource__business=self.pos.business).delete()

    def apply_products_action(self, data=None):  # pylint: disable=unused-argument
        """Deletes commissions for products."""
        CommissionRate.objects.filter(
            pos=self.pos,
            product__isnull=False,
        ).delete()
        StaffCommissionRate.objects.filter(
            pos=self.pos,
            product__isnull=False,
        ).delete()

    def apply_services_action(self, data=None):  # pylint: disable=unused-argument
        """Deletes commissions for services."""
        CommissionRate.objects.filter(
            pos=self.pos,
            service_variant__isnull=False,
        ).delete()
        StaffCommissionRate.objects.filter(
            pos=self.pos,
            service_variant__isnull=False,
        ).delete()

    def apply_egifts_action(self, data=None):  # pylint: disable=unused-argument
        """Deletes commissions for egifts."""
        CommissionRate.objects.filter(
            pos=self.pos,
            service_variant__isnull=False,
            voucher_template__isnull=False,
            voucher_template__type=Voucher.VOUCHER_TYPE__EGIFT_CARD,
        ).delete()
        StaffCommissionRate.objects.filter(
            pos=self.pos,
            service_variant__isnull=False,
            voucher_template__isnull=False,
            voucher_template__type=Voucher.VOUCHER_TYPE__EGIFT_CARD,
        ).delete()

    def apply_memberships_action(self, data=None):  # pylint: disable=unused-argument
        """Deletes commissions for memberships."""
        CommissionRate.objects.filter(
            pos=self.pos,
            service_variant__isnull=False,
            voucher_template__isnull=False,
            voucher_template__type=Voucher.VOUCHER_TYPE__MEMBERSHIP,
        ).delete()
        StaffCommissionRate.objects.filter(
            pos=self.pos,
            service_variant__isnull=False,
            voucher_template__isnull=False,
            voucher_template__type=Voucher.VOUCHER_TYPE__MEMBERSHIP,
        ).delete()

    def apply_packages_action(self, data=None):  # pylint: disable=unused-argument
        """Deletes commissions for packages."""
        CommissionRate.objects.filter(
            pos=self.pos,
            service_variant__isnull=False,
            voucher_template__isnull=False,
            voucher_template__type=Voucher.VOUCHER_TYPE__PACKAGE,
        ).delete()
        StaffCommissionRate.objects.filter(
            pos=self.pos,
            service_variant__isnull=False,
            voucher_template__isnull=False,
            voucher_template__type=Voucher.VOUCHER_TYPE__PACKAGE,
        ).delete()

    def clear_action(self, data=None):  # pylint: disable=unused-argument
        """Set default commissions to 0. Deletes commissions for business."""
        self.product_commission_rate = 0
        self.service_commission_rate = 0
        self.egift_commission_rate = 0
        self.membership_commission_rate = 0
        self.package_commission_rate = 0
        self.save()
        self.apply_action()

    @property
    def has_any_commission_set(self) -> bool:
        return any(
            [
                self.product_commission_rate,
                self.service_commission_rate,
                self.egift_commission_rate,
                self.membership_commission_rate,
                self.package_commission_rate,
            ]
        )

    def staff_apply_action(self, data):
        """Deletes commissions for staffer."""
        StaffCommissionRate.objects.filter(
            pos=self.pos,
            resource=data['resource_id'],
        ).delete()

    def staff_apply_products_action(self, data):
        """Deletes staffer commissions."""
        StaffCommissionRate.objects.filter(
            pos=self.pos,
            resource=data['resource_id'],
            product__isnull=False,
        ).delete()

    def staff_apply_services_action(self, data):
        """Deletes staffer commissions for products."""
        StaffCommissionRate.objects.filter(
            pos=self.pos,
            resource=data['resource_id'],
            service_variant__isnull=False,
        ).delete()

    def staff_apply_egifts_action(self, data):
        """Deletes staffer commissions for egifts."""
        StaffCommissionRate.objects.filter(
            pos=self.pos,
            resource=data['resource_id'],
            voucher_template__isnull=False,
            voucher_template__type=Voucher.VOUCHER_TYPE__EGIFT_CARD,
        ).delete()

    def staff_apply_memberships_action(self, data):
        """Deletes staffer commissions for memberships."""
        StaffCommissionRate.objects.filter(
            pos=self.pos,
            resource=data['resource_id'],
            voucher_template__isnull=False,
            voucher_template__type=Voucher.VOUCHER_TYPE__MEMBERSHIP,
        ).delete()

    def staff_apply_packages_action(self, data):
        """Deletes staffer commissions for packages."""
        StaffCommissionRate.objects.filter(
            pos=self.pos,
            resource=data['resource_id'],
            voucher_template__isnull=False,
            voucher_template__type=Voucher.VOUCHER_TYPE__PACKAGE,
        ).delete()

    def staff_clear_action(self, data):
        self.staff_apply_action(data)


class StaffCommissionDefault(ActionMixin, ArchiveModel):
    ACTIONS = StaffCommissionRateAction.choices()
    resource = models.ForeignKey(
        Resource,
        related_name='staff_commission_defaults',
        on_delete=models.CASCADE,
    )
    product_commission_type = models.CharField(
        max_length=1,
        choices=CommissionType.choices(),
        default=None,
        null=True,
        blank=True,
    )
    product_commission_rate = models.DecimalField(
        max_digits=10, decimal_places=2, default=None, blank=True, null=True
    )

    service_commission_type = models.CharField(
        max_length=1,
        choices=CommissionType.choices(),
        default=None,
        null=True,
        blank=True,
    )
    service_commission_rate = models.DecimalField(
        max_digits=10, decimal_places=2, default=None, blank=True, null=True
    )

    egift_commission_type = models.CharField(
        max_length=1,
        choices=CommissionType.choices(),
        default=None,
        null=True,
        blank=True,
    )
    egift_commission_rate = models.DecimalField(
        max_digits=10, decimal_places=2, default=None, blank=True, null=True
    )

    membership_commission_type = models.CharField(
        max_length=1,
        choices=CommissionType.choices(),
        default=None,
        null=True,
        blank=True,
    )
    membership_commission_rate = models.DecimalField(
        max_digits=10, decimal_places=2, default=None, blank=True, null=True
    )

    package_commission_type = models.CharField(
        max_length=1,
        choices=CommissionType.choices(),
        default=None,
        null=True,
        blank=True,
    )
    package_commission_rate = models.DecimalField(
        max_digits=10, decimal_places=2, default=None, blank=True, null=True
    )

    objects = ArchiveManager()
    all_objects = models.Manager()

    COMMON_FIELDS = [
        'product_commission_type',
        'product_commission_rate',
        'product_commission_for',
        'service_commission_type',
        'service_commission_rate',
        'egift_commission_type',
        'egift_commission_rate',
        'egift_commission_for',
        'membership_commission_type',
        'membership_commission_rate',
        'membership_commission_for',
        'package_commission_type',
        'package_commission_rate',
        'package_commission_for',
    ]

    @classmethod
    def get_with_defaults(cls, resource: Resource):
        pos = resource.business.pos
        staffer_defaults, _created = StaffCommissionDefault.objects.get_or_create(
            resource=resource,
        )
        defaults, _created = CommissionDefaults.objects.get_or_create(pos=pos)

        for field_name in cls.COMMON_FIELDS:
            staffer_value = getattr(staffer_defaults, field_name, None)

            if staffer_value is None:
                global_value = getattr(defaults, field_name)
                setattr(staffer_defaults, field_name, global_value)

        return staffer_defaults

    def default_action(self, data=None):  # pylint: disable=unused-argument
        for field_name in self.COMMON_FIELDS:
            setattr(self, field_name, None)

        self.save()

    def clear_action(self, data=None):  # pylint: disable=unused-argument
        self.product_commission_rate = 0
        self.service_commission_rate = 0
        self.egift_commission_rate = 0
        self.package_commission_rate = 0
        self.membership_commission_rate = 0
        self.save()

        StaffCommissionRate.objects.filter(resource=self.resource).delete()


class CommissionRate(ActionMixin, ArchiveModel):
    ACTIONS = CommissionRateAction.choices()

    id = models.AutoField(primary_key=True, db_column='commission_rate_id')
    pos = models.ForeignKey(
        POS,
        related_name='commission_rates',
        on_delete=models.CASCADE,
    )
    product = models.OneToOneField(
        'warehouse.Commodity',
        related_name='default_commission_rate',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    service_variant = models.OneToOneField(
        ServiceVariant,
        related_name='default_commission_rate',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    voucher_template = models.OneToOneField(
        'voucher.VoucherTemplate',
        related_name='default_commission_rate',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    type = models.CharField(max_length=1, choices=CommissionDefaults.COMMISSION_TYPES)
    rate = models.DecimalField(max_digits=10, decimal_places=2)

    objects = ArchiveManager()
    all_objects = models.Manager()

    def __repr__(self):
        if self.product:
            type_str = 'product_id='
            type_value = self.product_id
        elif self.service_variant:
            type_str = 'service_variant_id='
            type_value = self.service_variant_id
        elif self.voucher_template:
            type_str = 'voucher_template_id='
            type_value = self.voucher_template_id
        else:
            type_str = None
            type_value = None
        return f'<CommissionRate: {type_str}{type_value} {self.type} {self.rate:.2f}>'

    @classmethod
    def get_with_defaults(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        cls,
        pos: POS,
        resource: Resource = None,
        product_id: int = None,
        service_variant_id: int = None,
        voucher_template_id: int = None,
        voucher_type: str = None,
    ) -> t.Tuple['CommissionRate', dict]:
        """Return individual commissions for service/product/voucher_template
        and pos commission default.
        """
        if pos is None:
            # pylint: disable=broad-exception-raised
            raise RuntimeError('POS must not be None')
        if (bool(product_id) + bool(service_variant_id) + bool(voucher_template_id)) != 1:
            raise RuntimeError(  # pylint: disable=broad-exception-raised
                'Exactly one of: product_id, service_variant_id, '
                'voucher_template_id arguments must be non-empty'
            )

        instance = cls.objects.filter(
            pos=pos,
            product_id=product_id,
            service_variant_id=service_variant_id,
            voucher_template_id=voucher_template_id,
        ).first()

        if resource:
            defaults = StaffCommissionDefault.get_with_defaults(resource)
        else:
            defaults, _created = CommissionDefaults.objects.get_or_create(pos=pos)

        if product_id:
            instance_defaults = {
                'type': defaults.product_commission_type,
                'rate': defaults.product_commission_rate,
            }
        elif service_variant_id:
            instance_defaults = {
                'type': defaults.service_commission_type,
                'rate': defaults.service_commission_rate,
            }
        elif voucher_template_id:
            if voucher_type is None:
                voucher_type = VoucherTemplate.objects.get(id=voucher_template_id).type

            if voucher_type == Voucher.VOUCHER_TYPE__EGIFT_CARD:
                instance_defaults = {
                    'type': defaults.egift_commission_type,
                    'rate': defaults.egift_commission_rate,
                }
            elif voucher_type == Voucher.VOUCHER_TYPE__MEMBERSHIP:
                instance_defaults = {
                    'type': defaults.membership_commission_type,
                    'rate': defaults.membership_commission_rate,
                }
            elif voucher_type == Voucher.VOUCHER_TYPE__PACKAGE:
                instance_defaults = {
                    'type': defaults.package_commission_type,
                    'rate': defaults.package_commission_rate,
                }

        if not instance:
            instance = cls(
                pos=pos,
                product_id=product_id,
                service_variant_id=service_variant_id,
                voucher_template_id=voucher_template_id,
                type=instance_defaults['type'],  # pylint: disable=possibly-used-before-assignment
                rate=instance_defaults['rate'],  # pylint: disable=possibly-used-before-assignment
            )

        return instance, instance_defaults

    @classmethod
    def for_products(cls, pos: POS, product_ids: t.List[int]) -> t.List['CommissionRate']:
        """Return CommissionRate for products ids."""
        assert pos is not None

        product_ids = set(map(int, product_ids))

        defaults, _created = CommissionDefaults.objects.get_or_create(pos=pos)
        default_type = defaults.product_commission_type
        default_rate = defaults.product_commission_rate

        for instance in cls.all_objects.filter(
            pos=pos,
            product_id__in=product_ids,
        ).iterator():
            product_ids.remove(instance.product_id)
            yield instance

        for product_id in Commodity.objects.filter(
            business=pos.business,
            id__in=product_ids,
        ).values_list('id', flat=True):
            yield cls(
                pos=pos,
                product_id=product_id,
                type=default_type,
                rate=default_rate,
            )

    @classmethod
    def for_service_variants(
        cls, pos: POS, service_variant_ids: t.List[int]
    ) -> t.List['CommissionRate']:
        assert pos is not None

        service_variant_ids = set(map(int, service_variant_ids))

        defaults, _created = CommissionDefaults.objects.get_or_create(pos=pos)
        default_type = defaults.service_commission_type
        default_rate = defaults.service_commission_rate

        for instance in cls.all_objects.filter(
            pos=pos,
            service_variant_id__in=service_variant_ids,
        ).iterator():
            service_variant_ids.remove(instance.service_variant_id)
            yield instance

        for service_variant_id in ServiceVariant.objects.filter(
            service__business_id=pos.business_id,
            id__in=service_variant_ids,
        ).values_list('id', flat=True):
            yield cls(
                pos=pos, service_variant_id=service_variant_id, type=default_type, rate=default_rate
            )

    @classmethod
    def for_voucher_templates(
        cls, pos: POS, voucher_type: str, voucher_template_ids: t.List[int]
    ) -> t.List['CommissionRate']:
        assert pos is not None
        assert voucher_type in [choice[0] for choice in Voucher.TYPES]

        voucher_template_ids = set(map(int, voucher_template_ids))

        defaults, _created = CommissionDefaults.objects.get_or_create(pos=pos)

        if voucher_type == Voucher.VOUCHER_TYPE__EGIFT_CARD:
            default_type = defaults.egift_commission_type
            default_rate = defaults.egift_commission_rate

        elif voucher_type == Voucher.VOUCHER_TYPE__MEMBERSHIP:
            default_type = defaults.membership_commission_type
            default_rate = defaults.membership_commission_rate

        elif voucher_type == Voucher.VOUCHER_TYPE__PACKAGE:
            default_type = defaults.package_commission_type
            default_rate = defaults.package_commission_rate
        else:
            assert False

        for instance in cls.all_objects.filter(
            pos=pos,
            voucher_template_id__in=voucher_template_ids,
            voucher_template__type=voucher_type,
        ).iterator():
            voucher_template_ids.remove(instance.voucher_template_id)
            yield instance

        for voucher_template_id in VoucherTemplate.objects.filter(
            pos=pos,
            type=voucher_type,
            id__in=voucher_template_ids,
        ).values_list('id', flat=True):
            yield cls(
                pos=pos,
                voucher_template_id=voucher_template_id,
                type=default_type,
                rate=default_rate,
            )

    def apply_action(self, data=None):  # pylint: disable=unused-argument
        """Deletes StaffCommissionRates."""
        StaffCommissionRate.objects.filter(
            pos=self.pos, product=self.product, service_variant=self.service_variant
        ).delete()


class StaffCommissionRate(ActionMixin, ArchiveModel):
    """
    StaffCommissionRate

    product=None and service_variant=None is a special case;
    used only to clear all commissions for given staffer
    """

    ACTIONS = StaffCommissionRateAction.choices()

    id = models.AutoField(primary_key=True, db_column='staff_commission_rate_id')
    pos = models.ForeignKey(
        POS,
        related_name='staff_commission_rates',
        on_delete=models.CASCADE,
    )
    resource = models.ForeignKey(
        Resource,
        related_name='commission_rates',
        on_delete=models.CASCADE,
    )
    product = models.ForeignKey(
        'warehouse.Commodity',
        related_name='staff_commission_rates',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    service_variant = models.ForeignKey(
        ServiceVariant,
        related_name='staff_commission_rates',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    voucher_template = models.ForeignKey(
        'voucher.VoucherTemplate',
        related_name='staff_commission_rates',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
    )
    type = models.CharField(
        max_length=1,
        choices=CommissionDefaults.COMMISSION_TYPES,
        default=CommissionDefaults.COMMISSION_TYPE__PERCENT,
    )
    rate = models.DecimalField(max_digits=10, decimal_places=2)

    objects = ArchiveManager()
    all_objects = models.Manager()

    class Meta:
        unique_together = (('resource', 'product'), ('resource', 'service_variant'))

    def __repr__(self):
        if self.product:
            type_str = 'product_id='
            type_value = self.product_id
        if self.service_variant:
            type_str = 'service_variant_id='
            type_value = self.service_variant_id
        if self.voucher_template:
            type_str = 'voucher_template_id='
            type_value = self.voucher_template_id
        return (
            f'<StaffCommissionRate: resource_id={self.resource_id} '
            f'{type_str}{type_value} {self.type} {self.rate:.2f}>'  # pylint: disable=possibly-used-before-assignment
        )

    @classmethod
    def get_with_defaults(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        cls,
        pos: POS,
        resource: Resource,
        product_id: int = None,
        service_variant_id: int = None,
        voucher_template_id: int = None,
        voucher_type: int = None,
    ) -> t.Tuple['StaffCommissionRate', dict]:
        """Returns staff commission rate for specific
        service/product/voucher_template and individual commission set per
        specific service/product/voucher_template on business level.
        """
        assert pos and resource
        if (bool(product_id) + bool(service_variant_id) + bool(voucher_template_id)) != 1:
            raise RuntimeError(  # pylint: disable=broad-exception-raised
                'Exactly one of: product_id, service_variant_id, '
                'voucher_template_id arguments must be non-empty'
            )

        instance = (
            cls.objects.filter(pos=pos, resource=resource)
            .filter(
                product_id=product_id,
                service_variant_id=service_variant_id,
                voucher_template_id=voucher_template_id,
            )
            .first()
        )

        defaults = CommissionRate.get_with_defaults(
            pos=pos,
            resource=resource,
            product_id=product_id,
            service_variant_id=service_variant_id,
            voucher_template_id=voucher_template_id,
            voucher_type=voucher_type,
        )[0]

        instance_defaults = {'type': defaults.type, 'rate': defaults.rate}

        if not instance:
            instance = cls(
                pos=pos,
                resource=resource,
                product_id=product_id,
                service_variant_id=service_variant_id,
                voucher_template_id=voucher_template_id,
                type=instance_defaults['type'],
                rate=instance_defaults['rate'],
            )

        return instance, instance_defaults

    @classmethod
    def for_products(
        cls, pos: POS, resource: Resource, product_ids: t.List[int]
    ) -> t.List['StaffCommissionRate']:
        """Returns StaffCommissionRate for selected products."""
        assert pos and resource

        product_ids = set(map(int, product_ids))

        defaults, _created = CommissionDefaults.objects.get_or_create(pos=pos)
        default_type = defaults.product_commission_type
        default_rate = defaults.product_commission_rate

        for instance in cls.all_objects.filter(
            pos=pos,
            resource_id=resource.id,
            product_id__in=product_ids,
        ).iterator():
            product_ids.remove(instance.product_id)
            yield instance

        for product_id in Commodity.objects.filter(
            business=pos.business,
            id__in=product_ids,
        ).values_list('id', flat=True):
            yield cls(
                pos=pos,
                resource=resource,
                product_id=product_id,
                type=default_type,
                rate=default_rate,
            )

    @classmethod
    def for_service_variants(
        cls, pos: POS, resource: Resource, service_variant_ids: t.List[int]
    ) -> t.List['StaffCommissionRate']:
        """Returns StaffCommissionRate for selected services."""
        assert pos and resource

        service_variant_ids = set(map(int, service_variant_ids))

        defaults, _created = CommissionDefaults.objects.get_or_create(pos=pos)
        default_type = defaults.service_commission_type
        default_rate = defaults.service_commission_rate

        for instance in cls.all_objects.filter(
            pos=pos,
            resource_id=resource.id,
            service_variant_id__in=service_variant_ids,
        ).iterator():
            service_variant_ids.remove(instance.service_variant_id)
            yield instance

        for service_variant_id in ServiceVariant.objects.filter(
            service__business_id=pos.business_id,
            id__in=service_variant_ids,
        ).values_list('id', flat=True):
            yield cls(
                pos=pos,
                resource=resource,
                service_variant_id=service_variant_id,
                type=default_type,
                rate=default_rate,
            )

    @classmethod
    def for_voucher_templates(
        cls, pos: POS, resource: Resource, voucher_type: str, voucher_template_ids: t.List[int]
    ) -> t.List['StaffCommissionRate']:
        """Returns StaffCommissionRate for selected voucher_templates."""
        assert pos is not None
        assert voucher_type in [choice[0] for choice in Voucher.TYPES]

        voucher_template_ids = set(map(int, voucher_template_ids))

        defaults, _created = CommissionDefaults.objects.get_or_create(pos=pos)

        if voucher_type == Voucher.VOUCHER_TYPE__EGIFT_CARD:
            default_type = defaults.egift_commission_type
            default_rate = defaults.egift_commission_rate

        elif voucher_type == Voucher.VOUCHER_TYPE__MEMBERSHIP:
            default_type = defaults.membership_commission_type
            default_rate = defaults.membership_commission_rate

        elif voucher_type == Voucher.VOUCHER_TYPE__PACKAGE:
            default_type = defaults.package_commission_type
            default_rate = defaults.package_commission_rate
        else:
            assert False

        for instance in cls.all_objects.filter(
            pos=pos,
            resource=resource,
            voucher_template__id__in=voucher_template_ids,
            voucher_template__type=voucher_type,
        ).iterator():
            voucher_template_ids.remove(instance.voucher_template_id)
            yield instance

        for voucher_template_id in VoucherTemplate.objects.filter(
            pos=pos,
            type=voucher_type,
            id__in=voucher_template_ids,
        ).values_list('id', flat=True):
            yield cls(
                pos=pos,
                resource=resource,
                voucher_template_id=voucher_template_id,
                type=default_type,
                rate=default_rate,
            )

    def default_action(self, data=None):  # pylint: disable=unused-argument
        self.delete()


class Commission(models.Model):
    row = models.OneToOneField(  # ACHTUNG
        TransactionRow,
        primary_key=True,
        related_name='commission',
        on_delete=models.PROTECT,
    )
    created = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Created (UTC)',
    )
    resource = models.ForeignKey(
        Resource,
        related_name='commissions',
        on_delete=models.CASCADE,
    )
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        help_text='based on rate, type and discounted total ' 'of related transaction row',
    )
    # copied on create from CommissionDefaults or StaffCommissionRate
    type = models.CharField(
        max_length=1,
        choices=CommissionDefaults.COMMISSION_TYPES,
        default=CommissionDefaults.COMMISSION_TYPE__PERCENT,
        help_text='was commission based on percent or fixed rate',
    )
    rate = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        help_text='commission percent or fixed rate, depending on the type',
    )

    def __str__(self):
        currency = format_currency(self.amount)
        rate = (
            f'{self.rate}% rate'
            if self.type == CommissionDefaults.COMMISSION_TYPE__PERCENT
            else 'fixed rate'
        )
        return f'{currency} for {self.resource} ({rate})'

    def __repr__(self):
        return (
            f'<Commission row_id={self.row_id} '
            f'resource_id={self.resource_id} amount={self.amount}>'
        )


class CommissionChangeLog(models.Model):
    created = models.DateTimeField(auto_now_add=True)
    transaction_row = models.ForeignKey(
        TransactionRow,
        related_name='commission_change_logs',
        on_delete=models.DO_NOTHING,
    )
    operator = models.ForeignKey(
        User,
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
        db_constraint=False,
    )
    data = models.TextField()

    @cached_property
    def previous(self):
        return CommissionChangeLog.objects.filter(
            transaction_row_id=self.transaction_row_id,
            created__lt=self.created,
        ).last()

    @staticmethod
    def format_data(data):
        decoded_json = json.loads(data)

        return json.dumps(
            {k: json.dumps(v) for k, v in decoded_json.items()},
            indent=True,
            sort_keys=True,
        )

    @property
    def diff(self):
        """Compares current data with previous; used in admin."""
        previous = self.previous

        if not previous or not previous.data:
            return 'No previous data'

        try:
            prev_data = self.format_data(previous.data)
            curr_data = self.format_data(self.data)
        except ValueError:
            return 'data error'

        differ = Differ()
        _diff = differ.compare(prev_data.split('\n'), curr_data.split('\n'))

        return '\n'.join((line for line in _diff if line[0] in '+-'))


class BankAccount(ArchiveModel, models.Model):
    from webapps.pos.tools import get_bank_account_choices

    TYPE_CHOICES = get_bank_account_choices()

    routing_number = models.CharField(max_length=128, blank=True, null=True)
    account_number = models.CharField(max_length=128)
    type = models.CharField(
        max_length=16,
        choices=TYPE_CHOICES,
        blank=True,
        null=True,
    )
    pos = models.OneToOneField(
        POS,
        blank=True,
        null=True,
        related_name='bank_account',
        on_delete=models.PROTECT,
    )

    def __str__(self):
        return f'id:{self.id} {self.account_number} {self.routing_number}'

    @staticmethod
    def parse(number):
        if settings.API_COUNTRY == Country.US:
            r = re.compile(r'^(?P<routing>\d{9})[ -]?(?P<account>\d+)$')
            m = r.match(number)

            if m is None:
                raise ValueError

            return {
                'routing_number': m.group('routing'),
                'account_number': m.group('account'),
            }
        return {'account_number': number}

    def get_full_account_number(self):
        return '-'.join([_f for _f in [self.routing_number, self.account_number] if _f])

    def set_full_account_number(self, number):
        parsed = self.parse(number)
        self.routing_number = parsed.get('routing_number')
        self.account_number = parsed.get('account_number')

    full_account_number = property(get_full_account_number, set_full_account_number)


class BusinessAddressChange(ArchiveModel):
    SELECT_LAST_FROM_MINUTES = 1

    business_address_fields = (
        'address',
        'address2',
        'city',
    )

    business = models.ForeignKey(
        Business,
        related_name='receipts',
        on_delete=models.CASCADE,
    )
    resolved = models.BooleanField(default=False)
    old_address_data = JSONField(default=dict)
    new_address_data = JSONField(default=dict)

    description = models.TextField(
        null=True,
        blank=True,
        verbose_name='Description',
    )

    @classmethod
    def get_or_create_last(cls, business):
        timezone = business.get_timezone()
        now = datetime.datetime.now(tz=timezone)
        minute_ago = now - relativedelta(minutes=cls.SELECT_LAST_FROM_MINUTES)

        record = (
            cls.objects.filter(
                business=business,
                created__gte=minute_ago,
            )
            .exclude(
                resolved=True,
            )
            .first()
        )
        if not record:  # get new object
            record = cls(business=business)
        return record

    def set_new_address_details(
        self, dirty_fields=None, old_region=None, new_region=None, **kwargs
    ):
        if dirty_fields is None:
            dirty_fields = {}

        address_data = {
            key: value
            for (key, value) in list(dirty_fields.items())
            if key in self.business_address_fields
        }

        if address_data:
            new_address_data = {
                key: getattr(self.business, key) for key in list(address_data.keys())
            }
            self.new_address_data = new_address_data
            self.old_address_data = address_data
        if old_region:
            self.old_address_data['region'] = old_region.name
        if new_region:
            self.new_address_data['region'] = new_region.name


@receiver(post_save, sender=Business)
def business_with_pos_address_change(sender, instance, action=None, **kwargs):
    pos = instance._get_pos()  # pylint: disable=protected-access
    pay_by_app_enabled = pos.pay_by_app_status == POS.PAY_BY_APP_ENABLED if pos else False
    if not pay_by_app_enabled:
        return
    dirty_fields = instance.get_dirty_fields()
    address_change = BusinessAddressChange.get_or_create_last(instance)
    region = instance.region
    if sender.__name__ == 'Business_regions' and action == 'post_add':
        address_change.set_new_address_details(new_region=region)
    elif not address_change.old_address_data:
        address_change.set_new_address_details(dirty_fields=dirty_fields)
        address_change.set_new_address_details(old_region=region)
    whole_data = dict(dirty_fields)
    whole_data.update(address_change.old_address_data)
    whole_data.update(address_change.new_address_data)
    business_address_fields = BusinessAddressChange.business_address_fields
    if any(key in whole_data for key in business_address_fields):
        fraud_status = pos.fraud_status
        api_country = settings.API_COUNTRY
        if (
            fraud_status == POS.FRAUD_STATUS_WHITELIST
            and api_country in settings.ANTI_FRAUD_COUNTRIES_LIST
        ):
            pos.fraud_status = POS.FRAUD_STATUS_POSSIBLE_FRAUD
            pos.save()
        address_change.save()


class POSPlanBatchUpdateLog(ArchiveModel):
    status = models.CharField(max_length=16)
    operator = models.ForeignKey(
        User,
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
        db_constraint=False,
    )
    data = models.TextField(
        null=True,
        blank=True,
    )


class BCRPromoCheckoutSeenFirstTimestamp(ArchiveModel):
    business = models.ForeignKey(
        Business,
        related_name='splash_checkouts',
        on_delete=models.CASCADE,
    )
    operator = models.ForeignKey(
        'user.User',
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
    )
    first_transaction = models.ForeignKey(
        Transaction,
        related_name='+',
        on_delete=models.DO_NOTHING,
    )
    splash_type = models.CharField(
        choices=BCRPromoSplashType.choices(),
        max_length=32,
    )

    class Meta:
        unique_together = (
            'business',
            'operator',
            'splash_type',
        )

    objects = BaseArchiveManager.from_queryset(ArchiveQuerySet)()
    all_objects = models.Manager()


class PostCheckoutSplash(ArchiveModel):
    business = models.ForeignKey(
        Business,
        related_name='ttp_splash_checkouts',
        on_delete=models.CASCADE,
    )
    operator = models.ForeignKey(
        'user.User',
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
    )
    first_transaction = models.ForeignKey(
        Transaction,
        related_name='+',
        on_delete=models.DO_NOTHING,
    )
    splash_type = models.CharField(
        choices=SplashType.choices(),
        max_length=48,
    )

    class Meta:
        constraints = [
            UniqueConstraint(
                fields=('business', 'operator', 'splash_type'),
                name='pos_ttppromocheckoutseenfirsttimestamp_business_operator_splash_type',
            ),
        ]

    objects = BaseArchiveManager.from_queryset(ArchiveQuerySet)()
    all_objects = models.Manager()


class Splash(models.Model):
    business = models.ForeignKey(
        Business,
        related_name='+',
        on_delete=models.CASCADE,
    )
    operator = models.ForeignKey(
        'user.User',
        null=True,
        blank=True,
        related_name="+",
        on_delete=models.DO_NOTHING,
    )
    seen_at = models.DateTimeField()
    type = models.CharField(choices=SplashType.choices(), max_length=48)

    class Meta:
        constraints = [
            UniqueConstraint(
                fields=('business', 'operator', 'type'),
                name='pos_splash_business_operator_type_unique',
            ),
        ]


class NoShowSplash(models.Model):
    business = models.ForeignKey(
        Business,
        related_name='+',
        on_delete=models.CASCADE,
    )
    operator = models.ForeignKey(
        'user.User',
        null=True,
        blank=True,
        related_name="+",
        on_delete=models.DO_NOTHING,
    )
    seen_at = models.DateField()
    type = models.CharField(choices=SplashType.choices(), max_length=48)


class HigherPrepaymentSplash(models.Model):
    business = models.ForeignKey(
        Business,
        related_name='+',
        on_delete=models.CASCADE,
    )
    operator = models.ForeignKey(
        'user.User',
        null=True,
        blank=True,
        related_name="+",
        on_delete=models.DO_NOTHING,
    )
    seen_at = models.DateTimeField()


class HigherPrepaymentSplashDecision(ArchiveModel):
    class Decision(StrEnum):
        FOREVER = 'FOREVER'
        DATE = 'DATE'

    pos = models.ForeignKey(
        POS,
        related_name='+',
        on_delete=models.CASCADE,
    )
    decision = models.CharField(max_length=50, choices=Decision.choices())


class TapToPayAppetiteSplash(models.Model):
    business = models.ForeignKey(
        Business,
        related_name='+',
        on_delete=models.CASCADE,
    )
    operator = models.ForeignKey(
        'user.User',
        null=True,
        blank=True,
        related_name="+",
        on_delete=models.DO_NOTHING,
    )
    seen_at = models.DateTimeField()


class TapToPayAppetiteExperiment(ArchiveModel):
    business = UniqueForeignKey(
        Business,
        related_name='+',
        on_delete=models.CASCADE,
        unique=True,
    )
    opt_in = models.BooleanField(null=False, blank=False, default=False)


class TippingAfterAppointmentAnswer(ArchiveModel):
    class Answer(StrEnum):
        ACCEPT = 'ACCEPT'
        NOT_NOW = 'NOT_NOW'

    client = UniqueForeignKey(
        'user.User',
        null=True,
        blank=True,
        related_name="+",
        on_delete=models.DO_NOTHING,
    )
    answer = models.CharField(max_length=50, choices=Answer.choices())


class KeepPrepaymentSplash(models.Model):
    business = models.ForeignKey(
        Business,
        related_name='+',
        on_delete=models.CASCADE,
    )
    operator = models.ForeignKey(
        'user.User',
        null=True,
        blank=True,
        related_name="+",
        on_delete=models.DO_NOTHING,
    )
    seen_at = models.DateTimeField()
