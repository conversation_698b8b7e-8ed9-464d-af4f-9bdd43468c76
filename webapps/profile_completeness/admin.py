from functools import update_wrapper

from django.contrib import (
    admin,
    messages,
)
from django.http import HttpResponseRedirect
from django.urls import (
    path,
    reverse,
)

from lib.admin_helpers import BaseModelAdmin, NoAddDelMixin, ReadOnlyTabular
from webapps.profile_completeness.models import (
    CompletedTier,
    Reward,
    CompletedStep,
    ProfileCompleteness,
    ProfileCompletenessStats,
)


class CompletedStepAdmin(ReadOnlyTabular, BaseModelAdmin):
    change_form_template = 'admin/change_forms/change_form__profile_completeness.html'
    model = CompletedStep
    list_display = (
        'id',
        'business',
        'tier_level',
        'step_internal_name',
        'created',
    )
    list_display_links = (
        'id',
        'business',
    )
    search_fields = (
        '=id',
        'business__id',
        'business__name',
    )
    list_filter = (
        'step_internal_name',
        'tier_level',
        'updated',
    )
    exclude = ('deleted',)
    readonly_fields = list_display

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('business')


admin.site.register(CompletedStep, CompletedStepAdmin)


class RewardAdmin(ReadOnlyTabular, BaseModelAdmin):
    model = Reward
    list_display = (
        'id',
        'business',
        'reward_internal_name',
        'created',
    )
    list_display_links = (
        'id',
        'business',
    )
    search_fields = (
        '=id',
        'business__id',
        'business__name',
    )
    list_filter = (
        'reward_internal_name',
        'updated',
    )
    exclude = ('deleted',)
    readonly_fields = list_display

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('business')


admin.site.register(Reward, RewardAdmin)


class CompletedTierAdmin(ReadOnlyTabular, BaseModelAdmin):
    model = CompletedTier
    list_display = (
        'id',
        'business',
        'tier_level',
        'created',
    )
    list_display_links = (
        'id',
        'business',
    )
    search_fields = (
        '=id',
        'business__id',
        'business__name',
    )
    list_filter = (
        'tier_level',
        'updated',
    )
    exclude = ('deleted',)
    readonly_fields = list_display

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('business')


admin.site.register(CompletedTier, CompletedTierAdmin)


class ProfileCompletenessAdmin(NoAddDelMixin, BaseModelAdmin):
    model = ProfileCompleteness
    verbose_name = 'Profile Completeness'
    list_display = (
        'id',
        'business',
        'tier_level',
    )
    list_display_links = (
        'id',
        'business',
    )
    search_fields = (
        '=id',
        'business__id',
        'business__name',
    )
    list_filter = (
        'tier_level',
        'updated',
    )
    readonly_fields = (
        'id',
        'business',
        'updated',
    )
    exclude = ('deleted',)
    change_form_template = 'admin/change_forms/change_form__profile_completeness.html'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('business')

    def save_model(self, request, obj, form, change):
        obj.change_tier_level(obj.tier_level)
        obj.save()

    def update_view(self, request, object_id):
        """
        Custom view for updating tier level
        """
        profile_completeness = self.get_object(request, object_id)
        if profile_completeness is None:
            return self._get_obj_does_not_exist_redirect(request, self, object_id)

        initial_tier_level = profile_completeness.tier_level
        profile_completeness.update_tier_level()

        if profile_completeness.tier_level != initial_tier_level:
            messages.success(
                request,
                'Updated tier level from {initial_tier_level} to {profile_completeness.tier_level}',
            )
        else:
            messages.success(request, 'Tier level is up to date')

        return HttpResponseRedirect(
            reverse('admin:profile_completeness_profilecompleteness_change', args=(object_id,)),
        )

    def get_urls(self):
        def wrap(view):
            def wrapper(*args, **kwargs):
                return self.admin_site.admin_view(view)(*args, **kwargs)

            wrapper.model_admin = self
            return update_wrapper(wrapper, view)

        urls = [
            path(
                '<path:object_id>/update/',
                wrap(self.update_view),
                name='update_profile_completeness',
            ),
        ]
        return urls + super().get_urls()


admin.site.register(ProfileCompleteness, ProfileCompletenessAdmin)


class ProfileCompletenessStatsAdmin(ReadOnlyTabular, BaseModelAdmin):
    model = ProfileCompletenessStats
    list_display = (
        'id',
        'business',
        'customer_bookings_count',
        'all_bookings_count',
    )
    list_display_links = (
        'id',
        'business',
    )
    search_fields = (
        '=id',
        'business__id',
        'business__name',
    )
    exclude = ('deleted',)
    readonly_fields = list_display

    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .select_related(
                'business',
                'business__owner',
            )
            .only(
                'id',
                'business__id',
                'business__name',
                'business__owner__email',
                'customer_bookings_count',
                'all_bookings_count',
            )
        )


admin.site.register(ProfileCompletenessStats, ProfileCompletenessStatsAdmin)
