from datetime import datetime, time, timedelta
from decimal import Decimal
import pytest

from dateutil.relativedelta import relativedelta
from django.conf import settings
from freezegun import freeze_time
from mock import patch, PropertyMock
from model_bakery.recipe import baker

from lib.tools import format_currency, tznow
from service.booking.tools import AppointmentData
from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.booking.enums import (
    AppointmentCustomerMode as ACMode,
    SubbookingServiceVariantMode as SVMode,
)
from webapps.booking.models import (
    Appointment,
    BookingSources,
    SubBooking,
)
from webapps.booking.serializers.appointment import (
    AppointmentSerializer,
    CustomerAppointmentSerializer,
)
from webapps.booking.tests.test_appointment_base import (
    BaseTestAppointment,
    build_appointment_data,
    build_custappt_data,
)
from webapps.business.enums import DiscountType, PriceType
from webapps.business.models import (
    Business,
    ServicePromotion,
    ServiceVariant,
    ServiceVariantPayment,
)
from webapps.business.models.service import (
    SERVICE_VARIANT_CLIENT_DISCOUNT,
    SERVICE_VARIANT_FLASH_SALE,
)
from webapps.business.serializers import (
    Flash<PERSON>alePromotionSerializer,
    HappyHourPromotionSerializer,
    LastMinutePromotionSerializer,
)
from webapps.pos.baker_recipes import pos_recipe
from webapps.pos.calculations import round_currency
from webapps.pos.enums import PaymentTypeEnum
from webapps.pos.models import CommissionChangeLog, POS, PaymentType
from webapps.pos.provider.fake import _CARDS
from webapps.pos.serializers import CustomerAppointmentTransactionSerializer
from webapps.profile_completeness.steps import LaunchPromotionsStep
from webapps.schedule.ports import DayOfWeek


class PropertiesMixin:

    def tearDown(self):  # pylint: disable=C0103
        ServicePromotion.objects.all().delete()
        super().tearDown()

    @property
    def initial_time(self):
        return datetime(
            year=2018,
            month=11,
            day=20,
            hour=11,
            minute=12,
            second=13,
            tzinfo=self.business.get_timezone(),
        )

    @property
    def initial_date(self):
        return self.initial_time.date()

    @property
    def context(self):
        return {
            'business': self.business,
            'booking_source': self.booking_source,
        }

    @property
    def promotion_context(self):
        return {
            'business': self.business,
            'booking_source': self.booking_source,
        }

    @property
    def customer_appointment_context(self):
        return {
            'business': self.business,
            'single_category': self.business.is_single_category,
            'source': self.booking_source,
            'type': Appointment.TYPE.CUSTOMER,
            'user': self.customer_bci.user,
            'compatibilities': {
                'prepayment': True,
            },
            'is_external': False,
        }

    @property
    def business_appointment_context(self):
        return {
            'business': self.business,
            'single_category': self.business.is_single_category,
            'source': self.booking_source,
            'type': Appointment.TYPE.BUSINESS,
            'user': self.user,
        }


@pytest.mark.django_db
class BasePromotionTest(BaseTestAppointment, PropertiesMixin):

    def setUp(self):
        super().setUp()

        self.pos = pos_recipe.make(
            business=self.business,
            pay_by_app_status=POS.PAY_BY_APP_ENABLED,
        )
        baker.make(PaymentType, code=PaymentTypeEnum.PAY_BY_APP, pos=self.pos)
        baker.make(PaymentType, code=PaymentTypeEnum.CREDIT_CARD, pos=self.pos)

        self.variant.price = Decimal('100.00')
        self.variant.type = PriceType.FIXED
        self.variant.save()

        self.gap_hole_variant.price = Decimal('200.0')
        self.gap_hole_variant.type = PriceType.FIXED
        self.gap_hole_variant.save()

    def tearDown(self):
        CommissionChangeLog.objects.all().delete()
        super().tearDown()

    @property
    def current_date(self):
        return self.date.strftime(settings.DATE_FORMAT)

    def customer_user(self, discount=0):
        user = baker.make('user.User', email='<EMAIL>', cell_phone='3213213214')
        self.customer_bci = baker.make(
            'business.BusinessCustomerInfo',
            user=user,
            discount=discount,
            business=self.business,
        )

        return self.customer_bci.user

    def make_subbooking_data(self, booked_from, variants, **kwargs):
        dry_run = kwargs.pop('dry_run', True)

        if isinstance(booked_from, time):
            booked_from = self._dt_from_hour(booked_from)

        variant = variants[0]

        data = build_custappt_data(
            variant=variant,
            booked_from=booked_from,
            staffer=self.staffer,
            **kwargs,
        )

        for sbk_id, _variant in enumerate(variants[1:], 1):
            if dry_run:
                _booked_from = None
            else:
                offset = self.variant.duration * sbk_id
                _booked_from = booked_from + offset

            _d = build_custappt_data(
                variant=_variant,
                booked_from=_booked_from,
                staffer=self.staffer,
                **kwargs,
            )
            data['subbookings'].extend(_d['subbookings'])

        return data

    def make_payment_method(self, user=None):
        return baker.make(
            'pos.PaymentMethod',
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=user or self.user,
        )

    def customer_appointment_data(self, booked_from, dry_run=True, variants=None, recurring=False):
        variants = variants or [list(self.variants.values())[0]]

        data = self.make_subbooking_data(
            booked_from=booked_from,
            variants=variants,
            dry_run=dry_run,
            recurring=recurring,
        )

        data['dry_run'] = dry_run
        data['payment_method'] = self.make_payment_method().id

        return data

    def customer_appointment_serializer(
        self,  # pylint: disable=too-many-arguments
        booked_from,
        recurring=False,
        subbookings_hours=None,
        dry_run=True,
        variants=None,
    ):
        subbookings_hours = subbookings_hours or []
        _variants = variants or [self.variant]
        variants = _variants + _variants * len(subbookings_hours)

        if isinstance(booked_from, time):
            booked_from = self._dt_from_hour(booked_from)

        data = self.make_subbooking_data(
            booked_from,
            variants,
            dry_run=dry_run,
            recurring=recurring,
        )

        card = self.make_payment_method()
        data['payment_method'] = card.id
        data['dry_run'] = dry_run

        serializer = CustomerAppointmentSerializer(
            instance=None,
            data=data,
            context=self.customer_appointment_context,
        )
        self.assertTrue(serializer.is_valid(), serializer.errors)
        appointment = serializer.save()
        _ = serializer.data

        cat_serializer = self.customer_appointment_transaction_serializer(data, appointment)

        return serializer, cat_serializer

    def customer_appointment_transaction_serializer(self, data, appointment):
        serializer = CustomerAppointmentTransactionSerializer(
            data=data,
            context={
                'currency_symbol': settings.CURRENCY_CODE,
                'valid_currency': True,
                'user': self.user,
                'business': self.business,
                'pos': self.business.pos,
                'appointment_checkout': appointment.checkout,
                'appointment_data': AppointmentData.build(appointment),
                'extra_data': {},
                'compatibilities': {
                    'prepayment': True,
                },
            },
        )

        self.assertTrue(serializer.is_valid(), serializer.errors)

        return serializer

    def business_appointment_serializer(
        self,
        booked_from,
        customer_user=None,
        dry_run=True,
        # subbookings_hours=None,
        staffer=None,
    ):
        staffer = staffer or self.staffer
        # subbookings_hours = subbookings_hours or []

        if isinstance(booked_from, time):
            booked_from = self._dt_from_hour(booked_from)

        data = build_appointment_data(
            variant=self.variant,
            booked_from=booked_from,
            booked_till=None,
            staffer=staffer,
        )

        data['dry_run'] = dry_run

        (
            data.update(
                {  # pylint: disable=W0106
                    'customer': {
                        'mode': ACMode.CUSTOMER_CARD,
                        'id': customer_user.id,
                    }
                }
            )
            if customer_user
            else data.update({'customer': None})
        )

        serializer = AppointmentSerializer(
            instance=None,
            data=data,
            context=self.business_appointment_context,
        )
        self.assertTrue(serializer.is_valid(), serializer.errors)
        serializer.save()
        _ = serializer.data
        return serializer

    def biz_push_recipient(self):
        # profile for notification
        booking_source, _ = BookingSources.objects.get_or_create(
            name='Android',
            app_type=BookingSources.BUSINESS_APP,
            api_key='xx312',
        )
        biz_user_profile = baker.make(
            'user.UserProfile',
            profile_type='B',
            region=self.region,
            source=booking_source,
            user=self.user,
        )
        biz_notification = baker.make(
            'notification.UserNotification',
            profile=biz_user_profile,
            type='P',
        )
        baker.make(
            'notification.Reciever',
            identifier='xxx',
            business=self.business,
            device='android',
            customer_notifications=biz_notification,
        )

    def flash_sale(self, **kwargs):
        promotion_duration = kwargs.pop('promotion_duration', 1)
        discount_rate = kwargs.pop('discount_rate', 10)
        booking_start = kwargs.pop('booking_start', self.date).strftime(settings.DATE_FORMAT)
        _be = kwargs.pop('booking_end', None)
        booking_end = _be.strftime(settings.DATE_FORMAT) if _be else None
        variant_ids = kwargs.pop('variant_ids', [])

        data = {
            'promotion_duration': promotion_duration,
            'discount_rate': discount_rate,
            'booking_start': booking_start,
            'booking_end': booking_end,
            'service_variant_ids': variant_ids,
        }

        serializer = FlashSalePromotionSerializer(
            data=data,
            context=self.promotion_context,
        )

        self.assertTrue(serializer.is_valid(), serializer.errors)
        serializer.save()
        return serializer

    def last_minute(self, **kwargs):
        data = {
            'discount_rate': kwargs.get('discount_rate', 10),
            'last_minute_hours': kwargs.get('last_minute_hours', 1),
            'service_variant_ids': kwargs.get('variant_ids', []),
        }
        serializer = LastMinutePromotionSerializer(
            data=data,
            context=self.promotion_context,
        )

        self.assertTrue(serializer.is_valid(), serializer.errors)
        serializer.save()
        return serializer

    def happy_hours(self, data):
        serializer = HappyHourPromotionSerializer(
            data=data,
            context=self.promotion_context,
            many=True,
        )

        self.assertTrue(serializer.is_valid(), serializer.errors)
        serializer.save()

        return serializer

    def booking_time(self, hour, minute):
        return datetime.combine(
            self.date,
            time(hour=hour, minute=minute),
        ).replace(tzinfo=self.business.get_timezone())

    def prepayment_for_variant(self, amount, variant=None):
        baker.make(
            ServiceVariantPayment,
            service_variant=variant or self.variant,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal(amount),
        )


@pytest.mark.django_db
@patch(
    (
        'webapps.booking.serializers.appointment.'
        'BusinessInCustomerAppointmentSerializer.to_representation'
    ),
    return_value=None,
)
@patch('lib.elasticsearch.tools.index_document', return_value=None)
@patch.object(Business, 'boost_status', PropertyMock(return_value=Business.BoostStatus.ENABLED))
class TestPromotionResolver(BasePromotionTest):

    def _assert_step_completed(self):
        self.assertTrue(LaunchPromotionsStep.check_completed(self.business))
        self.assertTrue(LaunchPromotionsStep.is_marked_as_completed(self.business))

    def test_client_flash_sale_single_appointment(self, *__):
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user()

        with freeze_time(booking_time):
            self.flash_sale(
                discount_rate=10,
                variant_ids=[v.id for v in list(self.variants.values())],
            )
            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            subbooking = appointment.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(90.0, subbooking['service_promotion']['_price'])
        self._assert_step_completed()

    def test_client_flash_sale_single_prepayment_appointment(self, *__):
        discount_rate = 10
        prepayment_amount = 20.0
        expected_price = round_currency(
            self.variant.price - self.variant.price * discount_rate / 100
        )
        expected_prepayment = round_currency(
            prepayment_amount - prepayment_amount * discount_rate / 100
        )

        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.prepayment_for_variant(amount=prepayment_amount)
        self.customer_user()

        with freeze_time(booking_time):
            self.flash_sale(
                discount_rate=discount_rate,
                variant_ids=[v.id for v in list(self.variants.values())],
            )
            c_serializer, cat_serializer = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )

            subbooking = c_serializer.data['subbookings'][0]
            self.assertEqual(
                expected_price,
                subbooking['service_promotion']['_price'],
            )
            self.assertEqual(
                expected_prepayment,
                cat_serializer.validated_data['prepayment'],
            )
        self._assert_step_completed()

    def test_client_flash_sale_next_week(self, *__):
        booking_time = self.booking_time(9, 30)
        self.customer_user(discount=5)

        with freeze_time(booking_time):
            booking_start = tznow()
            booking_end = booking_start + timedelta(days=14)

            self.flash_sale(
                discount_rate=10,
                variant_ids=[v.id for v in list(self.variants.values())],
                booking_start=booking_start,
                booking_end=booking_end,
            )

            booked_from = booking_time + timedelta(days=7, hours=1)

            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            subbooking = appointment.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(90.0, subbooking['service_promotion']['_price'])
        self._assert_step_completed()

    def test_client_flash_sale_book_for_last_day(self, *__):
        """Book today for today and tomorrow to get 10% off."""
        self.customer_user(discount=5)

        booking_time = self.booking_time(9, 30)

        with freeze_time(booking_time):
            booking_start = booking_time + timedelta(days=1, hours=1)
            booking_end = booking_start + timedelta(days=1)

            self.flash_sale(
                discount_rate=10,
                variant_ids=[v.id for v in list(self.variants.values())],
                booking_start=booking_start,
                booking_end=booking_end,
            )

            booked_from = booking_end

            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            subbooking = appointment.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(90.0, subbooking['service_promotion']['_price'])
        self._assert_step_completed()

    def test_client_flash_sale_book_on_last_day(self, *__):
        """Book today for today and tomorrow to get 10% off."""
        self.customer_user(discount=5)

        booking_time = self.booking_time(9, 30)

        with freeze_time(booking_time) as frozen_time:
            booking_start = booking_time + timedelta(days=1, hours=1)
            booking_end = booking_start + timedelta(days=1)

            self.flash_sale(
                discount_rate=10,
                variant_ids=[v.id for v in list(self.variants.values())],
                booking_start=booking_start,
                booking_end=booking_end,
            )

            frozen_time.tick(timedelta(days=1))
            booked_from = booking_end - timedelta(minutes=30)

            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            subbooking = appointment.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(90.0, subbooking['service_promotion']['_price'])
        self._assert_step_completed()

    def test_client_flash_sale_from_booking(self, *__):
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user()

        with freeze_time(booking_time):
            self.flash_sale(
                discount_rate=10,
                variant_ids=[v.id for v in list(self.variants.values())],
            )
            _appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            _subbooking = _appointment.data['subbookings'][0]
            self.assertIn('service_promotion', _subbooking)
            self.assertIsNotNone(_subbooking['service_promotion'])

            booking = SubBooking.objects.get(id=_subbooking['id'])
            appointment_from_booking = AppointmentWrapper([booking])
            appointment = CustomerAppointmentSerializer(
                instance=appointment_from_booking,
                context={
                    'business': booking.appointment.business,
                    'user': booking.appointment.booked_for.user,
                    'dry_run': True,
                },
            )

            subbooking = appointment.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(90.0, subbooking['service_promotion']['_price'])
        self._assert_step_completed()

    def test_client_flash_sale_single_appointment_no_bci(self, *__):
        booked_from = self.booking_time(10, 0)
        booking_time = self.booking_time(9, 30)

        with freeze_time(booking_time):
            self.flash_sale(
                discount_rate=10,
                variant_ids=[v.id for v in list(self.variants.values())],
            )

            customer_user_no_bci = baker.make(
                'user.User', email='<EMAIL>', cell_phone='3213213666'
            )
            data = {
                'compatibilities': {
                    'prepayment': True,
                },
                'customer_note': 'duh!',
                'dry_run': False,
                'recurring': False,
                'subbookings': [
                    {
                        'booked_from': booked_from.isoformat(),
                        'service_variant': {
                            'id': self.variant.id,
                            'mode': SVMode.VARIANT,
                        },
                        'staffer_id': self.staffer.id,
                    }
                ],
            }

            serializer = CustomerAppointmentSerializer(
                instance=None,
                data=data,
                context={
                    'business': self.business,
                    'single_category': self.business.is_single_category,
                    'source': self.booking_source,
                    'type': Appointment.TYPE.CUSTOMER,
                    'user': customer_user_no_bci,
                },
            )
            self.assertTrue(serializer.is_valid(), serializer.errors)
            serializer.save()

            subbooking = serializer.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(90.0, subbooking['service_promotion']['_price'])
        self._assert_step_completed()

    def test_client_total_for_multibooking_appointment(self, *__):
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user()

        with freeze_time(booking_time):
            self.flash_sale(
                discount_rate=10, variant_ids=[v.id for v in list(self.variants.values())]
            )
            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
                subbookings_hours=[time(10, 30), time(11, 0)],
            )

            for subbooking in appointment.data['subbookings']:
                self.assertIn('service_promotion', subbooking)
                self.assertIsNotNone(subbooking['service_promotion'])

            subbooking_sum = sum(
                (s['service_promotion']['_price'] for s in appointment.data['subbookings'])
            )

            self.assertEqual(
                format_currency(subbooking_sum),
                appointment.data['total'],
            )
        self._assert_step_completed()

    def test_client_multi_appointment_total_using_single(self, *__):
        booking_time = self.booking_time(9, 30)
        booked_from = self.booking_time(10, 0)
        customer_user = self.customer_user()

        with freeze_time(booking_time):
            self.flash_sale(
                discount_rate=10, variant_ids=[v.id for v in list(self.variants.values())]
            )
            appt, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
                subbookings_hours=[time(10, 30), time(11, 0)],
            )

            appointment = AppointmentWrapper.get_by_subbooking_id(
                appointment_id=appt.data['subbookings'][0]['id'],
                customer_user_id=customer_user.id,
            )

            serializer = CustomerAppointmentSerializer(
                instance=appointment,
                context={
                    'business': appt.business,
                    'user': customer_user,
                    'single_category': appt.business.is_single_category,
                },
            )

            data = serializer.data

            subbooking_sum = sum((s['service_promotion']['_price'] for s in data['subbookings']))
            self.assertEqual(270.0, subbooking_sum)
            self.assertIn('total', data)
            self.assertEqual(format_currency(subbooking_sum), data['total'])
        self._assert_step_completed()

    def test_client_multi_appointment_total_using_multi(self, *__):
        booking_time = self.booking_time(9, 30)
        booked_from = self.booking_time(10, 0)
        customer_user = self.customer_user()

        with freeze_time(booking_time):
            self.flash_sale(
                discount_rate=10, variant_ids=[v.id for v in list(self.variants.values())]
            )
            appt, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
                subbookings_hours=[time(10, 30), time(11, 0)],
            )

            appointment = AppointmentWrapper.get_by_appointment_id(
                appointment_id=appt.data['appointment_id'],
                customer_user_id=customer_user.id,
            )

            serializer = CustomerAppointmentSerializer(
                instance=appointment,
                context={
                    'business': appt.business,
                    'user': customer_user,
                    'single_category': appt.business.is_single_category,
                },
            )

            data = serializer.data

            subbooking_sum = sum((s['service_promotion']['_price'] for s in data['subbookings']))

            self.assertIn('total', data)
            self.assertEqual(270.0, subbooking_sum)
            self.assertEqual(format_currency(subbooking_sum), data['total'])
        self._assert_step_completed()

    def test_client_last_minute_for_single_appointment(self, *__):
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user()

        with freeze_time(booking_time):
            self.last_minute(
                discount_rate=10,
                variant_ids=[v.id for v in list(self.variants.values())],
            )
            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            subbooking = appointment.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(90.0, subbooking['service_promotion']['_price'])
        self._assert_step_completed()

    def test_client_last_minute_for_single_prepayment_appointment(self, *__):
        discount_rate = 10
        prepayment_amount = 20.0
        expected_price = round_currency(
            self.variant.price - self.variant.price * discount_rate / 100
        )
        expected_prepayment = round_currency(
            prepayment_amount - prepayment_amount * discount_rate / 100
        )

        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user()
        self.prepayment_for_variant(amount=prepayment_amount)

        with freeze_time(booking_time):
            self.last_minute(
                discount_rate=discount_rate,
                variant_ids=[v.id for v in list(self.variants.values())],
            )
            c_serializer, cat_serializer = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )

            subbooking = c_serializer.data['subbookings'][0]
            self.assertEqual(
                expected_price,
                subbooking['service_promotion']['_price'],
            )
            self.assertEqual(
                expected_prepayment,
                cat_serializer.validated_data['prepayment'],
            )
        self._assert_step_completed()

    def test_client_happy_hours_rate_discount(self, *__):
        data = [
            {
                'day_of_week': dow,
                'service_variants': [
                    {
                        'discount_type': DiscountType.RATE,
                        'discount_amount': 50.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': self.variant.id,
                    },
                ],
            }
            for dow in DayOfWeek
        ]
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user()

        with freeze_time(booking_time):
            self.happy_hours(data)
            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            subbooking = appointment.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(50.0, subbooking['service_promotion']['_price'])
        self._assert_step_completed()

    def test_client_happy_hours_100_rate_discount(self, *__):
        discount_amount = 100.0
        data = [
            {
                'day_of_week': dow,
                'service_variants': [
                    {
                        'discount_type': DiscountType.RATE,
                        'discount_amount': discount_amount,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': self.variant.id,
                    },
                ],
            }
            for dow in DayOfWeek
        ]
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user()

        with freeze_time(booking_time):
            self.happy_hours(data)
            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            subbooking = appointment.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(0.0, subbooking['service_promotion']['_price'])
        self._assert_step_completed()

    def test_client_happy_hours_book_before_end(self, *__):
        data = [
            {
                'day_of_week': dow,
                'service_variants': [
                    {
                        'discount_type': DiscountType.RATE,
                        'discount_amount': 50.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': self.variant.id,
                    },
                ],
            }
            for dow in DayOfWeek
        ]
        booked_from = time(11, 45)
        booking_time = self.booking_time(9, 30)
        self.customer_user()

        with freeze_time(booking_time):
            self.happy_hours(data)
            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            subbooking = appointment.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(50.0, subbooking['service_promotion']['_price'])
        self._assert_step_completed()

    def test_client_happy_hours_book_after_end(self, *__):
        data = [
            {
                'day_of_week': dow,
                'service_variants': [
                    {
                        'discount_type': DiscountType.RATE,
                        'discount_amount': 50.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': self.variant.id,
                    },
                ],
            }
            for dow in DayOfWeek
        ]
        booked_from = time(12, 45)
        booking_time = self.booking_time(9, 30)
        self.customer_user()

        with freeze_time(booking_time):
            self.happy_hours(data)
            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            subbooking = appointment.data['subbookings'][0]
            assert not subbooking['service_promotion']
        self._assert_step_completed()

    def test_client_happy_hours_amount_discount(self, *__):
        data = [
            {
                'day_of_week': dow,
                'service_variants': [
                    {
                        'discount_type': DiscountType.AMOUNT,
                        'discount_amount': 25.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': self.variant.id,
                    },
                ],
            }
            for dow in DayOfWeek
        ]
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user()

        with freeze_time(booking_time):
            self.happy_hours(data)
            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            subbooking = appointment.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(75.0, subbooking['service_promotion']['_price'])
        self._assert_step_completed()

    def test_client_happy_hours_amount_discount_over_price(self, *__):
        discount_amount = 101.0  # 1 more than variant.price
        data = [
            {
                'day_of_week': dow,
                'service_variants': [
                    {
                        'discount_type': DiscountType.AMOUNT,
                        'discount_amount': discount_amount,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': self.variant.id,
                    },
                ],
            }
            for dow in DayOfWeek
        ]
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user()

        with freeze_time(booking_time):
            self.happy_hours(data)
            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            subbooking = appointment.data['subbookings'][0]
            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(0.0, subbooking['service_promotion']['_price'])
        self._assert_step_completed()

    def test_client_change_hh_appointment_to_day_without_promotion(self, *__):
        discount_amount = 50.0
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        day_of_week = (booking_time.weekday() + 1) % 7
        data = [
            {
                'day_of_week': day_of_week,
                'service_variants': [
                    {
                        'discount_type': DiscountType.AMOUNT,
                        'discount_amount': discount_amount,
                        'hour_from': '10:00',
                        'hour_till': '19:00',
                        'service_variant_id': self.variant.id,
                    },
                ],
            },
        ]
        self.customer_user()

        with freeze_time(booking_time):
            self.happy_hours(data)
            c_serializer, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )

            subbooking = c_serializer.data['subbookings'][0]
            self.assertIsNotNone(subbooking['service_promotion'])

            appt = AppointmentWrapper.get_by_subbooking_id(
                customer_user_id=self.customer_bci.user_id,
                appointment_id=subbooking['id'],
            )

            _booking = appt.subbookings[0]
            updated_data = c_serializer.data
            updated_data['subbookings'][0]['booked_from'] = (
                _booking.booked_from + timedelta(days=1)
            ).isoformat()
            updated_data['subbookings'][0]['booked_till'] = (
                _booking.booked_from + timedelta(days=1)
            ).isoformat()
            updated_data['dry_run'] = False

            serializer = CustomerAppointmentSerializer(
                instance=appt,
                data=updated_data,
                context={
                    'business': appt.business,
                    'single_category': appt.business.is_single_category,
                    'user': self.customer_bci.user,
                    'compatibilities': {},
                },
            )

            self.assertTrue(serializer.is_valid(), serializer.errors)
            serializer.save()

            data = serializer.data
            subbooking = data['subbookings'][0]
            assert not subbooking['service_promotion']
            self.assertEqual(format_currency(self.variant.price), data['total'])
        self._assert_step_completed()

    def test_hh_variant_resolving(self, *__):
        services = baker.make(
            'business.Service',
            business=self.business,
            active=True,
            _quantity=3,
        )
        variants = [
            baker.make(
                ServiceVariant,
                service=service,
                price=100,
                type=PriceType.FIXED,
                active=True,
                duration=relativedelta(minutes=30),
                time_slot_interval=relativedelta(minutes=self.INTERVAL),
            )
            for service in services
        ]
        for variant in variants:
            variant.add_staffers([self.staffer])
        self.assertEqual(3, len(variants))

        data = [
            {
                'day_of_week': 1,
                'service_variants': [
                    {
                        'discount_type': DiscountType.RATE,
                        'discount_amount': 10.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': variant.id,
                    }
                    for variant in variants
                ],
            },
            {
                'day_of_week': 2,
                'service_variants': [
                    {
                        'discount_type': DiscountType.RATE,
                        'discount_amount': 50.0,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': self.variant.id,
                    }
                ],
            },
        ]
        self.assertEqual(3, len(data[0]['service_variants']))
        self.assertEqual(1, len(data[1]['service_variants']))

        self.customer_user()

        booking_time = self.booking_time(9, 30)
        # we want to book for monday and thuesday
        booked_from = self.booking_time(10, 0)
        booked_from = booked_from + timedelta(days=(7 - booked_from.isoweekday() + 1))

        with freeze_time(booking_time):
            self.happy_hours(data)

            # visit on Monday would be 10% off
            serializer, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                variants=variants[1:],
                dry_run=False,
            )

            subbooking = serializer.data['subbookings'][0]
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(90.0, subbooking['service_promotion']['_price'])

            # another visit on tuesday same variant no longer promoted
            serializer, _cat = self.customer_appointment_serializer(
                booked_from=booked_from + timedelta(days=1),
                variants=variants[1:],
                dry_run=False,
            )

            subbooking = serializer.data['subbookings'][0]
            assert not subbooking['service_promotion']
        self._assert_step_completed()

    def test_client_change_hh_appointment_after_promo_time_range(self, *__):
        discount_amount = 50.0  # 1 more than variant.price
        data = [
            {
                'day_of_week': dow,
                'service_variants': [
                    {
                        'discount_type': DiscountType.AMOUNT,
                        'discount_amount': discount_amount,
                        'hour_from': '10:00',
                        'hour_till': '12:00',
                        'service_variant_id': self.variant.id,
                    },
                ],
            }
            for dow in DayOfWeek
        ]
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user()

        with freeze_time(booking_time):
            self.happy_hours(data)

            serializer, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )

            subbooking = serializer.data['subbookings'][0]
            self.assertIsNotNone(subbooking['service_promotion'])

            appt = AppointmentWrapper.get_by_subbooking_id(
                customer_user_id=self.customer_bci.user_id,
                appointment_id=subbooking['id'],
            )

            updated_data = serializer.data
            updated_data['subbookings'][0].update(
                {
                    'booked_from': self.booking_time(12, 0),
                    'booked_till': self.booking_time(12, 30),
                }
            )
            updated_data['dry_run'] = False

            serializer = CustomerAppointmentSerializer(
                instance=appt,
                data=updated_data,
                context={
                    'business': appt.business,
                    'single_category': appt.business.is_single_category,
                    'user': self.customer_bci.user,
                    'compatibilities': {},
                },
            )

            self.assertTrue(serializer.is_valid(), serializer.errors)
            serializer.save()

            data = serializer.data
            subbooking = data['subbookings'][0]
            assert not subbooking['service_promotion']
            self.assertEqual(format_currency(self.variant.price), data['total'])
        self._assert_step_completed()

    def test_client_best_discount(self, *__):
        """LM Should always win.

        CD = 5, FS = 10, HH = 15, LM = 20 (%)

        """
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 45)

        client_discount = 5
        flash_sale_discount = 10
        happy_hours_discount = 15
        last_minute_discount = 20

        self.customer_user(discount=client_discount)

        with freeze_time(booking_time):
            self.flash_sale(
                discount_rate=flash_sale_discount,
                variant_ids=[v.id for v in list(self.variants.values())],
            )
            self.last_minute(
                discount_rate=last_minute_discount,
                variant_ids=[v.id for v in list(self.variants.values())],
            )
            happy_hours = [
                {
                    'day_of_week': dow,
                    'service_variants': [
                        {
                            'discount_type': DiscountType.RATE,
                            'discount_amount': happy_hours_discount,
                            'hour_from': '10:00',
                            'hour_till': '12:00',
                            'service_variant_id': self.variant.id,
                        },
                    ],
                }
                for dow in DayOfWeek
            ]
            self.happy_hours(happy_hours)

            appointment, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            _subbooking = appointment.data['subbookings'][0]
            self.assertIn('service_promotion', _subbooking)
            self.assertIsNotNone(_subbooking['service_promotion'])
            self.assertEqual(80.0, _subbooking['service_promotion']['_price'])

            # get appointment from existing booking
            booking = SubBooking.objects.get(id=_subbooking['id'])
            appointment_from_booking = AppointmentWrapper([booking])
            context = {
                'business': booking.appointment.business,
                'user': booking.appointment.booked_for.user,
                'dry_run': True,
            }
            subbooking = CustomerAppointmentSerializer(
                instance=appointment_from_booking,
                context=context,
            ).data['subbookings'][0]

            self.assertIn('service_promotion', subbooking)
            self.assertIsNotNone(subbooking['service_promotion'])
            self.assertEqual(80.0, subbooking['service_promotion']['_price'])
        self._assert_step_completed()

    def test_client_best_prepayment_discount(self, *__):
        """LM Should always win.

        CD = 5, FS = 10, HH = 15, LM = 20 (%)

        """
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 45)

        client_discount = 5
        flash_sale_discount = 10
        happy_hours_discount = 15
        expected_discount = last_minute_discount = 20
        prepayment_amount = 20.0

        expected_price = round_currency(
            self.variant.price - self.variant.price * expected_discount / 100
        )

        expected_prepayment = round_currency(
            prepayment_amount - prepayment_amount * expected_discount / 100
        )

        self.customer_user(discount=client_discount)
        self.prepayment_for_variant(amount=prepayment_amount)

        with freeze_time(booking_time):
            self.flash_sale(
                discount_rate=flash_sale_discount,
                variant_ids=[v.id for v in list(self.variants.values())],
            )
            self.last_minute(
                discount_rate=last_minute_discount,
                variant_ids=[v.id for v in list(self.variants.values())],
            )
            happy_hours = [
                {
                    'day_of_week': dow,
                    'service_variants': [
                        {
                            'discount_type': DiscountType.RATE,
                            'discount_amount': happy_hours_discount,
                            'hour_from': '10:00',
                            'hour_till': '12:00',
                            'service_variant_id': self.variant.id,
                        },
                    ],
                }
                for dow in DayOfWeek
            ]
            self.happy_hours(happy_hours)

            c_serializer, cat_serializer = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )

            _subbooking = c_serializer.data['subbookings'][0]
            self.assertEqual(
                expected_price,
                _subbooking['service_promotion']['_price'],
            )
            self.assertEqual(expected_prepayment, cat_serializer.validated_data['prepayment'])

            # get appointment from existing booking
            booking = SubBooking.objects.get(id=_subbooking['id'])
            c_serializer_2 = CustomerAppointmentSerializer(
                instance=AppointmentWrapper([booking]),
                context={
                    'business': booking.appointment.business,
                    'user': booking.appointment.booked_for.user,
                    'dry_run': True,
                },
            )
            subbooking = c_serializer_2.data['subbookings'][0]

            self.assertEqual(
                expected_price,
                subbooking['service_promotion']['_price'],
            )
        self._assert_step_completed()

    def test_business_do_not_clean_client_single(self, *__):
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user(discount=5)

        with freeze_time(booking_time):
            self.flash_sale(
                discount_rate=10,
                variant_ids=[v.id for v in list(self.variants.values())],
            )
            ca_serializer, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            ca_subbooking = ca_serializer.data['subbookings'][0]
            self.assertIn('service_promotion', ca_subbooking)
            self.assertIsNotNone(ca_subbooking['service_promotion'])
            self.assertEqual(90.0, ca_subbooking['service_promotion']['_price'])

            b_appointment = AppointmentWrapper.get_by_subbooking_id(
                ca_subbooking['id'],
                self.business.id,
            )
            self.assertIsNotNone(b_appointment)

            # move appointment as business
            data = {
                '_notify_about_reschedule': False,
                '_preserve_order': False,
                '_update_future_bookings': False,
                '_version': b_appointment._version,  # pylint:: disable=W0212
                'business_note': '',
                'business_secret_note': '',
                'customer': {
                    'id': self.customer_bci.id,
                    'mode': ACMode.CUSTOMER_CARD,
                },
                'customer_note': None,
                'dry_run': False,
                'new_repeating': None,
                'overbooking': False,
                'subbookings': [
                    {
                        'appliance_id': -1,
                        'booked_from': self.booking_time(10, 15),
                        'booked_till': (self.booking_time(10, 15) + self.variant.duration),
                        'duration': None,
                        'id': None,
                        'service_variant': {
                            'id': self.variant.id,
                            'mode': SVMode.VARIANT,
                            'service_name': None,
                        },
                        'staffer_id': -1,
                    }
                ],
            }

            ba_serializer = AppointmentSerializer(
                instance=b_appointment,
                data=data,
                context=self.business_appointment_context,
            )

            self.assertTrue(ba_serializer.is_valid(), ba_serializer.errors)
            ba_serializer.save()

            b_subbooking = ba_serializer.data['subbookings'][0]
            self.assertIsNotNone(b_subbooking['service_promotion'])
            self.assertEqual(90.0, b_subbooking['service_promotion']['_price'])
        self._assert_step_completed()

    def test_business_do_not_clean_client_multi(self, *__):
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user(discount=5)

        with freeze_time(booking_time):
            self.flash_sale(
                discount_rate=10,
                variant_ids=[v.id for v in list(self.variants.values())],
            )
            ca_serializer, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                subbookings_hours=[time(10, 30), time(11, 0)],
                dry_run=False,
            )
            ca_subbookings = ca_serializer.data['subbookings']

            for subbooking in ca_subbookings:
                self.assertIn('service_promotion', subbooking)
                self.assertIsNotNone(subbooking['service_promotion'])
                self.assertEqual(90.0, subbooking['service_promotion']['_price'])

            b_appointment = AppointmentWrapper.get_by_appointment_id(
                ca_serializer.data['appointment_id'],
                self.business.id,
            )

            self.assertIsNotNone(b_appointment)

            data = {
                '_notify_about_reschedule': False,
                '_preserve_order': False,
                '_update_future_bookings': False,
                '_version': b_appointment._version,  # pylint:: disable=W0212
                'business_note': '',
                'business_secret_note': '',
                'customer': {
                    'id': self.customer_bci.id,
                    'mode': ACMode.CUSTOMER_CARD,
                },
                'customer_note': None,
                'dry_run': False,
                'new_repeating': None,
                'overbooking': False,
                'subbookings': [],
            }
            delta = timedelta(minutes=15)
            for idx, ca_subbooking in enumerate(ca_subbookings, 1):
                booked_from = booking_time + delta * idx
                booked_till = booked_from + self.variant.duration
                data['subbookings'].append(
                    {
                        'appliance_id': -1,
                        'booked_from': booked_from,
                        'booked_till': booked_till,
                        'duration': None,
                        'id': None,
                        'service_variant': {
                            'id': ca_subbooking['service_variant']['id'],
                            'mode': SVMode.VARIANT,
                        },
                        'staffer_id': ca_subbooking['staffer']['id'],
                    }
                )

            ba_serializer = AppointmentSerializer(
                instance=b_appointment,
                data=data,
                context=self.business_appointment_context,
            )

            self.assertTrue(ba_serializer.is_valid(), ba_serializer.errors)
            ba_serializer.save()

            for b_subbooking in ba_serializer.data['subbookings']:
                for key in ['_price', 'discount', 'type']:
                    self.assertEqual(
                        ca_subbooking['service_promotion'][key],  # pylint: disable=W0631
                        b_subbooking['service_promotion'][key],
                    )
        self._assert_step_completed()

    def test_business_change_bci_for_appointment(self, *__):
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user(discount=5)

        with freeze_time(booking_time):
            self.flash_sale(
                discount_rate=10,
                variant_ids=[v.id for v in list(self.variants.values())],
            )
            ca_serializer, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            ca_subbooking = ca_serializer.data['subbookings'][0]
            self.assertIn('service_promotion', ca_subbooking)
            self.assertIsNotNone(ca_subbooking['service_promotion'])
            self.assertEqual(90.0, ca_subbooking['service_promotion']['_price'])

            b_appointment = AppointmentWrapper.get_by_subbooking_id(
                ca_subbooking['id'],
                self.business.id,
            )
            self.assertIsNotNone(b_appointment)

            bci_2 = baker.make(
                'business.BusinessCustomerInfo',
                user=baker.make(
                    'user.User', email='<EMAIL>', cell_phone='3213213215'
                ),
                discount=50,
                business=self.business,
            )

            # change bci for client appointment as business
            data = {
                '_notify_about_reschedule': False,
                '_preserve_order': False,
                '_update_future_bookings': False,
                '_version': b_appointment._version,  # pylint: disable=W0212
                'business_note': '',
                'business_secret_note': '',
                'customer': {
                    'id': bci_2.id,
                    'mode': ACMode.CUSTOMER_CARD,
                },
                'customer_note': None,
                'dry_run': False,
                'new_repeating': None,
                'overbooking': False,
                'subbookings': [
                    {
                        'appliance_id': -1,
                        'booked_from': self.booking_time(10, 0),
                        'booked_till': (self.booking_time(10, 0) + self.variant.duration),
                        'duration': None,
                        'id': None,
                        'service_variant': {
                            'id': self.variant.id,
                            'mode': SVMode.VARIANT,
                            'service_name': None,
                        },
                        'staffer_id': -1,
                    }
                ],
            }

            ba_serializer = AppointmentSerializer(
                instance=b_appointment,
                data=data,
                context=self.business_appointment_context,
            )

            self.assertTrue(ba_serializer.is_valid(), ba_serializer.errors)
            ba_serializer.save()
            b_subbooking = ba_serializer.data['subbookings'][0]
            self.assertIsNotNone(b_subbooking['service_promotion'])
            self.assertEqual(50.0, b_subbooking['service_promotion']['_price'])
        self._assert_step_completed()

    def test_business_change_service_for_bci_appointment(self, *__):
        booked_from = time(10, 0)
        booking_time = self.booking_time(9, 30)
        self.customer_user(discount=5)

        with freeze_time(booking_time):
            self.flash_sale(
                discount_rate=10,
                variant_ids=[v.id for v in list(self.variants.values())],
            )
            ca_serializer, _cat = self.customer_appointment_serializer(
                booked_from=booked_from,
                dry_run=False,
            )
            ca_subbooking = ca_serializer.data['subbookings'][0]
            self.assertIn('service_promotion', ca_subbooking)
            self.assertIsNotNone(ca_subbooking['service_promotion'])

            self.assertEqual(
                ca_subbooking['service_promotion']['price_before_discount'], Decimal('100')
            )
            self.assertEqual(
                ca_subbooking['service_promotion']['promotion_type'], SERVICE_VARIANT_FLASH_SALE
            )
            self.assertEqual(ca_subbooking['service_promotion']['_price'], Decimal('90'))

            b_appointment = AppointmentWrapper.get_by_subbooking_id(
                ca_subbooking['id'],
                self.business.id,
            )
            self.assertIsNotNone(b_appointment)

            # move appointment as business
            data = {
                '_notify_about_reschedule': False,
                '_preserve_order': False,
                '_update_future_bookings': False,
                '_version': b_appointment._version,  # pylint: disable=W0212
                'business_note': '',
                'business_secret_note': '',
                'customer': {
                    'id': self.customer_bci.id,
                    'mode': ACMode.CUSTOMER_CARD,
                },
                'customer_note': None,
                'dry_run': True,
                'new_repeating': None,
                'overbooking': False,
                'subbookings': [
                    {
                        'appliance_id': -1,
                        'booked_from': self.booking_time(10, 0),
                        'booked_till': None,
                        'duration': None,
                        'id': ca_subbooking['id'],
                        'service_variant': {
                            'id': self.gap_hole_variant.id,  # change variant
                            'mode': SVMode.VARIANT,
                            'service_name': None,
                        },
                        'staffer_id': -1,
                    }
                ],
            }

            ba_serializer = AppointmentSerializer(
                instance=b_appointment,
                data=data,
                context=self.business_appointment_context,
            )

            self.assertTrue(ba_serializer.is_valid(), ba_serializer.errors)
            ba_serializer.save()

            b_subbooking = ba_serializer.data['subbookings'][0]
            self.assertIsNotNone(b_subbooking['service_promotion'])

            self.assertEqual(
                b_subbooking['service_promotion']['price_before_discount'], Decimal('200')
            )
            self.assertEqual(
                b_subbooking['service_promotion']['promotion_type'], SERVICE_VARIANT_CLIENT_DISCOUNT
            )
            self.assertEqual(b_subbooking['service_promotion']['_price'], Decimal('190'))

        self._assert_step_completed()

    def test_business_empty_appointment(self, *__):
        booked_from = self.booking_time(10, 0)
        booked_till = booked_from + timedelta(minutes=30)
        booking_time = self.booking_time(9, 45)
        self.customer_user()

        with freeze_time(booking_time):
            self.flash_sale(
                discount_rate=10,
                variant_ids=[v.id for v in list(self.variants.values())],
            )

            data = {
                '_notify_about_reschedule': True,
                '_preserve_order': False,
                '_update_future_bookings': False,
                '_version': 0,
                'business_note': '',
                'business_secret_note': '',
                'customer': None,
                'customer_note': None,
                'dry_run': True,
                'new_repeating': None,
                'overbooking': False,
                'subbookings': [
                    {
                        'appliance_id': -1,
                        'booked_from': booked_from.isoformat(),
                        'booked_till': booked_till.isoformat(),
                        'duration': None,
                        'id': None,
                        'service_variant': None,
                        'staffer_id': -1,
                    }
                ],
            }
            ba_serializer = AppointmentSerializer(
                instance=None,
                data=data,
                context=self.business_appointment_context,
            )
            self.assertTrue(ba_serializer.is_valid(), ba_serializer.errors)
        self._assert_step_completed()
