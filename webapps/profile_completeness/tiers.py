from __future__ import annotations

import typing as t
from abc import ABCMeta

from django.utils.translation import gettext_lazy as _

from lib.events import EventSignal
from webapps.business.models import Business
from webapps.profile_completeness.enums import TierLevel
from webapps.profile_completeness.events import (
    notify_tier1_completed,
    notify_tier2_completed,
    notify_tier3_completed,
    notify_tier4_completed,
    notify_tier5_completed,
)
from webapps.profile_completeness.rewards import (
    BaseReward,
    BooksyFlipbookReward,
    InfluencerPosterReward,
    InfluencerSocialPostTemplatesReward,
    MarketingKitReward,
    ProfileTipsReward,
    MessageBlastTemplateReward,
    SocialPostTemplatesReward,
)
from webapps.profile_completeness.steps import (
    ActivateMobilePaymentsStep,
    AddThreePhotosStep,
    AddWorkplacePhotoStep,
    AttractUsingBoostStep,
    BaseStep,
    Collect100ReviewsStep,
    Collect10ReviewsStep,
    Collect250ReviewsStep,
    Complete100CustomerBookingsStep,
    Complete10CustomerBookingsStep,
    Complete500CustomerBookingsStep,
    InviteCustomerToBooksyStep,
    LaunchPromotionsStep,
    LinkProfileStep,
    ReferBusinessStep,
    ReviewBooksyAppStep,
    SellGiftcardStep,
    SendMessageBlastStep,
    ShareBooksyProfileStep,
    SourceBookingStep,
    TurnOnNoshowProtectionStep,
    UpgradeVoicemailGreetingsStep,
    UseSocialPostCreatorStep,
)

CompletedStep = None  # pylint: disable=invalid-name
CompletedTier = None  # pylint: disable=invalid-name

tier_classes: t.Dict[TierLevel, t.Type[BaseTier]] = {}


class BaseTier(metaclass=ABCMeta):
    rewards: t.List[BaseReward]
    steps: t.List[BaseStep]
    active: bool = False
    steps_count_completed: int = 0
    level: TierLevel
    notification_on_complete: EventSignal = None

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)

        tier_classes[cls.level] = cls

    def __init__(self, business: Business) -> None:
        self.business = business

    @property
    def steps_count(self):
        return len(self.steps)

    def _init_steps(self, step_classes: t.Iterable[t.Type[BaseStep]]) -> None:
        self.steps = [
            step_class() for step_class in step_classes if step_class.is_available(self.business)
        ]

    def check_completed(self):
        global CompletedStep  # pylint: disable=global-statement,invalid-name
        if CompletedStep is None:
            from webapps.profile_completeness.models import (  # pylint: disable=redefined-outer-name
                CompletedStep,
            )

        step_internal_names = [step.internal_name for step in self.steps]
        completed_steps_count = CompletedStep.objects.filter(
            business=self.business,
            step_internal_name__in=step_internal_names,
        ).count()

        return completed_steps_count == len(step_internal_names)

    def _on_marked_completed(self):
        for reward in self.rewards:
            reward.process_reward(business=self.business)

        self.notification_on_complete.send(self.business)

    def mark_completed(self):
        global CompletedTier  # pylint: disable=global-statement,invalid-name
        if CompletedTier is None:
            from webapps.profile_completeness.models import (  # pylint: disable=redefined-outer-name
                CompletedTier,
            )

        created = CompletedTier.objects.get_or_create(
            business=self.business,
            tier_level=self.level,
        )[1]
        if created:
            self._on_marked_completed()

    def __repr__(self):
        return f'{self.__class__} level={self.level}'


class Tier1(BaseTier):
    title = _('Novice')
    level = TierLevel.LEVEL_1
    notification_on_complete = notify_tier1_completed

    description = _(
        'Get started: Spread the word that you’re on Booksy. Soon your '
        'calendar will be booking itself.'
    )
    background = 'https://d2zdpiztbgorvt.cloudfront.net/region1/backgrounds/tier_background_5.jpg'

    def __init__(self, business: Business) -> None:
        super().__init__(business)
        self.rewards = [
            BooksyFlipbookReward(),
            SocialPostTemplatesReward(),
        ]

        self._init_steps(
            [
                AddWorkplacePhotoStep,
                InviteCustomerToBooksyStep,
                UpgradeVoicemailGreetingsStep,
                ShareBooksyProfileStep,
                Complete10CustomerBookingsStep,
            ]
        )


class Tier2(BaseTier):
    title = _('Apprentice')
    level = TierLevel.LEVEL_2
    notification_on_complete = notify_tier2_completed

    # xgettext:no-python-format
    description = _(
        'Be social: Nearly 50% of the world\'s population uses social media, '
        'start building your digital storefront.'
    )
    background = 'https://d2zdpiztbgorvt.cloudfront.net/region1/backgrounds/tier_background_1.jpg'

    def __init__(self, business: Business) -> None:
        super().__init__(business)
        self.rewards = [
            MessageBlastTemplateReward(),
        ]

        self._init_steps(
            [
                AddThreePhotosStep,
                UseSocialPostCreatorStep,
                LinkProfileStep,
                Collect10ReviewsStep,
            ]
        )


class Tier3(BaseTier):
    title = _('Associate')
    level = TierLevel.LEVEL_3
    notification_on_complete = notify_tier3_completed

    description = _(
        'Protect your bottom line: Explore more ways to make money and get '
        'paid even when your clients don’t show up.'
    )
    background = 'https://d2zdpiztbgorvt.cloudfront.net/region1/backgrounds/tier_background_2.jpg'

    def __init__(self, business: Business) -> None:
        super().__init__(business)
        self.rewards = [
            ProfileTipsReward(),
        ]
        self._init_steps(
            [
                ActivateMobilePaymentsStep,
                TurnOnNoshowProtectionStep,
                SellGiftcardStep,
                ReviewBooksyAppStep,
                Complete100CustomerBookingsStep,
            ]
        )


class Tier4(BaseTier):
    title = _('Master')
    level = TierLevel.LEVEL_4
    notification_on_complete = notify_tier4_completed

    description = _(
        'Rise above: Increase your visibility and set yourself apart from the '
        'competition using Boost.'
    )
    background = 'https://d2zdpiztbgorvt.cloudfront.net/region1/backgrounds/tier_background_4.jpg'

    def __init__(self, business: Business) -> None:
        super().__init__(business)
        self.rewards = [
            MarketingKitReward(),
        ]
        self._init_steps(
            [
                SendMessageBlastStep,
                AttractUsingBoostStep,
                LaunchPromotionsStep,
                Collect100ReviewsStep,
            ]
        )


class Tier5(BaseTier):
    title = _('Influencer')
    level = TierLevel.LEVEL_5
    notification_on_complete = notify_tier5_completed

    description = _(
        'Be a leader: The global Booksy community connects thousands '
        'of Providers with millions of customers.'
    )
    background = 'https://d2zdpiztbgorvt.cloudfront.net/region1/backgrounds/tier_background_3.jpg'

    def __init__(self, business: Business):
        super().__init__(business)
        self.rewards = [
            InfluencerPosterReward(),
            InfluencerSocialPostTemplatesReward(),
        ]
        self._init_steps(
            [
                ReferBusinessStep,
                Complete500CustomerBookingsStep,
                SourceBookingStep,
                Collect250ReviewsStep,
            ]
        )


def get_tiers(business: Business) -> t.List[BaseTier]:
    return [
        Tier1(business),
        Tier2(business),
        Tier3(business),
        Tier4(business),
        Tier5(business),
    ]


def get_tier(tier_level: TierLevel, business: Business) -> t.Optional[BaseTier]:
    """Returns Tier class object based on tier_level"""
    tier_class = tier_classes.get(tier_level)
    if tier_class is None:
        return None

    return tier_class(business)
