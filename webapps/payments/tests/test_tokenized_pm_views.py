from dataclasses import asdict

import mock
import stripe
from django.urls import reverse
from model_bakery import baker
from rest_framework import status

from drf_api.lib.base_drf_test_case import CustomerAPITestCase
from lib.baker_utils import get_or_create_booking_source
from lib.feature_flag.feature.payment import CxPromotedPaymentMethods
from lib.payment_providers.entities import CardData
from lib.payment_providers.enums import PaymentMethodType, SetupIntentStatus
from lib.payments.enums import PaymentProviderCode
from lib.tests.utils import override_feature_flag
from webapps.booking.models import BookingSources
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_providers.models import (
    Customer,
    SetupIntent,
    StripeTokenizedPaymentMethod,
    TokenizedPaymentMethod,
)
from webapps.payment_providers.ports.customer_ports import PaymentProvidersCustomerPort
from webapps.user.models import User


class SetupIntentViewTestCase(CustomerAPITestCase):
    @classmethod
    def setUpTestData(cls):
        cls.user = baker.make(
            User,
            email='<EMAIL>',
            cell_phone='+48697850000',
        )
        cls.customer_booking_src = get_or_create_booking_source(
            app_type=BookingSources.CUSTOMER_APP,
            api_key='customer_key',
        )
        customer_wallet, _ = PaymentGatewayPort.get_or_create_customer_wallet(
            customer_user_id=cls.user.id,
            email=cls.user.email,
            phone=cls.user.cell_phone,
            statement_name='',
        )
        cls.customer = Customer.objects.get(id=customer_wallet.customer_id)
        with mock.patch(
            'webapps.payment_providers.providers.stripe.stripe.Customer.create'
        ) as stripe_mock:
            stripe_mock.return_value = mock.MagicMock(id='customer_external_id')
            PaymentProvidersCustomerPort.create_provider_customer(
                customer_id=customer_wallet.customer_id,
                payment_provider_code=PaymentProviderCode.STRIPE,
            )

    @mock.patch('webapps.payment_providers.services.stripe.stripe.StripeProvider')
    def test_post_setup_intent(self, stripe_provider_mock):
        client_secret = 'external_setup_intent_client_secret'
        setup_intent_id = 'external_setup_intent_id'
        stripe_provider_mock.create_setup_intent.return_value = stripe.SetupIntent().construct_from(
            {
                'client_secret': client_secret,
                'id': setup_intent_id,
            },
            key=stripe.api_key,
        )
        response = self.client.post(
            reverse(
                'payments__create_setup_intent',
            ),
            content_type='application/json',
        )
        self.assertEqual(
            response.status_code,
            200,
        )
        self.assertDictEqual(
            response.json(),
            {
                'client_secret': client_secret,
                'id': str(SetupIntent.objects.last().id),
                'status': SetupIntentStatus.NEW,
            },
        )

    def test_get_setup_intent(self):
        client_secret = 'external_setup_intent_client_secret'
        setup_intent = baker.make(
            SetupIntent,
            customer=self.customer,
            provider_code=PaymentProviderCode.STRIPE,
            method_type=PaymentMethodType.CARD,
            client_secret=client_secret,
        )
        response = self.client.get(
            reverse('payments__setup_intent_details', kwargs={'setup_intent_id': setup_intent.id}),
            content_type='application/json',
        )
        self.assertEqual(
            response.status_code,
            200,
        )
        self.assertDictEqual(
            response.json(),
            {
                'client_secret': client_secret,
                'id': str(SetupIntent.objects.last().id),
                'status': SetupIntentStatus.NEW,
            },
        )

        other_customer = baker.make(
            Customer,
        )
        other_setup_intent = baker.make(
            SetupIntent,
            customer=other_customer,
            provider_code=PaymentProviderCode.STRIPE,
            method_type=PaymentMethodType.CARD,
            client_secret=client_secret,
        )
        response = self.client.get(
            reverse(
                'payments__setup_intent_details', kwargs={'setup_intent_id': other_setup_intent.id}
            ),
            content_type='application/json',
        )
        self.assertEqual(
            response.status_code,
            404,
        )


class SetDefaultTokenizedPaymentMethodViewTestCase(CustomerAPITestCase):
    def setUp(self) -> None:
        self.user = baker.make(
            User,
            email='<EMAIL>',
            cell_phone='+48697850000',
        )
        self.customer_booking_src = get_or_create_booking_source(
            app_type=BookingSources.CUSTOMER_APP,
            api_key='customer_key',
        )
        customer_wallet, _ = PaymentGatewayPort.get_or_create_customer_wallet(
            customer_user_id=self.user.id,
            email=self.user.email,
            phone=self.user.cell_phone,
            statement_name='',
        )
        self.customer = Customer.objects.get(id=customer_wallet.customer_id)
        self.tokenized_pm_1 = baker.make(
            TokenizedPaymentMethod,
            customer=self.customer,
            provider_code=PaymentProviderCode.STRIPE,
            method_type=PaymentMethodType.CARD,
            default=True,
            details=asdict(
                CardData(
                    last_digits='1234',
                    expiry_month=1,
                    expiry_year=2100,
                    brand='visa',
                    cardholder_name='cardholder name',
                ),
            ),
        )
        self.tokenized_pm_2 = baker.make(
            TokenizedPaymentMethod,
            customer=self.customer,
            provider_code=PaymentProviderCode.STRIPE,
            method_type=PaymentMethodType.CARD,
            default=False,
            details=asdict(
                CardData(
                    last_digits='2345',
                    expiry_month=2,
                    expiry_year=2101,
                    brand='mastercard',
                    cardholder_name='cardholder name',
                ),
            ),
        )
        super().setUp()

    def test_post_set_default_tokenized_pm(self):
        response = self.client.post(
            reverse(
                'payments__set_default_tokenized_pm',
                kwargs={'payment_method_id': self.tokenized_pm_2.id},
            ),
            content_type='application/json',
        )
        response_json = response.json()
        self.assertEqual(
            response_json['id'],
            str(self.tokenized_pm_2.id),
        )
        self.assertEqual(response_json['default'], True)
        self.tokenized_pm_1.refresh_from_db()
        self.tokenized_pm_2.refresh_from_db()
        self.assertEqual(
            self.tokenized_pm_1.default,
            False,
        )
        self.assertEqual(
            self.tokenized_pm_2.default,
            True,
        )


class StripeApplePayPaymentTokenViewTestCase(CustomerAPITestCase):
    def setUp(self) -> None:
        self.user = baker.make(
            User,
            email='<EMAIL>',
            cell_phone='+48697850000',
        )
        self.customer_booking_src = get_or_create_booking_source(
            app_type=BookingSources.CUSTOMER_APP,
            api_key='customer_key',
        )
        super().setUp()

    @mock.patch('webapps.payment_providers.services.stripe.stripe.StripeProvider')
    def test_post_stripe_apple_pay_tokenized_pm(self, stripe_provider_mock):
        token_id = 'token_id'
        stripe_provider_mock.create_payment_token.return_value = stripe.Token().construct_from(
            {
                'id': token_id,
            },
            key=stripe.api_key,
        )
        response = self.client.post(
            reverse('payments__payment_token_apple_pay'),
            data={
                'pk_token': 'pk_token',
                'pk_token_instrument_name': 'pk_token_instrument_name',
                'pk_token_payment_network': 'pk_token_payment_network',
                'pk_token_transaction_id': 'pk_token_transaction_id',
            },
            content_type='application/json',
        )
        self.assertEqual(
            response.json()['id'],
            token_id,
        )


class ListTokenizedPaymentMethodsViewTestCase(CustomerAPITestCase):
    CARD_DETAILS = {
        'last4': '1234',
        'other': 'detail',
        'expiry_month': 6,
        'expiry_year': 2023,
    }

    @classmethod
    @mock.patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    def setUpTestData(cls, _):
        cls.user = baker.make(
            User,
            email='<EMAIL>',
            cell_phone='+48697850000',
        )
        cls.customer_booking_src = get_or_create_booking_source(
            app_type=BookingSources.CUSTOMER_APP,
            api_key='customer_key',
        )
        customer_wallet, _ = PaymentGatewayPort.get_or_create_customer_wallet(
            customer_user_id=cls.user.id,
            email=cls.user.email,
            phone=cls.user.cell_phone,
            statement_name='',
        )
        cls.customer = Customer.objects.get(id=customer_wallet.customer_id)
        cls.pm1_stripe = baker.make(
            TokenizedPaymentMethod,
            customer=cls.customer,
            provider_code=PaymentProviderCode.STRIPE,
            method_type=PaymentMethodType.CARD,
            details=cls.CARD_DETAILS,
        )
        cls.pm2_adyen = baker.make(
            TokenizedPaymentMethod,
            customer=cls.customer,
            provider_code=PaymentProviderCode.ADYEN,
            method_type=PaymentMethodType.CARD,
            details=cls.CARD_DETAILS,
        )

    def _assert_expected_pm_ids_set(self, expected_pms, response):
        expected_pm_ids = set(map(str, [pm.id for pm in expected_pms]))
        self.assertEqual(expected_pm_ids, {pm['id'] for pm in response['payment_methods']})

    @override_feature_flag(
        {
            CxPromotedPaymentMethods.flag_name: {
                'prepayment': 'Blik',
                'booksy_pay': "Apple_Pay/Google_Pay",
            }
        }
    )
    @mock.patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    def test_get_all_tokenized_pms_with_promoted_payment_methods(self, _):
        response = self.client.get(
            reverse('payments__list_all_tokenized_pms'),
            content_type='application/json',
        ).json()
        self._assert_expected_pm_ids_set(
            expected_pms=[
                self.pm1_stripe,
                self.pm2_adyen,
            ],
            response=response,
        )
        self.assertEqual(
            response['cx_promoted_payment_methods'],
            {'prepayment': 'Blik', 'booksy_pay': "Apple_Pay/Google_Pay"},
        )

    @mock.patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    def test_get_all_tokenized_pms(self, _):
        response = self.client.get(
            reverse('payments__list_all_tokenized_pms'),
            content_type='application/json',
        ).json()
        self._assert_expected_pm_ids_set(
            expected_pms=[
                self.pm1_stripe,
                self.pm2_adyen,
            ],
            response=response,
        )

    @mock.patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    def test_get_stripe_tokenized_pms(self, _):
        response = self.client.get(
            reverse(
                'payments__list_provider_tokenized_pms',
                kwargs={'provider_code': PaymentProviderCode.STRIPE},
            ),
            content_type='application/json',
        ).json()
        self._assert_expected_pm_ids_set(
            expected_pms=[
                self.pm1_stripe,
            ],
            response=response,
        )

    @override_feature_flag(
        {
            CxPromotedPaymentMethods.flag_name: {
                'prepayment': 'Apple_Pay/Google_Pay',
                'booksy_pay': 'Blik',
            }
        }
    )
    @mock.patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    def test_get_stripe_tokenized_pms_with_promoted_payment_methods(self, _):
        response = self.client.get(
            reverse(
                'payments__list_provider_tokenized_pms',
                kwargs={'provider_code': PaymentProviderCode.STRIPE},
            ),
            content_type='application/json',
        ).json()
        self._assert_expected_pm_ids_set(
            expected_pms=[
                self.pm1_stripe,
            ],
            response=response,
        )
        self.assertEqual(
            response['cx_promoted_payment_methods'],
            {'prepayment': 'Apple_Pay/Google_Pay', 'booksy_pay': 'Blik'},
        )

    def test_get_adyen_tokenized_pms(self):
        response = self.client.get(
            reverse(
                'payments__list_provider_tokenized_pms',
                kwargs={'provider_code': PaymentProviderCode.ADYEN},
            ),
            content_type='application/json',
        ).json()
        self._assert_expected_pm_ids_set(
            expected_pms=[
                self.pm2_adyen,
            ],
            response=response,
        )


class TokenizedPaymentMethodsViewTestCase(CustomerAPITestCase):
    CARD_DETAILS = {
        'last4': '1234',
        'other': 'detail',
    }

    @classmethod
    @mock.patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    def setUpTestData(cls, _):
        cls.user = baker.make(
            User,
            email='<EMAIL>',
            cell_phone='+48697850000',
        )
        cls.customer_booking_src = get_or_create_booking_source(
            app_type=BookingSources.CUSTOMER_APP,
            api_key='customer_key',
        )
        customer_wallet, _ = PaymentGatewayPort.get_or_create_customer_wallet(
            customer_user_id=cls.user.id,
            email=cls.user.email,
            phone=cls.user.cell_phone,
            statement_name='',
        )
        cls.customer = Customer.objects.get(id=customer_wallet.customer_id)
        cls.pm1_stripe = baker.make(
            TokenizedPaymentMethod,
            customer=cls.customer,
            provider_code=PaymentProviderCode.STRIPE,
            method_type=PaymentMethodType.CARD,
            details=cls.CARD_DETAILS,
        )
        baker.make(
            StripeTokenizedPaymentMethod,
            tokenized_payment_method=cls.pm1_stripe,
        )

        user2 = baker.make(
            User,
            email='<EMAIL>',
            cell_phone='+48697850000',
        )
        customer_wallet2, _ = PaymentGatewayPort.get_or_create_customer_wallet(
            customer_user_id=user2.id,
            email=user2.email,
            phone=user2.cell_phone,
            statement_name='',
        )
        customer2 = Customer.objects.get(id=customer_wallet2.customer_id)
        cls.pm2_stripe = baker.make(
            TokenizedPaymentMethod,
            customer=customer2,
            provider_code=PaymentProviderCode.STRIPE,
            method_type=PaymentMethodType.CARD,
            details=cls.CARD_DETAILS,
        )

    @mock.patch('webapps.payment_providers.services.stripe.stripe.StripeProvider')
    @mock.patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    def test_delete_tokenized_payment_method(self, _, _stripe_provider_mock):
        self.client.delete(
            reverse(
                'payments__tokenized_pm',
                kwargs={'payment_method_id': self.pm1_stripe.id},
            ),
            content_type='application/json',
        )
        self.assertEqual(
            TokenizedPaymentMethod.objects.filter(customer=self.customer).count(),
            0,
        )
        self.assertEqual(
            TokenizedPaymentMethod.all_objects.filter(customer=self.customer).count(),
            1,
        )

        self.assertEqual(
            StripeTokenizedPaymentMethod.objects.filter(
                tokenized_payment_method=self.pm1_stripe,
            ).count(),
            0,
        )
        self.assertEqual(
            StripeTokenizedPaymentMethod.all_objects.filter(
                tokenized_payment_method=self.pm1_stripe,
            ).count(),
            1,
        )
        resp = self.client.delete(
            reverse(
                'payments__tokenized_pm',
                kwargs={'payment_method_id': self.pm1_stripe.id},
            ),
            content_type='application/json',
        )
        self.assertEqual(
            resp.status_code,
            status.HTTP_404_NOT_FOUND,
        )

    @mock.patch('webapps.payment_providers.services.stripe.stripe.StripeProvider')
    @mock.patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    def test_delete_tokenized_payment_not_card_owner(self, _, _stripe_provider_mock):
        resp = self.client.delete(
            reverse(
                'payments__tokenized_pm',
                kwargs={'payment_method_id': self.pm2_stripe.id},
            ),
            content_type='application/json',
        )
        self.assertEqual(
            resp.status_code,
            status.HTTP_404_NOT_FOUND,
        )
