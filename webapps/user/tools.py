from functools import lru_cache
from typing import Optional
from collections import namedtuple

from django.apps import apps as django_apps
from django.contrib.auth import get_user
from django.core.exceptions import ObjectDoesNotExist
from django.db import DEFAULT_DB_ALIAS

from webapps.user.tasks.business_customer_info import update_user_bci
from webapps.user.tasks.comments import reindex_user_comments
from webapps.user.tasks.family_and_friends import update_member_profile_task


BciCommentsData = namedtuple(
    'BciCommentsData',
    [
        'instance',
        'created',
        'model_name',
        'update_fields',
        'comment_fields',
        'check_relationship',
    ],
    defaults=(None, None, None, None, None, False),
)


def get_user_model() -> 'User':
    """Dynamically import User model"""
    return django_apps.get_model('user', 'User', require_ready=False)


def get_system_user(
    username='admin',
    email='<EMAIL>',
    using=DEFAULT_DB_ALIAS,
    user_model=None,
):
    user = user_model if user_model else get_user_model()
    system = user.objects.using(using).get_or_create(
        username=username,
        defaults={
            'username': username,
            'email': email,
            'first_name': 'Booksy',
            'last_name': 'System',
            'is_active': True,
            'is_staff': True,
            'is_superuser': True,
        },
    )[0]
    return system


def get_umbrella_user(using=DEFAULT_DB_ALIAS, user_model=None):
    return get_system_user(
        username='umbrella_admin',
        email='<EMAIL>',
        using=using,
        user_model=user_model,
    )


def get_public_api_user(partner_name, partner_uuid):
    return get_system_user(
        username=partner_name,
        email=f'info+public_api_{partner_uuid}@booksy.com',
    )


@lru_cache(maxsize=1)
def get_user_from_django_request(request):
    auth_user = get_user(request)
    if auth_user.id is None:
        return None
    try:
        user = auth_user.user
    except ObjectDoesNotExist:
        user = None
    return user


def update_bci_and_comments(bci_comments_data):

    dirty_fields = set(
        bci_comments_data.instance.get_dirty_fields(
            check_relationship=bci_comments_data.check_relationship
        )
    )
    if bci_comments_data.model_name == 'User':
        user_id = bci_comments_data.instance.id
        profile_id = None
    else:  # model_name == UserProfile
        user_id = bci_comments_data.instance.user_id
        profile_id = bci_comments_data.instance.id

    fields_to_update = dirty_fields.intersection(bci_comments_data.update_fields)

    if fields_to_update:
        update_user_bci.delay(
            user_id=user_id,
            profile_id=profile_id,
            fields_to_update=list(fields_to_update),
        )

    if (
        dirty_fields.intersection(bci_comments_data.comment_fields)
        and not bci_comments_data.created
    ):
        reindex_user_comments.delay(user_id)


def update_family_and_friends_member_profile(instance, update_fields, model_name):
    dirty_fields = set(instance.get_dirty_fields(check_relationship=True))
    fields_to_update = dirty_fields.intersection(update_fields)

    if not fields_to_update:
        return

    update_member_profile_task.delay(
        user_id=instance.user.id if model_name == 'UserProfile' else instance.id,
        fields_to_update=list(fields_to_update),
        model_name=model_name,
    )


def can_change_password(request_user, user):
    """Do not allow non-superusers to change other
    staffers' passwords."""
    if (
        not request_user.is_superuser
        and user.id != request_user.id
        and (user.is_staff or user.is_superuser)
    ):
        return False
    return True


def truncate_last_name(last_name: Optional[str]) -> str:
    return f'{last_name[0]}…' if last_name else ''


def get_user_or_default(user_id: int = None) -> 'User':
    if user_id:
        return get_user_model().objects.get(id=user_id)
    return get_system_user()
