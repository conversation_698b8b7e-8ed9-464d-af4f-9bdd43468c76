import random
import string

import jwt
import pytest
from django.core.cache import cache
from django.db.utils import IntegrityError
from django.test import override_settings
from freezegun import freeze_time
from mock import patch
from model_bakery import baker

from lib.test_utils import get_settings
from service.tools import jwt_public_key
from webapps.business.models import (
    Business,
    Resource,
    settings,
    BusinessCategory,
)
from webapps.user.models import (
    ChangeEmailRequest,
    ChangePasswordRequest,
    User,
    UserProfile,
    UserSessionCache,
    CustomerFavoriteCategory,
)
from webapps.user.tasks.sync import _get_user_cache_key
from webapps.user.baker_recipes import user_recipe


def test_external_api_id_property_id_is_none():
    user = User()
    assert user.external_api_id is None


@override_settings(DEPLOYMENT_LEVEL='dead')
@override_settings(API_COUNTRY='rus')
def test_external_api_id_property_id_not_live():
    user = User(id=123)
    assert user.external_api_id == 'dead-rus-123'


@override_settings(**get_settings(deployment_level='live'))
@override_settings(API_COUNTRY='rus')
def test_external_api_id_property_id_live():
    user = User(id=123)
    assert user.external_api_id == 'rus-123'


@pytest.mark.django_db
def test_get_staffer_access_level():
    user = baker.make(User)
    business = baker.make(Business)
    baker.make(
        Resource,
        staff_user=user,
        business=business,
        staff_access_level=Resource.STAFF_ACCESS_LEVEL_RECEPTION,
    )

    result = user.get_staffer_access_level(business)
    assert result == Resource.STAFF_ACCESS_LEVEL_RECEPTION


@pytest.mark.django_db
def test_get_staffer_access_level_no_resource():
    user = baker.make(User)
    business = baker.make(Business)

    result = user.get_staffer_access_level(business)
    assert result is None


@pytest.mark.django_db
def test_customer_profile():
    user = baker.make(User)
    profile = baker.make(
        UserProfile,
        profile_type=UserProfile.Type.CUSTOMER,
        user=user,
    )
    baker.make(
        UserProfile,
        profile_type=UserProfile.Type.BUSINESS,
        user=user,
    )
    assert user.customer_profile == profile


@pytest.mark.django_db
@patch('webapps.user.signals.PaymentGatewayAdapter.get_or_create_customer_wallet')
@patch('webapps.user.signals.update_customer_wallet_task.delay')
def test_wallet_creation_and_update(update_wallet_mock, create_wallet_mock):
    user = baker.make(User)
    assert create_wallet_mock.call_count == 0
    assert update_wallet_mock.call_count == 0
    baker.make(
        UserProfile,
        profile_type=str(UserProfile.Type.BUSINESS),
        user=user,
    )
    assert create_wallet_mock.call_count == 0
    assert update_wallet_mock.call_count == 0
    profile = baker.make(
        UserProfile,
        profile_type=str(UserProfile.Type.CUSTOMER),
        user=user,
    )
    # make sure that payment_gateway is created only if customer profile is created
    assert create_wallet_mock.call_count == 1
    assert update_wallet_mock.call_count == 0

    # make sure that updating profile won't create extra wallets
    profile.city = "Warsaw"
    profile.save()
    assert create_wallet_mock.call_count == 1
    assert update_wallet_mock.call_count == 0

    # make sure that updating field relevant field
    # on the User instance will trigger payment_gateway update
    user.cell_phone = "123456123"
    user.save()
    assert create_wallet_mock.call_count == 1
    assert update_wallet_mock.call_count == 1

    user.email = "<EMAIL>"
    user.save()
    assert create_wallet_mock.call_count == 1
    assert update_wallet_mock.call_count == 2
    user.first_name = "Chad"
    user.save()
    assert create_wallet_mock.call_count == 1
    assert update_wallet_mock.call_count == 3


@pytest.mark.django_db
def test_customer_profile_not_existing():
    user = baker.make(User)
    baker.make(
        UserProfile,
        profile_type=UserProfile.Type.BUSINESS,
        user=user,
    )
    assert user.customer_profile is None


@pytest.mark.django_db
def test_user_email_max_length():
    long_email = ''.join(random.choice(string.ascii_lowercase) for i in range(100)) + "@abc.com"
    user = baker.make(User, email=long_email)
    user.save()
    assert User._meta.get_field('email').max_length >= 254


@pytest.mark.django_db
@patch('webapps.user.models.User.generate_email_reset_jwt_token', return_value='123456')
def test_get_email_change_jwt_url(_):
    user = baker.make(User)
    default_url = 'http://localhost:8068/user-reset/en-us/change_email/123456/'
    pl_url = 'http://localhost:8068/user-reset/pl-us/change_email/123456/'

    assert user.get_email_change_jwt_url(request_data={}) == default_url
    assert user.get_email_change_jwt_url(request_data={}, language='pl') == pl_url


@pytest.mark.django_db
@freeze_time('2022-02-02')
@patch('webapps.user.models.get_random_string', return_value='symx6cp6mn4hfb0c')
def test_generate_email_reset_jwt_token(mocked_random):
    check_jwt_generation(
        mocked_random, request_class=ChangeEmailRequest, jwt_token_type='email_change'
    )


@pytest.mark.django_db
@freeze_time('2022-02-02')
@patch('webapps.user.models.get_random_string', return_value='symx6cp6mn4hfb0c')
def test_generate_password_reset_jwt_token(mocked_random):
    check_jwt_generation(
        mocked_random, request_class=ChangePasswordRequest, jwt_token_type='password_change'
    )


@pytest.mark.django_db
def test_generate_jwt_token():
    user = baker.make(User)
    nonce = 'supersecretnonce'
    subject = 'password_change'

    generated_token = user.generate_jwt_token(nonce=nonce, subject=subject)

    decoded_token = jwt.decode(
        generated_token,
        jwt_public_key(),
        algorithms=[settings.USER_JWT_ALG],
        audience=f'{settings.API_COUNTRY}.booksy.com',
    )

    assert decoded_token['sub'] == subject
    assert decoded_token['nonce'] == nonce
    assert decoded_token['aud'] == 'us.booksy.com'


def check_jwt_generation(mocked_random, request_class, jwt_token_type):
    user = baker.make(User)
    request_data = {
        'user_agent': 'Chrome',
        'fingerprint': 'test-random-fingerprint',
        'ip': '***********',
    }

    if jwt_token_type == 'email_change':
        generated_token = user.generate_email_reset_jwt_token(request_data)
    else:
        generated_token = user.generate_password_reset_jwt_token(request_data)

    request_object = request_class.objects.get(x_fingerprint='test-random-fingerprint')

    mocked_random.assert_called_once_with(16)
    assert request_object.user_agent == 'Chrome'
    assert request_object.x_fingerprint == 'test-random-fingerprint'
    assert request_object.ip == '***********'
    assert generated_token == user.generate_jwt_token('symx6cp6mn4hfb0c', jwt_token_type)


@pytest.mark.django_db
def test_delete_all_user_sessions():
    user = baker.make(User)
    user.delete_all_user_sessions()

    assert UserSessionCache.objects.count() == 0

    baker.make(UserSessionCache, user=user)

    user.delete_all_user_sessions()
    assert UserSessionCache.objects.count() == 0


@pytest.mark.usefixtures('switch_on_new_login_fixture')
@pytest.mark.django_db
@patch('webapps.session.booksy_auth.grpc_client.BooksyAuthClient._make_request')
def test_delete_all_user_sessions_in_auth(mock_make_request):
    user = baker.make(User)
    baker.make(UserSessionCache, user=user)

    user.delete_all_user_sessions()

    assert UserSessionCache.objects.count() == 0
    assert mock_make_request.call_args[0][2] == dict(country_user_id=user.id)


@pytest.mark.usefixtures('switch_on_new_login_fixture')
@pytest.mark.parametrize(
    'field_name, use_master',
    [
        ('email', True),
        ('first_name', False),
        ('facebook_id', True),
        ('home_phone', False),
        ('apple_user_uuid', True),
        ('superuser', True),
        ('is_superuser', True),
        ('password', True),
    ],
)
@pytest.mark.django_db
@patch('webapps.user.tasks.sync.sync_user_booksy_auth_from_replica_task')
@patch('webapps.user.tasks.sync.sync_user_booksy_auth_task')
def test_user_post_save_run_sync_auth(mock_sync_master, mock_sync_slave, field_name, use_master):
    user = baker.make(
        User,
        superuser=False,
        is_superuser=False,
    )  # first sync from master is for create
    cache.delete_pattern(_get_user_cache_key('*'))
    value = True if field_name in {'superuser', 'is_superuser'} else 'value'
    setattr(user, field_name, value)

    user.save()

    assert mock_sync_master.apply_async.call_count - 1 == int(use_master)
    assert mock_sync_slave.apply_async.call_count == int(not use_master)


@pytest.mark.usefixtures('switch_on_new_login_fixture')
@pytest.mark.parametrize(
    'field_name, sync',
    [
        ('facebook_id', True),
        ('updated', False),
        ('service_color_palette', False),
        ('password', True),
    ],
)
@pytest.mark.django_db
@patch('webapps.user.tasks.sync.sync_user_booksy_auth_proxy')
def test_user_post_save_not_run_sync_auth(mock_sync_proxy, field_name, sync):
    user = baker.make(User)  # first auth sync
    cache.delete_pattern(_get_user_cache_key('*'))
    setattr(user, field_name, 'value')

    user.save()

    assert mock_sync_proxy.call_count - 1 == int(sync)


@pytest.mark.usefixtures('switch_on_new_login_fixture')
@pytest.mark.django_db
@patch('webapps.user.tasks.sync.sync_user_booksy_auth_from_replica_task')
@patch('webapps.user.tasks.sync.sync_user_booksy_auth_task')
def test_userprofile_post_save_run_sync_auth(mock_sync_master, mock_sync_slave):
    key_pattern = _get_user_cache_key('*')
    cache.delete_pattern(key_pattern)  # tasks are mocked so we need manuale clear cache
    profile = baker.prepare(
        UserProfile,
        user=baker.make(User),
    )

    profile.save()  # after create send data immediately
    assert mock_sync_master.apply_async.call_count == 1
    assert mock_sync_slave.apply_async.call_count == 0

    cache.delete_pattern(key_pattern)  # tasks are mocked so we need manuale clear cache
    profile.save()  # after update use replica to read data
    assert mock_sync_master.apply_async.call_count == 1
    assert mock_sync_slave.apply_async.call_count == 1


@pytest.mark.django_db(transaction=True)
def test_check_password_hash():
    prefix_hash_with_390k_iteration = 'pbkdf2_sha256$390000$'
    hash_with_600k_iteration = (
        'pbkdf2_sha256$600000$'
        'FYtQDXkdRckiNC06f5pANQ$YVSgKukInzmhu6TC2f3k3R6U2tT9JKBv8ltlU0CpaFM='
    )  # result of make_password('some_password')
    user = baker.make(User, password=hash_with_600k_iteration)

    assert user.check_password('some_password')  # also should update password hash in db
    user.refresh_from_db()

    assert user.password.startswith(prefix_hash_with_390k_iteration)  # instead of 600k


@pytest.mark.django_db
def test_format_account_user_method():
    user = user_recipe.make()
    profile = baker.make(UserProfile, user=user)
    assert user.format_account(profile) == profile.format_account()


@pytest.mark.django_db
def test_create_favorite_category():
    user = baker.make(User)
    category = baker.make(BusinessCategory)
    favorite_category = CustomerFavoriteCategory.objects.create(user=user, category=category)
    assert favorite_category.user == user
    assert favorite_category.category == category


@pytest.mark.django_db
def test_unique_together_constraint():
    user = baker.make(User)
    category = baker.make(BusinessCategory)
    CustomerFavoriteCategory.objects.create(user=user, category=category)
    with pytest.raises(IntegrityError):
        CustomerFavoriteCategory.objects.create(user=user, category=category)


@pytest.mark.django_db
def test_update_favorite_categories_for_user():
    user = baker.make(User)
    category_1 = baker.make(BusinessCategory)
    category_2 = baker.make(BusinessCategory)
    category_3 = baker.make(BusinessCategory)
    baker.make(CustomerFavoriteCategory, user=user, category=category_1)
    CustomerFavoriteCategory.update_categories_for_user(user.id, [category_2.id, category_3.id])

    favorite_categories = CustomerFavoriteCategory.objects.filter(user=user).values_list(
        'category_id', flat=True
    )
    expected_favorites = {category_2.id, category_3.id}

    assert set(favorite_categories) == expected_favorites
