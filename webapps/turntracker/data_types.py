from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal
from typing import Optional

import dataclasses
from dateutil.tz import tzfile

from webapps.turntracker.enums import TileIcon, TileSize, TileType


# see README.md, if you like to use it
Slot = int


#
# INPUT DATA
#


@dataclass
class TrackerSettings:
    # can be simply a model
    ...


@dataclass
class TrackerBusiness:
    """From models: business.Business, turntracker.Settings"""

    business_id: int
    timezone: tzfile
    settings: TrackerSettings


@dataclass
class TrackerStaffer:
    """
    From schedule.ports.get_working_hours

    As of now, with_time_offs_merged=True
    https://booksy.slack.com/archives/C03QTBP4V42/p1659705155366589
    This make TrackerTimeOff obsolete.
    """

    staffer_id: int
    hours: list[tuple[Slot, Slot]]


# @dataclass
# class TrackerTimeOff:
#     """
#     From schedule.ResourceTimeOff
#     From booking.SubBooking
#        (appointment__type=AppointmentType.RESERVATION)
#     """
#     staffer_id: int
#     time_from: Optional[Slot]
#     time_till: Optional[Slot]


@dataclass
class PaymentInfo:
    checkout_booking_ids: list[int]
    transaction_id: Optional[int] = None
    finish_turn_id: Optional[int] = None
    payable: Optional[bool] = None


@dataclass
class PaymentObject:
    value: Decimal
    payment_object_id: int = None


# pylint: disable=too-many-instance-attributes
@dataclass
class TrackerBooking:  # pylint: disable=too-many-instance-attributes
    """
    From booking.SubBooking, pos.TransactionRow

    All SubBookings on given date must be loaded, independently of booking time,
    limited only by status (STATUSES_OCCUPYING_TIME_SLOTS?) and selected staffer_ids.
    """

    subbooking_id: int  # Subbooking
    appointment_id: int  # Subbooking
    appointment_status: str  # Appointment
    appointment_type: str  # Appointment

    booked_from: Slot
    duration: int  # `duration` originally is `relativedelta(minutes=...)`

    staffer_id: int  # Subbooking
    service_variant_id: Optional[int]  # Subbooking
    service_name: str  # Service-ServiceVariant
    service_price: Decimal  # from SubBooking (not settled) or TransactionRow (settled)

    is_settled: bool  # has TransactionRow
    created: datetime
    autoassign: bool
    payment_info: Optional[PaymentInfo] = None
    is_service: bool = True  # False when Tr. has no any relation to any Booking OR Service(
    # include manually added Service during checkout)

    # just pass to TileBooking:
    service_color: int = None


@dataclass
class PenTile:
    pentile_id: int
    staffer_id: int
    type: 'TileType'
    size: 'TileSize'
    icon: 'TileIcon'
    rotated: datetime = None


@dataclass
class PenTileInTile:
    pentile_id: int
    staffer_id: int
    rotated: datetime = None


@dataclass
class GridSource:
    business: TrackerBusiness
    bookings: list[TrackerBooking]
    staffers: list[TrackerStaffer]
    tiles: list[PenTile]


#
# OUTPUT DATA
#


@dataclass
class TileBooking:
    booked_from: Slot
    service_name: str
    subbooking_id: int
    appointment_id: int
    service_color: int
    appointment_status: str
    duration: int
    payment_info: Optional[PaymentInfo] = None


@dataclass
class Tile:
    type: TileType
    size: TileSize = TileSize.FULL
    icon: Optional[TileIcon] = None
    amount: Optional[Decimal] = None
    bookings: Optional[list[TileBooking]] = None
    booked_from: Optional[Slot] = None
    pen_tile: 'PenTileInTile' = None

    @property
    def is_accessible(self):
        return self.type not in [TileType.TIME_OFF, TileType.BUSY]

    def __lt__(self, other: 'Tile'):
        return self.sort_value < other.sort_value

    @property
    def sort_value(self):
        return self.type.weight + self.size.weight + (self.icon.weight if self.icon else 0)


@dataclass
class GridRow:
    staffer_id: int
    tiles: list[Tile]
    revenue: 'Decimal' = dataclasses.field(default_factory=Decimal)

    @property
    def turns(self):
        skip_tiles = [TileType.NEXT, TileType.TIME_OFF]
        number = 0
        for tile in self.tiles:
            if not tile.type in skip_tiles:
                number += 1
        return number


@dataclass
class Grid:
    rows: list[GridRow]
