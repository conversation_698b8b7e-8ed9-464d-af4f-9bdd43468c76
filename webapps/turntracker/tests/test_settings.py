from django.shortcuts import reverse
from model_bakery import baker
from rest_framework import status
from rest_framework.test import APITestCase

from lib.baker_utils import get_or_create_booking_source
from service.exceptions import ServiceError
from webapps.booking.models import BookingSources
from webapps.business.baker_recipes import business_recipe, resource_recipe
from webapps.business.enums import StaffAccessLevels
from webapps.business.models import Resource
from webapps.consts import WEB
from webapps.turntracker.enums import TrackerMode
from webapps.turntracker.models import TrackerSettings
from webapps.turntracker.serializers import TurntrackerSettingsSerializer
from webapps.user.baker_recipes import user_recipe
from webapps.user.enums import AuthOriginEnum


class TrackerSettingsBaseSetUp(APITestCase):
    @classmethod
    def setUpTestData(cls):
        cls.booking_source = get_or_create_booking_source(
            app_type=BookingSources.BUSINESS_APP,
            name=WEB,
        )

        cls.headers = {'HTTP_X_API_KEY': cls.booking_source.api_key}
        cls.user = user_recipe.make()

    def setUp(self):
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        self.client.credentials(HTTP_X_ACCESS_TOKEN=self.session.session_key)
        self.business = business_recipe.make()

    def _set_business_access_level(self, access_level: StaffAccessLevels):
        return resource_recipe.make(
            business=self.business,
            staff_user=self.user,
            staff_access_level=access_level,
        )


class TestSettingsRetrieve(TrackerSettingsBaseSetUp):
    def setUp(self):
        super().setUp()
        self._set_business_access_level(Resource.STAFF_ACCESS_LEVEL_MANAGER)

    def test_retrieve_ok(self):
        self.url = reverse('turntracker_settings', args=(self.business.id,))
        response = self.client.get(self.url, **self.headers)
        data = response.data
        self.assertFalse(data['enabled'])
        self.assertEqual(data['mode'], TrackerMode.STAFF_MODE)
        self.assertEqual(data['minimum_turn_value'], '0.00')
        self.assertEqual(data['full_turn_value'], '50.00')
        self.assertFalse(data['revenue_split'])
        self.assertTrue(data['penalty_turns'])
        self.assertTrue(data['added_turns'])
        self.assertFalse(data['bonus_turns'])
        self.assertFalse(data['walk_in_customer_warning'])
        self.assertTrue(data['count_in_scheduled'])
        self.assertTrue(data['predict_conflicts'])
        self.assertEqual(data['predict_conflicts_interval'], 30)
        self.assertTrue(data['show_revenue'])
        self.assertTrue(data['auto_merge_half_turns'])

    def test_retrieve_other_turntracker_settings(self):
        tracker_settings = baker.make(TrackerSettings, business_id=115325)
        self.url = reverse('turntracker_settings', args=(tracker_settings.business_id,))
        response = self.client.get(self.url, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


class TestSettingsUpdate(TrackerSettingsBaseSetUp):
    def setUp(self):
        super().setUp()
        self._set_business_access_level(Resource.STAFF_ACCESS_LEVEL_MANAGER)

    def test_enable_turntracker(self):
        self.url = reverse('turntracker_settings', args=(self.business.id,))
        response = self.client.put(
            self.url,
            **self.headers,
            data={
                'enabled': True,
            },
        )
        data = response.data
        self.assertTrue(data['enabled'])

    def test_update_settings(self):
        self.url = reverse('turntracker_settings', args=(self.business.id,))
        response = self.client.put(
            self.url,
            **self.headers,
            data={
                'full_turn_value': 100.51,
                'minimum_turn_value': 0.00,
                'walk_in_customer_warning': True,
                'show_revenue': False,
            },
        )
        data = response.data
        self.assertEqual(data['full_turn_value'], '100.51')
        self.assertEqual(data['minimum_turn_value'], '0.00')
        self.assertTrue(data['walk_in_customer_warning'])
        self.assertFalse(data['show_revenue'])

    def test_update_minimum_turn_validation_ok(self):
        self.url = reverse('turntracker_settings', args=(self.business.id,))
        response = self.client.put(
            self.url,
            **self.headers,
            data={
                'minimum_turn_value': 10,
            },
        )
        data = response.data
        self.assertEqual(data['minimum_turn_value'], '10.00')

    def test_update_turn_values_validation(self):
        with self.assertRaisesMessage(ServiceError, 'Turn value cannot be negative.'):
            serializer = TurntrackerSettingsSerializer(
                data={
                    'full_turn_value': 75,
                    'minimum_turn_value': -1,
                }
            )
            serializer.is_valid(raise_exception=True)

        with self.assertRaisesMessage(ServiceError, 'The minimum possible turn value to set is 5.'):
            serializer = TurntrackerSettingsSerializer(
                data={
                    'full_turn_value': 10,
                    'minimum_turn_value': 3,
                }
            )
            serializer.is_valid(raise_exception=True)

        with self.assertRaisesMessage(
            ServiceError, 'Full turn value must be greater than minimum turn value.'
        ):
            serializer = TurntrackerSettingsSerializer(
                data={
                    'full_turn_value': 10,
                    'minimum_turn_value': 20,
                }
            )
            serializer.is_valid(raise_exception=True)

        with self.assertRaisesMessage(ServiceError, 'A valid number is required.'):
            serializer = TurntrackerSettingsSerializer(
                data={
                    'full_turn_value': 50,
                    'minimum_turn_value': "",
                }
            )
            serializer.is_valid(raise_exception=True)

    def test_update_other_business_turntracker_settings(self):
        tracker_settings = baker.make(TrackerSettings, business_id=115325)
        self.url = reverse('turntracker_settings', args=(tracker_settings.business_id,))
        response = self.client.put(
            self.url,
            **self.headers,
            data={
                'enabled': True,
            },
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


class TestSettingsPermissionLevel(TrackerSettingsBaseSetUp):
    def put_enabled_true(self):
        self.url = reverse('turntracker_settings', args=(self.business.id,))
        response = self.client.put(
            self.url,
            **self.headers,
            data={
                'enabled': True,
            },
        )
        return response

    def test_200_retrieve_reception_ok(self):
        self._set_business_access_level(Resource.STAFF_ACCESS_LEVEL_RECEPTION)
        self.url = reverse('turntracker_settings', args=(self.business.id,))
        response = self.client.get(self.url, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_200_retrieve_staff_ok(self):
        self._set_business_access_level(Resource.STAFF_ACCESS_LEVEL_RECEPTION)
        self.url = reverse('turntracker_settings', args=(self.business.id,))
        response = self.client.get(self.url, **self.headers)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_200_update_manager_ok(self):
        self._set_business_access_level(Resource.STAFF_ACCESS_LEVEL_MANAGER)
        response = self.put_enabled_true()
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_404_update_reception_permission(self):
        self._set_business_access_level(Resource.STAFF_ACCESS_LEVEL_RECEPTION)
        self.url = reverse('turntracker_settings', args=(self.business.id,))
        response = self.put_enabled_true()
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_404_update_staff_permission(self):
        self._set_business_access_level(Resource.STAFF_ACCESS_LEVEL_STAFF)
        self.url = reverse('turntracker_settings', args=(self.business.id,))
        response = self.put_enabled_true()
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
