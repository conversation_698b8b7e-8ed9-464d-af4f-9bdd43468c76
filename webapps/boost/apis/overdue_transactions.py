import datetime
from dataclasses import dataclass
from decimal import Decimal

from dataclasses_json import DataClassJsonMixin
from django.conf import settings
from django.db.models import Prefetch

from lib.locks import BoostSetPaidLock, RedlockError
from webapps.billing.apis.entities import BillingBulkPaymentResponse
from webapps.booking.models import Appointment
from webapps.boost.apis.helpers import create_new_transaction_with_paid_boost_appointments
from webapps.boost.events import marketplace_transaction_successfully_paid
from webapps.boost.models import BoostAppointment
from webapps.boost.payment.utils import get_payment_provider
from webapps.marketplace.models import MarketplaceTransaction, MarketplaceTransactionStatus


@dataclass(frozen=True)
class BoostAppointmentDataClass(DataClassJsonMixin):
    public_id: str  # -> e.g. boost_appointment:1234
    business_id: int
    gross_amount: Decimal
    client: str
    date: datetime.datetime

    @classmethod
    def create_from_boost_appointment(cls, boost_appointment):
        return cls(
            public_id=f'boost_appointment:{boost_appointment.id}',
            business_id=boost_appointment.appointment.business_id,
            gross_amount=boost_appointment.gross_amount,
            client=boost_appointment.appointment.customer_full_name,
            date=boost_appointment.appointment.booked_from,
        )


@dataclass(frozen=True)
class BoostTransaction(DataClassJsonMixin):
    public_id: str  # -> e.g. marketplace_transaction:12345
    charge_lock_key: str | int  # -> e.g. 12345
    business_id: int
    gross_amount: Decimal
    appointments: list[BoostAppointmentDataClass]
    description: str | None
    created: datetime.datetime
    currency: str = settings.CURRENCY_CODE

    @classmethod
    def create_from_boost_transaction(cls, boost_transaction, boost_appointments):
        return cls(
            public_id=boost_transaction.public_id,
            charge_lock_key=boost_transaction.id,
            business_id=boost_transaction.business_id,
            gross_amount=boost_transaction.total,
            appointments=boost_appointments,
            description=None,
            created=boost_transaction.created,
        )


@dataclass(frozen=True)
class BoostOverdueInfo(DataClassJsonMixin):
    business_id: int
    gross_amount: Decimal
    currency: str = settings.CURRENCY_CODE


# pylint: disable=unused-argument
class BoostOverdueTransactionAPI:
    @staticmethod
    def get_transactions(
        *,
        business_id: int,
    ) -> list[BoostTransaction]:
        unpaid_transactions = MarketplaceTransaction.objects.boost_debts(
            business_id=business_id
        ).prefetch_related(
            Prefetch(
                'boost_appointments',
                queryset=BoostAppointment.objects.payable_boost_appointments().prefetch_related(
                    Prefetch(
                        'appointment', queryset=Appointment.objects.annotate_customer_full_name()
                    )
                ),
                to_attr='payable_ba',
            )
        )
        boost_transactions_list = []
        for boost_transaction in unpaid_transactions:
            boost_appointments = [
                BoostAppointmentDataClass.create_from_boost_appointment(boost_appointment)
                for boost_appointment in boost_transaction.payable_ba
            ]
            if boost_appointments:
                boost_transactions_list.append(
                    BoostTransaction.create_from_boost_transaction(
                        boost_transaction, boost_appointments
                    )
                )

        return boost_transactions_list

    @staticmethod
    def get_overdue_info(*, business_id: int) -> BoostOverdueInfo:
        all_debts_amount = MarketplaceTransaction.objects.all_debts_value(business_id)

        return BoostOverdueInfo(business_id=business_id, gross_amount=all_debts_amount)

    @staticmethod
    def set_as_paid(
        *,
        business_id: int,
        transactions: list[BoostTransaction],
        payment_response: BillingBulkPaymentResponse,
    ) -> bool | None:
        try:
            lock = BoostSetPaidLock.lock(business_id)
        except RedlockError:
            lock = None

        if not lock:
            return False

        if MarketplaceTransactionStatus.objects.filter(
            external_id=payment_response.payment_id
        ).exists():
            return False

        from webapps.boost.payment.stripe import PaymentStripe

        payment_source = payment_response.payment_processor
        payment_processor = get_payment_provider(payment_source)

        if payment_processor == PaymentStripe:
            total_amount = payment_response.payment_processor_response.payment_intent.decimal_amount

            # pylint: disable=unexpected-keyword-arg, disable=no-value-for-parameter
            _, transaction_response = payment_processor.parse_transaction_result(
                result=payment_response.payment_processor_response.payment_intent,
                amount=total_amount,
                error=[],
                is_success=True,
            )
        else:
            _, transaction_response = payment_processor.parse_transaction_result(
                transaction_result=payment_response.payment_processor_response, errors=[]
            )

        all_boost_appointments_ids = [
            int(ba_dataclass.public_id.split(':')[1])
            for mt_transaction in transactions
            for ba_dataclass in mt_transaction.appointments
        ]

        create_new_transaction_with_paid_boost_appointments(
            business_id, payment_source, all_boost_appointments_ids, transaction_response
        )

        marketplace_transaction_successfully_paid.send(
            BoostOverdueTransactionAPI.__name__,
            business_id=business_id,
        )

        BoostSetPaidLock.try_to_unlock(lock)
        return True
