import braintree
import pytest
from model_bakery import baker

from webapps.boost.enums import (
    MarketplaceTransactionType,
    MarketplaceTransactionStatusEnum,
    MarketplaceTransactionStage,
)
from webapps.boost.models import BoostAppointment
from webapps.boost.tests.utils import create_boost_finished_visit
from webapps.marketplace.models import MarketplaceTransactionStatus
from webapps.marketplace.tests import get_dummy_transaction_response


@pytest.mark.django_db
@pytest.mark.parametrize(
    'transaction_type, transaction_status, expected_transaction_stage',
    [
        (
            MarketplaceTransactionType.REFUND,
            MarketplaceTransactionStatusEnum.SUCCEEDED,
            MarketplaceTransactionStage.REFUNDED,
        ),
        (
            MarketplaceTransactionType.CHARGE,
            MarketplaceTransactionStatusEnum.SUCCEEDED,
            MarketplaceTransactionStage.CHARGED,
        ),
        (
            MarketplaceTransactionType.CHARGE,
            MarketplaceTransactionStatusEnum.AUTHORIZATION_EXPIRED,
            MarketplaceTransactionStage.NOT_CHARGED,
        ),
        (
            MarketplaceTransactionType.REFUND,
            MarketplaceTransactionStatusEnum.VOIDED,
            MarketplaceTransactionStage.REFUNDED,
        ),
        (
            MarketplaceTransactionType.REFUND,
            MarketplaceTransactionStatusEnum.PROCESSING,
            MarketplaceTransactionStage.REFUNDED,
        ),
    ],
)
def test_annotate_transaction_stage(
    transaction_type, transaction_status, expected_transaction_stage
):
    appointment = create_boost_finished_visit()
    transaction_response = get_dummy_transaction_response(
        is_success=True,
        status=transaction_status,
    )
    appointment.boost_appointment.transaction.set_status(
        transaction_response,
        [],
        [appointment.boost_appointment],
        transaction_type,
        are_these_boost_appointments=True,
    )

    result = BoostAppointment.objects.annotate_transaction_stage().first()
    assert result.transaction_stage == expected_transaction_stage


@pytest.mark.django_db
@pytest.mark.parametrize(
    'statuses, expected_transaction_stage',
    [
        (
            [
                (
                    MarketplaceTransactionType.CHARGE,
                    MarketplaceTransactionStatusEnum.SUBMITTED_FOR_SETTLEMENT,
                ),
                (MarketplaceTransactionType.CHARGE, MarketplaceTransactionStatusEnum.SETTLING),
                (MarketplaceTransactionType.CHARGE, MarketplaceTransactionStatusEnum.SETTLED),
            ],
            MarketplaceTransactionStage.CHARGED,
        ),
        (
            [
                (MarketplaceTransactionType.CHARGE, MarketplaceTransactionStatusEnum.SUCCEEDED),
                (MarketplaceTransactionType.REFUND, MarketplaceTransactionStatusEnum.SUCCEEDED),
            ],
            MarketplaceTransactionStage.REFUNDED,
        ),
        (
            [
                (MarketplaceTransactionType.CHARGE, MarketplaceTransactionStatusEnum.SUCCEEDED),
                (MarketplaceTransactionType.REFUND, MarketplaceTransactionStatusEnum.SUCCEEDED),
                (MarketplaceTransactionType.CHARGE, MarketplaceTransactionStatusEnum.SUCCEEDED),
            ],
            MarketplaceTransactionStage.CHARGED,
        ),
        (
            [
                (
                    MarketplaceTransactionType.CHARGE,
                    MarketplaceTransactionStatusEnum.PROCESSOR_DECLINED,
                ),
                (
                    MarketplaceTransactionType.CHARGE,
                    MarketplaceTransactionStatusEnum.PROCESSOR_DECLINED,
                ),
                (
                    MarketplaceTransactionType.CHARGE,
                    MarketplaceTransactionStatusEnum.PROCESSOR_DECLINED,
                ),
            ],
            MarketplaceTransactionStage.NOT_CHARGED,
        ),
        (
            [
                (MarketplaceTransactionType.CHARGE, MarketplaceTransactionStatusEnum.SUCCEEDED),
                (MarketplaceTransactionType.REFUND, MarketplaceTransactionStatusEnum.SUCCEEDED),
                (MarketplaceTransactionType.CHARGE, MarketplaceTransactionStatusEnum.FAILED),
            ],
            MarketplaceTransactionStage.REFUNDED,
        ),
        (
            [
                (
                    MarketplaceTransactionType.CHARGE,
                    MarketplaceTransactionStatusEnum.SUBMITTED_FOR_SETTLEMENT,
                ),
                (MarketplaceTransactionType.REFUND, MarketplaceTransactionStatusEnum.VOIDED),
            ],
            MarketplaceTransactionStage.REFUNDED,
        ),
    ],
)
def test_annotate_transaction_stage_for_multiple_statuses(statuses, expected_transaction_stage):
    appointment = create_boost_finished_visit()
    for status in statuses:
        transaction_type, transaction_status = status
        transaction_response = get_dummy_transaction_response(
            is_success=True,
            status=transaction_status,
        )
        appointment.boost_appointment.transaction.set_status(
            transaction_response,
            [],
            [appointment.boost_appointment],
            transaction_type,
            are_these_boost_appointments=True,
        )

    result = BoostAppointment.objects.annotate_transaction_stage().first()
    assert result.transaction_stage == expected_transaction_stage


@pytest.mark.django_db
@pytest.mark.parametrize(
    'transaction_type, status, expected',
    [
        (
            MarketplaceTransactionType.CHARGE,
            braintree.Transaction.Status.Settled,
            True,
        ),
        (
            MarketplaceTransactionType.REFUND,
            braintree.Transaction.Status.Settled,
            False,
        ),
        (
            MarketplaceTransactionType.CHARGE,
            braintree.Transaction.Status.Authorized,
            False,
        ),
    ],
)
def test_annotate_has_ever_been_charged(transaction_type, status, expected):
    appointment = create_boost_finished_visit()
    baker.make(
        MarketplaceTransactionStatus,
        transaction=appointment.boost_appointment.transaction,
        type=transaction_type,
        status=status,
        boost_appointments=[appointment.boost_appointment],
    )
    result = BoostAppointment.objects.annotate_has_ever_been_charged().get(
        pk=appointment.boost_appointment.id
    )
    assert result.has_ever_been_charged == expected
