from django.utils.translation import gettext_noop

from lib.enums import StrEnum


class VolumeMeasureEnum(StrEnum):
    # ISO system
    GRAM = gettext_noop('gram')
    KILOGRAM = gettext_noop('kilogram')
    LITER = gettext_noop('liter')
    MILLILITER = gettext_noop('milliliter')

    # Imperial system - volume
    FLUID_OUNCE = gettext_noop('fluid ounce')
    GILL = gettext_noop('gill')
    CUP = gettext_noop('cup')
    PINT = gettext_noop('pint')
    QUART = gettext_noop('quart')
    GALLON = gettext_noop('gallon')

    # Imperial system - mass
    GRAIN = gettext_noop('grain')
    DRAM = gettext_noop('dram')
    OUNCE = gettext_noop('ounce')
    POUND = gettext_noop('pound')

    # Other
    PIECE = gettext_noop('piece')
    AMPOULE = gettext_noop('ampoule')
    PACKAGE = gettext_noop('package')


STANDARD_VOLUME_MEASURES = [
    # ISO system
    {
        'label': VolumeMeasureEnum.GRAM,
        'symbol': 'g',
        'standard': True,
    },
    {
        'label': VolumeMeasureEnum.KILOGRAM,
        'symbol': 'kg',
        'standard': True,
    },
    {
        'label': VolumeMeasureEnum.LITER,
        'symbol': 'l',
        'standard': True,
    },
    {
        'label': VolumeMeasureEnum.MILLILITER,
        'symbol': 'ml',
        'standard': True,
    },
    # Imperial system - volume
    {
        'label': VolumeMeasureEnum.FLUID_OUNCE,
        'symbol': 'fl oz',
        'standard': True,
    },
    {
        'label': VolumeMeasureEnum.GILL,
        'symbol': 'gill',
        'standard': True,
    },
    {
        'label': VolumeMeasureEnum.CUP,
        'symbol': 'cup',
        'standard': True,
    },
    {
        'label': VolumeMeasureEnum.PINT,
        'symbol': 'pint',
        'standard': True,
    },
    {
        'label': VolumeMeasureEnum.QUART,
        'symbol': 'qt',
        'standard': True,
    },
    {
        'label': VolumeMeasureEnum.GALLON,
        'symbol': 'gal',
        'standard': True,
    },
    # Imperial system - mass
    {
        'label': VolumeMeasureEnum.GRAIN,
        'symbol': 'gr',
        'standard': True,
    },
    {
        'label': VolumeMeasureEnum.DRAM,
        'symbol': 'dr',
        'standard': True,
    },
    {
        'label': VolumeMeasureEnum.OUNCE,
        'symbol': 'oz',
        'standard': True,
    },
    {
        'label': VolumeMeasureEnum.POUND,
        'symbol': 'lb',
        'standard': True,
    },
    # Other
    {
        'label': VolumeMeasureEnum.PIECE,
        'symbol': gettext_noop('pcs.'),
        'standard': True,
    },
    {
        'label': VolumeMeasureEnum.AMPOULE,
        'symbol': gettext_noop('amp.'),
        'standard': True,
    },
    {
        'label': VolumeMeasureEnum.PACKAGE,
        'symbol': gettext_noop('pkg.'),
        'standard': True,
    },
]
