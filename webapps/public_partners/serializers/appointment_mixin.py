from contextlib import contextmanager

from rest_framework import serializers

from lib.feature_flag.feature.public_api import PublicAPIAppointmentChangedByUserFlag
from lib.fields.phone_number import Booksy<PERSON><PERSON><PERSON>erial<PERSON><PERSON>ield
from webapps.booking.enums import WhoMakesChange
from webapps.booking.exceptions import BookingConflict
from webapps.booking.models import BookingChange
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.kill_switch.models import KillSwitch
from webapps.public_partners.exceptions import UnprocessableEntityException
from webapps.public_partners.fields import BusinessCustomerInfoField


class PAAppointmentHistoryMixin:
    CHANGED_BY = BookingChange.BY_SYSTEM
    HISTORY_CREATOR = {
        BookingChange.BY_SYSTEM: 'system',
        BookingChange.BY_BUSINESS: 'business',
        BookingChange.BY_CUSTOMER: 'customer',
    }

    @contextmanager
    def save_change_history(self, changed_fields=None, reason=None):
        if self.instance and KillSwitch.alive(KillSwitch.System.APPOINTMENT_HISTORY):
            instance_before = self.instance.extract_state(changed_fields)
        else:
            instance_before = None

        yield

        if KillSwitch.alive(KillSwitch.System.APPOINTMENT_HISTORY):
            instance_after = self.instance.extract_state(changed_fields)
            self.instance.history.create(instance_before, instance_after)

        changed_by_user = None
        if PublicAPIAppointmentChangedByUserFlag():
            changed_by_user = self.instance.updated_by

        BookingChange.add(
            self.instance,
            changed_by=self.CHANGED_BY,
            changed_user=changed_by_user,
            metadata={
                'partner': (self.context.get('partner') and self.context['partner'].name),
                'user_id': (self.context.get('user') and self.context['user'].pk),
                'endpoint': 'Public API Business',
                'reason': (
                    f'public_api:appointment:'
                    f'{self.HISTORY_CREATOR.get(self.CHANGED_BY, "system")}_{reason or "unknown"}'
                ),
            },
        )


class PAAppointmentStatusMixin:
    UPDATE_STATUS_FIELDS = ['status']

    def _update_status(self, data=None):
        data = {
            k: v for k, v in (data or self.validated_data).items() if k in self.UPDATE_STATUS_FIELDS
        }
        try:
            self.instance.update_appointment(
                updated_by_id=self.context['user'].pk,
                status=data.pop('status'),
                who_makes_change=self.WHO_MAKES_CHANGE,
                **data,
            )
        except BookingConflict as e:
            raise UnprocessableEntityException(detail=e.message) from e


class PABusinessAppointmentMixin(metaclass=serializers.SerializerMetaclass):
    WHO_MAKES_CHANGE = WhoMakesChange.BUSINESS
    CHANGED_BY = BookingChange.BY_BUSINESS

    customer_note = serializers.CharField(read_only=True, required=False)


class PACustomerAppointmentMixin(metaclass=serializers.SerializerMetaclass):
    WHO_MAKES_CHANGE = WhoMakesChange.CUSTOMER
    CHANGED_BY = BookingChange.BY_CUSTOMER

    booked_for_id = BusinessCustomerInfoField(
        required=True,
        source='booked_for',
        queryset=BusinessCustomerInfo.objects.filter(deleted__isnull=True),
    )
    customer_name = serializers.CharField(required=False, read_only=True)
    customer_phone = BooksyPhoneSerializerField(required=False, read_only=True)
    customer_email = serializers.EmailField(required=False, read_only=True)
    business_note = serializers.CharField(required=False, read_only=True)
