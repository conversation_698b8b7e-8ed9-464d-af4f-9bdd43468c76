from model_bakery import baker
from rest_framework import status

from lib.feature_flag.feature.public_api import PublicAPIUserUpdatesFixes
from lib.test_utils import compare_expected_fields
from lib.tests.utils import override_eppo_feature_flag
from webapps.business.enums import ResourceType, StaffAccessLevels
from webapps.business.models import Resource
from webapps.notification.models import Reciever, UserNotification
from webapps.public_partners.tests import PublicApiBaseTestCase
from webapps.user.models import User, UserProfile
from webapps.user.tools import get_system_user


class UserViewSetTestCase:
    def _setup_business_with_partner(self, owner=None):
        self.owner = owner or baker.make_recipe(
            'webapps.user.business_user',
            username='public_api_user_placeholder',
        )
        self.business = baker.make_recipe('webapps.business.business_recipe', owner=self.owner)
        self.partner.add_business(self.business)

    def get_url(self, business_id=None):
        return self._url.format(business_id or self.business.id)

    def get_details_url(self, user_id=None, business_id=None):
        return self._details_url.format(business_id or self.business.id, user_id or self.owner.id)

    def test_check_not_allowed_methods(self):
        self.check_not_allowed_methods(self.get_url(), {'post', 'put'})

    def test_create_user_with_no_data(self):
        resp = self.post(self.get_url())

        assert resp.status_code == status.HTTP_400_BAD_REQUEST
        assert resp.json() == dict(
            first_name=['This field is required.'],
            last_name=['This field is required.'],
            email=['This field is required.'],
        )

    def test_create_user_with_incorrect_email(self):
        resp = self.post(
            self.get_url(),
            dict(
                first_name='Staszczyk',
                last_name='Muniek',
                email='user@example',
            ),
        )
        assert resp.status_code == status.HTTP_400_BAD_REQUEST
        body = resp.json()
        assert body['email'] == ['Enter a valid email address.']

    def test_create_user_with_incorrect_data(self):
        resp = self.post(
            self.get_url(),
            dict(
                first_name='User',
                last_name=None,
                email='<EMAIL>',
                language='pl',
            ),
        )
        assert resp.status_code == status.HTTP_400_BAD_REQUEST
        body = resp.json()
        assert body['last_name'] == ['This field may not be null.']

    def test_create_user_with_incorrect_language(self):
        resp = self.post(
            self.get_url(),
            dict(
                first_name='Muniek',
                last_name='Staszczyk',
                email='<EMAIL>',
                language='zoo',
            ),
        )
        assert resp.status_code == status.HTTP_400_BAD_REQUEST
        body = resp.json()
        assert body['language'] == ['"zoo" is not a valid choice.']

    def test_create_user_with_taken_email_the_same_business(self):
        resp = self.post(
            self.get_url(),
            dict(
                first_name='Muniek',
                last_name='Staszczyk',
                email='<EMAIL>',
                language='pl',
            ),
        )
        created_staffer_id = resp.data['staff_user_id']
        assert resp.status_code == status.HTTP_201_CREATED

        resp = self.post(
            self.get_url(),
            dict(
                first_name='Muniek',
                last_name='Staszczyk',
                email='  <EMAIL> ',
                language='pl',
            ),
        )
        assert resp.status_code == status.HTTP_200_OK

        updated_staffer_id = resp.data['staff_user_id']
        assert created_staffer_id == updated_staffer_id

    def test_create_user_with_taken_email_different_businesses(self):
        duplicated_email = '<EMAIL>'
        old_owner = baker.make_recipe('webapps.user.business_user', email=duplicated_email)
        old_staffer = baker.make_recipe('webapps.business.staffer_recipe', staff_user=old_owner)
        _old_business = baker.make_recipe('webapps.business.business_recipe', owner=old_owner)
        staffer_in_old_business_id = old_staffer.id

        resp = self.post(
            self.get_url(business_id=self.business.id),
            dict(
                first_name='Muniek',
                last_name='Staszczyk',
                email=duplicated_email,
                language='pl',
            ),
        )
        staffer_in_new_business_id = resp.data['staff_user_id']

        assert resp.status_code == status.HTTP_200_OK
        assert staffer_in_old_business_id != staffer_in_new_business_id

    def test_create_user_with_correct_data(self):
        resp = self.post(
            self.get_url(),
            dict(
                first_name='Muniek',
                last_name='Staszczyk',
                email='<EMAIL>',
                language='pl',
            ),
        )
        user = User.objects.get(pk=resp.data['id'])
        user_staffer = user.staffers.first()
        expected_fields = (
            'id',
            'first_name',
            'last_name',
            'email',
            'language',
            'staff_user_id',
        )

        assert resp.status_code == status.HTTP_201_CREATED
        compare_expected_fields(resp.data.keys(), expected_fields)
        assert resp.data == dict(
            id=user.id,
            first_name='Muniek',
            last_name='Staszczyk',
            email='<EMAIL>',
            language='pl',
            staff_user_id=user_staffer.id,
        )
        assert user.profiles.count() == 1
        assert user.profiles.first().language == 'pl'
        assert user.staffers.count() == 1
        assert user_staffer.business.id == self.business.id
        assert user_staffer.type == 'S'
        assert not user.password == ''

        return {'resp': resp, 'user': user, 'user_staffer': user_staffer}


class UserViewSetOwnerTestCase(UserViewSetTestCase, PublicApiBaseTestCase):
    _url = '/public-api/us/business/{}/user/owner/'
    _details_url = '/public-api/us/business/{}/user/owner/{}/'
    _VERSION = '0.3'

    def setUp(self):
        super().setUp()
        self._setup_business_with_partner()

    def test_check_not_allowed_methods(self):
        self.check_not_allowed_methods(self.get_url(), {'post', 'put'})

    def test_create_user_with_correct_data(self):
        test_result = super().test_create_user_with_correct_data()
        assert self.business.id in [b.id for b in test_result['user'].businesses.all()]
        assert test_result['user_staffer'].staff_access_level == 'owner'

    def test_create_owner_for_business_which_has_placeholder_owner(self):
        self.business.owner = get_system_user(username='public_api_user_1234')
        self.business.save()
        resp = self.post(
            self.get_url(),
            dict(
                first_name='Muniek',
                last_name='Staszczyk',
                email='<EMAIL>',
                language='pl',
            ),
        )
        assert status.HTTP_201_CREATED == resp.status_code

    def test_create_owner_for_business_which_has_owner_already(self):
        self.business.owner = get_system_user()
        self.business.save()
        resp = self.post(
            self.get_url(),
            dict(
                first_name='Muniek',
                last_name='Staszczyk',
                email='<EMAIL>',
                language='pl',
            ),
        )
        assert resp.status_code == status.HTTP_400_BAD_REQUEST
        assert resp.data['non_field_errors'][0].code == 'business_ownership_taken'

    def test_update_owner_as_oauth2_partner(self):
        owner = self.business.owner
        oauth2_app = baker.make_recipe('webapps.public_partners.oauth2_application_recipe')
        oauth2_access_token = baker.make_recipe(
            'webapps.public_partners.oauth2_access_token_recipe',
            application=oauth2_app,
            user=owner,
            scope='business:write',
        )
        baker.make_recipe(
            'webapps.public_partners.oauth2_installation_recipe',
            application=oauth2_app,
            business=self.business,
            user=owner,
        )
        baker.make_recipe(
            'webapps.business.staffer_recipe',
            business=self.business,
            staff_user=owner,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_OWNER,
        )
        self._authenticate_token(oauth2_access_token.token)

        resp = self.put(
            self.get_url(),
            dict(
                first_name='Muniek',
                last_name='Staszczyk',
                email='<EMAIL>',
                language='pl',
            ),
            version='0.4',
        )
        assert status.HTTP_405_METHOD_NOT_ALLOWED == resp.status_code


class UserViewSetOwnerAsMergerPartnerTestCase(UserViewSetTestCase, PublicApiBaseTestCase):
    _url = '/public-api/us/business/{}/user/owner/'
    _details_url = '/public-api/us/business/{}/user/owner/{}/'
    _VERSION = '0.3'

    def setUp(self):
        super().setUp()
        owner = baker.make_recipe(
            'webapps.user.business_user', first_name='Muniek', last_name='Staszczyk'
        )
        self._setup_business_with_partner(owner)

    def test_update_owner_old(self):
        old_owner_email = '<EMAIL>'
        old_owner_notification_email = '<EMAIL>'
        owner_notification_emails = [old_owner_email, old_owner_notification_email]
        new_owner_email = '<EMAIL>'
        self.business.owner = get_system_user()
        self.business.save()

        old_owner = self.business.owner
        old_owner = self._test_update_placeholder_owner(old_owner, old_owner_email)
        old_owner = self._test_update_regular_owner_with_old_email(old_owner, old_owner_email)
        self._test_update_regular_owner_with_new_email(
            new_owner_email, old_owner, owner_notification_emails
        )

    @override_eppo_feature_flag({PublicAPIUserUpdatesFixes.flag_name: True})
    def test_update_owner(self):
        old_owner_email = '<EMAIL>'
        old_owner_notification_email = '<EMAIL>'
        owner_notification_emails = [old_owner_email, old_owner_notification_email]
        new_owner_email = '<EMAIL>'
        self.business.owner = get_system_user()
        self.business.save()

        old_owner = self.business.owner
        old_owner = self._test_update_placeholder_owner(old_owner, old_owner_email)
        old_owner = self._test_update_regular_owner_with_old_email(old_owner, old_owner_email)
        old_owner = self._test_update_regular_owner_with_new_email(
            new_owner_email, old_owner, owner_notification_emails
        )
        self._test_update_owner_without_business_profile(new_owner_email, old_owner)

    def _test_update_placeholder_owner(self, old_owner, old_owner_email):
        resp = self.put(
            self.get_url(),
            dict(
                first_name='Muniek',
                last_name='Staszczyk',
                email=old_owner_email,
                language='pl',
            ),
        )
        assert status.HTTP_201_CREATED == resp.status_code
        self.business.refresh_from_db()
        new_owner = self.business.owner
        assert new_owner.id != old_owner.id
        assert 'Muniek' == new_owner.first_name
        owner_staffers = self.business.resources.filter(
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_OWNER
        )
        assert owner_staffers.count() == 1

        return new_owner

    def _test_update_regular_owner_with_old_email(self, old_owner, old_owner_email):
        resp = self.put(
            self.get_url(),
            dict(
                first_name='Muniek',
                last_name='Staszczyk',
                email=old_owner_email,
                language='pl',
            ),
        )
        assert status.HTTP_200_OK == resp.status_code
        self.business.refresh_from_db()
        new_owner = self.business.owner
        assert new_owner.id == old_owner.id
        assert 'Muniek' == new_owner.first_name
        owner_staffers = self.business.resources.filter(
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_OWNER
        )
        assert owner_staffers.count() == 1

        return new_owner

    def _test_update_regular_owner_with_new_email(
        self, new_owner_email, old_owner, owner_notification_emails
    ):
        self._configure_email_notifications(old_owner, owner_notification_emails)
        owner_profile = old_owner.profiles.get(profile_type=UserProfile.Type.BUSINESS)
        notifications = owner_profile.notifications
        assert notifications.count() == 1
        assert notifications.first().recievers.count() == len(owner_notification_emails)

        resp = self.put(
            self.get_url(),
            dict(
                first_name='Joe',
                last_name='Cocker',
                email=new_owner_email,
                language='pl',
            ),
        )
        assert status.HTTP_201_CREATED == resp.status_code
        self.business.refresh_from_db()
        new_owner = self.business.owner
        assert new_owner.id != old_owner.id
        assert 'Joe' == new_owner.first_name
        owner_staffers = self.business.resources.filter(
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_OWNER
        )
        assert owner_staffers.count() == 1
        assert notifications.first().recievers.count() == 0

        return new_owner

    def _test_update_owner_without_business_profile(self, new_owner_email, old_owner):
        owner_profile = old_owner.profiles.get(profile_type=UserProfile.Type.BUSINESS)
        owner_profile.delete()

        resp = self.put(
            self.get_url(),
            dict(
                first_name='Joe',
                last_name='Cocker',
                email=new_owner_email,
                language='pl',
            ),
        )
        assert status.HTTP_200_OK == resp.status_code
        self.business.refresh_from_db()
        new_owner = self.business.owner
        assert new_owner.id == old_owner.id
        assert 'Joe' == new_owner.first_name
        owner_staffers = self.business.resources.filter(
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_OWNER
        )
        assert owner_staffers.count() == 1

        return new_owner

    def _configure_email_notifications(self, owner, emails):
        receivers = [
            baker.make(Reciever, business=self.business, identifier=email) for email in emails
        ]
        owner_profile = owner.profiles.get(profile_type=UserProfile.Type.BUSINESS)

        email_notification = baker.make(UserNotification, type=UserNotification.EMAIL_NOTIFICATION)
        for receiver in receivers:
            email_notification.recievers.add(receiver)
        owner_profile.notifications.add(email_notification)

    @override_eppo_feature_flag({PublicAPIUserUpdatesFixes.flag_name: True})
    def test_update_owner_with_ideal_staffer_assignment(self):
        owner = self.business.owner

        self.assertEqual(owner.staffers.count(), 0)
        old_owner_staffer = baker.make(
            Resource,
            staff_user=owner,
            name='Marian',
            active=True,
            type=ResourceType.STAFF,
            staff_access_level=StaffAccessLevels.OWNER,
            business=self.business,
        )
        new_owner_staffer = baker.make(
            Resource,
            name='Muniek Staszczyk',
            active=True,
            type=ResourceType.STAFF,
            staff_access_level=StaffAccessLevels.STAFF,
            business=self.business,
        )
        self.assertEqual(old_owner_staffer.staff_user, owner)
        self.assertEqual(new_owner_staffer.staff_user, None)

        resp = self.put(
            self.get_url(),
            {
                'first_name': 'Muniek',
                'last_name': 'Staszczyk',
                'email': owner.email,
                'language': 'pl',
            },
        )
        self.assertEqual(resp.status_code, status.HTTP_200_OK)

        body = resp.json()
        old_owner_staffer.refresh_from_db()
        new_owner_staffer.refresh_from_db()

        self.assertEqual(old_owner_staffer.staff_user, None)
        self.assertEqual(new_owner_staffer.id, body['staff_user_id'])
        self.assertEqual(new_owner_staffer.staff_user, owner)
        self.assertEqual(new_owner_staffer.name, 'Muniek Staszczyk')
        self.assertEqual(new_owner_staffer.staff_access_level, 'owner')

    @override_eppo_feature_flag({PublicAPIUserUpdatesFixes.flag_name: True})
    def test_update_owner_with_current_staffer_update(self):
        owner = self.business.owner
        self.assertEqual(owner.staffers.count(), 0)

        current_owner_staffer = baker.make(
            Resource,
            staff_user=owner,
            name='Marian',
            active=True,
            type=ResourceType.STAFF,
            staff_access_level=StaffAccessLevels.OWNER,
            business=self.business,
        )
        other_staffer = baker.make(
            Resource,
            name='Muniek Staszczyk',
            active=False,
            type=ResourceType.STAFF,
            staff_access_level=StaffAccessLevels.STAFF,
            business=self.business,
        )
        self.assertEqual(current_owner_staffer.staff_user, owner)

        resp = self.put(
            self.get_url(),
            {
                'first_name': 'Muniek',
                'last_name': 'Staszczyk',
                'email': owner.email,
                'language': 'pl',
            },
        )
        self.assertEqual(resp.status_code, status.HTTP_200_OK)

        body = resp.json()
        current_owner_staffer.refresh_from_db()
        other_staffer.refresh_from_db()

        self.assertEqual(current_owner_staffer.id, body['staff_user_id'])
        self.assertEqual(current_owner_staffer.staff_user, owner)
        self.assertEqual(current_owner_staffer.name, 'Marian')
        self.assertEqual(other_staffer.staff_user, None)

    @override_eppo_feature_flag({PublicAPIUserUpdatesFixes.flag_name: True})
    def test_update_owner_with_no_staffer_and_matched_inactive_staffer_assignment(
        self,
    ):
        owner = self.business.owner
        self.assertEqual(owner.staffers.count(), 0)

        new_owner_staffer = baker.make(
            Resource,
            name='Muniek Staszczyk',
            active=False,
            type=ResourceType.STAFF,
            staff_access_level=StaffAccessLevels.STAFF,
            business=self.business,
        )
        self.assertEqual(new_owner_staffer.staff_user, None)

        resp = self.put(
            self.get_url(),
            {
                'first_name': 'Muniek',
                'last_name': 'Staszczyk',
                'email': owner.email,
                'language': 'pl',
            },
        )
        self.assertEqual(resp.status_code, status.HTTP_200_OK)

        body = resp.json()
        new_owner_staffer.refresh_from_db()

        self.assertEqual(new_owner_staffer.id, body['staff_user_id'])
        self.assertEqual(new_owner_staffer.staff_user, owner)
        self.assertEqual(new_owner_staffer.name, 'Muniek Staszczyk')

    @override_eppo_feature_flag({PublicAPIUserUpdatesFixes.flag_name: True})
    def test_update_owner_with_staffer_creation(self):
        owner = self.business.owner
        self.assertEqual(owner.staffers.count(), 0)

        resp = self.put(
            self.get_url(),
            {
                'first_name': 'Muniek',
                'last_name': 'Staszczyk',
                'email': owner.email,
                'language': 'pl',
            },
        )
        self.assertEqual(resp.status_code, status.HTTP_200_OK)

        body = resp.json()
        new_owner_resource = owner.staffers.get(
            active=True, staff_access_level=StaffAccessLevels.OWNER
        )

        self.assertEqual(new_owner_resource.id, body['staff_user_id'])
        self.assertEqual(new_owner_resource.staff_user, owner)
        self.assertEqual(new_owner_resource.name, 'Muniek Staszczyk')


class UserViewSetStafferTestCase(UserViewSetTestCase, PublicApiBaseTestCase):
    _url = '/public-api/us/business/{}/user/staffer/'
    _details_url = '/public-api/us/business/{}/user/staffer/{}/'
    _VERSION = '0.3'

    def setUp(self):
        super().setUp()
        self._setup_business_with_partner()

    def test_create_user_with_correct_data(self):
        test_result = super().test_create_user_with_correct_data()
        assert test_result['user_staffer'].staff_access_level == 'staff'
