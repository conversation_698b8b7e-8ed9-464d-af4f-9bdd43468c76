from model_bakery import baker
from rest_framework import status

from lib.test_utils import compare_expected_fields
from webapps.business.models import Resource
from webapps.public_partners.tests import PublicApiBaseTestCase
from webapps.schedule.models import Schedule


class BaseCustomBusinessScheduleBulkImportViewTestCase(PublicApiBaseTestCase):
    _VERSION = '0.4'
    _SCHEDULE_FIELDS = (
        'date',
        'hours',
    )


class CustomBusinessScheduleBulkImportViewTestCase(
    BaseCustomBusinessScheduleBulkImportViewTestCase
):
    _url = '/public-api/us/business/{}/schedule/custom/bulk_import/'

    def get_url(self, business_id=None):
        return self._url.format(business_id or self.business.id)

    def test_bulk_import_success(self):
        data = [
            dict(date='2042-02-01', hours=[{'hour_from': '08:00', 'hour_till': '11:00'}]),
            dict(
                date='2042-03-01',
                hours=[
                    {'hour_from': '08:00', 'hour_till': '12:00'},
                    {'hour_from': '14:00', 'hour_till': '18:00'},
                ],
            ),
            dict(date='2042-04-01', hours=[{'hour_from': '08:00', 'hour_till': '13:00'}]),
        ]
        resp = self.post(self.get_url(), data=data)
        body = resp.json()
        self.assertEqual(resp.status_code, status.HTTP_201_CREATED)
        self.assertEqual(
            Schedule.objects.filter(business_id=self.business.pk, resource_id__isnull=True).count(),
            3,
        )
        for idx, schedule in enumerate(body):
            compare_expected_fields(schedule.keys(), self._SCHEDULE_FIELDS)
            self.assertEqual(schedule['date'], data[idx]['date'])
            self.assertEqual(schedule['hours'], data[idx]['hours'])

    def test_bulk_import_failure_with_invalid_date(self):
        data = [
            dict(date='2042-02-01', hours=[{'hour_from': '08:00', 'hour_till': '11:00'}]),
            dict(date=None, hours=[{'hour_from': '08:00', 'hour_till': '12:00'}]),
            dict(date='2042-04-01', hours=[{'hour_from': '08:00', 'hour_till': '13:00'}]),
        ]
        resp = self.post(self.get_url(), data=data)
        body = resp.json()
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            Schedule.objects.filter(business_id=self.business.pk, resource_id__isnull=True).count(),
            0,
        )
        self.assertEqual(body, [{}, {'date': ['This field may not be null.']}, {}])

    def test_bulk_import_failure_with_invalid_hours(self):
        data = [
            dict(date='2042-02-01', hours=[{'hour_from': '08:00', 'hour_till': '11:00'}]),
            dict(date='2042-03-01', hours=[{'hour_from': '08:00', 'hour_till': '12:00'}]),
            dict(date='2042-04-01', hours=[{'hour_from': '08:00', 'hour_till': None}]),
        ]
        resp = self.post(self.get_url(), data=data)
        body = resp.json()
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            Schedule.objects.filter(business_id=self.business.pk, resource_id__isnull=True).count(),
            0,
        )
        self.assertEqual(
            body, [{}, {}, {'hours': [{'hour_till': ['This field may not be null.']}]}]
        )

        data = [
            dict(date='2042-02-01', hours=[{'hour_from': '08:00', 'hour_till': '11:00'}]),
            dict(date='2042-03-01', hours=[{'hour_from': '08:00', 'hour_till': '12:00'}]),
            dict(date='2042-04-01', hours=[{'hour_from': '08:00', 'hour_till': '25:33'}]),
        ]
        resp = self.post(self.get_url(), data=data)
        body = resp.json()
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            Schedule.objects.filter(business_id=self.business.pk, resource_id__isnull=True).count(),
            0,
        )
        error_msg = 'Time has wrong format. Use one of these formats instead: hh:mm[:ss[.uuuuuu]].'
        self.assertEqual(
            body,
            [
                {},
                {},
                {'hours': [{'hour_till': [error_msg]}]},
            ],
        )

        data = [
            dict(date='2042-02-01', hours=[{'hour_from': '08:00', 'hour_till': '11:00'}]),
            dict(date='2042-03-01', hours=[{'hour_from': '08:00', 'hour_till': '12:00'}]),
            dict(date='2042-04-01', hours=[{'hour_from': '12:00', 'hour_till': '08:00'}]),
        ]
        resp = self.post(self.get_url(), data=data)
        body = resp.json()
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            Schedule.objects.filter(business_id=self.business.pk, resource_id__isnull=True).count(),
            0,
        )
        self.assertEqual(
            body,
            [{}, {}, {'hours': [{'non_field_errors': ['Hour till should be after hour from']}]}],
        )

    def test_bulk_import_failure_with_overlapping_hours(self):
        data = [
            dict(date='2042-02-01', hours=[{'hour_from': '08:00', 'hour_till': '11:00'}]),
            dict(
                date='2042-03-01',
                hours=[
                    {'hour_from': '08:00', 'hour_till': '12:00'},
                    {'hour_from': '11:00', 'hour_till': '15:00'},
                ],
            ),
            dict(date='2042-04-01', hours=[{'hour_from': '08:00', 'hour_till': '13:00'}]),
        ]
        resp = self.post(self.get_url(), data=data)
        body = resp.json()
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            Schedule.objects.filter(business_id=self.business.pk, resource_id__isnull=True).count(),
            0,
        )
        self.assertEqual(
            body,
            [
                {},
                {
                    'hours': {
                        'non_field_errors': [
                            'Incorrect hours. Hours on the same day can not intersect.'
                        ]
                    }
                },
                {},
            ],
        )

    def test_bulk_import_failure_with_existing_date(self):
        baker.make(
            Schedule,
            business=self.business,
            date='2042-04-01',
            hours=[],
        )
        data = [
            dict(date='2042-02-01', hours=[{'hour_from': '08:00', 'hour_till': '11:00'}]),
            dict(date='2042-03-01', hours=[{'hour_from': '08:00', 'hour_till': '12:00'}]),
            dict(date='2042-04-01', hours=[{'hour_from': '08:00', 'hour_till': '13:00'}]),
        ]
        resp = self.post(self.get_url(), data=data)
        body = resp.json()
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            Schedule.objects.filter(business_id=self.business.pk, resource_id__isnull=True).count(),
            1,
        )
        self.assertEqual(
            body, [{}, {}, {'non_field_errors': ['The field date for business must be unique.']}]
        )

    def test_bulk_import_failure_with_duplicated_date(self):
        data = [
            dict(date='2042-02-01', hours=[{'hour_from': '08:00', 'hour_till': '11:00'}]),
            dict(date='2042-03-01', hours=[{'hour_from': '08:00', 'hour_till': '12:00'}]),
            dict(date='2042-03-01', hours=[{'hour_from': '08:00', 'hour_till': '13:00'}]),
        ]
        resp = self.post(self.get_url(), data=data)
        body = resp.json()
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            Schedule.objects.filter(business_id=self.business.pk, resource_id__isnull=True).count(),
            0,
        )
        self.assertEqual(body, ['Integrity error with given payload.'])


class CustomResourceScheduleBulkImportViewTestCase(
    BaseCustomBusinessScheduleBulkImportViewTestCase
):
    _url = '/public-api/us/business/{}/schedule/resource/{}/custom/bulk_import/'

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.staffer = baker.make(
            Resource,
            business_id=cls.business.id,
            type=Resource.STAFF,
        )

    def get_url(self, business_id=None, resource_id=None):
        return self._url.format(business_id or self.business.id, resource_id or self.staffer.id)

    def test_bulk_import_success(self):
        data = [
            dict(date='2042-02-01', hours=[{'hour_from': '08:00', 'hour_till': '11:00'}]),
            dict(
                date='2042-03-01',
                hours=[
                    {'hour_from': '08:00', 'hour_till': '12:00'},
                    {'hour_from': '14:00', 'hour_till': '18:00'},
                ],
            ),
            dict(date='2042-04-01', hours=[{'hour_from': '08:00', 'hour_till': '13:00'}]),
        ]
        resp = self.post(self.get_url(), data=data)
        body = resp.json()
        self.assertEqual(resp.status_code, status.HTTP_201_CREATED)
        self.assertEqual(
            Schedule.objects.filter(
                business_id=self.business.pk,
                resource_id=self.staffer.pk,
            ).count(),
            3,
        )
        for idx, schedule in enumerate(body):
            compare_expected_fields(schedule.keys(), self._SCHEDULE_FIELDS)
            self.assertEqual(schedule['date'], data[idx]['date'])
            self.assertEqual(schedule['hours'], data[idx]['hours'])

    def test_bulk_import_failure_with_invalid_date(self):
        data = [
            dict(date='2042-02-01', hours=[{'hour_from': '08:00', 'hour_till': '11:00'}]),
            dict(date=None, hours=[{'hour_from': '08:00', 'hour_till': '12:00'}]),
            dict(date='2042-04-01', hours=[{'hour_from': '08:00', 'hour_till': '13:00'}]),
        ]
        resp = self.post(self.get_url(), data=data)
        body = resp.json()
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            Schedule.objects.filter(
                business_id=self.business.pk,
                resource_id=self.staffer.pk,
            ).count(),
            0,
        )
        self.assertEqual(body, [{}, {'date': ['This field may not be null.']}, {}])

    def test_bulk_import_failure_with_invalid_hours(self):
        data = [
            dict(date='2042-02-01', hours=[{'hour_from': '08:00', 'hour_till': '11:00'}]),
            dict(date='2042-03-01', hours=[{'hour_from': '08:00', 'hour_till': '12:00'}]),
            dict(date='2042-04-01', hours=[{'hour_from': '08:00', 'hour_till': None}]),
        ]
        resp = self.post(self.get_url(), data=data)
        body = resp.json()
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            Schedule.objects.filter(
                business_id=self.business.pk,
                resource_id=self.staffer.pk,
            ).count(),
            0,
        )
        self.assertEqual(
            body, [{}, {}, {'hours': [{'hour_till': ['This field may not be null.']}]}]
        )

        data = [
            dict(date='2042-02-01', hours=[{'hour_from': '08:00', 'hour_till': '11:00'}]),
            dict(date='2042-03-01', hours=[{'hour_from': '08:00', 'hour_till': '12:00'}]),
            dict(date='2042-04-01', hours=[{'hour_from': '08:00', 'hour_till': '25:33'}]),
        ]
        resp = self.post(self.get_url(), data=data)
        body = resp.json()
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            Schedule.objects.filter(
                business_id=self.business.pk,
                resource_id=self.staffer.pk,
            ).count(),
            0,
        )
        error_msg = 'Time has wrong format. Use one of these formats instead: hh:mm[:ss[.uuuuuu]].'
        self.assertEqual(
            body,
            [
                {},
                {},
                {'hours': [{'hour_till': [error_msg]}]},
            ],
        )

        data = [
            dict(date='2042-02-01', hours=[{'hour_from': '08:00', 'hour_till': '11:00'}]),
            dict(date='2042-03-01', hours=[{'hour_from': '08:00', 'hour_till': '12:00'}]),
            dict(date='2042-04-01', hours=[{'hour_from': '12:00', 'hour_till': '08:00'}]),
        ]
        resp = self.post(self.get_url(), data=data)
        body = resp.json()
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            Schedule.objects.filter(
                business_id=self.business.pk,
                resource_id=self.staffer.pk,
            ).count(),
            0,
        )
        self.assertEqual(
            body,
            [{}, {}, {'hours': [{'non_field_errors': ['Hour till should be after hour from']}]}],
        )

    def test_bulk_import_failure_with_overlapping_hours(self):
        data = [
            dict(date='2042-02-01', hours=[{'hour_from': '08:00', 'hour_till': '11:00'}]),
            dict(
                date='2042-03-01',
                hours=[
                    {'hour_from': '08:00', 'hour_till': '12:00'},
                    {'hour_from': '11:00', 'hour_till': '15:00'},
                ],
            ),
            dict(date='2042-04-01', hours=[{'hour_from': '08:00', 'hour_till': '13:00'}]),
        ]
        resp = self.post(self.get_url(), data=data)
        body = resp.json()
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            Schedule.objects.filter(
                business_id=self.business.pk,
                resource_id=self.staffer.pk,
            ).count(),
            0,
        )
        self.assertEqual(
            body,
            [
                {},
                {
                    'hours': {
                        'non_field_errors': [
                            'Incorrect hours. Hours on the same day can not intersect.'
                        ]
                    }
                },
                {},
            ],
        )

    def test_bulk_import_failure_with_existing_date(self):
        baker.make(
            Schedule,
            business=self.business,
            date='2042-02-01',
            hours=[],
        )
        baker.make(
            Schedule,
            business=self.business,
            resource=self.staffer_owner,
            date='2042-03-01',
            hours=[],
        )
        baker.make(
            Schedule,
            business=self.business,
            resource=self.staffer,
            date='2042-04-01',
            hours=[],
        )
        data = [
            dict(date='2042-02-01', hours=[{'hour_from': '08:00', 'hour_till': '11:00'}]),
            dict(date='2042-03-01', hours=[{'hour_from': '08:00', 'hour_till': '12:00'}]),
            dict(date='2042-04-01', hours=[{'hour_from': '08:00', 'hour_till': '13:00'}]),
        ]
        resp = self.post(self.get_url(), data=data)
        body = resp.json()
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            Schedule.objects.filter(
                business_id=self.business.pk,
                resource_id=self.staffer.pk,
            ).count(),
            1,
        )
        self.assertEqual(
            body,
            [
                {},
                {},
                {'non_field_errors': ['The field date for business and resource must be unique.']},
            ],
        )

    def test_bulk_import_failure_with_duplicated_date(self):
        data = [
            dict(date='2042-02-01', hours=[{'hour_from': '08:00', 'hour_till': '11:00'}]),
            dict(date='2042-03-01', hours=[{'hour_from': '08:00', 'hour_till': '12:00'}]),
            dict(date='2042-03-01', hours=[{'hour_from': '08:00', 'hour_till': '13:00'}]),
        ]
        resp = self.post(self.get_url(), data=data)
        body = resp.json()
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            Schedule.objects.filter(
                business_id=self.business.pk,
                resource_id=self.staffer.pk,
            ).count(),
            0,
        )
        self.assertEqual(body, ['Integrity error with given payload.'])
