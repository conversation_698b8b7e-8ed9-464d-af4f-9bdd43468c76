# pragma pylint: disable=line-too-long

from typing import Iterable
from unittest.mock import patch
from dateutil.relativedelta import relativedelta
from model_bakery import baker
from rest_framework import status

from lib.baker_utils import get_or_create_booking_source
from lib.rivers import River
from lib.test_utils import increase_appointment_next_id, compare_expected_fields
from webapps.booking.enums import AppointmentStatus
from webapps.booking.models import (
    Appointment,
    SubBooking,
    AppointmentTraveling,
    BookingResource,
    BookingSources,
)
from webapps.business.enums import ComboType, PriceType, StaffAccessLevels
from webapps.business.models import (
    Service,
    ServiceVariant,
    ComboMembership,
    Resource,
    TravelingToClients,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.public_partners.models.metadata import AppointmentMetadata, SubbookingMetadata
from webapps.public_partners.tests import (
    PublicApiBaseTestCase,
    ResourcePermissionTestCaseMixin,
    TokenScopePermissionTestCaseMixin,
)
from webapps.public_partners.tests.conftest import (
    assert_appointment_customer,
    assert_appointment,
    assert_appointment_subbooking,
)


class AppointmentBulkImportViewV04TestCase(
    TokenScopePermissionTestCaseMixin,
    ResourcePermissionTestCaseMixin,
    PublicApiBaseTestCase,
):  # pylint: disable=too-many-public-methods
    _url = '/public-api/us/business/{}/appointment/bulk_import/'
    _VERSION = '0.4'
    _FORBIDDEN_STAFF_ACCESS_LEVELS = [
        StaffAccessLevels.STAFF,
        StaffAccessLevels.ADVANCED,
        StaffAccessLevels.RECEPTION,
    ]
    _REQUIRED_TOKEN_SCOPES = ['business:write']
    _APPOINTMENT_FIELDS = {
        'id',
        'business_id',
        'business_timezone',
        'status',
        'status_changed',
        'type',
        'booked_from',
        'booked_till',
        'subbookings',
        'booked_for_id',
        'customer_name',
        'customer_phone',
        'customer_email',
        'customer_note',
        'business_note',
        'business_secret_note',
        'traveling',
        'metadata',
        'import_uid',
        'version',
        'created',
        'updated',
    }
    _SUBBOOKING_FIELDS = {
        'id',
        'booked_from',
        'booked_till',
        'service_variant_id',
        'service_id',
        'service_name',
        'staffer_id',
        'appliance_id',
        'combo_type',
        'combo_children',
        'metadata',
    }
    _SUBBOOKING_COMBO_FIELDS = {
        'id',
        'booked_from',
        'booked_till',
        'service_variant_id',
        'service_id',
        'service_name',
        'staffer_id',
        'appliance_id',
        'metadata',
    }

    @classmethod
    def setUpTestData(cls):  # pylint: disable=too-many-statements
        super().setUpTestData()
        cls.traveling_to_client = baker.make(
            TravelingToClients,
            business=cls.business,
            traveling_only=False,
            distance=20,
            price=10.0,
            price_type=PriceType.FIXED,
        )
        cls.appointment = baker.make(
            Appointment,
            business=cls.business,
        )
        cls.customer = baker.make(
            BusinessCustomerInfo,
            business=cls.business,
            first_name='Firstname',
            last_name='Lastname',
            email='<EMAIL>',
            cell_phone='+***********',
        )
        cls.customer2 = baker.make(
            BusinessCustomerInfo,
            business=cls.business,
            client_type=BusinessCustomerInfo.CLIENT_TYPE__INSTAGRAM,
            first_name='Firstname2',
            last_name='Lastname2',
            email='<EMAIL>',
            cell_phone='+***********',
        )
        cls.customer3 = baker.make(
            BusinessCustomerInfo,
            business=cls.business,
            first_name='Firstname2',
            last_name='Lastname2',
            email='<EMAIL>',
            first_appointment=cls.appointment,
        )
        cls.staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            business=cls.business,
        )
        cls.staffer2 = baker.make(
            Resource,
            type=Resource.STAFF,
            business=cls.business,
        )
        cls.appliance = baker.make(
            Resource,
            type=Resource.APPLIANCE,
            business=cls.business,
        )
        cls.appliance2 = baker.make(
            Resource,
            type=Resource.APPLIANCE,
            business=cls.business,
        )
        cls.inactive_staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            business=cls.business,
            active=False,
        )
        cls.inactive_appliance = baker.make(
            Resource,
            type=Resource.APPLIANCE,
            business=cls.business,
            active=False,
        )
        cls.service = baker.make(
            Service,
            business=cls.business,
            name='service_name',
        )
        cls.service_variant = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=15),
            service=cls.service,
        )
        cls.service_variant2 = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=25),
            service=cls.service,
        )
        cls.service_variant3 = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=15),
            gap_hole_duration=relativedelta(minutes=5),
            gap_hole_start_after=relativedelta(minutes=5),
            service=cls.service,
        )
        cls.service2 = baker.make(
            Service,
            business=cls.business,
            name='service2_name',
        )
        cls.service2_variant = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=15),
            service=cls.service2,
        )
        cls.mobile_service = baker.make(
            Service,
            business=cls.business,
            name='mobile_service',
            is_traveling_service=True,
        )
        cls.mobile_service_variant = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=15),
            service=cls.mobile_service,
        )
        cls.combo_service = baker.make(
            Service,
            business=cls.business,
            name='combo_service',
            combo_type=ComboType.SEQUENCE,
        )
        cls.combo_service_variant = baker.make(
            ServiceVariant,
            service=cls.combo_service,
        )
        cls.combo_membership1 = baker.make(
            ComboMembership,
            combo=cls.combo_service_variant,
            child=cls.service_variant,
            order=0,
        )
        cls.combo_membership2 = baker.make(
            ComboMembership,
            combo=cls.combo_service_variant,
            child=cls.service2_variant,
            order=1,
        )
        cls.parallel_service = baker.make(
            Service,
            business=cls.business,
            name='parallel_service',
            parallel_clients=2,
        )
        cls.parallel_service_variant = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=15),
            service=cls.parallel_service,
        )
        cls.appliance_service = baker.make(
            Service,
            business=cls.business,
            name='service_appliance',
        )
        cls.appliance_service_variant = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=15),
            service=cls.appliance_service,
        )
        cls.inactive_service = baker.make(
            Service,
            business=cls.business,
            name='inactive_service',
            active=False,
        )
        cls.inactive_service_variant = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=15),
            service=cls.inactive_service,
            active=False,
        )
        cls.parallel_combo_service = baker.make(
            Service,
            business=cls.business,
            name='parallel_combo_service',
            combo_type=ComboType.PARALLEL,
        )
        cls.parallel_combo_service_variant = baker.make(
            ServiceVariant,
            service=cls.parallel_combo_service,
        )
        cls.parallel_combo_membership1 = baker.make(
            ComboMembership,
            combo=cls.parallel_combo_service_variant,
            child=cls.service_variant2,
            order=0,
        )
        cls.parallel_combo_membership2 = baker.make(
            ComboMembership,
            combo=cls.parallel_combo_service_variant,
            child=cls.service2_variant,
            order=0,
        )

        cls.service.add_staffers([cls.staffer])
        cls.service2.add_staffers([cls.staffer])
        cls.service2.add_staffers([cls.staffer2])
        cls.service2.add_appliances([cls.appliance])
        cls.service2.add_appliances([cls.appliance2])
        cls.inactive_service.add_staffers([cls.staffer])
        cls.inactive_service.add_appliances([cls.appliance])
        cls.mobile_service.add_staffers([cls.staffer])
        cls.combo_service.add_staffers([cls.staffer, cls.staffer2])
        cls.combo_service.add_appliances([cls.appliance, cls.appliance2])
        cls.parallel_service.add_staffers([cls.staffer])
        cls.appliance_service.add_appliances([cls.appliance])
        cls.parallel_combo_service.add_staffers([cls.staffer, cls.staffer2])
        cls.parallel_combo_service.add_appliances([cls.appliance, cls.appliance2])

        cls.formatted_url = cls._url.format(cls.business.id)

    # pylint: disable=line-too-long
    def setUp(self):
        super().setUp()
        increase_appointment_next_id()
        self.traveling_coordinates_task_patcher = patch(
            'webapps.public_partners.services.bulk_import.appointment_bulk_import.add_coordinates_to_traveling_appointments_task'
        )
        self.traveling_coordinates_task_mock = self.traveling_coordinates_task_patcher.start()
        self.bump_document_patcher = patch(
            'webapps.public_partners.services.bulk_import.appointment_bulk_import.bump_document'
        )
        self.bump_document_mock = self.bump_document_patcher.start()

    def tearDown(self) -> None:
        self.traveling_coordinates_task_patcher.stop()
        self.bump_document_patcher.stop()
        super().tearDown()

    def assert_result_count(self):
        self.assertEqual(Appointment.objects.count(), 9)  # +one existing
        self.assertEqual(Appointment.objects.filter(booked_for_id__isnull=False).count(), 4)
        self.assertEqual(Appointment.objects.filter(booked_for_id__isnull=True).count(), 5)
        self.assertEqual(AppointmentTraveling.objects.count(), 2)
        self.assertEqual(SubBooking.objects.count(), 17)
        self.assertEqual(SubBooking.objects.filter(combo_parent_id__isnull=False).count(), 6)
        self.assertEqual(SubBooking.objects.filter(combo_parent_id__isnull=True).count(), 11)
        self.assertEqual(AppointmentMetadata.objects.count(), 3)
        self.assertEqual(SubbookingMetadata.objects.count(), 7)

    def assert_no_result_count(self):
        self.assertEqual(Appointment.objects.count(), 1)  # +one existing
        self.assertEqual(Appointment.objects.filter(booked_for_id__isnull=False).count(), 0)
        self.assertEqual(Appointment.objects.filter(booked_for_id__isnull=True).count(), 1)
        self.assertEqual(AppointmentTraveling.objects.count(), 0)
        self.assertEqual(SubBooking.objects.count(), 0)
        self.assertEqual(SubBooking.objects.filter(combo_parent_id__isnull=False).count(), 0)
        self.assertEqual(SubBooking.objects.filter(combo_parent_id__isnull=True).count(), 0)
        self.assertEqual(AppointmentMetadata.objects.count(), 0)
        self.assertEqual(SubbookingMetadata.objects.count(), 0)

    def assert_subbookings_count(self, appointment_id, count=0):
        self.assertEqual(SubBooking.objects.filter(appointment_id=appointment_id).count(), count)

    def assert_subbooking_resources_count(self, subbooking_id, count=0):
        self.assertEqual(BookingResource.objects.filter(subbooking_id=subbooking_id).count(), count)

    def assert_appointment_metadata_count(self, appointment_id, count=0):
        self.assertEqual(
            AppointmentMetadata.objects.filter(appointment_id=appointment_id).count(), count
        )

    def assert_subbooking_metadata_count(self, subbooking_id, count=0):
        self.assertEqual(
            SubbookingMetadata.objects.filter(subbooking_id=subbooking_id).count(), count
        )

    def assert_bumped_document(self, river, expected_ids):
        def normalize_ids(ids):
            return (
                [ids]
                if not isinstance(ids, Iterable) or isinstance(ids, str)
                else list(sorted(ids))
            )

        expected_ids = normalize_ids(expected_ids)
        for call in self.bump_document_mock.call_args_list:
            if call.args[0] == river and normalize_ids(call.args[1]) == expected_ids:
                return

        self.fail(f'Could not find call `bump_document({river!r}, {expected_ids})`')

    def appointments_body(self):
        body = [
            # Appointment #1 - past
            dict(
                subbookings=[
                    dict(
                        booked_from='2020-07-13T13:00',
                        staffer_id=self.staffer.pk,
                        service_variant_id=self.service_variant.pk,
                    )
                ],
                booked_for_id=self.customer.pk,
                metadata=dict(extra=dict(foo=1)),
                status=AppointmentStatus.FINISHED,
                business_secret_note='Secret note',
            ),
            # Appointment #2 - future
            dict(
                subbookings=[
                    dict(
                        booked_from='2037-07-13T13:00',
                        staffer_id=self.staffer.pk,
                        service_variant_id=self.service_variant.pk,
                    ),
                    dict(
                        booked_from='2037-07-13T13:15',
                        staffer_id=self.staffer.pk,
                        service_variant_id=self.service_variant2.pk,
                        metadata=dict(extra=dict(bar=1)),
                    ),
                ],
                booked_for_id=self.customer3.pk,
                status=AppointmentStatus.ACCEPTED,
            ),
            # Appointment #3 - mixed service-variant and service-name
            dict(
                subbookings=[
                    dict(
                        booked_from='2037-07-13T13:00',
                        staffer_id=self.staffer.pk,
                        service_variant_id=self.service_variant.pk,
                        metadata=dict(extra=dict(bar=1)),
                    ),
                    dict(
                        booked_from='2037-07-13T13:15',
                        booked_till='2037-07-13T14:00',
                        staffer_id=self.staffer.pk,
                        service_name='Custom service',
                        metadata=dict(extra=dict(bar=2)),
                    ),
                ],
                status=AppointmentStatus.ACCEPTED,
                # will be skipped
                traveling=dict(
                    address_line_1='Address 1',
                    city='City',
                    zipcode='12345',
                ),
                metadata=dict(extra=dict(foo=1)),
            ),
            # Appointment #4 - combo services
            dict(
                subbookings=[
                    dict(
                        booked_from='2037-07-13T13:00',
                        service_variant_id=self.combo_service_variant.pk,
                        combo_children=[
                            dict(
                                service_variant_id=self.combo_membership1.child_id,
                                staffer_id=self.staffer.pk,
                                metadata=dict(extra=dict(baz=1)),
                            ),
                            dict(
                                service_variant_id=self.combo_membership2.child_id,
                                staffer_id=self.staffer2.pk,
                                appliance_id=self.appliance.pk,
                                metadata=dict(extra=dict(baz=2)),
                            ),
                        ],
                        metadata=dict(extra=dict(bar=1)),
                    )
                ],
                booked_for_id=self.customer.pk,
                status=AppointmentStatus.ACCEPTED,
            ),
            # Appointment #5 - parallel combo services
            dict(
                subbookings=[
                    dict(
                        booked_from='2037-07-13T13:00',
                        service_variant_id=self.parallel_combo_service_variant.pk,
                        combo_children=[
                            dict(
                                service_variant_id=self.parallel_combo_membership1.child_id,
                                staffer_id=self.staffer.pk,
                            ),
                            dict(
                                service_variant_id=self.parallel_combo_membership2.child_id,
                                staffer_id=self.staffer2.pk,
                                appliance_id=self.appliance.pk,
                            ),
                        ],
                    )
                ],
                status=AppointmentStatus.ACCEPTED,
            ),
            # Appointment #6 - mixed combo services and service-variant
            dict(
                subbookings=[
                    dict(
                        booked_from='2037-07-13T13:00',
                        service_variant_id=self.combo_service_variant.pk,
                        combo_children=[
                            dict(
                                service_variant_id=self.combo_membership1.child_id,
                                staffer_id=self.staffer.pk,
                            ),
                            dict(
                                service_variant_id=self.combo_membership2.child_id,
                                staffer_id=self.staffer2.pk,
                                appliance_id=self.appliance.pk,
                            ),
                        ],
                    ),
                    dict(
                        booked_from='2037-07-13T13:30',
                        staffer_id=self.staffer.pk,
                        service_variant_id=self.service_variant2.pk,
                        metadata=dict(extra=dict(bar=1)),
                    ),
                ],
                status=AppointmentStatus.ACCEPTED,
            ),
            # Appointment #7 - mobile service with traveling
            dict(
                subbookings=[
                    dict(
                        booked_from='2037-07-13T13:00',
                        staffer_id=self.staffer.pk,
                        service_variant_id=self.mobile_service_variant.pk,
                    )
                ],
                booked_for_id=self.customer2.pk,
                status=AppointmentStatus.ACCEPTED,
                traveling=dict(
                    address_line_1='Address 1',
                    city='City',
                    zipcode='12345',
                    price=15,
                ),
                metadata=dict(extra=dict(foo=1)),
            ),
            # Appointment #8 - mobile service with traveling without price
            dict(
                subbookings=[
                    dict(
                        booked_from='2037-07-13T13:00',
                        staffer_id=self.staffer.pk,
                        service_variant_id=self.mobile_service_variant.pk,
                    )
                ],
                status=AppointmentStatus.ACCEPTED,
                traveling=dict(
                    address_line_1='Address 2',
                    city='City 2',
                    zipcode='54321',
                ),
            ),
        ]
        return body

    def basic_response_for_resource_permission_testing(self):
        return self.post(self.formatted_url, data=self.appointments_body())

    def basic_response_for_token_scope_permission_testing(self):
        return self.post(self.formatted_url, data=self.appointments_body())

    @patch('webapps.booking.models.BookingSources.get_cached')
    def test_bulk_import_success(self, get_cached_mock):  # pylint: disable=too-many-statements
        get_cached_mock.return_value = get_or_create_booking_source(
            app_type=BookingSources.INTERNAL_APP,
        )

        with self.captureOnCommitCallbacks(execute=True):
            data = self.appointments_body()
            resp = self.post(self.formatted_url, data=data)
            body = resp.json()

        self.assertEqual(resp.status_code, status.HTTP_201_CREATED)
        self.assertEqual(len(body), 8)
        self.assert_result_count()

        self.traveling_coordinates_task_mock.delay.assert_called_once()
        self.assert_bumped_document(
            River.BUSINESS_CUSTOMER, filter(None, {s['booked_for_id'] for s in body})
        )
        self.bump_document_mock.assert_any_call(River.AVAILABILITY, self.business.pk)
        self.bump_document_mock.assert_any_call(
            River.AGGREGATE_STATISTICS, [(self.business.pk, '2020-07-13')]
        )

        for appointment in body:
            compare_expected_fields(appointment.keys(), self._APPOINTMENT_FIELDS)
            for subbooking in appointment['subbookings']:
                compare_expected_fields(subbooking.keys(), self._SUBBOOKING_FIELDS)

        # Appointment #1 - past
        appointment_0 = body[0]
        appointment_0_subbookings = appointment_0['subbookings']
        self.assertEqual(len(appointment_0_subbookings), 1)
        assert_appointment(
            appointment_0,
            booked_from='2020-07-13T13:00',
            booked_till='2020-07-13T13:15',
            business=self.business,
            status=AppointmentStatus.FINISHED,
        )
        assert_appointment_customer(
            appointment_0,
            customer=self.customer,
        )
        assert_appointment_subbooking(
            appointment_0_subbookings[0],
            booked_from='2020-07-13T13:00',
            booked_till='2020-07-13T13:15',
            service_variant=self.service_variant,
            staffer=self.staffer,
        )
        self.assertEqual(appointment_0['traveling'], None)
        self.assertEqual(appointment_0['metadata']['extra'], {'foo': 1})
        self.assertEqual(appointment_0['business_secret_note'], 'Secret note')
        self.assert_subbookings_count(appointment_0['id'], count=1)
        self.assert_subbooking_resources_count(appointment_0_subbookings[0]['id'], count=1)
        self.assert_appointment_metadata_count(appointment_0['id'], count=1)
        self.assert_subbooking_metadata_count(appointment_0_subbookings[0]['id'], count=0)

        # Appointment #2 - future
        appointment_1 = body[1]
        appointment_1_subbookings = appointment_1['subbookings']
        self.assertEqual(len(appointment_1_subbookings), 2)
        assert_appointment(
            appointment_1,
            booked_from='2037-07-13T13:00',
            booked_till='2037-07-13T13:40',
            business=self.business,
            status=AppointmentStatus.ACCEPTED,
        )
        assert_appointment_customer(
            appointment_1,
            customer=self.customer3,
        )
        assert_appointment_subbooking(
            appointment_1_subbookings[0],
            booked_from='2037-07-13T13:00',
            booked_till='2037-07-13T13:15',
            service_variant=self.service_variant,
            staffer=self.staffer,
        )
        assert_appointment_subbooking(
            appointment_1_subbookings[1],
            booked_from='2037-07-13T13:15',
            booked_till='2037-07-13T13:40',
            service_variant=self.service_variant2,
            staffer=self.staffer,
        )
        self.assertEqual(appointment_1['traveling'], None)
        self.assertEqual(appointment_1['metadata'], None)
        self.assertEqual(appointment_1_subbookings[0]['metadata'], None)
        self.assertEqual(appointment_1_subbookings[1]['metadata']['extra'], {'bar': 1})
        self.assert_subbookings_count(appointment_1['id'], count=2)
        self.assert_subbooking_resources_count(appointment_1_subbookings[0]['id'], count=1)
        self.assert_subbooking_resources_count(appointment_1_subbookings[1]['id'], count=1)
        self.assert_appointment_metadata_count(appointment_1['id'], count=0)
        self.assert_subbooking_metadata_count(appointment_1_subbookings[0]['id'], count=0)
        self.assert_subbooking_metadata_count(appointment_1_subbookings[1]['id'], count=1)

        # Appointment #3 - mixed service-variant and service-name
        appointment_2 = body[2]
        appointment_2_subbookings = appointment_2['subbookings']
        self.assertEqual(len(appointment_2_subbookings), 2)
        assert_appointment(
            appointment_2,
            booked_from='2037-07-13T13:00',
            booked_till='2037-07-13T14:00',
            business=self.business,
            status=AppointmentStatus.ACCEPTED,
        )
        assert_appointment_customer(
            appointment_2,
            customer=None,
        )
        assert_appointment_subbooking(
            appointment_2_subbookings[0],
            booked_from='2037-07-13T13:00',
            booked_till='2037-07-13T13:15',
            service_variant=self.service_variant,
            staffer=self.staffer,
        )
        assert_appointment_subbooking(
            appointment_2_subbookings[1],
            booked_from='2037-07-13T13:15',
            booked_till='2037-07-13T14:00',
            service_variant=None,
            service_name='Custom service',
            staffer=self.staffer,
        )
        self.assertEqual(appointment_2['traveling'], None)
        self.assertEqual(appointment_2['metadata']['extra'], {'foo': 1})
        self.assertEqual(appointment_2_subbookings[0]['metadata']['extra'], {'bar': 1})
        self.assertEqual(appointment_2_subbookings[1]['metadata']['extra'], {'bar': 2})
        self.assert_subbookings_count(appointment_2['id'], count=2)
        self.assert_subbooking_resources_count(appointment_2_subbookings[0]['id'], count=1)
        self.assert_subbooking_resources_count(appointment_2_subbookings[1]['id'], count=1)
        self.assert_appointment_metadata_count(appointment_2['id'], count=1)
        self.assert_subbooking_metadata_count(appointment_2_subbookings[0]['id'], count=1)
        self.assert_subbooking_metadata_count(appointment_2_subbookings[1]['id'], count=1)

        # Appointment #4 - combo services
        appointment_3 = body[3]
        appointment_3_subbookings = appointment_3['subbookings']
        self.assertEqual(len(appointment_3_subbookings), 1)
        assert_appointment(
            appointment_3,
            booked_from='2037-07-13T13:00',
            booked_till='2037-07-13T13:30',
            business=self.business,
            status=AppointmentStatus.ACCEPTED,
        )
        assert_appointment_customer(
            appointment_3,
            customer=self.customer,
        )
        assert_appointment_subbooking(
            appointment_3_subbookings[0],
            booked_from='2037-07-13T13:00',
            booked_till='2037-07-13T13:30',
            service_variant=self.combo_service_variant,
            combo_type=ComboType.SEQUENCE,
            staffer=None,
            appliance=None,
        )
        assert_appointment_subbooking(
            appointment_3_subbookings[0]['combo_children'][0],
            booked_from='2037-07-13T13:00',
            booked_till='2037-07-13T13:15',
            service_variant=self.service_variant,
            staffer=self.staffer,
        )
        assert_appointment_subbooking(
            appointment_3_subbookings[0]['combo_children'][1],
            booked_from='2037-07-13T13:15',
            booked_till='2037-07-13T13:30',
            service_variant=self.service2_variant,
            staffer=self.staffer2,
            appliance=self.appliance,
        )
        self.assertEqual(appointment_3['traveling'], None)
        self.assertEqual(appointment_3['metadata'], None)
        self.assertEqual(appointment_3_subbookings[0]['metadata']['extra'], {'bar': 1})
        self.assertEqual(
            appointment_3_subbookings[0]['combo_children'][0]['metadata']['extra'], {'baz': 1}
        )
        self.assertEqual(
            appointment_3_subbookings[0]['combo_children'][1]['metadata']['extra'], {'baz': 2}
        )
        self.assert_subbookings_count(appointment_3['id'], count=3)
        self.assertEqual(
            SubBooking.objects.filter(
                appointment_id=appointment_3['id'],
                combo_parent_id__isnull=False,
            ).count(),
            2,
        )
        self.assert_subbooking_resources_count(appointment_3_subbookings[0]['id'], count=0)
        self.assert_subbooking_resources_count(
            appointment_3_subbookings[0]['combo_children'][0]['id'], count=1
        )
        self.assert_subbooking_resources_count(
            appointment_3_subbookings[0]['combo_children'][1]['id'], count=2
        )
        self.assert_appointment_metadata_count(appointment_3['id'], count=0)
        self.assert_subbooking_metadata_count(appointment_3_subbookings[0]['id'], count=1)
        self.assert_subbooking_metadata_count(
            appointment_3_subbookings[0]['combo_children'][0]['id'], count=1
        )
        self.assert_subbooking_metadata_count(
            appointment_3_subbookings[0]['combo_children'][1]['id'], count=1
        )

        # Appointment #5 - parallel combo services
        appointment_4 = body[4]
        appointment_4_subbookings = appointment_4['subbookings']
        self.assertEqual(len(appointment_4_subbookings), 1)
        assert_appointment(
            appointment_4,
            booked_from='2037-07-13T13:00',
            booked_till='2037-07-13T13:25',
            business=self.business,
            status=AppointmentStatus.ACCEPTED,
        )
        assert_appointment_customer(
            appointment_4,
            customer=None,
        )
        assert_appointment_subbooking(
            appointment_4_subbookings[0],
            booked_from='2037-07-13T13:00',
            booked_till='2037-07-13T13:25',
            service_variant=self.parallel_combo_service_variant,
            combo_type=ComboType.PARALLEL,
            staffer=None,
            appliance=None,
        )
        assert_appointment_subbooking(
            appointment_4_subbookings[0]['combo_children'][0],
            booked_from='2037-07-13T13:00',
            booked_till='2037-07-13T13:25',
            service_variant=self.service_variant2,
            staffer=self.staffer,
        )
        assert_appointment_subbooking(
            appointment_4_subbookings[0]['combo_children'][1],
            booked_from='2037-07-13T13:00',
            booked_till='2037-07-13T13:15',
            service_variant=self.service2_variant,
            staffer=self.staffer2,
            appliance=self.appliance,
        )
        self.assertEqual(appointment_4['traveling'], None)
        self.assertEqual(appointment_4['metadata'], None)
        self.assertEqual(appointment_4_subbookings[0]['metadata'], None)
        self.assertEqual(appointment_4_subbookings[0]['combo_children'][0]['metadata'], None)
        self.assertEqual(appointment_4_subbookings[0]['combo_children'][1]['metadata'], None)
        self.assert_subbookings_count(appointment_4['id'], count=3)
        self.assertEqual(
            SubBooking.objects.filter(
                appointment_id=appointment_4['id'],
                combo_parent_id__isnull=False,
            ).count(),
            2,
        )
        self.assert_subbooking_resources_count(appointment_4_subbookings[0]['id'], count=0)
        self.assert_subbooking_resources_count(
            appointment_4_subbookings[0]['combo_children'][0]['id'], count=1
        )
        self.assert_subbooking_resources_count(
            appointment_4_subbookings[0]['combo_children'][1]['id'], count=2
        )
        self.assert_appointment_metadata_count(appointment_4['id'], count=0)
        self.assert_subbooking_metadata_count(appointment_4_subbookings[0]['id'], count=0)
        self.assert_subbooking_metadata_count(
            appointment_4_subbookings[0]['combo_children'][0]['id'], count=0
        )
        self.assert_subbooking_metadata_count(
            appointment_4_subbookings[0]['combo_children'][1]['id'], count=0
        )

        # Appointment #6 - mixed combo services and service-variant
        appointment_5 = body[5]
        appointment_5_subbookings = appointment_5['subbookings']
        self.assertEqual(len(appointment_5_subbookings), 2)
        assert_appointment(
            appointment_5,
            booked_from='2037-07-13T13:00',
            booked_till='2037-07-13T13:55',
            business=self.business,
            status=AppointmentStatus.ACCEPTED,
        )
        assert_appointment_customer(
            appointment_5,
            customer=None,
        )
        assert_appointment_subbooking(
            appointment_5_subbookings[0],
            booked_from='2037-07-13T13:00',
            booked_till='2037-07-13T13:30',
            service_variant=self.combo_service_variant,
            combo_type=ComboType.SEQUENCE,
            staffer=None,
            appliance=None,
        )
        assert_appointment_subbooking(
            appointment_5_subbookings[0]['combo_children'][0],
            booked_from='2037-07-13T13:00',
            booked_till='2037-07-13T13:15',
            service_variant=self.service_variant,
            staffer=self.staffer,
        )
        assert_appointment_subbooking(
            appointment_5_subbookings[0]['combo_children'][1],
            booked_from='2037-07-13T13:15',
            booked_till='2037-07-13T13:30',
            service_variant=self.service2_variant,
            staffer=self.staffer2,
            appliance=self.appliance,
        )
        assert_appointment_subbooking(
            appointment_5_subbookings[1],
            booked_from='2037-07-13T13:30',
            booked_till='2037-07-13T13:55',
            service_variant=self.service_variant2,
            staffer=self.staffer,
            appliance=None,
        )
        self.assertEqual(appointment_5['traveling'], None)
        self.assertEqual(appointment_5['metadata'], None)
        self.assertEqual(appointment_5_subbookings[0]['metadata'], None)
        self.assertEqual(appointment_5_subbookings[0]['combo_children'][0]['metadata'], None)
        self.assertEqual(appointment_5_subbookings[0]['combo_children'][1]['metadata'], None)
        self.assertEqual(appointment_5_subbookings[1]['metadata']['extra'], {'bar': 1})
        self.assert_subbookings_count(appointment_5['id'], count=4)
        self.assertEqual(
            SubBooking.objects.filter(
                appointment_id=appointment_5['id'],
                combo_parent_id__isnull=False,
            ).count(),
            2,
        )
        self.assert_subbooking_resources_count(appointment_5_subbookings[0]['id'], count=0)
        self.assert_subbooking_resources_count(
            appointment_5_subbookings[0]['combo_children'][0]['id'], count=1
        )
        self.assert_subbooking_resources_count(
            appointment_5_subbookings[0]['combo_children'][1]['id'], count=2
        )
        self.assert_subbooking_resources_count(appointment_5_subbookings[1]['id'], count=1)
        self.assert_appointment_metadata_count(appointment_5['id'], count=0)
        self.assert_subbooking_metadata_count(appointment_5_subbookings[0]['id'], count=0)
        self.assert_subbooking_metadata_count(
            appointment_5_subbookings[0]['combo_children'][0]['id'], count=0
        )
        self.assert_subbooking_metadata_count(
            appointment_5_subbookings[0]['combo_children'][1]['id'], count=0
        )
        self.assert_subbooking_metadata_count(appointment_5_subbookings[1]['id'], count=1)

        # Appointment #7 - mobile service with traveling
        appointment_6 = body[6]
        appointment_6_subbookings = appointment_6['subbookings']
        self.assertEqual(len(appointment_6_subbookings), 1)
        assert_appointment(
            appointment_6,
            booked_from='2037-07-13T13:00',
            booked_till='2037-07-13T13:15',
            business=self.business,
            status=AppointmentStatus.ACCEPTED,
        )
        assert_appointment_customer(
            appointment_6,
            customer=self.customer2,
        )
        assert_appointment_subbooking(
            appointment_6_subbookings[0],
            booked_from='2037-07-13T13:00',
            booked_till='2037-07-13T13:15',
            service_variant=self.mobile_service_variant,
            staffer=self.staffer,
        )
        self.assertEqual(appointment_6['traveling']['address_line_1'], 'Address 1')
        self.assertEqual(appointment_6['traveling']['formatted_price'], '$15.00')
        self.assertEqual(appointment_6['metadata']['extra'], {'foo': 1})
        self.assert_subbookings_count(appointment_6['id'], count=1)
        self.assert_subbooking_resources_count(appointment_6_subbookings[0]['id'], count=1)
        self.assert_appointment_metadata_count(appointment_6['id'], count=1)
        self.assert_subbooking_metadata_count(appointment_6_subbookings[0]['id'], count=0)

        # Appointment #8 - mobile service with traveling without price
        appointment_7 = body[7]
        appointment_7_subbookings = appointment_7['subbookings']
        self.assertEqual(len(appointment_7_subbookings), 1)
        assert_appointment(
            appointment_7,
            booked_from='2037-07-13T13:00',
            booked_till='2037-07-13T13:15',
            business=self.business,
            status=AppointmentStatus.ACCEPTED,
        )
        assert_appointment_customer(
            appointment_7,
            customer=None,
        )
        assert_appointment_subbooking(
            appointment_7_subbookings[0],
            booked_from='2037-07-13T13:00',
            booked_till='2037-07-13T13:15',
            service_variant=self.mobile_service_variant,
            staffer=self.staffer,
        )
        self.assertEqual(appointment_7['traveling']['address_line_1'], 'Address 2')
        self.assertEqual(appointment_7['traveling']['formatted_price'], '$10.00')
        self.assertEqual(appointment_7['metadata'], None)
        self.assert_subbookings_count(appointment_7['id'], count=1)
        self.assert_subbooking_resources_count(appointment_7_subbookings[0]['id'], count=1)
        self.assert_appointment_metadata_count(appointment_7['id'], count=0)
        self.assert_subbooking_metadata_count(appointment_7_subbookings[0]['id'], count=0)

        self.customer.refresh_from_db()
        self.assertTrue(self.customer.visible_in_business)
        self.assertEqual(
            self.customer.client_type, BusinessCustomerInfo.CLIENT_TYPE__BUSINESS_DIRECTLY
        )
        self.assertEqual(self.customer.first_appointment_id, appointment_0['id'])
        self.customer2.refresh_from_db()
        self.assertTrue(self.customer2.visible_in_business)
        self.assertEqual(self.customer2.client_type, BusinessCustomerInfo.CLIENT_TYPE__INSTAGRAM)
        self.assertEqual(self.customer2.first_appointment_id, appointment_6['id'])
        self.customer3.refresh_from_db()
        self.assertTrue(self.customer3.visible_in_business)
        self.assertEqual(
            self.customer3.client_type, BusinessCustomerInfo.CLIENT_TYPE__BUSINESS_DIRECTLY
        )
        self.assertEqual(self.customer3.first_appointment_id, self.appointment.pk)

    def test_bulk_import_failure_with_invalid_service_variant_id(self):
        data = self.appointments_body()
        data[1]['subbookings'][0]['service_variant_id'] = -1
        resp = self.post(self.formatted_url, data=data)
        body = resp.json()

        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(len(body), 8)
        self.assertEqual(
            body[1],
            {
                'subbookings': [
                    {'service_variant_id': ['Invalid pk "-1" - object does not exist.']},
                    {},
                ]
            },
        )
        self.assert_no_result_count()

        self.traveling_coordinates_task_mock.delay.assert_not_called()
        self.bump_document_mock.assert_not_called()

    def test_bulk_import_failure_with_invalid_staffer_and_appliance_id(self):
        data = self.appointments_body()
        data[1]['subbookings'][0]['staffer_id'] = None
        data[1]['subbookings'][0]['appliance_id'] = None
        resp = self.post(self.formatted_url, data=data)
        body = resp.json()

        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(len(body), 8)
        self.assertEqual(
            body[1],
            {
                'subbookings': [
                    {'non_field_errors': ['Service requires a staffer or an appliance.']},
                    {},
                ]
            },
        )
        self.assert_no_result_count()

        self.traveling_coordinates_task_mock.delay.assert_not_called()
        self.bump_document_mock.assert_not_called()

    def test_bulk_import_failure_with_invalid_staffer_id(self):
        data = self.appointments_body()
        data[1]['subbookings'][0]['staffer_id'] = -1
        resp = self.post(self.formatted_url, data=data)
        body = resp.json()

        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(len(body), 8)
        self.assertEqual(
            body[1],
            {'subbookings': [{'staffer_id': ['Invalid pk "-1" - object does not exist.']}, {}]},
        )
        self.assert_no_result_count()

        self.traveling_coordinates_task_mock.delay.assert_not_called()
        self.bump_document_mock.assert_not_called()

    def test_bulk_import_failure_with_invalid_appliance_id(self):
        data = self.appointments_body()
        data[1]['subbookings'][0]['appliance_id'] = -1
        resp = self.post(self.formatted_url, data=data)
        body = resp.json()

        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(len(body), 8)
        self.assertEqual(
            body[1],
            {'subbookings': [{'appliance_id': ['Invalid pk "-1" - object does not exist.']}, {}]},
        )
        self.assert_no_result_count()

        self.traveling_coordinates_task_mock.delay.assert_not_called()
        self.bump_document_mock.assert_not_called()

    def test_bulk_import_failure_with_invalid_customer_id(self):
        data = self.appointments_body()
        data[1]['booked_for_id'] = -1
        resp = self.post(self.formatted_url, data=data)
        body = resp.json()

        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(len(body), 8)
        self.assertEqual(body[1], {'booked_for_id': ['Invalid pk "-1" - object does not exist.']})
        self.assert_no_result_count()

        self.traveling_coordinates_task_mock.delay.assert_not_called()
        self.bump_document_mock.assert_not_called()

    def test_bulk_import_failure_with_invalid_traveling(self):
        data = self.appointments_body()
        data[6]['traveling'] = None
        resp = self.post(self.formatted_url, data=data)
        body = resp.json()

        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(len(body), 8)
        self.assertEqual(body[6], {'traveling': ['Mobile service info is required']})

        data = self.appointments_body()
        data[6]['traveling']['city'] = None
        resp = self.post(self.formatted_url, data=data)
        body = resp.json()

        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(len(body), 8)
        self.assertEqual(body[6], {'traveling': {'city': ['This field may not be null.']}})

        self.assert_no_result_count()
        self.traveling_coordinates_task_mock.delay.assert_not_called()
        self.bump_document_mock.assert_not_called()

    def test_bulk_import_failure_with_invalid_subbooking_metadata(self):
        data = self.appointments_body()
        data[1]['subbookings'][1]['metadata']['kind'] = 'foo'
        resp = self.post(self.formatted_url, data=data)
        body = resp.json()

        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(len(body), 8)
        self.assertEqual(
            body[1], {'subbookings': [{}, {'metadata': {'kind': ['"foo" is not a valid choice.']}}]}
        )
        self.assert_no_result_count()

        self.traveling_coordinates_task_mock.delay.assert_not_called()
        self.bump_document_mock.assert_not_called()

    @patch('webapps.booking.models.BookingSources.get_cached')
    def test_bulk_import_with_only_customer_name(self, get_cached_mock):
        get_cached_mock.return_value = get_or_create_booking_source(
            app_type=BookingSources.INTERNAL_APP,
        )
        appointment_data = [
            {
                'subbookings': [
                    {
                        'booked_from': '2020-07-13T13:00',
                        'staffer_id': self.staffer.pk,
                        'service_variant_id': self.service_variant.pk,
                    }
                ],
                'customer_name': 'Test testowy',
                'status': AppointmentStatus.FINISHED,
            },
        ]
        resp = self.post(self.formatted_url, data=appointment_data)
        body = resp.json()

        appointment = Appointment.objects.get(id=body[0]['id'])
        self.assertIsNone(appointment.booked_for)
        self.assertEqual(appointment.customer_name, 'Test testowy')

    @patch('webapps.booking.models.BookingSources.get_cached')
    def test_bulk_import_with_no_customer_data(self, get_cached_mock):
        get_cached_mock.return_value = get_or_create_booking_source(
            app_type=BookingSources.INTERNAL_APP,
        )

        appointment_data = [
            {
                'subbookings': [
                    {
                        'booked_from': '2020-07-13T13:00',
                        'staffer_id': self.staffer.pk,
                        'service_variant_id': self.service_variant.pk,
                    }
                ],
                'status': AppointmentStatus.FINISHED,
            },
        ]
        resp = self.post(self.formatted_url, data=appointment_data)
        body = resp.json()

        appointment = Appointment.objects.get(id=body[0]['id'])
        self.assertIsNone(appointment.booked_for)
        self.assertIsNone(appointment.customer_name)

    @patch('webapps.booking.models.BookingSources.get_cached')
    def test_bulk_import_with_mixed_customer_data(self, get_cached_mock):
        get_cached_mock.return_value = get_or_create_booking_source(
            app_type=BookingSources.INTERNAL_APP,
        )

        appointment_data = [
            {
                'subbookings': [
                    {
                        'booked_from': '2020-07-13T13:00',
                        'staffer_id': self.staffer.pk,
                        'service_variant_id': self.service_variant.pk,
                    }
                ],
                'status': AppointmentStatus.FINISHED,
            },
            {
                'subbookings': [
                    {
                        'booked_from': '2020-07-13T13:00',
                        'staffer_id': self.staffer.pk,
                        'service_variant_id': self.service_variant.pk,
                    }
                ],
                'customer_name': 'Test testowy',
                'status': AppointmentStatus.FINISHED,
            },
            {
                'subbookings': [
                    {
                        'booked_from': '2020-07-13T13:00',
                        'staffer_id': self.staffer.pk,
                        'service_variant_id': self.service_variant.pk,
                    }
                ],
                'booked_for_id': self.customer3.pk,
                'status': AppointmentStatus.FINISHED,
            },
        ]
        resp = self.post(self.formatted_url, data=appointment_data)
        body = resp.json()

        appointment_with_no_data = Appointment.objects.get(id=body[0]['id'])
        self.assertIsNone(appointment_with_no_data.booked_for)
        self.assertIsNone(appointment_with_no_data.customer_name)

        appointment_with_name = Appointment.objects.get(id=body[1]['id'])
        self.assertIsNone(appointment_with_name.booked_for)
        self.assertEqual(appointment_with_name.customer_name, 'Test testowy')

        appointment_with_related_bci = Appointment.objects.get(id=body[2]['id'])
        self.assertEqual(appointment_with_related_bci.booked_for, self.customer3)
        self.assertEqual(
            appointment_with_related_bci.customer_name,
            self.customer3.full_name,
        )
