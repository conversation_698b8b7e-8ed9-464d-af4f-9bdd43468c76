from model_bakery import baker
from rest_framework import status

from lib.test_utils import compare_expected_fields, create_subbooking
from webapps.public_partners.tests import (
    FirewallTestCaseMixin,
    PublicApiBaseTestCase,
)
from webapps.reviews.models import Review


REVIEW_DATA = {
    'first_name': '<PERSON>',
    'last_name': '<PERSON><PERSON><PERSON>',
    'rank': 5,
    'title': 'it was great',
    'review': 'I enjoyed treatment so much',
    'created': '2020-04-01T10:00:00Z',
    'reply_content': 'Thank you very much',
    'reply_updated': '2020-04-02T12:00:00Z',
    'import_uid': 'external_id-1234',
}

REVIEW_FIELDS = {
    'id',
    'business_id',
    'first_name',
    'last_name',
    'appointment',
    'subbooking',
    'rank',
    'title',
    'review',
    'created',
    'reply_content',
    'reply_updated',
    'import_uid',
}


class ReviewViewSetV02TestCase(FirewallTestCaseMixin, PublicApiBaseTestCase):
    _url = '/public-api/us/business/{}/appointment/{}/review/'
    _VERSION = '0.2'

    def setUp(self):
        super().setUp()
        self.subbooking, *_ = create_subbooking(
            business=self.business,
        )

    def get_url(self, business_id=None, appointment_id=None):
        return self._url.format(
            business_id or self.business.id, appointment_id or self.subbooking.appointment.id
        )

    def test_check_not_allowed_methods(self):
        self.check_not_allowed_methods(self.get_url(), {'get', 'post', 'put', 'patch', 'delete'})

    def test_create_review(self):
        data = REVIEW_DATA.copy()
        assert not hasattr(self.subbooking, 'review')
        resp = self.post(self.get_url(), data=data)
        assert resp.status_code == status.HTTP_201_CREATED
        self.subbooking.refresh_from_db()
        assert hasattr(self.subbooking, 'review')

        review = resp.json()
        appointment = self.subbooking.appointment
        compare_expected_fields(review.keys(), REVIEW_FIELDS)
        assert review['id']
        assert review['first_name'] == data['first_name']
        assert review['last_name'] == data['last_name']
        assert review['appointment'] == {'id': appointment.id, 'import_uid': appointment.import_uid}
        assert review['subbooking'] == self.subbooking.id
        assert review['rank'] == data['rank']
        assert review['title'] == data['title']
        assert review['review'] == data['review']
        assert review['created'] == data['created']
        assert review['reply_content'] == data['reply_content']
        assert review['reply_updated'] == data['reply_updated']
        assert self.subbooking.review.import_uid == data['import_uid']

    def test_create_review_error_rank(self):
        data = REVIEW_DATA.copy()
        data['rank'] = 4.5
        resp = self.post(self.get_url(), data=data)
        assert resp.status_code == status.HTTP_400_BAD_REQUEST
        assert resp.json() == dict(
            rank=['A valid integer is required.'],
        )

    def test_get_reviews(self):
        baker.make(Review, business=self.business, rank=5, subbooking=self.subbooking)
        assert hasattr(self.subbooking, 'review')
        resp = self.get(self.get_url())
        assert resp.status_code == status.HTTP_200_OK

        reviews = resp.json()
        assert len(reviews) == 1
        review = reviews[0]
        compare_expected_fields(review.keys(), REVIEW_FIELDS)

    def test_get_reviews_error_appointment(self):
        other_subbooking, *_ = create_subbooking(
            business=self.business,
        )
        baker.make(Review, business=self.business, rank=5, subbooking=self.subbooking)
        assert hasattr(self.subbooking, 'review')
        assert not hasattr(other_subbooking, 'review')
        resp = self.get(self.get_url(appointment_id=other_subbooking.appointment.id))
        assert resp.status_code == status.HTTP_200_OK

    def test_get_reviews_empty(self):
        assert not hasattr(self.subbooking, 'review')
        resp = self.get(self.get_url())
        assert resp.status_code == status.HTTP_200_OK
        reviews = resp.json()
        assert len(reviews) == 0

    def test_get_reviews_mismatch_appointment_business(self):
        other_business = baker.make_recipe(
            'webapps.business.business_recipe',
        )
        self.partner.add_business(other_business)
        other_subbooking, *_ = create_subbooking(
            business=other_business,
        )
        baker.make(Review, business=self.business, rank=5, subbooking=self.subbooking)
        assert hasattr(self.subbooking, 'review')
        assert not hasattr(other_subbooking, 'review')
        resp = self.get(
            self.get_url(business_id=self.business.id, appointment_id=other_subbooking.id)
        )
        assert resp.status_code == status.HTTP_404_NOT_FOUND

    def test_delete_review(self):
        baker.make(Review, business=self.business, rank=5, subbooking=self.subbooking)
        assert hasattr(self.subbooking, 'review')
        resp = self.delete(self.get_url())
        assert resp.status_code == status.HTTP_204_NO_CONTENT

    def test_update_review(self):
        baker.make(
            Review,
            business=self.business,
            rank=5,
            review='super!!!',
            subbooking=self.subbooking,
        )
        data = {'reply_content': 'thanks'}
        resp = self.put(self.get_url(), data=data)
        assert resp.status_code == status.HTTP_200_OK
        review = Review.objects.get(
            business=self.business,
            subbooking=self.subbooking,
        )
        assert review.reply_content == data['reply_content']
        assert review.reply_updated is not None

    def test_partial_update_review(self):
        baker.make(
            Review,
            business=self.business,
            rank=5,
            review='super!!!',
            subbooking=self.subbooking,
        )
        data = {'reply_content': 'thanks'}
        resp = self.patch(self.get_url(), data=data)
        assert resp.status_code == status.HTTP_200_OK
        review = Review.objects.get(
            business=self.business,
            subbooking=self.subbooking,
        )
        assert review.reply_content == data['reply_content']
        assert review.reply_updated is not None

    def test_delete_missing_review_error(self):
        resp = self.delete(self.get_url())
        assert resp.status_code == status.HTTP_404_NOT_FOUND

    def test_create_review_by_wrong_partner_error(self):
        self.authenticate_other_partner()
        resp = self.post(self.get_url(), data=REVIEW_DATA)
        assert resp.status_code == status.HTTP_403_FORBIDDEN

    def basic_response_for_firewall_testing(self):
        baker.make(Review, business=self.business, rank=5, subbooking=self.subbooking)
        return self.get(self.get_url())


class ReviewViewSetV03TestCase(FirewallTestCaseMixin, PublicApiBaseTestCase):
    _url = '/public-api/us/business/{}/appointment/{}/review/'
    _VERSION = '0.3'

    def setUp(self):
        super().setUp()
        self.subbooking, *_ = create_subbooking(
            business=self.business,
        )

    def get_url(self, business_id=None, appointment_id=None):
        return self._url.format(
            business_id or self.business.id, appointment_id or self.subbooking.appointment.id
        )

    def test_check_not_allowed_methods(self):
        self.check_not_allowed_methods(self.get_url(), {'get', 'put', 'patch', 'delete'})

    def test_create_review(self):
        data = REVIEW_DATA.copy()
        assert not hasattr(self.subbooking, 'review')
        resp = self.put(self.get_url(), data=data)
        assert resp.status_code == status.HTTP_201_CREATED
        self.subbooking.refresh_from_db()
        assert hasattr(self.subbooking, 'review')

        review = resp.json()
        appointment = self.subbooking.appointment
        compare_expected_fields(review.keys(), REVIEW_FIELDS)
        assert review['id']
        assert review['first_name'] == data['first_name']
        assert review['last_name'] == data['last_name']
        assert review['appointment']['id'] == appointment.id
        assert review['appointment']['import_uid'] == appointment.import_uid
        assert review['subbooking'] == self.subbooking.id
        assert review['rank'] == data['rank']
        assert review['title'] == data['title']
        assert review['review'] == data['review']
        assert review['created'] == data['created']
        assert review['reply_content'] == data['reply_content']
        assert review['reply_updated'] == data['reply_updated']
        assert self.subbooking.review.import_uid == data['import_uid']

    def test_create_review_error_rank(self):
        data = REVIEW_DATA.copy()
        data['rank'] = 4.5
        resp = self.put(self.get_url(), data=data)
        assert resp.status_code == status.HTTP_400_BAD_REQUEST
        assert resp.json() == dict(
            rank=['A valid integer is required.'],
        )

    def test_get_review(self):
        baker.make(Review, business=self.business, rank=5, subbooking=self.subbooking)
        assert hasattr(self.subbooking, 'review')
        resp = self.get(self.get_url())
        assert resp.status_code == status.HTTP_200_OK

        review = resp.json()
        appointment = self.subbooking.appointment
        compare_expected_fields(review.keys(), REVIEW_FIELDS)
        assert review['id']
        assert review['appointment']['id'] == appointment.id
        assert review['appointment']['import_uid'] == appointment.import_uid
        assert review['subbooking'] == self.subbooking.id
        assert review['rank'] == 5

    def test_get_review_error_appointment(self):
        other_subbooking, *_ = create_subbooking(
            business=self.business,
        )
        baker.make(Review, business=self.business, rank=5, subbooking=self.subbooking)
        assert hasattr(self.subbooking, 'review')
        assert not hasattr(other_subbooking, 'review')
        resp = self.get(self.get_url(appointment_id=other_subbooking.appointment.id))
        assert resp.status_code == status.HTTP_404_NOT_FOUND

    def test_get_review_empty(self):
        assert not hasattr(self.subbooking, 'review')
        resp = self.get(self.get_url())
        assert resp.status_code == status.HTTP_404_NOT_FOUND

    def test_get_review_mismatch_appointment_business(self):
        other_business = baker.make_recipe(
            'webapps.business.business_recipe',
        )
        self.partner.add_business(other_business)
        other_subbooking, *_ = create_subbooking(
            business=other_business,
        )
        baker.make(Review, business=self.business, rank=5, subbooking=self.subbooking)
        assert hasattr(self.subbooking, 'review')
        assert not hasattr(other_subbooking, 'review')
        resp = self.get(
            self.get_url(business_id=self.business.id, appointment_id=other_subbooking.id)
        )
        assert resp.status_code == status.HTTP_404_NOT_FOUND

    def test_delete_review(self):
        baker.make(Review, business=self.business, rank=5, subbooking=self.subbooking)
        assert hasattr(self.subbooking, 'review')
        resp = self.delete(self.get_url())
        assert resp.status_code == status.HTTP_204_NO_CONTENT

    def test_update_review(self):
        baker.make(
            Review,
            business=self.business,
            rank=5,
            review='super!!!',
            subbooking=self.subbooking,
        )
        data = REVIEW_DATA.copy()
        data['reply_content'] = 'thanks'
        resp = self.put(self.get_url(), data=data)
        assert resp.status_code == status.HTTP_200_OK
        review = Review.objects.get(
            business=self.business,
            subbooking=self.subbooking,
        )
        assert review.reply_content == data['reply_content']
        assert review.reply_updated is not None

    def test_partial_update_review(self):
        baker.make(
            Review,
            business=self.business,
            rank=5,
            review='super!!!',
            subbooking=self.subbooking,
        )
        data = {'reply_content': 'thanks'}
        resp = self.patch(self.get_url(), data=data)
        assert resp.status_code == status.HTTP_200_OK
        review = Review.objects.get(
            business=self.business,
            subbooking=self.subbooking,
        )
        assert review.reply_content == data['reply_content']
        assert review.reply_updated is not None

    def test_delete_missing_review_error(self):
        resp = self.delete(self.get_url())
        assert resp.status_code == status.HTTP_404_NOT_FOUND

    def test_create_review_by_wrong_partner_error(self):
        self.authenticate_other_partner()
        resp = self.post(self.get_url(), data=REVIEW_DATA)
        assert resp.status_code == status.HTTP_403_FORBIDDEN

    def basic_response_for_firewall_testing(self):
        baker.make(Review, business=self.business, rank=5, subbooking=self.subbooking)
        return self.get(self.get_url())
