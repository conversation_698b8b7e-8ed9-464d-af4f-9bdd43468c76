from model_bakery import baker
from rest_framework import status

from lib.test_utils import compare_expected_fields
from webapps.business.baker_recipes import category_recipe, treatment_recipe
from webapps.business.models import Business
from webapps.business.models.category import BusinessCategory
from webapps.public_partners.tests import FirewallTestCaseMixin, PublicApiBaseTestCase


class BusinessCategoriesV02TestCase(
    FirewallTestCaseMixin,
    PublicApiBaseTestCase,
):
    url = '/public-api/us/business_categories/'
    _VERSION = '0.2'

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        Business.objects.all().delete()
        BusinessCategory.objects.all().delete()
        category_recipe.make(_quantity=25)
        category_recipe.make(visible_for_biz=False)
        treatment_recipe.make(_quantity=2)
        treatment_recipe.make(_quantity=2)

    def test_check_not_allowed_methods(self):
        self.check_not_allowed_methods(self.url, {'get', 'options'})

    def test_categories(self):
        resp = self.get(self.url)
        assert resp.status_code == status.HTTP_200_OK
        assert len(resp.json()) == 27

    def basic_response_for_firewall_testing(self):
        return self.get(self.url)


class BusinessCategoriesV03TestCase(BusinessCategoriesV02TestCase):
    url = '/public-api/us/business_category/'
    _VERSION = '0.3'


class BaseBusinessCategoryV02TestCase(PublicApiBaseTestCase):
    _url = '/public-api/us/business/{}/categories/'
    _details_url = '/public-api/us/business/{}/categories/{}/'
    _VERSION = '0.2'

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.business.primary_category = None
        cls.business.save()

    def get_url(self, business_id=None):
        return self._url.format(business_id or self.business.id)

    def get_details_url(self, category_id, business_id=None):
        return self._details_url.format(business_id or self.business.id, category_id)

    def create_categories(self):
        categories = category_recipe.make(_quantity=2)
        treatments = treatment_recipe.make(_quantity=2)
        self.business.categories.add(*categories)
        self.business.categories.add(*treatments)


class BusinessCategoryViewV02TestCase(FirewallTestCaseMixin, BaseBusinessCategoryV02TestCase):
    def test_check_not_allowed_methods(self):
        self.check_not_allowed_methods(self.get_url(), {'get', 'options'})

    def test_check_not_allowed_details_methods(self):
        self.check_not_allowed_methods(self.get_details_url(1), {'put', 'delete', 'options'})

    def test_get_proper_categories_fields(self):
        self.create_categories()
        resp = self.get(self.get_url())
        assert resp.status_code == status.HTTP_200_OK
        body = resp.json()
        category = body[0]

        fields = {
            'id',
            'type',
            'name',
            'full_name',
            'plural_name',
            'keywords',
            'featured',
            'children',
            'internal_name',
        }
        compare_expected_fields(category.keys(), fields)

    def test_get_children(self):
        expected_parent = category_recipe.make()
        expected_child = category_recipe.make(parent=expected_parent)
        self.business.categories.set([expected_parent])

        resp = self.get(self.get_url())
        assert resp.status_code == status.HTTP_200_OK
        body = resp.json()
        category = body[0]

        self.assertEqual(
            {
                'id': expected_parent.id,
                'type': expected_parent.type,
                'name': expected_parent.name,
                'full_name': expected_parent.full_name,
                'plural_name': expected_parent.plural_name,
                'internal_name': expected_parent.internal_name,
                'keywords': expected_parent.keywords,
                'featured': expected_parent.featured,
                'children': [
                    {
                        'id': expected_child.id,
                        'type': expected_child.type,
                        'name': expected_child.name,
                        'full_name': expected_child.full_name,
                        'plural_name': expected_child.plural_name,
                        'internal_name': expected_child.internal_name,
                        'keywords': expected_child.keywords,
                        'featured': expected_child.featured,
                        'children': [],
                    }
                ],
            },
            category,
        )

    def test_other_business_has_no_access_to_categories(self):
        self.create_categories()
        other_business = baker.make_recipe(
            'webapps.business.business_recipe',
        )
        self.partner.add_business(other_business)

        resp = self.get(self.get_url(other_business.id))
        assert resp.status_code == status.HTTP_200_OK
        body = resp.json()
        assert len(body) == 0

    def basic_response_for_firewall_testing(self):
        return self.get(self.get_url())


class BusinessCategoryUpdateV02TestCase(FirewallTestCaseMixin, BaseBusinessCategoryV02TestCase):
    def test_expected_fields(self):
        treatment = treatment_recipe.make()

        resp = self.put(self.get_details_url(treatment.id))
        assert resp.status_code == status.HTTP_201_CREATED
        result_treatment = resp.json()
        fields = {
            'id',
            'type',
            'name',
            'full_name',
            'plural_name',
            'keywords',
            'featured',
            'children',
            'internal_name',
        }
        compare_expected_fields(result_treatment.keys(), fields)
        for field in fields:
            if field == 'children':
                assert len(result_treatment[field]) == 0
            else:
                assert result_treatment[field] == getattr(treatment, field)

    def test_assign_to_treatment(self):
        treatment = treatment_recipe.make()

        resp = self.put(self.get_details_url(treatment.id))
        assert resp.status_code == status.HTTP_201_CREATED
        assert self.business.categories.all().count() == 1
        assert self.business.categories.all()[0] == treatment

    def test_assign_to_category(self):
        category = category_recipe.make()

        resp = self.put(self.get_details_url(category.id))
        assert resp.status_code == status.HTTP_201_CREATED
        assert self.business.categories.all().count() == 1
        assert self.business.categories.all()[0] == category

    def test_assign_to_category_already_exist_error(self):
        category = category_recipe.make()
        self.put(self.get_details_url(category.id))

        resp = self.put(self.get_details_url(category.id))
        assert resp.status_code == status.HTTP_200_OK

    def test_assign_to_not_existing_category_error(self):
        resp = self.put(self.get_details_url(999999))
        assert resp.status_code == status.HTTP_404_NOT_FOUND
        assert self.business.categories.all().count() == 0

    def basic_response_for_firewall_testing(self):
        treatment = treatment_recipe.make()
        return self.put(self.get_details_url(treatment.id))


class BusinessCategoryDeleteV02TestCase(BaseBusinessCategoryV02TestCase):
    def test_unassign_from_treatment(self):
        treatment = treatment_recipe.make()
        self.business.categories.add(treatment)

        resp = self.delete(self.get_details_url(treatment.id))
        assert resp.status_code == status.HTTP_204_NO_CONTENT

    def test_unassign_from_category(self):
        category = category_recipe.make()
        self.business.categories.add(category)

        resp = self.delete(self.get_details_url(category.id))
        assert resp.status_code == status.HTTP_204_NO_CONTENT

    def test_unassign_from_category_not_assigned_error(self):
        category = category_recipe.make()

        resp = self.delete(self.get_details_url(category.id))
        assert resp.status_code == status.HTTP_404_NOT_FOUND

    def test_unassign_from_not_existing_category_error(self):
        resp = self.delete(self.get_details_url('999999'))
        assert resp.status_code == status.HTTP_404_NOT_FOUND

    def basic_response_for_firewall_testing(self):
        return self.get(self.url)


class PrimaryBusinessCategoryVersion02TestCase(
    FirewallTestCaseMixin, BaseBusinessCategoryV02TestCase
):
    _url = '/public-api/us/business/{}/primary_category/'
    _details_url = '/public-api/us/business/{}/primary_category/{}/'

    def test_check_not_allowed_methods(self):
        self.check_not_allowed_methods(self.get_url(), {'get', 'options'})

    def test_check_not_allowed_details_methods(self):
        self.check_not_allowed_methods(self.get_details_url(1), {'put', 'options'})

    def test_set_as_primary_treatment(self):
        treatment = treatment_recipe.make()

        assert self.business.primary_category is None
        resp = self.put(self.get_details_url(treatment.id))
        assert resp.status_code == status.HTTP_201_CREATED
        self.business.refresh_from_db()
        assert self.business.primary_category == treatment

    def test_set_as_primary_category(self):
        category = category_recipe.make()

        assert self.business.primary_category is None
        resp = self.put(self.get_details_url(category.id))
        assert resp.status_code == status.HTTP_201_CREATED
        self.business.refresh_from_db()
        assert self.business.primary_category == category

    def test_set_as_primary_category_with_override(self):
        category = category_recipe.make()
        self.put(self.get_details_url(category.id))
        other_category = category_recipe.make()

        resp = self.put(self.get_details_url(other_category.id))
        assert resp.status_code == status.HTTP_201_CREATED
        self.business.refresh_from_db()
        assert self.business.primary_category != category

    def basic_response_for_firewall_testing(self):
        category = category_recipe.make()
        return self.put(self.get_details_url(category.id))


class BaseBusinessCategoryV03TestCase(PublicApiBaseTestCase):
    _url = '/public-api/us/business/{}/category/'
    _details_url = '/public-api/us/business/{}/category/{}/'
    _VERSION = '0.3'


class BusinessCategoryViewV03TestCase(BusinessCategoryViewV02TestCase):
    pass


class BusinessCategoryUpdateV03TestCase(BusinessCategoryUpdateV02TestCase):
    pass


class BusinessCategoryDeleteV03TestCase(BusinessCategoryDeleteV02TestCase):
    pass
