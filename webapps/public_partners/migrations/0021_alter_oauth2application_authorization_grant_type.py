# Generated by Django 4.1.7 on 2023-05-19 10:16

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("public_partners", "0020_alter_partnerpermissionbusiness_managers"),
    ]

    operations = [
        migrations.AlterField(
            model_name="oauth2application",
            name="authorization_grant_type",
            field=models.CharField(
                choices=[
                    ("authorization-code", "Authorization code"),
                    ("implicit", "Implicit"),
                    ("password", "Resource owner password-based"),
                    ("client-credentials", "Client credentials"),
                    ("openid-hybrid", "OpenID connect hybrid"),
                    ("urn:ietf:params:oauth:grant-type:jwt-bearer", "JWT Assertion"),
                ],
                max_length=150,
            ),
        ),
    ]
