import json
import logging
import uuid
from datetime import timedelta

import jwt
from django.contrib.auth.validators import UnicodeUsernameValidator
from django.db import models, transaction
from django.db.models import J<PERSON><PERSON>ield
from django.utils.translation import gettext_lazy as _
from jwt import (
    DecodeError,
    ExpiredSignatureError,
    InvalidAudienceError,
    InvalidIssuerError,
    MissingRequiredClaimError,
    InvalidSignatureError,
)
from jwt.algorithms import RSAAlgorithm
from jwt.exceptions import InvalidKeyError

from lib.fields.email_array import EmailArrayField
from lib.models import ArchiveManager, ArchiveModel
from lib.tools import tznow
from webapps.public_partners.enum import PublicAPIVersionEnum

_BOOKSY_ISSUER = 'https://public-api.booksy.com'
_logger = logging.getLogger('booksy.public_api')


class PublicBooksyPartner(ArchiveModel):
    uuid = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
        db_index=True,
    )
    name = models.CharField(
        _('name'),
        max_length=150,
        unique=True,
        help_text=_('Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.'),
        validators=[UnicodeUsernameValidator()],
        error_messages={
            'unique': _("A public partner with that name already exists."),
        },
        db_index=True,
    )
    is_active = models.BooleanField(
        _('active'),
        default=True,
        help_text=_(
            'Designates whether this partner should be treated as active. '
            'Unselect this instead of deleting accounts.'
        ),
    )
    uses_merger = models.BooleanField(
        default=False, help_text=_('Communicates with the System through Booksy Merger.')
    )
    merger_forward = models.BooleanField(
        default=False, help_text=_('Appointments managed through booksy merger.')
    )
    default_integration_name = models.TextField(
        max_length=150,
        blank=True,
        null=True,
        help_text=_('Default name of the integration (Enterprise etc).'),
    )
    date_joined = models.DateTimeField(_('date joined'), default=tznow)
    last_login = models.DateTimeField(
        _('last login'),
        blank=True,
        null=True,
    )
    public_jwk = JSONField(default=dict)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.token = None

    def authorized_token(self, token) -> bool:
        """Check if partner's token  if it signed with proper signature"""
        try:
            headers = jwt.get_unverified_header(token)
        except DecodeError:
            return False
        algorithm = headers.get('alg')
        expected_algorithm = 'RS256'

        if algorithm is None or not isinstance(algorithm, str) or algorithm != expected_algorithm:
            return False

        try:
            algorithm = RSAAlgorithm(RSAAlgorithm.SHA256).from_jwk(json.dumps(self.public_jwk))
        except InvalidKeyError:
            # improperly configured partner
            return False

        return self._is_valid_token(token, algorithm, expected_algorithm)

    def _is_valid_token(self, token, algorithm, expected_algorithm='RS256'):
        """Validate token for given partner"""
        is_authorized = False
        required_claims = ['exp', 'iat', 'iss', 'aud']
        errors = (
            DecodeError,
            ExpiredSignatureError,
            InvalidSignatureError,
            InvalidIssuerError,
            InvalidAudienceError,
            MissingRequiredClaimError,
        )
        try:
            self.token = jwt.decode(
                token,
                algorithm,
                audience=self.uuid.hex,
                issuer=_BOOKSY_ISSUER,
                algorithms=[expected_algorithm],
                # add small leeway if UTC clock
                # is not sync
                # on other side
                leeway=timedelta(seconds=10),
                # for debug change for greater time
                options={'require': required_claims},
            )
        except errors as e:
            _logger.error('[PUBLIC API FAILED LOGIN FOR %s %s]', self.name, e)
        else:
            is_authorized = True

        return is_authorized

    def add_business(
        self,
        business: 'Business',
        import_uid: str = None,
    ) -> None:
        """Add given business to partner"""
        if import_uid is None:
            # generate some fake id if not given; ensure max_length=64
            import_uid = f'{self.name[:27]}_{uuid.uuid4()}'

        with transaction.atomic():
            permissions = PartnerPermissionBusiness(
                partner=self,
                business=business,
            )
            permissions.save()
            data = BusinessPartnerData(
                business=business,
                import_uid=import_uid,
            )
            data.save()

    @property
    def configuration_exists(self):
        return hasattr(self, 'configuration')

    # <editor-fold desc="Methods from Django User">
    # IN order to use throtling and caching per user from DRF
    # we need this methods
    @property
    def id(self):  # pylint: disable=invalid-name
        return self.uuid.int

    @property
    def is_authenticated(self):
        """
        Always return True. This is a way to tell if the user has been
        authenticated in templates.
        """
        return True

    @property
    def is_anonymous(self):
        """
        Always return False. This is a way of comparing User objects to
        anonymous users.
        """
        return False

    # </editor-fold>


class BusinessPartnerData(ArchiveModel):
    """Model to store partner related fields for each Business"""

    class Meta:
        unique_together = [('business', 'import_uid')]

    business = models.OneToOneField(
        to='business.Business',
        db_index=True,
        related_name='partner_data',
        on_delete=models.CASCADE,
    )
    import_uid = models.CharField(max_length=64, db_index=True)


class PartnerPermissionBusiness(ArchiveModel):
    class Meta:
        unique_together = [
            ['partner', 'business'],
        ]

    partner = models.ForeignKey(
        to=PublicBooksyPartner,
        on_delete=models.CASCADE,
        db_index=True,
    )
    business = models.ForeignKey(
        to='business.Business',
        on_delete=models.CASCADE,
        db_index=True,
    )

    objects = ArchiveManager()
    all_objects = models.Manager()


class PartnerConfiguration(ArchiveModel):
    partner = models.OneToOneField(
        to=PublicBooksyPartner,
        on_delete=models.CASCADE,
        primary_key=True,
        related_name='configuration',
    )

    booking_webhook = models.URLField(
        help_text=_('Url address to notify when booking created, modified, canceled'),
        null=True,
        blank=True,
    )

    cert_path = models.TextField(
        blank=True, null=True, help_text=_('Certificate path for partner webhook verification.')
    )

    webhook_version = models.CharField(
        choices=PublicAPIVersionEnum.choices(),
        default=PublicAPIVersionEnum.V01,
        max_length=10,
    )

    firewall_ip = models.GenericIPAddressField(
        blank=True,
        null=True,
        help_text=_('If defined will be used to filter request from other ips for this partner'),
    )

    alert_emails = EmailArrayField(
        default=list,
        help_text=_(
            'If defined will be used to alert recipients in case webhook communication failed'
        ),
        blank=True,
        null=True,
    )

    api_key = models.TextField(
        blank=True,
        null=True,
        help_text=_('Use for authorize requests to protected API by API key.'),
    )

    throttling_limit = models.IntegerField(
        default=200, null=False, help_text='Requests per minute (default value is 200)'
    )
