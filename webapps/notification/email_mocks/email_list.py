GLOBAL_EMAIL_LIST = {
    # scenarios_name : [template_name]
    'account_added': ['customer_welcome'],
    'password_reset': [
        'password_reset_customer',
        'password_reset_business',
    ],
    'access_rights': ['staffer_invitation', 'staffer_locked_access_alert'],
    'booking_changed': [
        'booking_reminder',
        'customer_booking_confirmation_awaiting',
        'customer_booking_confirmation',
        'business_booking_confirmation',
        'business_booking_cancel',
        'customer_booking_cancel',
        'business_booking_cancel_by_business',
        # 'customer_booking_confirmation_manual',
        'customer_booking_decline_manual',
        'customer_booking_rescheduled_manual',
        'business_booking_rescheduled',
        'customer_booking_reschedule_request',
        'customer_booking_rescheduled_auto',
        'business_booking_reschedule_request',
        'business_booking_reschedule_response',
        'customer_booksy_pay_before_appointment_started_reminder',
        'customer_booksy_pay_before_appointment_finished_reminder',
    ],
    'booking_finished': ['reminder', 'review_request'],
    'business_activity': ['inactivity_login'],
    'no_show_proposition': [
        'cancellation_fee_third_encouragement',
    ],
    'no_show_information': ['educational'],
    'review': ['review_added', 'review_reply_added'],
    'sms_limits': [
        'demo',
        'free',
        'paid100',
        'paid080',
    ],
    # other
    'examples': ['signature_test'],
    'pos_report': ['product_stock', 'commissions'],
    'sms_report': ['sms_report_tool'],
    'gdpr_export': [
        'business_export',
        'user_profile_customer_export',
    ],
    'umbrella_venue': ['change_details'],
    'feedback': ['feedback'],
    'marketpay': [
        'welcome',
        'payout_allowed',
        'payout_not_allowed',
        'refund_client',
        'marketpay_payout',
    ],
    'marketplace_payments': ['fail'],
    'receipt': ['receipt'],
    'invoice': ['invoice'],
    'register': ['register'],  # service/pos/registers.py:547, webapps/register/tasks.py:63
    # 'cs_mail': [
    #     'c2b_referral_report',
    # ],
    'business_statistics': ['report'],
    'agreements': [
        'disclosure_obligation_customer',
        'disclosure_obligation_business',
    ],
    'invitation': [
        'invitation_01',
    ],
    'report_update_repeating': [
        'report_update_repeating_conflicts',
        'report_update_repeating_no_conflicts',
    ],
    'external_business': ['report_external_business_importer'],
    'versum_initial_password_set': ['password_set'],
    # check mail_email_change.html webapps/user/models.py:174
    # 'pos_plans/pos_plan__{}.yaml'.format(settings.API_COUNTRY) service/pos/business_pos.py:233
    # 'pdf/product_stock_changes.html' service/pos/business_products.py:674
}
