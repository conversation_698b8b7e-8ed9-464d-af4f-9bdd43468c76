from lib.enums import IntEnum

from lib.enums import StrC<PERSON>icesEnum, StrEnum


# NotificationSchedule
# STATE_PENDING = 'PENDING'
# STATE_RECEIVED = 'RECEIVED'
# STATE_SUCCESS = 'SUCCESS'
# STATE_SKIPPED = 'SKIPPED'
# STATE_FAILURE = 'FAILURE'
# STATE_CANCELED = 'CANCELED'
# STATES = [
#     (STATE_PENDING, "Task is pending"),
#     (STATE_RECEIVED, "Task was received by a worker"),
#     (STATE_SUCCESS, "Task succeeded"),
#     (STATE_SKIPPED, "Task was skipped (nothing sent)"),
#     (STATE_FAILURE, "Task failed"),
#     (STATE_CANCELED, "Task was cancelled manually"),
# ]


class NotificationCategory(StrChoicesEnum):
    """Category of Notification

    AKA NotificationHistoryDocument.task_type
    Used to classify SMS

    Maybe it could be simpler...
    """

    ACCOUNT_ADDED = 'AA', 'Account added'
    ADMIN_BULK_FUND_TRANSFER = 'AB', 'Bulk fund transfer'
    ADMIN_MANUAL_NOTIFICATION = 'AM', 'Admin manual notification'
    B_LISTING_CLAIMED = 'BD', 'B Listing claimed'
    BOOKSY_BILLING = 'BB', 'Booksy Billing'
    BOOKSY_GIFT_CARD = 'GC', 'Booksy Gift Card'
    BLAST = 'BL', 'Blast'
    BOOKING_CHANGED = 'BC', 'Booking changed'
    BOOKING_FINISHED = 'BF', 'Booking finished'
    BOOKING_OTHER = 'BO', 'Booking other'
    BUSINESS_ACCOUNT_INVITE = 'BI', 'Business account invite'
    BUSINESS_ACTIVITY = 'BT', 'Business activity'
    BUSINESS_ADDED = 'BA', 'Business added'
    BOOKSY_AWARD = 'BZ', 'Best of Booksy Award'
    PROVIDER_CALENDAR_IMPORTER = 'CI', 'Provider calendar importer'
    CARD_VALIDATION = 'CV', 'Card Validation'
    CONSENT_FORM_SMS_REQUEST = 'CR', 'Consent Form SMS request'
    CUSTOMER_CHANGE = 'CC', 'Changes made by customer (e.g. account deletion request)'
    CUSTOMER_INVITATION = 'CX', 'Customer invitation'
    DIGITAL_FLYER = 'DF', 'Digital Flyer'
    DISCOUNT_GRANTED = 'DG', 'Discount granted'
    ECOMMERCE = 'EC', 'Ecommerce'
    EXTERNAL_BUSINESS_RECOMMENDATION = (
        'EB',
        'External Business recomendation',
    )
    FAMILY_AND_FRIENDS_MEMBER_INVITED_EMAIL = 'FE', 'Family and friends invitation email'
    FAMILY_AND_FRIENDS_MEMBER_INVITED_PUSH = 'FP', 'Family and friends invitation push'
    FAMILY_AND_FRIENDS_MEMBER_INVITED_SMS = 'FS', 'Family and friends invitation sms'
    GDPR_EXPORT = 'GE', 'GDPR Export'
    HH_PROMO_INCENTIVE = 'HH', 'HH Promo incentive'
    IMPORT_AND_INVITE = 'II', 'Import and invite'
    IMPORT_CUSTOMERS_REPORT = 'IC', 'Import Customers report'
    IMPORT_WAREHOUSE_COMMODITIES_REPORT = 'IW', 'Import warehouse commodities report'
    IMPORT_WHOLESALER_COMMODITIES_REPORT = 'WC', 'Import wholesaler commodities report'
    INVITATION = 'IT', 'Invitation'
    INVITATION_REMINDER = 'IR', 'Invitation reminder'
    KYC_SUCCESS = 'KS', '(effortless) KYC passed successfully'
    KYC_DATA_REQUIRED = 'KD', '(effortless) KYC - extra data required'
    LM_PROMO_INCENTIVE = 'LM', 'LM Promo incentive'
    MARKETPAY_PASSED_KYC = 'MP', 'Marketpay passed KYC'
    MARKETPAY_FIRST_FAILED_KYC = 'MF', 'Marketpay first failed KYC'
    MARKETPAY_SUBSEQUENT_FAILED_KYC = 'MS', 'Marketpay subsequent failed KYC'
    MARKETPAY_BLOCKED_PAYOUT = 'MB', 'Marketpay blocked payout'
    MARKETPAY_PAYOUT_REVERSED = 'MR', 'Marketpay payout reversed'
    MARKETPAY_MOBILE_PAYMENTS_REGAINED = 'MM', 'Marketpay mobile payments regained'
    MARKETPAY_PAYOUT = 'MA', 'Marketpay payout'
    MARKETPAY_BUSINESS_REFUND = 'ME', 'Marketpay business refund'
    MARKETPAY_CHARGEBACK = 'MC', 'Marketpay chargeback reversed'
    MARKETPAY_CHARGEBACK_REVERSED = 'ME', 'Marketpay chargeback'
    MARKETPAY_SECOND_CHARGEBACK = 'MD', 'Marketpay second chargeback'
    MARKETPAY_CANCELLATION_FEE_CHARGE = 'MG', 'Marketpay cancellation fee charge'
    MARKETPAY_PAYMENT_COMPLETED = 'PC', 'Marketpay payment completed'
    MARKETPAY_CUSTOMER_REFUND = 'MH', 'Marketpay customer refund'
    MESSAGE_BLAST = 'NB', 'Message Blast'
    NO_SHOW_INFORMATION = 'NS', 'No Show information'
    NO_SHOW_CONFIRMATION = 'NC', 'No Show confirmation'
    NO_SHOW_PROPOSITION = 'CF', 'No Show proposition'
    PASSWORD_RESET = 'PR', 'Password reset'
    PERFORMANCE = 'PF', 'Performance'
    POS = 'PO', 'POS'
    PROFILE_COMPLETENESS = 'PC', 'Profile completeness'
    REFERRAL = 'RF', 'Referral'
    REVIEW_REQUEST = 'RR', 'Review request'
    REVIEW_ADDED = 'RA', 'Review added'
    REVIEW_PROMPT = 'RP', 'Review prompt'
    REVIEW_REPLY_ADDED = 'RR', 'Review Reply added'
    SEARCH_VISIBILITY = 'SV', 'Search Visibility'
    SMS_BLAST = 'SB', 'SMS Blast'
    SMS_BOOKING_REACTIVATION = 'BR', 'SMS Booking reactivation'
    SMS_EXTERNAL_BUSINESS = 'SE', 'SMS External Business'
    SMS_GATE = 'SG', 'SMS Gate'
    SMS_REGISTRATION_CODE = 'SR', 'SMS Registration Code'
    SMS_TOOL = 'ST', 'SMS Tool'
    SMS_WAIT_LIST = 'WL', 'SMS Wait list'
    STATISTICS = 'SC', 'Statistics'
    TEMPLATE_TEST = 'TT', 'Template test'
    TRANSACTION = 'TR', 'Transaction'
    UPDATE_REPEATING_REPORT = 'UR', 'Update repeating report'
    VOUCHER_ORDER = 'VO', 'Voucher order'
    VOUCHER = 'VR', 'Voucher'
    RECOGNITION = 'RG', 'Recognition'
    PORTFOLIO_PHOTO_COMMENT_ADDED = 'PC', 'Portfolio photo comment added'
    PORTFOLIO_PHOTO_ADDED = 'PP', 'Portfolio photo added'
    BOOST = 'BS', 'Boost'
    ONBOARDING = 'OB', 'Onboarding'
    SWITCH_TO_PRO = 'SP', 'Switch to Pro'
    STRIPE_FIRST_TIME_KYC = 'X1', 'Stripe first time KYC'
    STRIPE_KYC_FAILED = 'X2', 'Stripe KYC failed'
    STRIPE_KYC_REQUIRES_UPDATE = 'X3', 'Stripe KYC requires update'
    STRIPE_REFUND_MERCHANT = 'X4', 'Stripe refund (notification to merchant)'
    STRIPE_REFUND_CUSTOMER = 'X5', 'Stripe refund (notification to customer)'
    STRIPE_PAYOUT_PAID = 'X6', 'Stripe payout paid'
    STRIPE_PAYOUT_FAILED = 'X7', 'Stripe payout failed'
    STRIPE_KYC_PREFILLED = 'X8', 'Stripe KYC prefilled'
    STRIPE_KYC_MIGRATION_FAILED = 'X9', 'Stripe KYC migration failed'
    ITERABLE = 'IL', 'Iterable'
    PARTNER_APP = 'PA', 'Partner app'
    SMS_MARKETING_CONSENT = 'MC', 'Business has checked on BCI\'s communication agreement'
    APPT_PREPAYMENT = 'AP', 'Prepayment for appointment'
    SMS_MARKETING_OPT_IN = 'MO', 'Ask customer for sms marketing consent'
    VOUCHER_MERCHANT_ACTIVATED = 'VC', 'Merchant Voucher purchase confirmation'
    BOOKSY_PAY = 'BP', 'Booksy pay'
    PAYOUT_METHOD_CHANGED = 'PM', 'Payout method changed'


class ScheduleState(StrChoicesEnum):
    PENDING = 'PENDING', 'Notification is pending'
    RECEIVED = 'RECEIVED', 'Notification was received by a worker'
    STARTED = 'STARTED', 'Notification execution has been started'
    SUCCESS = 'SUCCESS', 'Notification succeeded'
    SKIPPED = 'SKIPPED', 'Notification was skipped (nothing sent)'
    FAILURE = 'FAILURE', 'Notification failed'
    CANCELED = 'CANCELED', 'Task was cancelled manually'

    @classmethod
    def unready_states(cls):
        return frozenset({cls.PENDING, cls.RECEIVED, cls.STARTED})

    @classmethod
    def negative_states(cls):
        return frozenset({cls.SKIPPED, cls.FAILURE, cls.CANCELED})


class NotificationStatus(StrEnum):
    NEW = 'new'
    ACTIVE = 'active'
    ARCHIVED = 'archived'


class NotificationIcon(StrEnum):
    APPOINTMENT = 'APPOINTMENT'
    CANCELLATION = 'CANCELLATION'
    NO_SHOW = 'NO_SHOW'
    CONFIRM = 'CONFIRM'
    MOBILE_PAYMENT = 'MOBILE_PAYMENT'
    MOBILE_PAYMENT_PAYOUT = 'MOBILE_PAYMENT_PAYOUT'
    MOBILE_PAYMENT_WARNING = 'MOBILE_PAYMENT_WARNING'
    REFERRAL = 'REFERRAL'
    REVIEW_GOOD = 'REVIEW_GOOD'
    REVIEW_BAD = 'REVIEW_BAD'
    COMMENT = 'COMMENT'
    SUGGESTION = 'SUGGESTION'
    RECOGNITION = 'RECOGNITION'
    TROPHY = 'TROPHY'
    IMPORT_INVITE = 'IMPORT_INVITE'
    BOOKSY_CARD_READER = 'BOOKSY_CARD_READER'
    ITERABLE = 'ITERABLE'
    BOOKSY_GIFT_CARD = 'BOOKSY_GIFT_CARD'


class NotificationGroup(IntEnum):
    NOTIFICATION = 1
    RECOGNITION = 2
    BOOST = 3
    PERFORMANCE = 4
    PAYMENT = 5
    REVIEW = 6
    COMMENT = 7
    APPOINTMENTS = 8
    APPOINTMENT = 9
    MESSAGE_BLAST = 10
    REVIEWS = 11
    PREPAYMENTS = 12
    PROFILE = 13
    REFERRAL = 14
    GIFT_CARDS = 16
    BOOKSY_CARD_READER = 17
    ITERABLE = 18
    KYC = 19
    ECOMMERCE = 20


class NotificationSize(IntEnum):
    NORMAL = 0
    BIG = 1

    @classmethod
    def get_id_name(cls):
        return {member.value: member.name for member in list(cls.__members__.values())}


class RecipientType(StrEnum):
    CUSTOMER = 'C'
    BUSINESS = 'B'


class ColorType(StrEnum):
    SUCCESS = 'SUCCESS'
    WARNING = 'WARNING'
    ALERT = 'ALERT'


class NotificationSendStatus(StrChoicesEnum):
    GATEWAY_SUCCESS = 'S', 'Gateway success'
    GATEWAY_ERROR = 'E', 'Gateway error'
    WEBHOOK_SUCCESS = 'C', 'Webhook success'
    WEBHOOK_ERROR = 'F', 'Webhook error'
    NO_INFORMATION = '-', 'No information'

    @classmethod
    def success_statuses(cls):
        return {
            cls.GATEWAY_SUCCESS,
            cls.WEBHOOK_SUCCESS,
        }


class NotificationTarget(StrEnum):
    BOOKING = 'booking'
    BOOKING_TOOLS = 'booking_tools'
    REVIEW = 'review'
    PORTFOLIO_PHOTO = 'portfolio_photo'
    AGENDA = 'agenda'
    SERVICES = 'services'
    CALENDAR = 'calendar'
    CUSTOMER_CARD = 'customer_card'
    PROFILE_STATISTICS = 'profile_statistics'
    BUSINESS_PROFILE_REVIEWS = 'business_profile_reviews'
    PORTFOLIO = 'portfolio'
    BOOST_DASHBOARD = 'boost_dashboard'
    BOOST_BAN = 'boost_ban'
    BLAST_DASHBOARD = 'blast_dashboard'
    BLAST_NEWOPENINGS = 'blast_newopenings'
    BLAST_TIMEOFF = 'blast_timeoff'
    BLAST_NEW_SERVICES = 'blast_new_services'
    BLAST_CHANGE = 'blast_change'
    BLAST_TREAT_YOURSELF = 'blast_treat_yourself'
    BLAST_AUTOMATED_CHECK = 'blast_automated_check'
    BLAST_ITSTIME = 'blast_itstime'
    BLAST_MISS = 'blast_miss'
    BLAST_BIRTHDAY = 'blast_birthday'
    REFERRAL = 'referral'
    REFERRAL_KYC = 'referral_kyc'
    REVIEW_LIST = 'review_list'
    SOLD_GIFT_CARDS = 'sold_gift_cards'
    NO_SHOW_PROTECTION = 'no_show_protection'
    PROFILE_PHOTOS = 'profile_photos'
    GIFT_CARDS = 'gift_cards'
    HAPPY_HOURS = 'happy_hours'
    LAST_MINUTE = 'last_minute'
    PREPAYMENT_POPUP = 'prepayment_popup'
    KYC_DASHBOARD = 'kyc_dashboard'
    KYC_DASHBOARD_STRIPE = 'kyc_dashboard_stripe'
    PAYOUT_DASHBOARD = 'payout_dashboard'
    UPGRADE = 'upgrade'
    SUBSCRIPTIONS = 'subscriptions'
    STRIPE_DASHBOARD = 'stripe_dashboard'
    TRANSACTION = 'transaction'
    TRANSACTION_CALL_FOR_PAYMENT = 'transaction.call_for_payment'
    REVIEWS_PROMPT = 'reviews_prompt'
    DEEPLINK = 'deeplink'
    BOOKSY_AWARDS = 'booksy_awards'
    PARTNER_APP_SETUP = 'partner_app_setup'
    PARTNER_APP_DASHBOARD = 'partner_app_dashboard'
    PARTNER_APP_ACTION = 'partner_app_action'
    SHOW_APPT_DETAIL_FOR_PREPAYMENT = 'show_appointment_detail_for_prepayment'
    PAYMENTS_BALANCE = 'payments_balance'
    MOBILE_PAYMENTS = 'mobile_payments'
    CARD_MANAGEMENT = 'card_management'
    CALENDAR_IMPORTER = 'calendar_importer'
    MOBILE_PAYMENTS_PROMO = 'mobile_payments_promo'
    PAYMENTS_BALANCE_PENDING_KYC = 'payments_balance_pending_kyc'
    PAYMENTS_BALANCE_SUBSCRIBE = 'payments_balance_subscribe'
    PAYMENTS_BALANCE_ENABLE = 'payments_balance_enable'
    NO_SHOW_CONFIRMATION = 'no_show_confirmation'
    COVER_PHOTO = 'cover_photo'
    BUSINESS_HOURS = 'business_hours'
    SHOW_APPT_DETAIL_BOOKSY_PAY = 'show_appointment_detail_for_booksy_pay'
    ECOMMERCE = 'ecommerce'
    TIPPING_AFTER_APPT_EXPERIMENT = 'tipping_after_appt_experiment'
    BOOKSY_GIFT_CARD_CONFIGURATION = 'booksy_gift_card_configuration'


class NotificationService(StrChoicesEnum):
    # SMS
    SMSAPI = 'S', 'SMSAPI'
    ROUTE = 'R', 'Route'
    TWILIO = 'T', 'Twilio'
    EVOX = 'E', 'EVOX'
    DEVEL = 'D', 'Devel'
    TELNYX = 'X', 'Telnyx'
    VONAGE = 'V', 'Vonage'

    # Email
    ITERABLE = 'I', 'Iterable'
    MANDRILL = 'M', 'Mandrill'


class DeeplinkFeature(StrEnum):
    BLAST = 'blast'
    KYC = 'kyc'
    MERCHANT_CUSTOMER_INVITE = 'merchant_customer_invite'
    INVITE_NOT_FORCED_APP_DOWNLOAD = 'invite_not_forced_app_download'
    INVITE_NOT_FORCED_APP_REDIRECT = 'invite_not_forced_app_redirect'
    SHOW_BUSINESSES = 'show_businesses'
    WAITLIST = 'waitlist'
    WAITLIST_JOIN = 'waitlist_join'
    CONSENT_FORM_SMS_REQUEST = 'consent_form_sms_request'
    CONSENT_FORM = 'consent_form'
    VERSUM_OFFLINE_BOOKING = 'versum_offline_booking'
    REVIEW = 'review'
    STAFFER_CUSTOMER_INVITE = 'staffer_customer_invite'
    BUSINESS_QR_CODE = 'business_qr_code'
    BUSINESS_QR_CODE_APP_REDIRECT = 'business_qr_code_app_redirect'
    MOBILE_PAYMENT = 'mobile_payment'
    PREPAYMENT = 'prepayment'
    CARD_MANAGEMENT = 'card_management'
    SMS_MARKETING_OPT_IN = 'sms_marketing_opt_in'
    SMS_NO_SHOW_CONFIRMATION = 'sms_no_show_confirmation'
    SHARE_PROFILE = 'profile_share_from_profile'
    SOCIAL_POST_CREATOR = 'social_post_creator'
    SHARE_DEEPLINK = 'profile_share_from_deeplink'
    PROFILE_SETUP = 'profile_share_from_profile_setup'
    SHARE_PROFILE_COMPLETENESS = 'profile_share_from_profile_completeness'
    SHARE_TARGET_TYPE = 'profile_share_from_target_type'
    SHARE_BOOST = 'profile_share_from_boost'
    SHARE_PROFILE_INVITE = 'invite_from_share_profile'
    SHARE_PROFILE_REGISTRATION = 'profile_share_from_registration'


class BlastSendType(StrChoicesEnum):
    AUTOMATIC = 'A', 'Sent automatically'
    MANUAL = 'M', 'Sent manually'


class DeeplinkCampaignEnum(StrEnum):
    PAYMENT_LINKS = 'payment_links_experiment'


class DeeplinkCampaign(StrEnum):
    PAYMENT_LINKS = 'Payment Links'
    CARD_INVALID = 'Card Invalid'
    CARD_EXPIRED = 'Card Expired'
    CARD_SOON_EXPIRED = 'Card Soon Expired'
    CARD_SOON_EXPIRED_2 = 'Card Soon Expired 2'
    CARD_SOON_EXPIRED_3 = 'Card Soon Expired 3'


# Moved here due to errors in monkeypatching order while importing anything from lib.booksy_sms
class ProfileKey(StrEnum):
    MARKETING = 'marketing'
    INVITATION = 'invitation'
    SYSTEM = 'system'
    REGISTRATION_CODE = 'registration_code'


class CustomerBookingReminderType(StrEnum):
    DEFAULT = 'default'
    SPECIFIC_DAYTIME = 'specific_daytime'
    HOURS_BEFORE = 'hours_before'


class CustomerBookingReminderTaskTypes(StrEnum):
    DEFAULT = 'customer_booking_reminder'
    MORNING_8_AM = f'customer_booking_reminder_{CustomerBookingReminderType.SPECIFIC_DAYTIME.value}'
    TWO_HOURS_BEFORE = f'customer_booking_reminder_{CustomerBookingReminderType.HOURS_BEFORE.value}'
