from django.utils.translation import gettext as _

from webapps.business.models import Business
from webapps.notification.base import BaseNotification, PushTarget, PopupTemplate
from webapps.notification.channels import PushChannel, PopupChannel
from webapps.notification.enums import (
    NotificationCategory,
    NotificationGroup,
    NotificationIcon,
    NotificationSize,
    NotificationTarget,
)
from webapps.notification.recipients import SystemSender, Managers, OwnerOnly


class StripePappersProcessingKYCFinished(BaseNotification):
    category = NotificationCategory.STRIPE_KYC_PREFILLED
    sender = SystemSender
    recipients = (Managers,)
    channels = (PushChannel, PopupChannel)
    popup_template = PopupTemplate(
        icon=NotificationIcon.MOBILE_PAYMENT,
        relevance=1,
        group=NotificationGroup.NOTIFICATION,
        size=NotificationSize.NORMAL,
        messages=[
            _('Processing data complete'),
            _('Please complete the Data Verification procedure and add a payout account number.'),
        ],
    )

    def __init__(self, business: Business, **parameters):
        super().__init__(business, **parameters)
        self.business = business

    @property
    def identity(self) -> str:
        return f'{self.notif_type},{self.business.id}'

    def get_target(self) -> PushTarget:
        return PushTarget(
            type=NotificationTarget.KYC_DASHBOARD_STRIPE,
            title=_('Processing data complete'),
        )

    def get_push_content(self) -> str:
        return _(
            'Please complete the Data Verification procedure and add a payout account number.',
        )


class PLStripeKYCMigrationFailed(BaseNotification):
    category = NotificationCategory.STRIPE_KYC_MIGRATION_FAILED
    sender = SystemSender
    recipients = (OwnerOnly,)
    channels = (PushChannel, PopupChannel)
    popup_template = PopupTemplate(
        icon=NotificationIcon.MOBILE_PAYMENT,
        relevance=1,
        group=NotificationGroup.NOTIFICATION,
        size=NotificationSize.NORMAL,
        messages=[
            _('Important! Fill in the required data'),
            _('To continue using mobile payments please add missing data on Stripe platform.'),
        ],
    )

    def __init__(self, business: Business, **parameters):
        super().__init__(business, **parameters)
        self.business = business

    @property
    def identity(self) -> str:
        return f'{self.notif_type},{self.business.id}'

    def get_target(self) -> PushTarget:
        return PushTarget(
            type=NotificationTarget.KYC_DASHBOARD_STRIPE,
            title=_('Important! Fill in the required data'),
        )

    def get_push_content(self) -> str:
        return _(
            'To continue using mobile payments please add missing data on Stripe platform.',
        )
