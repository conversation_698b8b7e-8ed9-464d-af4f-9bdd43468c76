import typing
from datetime import datetime, time, timedelta
from unittest import TestCase

import pytest
from django.conf import settings
from freezegun import freeze_time
from mock.mock import patch
from pytz import UTC

from lib.feature_flag.enums import ExperimentVariants
from lib.feature_flag.feature.notification import AdditionalBookingReminderExperiment
from lib.tests.utils import override_eppo_feature_flag
from lib.tools import tznow
from webapps.booking.baker_recipes import appointment_recipe, booking_recipe
from webapps.booking.models import Appointment
from webapps.business.baker_recipes import bci_recipe, business_recipe
from webapps.notification.enums import CustomerBookingReminderTaskTypes, CustomerBookingReminderType
from webapps.notification.models import NotificationSchedule
from webapps.notification.scenarios import BookingChangedScenario, start_scenario
from webapps.notification.scenarios.scenarios_booking import BookingChangedScenario as BCS
from webapps.notification.tasks import execute_task


@pytest.mark.parametrize(
    'task_type, expected_task_type',
    [
        (
            None,
            'BC',
        ),
        (
            'BP',
            'BP',
        ),
    ],
)
def test_get_history_data__task_type(
    task_type,
    expected_task_type,
):
    history_data = BookingChangedScenario.get_history_data(
        params={'test': 1},
        task_id='booking_changed:test:1',
        task_type=task_type,
    )
    assert history_data['task_type'] == expected_task_type


class TestBaseCustomerBookingReminder(TestCase):
    def setUp(self):
        super().setUp()
        self.business = business_recipe.make(time_zone_name='America/New_York')

    def _get_reminder_type(self, experiment_variant: typing.Optional[ExperimentVariants] = None):
        types = {
            ExperimentVariants.VARIANT_A: CustomerBookingReminderType.HOURS_BEFORE,
            ExperimentVariants.VARIANT_B: CustomerBookingReminderType.SPECIFIC_DAYTIME,
        }
        return types.get(experiment_variant, CustomerBookingReminderTaskTypes.DEFAULT)

    def _get_reminder_task_type(
        self, experiment_variant: typing.Optional[ExperimentVariants] = None
    ):
        task_types = {
            ExperimentVariants.VARIANT_A: CustomerBookingReminderTaskTypes.TWO_HOURS_BEFORE,
            ExperimentVariants.VARIANT_B: CustomerBookingReminderTaskTypes.MORNING_8_AM,
        }
        return task_types.get(experiment_variant, CustomerBookingReminderTaskTypes.DEFAULT)

    def _get_reminder_task_id(self, appointment_id, task_type=None):
        task_type = (
            self.reminder_task_type
            if hasattr(self, 'reminder_task_type')
            else CustomerBookingReminderTaskTypes.DEFAULT
        )
        return f'{BCS.SCENARIO_NAME}:{task_type}:appointment_id={appointment_id}'

    def _prepare_appointment(self, booked_from):
        bci = bci_recipe.make(business=self.business)
        appointment = appointment_recipe.make(
            business=self.business,
            booked_from=booked_from,
            booked_till=booked_from + timedelta(minutes=30),
            status=Appointment.STATUS.ACCEPTED,
            booked_for=bci,
        )
        booking = booking_recipe.prepare(
            appointment=appointment,
            booked_from=booked_from,
            booked_till=booked_from + timedelta(minutes=30),
        )
        booking.save(override=True)

        return appointment


@pytest.mark.freeze_time(datetime(2024, 1, 1, 16, tzinfo=UTC))
@pytest.mark.django_db
class TestCustomerBookingReminder(TestBaseCustomerBookingReminder):
    # The reminder is scheduled 24 hours (for the default business settings) before the appointment
    # date. However, there is additional, more complex logic that determines when to schedule
    # a reminder and when not to. These tests verify only the simplest scenarios.

    # The default minimum limit in hours between the time of the event (e.g. appointment creation)
    # and the date of the appointment. Below this limit, the reminder is not created or updated.
    DEFAULT_LIMIT = settings.DEFAULT_REMINDER_HOURS - BCS.MIN_REMINDER_HOURS

    def test_create_appointment_below_the_default_limit(self):
        # one second below the limit
        booked_from = tznow() + timedelta(hours=self.DEFAULT_LIMIT) - timedelta(seconds=1)
        appointment = self._prepare_appointment(booked_from=booked_from)

        start_scenario(BCS, appointment=appointment, action=BCS.CUSTOMER_CREATED)

        # reminder is not scheduled
        assert not NotificationSchedule.objects.filter(
            task_id=self._get_reminder_task_id(appointment.id)
        ).exists()

    def test_create_appointment_that_meets_limit(self):
        booked_from = tznow() + timedelta(hours=self.DEFAULT_LIMIT)
        appointment = self._prepare_appointment(booked_from=booked_from)

        start_scenario(BCS, appointment=appointment, action=BCS.CUSTOMER_CREATED)

        # reminder is scheduled to be sent 24 hours before the appointment date
        notification = NotificationSchedule.objects.get(
            task_id=self._get_reminder_task_id(appointment.id)
        )
        expected_scheduled_time = booked_from - timedelta(hours=settings.DEFAULT_REMINDER_HOURS)
        assert notification.scheduled == expected_scheduled_time


class TestBaseAdditionalCustomerBookingReminder(TestBaseCustomerBookingReminder):
    def _test_create_appointment_below_the_time_limit(self):
        # if the appointment was scheduled less than 6 hours before the appointment date, a reminder
        # is not needed.

        booked_from = tznow() + timedelta(hours=BCS.MIN_REMINDER_HOURS) - timedelta(seconds=1)
        appointment = self._prepare_appointment(booked_from=booked_from)

        start_scenario(BCS, appointment=appointment, action=BCS.CUSTOMER_CREATED)

        assert not NotificationSchedule.objects.filter(
            task_id=self._get_reminder_task_id(appointment.id)
        ).exists()

    def _assert_reminder_sent(self, notification):
        with (
            patch(
                'webapps.notification.scenarios.scenarios_booking_mixin.BookingMixin'
                '.generic_booking_communication_sender'
            ) as mock_sender,
            freeze_time(notification.scheduled),
        ):
            execute_task(notification)

        mock_sender.assert_called_once()

    def _assert_reminder_not_sent(self, notification):
        with (
            patch(
                'webapps.notification.scenarios.scenarios_booking_mixin.BookingMixin'
                '.generic_booking_communication_sender'
            ) as mock_sender,
            freeze_time(notification.scheduled),
        ):
            execute_task(notification)

        mock_sender.assert_not_called()


@pytest.mark.freeze_time(datetime(2024, 1, 1, 16, tzinfo=UTC))
@pytest.mark.django_db
class TestAdditionalCustomerBookingReminder(TestBaseCustomerBookingReminder):
    def _test_no_additional_reminder(self):
        # When the experiment is disabled and for control variant, there should be no verification
        # whether to schedule an additional reminder or not, regardless of the time booked_from.

        # appointment date that meets the requirements for both experimental variants
        booked_from = tznow() + timedelta(days=3)
        appointment = self._prepare_appointment(booked_from=booked_from)

        with patch(
            'webapps.notification.scenarios.scenarios_booking.BookingChangedScenario'
            '.is_booking_reminder_needed'
        ) as mock_is_booking_reminder_needed:
            start_scenario(BCS, appointment=appointment, action=BCS.CUSTOMER_CREATED)

        # only verification for the default reminder
        mock_is_booking_reminder_needed.assert_called_once_with(appointment, adding=True)

    @override_eppo_feature_flag({AdditionalBookingReminderExperiment.flag_name: None})
    def test_experiment_disabled(self):
        self._test_no_additional_reminder()

    @override_eppo_feature_flag(
        {AdditionalBookingReminderExperiment.flag_name: ExperimentVariants.CONTROL}
    )
    def test_control_variant(self):
        self._test_no_additional_reminder()

    def test_skip_because_status_is_not_accepted(self):
        booked_from = tznow() + timedelta(hours=BCS.MIN_REMINDER_HOURS)
        appointment = self._prepare_appointment(booked_from=booked_from)

        self.assertFalse(BCS.should_skip_additional_customer_booking_reminder(appointment))

        appointment.status = Appointment.STATUS.CANCELED
        appointment.save()
        self.assertTrue(BCS.should_skip_additional_customer_booking_reminder(appointment))

    def test_skip_because_booked_for_is_none(self):
        booked_from = tznow() + timedelta(hours=BCS.MIN_REMINDER_HOURS)
        appointment = self._prepare_appointment(booked_from=booked_from)

        self.assertFalse(BCS.should_skip_additional_customer_booking_reminder(appointment))

        appointment.booked_for = None
        appointment.save()
        self.assertTrue(BCS.should_skip_additional_customer_booking_reminder(appointment))

    def test_skip_because_is_too_late(self):
        booked_from = tznow() + timedelta(hours=BCS.MIN_HOURS_BEFORE_ADDITIONAL_REMINDER)
        appointment = self._prepare_appointment(booked_from=booked_from)

        self.assertFalse(BCS.should_skip_additional_customer_booking_reminder(appointment))

        _timedelta = timedelta(seconds=1)
        appointment.booked_from = appointment.booked_from - _timedelta
        appointment.booked_till = appointment.booked_till - _timedelta
        appointment.save()
        self.assertTrue(BCS.should_skip_additional_customer_booking_reminder(appointment))

    def test_skip_because_is_too_early(self):
        # the booked_from datetime is not important for this test
        booked_from = tznow() + timedelta(hours=BCS.MIN_HOURS_BEFORE_ADDITIONAL_REMINDER)
        appointment = self._prepare_appointment(booked_from=booked_from)

        _7_am = datetime(2024, 1, 1, 7, tzinfo=self.business.get_timezone())
        with freeze_time(_7_am):
            self.assertFalse(BCS.should_skip_additional_customer_booking_reminder(appointment))

        with freeze_time(_7_am - timedelta(seconds=1)):
            self.assertTrue(BCS.should_skip_additional_customer_booking_reminder(appointment))


@pytest.mark.freeze_time(datetime(2024, 1, 1, 16, tzinfo=UTC))
@pytest.mark.django_db
class TestAdditionalCustomerBookingReminderVariantA(TestBaseAdditionalCustomerBookingReminder):
    EXPERIMENT_VARIANT = ExperimentVariants.VARIANT_A
    TIME_BEFORE_ADDITIONAL_REMINDER = timedelta(hours=BCS.HOURS_BEFORE_ADDITIONAL_REMINDER)

    @property
    def reminder_type(self):
        return self._get_reminder_type(self.EXPERIMENT_VARIANT)

    @property
    def reminder_task_type(self):
        return self._get_reminder_task_type(self.EXPERIMENT_VARIANT)

    def test_get_reminder_datetime(self):
        booked_from = tznow()
        _datetime = BCS.get_reminder_datetime(
            appointment=self._prepare_appointment(booked_from),
            reminder_type=self.reminder_type,
        )
        self.assertEqual(
            booked_from - self.TIME_BEFORE_ADDITIONAL_REMINDER,
            _datetime,
        )

    def test_create_appointment_below_the_time_limit(self):
        with override_eppo_feature_flag(
            {AdditionalBookingReminderExperiment.flag_name: self.EXPERIMENT_VARIANT}
        ):
            self._test_create_appointment_below_the_time_limit()

    def test_create_appointment_that_meets_the_requirements(self):
        # appointment is created without any further changes

        # appointment date meets the limit
        booked_from = tznow() + timedelta(hours=BCS.MIN_REMINDER_HOURS)
        appointment = self._prepare_appointment(booked_from=booked_from)

        # create the reminder
        with override_eppo_feature_flag(
            {AdditionalBookingReminderExperiment.flag_name: self.EXPERIMENT_VARIANT}
        ):
            start_scenario(BCS, appointment=appointment, action=BCS.CUSTOMER_CREATED)

        notification = NotificationSchedule.objects.get(
            task_id=self._get_reminder_task_id(appointment.id)
        )

        # reminder is scheduled to be sent 2 hours before the appointment date
        assert notification.scheduled == booked_from - self.TIME_BEFORE_ADDITIONAL_REMINDER

        # send the reminder
        self._assert_reminder_sent(notification)

    def test_create_and_move_appointment_to_the_future(self):
        # appointment date meets the limit
        booked_from = tznow() + timedelta(hours=BCS.MIN_REMINDER_HOURS)
        appointment = self._prepare_appointment(booked_from=booked_from)

        # create the reminder
        with override_eppo_feature_flag(
            {AdditionalBookingReminderExperiment.flag_name: self.EXPERIMENT_VARIANT}
        ):
            start_scenario(BCS, appointment=appointment, action=BCS.CUSTOMER_CREATED)

        notification = NotificationSchedule.objects.get(
            task_id=self._get_reminder_task_id(appointment.id)
        )

        # reminder is scheduled to be sent 2 hours before the appointment date
        expected_initial_scheduled_time = booked_from - self.TIME_BEFORE_ADDITIONAL_REMINDER
        assert notification.scheduled == expected_initial_scheduled_time

        # move the appointment to the future
        _timedelta = timedelta(days=2)
        appointment.booked_from = appointment.booked_from + _timedelta
        appointment.booked_till = appointment.booked_till + _timedelta
        appointment.save()

        with override_eppo_feature_flag(
            {AdditionalBookingReminderExperiment.flag_name: self.EXPERIMENT_VARIANT}
        ):
            start_scenario(BCS, appointment=appointment, action=BCS.CUSTOMER_BOOKING_RESCHEDULED)

        # reminder is rescheduled
        notification.refresh_from_db()
        assert notification.scheduled == expected_initial_scheduled_time + _timedelta

        # send the reminder
        self._assert_reminder_sent(notification)

    def test_create_and_move_appointment_to_the_past(self):
        # appointment date meets the limit
        booked_from = tznow() + timedelta(hours=BCS.MIN_REMINDER_HOURS)
        appointment = self._prepare_appointment(booked_from=booked_from)

        # create the reminder
        with override_eppo_feature_flag(
            {AdditionalBookingReminderExperiment.flag_name: self.EXPERIMENT_VARIANT}
        ):
            start_scenario(BCS, appointment=appointment, action=BCS.CUSTOMER_CREATED)

        notification = NotificationSchedule.objects.get(
            task_id=self._get_reminder_task_id(appointment.id)
        )

        # reminder is scheduled to be sent 2 hours before the appointment date
        expected_initial_scheduled_time = booked_from - self.TIME_BEFORE_ADDITIONAL_REMINDER
        assert notification.scheduled == expected_initial_scheduled_time

        # move the appointment to the past
        _timedelta = timedelta(hours=BCS.MIN_HOURS_BEFORE_ADDITIONAL_REMINDER, seconds=1)
        appointment.booked_from = appointment.booked_from - _timedelta
        appointment.booked_till = appointment.booked_till - _timedelta
        appointment.save()

        with override_eppo_feature_flag(
            {AdditionalBookingReminderExperiment.flag_name: self.EXPERIMENT_VARIANT}
        ):
            start_scenario(BCS, appointment=appointment, action=BCS.CUSTOMER_BOOKING_RESCHEDULED)

        #  reminder is not rescheduled because the requirements were not met
        notification.refresh_from_db()
        assert notification.scheduled == expected_initial_scheduled_time

        # skip the reminder because the time remaining for the appointment is less than 2 hours
        self._assert_reminder_not_sent(notification)

    def test_create_and_cancel_appointment(self):
        # This scenario is just an example, but this behavior is more general. The reminder has to
        # be sent only if the appointment status is accepted

        # appointment date meets the limit
        booked_from = tznow() + timedelta(hours=BCS.MIN_REMINDER_HOURS)
        appointment = self._prepare_appointment(booked_from=booked_from)

        # create the reminder
        with override_eppo_feature_flag(
            {AdditionalBookingReminderExperiment.flag_name: self.EXPERIMENT_VARIANT}
        ):
            start_scenario(BCS, appointment=appointment, action=BCS.CUSTOMER_CREATED)

        notification = NotificationSchedule.objects.get(
            task_id=self._get_reminder_task_id(appointment.id)
        )

        # reminder is scheduled to be sent 2 hours before the appointment date
        expected_initial_scheduled_time = booked_from - self.TIME_BEFORE_ADDITIONAL_REMINDER
        assert notification.scheduled == expected_initial_scheduled_time

        # cancel the appointment
        appointment.status = Appointment.STATUS.CANCELED
        appointment.save()

        with override_eppo_feature_flag(
            {AdditionalBookingReminderExperiment.flag_name: self.EXPERIMENT_VARIANT}
        ):
            start_scenario(BCS, appointment=appointment, action=BCS.CUSTOMER_CANCEL)

        # reminder still exists and is not rescheduled
        notification.refresh_from_db()
        assert notification.scheduled == expected_initial_scheduled_time

        # skip the reminder, because the appointment is canceled
        self._assert_reminder_not_sent(notification)

    def test_create_appointment_that_meets_the_requirements_but_skip_reminder_because_is_7_am(self):
        _datetime = datetime(2024, 1, 1, 7, 59, tzinfo=UTC)
        with freeze_time(_datetime):
            # appointment date meets the limit
            booked_from = tznow() + timedelta(hours=BCS.MIN_REMINDER_HOURS)
            appointment = self._prepare_appointment(booked_from=booked_from)

            # create the reminder
            with override_eppo_feature_flag(
                {AdditionalBookingReminderExperiment.flag_name: self.EXPERIMENT_VARIANT}
            ):
                start_scenario(BCS, appointment=appointment, action=BCS.CUSTOMER_CREATED)

            notification = NotificationSchedule.objects.get(
                task_id=self._get_reminder_task_id(appointment.id)
            )

            # reminder is scheduled to be sent 2 hours before the appointment date
            expected_initial_scheduled_time = booked_from - self.TIME_BEFORE_ADDITIONAL_REMINDER
            assert notification.scheduled == expected_initial_scheduled_time

        # do not send reminder, because is before 7 am local time
        self._assert_reminder_not_sent(notification)


@pytest.mark.freeze_time(datetime(2024, 1, 1, 9, tzinfo=UTC))
@pytest.mark.django_db
class TestAdditionalCustomerBookingReminderVariantB(TestBaseAdditionalCustomerBookingReminder):
    EXPERIMENT_VARIANT = ExperimentVariants.VARIANT_B
    TIME_ADDITIONAL_REMINDER_SENT = time(BCS.HOUR_ADDITIONAL_REMINDER_SENT)

    def setUp(self):
        super().setUp()
        self.timezone = self.business.get_timezone()

    @property
    def reminder_type(self):
        return self._get_reminder_type(self.EXPERIMENT_VARIANT)

    @property
    def reminder_task_type(self):
        return self._get_reminder_task_type(self.EXPERIMENT_VARIANT)

    def _get_expected_scheduled_local_time(self, booked_from):
        return datetime.combine(
            booked_from.astimezone(self.timezone).date(),
            self.TIME_ADDITIONAL_REMINDER_SENT,
            tzinfo=self.timezone,
        )

    @patch('random.randint', return_value=0)
    def test_get_reminder_datetime__ok(self, _):
        booked_from = tznow() + timedelta(days=1)
        _datetime = BCS.get_reminder_datetime(
            appointment=self._prepare_appointment(booked_from),
            reminder_type=self.reminder_type,
        )
        self.assertEqual(
            self._get_expected_scheduled_local_time(booked_from),
            _datetime.astimezone(self.timezone),
        )

    def test_get_reminder_datetime__appointment_day(self):
        booked_from = tznow()
        _datetime = BCS.get_reminder_datetime(
            appointment=self._prepare_appointment(booked_from),
            reminder_type=self.reminder_type,
        )
        self.assertIsNone(_datetime)

    def test_create_appointment_below_the_time_limit(self):
        with override_eppo_feature_flag(
            {AdditionalBookingReminderExperiment.flag_name: self.EXPERIMENT_VARIANT}
        ):
            self._test_create_appointment_below_the_time_limit()

    @patch('random.randint', return_value=0)
    def test_create_appointment_that_meets_the_requirements(self, _):
        # appointment is created without any further changes

        # appointment date meets the limit
        booked_from = tznow() + timedelta(days=1, hours=BCS.MIN_REMINDER_HOURS)
        appointment = self._prepare_appointment(booked_from=booked_from)

        # create the reminder
        with override_eppo_feature_flag(
            {AdditionalBookingReminderExperiment.flag_name: self.EXPERIMENT_VARIANT}
        ):
            start_scenario(BCS, appointment=appointment, action=BCS.CUSTOMER_CREATED)

        notification = NotificationSchedule.objects.get(
            task_id=self._get_reminder_task_id(appointment.id)
        )

        # reminder is scheduled to be sent at 8 am local time of the appointment date
        assert notification.scheduled.astimezone(
            self.timezone
        ) == self._get_expected_scheduled_local_time(booked_from)

        # send the reminder
        self._assert_reminder_sent(notification)

    @patch('random.randint', return_value=0)
    def test_create_and_move_appointment_to_the_future(self, _):
        # appointment date meets the limit
        booked_from = tznow() + timedelta(days=1, hours=BCS.MIN_REMINDER_HOURS)
        appointment = self._prepare_appointment(booked_from=booked_from)

        # create the reminder
        with override_eppo_feature_flag(
            {AdditionalBookingReminderExperiment.flag_name: self.EXPERIMENT_VARIANT}
        ):
            start_scenario(BCS, appointment=appointment, action=BCS.CUSTOMER_CREATED)

        notification = NotificationSchedule.objects.get(
            task_id=self._get_reminder_task_id(appointment.id)
        )

        # reminder is scheduled to be sent at 8 am local time of the appointment date
        expected_initial_scheduled_time = self._get_expected_scheduled_local_time(booked_from)
        assert notification.scheduled.astimezone(self.timezone) == expected_initial_scheduled_time

        # move the appointment to the future
        _timedelta = timedelta(days=2)
        appointment.booked_from = appointment.booked_from + _timedelta
        appointment.booked_till = appointment.booked_till + _timedelta
        appointment.save()

        with override_eppo_feature_flag(
            {AdditionalBookingReminderExperiment.flag_name: self.EXPERIMENT_VARIANT}
        ):
            start_scenario(BCS, appointment=appointment, action=BCS.CUSTOMER_BOOKING_RESCHEDULED)

        # reminder is rescheduled
        notification.refresh_from_db()
        assert (
            notification.scheduled.astimezone(self.timezone)
            == expected_initial_scheduled_time + _timedelta
        )

        # send the reminder
        self._assert_reminder_sent(notification)

    @patch('random.randint', return_value=0)
    def test_create_and_move_appointment_to_the_past(self, _):
        # appointment date meets the limit
        booked_from = tznow() + timedelta(days=1, hours=BCS.MIN_REMINDER_HOURS)
        appointment = self._prepare_appointment(booked_from=booked_from)

        # create the reminder
        with override_eppo_feature_flag(
            {AdditionalBookingReminderExperiment.flag_name: self.EXPERIMENT_VARIANT}
        ):
            start_scenario(BCS, appointment=appointment, action=BCS.CUSTOMER_CREATED)

        notification = NotificationSchedule.objects.get(
            task_id=self._get_reminder_task_id(appointment.id)
        )

        # reminder is scheduled to be sent at 8 am local time of the appointment date
        expected_initial_scheduled_time = self._get_expected_scheduled_local_time(booked_from)
        assert notification.scheduled.astimezone(self.timezone) == expected_initial_scheduled_time

        # move the appointment to the past
        _timedelta = timedelta(hours=BCS.MIN_HOURS_BEFORE_ADDITIONAL_REMINDER, seconds=1)
        appointment.booked_from = appointment.booked_from - _timedelta
        appointment.booked_till = appointment.booked_till - _timedelta
        appointment.save()

        with override_eppo_feature_flag(
            {AdditionalBookingReminderExperiment.flag_name: self.EXPERIMENT_VARIANT}
        ):
            start_scenario(BCS, appointment=appointment, action=BCS.CUSTOMER_BOOKING_RESCHEDULED)

        #  reminder is not rescheduled because the requirements were not met
        notification.refresh_from_db()
        assert notification.scheduled.astimezone(self.timezone) == expected_initial_scheduled_time

        # skip the reminder because the time remaining for the appointment is less than 2 hours
        self._assert_reminder_not_sent(notification)

    @patch('random.randint', return_value=0)
    def test_create_and_cancel_appointment(self, _):
        # This scenario is just an example, but this behavior is more general. The reminder has to
        # be sent only if the appointment status is accepted

        # appointment date meets the limit
        booked_from = tznow() + timedelta(days=1, hours=BCS.MIN_REMINDER_HOURS)
        appointment = self._prepare_appointment(booked_from=booked_from)

        # create the reminder
        with override_eppo_feature_flag(
            {AdditionalBookingReminderExperiment.flag_name: self.EXPERIMENT_VARIANT}
        ):
            start_scenario(BCS, appointment=appointment, action=BCS.CUSTOMER_CREATED)

        notification = NotificationSchedule.objects.get(
            task_id=self._get_reminder_task_id(appointment.id)
        )

        # reminder is scheduled to be sent at 8 am local time of the appointment date
        expected_initial_scheduled_time = self._get_expected_scheduled_local_time(booked_from)
        assert notification.scheduled.astimezone(self.timezone) == expected_initial_scheduled_time

        # cancel the appointment
        appointment.status = Appointment.STATUS.CANCELED
        appointment.save()

        with override_eppo_feature_flag(
            {AdditionalBookingReminderExperiment.flag_name: self.EXPERIMENT_VARIANT}
        ):
            start_scenario(BCS, appointment=appointment, action=BCS.CUSTOMER_CANCEL)

        # reminder still exists and is not rescheduled
        notification.refresh_from_db()
        assert notification.scheduled == expected_initial_scheduled_time

        # skip the reminder, because the appointment is canceled
        self._assert_reminder_not_sent(notification)

    @patch('random.randint', return_value=0)
    def test_create_appointment_that_meets_the_requirements_but_skip_reminder_variant_b(self, _):
        # appointment date meets the limit

        tomorrow_local_datetime = tznow().astimezone(self.timezone) + timedelta(days=1)
        booked_from = (
            datetime.combine(
                tomorrow_local_datetime.date(),
                self.TIME_ADDITIONAL_REMINDER_SENT,
                tzinfo=self.timezone,
            )
            + timedelta(hours=BCS.MIN_HOURS_BEFORE_ADDITIONAL_REMINDER)
            - timedelta(seconds=1)
        ).astimezone(UTC)
        appointment = self._prepare_appointment(booked_from=booked_from)

        # create the reminder
        with override_eppo_feature_flag(
            {AdditionalBookingReminderExperiment.flag_name: self.EXPERIMENT_VARIANT}
        ):
            start_scenario(BCS, appointment=appointment, action=BCS.CUSTOMER_CREATED)

        notification = NotificationSchedule.objects.get(
            task_id=self._get_reminder_task_id(appointment.id)
        )

        # reminder is scheduled to be sent at 8 am local time of the appointment date
        expected_initial_scheduled_time = self._get_expected_scheduled_local_time(booked_from)
        assert notification.scheduled.astimezone(self.timezone) == expected_initial_scheduled_time

        # skip the reminder because the time remaining for the appointment is less than
        # MIN_HOURS_BEFORE_ADDITIONAL_REMINDER
        self._assert_reminder_not_sent(notification)
