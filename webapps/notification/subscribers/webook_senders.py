from abc import ABC, abstractmethod

# pylint: disable=no-name-in-module
from booksy_proto_notifications_webhooks.pubsub.v1.webhook_pb2 import (
    NotificationStatus,
    NotificationWebhook,
)

# pylint: enable=no-name-in-module
from webapps.notification.enums import NotificationSendStatus
from webapps.notification.subscribers.serializers import EmailIterableNotificationWebhookSerializer
from webapps.notification.subscribers.serializers import NotificationWebhookSerializer
from webapps.notification.subscribers.serializers import SmsNotificationWebhookSerializer
from webapps.notification.tasks import set_sms_notification_status
from webapps.notification.tasks import set_email_notification_status


class Provider(ABC):

    @property
    @abstractmethod
    def serializer(self): ...

    def handle(self, data: NotificationWebhook):
        serialized_data = self.serializer(instance=data).data
        if serialized_data['status'] == NotificationStatus.SUCCESS:
            self.handle_success(serialized_data)
        elif serialized_data['status'] == NotificationStatus.ERROR:
            self.handle_failed(serialized_data)

    @abstractmethod
    def handle_success(self, data: dict): ...

    @abstractmethod
    def handle_failed(self, data: dict): ...

    @staticmethod
    def convert_errors(error_dct):
        return [error_dct] if error_dct else None


class SmsVonage(Provider):
    serializer = SmsNotificationWebhookSerializer

    def handle_success(self, data):
        self._handle(data, status=NotificationSendStatus.WEBHOOK_SUCCESS)

    def handle_failed(self, data: dict):
        errors = self.convert_errors(data['errors'])
        self._handle(data, status=NotificationSendStatus.WEBHOOK_ERROR, errors=errors)

    @staticmethod
    def _handle(data, status, errors=None):
        set_sms_notification_status.delay(
            webhook_id=data['webhook_id'],
            send_status=status,
            api_status=data['api_status'],
            phone_number=data['phone'],
            errors=errors,
        )


class SmsTelnyx(Provider):
    serializer = SmsNotificationWebhookSerializer

    def handle_success(self, data):
        self._handle(data, status=NotificationSendStatus.WEBHOOK_SUCCESS)

    def handle_failed(self, data):
        errors = self.convert_errors(data['errors'])
        self._handle(data, status=NotificationSendStatus.WEBHOOK_ERROR, errors=errors)

    @staticmethod
    def _handle(data, status, errors=None):
        set_sms_notification_status.delay(
            webhook_id=data['webhook_id'],
            send_status=status,
            api_status=data['api_status'],
            phone_number=data['phone'],
            errors=errors,
        )


class EmailIterable(Provider):
    serializer = EmailIterableNotificationWebhookSerializer

    def handle_success(self, data):
        self._handle(data, status=NotificationSendStatus.WEBHOOK_SUCCESS)

    def handle_failed(self, data: dict):
        errors = self.convert_errors(data['errors'])
        self._handle(data, status=NotificationSendStatus.WEBHOOK_ERROR, errors=errors)

    @staticmethod
    def _handle(data, status, errors=None):
        set_email_notification_status.delay(
            notification_id=data['webhook_id'],
            email=data['email'],
            send_status=status,
            errors=errors,
        )


class PushIterable(Provider):
    serializer = NotificationWebhookSerializer

    def handle_success(self, data):
        pass

    def handle_failed(self, data):
        pass
