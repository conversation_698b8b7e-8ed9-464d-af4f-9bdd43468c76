import json
import logging
import typing as t
from collections import namedtuple
from datetime import <PERSON><PERSON><PERSON>
from json import JSONDecodeError
from ssl import SSLError
from uuid import uuid4

import redis
import requests
from celery import chord
from celery.exceptions import MaxRetriesExceededError, Retry
from django.conf import settings
from django.core.cache import caches
from elasticsearch.exceptions import (
    TransportError,
    ConnectionError as ElasticConnectionError,
    ConnectionTimeout,
    ConflictError,
)
from firebase_admin.exceptions import FirebaseError, InvalidArgumentError, NotFoundError

from lib.celery_tools import celery_task
from lib.enums import AppleService
from lib.feature_flag.feature import HowOldDeleteOldNotRefreshedPushTokens
from lib.tools import tznow
from webapps.notification.loggers.decorators import (
    trace_apns_push_task,
    trace_fcm_push_task,
    trace_gcm_push_task,
)
from webapps.notification.models import UserNotification
from webapps.notification.push import <PERSON><PERSON>ush, <PERSON>ush<PERSON><PERSON><PERSON><PERSON>, get_gcm_result_error
from webapps.user.models import UserProfile
from billiard.exceptions import SoftTimeLimitExceeded
from lib.celery_utils.celery_signals import HardSubworkerShutdown

from wsgi_proxy import Harakiri


# NEW CODE FOR NOTIFICATIONS
def get_logger(name='booksy.push_notifications'):
    return logging.getLogger(name)


PushResult = namedtuple('PushResult', ('device', 'token', 'result'))

_RETRY_INTERVAL_SECONDS = 5


class PushRetryException(Exception):
    pass


def send_push_notification(
    receivers,
    alert,
    target=None,
    history_data=None,
    push_android=True,
    push_ios=True,
    blast_id=None,
    use_celery=True,
    save_history_data=True,
    push_data=None,
    retry_on=True,
):
    """
    Send push notifications.

    Spawns tasks directly or asynchronously.
    Only tasks spawned asynchronously will be retried on failure.
    Tasks spawned directly will fail gently.

    """
    from webapps.notification.models import (
        Reciever,
        UserNotification,
    )

    _logger = get_logger()
    _logger.info('[prepare] %s' % receivers)

    target = target or ()

    push_data = push_data or {}
    if isinstance(alert, dict):
        push_data['alert'] = alert.get('alert', alert)
        push_data['title'] = alert.get('title')
    else:
        push_data['alert'] = alert

    if len(target) >= 1:
        push_data['type'] = target[0]
    if len(target) >= 2:
        push_data['args'] = target[1:]

    android_receivers, ios_receivers = [], []
    for receiver in receivers:
        if receiver.device == Reciever.ANDROID and push_android:
            android_receivers.append(receiver)
        elif receiver.device == Reciever.IOS and push_ios:
            ios_receivers.append(receiver)

    notification_history_data = {
        'title': push_data['alert'],
        'type': UserNotification.PUSH_NOTIFICATION,
        'blast_id': blast_id,
        'meta_to': [receiver.token for receiver in receivers],
        'meta_push_recievers': receivers,
        'meta_target': target,
    }

    if history_data:
        notification_history_data.update(history_data)

    if not push_data.get('task_id') and notification_history_data.get('task_id'):
        push_data['task_id'] = notification_history_data['task_id']
    if not push_data.get('task_type') and notification_history_data.get('task_type'):
        push_data['task_type'] = notification_history_data['task_type']

    _logger.debug(
        '[notification_history_data] {notification_history_data!r}, '
        '[android_receivers] {android_receivers!r}, '
        '[ios_receivers] {ios_receivers!r}'
        '[push data] {push_data!r},'
        '[use_celery] {use_celery}'.format(
            notification_history_data=notification_history_data,
            android_receivers=android_receivers,
            ios_receivers=ios_receivers,
            push_data=push_data,
            use_celery=use_celery,
        )
    )

    if use_celery:
        try:
            result = _send_pushes_with_chords(
                push_data=push_data,
                ios_receivers=ios_receivers,
                android_receivers=android_receivers,
                notification_history_data=notification_history_data,
            )
        except redis.exceptions.ConnectionError as e:
            if retry_on:
                _logger.exception('Retrying after redis exception')
                retry_send_push_notification_task.apply_async(
                    kwargs=dict(
                        push_data=push_data,
                        ios_receivers=ios_receivers,
                        android_receivers=android_receivers,
                        notification_history_data=notification_history_data,
                    ),
                    countdown=_RETRY_INTERVAL_SECONDS,
                )
                result = 'Send push initially failed. ' 'Will be retried in {}s'.format(
                    _RETRY_INTERVAL_SECONDS
                )
            else:
                result = 'Error with redis {!r}'.format(e)
    else:
        notification_tasks = _create_send_push_tasks(
            push_data=push_data,
            ios_receivers=ios_receivers,
            android_receivers=android_receivers,
        )
        tasks_result = [task() for task in notification_tasks]
        if save_history_data:
            result = _save_history_task(
                results=tasks_result, history_data=notification_history_data
            )
        else:
            result = None

    _logger.debug('[push_result] %s' % result)

    return str(result)


@celery_task(
    default_retry_delay=_RETRY_INTERVAL_SECONDS,
    autoretry_for=(PushRetryException,),
    retry_kwargs={'max_retries': 3},
)
def retry_send_push_notification_task(
    push_data,
    ios_receivers,
    android_receivers,
    notification_history_data,
):
    """Proxy function that retry send pushes in chord.

    Sometimes without any reason redis client (redis-py) throw Error 32

    Explanation:
        Before version 3.1 redis-py always retry
        connection if something like that happened but it lead to data
        duplication. In newer version this 'bug' was removed.
        So now we need to retry it on our side.
        more about bug and discussion in:
        https://github.com/andymccurdy/redis-py/issues/1140


    :param push_data: dict with keys: alert; optional type; optional args
    :param ios_receivers: list on namedtuples (PushReceiver);
        See webapps.notification.push.notification_receivers_list
    :param android_receivers: list on namedtuples (PushReceiver);
        See webapps.notification.push.notification_receivers_list
    :param notification_history_data:

    :return: result of _send_pushes_with_chords
    """

    try:
        result = _send_pushes_with_chords(
            push_data=push_data,
            ios_receivers=ios_receivers,
            android_receivers=android_receivers,
            notification_history_data=notification_history_data,
        )
    except redis.exceptions.ConnectionError:
        raise PushRetryException('Retrying send pushes')
    return result


@celery_task(bind=True, retry_backoff=True, ignore_result=False)
@trace_fcm_push_task
def fcm_android_push_task(self, token, push_data):
    """
    Send push message using Admin SDK (firebase_admin).
    """
    from webapps.notification.models import Reciever

    _logger = get_logger()
    result = {}

    if 'server' not in push_data:
        push_data['server'] = settings.API_URL

    if (
        settings.LOCAL_DEPLOYMENT or settings.PYTEST or not settings.SEND_NOTIFICATIONS
    ) and settings.PUSH_NOTIFICATION_TEST:
        _logger.debug('[FCM devel android push] token: %s, push_data: %s', token, push_data)
        return _devel_push_dump(Reciever.ANDROID, token, push_data)

    try:
        result['success'] = FCMPush.send_message(token, push_data)
        _logger.info('[FCM android push] token: %s, result: %s', token, result)

    except NotFoundError as err:
        result['error'] = repr(err)
        _res = _remove_unused_tokens((token,))
        _logger.warning('[FCM android push] deleted %d receivers', _res)

    except FirebaseError as err:
        result['error'] = repr(err)
        _logger.error('[FCM android push] firebase error: %r with code %r', err, err.code)

    except Retry:
        # on retry exception do not log just retry once again
        raise

    except (HardSubworkerShutdown, Harakiri, SoftTimeLimitExceeded) as err:
        self.retry()
        _logger.exception('[FCM android push] exception: %r', err)

    except Exception as err:
        result['error'] = repr(err)
        _logger.exception('[FCM android push] exception: %r', err)

    return PushResult(Reciever.ANDROID, token, result)


@celery_task(bind=True, max_retry=3, default_retry_delay=1, ignore_result=False)
@trace_apns_push_task
def apns_push_task(
    self,
    token,
    push_data,
    is_tablet=False,
    receiver_id=None,
    profile_type=UserProfile.Type.CUSTOMER,
):
    """
    Send push message using APNS cloud messaging service.

    """
    from lib.apple import apns_client
    from webapps.notification.models import Reciever

    _logger = get_logger()
    level = logging.DEBUG

    _logger.debug('[ios] token: %s, push_data: %s', token, push_data)

    if (
        settings.LOCAL_DEPLOYMENT or settings.PYTEST or not settings.SEND_NOTIFICATIONS
    ) and settings.PUSH_NOTIFICATION_TEST:
        _logger.debug('[ios] devel run %r, %r', token, push_data)
        return _devel_push_dump(Reciever.IOS, token, push_data)
    countdown = float(self.default_retry_delay) * (2**self.request.retries)
    try:
        body = push_data.get('alert')
        title = push_data.get('title')

        if 'sound' not in push_data:
            push_data['sound'] = 'default'

        apns_request = apns_client.prepare_request(
            device_token=token, alert_title=title, alert_body=body, push_data=push_data
        )

        _logger.debug('[ios request] %s', apns_request)

        if is_tablet:
            topic = AppleService.BIZ_SALON
        else:
            topic = (
                AppleService.CUST if profile_type == UserProfile.Type.CUSTOMER else AppleService.BIZ
            )
        conn = apns_client.get_connection(profile_type, is_tablet)
        result = apns_client.send_message(conn, apns_request)

        _logger.debug(
            '[ios] %s, %s, %s, is_tablet=%s, profile_type=%s',
            topic,
            conn,
            result,
            is_tablet,
            profile_type,
        )

        if 'Success' in result and receiver_id:
            Reciever.objects.filter(id=receiver_id).update(is_tablet=is_tablet)
            _logger.log(
                level,
                '[ios push] setting receiver id %s as is_tablet: %s',
                receiver_id,
                is_tablet,
            )

        elif result in (
            'TooManyRequests',
            'InternalServerError',
            'ServiceUnavailable',
            'Shutdown',
        ):

            _logger.log(level, '[ios push] apns retry: %r', result)
            self.retry(countdown=countdown)
        elif 'Unregistered' in result:
            _remove_unused_tokens((token,))
        elif 'BadDeviceToken' in result and receiver_id is None:
            _remove_unused_tokens((token,))

        _logger.log(level, '[ios push] %s result: %s', push_data, result)
    except MaxRetriesExceededError as err:
        _logger.exception(
            "ios.push_notification %r %r %r",
            token,
            push_data,
            err,
        )
        result = str(err)
    except Retry:
        # on retry exception do not log just retry once again
        raise
    except (SSLError, BrokenPipeError, ConnectionResetError) as err:
        # We should get rid of old apns2==0.7.2
        # and replace it with separate service for push notifications
        # but for now as workaround we only can retry send it once again
        # and hope it will not happen
        _logger.exception('[ios exc]  %s', err)
        self.retry(countdown=countdown)
    except Exception as err:
        # on all other exception than:
        #   MaxRetriesExceededError, Retry
        # log and softly fail
        _logger.exception('[ios exc] %s', err)
        result = '{!r}'.format(err)

    return PushResult(Reciever.IOS, token, result)


def _prepare_result_for_history(result):
    return {"device": result.device, "token": result.token, "result": result.result}


@celery_task(
    bind=True,
    ignore_result=False,
    autoretry_for=(
        TransportError,
        ElasticConnectionError,
        ConnectionTimeout,
        ConflictError,
    ),
    retry_backoff=15,
    retry_jitter=False,
    retry_kwargs={'max_retries': 5},
)
def _save_history_task(self, results, history_data=None, chord_history_data_id=None):
    from webapps.notification.models import NotificationHistory

    if chord_history_data_id is not None:
        history_data = pop_history_data(chord_history_data_id)
    _logger = get_logger()

    results = results or []

    android_results, ios_results = [], []

    history_results = []
    for result in results:
        # deserialize results
        if isinstance(result, list):
            result = PushResult(*result)
        elif isinstance(result, dict):
            result = PushResult(**result)

        if result.device == 'android':
            android_results.append((result.token, result.result))

        elif result.device == 'ios':
            ios_results.append((result.token, result.result))

        history_results.append(_prepare_result_for_history(result))

    history_data['meta_push_result'] = history_results

    _logger.info('[history_data] %s ' % history_data)

    NotificationHistory.add(**history_data)

    return {
        'android_result': android_results,
        'ios_result': ios_results,
    }


def _devel_push_dump(device_type, token, push_data):
    """Push notification dumper."""
    _logger = get_logger()
    body = {'device_type': device_type, 'token': token, 'data': push_data}
    _logger.info(json.dumps(body).encode('utf-8'))

    return PushResult(device_type, token, 'success')


def _remove_unused_tokens(tokens: t.Union[t.Tuple[str], t.List[str]]):
    """
    :param tokens: token list
    :return: number of removed tokens
    """
    from webapps.notification.models import Reciever

    _logger = get_logger()
    _logger.info('removing unused tokens: %r' % ', '.join(tokens))

    return Reciever.objects.filter(identifier__in=tokens).update(
        deleted=tznow(),
    )


def _create_send_push_tasks(push_data, ios_receivers, android_receivers):
    """

    :param push_data: dict with keys: alert; optional type; optional args
    :param ios_receivers: list on namedtuples (PushReceiver);
        See webapps.notification.push.notification_receivers_list
    :param android_receivers: list on namedtuples (PushReceiver);
        See webapps.notification.push.notification_receivers_list
    :return: list of created tasks for
        - android gcm_push_task
        - ios apns_push_task
    """
    notification_tasks = []
    for receiver in android_receivers:
        if isinstance(receiver, dict):
            receiver = PushReceiver(**receiver)

        _push_data = push_data.copy()
        if receiver.args is not None:
            _push_data['args'] = receiver.args

        notification_tasks.append(fcm_android_push_task.s(receiver.token, _push_data))

    for receiver in ios_receivers:
        if isinstance(receiver, dict):
            receiver = PushReceiver(**receiver)
        _push_data = push_data.copy()

        if receiver.args is not None:
            _push_data['args'] = receiver.args

        _push_data['badge'] = receiver.badge

        if receiver.is_tablet is not None:
            _task = apns_push_task.s(
                token=receiver.token,
                push_data=_push_data,
                profile_type=receiver.profile_type,
                is_tablet=receiver.is_tablet,
            )
            notification_tasks.append(_task)
        else:
            notification_tasks.extend(
                _create_guess_apns_push_tasks(
                    receiver=receiver,
                    push_data=_push_data,
                )
            )
    return notification_tasks


def _create_guess_apns_push_tasks(receiver, push_data):
    notification_tasks = []

    # we only have a business tablet app, so we want to prevent unnecessary tries
    if receiver.profile_type == UserProfile.Type.BUSINESS:
        is_tablet_try_cases = (True, False)
    else:
        is_tablet_try_cases = (False,)

    for is_tablet in is_tablet_try_cases:
        _task = apns_push_task.s(
            token=receiver.token,
            push_data=push_data,
            profile_type=receiver.profile_type,
            is_tablet=is_tablet,
            receiver_id=receiver.receiver_id,
        )
        notification_tasks.append(_task)
    return notification_tasks


def pop_history_data(chord_history_data_id):
    history_data = caches['celery_backend'].get(chord_history_data_id)
    if history_data is not None:
        caches['celery_backend'].delete(chord_history_data_id)
    return history_data


def save_history_data(history_data):
    cache_template = 'chord_history_data:{}>'
    chord_history_data_id = cache_template.format(uuid4())
    caches['celery_backend'].set(chord_history_data_id, history_data)
    return chord_history_data_id


def _send_pushes_with_chords(
    push_data, ios_receivers, android_receivers, notification_history_data
):
    """Create task to send pushes to ios and android receivers
    and save result of task execution in _save_history_task

    :param push_data: dict with keys: alert; optional type; optional args
    :param ios_receivers: list on namedtuples (PushReceiver);
        See webapps.notification.push.notification_receivers_list
    :param android_receivers: list on namedtuples (PushReceiver);
        See webapps.notification.push.notification_receivers_list
    :return: result of execution _save_history_task
    """
    _logger = get_logger()
    notification_tasks = _create_send_push_tasks(
        push_data=push_data,
        ios_receivers=ios_receivers,
        android_receivers=android_receivers,
    )

    chord_history_data_id = save_history_data(notification_history_data)
    push_job = chord(
        notification_tasks,
        _save_history_task.s(
            chord_history_data_id=chord_history_data_id,
        ),
    )

    _logger.debug('[push_job] %s' % push_job)
    result = push_job.apply_async()
    return str(result)


@celery_task
def delete_expired_push_tokens_task():
    from django.db.models import Q
    from webapps.notification.models import Reciever

    deadline = tznow() - timedelta(days=HowOldDeleteOldNotRefreshedPushTokens())
    receivers_ids_to_delete = Reciever.objects.filter(
        Q(
            last_refresh__isnull=False,
            last_refresh__lte=deadline,
        )
        | Q(
            last_refresh__isnull=True,
            created__lte=deadline,
        ),
        deleted__isnull=True,
        customer_notifications__type=UserNotification.PUSH_NOTIFICATION,
    ).values_list('id', flat=True)[:2000]
    Reciever.objects.filter(id__in=receivers_ids_to_delete).delete()
