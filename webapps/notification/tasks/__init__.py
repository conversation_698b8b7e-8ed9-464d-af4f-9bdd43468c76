import datetime
import json
import logging
import time
import traceback
import typing as t
from collections import defaultdict

from dateutil.relativedelta import relativedelta
from ddtrace import tracer
from django.conf import settings
from django.db import OperationalError
from django.utils.translation import activate, get_language
from elasticsearch import AuthorizationException, NotFoundError

from lib import safe_json
from lib.booksy_sms import (
    SMSServiceStatus,
    get_reasonable_send_datetime,
    get_reasonable_send_datetime_sms,
    parse_phone_number,
    send_sms,
)
from lib.celery_tools import celery_task, check_queues, post_transaction_task
from lib.datadog.celery.resource_name_util import append_current_celery_span_resource_name
from bo_obs.datadog.enums import DatadogOperationNames
from lib.datadog.enums import DatadogCustomServices
from lib.db import PRIMARY_DB, READ_ONLY_DB, is_replica_synced, using_db_for_reads
from lib.dispatch_context import dispatch_context
from lib.events import deserialize_instance
from lib.feature_flag.feature.notification import WarnSmsStatusUpdateFailureFlag
from lib.sensi.sensidb import execute_on_db
from lib.tools import tznow
from service.notification.enums import IterablePushWebhookTargetApp, TelnyxMessageStatus
from webapps.kill_switch.models import KillSwitch
from webapps.notification.elasticsearch import NotificationHistoryDocument
from webapps.notification.enums import NotificationSendStatus, ScheduleState
from webapps.notification.notifications.cache_dispatcher import NotificationDispatcherHistoryCache
from webapps.notification.scenarios.sms_limits import sms_limits_notification
from webapps.notification.tasks.sms_codes import send_sms_registration_code_task

log = logging.getLogger('booksy.notifications')


class NotificationRetryException(Exception):
    pass


class NotificationStatusRetryException(Exception):
    pass


def execute_task(task):
    from webapps.notification.scenarios import (
        dispatch_scenario_task,
        task_result_appended,
    )
    from webapps.notification.scenarios.base import ScenarioSkipped

    start_time = time.time()
    state = None
    result = {}
    try:
        parameters = task.parameters.copy()
        result = dispatch_scenario_task(task.task_id, parameters)
    except ScenarioSkipped as e:
        state = ScheduleState.SKIPPED
        result = {
            'result': repr(e),
        }
    except Exception as e:
        state = ScheduleState.FAILURE
        result = {
            'exception': repr(e),
            'stack': traceback.format_exc().splitlines(),
        }
        log.exception("error in scenario: %s %r", task.task_id, task.parameters)
        raise
    else:
        state = ScheduleState.SUCCESS
        result = {
            'result': result,
        }
    finally:
        # skip updating NotificationSchedule:
        # BaseNotification will do it asynchronously in async_send_notification_task
        if result == '__NOTIFICATION_SEND__':
            return '__NOTIFICATION_SEND__'

        task.state = state
        result.update(
            {
                'duration': time.time() - start_time,
                'state': state,
                'created': task.created.strftime('%F_%T.%f'),
                'updated': task.updated.strftime('%F_%T.%f'),
                'scheduled': task.scheduled.strftime('%F_%T.%f'),
                'finished': tznow().strftime('%F_%T.%f'),
            }
        )
        task.result = safe_json.dumps(
            task_result_appended(task.result, result),
            pretty=True,
        )
        task.finished = tznow()
        task.save()

    return task.state


@post_transaction_task(
    time_limit=31 * 60,
    soft_time_limit=30 * 60,
)
@using_db_for_reads(PRIMARY_DB)
def instant_notification_schedule_task(name, time, parameters):
    """Create a NotificationSchedule entry and instantly run it."""
    from webapps.notification.models import NotificationSchedule

    task = NotificationSchedule.get_or_create(name, time, parameters)
    return execute_task(task), task.task_id


@celery_task
@using_db_for_reads(READ_ONLY_DB)
def process_notification_schedules_task_with_cache():
    from webapps.notification.models import NotificationSchedule

    now = tznow()
    now_isoformat = now.isoformat()
    schedule_ids = NotificationSchedule.objects.filter(
        state=ScheduleState.PENDING,
        scheduled__lt=now,
        scheduled__gt=now - datetime.timedelta(days=1),
    ).values_list('id', flat=True)

    if len(schedule_ids) == 0:
        return

    new_schedule_ids = set(schedule_ids) - NotificationDispatcherHistoryCache.get_history()
    if new_schedule_ids:
        NotificationDispatcherHistoryCache.add_to_history(list(new_schedule_ids))

    for schedule_id in new_schedule_ids:
        single_notification_schedule_task.delay(schedule_id, now_isoformat)

    return len(schedule_ids)


@post_transaction_task(
    time_limit=31 * 60,
    soft_time_limit=30 * 60,
    default_retry_delay=20,
    autoretry_for=(
        NotificationRetryException,  # replica is not synced
        OperationalError,  # conflict with recovery error
    ),
    retry_kwargs={'max_retries': 3},
)
@using_db_for_reads(READ_ONLY_DB)
def single_notification_schedule_task(notification_id: int, timestamp: str):
    """Run a single NotificationSchedule existing in DB."""
    from webapps.notification.models import NotificationSchedule

    task = NotificationSchedule.objects.filter(id=notification_id).first()

    if task is None or not is_replica_synced(
        timestamp=datetime.datetime.fromisoformat(timestamp),
        using=READ_ONLY_DB,
    ):
        raise NotificationRetryException(f'notification_id={notification_id}')

    if not NotificationSchedule.objects.filter(
        id=notification_id,
        state__in=(
            ScheduleState.PENDING,
            ScheduleState.RECEIVED,
        ),
    ).update(
        state=ScheduleState.STARTED,
        updated=tznow(),
    ):
        log.warning(
            msg='single_notification_schedule_task stopped to prevent multi execution',
            extra={
                'celery_task_id': dispatch_context.task_id,
                'domain_name': task.task_id,  # NotificationSchedule.task_id
            },
        )
        return
    return execute_task(task), task.task_id


@post_transaction_task
def retrieve_stuck_started_notification_schedules_task():
    from webapps.notification.elasticsearch import NotificationHistoryDocument
    from webapps.notification.models import NotificationSchedule

    if not KillSwitch.exist_and_alive(KillSwitch.System.RETRIEVE_STUCK_STARTED_NOTIFICATION):
        return
    current_time = tznow()
    processed_schedules_tasks_ids = NotificationSchedule.objects.filter(
        state=ScheduleState.STARTED,
        updated__lt=current_time - datetime.timedelta(minutes=15),
        updated__gt=current_time - datetime.timedelta(minutes=30),
    ).values_list('task_id', flat=True)
    history_documents = (
        NotificationHistoryDocument.search()
        .query(
            'terms',
            task_id=list(processed_schedules_tasks_ids),
        )
        .execute()
    )
    results = {
        'success_tasks': [],
        'failed_tasks': [],
    }
    for history_document in history_documents:
        status = history_document.status
        if status == NotificationSendStatus.NO_INFORMATION:
            continue
        elif status in [
            NotificationSendStatus.GATEWAY_SUCCESS,
            NotificationSendStatus.WEBHOOK_SUCCESS,
        ]:
            results['success_tasks'].append(history_document.task_id)
        elif status in [
            NotificationSendStatus.GATEWAY_ERROR,
            NotificationSendStatus.WEBHOOK_ERROR,
        ]:
            results['failed_tasks'].append(history_document.task_id)

    NotificationSchedule.objects.filter(
        task_id__in=results['success_tasks'],
    ).update(
        state=ScheduleState.SUCCESS,
    )
    NotificationSchedule.objects.filter(
        task_id__in=results['failed_tasks'],
    ).update(
        state=ScheduleState.FAILURE,
    )


@post_transaction_task
def retrieve_stuck_received_notification_schedules_task():
    from webapps.notification.models import NotificationSchedule

    if not KillSwitch.exist_and_alive(KillSwitch.System.RETRIEVE_STUCK_RECEIVED_NOTIFICATION):
        return
    current_time = tznow()
    if number_of_stuck_tasks := NotificationSchedule.objects.filter(
        state=ScheduleState.RECEIVED,
        updated__lt=current_time - datetime.timedelta(minutes=15),
        updated__gt=current_time - datetime.timedelta(minutes=35),
    ).update(
        state=ScheduleState.PENDING,
    ):
        log.warning(
            msg=f'retrieve_stuck_received_notification_schedules_task',
            extra={
                'quantity': number_of_stuck_tasks,
            },
        )


@post_transaction_task(time_limit=16 * 60, soft_time_limit=15 * 60)
def bulk_sms_send_task(
    message: str,
    phone_numbers: t.List[str],
    history_data: dict,
    blast_id: t.Optional[int] = None,
    blast=None,
    language: t.Optional[str] = None,
    **kwargs,
) -> t.List[dict]:
    from webapps.notification.tools import bulk_apply_sms_limits

    blast_id = blast.id if blast else blast_id  # for legacy tasks in queue
    results = []
    default_language = settings.LANGUAGE_CODE[:2].lower()
    if language is None:
        language = default_language
    activate(language)
    blasts_phones_dict = kwargs.get('blasts_phones_dict') or {}

    # Apply sms limits right before sending (still possible that meanwhile
    # doing for loop limits will be reached...)
    initial_cnt = len(phone_numbers)
    phone_numbers, stats = bulk_apply_sms_limits(history_data, phone_numbers, message)
    rejected_cnt = initial_cnt - len(phone_numbers)
    log.info('bulk_sms_send_task: apply sms limits rejected_numbers count: %d', rejected_cnt)

    for phone_number in phone_numbers:
        phone_history_data = dict(history_data)
        phone_history_data.update(blasts_phones_dict.get(phone_number) or {})
        result = send_sms(
            to=phone_number,
            message=message,
            history_data=phone_history_data,
            dediacrit=False if blast_id else None,
            blast_id=blast_id,
        )
        results.append(result.to_dict())
        # this is required by Twillo, because their service
        # can send only 1 sms per second
        # smsapi.pl probably also have some limitations
        # time.sleep(1.0)

    if 'business_id' not in history_data:
        return results
    from webapps.business.models import Business

    business = Business.objects.filter(id=history_data['business_id']).first()
    if business:
        sms_limits_notification(business)
    activate(default_language)
    log.debug('bulk_sms_send_task: %r', results)
    return results


def _get_cc_to_eta(reasonable_hrs=False, tz=None):
    ret = {}
    for country, value in list(settings.SMS_SETTINGS_PER_COUNTRY.items()):
        if reasonable_hrs or ('force_marketing_night_adjust' in value):
            tz = value.get('timezone', tz)
            ret[country] = get_reasonable_send_datetime(country, tz)

    return ret


def commence_send_sms(
    message,
    phone_numbers,
    history_data,
    blast_id=None,
    reasonable_hrs=False,
    tz=None,
    business=None,
    **kwargs,
):
    cc_to_eta = _get_cc_to_eta(reasonable_hrs, tz)

    default_eta = (
        get_reasonable_send_datetime_sms(timezone=tz, business=business) if reasonable_hrs else None
    )

    blasts_phones_dict = kwargs.get('blasts_phones_dict') or {}
    reparsed_blasts_phones_dict = {}

    # dict {ETA: [numbers]}
    send_numbers = defaultdict(list)
    for phone_number in phone_numbers:
        parsed_number = parse_phone_number(phone_number)
        if not (parsed_number.is_valid and parsed_number.is_mobile):
            continue
        eta = cc_to_eta.get(parsed_number.country.lower(), default_eta)

        global_short = parsed_number.global_short
        send_numbers[eta].append(global_short)
        reparsed_blasts_phones_dict[global_short] = blasts_phones_dict.get(phone_number)

    language = get_language()
    language = language[:2] if language else None

    for eta, recipients in list(send_numbers.items()):
        if eta:
            history_data['meta_send_at'] = eta.isoformat()
        log.debug('Sending sms with history data: ' + json.dumps(history_data))
        # Remember to always pass PhoneNumber.global_short!
        # see bulk_apply_sms_limits()
        bulk_sms_send_task.apply_async(
            args=[message, recipients, history_data],
            kwargs=dict(
                blast_id=blast_id,
                language=language,
                blasts_phones_dict=reparsed_blasts_phones_dict,
            ),
            eta=eta,
        )


_min_delete_old_notifications_days = 14
_min_delete_old_notifications_paid_sms_days = 90


@celery_task
def notifications_cleanup_task(
    days=_min_delete_old_notifications_days,
    paid_sms_days=_min_delete_old_notifications_paid_sms_days,
):
    from webapps.notification.models import (
        NotificationHistory,
        NotificationSchedule,
        UserNotification,
    )

    if (
        days < _min_delete_old_notifications_days
        or paid_sms_days < _min_delete_old_notifications_paid_sms_days
    ):
        raise RuntimeError('You can only delete old notifications')
    cutoff = tznow() - relativedelta(days=days)
    schedules_qs = NotificationSchedule.objects.filter(
        scheduled__lte=cutoff,
    )
    schedules_deleted = schedules_qs.delete()

    histories_qs = NotificationHistory.objects.filter(
        created__lte=cutoff,
    ).exclude(
        # we need to keep Business SMS longer for sms limits
        type=UserNotification.SMS_NOTIFICATION,
        sender=NotificationHistory.SENDER_BUSINESS,
    )
    histories_deleted = histories_qs.delete()

    paid_sms_cutoff = tznow() - relativedelta(days=paid_sms_days)
    paid_sms_qs = NotificationHistory.objects.filter(
        created__lte=paid_sms_cutoff,
    ).filter(
        type=UserNotification.SMS_NOTIFICATION,
        sender=NotificationHistory.SENDER_BUSINESS,
    )
    paid_sms_deleted = paid_sms_qs.delete()

    return {
        'schedules': list(schedules_deleted),
        'histories': list(histories_deleted),
        'paid_sms': list(paid_sms_deleted),
    }


def trace_sms_codes_delivery(
    phone: str,
    abuse_ip_address: str = '',
    abuse_fingerprint: str = '',
    metadata: str = '',
    **kwargs,
) -> None:
    parsed_phone = parse_phone_number(phone)
    with tracer.trace(
        DatadogOperationNames.SMS_CODE_UNUSED,
        service=DatadogCustomServices.NOTIFICATIONS,
        resource=parsed_phone.country,
    ) as span:
        span.set_tag('phone', phone)
        span.set_tag('abuse_ip_address', abuse_ip_address)
        span.set_tag('abuse_fingerprint', abuse_fingerprint)
        span.set_tag('metadata', str(metadata))


@celery_task
def smscodes_cleanup_task():
    from webapps.notification.models import NotificationSMSCodes

    qs = list(
        NotificationSMSCodes.objects.filter(
            created__lte=tznow() - datetime.timedelta(seconds=settings.SMS_REGISTRATION_PERIOD),
            consumed__isnull=True,
        )
        .exclude(sms_code='')
        .values('phone', 'abuse_ip_address', 'abuse_fingerprint', 'metadata')
    )

    result = NotificationSMSCodes.objects.filter(
        created__lte=tznow() - datetime.timedelta(seconds=settings.SMS_REGISTRATION_PERIOD),
    ).delete()

    for entry in qs:
        trace_sms_codes_delivery(**entry)

    return result


@celery_task
def email_codes_cleanup_task():
    from webapps.notification.models import NotificationEmailCodes

    result = NotificationEmailCodes.objects.filter(
        created__lte=tznow() - datetime.timedelta(seconds=settings.SMS_REGISTRATION_PERIOD),
    ).delete()

    return result


def _is_replacing_banned():
    from webapps.kill_switch.models import KillSwitch

    return (
        KillSwitch.killed(KillSwitch.System.REPLACE_BANNED_TWILIO_NUMBER)
        or not settings.LIVE_DEPLOYMENT
    )


@celery_task(
    bind=True,
    time_limit=8 * 60,
    soft_time_limit=4 * 60,
    max_retries=3,
)
def replace_banned_twilio_number(self, message_sid):
    """
    Task for replacing banned numbers in Twilio copilot pool.

    :param message_sid: Twilio message sid
    :return: bool
    """
    from lib.booksy_sms.twilio_sms import BooksyTwilioClient
    from twilio.rest import TwilioException

    if _is_replacing_banned():
        return

    _log = logging.getLogger('booksy.twilio_api')

    service_settings = settings.TWILIO_SETTINGS

    client = BooksyTwilioClient(service_settings['account_sid'], service_settings['auth_token'])

    try:
        message = client.messages(message_sid).fetch()
        _log.info('mesasge %r for sid %s', message, message_sid)

        if not message or not message.messaging_service_sid:
            return False

        result = client.replace_banned(
            message.from_,
            message.messaging_service_sid,
        )
        _log.info(
            'replaced %r for %r with %s',
            message.from_,
            message.messaging_service_sid,
            result,
        )

    except TwilioException as err:
        _log.exception('twilio api exc %r', str(err))
        raise self.retry(retry_backoff=60, retry_jitter=True)

    except Exception as err:
        _log.exception('twilio exc %r', str(err))
        return False

    is_registration_code = (
        message.messaging_service_sid
        == service_settings['messaging_services']['registration_codes']
    )
    if is_registration_code:
        twilio_resend_registration_code.delay(
            message.body, message.to, message.messaging_service_sid
        )


@celery_task(
    bind=True,
    time=10 * 60,
    soft_time_limit=9 * 60,
    max_retries=3,
)
def twilio_resend_registration_code(self, message, recipient, messaging_service_id):
    """
    Resends registration code to recipient.

    :param message: SMS text.
    :param recipient: String phone number.
    :param messaging_service_id: String copilot sid.

    :return: bool
    """
    from lib.booksy_sms.twilio_sms import twilio_send_sms

    service_settings = settings.TWILIO_SETTINGS

    is_registration = (
        messaging_service_id == service_settings['messaging_services']['registration_codes']
    )

    if not is_registration:
        return False

    result = twilio_send_sms(
        service_settings=service_settings,
        phone_number=recipient,
        message=message,
        metadata={'task_id': 'sms_registration_code::'},
    )

    if result.status == SMSServiceStatus.ERROR:
        raise self.retry(
            retry_backoff=5,
            retry_jitter=True,
            retry_backoff_max=50,
        )

    return True


@celery_task
def async_send_notification_task(notif_type, **kwargs):
    from webapps.notification.base import BaseNotification

    append_current_celery_span_resource_name(f'__{notif_type}')
    notification = BaseNotification.get_type(notif_type)
    if notification:
        instance, params = deserialize_instance(**kwargs)
        notification(instance, **params).send()
    else:
        log.error('Notification class not found %s', notif_type)


@celery_task(
    default_retry_delay=20,
    autoretry_for=(NotificationStatusRetryException,),
    retry_kwargs={'max_retries': 3},
    retry_backoff=2,
    retry_jitter=False,
)
def set_email_notification_status(
    notification_id: int,
    send_status: NotificationSendStatus,
    email: t.Optional[str] = None,
    errors: t.Optional[list] = None,
) -> None:

    try:
        notification = NotificationHistoryDocument.get(notification_id)
    except NotFoundError:
        raise NotificationStatusRetryException(
            f'notification_id={notification_id},status={send_status}'
        )

    if email is not None and notification.recipient_email != email:
        log.warning(
            "Emails do not match! (notification_id: %s, email: %s, status: %s)",
            notification_id,
            email,
            send_status,
        )
    notification.status = send_status
    notification.errors = errors
    notification.updated = tznow()
    notification.save()


# endregion


@celery_task(
    autoretry_for=(NotificationStatusRetryException,),
    retry_kwargs={'max_retries': 5},
    retry_backoff=30,
    retry_jitter=False,
)
def set_sms_notification_status(
    send_status: str,
    phone_number: str,
    api_status: str | None = None,
    notification_id: t.Optional[str] = None,
    errors: t.Optional[list] = None,
    webhook_id: t.Optional[str] = None,
) -> None:
    try:
        if notification_id:
            notification = NotificationHistoryDocument.get(notification_id)
        elif webhook_id:
            notification = (
                NotificationHistoryDocument.search()
                .filter(
                    'term',
                    webhook_id=webhook_id,
                )
                .execute()
                .hits[0]
            )
        else:
            raise ValueError(
                f'notification_id: {notification_id} and webhook_id: {webhook_id} are wrong'
            )

        if parse_phone_number(notification.metadata.to) != parse_phone_number(phone_number):
            log.warning(
                "Phone numbers do not match! (notification_id: %s, phone number: %s, status: %s)",
                notification.meta.id,
                phone_number,
                send_status,
            )

        if warn_status_flag := WarnSmsStatusUpdateFailureFlag():
            if api_status:
                api_statuses = getattr(notification.metadata, 'api_statuses', [])
                api_statuses.append((api_status, tznow()))
                setattr(notification.metadata, 'api_statuses', api_statuses)

            notification.status = send_status
            notification.errors = errors
            notification.updated = tznow()
            notification.save()

    except NotFoundError:
        raise NotificationStatusRetryException(
            f'notification_id={notification_id},status={send_status}'
        )
    except IndexError:
        raise NotificationStatusRetryException(f'webhook_id={webhook_id},status={send_status}')
    except AuthorizationException as ex:
        log.warning(
            "set_sms_notification_status_task [elasticsearch authorization exception] "
            f" (notification_id: {notification_id}): {ex}"
        )

    if not warn_status_flag:
        if api_status:
            api_statuses = getattr(notification.metadata, 'api_statuses', [])
            api_statuses.append((api_status, tznow()))
            setattr(notification.metadata, 'api_statuses', api_statuses)

        notification.status = send_status
        notification.errors = errors
        notification.updated = tznow()
        notification.save()


@celery_task
def send_iterable_push_sent_notification(
    notification_id: str,
    email: str,
    message: str,
    target_app: IterablePushWebhookTargetApp,
    deeplink: t.Optional[str] = None,
    title: t.Optional[str] = None,
) -> None:
    from webapps.business.models import Business
    from webapps.business.notifications.notifications import IterablePushSendHistoryNotification

    business_qs = (
        Business.objects.filter(
            owner__email=email,
        )
        .order_by('-id')
        .values_list('id', flat=True)
    )

    active_business_qs = business_qs.filter(active=True)
    if active_business_qs.exists():
        business_id = active_business_qs.first()
    else:
        business_id = business_qs.first()

    if not business_id:
        return

    if target_app == IterablePushWebhookTargetApp.BUSINESS:
        IterablePushSendHistoryNotification(
            business_id=business_id,
            notification_id=notification_id,
            deeplink=deeplink,
            message=message,
            title=title,
        ).send()
    elif target_app == IterablePushWebhookTargetApp.CUSTOMER:
        # to be done in MAR-1159
        pass


@celery_task
def add_receiver_filter_to_notification_index_mapping():
    from webapps.notification.elasticsearch import (
        NotificationDocument,
        NotificationIndex,
    )

    new_field_name = 'receiver_filter'
    new_field = NotificationDocument._ObjectBase__get_field(  # pylint: disable=protected-access
        new_field_name
    )
    if not new_field:
        return
    mapping = {'properties': {new_field_name: new_field.to_dict()}}
    NotificationIndex().put_mapping(body=mapping)
