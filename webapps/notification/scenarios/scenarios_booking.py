import datetime
import json
import logging
import random
import urllib.parse

import pytz
from django.conf import settings
from django.utils.timezone import make_aware
from django.utils.translation import gettext_lazy as _
from future.backports.datetime import timedelta

from lib.datetime_utils import format_timedelta
from lib.db import PRIMARY_DB, using_db_for_reads
from lib.email import send_email
from lib.feature_flag.adapter import UserData
from lib.feature_flag.bug import (
    FixMissingReminderForRepeatingBooking,
    UseNoReplyAddressInReplyToFieldFlag,
)
from lib.feature_flag.enums import AppDomains, ExperimentVariants, SubjectType
from lib.feature_flag.experiment.booking import SuggestiveReviewsExperiment
from lib.feature_flag.feature.notification import (
    AdditionalBookingReminderExperiment,
    EnableSendingAdditionalBookingReminderFlag,
    IncludeBookingIdInAddReviewPushTargetFlag,
    SendTippingAppetiteExperimentPushFlag,
)
from lib.feature_flag.feature.payment import PromoteTTPAndBCRPaymentsToCustomersFlag
from lib.payment_gateway.enums import PaymentMethodType
from lib.tools import dict_merge, sget_v2, tznow


from webapps.segment.utils import (
    spawn_booking_created_analytics_events,
    spawn_booking_finished_analytics_events,
)

from webapps.business.enums import CustomData
from webapps.family_and_friends.booking_notifications import recipient_for_review
from webapps.kill_switch.models import KillSwitch
from webapps.notification.enums import CustomerBookingReminderType
from webapps.notification.scenarios.base import (
    Scenario,
    ScenarioSkipped,
    random_timedelta,
)
from webapps.notification.scenarios.scenarios_booking_mixin import (
    BookingMixin,
    DONT_SEND_CANCELLATION_EMAIL,
    DONT_SEND_CANCELLATION_SMS,
)
from webapps.booksy_pay.notifications import is_booksy_pay_reminder_needed
from webapps.wait_list.tasks import waitlist_scenario_task


class BookingChangedScenario(Scenario, BookingMixin):
    """Events to be executed when booking is created or changed"""

    SCENARIO_NAME = 'booking_changed'
    # actions
    BUSINESS_CANCEL = 'business_cancel'
    BUSINESS_CANCEL_PUBLIC_API = 'business_cancel_public_api'
    BUSINESS_CONFIRM = 'business_confirm'
    BUSINESS_DECLINE = 'business_decline'
    BUSINESS_BOOKING_RESCHEDULE_REQUEST = 'business_booking_reschedule_request'
    BUSINESS_BOOKING_RESCHEDULE_PUBLIC_API = 'business_booking_reschedule_public_api'
    BUSINESS_CREATED = 'business_created'
    BUSINESS_CHANGE = 'business_change'
    BUSINESS_CHANGED_CUSTOMER = 'business_changed_customer'
    BUSINESS_CHANGED_STAFFER = 'business_changed_staffer'
    CUSTOMER_BOOKING_RESCHEDULED = 'customer_booking_rescheduled'
    CUSTOMER_BOOKING_RESCHEDULE_RESPONSE = 'customer_booking_reschedule_response'
    CUSTOMER_CANCEL = 'customer_cancel'
    CUSTOMER_CREATED = 'customer_created'
    CUSTOMER_PAID_FOR_AWAITING_PREPAYMENT = 'customer_paid_for_awaiting_prepayment'

    MIN_REMINDER_HOURS = 6
    HOURS_BEFORE_ADDITIONAL_REMINDER = 2
    MIN_HOURS_BEFORE_ADDITIONAL_REMINDER = 1
    HOUR_ADDITIONAL_REMINDER_SENT = 8
    MIN_HOUR_ADDITIONAL_REMINDER_SENT = 7
    MIN_SECONDS_OFFSET_FOR_ADDITIONAL_REMINDER = 0
    MAX_SECONDS_OFFSET_FOR_ADDITIONAL_REMINDER = 900  # 15 min

    @staticmethod
    def get_history_data(
        params,
        task_id,
        task_type=None,
    ):
        from webapps.notification.models import NotificationHistory

        return {
            'parameters': json.dumps(params),
            'task_id': task_id,
            'task_type': task_type or NotificationHistory.TASK_TYPE__BOOKING_CHANGED,
        }

    @classmethod
    def is_booking_reminder_needed(
        cls,
        appointment,
        adding=False,
        reminder_type=CustomerBookingReminderType.DEFAULT,
    ):
        from webapps.booking.models import Appointment

        # this ignores bookings during edition
        # This occurs when Business creates new SubBooking during creation
        if not adding and appointment.booked_for is None:
            return False

        OK_BOOKING_TYPES = [
            Appointment.TYPE.BUSINESS,
            Appointment.TYPE.CUSTOMER,
        ]

        if appointment.type not in OK_BOOKING_TYPES:
            return False

        OK_BOOKING_STATUSES = [
            Appointment.STATUS.ACCEPTED,
        ]

        if appointment.status not in OK_BOOKING_STATUSES:
            return False

        if reminder_type == CustomerBookingReminderType.DEFAULT:
            min_reminder_hours_before = cls.get_reminder_hours(appointment) - cls.MIN_REMINDER_HOURS
            if (
                min_reminder_hours_before > 0
                and appointment.booked_from - datetime.timedelta(hours=min_reminder_hours_before)
                < tznow()
            ):
                return False
        elif reminder_type in {
            CustomerBookingReminderType.HOURS_BEFORE,
            CustomerBookingReminderType.SPECIFIC_DAYTIME,
        }:
            if appointment.booked_from - datetime.timedelta(hours=cls.MIN_REMINDER_HOURS) < tznow():
                return False

        return True

    @staticmethod
    def _get_additional_customer_booking_reminder_type(appointment_id):
        experiment_variants = {
            ExperimentVariants.VARIANT_A: CustomerBookingReminderType.HOURS_BEFORE,
            ExperimentVariants.VARIANT_B: CustomerBookingReminderType.SPECIFIC_DAYTIME,
        }

        experiment_variant = AdditionalBookingReminderExperiment(
            UserData(
                subject_key=appointment_id,
                subject_type=SubjectType.APPOINTMENT_ID.value,
                is_experiment=True,
                app_domain=AppDomains.CUSTOMER.value,
            )
        )

        return experiment_variants.get(experiment_variant)

    @classmethod
    def get_reminder_datetime(cls, appointment, reminder_type):
        _datetime = None
        booked_from = appointment.booked_from
        timezone = appointment.business.get_timezone()

        if reminder_type == CustomerBookingReminderType.HOURS_BEFORE:
            _datetime = booked_from - datetime.timedelta(hours=cls.HOURS_BEFORE_ADDITIONAL_REMINDER)
        elif reminder_type == CustomerBookingReminderType.SPECIFIC_DAYTIME:
            morning_time_local = datetime.time(cls.HOUR_ADDITIONAL_REMINDER_SENT, tzinfo=timezone)
            booked_from_local = booked_from.astimezone(timezone)

            if booked_from_local.date() > tznow(tz=timezone).date():
                offset = timedelta(
                    seconds=random.randint(
                        cls.MIN_SECONDS_OFFSET_FOR_ADDITIONAL_REMINDER,
                        cls.MAX_SECONDS_OFFSET_FOR_ADDITIONAL_REMINDER,
                    )
                )
                _datetime = (
                    datetime.datetime.combine(
                        booked_from_local.date(), morning_time_local
                    ).astimezone(tz=pytz.UTC)
                    - offset
                )

        return _datetime

    @staticmethod
    def _get_reminder_method_name(reminder_type):
        return f'customer_booking_reminder_{reminder_type.value}'

    @classmethod
    def is_booking_created_analytics_needed(
        cls,
        appointment: 'Appointment',
        action: str | None = None,
        send_booking_created_analytics: bool = False,
    ) -> bool:
        """
        Check if the appointment requires sending analytics after creation.
        Usually, we have to check appointment type and scenario action.

        In some cases, when action is None (for example: repeating appointment replan),
        we also check send_booking_created_analytics parameter.
        """
        from webapps.booking.models import Appointment

        return appointment.type in Appointment.TYPES_BOOKABLE and (
            action in (cls.BUSINESS_CREATED, cls.CUSTOMER_CREATED)
            or action is None
            and send_booking_created_analytics
        )

    @classmethod
    def plan(cls, params):
        from webapps.booking.models import Appointment
        from webapps.notification.tools import (
            get_payment_method_availability_notifications_sent_count,
            add_payment_method_availability_notification_sent_id,
        )
        from webapps.pos.utils import has_tap_to_pay_and_bcr_available
        from webapps.pos.utils import has_payment_method_customer_reminder_notifications

        original_params = params.copy()
        # checks if params are the same as when scenario is called in
        # webapps.booking.models.RepeatingBooking._replan_appointments
        if (
            FixMissingReminderForRepeatingBooking()
            and original_params.get('action') is None
            and not original_params.get('repeating_use_all')
        ):
            try:
                params, appointment = cls.get_params_objects(params.copy(), 'appointment')
            except ScenarioSkipped:
                # Datadog and FLS-3048 shows that sometimes newly created appointment is not yet available
                # in the replica db and DoesNotExist exception is thrown. That's why I've added additional
                # check in the primary db in this situation.
                with using_db_for_reads(PRIMARY_DB):
                    params, appointment = cls.get_params_objects(params.copy(), 'appointment')
        else:
            params, appointment = cls.get_params_objects(params, 'appointment')
        action = params.pop('action', None)
        repeating_use_all = params.pop('repeating_use_all', True)
        if appointment is None:
            scenario_logger = logging.getLogger('booksy.scenario')
            scenario_logger.error(
                "BookingChangedScenario - appointment is None: " "original_params=%r params=%r",
                original_params,
                params,
            )

        task_list = []

        def add_task(
            method_name,
            datetime=None,
            appointment_override=None,
            extra_params=None,
        ):
            if datetime is None:
                datetime = tznow()
            task_appointment = appointment_override or appointment
            if extra_params is None:
                extra_params = {}

            task_params = {
                'appointment_id': task_appointment.id,
            }
            task_params.update(params)
            task_params.update(extra_params)
            task_id = f'{cls.SCENARIO_NAME}:{method_name}:appointment_id={task_appointment.id}'
            task_list.append((task_id, datetime, task_params))
            return task_id

        def add_business_booking_reschedule_requests_tasks():
            add_task(
                'customer_booking_reschedule_request',
                # to let us stop it if merchant will reschedule again!
                datetime=tznow() + datetime.timedelta(minutes=5),
            )
            add_task('business_booking_reschedule_request')

        def add_booksy_pay_reminder_tasks(appointment: 'Appointment'):
            when = appointment.booked_from - datetime.timedelta(hours=2)
            if tznow() <= when:
                add_task(
                    'customer_booksy_pay_before_appointment_started_reminder',
                    appointment_override=appointment,
                    datetime=when,
                )

            add_task(
                'customer_booksy_pay_before_appointment_finished_reminder',
                appointment_override=appointment,
                datetime=(appointment.booked_till - datetime.timedelta(minutes=5)),
            )

        reminder_hours = cls.get_reminder_hours(appointment)

        additional_customer_booking_reminder_type = None
        if (
            appointment.type == Appointment.TYPE.CUSTOMER
            and not appointment.repeating
            and reminder_hours >= settings.DEFAULT_REMINDER_HOURS
        ):
            additional_customer_booking_reminder_type = (
                cls._get_additional_customer_booking_reminder_type(appointment.id)
            )

        if appointment.repeating:
            if repeating_use_all:
                appointments = appointment.repeating.extra_appointments_unlimited
            else:
                appointments = [appointment]
            for appt in appointments:
                if cls.is_booking_reminder_needed(appt, adding=True):
                    add_task(
                        'customer_booking_reminder',
                        datetime=(appt.booked_from - datetime.timedelta(hours=reminder_hours)),
                        appointment_override=appt,
                        extra_params={
                            'repeating_use_first': False,
                        },
                    )

                if is_booksy_pay_reminder_needed(appt):
                    add_booksy_pay_reminder_tasks(appt)

                if cls.is_booking_created_analytics_needed(
                    appointment=appt,
                    action=action,
                    send_booking_created_analytics=original_params.get(
                        'send_booking_created_analytics',
                        False,
                    ),
                ):
                    spawn_booking_created_analytics_events(appt)
        else:
            # single booking and multibooking case
            if cls.is_booking_reminder_needed(appointment, adding=True):
                add_task(
                    'customer_booking_reminder',
                    datetime=(appointment.booked_from - datetime.timedelta(hours=reminder_hours)),
                )

            if (
                additional_customer_booking_reminder_type is not None
                and cls.is_booking_reminder_needed(
                    appointment,
                    adding=True,
                    reminder_type=additional_customer_booking_reminder_type,
                )
            ):
                _datetime = cls.get_reminder_datetime(
                    appointment=appointment,
                    reminder_type=additional_customer_booking_reminder_type,
                )
                method_name = cls._get_reminder_method_name(
                    reminder_type=additional_customer_booking_reminder_type,
                )
                if _datetime:
                    add_task(
                        method_name,
                        datetime=_datetime,
                    )

            if is_booksy_pay_reminder_needed(appointment):
                add_booksy_pay_reminder_tasks(appointment)

            if cls.is_booking_created_analytics_needed(
                appointment=appointment,
                action=action,
                send_booking_created_analytics=original_params.get(
                    'send_booking_created_analytics',
                    False,
                ),
            ):
                spawn_booking_created_analytics_events(appointment)

        ttp_available, bcr_available = has_tap_to_pay_and_bcr_available(
            business_id=appointment.business_id
        )
        if (
            appointment.type in Appointment.TYPES_BOOKABLE
            and appointment.booked_for
            and appointment.booked_for.user
            and has_payment_method_customer_reminder_notifications(
                business_id=appointment.business_id
            )
            and (ttp_available or bcr_available)
            and get_payment_method_availability_notifications_sent_count(
                user=appointment.booked_for.user
            )
            < 3
        ):
            when = appointment.booked_from - datetime.timedelta(hours=1)
            if tznow() <= when:
                if PromoteTTPAndBCRPaymentsToCustomersFlag():
                    if bcr_available:
                        # if bcr (or bcr + ttp)
                        event = (
                            'customer_ttp_or_bcr_availability_before_appointment_started_reminder'
                        )
                    else:
                        # if only ttp
                        event = 'customer_tap_to_pay_before_appointment_started_reminder'

                    payment_methods = []
                    if bcr_available:
                        payment_methods.append(PaymentMethodType.TERMINAL)
                    if ttp_available:
                        payment_methods.append(PaymentMethodType.TAP_TO_PAY)

                    task_id = add_task(
                        event,
                        datetime=when,
                    )
                    add_payment_method_availability_notification_sent_id(
                        user=appointment.booked_for.user,
                        task_id=task_id,
                        payment_methods=payment_methods,
                    )
                else:
                    task_id = add_task(
                        'customer_tap_to_pay_before_appointment_started_reminder',
                        datetime=when,
                    )
                    add_payment_method_availability_notification_sent_id(
                        user=appointment.booked_for.user,
                        task_id=task_id,
                        payment_methods=[PaymentMethodType.TAP_TO_PAY],
                    )

        # send mails only for future bookings
        if appointment.booked_till > tznow():
            if action == cls.CUSTOMER_CREATED:
                add_task('business_booking_confirmation')
                from webapps.booking.models import SubBooking

                # removed due wrong message amd logic
                if appointment.status == Appointment.STATUS.ACCEPTED:
                    add_task('customer_booking_confirmation')
            elif action == cls.BUSINESS_CREATED:
                add_task('customer_booking_confirmation')
                add_task('business_booking_confirmation')

            elif action == cls.BUSINESS_CHANGED_CUSTOMER:
                add_task('customer_booking_confirmation')
            elif action == cls.BUSINESS_CHANGED_STAFFER:
                add_task('business_booking_confirmation')

            elif action == cls.CUSTOMER_CANCEL:
                add_task('business_booking_cancel')
            elif action == cls.BUSINESS_CANCEL:
                add_task('customer_booking_cancel')
                add_task('business_booking_cancel_by_business')

            elif action == cls.BUSINESS_CANCEL_PUBLIC_API:
                add_task('customer_booking_cancel')

            elif action == cls.BUSINESS_CONFIRM:
                add_task('customer_booking_confirmation_manual')

            elif action == cls.BUSINESS_DECLINE:
                add_task('customer_booking_decline_manual')
            elif action == cls.CUSTOMER_BOOKING_RESCHEDULED:
                add_task('business_booking_rescheduled')
            elif action == cls.BUSINESS_BOOKING_RESCHEDULE_REQUEST:
                add_business_booking_reschedule_requests_tasks()

            elif action == cls.BUSINESS_BOOKING_RESCHEDULE_PUBLIC_API:
                add_task('customer_booking_reschedule_request')

            elif action == cls.CUSTOMER_BOOKING_RESCHEDULE_RESPONSE:
                add_task('business_booking_reschedule_response')
            elif action == cls.CUSTOMER_PAID_FOR_AWAITING_PREPAYMENT:
                # todo talk with PM to ensure
                add_task('customer_booking_confirmation')
                add_task('business_booking_confirmation')
        elif (
            params.get('notify_about_reschedule')
            and action == cls.BUSINESS_BOOKING_RESCHEDULE_REQUEST
        ):
            add_business_booking_reschedule_requests_tasks()

        cls.add_to_schedule(task_list)

    @classmethod
    def get_reminder_hours(cls, appointment):

        try:
            reminder_hours = min(
                cls.get_previous_reminder_hour(appointment) or float('inf'),
                appointment.business.custom_data.get(CustomData.BOOKING_REMIND_BEFORE)
                or float('inf'),
            )
            if reminder_hours == float('inf'):
                reminder_hours = settings.DEFAULT_REMINDER_HOURS
            # validate admin data
            return min(int(reminder_hours), settings.MAX_REMINDER_HOURS)
        except:
            return settings.DEFAULT_REMINDER_HOURS

    @classmethod
    def event_customer_booking_reminder(cls, params, task_id):
        params, appointment = cls.get_params_objects(
            params,
            'appointment',
            remove=False,
        )
        appointment.business.raise_if_customer_notifications_are_disabled()

        if not cls.is_time_diff_acceptable(appointment):
            booking_diff = cls.get_time_diff_booked_from_and_updated(appointment)
            raise ScenarioSkipped(
                {
                    'diff': format_timedelta(booking_diff),
                    'updated': appointment.updated.strftime('%F %T'),
                    'booked_from': appointment.booked_from.strftime('%F %T'),
                }
            )

        if not cls.is_booking_reminder_needed(appointment):
            raise ScenarioSkipped({'message': 'reminder not needed'})

        if settings.NOTIFICATION_SMS_DEDUCTION:
            sms_deduction = int(appointment.booked_for.user is None)
        else:
            sms_deduction = 0

        return cls.generic_booking_communication_sender(
            scenario_name=cls.SCENARIO_NAME,
            template_name='booking_reminder',
            to_customer=True,
            params=params,
            sms_deduction=sms_deduction,
            history_data=cls.get_history_data(params, task_id),
            _calculate_booking_changes=False,
        )

    @classmethod
    def _event_additional_customer_booking_reminder(cls, params, task_id):
        if not EnableSendingAdditionalBookingReminderFlag():
            message = 'Sending an additional customer booking reminder disabled by feature flag'
            raise ScenarioSkipped({'message': message})

        params, appointment = cls.get_params_objects(
            params,
            'appointment',
            remove=False,
        )
        appointment.business.raise_if_customer_notifications_are_disabled()

        if cls.should_skip_additional_customer_booking_reminder(appointment):
            raise ScenarioSkipped({'message': 'reminder not needed'})

        return cls.generic_booking_communication_sender(
            scenario_name=cls.SCENARIO_NAME,
            template_name='booking_reminder',
            to_customer=True,
            params=params,
            history_data=cls.get_history_data(params, task_id),
            _calculate_booking_changes=False,
        )

    @classmethod
    def event_customer_booking_reminder_hours_before(cls, params, task_id):
        cls._event_additional_customer_booking_reminder(params, task_id)

    @classmethod
    def event_customer_booking_reminder_specific_daytime(cls, params, task_id):
        cls._event_additional_customer_booking_reminder(params, task_id)

    @classmethod
    def get_extra_parameters_for_booksy_pay(cls, params, task_id, appointment_id):
        from webapps.notification.enums import NotificationTarget
        from webapps.notification.models import NotificationHistory

        return {
            'history_data': cls.get_history_data(
                params,
                task_id,
                task_type=NotificationHistory.TASK_TYPE__BOOKSY_PAY,
            ),
            'target': (
                NotificationTarget.SHOW_APPT_DETAIL_BOOKSY_PAY.value,
                appointment_id,
            ),
        }

    @classmethod
    def _customer_booksy_pay_reminder(
        cls,
        params,
        task_id,
        template_name,
    ):
        from webapps.booking.models import Appointment

        params, appointment = cls.get_params_objects(
            params,
            'appointment',
            remove=False,
        )
        appointment.business.raise_if_customer_notifications_are_disabled()

        if appointment.type not in Appointment.TYPES_BOOKABLE:
            raise ScenarioSkipped({'message': 'Booksy pay reminder not needed'})

        if not appointment.is_booksy_pay_payment_window_open:
            raise ScenarioSkipped({'message': 'Booksy pay payment window is closed'})

        extra_parameters = cls.get_extra_parameters_for_booksy_pay(
            params=params,
            task_id=task_id,
            appointment_id=appointment.id,
        )

        return cls.generic_booking_communication_sender(
            scenario_name=cls.SCENARIO_NAME,
            template_name=template_name,
            to_customer=True,
            params=params,
            extra_variables=cls.get_extra_variables_for_booksy_pay,
            _calculate_booking_changes=False,
            **extra_parameters,
        )

    @classmethod
    def event_customer_booksy_pay_before_appointment_started_reminder(cls, params, task_id):
        return cls._customer_booksy_pay_reminder(
            params,
            task_id,
            'customer_booksy_pay_before_appointment_started_reminder',
        )

    @classmethod
    def event_customer_booksy_pay_before_appointment_finished_reminder(cls, params, task_id):
        return cls._customer_booksy_pay_reminder(
            params,
            task_id,
            'customer_booksy_pay_before_appointment_finished_reminder',
        )

    @classmethod
    def event_customer_tap_to_pay_before_appointment_started_reminder(cls, params, task_id):
        from webapps.booking.models import Appointment
        from webapps.notification.models import NotificationHistory
        from webapps.notification.tools import (
            get_payment_method_availability_notifications_sent_count,
        )
        from webapps.pos.utils import has_payment_method_customer_reminder_notifications
        from webapps.pos.utils import has_tap_to_pay_and_bcr_available

        template_name = 'customer_tap_to_pay_before_appointment_started_reminder'

        params, appointment = cls.get_params_objects(
            params,
            'appointment',
            remove=False,
        )
        appointment.business.raise_if_customer_notifications_are_disabled()

        if appointment.type not in Appointment.TYPES_BOOKABLE:
            raise ScenarioSkipped({'message': 'Payment availability reminder not needed'})

        if not has_payment_method_customer_reminder_notifications(
            business_id=appointment.business_id
        ):
            raise ScenarioSkipped(
                {'message': "Client payment availability notifications not available"}
            )

        if not appointment.booked_for:
            raise ScenarioSkipped({'message': 'No BCI for this appointment'})

        if not appointment.booked_for.user:
            raise ScenarioSkipped({'message': 'No User for this BCI'})

        ttp_available, _bcr_available = has_tap_to_pay_and_bcr_available(
            business_id=appointment.business_id
        )
        if not ttp_available:
            raise ScenarioSkipped({'message': 'TTP not available'})

        if (
            get_payment_method_availability_notifications_sent_count(
                user=appointment.booked_for.user
            )
            > 3
        ):
            raise ScenarioSkipped({'message': 'Already more than 3 notifications sent'})

        return cls.generic_booking_communication_sender(
            scenario_name=cls.SCENARIO_NAME,
            template_name=template_name,
            to_customer=True,
            params=params,
            _calculate_booking_changes=False,
            history_data=cls.get_history_data(
                params,
                task_id,
                task_type=NotificationHistory.TASK_TYPE__TTP,
            ),
        )

    @classmethod
    def event_customer_ttp_or_bcr_availability_before_appointment_started_reminder(
        cls, params, task_id
    ):
        from webapps.booking.models import Appointment
        from webapps.notification.models import NotificationHistory
        from webapps.notification.tools import (
            get_payment_method_availability_notifications_sent_count,
        )
        from webapps.pos.utils import has_payment_method_customer_reminder_notifications
        from webapps.pos.utils import has_tap_to_pay_and_bcr_available

        template_name = 'customer_ttp_or_bcr_availability_before_appointment_started_reminder'

        params, appointment = cls.get_params_objects(
            params,
            'appointment',
            remove=False,
        )
        appointment.business.raise_if_customer_notifications_are_disabled()

        if appointment.type not in Appointment.TYPES_BOOKABLE:
            raise ScenarioSkipped({'message': 'Payment availability reminder not needed'})

        if not has_payment_method_customer_reminder_notifications(
            business_id=appointment.business_id
        ):
            raise ScenarioSkipped(
                {'message': "Client payment availability notifications not available"}
            )

        if not appointment.booked_for:
            raise ScenarioSkipped({'message': 'No BCI for this appointment'})

        if not appointment.booked_for.user:
            raise ScenarioSkipped({'message': 'No User for this BCI'})

        _ttp_available, bcr_available = has_tap_to_pay_and_bcr_available(
            business_id=appointment.business_id
        )
        if not bcr_available:
            raise ScenarioSkipped({'message': 'BCR not available'})

        if (
            get_payment_method_availability_notifications_sent_count(
                user=appointment.booked_for.user
            )
            > 3
        ):
            raise ScenarioSkipped({'message': 'Already more than 3 notifications sent'})

        return cls.generic_booking_communication_sender(
            scenario_name=cls.SCENARIO_NAME,
            template_name=template_name,
            to_customer=True,
            params=params,
            _calculate_booking_changes=False,
            history_data=cls.get_history_data(
                params,
                task_id,
                task_type=NotificationHistory.TASK_TYPE__TTP_OR_BCR,
            ),
        )

    @classmethod
    def should_skip_additional_customer_booking_reminder(cls, appointment):
        from webapps.booking.models import Appointment

        if appointment.booked_for is None or appointment.status != Appointment.STATUS.ACCEPTED:
            return True

        if (
            appointment.booked_from
            - datetime.timedelta(hours=cls.MIN_HOURS_BEFORE_ADDITIONAL_REMINDER)
            < tznow()
        ):
            return True

        now_local_time = tznow().astimezone(appointment.business.get_timezone())
        if now_local_time.time() < datetime.time(cls.MIN_HOUR_ADDITIONAL_REMINDER_SENT):
            return True

        return False

    @classmethod
    def is_time_diff_acceptable(cls, appointment):
        """
        I and Jarosław Porada suppose that this condition purpose is to eliminate reminders for
        appointments that could get booking_changed notification in less than
        2 hours before reminder.
        But there is no documentation related to this ancient condition with roots in 2015.
        """
        booking_diff = cls.get_time_diff_booked_from_and_updated(appointment)
        reminder_hours = cls.get_reminder_hours(appointment)
        if booking_diff < datetime.timedelta(hours=reminder_hours + 2):
            return False
        return True

    @classmethod
    def get_time_diff_booked_from_and_updated(cls, appointment):
        return appointment.booked_from - appointment.updated

    @classmethod
    def get_previous_reminder_hour(cls, appointment):
        import dateutil

        previous_reminder_hours = appointment.business.custom_data.get(
            CustomData.REMINDER_HOUR_CHANGE_OLD_VALUE
        )
        if not previous_reminder_hours:
            return None
        created = appointment.created
        acceptable_changes = []
        for str_datetime, hours in previous_reminder_hours.items():
            change_datetime = dateutil.parser.parse(str_datetime)
            if change_datetime < created:
                continue
            acceptable_changes.append(change_datetime)
        if not acceptable_changes:
            return None
        # hours settings from before first change after appointment creation
        return previous_reminder_hours.get(min(acceptable_changes).isoformat())

    @classmethod
    def get_extra_variables_for_customer_booking_confirmation(cls, appointment, language):
        from lib.gdpr_descriptions import BUSINESS_CUSTOMER_AGREEMENTS
        from webapps.family_and_friends.booking_notifications import (
            disclosure_obligation_agreement_parser,
        )

        agreement = BUSINESS_CUSTOMER_AGREEMENTS.get(settings.DO_AGREEMENT)
        description = (
            agreement.get_description(
                business_id=appointment.business_id, business_name=appointment.business.name
            )
            if agreement
            else ''
        )

        is_first_appointment = False
        if appointment.booked_for:
            is_first_appointment = appointment.id == appointment.booked_for.first_appointment_id

        show = (
            is_first_appointment
            and not agreement.is_blacklisted
            and appointment.is_family_and_friends
        )

        data = disclosure_obligation_agreement_parser(description)
        data['show_disclosure_obligation_agreement'] = show
        if appointment.is_booksy_pay_payment_window_open:
            data.update(cls.get_extra_variables_for_booksy_pay(appointment, language))

        return data

    @classmethod
    def event_customer_booking_confirmation(cls, params, task_id):
        from webapps.booking.models import Appointment
        from webapps.business_related.helpers import (
            generate_pdf_with_safety_rules,
        )
        from webapps.business_related.models import SafetyRule

        try:
            params, appointment = cls.get_params_objects(params, 'appointment', remove=False)
        except Appointment.DoesNotExist:
            raise ScenarioSkipped({'appointment.exist': False})

        business = appointment.business
        safety_rules = (
            SafetyRule.objects.filter(
                members=business.id,
            )
            .order_by('id')
            .values_list('text', flat=True)
        )

        pdf_report = generate_pdf_with_safety_rules(business, safety_rules)

        if appointment.status == Appointment.STATUS.UNCONFIRMED:
            return cls.generic_booking_communication_sender(
                scenario_name=cls.SCENARIO_NAME,
                template_name='customer_booking_confirmation_awaiting',
                to_customer=True,
                params=params,
                history_data=cls.get_history_data(params, task_id),
                _calculate_booking_changes=False,
                attachments=(
                    [
                        (
                            _(f"health_and_safety_rules.{'pdf'}"),
                            pdf_report,
                            'application/pdf',
                        )
                    ]
                    if safety_rules
                    else []
                ),
            )
        else:
            extra_parameters = (
                cls.get_extra_parameters_for_booksy_pay(
                    params=params,
                    task_id=task_id,
                    appointment_id=appointment.id,
                )
                if appointment.is_booksy_pay_payment_window_open
                else {'history_data': cls.get_history_data(params, task_id)}
            )

            return cls.generic_booking_communication_sender(
                scenario_name=cls.SCENARIO_NAME,
                template_name='customer_booking_confirmation',
                to_customer=True,
                params=params,
                extra_variables=cls.get_extra_variables_for_customer_booking_confirmation,
                _calculate_booking_changes=False,
                attachments=(
                    [
                        (
                            _(f"health_and_safety_rules.{'pdf'}"),
                            pdf_report,
                            'application/pdf',
                        )
                    ]
                    if safety_rules
                    else []
                ),
                **extra_parameters,
            )

    @classmethod
    def event_business_booking_confirmation(cls, params, task_id):
        return cls.generic_booking_communication_sender(
            scenario_name=cls.SCENARIO_NAME,
            template_name='business_booking_confirmation',
            to_customer=False,
            params=params,
            history_data=cls.get_history_data(params, task_id),
            _calculate_booking_changes=False,
        )

    @classmethod
    def event_business_booking_cancel(cls, params, task_id):
        params, appointment = cls.get_params_objects(
            params,
            'appointment',
            remove=False,
        )
        if settings.WAITLIST_ENABLED:
            waitlist_scenario_task.delay(appointment.business_id)

        return cls.generic_booking_communication_sender(
            scenario_name=cls.SCENARIO_NAME,
            template_name='business_booking_cancel',
            to_customer=False,
            params=params,
            history_data=cls.get_history_data(params, task_id),
            _calculate_booking_changes=False,
        )

    @classmethod
    def event_customer_booking_cancel(cls, params, task_id):
        params, appointment = cls.get_params_objects(
            params,
            'appointment',
            remove=False,
        )
        if settings.WAITLIST_ENABLED:
            waitlist_scenario_task.delay(appointment.business_id)

        if not params.get('_notify_about_cancel_email', False) and not params.get(
            '_notify_about_cancel_sms', False
        ):
            raise ScenarioSkipped(
                {
                    'reason': '_notify_about_cancel(sms and email) is False',
                    'booking_id': appointment.id,
                }
            )
        elif not params.get('_notify_about_cancel_email', False):
            # client must be informed only by sms
            params[DONT_SEND_CANCELLATION_EMAIL] = True
        elif not params.get('_notify_about_cancel_sms', False):
            # client must be informed only by email
            params[DONT_SEND_CANCELLATION_SMS] = True
        else:
            # client must be informed by email and sms
            # both fields DONT_SEND_CANCELLATION...
            # are set to False
            pass

        return cls.generic_booking_communication_sender(
            scenario_name=cls.SCENARIO_NAME,
            template_name='customer_booking_cancel',
            to_customer=True,
            params=params,
            history_data=cls.get_history_data(params, task_id),
            _calculate_booking_changes=False,
        )

    @classmethod
    def event_business_booking_cancel_by_business(cls, params, task_id):
        return cls.generic_booking_communication_sender(
            scenario_name=cls.SCENARIO_NAME,
            template_name='business_booking_cancel_by_business',
            to_customer=False,
            params=params,
            history_data=cls.get_history_data(params, task_id),
            _calculate_booking_changes=False,
        )

    @classmethod
    def event_customer_booking_confirmation_manual(cls, params, task_id):
        return cls.generic_booking_communication_sender(
            scenario_name=cls.SCENARIO_NAME,
            template_name='customer_booking_confirmation',
            to_customer=True,
            params=params,
            extra_variables=cls.get_extra_variables_for_customer_booking_confirmation,
            history_data=cls.get_history_data(params, task_id),
            _calculate_booking_changes=False,
        )

    @classmethod
    def event_customer_booking_decline_manual(cls, params, task_id):
        return cls.generic_booking_communication_sender(
            scenario_name=cls.SCENARIO_NAME,
            template_name='customer_booking_decline_manual',
            to_customer=True,
            params=params,
            history_data=cls.get_history_data(params, task_id),
            _calculate_booking_changes=False,
        )

    @classmethod
    def event_business_booking_rescheduled(cls, params, task_id):
        from webapps.business.models import Business

        history_data = cls.get_history_data(params, task_id)
        params, appointment = cls.get_params_objects(
            params,
            'appointment',
            remove=False,
        )

        if settings.WAITLIST_ENABLED:
            waitlist_scenario_task.delay(appointment.business_id)

        if appointment.business.booking_mode != Business.BookingMode.AUTO:
            cls.generic_booking_communication_sender(
                scenario_name=cls.SCENARIO_NAME,
                template_name='customer_booking_rescheduled_manual',
                to_customer=True,
                params=params.copy(),
                history_data=history_data,
                _calculate_booking_changes=True,
            )

        return cls.generic_booking_communication_sender(
            scenario_name=cls.SCENARIO_NAME,
            template_name='business_booking_rescheduled',
            to_customer=False,
            params=params,
            history_data=history_data,
            _calculate_booking_changes=True,
        )

    @classmethod
    def event_customer_booking_reschedule_request(cls, params, task_id):
        from webapps.booking.models import Appointment

        history_data = cls.get_history_data(params, task_id)
        params, appointment = cls.get_params_objects(
            params,
            'appointment',
            remove=False,
        )

        if not params.get('notify_about_reschedule', True):
            raise ScenarioSkipped(
                {
                    'reason': 'notify_about_reschedule is False',
                    'appointment_id': appointment.id,
                }
            )

        if appointment.booked_for_id is None:
            raise ScenarioSkipped(
                {
                    'reason': 'appointment.booked_for is None',
                    'appointment_id': appointment.id,
                }
            )

        if appointment.type == Appointment.TYPE.CUSTOMER:
            template_name = 'customer_booking_reschedule_request'
        else:
            template_name = 'customer_booking_rescheduled_auto'

        if settings.NOTIFICATION_SMS_DEDUCTION:
            sms_deduction = int(appointment.booked_for.user_id is None)
        else:
            sms_deduction = 0

        return cls.generic_booking_communication_sender(
            scenario_name=cls.SCENARIO_NAME,
            template_name=template_name,
            to_customer=True,
            params=params,
            sms_deduction=sms_deduction,
            history_data=history_data,
            _calculate_booking_changes=True,
        )

    @classmethod
    def event_business_booking_reschedule_request(cls, params, task_id):
        return cls.generic_booking_communication_sender(
            scenario_name=cls.SCENARIO_NAME,
            template_name='business_booking_reschedule_request',
            to_customer=False,
            params=params,
            history_data=cls.get_history_data(params, task_id),
            _calculate_booking_changes=True,
        )

    @classmethod
    def event_business_booking_reschedule_response(cls, params, task_id):
        history_data = cls.get_history_data(params, task_id)

        def reschedule_confirmed(appointment, language):
            from webapps.booking.models import Appointment

            confirmed = appointment.status == Appointment.STATUS.ACCEPTED
            return {'reschedule_confirmed': confirmed}

        return cls.generic_booking_communication_sender(
            scenario_name=cls.SCENARIO_NAME,
            template_name='business_booking_reschedule_response',
            to_customer=False,
            params=params,
            history_data=history_data,
            extra_variables=reschedule_confirmed,
            _calculate_booking_changes=True,
        )


# this allow for simple testing with
# reload(webapps.notification.scenarios).test_mail_text()
def test_changed_mail():
    BookingChangedScenario.event_business_booking_reschedule_response(
        {'appointment_id': 498}, 'test'
    )


# this allow for simple testing with
# import webapps.notification.scenarios.scenarios_booking
# reload(webapps.notification.scenarios.scenarios_booking).test_booking_info()
def test_booking_info():
    print(
        BookingChangedScenario.event_business_booking_reschedule_response(
            {'appointment_id': 486}, 'test'
        )
    )
    # print BookingChangedScenario.event_customer_booking_reschedule_request({'booking_id': 201, 'previous_booked_from': 86400 * (365*45+81.5)}, 'test')


class BookingFinishedScenario(Scenario):
    """Events to be executed when booking has been finished"""

    SCENARIO_NAME = 'booking_finished'

    @classmethod
    def plan(cls, params):
        from webapps.booking.notifications.first_customer_bookings import (
            BusinessFirstCustomerBookingFinished,
        )
        from webapps.business.models import Business

        params, appointment = cls.get_params_objects(params, 'appointment')
        cls._send_prompted_reviews_notification_if_applicable(appointment)

        if (
            appointment.business.status == Business.Status.TRIAL
            and appointment.is_first_cb_for_business()
        ):
            BusinessFirstCustomerBookingFinished(
                None,
                business_id=appointment.business_id,
            ).send()

        if cls.is_review_request_needed(appointment):
            cls._request_for_review(appointment)
        elif SendTippingAppetiteExperimentPushFlag():
            cls._tipping_appetite_experiment(appointment)

        spawn_booking_finished_analytics_events(appointment)

    @classmethod
    def _request_for_review(cls, appointment):
        from webapps.booking.models import Appointment

        suffix = f'appointment_id={appointment.id}'
        task_params = {
            'appointment_id': appointment.id,
        }
        if appointment.type == Appointment.TYPE.BUSINESS:
            cls.add_to_schedule(
                [
                    (
                        'booking_finished:after2hours_business_appointment:' + suffix,
                        tznow() + random_timedelta(118 * 60, 122 * 60),
                        task_params,
                    ),
                ]
            )
            return
        if appointment.type != Appointment.TYPE.CUSTOMER:
            return

        business_tz = appointment.booked_for.business.get_timezone()
        booked_till = appointment.booked_till.astimezone(business_tz)
        now = tznow()
        next_day = booked_till + datetime.timedelta(days=1)
        next_day = next_day.replace(hour=10, minute=0, second=0)
        next_day = next_day + random_timedelta(-2 * 60, +2 * 60)

        half_hour_ago = now - datetime.timedelta(minutes=30)
        day_ago = now - datetime.timedelta(days=1)
        month_ago = now - datetime.timedelta(days=30)
        experiment_variant = SuggestiveReviewsExperiment(
            UserData(
                subject_key=appointment.business_id,
                subject_type=SubjectType.BUSINESS_ID.value,
                is_experiment=True,
            )
        )

        # last 30 mins
        if half_hour_ago < booked_till:
            if experiment_variant == ExperimentVariants.VARIANT_A:
                cls.add_to_schedule(
                    [
                        (
                            'booking_finished:immediate:' + suffix,
                            tznow() + datetime.timedelta(minutes=1),
                            task_params,
                        ),
                        ('booking_finished:next_day:' + suffix, next_day, task_params),
                    ]
                )
            else:
                cls.add_to_schedule(
                    [
                        (
                            'booking_finished:after30minutes:' + suffix,
                            tznow() + random_timedelta(28 * 60, 32 * 60),
                            task_params,
                        ),
                        ('booking_finished:next_day:' + suffix, next_day, task_params),
                    ]
                )

        # between 30 mins and 24 hrs
        elif day_ago < booked_till < half_hour_ago:
            cls.add_to_schedule(
                [
                    ('booking_finished:next_day:' + suffix, next_day, task_params),
                ]
            )

        # between 24 hrs and 30 days
        elif month_ago < booked_till < day_ago:
            cls.add_to_schedule(
                [
                    (
                        'booking_finished:immediate:' + suffix,
                        tznow() + datetime.timedelta(minutes=1),
                        task_params,
                    ),
                ]
            )

    @classmethod
    def _tipping_appetite_experiment(cls, appointment):
        from webapps.notification.models import TippingAppetiteNotificationRecord

        suffix = f'appointment_id={appointment.id}'
        task_params = {
            'appointment_id': appointment.id,
        }
        if (
            sget_v2(appointment, ['booked_for', 'user', 'id'])
            and appointment.total_value
            and appointment.total_value > 0
            and not TippingAppetiteNotificationRecord.objects.filter(
                user_id=appointment.booked_for.user.id
            ).exists()
        ):
            cls.add_to_schedule(
                [
                    (
                        'booking_finished:tipping_experiment_push:' + suffix,
                        tznow() + datetime.timedelta(hours=1),
                        task_params,
                    ),
                ]
            )

    @classmethod
    def event_after30minutes(cls, params, task_id):
        history_data = cls.get_history_data(params, task_id)
        params, appointment = cls.get_params_objects(params, 'appointment')

        cls.check_review_request_validity(appointment)
        booking_id = cls.get_primary_booking(appointment, only_id=True)

        bci = recipient_for_review(appointment)
        channel = None
        if cls.can_send_push(customer=bci.user):
            cls.send_push(appointment, history_data=history_data)
            channel = 'push'
        elif cls.can_send_email(appointment=appointment, bci=bci):
            cls.send_email(appointment, history_data=history_data)
            channel = 'email'

        cls.create_short_review_notifications(booking_id, bci)
        return {'booked_till': str(appointment.booked_till), 'sent': channel}

    event_immediate = event_after30minutes

    @classmethod
    def event_after2hours_business_appointment(cls, params, task_id):
        from webapps.experiment_v3.exp.review_non_user import NonUserSMSReviewExperiment

        history_data = cls.get_history_data(params, task_id)
        params, appointment = cls.get_params_objects(params, 'appointment')
        channel = None
        if NonUserSMSReviewExperiment.can_send_review_sms(appointment.booked_for):
            cls.send_sms(appointment)
            channel = 'sms'
        return {'booked_till': str(appointment.booked_till), 'sent': channel}

    @classmethod
    def event_next_day(cls, params, task_id):
        history_data = cls.get_history_data(params, task_id)
        params, appointment = cls.get_params_objects(params, 'appointment')

        booking_id = cls.get_primary_booking(appointment, only_id=True)

        cls.check_review_request_validity(appointment)
        bci = recipient_for_review(appointment)
        if not cls.can_send_email(appointment, bci):
            return {'can_send_email': False}

        # We don't want to send exacly the same email twice
        # So when we sent email after 30 minutes
        # (because we were unable to send push)
        # then we change default subject to alternative one

        first = cls.can_send_push(customer=bci.user)
        cls.send_email(
            appointment,
            first_email=first,
            history_data=history_data,
        )
        cls.create_short_review_notifications(booking_id, bci)

        return {'booked_till': str(appointment.booked_till), 'sent': 'email'}

    @classmethod
    def check_review_request_validity(cls, appointment):
        bci = recipient_for_review(appointment)
        if not bci:
            raise ScenarioSkipped(message={'recipient_for_review': None})

        if not bci.business.active:
            raise ScenarioSkipped(message={'business.active': False})

        if not bci.business.visible:
            raise ScenarioSkipped(message={'business.visible': False})

        appointment_bci = appointment.booked_for
        if appointment_bci.user is None and appointment_bci.visible_in_business is False:
            raise ScenarioSkipped(
                message={
                    'appointment.booked_for.visible_in_business': False,
                    'appointment.booked_for.user': None,
                }
            )

        # 39057. Checking if review is needed. Customer could add
        # review in time between planing task and executing this method
        if not cls.is_review_request_needed(appointment):
            raise ScenarioSkipped(message={'appointment.needed': False})

    @classmethod
    def is_review_request_needed(cls, appointment):
        from webapps.booking.models import Appointment, BookingChange
        from webapps.experiment_v3.exp.review_non_user import NonUserSMSReviewExperiment
        from webapps.reviews.models import Review

        # ticket 26084-review_request_disabled
        if appointment.business.custom_data.get('review_request_disabled', False):
            return False

        bci = recipient_for_review(appointment)
        bci_in_experiment = (
            appointment.type == Appointment.TYPE.BUSINESS
            and NonUserSMSReviewExperiment.can_send_review_sms(bci)
        )
        if not bci_in_experiment and (
            appointment.type != Appointment.TYPE.CUSTOMER
            or bci is None
            or bci.user is None
            or appointment.booked_till < (tznow() - datetime.timedelta(days=30))
        ):
            return False

        # check if booking was moved to past
        changes = appointment.bookingchange_set.filter(
            changed_by__in=[BookingChange.BY_CUSTOMER, BookingChange.BY_BUSINESS]
        ).order_by('created')
        last_booked_till = None
        for i, c in enumerate(changes):
            # booking has ended, then was edited
            if (
                last_booked_till
                and changes.count() > 1
                and (last_booked_till < c.created or i == 0)
                and c.booked_till < tznow()
            ):
                return False

            last_booked_till = c.booked_till

        return not Review.objects.filter(
            business_id=bci.business_id,
            user_id=bci.user_id,
        ).exists()

    @classmethod
    def send_push(cls, appointment, history_data=None):
        from webapps.notification.models import NotificationHistory
        from webapps.notification.push import notification_receivers_list
        from webapps.notification.tasks.push import send_push_notification

        appointment.business.raise_if_customer_notifications_are_disabled()
        bci = appointment.booked_for

        user_language = cls.get_language(bci.user, None)

        text = cls.render_template(
            cls.SCENARIO_NAME,
            'review_request',
            language=user_language,
            extension='push',
            template_variables={
                'business': bci.business,
                'user': bci.user,
            },
        )

        receivers = (
            notification_receivers_list(user_id=bci.user.id, customer=True)
            if bci.user is not None
            else []
        )

        experiment_variant = SuggestiveReviewsExperiment(
            UserData(
                subject_key=bci.business_id,
                subject_type=SubjectType.BUSINESS_ID.value,
                is_experiment=True,
            )
        )
        push_target = ['business.add_review', bci.business_id]
        if experiment_variant == ExperimentVariants.VARIANT_A:
            push_target.extend([appointment.first_booking_id, 'in_experiment'])
        elif IncludeBookingIdInAddReviewPushTargetFlag():
            push_target.append(appointment.first_booking_id)

        send_push_notification(
            receivers=receivers,
            alert=text,
            target=tuple(push_target),
            history_data=dict_merge(
                {
                    'booking_id': appointment.first_booking_id,
                    'business_id': bci.business_id,
                    'customer_id': bci.user_id,
                    'customer_card_id': bci.id,
                    'sender': NotificationHistory.SENDER_BUSINESS,
                },
                history_data,
            ),
        )

    @classmethod
    def event_tipping_experiment_push(cls, params, task_id):
        from webapps.notification.enums import NotificationTarget
        from webapps.notification.models import NotificationHistory
        from webapps.notification.push import notification_receivers_list
        from webapps.notification.tasks.push import send_push_notification
        from webapps.notification.models import TippingAppetiteNotificationRecord

        history_data = cls.get_history_data(params, task_id)
        params, appointment = cls.get_params_objects(params, 'appointment')
        bci = appointment.booked_for
        user_language = cls.get_language(bci.user, None)

        text = cls.render_template(
            cls.SCENARIO_NAME,
            'tipping_experiment',
            language=user_language,
            extension='push',
        )
        receivers = (
            notification_receivers_list(user_id=bci.user.id, customer=True)
            if bci.user is not None
            else []
        )
        send_push_notification(
            receivers=receivers,
            alert=text,
            target=(NotificationTarget.TIPPING_AFTER_APPT_EXPERIMENT, appointment.id),
            history_data=dict_merge(
                {
                    'booking_id': appointment.first_booking_id,
                    'business_id': bci.business_id,
                    'customer_id': bci.user_id,
                    'sender': NotificationHistory.SENDER_SYSTEM,
                },
                history_data,
            ),
        )

        TippingAppetiteNotificationRecord.objects.create(task_id=task_id, user_id=bci.user.id)

    @classmethod
    def send_email(cls, appointment, first_email=True, history_data=None):
        from webapps.notification.models import NotificationHistory
        from webapps.user.models import UnsubscribedEmail

        appointment.business.raise_if_customer_notifications_are_disabled()
        bci = recipient_for_review(appointment)

        business_review_url = urllib.parse.urljoin(
            settings.MARKETPLACE_URL,
            '/%s/dl/add-review/%d'
            % (
                settings.MARKETPLACE_LANG_COUNTRY,
                bci.business.id,
            ),
        )

        email = cls.get_customer_email_from_bci(bci) or appointment.customer_email or None
        if not UnsubscribedEmail.can_send_email(email):
            raise ScenarioSkipped({'unsubscribed': True})

        body = cls.render_template(
            cls.SCENARIO_NAME,
            'reminder',
            language=cls.get_language(bci.user, None),
            template_variables={
                'business': bci.business,
                'user': bci.user,
                'business_review_url': business_review_url,
                'first_email': first_email,
                'customer_first_name': bci.user.first_name if bci.user_id else bci.first_name,
            },
        )

        if UseNoReplyAddressInReplyToFieldFlag():
            #  Along with the flag we can remove the get_business_emails_from_booking method.
            from_data = (
                bci.business.name,
                settings.NO_REPLY_EMAIL,
            )
        else:
            from_data = (bci.business.name, cls.get_business_emails_from_booking(appointment))

        send_email(
            to_addr=email,
            body=body,
            history_data=dict_merge(
                {
                    'booking_id': appointment.first_booking_id,
                    'business_id': bci.business_id,
                    'customer_id': bci.user_id,
                    'customer_card_id': bci.id,
                    'sender': NotificationHistory.SENDER_BUSINESS,
                },
                history_data,
            ),
            from_data=from_data,
            to_name=(bci.user.get_full_name() if bci.user_id else bci.full_name),
        )

    @classmethod
    def send_sms(cls, appointment):
        from webapps.booking.notifications.review import SMSReviewWithoutUserNotification

        SMSReviewWithoutUserNotification(
            None,
            appointment_id=appointment.id,
        ).send()

    @classmethod
    def create_short_review_notifications(cls, booking_id, bci=None):
        from webapps.booking.models import SubBooking

        bci_user_id, business_id = (
            SubBooking.objects.filter(
                id=booking_id,
            )
            .values_list(
                'appointment__booked_for__user_id',
                'appointment__business_id',
            )
            .first()
        )
        bci_user_id = bci.user.id if bci and bci.user else bci_user_id
        cls._create_popup_review_notification(bci_user_id, business_id, booking_id)

    @classmethod
    def _create_popup_review_notification(cls, bci_user_id, business_id, booking_id):
        from webapps.pop_up_notification.models import ShortReviewNotification

        if not bci_user_id:
            return  # not possible to create popup for non registered user
        if ShortReviewNotification.objects.filter(
            user_id=bci_user_id,
            business_id=business_id,
            subbooking_id=booking_id,
        ).exists():
            return

        srn = ShortReviewNotification(
            user_id=bci_user_id,
            business_id=business_id,
            valid_till=make_aware(datetime.datetime.today()) + datetime.timedelta(days=30),
            subbooking_id=booking_id,
        )
        srn.save()
        logger = logging.getLogger('booksy.notifications')
        logger.info('Create Short Review Notification: {}'.format(srn))

    @classmethod
    def _send_prompted_reviews_notification_if_applicable(cls, appointment):
        from webapps.booking.models import Appointment
        from webapps.business.notifications.prompted_reviews import (
            PromptedReviewsFiveCustomerAppointmentsFinishedNotification,
        )

        business_id = appointment.business_id
        if (
            appointment.type == Appointment.TYPE.CUSTOMER
            and cls._is_prompted_review_notification_applicable(business_id)
            and Appointment.objects.filter(
                business_id=business_id,
                type=Appointment.TYPE.CUSTOMER,
                status=Appointment.STATUS.FINISHED,
            ).count()
            == 5
        ):
            PromptedReviewsFiveCustomerAppointmentsFinishedNotification(
                None,
                business_id=business_id,
            ).send()

    @staticmethod
    def _is_prompted_review_notification_applicable(business_id):
        return KillSwitch.alive(KillSwitch.System.PROMPTED_REVIEWS_IOS) or KillSwitch.alive(
            KillSwitch.System.PROMPTED_REVIEWS_ANDROID
        )


# this allow for simple testing with
# import webapps.notification.scenarios
# reload(webapps.notification.scenarios).test_mail_review_send()
def test_mail_review_send():
    from webapps.booking.models import SubBooking

    booking = SubBooking.objects.get(id=472)
    # booking.booked_for.user.email = '<EMAIL>'
    # booking.booked_for.user.email = '<EMAIL>'
    # booking.booked_for.user.email = '<EMAIL>'
    # booking.booked_for.user.email = '<EMAIL>'
    booking.appointment.booked_for.user.save()

    print(BookingFinishedScenario.event_next_day({'booking_id': booking.id}, 'test'))
