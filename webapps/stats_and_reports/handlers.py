import urllib
import urllib.parse
from abc import ABC, abstractmethod

from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from rest_framework import status

import versions
from lib.segment_analytics.enums import EventType
from service.tools import AnalyticsTokensMixin, RequestHandler, session
from webapps.segment.tasks import analytics_business_report_generated_to_email
from webapps.stats_and_reports.commission_stats import CommissionStats
from webapps.stats_and_reports.reports.main_dashboard import create_main_reports_dashboard
from webapps.stats_and_reports.reports.simple_stats import (
    SimpleCommissionStats,
    SimpleStatsBase,
    create_simple_stats_thin_dashboard,
)
from webapps.stats_and_reports.serializers import (
    CommissionSimpleStatsSerializer,
    CommissionStatsRequestSerializer,
    CommissionStatsResponseSerializer,
    DashboardSerializer,
    MainDashboardSerializer,
    ReportDashboardRequestSerializer,
    ReportDashbordBaseRequestSerializer,
    ReportRequestSerializer,
    ReportSectionRequestSerializer,
    ReportSectionSerializer,
    ReportSendByEmailRequestSerializer,
    ReportSerializer,
    SimpleStatisticDashboardRequestSerializer,
    SimpleStatisticSingleTileRequestSerializer,
    SimpleStatisticTimeScopeRequestSerializer,
    SimpleStatsDashboardSerializer,
    SimpleStatsInstanceSerializer,
    SimpleStatsTilesListSerializer,
)
from webapps.stats_and_reports.tasks import send_report_by_email_task


class BaseRedirectHandler(RequestHandler):
    # 307 guarantees that the method and the body will not be changed when the redirected request is
    # made. See: https://developer.mozilla.org/en-US/docs/Web/HTTP/Status/307
    http_redirect_status = status.HTTP_307_TEMPORARY_REDIRECT

    @staticmethod
    def get_api_url() -> str:
        api_url = settings.API_URL
        if settings.LOCAL_DEPLOYMENT:
            api_url = settings.FRONTDESK_APP_URL
            api_url = api_url.replace('http://', 'https://')
        return api_url

    @staticmethod
    @abstractmethod
    def get_endpoint_path() -> str: ...

    def get_redirect_url(self, business_id, *args, **kwargs) -> str:
        api_url = self.get_api_url()
        url = urllib.parse.urljoin(
            api_url,
            f"/api/{settings.API_COUNTRY}/{versions.VERSION}"
            f"/business_api/me/stats/businesses/{business_id}"
            f"/{self.get_endpoint_path()}/?{self.request.query}",
        )

        return url


class HttpGetRedirectHandler(BaseRedirectHandler, ABC):
    # pylint: disable=too-many-ancestors
    @session(login_required=True)
    def get(self, business_id):
        url = self.get_redirect_url(business_id)
        self.redirect(url, status=self.http_redirect_status)


class HttpPostRedirectHandler(BaseRedirectHandler, ABC):
    # pylint: disable=too-many-ancestors
    @session(login_required=True)
    def post(self, business_id):
        url = self.get_redirect_url(business_id)
        self.redirect(url, status=self.http_redirect_status)


class ReportsHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @session(login_required=True)
    def get(self, business_id):
        """# pylint: disable=line-too-long
        swagger:
            summary: Get Business Statistics
            parameters:
                - name: business_id
                  description: Business id
                  type: integer
                  paramType: path
                  required: true
                - name: report_key
                  paramType: query
                  type: string
                  required: true
                  enum_from_const: webapps.stats_and_reports.reports.collection.AVAILABLE_REPORTS_KEYS
                - name: date_from
                  paramType: query
                  type: string
                  format: date
                  required: false
                - name: date_till
                  description: Inclusive
                  paramType: query
                  type: string
                  format: date
                  required: false
                - name: time_span
                  paramType: query
                  type: string
                  required: true
                  enum_from_const: webapps.stats_and_reports.reports.time_data.TimeScopeType
                - name: page
                  type: integer
                  paramType: query
                  defaultValue: 1
                - name: per_page
                  type: integer
                  paramType: query
                  defaultValue: 20
                - name: staffer_id
                  type: integer
                  paramType: query
                - name: staffer_performance_section
                  type: string
                  type: array
                  items:
                    type: string
                  paramType: query
        :swagger
        """
        business = self.business_with_manager(business_id)
        data = self._prepare_get_arguments(['staffer_performance_section'])
        request_serializer = ReportRequestSerializer(
            data=data,
            context={'business_id': business_id},
        )
        self.validate_serializer(request_serializer)

        report = request_serializer.get_report(business, self.language)
        report_serializer = ReportSerializer(
            instance=report,
            context={
                'business': business,
            },
        )
        self.finish_with_json(status.HTTP_200_OK, report_serializer.data)


class ReportsDownloadHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @session(login_required=True)
    def get(self, business_id):
        """# pylint: disable=line-too-long
        swagger:
            summary: Download report as xlsx spreadsheet file
            parameters:
                - name: business_id
                  description: Business id
                  type: integer
                  paramType: path
                  required: true
                - name: report_key
                  paramType: query
                  type: string
                  required: true
                  enum_from_const: webapps.stats_and_reports.reports.collection.AVAILABLE_REPORTS_KEYS
                - name: date_from
                  paramType: query
                  type: string
                  format: date
                  required: true
                - name: date_till
                  description: Inclusive
                  paramType: query
                  type: string
                  format: date
                  required: false
                - name: time_span
                  paramType: query
                  type: string
                  required: true
                  enum_from_const: webapps.stats_and_reports.reports.time_data.TimeScopeType
                - name: staffer_id
                  type: integer
                  paramType: query
                - name: staffer_performance_section
                  type: string
                  type: array
                  items:
                    type: string
                  paramType: query
            produces:
                - application/octet-stream
        :swagger
        """
        business = self.business_with_manager(business_id)
        data = self._prepare_get_arguments(['staffer_performance_section'])
        serializer = ReportRequestSerializer(
            data=data,
            context={'business_id': business_id},
        )
        self.validate_serializer(serializer)
        report = serializer.get_report(business, self.language, paginate=False)

        filename = urllib.parse.quote(report.filename)
        self.set_header('Content-Type', 'application/octet-stream')
        self.set_header('Content-Disposition', f'attachment; filename="{filename}"')
        self.write(report.get_spreadsheet().read())
        self.finish()


class ReportsSendByEmailRedirectHandler(HttpPostRedirectHandler):
    # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @staticmethod
    def get_endpoint_path() -> str:
        return 'report/send_by_email'


class ReportsSendByEmailHandler(AnalyticsTokensMixin, RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @session(login_required=True)
    def post(self, business_id):
        """# pylint: disable=line-too-long
        swagger:
            summary: Sends report xlsx spreadsheet file via email
            parameters:
                - name: business_id
                  description: Business id
                  type: integer
                  paramType: path
                  required: true
                - name: report_key
                  paramType: query
                  type: string
                  required: true
                  enum_from_const: webapps.stats_and_reports.reports.collection.AVAILABLE_REPORTS_KEYS
                - name: date_from
                  paramType: query
                  type: string
                  format: date
                  required: true
                - name: date_till
                  description: Inclusive
                  paramType: query
                  type: string
                  format: date
                  required: false
                - name: time_span
                  paramType: query
                  type: string
                  required: true
                  enum_from_const: webapps.stats_and_reports.reports.time_data.TimeScopeType
                - name: email
                  paramType: query
                  type: string
                  format: email
                  required: true
                - name: staffer_id
                  type: integer
                  paramType: query
                - name: staffer_performance_section
                  type: string
                  type: array
                  items:
                    type: string
                  paramType: query
        :swagger
        """
        self.business_with_manager(business_id)
        data = self._prepare_get_arguments(['staffer_performance_section'])
        serializer = ReportSendByEmailRequestSerializer(
            data=data,
            context={'business_id': business_id},
        )
        validated_data = self.validate_serializer(serializer)
        send_report_by_email_task.delay(
            business_id,
            validated_data['date_from'].isoformat() if validated_data['date_from'] else None,
            validated_data['date_till'].isoformat() if validated_data['date_till'] else None,
            validated_data['time_span'],
            validated_data['report_key'],
            validated_data['email'],
            self.language,
            extra={
                'staffer_id': validated_data.get('staffer_id'),
                'staffer_performance_sections': validated_data.get(
                    'staffer_performance_section',
                    [],
                ),
            },
        )
        if self.firebase_auth_dict:
            analytics_business_report_generated_to_email.delay(
                user_id=self.user.id,
                report_key=validated_data['report_key'],
                auth_data=self.firebase_auth_dict,
                context={
                    'source_id': self.booking_source.id,
                    'business_id': business_id,
                    'event_type': EventType.BUSINESS,
                    'session_user_id': self.user.id,
                },
            )
        self.finish_with_json(status.HTTP_200_OK, {})


class ReportsDashboardHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @session(login_required=True)
    def get(self, business_id):
        """# pylint: disable=line-too-long
        swagger:
            parameters:
                - name: business_id
                  description: Business id
                  type: integer
                  paramType: path
                  required: true
                - name: dashboard
                  paramType: query
                  type: string
                  required: true
                  enum_from_const: webapps.stats_and_reports.reports.dashboards.AVAILABLE_DASHBOARDS_KEYS
                - name: date_from
                  paramType: query
                  type: string
                  format: date
                  required: true
                - name: date_till
                  description: Inclusive
                  paramType: query
                  type: string
                  format: date
                  required: false
                - name: time_span
                  paramType: query
                  type: string
                  required: true
                  enum_from_const: webapps.stats_and_reports.reports.time_data.TimeScopeType
        :swagger
        """
        business = self.business_with_manager(business_id)
        data = self._prepare_get_arguments()
        request_serializer = ReportDashboardRequestSerializer(data=data)
        self.validate_serializer(request_serializer)

        dashboard = request_serializer.get_dashboard(business, self.language)
        dashboard_serializer = DashboardSerializer(
            instance=dashboard,
            context={
                'business': business,
            },
        )
        self.finish_with_json(status.HTTP_200_OK, dashboard_serializer.data)


class ReportsMainDashboardHandler(RequestHandler):
    """Main Reports Dashboard is quite different from other dashboards.
    It does not contain listing of reports, it also does not contain preview
    of reports tables. Therefore we use dedicated endpoint and data
    structures for it.
    """

    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @session(login_required=True)
    def get(self, business_id):
        """# pylint: disable=line-too-long
        swagger:
            parameters:
                - name: business_id
                  description: Business id
                  type: integer
                  paramType: path
                  required: true
                - name: date_from
                  paramType: query
                  type: string
                  format: date
                  required: true
                - name: date_till
                  description: Inclusive
                  paramType: query
                  type: string
                  format: date
                  required: false
                - name: time_span
                  paramType: query
                  type: string
                  required: true
                  enum_from_const: webapps.stats_and_reports.reports.time_data.TimeScopeType
            type: ReportsMainDashboardResponse
        :swagger
        swaggerModels:
            AppointmentsStatsData:
                id: AppointmentsStatsData
                properties:
                    total_appointments_count:
                        type: integer
                    confirmed_appointments_count:
                        type: integer
                    finished_appointments_count:
                        type: integer
                    noshow_appointments_count:
                        type: integer
                    cancelled_appointments_count:
                        type: integer
            RevenueStatsData:
                id: RevenueStatsData
                properties:
                    total_revenue:
                        type: number
                    services_revenue:
                        type: number
                    products_revenue:
                        type: number
                    tips_revenue:
                        type: number
                    gift_cards_revenue:
                        type: number
                    memberships_revenue:
                        type: number
                    packages_revenue:
                        type: number
            ClientsStatsData:
                id: ClientsStatsData
                properties:
                    total_clients:
                        type: integer
                    new_clients:
                        type: integer
                    returning_clients:
                        type: integer
            ReportsMainDashboardResponse:
                id: ReportsMainDashboardResponse
                properties:
                    date_from:
                        type: string
                        format: date
                    date_till:
                        type: string
                        format: date
                    appointments_stats:
                        type: AppointmentsStatsData
                    revenue_stats:
                        type: RevenueStatsData
                    clients_stats:
                        type: ClientsStatsData
                    appointments_chart:
                        type: object
                    revenue_chart:
                        type: object
        :swaggerModels
        """
        business = self.business_with_manager(business_id)
        data = self._prepare_get_arguments()
        request_serializer = ReportDashbordBaseRequestSerializer(data=data)
        self.validate_serializer(request_serializer)

        scope = request_serializer.get_time_data_scope(business, self.language)
        main_dashboard = create_main_reports_dashboard(scope)
        dashboard_serializer = MainDashboardSerializer(instance=main_dashboard)
        self.finish_with_json(status.HTTP_200_OK, dashboard_serializer.data)


class ReportSectionHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @session(login_required=True)
    def get(self, business_id):
        """# pylint: disable=line-too-long
        swagger:
            summary: >
                Get report section (allows sorting and pagination, section-wise)
            parameters:
                - name: business_id
                  description: Business id
                  type: integer
                  paramType: path
                  required: true
                - name: section_key
                  paramType: query
                  type: string
                  required: true
                  enum_from_const: webapps.stats_and_reports.reports.collection.AVAILABLE_REPORT_SECTIONS_KEYS
                - name: date_from
                  paramType: query
                  type: string
                  format: date
                  required: true
                - name: date_till
                  description: Inclusive
                  paramType: query
                  type: string
                  format: date
                  required: false
                - name: resource_id
                  description: >
                    Some sections (e.g. Staffer appointments list) require the
                    id of particular resource.
                  paramType: query
                  type: integer
                  required: false
                - name: page
                  type: integer
                  paramType: query
                  defaultValue: 1
                - name: per_page
                  type: integer
                  paramType: query
                  defaultValue: 20
                - name: sorting
                  type: string
                  paramType: query
                  required: false
                - name: time_span
                  paramType: query
                  type: string
                  required: true
                  enum_from_const: webapps.stats_and_reports.reports.time_data.TimeScopeType
        :swagger
        """
        business = self.business_with_manager(business_id)
        data = self._prepare_get_arguments()
        request_serializer = ReportSectionRequestSerializer(
            data=data,
            context={'business_id': business_id},
        )
        self.validate_serializer(request_serializer)

        section = request_serializer.get_report_section(business, self.language)
        section_serializer = ReportSectionSerializer(instance=section)
        self.finish_with_json(status.HTTP_200_OK, section_serializer.data)


class SimpleStatisticHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @session(login_required=True)
    def get(self, business_id):
        """# pylint: disable=line-too-long
        swagger:
            summary: DEPRECATED
            notes: >
                DEPRECATED - Please use:
                    "GET /business_api/me/businesses/(id)/stats/simple_statistics/tiles/"
                    "GET /business_api/me/businesses/(id)/stats/simple_statistics/tiles/(key)/"
                    "GET /business_api/me/businesses/(id)/stats/simple_statistics/commission_stats/"
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: time_span
                  paramType: query
                  type: string
                  required: true
                  enum_from_const: webapps.stats_and_reports.reports.time_data.SimpleStatTimeScopeType
        :swagger
        """
        business = self.business_with_staffer(business_id)
        data = self._prepare_get_arguments()
        request_serializer = SimpleStatisticDashboardRequestSerializer(data=data)
        self.validate_serializer(request_serializer)

        statistic = request_serializer.get_statistic(
            business,
            self.language,
            self.user_staffer,
            self.access_level,
        )
        statistic_serializer = SimpleStatsDashboardSerializer(
            instance=statistic,
        )
        self.finish_with_json(status.HTTP_200_OK, statistic_serializer.data)


class SimpleStatisticTilesListRedirectHandler(HttpGetRedirectHandler):
    # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @staticmethod
    def get_endpoint_path() -> str:
        return 'simple_statistics/tiles'


class SimpleStatisticTilesListHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @session(login_required=True)
    def get(self, business_id):
        """
        swagger:
            summary: Lists available simple stats tiles keys
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
            type: TilesListResponse
        :swagger
        swaggerModels:
            StatsTile:
                id: StatsTile
                properties:
                    key:
                        type: string
                    title:
                        type: string
            TilesList:
                id: TilesList
                properties:
                    today_label:
                        type: string
                    week_label:
                        type: string
                    month_label:
                        type: string
                    year_label:
                        type: string
                    tiles:
                        type: array
                        items:
                            type: StatsTile
            TilesListResponse:
                id: TilesListResponse
                properties:
                    data:
                        type: TilesList
        :swaggerModels
        """
        business = self.business_with_staffer(business_id)
        simple_stats_thin_dashboard = create_simple_stats_thin_dashboard(business, self.language)
        response_serializer = SimpleStatsTilesListSerializer(instance=simple_stats_thin_dashboard)
        # We always wrap objects in a "data" field, following:
        # https://phauer.com/2015/restful-api-design-best-practices/#wrap-the-actual-data-in-a-data-field  # pylint: disable=line-too-long
        self.finish_with_json(status.HTTP_200_OK, {'data': response_serializer.data})


class SimpleStatisticTileDetailsRedirectHandler(BaseRedirectHandler):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @session(login_required=True)
    def get(self, business_id, simple_stats_tile_key):
        url = self.get_redirect_url(business_id, tile_key=simple_stats_tile_key)
        self.redirect(url, status=self.http_redirect_status)

    @staticmethod
    def get_endpoint_path() -> str:
        return 'simple_statistics/tiles'

    def get_redirect_url(self, business_id, *args, **kwargs):
        api_url = self.get_api_url()
        url = urllib.parse.urljoin(
            api_url,
            f"/api/{settings.API_COUNTRY}/{versions.VERSION}/business_api/me/stats/businesses"
            f"/{business_id}/{self.get_endpoint_path()}/{kwargs['tile_key']}/?{self.request.query}",
        )
        return url


class SimpleStatisticTileDetailsHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @session(login_required=True)
    def get(self, business_id, simple_stats_tile_key):
        """# pylint: disable=line-too-long
        swagger:
            summary: Get single simple stats tile data
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: simple_stats_tile_key
                  type: string
                  paramType: path
                  required: true
                  enum_from_const: webapps.stats_and_reports.reports.simple_stats.SimpleStatsThinDashboard.stats_classes_keys
                - name: time_span
                  paramType: query
                  type: string
                  required: true
                  enum_from_const: webapps.stats_and_reports.reports.time_data.SimpleStatTimeScopeType
            type: StatsTileDetailsResponse
        :swagger
        swaggerModels:
            ValueWithDelta:
                id: ValueWithDelta
                properties:
                    value:
                        type: string
                    delta:
                        type: number
            StatsDataPoint:
                id: StatsDataPoint
                properties:
                    date:
                        type: string
                    value:
                        type: string
            TimeLinePoint:
                id: TimeLinePoint
                properties:
                    date:
                        type: string
                    label:
                        type: string
            StatsBottomBar:
                id: StatsBottomBar
                properties:
                    title:
                        type: string
                    data_points:
                        type: array
                        items:
                            type: StatsDataPoint
            StatsDataSeries:
                id: StatsDataSeries
                properties:
                    key:
                        type: string
                    label:
                        type: string
                    is_forecast:
                        type: boolean
                    data_points:
                        type: array
                        items:
                            type: StatsDataPoint
            StatsChart:
                id: StatsChart
                properties:
                    key:
                        type: string
                    title:
                        type: string
                    max_value:
                        type: string
                    timeline:
                        type: array
                        items:
                            type: TimeLinePoint
                    data_series:
                        type: array
                        items:
                            type: StatsDataSeries
                    bottom_bar:
                        type: StatsBottomBar
            StatsSummaryBoxEntry:
                id: StatsSummaryBoxEntry
                properties:
                    label:
                        type: string
                    value:
                        type: ValueWithDelta
                    hint:
                        type: string
                    is_negative_indicator:
                        type: boolean
            StatsSummaryBox:
                id: StatsSummaryBox
                properties:
                    entries:
                        type: array
                        items:
                            type: StatsSummaryBoxEntry
            StatsTileDetails:
                id: StatsTileDetails
                properties:
                    key:
                        type: string
                    title:
                        type: string
                    header_value:
                        type: ValueWithDelta
                    chart:
                        type: StatsChart
                    summary_box:
                        type: StatsSummaryBox
            StatsTileDetailsResponse:
                id: StatsTileDetailsResponse
                properties:
                    data:
                        type: StatsTileDetails
        :swaggerModels
        """
        business = self.business_with_staffer(business_id)
        data = self._prepare_get_arguments()
        data['simple_stats_tile_key'] = simple_stats_tile_key
        request_serializer = SimpleStatisticSingleTileRequestSerializer(
            data=data,
            context={'business_id': business_id},
        )
        self.validate_serializer(request_serializer)

        simple_stats_tile: SimpleStatsBase = request_serializer.get_simple_stats_instance(
            business,
            self.language,
            self.user_staffer,
            self.access_level,
        )
        serializer = SimpleStatsInstanceSerializer(instance=simple_stats_tile)
        # We always wrap objects in a "data" field, following:
        # https://phauer.com/2015/restful-api-design-best-practices/#wrap-the-actual-data-in-a-data-field  # pylint: disable=line-too-long
        self.finish_with_json(status.HTTP_200_OK, {'data': serializer.data})


class SimpleCommissionStatsRedirectHandler(HttpGetRedirectHandler):
    # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @staticmethod
    def get_endpoint_path() -> str:
        return 'simple_statistics/commission_stats'


class SimpleCommissionStatsHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @session(login_required=True)
    def get(self, business_id):
        """# pylint: disable=line-too-long
        swagger:
            summary: Get simple stats commissions summary
            note: >
                Owner and manager do not see "Your commissions" section.
                Lite or Unknown businesses also do not see "Your commissions" section.
                Pro businesses see section only if they have any default commissions set.
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: time_span
                  paramType: query
                  type: string
                  required: true
                  enum_from_const: webapps.stats_and_reports.reports.time_data.SimpleStatTimeScopeType
            type: SimpleCommissionStatsResponse
        :swagger
        swaggerModels:
            SimpleCommissionStats:
                id: SimpleCommissionStats
                properties:
                    current_week:
                        type: string
                    current_month:
                        type: string
            SimpleCommissionStatsResponse:
                id: SimpleCommissionStatsResponse
                properties:
                    data:
                        type: SimpleCommissionStats
                        description: Nullable
        :swaggerModels
        """
        business = self.business_with_staffer(business_id)
        data = self._prepare_get_arguments()
        request_serializer = SimpleStatisticTimeScopeRequestSerializer(data=data)
        self.validate_serializer(request_serializer)

        time_scope = request_serializer.get_time_data_scope(business, self.language)
        simple_commission_stats = SimpleCommissionStats(
            time_scope, self.user_staffer, self.access_level
        )

        response_data = None
        if simple_commission_stats.commission_stats:
            response_data = CommissionSimpleStatsSerializer(
                simple_commission_stats.commission_stats
            ).data

        # We always wrap objects in a "data" field, following:
        # https://phauer.com/2015/restful-api-design-best-practices/#wrap-the-actual-data-in-a-data-field  # pylint: disable=line-too-long
        self.finish_with_json(status.HTTP_200_OK, {'data': response_data})


class CommissionStatsRedirectHandler(HttpGetRedirectHandler):
    # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @staticmethod
    def get_endpoint_path() -> str:
        return 'commission_stats'


class CommissionStatsHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @session(login_required=True)
    def get(self, business_id):
        """# pylint: disable=line-too-long
        swagger:
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: time_span
                  paramType: query
                  type: string
                  required: true
                  enum_from_const: webapps.stats_and_reports.commission_stats.CommissionStatsScopeType
            type: CommissionStatsResponse
        :swagger
        swaggerModels:
        CommissionStatsEntry:
            id: CommissionStatsEntry
            properties:
                year:
                    type: integer
                month:
                    type: integer
                week:
                    type: integer
                label:
                    type: string
                services_total:
                    type: number
                products_total:
                    type: number
                others_total:
                    type: number
                total_commission:
                    type: number

        CommissionStatsResponse:
            id: CommissionStatsResponse
            properties:
                staffer:
                    type: integer
                time_span:
                    type: string
                last_x_weeks:
                    type: integer
                last_x_months:
                    type: integer
                entries:
                    type: array
                    items:
                        type: CommissionStatsEntry
        :swaggerModels
        """
        business = self.business_with_staffer(business_id)
        data = self._prepare_get_arguments()
        request_serializer = CommissionStatsRequestSerializer(data=data)
        self.validate_serializer(request_serializer)

        commission_stats = CommissionStats(
            business=business,
            staffer=self.user_staffer,
            time_span=request_serializer.validated_data['time_span'],
        )

        response_serializer = CommissionStatsResponseSerializer(instance=commission_stats)
        self.finish_with_json(status.HTTP_200_OK, response_serializer.data)
