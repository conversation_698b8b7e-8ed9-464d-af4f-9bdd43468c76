import datetime
from urllib.parse import urlencode
from typing import Callable, ContextManager, Type

import pytest
import pytz
from django.conf import settings
from django.core import mail
from django.test import override_settings
from django.utils.translation import activate, get_language
from freezegun import freeze_time
from mock import MagicMock, patch
from model_bakery import baker
from parameterized import parameterized, parameterized_class
from rest_framework import status

from lib.feature_flag.bug import UseTodayDateInBusinessTZForSimpleStatsChart
from lib.feature_flag.feature import FrenchCertificationVoucherSummaryReportFlag
from lib.tests.utils import override_eppo_feature_flag
from lib.tools import id_to_external_api, tznow
from service.pos.tests import create_receipt
from service.tests import BaseAsyncHTTPTest, dict_assert
from webapps.booking.models import BookingSources
from webapps.booking.tests.utils import create_appointment
from webapps.business.baker_recipes import business_recipe, staffer_recipe
from webapps.business.models import Business, Resource
from webapps.consts import FRONTDESK
from webapps.french_certification.services import JETService
from webapps.invoicing.tests.common import french_certification_enabled
from webapps.pos.enums import receipt_status
from webapps.pos.models import POS, TransactionRow
from webapps.segment.consts import UserRoleEnum
from webapps.segment.enums import BooksyAppVersions, DeviceTypeName
from webapps.stats_and_reports.reports.base import ReportSection
from webapps.stats_and_reports.reports.other.appointments_list_report import (
    AppointmentsList as AppointmentsListBusinessPerformance,
)
from webapps.stats_and_reports.reports.other.performance_report import (
    ClientsSummary as ClientsSummaryBusinessPerformance,
    AppointmentsSummary as AppointmentsSummaryBusinessPerformance,
)
from webapps.stats_and_reports.reports.other.register_report import (
    CashRegistersSummary as CashRegistersSummaryBusinessPerformance,
    CashRegistersTransactions as CashRegistersTransactionsBusinessPerformance,
)
from webapps.stats_and_reports.reports.other.sales_report import (
    SalesByServices as SalesByServicesBusinessPerformance,
    SalesByProducts as SalesByProductsBusinessPerformance,
)
from webapps.stats_and_reports.reports.other.staff_performance_report import (
    StaffMembersRevenue as StaffMembersRevenueBusinessPerformance,
)
from webapps.stats_and_reports.report_keys import ReportKeys
from webapps.stats_and_reports.reports import (
    AppointmentsList,
    AppointmentsListByServices,
    AppointmentsListByStaffer,
    AppointmentsProfitability,
    AppointmentsSummary,
    CashRegistersSummary,
    CashRegistersTransactions,
    CategoriesServicesSummary,
    ClientsByTypesSection,
    ClientsList,
    ClientsSummaryReport,
    HighRotatingProductsDashboardSection,
    HighlightsSection,
    LowStockDashboardSection,
    MembershipsRedemptions,
    NewClients,
    OutstandingInvoices,
    PackagesRedemptions,
    ReturningClients,
    SalesByMemberships,
    SalesByPackages,
    SalesByProducts,
    SalesByServices,
    SalesLogBeforeFrenchMigrationSection,
    SalesSummaryBeforeFrenchMigrationSection,
    SalesTrends,
    SlippingAwayClients,
    StaffGratuity,
    StaffMembersCommissionSection,
    StockMovementSummary,
    TaxesSummary,
    Top10StaffMembersRevenueSection,
    TopAddonsSection,
    TopClientsSection,
    TopServicesSection,
    TotalRevenueSection,
    PaymentMethodsArchiveSection,
)
from webapps.stats_and_reports.reports.other.executive_summary import TotalRevenue
from webapps.stats_and_reports.reports.revenue.membership_summary import (
    MembershipsActiveSection,
    MembershipsCloseToExpirationSection,
    MembershipsExpiredSection,
)
from webapps.stats_and_reports.reports.revenue.packages_summary import (
    PackagesSummaryActiveSection,
    PackagesSummaryExpiredCloseSection,
    PackagesSummaryExpiredSection,
)
from webapps.stats_and_reports.reports.simple_stats import (
    RevenueSimpleStats,
    SimpleStatsThinDashboard,
)
from webapps.stats_and_reports.reports.time_data import SimpleStatTimeScopeType, TimeScopeType
from webapps.stripe_integration.baker_recipes import stripe_account_recipe
from webapps.user.baker_recipes import user_recipe
from webapps.user.enums import AuthOriginEnum
from webapps.voucher.enums import VoucherStatus, VoucherType
from webapps.voucher.models import Voucher, VoucherTemplate


@pytest.mark.django_db
class ReportsSendByEmailHandlerTests(BaseAsyncHTTPTest):
    url = '/business_api/me/stats/businesses/{business_id}/report/send_by_email/?'

    @override_settings(POS__STRIPE_TERMINAL=True)
    @override_settings(POS__TAP_TO_PAY=True)
    @patch('lib.tagmanager.client.DISABLED_DURING_PYTESTS', False)
    @patch('lib.tagmanager.client.GTMClient._request_api')
    def test_post(self, request_api_mock):
        frontdesk_source, _ = BookingSources.objects.get_or_create(
            app_type=BookingSources.BUSINESS_APP,
            name=FRONTDESK,
            defaults={
                'api_key': 'frontdesk-test-api-key',
            },
        )
        baker.make(POS, business=self.business, marketpay_enabled=True)
        url = self.url.format(business_id=self.business.id)
        query_params = {
            'report_key': ReportKeys.BusinessPerformanceReport,
            'date_from': '2021-01-01',
            'time_span': TimeScopeType.YEAR,
            'email': '<EMAIL>',
        }
        url += urlencode(query_params)

        extra_headers = {'X-User-Pseudo-ID': 'TEST.1234', 'X-API-KEY': frontdesk_source.api_key}
        resp = self.fetch(url, method='POST', body={}, extra_headers=extra_headers)
        assert resp.code == status.HTTP_200_OK, resp.json

        dict_assert(
            request_api_mock.call_args_list[0][1],
            {
                'endpoint': '/p/collect',
                'method': 'post',
                'payload': {
                    'firebase_auth': {
                        'client_id': 'TEST.1234',
                    },
                    'events': [
                        {
                            'params': {
                                'generated_report_type': 'business_performance_report',
                                'email': self.business.owner.email,
                            },
                            'name': 'Business_Report_Generated_To_Email',
                        }
                    ],
                    'user_id': id_to_external_api(self.user.id),
                    'user_properties': {
                        'business_id': {'value': id_to_external_api(self.business.id)},
                        'country': {'value': settings.API_COUNTRY},
                        'offer_type': {'value': str(Business.Package.UNKNOWN.label)},
                        'business_phone': {'value': ''},
                        'app_version': {'value': BooksyAppVersions.B30},
                        'device_type': {'value': str(DeviceTypeName.DESKTOP)},
                        'user_role': {'value': str(UserRoleEnum.OWNER)},
                        'logged_in_user_id': {'value': id_to_external_api(self.user.id)},
                    },
                },
            },
        )

        assert mail.outbox[0].subject == 'Detailed Business Performance Report 1/1/21 - 12/31/21'
        assert mail.outbox[0].to == ['<EMAIL>']
        assert mail.outbox[0].attachments[0][0] == 'business-performance-report.xlsx'


@pytest.mark.django_db
@pytest.mark.usefixtures('default_voucher_background')
class SimpleStatisticHandlersTests(BaseAsyncHTTPTest):
    maxDiff = None

    @freeze_time(datetime.datetime(2021, 10, 27, 12, tzinfo=pytz.UTC))
    def test_get_simple_stats_tile_list(self):
        url = f'/business_api/me/stats/businesses/{self.business.id}/simple_statistics/tiles/'
        resp = self.fetch(url, method='GET')
        self.assertEqual(resp.code, status.HTTP_200_OK, resp.json)
        self.assertDictEqual(
            resp.json,
            {
                'data': {
                    'today_label': 'Today',
                    'week_label': 'Week #44',
                    'month_label': 'October',
                    'year_label': '2021',
                    'tiles': [
                        {'key': 'revenue_simple_stats', 'title': 'Revenue'},
                        {'key': 'appointments_simple_stats', 'title': 'Appointments'},
                        {'key': 'clients_simple_stats', 'title': 'Clients'},
                    ],
                },
            },
        )

    @freeze_time(datetime.datetime(2021, 10, 27, 12, tzinfo=pytz.UTC))
    def test_get_simple_stats_tile_list_es_mx(self):
        previous_language = get_language()
        url = f'/business_api/me/stats/businesses/{self.business.id}/simple_statistics/tiles/'
        resp = self.fetch(url, method='GET', extra_headers={'Accept-Language': 'es_MX'})
        self.assertEqual(resp.code, status.HTTP_200_OK, resp.json)
        self.assertDictEqual(
            resp.json,
            {
                'data': {
                    'today_label': 'Hoy',
                    'week_label': 'Semana #44',
                    'month_label': 'Octubre',
                    'year_label': '2021',
                    'tiles': [
                        {'key': 'revenue_simple_stats', 'title': 'Ingresos'},
                        {'key': 'appointments_simple_stats', 'title': 'Reservas'},
                        {'key': 'clients_simple_stats', 'title': 'Clientes'},
                    ],
                },
            },
        )
        activate(previous_language)

    def test_get_simple_stats_tile_data(self):
        for key in SimpleStatsThinDashboard.stats_classes_keys:
            url = (
                f'/business_api/me/stats/businesses/{self.business.id}/simple_statistics/tiles/'
                f'{key}/?time_span={SimpleStatTimeScopeType.MONTH}'
            )
            resp = self.fetch(url, method='GET')
            self.assertEqual(resp.code, status.HTTP_200_OK, resp.json)

    @patch(
        'webapps.stats_and_reports.reports.simple_stats.SimpleCommissionStats.'
        'can_show_your_commission_section',
        MagicMock(return_value=True),
    )
    def test_get_simple_commission_stats(self):
        url = (
            f'/business_api/me/stats/businesses/{self.business.id}/simple_statistics/'
            f'commission_stats/?time_span={SimpleStatTimeScopeType.MONTH}'
        )
        resp = self.fetch(url, method='GET')
        self.assertEqual(resp.code, status.HTTP_200_OK, resp.json)
        self.assertDictEqual(
            resp.json, {'data': {'current_week': '$0.00', 'current_month': '$0.00'}}
        )

    @freeze_time(datetime.datetime(2023, 3, 28, 10, tzinfo=pytz.timezone('America/Los_Angeles')))
    def test_timezone_change(self):
        business = business_recipe.make(time_zone_name='America/Los_Angeles')
        self.session = business.owner.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        pos = baker.make(POS, business=business)
        appointment = create_appointment(business=business)
        txn = create_receipt(pos, appointment.subbookings[0], receipt_status.PAYMENT_SUCCESS)

        gross_total = 99
        baker.make(
            TransactionRow,
            transaction=txn,
            type=TransactionRow.TRANSACTION_ROW_TYPE__SERVICE,
            subbooking=appointment.subbookings[0],
            gross_total=gross_total,
        )
        with freeze_time(
            datetime.datetime(2023, 3, 28, 21, tzinfo=pytz.timezone(business.time_zone_name))
        ):
            self.assertEqual(business.tznow.date(), datetime.date(2023, 3, 28))
            self.assertEqual(tznow().date(), datetime.date(2023, 3, 29))  # UTC - 29th

            url = (
                f'/business_api/me/stats/businesses/{business.id}/'
                'simple_statistics/tiles/revenue_simple_stats'
            )
            resp = self.fetch(url, args={'time_span': SimpleStatTimeScopeType.TODAY}, method='GET')
            self.assertEqual(resp.code, status.HTTP_200_OK)
            self.assertDictEqual(
                resp.json['data']['header_value'],
                {'delta': 0, 'value': f'${gross_total}'},
            )

    # Implemented test case to expose FLS-2344 and prove that fix works. Conditions to expose:
    #   *   Business in its timezone is on Sunday
    #           (in US, Sunday is the first day of week)
    #   *   Py datetime module uses local time and it is on Saturday
    #           (in US, Saturday is the last day of week)
    # In PL market issue is symmetric with regards to the fact that Monday is
    # first day of week. It relates to other markets as well.
    @freeze_time(datetime.datetime(2025, 1, 4, 23, minute=30, tzinfo=pytz.timezone("GMT")))
    @override_eppo_feature_flag({UseTodayDateInBusinessTZForSimpleStatsChart.flag_name: True})
    def test_simple_stats_returns_correct_result_near_to_end_of_week_with_different_timezones_flag_enabled(  # pylint: disable=line-too-long
        self,
    ):
        business = business_recipe.make(
            time_zone_name='Europe/Warsaw'
        )  # business day is Sunday (5-th Jan 25)
        self.session = business.owner.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

        self.assertEqual(business.tznow.date(), datetime.date(2025, 1, 5))
        self.assertEqual(tznow().date(), datetime.date(2025, 1, 4))

        url = (
            f'/business_api/me/stats/businesses/{business.id}/'
            'simple_statistics/tiles/revenue_simple_stats'
        )
        resp = self.fetch(url, args={'time_span': SimpleStatTimeScopeType.TODAY}, method='GET')
        self.assertEqual(resp.code, status.HTTP_200_OK)
        actual_dates = list(
            map(lambda field: field['date'], resp.json['data']['chart']['timeline'])
        )
        expected_dates = [
            '2025-01-05',
            '2025-01-06',
            '2025-01-07',
            '2025-01-08',
            '2025-01-09',
            '2025-01-10',
            '2025-01-11',
        ]
        self.assertEqual(actual_dates, expected_dates)

    @freeze_time(datetime.datetime(2025, 1, 4, 23, minute=30, tzinfo=pytz.timezone("GMT")))
    def test_simple_stats_returns_correct_result_near_to_end_of_week_with_different_timezones_flag_disabled(  # pylint: disable=line-too-long
        self,
    ):
        business = business_recipe.make(
            time_zone_name='Europe/Warsaw'
        )  # business day is Sunday (5-th Jan 25)
        self.session = business.owner.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

        self.assertEqual(business.tznow.date(), datetime.date(2025, 1, 5))
        self.assertEqual(tznow().date(), datetime.date(2025, 1, 4))

        url = (
            f'/business_api/me/stats/businesses/{business.id}/'
            'simple_statistics/tiles/revenue_simple_stats'
        )
        resp = self.fetch(url, args={'time_span': SimpleStatTimeScopeType.TODAY}, method='GET')
        self.assertEqual(resp.code, status.HTTP_500_INTERNAL_SERVER_ERROR)


@pytest.mark.django_db
class ReportsDashboardHandlerTests(BaseAsyncHTTPTest):
    @staticmethod
    def get_reports_dashboard_path(business_id: int) -> str:
        return f'/business_api/me/stats/businesses/{business_id}/dashboard/'

    @staticmethod
    def get_args():
        return {
            'date_from': '2023-03-01',
            'date_till': '2023-03-31',
            'time_span': 'month',
            'dashboard': 'cash_flow',
        }

    @override_settings(POS__PAY_BY_APP=True)
    def test_business_without_stripe_account(self):
        business = business_recipe.make(time_zone_name='America/Los_Angeles')
        self.session = business.owner.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        baker.make(POS, business=business, marketpay_enabled=True)

        reports_dashboard_path = self.get_reports_dashboard_path(business.id)
        dashboard_resp = self.fetch(reports_dashboard_path, args=self.get_args(), method='GET')
        self.assertNotIn(
            {'key': 'stripe_payout_batches', 'title': 'Payouts Batches', 'is_new': False},
            dashboard_resp.json['reports'],
        )
        self.assertNotIn(
            {
                'key': 'bcr_payout_batches',
                'title': 'Booksy Card Reader Payouts Batches',
                'is_new': False,
            },
            dashboard_resp.json['reports'],
        )
        self.assertNotIn(
            {
                'key': 'ttp_payout_batches',
                'title': 'Tap to Pay Payouts Batches',
                'is_new': False,
            },
            dashboard_resp.json['reports'],
        )
        self.assertIn(
            {'key': 'payout_batches', 'title': 'Payouts Batches', 'is_new': False},
            dashboard_resp.json['reports'],
        )

    @override_settings(POS__PAY_BY_APP=True)
    def test_business_with_stripe_account_force_stripe_pba_off(self):
        business = business_recipe.make(time_zone_name='America/Los_Angeles')
        self.session = business.owner.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        pos = baker.make(POS, business=business, marketpay_enabled=True)
        stripe_account_recipe.make(pos=pos)

        reports_dashboard_path = self.get_reports_dashboard_path(business.id)
        dashboard_resp = self.fetch(reports_dashboard_path, args=self.get_args(), method='GET')
        self.assertNotIn(
            {'key': 'stripe_payout_batches', 'title': 'Payouts Batches', 'is_new': False},
            dashboard_resp.json['reports'],
        )
        self.assertIn(
            {'key': 'payout_batches', 'title': 'Payouts Batches', 'is_new': False},
            dashboard_resp.json['reports'],
        )

    @override_settings(POS__PAY_BY_APP=True)
    def test_business_with_stripe_account_force_stripe_pba_on(self):
        business = business_recipe.make(time_zone_name='America/Los_Angeles')
        self.session = business.owner.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        pos = baker.make(POS, business=business, marketpay_enabled=True)
        pos.pos_refactor_stage2_enabled = True
        pos._force_stripe_pba = True  # pylint: disable=protected-access
        pos.save()
        stripe_account_recipe.make(pos=pos)

        reports_dashboard_path = self.get_reports_dashboard_path(business.id)
        dashboard_resp = self.fetch(reports_dashboard_path, args=self.get_args(), method='GET')
        self.assertIn(
            {'key': 'stripe_payout_batches', 'title': 'Payouts Batches', 'is_new': False},
            dashboard_resp.json['reports'],
        )
        self.assertNotIn(
            {
                'key': 'bcr_payout_batches',
                'title': 'Booksy Card Reader Payouts Batches',
                'is_new': False,
            },
            dashboard_resp.json['reports'],
        )
        self.assertNotIn(
            {
                'key': 'ttp_payout_batches',
                'title': 'Tap to Pay Payouts Batches',
                'is_new': False,
            },
            dashboard_resp.json['reports'],
        )
        self.assertNotIn(
            {'key': 'payout_batches', 'title': 'Payouts Batches', 'is_new': False},
            dashboard_resp.json['reports'],
        )

    def test_reports_based_on_flags_all_off(self):
        business = business_recipe.make(time_zone_name='America/Los_Angeles')
        self.session = business.owner.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        baker.make(POS, business=business)

        reports_dashboard_path = self.get_reports_dashboard_path(business.id)

        with override_settings(
            POS__PAY_BY_APP=False, POS__TAP_TO_PAY=False, POS__STRIPE_TERMINAL=False
        ):
            dashboard_resp = self.fetch(reports_dashboard_path, args=self.get_args(), method='GET')
            reports = dashboard_resp.json['reports']
            self.assertEqual(len(reports), 2)
            self.assertIn(
                {
                    'key': 'cash_registers_transactions',
                    'title': 'Cash registers transactions',
                    'is_new': False,
                },
                reports,
            )
            self.assertIn(
                {
                    'key': 'cash_registers_summary',
                    'title': 'Cash registers summary',
                    'is_new': False,
                },
                reports,
            )

    def test_reports_based_on_flags_bcr_on(self):
        business = business_recipe.make(time_zone_name='America/Los_Angeles')
        self.session = business.owner.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        baker.make(POS, business=business)

        reports_dashboard_path = self.get_reports_dashboard_path(business.id)

        with override_settings(
            POS__PAY_BY_APP=False, POS__TAP_TO_PAY=False, POS__STRIPE_TERMINAL=True
        ):
            dashboard_resp = self.fetch(reports_dashboard_path, args=self.get_args(), method='GET')
            reports = dashboard_resp.json['reports']
            self.assertEqual(len(reports), 2 + 3)  # 2 cash registers + 3 bcr
            self.assertIn(
                {
                    'key': 'cash_registers_transactions',
                    'title': 'Cash registers transactions',
                    'is_new': False,
                },
                reports,
            )
            self.assertIn(
                {
                    'key': 'cash_registers_summary',
                    'title': 'Cash registers summary',
                    'is_new': False,
                },
                reports,
            )
            self.assertIn(
                {
                    'key': 'bcr_transactions_summary',
                    'title': 'Booksy Card Reader transactions summary',
                    'is_new': False,
                },
                reports,
            )
            self.assertIn(
                {'key': 'bcr_log', 'title': 'Booksy Card Reader log', 'is_new': False}, reports
            )
            self.assertIn(
                {'key': 'bcr_refunds', 'title': 'Booksy Card Reader Refunds', 'is_new': False},
                reports,
            )

    def test_reports_based_on_flags_ttp_on(self):
        business = business_recipe.make(time_zone_name='America/Los_Angeles')
        self.session = business.owner.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        baker.make(POS, business=business, tap_to_pay_enabled=True)

        reports_dashboard_path = self.get_reports_dashboard_path(business.id)

        with override_settings(
            POS__PAY_BY_APP=False, POS__TAP_TO_PAY=True, POS__STRIPE_TERMINAL=False
        ):
            dashboard_resp = self.fetch(reports_dashboard_path, args=self.get_args(), method='GET')
            reports = dashboard_resp.json['reports']
            self.assertEqual(len(reports), 2 + 3)  # 2 cash registers + 3 ttp
            self.assertIn(
                {
                    'key': 'cash_registers_transactions',
                    'title': 'Cash registers transactions',
                    'is_new': False,
                },
                reports,
            )
            self.assertIn(
                {
                    'key': 'cash_registers_summary',
                    'title': 'Cash registers summary',
                    'is_new': False,
                },
                reports,
            )
            self.assertIn(
                {
                    'key': 'ttp_transactions_summary',
                    'title': 'Tap to Pay transactions summary',
                    'is_new': False,
                },
                reports,
            )
            self.assertIn({'key': 'ttp_log', 'title': 'Tap to Pay log', 'is_new': False}, reports)
            self.assertIn(
                {'key': 'ttp_refunds', 'title': 'Tap to Pay Refunds', 'is_new': False}, reports
            )

    def test_reports_based_on_flags_pba_on(self):
        business = business_recipe.make(time_zone_name='America/Los_Angeles')
        self.session = business.owner.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        baker.make(POS, business=business, marketpay_enabled=True)

        reports_dashboard_path = self.get_reports_dashboard_path(business.id)

        with override_settings(
            POS__PAY_BY_APP=True, POS__TAP_TO_PAY=False, POS__STRIPE_TERMINAL=False
        ):
            dashboard_resp = self.fetch(reports_dashboard_path, args=self.get_args(), method='GET')
            reports = dashboard_resp.json['reports']
            self.assertEqual(len(reports), 2 + 6)  # 2 cash registers + 6 PBA related
            self.assertIn(
                {
                    'key': 'cash_registers_transactions',
                    'title': 'Cash registers transactions',
                    'is_new': False,
                },
                reports,
            )
            self.assertIn(
                {
                    'key': 'cash_registers_summary',
                    'title': 'Cash registers summary',
                    'is_new': False,
                },
                reports,
            )
            self.assertIn(
                {'key': 'mobile_payments_log', 'title': 'Mobile payments log', 'is_new': False},
                reports,
            )
            self.assertIn(
                {'key': 'prepayments', 'title': 'Deposits', 'is_new': False},
                reports,
            )
            self.assertIn(
                {'key': 'cancellation_fees', 'title': 'Cancellation fees', 'is_new': False},
                reports,
            )
            self.assertIn(
                {'key': 'refunds', 'title': 'Refunds', 'is_new': False},
                reports,
            )
            self.assertIn(
                {
                    'key': 'mobile_payments_transactions_summary',
                    'title': 'Mobile payments transactions summary',
                    'is_new': False,
                },
                reports,
            )
            self.assertIn(
                {'key': 'payout_batches', 'title': 'Payouts Batches', 'is_new': False},
                reports,
            )


@pytest.mark.django_db
class ReportsPermissionsHandlerTests(BaseAsyncHTTPTest):
    @parameterized.expand(
        [
            "/business_api/me/stats/businesses/{business_id}/report/?",
            "/business_api/me/stats/businesses/{business_id}/report/download/?",
            "/business_api/me/stats/businesses/{business_id}/section/?",
            "/business_api/me/stats/businesses/{business_id}/dashboard/?",
            "/business_api/me/stats/businesses/{business_id}/dashboard/main/?",
        ]
    )
    def test_minimal_required_permissions(self, url_string):
        staff_user = user_recipe.make()
        staffer = staffer_recipe.make(
            business=self.business,
            staff_user=staff_user,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_MANAGER,
        )
        self.session = staff_user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

        url = url_string.format(business_id=self.business.id)
        response = self.fetch(url, method='GET')
        self.assertEqual(response.code, status.HTTP_400_BAD_REQUEST)

        staffer.staff_access_level = Resource.STAFF_ACCESS_LEVEL_RECEPTION
        staffer.save()
        response = self.fetch(url, method='GET')
        self.assertEqual(response.code, status.HTTP_404_NOT_FOUND)

    def test_permissions_on_send_email(self):
        staff_user = user_recipe.make()
        staffer = staffer_recipe.make(
            business=self.business,
            staff_user=staff_user,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_MANAGER,
        )
        self.session = staff_user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

        url = f"/business_api/me/stats/businesses/{self.business.id}/report/send_by_email/?"
        response = self.fetch(url, method='POST', body={})

        self.assertEqual(response.code, status.HTTP_400_BAD_REQUEST)

        staffer.staff_access_level = Resource.STAFF_ACCESS_LEVEL_RECEPTION
        staffer.save()
        response = self.fetch(url, method='POST', body={})
        self.assertEqual(response.code, status.HTTP_404_NOT_FOUND)


@pytest.mark.django_db
class FCDashboardsTests(BaseAsyncHTTPTest):
    appointments_hidden_reports = [
        AppointmentsSummary.key,
        AppointmentsListByServices.key,
        AppointmentsListByStaffer.key,
        CategoriesServicesSummary.key,
        AppointmentsList.key,
    ]
    appointments_hidden_sections = [
        HighlightsSection.key,
        TopServicesSection.key,
        TopAddonsSection.key,
    ]

    clients_hidden_reports = [
        ReturningClients.key,
        ClientsList.key,
        ClientsSummaryReport.key,
        SlippingAwayClients.key,
        NewClients.key,
    ]

    revenue_hidden_sections = [
        TotalRevenueSection.key,
    ]
    revenue_hidden_reports = [
        SalesByServices.key,
        SalesByProducts.key,
        SalesTrends.key,
        AppointmentsProfitability.key,
        TaxesSummary.key,
        OutstandingInvoices.key,
        SalesByMemberships.key,
        MembershipsRedemptions.key,
        SalesByPackages.key,
        PackagesRedemptions.key,
    ]

    cash_flow_hidden_reports = [
        CashRegistersSummary.key,
        CashRegistersTransactions.key,
    ]

    inventory_hidden_reports = [
        StockMovementSummary.key,
    ]
    inventory_hidden_sections = [
        HighRotatingProductsDashboardSection.key,
        LowStockDashboardSection.key,
    ]

    staff_hidden_reports = [
        StaffGratuity.key,
    ]
    staff_hidden_sections = [
        Top10StaffMembersRevenueSection.key,
        StaffMembersCommissionSection.key,
    ]

    business_performance_report_hidden_sections = [
        TotalRevenue.key,
        AppointmentsListBusinessPerformance.key,
        SalesByServicesBusinessPerformance.key,
        SalesByProductsBusinessPerformance.key,
        ClientsSummaryBusinessPerformance.key,
        AppointmentsSummaryBusinessPerformance.key,
        StaffMembersRevenueBusinessPerformance.key,
        CashRegistersSummaryBusinessPerformance.key,
        CashRegistersTransactionsBusinessPerformance.key,
    ]

    hidden_reports = (
        appointments_hidden_reports
        + clients_hidden_reports
        + revenue_hidden_reports
        + cash_flow_hidden_reports
        + inventory_hidden_reports
        + staff_hidden_reports
    )

    hidden_sections = (
        revenue_hidden_sections
        + inventory_hidden_sections
        + staff_hidden_sections
        + appointments_hidden_sections
        + business_performance_report_hidden_sections
    )

    def setUp(self):
        super().setUp()
        baker.make(POS, business=self.business, marketpay_enabled=True)

    def assert_not_in_response_keys(self, container: dict, keys: list[str]):
        response_keys = {item['key'] for item in container}
        self.assertEqual(response_keys & set(keys), set())

    @french_certification_enabled()
    def test_dashboard_main_reports_hidden(self):
        response = self.fetch(
            f'/business_api/me/stats/businesses/{self.business.id}/dashboard/main/',
            args={'date_from': datetime.date.today().isoformat(), 'time_span': 'month'},
        )

        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertIsNone(response.json['revenue_chart'])
        self.assertIsNone(response.json['revenue_stats'])

    def test_dashboard_main_reports_not_hidden_if_not_fc(self):
        response = self.fetch(
            f'/business_api/me/stats/businesses/{self.business.id}/dashboard/main/',
            args={'date_from': datetime.date.today().isoformat(), 'time_span': 'month'},
        )

        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertIsNotNone(response.json['revenue_chart'])
        self.assertIsNotNone(response.json['revenue_stats'])

    @french_certification_enabled()
    def test_dashboard_appointments_flag_on_should_hide_reports(self):
        response = self.fetch(
            f'/business_api/me/stats/businesses/{self.business.id}/dashboard',
            args={
                'date_from': datetime.date.today().isoformat(),
                'time_span': 'month',
                'dashboard': 'appointments',
            },
        )
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assert_not_in_response_keys(
            response.json['preview_sections'],
            self.appointments_hidden_sections,
        )
        self.assert_not_in_response_keys(response.json['reports'], self.appointments_hidden_reports)

    def test_dashboard_appointments_flag_on_but_not_fc_should_not_hide_reports(self):
        response = self.fetch(
            f'/business_api/me/stats/businesses/{self.business.id}/dashboard',
            args={
                'date_from': datetime.date.today().isoformat(),
                'time_span': 'month',
                'dashboard': 'appointments',
            },
        )
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertNotEqual(response.json['preview_sections'], [])

        report = {report['key'] for report in response.json['reports']}
        self.assertEqual(
            report & set(self.appointments_hidden_reports),
            set(self.appointments_hidden_reports),
        )

    @parameterized.expand(hidden_reports)
    @french_certification_enabled()
    def test_report_should_be_not_accessible(self, report_key):
        for path in ('report', 'report/download'):
            response = self.fetch(
                f'/business_api/me/stats/businesses/{self.business.id}/{path}',
                args={
                    'date_from': datetime.date.today().isoformat(),
                    'time_span': 'month',
                    'report_key': report_key,
                },
            )
            self.assertEqual(response.code, status.HTTP_400_BAD_REQUEST)
            self.assertDictEqual(
                response.json,
                {
                    'errors': [
                        {
                            'field': 'report_key',
                            'description': f'"{report_key}" is not a valid choice.',
                            'code': 'invalid_choice',
                        }
                    ]
                },
            )

    @parameterized.expand(hidden_reports)
    @french_certification_enabled()
    def test_report_send_by_email_should_be_not_accessible_with_flag_on(self, report_key):
        response = self.fetch(
            f'/business_api/me/stats/businesses/{self.business.id}/report/send_by_email',
            method='POST',
            args={
                'date_from': datetime.date.today().isoformat(),
                'time_span': 'month',
                'report_key': report_key,
                'email': '<EMAIL>',
            },
            body={},
        )
        self.assertEqual(response.code, status.HTTP_400_BAD_REQUEST)
        self.assertDictEqual(
            response.json,
            {
                'errors': [
                    {
                        'field': 'report_key',
                        'description': f'"{report_key}" is not a valid choice.',
                        'code': 'invalid_choice',
                    }
                ]
            },
        )

    @parameterized.expand(hidden_reports)
    def test_report_should_be_accessible_if_not_fc(self, report_key):
        response = self.fetch(
            f'/business_api/me/stats/businesses/{self.business.id}/report',
            args={
                'date_from': datetime.date.today().isoformat(),
                'time_span': 'month',
                'report_key': report_key,
            },
        )
        self.assertEqual(response.code, status.HTTP_200_OK)

    @parameterized.expand(hidden_sections)
    @french_certification_enabled()
    def test_section_should_be_not_accessible(self, section_key):
        response = self.fetch(
            f'/business_api/me/stats/businesses/{self.business.id}/section',
            args={
                'date_from': datetime.date.today().isoformat(),
                'time_span': 'month',
                'section_key': section_key,
            },
        )
        self.assertEqual(response.code, status.HTTP_400_BAD_REQUEST)
        self.assertDictEqual(
            response.json,
            {
                'errors': [
                    {
                        'field': 'section_key',
                        'description': f'"{section_key}" is not a valid choice.',
                        'code': 'invalid_choice',
                    }
                ]
            },
        )

    @french_certification_enabled()
    def test_dashboard_clients_should_hide_reports(self):
        response = self.fetch(
            f'/business_api/me/stats/businesses/{self.business.id}/dashboard',
            args={
                'date_from': datetime.date.today().isoformat(),
                'time_span': 'month',
                'dashboard': 'clients',
            },
        )
        self.assertEqual(response.code, status.HTTP_200_OK)
        hidden_sections_keys = [
            ClientsByTypesSection.key,
            TopClientsSection.key,
        ]
        self.assert_not_in_response_keys(response.json['preview_sections'], hidden_sections_keys)
        self.assert_not_in_response_keys(response.json['reports'], self.clients_hidden_reports)

    @french_certification_enabled()
    def test_dashboard_revenue_should_hide_reports(self):
        response = self.fetch(
            f'/business_api/me/stats/businesses/{self.business.id}/dashboard',
            args={
                'date_from': datetime.date.today().isoformat(),
                'time_span': 'month',
                'dashboard': 'revenue',
            },
        )
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertIsNone(response.json['header_box'])

        self.assert_not_in_response_keys(
            response.json['preview_sections'], ['total_revenue_by_type']
        )
        self.assert_not_in_response_keys(response.json['charts'], ['revenue_chart'])
        self.assert_not_in_response_keys(response.json['reports'], self.revenue_hidden_reports)

    @parameterized.expand(
        [
            ("memberships_summary",),
            ("packages_summary",),
            ("sales_log_before_french_migration",),
            ("sales_summary_before_french_migration",),
            ("payment_methods_archive",),
        ]
    )
    @french_certification_enabled()
    def test_dashboard_revenue_selected_reports_not_visible_by_default(self, report_key: str):
        resp = self.fetch(
            f"/business_api/me/stats/businesses/{self.business.id}/dashboard",
            args={
                'date_from': datetime.date.today().isoformat(),
                'time_span': "month",
                'dashboard': "revenue",
            },
        )
        reports_by_key = [report["key"] for report in resp.json["reports"]]

        self.assertNotIn(report_key, reports_by_key)

    @parameterized.expand(
        [
            ("sales_log_before_french_migration",),
            ("sales_summary_before_french_migration",),
            ("payment_methods_archive",),
        ]
    )
    @french_certification_enabled()
    def test_dashboard_revenue_has_extra_reports_for_migrated_business(self, report_key: str):
        self.business.created -= datetime.timedelta(weeks=2)
        self.business.save()
        JETService.data_initialization_event(business_id=self.business.id)

        resp = self.fetch(
            f"/business_api/me/stats/businesses/{self.business.id}/dashboard",
            args={
                'date_from': datetime.date.today().isoformat(),
                'time_span': "month",
                'dashboard': "revenue",
            },
        )
        reports_by_key = [report["key"] for report in resp.json["reports"]]

        self.assertIn(report_key, reports_by_key)

    @parameterized.expand(
        [
            ("memberships_summary",),
            ("packages_summary",),
        ]
    )
    @french_certification_enabled()
    @override_eppo_feature_flag({FrenchCertificationVoucherSummaryReportFlag.flag_name: True})
    def test_dashboard_revenue_extra_reports_not_visible_for_px_without_active_vouchers(
        self, report_key: str
    ):
        resp = self.fetch(
            f"/business_api/me/stats/businesses/{self.business.id}/dashboard",
            args={
                'date_from': datetime.date.today().isoformat(),
                'time_span': "month",
                'dashboard': "revenue",
            },
        )
        reports_by_key = [report["key"] for report in resp.json["reports"]]

        self.assertNotIn(report_key, reports_by_key)

    @parameterized.expand(
        [
            ("memberships_summary",),
            ("packages_summary",),
        ]
    )
    @french_certification_enabled()
    @override_eppo_feature_flag({FrenchCertificationVoucherSummaryReportFlag.flag_name: True})
    def test_dashboard_revenue_has_extra_reports_for_px_with_active_vouchers(self, report_key: str):
        for voucher_type in (
            VoucherType.MEMBERSHIP,
            VoucherType.PACKAGE,
        ):
            baker.make(
                Voucher,
                status=VoucherStatus.ACTIVE,
                voucher_template=baker.make(VoucherTemplate, type=voucher_type),
                pos=self.business.pos,
            )

        resp = self.fetch(
            f"/business_api/me/stats/businesses/{self.business.id}/dashboard",
            args={
                'date_from': datetime.date.today().isoformat(),
                'time_span': "month",
                'dashboard': "revenue",
            },
        )
        reports_by_key = [report["key"] for report in resp.json["reports"]]

        self.assertIn(report_key, reports_by_key)

    @french_certification_enabled()
    def test_dashboard_cash_flow_should_hide_reports(self):
        response = self.fetch(
            f'/business_api/me/stats/businesses/{self.business.id}/dashboard',
            args={
                'date_from': datetime.date.today().isoformat(),
                'time_span': 'month',
                'dashboard': 'cash_flow',
            },
        )
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assert_not_in_response_keys(response.json['reports'], self.cash_flow_hidden_reports)

    @french_certification_enabled()
    def test_dashboard_inventory_should_hide_reports(self):
        response = self.fetch(
            f'/business_api/me/stats/businesses/{self.business.id}/dashboard',
            args={
                'date_from': datetime.date.today().isoformat(),
                'time_span': 'month',
                'dashboard': 'inventory',
            },
        )
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertIsNone(response.json['meter_section'])
        self.assert_not_in_response_keys(
            response.json['preview_sections'], self.inventory_hidden_sections
        )
        self.assert_not_in_response_keys(response.json['reports'], self.inventory_hidden_reports)

    @french_certification_enabled()
    def test_dashboard_staff_should_hide_reports(self):
        response = self.fetch(
            f'/business_api/me/stats/businesses/{self.business.id}/dashboard',
            args={
                'date_from': datetime.date.today().isoformat(),
                'time_span': 'month',
                'dashboard': 'staff',
            },
        )
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assert_not_in_response_keys(
            response.json['preview_sections'], self.staff_hidden_sections
        )
        self.assert_not_in_response_keys(response.json['reports'], self.staff_hidden_reports)

    @french_certification_enabled()
    def test_dashboard_mobile_should_not_return_revenue_tile(self):
        response = self.fetch(
            f'/business_api/me/stats/businesses/{self.business.id}/simple_statistics/tiles',
        )

        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assert_not_in_response_keys(response.json['data']['tiles'], [RevenueSimpleStats.key])

    @french_certification_enabled()
    def test_dashboard_mobile_tile_should_not_be_accessible(self):
        response = self.fetch(
            f'/business_api/me/stats/businesses/{self.business.id}/'
            f'simple_statistics/tiles/{RevenueSimpleStats.key}',
            args={'time_span': 'today'},
        )

        self.assertEqual(response.code, status.HTTP_400_BAD_REQUEST)
        self.assertDictEqual(
            response.json,
            {
                'errors': [
                    {
                        'field': 'simple_stats_tile_key',
                        'description': f'"{RevenueSimpleStats.key}" is not a valid choice.',
                        'code': 'invalid_choice',
                    }
                ]
            },
        )

    def test_dashboard_mobile_tile_should_be_accessible_if_not_fc(self):
        response = self.fetch(
            f'/business_api/me/stats/businesses/{self.business.id}/'
            f'simple_statistics/tiles/{RevenueSimpleStats.key}',
            args={'time_span': 'today'},
        )

        self.assertEqual(response.code, status.HTTP_200_OK)

    @french_certification_enabled()
    def test_report_business_performance_report(self):
        response = self.fetch(
            f'/business_api/me/stats/businesses/{self.business.id}/report',
            args={
                'date_from': datetime.date.today().isoformat(),
                'time_span': 'month',
                'report_key': 'business_performance_report',
            },
        )
        self.assertEqual(response.code, status.HTTP_200_OK)

        self.assert_not_in_response_keys(
            response.json['sections'],
            self.business_performance_report_hidden_sections,
        )


class ReportExtraSetupsMixin:
    business: Business
    enterContext: Callable[[ContextManager], None]

    def extra_setup__business_performance_report(self):
        baker.make(POS, business=self.business, marketpay_enabled=True)
        self.enterContext(french_certification_enabled())
        self.enterContext(override_settings(POS__PAY_BY_APP=True))
        self.enterContext(override_settings(POS__STRIPE_TERMINAL=True))
        self.enterContext(override_settings(POS__TAP_TO_PAY=True))

    def extra_setup__memberships_summary(self):
        self.enterContext(french_certification_enabled())

    def extra_setup__packages_summary(self):
        self.enterContext(french_certification_enabled())

    def extra_setup__payment_methods_archive(self):
        self.enterContext(french_certification_enabled())


@pytest.mark.django_db
class ReportsHandler(ReportExtraSetupsMixin, BaseAsyncHTTPTest):
    @parameterized.expand(
        [
            ("business_performance_report",),
            ("memberships_summary",),
            ("packages_summary",),
            ("payment_methods_archive",),
        ]
    )
    def test_report_returned_successfully(self, report_key: str):
        getattr(self, f"extra_setup__{report_key}", lambda *a, **kw: None)()
        resp = self._request(report_key)
        self.assertEqual(resp.code, 200)

    def _request(self, report_key: str):
        return self.fetch(
            f'/business_api/me/stats/businesses/{self.business.id}/report',
            args={
                'report_key': report_key,
                'date_from': datetime.date.today().isoformat(),
                'time_span': 'month',
            },
        )


@pytest.mark.django_db
class ReportsDownloadHandler(ReportExtraSetupsMixin, BaseAsyncHTTPTest):
    @parameterized.expand(
        [
            ("business_performance_report",),
            ("memberships_summary",),
            ("packages_summary",),
            ("payment_methods_archive",),
        ]
    )
    def test_report_returned_successfully(self, report_key: str):
        getattr(self, f"extra_setup__{report_key}", lambda *a, **kw: None)()
        resp = self._request(report_key)
        self.assertEqual(resp.code, 200)

    def _request(self, report_key: str):
        return self.fetch(
            f'/business_api/me/stats/businesses/{self.business.id}/report/download',
            args={
                'report_key': report_key,
                'date_from': datetime.date.today().isoformat(),
                'time_span': 'month',
            },
        )


class ReportSectionHandlerMixin:
    section_cls: Type[ReportSection]

    @staticmethod
    def skip_if_redundant(method):
        # Using 'parameterized_class' on a class with tests defined in a mixin uses tests from
        # a mixin as a template for creating parameterized tests. However, it also leaves these
        # tests inside the mixin so that they are also run. Since they do not get parameterized
        # they will # fail when accessing attributes 'parameterized_class' should have created.
        # FIXME support for mixin in 'parameterized_class'  # pylint: disable=fixme
        def wrapper(self, *args, **kwargs):
            if not hasattr(self, "section_cls"):
                pytest.skip("redundant test created by 'parameterized_class'")
            return method(self, *args, **kwargs)

        return wrapper

    @skip_if_redundant
    def test_when_no_data_then_returns_headers_but_no_rows(self):
        resp = self._get()
        self.assertEqual(resp.code, 200)
        self.assertAtLastPage(resp.json)
        self.assertNotEqual(resp.json["table"]["headers"], [])
        self.assertEqual(resp.json["table"]["rows"], [])

    @skip_if_redundant
    def test_returns_section_rows(self):
        row = MagicMock()
        row.get_serializer().data = row_data = {"key": "123"}
        with patch.object(self.section_cls, "get_data") as mock_get_data:
            mock_get_data().generation_time = datetime.datetime.now()
            mock_get_data().rows = [row]

            resp = self._get()

        self.assertEqual(resp.code, 200)
        self.assertAtLastPage(resp.json)
        self.assertEqual(resp.json["table"]["headers"], [])
        self.assertEqual(resp.json["table"]["rows"], [row_data])

    def assertAtLastPage(self, data: dict):  # pylint: disable=invalid-name
        pagination = data.get("pagination")
        if pagination is None:
            return

        self.assertEqual(data["pagination"]["page"], 1)
        self.assertEqual(data["pagination"]["last_page"], 1)

    def _get(self):
        return self.fetch(
            method="GET",
            path=f"/business_api/me/stats/businesses/{self.business.id}/section",
            args={
                "section_key": self.section_cls.key,
                "date_from": "2024-08-01",
                "date_till": "2024-08-31",
                "time_span": "month",
            },
        )


@parameterized_class(
    ("testname", "section_cls"),
    [
        (
            cls.__name__,
            cls,
        )
        for cls in (
            MembershipsActiveSection,
            MembershipsCloseToExpirationSection,
            MembershipsExpiredSection,
            PackagesSummaryActiveSection,
            PackagesSummaryExpiredCloseSection,
            PackagesSummaryExpiredSection,
            PaymentMethodsArchiveSection,
            SalesLogBeforeFrenchMigrationSection,
            SalesSummaryBeforeFrenchMigrationSection,
        )
    ],
)
@pytest.mark.django_db
class ReportSectionHandlerTests(
    ReportSectionHandlerMixin,
    BaseAsyncHTTPTest,
):
    pass


@parameterized_class(
    ("testname", "section_cls"),
    [
        (
            cls.__name__,
            cls,
        )
        for cls in (
            MembershipsActiveSection,
            MembershipsCloseToExpirationSection,
            MembershipsExpiredSection,
            PackagesSummaryActiveSection,
            PackagesSummaryExpiredCloseSection,
            PackagesSummaryExpiredSection,
            PaymentMethodsArchiveSection,
            SalesLogBeforeFrenchMigrationSection,
            SalesSummaryBeforeFrenchMigrationSection,
        )
    ],
)
@pytest.mark.django_db
class ReportSectionHandlerFrenchCertificationEnabledTests(
    ReportSectionHandlerMixin,
    BaseAsyncHTTPTest,
):
    def setUp(self):
        super().setUp()
        self.enterContext(french_certification_enabled())
