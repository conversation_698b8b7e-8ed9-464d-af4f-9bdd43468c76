import datetime
import unittest
from decimal import Decimal

import pytest
import pytz
from dateutil.relativedelta import relativedelta
from model_bakery import baker
from model_bakery.recipe import Recipe

from lib.baker_utils import get_or_create_booking_source
from lib.test_utils import create_subbooking
from webapps.booking.enums import AppointmentStatus, AppointmentType
from webapps.booking.models import BookingResource
from webapps.business.enums import (
    PriceType,
)
from webapps.business.models import (
    Business,
    Resource,
    Service,
    ServiceCategory,
    ServiceVariant,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.stats_and_reports.reports.appointment_reports.appointments_cancellations import (
    AppointmentsCancellations,
    AppointmentsCancellationsNoShowsTable,
    AppointmentsCancellationsSection,
    AppointmentsNoShowsSection,
)
from webapps.stats_and_reports.reports.time_data import (
    TimeDataScope,
    TimeScopeType,
)
from webapps.stats_and_reports.serializers import ReportSerializer
from webapps.user.models import User


@pytest.mark.django_db
class AppointmentsCancellationsTests(unittest.TestCase):

    def _prepare_data(self):
        self.business = baker.make(Business, time_zone_name='UTC')

        staffer_recipe = Recipe(
            Resource,
            business_id=self.business.id,
            type=Resource.STAFF,
        )
        self.staffer_a = staffer_recipe.make(name='Staff A')
        self.staffer_b = staffer_recipe.make(name='Staff B')

        bci_recipe = Recipe(
            BusinessCustomerInfo,
            business_id=self.business.id,
        )
        self.bci_a = bci_recipe.make(first_name='Cust', last_name='A')
        self.bci_b = bci_recipe.make(
            user=baker.make(
                User,
                first_name='Cust',
                last_name='B',
            )
        )

    def setUp(self) -> None:
        super().setUp()
        self._prepare_data()
        self.time_scope = TimeDataScope(
            self.business,
            'en',
            date_from=datetime.date(2020, 12, 1),
            date_till=datetime.date(2020, 12, 31),
            time_span=TimeScopeType.MONTH,
        )
        self.section = AppointmentsCancellationsSection(self.time_scope)
        self.report = AppointmentsCancellations(self.time_scope)

    def test_queryset_annotations(self):
        booked_from = datetime.datetime(2020, 12, 1, 10, 15, tzinfo=pytz.UTC)
        booking_source = get_or_create_booking_source()
        appointment_kwargs = dict(
            type=AppointmentType.BUSINESS,
            status=AppointmentStatus.CANCELED,
            source=booking_source,
        )

        create_subbooking(
            business=self.business,
            booking_kws=dict(
                booked_from=booked_from,
                booked_till=booked_from + datetime.timedelta(minutes=15),
                booked_for=self.bci_a,
                **appointment_kwargs,
            ),
        )
        create_subbooking(
            business=self.business,
            booking_kws=dict(
                booked_from=booked_from,
                booked_till=booked_from + datetime.timedelta(minutes=15),
                booked_for=self.bci_b,
                **appointment_kwargs,
            ),
        )
        booking_c = create_subbooking(
            business=self.business,
            booking_kws=dict(
                booked_from=booked_from,
                booked_till=booked_from + datetime.timedelta(minutes=15),
                customer_name='Plaintext cust',
                **appointment_kwargs,
            ),
        )[0]
        baker.make(
            BookingResource,
            subbooking=booking_c,
            resource=self.staffer_a,
        )

        result = self.section.get_queryset().order_by('id')
        assert result[0]['customer_name_merged'] == 'Cust A'
        assert result[1]['customer_name_merged'] == 'Cust B'
        assert result[2]['customer_name_merged'] == 'Plaintext cust'
        assert result[2]['staffer_name'] == self.staffer_a.name

    def test_queryset_filters(self):
        booked_from = datetime.datetime(2020, 12, 1, 10, 15, tzinfo=pytz.UTC)
        source = get_or_create_booking_source()
        appointment_kwargs = dict(
            type=AppointmentType.BUSINESS,
            status=AppointmentStatus.CANCELED,
            source=source,
        )

        proper = create_subbooking(
            business=self.business,
            booking_kws=dict(
                booked_from=booked_from,
                booked_till=booked_from + datetime.timedelta(minutes=15),
                **appointment_kwargs,
            ),
        )[0]
        invalid_type = create_subbooking(
            business=self.business,
            booking_kws=dict(
                booked_from=booked_from,
                booked_till=booked_from + datetime.timedelta(minutes=15),
                **{**appointment_kwargs, **dict(type=AppointmentType.RESERVATION)},
            ),
        )[0]
        status_accepted = create_subbooking(
            business=self.business,
            booking_kws=dict(
                booked_from=booked_from,
                booked_till=booked_from + datetime.timedelta(minutes=15),
                **{**appointment_kwargs, **dict(status=AppointmentStatus.ACCEPTED)},
            ),
        )[0]
        status_finished = create_subbooking(
            business=self.business,
            booking_kws=dict(
                booked_from=booked_from,
                booked_till=booked_from + datetime.timedelta(minutes=15),
                **{**appointment_kwargs, **dict(status=AppointmentStatus.FINISHED)},
            ),
        )[0]
        invalid_date = create_subbooking(
            business=self.business,
            booking_kws=dict(
                booked_from=datetime.datetime(
                    2020,
                    11,
                    1,
                    10,
                    15,
                    tzinfo=pytz.UTC,
                ),
                booked_till=datetime.datetime(
                    2020,
                    11,
                    1,
                    10,
                    30,
                    tzinfo=pytz.UTC,
                ),
                **appointment_kwargs,
            ),
        )[0]
        result = self.section.get_queryset()
        ids = [r['id'] for r in result]
        assert proper.id in ids
        assert invalid_type not in ids
        assert status_accepted not in ids
        assert status_finished not in ids
        assert invalid_date not in ids

    def test_queryset_filters_no_shows(self):
        booked_from = datetime.datetime(2020, 12, 1, 10, 15, tzinfo=pytz.UTC)
        source = get_or_create_booking_source()
        appointment_kwargs = dict(
            type=AppointmentType.BUSINESS,
            source=source,
        )
        status_no_show = create_subbooking(
            business=self.business,
            booking_kws=dict(
                booked_from=booked_from,
                booked_till=booked_from + datetime.timedelta(minutes=15),
                status=AppointmentStatus.NOSHOW,
                **appointment_kwargs,
            ),
        )[0]
        status_canceled = create_subbooking(
            business=self.business,
            booking_kws=dict(
                booked_from=booked_from,
                booked_till=booked_from + datetime.timedelta(minutes=15),
                status=AppointmentStatus.CANCELED,
                **appointment_kwargs,
            ),
        )[0]
        no_shows_section = AppointmentsNoShowsSection(self.time_scope)
        result = no_shows_section.get_queryset()
        ids = [r['id'] for r in result]
        assert status_no_show.id in ids
        assert status_canceled not in ids

    def test_table(self):
        booked_from = datetime.datetime(2020, 12, 1, 10, 15, tzinfo=pytz.UTC)
        service_cat = baker.make(ServiceCategory, name='Category A')
        service = baker.make(
            Service,
            name='Service A',
            service_category=service_cat,
        )
        service_variant = baker.make(
            ServiceVariant,
            service=service,
            duration=relativedelta(minutes=10),
            price=Decimal('20.00'),
            type=PriceType.FIXED,
        )
        source = get_or_create_booking_source()
        appointment_kwargs = dict(
            type=AppointmentType.BUSINESS,
            booked_for=self.bci_a,
            status=AppointmentStatus.CANCELED,
            source=source,
        )
        booking_a = create_subbooking(
            business=self.business,
            booking_kws=dict(
                booked_from=booked_from,
                booked_till=booked_from + datetime.timedelta(minutes=15),
                service_variant=service_variant,
                **appointment_kwargs,
            ),
        )[0]
        booking_b = create_subbooking(
            business=self.business,
            booking_kws=dict(
                booked_till=booked_from + datetime.timedelta(minutes=15),
                service_variant=service_variant,
                booked_from=booked_from + datetime.timedelta(minutes=5),
                resolved_discount=10,
                resolved_price=Decimal('18.00'),
                **{**appointment_kwargs, **dict(booked_for=self.bci_b)},
            ),
        )[0]
        baker.make(
            BookingResource,
            subbooking=booking_a,
            resource=self.staffer_a,
        )
        baker.make(
            BookingResource,
            subbooking=booking_b,
            resource=self.staffer_b,
        )
        data_table = self.section.get_data(False)

        rows_expected = [
            AppointmentsCancellationsNoShowsTable.Row(
                booked_from=booking_a.booked_from,
                subbooking_id=booking_a.id,
                category_name='Category A',
                service_name='Service A',
                customer_name='Cust A',
                staffer_name='Staff A',
                service_duration=15,
                service_value=Decimal('20.00'),
                client_discount=0,
                revenue_lost=Decimal('20.00'),
                _business=self.business,
                _language='en',
            ),
            AppointmentsCancellationsNoShowsTable.Row(
                booked_from=booking_b.booked_from,
                subbooking_id=booking_b.id,
                category_name='Category A',
                service_name='Service A',
                customer_name='Cust B',
                staffer_name='Staff B',
                service_duration=10,
                service_value=Decimal('20.00'),
                client_discount=10,
                revenue_lost=Decimal('18.00'),
                _business=self.business,
                _language='en',
            ),
        ]
        assert data_table.rows == rows_expected
        assert data_table.total_row == AppointmentsCancellationsNoShowsTable.TotalRow(
            service_duration=25,
            service_value=Decimal('40.00'),
            revenue_lost=Decimal('38.00'),
        )

    def test_serializer(self):
        serializer = ReportSerializer(instance=self.report)
        assert serializer.data
        section_data = serializer.data['sections'][0]
        assert section_data['key'] == 'appointments_cancellations_section'
