# pylint: disable=duplicate-code

import typing
from collections import OrderedDict
from dataclasses import dataclass
from decimal import Decimal

from django.db.models import F, QuerySet
from django.utils.translation import gettext, gettext_lazy as _
from openpyxl.styles import Alignment
from openpyxl.worksheet.worksheet import Worksheet
from rest_framework import serializers

from webapps.stats_and_reports.models import CommodityStockLevelReplica
from webapps.stats_and_reports.report_keys import ReportKeys
from webapps.stats_and_reports.reports import fields
from webapps.stats_and_reports.reports.base import (
    BaseReport,
    ReportSection,
    ReportTable,
    ReportTableRow,
    SortableField,
    Sorting,
)
from webapps.stats_and_reports.reports.mixins import GetWarehousesReportMixin
from webapps.stats_and_reports.reports.spreadsheet_utils import (
    format_alignment,
    format_number,
    NumberFormat,
    ws_range,
)


class LowStockTableTotalRowSerializer(serializers.Serializer):
    label = serializers.CharField(default=_('Total'))
    estimated_net_purchase_cost = fields.ReportsCurrencyField()

    def to_representation(self, instance):
        row_dict = super().to_representation(instance)
        result = OrderedDict.fromkeys('abcdefg')  # 7 empty cols
        result.update(row_dict)
        return result


class LowStockTableRowSerializer(serializers.Serializer):
    category_name = serializers.CharField()
    product_name = serializers.CharField()
    product_code = serializers.CharField()
    brand_name = serializers.CharField()
    pieces_on_hand = serializers.IntegerField()
    min_stock_level = serializers.IntegerField()
    max_stock_level = serializers.IntegerField()
    reorder_amount = serializers.IntegerField()
    estimated_net_purchase_cost = fields.ReportsCurrencyField()


class LowStockTable(ReportTable):
    header = (
        _('Category'),
        _('Product'),
        _('Product code'),
        _('Brand'),
        _('Stock on hand (pcs)'),
        _('Min stock level'),
        _('Max stock level'),
        _('Reorder'),
        _('Estimated net purchase cost'),
    )

    @dataclass
    class Row(ReportTableRow):
        category_name: str
        product_name: str
        product_code: str
        brand_name: str
        pieces_on_hand: int
        min_stock_level: int
        max_stock_level: int
        reorder_amount: int
        estimated_net_purchase_cost: Decimal

        def get_serializer_class(self):
            return LowStockTableRowSerializer

    @dataclass
    class TotalRow(ReportTableRow):
        estimated_net_purchase_cost: Decimal

        def get_serializer_class(self):
            return LowStockTableTotalRowSerializer

    def format_spreadsheet_table_header(self, ws: Worksheet):
        super().format_spreadsheet_table_header(ws)
        # In output file all columns are shifted by one (we insert empty column
        # at the beginning of the file), so "A" here will become "B" in xlsx
        format_alignment(
            ws_range(ws, 'F:J'), Alignment(horizontal='right', wrap_text=True, vertical='center')
        )

    def format_spreadsheet_table_row(self, ws: Worksheet, row: ReportTableRow):
        super().format_spreadsheet_table_row(ws, row)
        format_number(ws_range(ws, 'F:I'), NumberFormat.GENERAL)
        format_number(ws_range(ws, 'J:J'), NumberFormat.CURRENCY)


class LowStockDashboardTableRowSerializer(serializers.Serializer):
    item_name = serializers.CharField()
    pieces_on_hand = serializers.IntegerField()
    value = fields.ReportsCurrencyField()
    _meta = serializers.DictField()


class LowStockDashboardTable(ReportTable):
    header = (
        _('Item'),
        _('On stock'),
        _('Value'),
    )

    @dataclass
    class Row(ReportTableRow):
        item_name: str
        pieces_on_hand: int
        value: Decimal
        _meta: dict

        def get_serializer_class(self):
            return LowStockDashboardTableRowSerializer


class WarehouseLowStockSection(ReportSection):
    key = 'warehouse_low_stock_section'
    requires_resource_id = True
    is_paginated = True
    append_total_row = True
    sortable_fields = (
        SortableField('product_name'),
        SortableField('product_code'),
        SortableField('brand_name'),
        SortableField('category_name'),
    )
    default_sorting = Sorting('product_name', Sorting.ASC)

    @property
    def title(self):
        return self.resource_label

    def get_queryset(self) -> QuerySet:
        """This report shows always "current" data and does not filter by
        selected time scope.
        """
        return (
            CommodityStockLevelReplica.objects.filter(
                commodity__business_id=self.scope.business.id,
                warehouse_id=self.resource_id,
                minimum_packages__gt=0,
                remaining_volume__lt=F('minimum_packages') * F('commodity__total_pack_capacity'),
            )
            .annotate(
                product_name=F('commodity__name'),
                product_code=F('commodity__product_code'),
                brand_name=F('commodity__brand__name'),
                category_name=F('commodity__category__name'),
                unit_symbol=F('commodity__volume_unit__symbol'),
            )
            .select_related(
                'commodity',
            )
        )

    def coerce_data_to_table(self, results) -> LowStockTable:
        rows = []
        for commodity_stock_level in results:
            estimated_net_purchase_cost = (
                commodity_stock_level.reorder_amount
                * commodity_stock_level.commodity.current_net_purchase_price
            )
            rows.append(
                LowStockTable.Row(
                    category_name=commodity_stock_level.category_name,
                    product_name=commodity_stock_level.product_name,
                    product_code=commodity_stock_level.product_code,
                    brand_name=commodity_stock_level.brand_name,
                    pieces_on_hand=commodity_stock_level.total_packages_left,
                    min_stock_level=commodity_stock_level.minimum_packages,
                    max_stock_level=commodity_stock_level.maximum_packages,
                    reorder_amount=commodity_stock_level.reorder_amount,
                    estimated_net_purchase_cost=estimated_net_purchase_cost,
                )
            )
        return LowStockTable(
            rows=rows,
            sortable_fields=self.sortable_fields,
            active_sorting=self.sorting_to_apply,
        )

    def calculate_total_row(self, queryset: QuerySet) -> ReportTableRow:
        total_estimated_net_purchase_cost = Decimal(0)

        for commodity_stock_level in queryset:
            estimated_net_purchase_cost = (
                commodity_stock_level.reorder_amount
                * commodity_stock_level.commodity.current_net_purchase_price
            )
            total_estimated_net_purchase_cost += estimated_net_purchase_cost

        return LowStockTable.TotalRow(
            estimated_net_purchase_cost=total_estimated_net_purchase_cost,
        )


class LowStockDashboardSection(ReportSection):
    key = 'low_stock_dashboard_section'
    title = _('Low stock products')

    MAX_LOW_STOCK_ENTRIES = 10

    def get_queryset(self) -> QuerySet:
        """This report shows always "current" data and does not filter by
        selected time scope.
        """
        return (
            CommodityStockLevelReplica.objects.filter(
                commodity__business_id=self.scope.business.id,
                minimum_packages__gt=0,
                remaining_volume__lt=F('minimum_packages') * F('commodity__total_pack_capacity'),
            )
            .annotate(
                commodity_name=F('commodity__name'),
                brand_name=F('commodity__brand__name'),
            )
            .select_related(
                'commodity',
            )
            .order_by(
                'remaining_volume',
                'commodity_name',
            )[: self.MAX_LOW_STOCK_ENTRIES]
        )

    def coerce_data_to_table(self, results) -> LowStockDashboardTable:
        rows = []
        for commodity_stock_level in results:
            item_name = commodity_stock_level.commodity_name
            if commodity_stock_level.brand_name:
                item_name += f' · {commodity_stock_level.brand_name}'
            value = (
                commodity_stock_level.total_packages_left
                * commodity_stock_level.commodity.gross_price
            )
            rows.append(
                LowStockDashboardTable.Row(
                    item_name=item_name,
                    pieces_on_hand=commodity_stock_level.total_packages_left,
                    value=value,
                    _meta={
                        'minimum_packages': commodity_stock_level.minimum_packages,
                        'maximum_packages': commodity_stock_level.maximum_packages,
                    },
                )
            )
        return LowStockDashboardTable(rows=rows)


class LowStock(GetWarehousesReportMixin, BaseReport):
    key = ReportKeys.LowStock
    is_date_filtered = False  # current moment

    def _set_period_dates_xlsx(self):
        self.business_xlsx_info.frozen_date_from = self.scope.date_today_datetime_utc
        self.business_xlsx_info.frozen_date_till = self.scope.date_today_datetime_utc

    @staticmethod
    def get_title() -> str:
        return gettext('Low Stock')

    @property
    def subtitle(self) -> str:
        return gettext(
            'List of products with low levels of stock on hand '
            'and their estimated net purchase cost per storage location'
        )

    def __init__(self, *args) -> None:
        super().__init__(*args)
        self.add_section_for_each_warehouse(WarehouseLowStockSection)

    def get_column_widths(self) -> typing.List[int]:
        return [
            5,
            18,
            18,
            18,
            18,
            12,
            12,
            12,
            12,
            18,
        ]
