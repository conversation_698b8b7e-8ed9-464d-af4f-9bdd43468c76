# pylint: disable=duplicate-code
from collections import OrderedDict
from dataclasses import InitVar, dataclass
from datetime import datetime
from decimal import Decimal

from django.db.models import Count, F, OuterRef, QuerySet, Subquery, Sum, Value, Case, When, Q
from django.db.models.functions import Coalesce, Concat
from django.utils.translation import gettext, gettext_lazy as _
from openpyxl import Workbook
from openpyxl.styles import Alignment
from openpyxl.worksheet.worksheet import Worksheet
from rest_framework import serializers

from lib.french_certification.utils import french_certification_enabled
from lib.tools import firstof
from webapps.business.models import Business
from webapps.french_certification.enums import FiscalReceiptType
from webapps.french_certification.models import FiscalReceipt
from webapps.pos.enums import PAYMENT_TYPE_LABEL, receipt_status
from webapps.stats_and_reports import models
from webapps.stats_and_reports.report_keys import ReportKeys
from webapps.stats_and_reports.reports import fields as report_fields
from webapps.stats_and_reports.reports.adapters import get_jet_initialized_at_adapter
from webapps.stats_and_reports.reports.base import (
    BaseReport,
    ReportSection,
    ReportTable,
    ReportTableRow,
    SortableField,
    Sorting,
)
from webapps.stats_and_reports.reports.sales_reports.mixins import (
    ReportVisibleToProvidersMigratedToFrenchCertificationMixin,
)
from webapps.stats_and_reports.reports.spreadsheet_utils import (
    NumberFormat,
    empty_line,
    format_alignment,
    format_number,
    set_columns_width,
    ws_range,
)


class FiscalReceiptSalesLogTableRowSerializer(serializers.Serializer):
    checkout_date = report_fields.ReportsDatetimeField()
    cancellation_date = report_fields.ReportsDatetimeOrTextField()
    fiscal_receipt_number = serializers.CharField()
    type_service = serializers.CharField()
    category = serializers.CharField()
    service_product_addon = serializers.CharField()
    client = serializers.CharField()
    staffer = serializers.CharField()
    quantity = serializers.IntegerField()
    booking_date = report_fields.ReportsDatetimeField()
    service_value = report_fields.ReportsCurrencyField()
    addon_value = report_fields.ReportsCurrencyField()
    revenue_net = report_fields.ReportsCurrencyField()
    discount = report_fields.ReportsCurrencyField()
    tax = report_fields.ReportsCurrencyField()
    total_revenue = report_fields.ReportsCurrencyField()
    payment_type = serializers.CharField()


class FiscalReceiptSalesLogTableTotalRowSerializer(serializers.Serializer):
    label = serializers.CharField(default=_('Total'))
    service_value = report_fields.ReportsCurrencyField()
    addon_value = report_fields.ReportsCurrencyField()
    revenue_net = report_fields.ReportsCurrencyField()
    discount = report_fields.ReportsCurrencyField()
    tax = report_fields.ReportsCurrencyField()
    total_revenue = report_fields.ReportsCurrencyField()

    def to_representation(self, instance):
        row_dict = super().to_representation(instance)
        # This summary row begins with 9 empty columns
        result = OrderedDict.fromkeys('abcdefghi')
        result.update(row_dict)
        return result


class SalesLogTableRowSerializer(serializers.Serializer):
    checkout_date = report_fields.ReportsDatetimeField()
    transaction_id = serializers.CharField()
    type_service = serializers.CharField()
    category = serializers.CharField()
    service_product_addon = serializers.CharField()
    client = serializers.CharField()
    staffer = serializers.CharField()
    quantity = serializers.IntegerField()
    booking_date = report_fields.ReportsDatetimeField()
    service_value = report_fields.ReportsCurrencyField()
    addon_value = report_fields.ReportsCurrencyField()
    revenue_net = report_fields.ReportsCurrencyField()
    discount = report_fields.ReportsCurrencyField()
    tax = report_fields.ReportsCurrencyField()
    gratuity = report_fields.ReportsCurrencyField()
    total_revenue = report_fields.ReportsCurrencyField()
    payment_type = serializers.CharField()


class SalesLogTableTotalRowSerializer(serializers.Serializer):
    label = serializers.CharField(default=_('Total'))
    service_value = report_fields.ReportsCurrencyField()
    addon_value = report_fields.ReportsCurrencyField()
    revenue_net = report_fields.ReportsCurrencyField()
    discount = report_fields.ReportsCurrencyField()
    tax = report_fields.ReportsCurrencyField()
    gratuity = report_fields.ReportsCurrencyField()
    total_revenue = report_fields.ReportsCurrencyField()

    def to_representation(self, instance):
        row_dict = super().to_representation(instance)
        # This summary row begins with 8 empty columns
        result = OrderedDict.fromkeys('abcdefgh')
        result.update(row_dict)
        return result


class SalesLogTable(ReportTable):
    header = (
        _('Checkout date'),
        _('Transaction ID'),
        _('Type'),
        _('Category'),
        _('Service / Product / Add-on'),
        _('Client'),
        _('Staffer'),
        _('Quantity'),
        _('Booking date'),
        _('Service value'),
        _('Add-on value'),
        _('Revenue net'),
        _('Discount'),
        _('Tax'),
        _('Tip'),
        _('Total Revenue'),
        _('Payment Type'),
    )

    @dataclass
    class Row(ReportTableRow):  # pylint: disable=too-many-instance-attributes
        checkout_date: datetime
        transaction_id: str
        type_service: str
        category: str
        service_product_addon: str
        client: str
        staffer: str
        quantity: int
        booking_date: datetime
        service_value: Decimal
        addon_value: Decimal
        revenue_net: Decimal
        discount: Decimal
        tax: Decimal
        gratuity: Decimal
        total_revenue: Decimal
        payment_type: str

        # Business and user language is required for formatting datetime
        _business: InitVar[Business]
        _language: InitVar[str]

        def __post_init__(self, _business, _language):
            self.business = _business
            self.language = _language

        def get_serializer_class(self):
            return SalesLogTableRowSerializer

        def get_serializer(self):
            serializer = super().get_serializer()
            serializer.context['business'] = self.business
            serializer.context['language'] = self.language
            return serializer

    @dataclass
    class TotalRow(ReportTableRow):
        service_value: Decimal
        addon_value: Decimal
        revenue_net: Decimal
        discount: Decimal
        tax: Decimal
        gratuity: Decimal
        total_revenue: Decimal

        def get_serializer_class(self):
            return SalesLogTableTotalRowSerializer

    def format_spreadsheet_table_header(self, ws: Worksheet):
        super().format_spreadsheet_table_header(ws)
        format_alignment(
            ws_range(ws, 'B:B'), Alignment(horizontal='right', wrap_text=True, vertical='center')
        )
        format_alignment(
            ws_range(ws, 'C:H'), Alignment(horizontal='left', wrap_text=True, vertical='center')
        )
        format_alignment(
            ws_range(ws, 'I:Q'), Alignment(horizontal='right', wrap_text=True, vertical='center')
        )
        format_alignment(
            ws_range(ws, 'R:R'), Alignment(horizontal='left', wrap_text=True, vertical='center')
        )

    def format_spreadsheet_table_row(
        self,
        ws: Worksheet,
        row: ReportTableRow,
    ):
        super().format_spreadsheet_table_row(ws, row)
        # In output file all columns are shifted by one (we insert empty column
        # at the beginning of the file), so "A" here will become "B" in xlsx
        format_alignment(ws_range(ws, 'B:B'), Alignment(horizontal='right'))
        format_alignment(ws_range(ws, 'C:H'), Alignment(horizontal='left'))
        format_number(ws_range(ws, 'I:I'), NumberFormat.GENERAL)
        format_alignment(ws_range(ws, 'J:J'), Alignment(horizontal='right'))
        format_number(ws_range(ws, 'K:Q'), NumberFormat.CURRENCY)
        format_alignment(ws_range(ws, 'R:R'), Alignment(horizontal='left'))

    def format_spreadsheet_table_total_row(self, ws: Worksheet):
        super().format_spreadsheet_table_total_row(ws)
        format_number(ws_range(ws, 'K:Q'), NumberFormat.CURRENCY)


class FiscalReceiptSalesLogTable(SalesLogTable):
    header = (
        _('Checkout date'),
        _('Cancellation date'),
        _('Fiscal receipt number'),
        _('Type'),
        _('Category'),
        _('Service / Product / Add-on'),
        _('Client'),
        _('Staffer'),
        _('Quantity'),
        _('Booking date'),
        _('Service value'),
        _('Add-on value'),
        _('Revenue net'),
        _('Discount'),
        _('Tax'),
        _('Total Revenue'),
        _('Payment Type'),
    )

    @dataclass
    class Row(ReportTableRow):  # pylint: disable=too-many-instance-attributes
        checkout_date: datetime
        cancellation_date: datetime | str
        fiscal_receipt_number: str
        type_service: str
        category: str
        service_product_addon: str
        client: str
        staffer: str
        quantity: int
        booking_date: datetime
        service_value: Decimal
        addon_value: Decimal
        revenue_net: Decimal
        discount: Decimal
        tax: Decimal
        total_revenue: Decimal
        payment_type: str

        # Business and user language is required for formatting datetime
        _business: InitVar[Business]
        _language: InitVar[str]

        def __post_init__(self, _business, _language):
            self.business = _business
            self.language = _language

        def get_serializer(self):
            serializer = super().get_serializer()
            serializer.context['business'] = self.business
            serializer.context['language'] = self.language
            return serializer

        def get_serializer_class(self):
            return FiscalReceiptSalesLogTableRowSerializer

    @dataclass
    class TotalRow(ReportTableRow):
        service_value: Decimal
        addon_value: Decimal
        revenue_net: Decimal
        discount: Decimal
        tax: Decimal
        total_revenue: Decimal

        def get_serializer_class(self):
            return FiscalReceiptSalesLogTableTotalRowSerializer


@dataclass
class FiscalReceiptData:
    cancellation_date: datetime | str
    number: str


class SalesLogSection(ReportSection):
    key = 'sales_log_section'
    is_paginated = True
    append_total_row = True
    sortable_fields = (
        SortableField('checkout_date'),
        SortableField('client'),
        SortableField('staffer'),
    )
    default_sorting = Sorting('checkout_date', Sorting.DESC)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fiscal_cancellations_enabled = french_certification_enabled(self.scope.business.id)

    def get_queryset(self) -> QuerySet:
        if self.fiscal_cancellations_enabled:
            return self._fc_get_queryset()
        return self._get_queryset()

    def _fc_get_queryset(self):
        self.basket_id_to_fiscal_number_mapping__cancellations: dict[str, FiscalReceiptData] = {}
        self.basket_id_to_fiscal_number_mapping__sales: dict[str, FiscalReceiptData] = {}
        fiscal_receipts_sales = self._get_fiscal_sales_queryset()
        fiscal_receipts_cancellations = self._get_fiscal_cancellations_queryset()
        merged_queryset = fiscal_receipts_sales.union(fiscal_receipts_cancellations, all=True)
        return merged_queryset

    def _get_queryset(self):
        filter_receipt_status = receipt_status.SUCCESS_STATUSES_WITH_PREPAYMENT
        date_from = self.scope.date_from_datetime_utc
        date_till = self.scope.date_till_by_span
        if till_jet := self.scope.till_initialized_date:
            date_till = min(date_till, till_jet)
            date_from = min(date_from, till_jet)
        return (
            models.TransactionRowReplica.objects.filter(
                transaction__latest_receipt__status_code__in=filter_receipt_status,
                # There can be transactions, that are not bound to Appointments:
                transaction__pos__business_id=self.scope.business.id,
                transaction__latest_receipt__created__gte=date_from,
                transaction__latest_receipt__created__lte=date_till,
                transaction__children__isnull=True,
            )
            .annotate(
                id_transaction=F('transaction__id'),
                checkout_date=F('transaction__latest_receipt__created'),
                service_category=F('service_variant__service__service_category__name'),
                service_name=F('service_variant__service__name'),
                product_name=F('product__name'),
                addon_name=F('addon_use__name'),
                product_category=F('product__category__name'),
                appointment_client=F('transaction__appointment__customer_name'),
                transaction_client=Concat(
                    F('transaction__customer_card__first_name'),
                    Value(' '),
                    F('transaction__customer_card__last_name'),
                ),
                transaction_user=Concat(
                    F('transaction__customer__first_name'),
                    Value(' '),
                    F('transaction__customer__last_name'),
                ),
                staffer_name=Subquery(
                    models.BookingResourceReplica.objects.filter(
                        subbooking_id=OuterRef('subbooking_id'),
                        resource__type=models.ResourceReplica.STAFF,
                    ).values('resource__name')[:1]
                ),
                staffer=Coalesce(
                    F('commission__resource__name'),
                    F('staffer_name'),
                ),
                booking_date=F('transaction__appointment__booked_from'),
                service_value=Case(
                    When(
                        ~Q(type=models.TransactionRowReplica.TRANSACTION_ROW_TYPE__ADDON),
                        then=F('service_variant_price'),
                    ),
                ),
                addon_value=Case(
                    When(
                        type=models.TransactionRowReplica.TRANSACTION_ROW_TYPE__ADDON,
                        then=F('addon_use__price'),
                    ),
                ),
                gratuity=F('transaction__tip__amount'),
                total_bookings=Count('transaction__appointment__bookings__id', distinct=True),
                payment_type=F('transaction__latest_receipt__payment_type__code'),
            )
            .values(
                'id',
                'id_transaction',
                'checkout_date',
                'type',
                'service_category',
                'service_name',
                'product_category',
                'product_name',
                'addon_name',
                'appointment_client',
                'transaction_client',
                'transaction_user',
                'staffer',
                'quantity',
                'booking_date',
                'service_value',
                'addon_value',
                'net_total',
                'real_discount_amount',
                'tax_amount',
                'gratuity',
                'total_bookings',
                'payment_type',
            )
            .order_by('checkout_date')
        )

    def get_sequence_record(self, ids):
        sequences = (
            models.SequenceRecordReplica.objects.filter(
                business=self.scope.business.id,
                type=models.SequenceRecordReplica.SALES_DOCUMENT,
                related_document_id__in=ids,
            )
            .annotate_assigned_number()
            .values('assigned_number', 'related_document_id')
        )

        assigned_numbers = {
            sequence['related_document_id']: sequence['assigned_number'] for sequence in sequences
        }

        return assigned_numbers

    @staticmethod
    def get_client_name(row):
        possible_names = [
            row.get('appointment_client'),
            row.get('transaction_client'),
            row.get('transaction_user'),
        ]

        name = firstof(name for name in possible_names if name and not name.isspace())
        return name or gettext('Walk-in')

    def coerce_data_to_table(self, results) -> SalesLogTable:
        if self.fiscal_cancellations_enabled:
            return self._fc_coerce_data_to_table(results)
        return self._coerce_data_to_table(results)

    def _fc_coerce_data_to_table(self, results):
        rows = []
        for row in results:
            basket_id = str(row.get('basket_id'))
            if row.get('is_fiscal_cancellation'):
                fiscal_receipt_data = self.basket_id_to_fiscal_number_mapping__cancellations.get(
                    basket_id
                )  # pylint: disable=line-too-long
            else:
                fiscal_receipt_data = self.basket_id_to_fiscal_number_mapping__sales.get(
                    basket_id
                )  # pylint: disable=line-too-long
            category = row.get('service_category') or row.get('product_category')
            service_product_addon = (
                row.get('addon_name') or row.get('service_name') or row.get('product_name')
            )
            client = self.get_client_name(row)
            staffer = row.get('staffer') or gettext('Not selected')

            service_value = row.get('fiscal_service_value') or Decimal(0)
            addon_value = row.get('fiscal_addon_value') or Decimal(0)
            revenue_net = row.get('fiscal_net_total') or Decimal(0)
            real_discount_amount = row.get('fiscal_real_discount_amount') or Decimal(0)
            tax_amount = row.get('fiscal_tax_amount') or Decimal(0)
            payment_type = PAYMENT_TYPE_LABEL.get(row['payment_type'])
            type_service = models.TransactionRowReplica.TRANSACTION_TYPES.get(row['type'])

            total_revenue_per_transaction = tax_amount + revenue_net

            rows.append(
                FiscalReceiptSalesLogTable.Row(
                    checkout_date=row['checkout_date'],
                    cancellation_date=fiscal_receipt_data.cancellation_date,
                    fiscal_receipt_number=fiscal_receipt_data.number,
                    type_service=type_service,
                    category=category,
                    service_product_addon=service_product_addon,
                    client=client,
                    staffer=staffer,
                    quantity=row['quantity'],
                    booking_date=row['booking_date'],
                    service_value=service_value,
                    addon_value=addon_value,
                    revenue_net=revenue_net,
                    discount=real_discount_amount,
                    tax=tax_amount,
                    total_revenue=total_revenue_per_transaction,
                    payment_type=payment_type,
                    _business=self.scope.business,
                    _language=self.scope.language,
                )
            )

        return FiscalReceiptSalesLogTable(
            rows=rows,
            sortable_fields=self.sortable_fields,
            active_sorting=self.sorting_to_apply,
        )

    def _coerce_data_to_table(self, results):
        rows = []
        transaction_ids = [row['id_transaction'] for row in results]
        assigned_numbers = self.get_sequence_record(transaction_ids)

        # There may be more than one transaction row with the same tip
        # We have to display it only once (with first transaction row)
        transactions_tips_already_used = set()

        for row in results:
            id_transaction = row.get('id_transaction')
            assigned_number = assigned_numbers.get(id_transaction, id_transaction)
            category = row.get('service_category') or row.get('product_category')
            service_product_addon = (
                row.get('addon_name') or row.get('service_name') or row.get('product_name')
            )
            client = self.get_client_name(row)
            staffer = row.get('staffer') or gettext('Not selected')

            service_value = row.get('service_value') or Decimal(0)
            addon_value = row.get('addon_value') or Decimal(0)
            revenue_net = row.get('net_total') or Decimal(0)
            real_discount_amount = row.get('real_discount_amount') or Decimal(0)
            tax_amount = row.get('tax_amount') or Decimal(0)
            payment_type = PAYMENT_TYPE_LABEL.get(row['payment_type'])
            type_service = models.TransactionRowReplica.TRANSACTION_TYPES.get(row['type'])

            gratuity = Decimal(0)
            if id_transaction not in transactions_tips_already_used:
                gratuity = row.get('gratuity') or Decimal(0)
                transactions_tips_already_used.add(id_transaction)

            total_revenue_per_transaction = tax_amount + revenue_net + gratuity

            rows.append(
                SalesLogTable.Row(
                    checkout_date=row['checkout_date'],
                    transaction_id=assigned_number,
                    type_service=type_service,
                    category=category,
                    service_product_addon=service_product_addon,
                    client=client,
                    staffer=staffer,
                    quantity=row['quantity'],
                    booking_date=row['booking_date'],
                    service_value=service_value,
                    addon_value=addon_value,
                    revenue_net=revenue_net,
                    discount=real_discount_amount,
                    tax=tax_amount,
                    gratuity=gratuity,
                    total_revenue=total_revenue_per_transaction,
                    payment_type=payment_type,
                    _business=self.scope.business,
                    _language=self.scope.language,
                )
            )

        return SalesLogTable(
            rows=rows,
            sortable_fields=self.sortable_fields,
            active_sorting=self.sorting_to_apply,
        )

    def calculate_total_row(self, queryset: QuerySet) -> ReportTableRow:
        if self.fiscal_cancellations_enabled:
            return self._fc_calculate_total_row(queryset)
        return self._calculate_total_row(queryset)

    def _fc_calculate_total_row(self, queryset):
        aggregated_qs = queryset.aggregate(
            total_service_value=Coalesce(Sum('fiscal_service_value'), Decimal(0)),
            total_addon_value=Coalesce(Sum('fiscal_addon_value'), Decimal(0)),
            total_revenue_net=Coalesce(Sum('fiscal_net_total'), Decimal(0)),
            total_discount=Coalesce(Sum('fiscal_real_discount_amount'), Decimal(0)),
            total_tax=Coalesce(Sum('fiscal_tax_amount'), Decimal(0)),
        )
        return FiscalReceiptSalesLogTable.TotalRow(
            service_value=aggregated_qs['total_service_value'],
            addon_value=aggregated_qs['total_addon_value'],
            revenue_net=aggregated_qs['total_revenue_net'],
            discount=aggregated_qs['total_discount'],
            tax=aggregated_qs['total_tax'],
            total_revenue=aggregated_qs['total_tax'] + aggregated_qs['total_revenue_net'],
        )

    def _calculate_total_row(self, queryset):
        aggregated_qs = queryset.aggregate(
            total_service_value=Coalesce(Sum('service_value'), Decimal(0)),
            total_addon_value=Coalesce(Sum('addon_value'), Decimal(0)),
            total_revenue_net=Coalesce(Sum('net_total'), Decimal(0)),
            total_discount=Coalesce(Sum('real_discount_amount'), Decimal(0)),
            total_tax=Coalesce(Sum('tax_amount'), Decimal(0)),
        )
        transactions_ids = list(queryset.values_list('id_transaction', flat=True))
        tips_total = models.TransactionReplica.objects.filter(id__in=transactions_ids).aggregate(
            tips_total=Sum('tip__amount')
        )['tips_total'] or Decimal(0)
        return SalesLogTable.TotalRow(
            aggregated_qs['total_service_value'],
            aggregated_qs['total_addon_value'],
            aggregated_qs['total_revenue_net'],
            aggregated_qs['total_discount'],
            aggregated_qs['total_tax'],
            tips_total,
            aggregated_qs['total_tax'] + aggregated_qs['total_revenue_net'] + tips_total,
        )

    def _get_fiscal_cancellations_queryset(self):
        cancellations_fiscal_receipts = FiscalReceipt.objects.filter(
            business_id=self.scope.business.id,
            type=FiscalReceiptType.CANCELLATION,
            created__gte=self.scope.date_from_datetime_utc,
            created__lte=self.scope.date_till_by_span,
        )
        cancellations_basket_items_ids = []
        for fiscal_receipt in cancellations_fiscal_receipts:
            cancellations_basket_items_ids.extend(fiscal_receipt.basket_item_ids)
            fiscal_receipt_data = FiscalReceiptData(
                cancellation_date=fiscal_receipt.created,
                number=fiscal_receipt.number,
            )
            self.basket_id_to_fiscal_number_mapping__cancellations[
                str(fiscal_receipt.basket_id)
            ] = fiscal_receipt_data

        filter_receipt_status = receipt_status.SUCCESS_STATUSES_WITH_PREPAYMENT
        return (
            models.TransactionRowReplica.objects.filter(
                transaction__latest_receipt__status_code__in=filter_receipt_status,
                # There can be transactions, that are not bound to Appointments:
                transaction__pos__business_id=self.scope.business.id,
                transaction__children__isnull=True,
                basket_item_id__in=cancellations_basket_items_ids,
            )
            .annotate(
                id_transaction=F('transaction__id'),
                basket_id=F('transaction__basket_id'),
                checkout_date=F('transaction__latest_receipt__created'),
                service_category=F('service_variant__service__service_category__name'),
                service_name=F('service_variant__service__name'),
                product_name=F('product__name'),
                addon_name=F('addon_use__name'),
                product_category=F('product__category__name'),
                appointment_client=F('transaction__appointment__customer_name'),
                transaction_client=Concat(
                    F('transaction__customer_card__first_name'),
                    Value(' '),
                    F('transaction__customer_card__last_name'),
                ),
                transaction_user=Concat(
                    F('transaction__customer__first_name'),
                    Value(' '),
                    F('transaction__customer__last_name'),
                ),
                staffer_name=Subquery(
                    models.BookingResourceReplica.objects.filter(
                        subbooking_id=OuterRef('subbooking_id'),
                        resource__type=models.ResourceReplica.STAFF,
                    ).values('resource__name')[:1]
                ),
                staffer=Coalesce(
                    F('commission__resource__name'),
                    F('staffer_name'),
                ),
                booking_date=F('transaction__appointment__booked_from'),
                fiscal_service_value=Case(
                    When(
                        ~Q(type=models.TransactionRowReplica.TRANSACTION_ROW_TYPE__ADDON),
                        then=-F('service_variant_price'),
                    ),
                ),
                fiscal_addon_value=Case(
                    When(
                        type=models.TransactionRowReplica.TRANSACTION_ROW_TYPE__ADDON,
                        then=-F('addon_use__price'),
                    ),
                ),
                total_bookings=Count('transaction__appointment__bookings__id', distinct=True),
                payment_type=F('transaction__latest_receipt__payment_type__code'),
                fiscal_net_total=-F('net_total'),
                fiscal_real_discount_amount=-F('real_discount_amount'),
                fiscal_tax_amount=-F('tax_amount'),
                is_fiscal_cancellation=Value(True),
            )
            .values(
                'id',
                'id_transaction',
                'basket_id',
                'checkout_date',
                'type',
                'service_category',
                'service_name',
                'product_category',
                'product_name',
                'addon_name',
                'appointment_client',
                'transaction_client',
                'transaction_user',
                'staffer',
                'quantity',
                'booking_date',
                'fiscal_service_value',
                'fiscal_addon_value',
                'fiscal_net_total',
                'fiscal_real_discount_amount',
                'fiscal_tax_amount',
                'total_bookings',
                'payment_type',
                'is_fiscal_cancellation',
            )
        )

    def _get_fiscal_sales_queryset(self):
        sales_fiscal_receipts = FiscalReceipt.objects.filter(
            business_id=self.scope.business.id,
            type=FiscalReceiptType.SALE,
            created__gte=self.scope.date_from_datetime_utc,
            created__lte=self.scope.date_till_by_span,
        )
        sales_basket_items_ids = []
        for fiscal_receipt in sales_fiscal_receipts:
            sales_basket_items_ids.extend(fiscal_receipt.basket_item_ids)
            fiscal_receipt_data = FiscalReceiptData(
                cancellation_date='',
                number=fiscal_receipt.number,
            )
            self.basket_id_to_fiscal_number_mapping__sales[str(fiscal_receipt.basket_id)] = (
                fiscal_receipt_data
            )

        filter_receipt_status = receipt_status.SUCCESS_STATUSES_WITH_PREPAYMENT
        return (
            models.TransactionRowReplica.objects.filter(
                transaction__latest_receipt__status_code__in=filter_receipt_status,
                # There can be transactions, that are not bound to Appointments:
                transaction__pos__business_id=self.scope.business.id,
                transaction__children__isnull=True,
                basket_item_id__in=sales_basket_items_ids,
            )
            .annotate(
                id_transaction=F('transaction__id'),
                basket_id=F('transaction__basket_id'),
                checkout_date=F('transaction__latest_receipt__created'),
                service_category=F('service_variant__service__service_category__name'),
                service_name=F('service_variant__service__name'),
                product_name=F('product__name'),
                addon_name=F('addon_use__name'),
                product_category=F('product__category__name'),
                appointment_client=F('transaction__appointment__customer_name'),
                transaction_client=Concat(
                    F('transaction__customer_card__first_name'),
                    Value(' '),
                    F('transaction__customer_card__last_name'),
                ),
                transaction_user=Concat(
                    F('transaction__customer__first_name'),
                    Value(' '),
                    F('transaction__customer__last_name'),
                ),
                staffer_name=Subquery(
                    models.BookingResourceReplica.objects.filter(
                        subbooking_id=OuterRef('subbooking_id'),
                        resource__type=models.ResourceReplica.STAFF,
                    ).values('resource__name')[:1]
                ),
                staffer=Coalesce(
                    F('commission__resource__name'),
                    F('staffer_name'),
                ),
                booking_date=F('transaction__appointment__booked_from'),
                fiscal_service_value=Case(
                    When(
                        ~Q(type=models.TransactionRowReplica.TRANSACTION_ROW_TYPE__ADDON),
                        then=F('service_variant_price'),
                    ),
                ),
                fiscal_addon_value=Case(
                    When(
                        type=models.TransactionRowReplica.TRANSACTION_ROW_TYPE__ADDON,
                        then=F('addon_use__price'),
                    ),
                ),
                total_bookings=Count('transaction__appointment__bookings__id', distinct=True),
                payment_type=F('transaction__latest_receipt__payment_type__code'),
                fiscal_net_total=F('net_total'),
                fiscal_real_discount_amount=F('real_discount_amount'),
                fiscal_tax_amount=F('tax_amount'),
                is_fiscal_cancellation=Value(False),
            )
            .values(
                'id',
                'id_transaction',
                'basket_id',
                'checkout_date',
                'type',
                'service_category',
                'service_name',
                'product_category',
                'product_name',
                'addon_name',
                'appointment_client',
                'transaction_client',
                'transaction_user',
                'staffer',
                'quantity',
                'booking_date',
                'fiscal_service_value',
                'fiscal_addon_value',
                'fiscal_net_total',
                'fiscal_real_discount_amount',
                'fiscal_tax_amount',
                'total_bookings',
                'payment_type',
                'is_fiscal_cancellation',
            )
        )


class SalesLogBeforeFrenchMigrationSection(SalesLogSection):
    key = "sales_log_before_french_migration_section"

    def __init__(self, *args, **kwargs):
        """TimeDataScope, Period etc. have their own calculations of date_till depending
        only on date_from, so it makes it difficult to manipulate date_till in cases
        like French Certification. Purpose of the code below is to change final
        date_till, no matter what period of time was selected by user.
        """
        super().__init__(*args, **kwargs)
        self.fiscal_cancellations_enabled = False
        if jet_initialized_at := get_jet_initialized_at_adapter(self.scope.business.id):
            self.scope.till_initialized_date = jet_initialized_at


class SalesLog(BaseReport):
    key = ReportKeys.SalesLog
    section = SalesLogSection

    @staticmethod
    def get_title() -> str:
        return gettext('Sales log')

    @property
    def subtitle(self) -> str:
        return gettext(
            'List of all transactions, incl. sales type, value, revenue, and payment type'
        )

    def __init__(self, *args) -> None:
        super().__init__(*args)
        self.sales_log = self._add_section(self.section)

    def render_spreadsheet_content(self, workbook: Workbook):
        ws = workbook.active
        ws.title = self.get_title()
        empty_line(ws)

        self.sales_log.render_spreadsheet_content(ws)
        empty_line(ws)

        ws.insert_cols(0, 1)
        set_columns_width(
            ws_range(ws, 'B:R'),
            [25, 25, 15, 20, 20, 20, 20, 15, 25, 20, 20, 20, 20, 20, 20, 20, 20],
        )


class SalesLogBeforeFrenchMigration(
    ReportVisibleToProvidersMigratedToFrenchCertificationMixin,
    SalesLog,
):
    key = ReportKeys.SalesLogBeforeFrenchMigration
    section = SalesLogBeforeFrenchMigrationSection

    @staticmethod
    def get_title() -> str:
        return gettext('Sales Log Archived')
