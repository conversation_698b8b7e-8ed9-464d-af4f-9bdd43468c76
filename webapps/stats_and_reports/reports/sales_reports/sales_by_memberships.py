# pylint: disable=duplicate-code

import datetime
import typing
from collections import OrderedDict
from dataclasses import dataclass, InitVar
from decimal import Decimal

from django.db.models import F, QuerySet, Sum
from django.db.models.functions import Coalesce
from django.utils.translation import gettext, gettext_lazy as _
from openpyxl.styles import Alignment
from openpyxl.worksheet.worksheet import Worksheet
from rest_framework import serializers

from webapps.pos.enums import PAYMENT_TYPE_LABEL, receipt_status
from webapps.stats_and_reports import models
from webapps.stats_and_reports.report_keys import ReportKeys
from webapps.stats_and_reports.reports import fields as report_fields
from webapps.stats_and_reports.reports.base import (
    BaseReport,
    ReportSection,
    ReportTable,
    ReportTableRow,
    SortableField,
    Sorting,
)
from webapps.stats_and_reports.reports.sales_reports.sales_by_packages import (
    VouchersSalesLogCommonBaseSection,
)
from webapps.stats_and_reports.reports.spreadsheet_utils import (
    format_alignment,
    format_number,
    NumberFormat,
    ws_range,
)
from webapps.voucher.enums import VoucherType


class TableTotalRowSerializer(serializers.Serializer):
    label = serializers.CharField(default=_('Total'))
    revenue_net = report_fields.ReportsCurrencyField()
    tax = report_fields.ReportsCurrencyField()
    discount = report_fields.ReportsCurrencyField()
    total_revenue = report_fields.ReportsCurrencyField()

    def to_representation(self, instance):
        row_dict = super().to_representation(instance)
        result = OrderedDict.fromkeys('abc')  # 4 empty cols
        result.update(row_dict)
        return result


class SalesByMembershipsTableRowSerializer(serializers.Serializer):
    membership_name = serializers.CharField()
    price = report_fields.ReportsCurrencyField()
    validity = serializers.CharField()
    quantity_sold = serializers.IntegerField()
    revenue_net = report_fields.ReportsCurrencyField()
    tax = report_fields.ReportsCurrencyField()
    discount = report_fields.ReportsCurrencyField()
    total_revenue = report_fields.ReportsCurrencyField()


class SalesByMembershipsTable(ReportTable):
    header = (
        _('Membership name'),
        _('Price'),
        _('Validity'),
        _('Quantity sold'),
        _('Revenue net'),
        _('Tax'),
        _('Discount'),
        _('Total Revenue'),
    )

    @dataclass
    class Row(ReportTableRow):  # pylint: disable=too-many-instance-attributes
        membership_name: str
        price: Decimal
        validity: str
        quantity_sold: int
        revenue_net: Decimal
        tax: Decimal
        discount: Decimal
        total_revenue: Decimal

        # Business and user language is required for formatting datetime
        _business: InitVar[models.Business]
        _language: InitVar[str]

        def __post_init__(self, _business, _language):
            self.business = _business
            self.language = _language

        def get_serializer_class(self):
            return SalesByMembershipsTableRowSerializer

        def get_serializer(self):
            serializer = super().get_serializer()
            serializer.context['business'] = self.business
            serializer.context['language'] = self.language
            return serializer

    @dataclass
    class TotalRow(ReportTableRow):
        revenue_net: Decimal
        tax: Decimal
        discount: Decimal
        total_revenue: Decimal

        def get_serializer_class(self):
            return TableTotalRowSerializer

    def format_spreadsheet_table_header(self, ws: Worksheet):
        super().format_spreadsheet_table_header(ws)
        format_alignment(
            ws_range(ws, 'B:D'), Alignment(horizontal='left', wrap_text=True, vertical='center')
        )
        format_alignment(
            ws_range(ws, 'C:C'), Alignment(horizontal='right', wrap_text=True, vertical='center')
        )
        format_alignment(
            ws_range(ws, 'E:I'), Alignment(horizontal='right', wrap_text=True, vertical='center')
        )

    def format_spreadsheet_table_row(self, ws: Worksheet, row: ReportTableRow):
        super().format_spreadsheet_table_row(ws, row)
        # In output file all columns are shifted by one (we insert empty column
        # at the beginning of the file), so "A" here will become "B" in xlsx
        format_alignment(ws_range(ws, 'B:B'), Alignment(horizontal='left'))
        format_number(ws_range(ws, 'C:C'), NumberFormat.CURRENCY)
        format_alignment(ws_range(ws, 'D:D'), Alignment(horizontal='left'))
        format_number(ws_range(ws, 'E:E'), NumberFormat.GENERAL)
        format_number(ws_range(ws, 'F:I'), NumberFormat.CURRENCY)

    def format_spreadsheet_table_total_row(self, ws: Worksheet):
        super().format_spreadsheet_table_total_row(ws)
        format_number(ws_range(ws, 'F:I'), NumberFormat.CURRENCY)


class SalesByMembershipsSection(ReportSection):
    key = 'sales_by_memberships_section'
    is_paginated = True
    append_total_row = True
    sortable_fields = (SortableField('membership_name'),)

    def get_queryset(self) -> QuerySet:
        filter_receipt_status = receipt_status.SUCCESS_STATUSES_WITH_PREPAYMENT
        return (
            models.TransactionRowReplica.objects.filter(
                # There can be transactions, that are not bound to Appointments:
                transaction__latest_receipt__status_code__in=filter_receipt_status,
                transaction__pos__business_id=self.scope.business.id,
                transaction__latest_receipt__created__gte=self.scope.date_from_datetime_utc,
                transaction__latest_receipt__created__lte=self.scope.date_till_by_span,
                type=models.TransactionRowReplica.TRANSACTION_ROW_TYPE__VOUCHER,
                voucher__pos__business_id=self.scope.business.id,
                voucher__voucher_template__type=VoucherType.MEMBERSHIP,
            )
            .annotate(
                _voucher_template=F('voucher__voucher_template'),
            )
            .values('_voucher_template')
            .annotate(
                membership_name=F('voucher__voucher_template__name'),
                price=F('voucher__voucher_template__item_price'),
                validity=F('voucher__voucher_template__valid_till'),
                _quantity=Sum(Coalesce(F('quantity'), 0)),
                revenue_net=Sum(Coalesce(F('net_total'), Decimal(0))),
                tax=Sum(Coalesce(F('tax_amount'), Decimal(0))),
                discount=Sum(Coalesce(F('real_discount_amount'), Decimal(0))),
                total_revenue=F('revenue_net') + F('tax'),
            )
            .order_by('membership_name')
        )

    def get_total_row_queryset(self) -> QuerySet:
        return self.get_queryset().aggregate(
            total_revenue_net=Coalesce(Sum('revenue_net'), Decimal(0)),
            total_tax=Coalesce(Sum('tax'), Decimal(0)),
            total_discount=Coalesce(Sum('discount'), Decimal(0)),
            total_total_revenue=Coalesce(Sum('total_revenue'), Decimal(0)),
        )

    def coerce_data_to_table(self, results) -> SalesByMembershipsTable:
        rows = []

        for row in results:
            rows.append(
                SalesByMembershipsTable.Row(
                    membership_name=row.get('membership_name'),
                    price=row.get('price'),
                    validity=models.VoucherTemplateReplica.VALID_TILL_TO_STRING.get(
                        row.get('validity')
                    ),
                    quantity_sold=row.get('_quantity'),
                    revenue_net=row.get('revenue_net'),
                    tax=row.get('tax'),
                    discount=row.get('discount'),
                    total_revenue=row.get('total_revenue'),
                    _business=self.scope.business,
                    _language=self.scope.language,
                )
            )

        return SalesByMembershipsTable(
            rows=rows,
            sortable_fields=self.sortable_fields,
            active_sorting=self.sorting_to_apply,
        )

    def calculate_total_row(self, queryset: QuerySet) -> ReportTableRow:
        return SalesByMembershipsTable.TotalRow(
            queryset['total_revenue_net'],
            queryset['total_tax'],
            queryset['total_discount'],
            queryset['total_total_revenue'],
        )


class MembershipsSalesLogTableRowSerializer(serializers.Serializer):
    checkout_date = report_fields.ReportsDatetimeField()
    transaction_id = serializers.CharField()
    buyer = serializers.CharField()
    client = serializers.CharField()
    membership_number = serializers.IntegerField()
    valid_from = report_fields.ReportsDateOnlyField()
    valid_till = report_fields.ReportsDateOnlyField()
    revenue_net = report_fields.ReportsCurrencyField()
    tax = report_fields.ReportsCurrencyField()
    discount = report_fields.ReportsCurrencyField()
    total_revenue = report_fields.ReportsCurrencyField()
    payment_type = serializers.CharField()


class MembershipsSalesLogTableTotalRowSerializer(serializers.Serializer):
    label = serializers.CharField(default=_('Total'))
    revenue_net = report_fields.ReportsCurrencyField()
    tax = report_fields.ReportsCurrencyField()
    discount = report_fields.ReportsCurrencyField()
    total_revenue = report_fields.ReportsCurrencyField()

    def to_representation(self, instance):
        row_dict = super().to_representation(instance)
        result = OrderedDict.fromkeys('abcdef')  # 6 empty cols
        result.update(row_dict)
        return result


class MembershipsSalesLogTable(ReportTable):
    header = (
        _('Checkout date'),
        _('Transaction ID'),
        _('Buyer'),
        _('Client'),
        _('Membership number'),
        _('Valid from'),
        _('Valid to'),
        _('Revenue net'),
        _('Tax'),
        _('Discount'),
        _('Total Revenue'),
        _('Payment type'),
    )

    @dataclass
    class Row(ReportTableRow):  # pylint: disable=too-many-instance-attributes
        checkout_date: datetime.datetime
        transaction_id: str
        buyer: str
        client: str
        membership_number: int
        valid_from: datetime.date
        valid_till: datetime.date
        revenue_net: Decimal
        tax: Decimal
        discount: Decimal
        total_revenue: Decimal
        payment_type: str

        # Business and user language is required for formatting datetime
        _business: InitVar[models.Business]
        _language: InitVar[str]

        def __post_init__(self, _business, _language):
            self.business = _business
            self.language = _language

        def get_serializer_class(self):
            return MembershipsSalesLogTableRowSerializer

        def get_serializer(self):
            serializer = super().get_serializer()
            serializer.context['business'] = self.business
            serializer.context['language'] = self.language
            return serializer

    @dataclass
    class TotalRow(ReportTableRow):
        revenue_net: Decimal
        tax: Decimal
        discount: Decimal
        total_revenue: Decimal

        def get_serializer_class(self):
            return MembershipsSalesLogTableTotalRowSerializer

    def format_spreadsheet_table_header(self, ws: Worksheet):
        super().format_spreadsheet_table_header(ws)
        format_alignment(
            ws_range(ws, 'B:H'), Alignment(horizontal='left', wrap_text=True, vertical='center')
        )
        format_alignment(
            ws_range(ws, 'I:L'), Alignment(horizontal='right', wrap_text=True, vertical='center')
        )

    def format_spreadsheet_table_row(
        self,
        ws: Worksheet,
        row: ReportTableRow,
    ):
        super().format_spreadsheet_table_row(ws, row)
        # In output file all columns are shifted by one (we insert empty column
        # at the beginning of the file), so "A" here will become "B" in xlsx
        format_alignment(ws_range(ws, 'B:H'), Alignment(horizontal='left'))
        format_number(ws_range(ws, 'I:L'), NumberFormat.CURRENCY)
        format_alignment(ws_range(ws, 'M:M'), Alignment(horizontal='left'))

    def format_spreadsheet_table_total_row(self, ws: Worksheet):
        super().format_spreadsheet_table_total_row(ws)
        format_number(ws_range(ws, 'I:L'), NumberFormat.CURRENCY)


class MembershipsSalesLogSection(VouchersSalesLogCommonBaseSection):
    key = 'sales_log_by_memberships_section'
    title = _('Memberships Sales Log')
    description = _('List of Memberships sold with transaction details')
    is_paginated = True
    append_total_row = True
    sortable_fields = (
        SortableField('checkout_date'),
        SortableField('membership_number', db_column='voucher_number'),
        SortableField('valid_from'),
        SortableField('valid_till'),
        SortableField('payment_type', db_column='payment_type_code'),
    )
    default_sorting = Sorting('checkout_date', Sorting.ASC)

    def get_queryset(self) -> QuerySet:
        return self.get_base_queryset().filter(
            voucher__pos__business_id=self.scope.business.id,
            voucher__voucher_template__type=VoucherType.MEMBERSHIP,
        )

    def get_total_row_queryset(self) -> QuerySet:
        return self.get_queryset().aggregate(
            total_revenue_net=Coalesce(Sum('revenue_net'), Decimal(0)),
            total_tax=Coalesce(Sum('tax_amount'), Decimal(0)),
            total_discount=Coalesce(Sum('discount'), Decimal(0)),
            total_total_revenue=Coalesce(Sum('total_revenue'), Decimal(0)),
        )

    def coerce_data_to_table(self, results) -> MembershipsSalesLogTable:
        rows = []

        transaction_ids = list(set(row['transaction_id'] for row in results))
        assigned_numbers = self.get_sequence_record(transaction_ids)

        for row in results:
            assigned_number = assigned_numbers.get(row['transaction_id'], row['transaction_id'])
            payment_type_name = PAYMENT_TYPE_LABEL.get(
                row['payment_type_code'], row['payment_type_code']
            )
            transaction_buyer_name = self._bci_or_user_full_name(
                row,
                lookup_prefix='transaction__customer_card',
                default=_('Walk-in'),
            )
            voucher_owner_name = self._bci_or_user_full_name(row, lookup_prefix='voucher__customer')

            rows.append(
                MembershipsSalesLogTable.Row(
                    checkout_date=row['checkout_date'],
                    transaction_id=assigned_number,
                    buyer=transaction_buyer_name,
                    client=voucher_owner_name,
                    membership_number=row['voucher_number'],
                    valid_from=row['valid_from'],
                    valid_till=row['valid_till'],
                    revenue_net=row['revenue_net'],
                    tax=row['tax_amount'],
                    discount=row['discount'],
                    total_revenue=row['total_revenue'],
                    payment_type=payment_type_name,
                    _business=self.scope.business,
                    _language=self.scope.language,
                )
            )

        return MembershipsSalesLogTable(
            rows=rows,
            sortable_fields=self.sortable_fields,
            active_sorting=self.sorting_to_apply,
        )

    def calculate_total_row(self, queryset: QuerySet) -> ReportTableRow:
        return MembershipsSalesLogTable.TotalRow(
            queryset['total_revenue_net'],
            queryset['total_tax'],
            queryset['total_discount'],
            queryset['total_total_revenue'],
        )


class SalesByMemberships(BaseReport):
    key = ReportKeys.SalesByMemberships

    @staticmethod
    def get_title() -> str:
        return gettext('Sales by memberships')

    @property
    def subtitle(self) -> str:
        return gettext('Memberships sales summary by Membership name')

    def __init__(self, *args) -> None:
        super().__init__(*args)
        self._add_section(SalesByMembershipsSection)
        self._add_section(MembershipsSalesLogSection)

    def get_column_widths(self) -> typing.List[int]:
        # fmt: off
        return [
            5, 16, 14,
            16, 16, 16,
            12, 12, 12, 12, 12, 12, 12,
        ]
