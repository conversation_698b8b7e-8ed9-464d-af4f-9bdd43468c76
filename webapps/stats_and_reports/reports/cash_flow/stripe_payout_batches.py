from decimal import Decimal
from typing import List

from django.conf import settings
from django.db.models import Q, F, Min, Max, QuerySet, Subquery, OuterRef
from django.utils.translation import gettext, gettext_lazy as _

from lib.tools import major_unit
from webapps.business.models import Business
from webapps.pos.enums import PaymentTypeEnum, receipt_status
from webapps.stats_and_reports.models import (
    PaymentRowReplica,
    StripePaymentIntentReplica,
    StripePayoutReplica,
)
from webapps.stats_and_reports.report_keys import ReportKeys
from webapps.stats_and_reports.reports.base import BaseReport, TimeDataScope
from webapps.stats_and_reports.reports.cash_flow.mobile_payments_transactions_summary import (
    CashFlowSpreadsheetColumnsWidthMixin,
    MobilePaymentsTransactionsSummarySection,
)
from webapps.stats_and_reports.reports.time_data import TimeScopeType
from webapps.stripe_integration.enums import Stripe<PERSON>ayout<PERSON>tatus, StripePayoutMethodType


class StripePayoutBatchesSection(MobilePaymentsTransactionsSummarySection):
    key = 'stripe_payout_batches_section'
    title = _('Payouts Batches')
    description = _('List of Payouts with transaction details')

    payment_row_statuses = [
        receipt_status.PREPAYMENT_SUCCESS,
        receipt_status.DEPOSIT_CHARGE_SUCCESS,
        receipt_status.PAYMENT_SUCCESS,
        receipt_status.REFUNDED,
        receipt_status.CHARGEBACK,
        receipt_status.SECOND_CHARGEBACK,
        receipt_status.CHARGEBACK_REVERSED,
    ]

    def render_fast_payout_row(self, row):
        fast_payout_splits = row['fast_payout_splits'] or {}

        data = dict(  # pylint:disable=use-dict-literal
            transaction_date=row['created'],
            transaction_type='Fast Payout',
            transaction_amount=major_unit(fast_payout_splits.get('total_amount', 0)),
            service_date='-',
            service_name='-',
            service_value=Decimal(0),
            addons_value=Decimal(0),
            value_of_goods=None,
            tips=None,
            total=None,
            staffer='-',
            booking_created_date=None,
            client_name='-',
            receipt_number='-',
            _business=self.scope.business,
            _language=self.scope.language,
            _has_marketpay_splits=True,
        )
        if self.scope.business.pos.marketpay_enabled:
            data['net_amount'] = Decimal('0')
            data['processing_fee'] = -major_unit(fast_payout_splits.get('total_fee_amount', 0))
            data['paid_out'] = '-'

        data = self.modify_row_data(data)

        return self.get_table_class().Row(**data)

    def insert_fast_payout_objects_to_payment_rows_table(
        self, fast_payouts_qs: QuerySet, rows: List
    ):
        fast_payouts_objects = fast_payouts_qs.structured_values(
            'created', 'fast_payout_splits', 'amount'
        )
        for row in fast_payouts_objects:
            rows.append(self.render_fast_payout_row(row))
        self.rows_sorting_with_transaction_date(rows)

    def rows_sorting_with_transaction_date(self, rows: List) -> None:
        rev = False
        if self.sorting_to_apply.get_sorting_sign() == '-':
            rev = True
        rows.sort(key=lambda x: x.transaction_date, reverse=rev)

    def get_period_from_filter(self):
        return {"created__gte": self.scope.date_from_datetime_utc}

    def get_period_to_filter(self):
        return {"created__lte": self.scope.date_till_by_span}

    def fast_payouts_queryset(self):
        qs = StripePayoutReplica.objects.filter(
            account__pos__business_id=self.scope.business.id,
            status=StripePayoutStatus.PAID,
            method=StripePayoutMethodType.INSTANT,
            **self.get_period_from_filter(),
            **self.get_period_to_filter(),
        ).order_by('created')
        return qs

    def additional_rows_actions(self, rows: List):
        fast_payouts_qs = self.fast_payouts_queryset()
        if fast_payouts_qs:
            self.insert_fast_payout_objects_to_payment_rows_table(fast_payouts_qs, rows)

    def payment_rows_queryset(self):
        qs = PaymentRowReplica.objects.filter(
            payment_type__pos__business_id=self.scope.business.id,
            payment_type__code__in=[
                PaymentTypeEnum.PAY_BY_APP,
                PaymentTypeEnum.PREPAYMENT,
                PaymentTypeEnum.PAY_BY_APP_DONATIONS,
                PaymentTypeEnum.STRIPE_TERMINAL,
                PaymentTypeEnum.TAP_TO_PAY,
            ],
            status__in=self.payment_row_statuses,
            created__gte=self.scope.date_from_datetime_utc,
            created__lte=self.scope.date_till_by_span,
        ).filter(
            Q(children__isnull=True)  # last element in PaymentRow chain
            # or not last but children are refunds/chargebacks
            | (
                Q(children__isnull=False)
                & Q(
                    children__status__in=[
                        receipt_status.SENT_FOR_REFUND,
                        receipt_status.REFUNDED,
                        receipt_status.CHARGEBACK,
                    ]
                )
                & ~Q(children__status=F('status'))
            )
        )
        return qs

    def get_queryset(self):
        return (
            self.payment_rows_queryset()
            .filter(
                intents__balance_transactions__payout__id__isnull=False,
            )
            .annotate(
                # For structured_values() one-to-one relation
                receipt__transaction__tip_id=F('receipt__transaction__tip__id'),
                fixed_status=self.prepayments_fixed_status_annotation(),
                stripe_payout_created=Subquery(
                    StripePayoutReplica.objects.filter(
                        balance_transactions__payment_intent__payment_rows__in=OuterRef('id'),
                    )
                    .order_by()
                    .values('payout_created')[:1],
                ),
            )
            .distinct()
            .order_by(
                'created',
            )
        )


class StripeSinglePayoutSection(StripePayoutBatchesSection):
    key = 'stripe_single_payout_section'
    title = _('Stripe Payout details')
    description = _('Payout related transactions')

    def __init__(self, *args, payout_id, **kwargs):
        super().__init__(*args, **kwargs)
        self.payout_id = payout_id

    def get_queryset(self):
        queryset = (
            super()
            .get_queryset()
            .filter(
                intents__balance_transactions__payout__id=self.payout_id,
            )
            .distinct()
        )
        return queryset


class StripePayoutBatchesReport(CashFlowSpreadsheetColumnsWidthMixin, BaseReport):
    key = ReportKeys.StripePayoutBatchesReport

    @classmethod
    def is_available(cls, scope: TimeDataScope) -> bool:
        if not settings.POS__PAY_BY_APP:
            return False
        return scope.business.pos.force_stripe_pba

    @staticmethod
    def get_title() -> str:
        return gettext('Payouts Batches')

    def __init__(self, *args) -> None:
        super().__init__(*args)
        self._add_section(StripePayoutBatchesSection)


class StripeSinglePayoutReport(CashFlowSpreadsheetColumnsWidthMixin, BaseReport):
    @staticmethod
    def get_title() -> str:
        return gettext('Stripe Payout Report')

    # pylint: disable=duplicate-code
    def __init__(
        self,
        language: str,  # report date format depends on user language
        business: Business,
        payout_id: int,
    ) -> None:
        intents = StripePaymentIntentReplica.objects.filter(
            balance_transactions__payout__id=payout_id
        )
        limits = intents.aggregate(Min("created"), Max("created"))

        super().__init__(
            scope=TimeDataScope(
                business,
                language,
                # dates are used only in descriptions (eg. "Period from 10/8/21 to 10/9/21"),
                # actual filtering is based on payout id
                date_from=limits['created__min'].date(),
                date_till=limits['created__max'].date(),
                time_span=TimeScopeType.BETWEEN,
            ),
            default_pagination=None,
        )

        self._add_section(StripeSinglePayoutSection, payout_id=payout_id)
