from datetime import date

import pytest

from webapps.business.baker_recipes import business_recipe, resource_recipe
from webapps.business.models import Resource
from webapps.stats_and_reports.consts import MetadataFilterType
from webapps.stats_and_reports.reports import StaffPerformancePerStaffer
from webapps.stats_and_reports.reports.base import (
    ReportExecutionMetadata,
    ReportFilterOption,
    ReportMetadataFilter,
)
from webapps.stats_and_reports.reports.staff.staff_performance_per_staffer.constants import (
    StafferPerformanceReportTopic,
)
from webapps.stats_and_reports.reports.staff.staff_performance_per_staffer.sections import (
    StafferPerformanceGeneralCompletedAppointments,
    StafferPerformanceGeneralTips,
    StafferPerformanceGeneralRevenue,
    StafferPerformanceAppOverviewSection,
    StafferPerformanceCompletedByCategoryNService,
    StafferPerformanceNoShowByCategoryNService,
    StafferPerformanceAppointmentLog,
    StafferPerformanceStafferTime,
    StafferPerformanceStafferTimeOffs,
    StafferPerformanceStafferPlannedTimeOffs,
    StafferPerformanceSalesNCommisionsByType,
    StafferPerformanceSalesByProducts,
    StafferPerformanceClientsNReviews,
    StafferPerformanceDetailedReviews,
)
from webapps.stats_and_reports.reports.time_data import StafferTimeDataScope, TimeScopeType
from webapps.stats_and_reports.serializers import ReportSerializer

pytestmark = pytest.mark.django_db


def test_report_is_alive():
    business = business_recipe.make(time_zone_name='UTC')
    staffer = resource_recipe.make(active=True, business=business, type=Resource.STAFF)

    scope = StafferTimeDataScope(
        business=business,
        language='en',
        date_from=date(2020, 12, 1),
        date_till=date(2020, 12, 31),
        time_span=TimeScopeType.MONTH,
        staffer_id=staffer.id,
    )
    assert StaffPerformancePerStaffer.is_available(scope)


def test_report_is_new():
    assert not StaffPerformancePerStaffer.is_new


def test_report_sections_contain_category_key():
    business = business_recipe.make(time_zone_name='UTC')
    staffer = resource_recipe.make(active=True, business=business, type=Resource.STAFF)

    sections = [*StafferPerformanceReportTopic]

    scope = StafferTimeDataScope(
        business=business,
        language='en',
        date_from=date(2020, 12, 1),
        date_till=date(2020, 12, 31),
        time_span=TimeScopeType.MONTH,
        staffer_id=staffer.id,
        staffer_performance_sections=sections,
    )

    report = StaffPerformancePerStaffer(scope)
    serializer = ReportSerializer(instance=report)
    reports_keys_n_categories = [(s['key'], s['category_key']) for s in serializer.data['sections']]
    assert reports_keys_n_categories == [
        (StafferPerformanceGeneralCompletedAppointments.key, StafferPerformanceReportTopic.GENERAL),
        (StafferPerformanceGeneralRevenue.key, StafferPerformanceReportTopic.GENERAL),
        (StafferPerformanceGeneralTips.key, StafferPerformanceReportTopic.GENERAL),
        (
            StafferPerformanceAppOverviewSection.key,
            StafferPerformanceReportTopic.APPOINTMENTS_OVERVIEW,
        ),
        (
            StafferPerformanceCompletedByCategoryNService.key,
            StafferPerformanceReportTopic.APPOINTMENTS_OVERVIEW,
        ),
        (
            StafferPerformanceNoShowByCategoryNService.key,
            StafferPerformanceReportTopic.APPOINTMENTS_OVERVIEW,
        ),
        (StafferPerformanceAppointmentLog.key, StafferPerformanceReportTopic.APPOINTMENTS_LIST),
        (StafferPerformanceStafferTime.key, StafferPerformanceReportTopic.WORKING_HOURS),
        (StafferPerformanceStafferTimeOffs.key, StafferPerformanceReportTopic.WORKING_HOURS),
        (StafferPerformanceStafferPlannedTimeOffs.key, StafferPerformanceReportTopic.WORKING_HOURS),
        (
            StafferPerformanceSalesNCommisionsByType.key,
            StafferPerformanceReportTopic.SALES_N_COMMISSIONS,
        ),
        (StafferPerformanceSalesByProducts.key, StafferPerformanceReportTopic.SALES_N_COMMISSIONS),
        (StafferPerformanceClientsNReviews.key, StafferPerformanceReportTopic.CLIENTS_N_REVIEWS),
        (
            StafferPerformanceDetailedReviews.key,
            StafferPerformanceReportTopic.CLIENTS_N_REVIEWS,
        ),
    ]


def test_report_metadata():
    business = business_recipe.make(time_zone_name='UTC')
    metadata = ReportExecutionMetadata.for_report_n_business(
        StaffPerformancePerStaffer, business.id
    )
    assert metadata == ReportExecutionMetadata(
        title='Staff performance details',
        is_date_filtered=True,
        subtitle='Detailed performance metrics per staff member.',
        filters=[
            ReportMetadataFilter(
                type=MetadataFilterType.SINGLE,
                placeholder='Staff member',
                filter='staffer_id',
                options=[],
                default=None,
                message='Select a staff member to see the report',
                required=True,
            ),
            ReportMetadataFilter(
                type=MetadataFilterType.MULTI,
                placeholder='Section',
                filter='staffer_performance_section',
                options=[
                    ReportFilterOption(
                        label='General', value=StafferPerformanceReportTopic.GENERAL
                    ),
                    ReportFilterOption(
                        label='Appointments overview',
                        value=StafferPerformanceReportTopic.APPOINTMENTS_OVERVIEW,
                    ),
                    ReportFilterOption(
                        label='Appointments list',
                        value=StafferPerformanceReportTopic.APPOINTMENTS_LIST,
                    ),
                    ReportFilterOption(
                        label='Working hours', value=StafferPerformanceReportTopic.WORKING_HOURS
                    ),
                    ReportFilterOption(
                        label='Sales & commissions',
                        value=StafferPerformanceReportTopic.SALES_N_COMMISSIONS,
                    ),
                    ReportFilterOption(
                        label='Clients & reviews',
                        value=StafferPerformanceReportTopic.CLIENTS_N_REVIEWS,
                    ),
                ],
                default=[StafferPerformanceReportTopic.GENERAL],
                message=None,
                required=False,
            ),
        ],
    )


def test_report_metadata_multiple_staffers_filter_n_sort():
    business = business_recipe.make(time_zone_name='UTC')
    active_staffer1 = resource_recipe.make(
        active=True, business=business, type=Resource.STAFF, name='Ccccccc'
    )
    _ = resource_recipe.make(active=False, business=business, type=Resource.STAFF, name='Bbbbbbb')
    active_staffer2 = resource_recipe.make(
        active=True, business=business, type=Resource.STAFF, name='Aaaaaaaa'
    )
    _ = resource_recipe.make(
        active=True,
        business=business_recipe.make(time_zone_name='UTC'),
        type=Resource.STAFF,
        name='Ddddddd',
    )
    _ = resource_recipe.make(
        active=True, business=business, type=Resource.APPLIANCE, name='Eeeeeee'
    )

    metadata = ReportExecutionMetadata.for_report_n_business(
        StaffPerformancePerStaffer, business.id
    )

    staff_filter = metadata.filters[0]
    assert staff_filter.default is None
    assert staff_filter.options == [
        ReportFilterOption(label='Aaaaaaaa', value=active_staffer2.id),
        ReportFilterOption(label='Ccccccc', value=active_staffer1.id),
    ]
