from collections import defaultdict
from datetime import date
from decimal import Decimal
from itertools import chain
from typing import Iterable

import pandas as pd
from dateutil.relativedelta import relativedelta
from pandas import isna
from django.contrib.postgres.aggregates import ArrayAgg
from django.db.models import (
    F,
    Value,
    When,
    Case,
    DecimalField,
    QuerySet,
    DateField,
    CharField,
    ExpressionWrapper,
    Q,
    BooleanField,
    OuterRef,
    Subquery,
    IntegerField,
    Sum,
)
from django.db.models.fields.json import KeyTextTransform
from django.utils.encoding import force_str
from django.db.models.functions import (
    Lower,
    Upper,
    Cast,
    Coalesce,
    Concat,
    Left,
    Length,
)
from django.utils.translation import gettext_lazy as _, gettext
from django.contrib.postgres.fields.ranges import DateRange

from lib.tools import tznow
from webapps.booking.enums import AppointmentStatus
from webapps.booking.models import Appointment
from webapps.pos.enums import receipt_status
from webapps.pos.models import TransactionRow
from webapps.stats_and_reports.models import (
    SubBookingReplica,
    TransactionReplica,
    TransactionRowReplica,
    BusinessCustomerInfoReplica,
    PaymentTypeReplica,
    ResourceTimeOffReplica,
    BookingResourceReplica,
    ResourceReplica,
)
from webapps.stats_and_reports.reports.appointment_reports.appointments_list import (
    AppointmentsListBaseSection,
)
from webapps.business.models import Resource
from webapps.schedule.enums import STAFF_TIME_OFF_REASONS_DICT

from webapps.stats_and_reports.reports import TimeOccupancySection
from webapps.stats_and_reports.reports.base import (
    ReportSection,
    ReportTable,
)
from webapps.stats_and_reports.reports.common_stats import BaseSalesByProductSection
from webapps.stats_and_reports.reports.staff.schedules_base import SchedulesBaseSection
from webapps.stats_and_reports.reports.staff.staff_performance_per_staffer.constants import (
    ALL_DAY,
    RECEIPT_REQUIRED_STATUSES,
    NO_CATEGORY,
    StafferPerformanceReportTopic,
    REVIEW_TEXT_UPPER_BOUND,
)
from webapps.stats_and_reports.reports.staff.staff_performance_per_staffer.tables import (
    AppointmentsOverviewTable,
    NoShowChargesByCategoryNService,
    CompletedAppointmentsByCategoryNService,
    AppointmentsLogTable,
    StafferTimeTable,
    StafferTimeOffTable,
    StafferPlannedTimeOffTable,
    StafferSalesNCommisionsByTypeTable,
    StafferSalesByProductsTable,
    StafferPerformanceClientsNReviewsTable,
    StafferPerformanceDetailedReviewsTable,
    StafferPerformanceOverview,
)
from webapps.voucher.enums import VoucherType
from webapps.stats_and_reports.reports.utils import (
    format_date_for_xlsx,
    Round2,
    percentage,
    booking_status_label,
)

# pylint: disable=use-dict-literal


class ScopedSubBookingSectionMixin:
    def _base_booking_queryset(self, appointment_statuses: Iterable[AppointmentStatus]):
        qs = SubBookingReplica.objects.annotate_is_combo().filter(
            appointment__business_id=self.scope.business.id,
            booked_from__gte=self.scope.date_from_datetime_utc,
            booked_till__lte=self.scope.date_till_by_span,
            appointment__type__in=Appointment.TYPES_BOOKABLE,
            appointment__status__in=appointment_statuses,
            resources__in=[
                self.scope.staffer_id,
            ],
            is_combo=False,
        )
        return qs


class StafferPerformanceGeneralCompletedAppointments(ScopedSubBookingSectionMixin, ReportSection):
    key = 'staffer_performance_general_completed_appos'
    title = _('General')
    description = _('Top stats for the staff member over the selected time period.')
    category_key = StafferPerformanceReportTopic.GENERAL

    def get_queryset(self) -> QuerySet:
        qs = (
            self._base_booking_queryset(appointment_statuses=[AppointmentStatus.FINISHED])
            .annotate(
                booking_id=F('id'),
                appo_id=F('appointment_id'),
                review_rank=Case(
                    When(
                        review__deleted__isnull=False,
                        then=None,
                    ),
                    default=F('review__rank'),
                    output_field=IntegerField(),
                ),
                total_paid=Subquery(
                    TransactionRowReplica.objects.filter(
                        subbooking=OuterRef('id'),
                        transaction__children__isnull=True,
                        type__in=[
                            TransactionRowReplica.TRANSACTION_ROW_TYPE__SERVICE,
                            TransactionRowReplica.TRANSACTION_ROW_TYPE__ADDON,
                        ],
                        transaction__latest_receipt__status_code__in=RECEIPT_REQUIRED_STATUSES,
                    )
                    .annotate(revenue=Sum(F('net_total') + F('tax_amount')))
                    .values('revenue')[:1]
                ),
            )
            .values(
                'appo_id',
                'booking_id',
                'review_rank',
                'total_paid',
            )
        )

        return qs

    def coerce_data_to_table(self, results: QuerySet) -> ReportTable:
        appos_df = pd.DataFrame(
            results, columns=['appo_id', 'booking_id', 'review_rank', 'total_paid']
        )

        total_appos = appos_df.appo_id.unique().size
        total_services = appos_df.booking_id.unique().size

        average_charged = appos_df.total_paid.sum() / total_appos if total_appos else 0

        n_ranks = appos_df.loc[appos_df.review_rank.notna()].review_rank.size
        average_rank = appos_df.review_rank.sum() / n_ranks if n_ranks else 0

        return StafferPerformanceOverview(
            rows=[
                StafferPerformanceOverview.CoutnerRow(
                    field=gettext('Completed appointments'), value=total_appos
                ).add_tooltip(
                    dict(
                        field=gettext(
                            'Total number of appointments completed over the selected time period. '
                            'Appointments which have not been checked out are not included.'
                        )
                    ),
                ),
                StafferPerformanceOverview.CoutnerRow(
                    field=gettext('Performed services'), value=total_services
                ).add_tooltip(
                    dict(
                        field=gettext(
                            'Total number of services performed '
                            'over the selected time period, excluding add-ons.'
                        )
                    ),
                ),
                StafferPerformanceOverview.CurrencyRow(
                    field=gettext('Average appointment price'), value=average_charged
                ).add_tooltip(
                    dict(
                        field=gettext(
                            'Average appointment price calculated based '
                            'on the cost of services and add-ons.'
                        ),
                    ),
                ),
                StafferPerformanceOverview.Row(
                    field=gettext('Average review rating'), value=f'{average_rank:.1f}'
                ),
            ],
        )


class CombinedSaleMixin:
    def get_base_queryset(self):
        bookings = SubBookingReplica.objects.filter(
            resources__in=[self.scope.staffer_id],
            appointment__booked_from__gte=self.scope.date_from_datetime_utc,
            appointment__booked_till__lte=self.scope.date_till_by_span,
            appointment__type__in=Appointment.TYPES_BOOKABLE,
            appointment__status__in=[
                AppointmentStatus.FINISHED,
                AppointmentStatus.NOSHOW,
                AppointmentStatus.CANCELED,
            ],
        )

        bookings = list(bookings.values_list('id', flat=True))

        sales_via_service = TransactionRowReplica.objects.filter(
            subbooking__in=bookings,
            transaction__pos__business_id=self.scope.business.id,
            transaction__latest_receipt__status_code__in=RECEIPT_REQUIRED_STATUSES,
            transaction__children__isnull=True,
        )

        sales_via_commission = TransactionRowReplica.objects.filter(
            ~Q(subbooking__in=bookings),
            commission__resource_id=self.scope.staffer_id,
            transaction__latest_receipt__created__gte=self.scope.date_from_datetime_utc,
            transaction__latest_receipt__created__lte=self.scope.date_till_by_span,
            transaction__pos__business_id=self.scope.business.id,
            transaction__latest_receipt__status_code__in=RECEIPT_REQUIRED_STATUSES,
            transaction__children__isnull=True,
        )

        return sales_via_service | sales_via_commission


class StafferPerformanceGeneralRevenue(CombinedSaleMixin, ReportSection):
    key = 'staffer_performance_general_revenue'
    title = _('Revenue')
    category_key = StafferPerformanceReportTopic.GENERAL

    NOSHOW = 'No-Show'
    GIFTCARD = TransactionRowReplica.TRANSACTION_ROW_TYPE__VOUCHER + VoucherType.EGIFT_CARD
    PACKAGE = TransactionRowReplica.TRANSACTION_ROW_TYPE__VOUCHER + VoucherType.PACKAGE
    MEMBERSHIP = TransactionRowReplica.TRANSACTION_ROW_TYPE__VOUCHER + VoucherType.MEMBERSHIP

    def get_queryset(self) -> QuerySet:
        qs = (
            self.get_base_queryset()
            .annotate_tip_for_staffer(self.scope.staffer_id)
            .annotate(
                _staffer_id=Subquery(
                    BookingResourceReplica.objects.filter(
                        subbooking_id=OuterRef('subbooking'),
                        resource__type=ResourceReplica.STAFF,
                    ).values('resource_id')[:1]
                ),
                sold_by_staffer=ExpressionWrapper(
                    Q(_staffer_id=Value(self.scope.staffer_id)),
                    output_field=BooleanField(),
                ),
                commission_assigned=ExpressionWrapper(
                    Q(commission__isnull=False)
                    & Q(commission__resource_id=Value(self.scope.staffer_id)),
                    output_field=BooleanField(),
                ),
                type_transaction=Case(
                    When(
                        transaction__appointment__status__in=[
                            Value(AppointmentStatus.NOSHOW),
                            Value(AppointmentStatus.CANCELED),
                        ],
                        then=Value(self.NOSHOW),
                    ),
                    When(
                        voucher__voucher_template__type__isnull=False,
                        then=Concat(
                            Value(TransactionRowReplica.TRANSACTION_ROW_TYPE__VOUCHER),
                            F('voucher__voucher_template__type'),
                        ),
                    ),
                    When(type__isnull=False, then=F('type')),
                    default=Value('Not typed'),
                    output_field=CharField(),
                ),
                commission_amount=Case(
                    When(commission_assigned=True, then=F('commission__amount')),
                    When(commission_assigned__isnull=True, then=F('commission__amount')),
                    default=Decimal('0'),
                ),
                revenue=Case(
                    When(sold_by_staffer=True, then=F('net_total') + F('tax_amount') + F('tip')),
                    When(
                        sold_by_staffer__isnull=True,
                        then=F('net_total') + F('tax_amount') + F('tip'),
                    ),
                    default=Decimal('0'),
                ),
            )
            .values(
                'type_transaction',
            )
            .annotate(
                total_revenue=Sum(Coalesce(F('revenue'), Decimal(0))),
                total_commission=Sum(Coalesce(F('commission_amount'), Decimal(0))),
            )
        )

        return qs

    def coerce_data_to_table(self, results: QuerySet) -> ReportTable:
        payment_df = pd.DataFrame(
            results, columns=['type_transaction', 'total_revenue', 'total_commission']
        )
        services_and_addons = payment_df.loc[
            payment_df.type_transaction.isin(
                [
                    TransactionRowReplica.TRANSACTION_ROW_TYPE__SERVICE,
                    TransactionRowReplica.TRANSACTION_ROW_TYPE__ADDON,
                ]
            )
        ].total_revenue.sum()
        noshows = payment_df.loc[payment_df.type_transaction == self.NOSHOW].total_revenue.sum()
        products = payment_df.loc[
            payment_df.type_transaction == TransactionRowReplica.TRANSACTION_ROW_TYPE__PRODUCT
        ].total_revenue.sum()
        giftcards = payment_df.loc[payment_df.type_transaction == self.GIFTCARD].total_revenue.sum()
        packages = payment_df.loc[payment_df.type_transaction == self.PACKAGE].total_revenue.sum()
        memberships = payment_df.loc[
            payment_df.type_transaction == self.MEMBERSHIP
        ].total_revenue.sum()
        total_revenue = payment_df.total_revenue.sum()
        commissions = payment_df.total_commission.sum()

        return StafferPerformanceOverview(
            rows=[
                StafferPerformanceOverview.CurrencyRow(
                    field=gettext('Total revenue'), value=total_revenue
                ).add_tooltip(
                    dict(
                        field=gettext(
                            "Total combined sales including: service cost, tips, "
                            "add-ons, products sold, memberships, gift cards, packages, "
                            "travelling fees, deposits and cancellation fees."
                        ),
                    ),
                ),
                StafferPerformanceOverview.CurrencyRow(
                    field=gettext('Services & add-ons'), value=services_and_addons
                ).add_tooltip(
                    dict(
                        field=gettext(
                            "Total combined sales from services and add-ons performed "
                            "over the selected time period with tips"
                        ),
                    ),
                ),
                StafferPerformanceOverview.CurrencyRow(
                    field=gettext('No-Show fees'), value=noshows
                ).add_tooltip(
                    dict(
                        field=gettext(
                            "Total value of deposits and cancellation fees "
                            "charged over the selected time period."
                        ),
                    ),
                ),
                StafferPerformanceOverview.CurrencyRow(field=gettext('Products'), value=products),
                StafferPerformanceOverview.CurrencyRow(
                    field=gettext('Gift cards'), value=giftcards
                ),
                StafferPerformanceOverview.CurrencyRow(field=gettext('Packages'), value=packages),
                StafferPerformanceOverview.CurrencyRow(
                    field=gettext('Memberships'), value=memberships
                ),
                StafferPerformanceOverview.CurrencyRow(
                    field=gettext('Commission'), value=commissions
                ).add_tooltip(
                    dict(
                        field=gettext(
                            "Total value of staff member commissions over "
                            "the selected time period including commissions "
                            "on services, add-ons, gift cards, packages, memberships and products."
                        ),
                    ),
                ),
            ],
        )


class StafferPerformanceGeneralTips(CombinedSaleMixin, ReportSection):
    key = 'staffer_performance_general_tips'
    title = _('Tips')
    category_key = StafferPerformanceReportTopic.GENERAL

    def get_queryset(self) -> QuerySet:
        qs = (
            self.get_base_queryset()
            .annotate_tip_for_staffer(self.scope.staffer_id)
            .annotate(
                _staffer_id=Subquery(
                    BookingResourceReplica.objects.filter(
                        subbooking_id=OuterRef('subbooking'),
                        resource__type=ResourceReplica.STAFF,
                    ).values('resource_id')[:1]
                ),
                sold_by_staffer=ExpressionWrapper(
                    Q(_staffer_id=Value(self.scope.staffer_id)),
                    output_field=BooleanField(),
                ),
                commission_assigned=ExpressionWrapper(
                    Q(commission__isnull=False)
                    & Q(commission__resource_id=Value(self.scope.staffer_id)),
                    output_field=BooleanField(),
                ),
                rev=Case(
                    When(sold_by_staffer=True, then=F('total')),
                    When(sold_by_staffer__isnull=True, then=F('total')),
                    default=Decimal('0'),
                ),
                tip_=Case(
                    When(sold_by_staffer=True, then=F('tip')),
                    When(sold_by_staffer__isnull=True, then=F('tip')),
                    default=Decimal('0'),
                ),
            )
            .values('transaction_id')
            .annotate(
                tips=Sum(F('tip_')),
                revenue=Sum(Coalesce(F('rev'), Decimal(0))),
            )
        )

        return qs

    def coerce_data_to_table(self, results: QuerySet) -> ReportTable:
        payment_df = pd.DataFrame(results, columns=['transaction_id', 'revenue', 'tips'])

        revenue = payment_df.revenue.sum()
        tips = payment_df.tips.sum()
        n_transactions = payment_df.transaction_id.value_counts().sum()
        average_tips = tips / n_transactions if n_transactions else 0
        percent_average_tips = tips / revenue * 100 if revenue else 0

        return StafferPerformanceOverview(
            rows=[
                StafferPerformanceOverview.CurrencyRow(field=gettext('Total tips'), value=tips),
                StafferPerformanceOverview.CurrencyRow(
                    field=gettext('Average tip $$'), value=average_tips
                ),
                StafferPerformanceOverview.PercentageRow(
                    field=gettext('Average tip %'),  # xgettext:no-python-format
                    value=percent_average_tips,
                ),
            ],
        )


class StafferPerformanceAppOverviewSection(ScopedSubBookingSectionMixin, ReportSection):
    key = 'staffer_performance_app_overview'
    title = _('Appointment status')
    description = _('Summary of appointments by status (checked out, canceled, no-show).')
    category_key = StafferPerformanceReportTopic.APPOINTMENTS_OVERVIEW

    NO_SHOW = _('No-Show appointments')
    CANCELLED = _('Cancelled appointments')
    TB_CHARGED = _('Pending appointments')
    CHARGED = _('Completed appointments')

    _appoitment_statuses_map = {
        AppointmentStatus.NOSHOW: NO_SHOW,
        AppointmentStatus.CANCELED: CANCELLED,
        (AppointmentStatus.FINISHED, True): TB_CHARGED,
        (AppointmentStatus.FINISHED, False): CHARGED,
    }

    def get_base_qs(
        self,
        statuses=(
            AppointmentStatus.FINISHED,
            AppointmentStatus.CANCELED,
            AppointmentStatus.NOSHOW,
        ),
    ):
        return self._base_booking_queryset(appointment_statuses=statuses)

    def get_queryset(self) -> QuerySet:
        qs = (
            self.get_base_qs()
            .values('appointment_id')
            .annotate(
                appointment_duration=Sum(F('booked_till') - F('booked_from')),
                appointment_value=Sum(
                    SubBookingReplica.objects.filter(
                        id=OuterRef('id'),
                        service_variant__service__combo_type__isnull=True,
                        resources__in=[self.scope.staffer_id],
                    )
                    .values('appointment_id')
                    .annotate(
                        bookings_values=Sum(
                            Cast(
                                KeyTextTransform('service_variant_price', 'service_data_internal'),
                                DecimalField(max_digits=10, decimal_places=2),
                            )
                        ),
                    )
                    .values('bookings_values')[:1],
                    default=Decimal(0),
                    output_field=DecimalField(max_digits=10, decimal_places=2),
                ),
            )
            .values_list(
                'appointment_id', 'appointment__status', 'appointment_duration', 'appointment_value'
            )
        )

        return qs

    def get_transaction_info(self, appointment_ids: Iterable[int] = ()):
        required_statuses = receipt_status.SUCCESS_STATUSES_WITH_PREPAYMENT
        qs = (
            TransactionRowReplica.objects.filter(
                subbooking__resources__in=[self.scope.staffer_id],
                transaction__appointment_id__in=appointment_ids,
                transaction__children__isnull=True,
                transaction__latest_receipt__status_code__in=required_statuses,
            )
            .annotate_tip_for_staffer(self.scope.staffer_id)
            .annotate(
                appo_id=F('transaction__appointment_id'),
                commission_amount=Case(
                    When(
                        commission__resource=Value(self.scope.staffer_id),
                        then=F('commission__amount'),
                    ),
                    default=Decimal('0'),
                ),
            )
            .values('appo_id')
            .annotate(
                tips=Round2(Sum('tip')),
                revenue_net=Sum(Coalesce(F('net_total'), Decimal(0))),
                discount=Sum(Coalesce(F('real_discount_amount'), Decimal(0))),
                tax=Sum(Coalesce(F('tax_amount'), Decimal(0))),
                total_revenue=F('revenue_net') + F('tips') + F('tax'),
                total_revenue_no_tips=F('revenue_net') + F('tax'),
                commission=Sum(Coalesce(F('commission_amount'), Decimal(0))),
            )
        )
        return qs

    def default_dataframe(self, total_row=False):
        status_order = [
            gettext(self.CHARGED),
            gettext(self.TB_CHARGED),
            gettext(self.CANCELLED),
            gettext(self.NO_SHOW),
        ]
        if total_row:
            status_order.append(gettext('Total'))

        index = pd.Index(data=status_order, name='appo_status')
        return pd.DataFrame({}, index=index)

    @staticmethod
    def get_report_table(rows: pd.DataFrame):
        charged, tb_charged, cancelled, no_show, total = rows.reset_index().to_dict('records')

        data = AppointmentsOverviewTable(
            rows=[
                AppointmentsOverviewTable.Row(**charged).add_tooltip(
                    dict(
                        appo_status=gettext(
                            'Total number of appointments which have been checked out.'
                        ),
                    ),
                ),
                AppointmentsOverviewTable.Row(**tb_charged).add_tooltip(
                    dict(
                        appo_status=gettext(
                            'Total number completed appointments '
                            'which have not been checked out.'
                        ),
                    ),
                ),
                AppointmentsOverviewTable.Row(**cancelled).add_tooltip(
                    dict(
                        appo_status=gettext(
                            'Total number of appointments which have '
                            'been canceled either by the business or a client.'
                        ),
                    ),
                ),
                AppointmentsOverviewTable.Row(**no_show).add_tooltip(
                    dict(
                        appo_status=gettext('Total number of no-show appointments.'),
                    ),
                ),
                AppointmentsOverviewTable.Row(**total),
            ]
        )
        return data

    def coerce_data_to_table(self, results: QuerySet) -> ReportTable:
        appos_df = pd.DataFrame(
            results, columns=['appo_id', 'appo_status', 'appo_duration', 'appo_value']
        ).set_index('appo_id')

        appointment_ids = [*appos_df.index]
        transactions_info_raw = self.get_transaction_info(appointment_ids)

        txns_df = pd.DataFrame(
            transactions_info_raw,
            columns=[
                'appo_id',
                'tips',
                'revenue_net',
                'discount',
                'tax',
                'total_revenue',
                'total_revenue_no_tips',
                'commission',
            ],
        ).set_index('appo_id')

        appos_n_txns = appos_df.join(txns_df)

        if appos_n_txns.empty:
            default_rows = self.default_dataframe(total_row=True)
            rows = default_rows.join(appos_n_txns).fillna(0)
            return self.get_report_table(rows.drop('appo_status', axis=1))

        def map_statuses(ser):
            new_status = self._appoitment_statuses_map.get(
                ser.appo_status
            ) or self._appoitment_statuses_map.get((ser.appo_status, isna(ser.revenue_net)))
            ser.appo_status = force_str(new_status)
            return ser

        appos_n_txns = appos_n_txns.apply(map_statuses, axis=1)
        appos_n_txns['quantity'] = 1

        summed = appos_n_txns.groupby('appo_status').sum(numeric_only=False)
        summed = self.default_dataframe().join(summed)
        summed['quantity_rel'] = (summed['quantity'] / summed['quantity'].sum()) * 100
        summed.quantity_rel = summed.quantity_rel.round(1)

        summed.appo_duration.fillna(relativedelta(), inplace=True)
        summed.fillna(0, inplace=True)
        summed.quantity = summed.quantity.astype(int)

        summed.loc[gettext('Total')] = summed.sum(numeric_only=False)
        summed.quantity_rel.iloc[-1] = 100.0

        return self.get_report_table(summed)


class StafferPerformanceBookingsPivotTableMixin:
    @staticmethod
    def turn_bookings_to_pivot_table(bookings_df):
        groups = ['booking_service_category', 'booking_service_name']
        bookings_df.booking_service_category.fillna(NO_CATEGORY, inplace=True)

        categories = bookings_df.groupby(
            'booking_service_category', as_index=False, dropna=False
        ).sum(numeric_only=False)
        categories['booking_service_name'] = ''

        services_aggregate = {
            col: 'first' if col in groups else 'sum' for col in bookings_df.columns
        }
        services = bookings_df.groupby('booking_service_name', as_index=False, dropna=False).agg(
            services_aggregate
        )

        summed = pd.concat([categories, services]).set_index(groups).sort_index()
        summed.loc[gettext('Total'), :] = (
            bookings_df.loc[:, ~bookings_df.columns.isin(groups)].sum().values
        )

        summed.number_of_services = summed.number_of_services.astype(int)
        summed.index = pd.MultiIndex.from_frame(summed.index.to_frame().fillna('-'))

        summed = summed.reset_index().fillna(0)
        summed.loc[summed['booking_service_name'] != '', 'booking_service_category'] = ''
        return summed


class StafferPerformanceCompletedByCategoryNService(
    ScopedSubBookingSectionMixin, StafferPerformanceBookingsPivotTableMixin, ReportSection
):
    key = 'staffer_performance_completed_by_category_n_service'
    title = _('Performed services')
    description = _(
        'Summary of completed appointments '
        '(checkout status not relevant) '
        'by category and service type.'
    )
    category_key = StafferPerformanceReportTopic.APPOINTMENTS_OVERVIEW

    def get_queryset(self) -> QuerySet:
        qs = (
            self._base_booking_queryset(appointment_statuses=[AppointmentStatus.FINISHED])
            .annotate_service_variant_price()
            .annotate(
                booking_duration=Sum(F('booked_till') - F('booked_from')),
                booking_id=F('id'),
                booking_service_category=F('service_variant__service__service_category__name'),
                booking_service_name=Coalesce(
                    F('service_name'),
                    F('service_variant__service__name'),
                ),
                booking_value=F('service_variant_price'),
            )
            .values(
                'booking_id',
                'booking_duration',
                'booking_value',
                'booking_service_name',
                'booking_service_category',
            )
        )
        return qs

    def get_transaction_info(self, subbooking_ids):
        qs = (
            AppointmentsListBaseSection.revenue_stats_base_query(subbooking_ids)
            .annotate_tip_for_staffer(self.scope.staffer_id)
            .annotate(
                booking_id=F('subbooking_id'),
                appo_id=F('transaction__appointment_id'),
                commission_amount=Case(
                    When(
                        commission__resource=Value(self.scope.staffer_id),
                        then=F('commission__amount'),
                    ),
                    default=Decimal('0'),
                ),
            )
            .values('booking_id')
            .annotate(
                tips=Round2(Sum('tip')),
                revenue_net=Sum(Coalesce(F('net_total'), Decimal(0))),
                discount=Sum(Coalesce(F('real_discount_amount'), Decimal(0))),
                tax=Sum(Coalesce(F('tax_amount'), Decimal(0))),
                total_revenue=F('revenue_net') + F('tips') + F('tax'),
                total_revenue_no_tips=F('revenue_net') + F('tax'),
                commission=Sum(Coalesce(F('commission_amount'), Decimal(0))),
            )
        )

        return qs

    def coerce_data_to_table(self, results: QuerySet) -> ReportTable:
        booking_df = pd.DataFrame(
            results,
            columns=[
                'booking_id',
                'booking_duration',
                'booking_value',
                'booking_service_name',
                'booking_service_category',
            ],
        ).set_index('booking_id')

        booking_ids = [*booking_df.index]
        transactions_info_raw = self.get_transaction_info(booking_ids)
        txns_df = pd.DataFrame(
            transactions_info_raw,
            columns=[
                'booking_id',
                'tips',
                'revenue_net',
                'discount',
                'tax',
                'total_revenue',
                'total_revenue_no_tips',
                'commission',
            ],
        ).set_index('booking_id')

        bookings_n_txns = booking_df.join(txns_df)

        if bookings_n_txns.empty:
            summed = pd.concat(
                [bookings_n_txns, pd.DataFrame([pd.Series(0, index=bookings_n_txns.columns)])],
                ignore_index=True,
            )
            summed.booking_service_category = gettext('Total')
            summed.booking_service_name = ''
            summed['number_of_services'] = 0
        else:
            bookings_n_txns['number_of_services'] = 1
            summed = self.turn_bookings_to_pivot_table(bookings_n_txns)

        return CompletedAppointmentsByCategoryNService(
            rows=[
                CompletedAppointmentsByCategoryNService.Row(**r) for r in summed.to_dict('records')
            ],
        )


class StafferPerformanceNoShowByCategoryNService(
    ScopedSubBookingSectionMixin, StafferPerformanceBookingsPivotTableMixin, ReportSection
):
    key = 'staffer_performance_no_show_by_category_n_service'
    title = _('No-Show Protection charges')
    description = _(
        'Summary of no-show protection charges. '
        'Note: no-show protection fees are charged only '
        'if no-show protection is enabled. To start using it, '
        'go to Business settings > no-show protection and set '
        'up deposits and cancellation fees.'
    )
    category_key = StafferPerformanceReportTopic.APPOINTMENTS_OVERVIEW

    def get_queryset(self) -> QuerySet:
        qs = (
            self._base_booking_queryset(appointment_statuses=[AppointmentStatus.NOSHOW])
            .annotate(
                booking_id=F('id'),
                booking_service_category=F('service_variant__service__service_category__name'),
                booking_service_name=Coalesce(
                    F('service_name'),
                    F('service_variant__service__name'),
                ),
            )
            .values('booking_id', 'booking_service_name', 'booking_service_category')
        )
        return qs

    def get_transaction_info(self, subbooking_ids):
        qs = (
            AppointmentsListBaseSection.revenue_stats_base_query(subbooking_ids)
            .annotate(booking_id=F('subbooking_id'))
            .values('booking_id')
            .annotate(
                no_show_fee=Sum(Coalesce(F('net_total') + F('tax_amount'), Decimal(0))),
            )
        )

        return qs

    def coerce_data_to_table(self, results: QuerySet) -> ReportTable:
        booking_df = pd.DataFrame(
            results,
            columns=[
                'booking_id',
                'booking_service_name',
                'booking_service_category',
            ],
        ).set_index('booking_id')
        booking_ids = [*booking_df.index]
        transactions_info_raw = self.get_transaction_info(booking_ids)
        txns_df = pd.DataFrame(
            transactions_info_raw,
            columns=[
                'booking_id',
                'no_show_fee',
            ],
        ).set_index('booking_id')
        bookings_n_txns = booking_df.join(txns_df, how='right')

        if bookings_n_txns.empty:
            summed = pd.concat(
                [bookings_n_txns, pd.DataFrame([pd.Series(0, index=bookings_n_txns.columns)])],
                ignore_index=True,
            )
            summed.booking_service_category = gettext('Total')
            summed.booking_service_name = ''
            summed['number_of_services'] = 0
        else:
            bookings_n_txns['number_of_services'] = 1
            summed = self.turn_bookings_to_pivot_table(bookings_n_txns)

        return NoShowChargesByCategoryNService(
            rows=[NoShowChargesByCategoryNService.Row(**r) for r in summed.to_dict('records')],
        )


class StafferPerformanceAppointmentLog(ScopedSubBookingSectionMixin, ReportSection):
    key = 'staffer_performance_appointments_log'
    title = _('Appointments')
    description = _('Breakdown of appointments and sales completed by the staff member.')
    category_key = StafferPerformanceReportTopic.APPOINTMENTS_LIST

    def get_queryset(self) -> QuerySet:
        appointment_statuses = [
            Appointment.STATUS.ACCEPTED,
            Appointment.STATUS.CANCELED,
            Appointment.STATUS.FINISHED,
            Appointment.STATUS.NOSHOW,
        ]

        qs = (
            self._base_booking_queryset(appointment_statuses=appointment_statuses)
            .annotate_customer_name_merged(no_customer_name='')
            .annotate_service_variant_price()
            .annotate(
                status=F('appointment__status'),
            )
            .annotate(
                client_type=Case(
                    When(
                        appointment__booked_for__first_appointment__booked_from__range=(
                            self.scope.date_from_datetime_utc,
                            self.scope.date_till_by_span,
                        ),
                        then=Value(gettext('New')),
                    ),
                    When(appointment__booked_for__isnull=True, then=Value(gettext('Walk-in'))),
                    default=Value(gettext('Returning')),
                    output_field=CharField(),
                ),
            )
            .select_related(
                'service_variant',
                'service_variant__service',
                'service_variant__service__service_category',
            )
            .order_by('booked_from')
        )

        return qs

    def get_transaction_info_for_subbookings(self, subbooking_ids):
        qs = (
            AppointmentsListBaseSection.revenue_stats_base_query(subbooking_ids)
            .annotate(
                payment_type_ids=Subquery(
                    TransactionReplica.objects.filter(id=OuterRef('transaction_id'))
                    .annotate(
                        payment_type_ids=ArrayAgg('latest_receipt__payment_rows__payment_type_id')
                    )
                    .values('payment_type_ids')[:1]
                ),
                commission_amount=Case(
                    When(
                        commission__resource=Value(self.scope.staffer_id),
                        then=F('commission__amount'),
                    ),
                    default=Decimal('0'),
                ),
            )
            .annotate_tip_for_staffer(self.scope.staffer_id)
            .values('subbooking_id')
            .annotate(
                payment_type_ids=F('payment_type_ids'),
                revenue_net=Sum(Coalesce(F('net_total'), Decimal(0))),
                tax=Sum(Coalesce(F('tax_amount'), Decimal(0))),
                discount=Sum(Coalesce(F('real_discount_amount'), Decimal(0))),
                revenue_net_n_tax=F('revenue_net') + F('tax'),
                tip_amount=Round2(Sum(F('tip'))),
                commission=Sum(Coalesce(F('commission_amount'), Decimal(0))),
            )
        )
        return qs

    def get_traveling_for_subbookings(self, subbooking_ids):
        status_codes = receipt_status.SUCCESS_STATUSES_WITH_PREPAYMENT
        unique_rows = (
            TransactionRowReplica.objects.filter(
                transaction__appointment__bookings__in=subbooking_ids,
                transaction__children__isnull=True,
                transaction__latest_receipt__status_code__in=status_codes,
                type=TransactionRowReplica.TRANSACTION_ROW_TYPE__TRAVEL_FEE,
                commission__resource=self.scope.staffer_id,
            )
            .distinct()
            .values_list('id', flat=True)
        )

        qs = (
            TransactionRowReplica.objects.filter(id__in=unique_rows)
            .values('transaction__appointment_id')
            .annotate(
                traveling_net=Sum(Coalesce(F('net_total'), Decimal(0))),
                traveling_tax=Sum(Coalesce(F('tax_amount'), Decimal(0))),
                traveling_commission=Sum(Coalesce(F('commission__amount'), Decimal(0))),
            )
            .values_list(
                'transaction__appointment_id',
                'traveling_net',
                'traveling_tax',
                'traveling_commission',
            )
        )

        return qs

    def coerce_data_to_table(self, results: QuerySet) -> ReportTable:
        subbooking_ids = list(results.values_list('id', flat=True))
        transactions_info_raw = self.get_transaction_info_for_subbookings(subbooking_ids)
        transactions_info = {tr.pop('subbooking_id'): tr for tr in transactions_info_raw}
        traveling_fee_per_appointment = {
            appo_id: revenue_n_commission
            for appo_id, *revenue_n_commission in self.get_traveling_for_subbookings(subbooking_ids)
        }

        payment_types_ids = set(
            filter(
                None,
                chain.from_iterable(
                    transactions_info[t]['payment_type_ids'] for t in transactions_info
                ),
            )
        )
        payment_types = PaymentTypeReplica.objects.filter(id__in=payment_types_ids)
        payment_type_labels = {pt.id: pt.label for pt in payment_types}

        default_transaction_info = defaultdict(Decimal)
        rows = []
        for booking in results:
            # Get service Info
            service_length = relativedelta(booking.booked_till, booking.booked_from)
            service_name = booking.service_name
            service_category_name = NO_CATEGORY
            service_value = Decimal('0')
            if booking.service_variant:
                if not service_name:
                    service_name = booking.service_variant.service.name
                if booking.service_data.service_variant_price:
                    service_value = booking.service_data.service_variant_price
                if booking.service_variant.service.service_category:
                    service_category_name = booking.service_variant.service.service_category.name

            traveling_net, traveling_tax, traveling_commission = traveling_fee_per_appointment.pop(
                booking.appointment_id, (Decimal('0'), Decimal('0'), Decimal('0'))
            )
            traveling_fee = traveling_tax + traveling_net
            transaction_info = transactions_info.get(booking.id)

            # Get transaction Info
            payment_methods = "-"
            if transaction_info:
                payment_methods = ", ".join(
                    sorted(
                        payment_type_labels.get(payment_type_id, '-')
                        for payment_type_id in transaction_info['payment_type_ids']
                    )
                )
                total_revenue = (
                    transaction_info['revenue_net_n_tax']
                    + transaction_info['tip_amount']
                    + traveling_fee
                )
                total_revenue_wo_tips = transaction_info['revenue_net_n_tax'] + traveling_fee
            else:
                transaction_info = default_transaction_info
                total_revenue_wo_tips = total_revenue = Decimal('0')

            total_commission = transaction_info['commission'] + traveling_commission

            row = AppointmentsLogTable.Row(
                appointment_date=booking.booked_from.date(),
                service_category_name=service_category_name,
                service_name=service_name,
                customer_name=booking.customer_name_merged,
                appointment_status=booking_status_label(booking.status),
                client_type=booking.client_type,
                service_value=service_value,
                service_length=service_length,
                discount=transaction_info['discount'],
                revenue_net=transaction_info['revenue_net'],
                traveling_fee=traveling_net,
                tax_amount=transaction_info['tax'] + traveling_tax,
                tip_rate=(
                    percentage(transaction_info['tip_amount'], service_value)
                    if service_value
                    else None
                ),
                tip_amount=transaction_info['tip_amount'],
                commission_rate=percentage(total_commission, total_revenue_wo_tips),
                commission_amount=total_commission,
                total_revenue=total_revenue,
                total_revenue_wo_tips=total_revenue_wo_tips,
                payment_methods=payment_methods,
            )
            rows.append(row)

        return AppointmentsLogTable(
            rows=rows,
        )


class StafferPerformanceStafferTime(TimeOccupancySection):
    key = 'staffer_performance_member_time'
    title = _('Staff member working hours')
    description = _('Detailed information on the staff member’s working time.')
    category_key = StafferPerformanceReportTopic.WORKING_HOURS

    TIME_BLOCKED = _('Time Blocked')

    def _get_resources_qs(self):
        qs = super()._get_resources_qs()
        return qs.filter(id=self.scope.staffer_id)

    def _get_report_table_class(self):
        return StafferTimeTable


class TimeOffSectionFormattingMixin:
    @staticmethod
    def format_timeoff_span(business, language, time_off) -> str:
        start_date_formatted = format_date_for_xlsx(
            time_off['start_date'],
            language,
            business,
            date_format='date_ymd',
        )
        if time_off['start_date'] == time_off['end_date']:
            date_str = start_date_formatted
        else:
            end_date_formatted = format_date_for_xlsx(
                time_off['end_date'], language, business, date_format='date_ymd'
            )
            date_str = f"{start_date_formatted}-{end_date_formatted}"
        return date_str


class StafferPerformanceStafferTimeOffs(TimeOffSectionFormattingMixin, SchedulesBaseSection):
    key = 'staffer_performance_time_offs'
    title = _('Time off')
    description = _('Staff member scheduled time off with details.')
    category_key = StafferPerformanceReportTopic.WORKING_HOURS

    def get_queryset(self) -> QuerySet:
        today = tznow(tz=self.scope.business.get_timezone()).date()
        time_span_upper_bound = min(self.scope.date_till, today)

        return (
            ResourceTimeOffReplica.objects.filter(
                business_id=self.scope.business.id,
                resource__type=Resource.STAFF,
                date_range__overlap=DateRange(
                    self.scope.date_from,
                    time_span_upper_bound,
                    bounds='[]',
                ),
                resource_id=self.scope.staffer_id,
            )
            .annotate(
                start_date=Lower('date_range'),
                end_date=ExpressionWrapper(Upper('date_range') - 1, output_field=DateField()),
                approved_human=Case(
                    When(approved=True, then=Value('Yes')),
                    default=Value('No'),
                    output_field=CharField(),
                ),
            )
            .order_by('start_date')
            .values(
                'reason_code',
                'start_date',
                'end_date',
                'approved_human',
            )
        )

    def coerce_data_to_table(self, results: QuerySet) -> StafferTimeOffTable:
        def results_timeoff_gen():
            for time_off in results:
                reason = STAFF_TIME_OFF_REASONS_DICT.get(
                    time_off['reason_code'],
                    STAFF_TIME_OFF_REASONS_DICT['other'],
                )
                date_str = self.format_timeoff_span(
                    self.scope.business, self.scope.language, time_off
                )

                yield dict(
                    date=date_str, reason=reason, approved=gettext(time_off['approved_human'])
                )

        return StafferTimeOffTable(
            rows=[StafferTimeOffTable.Row(**row_dict) for row_dict in results_timeoff_gen()]
        )


class StafferPerformanceStafferPlannedTimeOffs(TimeOffSectionFormattingMixin, ReportSection):
    key = 'staffer_performance_planned_time_offs'
    title = _('Upcoming time off')
    description = _('Staff member’s upcoming time off and related details.')
    category_key = StafferPerformanceReportTopic.WORKING_HOURS

    def get_queryset(self) -> QuerySet:
        return (
            ResourceTimeOffReplica.objects.filter(
                business_id=self.scope.business.id,
                resource__type=Resource.STAFF,
                resource_id=self.scope.staffer_id,
            )
            .exclude(
                date_range__overlap=DateRange(
                    date.min,
                    tznow(tz=self.scope.business.get_timezone()).date(),
                    bounds='(]',
                ),
            )
            .annotate(
                start_date=Lower('date_range'),
                end_date=ExpressionWrapper(Upper('date_range') - 1, output_field=DateField()),
            )
            .order_by('start_date')
            .values('reason_code', 'hour_from', 'hour_till', 'start_date', 'end_date')
        )

    def coerce_data_to_table(self, results: QuerySet) -> StafferPlannedTimeOffTable:
        def results_timeoff_gen():
            for time_off in results:
                reason = STAFF_TIME_OFF_REASONS_DICT.get(
                    time_off['reason_code'],
                    STAFF_TIME_OFF_REASONS_DICT['other'],
                )
                if time_off['hour_from'] is None:
                    to_during_day = ALL_DAY
                else:
                    to_during_day = (
                        f"{time_off['hour_from'].strftime('%H:%M')}"
                        f"-{time_off['hour_till'].strftime('%H:%M')}"
                    )

                date_str = self.format_timeoff_span(
                    self.scope.business, self.scope.language, time_off
                )

                yield dict(date=date_str, time=to_during_day, reason=reason)

        return StafferPlannedTimeOffTable(
            rows=[StafferPlannedTimeOffTable.Row(**row_dict) for row_dict in results_timeoff_gen()]
        )


class StafferPerformanceSalesNCommisionsByType(ReportSection):
    key = 'staffer_performance_sales_n_commissions_by_type'
    title = _('Sales and commissions')
    description = _('Summary of all transactions associated with appointments and quick sales.')
    append_total_row = True
    category_key = StafferPerformanceReportTopic.SALES_N_COMMISSIONS

    order_of_txn_types = {
        TransactionRow.TRANSACTION_ROW_TYPE__SERVICE: 0,
        TransactionRow.TRANSACTION_ROW_TYPE__ADDON: 1,
        TransactionRow.TRANSACTION_ROW_TYPE__VOUCHER + VoucherType.EGIFT_CARD: 2,
        TransactionRow.TRANSACTION_ROW_TYPE__VOUCHER + VoucherType.PACKAGE: 3,
        TransactionRow.TRANSACTION_ROW_TYPE__VOUCHER + VoucherType.MEMBERSHIP: 4,
        TransactionRow.TRANSACTION_ROW_TYPE__PRODUCT: 5,
        TransactionRow.TRANSACTION_ROW_TYPE__DEPOSIT: 6,
        TransactionRow.TRANSACTION_ROW_TYPE__TRAVEL_FEE: 7,
    }

    def get_queryset(self) -> QuerySet:
        receipt_statuses = [
            receipt_status.PREPAYMENT_SUCCESS,
            receipt_status.PAYMENT_SUCCESS,
            receipt_status.DEPOSIT_CHARGE_SUCCESS,
        ]

        unique_rows = (
            TransactionRowReplica.objects.filter(
                Q(subbooking__resources__in=[self.scope.staffer_id])
                | Q(
                    commission__resource_id=self.scope.staffer_id,
                ),
                transaction__pos__business_id=self.scope.business.id,
                transaction__latest_receipt__created__gte=self.scope.date_from_datetime_utc,
                transaction__latest_receipt__created__lte=self.scope.date_till_by_span,
                transaction__latest_receipt__status_code__in=receipt_statuses,
                transaction__children__isnull=True,
            )
            .distinct()
            .values_list('id', flat=True)
        )

        qs = (
            TransactionRowReplica.objects.filter(
                id__in=unique_rows,
            )
            .annotate_tip_for_staffer(self.scope.staffer_id)
            .annotate(
                _type_transaction=Case(
                    When(
                        voucher__voucher_template__type__isnull=False,
                        then=Concat(
                            Value(TransactionRowReplica.TRANSACTION_ROW_TYPE__VOUCHER),
                            F('voucher__voucher_template__type'),
                        ),
                    ),
                    When(type__isnull=False, then=F('type')),
                    default=Value('Not typed'),
                    output_field=CharField(),
                ),
                staffer_id=Subquery(
                    BookingResourceReplica.objects.filter(
                        subbooking_id=OuterRef('subbooking'),
                        resource__type=ResourceReplica.STAFF,
                    ).values('resource_id')[:1]
                ),
                sold_by_staffer=ExpressionWrapper(
                    Q(subbooking__isnull=True) | Q(staffer_id=Value(self.scope.staffer_id)),
                    output_field=BooleanField(),
                ),
                commission_assigned=ExpressionWrapper(
                    Q(commission__resource_id=Value(self.scope.staffer_id)),
                    output_field=BooleanField(),
                ),
                commission_amount=Case(
                    When(commission_assigned=True, then=F('commission__amount')),
                    When(commission_assigned__isnull=True, then=F('commission__amount')),
                    default=Decimal('0'),
                ),
                tip_amount=Case(
                    When(sold_by_staffer=True, then=F('tip')),
                    default=Decimal('0'),
                ),
                tax=Case(
                    When(sold_by_staffer=True, then=F('tax_amount')),
                    default=Decimal('0'),
                ),
                revenue=Case(
                    When(sold_by_staffer=True, then=F('net_total')),
                    default=Decimal('0'),
                ),
            )
            .values(
                '_type_transaction',
            )
            .annotate(
                gratuity=Round2(Sum('tip_amount')),
                revenue_net=Sum(Coalesce(F('revenue'), Decimal(0))),
                tax=Sum(Coalesce(F('tax'), Decimal(0))),
                total_revenue=F('revenue_net') + F('gratuity') + F('tax'),
                total_commission=Sum(Coalesce(F('commission_amount'), Decimal(0))),
            )
        )

        return qs

    def coerce_data_to_table(self, results: QuerySet) -> ReportTable:
        rows = []
        for row in sorted(
            results, key=lambda x: self.order_of_txn_types.get(x['_type_transaction'], float('inf'))
        ):
            revenue_source = TransactionRowReplica.TRANSACTION_VOUCHERS_TYPES.get(
                row['_type_transaction'], ''
            )

            rows.append(
                StafferSalesNCommisionsByTypeTable.Row(
                    type=revenue_source,
                    revenue=row['total_revenue'],
                    commission=row['total_commission'],
                    tips=row['gratuity'],
                )
            )

        return StafferSalesNCommisionsByTypeTable(
            rows=rows,
        )

    def get_total_row_queryset(self) -> QuerySet:
        return self.get_queryset().aggregate(
            total_total_revenue=Coalesce(Sum('total_revenue'), Decimal(0)),
            total_total_commission=Coalesce(Sum('total_commission'), Decimal(0)),
            total_tips=Coalesce(Sum('gratuity'), Decimal(0)),
        )

    def calculate_total_row(self, queryset: QuerySet):
        return StafferSalesNCommisionsByTypeTable.Row(
            type=gettext('Total'),
            revenue=queryset['total_total_revenue'],
            commission=queryset['total_total_commission'],
            tips=queryset['total_tips'],
        )


class StafferPerformanceSalesByProducts(BaseSalesByProductSection):
    key = 'staffer_performance_sales_by_products'
    title = _('Product sales')
    description = _("Breakdown of products sold by the staff member.")
    append_total_row = True
    category_key = StafferPerformanceReportTopic.SALES_N_COMMISSIONS

    def get_queryset(self) -> QuerySet:
        unique_rows = (
            self.base_queryset()
            .filter(
                Q(subbooking__resources__in=[self.scope.staffer_id])
                | Q(
                    commission__resource_id=self.scope.staffer_id,
                ),
            )
            .distinct()
            .values_list('id', flat=True)
        )

        return (
            TransactionRowReplica.objects.filter(id__in=unique_rows)
            .annotate(
                product_name=F('product__name'),
                staffer_id=Subquery(
                    BookingResourceReplica.objects.filter(
                        subbooking_id=OuterRef('subbooking'),
                        resource__type=ResourceReplica.STAFF,
                    ).values('resource_id')[:1]
                ),
                sold_by_staffer=ExpressionWrapper(
                    Q(subbooking__isnull=True) | Q(staffer_id=Value(self.scope.staffer_id)),
                    output_field=BooleanField(),
                ),
                commission_assigned=ExpressionWrapper(
                    Q(commission__resource_id=Value(self.scope.staffer_id)),
                    output_field=BooleanField(),
                ),
                commission_amount=Case(
                    When(commission_assigned=True, then=F('commission__amount')),
                    default=Decimal('0'),
                ),
                tax=Case(
                    When(sold_by_staffer=True, then=F('tax_amount')),
                    default=Decimal('0'),
                ),
                revenue=Case(
                    When(sold_by_staffer=True, then=F('net_total')),
                    default=Decimal('0'),
                ),
                _quantity=Case(
                    When(sold_by_staffer=True, then=F('quantity')),
                    default=0,
                ),
                discount=Case(
                    When(sold_by_staffer=True, then=F('real_discount_amount')),
                    default=Decimal('0'),
                ),
            )
            .values('product_name')
            .annotate(
                product_category=F('product__category__name'),
                _quantity=Sum(Coalesce(F('_quantity'), 0)),
                revenue_net=Sum(Coalesce(F('revenue'), Decimal(0))),
                discount=Sum(Coalesce(F('discount'), Decimal(0))),
                tax=Sum(Coalesce(F('tax'), Decimal(0))),
                revenue_gross=F('revenue_net') + F('tax'),
                _commission_amount=Sum(Coalesce(F('commission_amount'), Decimal(0))),
            )
        )

    def coerce_data_to_table(self, results) -> StafferSalesByProductsTable:
        rows = []

        for row in results:
            rows.append(
                StafferSalesByProductsTable.Row(
                    product_category=row.get('product_category') or NO_CATEGORY,
                    product_name=row.get('product_name'),
                    quantity=row.get('_quantity'),
                    revenue_net=row.get('revenue_net'),
                    discount=row.get('discount'),
                    tax=row.get('tax'),
                    revenue_gross=row.get('revenue_gross'),
                    commission=row.get('_commission_amount'),
                )
            )

        return StafferSalesByProductsTable(
            rows=rows,
        )

    def get_total_row_queryset(self) -> QuerySet:
        return self.get_queryset().aggregate(
            total_quantity=Coalesce(Sum('_quantity'), 0),
            total_revenue_net=Coalesce(Sum('revenue_net'), Decimal(0)),
            total_discount=Coalesce(Sum('discount'), Decimal(0)),
            total_tax=Coalesce(Sum('tax'), Decimal(0)),
            total_revenue_gross=Coalesce(Sum('revenue_gross'), Decimal(0)),
            total_commission=Coalesce(Sum('_commission_amount'), Decimal(0)),
        )

    def calculate_total_row(self, queryset: QuerySet):
        return StafferSalesByProductsTable.Row(
            product_category=gettext('Total'),
            product_name='',
            quantity=queryset['total_quantity'],
            revenue_net=queryset['total_revenue_net'],
            discount=queryset['total_discount'],
            tax=queryset['total_tax'],
            revenue_gross=queryset['total_revenue_gross'],
            commission=queryset['total_commission'],
        )


class StafferPerformanceClientsNReviews(ScopedSubBookingSectionMixin, ReportSection):
    key = 'staffer_performance_clients_n_reviews'
    title = _('Clients and reviews')
    description = _("Summary of staff member’s clients by client status.")
    category_key = StafferPerformanceReportTopic.CLIENTS_N_REVIEWS

    def get_queryset(self) -> QuerySet:
        qs = (
            self._base_booking_queryset(appointment_statuses=[AppointmentStatus.FINISHED])
            .annotate(
                is_new_client=Case(
                    When(
                        appointment__booked_for__first_appointment__booked_from__range=(
                            self.scope.date_from_datetime_utc,
                            self.scope.date_till_by_span,
                        ),
                        then=True,
                    ),
                    When(appointment__booked_for__isnull=True, then=True),
                    default=False,
                    output_field=CharField(),
                ),
                is_staffer_selected_by_client=Coalesce(
                    F('is_staffer_requested_by_client'), Value(False), output_field=BooleanField()
                ),
                client=F('appointment__booked_for_id'),
                review_rank=Case(
                    When(
                        review__deleted__isnull=False,
                        then=None,
                    ),
                    default=F('review__rank'),
                    output_field=IntegerField(),
                ),
            )
            .values(
                'appointment_id',
                'client',
                'is_new_client',
                'is_staffer_selected_by_client',
                'review_rank',
            )
        )

        return qs

    def coerce_data_to_table(self, results: QuerySet) -> ReportTable:
        dataframe = pd.DataFrame(
            results,
            columns=[
                'appointment_id',
                'client',
                'is_new_client',
                'is_staffer_selected_by_client',
                'review_rank',
            ],
        )

        if dataframe.empty:
            return StafferPerformanceClientsNReviewsTable(rows=[])

        completed_apps = dataframe.appointment_id.unique().size
        overall_clients_no_walkins = dataframe.client.value_counts(dropna=True).count()
        walkins = dataframe.loc[dataframe.client.isna()].client.size
        overall_clients = overall_clients_no_walkins + walkins

        new_clients = (
            dataframe.loc[dataframe.is_new_client & dataframe.client.notna()]
            .client.value_counts(dropna=False)
            .count()
        )
        returning_clients = (
            dataframe.loc[dataframe.is_new_client == False]  # pylint: disable=singleton-comparison
            .client.value_counts(dropna=False)
            .count()
        )
        clients_selected_the_staffer = (
            dataframe.loc[dataframe.is_staffer_selected_by_client]
            .client.value_counts(dropna=True)
            .count()
            + dataframe.loc[
                dataframe.client.isna() & dataframe.is_staffer_selected_by_client
            ].client.size
        )
        reviews_count = dataframe.review_rank.count()

        grouped = StafferPerformanceClientsNReviewsTable.Row(
            completed_apps=completed_apps,
            overall_clients=overall_clients,
            new_clients=new_clients,
            new_clients_rate=new_clients / overall_clients * 100,
            returning_clients=returning_clients,
            returning_clients_rate=returning_clients / overall_clients * 100,
            walk_in_clients=walkins,
            walk_in_clients_rate=walkins / overall_clients * 100,
            clients_requested_staffer=clients_selected_the_staffer,
            clients_requested_staffer_rate=clients_selected_the_staffer / overall_clients * 100,
            reviews=reviews_count,
            avg_rating=dataframe.review_rank.sum() / reviews_count if reviews_count else 0,
        )

        return StafferPerformanceClientsNReviewsTable(
            rows=[grouped],
        )


class StafferPerformanceDetailedReviews(ScopedSubBookingSectionMixin, ReportSection):
    key = 'staffer_performance_reviews_from_this_period'
    title = _('Client satisfaction')
    description = _("Breakdown of reviews and rating for the staff member.")
    category_key = StafferPerformanceReportTopic.CLIENTS_N_REVIEWS

    def get_queryset(self) -> QuerySet:
        qs = (
            self._base_booking_queryset(appointment_statuses=[AppointmentStatus.FINISHED])
            .filter(
                review__deleted__isnull=True,
                review__isnull=False,
            )
            .annotate(
                review_text_length=Length(F('review__review')),
                review_text_with_bound=Case(
                    When(
                        review_text_length__gt=REVIEW_TEXT_UPPER_BOUND,
                        then=Concat(Left('review__review', REVIEW_TEXT_UPPER_BOUND), Value('...')),
                    ),
                    When(review_text_length__isnull=True, then=Value('')),
                    default=F('review__review'),
                    output_field=CharField(),
                ),
                client_full_name=Subquery(
                    BusinessCustomerInfoReplica.objects.filter(
                        appointments__id=OuterRef('appointment_id')
                    )
                    .annotate_full_name()
                    .values('full_name')[:1]
                ),
                serv_name=Case(
                    When(service_name__isnull=True, then=F('service_variant__service__name')),
                    default=F('service_name'),
                    output_field=CharField(),
                ),
            )
            .order_by('review__created')
            .values('review_text_with_bound', 'review__rank', 'client_full_name', 'serv_name')
        )

        return qs

    def coerce_data_to_table(self, results: QuerySet) -> ReportTable:
        return StafferPerformanceDetailedReviewsTable(
            rows=[
                StafferPerformanceDetailedReviewsTable.Row(
                    client_name=r['client_full_name'],
                    service_name=r['serv_name'],
                    rate=r['review__rank'],
                    review_text=r['review_text_with_bound'],
                )
                for r in results
            ],
        )
