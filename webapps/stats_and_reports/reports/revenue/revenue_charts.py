from decimal import Decimal

from django.conf import settings
from django.db.models import Case, <PERSON>rField, F, Q, QuerySet, Sum, Value, When
from django.db.models.functions import Coalesce
from django.utils.translation import gettext_lazy as _

from webapps.pos.enums import receipt_status
from webapps.stats_and_reports.consts import FirstDOW
from webapps.stats_and_reports.models import TransactionReplica, TransactionRowReplica
from webapps.stats_and_reports.reports.base import (
    BaseChart,
    ChartData,
    ChartDataPoint,
    ChartFilteredByPermission,
    DataSeries,
)
from webapps.stats_and_reports.reports.time_data import TimeScopeType
from webapps.stats_and_reports.reports.utils import get_business_tzinfo


class RevenueDataSeries(DataSeries):
    key = 'revenue'
    label = _('Revenue')


class RevenueForecastDataSeries(DataSeries):
    key = 'revenue_forecast'
    label = _('Revenue')
    is_forecast = True


class RevenueChart(BaseChart):
    key = 'revenue_chart'
    title = _('Revenue')

    def get_queryset(self):
        datetime_from, datetime_till = self.scope.charts_datetime_from_till
        status_codes = receipt_status.SUCCESS_STATUSES_WITH_PREPAYMENT
        return TransactionRowReplica.objects.filter(
            transaction__pos__business_id=self.scope.business.id,
            transaction__latest_receipt__created__gte=datetime_from,
            transaction__latest_receipt__created__lte=datetime_till,
            transaction__children__isnull=True,
            transaction__latest_receipt__status_code__in=status_codes,
        )

    def coerce_data_to_chart(self, results) -> ChartData:
        tips_by_date = self.get_tips()
        tips = self.rows_to_dict(tips_by_date, single_key='sum')
        revenue_stats = self.rows_to_dict(results, single_key='sum')

        current_date_range = self.scope.get_historical_date_range()
        points = []
        for date in current_date_range:
            key = self.date_to_key(date)
            value = revenue_stats.get(key, 0) + tips.get(key, 0)
            self.list_all_chart_values.append(value)
            points.append(ChartDataPoint(date=date, x_key=key, value=value))

        forecast_points = self.get_forecast_data(points)

        return ChartData(
            max_value=max(self.list_all_chart_values) if self.list_all_chart_values else 0,
            series=[
                RevenueDataSeries(data_points=points),
                RevenueForecastDataSeries(data_points=forecast_points),
            ],
        )

    def queryset_timespan_annotation(self, queryset):
        if (
            settings.COUNTRY_CONFIG.first_day_of_week == FirstDOW.SUNDAY
            and self.scope.time_span == TimeScopeType.WEEK
        ):
            week_dates_range, week_range = self.scope.non_iso_weeks_ranges()
            return (
                queryset.annotate(
                    date=Case(
                        When(
                            Q(transaction__latest_receipt__created__range=week_dates_range[0]),
                            then=Value(week_range[0]),
                        ),
                        When(
                            Q(transaction__latest_receipt__created__range=week_dates_range[1]),
                            then=Value(week_range[1]),
                        ),
                        When(
                            Q(transaction__latest_receipt__created__range=week_dates_range[2]),
                            then=Value(week_range[2]),
                        ),
                        When(
                            Q(transaction__latest_receipt__created__range=week_dates_range[3]),
                            then=Value(week_range[3]),
                        ),
                        output_field=CharField(),
                        default=Value(''),
                    ),
                )
                .values('date')
                .annotate(
                    sum=Sum(
                        Coalesce(F('net_total'), Decimal(0)) + Coalesce(F('tax_amount'), Decimal(0))
                    ),
                )
                .order_by()
            )

        tzinfo = get_business_tzinfo(self.scope.business, self.scope.date_from_datetime_utc)
        extract_date = self.scope.charts_queryset_annotation()
        return (
            queryset.annotate(
                date=extract_date('transaction__latest_receipt__created', tzinfo=tzinfo),
            )
            .values('date')
            .annotate(
                sum=Sum(
                    Coalesce(F('net_total'), Decimal(0)) + Coalesce(F('tax_amount'), Decimal(0))
                ),
            )
            .order_by()
        )

    def get_transactions_qs(self):
        queryset = self.get_queryset()
        transaction_ids = list(queryset.values_list('transaction_id', flat=True))
        return TransactionReplica.objects.filter(id__in=transaction_ids)

    def get_transaction_tips_qs(self):
        transactions_qs = self.get_transactions_qs()
        if (
            settings.COUNTRY_CONFIG.first_day_of_week == FirstDOW.SUNDAY
            and self.scope.time_span == TimeScopeType.WEEK
        ):
            week_dates_range, week_range = self.scope.non_iso_weeks_ranges()
            return transactions_qs.annotate(
                date=Case(
                    When(
                        Q(latest_receipt__created__range=week_dates_range[0]),
                        then=Value(week_range[0]),
                    ),
                    When(
                        Q(latest_receipt__created__range=week_dates_range[1]),
                        then=Value(week_range[1]),
                    ),
                    When(
                        Q(latest_receipt__created__range=week_dates_range[2]),
                        then=Value(week_range[2]),
                    ),
                    When(
                        Q(latest_receipt__created__range=week_dates_range[3]),
                        then=Value(week_range[3]),
                    ),
                    output_field=CharField(),
                    default=Value(''),
                ),
            )
        tzinfo = get_business_tzinfo(self.scope.business, self.scope.date_from_datetime_utc)
        extract_date = self.scope.charts_queryset_annotation()
        return transactions_qs.annotate(
            date=extract_date('latest_receipt__created', tzinfo=tzinfo),
        )

    def get_tips(self):
        return (
            self.get_transaction_tips_qs()
            .values(
                'date',
            )
            .annotate(sum=Sum(Coalesce(F('tip__amount'), Decimal(0))))
            .order_by()
        )


class RevenueChartStaffFiltered(ChartFilteredByPermission, RevenueChart):
    def filter_by_permission(self, queryset) -> QuerySet:
        if self.is_owner_or_manager():
            return queryset
        if self.staffer:
            return queryset.filter(
                commission__resource=self.staffer,
            )
        return queryset.none()

    def get_transactions_qs(self):
        queryset = self.get_queryset()
        queryset = self.filter_by_permission(queryset)
        transaction_ids = list(queryset.values_list('transaction_id', flat=True))
        return TransactionReplica.objects.filter(id__in=transaction_ids)

    def get_tips(self):
        return (
            self.get_transaction_tips_qs()
            .values(
                'date',
            )
            .annotate(
                normal_tips=Coalesce(
                    Sum(
                        F('tip__amount'),
                        filter=Q(tip__tip_rows__isnull=True),
                    ),
                    Decimal(0),
                ),
                split_tips=Coalesce(
                    Sum(
                        F('tip__tip_rows__amount'),
                        filter=Q(tip__tip_rows__staffer=self.staffer),
                    ),
                    Decimal(0),
                ),
                sum=F('normal_tips') + F('split_tips'),
            )
            .order_by()
        )
