from model_bakery import baker

from webapps.business.models.bci import Tag, BusinessCustomerInfoTag
from webapps.stats_and_reports.reports import ClientsByHashTagsSection
from webapps.stats_and_reports.reports.client_reports.clients_summary import ClientsByHashTagsTable
from webapps.stats_and_reports.reports.tests.base import PrepareData


class TestClientsByHashtag(PrepareData):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        test_tag = baker.make(
            Tag,
            name='#TestTag',
            business=cls.business,
        )
        baker.make(
            BusinessCustomerInfoTag,
            customer=cls.bci_1,
            tag=test_tag,
        )
        baker.make(
            BusinessCustomerInfoTag,
            customer=cls.bci_2,
            tag=test_tag,
        )
        baker.make(
            BusinessCustomerInfoTag,
            customer=cls.bci_3,
            tag=test_tag,
        )

    def setUp(self):
        super().setUp()
        self.section = ClientsByHashTagsSection(self.time_scope)

    def test_coerce_data_to_table_new_clients(self):
        result = self.section.get_data(False)

        test_tag_table = ClientsByHashTagsTable.Row(
            name='#TestTag',
            quantity=3,
            percentage=100,
        )
        disco_tag_table = ClientsByHashTagsTable.Row(
            name='Disco super star',
            quantity=1,
            percentage=33,
        )
        self.assertIn(test_tag_table, result.rows)
        self.assertIn(disco_tag_table, result.rows)
