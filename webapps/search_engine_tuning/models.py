import logging
import typing as t
from abc import abstractmethod
from collections import Counter, defaultdict
from datetime import datetime, timedelta
from functools import partial
from math import sqrt
from operator import itemgetter
from typing import Any, Dict

import pytz
from django.conf import settings
from django.core.serializers.json import DjangoJSONEncoder
from django.core.validators import MinV<PERSON>ueValidator
from django.db import models, transaction
from django.db.models import <PERSON>, Count, <PERSON><PERSON><PERSON><PERSON>, Max, Min, When
from django.db.models.expressions import F, Q, Subquery, Value
from django.db.models.fields import <PERSON><PERSON><PERSON><PERSON>, IntegerField

import webapps.search_engine_tuning.typing as set_t
from lib.elasticsearch.consts import (
    ESAvailabilityNature,
    REVIEWS_RANK_SCORE,
    SECONDARY_CATEGORY_SCORE,
)
from lib.elasticsearch.tools import ESJSONEncoder
from lib.gcs_dataset import (
    ConversionDataset,
)
from lib.fields.deprecate_field import deprecate_field
from lib.models import ArchiveModel
from lib.tools import relativedelta_total_seconds, tznow
from settings.elasticsearch import ES_DATETIME_FORMAT
from webapps import consts
from webapps.booking.models import Appointment, SubBooking
from webapps.business.models import (
    Business,
    Service,
    ServiceAddOn,
    ServiceVariant,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business.models.category import BusinessCategory
from webapps.marketplace.models import MarketplaceBusiness
from webapps.pos.models import TransactionRow
from webapps.reviews.models import Review
from webapps.search_engine_tuning import consts as c
from webapps.search_engine_tuning.fields.promotions_profitability import (
    ServicePromotionProfitabilityCalculator,
)
from webapps.search_engine_tuning.fields.user_interest import (
    UserInterestCalculator,
)
from webapps.user.const import Gender
from webapps.user.models import User
from webapps.utt.models import Category
from webapps.warehouse.models import Commodity

logger = logging.getLogger('booksy.search_tuning')

# we don't want newly introduced entries to jump to top of the rankings
POPULARITY_MIN_DAYS = 14

# skip entries that seem too old
POPULARITY_MAX_DAYS = 365


class TuningModel(models.Model):
    # region base object methods
    relation_field: str

    class Meta:
        abstract = True

    @property
    def related_model_id(self):
        return getattr(self, self.relation_field)

    def __repr__(self) -> str:
        return f'<{self.__class__.__name__}:{self.relation_field}: ' f'{self.related_model_id}>'

    def __str__(self) -> str:
        return f'{self.__class__.__name__} {self.relation_field}:' f'{self.related_model_id}>'

    # endregion base object methods

    # region update model
    def _get_column(self, column: str) -> t.Dict:
        return getattr(self, column)

    def get_field(self, column: str, field: str) -> t.Any:
        column = self._get_column(column)
        return column.get(field)

    def get_datetime_field(self, column: str, field: str) -> datetime:
        value = self.get_field(column, field)
        return (
            datetime.strptime(value, ES_DATETIME_FORMAT).replace(tzinfo=pytz.UTC) if value else None
        )

    def set_field(self, column: str, field: str, value: t.Any) -> None:
        self.set_fields(column, {field: value})

    def set_fields(self, column: str, values: t.Dict[str, t.Any]) -> None:
        self._get_column(column).update(values)
        self.save(update_fields=[column])

    def update_params(self, fields: set_t.FieldsList = None) -> None:
        self.update_multiple_tunings([self.related_model_id], fields)

    @classmethod
    def _ensure_related_ids_exist(
        cls,
        ids: t.Sequence[int],
    ) -> t.Set[int]:
        return set(
            cls._meta.pk.related_model.objects.filter(id__in=ids).values_list('id', flat=True)
        )

    @classmethod
    @abstractmethod
    def update_multiple_tunings(
        cls,
        ids: set_t.IdList,
        fields: set_t.FieldsList = None,
        update_es: bool = True,
        dry_run: bool = False,
        **kwargs,
    ) -> None:
        pass

    @classmethod
    def _get_or_create(cls, ids: set_t.IdList) -> t.Tuple[set_t.IdList, t.List[set_t.Type]]:
        ids = cls._ensure_related_ids_exist(ids)
        instances_map = cls.objects.in_bulk(ids, field_name=cls.relation_field)

        missing_ids = ids - set(instances_map)
        missing_objects = [cls(**{cls.relation_field: _id}) for _id in missing_ids]
        created_objects = cls.objects.bulk_create(missing_objects, batch_size=1000)
        instances_map.update({getattr(obj, cls.relation_field): obj for obj in created_objects})

        if instances_map:
            _ids, _instances = zip(*instances_map.items())
        else:
            _ids, _instances = [], []

        return _ids, _instances

    @transaction.atomic
    def _calculate_parameters(
        self, fields: set_t.FieldsList = None, dry_run: bool = False, **kwargs
    ) -> None:
        """
        Recompute all specified search parameters.

        Parameter considered specified if it:
            1) has field on the model of name 'my_field'
            2) has method that update this fields '_update_my_field'
        :return: None
        """
        column_names = (
            elem.name for elem in self._meta.local_concrete_fields if not elem.is_relation
        )
        if fields:
            column_names = filter(lambda column_name: column_name in fields, column_names)
        column_names = list(column_names)
        method_names = [f'_update_{name}' for name in column_names]

        # call all update methods
        for method_name in method_names:
            if hasattr(self, method_name):
                getattr(self, method_name)(**kwargs)

        # save model
        if not dry_run:
            self.save(update_fields=column_names)

    # endregion update model


class BusinessTuning(ArchiveModel, TuningModel):
    relation_field = 'business_id'
    fast_update_fields = [
        'secondary_category_score',
        'reviews_rating_score',
        'boost',
        'promotions_profitability',
        'max_discount_rate',
    ]
    _promotion_calculator = None

    # foreign key
    business_id: int
    business: Business = models.OneToOneField(
        Business,
        on_delete=models.CASCADE,
        primary_key=True,
        related_name='search_tuning',
    )

    # TODO: Remove when UTT2 will be fully operational
    # key is BusinessCategory_id
    # value is current percent of finished bookings for this service
    secondary_category_score = JSONField(default=dict, blank=True)

    utt_score = JSONField(default=dict, blank=True)
    reviews_rating_score = models.FloatField(default=0.0)
    conversion = models.FloatField(default=0.0)
    boost = models.FloatField(default=0.0)
    promotions_profitability = models.FloatField(default=0.0)
    max_discount_rate = models.IntegerField(default=0)
    business_availability = JSONField(default=dict, blank=True)
    hidden_on_web = models.BooleanField(default=False)

    return_rate = deprecate_field(models.FloatField(default=0.0))

    # region update model
    @classmethod
    def update_multiple_tunings(
        cls,
        ids: set_t.IdList,
        fields: set_t.FieldsList = None,
        update_es: bool = True,
        dry_run: bool = False,
        **kwargs,
    ) -> None:
        from webapps.business.elasticsearch import BusinessDocument

        business_ids, business_tunings = cls._get_or_create(ids)

        conversion_bucket = None
        if fields is None or 'conversion' in fields:
            conversion_bucket = ConversionDataset(ids=business_ids)
        # update in db
        for tuning in business_tunings:
            cls._calculate_parameters(
                tuning,
                fields,
                dry_run=dry_run,
                conversion_bucket=conversion_bucket,
            )

        # update in es
        if update_es and not dry_run:
            es_params = list(filter(None, map(lambda x: x.to_es_update(), business_tunings)))
            BusinessDocument.bulk_update(
                ids=business_ids,
                updates=es_params,
                use_celery=False,
                verbose=True,
                refresh_index=False,
            )

    # TODO: Remove when UTT2 will be fully operational
    def to_es_update(self) -> t.Dict[str, t.Any]:
        """Return dict of computed parameters (to update not reindex in ES)."""
        base = {
            # id need to resolve document in bulk_update
            'id': self.business_id,
            'reviews_rating_score': self.reviews_rating_score,
            'conversion': self.conversion,
            'promotion_boost': self.boost,
            'promotions_profitability': self.promotions_profitability,
            'max_discount_rate': self.max_discount_rate,
            'utt_score': self.utt_score,
        }
        base.update(self.get_secondary_categories_score())
        return base

    # endregion update model

    def get_secondary_categories_score(self):
        """Return dict of bookings_rates for each category

        :return: dict.
            Key is one of enum in settings.ES_BUCKET_FIELD_NAMES
            Value. Percentage of finished bookings in this category
            for this business
        """
        from webapps.business.elasticsearch import compute_booking_rates
        from webapps.business.elasticsearch.tools import BookingRateException

        result_dict = {}
        computed_rates = compute_booking_rates(self.secondary_category_score)
        for index, name in enumerate(settings.ES_BUCKET_FIELD_NAMES):
            if index >= len(computed_rates):
                msg = (
                    f"Could index document "
                    f"business: {self.business_id}. Category mapping is greater than "
                    f"ES_CATEGORY_BUCKETS {settings.ES_CATEGORY_BUCKETS}; "
                    f"index {index}"
                )
                raise BookingRateException(msg)
            result_dict[name] = computed_rates[index]
        return result_dict

    @property
    def promotion_calculator(self):
        if self._promotion_calculator is None:
            self._promotion_calculator = ServicePromotionProfitabilityCalculator(
                self.business_id,
                now=self.business.tznow,
                min_lead_time=relativedelta_total_seconds(self.business.booking_min_lead_time)
                / 3600,
            )
        return self._promotion_calculator

    # region update from es
    def refresh_availability_from_es(self, availability):
        """Update business_availability field on model.
        Filter all availability slots by  now <= date <= now + 5 days

        :param availability: list of dicts:
                            required keys:
                                    1) date
                                    2) date_active_till
        :return: None
        """
        start = self.business.tznow
        # always show 5 days (counting weekends)
        # this condition guarantee that
        # at least three days will be visible in admin panel
        fun = partial(
            self.filter_date_function,
            start_date=start.date(),
            end_date=(start + timedelta(days=5)).date(),
        )

        self.business_availability = [
            self.format_availability_doc(x) for x in filter(fun, availability)
        ]
        self.save(update_fields=['business_availability'])

    @staticmethod
    def filter_date_function(obj, start_date, end_date):
        # filter is only for Real availability
        # and date gte i lte is same for this type of doc
        available_date = obj['date']['gte'].date()
        if isinstance(obj['active_till_implicit'], str):
            active_till = datetime.strptime(
                obj['active_till_implicit'], settings.ES_DATETIME_FORMAT
            ).date()
        else:
            active_till = obj['active_till_implicit'].date()
        return (
            obj['nature'] == ESAvailabilityNature.REAL.value
            and active_till >= start_date
            and start_date <= available_date <= end_date
        )

    @staticmethod
    def format_availability_doc(obj):
        obj['date'] = obj['date']['gte'].strftime(settings.ES_DATETIME_FORMAT)
        return obj

    # endregion update from es

    # region UPDATE METHODS
    def _update_secondary_category_score(self, **__):
        days = c.LAST_BOOKINGS_PERIOD
        end = tznow()
        start = end - timedelta(days=days)
        primary_category_id = self.business.primary_category_id
        # PREPARE MAPPINGS
        # get treatments
        treatments_info = self.business.treatments.exclude(
            Q(Q(parent_id=primary_category_id) | Q(parent_id__isnull=True))
        ).values_list(
            'id',
            'parent_id',
        )
        if not treatments_info:
            self.secondary_category_score = {}
            return

        treatment_category_mapping = dict(treatments_info)

        services_info = Service.objects.filter(
            business_id=self.business_id,
            active=True,
            deleted__isnull=True,
            treatment_id__isnull=False,
            treatment_no_match=False,
        ).values_list('id', 'treatment_id')
        service_treatment_mapping = dict(services_info)

        service_variants = ServiceVariant.objects.filter(
            service_id__in=list(service_treatment_mapping.keys()),
            active=True,
            deleted__isnull=True,
        ).values_list('id', 'service_id')
        sv_service_mapping = dict(service_variants)
        # get bookings only with Booksy users
        bookings_info = self.get_bookings_only_with_booksy_users(start, end, sv_service_mapping)
        updated_params = Counter()
        total_bookings = 0
        for sv_info in bookings_info:
            sv_id, booking_count = sv_info
            total_bookings += booking_count
            service_id = sv_service_mapping.get(sv_id)
            if service_id is None:
                logger.error(
                    '[SET] No service for service_variant %s in business %s',
                    sv_id,
                    self.business_id,
                )
                continue
            treatment_id = service_treatment_mapping.get(service_id)
            if treatment_id is None:
                logger.error(
                    '[SET] No treatment for service %s in business %s', service_id, self.business_id
                )
                continue
            category_id = treatment_category_mapping.get(treatment_id)

            if category_id is None:
                # Two option possible
                # 1) It is main category of business nothing to do
                # 2) This business doesn't have configured categories and
                #    treatments nothing can we do here
                continue

            updated_params[category_id] += booking_count

        if total_bookings:
            f_total = float(total_bookings)
            for category_id in updated_params:
                updated_params[category_id] = round(updated_params[category_id] / f_total, 4)

        self.secondary_category_score = updated_params

    def _update_reviews_rating_score(self, **__):
        start = tznow() - timedelta(days=56)
        # get all reviews in last 56 days
        # all reviews with rank below 4 count as negative
        # (equal 4 count as neutral)
        reviews_aggregation = (
            Review.objects.filter(
                business_id=self.business_id,
                created__gte=start,
            )
            .values(
                'business_id',
            )
            .annotate(
                negative=Count(
                    Case(
                        When(rank__lt=4, then='id'),
                        output_field=IntegerField(),
                    ),
                    distinct=True,
                ),
                positive=Count(
                    Case(
                        When(rank=5, then='id'),
                        output_field=IntegerField(),
                    ),
                    distinct=True,
                ),
                # average=Avg('rank')
            )
            .order_by(
                # delete default order by review_id
                'business_id'
            )
            .annotate(total_count=F('negative') + F('positive'))
            .filter(total_count__gte=1)
            .values(
                'negative',
                'positive',
                # 'average',
            )
            .first()
        )
        # all reviews with rank 5  count as positive

        if not reviews_aggregation:
            self.reviews_rating_score = 0.0
            return

        positive_reviews = reviews_aggregation['positive']
        negative_reviews = reviews_aggregation['negative']
        total_reviews = float(positive_reviews + negative_reviews)
        avg = (
            (positive_reviews + 1.9208) / total_reviews
            - 1.96
            * sqrt(float(negative_reviews * positive_reviews) / total_reviews + 0.9604)
            / total_reviews
        ) / (1 + 3.8416 / total_reviews)

        self.reviews_rating_score = max(0.0, (10 * (avg - 0.2) / 8.0) ** 3) * REVIEWS_RANK_SCORE

    def _update_conversion(self, conversion_bucket: ConversionDataset, **__):
        conversion = 0.0
        # will be cleaned after use in update_businesses_tuning_params_task
        _conversion = conversion_bucket[self.business_id]
        if _conversion:
            conversion = _conversion.conversion
        # do not accept negative values
        conversion = max(conversion, 0)
        self.conversion = min(2000 * 1.33 * conversion, 2000)

    def get_boost(self):
        boost_completeness_dict = {
            0: 0.00,
            1: 0.25,
            2: 0.50,
            3: 1.00,
        }

        business = self.business
        if not business.has_promotion():
            return 0.0

        boost_completeness = business.boost_visibility_allowed
        if not boost_completeness['cover_photo']:
            return 0.00
        return boost_completeness_dict[sum(business.boost_visibility_allowed.values())]

    def _update_boost(self, **__):
        from webapps.marketplace.notifications import (
            BoostCompletenessNotification,
        )
        from webapps.marketplace.models import MarketplaceStageStatus

        tuning = BusinessTuning.objects.filter(
            business_id=self.business_id,
        ).first()
        old_boost = tuning.boost if tuning else None
        new_boost = self.get_boost()

        if new_boost != old_boost:
            if new_boost == 0.0:
                stage_name = 'visibility_boost_todolist_failed'
            elif new_boost == 1.0:
                stage_name = 'visibility_boost_todolist_completed'
                marketplace_biz, _ = MarketplaceBusiness.objects.get_or_create(
                    business=self.business,
                )
                if not marketplace_biz.boost_completed_sended:
                    BoostCompletenessNotification(self.business).send()
                    marketplace_biz.boost_completed_sended = True
                    marketplace_biz.save(
                        update_fields=['boost_completed_sended'],
                    )
            else:
                stage_name = f'visibility_boost_todolist_{int(new_boost*100)}%'
            MarketplaceStageStatus.add(stage_name, self.business)
        self.boost = new_boost

    def _update_promotions_profitability(self, **__):
        self.promotions_profitability = self.promotion_calculator.business_profitability

    def _update_max_discount_rate(self, **__):
        self.max_discount_rate = self.promotion_calculator.max_discount_rate

    def _update_utt_score(self, **__):

        days = c.LAST_BOOKINGS_PERIOD
        end = tznow()
        start = end - timedelta(days=days)
        primary_category_id = (
            (self.business.primary_category.category_utt or 0)
            if self.business.primary_category is not None
            else 0
        )
        # PREPARE MAPPINGS
        # get treatments
        treatments = dict(
            self.business.treatments_utt.exclude(
                category_id=primary_category_id,
            ).values_list(
                'id',
                'category_id',
            )
        )
        if not treatments:
            return

        services = dict(
            Service.objects.filter(
                business_id=self.business_id,
                active=True,
                deleted__isnull=True,
                treatment_utt_id__isnull=False,
            ).values_list('id', 'treatment_utt_id')
        )

        service_variants = dict(
            ServiceVariant.objects.filter(
                service_id__in=list(services.keys()),
                active=True,
                deleted__isnull=True,
            ).values_list('id', 'service_id')
        )

        # get bookings only with Booksy users
        bookings = self.get_bookings_only_with_booksy_users(start, end, service_variants)

        bookings_counts = Counter()
        total_bookings = 0.0
        for sv_id, booking_count in bookings:
            total_bookings += booking_count
            service_id = service_variants.get(sv_id)
            if service_id is None:
                logger.error(
                    '[SET] No service for service_variant %s in business %s',
                    sv_id,
                    self.business_id,
                )
                continue
            treatment_id = services.get(service_id)
            if treatment_id is None:
                logger.error(
                    '[SET] No treatment for service %s in business %s', service_id, self.business_id
                )
                continue
            category_id = treatments.get(treatment_id)

            if category_id is None:
                # Two option possible
                # 1) It is main category of business nothing to do
                # 2) This business doesn't have configured categories and
                #    treatments nothing can we do here
                continue

            bookings_counts[category_id] += booking_count

        if total_bookings:
            bookings_rate = {
                category_id: round(bookings_counts[category_id] / total_bookings, 4)
                for category_id in bookings_counts
            }
            self.utt_score = {
                category_id: float(
                    2 * booking_rate * SECONDARY_CATEGORY_SCORE
                    if booking_rate < 0.4
                    else SECONDARY_CATEGORY_SCORE
                )
                for category_id, booking_rate in bookings_rate.items()
            }

    @staticmethod
    def get_bookings_only_with_booksy_users(start, end, service_variants):
        if not service_variants:
            return []
        sv_ids = list(service_variants.keys())
        return (
            SubBooking.objects.filter(
                service_variant_id__in=sv_ids,
                service_variant_id__gte=min(sv_ids),
                service_variant_id__lte=max(sv_ids),
                appointment__status=Appointment.STATUS.FINISHED,
                appointment__booked_for__user_id__isnull=False,
                booked_from__range=(start, end),
            )
            .values('service_variant_id')
            .annotate(booking_per_sv=Count('service_variant_id'))
            .values_list('service_variant_id', 'booking_per_sv')
        )

    # endregion UPDATE METHODS


# TODO: Remove '_utt' from name when UTT2 will be fully operational
class UserTuning(ArchiveModel, TuningModel):
    relation_field = 'user_id'
    fast_update_fields = ['last_booking', 'businesses']
    non_generated_last_booking_keys = [
        'last_unfinished_booking',
        'last_unfinished_booking_data',
        'last_booking_reactivation',
    ]

    # foreign key
    user_id: int
    user: User = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        primary_key=True,
        related_name='search_tuning',
    )
    last_booking = JSONField(default=dict, blank=True, null=True, encoder=ESJSONEncoder)
    categories = JSONField(default=list, blank=True)
    categories_utt = JSONField(default=list, blank=True)
    treatments = JSONField(default=list, blank=True)
    treatments_utt = JSONField(default=list, blank=True)
    businesses = JSONField(default=list, blank=True, null=True)

    @classmethod
    def update_multiple_tunings(
        cls,
        ids: set_t.IdList,
        fields: set_t.FieldsList = None,
        update_es: bool = True,
        dry_run: bool = False,
        **kwargs,
    ) -> None:
        from webapps.user.elasticsearch.user import UserDocument

        user_ids, user_tunings = cls._get_or_create(ids)

        # region batch calculated values
        categories, categories_utt, treatments, treatments_utt = [None] * 4
        if fields is None or {'categories', 'treatments'} & set(fields):
            user_genders = dict(
                User.objects.filter(
                    id__in=user_ids,
                ).values_list('id', 'gender')
            )

            (categories, categories_utt, treatments, treatments_utt) = UserInterestCalculator(
                user_ids, user_genders
            ).calculate()

        last_booking_data = (
            cls._get_last_bookings_data(user_ids)
            if (fields is None or 'last_booking' in fields)
            else None
        )
        businesses = (
            cls._get_bookmarked_businesses(user_ids)
            if (fields is None or 'businesses' in fields)
            else None
        )
        # endregion batch calculated values

        # update in db
        for tuning in user_tunings:
            cls._calculate_parameters(
                tuning,
                fields,
                dry_run=dry_run,
                categories=categories,
                treatments=treatments,
                categories_utt=categories_utt,
                treatments_utt=treatments_utt,
                last_booking_data=last_booking_data,
                businesses=businesses,
            )

        if update_es and not dry_run:
            UserDocument.reindex(user_ids)

    @staticmethod
    def __sorted_by_key_id(list_):
        return sorted(list_, key=lambda obj: obj['id'] or -1)

    # region UPDATE METHODS
    def _update_categories(self, categories: set_t.UserData, **__) -> None:
        self.categories = self.__sorted_by_key_id(categories[self.user_id])

    def _update_treatments(self, treatments: set_t.UserData, **__) -> None:
        self.treatments = self.__sorted_by_key_id(treatments[self.user_id])

    def _update_categories_utt(self, categories_utt: set_t.UserData, **__) -> None:
        self.categories_utt = self.__sorted_by_key_id(categories_utt[self.user_id])

    def _update_treatments_utt(self, treatments_utt: set_t.UserData, **__) -> None:
        self.treatments_utt = self.__sorted_by_key_id(treatments_utt[self.user_id])

    # region last_booking
    @staticmethod
    def _get_last_bookings_data(user_ids: set_t.IdList) -> set_t.UserData:
        """Creates dict of users last location."""
        if user_ids is not None:
            filters = {'user_id__in': user_ids}
        else:
            filters = {'user__isnull': False}

        bci_ids = BusinessCustomerInfo.objects.filter(**filters).values_list('id', flat=True)
        last_booking_query = (
            Appointment.objects.filter(
                business__latitude__isnull=False,
                business__longitude__isnull=False,
                booked_for_id__in=Subquery(bci_ids),
            )
            .annotate(
                user_id=F('booked_for__user__id'),
            )
            .values('user_id')
            .annotate(
                last_booking=Max('created'),
            )
        )
        booking_location = Appointment.objects.filter(
            booked_for_id__in=Subquery(bci_ids),
        ).values_list('id', 'created', 'booked_for__user')
        booking_location = {
            (created, user_id): booking_id for booking_id, created, user_id in booking_location
        }
        bookind_ids = []
        for elem in last_booking_query:
            key = (elem['last_booking'], elem['user_id'])
            if key in booking_location:
                bookind_ids.append(booking_location[key])

        last_user_location = Appointment.objects.filter(id__in=bookind_ids).values_list(
            'booked_for__user__id', 'created', 'business__latitude', 'business__longitude'
        )

        return {
            user_id: {'location': {'lat': lat, 'lon': lon}, 'date': created}
            for user_id, created, lat, lon in last_user_location
        }

    # endregion last_booking

    def _update_last_booking(self, last_booking_data: set_t.UserData, **_) -> None:
        old_fields = {
            key: val
            for key, val in self.last_booking.items()
            if key in self.non_generated_last_booking_keys
        }
        self.last_booking = last_booking_data.get(self.user_id, {})
        self.last_booking.update(old_fields)

    # region businesses
    @staticmethod
    def _get_bookmarked_businesses(user_ids: set_t.IdList) -> set_t.UserData:
        """Prefetch finds businesses related to user."""
        from webapps.business.models import Resource

        # bookmarked businesses
        bookmarked_businesses_ids = (
            BusinessCustomerInfo.objects.filter(
                user_id__in=user_ids, bookmarked=True, business__active=True, business__visible=True
            )
            .order_by('-updated')
            .values_list('business_id', 'user_id')
        )
        user_bookmarked_businesses = defaultdict(list)
        for business_id, user_id in bookmarked_businesses_ids:
            user_bookmarked_businesses[user_id].append(business_id)

        # bookmarked staffers
        bookmarked_staffers_ids = (
            Resource.objects.filter(
                bookmarking_customers__user__in=user_ids,
                business__active=True,
                business__visible=True,
                visible=True,
                active=True,
                type=Resource.STAFF,
            )
            .order_by('-created')
            .values_list('bookmarking_customers__user', 'business', 'id')
        )
        user_bookmarked_staffers = defaultdict(lambda: defaultdict(list))
        for user_id, business_id, staffer_id in bookmarked_staffers_ids:
            user_bookmarked_staffers[user_id][business_id].append(staffer_id)

        # creating response
        businesses = defaultdict(
            lambda: defaultdict(lambda: {'bookmarked': False, 'bookmarked_staffers': []})
        )
        for user_id in user_ids:
            for business_id in user_bookmarked_businesses[user_id]:
                businesses[user_id][business_id]['bookmarked'] = True
            for business_id, staffers in user_bookmarked_staffers[user_id].items():
                businesses[user_id][business_id]['bookmarked_staffers'] = staffers or []
        return {
            user_id: [
                dict(id=_id, **business)  # pylint: disable=use-dict-literal
                for _id, business in user_businesses.items()
            ]
            for user_id, user_businesses in businesses.items()
        }

    # endregion businesses

    def _update_businesses(self, businesses: set_t.UserData, **__) -> None:
        self.businesses = businesses.get(self.user_id, [])

    # endregion UPDATE METHODS

    def selected_categories(self) -> t.List[int]:
        return self._select_best_ids(self.categories, 'score')

    def selected_treatments(self) -> t.List[int]:
        return self._select_best_ids(self.treatments, 'bookings_count')

    def selected_categories_utt(self) -> t.List[int]:
        return self._select_best_ids(self.categories_utt, 'score')

    def selected_treatments_utt(self) -> t.List[int]:
        return self._select_best_ids(self.treatments_utt, 'bookings_count')

    def _select_best_ids(self, iterator: t.Iterator[t.Dict[str, t.Any]], key: str) -> t.List[int]:
        selected = []
        try:
            selected = list(
                map(
                    itemgetter('id'),
                    sorted(
                        iterator,
                        key=itemgetter(key),
                        reverse=True,
                    ),
                )
            )[
                :2
            ]  # trimmed to 2 to fit number of galleries
        except KeyError:
            logger.info('User with id: ' '%s has malformed interest data.', self.user_id)
        except TypeError:
            # See 71804
            logger.exception(
                'User with id: ' '%s got unexpected type in interest data.', self.user_id
            )
        return selected


# region treatment
# TODO: Include to CategoryTuning when UTT2 will be fully operational
class CategoryMixin(TuningModel):
    class Meta:
        abstract = True

    @classmethod
    def get_x_bookings_ratio(cls) -> set_t.XBookings:
        gender_count = {gender: 0 for gender in Gender}
        category_count = {gender: {} for gender in Gender}
        for elem in cls.objects.all():
            for gender in gender_count:
                value = sum(elem.last_xbookings.get(gender, {}).values())
                category_count[gender][elem.related_model_id] = value
                gender_count[gender] += value
        for gender, total in gender_count.items():
            if total:
                category_count[gender] = {
                    category_id: count / total
                    for category_id, count in category_count[gender].items()
                }
        return category_count

    @classmethod
    @abstractmethod
    def update_multiple_tunings(
        cls,
        ids: set_t.IdList,
        fields: set_t.FieldsList = None,
        update_es: bool = True,
        dry_run: bool = False,
        **kwargs,
    ) -> None:
        pass


# TODO: Remove when UTT2 will be fully operational
class BusinessCategoryTuning(ArchiveModel, CategoryMixin):
    relation_field = 'business_category_id'
    LAST_DAYS = 90

    # foreign key
    business_category_id: int
    business_category = models.OneToOneField(
        BusinessCategory,
        on_delete=models.CASCADE,
        primary_key=True,
        related_name='search_tuning',
    )
    last_xbookings = JSONField(default=dict, blank=True, null=True, encoder=DjangoJSONEncoder)

    # region update model
    @classmethod
    def update_multiple_tunings(
        cls,
        ids: set_t.IdList,
        fields: set_t.FieldsList = None,
        update_es: bool = True,
        dry_run: bool = False,
        **kwargs,
    ) -> None:
        _bc_ids, bc_tunings = cls._get_or_create(ids)
        # update in db
        for tuning in bc_tunings:
            cls._calculate_parameters(tuning, fields, dry_run=dry_run)

    # endregion update model

    # region UPDATE METHODS
    def _update_last_xbookings(self, **_):
        latest_bookings = Appointment.objects.filter(
            booked_for__user__gender__isnull=False,
            created__gte=(tznow() - timedelta(days=self.LAST_DAYS)),
        )

        latest_category_bookings_user_ids = (
            latest_bookings.filter(
                business__primary_category_id=self.business_category_id,
                booked_for__user__gender__isnull=False,
            )
            .values_list('booked_for__user_id', flat=True)
            .distinct()
        )

        other_categories_bookings = (
            latest_bookings.filter(
                ~Q(business__primary_category_id=self.business_category_id),
                booked_for__user_id__in=Subquery(
                    latest_category_bookings_user_ids,
                ),
            )
            .values_list(
                'booked_for__user__gender',
                'business__primary_category_id',
            )
            .annotate(count=Count(['booked_for__user__gender', 'business__primary_category_id']))
        )

        category_count = defaultdict(dict)
        for gender, booking_category, count in other_categories_bookings:
            category_count[gender][booking_category] = count
        self.last_xbookings = dict(category_count)

    # endregion UPDATE METHODS


class CategoryTuning(ArchiveModel, CategoryMixin):
    relation_field = 'category_id'
    LAST_DAYS = 90

    # foreign key
    category_id: int
    category = models.OneToOneField(
        Category,
        on_delete=models.DO_NOTHING,
        primary_key=True,
        related_name='search_tuning',
    )
    last_xbookings = JSONField(default=dict, blank=True, null=True, encoder=DjangoJSONEncoder)

    # region update model
    @classmethod
    def update_multiple_tunings(
        cls,
        ids: set_t.IdList,
        fields: set_t.FieldsList = None,
        update_es: bool = True,
        dry_run: bool = False,
        **kwargs,
    ) -> None:
        _bc_ids, bc_tunings = cls._get_or_create(ids)
        # update in db
        for tuning in bc_tunings:
            cls._calculate_parameters(tuning, fields, dry_run=dry_run)

    # endregion update model

    # region UPDATE METHODS
    def _update_last_xbookings(self, **_):
        latest_bookings = Appointment.objects.filter(
            booked_for__user__gender__isnull=False,
            created__gte=(tznow() - timedelta(days=self.LAST_DAYS)),
        )

        latest_category_bookings_user_ids = (
            latest_bookings.filter(
                business__primary_category__category_utt_id=self.category_id,
                booked_for__user__gender__isnull=False,
            )
            .values_list('booked_for__user_id', flat=True)
            .distinct()
        )

        other_categories_bookings = (
            latest_bookings.filter(
                ~Q(business__primary_category__category_utt_id=self.category_id),
                booked_for__user_id__in=Subquery(
                    latest_category_bookings_user_ids,
                ),
            )
            .values_list(
                'booked_for__user__gender',
                'business__primary_category__category_utt_id',
            )
            .annotate(
                count=Count(
                    ['booked_for__user__gender', 'business__primary_category__category_utt_id']
                )
            )
        )

        category_count = defaultdict(dict)
        for gender, booking_category, count in other_categories_bookings:
            category_count[gender][booking_category] = count
        self.last_xbookings = dict(category_count)

    # endregion UPDATE METHODS


# endregion treatment


class BusinessCustomerTuning(ArchiveModel, TuningModel):
    relation_field = 'customer_id'

    customer = models.OneToOneField(
        BusinessCustomerInfo,
        on_delete=models.CASCADE,
        primary_key=True,
        related_name='search_tuning',
    )
    popular_items = JSONField(default=list, blank=True)
    any_mobile_customer_appointments = models.BooleanField(default=False)

    def _get_popular_addons(self):
        return (
            TransactionRow.objects.filter(
                addon_use__isnull=False,
                addon_use__service_addon__deleted__isnull=True,
                transaction__customer_card=self.customer,
            )
            .values(
                'addon_use__service_addon_id',
            )
            .annotate(
                popularity=Count('id'),
            )
            .values(
                'popularity',
                object_id=F('addon_use__service_addon_id'),
                object_type=Value('addon', output_field=CharField()),
            )
            .order_by('-popularity')
        )

    def _get_popular_service_variants(self):
        return (
            TransactionRow.objects.filter(
                service_variant__active=True,
                transaction__customer_card=self.customer,
            )
            .values(
                'service_variant_id',
            )
            .annotate(
                popularity=Count('id'),
            )
            .values(
                'popularity',
                object_id=F('service_variant_id'),
                object_type=Value('service-variant', output_field=CharField()),
            )
            .order_by('-popularity')
        )

    def _get_popular_services(self):
        return (
            TransactionRow.objects.filter(
                service_variant__service__active=True,
                transaction__customer_card=self.customer,
            )
            .values(
                'service_variant__service_id',
            )
            .annotate(
                popularity=Count('id'),
            )
            .values(
                'popularity',
                object_id=F('service_variant__service_id'),
                object_type=Value('service', output_field=CharField()),
            )
            .order_by('-popularity')
        )

    def _get_popular_commodities(self):
        return (
            TransactionRow.objects.filter(
                product__archived=False,
                transaction__customer_card=self.customer,
            )
            .values(
                'product_id',
            )
            .annotate(
                popularity=Count('id'),
            )
            .values(
                'popularity',
                object_id=F('product_id'),
                object_type=Value('product', output_field=CharField()),
            )
            .order_by('-popularity')
        )

    def _update_popular_items(self, **_):
        querysets = [
            self._get_popular_service_variants()[:50],
            self._get_popular_services()[:50],
            self._get_popular_commodities()[:50],
            self._get_popular_addons()[:50],
        ]
        queryset = querysets.pop(0)
        while querysets:
            queryset = queryset.union(querysets.pop(0))

        self.popular_items = list(queryset.order_by('-popularity'))

    def _update_any_mobile_customer_appointments(self, skip_if_true=True, **_):
        if skip_if_true and self.any_mobile_customer_appointments:
            return
        self.any_mobile_customer_appointments = bool(
            Appointment.objects.filter(
                business_id=self.customer.business_id,
                type=Appointment.TYPE.CUSTOMER,
                booked_for_id=self.customer_id,
                deleted__isnull=True,
                status__in=[
                    Appointment.STATUS.ACCEPTED,
                    Appointment.STATUS.FINISHED,
                ],
                source__name__in=consts.MOBILE_CUSTOMER,
                family_and_friends__isnull=True,
            ).exists()
        )

    @classmethod
    def update_any_mobile_customer_appointments(
        cls,
        customer_id: int,
        newest_appontment: Appointment = None,
        skip_if_true=True,
    ):
        _customer_ids, customer_tunings = cls._get_or_create([customer_id])
        if not customer_tunings:
            return
        tuning = customer_tunings[0]
        if skip_if_true and tuning.any_mobile_customer_appointments:
            return
        # update in db
        expected_statuses = [
            Appointment.STATUS.ACCEPTED,
            Appointment.STATUS.FINISHED,
        ]
        if (
            newest_appontment
            and newest_appontment.booked_for_id == customer_id
            and newest_appontment.status in expected_statuses
            and newest_appontment.type == Appointment.TYPE.CUSTOMER
            and newest_appontment.source.name in consts.MOBILE_CUSTOMER
        ):
            if not newest_appontment.is_family_and_friends:
                any_mobile_customer_appointments = True
                tuning.any_mobile_customer_appointments = any_mobile_customer_appointments
        else:
            tuning._update_any_mobile_customer_appointments(  # pylint: disable=protected-access
                skip_if_true=skip_if_true,
            )
        tuning.save(update_fields=['any_mobile_customer_appointments'])

    @classmethod
    def update_multiple_tunings(
        cls,
        ids: set_t.IdList,
        fields: set_t.FieldsList = None,
        update_es: bool = True,
        dry_run: bool = False,
        **kwargs,
    ) -> None:
        _customer_ids, customer_tunings = cls._get_or_create(ids)

        # update in db
        for tuning in customer_tunings:
            cls._calculate_parameters(
                tuning,
                fields,
                dry_run=dry_run,
            )


class ConversionAnalysisReport(models.Model):
    created: datetime = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Created (UTC)',
    )
    all = models.IntegerField(default=0, validators=[MinValueValidator(0)])
    errors = models.IntegerField(default=0, validators=[MinValueValidator(0)])
    non_zero = models.IntegerField(default=0, validators=[MinValueValidator(0)])
    from_zero = models.IntegerField(
        default=0,
        validators=[MinValueValidator(0)],
    )
    to_zero = models.IntegerField(default=0, validators=[MinValueValidator(0)])
    average = models.FloatField(default=0.0)
    average_abs = models.FloatField(default=0.0)
    median = models.FloatField(default=0.0)
    median_abs = models.FloatField(default=0.0)

    def __repr__(self):
        return f'<{self.__class__.__name__}: business_id: {self.created.strftime("%Y-%m-%d")}>'

    def __str__(self):
        return f'{self.__class__.__name__} business_id:{self.created.strftime("%Y-%m-%d")}'


class CommodityTuning(ArchiveModel, TuningModel):
    relation_field = 'commodity_id'

    commodity = models.OneToOneField(
        Commodity,
        on_delete=models.CASCADE,
        primary_key=True,
        related_name='search_tuning',
    )
    popularity = models.FloatField(default=0.0)

    def _update_popularity(self, **_):
        now = tznow()
        data = self.commodity.transaction_rows.filter(
            deleted__isnull=True,
            created__gt=now - timedelta(hours=POPULARITY_MAX_DAYS),
        ).aggregate(
            count=Count('quantity'),
            date_from=Min('created'),
        )

        if not data['count'] or not data['date_from']:
            self.popularity = 0.0
        else:
            self.popularity = data['count'] / max(
                (now - data['date_from']).days, POPULARITY_MIN_DAYS
            )

    @classmethod
    def update_multiple_tunings(
        cls,
        ids: set_t.IdList,
        fields: set_t.FieldsList = None,
        update_es: bool = True,
        dry_run: bool = False,
        **kwargs,
    ) -> None:
        from webapps.pos.elasticsearch.commodities import CommodityDocument

        commodity_ids, commodity_tunings = cls._get_or_create(ids)

        # update in db
        for tuning in commodity_tunings:
            cls._calculate_parameters(tuning, fields, dry_run=dry_run)

        # update in es
        if update_es and not dry_run:
            CommodityDocument.bulk_update(
                ids=commodity_ids,
                updates=[tuning.to_es_update() for tuning in commodity_tunings],
                use_celery=False,
                verbose=True,
            )

    def to_es_update(self) -> Dict[str, Any]:
        return {
            'id': self.commodity.id,
            'business_id': self.commodity.business_id,
            'popularity': self.popularity,
        }


class ServiceAddOnTuning(ArchiveModel, TuningModel):
    relation_field = 'addon_id'

    addon = models.OneToOneField(
        ServiceAddOn,
        on_delete=models.CASCADE,
        primary_key=True,
        related_name='search_tuning',
    )
    popularity = models.FloatField(default=0.0)

    def _update_popularity(self, **_):
        now = tznow()
        data = TransactionRow.objects.filter(
            deleted__isnull=True,
            addon_use__service_addon__deleted__isnull=True,
            created__gt=now - timedelta(days=POPULARITY_MAX_DAYS),
            addon_use__service_addon=self.addon_id,
        ).aggregate(
            count=Count('id'),
            date_from=Min('created'),
        )

        if not data['count'] or not data['date_from']:
            self.popularity = 0.0
        else:
            self.popularity = data['count'] / max(
                (now - data['date_from']).days, POPULARITY_MIN_DAYS
            )

    @classmethod
    def update_multiple_tunings(
        cls,
        ids: set_t.IdList,
        fields: set_t.FieldsList = None,
        update_es: bool = True,
        dry_run: bool = False,
        **kwargs,
    ) -> None:
        from webapps.pos.elasticsearch.addons import ServiceAddOnDocument

        addon_ids, addon_tunings = cls._get_or_create(ids)

        # update in db
        for tuning in addon_tunings:
            cls._calculate_parameters(tuning, fields, dry_run=dry_run)

        # update in es
        if update_es and not dry_run:
            ServiceAddOnDocument.bulk_update(
                ids=addon_ids,
                updates=[tuning.to_es_update() for tuning in addon_tunings],
                use_celery=False,
                verbose=True,
            )

    def to_es_update(self) -> Dict[str, Any]:
        return {
            'id': self.addon.id,
            'business_id': self.addon.business_id,
            'popularity': self.popularity,
        }


class ServiceTuning(ArchiveModel, TuningModel):
    relation_field = 'service_id'

    service = models.OneToOneField(
        Service,
        on_delete=models.CASCADE,
        primary_key=True,
        related_name='search_tuning',
    )
    popularity = models.FloatField(default=0.0)

    def _update_popularity(self, **_):
        now = tznow()
        data = TransactionRow.objects.filter(
            created__gt=now - timedelta(days=POPULARITY_MAX_DAYS),
            service_variant__service_id=self.service_id,
        ).aggregate(
            count=Count('id'),
            date_from=Min('created'),
        )

        if not data['count'] or not data['date_from']:
            self.popularity = 0.0
        else:
            self.popularity = data['count'] / max(
                (now - data['date_from']).days, POPULARITY_MIN_DAYS
            )

    @classmethod
    def update_multiple_tunings(
        cls,
        ids: set_t.IdList,
        fields: set_t.FieldsList = None,
        update_es: bool = True,
        dry_run: bool = False,
        **kwargs,
    ) -> None:
        from webapps.pos.elasticsearch.services import ServiceDocument

        service_ids, service_tunings = cls._get_or_create(ids)

        # update in db
        for tuning in service_tunings:
            cls._calculate_parameters(tuning, fields, dry_run=dry_run)

        # update in es
        if update_es and not dry_run:
            ServiceDocument.bulk_update(
                ids=service_ids,
                updates=[tuning.to_es_update() for tuning in service_tunings],
                use_celery=False,
                verbose=True,
            )

    def to_es_update(self) -> Dict[str, Any]:
        return {
            'id': self.service.id,
            'business_id': self.service.business_id,
            'popularity': self.popularity,
        }


class ServiceVariantTuning(ArchiveModel, TuningModel):
    relation_field = 'service_variant_id'

    service_variant = models.OneToOneField(
        ServiceVariant,
        on_delete=models.CASCADE,
        primary_key=True,
        related_name='search_tuning',
    )
    popularity = models.FloatField(default=0.0)

    def _update_popularity(self, **_):
        now = tznow()
        data = self.service_variant.transaction_rows.filter(
            deleted__isnull=True, created__gt=now - timedelta(days=POPULARITY_MAX_DAYS)
        ).aggregate(
            count=Count('id'),
            date_from=Min('created'),
        )

        if not data['count'] or not data['date_from']:
            self.popularity = 0.0
        else:
            self.popularity = data['count'] / max(
                (now - data['date_from']).days, POPULARITY_MIN_DAYS
            )

    @classmethod
    def update_multiple_tunings(
        cls,
        ids: set_t.IdList,
        fields: set_t.FieldsList = None,
        update_es: bool = True,
        dry_run: bool = False,
        **kwargs,
    ) -> None:
        from webapps.pos.elasticsearch.service_variants import (
            ServiceVariantDocument,
        )

        service_variant_ids, service_variant_tunings = cls._get_or_create(ids)

        # update in db
        for tuning in service_variant_tunings:
            cls._calculate_parameters(tuning, fields, dry_run=dry_run)

        # update in es
        if update_es and not dry_run:
            ServiceVariantDocument.bulk_update(
                ids=service_variant_ids,
                updates=[tuning.to_es_update() for tuning in service_variant_tunings],
                use_celery=False,
                verbose=True,
            )

    def to_es_update(self) -> Dict[str, Any]:
        return {
            'id': self.service_variant.id,
            'business_id': self.service_variant.service.business_id,
            'popularity': self.popularity,
        }
