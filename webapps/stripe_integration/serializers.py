from decimal import Decimal
from typing import Optional

from datetime import datetime, time, timedelta
from django.conf import settings
from django.core.validators import RegexValidator
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from drf_api.service.stripe_integration.enums import PayoutListMethodQueryType
from drf_api_utils.serializers import SchemaTypeSerializerMethod<PERSON>ield
from lib.feature_flag.feature.payment import FastPayoutTimeRestrictionFlag
from lib.serializers import RequiredContextMixin, MajorMinorCurrencyField
from lib.tools import major_unit
from webapps.booking.serializers.fields import BookingDateTimeField
from webapps.pos.models import Transaction, POS
from webapps.stripe_integration.enums import (
    FastPayoutStatus,
    StripeAccountStatus,
    StripePaymentIntentActions,
    StripeBalanceTransactionType,
    StripePayoutMethodType,
)
from webapps.stripe_integration.models import (
    StripeAccount,
    StripePayout,
    StripeBalanceTransaction,
)
from webapps.stripe_integration.provider import StripeProvider


class StripePayoutFeeSerializer(serializers.Serializer):
    total_amount = MajorMinorCurrencyField(read_only=True)
    fast_payout_provision = MajorMinorCurrencyField(read_only=True)
    fast_payout_txn_fee = MajorMinorCurrencyField(read_only=True)
    total_fee_amount = MajorMinorCurrencyField(read_only=True)
    total_payout_amount = MajorMinorCurrencyField(read_only=True)

    class Meta:
        fields = (
            'total_amount',
            'fast_payout_provision',
            'fast_payout_txn_fee',
            'total_fee_amount',
            'total_payout_amount',
        )


class StripeAccountSerializer(serializers.ModelSerializer):
    location = serializers.SerializerMethodField()
    status = serializers.CharField()
    onboarding_status = serializers.CharField()
    bcr_onboarding_status = serializers.CharField()
    can_be_deleted = serializers.SerializerMethodField()
    fast_payouts_status = serializers.CharField()
    closest_fast_payout_available_date = serializers.SerializerMethodField()

    class Meta:
        model = StripeAccount
        fields = (
            'status',
            'charges_enabled',
            'payouts_enabled',
            'location',
            'blocked',
            'onboarding_status',
            'bcr_onboarding_status',
            'kyc_verified_at_least_once',
            'can_be_deleted',
            'fast_payouts_status',
            'closest_fast_payout_available_date',
            'integration_mode',
        )

    @staticmethod
    def get_location(instance):
        location = StripeProvider.get_location(instance)
        if location:
            return location.external_id

    @staticmethod
    def get_can_be_deleted(instance):
        if not instance.can_be_safely_deleted:
            return False

        if instance.status in [
            StripeAccountStatus.VERIFICATION_PENDING,
            StripeAccountStatus.NOT_VERIFIED,
        ]:
            return False

        return True

    @staticmethod
    def get_closest_fast_payout_available_date(instance):
        if available_date := instance.pos.closest_fast_payout_available_date:
            return available_date.strftime(settings.DATE_FORMAT)

    @staticmethod
    def get_fast_payouts_merchant_enabled(instance):
        return instance.pos.fast_payout_enable_set_by_merchant

    @staticmethod
    def empty_stripe_account_data(fast_payout_visible):
        if fast_payout_visible and settings.POS__FAST_PAYOUTS:
            fast_payouts_status = FastPayoutStatus.MISSING_KYC
        else:
            fast_payouts_status = FastPayoutStatus.HIDDEN
        return {
            'status': StripeAccountStatus.TURNED_OFF,
            'onboarding_status': None,
            'bcr_onboarding_status': None,
            'blocked': False,
            'charges_enabled': False,
            'payouts_enabled': False,
            'location': None,
            'kyc_verified_at_least_once': False,
            'can_be_deleted': False,
            'fast_payouts_status': fast_payouts_status,
        }


class StripeReaderSerializer(serializers.Serializer):
    # this serializer is write only

    registration_code = serializers.CharField(max_length=32, required=True, write_only=True)
    label = serializers.CharField(required=True, write_only=True)

    def validate(self, attrs):
        location = StripeProvider.get_location(self.context['stripe_account'])
        if not location:
            raise serializers.ValidationError(_("This account is not verified!"))
        attrs['location_id'] = location.id
        attrs['external_location_id'] = location.external_id
        return super().validate(attrs)


class StripeReaderLabelSerializer(serializers.Serializer):
    # this serializer is write-only

    label = serializers.CharField(
        required=True,
        write_only=True,
        max_length=255,  # arbitrary, Stripe does not verify label's length
    )


class StripePaymentIntentSerializer(RequiredContextMixin, serializers.Serializer):
    transaction_id = serializers.IntegerField()
    terminal_identifier = serializers.CharField(
        max_length=255,
        allow_blank=True,
        allow_null=True,
        required=False,
    )
    terminal_device_type = serializers.CharField(
        max_length=255,
        allow_blank=True,
        allow_null=True,
        required=False,
    )

    required_context = ['pos']

    def validate(self, attrs):
        attrs = super().validate(attrs)

        try:
            transaction = Transaction.objects.get(
                id=attrs['transaction_id'], pos=self.context['pos']
            )
        except Transaction.DoesNotExist as e:
            raise serializers.ValidationError(
                {"transaction_id": "Transaction does not exist!"}
            ) from e

        attrs['transaction'] = transaction
        attrs['payment_row'] = StripeProvider.extract_stripe_payment_row(transaction)
        if not attrs['payment_row']:
            raise serializers.ValidationError('No stripe terminal payment in this transaction!')

        if not StripeProvider.has_valid_stripe_amount(attrs['payment_row']):
            raise serializers.ValidationError('Incorrect payment amount!')

        return attrs


class StripePaymentIntentActionSerializer(RequiredContextMixin, serializers.Serializer):
    action = serializers.ChoiceField(choices=StripePaymentIntentActions.choices())
    error_code = serializers.CharField(required=False)


class FastPayoutTriggerRequestSerializer(serializers.Serializer):
    alphanumeric = RegexValidator(
        r'^[0-9a-zA-Z!,.?/ ^@\[\]()#{}\-=\+\$&~]*$',
        # specific rules from StripeAPI https://stripe.com/docs/statement-descriptors
        r'Description can only contain English letters,'
        r' digits, a space and some punctuation (.,?!/()#-+^=][{}&~).',
    )
    amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    description = serializers.CharField(
        max_length=22, min_length=5, default=None, validators=[alphanumeric]
    )
    dry_run = serializers.BooleanField(default=False)

    def validate_amount(self, amount):
        account_holder: StripeAccount = self.context.get('account_holder')

        if amount > account_holder.fast_payout_max_amount:
            raise serializers.ValidationError(
                f'Payout amount is higher than limit: {account_holder.fast_payout_max_amount}'
            )
        if amount < account_holder.fast_payout_min_amount:
            raise serializers.ValidationError(
                f'Payout amount is lower than limit: {account_holder.fast_payout_min_amount}'
            )
        if amount > StripeProvider.get_instant_available(account_holder):
            raise serializers.ValidationError('Payout amount is higher than available balance')
        return amount

    def validate(self, attrs):
        if not settings.POS__FAST_PAYOUTS:
            raise serializers.ValidationError('Fast Payouts are not available in this country.')

        self.validate_is_fast_payout_available()
        return attrs

    @staticmethod
    def _is_current_hour_between(current_time, start_time, end_time):
        if start_time < end_time:
            return start_time <= current_time <= end_time

        return start_time <= current_time or current_time <= end_time

    @staticmethod
    def _get_minutes_to_end(current_time, end_time):
        current_datetime = datetime.combine(datetime.today(), current_time)
        end_datetime = datetime.combine(datetime.today(), end_time)

        if current_datetime > end_datetime:
            end_datetime += timedelta(days=1)

        return int((end_datetime - current_datetime).total_seconds() // 60)

    @staticmethod
    def _parse_time(time_str):
        hour, minute = map(int, time_str.split(':'))
        return time(hour, minute)

    def validate_is_fast_payout_available(self):
        pos: POS = self.context.get('pos')

        if time_restriction_data := FastPayoutTimeRestrictionFlag():
            start_time = self._parse_time(time_restriction_data.get('start_time'))
            end_time = self._parse_time(time_restriction_data.get('end_time'))
            current_time = datetime.utcnow().time()
            if self._is_current_hour_between(current_time, start_time, end_time):
                minutes_to_end = self._get_minutes_to_end(current_time, end_time)
                raise serializers.ValidationError(
                    _(
                        'Fast Payouts are temporarily unavailable. Please try again in {} minutes.'
                    ).format(minutes_to_end)
                )

        if not pos.new_fast_payouts_available_now:
            raise serializers.ValidationError(
                _(
                    'Next Fast Payout will be available after full settlement of the last Fast Payout'  # pylint: disable=line-too-long
                )
            )
        if not pos.fast_payouts_available_today:
            raise serializers.ValidationError(
                _('You have exceeded the daily limit of Fast Payouts. Try again tomorrow')
            )


class StripePayoutListItemSerializer(serializers.ModelSerializer):
    amount = SchemaTypeSerializerMethodField(schema_field=serializers.DecimalField)
    payout_date = BookingDateTimeField(source='payout_created')
    payout_id = serializers.IntegerField(source='id')
    method = SchemaTypeSerializerMethodField(schema_field=serializers.CharField)
    transaction_count = SchemaTypeSerializerMethodField(schema_field=serializers.IntegerField)

    class Meta:
        model = StripePayout
        fields = (
            'amount',
            'status',
            'method',
            'description',
            'payout_date',
            'payout_id',
            'transaction_count',
        )

    @staticmethod
    def get_amount(instance: StripePayout) -> Optional[Decimal]:
        if instance.method == StripePayoutMethodType.STANDARD:
            return major_unit(instance.amount + instance.application_fee)
        return (
            major_unit(instance.fast_payout_splits.get('total_amount') + instance.application_fee)
            if instance.fast_payout_splits is not None
            else None
        )

    @staticmethod
    def get_method(instance) -> PayoutListMethodQueryType:
        if instance.method == StripePayoutMethodType.INSTANT:
            return PayoutListMethodQueryType.FAST
        return instance.method

    @staticmethod
    def get_transaction_count(instance) -> int:
        return instance.balance_transactions.count()


class StripeCardSerializer(serializers.Serializer):
    last4 = serializers.CharField(max_length=4, allow_blank=True, required=True)
    brand = serializers.CharField(max_length=32, allow_blank=True, required=True)
    exp_month = serializers.IntegerField(required=True)
    exp_year = serializers.IntegerField(required=True)
    name = serializers.CharField(required=False, allow_blank=True, allow_null=True)


class StripeBankAccountSerializer(serializers.Serializer):
    last4 = serializers.CharField(max_length=4, allow_blank=True, required=True)
    account_holder_name = serializers.CharField(max_length=32, allow_blank=True, required=True)
    bank_name = serializers.CharField(max_length=32, allow_blank=True, required=True)
    routing_number = serializers.CharField(max_length=32, allow_blank=True, required=True)


class StripeCardOrBankDetailsSerializer(serializers.Serializer):
    card = StripeCardSerializer(required=False)
    bank_account = StripeBankAccountSerializer(required=False)


class StripeFastPayoutFeatureMerchantSetSerializer(serializers.Serializer):
    fast_payouts_merchant_enabled = serializers.BooleanField(required=True, allow_null=False)


class StripeBalanceAvailableSerializer(serializers.Serializer):
    instant_available = serializers.DecimalField(
        decimal_places=2,
        max_digits=10,
        required=True,
    )
    stripe_payout_due_date = BookingDateTimeField()


class StripeFastPayoutDetailSerializer(serializers.ModelSerializer):
    destination = SchemaTypeSerializerMethodField(schema_field=serializers.JSONField)
    payout_date = BookingDateTimeField(source='payout_created')
    fast_payout_splits = StripePayoutFeeSerializer()

    class Meta:
        model = StripePayout
        fields = (
            'status',
            'description',
            'destination',
            'payout_date',
            'fast_payout_splits',
        )

    @staticmethod
    def get_destination(instance: StripePayout) -> dict:
        if destination_json := instance.destination:
            serializer = StripeCardOrBankDetailsSerializer(
                data={destination_json.get('object'): destination_json}
            )
            if serializer.is_valid():
                return serializer.data


class StripeBalanceTransactionSerializer(serializers.ModelSerializer):
    amount = MajorMinorCurrencyField(read_only=True)
    created = BookingDateTimeField(source='transaction_created')

    class Meta:
        model = StripeBalanceTransaction
        fields = (
            'transaction_type',
            'created',
            'amount',
        )


class StripeStandardPayoutDetailSerializer(serializers.ModelSerializer):
    payout_value = serializers.SerializerMethodField()
    payout_total_value = serializers.SerializerMethodField()

    transactions_value = serializers.SerializerMethodField()
    refunds_value = serializers.SerializerMethodField()
    chargebacks_value = serializers.SerializerMethodField()
    payment_refunds_value = serializers.SerializerMethodField()
    commission_value = serializers.SerializerMethodField()

    destination = serializers.SerializerMethodField()
    transactions = StripeBalanceTransactionSerializer(source='balance_transactions', many=True)
    payout_date = BookingDateTimeField(source='payout_created')

    class Meta:
        model = StripePayout
        fields = (
            'status',
            'payout_value',
            'payout_total_value',
            'transactions_value',
            'refunds_value',
            'chargebacks_value',
            'payment_refunds_value',
            'commission_value',
            'destination',
            'transactions',
            'payout_date',
        )

    @staticmethod
    def get_payout_value(instance: StripePayout):
        return major_unit(instance.amount)

    @staticmethod
    def get_payout_total_value(instance: StripePayout):
        return major_unit(instance.amount + instance.application_fee)

    @staticmethod
    def get_destination(instance: StripePayout):
        if destination_json := instance.destination:
            serializer = StripeCardOrBankDetailsSerializer(
                data={destination_json.get('object'): destination_json}
            )
            if serializer.is_valid():
                return serializer.data

    @staticmethod
    def get_transactions_value(instance: StripePayout):
        return major_unit(
            instance.balance_transactions_breakdown.get(StripeBalanceTransactionType.PAYMENT) or 0
        )

    @staticmethod
    def get_refunds_value(instance: StripePayout):
        return major_unit(
            instance.balance_transactions_breakdown.get(StripeBalanceTransactionType.REFUND) or 0
        )

    @staticmethod
    def get_chargebacks_value(instance: StripePayout):
        return major_unit(
            instance.balance_transactions_breakdown.get(StripeBalanceTransactionType.CHARGE) or 0
        )

    @staticmethod
    def get_payment_refunds_value(instance: StripePayout):
        return major_unit(
            instance.balance_transactions_breakdown.get(StripeBalanceTransactionType.PAYMENT_REFUND)
            or 0
        )

    @staticmethod
    def get_commission_value(instance: StripePayout):
        return major_unit(
            instance.balance_transactions_breakdown.get(StripeBalanceTransactionType.TRANSFER) or 0
        )
