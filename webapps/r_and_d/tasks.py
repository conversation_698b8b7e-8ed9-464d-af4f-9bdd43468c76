import datetime
import logging
from typing import Any, Dict, List

from django.db.models import Q
from django.db.models.query import Prefetch

from lib.booksy_sms import send_sms
from lib.celery_tools import (
    celery_task,
)
from lib.db import using_db_for_reads, READ_ONLY_DB
from lib.deeplink import generate_deeplink
from lib.elasticsearch.consts import ESDocType
from lib.firebase import fb_api as fb
from lib.jinja_renderer import ScenariosJinjaRenderer
from lib.tools import tznow
from webapps.booking.models import Appointment
from webapps.booking.time_slot_tools import BookingRanges
from webapps.business import searchables
from webapps.business.business_categories.cache import TreatmentCache
from webapps.business.models import (
    Business,
    ServiceVariant,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business.searchables.business import BookingReactivationSearchable
from webapps.business.searchables.serializers import BusinessWithBoostSerializer
from webapps.kill_switch.models import Kill<PERSON><PERSON>
from webapps.notification.enums import DeeplinkFeature
from webapps.notification.models import NotificationHistory
from webapps.notification.scenarios.base import Scenario
from webapps.user.elasticsearch.tools import (
    append_to_user_search_data_fast_river,
)
from webapps.user.models import User

logger = logging.getLogger('booksy.r_and_d')


##########################################################
#### Churned merchant user reactivation Notification  ####
##########################################################


def get_businesses_for_churned_merchant_user_reactivation(businesses_ids=None, exclude_ids=None):
    if businesses_ids is None:
        kwargs = {
            'status__in': [
                Business.Status.BLOCKED_OVERDUE,
                Business.Status.TRIAL_BLOCKED,
                Business.Status.BLOCKED,
            ],
            # 'active': False
        }
    else:
        kwargs = {'id__in': businesses_ids}
    businesses = (
        Business.objects.filter(**kwargs)
        .exclude(
            id__in=exclude_ids or [],
        )
        .exclude(
            appointments__created__gte=tznow() - datetime.timedelta(days=30),
        )
        .prefetch_related(
            Prefetch(
                'business_customer_infos',
                queryset=BusinessCustomerInfo.objects.exclude(
                    client_type__in=[
                        BusinessCustomerInfo.CLIENT_TYPE__BUSINESS_DIRECTLY,
                    ],
                ),
                to_attr='customers',
            )
        )
    )
    return businesses


def search_businesses_for_churned_merchant_user_reactivation(
    business, median, distance_radius=3, reviews_count=2
):
    try:
        resp = (
            searchables.ReviewsSearchBusinessSearchable(
                ESDocType.BUSINESS,
            )
            .params(
                size=30,
            )
            .execute(
                dict(
                    category=[business.primary_category_id],
                    location_geo=dict(
                        lat=business.latitude,
                        lon=business.longitude,
                    ),
                    distance_radius=distance_radius,
                    distance_unit='km',
                    sort_order='distance',
                    gte_score=median,
                    gte_count=reviews_count,
                )
            )
        )
    except Exception as e:
        logger.exception(
            "business: {}, " "median: {}, " "msg_e: {}".format(business.id, median, e.message)
        )
        return []

    return [x.id for x in resp if x.id != business.id]


def get_props_businesses_for_churned_merchant_user_reactivation(business_ids):
    ret = []
    for b_id in business_ids:
        service_variants = ServiceVariant.objects.filter(
            service__active=True,
            active=True,
            service__business_id=b_id,
        )
        business = Business.objects.get(id=b_id)
        for sv in service_variants:
            br = BookingRanges(
                business=business,
                service_variant=sv,
                start=business.tznow,
                end=(business.tznow + datetime.timedelta(days=1)),
                slots_mode=True,
                ignore_invisible_staff=True,
            )
            if not br.time_slots()[0]:
                continue
            else:
                print('slots', br.time_slots()[0])
            ret.append(b_id)
            break
    return ret


@celery_task
@using_db_for_reads(READ_ONLY_DB)
def start_scenario_for_found_businesses(business_id, users_ids, props_businesses_ids):
    from webapps.notification.scenarios import start_scenario

    min_found_merchant_count = 2

    fb.save_churned_merchant_user_reactivation(business_id, users_ids, props_businesses_ids)

    if (
        not users_ids
        or not props_businesses_ids
        or len(props_businesses_ids) < min_found_merchant_count
    ):
        return

    logger.info(
        "For business id {} => users: {}, proposed businesses: {}".format(
            business_id, users_ids, props_businesses_ids
        )
    )

    start_scenario(
        'churned_merchant_user_reactivation',
        business_id=business_id,
        users_ids=users_ids,
        props_businesses_ids=props_businesses_ids,
    )


def already_send_businesses():
    return list(
        set(
            fb.get_churned_merchant_user_reactivation(only_business_ids=True)
            + fb.get_churned_merchant_businesses_processed(only_business_ids=True)
        )
    )


def get_users(business):
    bcis_ids = Appointment.objects.filter(
        created__gte=tznow() - datetime.timedelta(days=56),
        business__primary_category_id=business.primary_category_id,
    ).values_list('booked_for__id', flat=True)

    u_qs = (
        User.objects.filter(id__in=[u.user_id for u in business.customers if u.user_id])
        .exclude(
            business_customer_infos__in=bcis_ids,
        )
        .values_list('id', flat=True)
    )
    return list(u_qs)


@celery_task
@using_db_for_reads(READ_ONLY_DB)
def calculate_median_of_businesses():
    from webapps.business.models import Business

    fb.save_medians_of_businesses(Business.objects.filter(active=True))


@celery_task
@using_db_for_reads(READ_ONLY_DB)
def churned_merchant_user_reactivation(business_id, customers_ids, reviews_rank_score):
    business = Business.objects.get(id=business_id)
    business_ids = search_businesses_for_churned_merchant_user_reactivation(
        business, reviews_rank_score
    )

    props_businesses_ids = get_props_businesses_for_churned_merchant_user_reactivation(business_ids)
    start_scenario_for_found_businesses.delay(
        business_id,
        customers_ids,
        props_businesses_ids,
    )


#########################################################
############ High Volume Check Notification  ############
#########################################################


def check_if_user_email_or_phone_is_staffer(user_id):
    from webapps.business.models import Resource

    user = User.objects.get(id=user_id)
    if user.cell_phone is not None:
        qs = Resource.objects.filter(Q(staff_email=user.email) | Q(staff_email=user.cell_phone))
    else:
        qs = Resource.objects.filter(staff_email=user.email)
    if qs.exists():
        return True
    return False


def get_users_witch_have_push_in_period_of_time(days=14):
    from webapps.notification.models import NotificationHistory, UserNotification

    return (
        NotificationHistory.objects.filter(
            customer_id__isnull=False,
            created__gte=tznow() - datetime.timedelta(days=days),
            type=UserNotification.PUSH_NOTIFICATION,
        )
        .distinct('customer_id')
        .values_list('customer_id', flat=True)
    )


def already_send_high_volume_push_users():
    return fb.get_high_volume_users(only_ids=True) or []


def get_users_for_high_volume_push_notification():
    """1. Didn't make a booking in past 90d
    2. Didn't receive a push notification in 2 weeks.
    3. Didn't receive this notification."""

    customer_id_history = get_users_witch_have_push_in_period_of_time(days=14)
    ex_users = list(set(list(customer_id_history) + list(already_send_high_volume_push_users())))

    return (
        BusinessCustomerInfo.objects.filter(
            user__isnull=False,
            user__cell_phone__isnull=False,
            user__created__lte=(datetime.date.today() - datetime.timedelta(days=90)),
        )
        .exclude(
            Q(bookings__created__gte=(datetime.date.today() - datetime.timedelta(days=90)))
            | Q(bookings__booked_from__gte=(datetime.date.today() - datetime.timedelta(days=90))),
        )
        .exclude(user_id__in=ex_users)
        .distinct('user_id')
        .values_list('user_id', flat=True)
    )


@celery_task
@using_db_for_reads(READ_ONLY_DB)
def high_volume_start_scenario(user_id):
    from webapps.notification.scenarios import start_scenario

    if not check_if_user_email_or_phone_is_staffer(user_id=user_id):
        start_scenario(
            'high_volume_user_notification',
            user_id=user_id,
        )


#########################################################
########## Follow UP to big push Notification ###########
#########################################################


def already_send_follow_up_push_users():
    return fb.get_follow_up_users(only_ids=True) or []


@celery_task
@using_db_for_reads(READ_ONLY_DB)
def follow_up_start_scenario(user_id):
    from webapps.notification.scenarios import start_scenario

    if not check_if_user_email_or_phone_is_staffer(user_id=user_id):
        start_scenario(
            'follow_up_user_notification',
            user_id=user_id,
        )


def get_users_for_follow_up_push_notification():
    """1. Didn't make a booking in past 90d
    2. Didn't receive a push notification in 2 weeks.
    3. Did receive this notification."""
    from webapps.notification.models import NotificationHistory

    customer_id_history = get_users_witch_have_push_in_period_of_time(days=14)

    to_send = (
        NotificationHistory.objects.filter(
            customer_id__in=already_send_high_volume_push_users(),
            task_id__contains='high_volume_user_notification',
        )
        .distinct('customer_id')
        .values_list('customer_id', flat=True)
    )

    ex_users = list(set(list(customer_id_history) + list(already_send_follow_up_push_users())))

    return (
        User.objects.filter(cell_phone__isnull=False, id__in=to_send)
        .exclude(
            Q(
                business_customer_infos__bookings__created__gte=(
                    datetime.date.today() - datetime.timedelta(days=90)
                )
            )
            | Q(
                business_customer_infos__bookings__booked_from__gte=(
                    datetime.date.today() - datetime.timedelta(days=90)
                )
            ),
        )
        .exclude(id__in=ex_users)
        .distinct('id')
        .values_list('id', flat=True)
    )


def calcucalte_notification_send_date(pattern):
    cadence = pattern.cadence  # cadence in seconds
    cadence_in_days = cadence / (60 * 60 * 24)
    centroid = pattern.centroid
    timezone = pattern.business.get_timezone()
    if pattern.past_pattern:
        return None  # proposed booking in a past so no patterns for past bookings
    if cadence_in_days > 20:
        return get_cadence_notification_date(centroid, 7, timezone)
    elif cadence_in_days > 12:
        return get_cadence_notification_date(centroid, 5, timezone)
    elif cadence_in_days <= 12:
        return get_cadence_notification_date(centroid, 3, timezone)


def get_cadence_notification_date(centroid, days_before, timezone):
    calculated_day = centroid - datetime.timedelta(days=days_before)
    notification_time = datetime.datetime.combine(
        calculated_day,
        datetime.datetime.min.time(),
    )
    notification_time = notification_time.replace(
        hour=10,
        minute=30,
        tzinfo=timezone,
    )
    if notification_time < tznow():
        notification_time = tznow().replace(
            hour=10,
            minute=30,
            tzinfo=timezone,
        )
    return notification_time


# region booking reactivation
@celery_task
def check_booking_reactivation(
    user_id: int,
    service_variant_id: int,
    start_date: str,
    end_date: str,
):
    user = User.objects.get(id=user_id)
    if KillSwitch.killed(KillSwitch.System.BOOKINGS_REACTIVATION):
        return
    service_variant = ServiceVariant.objects.get(id=service_variant_id)

    business = service_variant.service.business

    try:
        start_date = datetime.date.fromisoformat(start_date)
        end_date = datetime.date.fromisoformat(end_date)
    except ValueError:
        return

    today = business.tznow.replace(hour=0, minute=0, second=0, microsecond=0)
    if not (start_date <= today.date() <= end_date):
        return

    treatment = service_variant.service.treatment
    if treatment is None:
        return

    search_data = dict(
        treatment=[treatment.id],
        location_geo=dict(
            lat=business.latitude,
            lon=business.longitude,
        ),
        available_for={
            'date_from': today,
            'date_to': today + datetime.timedelta(days=2),
            'time_from': None,
            'time_to': None,
        },
    )

    res = (
        BookingReactivationSearchable(ESDocType.BUSINESS, serializer=BusinessWithBoostSerializer())
        .params(
            size=10,
        )
        .search(search_data)
        .execute()
    )

    if res.hits.total.value and any(getattr(biz, 'promotion_boost', 0) > 0 for biz in res.hits):
        reactivate_booking(user, search_data)


def reactivate_booking(
    user: User,
    search_data: Dict[str, Any],
) -> None:
    user_tuning = user.get_search_tuning()
    utc_now = tznow()

    user_tuning.set_fields(
        'last_booking',
        dict(
            last_unfinished_booking=utc_now,
            last_unfinished_booking_data=search_data,
        ),
    )
    append_to_user_search_data_fast_river(user)


@celery_task
def reactivate_booking_sms_task(**kwargs) -> None:
    reactivate_booking_sms(**kwargs)


def reactivate_booking_sms(
    user_id: int,
    business_id: int,
    service_variant_id: int,
    date: str,
    business_ids: List[int],
    treatment_id: int,
) -> None:
    user = User.objects.get(id=user_id)
    user_tuning = user.search_tuning

    last_booking_date = user_tuning.get_datetime_field('last_booking', 'date')
    last_unfinished_booking = user_tuning.get_datetime_field(
        'last_booking', 'last_unfinished_booking'
    )
    if (
        KillSwitch.alive(KillSwitch.System.BOOKINGS_REACTIVATION)
        and last_booking_date is None
        or (last_unfinished_booking is None or last_booking_date < last_unfinished_booking)
    ):
        user_tuning.set_field('last_booking', 'last_booking_reactivation', tznow())
        treatment_name = TreatmentCache.get_by_id(treatment_id)['full_name']

        waitlist_deeplink = generate_deeplink(
            'C',
            dict(
                mobile_deeplink=(f'waitlist_join/{business_id}/{service_variant_id}/-1/{date}'),
                feature=DeeplinkFeature.WAITLIST_JOIN,
            ),
        )

        other_businesses_deeplink = generate_deeplink(
            'C',
            dict(
                mobile_deeplink=(f"show_businesses/{'/'.join(map(str, business_ids))}"),
                feature=DeeplinkFeature.SHOW_BUSINESSES,
            ),
        )

        send_sms(
            to=user.cell_phone,
            message=ScenariosJinjaRenderer().render(
                scenario_name='booking_reactivation',
                template_name='post_message',
                extension='sms',
                language=Scenario.get_language(user, None),
                template_args=dict(
                    treatment_name=treatment_name,
                    waitlist_deeplink=waitlist_deeplink,
                    other_businesses_deeplink=other_businesses_deeplink,
                ),
            ),
            history_data=dict(
                customer_id=user_id,
                business_id=business_id,
                parameters=dict(
                    service_variant_id=service_variant_id,
                    date=date,
                ),
                sender=NotificationHistory.SENDER_SYSTEM,
                task_type=(NotificationHistory.TASK_TYPE__SMS_BOOKING_REACTIVATION),
            ),
        )


# endregion booking reactivation
