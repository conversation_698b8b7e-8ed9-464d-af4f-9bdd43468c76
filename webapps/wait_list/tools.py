import datetime
from bisect import bisect
from itertools import groupby
from django.conf import settings
from django.db.models import Exists, OuterRef
from django.utils.functional import cached_property
from django.utils.translation import gettext as _

from lib.booksy_sms import get_reasonable_send_datetime, send_sms
from lib.deeplink import generate_deeplink
from lib.feature_flag.bug import AddChannelsParametersToDeeplink
from lib.time_24_hour import format_datetime
from lib.tools import tznow, switch_locale
from webapps.booking.models import SubBooking
from webapps.booking.time_slot_tools import BookingRanges
from webapps.business.models import (
    Business,
    ServiceVariant,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.notification.base import Channel
from webapps.notification.enums import DeeplinkFeature
from webapps.notification.models import NotificationHistory, UserNotification
from webapps.schedule.ports import get_opening_hours
from webapps.wait_list.models import WaitList, WaitListDisabled
from webapps.user.models import UserProfile


class WaitListManager:
    """Wait List Business Logic"""

    # keys for self.result i.e. for logging
    SERVICES = 'service_dates'
    ANY_TODAY_OMITTED = 'any_today_limit_omitted_users'
    QUERY_TODAY_OMITTED = 'query_today_limit_omitted_users'
    QUERY_EVER_OMITTED = 'query_ever_limit_omitted_users'
    NOTIFIED = 'notified_users'

    # NotificationHistory task id
    WAITLIST_TASK_ID = 'waitlist'

    DEEPLINK = 'waitlist_notification/{business_id}/{service_variant_id}/{staffer_id}/{date}'

    def __init__(self, business_id=None, business=None):
        if business:
            self.business = business
            self.business_id = business.id
        else:
            self.business_id = business_id
            self.business = Business.objects.get(pk=business_id)
        # log action result
        self.result = {
            self.ANY_TODAY_OMITTED: set(),
            self.QUERY_TODAY_OMITTED: set(),
            self.QUERY_EVER_OMITTED: set(),
            self.SERVICES: [],
            self.NOTIFIED: set(),
        }

    def execute_queries(self, returning_customers):
        """Groups WaitList items by date and service variant,
        queries if there are free slots for given date/service variant
        and notifies users waiting for it.
        """
        self.returning_customers = returning_customers
        wl_with_dates = sorted(
            ((date, w.service_variant_id, w) for w in self.waitlists for date in self.dates_gen(w)),
            key=lambda x: x[:2],
        )
        queries = groupby(wl_with_dates, lambda x: x[:2])
        for (date, service_variant_id), query in queries:
            self.single_query(date, service_variant_id, [x[2] for x in query])
        return {k: list(v) for k, v in list(self.result.items())}

    def single_query(self, date, service_variant_id, waitlists):
        """Collect time slots single date/service_variant"""
        service_variant = self.service_variants.get(service_variant_id)

        # limit waitlist items to users which can be notified
        active_waitlists = [w for w in waitlists if self.can_notify_user(w, date)]
        if not (service_variant and active_waitlists):
            return

        self.result[self.SERVICES].append((date.strftime(settings.DATE_FORMAT), service_variant_id))

        tz = self.business.get_timezone()

        # "konieczne by nie powiadamiać w południe o slotach z poranka"
        start = max(self.now, datetime.datetime.combine(date, datetime.time(0, 0, tzinfo=tz)))
        end = datetime.datetime.combine(date, datetime.time(23, 59, tzinfo=tz))
        if end <= start:
            return

        booking_ranges = BookingRanges(
            start=start,
            end=end,
            service_variant=service_variant,
            business=self.business,
            ignore_invisible_staff=True,
            slots_mode=True,
        )
        _, staff_slots = booking_ranges.time_slots()

        if staff_slots:
            for waitlist in active_waitlists:
                self.maybe_notify(waitlist, date, staff_slots)

    def maybe_notify(self, waitlist, date, staff_slots):
        """Finds the first needed staffer with free slot
        and notifies user
        """
        staffers = waitlist.staffers or list(staff_slots.keys())
        for sid in staffers:
            if sid not in staff_slots:
                continue  # staffers with empty slots are not included
            # if staffer sid has slots
            if staff_slots[sid][0][1]:
                # it works because we have single date slots
                self.result[self.NOTIFIED].add(waitlist.user_id)

                language = (
                    waitlist.user.profiles.filter(
                        profile_type=UserProfile.Type.CUSTOMER,
                    )
                    .first()
                    .language
                )

                return self.notify(waitlist, date, language=language)

    @switch_locale
    def notify(self, waitlist, date, language=None):
        service = self.service_variants[waitlist.service_variant_id].service.name
        date_msg = format_datetime(date, 'date_ymd', language)

        link_staffers = ','.join(map(str, waitlist.staffers)) if waitlist.staffers else '-1'
        if AddChannelsParametersToDeeplink():
            link = generate_deeplink(
                'C',
                {
                    'mobile_deeplink': self.DEEPLINK.format(
                        business_id=self.business_id,
                        service_variant_id=waitlist.service_variant_id,
                        staffer_id=link_staffers,
                        date=date.strftime(settings.DATE_FORMAT),
                    ),
                    'feature': DeeplinkFeature.WAITLIST,
                    'channel': Channel.Type.SMS,
                },
            )
        else:
            link = generate_deeplink(
                'C',
                {
                    'mobile_deeplink': self.DEEPLINK.format(
                        business_id=self.business_id,
                        service_variant_id=waitlist.service_variant_id,
                        staffer_id=link_staffers,
                        date=date.strftime(settings.DATE_FORMAT),
                    ),
                    'feature': DeeplinkFeature.WAITLIST,
                },
            )

        staffer_id = len(waitlist.staffers) == 1 and waitlist.staffers[0]
        if staffer_id:
            staffer = waitlist.business.resources.filter(pk=staffer_id).only('name').first().name
            message = _(
                'Check out new availability for a {service} with {staffer} on {date}. {link}'
            ).format(service=service, staffer=staffer, date=date_msg, link=link)
        else:
            message = _('Check out new availability for a {service} on {date}. {link}').format(
                service=service, date=date_msg, link=link
            )

        history_data = self.get_history_data(waitlist, date)

        return send_sms(waitlist.user.cell_phone, message, history_data=history_data)

    def dates_gen(self, waitlist):
        if waitlist.n_days is not None:  # feature flag
            yield from adjacent_dates(
                selected_date=waitlist.date,
                date_exact=waitlist.date_exact,
                days_count=waitlist.n_days,
                working_dates=[
                    date_ for date_, hours in self.business_working_hours.items() if hours
                ],
                today=self.today,
            )

        if self.today <= waitlist.date:
            yield waitlist.date

        if not waitlist.date_exact:
            for i in range(1, 4):
                date = waitlist.date - datetime.timedelta(days=i)
                if date < self.today:
                    break
                if self.business_working_hours.get(date):
                    yield date
                    break

            for i in range(1, 4):
                date = waitlist.date + datetime.timedelta(days=i)
                if self.business_working_hours.get(date):
                    yield date
                    break

    @cached_property
    def now(self):
        return self.business.tznow

    @cached_property
    def today(self):
        return self.now.date()

    @property
    def business_disabled(self):
        return WaitListDisabled.objects.filter(business_id=self.business_id).exists()

    def get_send_eta(self):
        return get_reasonable_send_datetime(self.business.country_code)

    @cached_property
    def waitlists(self):

        if self.business_disabled:
            return None

        waitlists = WaitList.objects.annotate(
            returning_customer=Exists(
                BusinessCustomerInfo.objects.filter(
                    business=OuterRef('business'),
                    user=OuterRef('user'),
                )
            )
        ).filter(
            business=self.business,
            date__gte=self.today - datetime.timedelta(days=1),
            returning_customer=self.returning_customers,
        )

        result_waitlist = []
        to_delete = []

        tz = self.business.get_timezone()
        midnight = datetime.time(0, 0, tzinfo=tz)
        before_midnight = datetime.time(23, 59, tzinfo=tz)
        for waitlist in waitlists.iterator():
            opening_hours = get_opening_hours(
                business_id=self.business.id,
                dates=[waitlist.date + datetime.timedelta(days=x) for x in range(-5, 6)],
            )
            opening_hours = {
                rec['date']: rec['hours']
                for rec in opening_hours
                if rec['hours'] or rec['date'] == waitlist.date
            }
            dates = adjacent_dates(
                selected_date=waitlist.date,
                date_exact=waitlist.date_exact,
                days_count=waitlist.n_days,
                working_dates=[date_ for date_, hours in opening_hours.items()],
                today=self.today,
            )
            if not dates:
                dates = [waitlist.date]
            booked_from = datetime.datetime.combine(dates[0], midnight)
            booked_till = datetime.datetime.combine(dates[-1], before_midnight)
            # check user did not book this service_variant in meantime
            qs = SubBooking.objects.filter(
                appointment__booked_for__user_id=waitlist.user_id,
                booked_from__gte=booked_from,
                booked_till__lt=booked_till,
                service_variant_id=waitlist.service_variant_id,
            )
            if qs.exists():
                to_delete.append(waitlist.id)
            else:
                result_waitlist.append(waitlist)

        deleted = tznow().replace(minute=0, second=0, microsecond=0)
        WaitList.objects.filter(
            id__in=to_delete,
        ).update(deleted=deleted)

        return result_waitlist

    def _get_business_working_hours_dates(self):
        def near_dates(date):
            return [date + datetime.timedelta(days=x) for x in range(-5, 6)]

        return sorted(
            {
                d
                for w in self.waitlists
                for d in (near_dates(w.date) if not w.date_exact else [w.date])
            }
        )

    @cached_property
    def business_working_hours(self):
        opening_hours = get_opening_hours(
            business_id=self.business.id, dates=self._get_business_working_hours_dates()
        )
        return {rec['date']: rec['hours'] for rec in opening_hours}

    @cached_property
    def service_variants(self):
        return {
            v.id: v
            for v in ServiceVariant.objects.filter(
                active=True,
                service__active=True,
                service__business=self.business,
            )
        }

    def get_history_data(self, waitlist, date):
        """History data stored in NotificationHistory after sms send"""
        date_str = date.strftime(settings.DATE_FORMAT)
        return {
            'sender': NotificationHistory.SENDER_SYSTEM,
            'type': UserNotification.SMS_NOTIFICATION,
            'task_id': self.get_task_id(waitlist, date),
            'business_id': waitlist.business_id,
            'customer_id': waitlist.user_id,
            'meta_date': date_str,
        }

    def get_task_id(self, waitlist, date):
        return f'{self.WAITLIST_TASK_ID}:{waitlist.id}:{date.strftime(settings.DATE_FORMAT)}'

    def can_notify_user(self, waitlist, date):  # pylint: disable=too-many-return-statements
        """Checks notification limits"""
        if waitlist.user_id in self.result[self.ANY_TODAY_OMITTED]:
            return False

        date_str = date.strftime(settings.DATE_FORMAT)

        # all today sent notifications for user
        today_notifications = list(
            NotificationHistory.objects.filter(
                task_type=NotificationHistory.TASK_TYPE__SMS_WAIT_LIST,
                customer_id=waitlist.user_id,
                created__date=self.today,
            )
        )

        if len(today_notifications) >= settings.WAITLIST_ANY_TODAY_LIMIT:
            self.result[self.ANY_TODAY_OMITTED].add(waitlist.user_id)
            return False

        # today sent notifications for user limited to date and business
        if today_notifications:
            today_query_notifications = [
                h
                for h in today_notifications
                if h.business_id == self.business_id and h.task_id.endswith(date_str)
            ]
            if len(today_query_notifications) >= settings.WAITLIST_QUERY_TODAY_LIMIT:
                self.result[self.QUERY_TODAY_OMITTED].add(waitlist.user_id)
                return False

        # ever sent notification for user limited to date and business
        query_notifications_count = NotificationHistory.objects.filter(
            task_type=NotificationHistory.TASK_TYPE__SMS_WAIT_LIST,
            task_id__endswith=date_str,
            customer_id=waitlist.user_id,
        ).count()
        if query_notifications_count >= settings.WAITLIST_QUERY_EVER_LIMIT:
            self.result[self.QUERY_EVER_OMITTED].add(waitlist.user_id)
            return False

        return True


def adjacent_dates(
    selected_date,
    date_exact: bool,
    days_count: int | None,
    working_dates: list,
    today,
) -> list:
    """
    Create list of `days_count` dates before and after `selected_date`, including itself.
    Skip dates not in `working_dates`.
    Don't include dates `today` as well.

    If `days_count` is None, take for 0 if `date_exact` else 1
    """
    # backward compatibiliy
    if days_count is None:
        days_count = 0 if date_exact else 1

    if days_count == 0:
        return [selected_date] if selected_date >= today else []

    # make sure they are sorted
    dates = sorted(working_dates)
    # remove lower than today
    dates = [d_ for d_ in dates if d_ >= today]
    # insert selected_date at position preserving sorted
    if selected_date in dates:
        position = dates.index(selected_date)
    else:
        position = bisect(dates, selected_date)
        dates.insert(position, selected_date)

    return dates[max(0, position - days_count) : position + days_count + 1]
