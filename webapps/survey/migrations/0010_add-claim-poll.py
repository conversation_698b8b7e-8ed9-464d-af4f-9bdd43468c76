# Generated by Django 2.0.13 on 2020-04-30 10:07

from django.db import migrations, models
import django.db.models.deletion


def forwards_func(apps, schema_editor):
    db_alias = schema_editor.connection.alias
    Poll = apps.get_model('survey', 'Poll')
    PollChoice = apps.get_model('survey', 'PollChoice')

    poll = Poll(
        name='boost_claim',
        response_model_name='BoostClaimPollResponse',
        ordered=True,
    )
    poll.save(using=db_alias)

    choices = (
        ("Existing client", None, 1),
        ("Walk-in client", "Walked into my shop off the street", 2),
        ("Referral client", "Another client reffered this client", 3),
        ("Instagram", "Found me on Instagram", 4),
        ("Facebook", "Found me on Facebook", 5),
        ("Other", "Eg. Yelp, Groupon, etc", 6),
    )

    for choice, description, order in choices:
        PollChoice(
            choice=choice, description=description, order=order, poll=poll, visible=True
        ).save(using=db_alias)


def backwards_func(apps, schema_editor):
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('marketplace', '0101_merge_20200403_1200'),
        ('business', '0278_merge_20200427_1122'),
        ('survey', '0009_add_timestamp_fields'),
    ]

    operations = [
        migrations.CreateModel(
            name='BoostClaimPollResponse',
            fields=[
                (
                    'pollresponse_ptr',
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to='survey.PollResponse',
                    ),
                ),
                (
                    '_created',
                    models.DateTimeField(
                        auto_now_add=True, db_column='created', verbose_name='Created (UTC)'
                    ),
                ),
                (
                    'business',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name='boost_churn_poll_responses',
                        to='business.Business',
                    ),
                ),
                (
                    'transaction_row',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name='boost_churn_poll_responses',
                        to='marketplace.MarketplaceTransactionRow',
                    ),
                ),
            ],
            bases=('survey.pollresponse',),
        ),
        migrations.AddField(
            model_name='poll',
            name='ordered',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='pollchoice',
            name='description',
            field=models.CharField(blank=True, max_length=256, null=True),
        ),
        migrations.AddField(
            model_name='pollchoice',
            name='order',
            field=models.IntegerField(default=0),
        ),
        migrations.RunPython(forwards_func, reverse_code=backwards_func),
    ]
