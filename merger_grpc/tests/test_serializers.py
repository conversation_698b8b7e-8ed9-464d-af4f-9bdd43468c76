from datetime import date, datetime
from dateutil.tz import gettz
from django.utils.timezone import override

from merger_grpc.appointment_serializers import (
    AppointmentSerializer,
    TimeSlotsQueryRequestSerializer,
    TimeSlotsQueryResponseSerializer,
)

WAW = gettz('Europe/Warsaw')


def test_timeslots_request_serializer():
    request = dict(
        business_id=1,
        subbookings=[
            dict(service_variant_id=101, staffer_id=[50, 51]),
            dict(service_variant_id=101, staffer_id=[]),
        ],
        omit_appointment_import_id='10',
        start_date=date(2021, 3, 29),
        end_date=date(2021, 3, 30),
    )
    with override('Europe/Warsaw'):
        message = TimeSlotsQueryRequestSerializer(instance=request).message
        serializer = TimeSlotsQueryRequestSerializer(message=message)
        assert serializer.is_valid(), serializer.errors
        assert serializer.validated_data == request


def test_timeslots_response_serializer():
    response = dict(
        business_id=1,
        staffers_availability=[
            dict(
                staffer_id=20,
                slots=[
                    datetime(2021, 3, 29, 10, tzinfo=WAW),
                    datetime(2021, 3, 29, 11, tzinfo=WAW),
                    datetime(2021, 3, 29, 12, tzinfo=WAW),
                ],
            )
        ],
    )
    with override('Europe/Warsaw'):
        message = TimeSlotsQueryResponseSerializer(instance=response).message
        serializer = TimeSlotsQueryResponseSerializer(message=message)
        assert serializer.is_valid(), serializer.errors
        assert serializer.validated_data == response


def test_appointment_serializer():
    appointment = dict(
        id=1,
        business_id=100,
        status='A',
        customer_name='Customer',
        customer_phone='+11111111',
        customer_email='<EMAIL>',
        booked_for_id=101,
        subbookings=[
            dict(
                id=21,
                booked_from=datetime(2021, 3, 26, 12, tzinfo=WAW),
                booked_till=datetime(2021, 3, 26, 13, tzinfo=WAW),
                staffer_id=2021,
                appliance_id=2022,
                service_variant_id=500,
                _available_staffer_ids=[],
            )
        ],
        _from_subdomain=False,
    )
    with override('Europe/Warsaw'):
        message = AppointmentSerializer(instance=appointment).message
        serializer = AppointmentSerializer(message=message)
        assert serializer.is_valid(), serializer.errors
        assert serializer.validated_data == appointment
