# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from merger_grpc.proto import appointment_pb2 as merger__grpc_dot_proto_dot_appointment__pb2


class AppointmentStub(object):
    """Appointment related procedures
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.TimeSlotsQuery = channel.unary_unary(
                '/appointment.Appointment/TimeSlotsQuery',
                request_serializer=merger__grpc_dot_proto_dot_appointment__pb2.TimeSlotsQueryRequest.SerializeToString,
                response_deserializer=merger__grpc_dot_proto_dot_appointment__pb2.TimeSlotsQueryResponse.FromString,
                )
        self.DryRunAppointment = channel.unary_unary(
                '/appointment.Appointment/DryRunAppointment',
                request_serializer=merger__grpc_dot_proto_dot_appointment__pb2.AppointmentDetails.SerializeToString,
                response_deserializer=merger__grpc_dot_proto_dot_appointment__pb2.AppointmentDetails.FromString,
                )
        self.SaveAppointment = channel.unary_unary(
                '/appointment.Appointment/SaveAppointment',
                request_serializer=merger__grpc_dot_proto_dot_appointment__pb2.AppointmentDetails.SerializeToString,
                response_deserializer=merger__grpc_dot_proto_dot_appointment__pb2.AppointmentDetails.FromString,
                )
        self.UpdateAppointment = channel.unary_unary(
                '/appointment.Appointment/UpdateAppointment',
                request_serializer=merger__grpc_dot_proto_dot_appointment__pb2.AppointmentUpdate.SerializeToString,
                response_deserializer=merger__grpc_dot_proto_dot_appointment__pb2.AppointmentUpdate.FromString,
                )
        self.ScheduleAppointmentsSync = channel.unary_unary(
                '/appointment.Appointment/ScheduleAppointmentsSync',
                request_serializer=merger__grpc_dot_proto_dot_appointment__pb2.AppointmentsSyncRequest.SerializeToString,
                response_deserializer=merger__grpc_dot_proto_dot_appointment__pb2.AppointmentsSyncResponse.FromString,
                )


class AppointmentServicer(object):
    """Appointment related procedures
    """

    def TimeSlotsQuery(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DryRunAppointment(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SaveAppointment(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateAppointment(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ScheduleAppointmentsSync(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_AppointmentServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'TimeSlotsQuery': grpc.unary_unary_rpc_method_handler(
                    servicer.TimeSlotsQuery,
                    request_deserializer=merger__grpc_dot_proto_dot_appointment__pb2.TimeSlotsQueryRequest.FromString,
                    response_serializer=merger__grpc_dot_proto_dot_appointment__pb2.TimeSlotsQueryResponse.SerializeToString,
            ),
            'DryRunAppointment': grpc.unary_unary_rpc_method_handler(
                    servicer.DryRunAppointment,
                    request_deserializer=merger__grpc_dot_proto_dot_appointment__pb2.AppointmentDetails.FromString,
                    response_serializer=merger__grpc_dot_proto_dot_appointment__pb2.AppointmentDetails.SerializeToString,
            ),
            'SaveAppointment': grpc.unary_unary_rpc_method_handler(
                    servicer.SaveAppointment,
                    request_deserializer=merger__grpc_dot_proto_dot_appointment__pb2.AppointmentDetails.FromString,
                    response_serializer=merger__grpc_dot_proto_dot_appointment__pb2.AppointmentDetails.SerializeToString,
            ),
            'UpdateAppointment': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateAppointment,
                    request_deserializer=merger__grpc_dot_proto_dot_appointment__pb2.AppointmentUpdate.FromString,
                    response_serializer=merger__grpc_dot_proto_dot_appointment__pb2.AppointmentUpdate.SerializeToString,
            ),
            'ScheduleAppointmentsSync': grpc.unary_unary_rpc_method_handler(
                    servicer.ScheduleAppointmentsSync,
                    request_deserializer=merger__grpc_dot_proto_dot_appointment__pb2.AppointmentsSyncRequest.FromString,
                    response_serializer=merger__grpc_dot_proto_dot_appointment__pb2.AppointmentsSyncResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'appointment.Appointment', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class Appointment(object):
    """Appointment related procedures
    """

    @staticmethod
    def TimeSlotsQuery(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/appointment.Appointment/TimeSlotsQuery',
            merger__grpc_dot_proto_dot_appointment__pb2.TimeSlotsQueryRequest.SerializeToString,
            merger__grpc_dot_proto_dot_appointment__pb2.TimeSlotsQueryResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DryRunAppointment(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/appointment.Appointment/DryRunAppointment',
            merger__grpc_dot_proto_dot_appointment__pb2.AppointmentDetails.SerializeToString,
            merger__grpc_dot_proto_dot_appointment__pb2.AppointmentDetails.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SaveAppointment(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/appointment.Appointment/SaveAppointment',
            merger__grpc_dot_proto_dot_appointment__pb2.AppointmentDetails.SerializeToString,
            merger__grpc_dot_proto_dot_appointment__pb2.AppointmentDetails.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def UpdateAppointment(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/appointment.Appointment/UpdateAppointment',
            merger__grpc_dot_proto_dot_appointment__pb2.AppointmentUpdate.SerializeToString,
            merger__grpc_dot_proto_dot_appointment__pb2.AppointmentUpdate.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ScheduleAppointmentsSync(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/appointment.Appointment/ScheduleAppointmentsSync',
            merger__grpc_dot_proto_dot_appointment__pb2.AppointmentsSyncRequest.SerializeToString,
            merger__grpc_dot_proto_dot_appointment__pb2.AppointmentsSyncResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
