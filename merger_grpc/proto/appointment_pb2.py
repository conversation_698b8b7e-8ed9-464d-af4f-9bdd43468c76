# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: merger_grpc/proto/appointment.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='merger_grpc/proto/appointment.proto',
  package='appointment',
  syntax='proto3',
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n#merger_grpc/proto/appointment.proto\x12\x0b\x61ppointment\"\x99\x02\n\x15TimeSlotsQueryRequest\x12\x13\n\x0b\x62usiness_id\x18\x01 \x01(\x05\x12\x42\n\x0bsubbookings\x18\x02 \x03(\x0b\x32-.appointment.TimeSlotsQueryRequest.SubBooking\x12$\n\x1aomit_appointment_import_id\x18\x03 \x01(\tH\x00\x12\x12\n\nstart_date\x18\x04 \x01(\x03\x12\x10\n\x08\x65nd_date\x18\x05 \x01(\x03\x1a<\n\nSubBooking\x12\x1a\n\x12service_variant_id\x18\x01 \x01(\x05\x12\x12\n\nstaffer_id\x18\x02 \x03(\x05\x42\x1d\n\x1b_omit_appointment_import_id\"\xbf\x01\n\x16TimeSlotsQueryResponse\x12\x13\n\x0b\x62usiness_id\x18\x01 \x01(\x05\x12V\n\x15staffers_availability\x18\x02 \x03(\x0b\x32\x37.appointment.TimeSlotsQueryResponse.StafferAvailability\x1a\x38\n\x13StafferAvailability\x12\x12\n\nstaffer_id\x18\x01 \x01(\x05\x12\r\n\x05slots\x18\x02 \x03(\x03\"\x92\x04\n\x12\x41ppointmentDetails\x12\x13\n\x0b\x62usiness_id\x18\x01 \x01(\x05\x12\x0c\n\x02id\x18\x02 \x01(\x05H\x00\x12\x13\n\timport_id\x18\x03 \x01(\tH\x01\x12\x10\n\x06status\x18\x04 \x01(\tH\x02\x12\x17\n\rcustomer_name\x18\x05 \x01(\tH\x03\x12\x18\n\x0e\x63ustomer_phone\x18\x06 \x01(\tH\x04\x12\x18\n\x0e\x63ustomer_email\x18\x07 \x01(\tH\x05\x12\x17\n\rbooked_for_id\x18\x08 \x01(\x05H\x06\x12,\n\x0bsubbookings\x18\t \x03(\x0b\x32\x17.appointment.SubBooking\x12\x19\n\x0f_from_subdomain\x18\n \x01(\x08H\x07\x12\x17\n\rcustomer_note\x18\x0b \x01(\tH\x08\x12\x44\n\x11_required_payment\x18\x0c \x01(\x0b\x32\'.appointment.AppointmentRequiredPaymentH\tB\x05\n\x03_idB\x0c\n\n_import_idB\t\n\x07_statusB\x10\n\x0e_customer_nameB\x11\n\x0f_customer_phoneB\x11\n\x0f_customer_emailB\x10\n\x0e_booked_for_idB\x12\n\x10__from_subdomainB\x10\n\x0e_customer_noteB\x14\n\x12__required_payment\"\xf9\x01\n\nSubBooking\x12\x0c\n\x02id\x18\x01 \x01(\x05H\x00\x12\x15\n\x0b\x62ooked_from\x18\x02 \x01(\x03H\x01\x12\x15\n\x0b\x62ooked_till\x18\x03 \x01(\x03H\x02\x12\x14\n\nstaffer_id\x18\x04 \x01(\x05H\x03\x12\x16\n\x0c\x61ppliance_id\x18\x05 \x01(\x05H\x04\x12\x1a\n\x12service_variant_id\x18\x06 \x01(\x05\x12\x1e\n\x16_available_staffer_ids\x18\x07 \x03(\x05\x42\x05\n\x03_idB\x0e\n\x0c_booked_fromB\x0e\n\x0c_booked_tillB\r\n\x0b_staffer_idB\x0f\n\r_appliance_id\"W\n\x1a\x41ppointmentRequiredPayment\x12\x12\n\nprepayment\x18\x01 \x01(\t\x12\x15\n\x0bpayment_url\x18\x02 \x01(\tH\x00\x42\x0e\n\x0c_payment_url\"\x8f\x01\n\x11\x41ppointmentUpdate\x12\x13\n\x0b\x62usiness_id\x18\x01 \x01(\x05\x12\n\n\x02id\x18\x02 \x01(\x05\x12\x11\n\timport_id\x18\x03 \x01(\t\x12\x10\n\x06status\x18\x04 \x01(\tH\x00\x12\x17\n\rcustomer_note\x18\x05 \x01(\tH\x01\x42\t\n\x07_statusB\x10\n\x0e_customer_note\"g\n\x17\x41ppointmentsSyncRequest\x12\x13\n\x0b\x62usiness_id\x18\x01 \x01(\x05\x12\x1a\n\x12\x62ooksy_customer_id\x18\x02 \x01(\x05\x12\x1b\n\x13partner_customer_id\x18\x03 \x01(\x05\"\x1a\n\x18\x41ppointmentsSyncResponse2\xdc\x03\n\x0b\x41ppointment\x12[\n\x0eTimeSlotsQuery\x12\".appointment.TimeSlotsQueryRequest\x1a#.appointment.TimeSlotsQueryResponse\"\x00\x12W\n\x11\x44ryRunAppointment\x12\x1f.appointment.AppointmentDetails\x1a\x1f.appointment.AppointmentDetails\"\x00\x12U\n\x0fSaveAppointment\x12\x1f.appointment.AppointmentDetails\x1a\x1f.appointment.AppointmentDetails\"\x00\x12U\n\x11UpdateAppointment\x12\x1e.appointment.AppointmentUpdate\x1a\x1e.appointment.AppointmentUpdate\"\x00\x12i\n\x18ScheduleAppointmentsSync\x12$.appointment.AppointmentsSyncRequest\x1a%.appointment.AppointmentsSyncResponse\"\x00\x62\x06proto3'
)




_TIMESLOTSQUERYREQUEST_SUBBOOKING = _descriptor.Descriptor(
  name='SubBooking',
  full_name='appointment.TimeSlotsQueryRequest.SubBooking',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='service_variant_id', full_name='appointment.TimeSlotsQueryRequest.SubBooking.service_variant_id', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='staffer_id', full_name='appointment.TimeSlotsQueryRequest.SubBooking.staffer_id', index=1,
      number=2, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=243,
  serialized_end=303,
)

_TIMESLOTSQUERYREQUEST = _descriptor.Descriptor(
  name='TimeSlotsQueryRequest',
  full_name='appointment.TimeSlotsQueryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='business_id', full_name='appointment.TimeSlotsQueryRequest.business_id', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='subbookings', full_name='appointment.TimeSlotsQueryRequest.subbookings', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='omit_appointment_import_id', full_name='appointment.TimeSlotsQueryRequest.omit_appointment_import_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='appointment.TimeSlotsQueryRequest.start_date', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='appointment.TimeSlotsQueryRequest.end_date', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_TIMESLOTSQUERYREQUEST_SUBBOOKING, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_omit_appointment_import_id', full_name='appointment.TimeSlotsQueryRequest._omit_appointment_import_id',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=53,
  serialized_end=334,
)


_TIMESLOTSQUERYRESPONSE_STAFFERAVAILABILITY = _descriptor.Descriptor(
  name='StafferAvailability',
  full_name='appointment.TimeSlotsQueryResponse.StafferAvailability',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='staffer_id', full_name='appointment.TimeSlotsQueryResponse.StafferAvailability.staffer_id', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='slots', full_name='appointment.TimeSlotsQueryResponse.StafferAvailability.slots', index=1,
      number=2, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=472,
  serialized_end=528,
)

_TIMESLOTSQUERYRESPONSE = _descriptor.Descriptor(
  name='TimeSlotsQueryResponse',
  full_name='appointment.TimeSlotsQueryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='business_id', full_name='appointment.TimeSlotsQueryResponse.business_id', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='staffers_availability', full_name='appointment.TimeSlotsQueryResponse.staffers_availability', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_TIMESLOTSQUERYRESPONSE_STAFFERAVAILABILITY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=337,
  serialized_end=528,
)


_APPOINTMENTDETAILS = _descriptor.Descriptor(
  name='AppointmentDetails',
  full_name='appointment.AppointmentDetails',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='business_id', full_name='appointment.AppointmentDetails.business_id', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='id', full_name='appointment.AppointmentDetails.id', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='import_id', full_name='appointment.AppointmentDetails.import_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='status', full_name='appointment.AppointmentDetails.status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='customer_name', full_name='appointment.AppointmentDetails.customer_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='customer_phone', full_name='appointment.AppointmentDetails.customer_phone', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='customer_email', full_name='appointment.AppointmentDetails.customer_email', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='booked_for_id', full_name='appointment.AppointmentDetails.booked_for_id', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='subbookings', full_name='appointment.AppointmentDetails.subbookings', index=8,
      number=9, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='_from_subdomain', full_name='appointment.AppointmentDetails._from_subdomain', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='customer_note', full_name='appointment.AppointmentDetails.customer_note', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='_required_payment', full_name='appointment.AppointmentDetails._required_payment', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_id', full_name='appointment.AppointmentDetails._id',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_import_id', full_name='appointment.AppointmentDetails._import_id',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_status', full_name='appointment.AppointmentDetails._status',
      index=2, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_customer_name', full_name='appointment.AppointmentDetails._customer_name',
      index=3, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_customer_phone', full_name='appointment.AppointmentDetails._customer_phone',
      index=4, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_customer_email', full_name='appointment.AppointmentDetails._customer_email',
      index=5, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_booked_for_id', full_name='appointment.AppointmentDetails._booked_for_id',
      index=6, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='__from_subdomain', full_name='appointment.AppointmentDetails.__from_subdomain',
      index=7, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_customer_note', full_name='appointment.AppointmentDetails._customer_note',
      index=8, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='__required_payment', full_name='appointment.AppointmentDetails.__required_payment',
      index=9, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=531,
  serialized_end=1061,
)


_SUBBOOKING = _descriptor.Descriptor(
  name='SubBooking',
  full_name='appointment.SubBooking',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='appointment.SubBooking.id', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='booked_from', full_name='appointment.SubBooking.booked_from', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='booked_till', full_name='appointment.SubBooking.booked_till', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='staffer_id', full_name='appointment.SubBooking.staffer_id', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='appliance_id', full_name='appointment.SubBooking.appliance_id', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='service_variant_id', full_name='appointment.SubBooking.service_variant_id', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='_available_staffer_ids', full_name='appointment.SubBooking._available_staffer_ids', index=6,
      number=7, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_id', full_name='appointment.SubBooking._id',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_booked_from', full_name='appointment.SubBooking._booked_from',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_booked_till', full_name='appointment.SubBooking._booked_till',
      index=2, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_staffer_id', full_name='appointment.SubBooking._staffer_id',
      index=3, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_appliance_id', full_name='appointment.SubBooking._appliance_id',
      index=4, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1064,
  serialized_end=1313,
)


_APPOINTMENTREQUIREDPAYMENT = _descriptor.Descriptor(
  name='AppointmentRequiredPayment',
  full_name='appointment.AppointmentRequiredPayment',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='prepayment', full_name='appointment.AppointmentRequiredPayment.prepayment', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='payment_url', full_name='appointment.AppointmentRequiredPayment.payment_url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_payment_url', full_name='appointment.AppointmentRequiredPayment._payment_url',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1315,
  serialized_end=1402,
)


_APPOINTMENTUPDATE = _descriptor.Descriptor(
  name='AppointmentUpdate',
  full_name='appointment.AppointmentUpdate',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='business_id', full_name='appointment.AppointmentUpdate.business_id', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='id', full_name='appointment.AppointmentUpdate.id', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='import_id', full_name='appointment.AppointmentUpdate.import_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='status', full_name='appointment.AppointmentUpdate.status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='customer_note', full_name='appointment.AppointmentUpdate.customer_note', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_status', full_name='appointment.AppointmentUpdate._status',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_customer_note', full_name='appointment.AppointmentUpdate._customer_note',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1405,
  serialized_end=1548,
)


_APPOINTMENTSSYNCREQUEST = _descriptor.Descriptor(
  name='AppointmentsSyncRequest',
  full_name='appointment.AppointmentsSyncRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='business_id', full_name='appointment.AppointmentsSyncRequest.business_id', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='booksy_customer_id', full_name='appointment.AppointmentsSyncRequest.booksy_customer_id', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='partner_customer_id', full_name='appointment.AppointmentsSyncRequest.partner_customer_id', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1550,
  serialized_end=1653,
)


_APPOINTMENTSSYNCRESPONSE = _descriptor.Descriptor(
  name='AppointmentsSyncResponse',
  full_name='appointment.AppointmentsSyncResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1655,
  serialized_end=1681,
)

_TIMESLOTSQUERYREQUEST_SUBBOOKING.containing_type = _TIMESLOTSQUERYREQUEST
_TIMESLOTSQUERYREQUEST.fields_by_name['subbookings'].message_type = _TIMESLOTSQUERYREQUEST_SUBBOOKING
_TIMESLOTSQUERYREQUEST.oneofs_by_name['_omit_appointment_import_id'].fields.append(
  _TIMESLOTSQUERYREQUEST.fields_by_name['omit_appointment_import_id'])
_TIMESLOTSQUERYREQUEST.fields_by_name['omit_appointment_import_id'].containing_oneof = _TIMESLOTSQUERYREQUEST.oneofs_by_name['_omit_appointment_import_id']
_TIMESLOTSQUERYRESPONSE_STAFFERAVAILABILITY.containing_type = _TIMESLOTSQUERYRESPONSE
_TIMESLOTSQUERYRESPONSE.fields_by_name['staffers_availability'].message_type = _TIMESLOTSQUERYRESPONSE_STAFFERAVAILABILITY
_APPOINTMENTDETAILS.fields_by_name['subbookings'].message_type = _SUBBOOKING
_APPOINTMENTDETAILS.fields_by_name['_required_payment'].message_type = _APPOINTMENTREQUIREDPAYMENT
_APPOINTMENTDETAILS.oneofs_by_name['_id'].fields.append(
  _APPOINTMENTDETAILS.fields_by_name['id'])
_APPOINTMENTDETAILS.fields_by_name['id'].containing_oneof = _APPOINTMENTDETAILS.oneofs_by_name['_id']
_APPOINTMENTDETAILS.oneofs_by_name['_import_id'].fields.append(
  _APPOINTMENTDETAILS.fields_by_name['import_id'])
_APPOINTMENTDETAILS.fields_by_name['import_id'].containing_oneof = _APPOINTMENTDETAILS.oneofs_by_name['_import_id']
_APPOINTMENTDETAILS.oneofs_by_name['_status'].fields.append(
  _APPOINTMENTDETAILS.fields_by_name['status'])
_APPOINTMENTDETAILS.fields_by_name['status'].containing_oneof = _APPOINTMENTDETAILS.oneofs_by_name['_status']
_APPOINTMENTDETAILS.oneofs_by_name['_customer_name'].fields.append(
  _APPOINTMENTDETAILS.fields_by_name['customer_name'])
_APPOINTMENTDETAILS.fields_by_name['customer_name'].containing_oneof = _APPOINTMENTDETAILS.oneofs_by_name['_customer_name']
_APPOINTMENTDETAILS.oneofs_by_name['_customer_phone'].fields.append(
  _APPOINTMENTDETAILS.fields_by_name['customer_phone'])
_APPOINTMENTDETAILS.fields_by_name['customer_phone'].containing_oneof = _APPOINTMENTDETAILS.oneofs_by_name['_customer_phone']
_APPOINTMENTDETAILS.oneofs_by_name['_customer_email'].fields.append(
  _APPOINTMENTDETAILS.fields_by_name['customer_email'])
_APPOINTMENTDETAILS.fields_by_name['customer_email'].containing_oneof = _APPOINTMENTDETAILS.oneofs_by_name['_customer_email']
_APPOINTMENTDETAILS.oneofs_by_name['_booked_for_id'].fields.append(
  _APPOINTMENTDETAILS.fields_by_name['booked_for_id'])
_APPOINTMENTDETAILS.fields_by_name['booked_for_id'].containing_oneof = _APPOINTMENTDETAILS.oneofs_by_name['_booked_for_id']
_APPOINTMENTDETAILS.oneofs_by_name['__from_subdomain'].fields.append(
  _APPOINTMENTDETAILS.fields_by_name['_from_subdomain'])
_APPOINTMENTDETAILS.fields_by_name['_from_subdomain'].containing_oneof = _APPOINTMENTDETAILS.oneofs_by_name['__from_subdomain']
_APPOINTMENTDETAILS.oneofs_by_name['_customer_note'].fields.append(
  _APPOINTMENTDETAILS.fields_by_name['customer_note'])
_APPOINTMENTDETAILS.fields_by_name['customer_note'].containing_oneof = _APPOINTMENTDETAILS.oneofs_by_name['_customer_note']
_APPOINTMENTDETAILS.oneofs_by_name['__required_payment'].fields.append(
  _APPOINTMENTDETAILS.fields_by_name['_required_payment'])
_APPOINTMENTDETAILS.fields_by_name['_required_payment'].containing_oneof = _APPOINTMENTDETAILS.oneofs_by_name['__required_payment']
_SUBBOOKING.oneofs_by_name['_id'].fields.append(
  _SUBBOOKING.fields_by_name['id'])
_SUBBOOKING.fields_by_name['id'].containing_oneof = _SUBBOOKING.oneofs_by_name['_id']
_SUBBOOKING.oneofs_by_name['_booked_from'].fields.append(
  _SUBBOOKING.fields_by_name['booked_from'])
_SUBBOOKING.fields_by_name['booked_from'].containing_oneof = _SUBBOOKING.oneofs_by_name['_booked_from']
_SUBBOOKING.oneofs_by_name['_booked_till'].fields.append(
  _SUBBOOKING.fields_by_name['booked_till'])
_SUBBOOKING.fields_by_name['booked_till'].containing_oneof = _SUBBOOKING.oneofs_by_name['_booked_till']
_SUBBOOKING.oneofs_by_name['_staffer_id'].fields.append(
  _SUBBOOKING.fields_by_name['staffer_id'])
_SUBBOOKING.fields_by_name['staffer_id'].containing_oneof = _SUBBOOKING.oneofs_by_name['_staffer_id']
_SUBBOOKING.oneofs_by_name['_appliance_id'].fields.append(
  _SUBBOOKING.fields_by_name['appliance_id'])
_SUBBOOKING.fields_by_name['appliance_id'].containing_oneof = _SUBBOOKING.oneofs_by_name['_appliance_id']
_APPOINTMENTREQUIREDPAYMENT.oneofs_by_name['_payment_url'].fields.append(
  _APPOINTMENTREQUIREDPAYMENT.fields_by_name['payment_url'])
_APPOINTMENTREQUIREDPAYMENT.fields_by_name['payment_url'].containing_oneof = _APPOINTMENTREQUIREDPAYMENT.oneofs_by_name['_payment_url']
_APPOINTMENTUPDATE.oneofs_by_name['_status'].fields.append(
  _APPOINTMENTUPDATE.fields_by_name['status'])
_APPOINTMENTUPDATE.fields_by_name['status'].containing_oneof = _APPOINTMENTUPDATE.oneofs_by_name['_status']
_APPOINTMENTUPDATE.oneofs_by_name['_customer_note'].fields.append(
  _APPOINTMENTUPDATE.fields_by_name['customer_note'])
_APPOINTMENTUPDATE.fields_by_name['customer_note'].containing_oneof = _APPOINTMENTUPDATE.oneofs_by_name['_customer_note']
DESCRIPTOR.message_types_by_name['TimeSlotsQueryRequest'] = _TIMESLOTSQUERYREQUEST
DESCRIPTOR.message_types_by_name['TimeSlotsQueryResponse'] = _TIMESLOTSQUERYRESPONSE
DESCRIPTOR.message_types_by_name['AppointmentDetails'] = _APPOINTMENTDETAILS
DESCRIPTOR.message_types_by_name['SubBooking'] = _SUBBOOKING
DESCRIPTOR.message_types_by_name['AppointmentRequiredPayment'] = _APPOINTMENTREQUIREDPAYMENT
DESCRIPTOR.message_types_by_name['AppointmentUpdate'] = _APPOINTMENTUPDATE
DESCRIPTOR.message_types_by_name['AppointmentsSyncRequest'] = _APPOINTMENTSSYNCREQUEST
DESCRIPTOR.message_types_by_name['AppointmentsSyncResponse'] = _APPOINTMENTSSYNCRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

TimeSlotsQueryRequest = _reflection.GeneratedProtocolMessageType('TimeSlotsQueryRequest', (_message.Message,), {

  'SubBooking' : _reflection.GeneratedProtocolMessageType('SubBooking', (_message.Message,), {
    'DESCRIPTOR' : _TIMESLOTSQUERYREQUEST_SUBBOOKING,
    '__module__' : 'merger_grpc.proto.appointment_pb2'
    # @@protoc_insertion_point(class_scope:appointment.TimeSlotsQueryRequest.SubBooking)
    })
  ,
  'DESCRIPTOR' : _TIMESLOTSQUERYREQUEST,
  '__module__' : 'merger_grpc.proto.appointment_pb2'
  # @@protoc_insertion_point(class_scope:appointment.TimeSlotsQueryRequest)
  })
_sym_db.RegisterMessage(TimeSlotsQueryRequest)
_sym_db.RegisterMessage(TimeSlotsQueryRequest.SubBooking)

TimeSlotsQueryResponse = _reflection.GeneratedProtocolMessageType('TimeSlotsQueryResponse', (_message.Message,), {

  'StafferAvailability' : _reflection.GeneratedProtocolMessageType('StafferAvailability', (_message.Message,), {
    'DESCRIPTOR' : _TIMESLOTSQUERYRESPONSE_STAFFERAVAILABILITY,
    '__module__' : 'merger_grpc.proto.appointment_pb2'
    # @@protoc_insertion_point(class_scope:appointment.TimeSlotsQueryResponse.StafferAvailability)
    })
  ,
  'DESCRIPTOR' : _TIMESLOTSQUERYRESPONSE,
  '__module__' : 'merger_grpc.proto.appointment_pb2'
  # @@protoc_insertion_point(class_scope:appointment.TimeSlotsQueryResponse)
  })
_sym_db.RegisterMessage(TimeSlotsQueryResponse)
_sym_db.RegisterMessage(TimeSlotsQueryResponse.StafferAvailability)

AppointmentDetails = _reflection.GeneratedProtocolMessageType('AppointmentDetails', (_message.Message,), {
  'DESCRIPTOR' : _APPOINTMENTDETAILS,
  '__module__' : 'merger_grpc.proto.appointment_pb2'
  # @@protoc_insertion_point(class_scope:appointment.AppointmentDetails)
  })
_sym_db.RegisterMessage(AppointmentDetails)

SubBooking = _reflection.GeneratedProtocolMessageType('SubBooking', (_message.Message,), {
  'DESCRIPTOR' : _SUBBOOKING,
  '__module__' : 'merger_grpc.proto.appointment_pb2'
  # @@protoc_insertion_point(class_scope:appointment.SubBooking)
  })
_sym_db.RegisterMessage(SubBooking)

AppointmentRequiredPayment = _reflection.GeneratedProtocolMessageType('AppointmentRequiredPayment', (_message.Message,), {
  'DESCRIPTOR' : _APPOINTMENTREQUIREDPAYMENT,
  '__module__' : 'merger_grpc.proto.appointment_pb2'
  # @@protoc_insertion_point(class_scope:appointment.AppointmentRequiredPayment)
  })
_sym_db.RegisterMessage(AppointmentRequiredPayment)

AppointmentUpdate = _reflection.GeneratedProtocolMessageType('AppointmentUpdate', (_message.Message,), {
  'DESCRIPTOR' : _APPOINTMENTUPDATE,
  '__module__' : 'merger_grpc.proto.appointment_pb2'
  # @@protoc_insertion_point(class_scope:appointment.AppointmentUpdate)
  })
_sym_db.RegisterMessage(AppointmentUpdate)

AppointmentsSyncRequest = _reflection.GeneratedProtocolMessageType('AppointmentsSyncRequest', (_message.Message,), {
  'DESCRIPTOR' : _APPOINTMENTSSYNCREQUEST,
  '__module__' : 'merger_grpc.proto.appointment_pb2'
  # @@protoc_insertion_point(class_scope:appointment.AppointmentsSyncRequest)
  })
_sym_db.RegisterMessage(AppointmentsSyncRequest)

AppointmentsSyncResponse = _reflection.GeneratedProtocolMessageType('AppointmentsSyncResponse', (_message.Message,), {
  'DESCRIPTOR' : _APPOINTMENTSSYNCRESPONSE,
  '__module__' : 'merger_grpc.proto.appointment_pb2'
  # @@protoc_insertion_point(class_scope:appointment.AppointmentsSyncResponse)
  })
_sym_db.RegisterMessage(AppointmentsSyncResponse)



_APPOINTMENT = _descriptor.ServiceDescriptor(
  name='Appointment',
  full_name='appointment.Appointment',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=1684,
  serialized_end=2160,
  methods=[
  _descriptor.MethodDescriptor(
    name='TimeSlotsQuery',
    full_name='appointment.Appointment.TimeSlotsQuery',
    index=0,
    containing_service=None,
    input_type=_TIMESLOTSQUERYREQUEST,
    output_type=_TIMESLOTSQUERYRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='DryRunAppointment',
    full_name='appointment.Appointment.DryRunAppointment',
    index=1,
    containing_service=None,
    input_type=_APPOINTMENTDETAILS,
    output_type=_APPOINTMENTDETAILS,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SaveAppointment',
    full_name='appointment.Appointment.SaveAppointment',
    index=2,
    containing_service=None,
    input_type=_APPOINTMENTDETAILS,
    output_type=_APPOINTMENTDETAILS,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='UpdateAppointment',
    full_name='appointment.Appointment.UpdateAppointment',
    index=3,
    containing_service=None,
    input_type=_APPOINTMENTUPDATE,
    output_type=_APPOINTMENTUPDATE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ScheduleAppointmentsSync',
    full_name='appointment.Appointment.ScheduleAppointmentsSync',
    index=4,
    containing_service=None,
    input_type=_APPOINTMENTSSYNCREQUEST,
    output_type=_APPOINTMENTSSYNCRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_APPOINTMENT)

DESCRIPTOR.services_by_name['Appointment'] = _APPOINTMENT

# @@protoc_insertion_point(module_scope)
