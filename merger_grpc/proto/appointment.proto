syntax = "proto3";

package appointment;


// Appointment related procedures
service Appointment {
    rpc TimeSlotsQuery (TimeSlotsQueryRequest) returns (TimeSlotsQueryResponse) {}
    rpc DryRunAppointment (AppointmentDetails) returns (AppointmentDetails) {}
    rpc SaveAppointment (AppointmentDetails) returns (AppointmentDetails) {}
    rpc UpdateAppointment (AppointmentUpdate) returns (AppointmentUpdate) {}
    rpc ScheduleAppointmentsSync (AppointmentsSyncRequest) returns (AppointmentsSyncResponse) {}
}


/*
    oneof explanation

    oneof is used here to define optional parameters.
    Without it, for unprovided parameters, default values are returned (e.g. 0 for ints).
    We want these parameters to be skipped in serialization/deserialization results.
    In the future we might use the "optional" keyword, for now it doesn't work correctly.
*/


/*
    TimeSlots

    Slots available for booking

    business_id: Business ID
    service_variant_id: ServiceVariant ID to book
    omit_appointment_import_id: Import ID of Appointment to omit - as if it not existed. It is used to edit existing appointment.
    staffer_id: IDs of staffers to include. If empty, include all visible staffers performing selected ServiceVariant.
    start_date: start date
    end_date: end date (included)
*/

message TimeSlotsQueryRequest {
    message SubBooking {
        int32 service_variant_id = 1;
        repeated int32 staffer_id = 2;
    }

    int32 business_id = 1;
    repeated SubBooking subbookings = 2;
    oneof _omit_appointment_import_id { string omit_appointment_import_id = 3; } // optional omit_appointment_import_id
    int64 start_date = 4;
    int64 end_date = 5;
}

message TimeSlotsQueryResponse {
    message StafferAvailability {
        int32 staffer_id = 1;
        repeated int64 slots = 2;
    }

    int32 business_id = 1;
    repeated StafferAvailability staffers_availability = 2;
}

// END TimeSlots


message AppointmentDetails {
    int32 business_id = 1;
    oneof _id { int32 id = 2; } // optional id
    oneof _import_id { string import_id = 3; } // optional import_id
    oneof _status { string status = 4; } // optional status
    oneof _customer_name { string customer_name = 5; } // optional customer_name
    oneof _customer_phone { string customer_phone = 6; } // optional customer_phone
    oneof _customer_email { string customer_email = 7; } // optional customer_email
    oneof _booked_for_id { int32 booked_for_id = 8; } // optional booked_for_id
    repeated SubBooking subbookings = 9;
    oneof __from_subdomain { bool _from_subdomain = 10; } // optional _from_subdomain
    oneof _customer_note { string customer_note = 11; } // optional customer_note
    oneof __required_payment { AppointmentRequiredPayment _required_payment = 12; } // optional _required_payment
}

message SubBooking {
    oneof _id { int32 id = 1; } // optional id
    oneof _booked_from { int64 booked_from = 2; } // optional booked_from
    oneof _booked_till { int64 booked_till = 3; } // optional booked_till
    oneof _staffer_id { int32 staffer_id = 4; } // optional staffer_id
    oneof _appliance_id { int32 appliance_id = 5; } // optional appliance_id
    int32 service_variant_id = 6;
    repeated int32 _available_staffer_ids = 7;
}

message AppointmentRequiredPayment {
    string prepayment = 1;
    oneof _payment_url { string payment_url = 2; } // optional payment_url
}


message AppointmentUpdate {
    int32 business_id = 1;
    int32 id = 2;
    string import_id = 3;
    oneof _status { string status = 4; } // optional status
    oneof _customer_note { string customer_note = 5; } // optional customer_note
}


message AppointmentsSyncRequest {
    int32 business_id = 1;
    int32 booksy_customer_id = 2;
    int32 partner_customer_id = 3;
}

message AppointmentsSyncResponse {}
