id: ResourceAvailability
required:
  - timeoff
  - not_working_day
  - not_working_hours
  - conflict
  - free_from
  - ok
properties:
  time_passed:
    type: boolean
    description: selected hours are in the past
  timeoff:
    type: boolean
    description: resource has a time-off on this day
  not_working_day:
    type: boolean
    description: resource doesn't work on that day
  not_working_hours:
    type: boolean
    description: resource doesn't work in selected hours
  conflict:
    type: boolean
    description: there is another booking or time reservation in selected hours
  free_from:
    type: string
    format: date-time
    description:
        (can be null)
        earliest free time for this resource
        (starting from previous booking or appointment start)
  ok:
    type: boolean
    description: resource is available at selected hours
  message:
    type: string
    description:
      A message to user describing most relevant availability trait.
      Type field defines how the message should be displayed.
  type:
    type: string
    description: type of availability message
    enum:
      - ok
      - info
      - error
