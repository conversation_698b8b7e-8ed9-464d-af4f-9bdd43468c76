id: SearchTransactionsResponse
description: List of POS transactions
required:
    - transaction_counts
    - transactions
    - page
    - per_page
    - count
properties:
    transaction_counts:
        type: POSFaceting
        description: Faceting for transactions
    transactions:
        type: array
        items:
            type: TransactionDetails
    count:
        type: integer
        description: number of Transactions matched
    page:
        type: integer
        description: results page
    per_page:
        type: integer
        description: how many results per page
