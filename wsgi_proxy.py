import os
import logging

import signal
from datetime import datetime
from functools import wraps

from datadog import statsd
from django.conf import settings
from django.core.handlers.wsgi import get_path_info
from django.urls.base import resolve
from django.urls.exceptions import Resolver404

from lib.uwsgi_tools import BooksyWSGIAdapter


class Harakiri(TimeoutError):
    pass


def exit_gracefully(*args):
    request_log = logging.getLogger('booksy.wsgi')
    request_log.error('uwsgi exit_gracefully')

    raise <PERSON><PERSON><PERSON>


try:
    import uwsgi  # pylint: disable=unused-import

    signal.signal(signal.SIGSYS, exit_gracefully)
except ImportError:
    pass


def is_old_request(environ):
    nginx_timestamp = environ.get('PROXY_TIME')
    nginx_timeout = int(environ.get('PROXY_READ_TIMEOUT', '0'))
    if nginx_timestamp and nginx_timeout:
        request_age = datetime.now() - datetime.fromtimestamp(float(nginx_timestamp))
        if request_age.total_seconds() > nginx_timeout - 1:
            request_log = logging.getLogger('booksy.wsgi')
            request_log.error('Dropped request with age: %s', request_age)
            return True
    return False


def extract_and_emit_time_in_queue(environ):
    nginx_timestamp = environ.get('PROXY_TIME')
    if settings.DD_AGENT_ENABLED and nginx_timestamp:
        time_in_listen_queue = datetime.now() - datetime.fromtimestamp(float(nginx_timestamp))
        statsd.histogram(
            'booksy.uwsgi.time_in_listen',
            time_in_listen_queue.total_seconds(),
            tags=[f'pod_name:{os.uname().nodename}'],
        )


def time_in_listen_metric_wrapper(func):
    @wraps(func)
    def wrapper(self, environ, start_response):
        path = get_path_info(environ)
        if 'health_check' not in path and 'liveness_check' not in path:
            extract_and_emit_time_in_queue(environ)
        return func(self, environ, start_response)

    return wrapper


def drop_old_request_wrapper(func):
    @wraps(func)
    def wrapper(self, environ, start_response):
        path = get_path_info(environ)
        if 'liveness_check' not in path and is_old_request(environ):
            start_response('504 GATEWAY_TIMEOUT', [('Content-Type', 'text/html')])
            return [b"Request dropped"]
        return func(self, environ, start_response)

    return wrapper


class TornadoDjangoWsgi:
    def __init__(self, django_app, tornado_app):
        self.django_app = django_app
        if settings.RUN_MODE == 'tornado':
            tornado_app = BooksyWSGIAdapter(tornado_app)
        self.tornado_app = tornado_app

    @time_in_listen_metric_wrapper
    @drop_old_request_wrapper
    def __call__(self, environ, start_response):
        path = get_path_info(environ)

        if update_path := not path.endswith('/'):
            # add missing training slash for backward compatibility
            path += '/'

        if self.use_django(path, environ.get('REQUEST_METHOD')):
            if update_path:
                environ['PATH_INFO'] = path
            return self.django_app(environ, start_response)
        return self.tornado_app(environ, start_response)

    @property
    def wildcard_router(self):
        return self.tornado_app.wildcard_router

    @property
    def handlers(self):
        return self.tornado_app.handlers

    @staticmethod
    def use_django(path: str, method: str) -> bool:
        """Decide if Django or Tornado should be used based on path.

        If path exists in Django urls then use Django.
        If not then fallback to Tornado.
        Tornado would also handle legitimate 404 errors.

        Additionally, this code will check a feature flag to determine which implementation
        should be used.
        Feature flag should be specified in the `__tornado_migration_feature_flag__` attribute
        of View class.

        For specifying allowed HTTP methods in view, use `__tornado_migration_allowed_methods__`.
        For empty list or attribute absent on view class, all methods will be served.
        """
        try:
            match = resolve(path)
        except Resolver404:
            use_django = False
        else:
            use_django = True

            # check for feature flag
            if klass := getattr(match.func, 'cls', None):
                if feature_flag := getattr(klass, '__tornado_migration_feature_flag__', None):
                    allowed_methods = getattr(klass, '__tornado_migration_allowed_methods__', None)
                    use_django = feature_flag() and (
                        not allowed_methods or method in allowed_methods
                    )

        return use_django
